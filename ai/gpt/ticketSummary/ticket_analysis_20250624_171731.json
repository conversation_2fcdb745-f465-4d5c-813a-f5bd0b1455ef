[{"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problems": ["The list of impacted custom objects for FPS01 differs from the list for FPS02, with only 3 common objects between the two lists despite expectations that FPS02 should include all FPS01 objects.", "The S/4HANA custom code checks cannot be configured for specific feature packs; they can only be configured for specific releases."], "solutions": ["If no changes are done between two runs, the list of impacted custom objects for FPS01 and FPS02 of the same product version should be the same.", "If upgrading directly to FP02, one can only consider the list of impacted custom objects for FPS02."], "causes": ["Differences between the SAP_S4U_2 ATC runs are due to changes in two objects, ZCL_FI_GL_UPLOAD_DPC_EXT and ZFI_REPT_BPAY_CREDIT, between the two runs.", "There might be simplification databases valid only for FPS01, not for FPS02, affecting the list of impacted objects.", "Custom Code checks are configured for specific releases, not feature packs, leading to differences in findings when different feature packs are evaluated."], "processed_at": "2025-06-24T17:17:37.231008"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e3c57b897e75ed8b86630200153af15", "title": "Readiness Check uploaded file is not valid", "problems": ["The customer attempted to upload a ZIP file named \"BPIAnalysisDataPRO20241105.zip\" to the SAP Readiness Check portal, but received an error message stating that the uploaded file is not valid.", "The customer used the report RC_VALUE_DISCOVERY_COLL_DATA, which is not intended for the SAP Readiness Check, leading to the creation of an incorrect file format for the intended upload."], "solutions": ["If the customer is aiming for a \"*PERSON*, discovery edition\" report, they should refer to the How-To Guide linked at https://sdmp-prod-prod-bpi-sdmp-app.cfapps.eu20-001.hana.ondemand.com/create-request/create-request.html for the relevant steps.", "If the customer is aiming for a Readiness Check for a conversion of SAP ERP 6 to a S/4HANA system, they should refer to SAP Note 2913617 - \"SAP Readiness Check for SAP S/4HANA\" for all necessary prerequisites and steps."], "causes": ["The customer mistakenly used the report RC_VALUE_DISCOVERY_COLL_DATA, which is not the Readiness Check report, causing an incorrect file format to be generated for the upload to the SAP Readiness Check portal.", "Mixing up different reports such as SPIDE reports and data collection files led to confusion and incorrect processing."], "processed_at": "2025-06-24T17:17:43.501172"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problems": ["Unable to upload the results of the readiness check to the specified URL (https://me.sap.com/readinesscheck); the message \"The connection was reset.\" always appears.", "The access issue persists despite having the necessary authorizations and trying different browsers and clearing cache."], "solutions": ["Verify if the S-user used to access the page has the necessary authorizations as mentioned in note 3310759.", "Use an incognito tab or deactivate the blocking of third-party cookies in the web browser.", "If it isn't possible to change the browser settings, open a new browser session, log in using the S-user, and then access the Readiness Check landing page.", "Refer to KBA 3330283 for additional resolution steps related to the SAP Readiness Check access issue.", "SAP Support uploaded the attached Zip files manually to the readiness check for the customer."], "causes": ["The S-user might not have the necessary authorizations to access SAP Readiness Check.", "The web browser being used might be blocking third-party cookies, which prevents accessing the readiness check.", "The \"connection reset\" issue might be due to duplicate files being loaded."], "processed_at": "2025-06-24T17:17:49.009929"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problems": ["SAP Readiness Check for custom code analysis jobs RC_COLLECT_ANALYSIS_DATA and TMW_RC_CCA_DATA_COLL are failing.", "The jobs are generating numerous short dumps during runtime.", "Syntax error in SAPLSTRD in system MWD preventing analysis.", "LOAD_PROGRAM_CLASS_MISMATCH error during ATC checks."], "solutions": ["Add required authorization object 'S_Q_GOVERN' with specific values to the user executing the jobs, which resolved the job failures.", "Fix syntax errors in SAPLSTRD to enable correct functioning of the system.", "Implement SAP Notes 2436688, 3293011, 3365357, and import simplification database content as per SAP Note 2241080 to ensure system readiness.", "Regenerate classes CL_ABAP_COMPILER and CL_CI_TEST_ABAP_COMPILER by activating them in transaction SE24."], "causes": ["Missing authorization object 'S_Q_GOVERN' leading to job cancellations.", "Syntax errors in SAPLSTRD preventing normal operation.", "Incomplete implementation of necessary SAP Notes and prerequisites for SAP Readiness Check.", "Interface changes at runtime causing LOAD_PROGRAM_CLASS_MISMATCH errors."], "processed_at": "2025-06-24T17:17:54.206533"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problems": ["Question regarding the integration of Readiness Check analysis into SAP Cloud ALM, specifically whether all types of readiness check results (including ECC to S4HANA conversion readiness check results) can be integrated and used to create follow-up items.", "Clarification needed on whether ECC version upgrades are supported with the Readiness Check for preparing S4HANA transformation."], "solutions": ["Refer to SAP Note 2913617 - SAP Readiness Check for SAP S/4HANA, which guides on running ECC to S4HANA readiness check and uploading the analysis results to Cloud ALM.", "Use the link provided in the help documentation for integrating readiness check results with SAP Cloud ALM to understand the support scenarios."], "causes": ["Lack of clear documentation or confirmation on whether all types of readiness check analyses can be integrated into CALM and used for creating follow-up items.", "Readiness Check is not supported for ECC version upgrades, only for upgrades from ECC to S/4HANA or from S/4HANA to another S/4HANA product version."], "processed_at": "2025-06-24T17:17:59.353560"}]