[{"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "The customer reported a discrepancy in the custom code analysis results for the S/4HANA 2023 feature pack stacks, FP01 and FP02. Specifically, the analysis flagged different sets of impacted objects for each feature pack, with only 3 common objects between them, which raised the question of why FP02's list did not include all objects from FP01.", "solution": "The Custom Code team analyzed the ATC runs provided by the customer and identified that the differences were due to changes in two specific objects, ZCL_FI_GL_UPLOAD_DPC_EXT and ZFI_REPT_BPAY_CREDIT, between the two runs. The team confirmed that if no changes were made between two runs, the list of impacted custom objects should be the same for both FPS01 and FPS02. The case was closed by the customer after receiving these findings.", "cause": "The root cause of the discrepancy was identified as changes in certain objects between the two ATC runs. ZCL_FI_GL_UPLOAD_DPC_EXT was changed on 13.11 and ZFI_REPT_BPAY_CREDIT was changed on 14.11, leading to different results between the runs for FPS01 and FPS02.", "processed_at": "2025-06-23T17:05:41.778880"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e3c57b897e75ed8b86630200153af15", "title": "Readiness Check uploaded file is not valid", "problem": "The customer attempted to upload a ZIP file generated by the RC_VALUE_DISCOVERY_COLL_DATA report to the SAP Readiness Check portal, which resulted in an error message stating the file is not valid. The customer is mixing up different reports, namely the discovery edition report with the Readiness Check report.", "solution": "The customer needs to clarify their aim. If aiming for a discovery edition report, they should follow the steps in the linked How-To Guide. If the goal is a Readiness Check for conversion from SAP ERP 6 to S/4HANA, the customer should refer to SAP Note 2913617 for the correct steps and prerequisites.", "cause": "The incorrect report was used for generating the data collection file. The RC_VALUE_DISCOVERY_COLL_DATA report is not intended for the Readiness Check, leading to the invalid file upload.", "processed_at": "2025-06-23T17:05:46.100952"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "Unable to upload the results of the readiness check to the SAP Readiness Check URL due to the \"connection reset\" error. The customer can access me.sap.com generally but not the readiness check.", "solution": "Verify if the S-user has the necessary authorizations as per note 3310759. If the S-user has the necessary authorizations, use an incognito tab or deactivate the blocking of third-party cookies in the web browser settings. Alternatively, open a new browser session, log in to the SAP ALM portal, and then access the Readiness Check landing page. SAP Support also uploaded the Zip files to the readiness check for the customer.", "cause": "Two possibilities were identified: the S-user might not have the necessary authorizations, or the web browser might be blocking third-party cookies.", "processed_at": "2025-06-23T17:05:50.611577"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "SAP Readiness Check's Custom Code Analysis jobs are failing, generating numerous short dumps during runtime.", "solution": "The issue was resolved by adding the necessary authorization object and value to the executing user, which allowed the job logs RC_COLLECT_ANALYSIS_DATA and TMW_RC_CCA_DATA_COLL to finish successfully.", "cause": "The failure of the jobs was due to missing authorization for the executing user, specifically the 'S_Q_GOVERN' authorization object with ACTVT: 06 and ATC_OTYPGO: 01.", "processed_at": "2025-06-23T17:05:54.905035"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "Question regarding the integration of SAP Readiness Check analysis into Cloud ALM, specifically whether all types of readiness check results, including ECC to S4HANA conversion, can be integrated and used to create follow-up items within CALM.", "solution": "The customer was directed to a help document that lists all support scenarios and checks integrated with CALM in SAP Readiness Check. It was confirmed that readiness check results can be uploaded to CALM, and follow-up items such as requirements and user stories can be created from there. However, it was clarified that Readiness Check is only used for upgrades from ECC to S/4HANA or from S/4HANA to another S/4HANA version, not for ECC version upgrades.", "cause": "The customer needed clarification on the capabilities and limitations of SAP Readiness Check integration with Cloud ALM, particularly regarding ECC upgrades versus ECC to S/4HANA conversions.", "processed_at": "2025-06-23T17:05:59.622251"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/46b50a1797eb9a10b86630200153af59", "title": "No value was passed to the mandatory parameter \"PCT_DETAIL_LIST\"", "problem": "The customer reported an ABAP dump occurring when executing the report \"RC_COLLECT_ANALYSIS_DATA\" due to a syntax error. Specifically, the error message indicated that no value was passed to the mandatory parameter \"PCT_DETAIL_LIST\".", "solution": "The issue was resolved by following SAP Note 3205320, which involved running the report \"ZNOTE_2745851_REPAIR\" and re-implementing SAP Note 2745851. After these steps, the report \"RC_COLLECT_ANALYSIS_DATA\" ran without generating an ABAP dump.", "cause": "The situation was initially caused by old coding that did not get updated during the implementation of SAP Notes 2758146 and/or 2745851. The customer was advised to de-implement and re-implement these SAP Notes in their latest versions and in the correct order to resolve the syntax errors.", "processed_at": "2025-06-23T17:06:04.370720"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbe45875c3a71ed4553db11f0501319d", "title": "Not able to upload the readiness analysis file in manage analysis files app", "problem": "Not able to upload the readiness analysis file in the Manage Analysis Files app. The uploaded file is denied as invalid due to unsupported format. The SAP Note 3275056 referred to for instructions seems to be intended for SAP ECC systems rather than S4 HANA, and a specific program mentioned in the note does not exist.", "solution": "", "cause": "", "processed_at": "2025-06-23T17:06:07.609227"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/79fdf13f977b1a5030c531f71153afae", "title": "Readiness Check WebView error", "problem": "Readiness Check WebView error where the display in the web view does not match the data shown in Excel for the \"Organizational structure - Overview\" area.", "solution": "The issue was identified as a display problem in the application web view. A fix was applied, and the customer was advised to check their analysis again to see if the numbers now appear correctly on the application web view. The customer confirmed the solution and closed the incident.", "cause": "Unknown.", "processed_at": "2025-06-23T17:06:10.854732"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0f499dc03b0c6e508c36d62a85e45abc", "title": "User's are unable to accesses the SAP Readinesscheck.", "problem": "User is unable to access the SAP Readiness Check portal, encountering a blank page despite having the relevant RC* authorizations assigned to their S-User ID.", "solution": "It is suggested to refer to the approaches outlined in SAP KBA 3330283 - \"SAP Readiness Check could not open in SAP for Me,\" which should provide steps to address the issue.", "cause": "The occurrence of an empty screen is not indicative of missing authorizations, as the application is designed to display a message when accessed without the relevant roles. Hence, the issue might not be related to authorization but could be addressed by following the guidance in the mentioned SAP KBA.", "processed_at": "2025-06-23T17:06:14.882580"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/877262d097cce6907bcf59081153af35", "title": "<PERSON><PERSON><PERSON>heck,", "problem": "We have applied the notes to execute the ReadinessCheck, however, even after performing the manual steps as indicated in the notes, some objects show the error. For example, the FIN_CORR_DISPLAY_ISSUE program, when trying to activate it, displays the message: \"The method 'GET_INSTANCE' is unknown or PROTECTED or PRIVATE.\" Because of this error, the TMW_RC_FDQ_DATA_COLL job is terminated with an error.", "solution": "The syntax error in the program or class should be resolved by importing Note **********. If the errors do not appear in the LBD development system, it is suggested to transport the healthy programs or classes from LBD to resolve the errors.", "cause": "improper user handling.", "processed_at": "2025-06-23T17:06:19.557058"}]