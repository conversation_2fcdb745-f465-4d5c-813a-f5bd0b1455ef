import streamlit as st
import sys
import os

# Add the current directory to Python path to enable relative imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from ticket_vector_search import TicketVectorSearch
from database import get_ticket_count
from config import STREAMLIT_CONFIG, SYSTEM_MESSAGES


def initialize_search_system():
    """Initialize the ticket search system"""
    if 'search_system' not in st.session_state:
        try:
            with st.spinner("Initializing SAP Ticket Assistant..."):
                st.session_state.search_system = TicketVectorSearch()
                st.session_state.system_ready = True
        except Exception as e:
            st.error(f"Failed to initialize system: {e}")
            st.session_state.system_ready = False
            st.session_state.search_system = None


def main():
    """Main Streamlit application"""
    st.set_page_config(
        page_title=STREAMLIT_CONFIG["page_title"],
        page_icon=STREAMLIT_CONFIG["page_icon"],
        layout=STREAMLIT_CONFIG["layout"]
    )
    
    st.title("🎫 SAP Ticket Assistant")
    st.markdown("Get recommendations based on similar SAP support cases")
    
    # Initialize the search system
    initialize_search_system()
    
    if not st.session_state.get('system_ready', False):
        st.error(SYSTEM_MESSAGES["system_not_ready"])
        return
    
    # Sidebar with system information
    with st.sidebar:
        st.header("📊 System Info")
        
        try:
            ticket_count = get_ticket_count()
            st.metric("Available Cases", ticket_count)
        except Exception as e:
            st.error(f"Error getting ticket count: {e}")
        
        st.markdown("---")
        st.markdown("**How it works:**")
        st.markdown("""
        1. 🔍 Enter your SAP-related question
        2. 🧠 AI finds similar support cases
        3. 💡 Get personalized recommendations
        4. 📚 Review source cases for context
        """)
        
        st.markdown("---")
        st.markdown("**Tips for better results:**")
        st.markdown("""
        - Be specific about your SAP component
        - Include error messages if any
        - Mention your SAP version if relevant
        - Describe what you were trying to do
        """)
    
    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = []
        # Add welcome message
        welcome_msg = SYSTEM_MESSAGES["welcome"]
        st.session_state.messages.append({"role": "assistant", "content": welcome_msg})
    
    # Display chat history
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # Chat input
    if prompt := st.chat_input("Ask me about your SAP issue..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate and display assistant response
        with st.chat_message("assistant"):
            with st.spinner("Searching for similar cases and generating recommendations..."):
                try:
                    # Prepare chat history for context (last 10 messages)
                    chat_history = []
                    recent_messages = st.session_state.messages[-10:]  # Last 10 messages
                    for msg in recent_messages[:-1]:  # Exclude the current user message
                        if msg["role"] in ["user", "assistant"]:
                            chat_history.append({
                                "role": msg["role"],
                                "content": msg["content"]
                            })
                    
                    # Get recommendation
                    result = st.session_state.search_system.search_and_recommend(
                        user_question=prompt,
                        chat_history=chat_history if chat_history else None
                    )
                    
                    if result["success"]:
                        # Display the main recommendation
                        st.markdown(result["recommendation"])
                        
                        # Display source tickets if available
                        if result["source_tickets"]:
                            with st.expander("📚 View Source Cases", expanded=False):
                                formatted_sources = st.session_state.search_system.format_source_tickets(
                                    result["source_tickets"]
                                )
                                st.markdown(formatted_sources)
                        
                        # Store the full response
                        full_response = result["recommendation"]
                        
                    else:
                        error_msg = f"❌ Error: {result.get('error', 'Unknown error occurred')}"
                        st.error(error_msg)
                        full_response = result["recommendation"]
                    
                except Exception as e:
                    error_msg = f"❌ An unexpected error occurred: {str(e)}"
                    st.error(error_msg)
                    full_response = "Sorry, I encountered an error while processing your request. Please try again."
        
        # Add assistant response to chat history
        st.session_state.messages.append({"role": "assistant", "content": full_response})
    
    # Add a clear chat button
    if st.sidebar.button("🗑️ Clear Chat History"):
        st.session_state.messages = []
        st.rerun()


if __name__ == "__main__":
    main()
