#!/usr/bin/env python3
"""
Launcher script for SAP Ticket Assistant
"""

import subprocess
import sys
import os


def check_dependencies():
    """Check if required packages are installed"""
    required_packages = ['streamlit', 'psycopg2', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            if package == 'psycopg2':
                try:
                    __import__('psycopg2-binary')
                except ImportError:
                    missing_packages.append('psycopg2-binary')
            else:
                missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall them with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def run_tests():
    """Run system tests before starting the app"""
    print("🔍 Running system tests...")
    try:
        result = subprocess.run([sys.executable, 'test_system.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            return True
        else:
            print("❌ Some tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def start_streamlit():
    """Start the Streamlit application"""
    print("🚀 Starting SAP Ticket Assistant...")
    try:
        # Change to the script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # Start Streamlit
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 
            'streamlit_chatbot.py',
            '--server.port', '8501',
            '--server.address', 'localhost'
        ])
    except KeyboardInterrupt:
        print("\n👋 Shutting down SAP Ticket Assistant...")
    except Exception as e:
        print(f"❌ Error starting Streamlit: {e}")


def main():
    """Main launcher function"""
    print("🎫 SAP Ticket Assistant Launcher")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Ask user if they want to run tests
    run_test = input("\n🧪 Run system tests before starting? (y/N): ").lower().strip()
    
    if run_test in ['y', 'yes']:
        if not run_tests():
            proceed = input("\n⚠️  Tests failed. Continue anyway? (y/N): ").lower().strip()
            if proceed not in ['y', 'yes']:
                print("Exiting...")
                sys.exit(1)
    
    # Start the application
    start_streamlit()


if __name__ == "__main__":
    main()
