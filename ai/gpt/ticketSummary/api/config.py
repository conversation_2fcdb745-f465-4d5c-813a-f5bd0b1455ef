"""
Configuration settings for SAP Ticket Assistant
"""

# Database Configuration
DATABASE_CONFIG = {
    "user": "184024b4abe8",
    "password": "cce6e3ded99172321cf1dd7e681ae92",
    "host": "127.0.0.1",
    "port": "8861",
    "database": "CxgapCTAJQku"
}

# LLM Service Configuration
LLM_CONFIG = {
    "base_url": "https://aicore-llm-proxy.internal.cfapps.eu12.hana.ondemand.com/",
    "api_key": "sk-58f4db0abc225fb9fa70a80e71ec732bfd086076ddc44c83",
    "completion_model": "gpt-4o-2024-08-06",
    "embedding_model": "text-embedding-ada-002-2"
}

# Search Configuration
SEARCH_CONFIG = {
    "default_limit": 3,  # Number of similar tickets to retrieve
    "similarity_threshold": 0.8,  # Cosine similarity threshold (optional)
    "max_context_length": 4000  # Maximum context length for LLM
}

# Streamlit Configuration
STREAMLIT_CONFIG = {
    "page_title": "SAP Ticket Assistant",
    "page_icon": "🎫",
    "layout": "wide",
    "max_chat_history": 20  # Maximum number of messages to keep in chat history
}

# System Messages
SYSTEM_MESSAGES = {
    "welcome": """👋 Welcome to SAP Ticket Assistant! 

I can help you find solutions to SAP-related problems by searching through our knowledge base of support cases.

Feel free to ask me about:
- SAP configuration issues
- Error messages and troubleshooting
- Best practices and recommendations
- Technical problems and solutions

What can I help you with today?""",
    
    "no_results": "I couldn't find any similar cases in our knowledge base. Please provide more specific details about your SAP issue, or consider contacting SAP support directly.",
    
    "error_generic": "Sorry, I encountered an error while processing your request. Please try again.",
    
    "system_not_ready": "System is not ready. Please check the configuration and try again."
}
