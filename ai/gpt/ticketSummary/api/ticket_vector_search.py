from typing import List, Dict, Any, Optional
from database import search_similar_tickets, verify_database_connection
from llm_client import LLMClient


class TicketVectorSearch:
    """Main class for ticket vector search and recommendation system"""
    
    def __init__(self):
        self.llm_client = LLMClient()
        self._verify_setup()
    
    def _verify_setup(self):
        """Verify that all components are properly set up"""
        print("Verifying ticket search system setup...")
        
        # Check database connection
        if not verify_database_connection():
            raise Exception("Database verification failed")
        
        # Check LLM service connection
        if not self.llm_client.test_connection():
            raise Exception("LLM service connection failed")
        
        print("✅ Ticket search system setup verified successfully")
    
    def search_and_recommend(self, user_question: str, chat_history: List[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Search for similar tickets and generate recommendations
        
        Args:
            user_question: The user's question
            chat_history: Previous chat messages (optional)
            
        Returns:
            Dictionary containing recommendation and source tickets
        """
        try:
            # Step 1: Generate embedding for user question
            print(f"Generating embedding for question: {user_question[:100]}...")
            embeddings = self.llm_client.get_embedding([user_question])
            
            if not embeddings or len(embeddings) == 0:
                return {
                    "success": False,
                    "error": "Failed to generate embedding for the question",
                    "recommendation": "Sorry, I couldn't process your question. Please try again.",
                    "source_tickets": []
                }
            
            query_embedding = embeddings[0]
            
            # Step 2: Search for similar tickets
            print("Searching for similar tickets...")
            similar_tickets = search_similar_tickets(query_embedding, limit=3)
            
            if not similar_tickets:
                return {
                    "success": True,
                    "recommendation": "I couldn't find any similar cases in our knowledge base. Please provide more specific details about your SAP issue, or consider contacting SAP support directly.",
                    "source_tickets": []
                }
            
            # Step 3: Extract solutions for context
            context_solutions = []
            for ticket in similar_tickets:
                if ticket['solution'] and ticket['solution'].strip():
                    context_solutions.append(ticket['solution'])
            
            if not context_solutions:
                return {
                    "success": True,
                    "recommendation": "I found some similar cases but they don't have detailed solutions available. Please provide more context about your specific issue.",
                    "source_tickets": similar_tickets
                }
            
            # Step 4: Generate recommendation using LLM
            print("Generating recommendation...")
            recommendation = self.llm_client.get_completion(
                user_question=user_question,
                context_solutions=context_solutions,
                chat_history=chat_history
            )
            
            return {
                "success": True,
                "recommendation": recommendation,
                "source_tickets": similar_tickets
            }
            
        except Exception as e:
            print(f"Error in search_and_recommend: {e}")
            return {
                "success": False,
                "error": str(e),
                "recommendation": "Sorry, I encountered an error while processing your question. Please try again.",
                "source_tickets": []
            }
    
    def get_ticket_details(self, ticket_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific ticket
        
        Args:
            ticket_id: The ID of the ticket
            
        Returns:
            Ticket details or None if not found
        """
        # This could be implemented to fetch full ticket details
        # For now, we'll return None as it's not immediately needed
        return None
    
    def format_source_tickets(self, tickets: List[Dict[str, Any]]) -> str:
        """
        Format source tickets for display
        
        Args:
            tickets: List of ticket dictionaries
            
        Returns:
            Formatted string representation of tickets
        """
        if not tickets:
            return "No source tickets found."
        
        formatted = "**Source Cases:**\n\n"
        for i, ticket in enumerate(tickets, 1):
            similarity_score = ticket.get('similarity_score', 0)
            similarity_percentage = (1 - similarity_score) * 100  # Convert distance to similarity percentage
            
            formatted += f"**Case {i}** (Similarity: {similarity_percentage:.1f}%)\n"
            formatted += f"- **Problem:** {ticket['problem'][:200]}{'...' if len(ticket['problem']) > 200 else ''}\n"
            formatted += f"- **Solution:** {ticket['solution'][:200]}{'...' if len(ticket['solution']) > 200 else ''}\n"
            if ticket['url']:
                formatted += f"- **URL:** {ticket['url']}\n"
            formatted += "\n"
        
        return formatted
