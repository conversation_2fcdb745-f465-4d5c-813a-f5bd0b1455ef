import requests
import json
from typing import List, Dict, Any
from config import LLM_CONFIG


class LLMClient:
    """Client for interacting with LLM services for embeddings and completions"""

    def __init__(self):
        self.base_url = LLM_CONFIG["base_url"]
        self.api_key = LLM_CONFIG["api_key"]
        self.completion_model = LLM_CONFIG["completion_model"]
        self.embedding_model = LLM_CONFIG["embedding_model"]
    
    def get_embedding(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for given texts
        
        Args:
            texts: List of text strings to embed
            
        Returns:
            List of embedding vectors
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.embedding_model,
            "input": texts
        }

        try:
            response = requests.post(
                f"{self.base_url}v1/embeddings",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            
            response_data = response.json()
            embeddings = [item['embedding'] for item in response_data['data']]
            return embeddings
            
        except requests.exceptions.RequestException as e:
            print(f"Error generating embeddings: {e}")
            return []
        except Exception as e:
            print(f"Error processing embedding response: {e}")
            return []
    
    def get_completion(self, user_question: str, context_solutions: List[str], chat_history: List[Dict[str, str]] = None) -> str:
        """
        Get completion from LLM with context from similar tickets
        
        Args:
            user_question: The user's question
            context_solutions: List of solution texts from similar tickets
            chat_history: Previous chat messages (optional)
            
        Returns:
            LLM response text
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # Prepare context from solutions
        context_text = "\n\n".join([f"Solution {i+1}: {solution}" for i, solution in enumerate(context_solutions)])
        
        # Build system prompt
        system_prompt = """You are an expert SAP support assistant. You help users solve SAP-related problems by providing recommendations based on similar cases from the knowledge base.

Your task is to:
1. Analyze the user's question carefully
2. Use the provided context from similar support cases to formulate a helpful response
3. Provide specific, actionable recommendations when possible
4. If the context doesn't contain relevant information, clearly state that and provide general guidance
5. Always be professional and helpful

Context from similar support cases:
{context}

Guidelines:
- Focus on practical solutions and steps
- Reference specific SAP components, transactions, or configurations when relevant
- If multiple solutions are provided in context, synthesize them appropriately
- Be clear about any limitations or prerequisites
- Suggest when to contact SAP support for complex issues"""

        formatted_system_prompt = system_prompt.format(context=context_text)
        
        # Build messages
        messages = [
            {"role": "system", "content": formatted_system_prompt}
        ]
        
        # Add chat history if provided
        if chat_history:
            messages.extend(chat_history)
        
        # Add current user question
        messages.append({"role": "user", "content": user_question})
        
        request_body = {
            "model": self.completion_model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 1000
        }

        try:
            response = requests.post(
                f"{self.base_url}v1/chat/completions",
                headers=headers,
                json=request_body,
                timeout=60
            )
            response.raise_for_status()

            response_data = response.json()
            if 'choices' in response_data and len(response_data['choices']) > 0:
                return response_data['choices'][0]['message']['content']
            else:
                return "Error: Invalid response format from LLM service"

        except requests.exceptions.RequestException as e:
            print(f"Error calling LLM API: {e}")
            return f"Error: Failed to get response from LLM service. Please try again."
        except Exception as e:
            print(f"Error processing LLM response: {e}")
            return f"Error: Failed to process LLM response. Please try again."
    
    def test_connection(self) -> bool:
        """Test if the LLM service is accessible"""
        try:
            # Test with a simple embedding request
            embeddings = self.get_embedding(["test"])
            return len(embeddings) > 0 and len(embeddings[0]) > 0
        except Exception as e:
            print(f"LLM service connection test failed: {e}")
            return False
