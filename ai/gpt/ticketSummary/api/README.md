# SAP Ticket Assistant

A Streamlit-based chatbot that provides SAP support recommendations based on vector similarity search of historical support tickets.

## Features

- 🔍 **Vector Similarity Search**: Finds the most relevant support cases based on semantic similarity
- 🧠 **AI-Powered Recommendations**: Uses GPT-4o to generate personalized solutions
- 💬 **Interactive Chat Interface**: Streamlit-based conversational UI
- 📚 **Source Case References**: Shows the original support cases used for recommendations
- 📊 **System Metrics**: Displays available case count and system status

## Architecture

The system consists of several components:

1. **Database Layer** (`database.py`): PostgreSQL connection and vector similarity queries
2. **LLM Client** (`llm_client.py`): Handles embeddings and completions via AI Core
3. **Vector Search** (`ticket_vector_search.py`): Main search and recommendation logic
4. **Streamlit App** (`streamlit_chatbot.py`): Web interface

## Setup

### Prerequisites

- Python 3.8+
- PostgreSQL with pgvector extension
- Access to AI Core LLM proxy service
- Populated `ticket_summary` table with embeddings

### Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure the `ticket_summary` table exists and contains data with embeddings

3. Verify database connection parameters in `database.py`

### Running the Application

```bash
streamlit run streamlit_chatbot.py
```

The application will be available at `http://localhost:8501`

## Configuration

### Database Connection
Update the connection parameters in `database.py`:
```python
connection = psycopg2.connect(
    user="your_user",
    password="your_password", 
    host="your_host",
    port="your_port",
    database="your_database"
)
```

### LLM Service
The LLM client is configured to use:
- **Base URL**: `https://aicore-llm-proxy.internal.cfapps.eu12.hana.ondemand.com/`
- **Completion Model**: `gpt-4o-2024-08-06`
- **Embedding Model**: `text-embedding-ada-002-2`

## Usage

1. **Start the Application**: Run the Streamlit app
2. **Ask Questions**: Type SAP-related questions in the chat input
3. **Get Recommendations**: The system will:
   - Generate embeddings for your question
   - Find similar support cases using vector search
   - Generate personalized recommendations using GPT-4o
   - Show source cases for reference

### Example Questions

- "How to resolve ABAP dump in transaction SE80?"
- "Custom code analysis showing errors after S/4HANA upgrade"
- "Performance issues with SAP HANA database queries"
- "Configuration problems with SAP Fiori launchpad"

## System Requirements

- **Database**: PostgreSQL with pgvector extension
- **Table Schema**: `ticket_summary` table with columns:
  - `id`: UUID primary key
  - `url`: Ticket URL
  - `problem`: Problem description
  - `solution`: Solution text
  - `cause`: Root cause
  - `processed_at`: Timestamp
  - `embedding`: Vector embedding (pgvector type)

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL service is running
   - Verify connection parameters
   - Ensure pgvector extension is installed

2. **No Similar Tickets Found**
   - Check if `ticket_summary` table has data
   - Verify embeddings are populated
   - Try more specific questions

3. **LLM Service Errors**
   - Check network connectivity to AI Core service
   - Verify API key is valid
   - Check service availability

### Debug Mode

To enable debug output, add print statements or use Python logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Development

### File Structure
```
api/
├── __init__.py
├── database.py              # Database operations
├── llm_client.py           # LLM service client
├── ticket_vector_search.py # Main search logic
├── streamlit_chatbot.py    # Streamlit application
├── requirements.txt        # Dependencies
└── README.md              # This file
```

### Adding Features

- **New Search Filters**: Modify `search_similar_tickets()` in `database.py`
- **Enhanced UI**: Update `streamlit_chatbot.py` with new Streamlit components
- **Different Models**: Change model names in `llm_client.py`
- **Custom Prompts**: Modify system prompts in `get_completion()` method
