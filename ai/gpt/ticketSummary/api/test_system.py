#!/usr/bin/env python3
"""
Test script for SAP Ticket Assistant system
"""

import sys
import os

# Add current directory to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from database import verify_database_connection, get_ticket_count
from llm_client import LLMClient
from ticket_vector_search import TicketVectorSearch


def test_database():
    """Test database connectivity and data availability"""
    print("🔍 Testing database connection...")
    
    try:
        if verify_database_connection():
            print("✅ Database connection successful")
            
            count = get_ticket_count()
            print(f"📊 Found {count} tickets with solutions in database")
            
            if count == 0:
                print("⚠️  Warning: No tickets with solutions found")
                return False
            
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


def test_llm_client():
    """Test LLM service connectivity"""
    print("\n🧠 Testing LLM service...")
    
    try:
        client = LLMClient()
        
        # Test embedding generation
        print("Testing embedding generation...")
        embeddings = client.get_embedding(["test query"])
        
        if embeddings and len(embeddings) > 0 and len(embeddings[0]) > 0:
            print(f"✅ Embedding generation successful (dimension: {len(embeddings[0])})")
        else:
            print("❌ Embedding generation failed")
            return False
        
        # Test completion
        print("Testing completion generation...")
        response = client.get_completion(
            user_question="Test question",
            context_solutions=["Test solution"]
        )
        
        if response and not response.startswith("Error"):
            print("✅ Completion generation successful")
            print(f"Sample response: {response[:100]}...")
            return True
        else:
            print(f"❌ Completion generation failed: {response}")
            return False
            
    except Exception as e:
        print(f"❌ LLM client test failed: {e}")
        return False


def test_vector_search():
    """Test the complete vector search system"""
    print("\n🔍 Testing vector search system...")
    
    try:
        search_system = TicketVectorSearch()
        print("✅ Vector search system initialized successfully")
        
        # Test search and recommendation
        print("Testing search and recommendation...")
        test_question = "How to resolve ABAP dump errors?"
        
        result = search_system.search_and_recommend(test_question)
        
        if result["success"]:
            print("✅ Search and recommendation successful")
            print(f"Found {len(result['source_tickets'])} similar tickets")
            print(f"Recommendation preview: {result['recommendation'][:150]}...")
            return True
        else:
            print(f"❌ Search and recommendation failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Vector search test failed: {e}")
        return False


def main():
    """Run all system tests"""
    print("🚀 Starting SAP Ticket Assistant System Tests\n")
    
    tests = [
        ("Database", test_database),
        ("LLM Client", test_llm_client),
        ("Vector Search", test_vector_search)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📋 TEST SUMMARY")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:15} {status}")
        if not passed:
            all_passed = False
    
    print("="*50)
    
    if all_passed:
        print("🎉 All tests passed! System is ready to use.")
        print("\nTo start the Streamlit app, run:")
        print("streamlit run streamlit_chatbot.py")
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        print("\nCommon fixes:")
        print("- Ensure PostgreSQL is running and accessible")
        print("- Verify ticket_summary table has data with embeddings")
        print("- Check LLM service connectivity")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
