import psycopg2
from typing import List, Dict, Any, Optional
from config import DATABASE_CONFIG


def get_database_connection():
    """Establish connection to PostgreSQL database"""
    try:
        connection = psycopg2.connect(**DATABASE_CONFIG)
        return connection
    except (Exception, psycopg2.Error) as error:
        print(f"Error connecting to PostgreSQL database: {error}")
        return None


def search_similar_tickets(query_embedding: List[float], limit: int = 5) -> List[Dict[str, Any]]:
    """
    Search for similar tickets based on vector similarity
    
    Args:
        query_embedding: The embedding vector of the user's query
        limit: Number of top similar tickets to return (default: 3)
    
    Returns:
        List of dictionaries containing ticket information
    """
    try:
        connection = get_database_connection()
        if connection is None:
            return []

        cursor = connection.cursor()

        # Query to find most similar tickets using cosine similarity
        # The <=> operator in pgvector represents cosine distance
        find_similar_query = """
        SELECT id, url, problem, solution, cause, processed_at, 
               (embedding <=> %s) as similarity_score
        FROM ticket_summary 
        WHERE solution IS NOT NULL AND solution != ''
        ORDER BY embedding <=> %s 
        LIMIT %s
        """

        cursor.execute(find_similar_query, (str(query_embedding), str(query_embedding), limit))
        results = cursor.fetchall()

        # Convert results to list of dictionaries
        tickets = []
        for row in results:
            ticket = {
                'id': row[0],
                'url': row[1],
                'problem': row[2],
                'solution': row[3],
                'cause': row[4],
                'processed_at': row[5],
                'similarity_score': float(row[6])
            }
            tickets.append(ticket)

        return tickets

    except (Exception, psycopg2.Error) as error:
        print(f"Failed to search similar tickets: {error}")
        return []
    finally:
        if connection:
            cursor.close()
            connection.close()


def get_ticket_count() -> int:
    """Get the total number of tickets in the database"""
    try:
        connection = get_database_connection()
        if connection is None:
            return 0

        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM ticket_summary WHERE solution IS NOT NULL AND solution != ''")
        count = cursor.fetchone()[0]
        return count

    except (Exception, psycopg2.Error) as error:
        print(f"Failed to get ticket count: {error}")
        return 0
    finally:
        if connection:
            cursor.close()
            connection.close()


def verify_database_connection() -> bool:
    """Verify that the database connection and table are accessible"""
    try:
        connection = get_database_connection()
        if connection is None:
            return False

        cursor = connection.cursor()
        
        # Check if table exists and has data
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'ticket_summary'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        if not table_exists:
            print("Error: ticket_summary table does not exist")
            return False

        # Check if table has records with embeddings
        cursor.execute("SELECT COUNT(*) FROM ticket_summary WHERE embedding IS NOT NULL")
        embedding_count = cursor.fetchone()[0]
        
        if embedding_count == 0:
            print("Warning: No records with embeddings found in ticket_summary table")
            return False

        print(f"Database verification successful. Found {embedding_count} records with embeddings.")
        return True

    except (Exception, psycopg2.Error) as error:
        print(f"Database verification failed: {error}")
        return False
    finally:
        if connection:
            cursor.close()
            connection.close()
