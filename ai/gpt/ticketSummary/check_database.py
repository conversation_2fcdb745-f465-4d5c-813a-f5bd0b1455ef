import psycopg2

def get_database_connection():
    """Establish connection to PostgreSQL database"""
    try:
        connection = psycopg2.connect(
            user="184024b4abe8",
            password="cce6e3ded99172321cf1dd7e681ae92",
            host="127.0.0.1",
            port="8861",
            database="CxgapCTAJQku"
        )
        return connection
    except (Exception, psycopg2.Error) as error:
        print(f"Error connecting to PostgreSQL database: {error}")
        return None

def check_ticket_summary_table():
    """Check the current status of ticket_summary table"""
    try:
        connection = get_database_connection()
        if connection is None:
            return
            
        cursor = connection.cursor()
        
        # Check table structure
        print("=== Table Structure ===")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'ticket_summary'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        for column in columns:
            print(f"{column[0]}: {column[1]} ({'NULL' if column[2] == 'YES' else 'NOT NULL'})")
        
        # Check record count
        print("\n=== Record Count ===")
        cursor.execute("SELECT COUNT(*) FROM ticket_summary;")
        count = cursor.fetchone()[0]
        print(f"Total records: {count}")
        
        # Show sample records
        if count > 0:
            print("\n=== Sample Records ===")
            cursor.execute("""
                SELECT id, url, LEFT(problem, 100) as problem_preview, processed_at 
                FROM ticket_summary 
                ORDER BY processed_at DESC 
                LIMIT 5;
            """)
            
            records = cursor.fetchall()
            for record in records:
                print(f"ID: {record[0]}")
                print(f"URL: {record[1]}")
                print(f"Problem: {record[2]}...")
                print(f"Processed: {record[3]}")
                print("-" * 50)
        
    except (Exception, psycopg2.Error) as error:
        print(f"Error checking table: {error}")
    finally:
        if connection:
            cursor.close()
            connection.close()

if __name__ == "__main__":
    check_ticket_summary_table()
