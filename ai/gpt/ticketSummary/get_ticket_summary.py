import csv
import json
import requests
import time
from datetime import datetime


def get_summary(ticket_content):
    base_url = "https://aicore-llm-proxy.internal.cfapps.eu12.hana.ondemand.com/"
    api_key = "sk-58f4db0abc225fb9fa70a80e71ec732bfd086076ddc44c83"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    request_body = {
        "model": "gpt-4o-2024-08-06",
        "messages": [
            {
                "role": "system",
                "content": "You are an expert SAP support analyst. Analyze the customer support ticket and identify ALL distinct issue combinations mentioned "
                           "then provide concise, professional summaries. Focus on identifying the main issue, "
                           "key symptoms, resolution steps taken, and final outcome. Keep the summary clear and "
                           "structured, highlighting the most important technical details and business impact. "
                           "Normally only 1 problem is mentioned in a ticket, only add a new group if an issue related to a totally different area is mentioned.\n\n"
                           "Each combination should include a Problem, its corresponding Solution, and the Cause.\n\n"
                           "Output your response as pure JSON without any markdown formatting, code block indicators, or additional text. "
                           "Return only a valid JSON array with the following structure:\n\n"
                           "[\n"
                           "  {\n"
                           "    \"problem\": \"String type: A precise and short summary about the main issue or problem reported by the customer.\",\n"
                           "    \"solution\": \"String type, not array: Describe solution provided step by step. Use sorted list when steps more than 1.\",\n"
                           "    \"cause\": \"String type: Root cause for this specific problem.\"\n"
                           "  }\n"
                           "]\n\n"
                           "Important: \n"
                           "- Each object must contain problem, solution, and cause fields\n"
                           "- Problem and Solution are required. If Cause is missing for related Problem, use empty string\n"
                           "- Only create objects when there is at least a Problem identified\n"
                           "- Extract ALL distinct problem-solution-cause combinations from the ticket\n"
                           "- Return valid JSON array only, no additional text or formatting"
            },
            {
                "role": "user",
                "content": f"Please analyze this SAP support ticket and provide the structured response:\n\n{ticket_content}"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 1000
    }

    try:
        response = requests.post(f"{base_url}v1/chat/completions",
                               headers=headers,
                               json=request_body,
                               timeout=30)
        response.raise_for_status()

        response_data = response.json()
        if 'choices' in response_data and len(response_data['choices']) > 0:
            return response_data['choices'][0]['message']['content']
        else:
            print(f"Response格式异常: {response_data}")
            return "Error: Invalid response format from AI Core"

    except requests.exceptions.RequestException as e:
        print(f"AI Core API调用失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"响应状态码: {e.response.status_code}")
            print(f"响应内容: {e.response.text}")
        return f"Error calling AI Core API: {str(e)}"
    except Exception as e:
        print(f"处理AI Core响应时出错: {e}")
        return f"Error processing AI Core response: {str(e)}"


def parse_structured_summary(response_text):
    """解析LLM返回的JSON数组响应，提取Problem-Solution-Cause组合"""
    result = []

    if not response_text:
        return result

    try:
        # 尝试直接解析JSON数组
        parsed_json = json.loads(response_text.strip())

        # 验证JSON结构 - 应该是一个数组
        if isinstance(parsed_json, list):
            # 验证每个元素的结构
            for item in parsed_json:
                if isinstance(item, dict):
                    # 确保每个item都有必需的字段
                    validated_item = {
                        "problem": item.get("problem", ""),
                        "solution": item.get("solution", ""),
                        "cause": item.get("cause", "")
                    }
                    # 只有当problem不为空时才添加
                    if validated_item["problem"].strip():
                        result.append(validated_item)
            return result

        # 如果JSON结构不符合预期，返回空结果
        print(f"⚠️  JSON结构不符合预期，期望数组但得到: {type(parsed_json)}")
        return result

    except json.JSONDecodeError as e:
        print(f"⚠️  JSON解析失败: {e}")
        print(f"原始响应: {response_text[:200]}...")

        # 如果JSON解析失败，尝试提取可能的JSON部分
        try:
            # 寻找可能的JSON数组开始和结束标记
            start_idx = response_text.find('[')
            end_idx = response_text.rfind(']')

            if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
                json_part = response_text[start_idx:end_idx + 1]
                parsed_json = json.loads(json_part)

                if isinstance(parsed_json, list):
                    for item in parsed_json:
                        if isinstance(item, dict):
                            validated_item = {
                                "problem": item.get("problem", ""),
                                "solution": item.get("solution", ""),
                                "cause": item.get("cause", "")
                            }
                            if validated_item["problem"].strip():
                                result.append(validated_item)
                    return result
        except:
            pass

        return result
    except Exception as e:
        print(f"⚠️  解析响应时出错: {e}")
        return result


def get_parsed_summary(ticket_content):
    try:
        ticket_summary = get_summary(ticket_content)
        return parse_structured_summary(ticket_summary)
    except Exception as e:
        print(f"AI Core API调用失败: {e}")
        # 返回空的结构化结果
        return []



def process_tickets(csv_file_path, output_file_path):
    """处理tickets并生成分析结果"""
    results = []
    processed_count = 0
    error_count = 0

    print(f"开始处理文件: {csv_file_path}")
    print(f"输出文件: {output_file_path}")

    try:
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            total_rows = sum(1 for row in reader)
            csvfile.seek(0)
            reader = csv.DictReader(csvfile)

            print(f"总共需要处理 {total_rows} 条tickets")

            for row_num, row in enumerate(reader, 1):
                try:
                    url = row['url']
                    title = row['title']
                    text = row['text']

                    print(f"正在处理第 {row_num}/{total_rows} 条ticket: {title[:50]}...")

                    # 组合ticket内容
                    ticket_content = f"Title: {title}\n\nContent: {text}"

                    # 获取分析结果
                    structured_summary = get_parsed_summary(ticket_content)

                    # 只有当存在有效组合时才创建对象
                    if structured_summary:
                        for item in structured_summary:
                            result = {
                                "url": url,
                                "title": title,
                                "problem": item.get("problem", ""),
                                "solution": item.get("solution", ""),
                                "cause": item.get("cause", ""),
                                "processed_at": datetime.now().isoformat()
                            }
                            results.append(result)
                    else:
                        # 如果没有找到任何组合，记录但不创建对象
                        print(f"  ⚠️  未找到有效的Problem-Solution-Cause组合")

                    processed_count += 1

                    print(f"✓ 完成第 {row_num} 条ticket的分析")

                    # 每处理10条记录就保存一次，防止数据丢失
                    if processed_count %  5 == 0:
                        save_results(results, output_file_path)
                        print(f"已保存前 {processed_count} 条记录到文件")

                    # 添加延迟避免API限制
                    time.sleep(1)

                except Exception as e:
                    error_count += 1
                    print(f"✗ 处理第 {row_num} 条ticket时出错: {str(e)}")
                    continue

    except FileNotFoundError:
        print(f"错误: 找不到文件 {csv_file_path}")
        return
    except Exception as e:
        print(f"读取CSV文件时出错: {str(e)}")
        return

    # 最终保存所有结果
    save_results(results, output_file_path)

    print(f"\n处理完成!")
    print(f"成功处理: {processed_count} 条tickets")
    print(f"处理失败: {error_count} 条tickets")
    print(f"结果已保存到: {output_file_path}")


def save_results(results, output_file_path):
    try:
        with open(output_file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(results, jsonfile, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存文件时出错: {str(e)}")


if __name__ == '__main__':
    csv_file_path = "RC Case_top500.csv"
    output_file_path = f"ticket_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

    try:
        process_tickets(csv_file_path, output_file_path)
    except KeyboardInterrupt:
        print("\n⚠️  处理被用户中断")
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")

    print("\n✅ 处理完成！")
