[{"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "Difference in impacted custom objects between S/4HANA 2023 FP01 and FP02.", "solution": "Analyze the SAP_S4U_2 ATC runs for both FP01 and FP02. Identify changes in custom objects ZCL_FI_GL_UPLOAD_DPC_EXT and ZFI_REPT_BPAY_CREDIT between the runs. Provide explanation based on object changes and confirm no changes should result in identical lists.", "cause": "Changes in specific custom objects between the two ATC runs.", "processed_at": "2025-06-25T15:56:03.124323"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "Mismatch in expected subset relationship of impacted objects between FP01 and FP02.", "solution": "Explain that impacted custom objects for FPS01 and FPS02 should be the same if no changes were made between runs. Confirm with customer that any differences are due to changes in objects.", "cause": "Objects recorded in the simplification DB with validity constraints may differ between FPS01 and FPS02, impacting results.", "processed_at": "2025-06-25T15:56:03.124331"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "Clarification needed on consideration of impacted objects when upgrading directly to FP02.", "solution": "Confirm with the customer that if upgrading directly to FP02, only the impacted objects for FP02 need to be considered.", "cause": "", "processed_at": "2025-06-25T15:56:03.124332"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bf5bb1c337d61022195030a0013184", "title": "Queries regarding Custom Code Analysis for S/4HANA 2023 FP01 and FP02", "problem": "ATC checks configured only for specific releases, not feature packs.", "solution": "Inform customer that ATC checks can only be configured for specific releases, not feature packs. Explain that results differ due to this configuration limitation.", "cause": "ATC checks are limited to specific releases, causing differences in results based on feature pack selection.", "processed_at": "2025-06-25T15:56:03.124334"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e3c57b897e75ed8b86630200153af15", "title": "Readiness Check uploaded file is not valid", "problem": "Customer attempted to upload an incorrect data collection file for SAP Readiness Check, resulting in an invalid file error.", "solution": "Clarified the difference between RC_VALUE_DISCOVERY_COLL_DATA and the required SAP Readiness Check Data Collector report. Provided guidance on using the correct report for the intended check, including referencing the relevant SAP Note 2913617 for necessary steps and prerequisites.", "cause": "Customer mistakenly used RC_VALUE_DISCOVERY_COLL_DATA instead of the SAP Readiness Check Data Collector.", "processed_at": "2025-06-25T15:56:08.364989"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ee2039e1c36f5e9c13c299bc7a01318b", "title": "Cannot Access Readiness Check for Results from 3193560", "problem": "Unable to upload results to SAP Readiness Check due to 'connection reset' error.", "solution": "1. Verify S-user has necessary authorizations as per SAP Note 3310759. 2. Use incognito mode or deactivate third-party cookie blocking in browser settings. 3. If unable to change browser settings, open a new browser session, log in using S-user, and access the Readiness Check landing page.", "cause": "Either lack of necessary authorizations for the S-user or browser blocking third-party cookies.", "processed_at": "2025-06-25T15:56:12.913348"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "SAP Readiness Check: Custom code analysis jobs are failing with short dumps.", "solution": "1. Review SM37 for job RC_COLLECT_ANALYSIS_DATA and ST22 for short dumps. 2. Assign missing authorization 'S_Q_GOVERN' to executing user. 3. Check job logs for errors and confirm successful completion after authorization update.", "cause": "Missing authorization for executing user.", "processed_at": "2025-06-25T15:56:18.503177"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "LOAD_PROGRAM_CLASS_MISMATCH error in ABAP dumps.", "solution": "1. Regenerate classes CL_ABAP_COMPILER and CL_CI_TEST_ABAP_COMPILER by activating in transaction SE24. 2. Ensure SAP Note 2436688 and other related SAP Notes are correctly implemented.", "cause": "Inconsistencies in the class interface version used by programs at runtime.", "processed_at": "2025-06-25T15:56:18.503185"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbd44da3c3e31adcecd47d9f05013128", "title": "SAP Readiness Check : Custom code Analysis jobs are failing", "problem": "Syntax error in SAPLSTRD causing ABAP dumps in system MWD.", "solution": "Fix the syntax error in function group STRD with the help of the ABAP team.", "cause": "Programming error leading to runtime syntax error in SAPLSTRD.", "processed_at": "2025-06-25T15:56:18.503186"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "Question if SAP Readiness Check results can be integrated into SAP Cloud ALM for follow-up items.", "solution": "Reviewed SAP documentation and confirmed integration capabilities. Directed customer to SAP help link for further details.", "cause": "Customer uncertainty about integration capabilities of SAP Readiness Check with SAP Cloud ALM.", "processed_at": "2025-06-25T15:56:22.971753"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5b8b17a7c3631a503331583bb0013195", "title": "Readiness check integration to Cloud ALM - SAP ECC upgrades?", "problem": "Clarification on whether Readiness Check supports ECC upgrades before S/4HANA transformation.", "solution": "Confirmed through SAP notes that Readiness Check is not supported for ECC upgrades.", "cause": "Readiness Check is designed for ECC to S/4HANA or S/4HANA upgrades, not for ECC version upgrades.", "processed_at": "2025-06-25T15:56:22.971779"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/46b50a1797eb9a10b86630200153af59", "title": "No value was passed to the mandatory parameter \"PCT_DETAIL_LIST\"", "problem": "ABAP dump due to missing mandatory parameter 'PCT_DETAIL_LIST' when executing report RC_COLLECT_ANALYSIS_DATA.", "solution": "1. De-implement SAP Notes 2758146 and 2745851. 2. Re-implement the notes in their latest versions and in the specified order. 3. Use SAP Note 3205320 to run report ZNOTE_2745851_REPAIR for syntax errors. 4. Re-check the report RC_COLLECT_ANALYSIS_DATA execution.", "cause": "Old coding not updated during previous SAP Note implementations.", "processed_at": "2025-06-25T15:56:27.573895"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cbe45875c3a71ed4553db11f0501319d", "title": "Not able to upload the readiness analysis file in manage analysis files app", "problem": "Not able to upload the readiness analysis file in manage analysis files app due to format issues.", "solution": "1. Review SAP Note 3275056 for instructions on generating the analysis file. 2. Identify that the program RC_UDP_START_DMR2 is missing and is intended for SAP_APPL systems, not S4 HANA. 3. Request customer to provide the Readiness Check data collection ZIP file for review. 4. Request screenshot of the error message referencing SAP Note 3275056.", "cause": "The readiness analysis file format is not supported by the Manage Analysis File app, and SAP Note 3275056 is intended for SAP_APPL systems rather than S4 HANA.", "processed_at": "2025-06-25T15:56:32.181633"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/79fdf13f977b1a5030c531f71153afae", "title": "Readiness Check WebView error", "problem": "Readiness Check WebView error displaying incorrect data compared to Excel download.", "solution": "The display issue was identified in the application web view while the data in Excel was correct. A fix was implemented and deployed to production, allowing the customer to view the correct numbers in the application web view.", "cause": "Display issue in the application web view causing discrepancies between web view and Excel data.", "processed_at": "2025-06-25T15:56:36.082370"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0f499dc03b0c6e508c36d62a85e45abc", "title": "User's are unable to accesses the SAP Readinesscheck.", "problem": "Users are unable to access the SAP Readiness Check portal, encountering a blank page.", "solution": "Refer to SAP KBA 3330283 for resolution steps on this issue, which includes checking potential technical configuration errors that could lead to a blank screen despite proper authorizations.", "cause": "A completely blank page is not due to missing authorizations but possibly technical issues preventing proper loading of the portal.", "processed_at": "2025-06-25T15:56:40.508508"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/877262d097cce6907bcf59081153af35", "title": "<PERSON><PERSON><PERSON>heck,", "problem": "Syntax error in FIN_CORR_DISPLAY_ISSUE program preventing activation, resulting in termination of TMW_RC_FDQ_DATA_COLL job.", "solution": "Import the syntax error program or class using Note **********. If errors are not present in LBD development system, transport healthy programs or classes from LBD to resolve errors.", "cause": "Improper user handling.", "processed_at": "2025-06-25T15:56:44.180705"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/96b50a1797eb9a10b86630200153af61", "title": "How to recover alm_sys_add_info.xsl and alm_sys_header.xsl", "problem": "The MIME objects 'alm_sys_add_info.xsl' and 'alm_sys_header.xsl' were accidentally deleted by the customer.", "solution": "Re-implement SAP Note 3236443 to restore the deleted files.", "cause": "Files were deleted following procedures in SAP Note 3308795.", "processed_at": "2025-06-25T15:56:48.929677"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/96b50a1797eb9a10b86630200153af61", "title": "How to recover alm_sys_add_info.xsl and alm_sys_header.xsl", "problem": "Customer unsure if 'alm_sys_add_info.xsl' and 'alm_sys_header.xsl' need to be restored for SAP Readiness Check for SAP Cloud ALM.", "solution": "Clarified that the files are not needed if SAP Readiness Check for SAP Cloud ALM is not required.", "cause": "Misunderstanding regarding the necessity of the files for the current system setup.", "processed_at": "2025-06-25T15:56:48.929700"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/25b70621c3bf169022195030a00131d5", "title": "SAP S/4HANA Readiness Check- Finance Data Quality Job Error \"SAPSQL_PARSE_ERROR\"", "problem": "SAP S/4HANA Readiness Check - Finance Data Quality Job Error 'SAPSQL_PARSE_ERROR'.", "solution": "Reviewed related SAP Knowledge Base Article 3426918 and followed the resolution steps provided in the article.", "cause": "SAPSQL_PARSE_ERROR due to a programming error in ABAP, specifically a dynamic SQL semantic error in method GET_MAX_RUNID.", "processed_at": "2025-06-25T15:56:52.769657"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/22e49217ebef5ad87d05f7510bd0cd8c", "title": "Readiness Check issue", "problem": "Dump error occurs when executing the RC_COLLECT_ANALYSIS_DATA program.", "solution": "Refer to KBA 3376638 which addresses the exception SMDB_CONTNET_NOT_FOUND triggered during report execution.", "cause": "Empty S/4HANA conversion target release.", "processed_at": "2025-06-25T15:56:56.637162"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0e586312c37b5650b118f71ad0013156", "title": "Definition for job RC_COLLECT_ANALYSIS_DATA is incomplete. Job cannot be released", "problem": "Definition for job RC_COLLECT_ANALYSIS_DATA is incomplete, preventing it from being released or executed.", "solution": "Assign the S_DEVELOP authorization role to the user and ensure the respective profile is generated.", "cause": "The user scheduling the data collection lacks the required authorizations.", "processed_at": "2025-06-25T15:57:01.545730"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0e586312c37b5650b118f71ad0013156", "title": "Definition for job RC_COLLECT_ANALYSIS_DATA is incomplete. Job cannot be released", "problem": "Error message 'Object RC_COLLECT_ANALYSIS_DATA of class RE does not exist in language EN' prevents job release.", "solution": "Follow resolution steps provided in SAP Note 0003540750 related to the error message.", "cause": "Potential mismatch in language settings or object definitions in the system.", "processed_at": "2025-06-25T15:57:01.545752"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cff846a9c3bf169022195030a0013185", "title": "During the execution we got a dump and the job is aborted", "problem": "ABAP program termination due to OBJECTS_OBJREF_NOT_ASSIGNED_NO runtime error.", "solution": "1. Verify latest version of SAP note 2758146 is implemented. 2. Implement SAP note 3233821. 3. If issue persists, de-implement note 2758146 and then implement it again.", "cause": "Access using a 'NULL' object reference in the ABAP program CL_CVI_READINESS_CHECK.", "processed_at": "2025-06-25T15:57:05.443213"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/aa4d3577977b1a5030c531f71153afb3", "title": "SAP Readiness check - Error in SAP Fiori Apps report", "problem": "Error in SAP Readiness Check with Recommended SAP Fiori Apps not populating.", "solution": "Review the Readiness Check data collection ZIP file. Ensure ST03 on the relevant system holds data for at least the last 3 finished months. Perform the Readiness Check data collection on a production system.", "cause": "The transaction_usage.xml file lacked relevant data, indicating ST03 on system ECD didn't hold historical workload data, common for test/demo systems.", "processed_at": "2025-06-25T15:57:11.178641"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/aa4d3577977b1a5030c531f71153afb3", "title": "SAP Readiness check - Error in SAP Fiori Apps report", "problem": "No data in bpa.xml file after running RC_COLLECT_ANALYSIS_DATA report.", "solution": "Check authorization settings. Assign authorization object SM_BPM_DET with 'OBJECT_MS' characteristic to the user executing the report.", "cause": "Job log showed 'No authorization for object', indicating missing authorizations for collected KPIs.", "processed_at": "2025-06-25T15:57:11.178661"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/aa4d3577977b1a5030c531f71153afb3", "title": "SAP Readiness check - Error in SAP Fiori Apps report", "problem": "Target S/4HANA Version list not populating in production client during Readiness check report execution.", "solution": "Execute the program RC_COLLECT_ANALYSIS_DATA in the production client as recommended.", "cause": "", "processed_at": "2025-06-25T15:57:11.178669"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/86e5deadc377569022195030a00131e2", "title": "Readniess Check/App Availability shows an ERROR", "problem": "Readiness Check Analysis in SAP for Me shows an error with certain sections not populated, specifically 'App Availability'.", "solution": "Update SAP Note 0002758146 to version 122 and verify App Availability analysis afterwards. Provide access to production client and ensure valid logon credentials are maintained.", "cause": "Zero file size of 'usage_data.xml' indicating issues during data collection or transaction ST03.", "processed_at": "2025-06-25T15:57:15.230132"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/949c5010c3ff1a1413c299bc7a0131c8", "title": "Dump XSLT_BAD_SOURCE_CONTEXT during the RC_BW_COLLECT_ANALYSIS_DATA  report", "problem": "Dump XSLT_BAD_SOURCE_CONTEXT occurs during execution of the RC_BW_COLLECT_ANALYSIS_DATA report in SAP NetWeaver 7.5.", "solution": "Refer to SAP Knowledge Base Article 2822563 for resolution steps regarding the XSLT_BAD_SOURCE_CONTEXT Runtime Error during SAP Readiness Check.", "cause": "Error caused by issues outlined in SAP Note 2822563 related to SAP Readiness Check procedures.", "processed_at": "2025-06-25T15:57:19.939960"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d454dbc8eb882e50a95ff847bad0cd95", "title": "Readiness check in CALM: Not possible to create follow up items", "problem": "Unable to create follow-up items in readiness check for ECC conversion to S4HANA integration with Cloud ALM.", "solution": "Enable SAP S/4HANA Conversion tile in Cloud ALM to view conversion analysis list.", "cause": "Integration issue between SAP Readiness Check and Cloud ALM preventing toggle of user stories and requirements.", "processed_at": "2025-06-25T15:57:23.979250"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/91b63ca3c3efda908710df00a0013175", "title": "RC_COLLECT_ANALYSIS_DATA Job cancelled", "problem": "RC_COLLECT_ANALYSIS_DATA job cancellation due to STRING_OFFSET_TOO_LARGE dump.", "solution": "To resolve the STRING_OFFSET_TOO_LARGE dump issue: 1. Analyze the ABAP code related to RC_COLLECT_ANALYSIS_DATA report for any string manipulations causing large offset. 2. Adjust the coding logic to handle string operations properly. 3. Test the report execution again to ensure successful completion.", "cause": "The job cancellation was caused by improper handling of string offsets in the ABAP code, resulting in the STRING_OFFSET_TOO_LARGE dump.", "processed_at": "2025-06-25T15:57:28.509485"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/52020eb73bef16d80870d44a85e45afd", "title": "Job RC_COLLECT_ANALYSIS_DATA in error", "problem": "Job RC_COLLECT_ANALYSIS_DATA terminated with an error message related to invalid bank account number.", "solution": "Apply the latest version 122 of SAP Readiness Check framework Note 2758146, perform pre-manual steps in Note 3010669, apply Note 2811183, and reapply Note 3010669. Open the R/3 connection for RGQ and provide authorized logon information.", "cause": "Missing configuration.", "processed_at": "2025-06-25T15:57:32.486025"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5e5019b0c3f392548710df00a001313b", "title": "Analysis note for SAP Readiness Check for upgrades S4hana", "problem": "Inquiry on necessity and implementation of SAP Note 3059197 for SAP Readiness Check for S4HANA upgrades.", "solution": "Confirm necessity to follow steps in SAP Note 3059197 for Readiness Check, emphasizing 'Reason and Prerequisites'. Clarify no implementation via SNOTE needed as the note is 'read only' without corrections.", "cause": "Customer uncertainty on whether the SAP Note 3059197 needs to be applied or implemented for accurate Readiness Check results.", "processed_at": "2025-06-25T15:57:36.777614"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/06255b40ebc82e50a95ff847bad0cd55", "title": "RC_UDP_COLLECT_ANALYSIS_DATA hängt", "problem": "RC_UDP_COLLECT_ANALYSIS_DATA job hangs and does not complete.", "solution": "Create indices for table LTBK and LTBP on field TABNUM in all affected systems. Update table and index statistics in all affected systems. Run the analysis again.", "cause": "The long-running job and its cancellation are caused by the SQL statement analyzing the Warehouse Management Transport Requirement tables LTBP and LTBK.", "processed_at": "2025-06-25T15:57:41.274246"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6fc9debfebbb16504c89f02dcad0cdbd", "title": "Unable to implement note 2811183", "problem": "Unable to implement SAP note 2811183 due to warning 'Object already exists and will be overwritten'.", "solution": "Ignore the warning message 'Object already exists and will be overwritten', and proceed with implementation by setting a check mark in front of the line.", "cause": "The object SAPLCVI_READINESS_CHECK already exists in the system and is flagged to be overwritten during note implementation.", "processed_at": "2025-06-25T15:57:45.273800"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1d60005ec373525013c299bc7a0131af", "title": "sap readiness home not working - https://me.sap.com/readinesscheck", "problem": "User cannot access the SAP Readiness Check portal due to a 'Connection reset' error.", "solution": "User was advised to access the portal via a VPN and download a HAR file for further analysis. Issue resolved as it was identified as a network issue on the user's end.", "cause": "Network issue on the user's side causing connection reset.", "processed_at": "2025-06-25T15:57:50.140874"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8040801ec373525013c299bc7a01314d", "title": "Recommended SAP Fiori Apps error in S/4 Hana readiness check", "problem": "Recommended SAP Fiori Apps check error in S/4 Hana readiness check.", "solution": "Implement SAP Note 2568736 as recommended in KBA 3346997 to address the technical error in SAP Readiness Check Report.", "cause": "Technical error in SAP Readiness Check Report preventing execution of the Recommended SAP Fiori Apps check.", "processed_at": "2025-06-25T15:57:53.667580"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d29a229747f31a10cb4df2b5536d436f", "title": "Readiness Check Upload Fails", "problem": "Readiness Check upload fails due to invalid file error.", "solution": "The development team has adjusted the upload process to accommodate larger files. The customer was advised to use a newly provided file for uploading.", "cause": "The issue was caused due to the size of the data collection file.", "processed_at": "2025-06-25T15:57:58.440058"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0113af84c30ca2900c443bcd2b0131d7", "title": "Running Report \"RC_VALUE_DISCOVERY_COLL_DATA\" shows different screen / selection option compared to \"*PERSON*\"", "problem": "Running the report 'RC_VALUE_DISCOVERY_COLL_DATA' in transaction SE38 shows different screen/selection options compared to the SAP Signavio Process Insights, Discovery Edition How-To Guide.", "solution": "Update SAP Note 2758146 to its latest version. If the issue persists, provide access to the production client of system FND and maintain valid logon credentials in the customer remote logon depot.", "cause": "The report configuration may have been outdated, requiring the implementation of a later version of the SAP Note.", "processed_at": "2025-06-25T15:58:03.262313"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2f059340ebc82e50a95ff847bad0cd07", "title": "Cannot access the check readiness page", "problem": "Customer cannot access the SAP Readiness Check Portal due to outdated URL.", "solution": "Customer was advised to use the correct URL: https://me.sap.com/readinesscheck/home. Ensure the address shown in the browser's URL matches the correct link.", "cause": "The link used by the customer pointed to an outdated URL: https://itsm.services.sap/readinesscheck/home.", "processed_at": "2025-06-25T15:58:08.295288"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2f059340ebc82e50a95ff847bad0cd07", "title": "Cannot access the check readiness page", "problem": "Customer needs authorization for SAP Readiness Check in SAP for Me.", "solution": "Customer was directed to contact their company's Super-Admin-S-User or a User-Admin-S-User to assign the necessary roles via the link provided in SAP Note 0003310759.", "cause": "Authorization roles for the Readiness Check Portal need to be assigned by the company's admin users, which cannot be done by SAP.", "processed_at": "2025-06-25T15:58:08.295309"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/351473ef3b7752500b700f3a85e45a7a", "title": "Dropdown list empty to select the Traget S/4 HANA version while running the report RC_COLLECT_ANALYSIS_DATA", "problem": "Dropdown list is empty when selecting the target SAP S/4 HANA version while running the report RC_COLLECT_ANALYSIS_DATA.", "solution": "Follow the steps outlined in SAP KBA 0002951527 to populate the 'Target Release' drop-down menu in the report RC_COLLECT_ANALYSIS_DATA. Ensure steps are performed correctly to show all target releases relevant to the current release.", "cause": "The drop-down menu does not show the release the system is currently on as a target release for a conversion/upgrade project.", "processed_at": "2025-06-25T15:58:14.460081"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/351473ef3b7752500b700f3a85e45a7a", "title": "Dropdown list empty to select the Traget S/4 HANA version while running the report RC_COLLECT_ANALYSIS_DATA", "problem": "Attempt to perform Custom Code Analysis after upgrading Sandbox system from S3HANA2021 FP02 to S4HANA 2023 FP01 version using report RC_BW_COLLECT_ANALYSIS_DATA.", "solution": "Note that Custom Code Analysis should be performed before the upgrade on the source/current release. Additionally, RC_BW_COLLECT_ANALYSIS_DATA is only applicable for BW systems, not S/4HANA.", "cause": "Incorrect report used for S/4HANA system and Custom Code Analysis needs to be done before the upgrade.", "processed_at": "2025-06-25T15:58:14.460133"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8d38023e1b09a5189778859c7b4bcb31", "title": "SYNTAX_ERROR with report RC_COLLECT_ANALYSIS_DATA", "problem": "SYNTAX_ERROR with report RC_COLLECT_ANALYSIS_DATA due to exception condition 'SMDB_CONTNET_NOT_FOUND'.", "solution": "Manually download SIC content and upload it in the system by referring to SAP KBA 2951527.", "cause": "SUPPORT_PORTAL connection is not set up, preventing SIC from being downloaded.", "processed_at": "2025-06-25T15:58:18.761895"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f898ed231bade594f46096859b4bcb36", "title": "S4HANA 2021 version is not showing in the SAP Readiness and Simplification Item check Report.", "problem": "S4HANA 2021 version is not showing in the SAP Readiness and Simplification Item check Report in the production system.", "solution": "Reset and re-implement SAP Note 2399707 as per latest version; follow recommendations from SAP blog; update Simplification Item Catalog as per KBA 2951527.", "cause": "Incomplete implementation of SAP Note 2399707; potential outdated Simplification Item Catalog.", "processed_at": "2025-06-25T15:58:23.166429"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f898ed231bade594f46096859b4bcb36", "title": "S4HANA 2021 version is not showing in the SAP Readiness and Simplification Item check Report.", "problem": "SUM-Phase error indicating SAP Note 2399707 not found during system upgrade preparation.", "solution": "Implement SAP Note 2399707 completely and ensure all instructions are followed.", "cause": "", "processed_at": "2025-06-25T15:58:23.166437"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ea93bf141b79e9504284d8f99a4bcb23", "title": "Syntax errors after installing Readiness checks notes", "problem": "Syntax errors during SAP Readiness Check report execution after installing readiness check notes.", "solution": "Implement SAP Note 2937003 to resolve the first syntax error related to Business Partner after applying Note 2811183. For the second syntax error, refer to SAP Note 2740037 regarding the job TMW_RC_DVM_DATA_COLL failing with dump: CALL_FUNCTION_PARM_UNKNOWN.", "cause": "Issues with specific SAP Notes related to syntax errors and job execution during the SAP Readiness Check.", "processed_at": "2025-06-25T15:58:27.973719"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4d3a5877478765901d1ff2b5536d4385", "title": "Readiness check running over 24 days", "problem": "Readiness check with Financial Data Quality is running for over 24 days.", "solution": "1. Schedule Financial Data Quality check separately from other SAP Readiness Check data collectors. 2. Use Parallel Analysis mode instead of Sequential Analysis in Expert Mode. 3. Limit analysis to one fiscal year per job in General Ledger.", "cause": "The volume of financial data impacts the execution time, causing the job to run for an extended period.", "processed_at": "2025-06-25T15:58:32.913193"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4d3a5877478765901d1ff2b5536d4385", "title": "Readiness check running over 24 days", "problem": "SPOOL_I/O_ERROR encountered during job execution.", "solution": "Schedule RSPO1043 as a daily job to resolve spool object inconsistencies.", "cause": "Inconsistent spool objects were found in the system, causing the SPOOL_I/O_ERROR.", "processed_at": "2025-06-25T15:58:32.913215"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7a862b051bbd65d09778859c7b4bcb0b", "title": "Report  RC_COLLECT_ANALYSIS_DATA  - dump error", "problem": "Report RC_COLLECT_ANALYSIS_DATA results in a dump error at the start.", "solution": "Verify steps from SAP Note 2972792 and check implementation of /SDF/NOTE_2909538.", "cause": "ABAP programming error after implementing SAP Note 2972792.", "processed_at": "2025-06-25T15:58:38.554995"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7a862b051bbd65d09778859c7b4bcb0b", "title": "Report  RC_COLLECT_ANALYSIS_DATA  - dump error", "problem": "Unable to connect to SBX system due to 'Cancelled host did not respond' error.", "solution": "Ensure R/3 connection is opened and credentials are entered in the secure area.", "cause": "", "processed_at": "2025-06-25T15:58:38.555019"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b48ee1231b0da9d87ce3ddb6bb4bcbd1", "title": "Custom Code Analysis contains no data, note 3059197", "problem": "Custom Code Analysis in SAP Readiness Check contains no data despite following SAP Note 3059197.", "solution": "Requested customer to provide the ZIP file created by ABAP Test Cockpit for verification.", "cause": "", "processed_at": "2025-06-25T15:58:42.172822"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7d655c2ddbb9e150d65b99fbd396194b", "title": "Problems creating Readiness Check Report", "problem": "Unable to create Readiness Check Report due to missing prerequisite SAP Note 2769657.", "solution": "Verify all prerequisite SAP Notes are correctly implemented. Specifically, update SAP Note 2758146 to its latest version 85.", "cause": "SAP Note 2769657 not applicable due to invalid ST-A/PI version level of the system.", "processed_at": "2025-06-25T15:58:46.123843"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4c6c555747544ed41cccb7da216d435d", "title": "Sizing Report is Missing in S/4Hana Initial Shipment Pack 2022 Readiness Check", "problem": "Sizing report is missing in the Readiness Check report for S/4HANA Initial Shipment Pack 2022.", "solution": "Informed customer that the sizing report is not available for systems already running on S/4HANA as per SAP Note 3059197. The sizing report is only available when preparing a conversion from an ERP system.", "cause": "The readiness check was run for a system already on S/4HANA, where sizing analysis is not applicable.", "processed_at": "2025-06-25T15:58:50.084690"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7caa28821b8a2950ea00dc2c9b4bcbda", "title": "SAP Readiness Check - Error in viewing IDoc Interfaces  & RFC/BAPI Interfaces", "problem": "Error in viewing IDoc Interfaces & RFC/BAPI Interfaces in SAP Readiness Check.", "solution": "Ensure the zip file directly contains XML files without additional folders. Correct start and end date values in XML files to format yyyyMMddHHmmss and re-upload the zip file.", "cause": "Incorrect folder structure in zip file and invalid start date values in XML files.", "processed_at": "2025-06-25T15:58:54.023589"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e84210b61bf56190490720ad3b4bcb2d", "title": "No TTcode data when collecting data via RC_VALUE_DISCOVERY_COLL_DATA for SAP Business Scenario Recommendations", "problem": "No TTcode data generated when collecting data via RC_VALUE_DISCOVERY_COLL_DATA for SAP Business Scenario Recommendations.", "solution": "Reviewed KBA 2977422 for troubleshooting guide and followed instructions provided in the document.", "cause": "Data collection job BUZ_SCENARIO_RECOMMENDATION_COL does not generate TTCODE.XML file.", "processed_at": "2025-06-25T15:58:58.081333"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9da6baa997ed42187bcf59081153aff3", "title": "Readynesscheck for SAP S/4HANA -> FQD Check doesnt work", "problem": "FDQ check in RC_COLLECT_ANALYSIS_DATA reports missing SAP Note 2972792 despite it being installed.", "solution": "De-implement SAP Note 2758146 completely, re-implement it in its latest available version 69, and ensure all yellow marked objects are selected for overwriting during implementation.", "cause": "Old coding not updated/overwritten by new coding during last update of SAP Note 2758146.", "processed_at": "2025-06-25T15:59:01.989652"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b63b792b1b8561144284d8f99a4bcbdc", "title": "Syntax error during note 3130905", "problem": "Syntax error during implementation of SAP note 3130905.", "solution": "Reviewed SAP note 2972792 and 3130905. Ensured all prerequisite steps were followed as described in both notes. Customer confirmed that errors were resolved and closed the incident.", "cause": "Potential misstep in following the instructions in SAP note 3130905 or related prerequisite note.", "processed_at": "2025-06-25T15:59:06.025230"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/562453891b126950ea00dc2c9b4bcb7f", "title": "Syntax error during note 3130905", "problem": "Syntax error during implementation of SAP Note 3130905.", "solution": "Re-implement SAP Note 3130905 along with prerequisite SAP Note 2972792. Ensure SAP Note 2758146 is updated to version 85.", "cause": "Outdated version of SAP Note 2758146 affecting dependency resolution.", "processed_at": "2025-06-25T15:59:12.043729"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/562453891b126950ea00dc2c9b4bcb7f", "title": "Syntax error during note 3130905", "problem": "Incomplete de-implementation of SAP Note 2972792 causing inconsistent objects.", "solution": "De-implement SAP Note 2972792 completely, clean inconsistent objects using SAP Note 3289850 guidance, then re-implement SAP Note 2972792.", "cause": "Objects not fully deleted during de-implementation of SAP Note 2972792 resulting in inconsistencies.", "processed_at": "2025-06-25T15:59:12.043734"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/562453891b126950ea00dc2c9b4bcb7f", "title": "Syntax error during note 3130905", "problem": "System dumps during execution after SAP Note implementation.", "solution": "Identify and clean inconsistent objects, ensure correct implementation of SAP notes, validate execution process.", "cause": "Inconsistent objects persisted after incomplete de-implementation of SAP Note 2972792.", "processed_at": "2025-06-25T15:59:12.043736"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a57e899dc3b36110b9946ecd2b0131fd", "title": "2 Topics labelled as \"Potentially high\" but they are in use", "problem": "Two SAP Readiness Check items are labeled as 'Potentially high' impact but are already implemented.", "solution": "Installed latest version of SAP note 2903677 which provides a different approach for customers already using New Asset Accounting, reducing the effort impact to Low. Requested the customer to install this note in their Sandbox environment and maintain access credentials.", "cause": "Initial SAP Readiness Check did not account for the implemented status of New GL and New Asset Accounting, leading to incorrect impact labeling.", "processed_at": "2025-06-25T15:59:16.952970"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a57e899dc3b36110b9946ecd2b0131fd", "title": "2 Topics labelled as \"Potentially high\" but they are in use", "problem": "Server connection issues preventing SAP Support from accessing the customer's system.", "solution": "Customer re-opened the connection to allow SAP Support to access the system for further checks.", "cause": "", "processed_at": "2025-06-25T15:59:16.952992"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/101e144b1b62659c992699798b4bcbae", "title": "When trying to free them I have many ina", "problem": "Inactive objects preventing SAP note release in development system.", "solution": "Attempt to activate inactive objects. De-implement the SAP note 2972792 and re-implement it. Verify changes in function modules after re-implementation.", "cause": "Incomplete or incorrect implementation of SAP note 2972792 leading to inactive objects.", "processed_at": "2025-06-25T15:59:22.627801"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/101e144b1b62659c992699798b4bcbae", "title": "When trying to free them I have many ina", "problem": "Missing FDQ data in ZIP file from Readiness Check report.", "solution": "De-implement and correctly re-implement FDQ SAP note 2972792 in SBX environment.", "cause": "Old version of APIs and keys due to incorrect implementation of FDQ SAP note 2972792.", "processed_at": "2025-06-25T15:59:22.627822"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/101e144b1b62659c992699798b4bcbae", "title": "When trying to free them I have many ina", "problem": "SQL error when accessing tables in SBX system.", "solution": "Investigate database and system configuration to resolve SQL error during table access.", "cause": "", "processed_at": "2025-06-25T15:59:22.627829"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2926e0fb1bb92914278cb9918b4bcb49", "title": "CVI information", "problem": "System doesn't show information related to CVI in readiness check.", "solution": "1. Verify if SAP Notes 2758146, 2399707, and 2811183 are implemented with the latest version. 2. Implement the latest version if necessary. 3. Run the readiness check report again. 4. If the issue persists, open a remote connection and attach the latest analysis zip file uploaded to the dashboard.", "cause": "", "processed_at": "2025-06-25T15:59:26.933089"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/158a6569db2921d09e62297cd396194f", "title": "Implementation of note 2758146 failed with status \"Incompletely implemented\"", "problem": "Implementation of SAP Note 2758146 failed with status 'Incompletely implemented'.", "solution": "De-implement both SAP Notes 2758146 and 2745851. Re-implement SAP Note 2758146 without any issues or particular options. Re-implement SAP Note 2745851. Verify both notes are listed in status 'Completely Implemented'.", "cause": "Unknown, possibly a temporary system issue or 'hick up'.", "processed_at": "2025-06-25T15:59:31.543902"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/158a6569db2921d09e62297cd396194f", "title": "Implementation of note 2758146 failed with status \"Incompletely implemented\"", "problem": "SAP connection attempt failed with 'route permission denied' error.", "solution": "Ensure correct permissions and working connections are provided. In case of issues, involve network colleagues to assist with connection setup.", "cause": "Permission issues with SAProuter settings preventing connection establishment.", "processed_at": "2025-06-25T15:59:31.543922"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/39b083591b0a6510ea00dc2c9b4bcb33", "title": "Readiness Check report terminates", "problem": "Readiness Check report terminates with short dump error due to syntax issue in ABAP program.", "solution": "Implement SAP Notes 2745851 and 3205320 to resolve syntax errors and ensure proper implementation of Business Process Improvement Content for SAP Readiness Check 2.0.", "cause": "Syntax error in ABAP program '/SDF/CL_S4RC20_REALCHECK' due to undeclared method 'ADDTOJSON'.", "processed_at": "2025-06-25T15:59:36.207287"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/39b083591b0a6510ea00dc2c9b4bcb33", "title": "Readiness Check report terminates", "problem": "Financial Data Quality check prerequisites not met, causing job cancellation after executing Readiness Check report.", "solution": "Check 'Schedule Analysis' for missing SAP Notes and implement all listed prerequisite notes for Financial Data Quality check.", "cause": "Missing prerequisite SAP Notes for Financial Data Quality check.", "processed_at": "2025-06-25T15:59:36.207294"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e5277a88dbf5a9109e62297cd3961986", "title": "SAP Readiness Check for SAP S/4HANA ( 2827612 ) in D01 system", "problem": "SAP Note 2827612 cannot be implemented in the D01 system.", "solution": "It was confirmed that SAP Note 2827612 is not applicable for the customer's system version (ST-PI 740 SP19). Therefore, no implementation is required.", "cause": "The note is not valid for the system version being used, as the latest package already includes its contents.", "processed_at": "2025-06-25T15:59:42.663754"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e5277a88dbf5a9109e62297cd3961986", "title": "SAP Readiness Check for SAP S/4HANA ( 2827612 ) in D01 system", "problem": "Error executing report RC_COLLECT_ANALYSIS_DATA due to prerequisite SAP Note 2827612 not being installed.", "solution": "The issue was transferred to the Readiness Check expert area for further assistance, as it involves readiness check-related questions, not note assistant issues.", "cause": "", "processed_at": "2025-06-25T15:59:42.663776"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e5277a88dbf5a9109e62297cd3961986", "title": "SAP Readiness Check for SAP S/4HANA ( 2827612 ) in D01 system", "problem": "Outdated code from SAP Note 2758146 causing errors despite latest version being implemented.", "solution": "De-implement SAP Note 2758146 completely and re-implement it again. Ensure modules with a yellow traffic light are ticked before continuing the implementation.", "cause": "Outdated code from the initial delivery of SAP Note 2758146 still in use.", "processed_at": "2025-06-25T15:59:42.663784"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/20736e54db2dad109e62297cd3961954", "title": "SAP Readiness Check", "problem": "Customer requests a SAP Readiness Check for their systems and environment before going live with S/4HANA.", "solution": "SAP Support provided guidance on the SAP Readiness Check, which assesses the suitability of the existing SAP ERP system for S/4HANA. Relevant knowledge articles and troubleshooting guides (KBs 2758146, 2968380, and 2913617) were provided to the customer.", "cause": "Customer was not familiar with the SAP Readiness Check process.", "processed_at": "2025-06-25T15:59:47.058608"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5bc0f89b1b2e2d5c278cb9918b4bcb58", "title": "Implementation of SAP note 2972792 - Financial Data Quality: Trigger Data Collection Reports to Check ...", "problem": "Warning message 'Object already exists and will be overwritten' during implementation of SAP note 2972792.", "solution": "Select the checkbox in front of the line to import the changes for these objects.", "cause": "Standard object not modified in the company, causing confusion about changes needed.", "processed_at": "2025-06-25T15:59:52.640504"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5bc0f89b1b2e2d5c278cb9918b4bcb58", "title": "Implementation of SAP note 2972792 - Financial Data Quality: Trigger Data Collection Reports to Check ...", "problem": "Syntax error in program FIN_AUTO_CORR_MIG_ERROR related to parameter mismatch.", "solution": "Implement SAP note 2890142 to deactivate the program FIN_AUTO_CORR_MIG_ERROR.", "cause": "Code inconsistencies due to parameter mismatch ('CT_KEY' vs 'CS_KEY') in SAP notes 2793849 and 2836444.", "processed_at": "2025-06-25T15:59:52.640520"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5bc0f89b1b2e2d5c278cb9918b4bcb58", "title": "Implementation of SAP note 2972792 - Financial Data Quality: Trigger Data Collection Reports to Check ...", "problem": "Locked user U0Y16 in system EER preventing progress.", "solution": "Unlock user U0Y16 in system EER.", "cause": "", "processed_at": "2025-06-25T15:59:52.640521"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5bc0f89b1b2e2d5c278cb9918b4bcb58", "title": "Implementation of SAP note 2972792 - Financial Data Quality: Trigger Data Collection Reports to Check ...", "problem": "Issue in SAP note 2502552 during readiness check implementation with methods CLS4SIC HRRCF SUM CHECK causing problems.", "solution": "Check the attached documentation for resolution of issues with methods CLS4SIC HRRCF SUM CHECK.", "cause": "", "processed_at": "2025-06-25T15:59:52.640523"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8fc8d59fdbd1e9d4d65b99fbd3961945", "title": "3059197 - SAP Readiness Check for SAP S/4HANA upgrades", "problem": "SAP Readiness Check report RC_COLLECT_ANALYSIS_DATA only shows SAP S/4HANA 2020 Initial Stack as the target version instead of the intended 2022 version.", "solution": "Provided SAP note 3213713 to address the issue where SAP S/4HANA Readiness check report does not show the latest S/4HANA target releases.", "cause": "The readiness check report was not updated to include the latest target version releases.", "processed_at": "2025-06-25T15:59:56.898139"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b0e5b01f1b0e29d0442fddb69b4bcb8d", "title": "RC_BW_4_HANA:NEW_CODE_ANALYSIS fails in BW system and no data in Custom code report", "problem": "RC_BW_4_HANA:NEW_CODE_ANALYSIS job fails in BW system, resulting in no data being available in the custom code report.", "solution": "Run the report RC_BW_COLLECT_ANALYSIS_DATA in SA38. Ensure SAP Note 3061594 is implemented. If the issue persists, provide system access for further investigation.", "cause": "The Results Database Table 'RSB4HANADETAILS' was empty during analysis execution.", "processed_at": "2025-06-25T16:00:01.986667"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b0e5b01f1b0e29d0442fddb69b4bcb8d", "title": "RC_BW_4_HANA:NEW_CODE_ANALYSIS fails in BW system and no data in Custom code report", "problem": "Download analysis data zip file does not contain any data despite successful execution of RC_BW_4_HANA:NEW_CODE_ANALYSIS.", "solution": "Re-trigger 'schedule analysis' and verify the status. Ensure correct execution process is followed.", "cause": "", "processed_at": "2025-06-25T16:00:01.986681"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d12958fb1b35add0d41feca8ab4bcbb8", "title": "Readiness Check Dashboard not showing 2 areas for any entries", "problem": "Readiness Check Dashboard not displaying 'Add-On Compatibility' and 'Active Business Functions' for any entries.", "solution": "1. Verify the status of the Maintenance Planner for the impacted systems. 2. Correct any errors in the system details within the Maintenance Planner. 3. Refresh the Readiness Check analysis to reprocess and update the dashboard.", "cause": "The Maintenance Planner reports the system status as 'Error', preventing data retrieval for these sections.", "processed_at": "2025-06-25T16:00:06.205415"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0f52be911b4aa510278cb9918b4bcb55", "title": "ABAP Dump CL_RC_FDQ_FI_COLLECTORS=======CP while running Readiness Check", "problem": "ABAP Dump occurs during execution of Readiness Check Report while extracting Financial Data.", "solution": "1. Implement latest version of SAP Note 2758146 (Current Version 69 - Latest 86). 2. De-implement and re-implement SAP Note 2972792. 3. Delete buffer for financial data and retry. 4. Maintain system credentials for further investigation by SAP Support.", "cause": "Method 'CHECK_FIML_PREREQ' does not exist; syntax error points to a similar method 'CHECK_FIAA_PREREQ'.", "processed_at": "2025-06-25T16:00:10.185819"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/715252c5db4aad10f7590b07f3961987", "title": "SAP readiness Check for SA HANA", "problem": "Error encountered when executing the program RC_COLLECT_ANALYSIS_DATA despite implementing prerequisite notes and updating STPI & STAPI to the latest versions.", "solution": "De-implement SAP Note 2758146 and 2399707, then download and implement the latest version of 2758146 (version 85) and 2399707 (version 157).", "cause": "", "processed_at": "2025-06-25T16:00:14.136902"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7b761a73db29a110d65b99fbd39619fb", "title": "Readiness Check liefert keine Ergebnisse", "problem": "Readiness Check results are not displayed despite successful upload.", "solution": "1. Attempted upload using different browsers (Chrome, Edge). 2. SAP support successfully created Readiness Check using provided ZIP file. 3. Customer successfully uploaded another file after SAP's test. 4. Customer deleted test uploads and attempted upload again, facing the same issue. 5. Suggested possible authorization problem; development team involved.", "cause": "Potential authorization issue or system-specific problem.", "processed_at": "2025-06-25T16:00:18.549667"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7b761a73db29a110d65b99fbd39619fb", "title": "Readiness Check liefert keine Ergebnisse", "problem": "Cannot upload ZIP file for Readiness Check using supported browsers due to internal policy restrictions.", "solution": "Customer informed of inability to install Firefox due to internal policies. Suggested arranging a Teams session for further investigation.", "cause": "Internal corporate policies restrict installation of Firefox browser.", "processed_at": "2025-06-25T16:00:18.549689"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/955c2e901b426d14490720ad3b4bcb8c", "title": "2758146 - SAP Readiness Check - Failed to execute current sub-queue. SAP Note cannot be processed further.", "problem": "SAP Readiness Check failed to execute current sub-queue; SAP Note cannot be processed further.", "solution": "Reviewed the information provided and confirmed that Version 85 was completely implemented. Issue resolved by implementing the correct version. Advised customer to download the latest version if they wish to implement Version 86 and provided steps for further implementation.", "cause": "Attempted to implement an obsolete version of the SAP Note.", "processed_at": "2025-06-25T16:00:22.569404"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e8b70101bde2110ea00dc2c9b4bcb5f", "title": "S4HANA FPS Upgrade", "problem": "No option to upgrade to S4HANA 1909 FPS 7 during assessment using report RC_COLLECT_ANALYSIS_DATA.", "solution": "Customer was informed that SP level upgrade is not supported; only version upgrades are possible. Suggested checking the version list and confirmed that 1909 would not appear in the upgrade options.", "cause": "SP level upgrades are not supported; only version upgrades are available, and 1909 is already the current version.", "processed_at": "2025-06-25T16:00:26.317603"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/98a6a9fedbb52990f7590b07f3961925", "title": "SAP Readiness check issue", "problem": "Unable to fetch SAP Readiness report using RC_COLLECT_ANALYSIS_DATA despite applying suggested notes.", "solution": "1. Verify that all prerequisites for 'Financial Data Quality' are fulfilled as per SAP Note 2972792. 2. Ensure implementation of all applicable SAP Notes mentioned. 3. Update ST-A/PI to version 01U* SP3.", "cause": "Missing prerequisites for 'Financial Data Quality' and SAP Note 2972792 not fully implemented.", "processed_at": "2025-06-25T16:00:33.438790"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/98a6a9fedbb52990f7590b07f3961925", "title": "SAP Readiness check issue", "problem": "Prerequisite check for SAP Note 3061414 failed, preventing the execution of RC_COLLECT_ANALYSIS_DATA.", "solution": "1. Confirm all prerequisites mentioned in SAP Note 3061414 are fulfilled. 2. Review the implementation of SAP Note 3061414 and related prerequisites.", "cause": "Incomplete fulfillment of prerequisites related to SAP Note 3061414.", "processed_at": "2025-06-25T16:00:33.438806"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/98a6a9fedbb52990f7590b07f3961925", "title": "SAP Readiness check issue", "problem": "Connection issues with EED system preventing SAP Support from logging in.", "solution": "1. Verify and correct the connection settings to EED system. 2. Ensure valid credentials are provided for SAP Support access.", "cause": "Connection to EED system was broken due to incorrect setup or credentials.", "processed_at": "2025-06-25T16:00:33.438810"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/34cf6faf1b7de550992699798b4bcbad", "title": "CQC for Deployment Readiness for SAP SuccessFactors", "problem": "Misalignment of service request with SAP Readiness Check report and CQC Deployment Readiness for SAP SuccessFactors.", "solution": "Redirect service request based on instructions in SAP Note 2313972, which outlines the proper channel for requesting CQC services.", "cause": "Customer mistakenly associated CQC Deployment Readiness with SAP Readiness Check, which are separate services.", "processed_at": "2025-06-25T16:00:37.911921"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/eac51a981beded104284d8f99a4bcb39", "title": "dump CALL_FUNCTION_NOT_ACTIVE  in RC_COLLECT_ANALY", "problem": "Dump CALL_FUNCTION_NOT_ACTIVE occurs when executing report RC_COLLECT_ANALYSIS_DATA via SE38.", "solution": "Reviewed ABAP dump details and identified inconsistencies related to specific function modules. Followed solution steps from SAP KBA 3093810. Confirmed resolution of inconsistencies, allowing execution of the report RC_COLLECT_ANALYSIS_DATA without errors.", "cause": "Inconsistencies related to certain function modules required by SAP Notes 2758148 and 2745851.", "processed_at": "2025-06-25T16:00:42.136949"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b27116ca1b19ed9017f8859c7b4bcb80", "title": "Exception error for Readiness check", "problem": "Exception error triggered with message 'SMDB_CONTNET_NOT_FOUND' while executing program RC_COLLECT_ANALYSIS_DATA using transaction code SE38.", "solution": "Investigate the SMDB content presence and availability. Ensure relevant data is correctly loaded and accessible in the system. Verify related system configurations and dependencies that might influence the SMDB content detection.", "cause": "SMDB content not found or inaccessible which is needed for the execution of the readiness check program.", "processed_at": "2025-06-25T16:00:45.879239"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/53cb65dadba1e1549e62297cd396194a", "title": "Issue Report RC_COLLECT_ANALYSIS_DATA", "problem": "Dump error when executing RC_COLLECT_ANALYSIS_DATA report for SAP Readiness Check.", "solution": "De-implement SAP Note 2758146 and 2399707. Download and implement the latest versions of these notes. Ensure all checkboxes are selected when implementing SAP Note 2399707.", "cause": "Outdated or incorrectly implemented SAP Notes causing syntax errors.", "processed_at": "2025-06-25T16:00:52.398667"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/53cb65dadba1e1549e62297cd396194a", "title": "Issue Report RC_COLLECT_ANALYSIS_DATA", "problem": "Unable to upload custom code results to SAP Readiness Check site.", "solution": "Enable ABAP Test Cockpit (ATC) for custom code analysis. Run SAP S/4HANA custom code checks and export results via ATC transaction as XML. Follow steps in Custom Code Migration Guide for SAP S/4HANA.", "cause": "Incorrect method for generating and exporting custom code analysis results.", "processed_at": "2025-06-25T16:00:52.398689"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/53cb65dadba1e1549e62297cd396194a", "title": "Issue Report RC_COLLECT_ANALYSIS_DATA", "problem": "Connection refused error when trying to access HEX system remotely.", "solution": "Check network settings and ensure SAProuter configuration is correct. Refer to SAP Note 1178624 for guidance on resolving 'Partner not reached' errors.", "cause": "Network interface issue or incorrect SAProuter configuration.", "processed_at": "2025-06-25T16:00:52.398697"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/53cb65dadba1e1549e62297cd396194a", "title": "Issue Report RC_COLLECT_ANALYSIS_DATA", "problem": "Generated report contains less data than expected.", "solution": "Ensure execution on a production system and relevant client. Verify completeness of data before uploading to SAP site.", "cause": "Report execution on non-productive system or client causing incomplete data.", "processed_at": "2025-06-25T16:00:52.398703"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/adf866a31b0ee914992699798b4bcb5a", "title": "SAP RC and Simplification Items Checks issues", "problem": "Discrepancies in the SAP Readiness and Simplification Item checks.", "solution": "1. Review the discrepancies documented by the customer. 2. Execute SIC check on SAP test system to compare with ERP system checks. 3. Ensure all relevant and consistency checks are executed for valid items.", "cause": "Customer needs understanding of readiness check report discrepancies before implementing S/4 HANA conversion.", "processed_at": "2025-06-25T16:00:59.474030"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/adf866a31b0ee914992699798b4bcb5a", "title": "SAP RC and Simplification Items Checks issues", "problem": "Difference in number of relevant SI items between readiness check dashboard and SAP system.", "solution": "Explain the difference between Consistency Check in ERP system and Relevance Check in report portal. Refer to Note 2399707 for detailed explanation.", "cause": "", "processed_at": "2025-06-25T16:00:59.474053"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/adf866a31b0ee914992699798b4bcb5a", "title": "SAP RC and Simplification Items Checks issues", "problem": "Need clarification on executing relevant and consistency checks across different clients.", "solution": "Execute Relevance Check on each productive client for product data. Perform Consistency Check on client 000 due to Software Update Manager logs availability.", "cause": "", "processed_at": "2025-06-25T16:00:59.474060"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/adf866a31b0ee914992699798b4bcb5a", "title": "SAP RC and Simplification Items Checks issues", "problem": "Uncertainty if SAP notes related to Financial Data Quality affect existing functionality.", "solution": "Clarify that FDQ check has no impact on existing functionality according to Note 2913617.", "cause": "", "processed_at": "2025-06-25T16:00:59.474065"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/adf866a31b0ee914992699798b4bcb5a", "title": "SAP RC and Simplification Items Checks issues", "problem": "Catalog updates leading to different relevant and consistency results.", "solution": "Explain catalog update process in Note 2399707. Trigger catalog updates explicitly via Update catalog button in /SDF/RC_START_CHECK.", "cause": "", "processed_at": "2025-06-25T16:00:59.474070"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/adf866a31b0ee914992699798b4bcb5a", "title": "SAP RC and Simplification Items Checks issues", "problem": "Limited ST03N data affecting relevance check accuracy.", "solution": "Suggest exporting one year of ST03N data to avoid missing relevant items. Check configuration for full ST03N data export.", "cause": "", "processed_at": "2025-06-25T16:00:59.474074"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/adf866a31b0ee914992699798b4bcb5a", "title": "SAP RC and Simplification Items Checks issues", "problem": "Concerns about SAP notes altering existing functionality.", "solution": "Reassure customer that notes related to FDQ analysis do not affect existing functionality but are suggested for pre-conversion checks.", "cause": "", "processed_at": "2025-06-25T16:00:59.474079"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1072aaaf1b99a99829ef997bbb4bcb31", "title": "Hana Transformation check", "problem": "No default KPIs available when running the RC_VALUE_DISCOVERY_COLL_DATA report during the upgrade from ECC6.0 to S4 HANA.", "solution": "1. Apply SNOTE correction notes: 1668882 and 2971435. 2. De-implement the existing previous version of note 2745851, then apply the note again. 3. Transport the corrections to the Q and P systems.", "cause": "Note 2745851 wasn't applied correctly.", "processed_at": "2025-06-25T16:01:03.560499"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/11597865dbd22990f7590b07f3961917", "title": "Error when applying SAP Note 2972792", "problem": "Error encountered when applying SAP Note 2972792 due to inactive and incorrectly versioned function modules.", "solution": "1. Retrieved version 00001 for specified function modules. 2. Activated all objects. 3. Implemented SAP Note 2972792 successfully. 4. De-implemented and re-implemented SAP Note to verify resolution.", "cause": "Some function modules were inactive or had incorrect code versions not aligned with the latest ST-PI release.", "processed_at": "2025-06-25T16:01:07.985970"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3213572ddbc62150bf57fef1f3961973", "title": "Need help with Pre-Req of 3112362", "problem": "Uncertainty about whether manual pre-requisite steps for OSS Note 3112362 can be transported or need to be applied separately in each system.", "solution": "SAP Support confirmed it is enough to transport the changes recorded in the development system's transport to other systems. Customers are advised to double-check the transport package alignment with Note 3112362.", "cause": "Unclear instructions in OSS Note 3112362 regarding pre-requisite steps for multiple systems.", "processed_at": "2025-06-25T16:01:11.864861"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/01e43dc0dba1a110d65b99fbd3961958", "title": "Missing Customer in SAP readiness Check", "problem": "Customer ID missing from the drop-down list in SAP Readiness Check using OSS User.", "solution": "Incident forwarded to component SV-SCS-S4R for further investigation.", "cause": "", "processed_at": "2025-06-25T16:01:15.623729"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a0b5abf91b8ead501c7fea836b4bcb81", "title": "SAP Readiness Check for SAP S/4HANA upgrades", "problem": "In SAP S/4HANA quality system S4Q, the latest update releases are not displayed when running program RC_COLLECT_ANALYSIS_DATA, despite all required notes being implemented and transported.", "solution": "The customer was advised to follow the solution outlined in SAP Note 3213713, which addresses the issue where the SAP S/4HANA Readiness Check report does not show the latest S/4HANA target releases.", "cause": "The root cause is not explicitly stated in the ticket; however, it involves discrepancies in displaying update releases between the development system and the quality system.", "processed_at": "2025-06-25T16:01:21.220086"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/78853e2e1b6da5d4caf9fd9b8b4bcbfa", "title": "ERROR_MESSAGE executing RC_COLLECT_ANALYSIS_DATA", "problem": "Job RC_COLLECT_ANALYSIS_DATA fails with ERROR_MESSAGE exception.", "solution": "Refer to SAP note 2968380 for troubleshooting and implement SAP note 2919704 to resolve the issue. The error message is raised from underlying APIs causing job termination.", "cause": "Incomplete tax code encountered during data collection leads to system exception ERROR_MESSAGE.", "processed_at": "2025-06-25T16:01:25.781223"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/13f5d4ea1b56a554992699798b4bcb7d", "title": "Customer has plans for S/4 HANA migration roadmap in future - Need suggestions", "problem": "Customer needs guidance on S/4 HANA migration including system version requirements and best practices.", "solution": "The customer was advised to check SAP Note 2913617 for SAP Readiness Check for SAP S/4HANA, and to contact their account manager for roadmap guidance as they do not own an active S4HANA cloud license.", "cause": "The customer does not own an active license for S4HANA cloud systems.", "processed_at": "2025-06-25T16:01:30.753873"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/13f5d4ea1b56a554992699798b4bcb7d", "title": "Customer has plans for S/4 HANA migration roadmap in future - Need suggestions", "problem": "Misrouting of support ticket to incorrect SAP component.", "solution": "The ticket was re-routed to the correct component for further assistance.", "cause": "Misrouting due to incorrect component selection not under HEC scope of support.", "processed_at": "2025-06-25T16:01:30.753881"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7bc5b584dbe1a110d65b99fbd3961985", "title": "TMW_RC_FDQ_DATA_COLL dumps with OBJECTS_OBJREF_NOT_ASSIGNED", "problem": "TMW_RC_FDQ_DATA_COLL job cancels with runtime error OBJECTS_OBJREF_NOT_ASSIGNED.", "solution": "De-implement SAP Note 2758146 and re-implement the latest version (v86), then re-execute RC_COLLECT_ANALYSIS_DATA.", "cause": "Incomplete implementation of SAP Note 2758146 leading to missing entries in table BCOS_CUST.", "processed_at": "2025-06-25T16:01:35.107537"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dec53990db0ae910769ad47cd3961984", "title": "SAP BW/4HANA Readiness Check report not existing in BW systems", "problem": "Unable to execute RC_BW_COLLECT_ANALYSIS_DATA report, error indicates report does not exist.", "solution": "Implemented SAP Note 3061594 to introduce RC_BW_COLLECT_ANALYSIS_DATA into the system.", "cause": "RC_BW_COLLECT_ANALYSIS_DATA report was not present in the system.", "processed_at": "2025-06-25T16:01:42.393622"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dec53990db0ae910769ad47cd3961984", "title": "SAP BW/4HANA Readiness Check report not existing in BW systems", "problem": "Status MENU of the user interface RC_BW_COLLECT_ANALYSIS_DATA missing, requiring Developer Access key.", "solution": "Followed SAP Note 3113043 and attempted to fix the issue using the SAP Note Analyzer tool as per guided installation instructions in SAP Note 2575059.", "cause": "Missing user interface components for RC_BW_COLLECT_ANALYSIS_DATA report.", "processed_at": "2025-06-25T16:01:42.393645"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dec53990db0ae910769ad47cd3961984", "title": "SAP BW/4HANA Readiness Check report not existing in BW systems", "problem": "Add-On Compatibility Analysis collectors did not collect the analysis data in readiness analysis report.", "solution": "Checked SAP Note 2408911 for guidance but issue persisted.", "cause": "", "processed_at": "2025-06-25T16:01:42.393652"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dec53990db0ae910769ad47cd3961984", "title": "SAP BW/4HANA Readiness Check report not existing in BW systems", "problem": "Maintenance planner reports system status as 'Unknown'.", "solution": "Suggested to raise a new incident for support component BC-UPG-MP if SAP Note 2408911 doesn't resolve the issue.", "cause": "System status reporting issue in maintenance planner.", "processed_at": "2025-06-25T16:01:42.393658"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dec53990db0ae910769ad47cd3961984", "title": "SAP BW/4HANA Readiness Check report not existing in BW systems", "problem": "Readiness report does not display target version and gives 'SMDB Content in Local DB Not Found' error.", "solution": "Updated simplification item catalog either automatically or manually using SAP Note 2951527.", "cause": "Missing SMDB content in local database.", "processed_at": "2025-06-25T16:01:42.393664"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/96d86ce01be92d10f46096859b4bcbf6", "title": "Report AGSRC_START_ANALYSIS for RC 2.0", "problem": "Customer is unsure whether to run the report AGSRC_START_ANALYSIS in Solution Manager for HANA readiness check 2.0.", "solution": "Inform customer that AGSRC_START_ANALYSIS is obsolete for SAP Readiness Check 1.0 and advise to use SAP Note 2758146 for Readiness Check 2.0. Confirm that RC 2.0 should be run in the individual system, not in Solution Manager.", "cause": "AGSRC_START_ANALYSIS is obsolete and not required for SAP Readiness Check 2.0. Use of outdated documentation led to confusion.", "processed_at": "2025-06-25T16:01:46.531259"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0f9ff7f71bce6954278cb9918b4bcb10", "title": "S4 Readiness checks *PERSON*", "problem": "Syntax errors occur when importing transport request DEVK968667 containing SNotes for S4 readiness checks.", "solution": "1. Check the DEV system for syntax errors in classes CL_RC_FDQ_FI_COLLECTORS, CL_RC_FDQ_WLI_MANAGER, and CL_RC_FDQ_ML_MONITOR. 2. Confirm that missing objects like type TT_FIML_METRICS and method BUILD_ML_WORKLIST exist with transport request DEVK968708. 3. Perform syntax check on TR DEVK968708 and ensure no errors. 4. Transport TR DEVK968708 to resolve the issue.", "cause": "Missing or unknown objects in the transport request DEVK968667 caused syntax errors during import.", "processed_at": "2025-06-25T16:01:52.711404"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0f9ff7f71bce6954278cb9918b4bcb10", "title": "S4 Readiness checks *PERSON*", "problem": "De-implementation of SNotes captured in transport DEVK968706 also results in errors during import to Test System.", "solution": "Review the DEV system to confirm syntax issues are resolved and verify existence of necessary objects with transport DEVK968708.", "cause": "De-implementation did not resolve the underlying missing or unknown objects causing syntax errors.", "processed_at": "2025-06-25T16:01:52.711426"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/71da12efdb4e6954bf57fef1f39619ad", "title": "Customer Vendor Integration Analysis- RC_COLLECT_ANALYSIS_DATA", "problem": "S/4 Hana Readiness check for Customer Vendor Integration Analysis couldn't be executed due to wrong format of collected data.", "solution": "1. Request access to the Managed System from the customer. 2. Ask the customer to attach the data collection ZIP file for verification.", "cause": "Wrong format of the collected data.", "processed_at": "2025-06-25T16:01:56.806008"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5e3a296cdb562910f7590b07f396196d", "title": "Latest Version of S/4HANA KAD analysis note performance poor - running 9 days now with minimum date range of 1 month", "problem": "Performance issue with KAD analysis note in SAP S/4HANA production system causing prolonged data extraction runtime.", "solution": "Cancel the active job, implement SAP Note 3159166, remove KPI KFFI000140 from the KPI list, and retrigger the data collection.", "cause": "The job was stuck on KPI KFFI000140 due to high data volume and poor index match in the database selection.", "processed_at": "2025-06-25T16:02:01.196686"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5e3a296cdb562910f7590b07f396196d", "title": "Latest Version of S/4HANA KAD analysis note performance poor - running 9 days now with minimum date range of 1 month", "problem": "Long runtime for data extraction report in production system with minimum date range.", "solution": "Exclude KPI KFFI000140 from data collection and proceed with the execution.", "cause": "Massive data volume in production compared to pre-production, causing slower processing.", "processed_at": "2025-06-25T16:02:01.196704"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e0fd46591ba969909778859c7b4bcb64", "title": "Readiness report job failed", "problem": "Readiness report job failed with ASSERTION FAILED dump while running RC_COLLECT_ANALYSIS_DATA.", "solution": "Check KBA 3247472 for detailed steps. Ensure ICM service with HTTP or HTTPS protocol is active.", "cause": "ICM service with HTTP or HTTPS protocol is not active.", "processed_at": "2025-06-25T16:02:04.588806"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/199392291be965d017f8859c7b4bcba7", "title": "Readiness Check", "problem": "Customer needs to run the Readiness Check for the S4P environment.", "solution": "Customer closed the incident without further interaction, suggesting they resolved the issue themselves or no longer required assistance.", "cause": "", "processed_at": "2025-06-25T16:02:08.391929"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e78ee6f1bcae914992699798b4bcbae", "title": "What pre-requisites do we need to install the SAP Readiness Check for SuccessFactors?", "problem": "Customer needs to know the pre-requisites for installing the SAP Readiness Check for SuccessFactors in their SAP HCM On-Premise environment.", "solution": "Refer the customer to SAP Note 3193560 which contains all relevant information regarding the installation pre-requisites and compatibility details.", "cause": "Customer lacks information on compatibility and pre-requisites for the SAP Readiness Check tool.", "processed_at": "2025-06-25T16:02:12.053947"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4a838dd31bce6114d41feca8ab4bcb99", "title": "Prerequisite up-to-date SAP Note 2827612 for Innovation Potential is not installed", "problem": "Prerequisite up-to-date SAP Note 2827612 for Innovation Potential is not installed during S4 Readiness Check.", "solution": "Verify the version of SAP Note 2758146 implemented and update it to the latest version available, which is version 86, as the issue related to SAP Note 2827612 is fixed in version 72 of SAP Note 2758146.", "cause": "SAP Note 2758146 was implemented in an outdated version (version 49), which did not include the fix for the related issue with SAP Note 2827612.", "processed_at": "2025-06-25T16:02:16.978094"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fea5d9ab1bcaa914490720ad3b4bcb4e", "title": "Obtain Financial Data Quality in SAP Readiness Check", "problem": "Inability to obtain Financial Data Quality in SAP Readiness Check due to incompatible SAP Notes.", "solution": "1. Apply SAP Note 2972792 to trigger data collection reports for financial data quality checks. 2. Do not apply SAP Note 2909538 as it is only valid for ST-PI 740 and not required for ST-PI 2008_1_700.", "cause": "SAP Note 2909538 is not applicable for the ST-PI version installed (2008_1_700), causing confusion during implementation.", "processed_at": "2025-06-25T16:02:21.801520"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/988938e5dbd22990f7590b07f39619ae", "title": "Runtime Error", "problem": "ABAP runtime error 'ITAB_ILLEGAL_REG' when executing the Process Discovery report RC_VALUE_DISCOVERY_COLL_DATA.", "solution": "Implement SAP Note 3066935 - ST-A/PI 01U SP2: Advance Corr. BP Analytics - PPIs for ERP, ensuring the latest version is applied.", "cause": "Correction SAP Note not yet implemented or implemented on an older version.", "processed_at": "2025-06-25T16:02:26.676376"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/988938e5dbd22990f7590b07f39619ae", "title": "Runtime Error", "problem": "ABAP Dump CX_SY_DYN_CALL_ILLEGAL_FUNC occurs when executing RC_VALUE_DISCOVERY_COLL_DATA.", "solution": "Repair the function group using transaction SE37 with the following steps: Select Menu 'Utilities', choose 'Repair Function Group', enter function group '/SDF/BPM_7X', and execute.", "cause": "", "processed_at": "2025-06-25T16:02:26.676384"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/585b2196dba1e1549e62297cd3961961", "title": "RC_COLLECT_ANALYSIS_DATA Execution error", "problem": "RC_COLLECT_ANALYSIS_DATA report execution error where the dropdown to select the target S/4HANA version is empty.", "solution": "1. Implement SAP Note 3213713 to update the SAP S/4HANA Readiness check report. 2. Verify the report now provides the option for conversion to the latest S/4HANA target releases.", "cause": "The SAP S/4HANA Readiness check report did not include the latest S/4HANA target releases.", "processed_at": "2025-06-25T16:02:30.809405"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/65eda81c1bd6e110d41feca8ab4bcb70", "title": "Customer Vendor Integration Analysis", "problem": "Customer Vendor Integration Analysis data is unavailable due to wrong format of collected data.", "solution": "Review the XML files for completeness. Ensure headers in XML files are correct as per provided template. Implement SAP Note 2758146 to fix XML completeness issue related to effort driver result. Forward the message for CVIA result XML issue to component LO-MD-BP-SYN. Manually fix and upload corrected files, update existing analysis with new files.", "cause": "Incomplete XML files missing header content, leading to incorrect data format.", "processed_at": "2025-06-25T16:02:34.742425"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9846946e1b56a554992699798b4bcbfc", "title": "Long Running Jobs (FIN_CORR_RECONCILE  & FIN_AA_CORR_RECONCILE)", "problem": "Long running jobs FIN_CORR_RECONCILE and FIN_AA_CORR_RECONCILE in QA4 Test System.", "solution": "1) Implement the latest version of SAP Notes 2758146 and 2972792. 2) Run report RTCCTOOL ensuring online collectors are active and follow recommendations from RTTCCTOOL report. 3) Modify Data Collection Mode from Sequential to Parallel Analysis in Expert Mode. 4) Schedule Financial Data Quality check separately and append results to original analysis session.", "cause": "High volume of financial data causing extended runtime for reconciliation reports.", "processed_at": "2025-06-25T16:02:39.287473"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6f67f3481bdae194ea00dc2c9b4bcb60", "title": "Business Scenario Recommendations for SAP S/4HANA", "problem": "Dump error encountered while executing program RC_VALUE_DISCOVERY_COLL_DATA for SAP Readiness Check report.", "solution": "Reset SAP Note 2745851, then reimplement using the latest version. Follow resolution note 2981833 for further guidance.", "cause": "Error during implementation of SAP Note 2745851.", "processed_at": "2025-06-25T16:02:42.870445"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4cccd5e01b122910ea00dc2c9b4bcb9d", "title": "RC_VALUE_DISCOVERY_COLL_DATA run gives 1 extra file", "problem": "RC_VALUE_DISCOVERY_COLL_DATA run generates an unexpected extra file (fui-site.css) beyond the anticipated 10 files.", "solution": "SAP Support advised that no correction is needed for the extra file and recommended proceeding with the submission of the generated zip file for Pathfinder report processing.", "cause": "", "processed_at": "2025-06-25T16:02:46.653227"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a72aa16cdb562910f7590b07f39619f7", "title": "*PERSON* as pilot customer", "problem": "Unable to clear SPAU notifications because the system is not added as a pilot customer to OSS notes.", "solution": "Identify the obsolete SAP note and de-implement it from the system.", "cause": "SAP note 2310438 is obsolete and was used for generating RC 1.0 reports, no longer applicable for the current system setup.", "processed_at": "2025-06-25T16:02:50.820479"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bcf229e5dbd2a990f7590b07f39619d6", "title": "Dump while executing S4 2022 Readiness check report", "problem": "Dump occurs when executing the Readiness check report RC_COLLECT_ANALYSIS_DATA for S4 HANA 2022.", "solution": "De-implement Note 2745851, download latest version of Note 2745851, apply latest version ensuring to check the checkbox in front of yellow warnings during note implementation.", "cause": "Low ST-A/PI version or Note 2745851 not applied correctly, causing missing method definition ADDTOJSON in class /SDF/CL_S4RC20.", "processed_at": "2025-06-25T16:02:54.763886"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1e41e56c1bbd6950f46096859b4bcb4f", "title": "SAP HANA READINESS CHECK ERROR", "problem": "Cannot run BSR report via program RC_VALUE_DISCOVERY_COLL_DATA.", "solution": "Implemented SAP Note 2758146 which allowed running the BSR report RC_VALUE_DISCOVERY_COLL_DATA.", "cause": "Program RC_VALUE_DISCOVERY_COLL_DATA was missing due to system already being on latest S4CORE version.", "processed_at": "2025-06-25T16:02:59.155998"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1e41e56c1bbd6950f46096859b4bcb4f", "title": "SAP HANA READINESS CHECK ERROR", "problem": "Unable to select target system after running RC_COLLECT_ANALYSIS_DATA.", "solution": "Downloaded and implemented the latest version 87 of SAP Note 2758146.", "cause": "Version 87 of the note was not initially available, causing the issue.", "processed_at": "2025-06-25T16:02:59.156021"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3d3256eb1ba5e11017f8859c7b4bcbfd", "title": "Unable to upload SNOTE 2758146 and 2745851 on dev", "problem": "Unable to upload SNOTE 2758146 and 2745851 on the development server, error 'data file of SAP note is incomplete'.", "solution": "Follow SAP Note 1816647 to address the error 'Data file of SAP Note is incomplete' when uploading a note in SNOTE.", "cause": "The corrections in the SAP Note are downloaded as ZIP or SAR files, which transaction SNOTE cannot extract.", "processed_at": "2025-06-25T16:03:03.161745"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/972ba4a91b12e5901c7fea836b4bcbaa", "title": "SAP Innovation And Optimization Pathfinder", "problem": "SAP Notes 2758146 and 2745851 show 'Obsolete version implemented' status in SNOTE, preventing progress in S/4 Hana readiness checks.", "solution": "1. Apply the latest versions of SAP Notes 2758146 and 2745851. 2. Execute report RC_COLLECT_ANALYSIS_DATA to verify successful implementation.", "cause": "The 'Obsolete version implemented' status indicates that an outdated version of the notes is applied.", "processed_at": "2025-06-25T16:03:07.652001"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ad2e714cdb822d54bf57fef1f39619ee", "title": "SAP Readiness Check for SAP Success Factors", "problem": "Difficulty in implementing SAP Note 3193560 due to incorrect reference to SAP Note 1117547.", "solution": "SAP Support identified a typo in the documentation of SAP Note 3193560 and clarified that SAP Note 1117547 is not a valid SAP Note but refers to manual correction instructions. They initiated an incident to correct the note and communicated the resolution to the customer.", "cause": "Typographical error in SAP Note 3193560 documentation leading to incorrect reference to SAP Note 1117547.", "processed_at": "2025-06-25T16:03:11.349616"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0467bbd5db862910bf57fef1f3961914", "title": "FI Inconsistencies ML for pre S/4 HANA check", "problem": "Missing methods and types during the re-implementation of OSS note 2972792, resulting in compilation errors.", "solution": "Reset and re-implement OSS notes 2972792 and 2909538, ensuring the latest versions are used.", "cause": "The methods 'CHECK_FIML_PREREQ' and 'BUILD_ML_WORKLIST', along with type 'TT_FIML_METRICS', are not present in the system, possibly due to an incomplete or outdated note implementation.", "processed_at": "2025-06-25T16:03:17.138989"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/306863a41b026110992699798b4bcb85", "title": "Add-on-Prüfung konnte aufgrund eines technischen Fehlers nicht ausgeführt werden > SV-SCS-S4R", "problem": "Add-on compatibility analysis section in SAP Readiness Check is missing data due to a technical error.", "solution": "Update T92 system data in the Maintenance Planner to correct the data in the Add-on Compatibility Analysis section.", "cause": "Incorrect T92 system data in the Maintenance Planner.", "processed_at": "2025-06-25T16:03:20.977929"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6bbcbdda1ba2a15c992699798b4bcbf0", "title": "Syntax issue while implementing the SAP Note: 2972792", "problem": "Syntax errors occur while implementing SAP Note: 2972792.", "solution": "1. Activate all inactive objects related to the SAP Note. 2. De-implement and re-implement the SAP Note, checking involved objects. 3. Retrieve older versions of existing objects and re-implement the note. 4. In worst case, delete objects and implement the note again.", "cause": "The SNote tool did not recognize the object CL_RC_FDQ_FI_AA_MONITOR => CONSTRUCTOR as requiring changes, resulting in code not being inserted and causing syntax errors.", "processed_at": "2025-06-25T16:03:26.819249"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6bbcbdda1ba2a15c992699798b4bcbf0", "title": "Syntax issue while implementing the SAP Note: 2972792", "problem": "Implementation of SAP Note: 2972792 fails due to existing version conflicts.", "solution": "De-implement the existing SAP Note version 60 before attempting to implement the latest version 70.", "cause": "Attempting to implement the latest version of the SAP Note without de-implementing the existing one led to version conflicts.", "processed_at": "2025-06-25T16:03:26.819265"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6bbcbdda1ba2a15c992699798b4bcbf0", "title": "Syntax issue while implementing the SAP Note: 2972792", "problem": "Syntax errors occur during implementation of SAP Note: 2745851.", "solution": "Follow the steps outlined in SAP Note: 3205320 to prevent syntax errors during implementation.", "cause": "", "processed_at": "2025-06-25T16:03:26.819270"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/28b9c7c71b71e1d0278cb9918b4bcba0", "title": "Delays in running SAP Readiness Check", "problem": "The RC_COLLECT_ANALYSIS_DATA job has been running for 5 days, which is significantly longer than expected.", "solution": "Confirmed that running the RC_COLLECT_ANALYSIS_DATA job for several days is normal in case extra checks like FDQ are selected, especially for larger system data sizes.", "cause": "Extra checks such as FDQ were selected, which inherently take longer to complete due to the large data size of the system.", "processed_at": "2025-06-25T16:03:32.687610"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/53578ac41ba221181c7fea836b4bcb43", "title": "Issue in Readiness  Check", "problem": "Conversion errors occur during SAP Readiness Check, causing business conversion to be stuck.", "solution": "Opened another incident under the correct DE2 system for detailed analysis and resolution. Forwarded to BC-DWB-TOO-ATF team for initial analysis.", "cause": "Duplicate incident with similar issues reported in the correct system.", "processed_at": "2025-06-25T16:03:37.143522"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d62222ccdbe22958bf57fef1f396198e", "title": "CL_TMW_RC_COLLECT_BPA cannot be deleted as per sapnote 3221283", "problem": "Error encountered during the implementation of SAP Note 2758146: The data object 'C_FILE_NAME' does not have a component called 'FDQ'.", "solution": "Check if SAP Note 2310438 is de-implemented and ensure all related notes are correctly applied. Provide system access to SAP Support for further investigation.", "cause": "Older error related to 'C_FILE_NAME' potentially not addressed due to incomplete or incorrect application of prerequisite notes.", "processed_at": "2025-06-25T16:03:42.276435"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d62222ccdbe22958bf57fef1f396198e", "title": "CL_TMW_RC_COLLECT_BPA cannot be deleted as per sapnote 3221283", "problem": "Inability to delete function modules as per SAP Note 3221283 due to dependencies: CL_TMW_RC_COLLECT_BPA used in multiple classes/interfaces.", "solution": "Forward the incident to the correct SAP support component (SV-SCS-S4R) for assistance with deletion procedures and evaluation of dependencies.", "cause": "Incorrect component selection for the incident and existing dependencies of the function module across multiple classes/interfaces.", "processed_at": "2025-06-25T16:03:42.276448"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6a53f827c3f22914fabf018dc00131fa", "title": "Readiness Check Issue", "problem": "SAP Note 2972792 cannot be applied correctly, resulting in incomplete corrections and syntax errors.", "solution": "1. Checked and updated all prerequisites for the SAP Note. 2. Verified the code expected by the note for missing lines. 3. Requested customer to run RTCCTOOL in SE38 and ensure updated ST-PI and ST-A/PI versions are installed. 4. Asked customer to open the R/3 connection and maintain logon data for SAP to attempt reapplication of the note.", "cause": "The code expected by SAP Note 2972792 is missing lines, leading to incomplete corrections and syntax errors.", "processed_at": "2025-06-25T16:03:48.465639"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/904414f71b122918d41feca8ab4bcb25", "title": "FI details missing in the S4hana readiness Report", "problem": "FI details missing in SAP Readiness Check for SAP S4HANA Conversion.", "solution": "Reviewed KBA 2968380 - SAP Readiness Check Report 2.0 - troubleshooting guide. Customer instructed to check this guide and provide feedback for further assistance.", "cause": "Incorrect naming conversion for FICO leading to missing data in Financial Data Quality sections.", "processed_at": "2025-06-25T16:03:52.721855"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/df6df43b1bdaed94278cb9918b4bcbbc", "title": "RC_COLLECT_ANALYSIS_DATA- dump on /SDF/FDQ_CHECK_PREREQUISITES", "problem": "RC_COLLECT_ANALYSIS_DATA program dumps with 'Method CHECK_FIML_PREREQ does not exist' error.", "solution": "Implemented SAP Notes 2972792 and 2909538 to update Financial Data Quality prerequisites.", "cause": "Missing method CHECK_FIML_PREREQ in the program include.", "processed_at": "2025-06-25T16:03:59.013308"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/df6df43b1bdaed94278cb9918b4bcbbc", "title": "RC_COLLECT_ANALYSIS_DATA- dump on /SDF/FDQ_CHECK_PREREQUISITES", "problem": "FI-AA Analysis job dumps with SYNTAX_ERROR in CL_RC_FDQ_FI_AA_MONITOR.", "solution": "Implemented SAP Note 3155858 to resolve syntax error related to unknown type AA_T_ANLZ.", "cause": "Syntax error due to unknown type AA_T_ANLZ.", "processed_at": "2025-06-25T16:03:59.013321"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/df6df43b1bdaed94278cb9918b4bcbbc", "title": "RC_COLLECT_ANALYSIS_DATA- dump on /SDF/FDQ_CHECK_PREREQUISITES", "problem": "Authorization issue causing 'No authority for monitoring' error in RC_COLLECT_ANALYSIS_DATA.", "solution": "Assigned authorization object SM_BPM_DET with 'OBJECT_MS' characteristic to the user executing the report.", "cause": "Missing authorization for monitoring objects.", "processed_at": "2025-06-25T16:03:59.013323"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e999eae4c3b26d10cebe6610a0013130", "title": "Prerequisite up-to-date SAP note 2972792 for Financial Data Quality is not installed", "problem": "Prerequisite up-to-date SAP note 2972792 for Financial Data Quality is not installed despite all manual activities being completed.", "solution": "Check system SER for SAP note 2972792 status, identify inactive and non-consistent objects related to the note. Request customer to activate these objects or approve activation. Suggest de-implementing and re-implementing the SAP note with customer's approval.", "cause": "SAP note 2972792 was incompletely de-implemented, leading to inactive and non-consistent objects.", "processed_at": "2025-06-25T16:04:03.569644"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e999eae4c3b26d10cebe6610a0013130", "title": "Prerequisite up-to-date SAP note 2972792 for Financial Data Quality is not installed", "problem": "SAP Note 2972792 cannot be implemented in the customer's system.", "solution": "Advise customer to implement SAP Note 2909538 first, then attempt to run the report again.", "cause": "", "processed_at": "2025-06-25T16:04:03.569657"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a6687ed61b062190442fddb69b4bcb98", "title": "SAP BW/4HANA Readiness Check report - not pulling Add-On Compatibility Analysis collector data", "problem": "Add-On Compatibility Analysis collector data not present in SAP BW/4HANA Readiness Check report.", "solution": "Check 'Additional Software Details' in Maintenance Planner for Active Business Functions. If no items are maintained, the readiness check report will show 'No data'. Follow steps in SAP note 2847830.", "cause": "No Active Business Functions or relevant add-ons maintained in Maintenance Planner, resulting in 'No data' in the readiness check report.", "processed_at": "2025-06-25T16:04:07.634507"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4ef998b3478765901d1ff2b5536d4356", "title": "SAP Readiness Check Short dump on  FIN_CORR_REC_ANALYSIS", "problem": "Short dump on FIN_CORR_REC_ANALYSIS due to SQL0912N error indicating max locks reached.", "solution": "Increase INSTANCE_MEMORY significantly using STMM to allow more LOCKLIST, enabling the job to complete. Alternatively, consider setting DB2_AVOID_LOCK_ESCALATION=false temporarily.", "cause": "Excessive lock requests consuming available LOCKLIST memory.", "processed_at": "2025-06-25T16:04:13.817745"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4ef998b3478765901d1ff2b5536d4356", "title": "SAP Readiness Check Short dump on  FIN_CORR_REC_ANALYSIS", "problem": "Parent job RC_COLLECT_ANALYSIS_DATA never completes.", "solution": "Extend the retention period temporarily during job execution to prevent deletion of completed child jobs.", "cause": "Job running for more than the retention period causing deletion of prerequisite jobs.", "processed_at": "2025-06-25T16:04:13.817767"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d3d6b79ddb462910bf57fef1f396193e", "title": "Report RC_HCM_COLLECT_ANALYSIS_DATA is taking long time", "problem": "Report RC_HCM_COLLECT_ANALYSIS_DATA is taking an unusually long time to complete, running for over two weeks without finishing.", "solution": "The ticket was closed by the customer after the project was put on hold by management. No further action was requested.", "cause": "The report is new and it's unclear whether the lengthy runtime is normal or due to another issue.", "processed_at": "2025-06-25T16:04:18.351907"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a2d013101bee6518278cb9918b4bcba1", "title": "ERROR while running System readiness report", "problem": "Unable to generate system readiness report due to mismatched software component release versions and missing component during S/4 Hana upgrade preparation.", "solution": "Provide alternate solution for generating system readiness report. Review and update mismatched SAP_BASIS and S4CORE component versions; ensure SAP_APPL component is installed.", "cause": "Mismatched software component versions and missing SAP_APPL component prevent system readiness report generation.", "processed_at": "2025-06-25T16:04:22.945918"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a2d013101bee6518278cb9918b4bcba1", "title": "ERROR while running System readiness report", "problem": "ABAP runtime error RAISE_EXCEPTION with condition 'SMDB_CONTNET_NOT_FOUND' during system readiness report generation.", "solution": "Verify SUPPORT_PORTAL/PARCELBOX connection setup. Manually download and upload SIC content as per SAP KBA 2951527 if connection issues persist.", "cause": "Incorrect setup of SUPPORT_PORTAL/PARCELBOX connection causing SIC content download failure.", "processed_at": "2025-06-25T16:04:22.946001"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/055203901b6ee9181c7fea836b4bcba6", "title": "Readiness check for S4 HANA 2022", "problem": "The SAP readiness check report RC_COLLECT_ANALYSIS_DATA is showing very less scope for the S4HANA upgrade from 2020 to 2022.", "solution": "Explained that this is normal behavior for S/4 upgrades compared to ERP systems and referenced SAP Note 3059197 for further details.", "cause": "The Readiness Check for S/4 upgrades inherently contains less scope compared to the readiness check for ERP systems.", "processed_at": "2025-06-25T16:04:26.676507"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/116cc4301b2629d8442fddb69b4bcb84", "title": "TMW_RC_FDQ_DATA_COLL", "problem": "Financial Data Quality (FDQ) tile is empty in SAP Readiness Check report.", "solution": "Activate the finance option in the SAP Readiness Check job settings. Schedule a new Readiness Check report with only the finance option selected. Append the results of the finance data collection to the existing RC report.", "cause": "Finance option was de-selected while executing the RC job, preventing the TMW_RC_FDQ_DATA_COLL job from starting.", "processed_at": "2025-06-25T16:04:30.990352"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/087e7f241b6ee5d8442fddb69b4bcb1a", "title": "TMW_RC_FDQ_DATA_COLL", "problem": "FDQ tile is empty in SAP Readiness Check report.", "solution": "Activate the finance option in the SAP Readiness Check job to trigger the TMW_RC_FDQ_DATA_COLL job and populate the finance tile data. Then schedule a new RC report focusing only on the finance option and append the results to the existing report.", "cause": "Finance option was de-selected by default during the SAP Readiness Check job, preventing the related data collection process from starting.", "processed_at": "2025-06-25T16:04:35.336706"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/087e7f241b6ee5d8442fddb69b4bcb1a", "title": "TMW_RC_FDQ_DATA_COLL", "problem": "Duplicate case created by mistake.", "solution": "Confirmed the case as a duplicate and requested validation from the customer.", "cause": "", "processed_at": "2025-06-25T16:04:35.336721"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8e269901dba265d8f7590b07f39619f7", "title": "S/4HANA Technical Upgrade", "problem": "Add-On Compatibility tile is empty during S/4HANA upgrade readiness check.", "solution": "Refer to SAP KBA 2847830 for resolving the issue with SAP Readiness Check 2.0 where 'Add-On Compatibility' and 'Active Business Functions' tiles are empty.", "cause": "", "processed_at": "2025-06-25T16:04:39.823943"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/106e0b591b02a510992699798b4bcb69", "title": "Unable to import TR to Quality System", "problem": "Unable to import Transport Request (TR) to Quality System with Return Code 8 due to syntax errors and missing objects.", "solution": "Update BSQ system to ST-PI 2008_1_700 SP20; re-import TR BSDK904892 and then import TR related to Snote 2228460 to resolve missing objects and syntax errors.", "cause": "Mismatch in SP versions between development system BSD (SP20) and quality system BSQ (SP14), causing compatibility issues during TR import.", "processed_at": "2025-06-25T16:04:48.130408"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/106e0b591b02a510992699798b4bcb69", "title": "Unable to import TR to Quality System", "problem": "Syntax error related to unknown type '/SDF/CL_RC_S4SIC_SAMPLE' in various programs during import.", "solution": "Implement corrections from Snote 2228460 in development system BSD and import respective TR to BSQ.", "cause": "Incomplete implementation of necessary corrections in the development system BSD.", "processed_at": "2025-06-25T16:04:48.130416"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/106e0b591b02a510992699798b4bcb69", "title": "Unable to import TR to Quality System", "problem": "Syntax error related to missing INCLUDE report '/SDF/RC_EVENT_CLASS' during TR import.", "solution": "Ensure implementation of Snote 2228460 and re-import TR related to the note.", "cause": "Missing INCLUDE report '/SDF/RC_EVENT_CLASS' not addressed during initial implementation.", "processed_at": "2025-06-25T16:04:48.130419"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/106e0b591b02a510992699798b4bcb69", "title": "Unable to import TR to Quality System", "problem": "Syntax error due to the data object 'PARALLEL_CUSTOMIZING' lacking a component called 'ALLSERVER'.", "solution": "Implement Snote 2228460 to address table definition issues.", "cause": "Missing components in the data object 'PARALLEL_CUSTOMIZING' due to incomplete table definition updates.", "processed_at": "2025-06-25T16:04:48.130420"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/106e0b591b02a510992699798b4bcb69", "title": "Unable to import TR to Quality System", "problem": "Different number of parameters in FORM and PERFORM routine causing syntax error in program SAPLSC<PERSON>L.", "solution": "Open an incident on the component BC-UPG-NA for detailed analysis and resolution.", "cause": "Mismatch in parameters between FORM and PERFORM routine not resolved.", "processed_at": "2025-06-25T16:04:48.130421"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c2ce76ec1b8a6d90992699798b4bcb1f", "title": "Encountering a dump when running SRC", "problem": "Encountering a dump when running SAP Readiness Check due to exception condition 'SMDB_CONTNET_NOT_FOUND'.", "solution": "1. Check if SAP-SUPPORT_PORTAL connection is set up using transaction code SM59. 2. Manually download Simplification Item Catalog (SIC) content from SAP Support Portal. 3. Upload downloaded SIC content into the system using the provided steps in SAP KBA 2951527.", "cause": "The dump is caused because the SUPPORT_PORTAL connection is not setup, resulting in SIC content not being downloadable from SUPPORT_PORTAL.", "processed_at": "2025-06-25T16:04:52.522836"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f41cdfd91b8a2910278cb9918b4bcb97", "title": "Unable to run Readiness checks In ECC", "problem": "Unable to run S4HANA Readiness checks in SAP ECC system due to irrelevant prerequisite SAP notes.", "solution": "1. Re-implement SAP Note 2758146 and follow the prerequisite notes. 2. Ensure that the correct version of SAP Note 2758146 is implemented. 3. Update the note from SNOTE to the latest version. 4. Manually activate objects during implementation if there are warnings like 'object already exists and will be overwritten'.", "cause": "Prerequisite SAP notes required for the Readiness Check were not correctly implemented or were not applicable to the customer's system.", "processed_at": "2025-06-25T16:04:57.139829"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0d634162c3576558c52cb32f050131bd", "title": "Readiness Check fails because of SID", "problem": "Readiness Check fails due to system ID mismatch between SAP Focused Run and Maintenance Planner.", "solution": "1. Manually upload the system XML to Maintenance Planner using SAP Note 2408911. 2. Delete the system from Maintenance Planner and re-upload the data from LMDB to SAP.", "cause": "The readiness check tries to find the system UAP in Maintenance Planner, while it is uploaded by Focused Run as UAPHUE due to extended SID format, which is not supported for automatic uploads.", "processed_at": "2025-06-25T16:05:02.905987"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0d634162c3576558c52cb32f050131bd", "title": "Readiness Check fails because of SID", "problem": "Errors in tables for 'Add-On-Compatibility' and 'Active Business Functions' after uploading data to Readiness Check website.", "solution": "Ensure proper synchronization of system data using SAP Note 2408453 and SAP Note 2887157. Address any postbox connectivity issues by applying Backbone connectivity settings from the FRUN Masterguide.", "cause": "Connectivity issues with SAP Support Hub and incorrect SID format causing data discrepancies during upload.", "processed_at": "2025-06-25T16:05:02.905997"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/835ef1251bd66114406c33bd8b4bcbb6", "title": "*PERSON* 2972792", "problem": "Transport of SAP Note 2972792 failed with return code 8 in test system Q11.", "solution": "Download the latest version of SAP Note 2972792 and re-implement it in the DEV system before transporting again to Q11.", "cause": "Older version of SAP Note 2972792 was marked non-implementable due to SPAU cleanup.", "processed_at": "2025-06-25T16:05:06.616330"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/41897c831bea659c992699798b4bcbdc", "title": "SAP readiness report setup - pre-reqs", "problem": "Customer needs to install ST-A/PI version 01T or higher for Pathfinder report setup but currently has version 01S_731.", "solution": "Confirm that ST-A/PI version 01T or higher is mandatory and cannot be bypassed.", "cause": "ST-A/PI version 01T or higher is required for the proper functioning of the Pathfinder report setup.", "processed_at": "2025-06-25T16:05:11.154018"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/41897c831bea659c992699798b4bcbdc", "title": "SAP readiness report setup - pre-reqs", "problem": "Customer does not have Solution Manager connected to on-prem servers and queries if EWA can be bypassed for Pathfinder report setup.", "solution": "Confirm that EWA is generally a prerequisite but can be configured without Solution Manager if the Managed System is connected to SAP Backend. Reference SAP Note ##207223.", "cause": "EWA service is a standard prerequisite for Pathfinder report setup, but alternative configurations are possible.", "processed_at": "2025-06-25T16:05:11.154046"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7f098f551bae6d5c406c33bd8b4bcbaf", "title": "Issue while importing SPDD/SPAU Transport after EHP upgrade", "problem": "Error RC=8 while importing SPDD/SPAU transport after EHP upgrade.", "solution": "1. De-implement SAP Note 2310438. 2. Reimplement SAP Note 2758146. 3. Ensure SAP Note 2758146 is completely implemented in SNOTE. 4. Reinitiate the transport.", "cause": "SAP Note 2310438 was implemented causing syntax errors and incomplete implementation status of SAP Note 2758146.", "processed_at": "2025-06-25T16:05:15.573950"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7f098f551bae6d5c406c33bd8b4bcbaf", "title": "Issue while importing SPDD/SPAU Transport after EHP upgrade", "problem": "Connection error: 'partner not reached, Connection refused'.", "solution": "Review SAP Note 1178631 for steps to resolve partner connection issues.", "cause": "", "processed_at": "2025-06-25T16:05:15.573964"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d16326d31b4a6514d41feca8ab4bcbab", "title": "Note 2758146 from version 68 to 87", "problem": "Issue implementing the latest version of SAP Note 2758146 due to incomplete de-implementation of SAP Note 2310438.", "solution": "1. Completely deimplement SAP Note 2310438. 2. Deimplement the obsolete version of SAP Note 2758146. 3. Reimplement SAP Note 2758146. 4. Follow manual steps from SAP Note 2717209 if issue persists. 5. Refer to SAP Note 3221283 for specific solutions (Solution 4, 5, 6).", "cause": "SAP Note 2310438 was incompletely implemented, causing issues with updating SAP Note 2758146. Some objects from both notes were not deleted during deimplementation.", "processed_at": "2025-06-25T16:05:20.694238"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/970a6a6cc3b26d10cebe6610a00131c2", "title": "SAP Readiness Check 2.0 - Need to Decrease FDQ Running Time", "problem": "FDQ analysis during SAP Readiness Check is taking several days to complete.", "solution": "Review and adjust the data volume being processed by archiving or housekeeping to cover only necessary years. Consider using parallelization options outlined in SAP documentation.", "cause": "The duration of FDQ analysis is dependent on the amount of data processed and related settings, with no option to narrow down years in analysis.", "processed_at": "2025-06-25T16:05:24.537904"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1638c92ec3b2ed505fb42c65e00131f3", "title": "object \"C_FILE_NAME\" does not have a component. More details of error attached in the screenshot", "problem": "Syntax error occurs for few objects while updating SAP notes 2758146 and 2745851 to latest versions.", "solution": "Implement the latest version of SAP notes as suggested in SAP note 3221283, including updating SAP note 1668882 to resolve syntax errors. Perform SPAU activity for SAP note 1668882 to address conflicts with EHP8 SPAU transports.", "cause": "Conflicts with EHP8 SPAU transports and locked objects due to resetting and re-implementing SAP notes to their latest versions.", "processed_at": "2025-06-25T16:05:29.796348"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1638c92ec3b2ed505fb42c65e00131f3", "title": "object \"C_FILE_NAME\" does not have a component. More details of error attached in the screenshot", "problem": "Syntax error dump in class /SDF/CL_S4RC20 when executing program RC_COLLECT_ANALYSIS_DATA.", "solution": "Conduct research using SAP note 3283120 related to syntax error dump in class /SDF/CL_S4RC20 to identify possible solutions and involve colleagues from another support component for further assistance.", "cause": "", "processed_at": "2025-06-25T16:05:29.796370"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/111c531d1b8a2910278cb9918b4bcb28", "title": "technical errors in S/4HANA Readiness Check", "problem": "Technical errors occurred in the S/4HANA Readiness Check for 'Add-On Compatibility' and 'Active Business Functions' tiles.", "solution": "A refresh of the Readiness Check was performed, which resolved the issue by displaying the missing data.", "cause": "Technical error prevented the execution of the checks, leading to empty tiles.", "processed_at": "2025-06-25T16:05:33.639410"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a7192f8397b26550cdaf79171153afb6", "title": "Readiness report not working for ML data", "problem": "Material Ledger jobs do not trigger automatically when executing the Readiness Report in ECC.", "solution": "1. Implement SAP Notes 3130905 and 3129858 and execute the report 'NOTE_3130905' in Update & Activate mode. 2. Check system logs and ensure no errors in job execution. 3. Use SAP Note 2972792 to trigger data collection manually. 4. Review job logs for errors and perform necessary troubleshooting steps as per KBA 2968380.", "cause": "Incorrect or incomplete implementation of SAP Note 2972792 and its prerequisites, leading to syntax errors and inconsistent system objects.", "processed_at": "2025-06-25T16:05:40.960568"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a7192f8397b26550cdaf79171153afb6", "title": "Readiness report not working for ML data", "problem": "Material Ledger Financial Data Quality (FDQ) not generating after implementing SAP Notes.", "solution": "Re-implement SAP Note 2972792 and ensure all steps are followed correctly. Check for any inconsistent objects using SAP Note 3289850 and clean them. Ensure the correct system settings and flags are enabled as per SAP Note 2906603.", "cause": "Previous inconsistent implementations of SAP Note 2972792 causing syntax errors and inconsistent objects in the system.", "processed_at": "2025-06-25T16:05:40.960596"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a7192f8397b26550cdaf79171153afb6", "title": "Readiness report not working for ML data", "problem": "Unable to de-implement obsolete version of SAP Note 2972792.", "solution": "Refer to SAP Note 3289850 to check and clean inconsistent objects. Use the cleanup report provided in the note to resolve de-implementation issues.", "cause": "Inconsistent status of objects resulting from previous implementations of SAP Note 2972792 that were not successfully deleted during de-implementation.", "processed_at": "2025-06-25T16:05:40.960601"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6277ba6cc33e6d10b118f71ad0013109", "title": "Expert mode Screen parameter for FDQ analysis execution of RC_COLLECT_ANALYSIS_DATA report in production enviornment", "problem": "Short dump due to lock issue during FDQ data analysis execution of RC_COLLECT_ANALYSIS_DATA report in production environment.", "solution": "1. Review SAP Note ##2972792 for correct parameter settings in expert mode. 2. Execute jobs with reduced parallelism to minimize locking issues. 3. Collaborate with DB team to adjust database settings as per their recommendations.", "cause": "The maximum number of lock requests reached for the database, causing runtime dump.", "processed_at": "2025-06-25T16:05:45.341357"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7f349bd2dbc22d90769ad47cd39619b6", "title": "RAISE_EXCEPTION dump while running ABAP Program RC_COLLECT_ANALYSIS_DATA", "problem": "RAISE_EXCEPTION dump occurs while running ABAP Program RC_COLLECT_ANALYSIS_DATA in QA S4HANA system.", "solution": "Correct the RFC configuration. Ensure SUPPORT_PORTAL connection is properly set up to allow downloading SIC content. Manually download SIC content and upload it into the system using SAP KBA 2951527.", "cause": "SUPPORT_PORTAL connection was not set up, preventing SIC from being downloaded, leading to the exception condition 'SMDB_CONTNET_NOT_FOUND'.", "processed_at": "2025-06-25T16:05:49.939869"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/89ade5d847fee1101cccb7da216d4377", "title": "Readiness Check Pre-Req Note Already implemented", "problem": "Readiness Check report RC_COLLECT_ANALYSIS_DATA shows notification 'Prerequisite up-to-date SAP Note 2827612 for Innovation Potential is not installed.' despite ST-PI Version 740 p20.", "solution": "Verify if SAP Note 2827612 is indeed installed or required for ST-PI Version 740 p20. Provide access to the production client of Solution Manager P01 for thorough investigation.", "cause": "SAP Note 2827612 might be incorrectly flagged as missing due to version compatibility or incorrect system settings.", "processed_at": "2025-06-25T16:05:53.928086"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/49aac63c1b1ea150d41feca8ab4bcb30", "title": "GL and AA cause technical problem", "problem": "Error in SAP Readiness Check for General Ledger and Asset Accounting.", "solution": "Clear the buffer for FDQ by selecting only FDQ scope and clicking 'Delete Buffered Data for Selected Items'. Start Readiness check analysis again to ensure jobs for FDQ are starting correctly.", "cause": "New analysis was reading from buffered data containing old analysis with missing data.", "processed_at": "2025-06-25T16:05:57.779636"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/49aac63c1b1ea150d41feca8ab4bcb30", "title": "GL and AA cause technical problem", "problem": "Incomplete FDQ GL and AA results in uploaded files.", "solution": "Verify status of online collectors by executing RTCCTOOL and fix any errors. Check SAP note 2972792 to enable and verify online collectors.", "cause": "RTCCTOOL shows an error 'Update of verification PSE failed'.", "processed_at": "2025-06-25T16:05:57.779662"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/50ab1668473ae9101cccb7da216d4365", "title": "Readiness Check Error SV-SCS-S4R", "problem": "Custom Web Service Interfaces check could not be executed due to a technical issue.", "solution": "Changed the XML and provided a zip file attachment for the customer to download and update their existing analysis.", "cause": "Issue originated from the cloud application.", "processed_at": "2025-06-25T16:06:02.275702"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/50ab1668473ae9101cccb7da216d4365", "title": "Readiness Check Error SV-SCS-S4R", "problem": "RFC/BAPI Interfaces check could not be executed due to a technical issue.", "solution": "Requested customer to perform AUTHORITY-CHECK for specific objects and provided a reference to SAP Note 3061414 for detailed information.", "cause": "Lack of authority for specific RFC interface checks.", "processed_at": "2025-06-25T16:06:02.275727"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f0cadbc0eb4252907d05f7510bd0cd07", "title": "Cant upload ATC custom code check to readiness", "problem": "Unable to upload ATC custom code check results to SAP Readiness Check.", "solution": "The solution involved implementing SAP Note 0002781766 for enabling ATC check result export for SAP Readiness Check 2.0. Additionally, SAP Note ********** was checked for further guidance. The customer was advised to ensure the file was downloaded using the correct reports (RC_COLLECT_ANALYSIS_DATA or SYCM_DOWNLOAD_REPOSITORY_INFO) as specified in SAP Note 2913617. Development team engagement was suggested if the note was already implemented but issue persisted.", "cause": "The uploaded file was not valid due to incorrect download method, as it required specific reports for compatibility with SAP Readiness Check.", "processed_at": "2025-06-25T16:06:06.792010"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3e30e153c356d2108710df00a001316b", "title": "Readiness Job is running since long", "problem": "Financial Data Quality job is running for an extended period, impacting S/4 Hana pre-study project.", "solution": "1. Recommended scheduling the Financial Data Quality check separately from other SAP Readiness Check data collectors. 2. Ensure the implementation of the latest SAP Note 2972792 for Financial Data Quality checks.", "cause": "The duration of the Financial Data Quality check depends on the volume of financial data and system performance, which can result in extended runtimes.", "processed_at": "2025-06-25T16:06:10.756023"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/899f802bc3825a503331583bb00131ad", "title": "Error while implementing Notes for Readiness Check", "problem": "Error while implementing SAP Note 2758146 for Readiness Check in SNOTE transaction.", "solution": "Switch off the 'Modification Assistant' in transaction SE24 using Edit -> Modification Operation -> Switch off assistant. Reset the note, manually delete all undeleted objects, and implement the note again.", "cause": "Modification Assistant was switched on, causing restriction in modification/enhancement mode.", "processed_at": "2025-06-25T16:06:15.151366"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/899f802bc3825a503331583bb00131ad", "title": "Error while implementing Notes for Readiness Check", "problem": "Activation error for Function Module EDOC_CHECK_SUBOBJTYPE during SAP Note implementation.", "solution": "Switch off 'Modification Assistant' in SE24 and perform changes. Ensure objects are manually deleted if necessary and re-import the note if not in the development system.", "cause": "Syntax error occurred due to the activation process in modification mode.", "processed_at": "2025-06-25T16:06:15.151390"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5f995b62c3125618b118f71ad00131d5", "title": "while executing program RC_VALUE_DISCOVERY_COLL_DATA in prd system we are getting wrong output.", "problem": "Executing program RC_VALUE_DISCOVERY_COLL_DATA in production system yields incorrect output.", "solution": "Reviewed chat session, confirmed duplicate issue, advised customer to close ticket and focus on existing case 8195017/2024.", "cause": "Duplicate case.", "processed_at": "2025-06-25T16:06:19.099798"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f6bd79aec3cbd210ca3f1c0bb00131da", "title": "[C]No data collected in ppi_petst03n01_00001 file after running program RC_VALUE_DISCOVERY_COLL_DATA for Signavio data", "problem": "No data collected in ppi_petst03n01_00001 file after running program RC_VALUE_DISCOVERY_COLL_DATA for Signavio data upload.", "solution": "1. Change OS timezone to match SAP system timezone as per note 1841468. 2. Ensure ST03N transaction has data for last two months. 3. Execute RC_VALUE_DISCOVERY_COLL_DATA after confirming data availability.", "cause": "Timezone mismatch between OS and SAP system caused ST03N not to collect data, resulting in empty ppi_petst03n01_00001 file.", "processed_at": "2025-06-25T16:06:25.569800"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f6bd79aec3cbd210ca3f1c0bb00131da", "title": "[C]No data collected in ppi_petst03n01_00001 file after running program RC_VALUE_DISCOVERY_COLL_DATA for Signavio data", "problem": "ST03N does not have any data entries.", "solution": "1. Confirm timezone settings on both OS and SAP application level. 2. Perform necessary timezone maintenance. 3. Wait for sufficient data collection in ST03N.", "cause": "Timezone change led to ST03N not collecting data.", "processed_at": "2025-06-25T16:06:25.569823"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/add654e4c38e92d013c299bc7a0131a9", "title": "https://me.sap.com/readinesscheck refused to connect", "problem": "Unable to connect to SAP Readiness Check webpage, resulting in an empty page with error message 'account.sap.com refused to connect'.", "solution": "1. Clear the browser cache completely. 2. <PERSON><PERSON> accessing the webpage.", "cause": "Browser cache interference preventing page loading.", "processed_at": "2025-06-25T16:06:29.801832"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/99ea4495c36e5ed04daa2875e00131ba", "title": " Executing sap readiness check", "problem": "RC_COLLECT_ANALYSIS_DATA job fails at start-up in SAP readiness check execution.", "solution": "1. Review SAP Note ********** for troubleshooting guidance under CVI scope. 2. Apply corrections from SAP Note ********** addressing error messages from underlying APIs. 3. Assign DVM authorization role for complete scope usage if needed.", "cause": "Error messages raised from underlying APIs during background job execution.", "processed_at": "2025-06-25T16:06:35.994706"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/99ea4495c36e5ed04daa2875e00131ba", "title": " Executing sap readiness check", "problem": "Child job TMW_RC_DVM* failing during RC_COLLECT_ANALYSIS_DATA execution.", "solution": "Follow resolution steps in KBA 0003392704 related to TMW_RC_DVM_DATA_COLL job cancellation after system exception.", "cause": "", "processed_at": "2025-06-25T16:06:35.994742"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/99ea4495c36e5ed04daa2875e00131ba", "title": " Executing sap readiness check", "problem": "Multiple child jobs failing due to unmet prerequisites in SAP readiness check.", "solution": "1. Review SAP Note ********** for prerequisites of DVM, FDQ, and SI Check scopes in readiness check. 2. Ensure prerequisites are fulfilled before selecting scopes for analysis.", "cause": "Unmet prerequisites for scope analysis in readiness check.", "processed_at": "2025-06-25T16:06:35.994748"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/99ea4495c36e5ed04daa2875e00131ba", "title": " Executing sap readiness check", "problem": "Authorization issue preventing access to SAP/KBA note 0003444628.", "solution": "Release KBA 0003444628 for customer access and ensure authorization fit for target group.", "cause": "Customer profile does not fit authorization requirements for accessing KBA note.", "processed_at": "2025-06-25T16:06:35.994753"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b234218fc3c61250b118f71ad0013167", "title": "Issue with Integration data in S/4 2023 Readiness check", "problem": "Integration analysis does not read the imported data store created by SAP note 2568736.", "solution": "Enhance reminder message and ensure data is run on a system with the required information.", "cause": "Imported data store was not being recognized by the integration analysis tool.", "processed_at": "2025-06-25T16:06:42.877082"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b234218fc3c61250b118f71ad0013167", "title": "Issue with Integration data in S/4 2023 Readiness check", "problem": "No data available for RFC and BAPI interfaces in S/4HANA readiness check report.", "solution": "Run report RC_COLLECT_ANALYSIS_DATA for the interface type and upload the generated ZIP file to SAP Readiness Check.", "cause": "", "processed_at": "2025-06-25T16:06:42.877092"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b234218fc3c61250b118f71ad0013167", "title": "Issue with Integration data in S/4 2023 Readiness check", "problem": "IDOC file interface_idoc.xml generated with data but uploaded analysis indicates an error.", "solution": "Transfer case to development team and confirm prerequisites and data collection steps to identify potential new bug.", "cause": "Potential new bug affecting IDOC analysis despite successful data collection.", "processed_at": "2025-06-25T16:06:42.877093"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b234218fc3c61250b118f71ad0013167", "title": "Issue with Integration data in S/4 2023 Readiness check", "problem": "Authorization issues with user running the readiness check report.", "solution": "Verify user authorizations against SAP notes 3061414 and 2913617; ensure authorization object S_PROGRAM is assigned.", "cause": "User ID lacked required authorizations for scheduling data collectors.", "processed_at": "2025-06-25T16:06:42.877094"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b234218fc3c61250b118f71ad0013167", "title": "Issue with Integration data in S/4 2023 Readiness check", "problem": "Implementation of SAP note 2758146 caused persistent issues in readiness check.", "solution": "Re-implement note 2758146, clear buffered data and re-run integration analysis before appending results to SAP for Me.", "cause": "Error during implementation of SAP note 2758146 documented in troubleshooting guide.", "processed_at": "2025-06-25T16:06:42.877095"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/46c332b0c326525030037d9f05013139", "title": "SAP readiness check report generation issue", "problem": "Unable to run program RC_COLLECT_ANALYSIS_DATA due to empty dropdown for Target S/4HANA version.", "solution": "Refer to SAP Note 2951527 and follow the solution steps for resolving the empty dropdown issue for target release.", "cause": "Missing connection from the system to SAP to update the target version.", "processed_at": "2025-06-25T16:06:48.426307"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/46c332b0c326525030037d9f05013139", "title": "SAP readiness check report generation issue", "problem": "Program RC_HCM_COLLECT_ANALYSIS_DATA fails with a fatal error after running for 3 hours.", "solution": "Ensure all prerequisite notes are correctly implemented and verify system compatibility for running the analysis program.", "cause": "", "processed_at": "2025-06-25T16:06:48.426327"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1a84f2f4c326525030037d9f050131b1", "title": "SPIDE report getting cancelled", "problem": "SPIDE report cancellation due to memory dumps.", "solution": "1. Analyze the report execution failure. 2. Identify problematic KPIs causing memory dump. 3. Remove KPI 206 from the report. 4. Follow steps in KBA 0002197502 to manually execute data collection for removed KPIs.", "cause": "Memory dumps during processing of large KPIs.", "processed_at": "2025-06-25T16:06:53.252247"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1a84f2f4c326525030037d9f050131b1", "title": "SPIDE report getting cancelled", "problem": "Installation and implementation issues with SAP Notes 2745851 and 2758146 for SPIDE Report.", "solution": "1. Follow instructions from the Process Discovery How-To PDF. 2. Install the required SAP Notes correctly. 3. Verify readiness for S4 report system.", "cause": "Incorrect installation or implementation of SAP Notes leading to report execution failure.", "processed_at": "2025-06-25T16:06:53.252265"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1ab9d36feb3e92187d05f7510bd0cd08", "title": "Recommended Fiori Apps in SAP Readiness Check", "problem": "Recommended Fiori Apps section in SAP Readiness Check is not populated as expected.", "solution": "1. Requested customer to provide Readiness Check data collection ZIP file for review. 2. Verified the 'transaction_usage.xml' file within ZIP did not contain relevant data. 3. Informed customer that ST03 on system QE8 must hold historical workload data for at least the last 3 months to populate the check.", "cause": "The 'transaction_usage.xml' file is empty due to test system QE8 not holding historical workload data, which is common for test and demo systems.", "processed_at": "2025-06-25T16:06:58.127120"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1ab9d36feb3e92187d05f7510bd0cd08", "title": "Recommended Fiori Apps in SAP Readiness Check", "problem": "Customer received recommended Fiori apps despite 'transaction_usage.xml' being empty and seeks clarification.", "solution": "Explained that 'transaction_usage.xml' not being empty indicates ST03 data is present, which allows the checks for recommended Fiori apps to be based.", "cause": "Misunderstanding regarding the basis of recommendation for Fiori apps due to empty 'transaction_usage.xml'.", "processed_at": "2025-06-25T16:06:58.127145"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a448ddb73bae5e506f96634a85e45a83", "title": "Function group is not available while implementing the SAP note 2972792", "problem": "Function group /SDF/FDQ_ASSESSMENT does not exist while implementing SAP note 2972792.", "solution": "Manually create the Function group /SDF/FDQ_ASSESSMENT under package /SDF/FDQ_API.", "cause": "The Function group was not automatically created during the SAP note implementation process.", "processed_at": "2025-06-25T16:07:06.770724"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a448ddb73bae5e506f96634a85e45a83", "title": "Function group is not available while implementing the SAP note 2972792", "problem": "Object R3TR CINS 0020751258212 could not be edited because it is locked in a task assigned to a non-existing or different client request.", "solution": "Assign the inconsistent task to an existing request or release the request if it's assigned to a different client.", "cause": "The task is locked under a request that is either non-existent or assigned to another client.", "processed_at": "2025-06-25T16:07:06.770754"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a448ddb73bae5e506f96634a85e45a83", "title": "Function group is not available while implementing the SAP note 2972792", "problem": "Incomplete implementation of SAP note due to unselected objects requiring changes or manual changes made beforehand.", "solution": "Ensure all required changes are selected in the Confirm Changes dialog box and avoid manual changes before implementing the note.", "cause": "Failure to select all necessary objects for changes or pre-existing manual changes causing implementation issues.", "processed_at": "2025-06-25T16:07:06.770758"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7fd0ff0d3beedad06f96634a85e45aeb", "title": "BSR report failing with dump TSV_TNEW_PAGE_ALLOC_FAILED.", "problem": "BSR report failing with dump TSV_TNEW_PAGE_ALLOC_FAILED.", "solution": "Increase memory parameters: abap/heap_area_dia to 8GB, abap/heap_area_nondia to 8GB, abap/heap_area_total to 16GB. Verify report variant and adjust selection criteria to reduce processed records.", "cause": "Memory resource shortage due to insufficient ABAP heap memory settings.", "processed_at": "2025-06-25T16:07:12.401012"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7fd0ff0d3beedad06f96634a85e45aeb", "title": "BSR report failing with dump TSV_TNEW_PAGE_ALLOC_FAILED.", "problem": "Memory dump occurring during job BUZ_SCENARIO_SIGNAVIO_PI_COL execution.", "solution": "Exclude KPI causing termination from data collection as per SAP KBA 2818045. Adjust memory parameters based on recommendations.", "cause": "Specific KPI causing excessive memory usage leading to TSV_LIN_ALLOC_FAILED dump.", "processed_at": "2025-06-25T16:07:12.401019"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/280d48a1c3bedad08710df00a001318f", "title": "FDQ Readiness Check, error during SAP Note Implementation 2972792 function group /SDF/FDQ_ASSESSMENT does not exist", "problem": "Error 'Function Group /SDF/FDQ_ASSESSMENT does not exist' when implementing SAP Note 2972792.", "solution": "1. Check manual steps on Note 2972792 regarding Financial Data Quality. 2. Implement the latest version of SAP Note 2909538, which addresses the code issue.", "cause": "Code issue in prerequisite SAP Note 2909538 where the implementation for the missing function group was commented out, preventing proper execution.", "processed_at": "2025-06-25T16:07:17.295232"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c579d814c3c71218b118f71ad0013163", "title": "No *PERSON* in rc_collect_analysis_data", "problem": "Empty target release dropdown in Readiness Check options screen.", "solution": "Refer to SAP Note 2951527 which provides a solution for the empty dropdown issue when selecting target release in S/4HANA or BW/4HANA readiness check.", "cause": "System already on latest version; no available version to select.", "processed_at": "2025-06-25T16:07:24.618701"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c579d814c3c71218b118f71ad0013163", "title": "No *PERSON* in rc_collect_analysis_data", "problem": "Missing Custom Code Analysis option in SAP Readiness Check for S/4HANA upgrades.", "solution": "Ensure SAP Note 3059197 is applied correctly for S/4HANA upgrades, and verify the system version compatibility for the readiness check.", "cause": "Incorrect SAP Note application or version compatibility issue.", "processed_at": "2025-06-25T16:07:24.618724"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c579d814c3c71218b118f71ad0013163", "title": "No *PERSON* in rc_collect_analysis_data", "problem": "Errors in rc_collect_analysis_data report execution related to ST-A/PI version.", "solution": "Upgrade ST-A/PI to the required release version as indicated by the SAP readiness check prerequisites.", "cause": "ST-A/PI version lower than required for executing certain checks.", "processed_at": "2025-06-25T16:07:24.618730"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c579d814c3c71218b118f71ad0013163", "title": "No *PERSON* in rc_collect_analysis_data", "problem": "SAP Notes incompletely implemented causing errors in readiness check execution.", "solution": "De-implement and re-implement SAP Notes 3010669 and 2811183 in the source system and transport them again to PRD.", "cause": "Incomplete implementation of SAP Notes transported from DEV or QAS system.", "processed_at": "2025-06-25T16:07:24.618734"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c579d814c3c71218b118f71ad0013163", "title": "No *PERSON* in rc_collect_analysis_data", "problem": "Network timeout when attempting to log onto system PRD.", "solution": "Ensure valid logon credentials and working network connections are maintained for system access.", "cause": "Network connection issue causing timeout.", "processed_at": "2025-06-25T16:07:24.618740"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c579d814c3c71218b118f71ad0013163", "title": "No *PERSON* in rc_collect_analysis_data", "problem": "Function group CVI_READINESS_CHECK does not exist error.", "solution": "Verify SAP Note 3010669 and ensure proper implementation of enhancements to SAP Readiness Check Dashboard.", "cause": "Missing function group required for readiness check enhancements.", "processed_at": "2025-06-25T16:07:24.618745"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f6f30ddb475ed21036bf68be436d436d", "title": "Long running S/4 HANA Readiness Check Jobs", "problem": "Long runtime for S/4 HANA Readiness Check data collection jobs, specifically for Financial Data Quality (FDQ) analysis.", "solution": "Confirmed that a 5-day runtime is common for FDQ data collection due to the volume of financial data and system performance. No issues identified as the process completed successfully.", "cause": "The long runtime is primarily due to the high volume of financial data and system performance constraints, rather than any configuration issues or errors.", "processed_at": "2025-06-25T16:07:28.705457"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/af5a8d7147ca56541cccb7da216d4341", "title": "Readinesscheck - can not create document", "problem": "Service Unavailable error during SAP Readiness Check document creation.", "solution": "1. Forwarded case to development support team. 2. Development team fixed the issue in the production system. 3. Customer advised to try generating the document again.", "cause": "", "processed_at": "2025-06-25T16:07:32.504336"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2e3e9052c30ade9430037d9f05013193", "title": "Readiness Check Report Download Problem", "problem": "Customer receives a 'Service unavailable' error when attempting to download Readiness Check Report from SAP for Me.", "solution": "- Confirmed customer has the necessary authorizations. - Transferred the issue to the appropriate component (SV-SCS-S4R) for further handling. - Requested customer to retry generating the document. - Customer confirmed successful download after retrying.", "cause": "Potential temporary service disruption or miscommunication between components handling the report generation.", "processed_at": "2025-06-25T16:07:36.706119"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4bbba73c35ed65013c299bc7a0131bc", "title": "SAP ICCM - Readiness Check Report", "problem": "Unclear request related to ICCM readiness check report for ECC system.", "solution": "Requested customer clarification on issue specifics and referred to KBA 0003404904 for guidance on SAP Readiness Check.", "cause": "Lack of clarity on customer's request, possibly referring to another case without providing details.", "processed_at": "2025-06-25T16:07:40.267447"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cc01becac3e652548710df00a0013161", "title": "SAP ICCM - Readiness Check Report", "problem": "SAP ICCM readiness check report execution issue in ECC Quality system.", "solution": "1. Reviewed the OSS message for login into ECC Quality system. 2. Provided troubleshooting guides: 'SAP Readiness Check for SAP S/4HANA' and 'SAP Access to Customer Analyses in SAP Readiness Check'.", "cause": "Access issues preventing execution of ICCM readiness check report in ECC Quality system.", "processed_at": "2025-06-25T16:07:44.207781"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a21d8386c3ea96d030037d9f05013129", "title": "SAP ICCM - Readiness Check Report", "problem": "Request to execute ICCM check for ECC readiness report in the ECC Production system.", "solution": "Reviewed the OSS message for login into ECC Production system to execute ICCM readiness check.", "cause": "Request by the user for an ECC readiness report execution.", "processed_at": "2025-06-25T16:07:48.138992"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a21d8386c3ea96d030037d9f05013129", "title": "SAP ICCM - Readiness Check Report", "problem": "Confusion over whether separate cases need to be opened for each system.", "solution": "Suggested to correspond in the DEV system case until clarity about the issue is achieved.", "cause": "Uncertainty about the specific issue needing a separate case for each system.", "processed_at": "2025-06-25T16:07:48.139018"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/eb71ebedc39ad2948710df00a0013151", "title": "Error while generating the S4 Hana Readiness - Prepare Analysis Data", "problem": "Error encountered while executing the RC_COLLECT_ANALYSIS_DATA report due to out-of-date Customer Vendor Integration Analysis API.", "solution": "Reset SAP Notes related to CVI, specifically note 3010669, to obtain the correct version details. Implement the note 2758146 after resetting.", "cause": "The SAP Note 3010669 was not implemented as the necessary changes were already included in the system's current SAP_APPL version.", "processed_at": "2025-06-25T16:07:53.666034"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/eb71ebedc39ad2948710df00a0013151", "title": "Error while generating the S4 Hana Readiness - Prepare Analysis Data", "problem": "The prerequisite SAP Note 2827612 for Innovation Potential is not installed, impacting the S4 Hana Readiness Check.", "solution": "Deselect the Innovation Potential checkbox in the report to bypass the error temporarily and run other checks. Await the release of the updated version of the note.", "cause": "The latest version of SAP Note 2827612 was not implemented, causing the Innovation Potential check to fail.", "processed_at": "2025-06-25T16:07:53.666057"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a6d1941a3b0e5a546f96634a85e45ad9", "title": "Upload and download functionality for Readiness Report is not working anymore", "problem": "Unable to upload ATC findings to Readiness Report due to error message indicating no custom code analysis implemented.", "solution": "Issue resolved by verifying system setup and ensuring custom code analysis feature is properly implemented.", "cause": "Incorrect system setup or missing implementation of custom code analysis feature.", "processed_at": "2025-06-25T16:07:58.007450"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a6d1941a3b0e5a546f96634a85e45ad9", "title": "Upload and download functionality for Readiness Report is not working anymore", "problem": "Unable to export RC2AnalysisData zip file due to error message indicating service unavailability.", "solution": "Service availability restored by checking system logs and resolving any operational errors causing service disruption.", "cause": "Temporary service unavailability due to operational errors.", "processed_at": "2025-06-25T16:07:58.007473"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/067636e747de5e101cccb7da216d43e0", "title": "Link between S4 readiness check and Maintenance planner", "problem": "Readiness check analysis gathers information for wrong system due to duplicate system IDs in Maintenance Planner.", "solution": "Ensure Maintenance Planner only reflects the correct system by removing the invalid duplicate system IDs. Verify that only the correct system and its respective data are maintained in the Maintenance Planner.", "cause": "Duplicate system IDs maintained in Maintenance Planner causing misalignment with Readiness Check Analysis.", "processed_at": "2025-06-25T16:08:01.962673"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ea3e6f23c3d65250ca3f1c0bb00131da", "title": "PRD HELP", "problem": "Report designed in Plateau Report Designer only allows HTML format upon upload, customer wants CSV.", "solution": "Re-design the report from scratch using CSV extension in Plateau Report Designer, consult developer for implementation.", "cause": "The initial report design did not accommodate CSV format.", "processed_at": "2025-06-25T16:08:06.124095"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/230b4cd5c36e5ed04daa2875e0013155", "title": "Readiness reports", "problem": "Readiness Check report RC_COLLECT_ANALYSIS_DATA execution fails due to unmet prerequisites.", "solution": "1. Check SAP Notes relevant to Readiness Check on system HEP. 2. Update outdated SAP Notes such as 2758146 to their latest version as indicated by SAP Note 2913617. 3. De-implement and re-implement SAP Note 2758146 ensuring all objects with yellow triangles are selected during implementation.", "cause": "Outdated SAP Notes related to Readiness Check not implemented in their latest versions.", "processed_at": "2025-06-25T16:08:11.562662"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/230b4cd5c36e5ed04daa2875e0013155", "title": "Readiness reports", "problem": "Error encountered when running RTCCTOOL report indicating issues with RFC connection.", "solution": "1. Enable and verify online collectors are activated. 2. Run RTCCTOOL report via transaction SE38 and ensure no error messages related to RFC connection. 3. Fix any RFC issues first if errors are received.", "cause": "Incomplete prerequisites for SAP Note 2972792 regarding Financial Data Quality checks.", "processed_at": "2025-06-25T16:08:11.562669"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5dabf673c35ed65013c299bc7a01312a", "title": "Readiness Check", "problem": "Readiness Check report shows no FDQ_ML data despite the file containing information.", "solution": "1. Verify the zip file and confirm FDQ_ML data presence. 2. Use transaction SE38>RTCCTOOL to check settings and save fdq section. 3. Review SAP Note 2913617 for related tile/job. 4. Check job TMW_RC_FDQ_DATA_COLL for Empty FI ML XML File message. 5. Create a case for further investigation.", "cause": "Issue during data collection resulting in empty FI ML XML file.", "processed_at": "2025-06-25T16:08:16.066469"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5dabf673c35ed65013c299bc7a01312a", "title": "Readiness Check", "problem": "Unable to connect for further analysis due to technical issues.", "solution": "Confirm connection settings and open necessary connections for system check.", "cause": "", "processed_at": "2025-06-25T16:08:16.066477"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/37d7d58c3b96da906f96634a85e45ace", "title": "Custom Code Analysis The check reports that no custom code analysis is implemented in the system.", "problem": "Custom code analysis reports no implementation despite existing custom code.", "solution": "1. Confirmed use of SAP Note 2185390. 2. Verified scheduling of program SYCM_DOWNLOAD_REPOSITORY_INFO as a background job with correct settings. 3. Ensured execution of program post background job completion. 4. Checked the downloaded ZIP file for completeness. 5. Customer confirmed presence of CustomCode.xlsx with results.", "cause": "Incomplete execution or misconfiguration of the custom code analysis program, leading to incorrect reporting.", "processed_at": "2025-06-25T16:08:21.169729"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/127a01e447ee16901cccb7da216d4325", "title": "SAP Readiness cannot download", "problem": "Unable to download SAP Readiness report due to 'Service Unavailable' error.", "solution": "1. Clear the buffer by running the report RC_COLLECT_ANALYSIS_DATA in SE38 and clicking 'Delete Buffered Data for Selected Items'. 2. Run a new RC job to collect data and upload the generated zip file to SAP. 3. Attempt to download the report again. If the issue persists, provide the complete RC zip file and analysis link for further investigation.", "cause": "", "processed_at": "2025-06-25T16:08:25.831521"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/814db3cdc3229a50ecd47d9f05013139", "title": "Long running batch job RC_COLLECT_ANALYSIS_DATA - S4 Hana Readiness check", "problem": "Long running batch job RC_COLLECT_ANALYSIS_DATA during S4 Hana Readiness check.", "solution": "Rerun the RC_COLLECT_ANALYSIS_DATA job ensuring the correct scope items are selected for analysis. Validate completion of child jobs before rerunning.", "cause": "The RC_COLLECT_ANALYSIS_DATA job was canceled due to an unknown issue, despite related child jobs finishing successfully.", "processed_at": "2025-06-25T16:08:31.076202"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/814db3cdc3229a50ecd47d9f05013139", "title": "Long running batch job RC_COLLECT_ANALYSIS_DATA - S4 Hana Readiness check", "problem": "Missing 'Update Analysis' button in the dashboard for Simplification item consistency analysis.", "solution": "Create a new support incident with detailed steps and screenshots for further investigation.", "cause": "", "processed_at": "2025-06-25T16:08:31.076216"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7d2b514ec33a9654ecd47d9f05013106", "title": "SAP Readiness Check Error \"Service Unavailable\"/\"Error HTTP 503\"", "problem": "SAP Readiness Check portal returns 'Service Unavailable/Error HTTP 503' when attempting to download the result report.", "solution": "Update the Knowledge Base Article (KBA) 0003516776 with resolution information once internal problem is solved. Confirmed the portal availability via 'https://me.sap.com/readinesscheck'.", "cause": "Internal problem affecting the SAP Readiness Check portal.", "processed_at": "2025-06-25T16:08:35.866666"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7d2b514ec33a9654ecd47d9f05013106", "title": "SAP Readiness Check Error \"Service Unavailable\"/\"Error HTTP 503\"", "problem": "SAP Readiness Check site redirected to ALM portal, preventing access to previously uploaded requests.", "solution": "Inform customer that the old URL 'https://itsm.services.sap/readinesscheck/home' is invalid. Provide correct URL 'https://me.sap.com/readinesscheck'.", "cause": "Use of outdated and invalid URL for accessing SAP Readiness Check portal.", "processed_at": "2025-06-25T16:08:35.866674"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d1681670c32e1e908710df00a001314c", "title": "SAP Readiness Check [SAP Cloud ALM] - Custom Code Analysis has issue", "problem": "Custom Code Analysis in SAP Readiness Check displays 'No Data' message.", "solution": "1. Customer requested to provide issue screenshot, analysis ID, Readiness Check data collection ZIP file, and Custom Code Analysis data collection ZIP file for further analysis. 2. Case was investigated and auto closed as per system log.", "cause": "Ongoing issue in the custom code analysis feature.", "processed_at": "2025-06-25T16:08:40.544386"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/47768964c3a29a904daa2875e0013116", "title": "BUZ_SCENARIO_SIGNAVIO_PI_COL", "problem": "ABAP dump caused by resource shortage during SPIDE data collection job BUZ_SCENARIO_SIGNAVIO_PI_COL.", "solution": "Refer to SAP KBA 2818045 to determine and exclude the respective KPI from the data collection.", "cause": "Memory dump due to resource shortage, specifically TSV_TNEW_PAGE_ALLOC_FAILED.", "processed_at": "2025-06-25T16:08:45.361258"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/47768964c3a29a904daa2875e0013116", "title": "BUZ_SCENARIO_SIGNAVIO_PI_COL", "problem": "Job BUZ_SCENARIO_SIGNAVIO_PI_COL was cancelled due to timeout error on UAT server.", "solution": "No solution provided in the ticket for timeout error.", "cause": "", "processed_at": "2025-06-25T16:08:45.361281"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1436468697061a94b86630200153afed", "title": "No data collected in ppi_petst03n01_00001 file after running program RC_VALUE_DISCOVERY_COLL_DATAT", "problem": "No data collected in ppi_petst03n01_00001 file after running program RC_VALUE_DISCOVERY_COLL_DATAT.", "solution": "The case was closed by the customer as it was identified as a duplicate and transferred to the responsible team BC-CCM-MON-TUN.", "cause": "Duplicate case; the issue was already being handled under another case 8237163/2024.", "processed_at": "2025-06-25T16:08:50.269888"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1232c3f03bea5ad00870d44a85e45a87", "title": "Note can not be implementing", "problem": "Unable to implement SAP Note 2972792 due to manual corrections not working.", "solution": "1. Check the ST-PI and ST-A/PI versions and update them to SAPK-74001INSTPI. 2. Implement SAP Note 2909538. 3. Run transaction SA38 and enter the program name /SDF/NOTE_2909538. 4. Execute the program in 'Update and Activate' mode.", "cause": "The required program for manual corrections does not exist.", "processed_at": "2025-06-25T16:08:54.243773"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/feea10b63b429210d537962a85e45a31", "title": "Note can not be implementing", "problem": "Unable to implement SAP Note 2972792 due to manual corrections not working.", "solution": "Confirmed the note is only valid for ST-PI version 740, which is not the version used by the P11 system. Therefore, the note is not applicable to the customer's environment. No implementation is needed.", "cause": "The P11 system uses ST-PI version 700, which is incompatible with the requirements of SAP Note 2972792.", "processed_at": "2025-06-25T16:09:01.039729"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/feea10b63b429210d537962a85e45a31", "title": "Note can not be implementing", "problem": "Dependency issue with SAP Note 2110518 related to CVI: SAPScript texts are not retrieved.", "solution": "Advised to open a separate SAP Case for the new issue, as each case can only address one issue. Referenced SAP Note 50048 for handling multiple queries in one case.", "cause": "The issue with SAP Note 2110518 is a separate problem that requires independent investigation.", "processed_at": "2025-06-25T16:09:01.039747"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2899ae8feb265a1010affe540bd0cd97", "title": "Sap Readiness Check for ISU - System fails", "problem": "TMW_RC_BPA_DATA_COLL job canceled due to system exception ERROR_MESSAGE during SAP Readiness Check for ISU.", "solution": "Update the ST-A/PI version to 01V or higher as indicated in note 2745851 to prevent job cancellation and ensure compatibility for Business Process Analytics data collection.", "cause": "The ST-A/PI version is outdated, requiring version 01V or higher for compatibility.", "processed_at": "2025-06-25T16:09:05.550334"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2899ae8feb265a1010affe540bd0cd97", "title": "Sap Readiness Check for ISU - System fails", "problem": "Error encountered when trying to implement note 2913617 for ERP Readiness Check.", "solution": "Forward the case to the appropriate component for further investigation and resolution.", "cause": "", "processed_at": "2025-06-25T16:09:05.550344"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4df755b0c356dad022195030a00131d0", "title": "SAP-3071: SPIDE Report", "problem": "Frequent memory dumps 'TSV_TNEW_PAGE_ALLOC_FAILED' while running SPIDE report.", "solution": "Remove specific KPI causing memory dumps from analysis as described in KBA 2874082. Analyze these KPIs externally using TBI_REPORTS as per KBA 2197502.", "cause": "Memory dumps occur due to processing particularly large KPI during SPIDE report execution.", "processed_at": "2025-06-25T16:09:14.383266"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4df755b0c356dad022195030a00131d0", "title": "SAP-3071: SPIDE Report", "problem": "Connection to PRD is not open preventing further case investigation.", "solution": "Customer needs to open R/3 connection to facilitate further investigation.", "cause": "", "processed_at": "2025-06-25T16:09:14.383275"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4df755b0c356dad022195030a00131d0", "title": "SAP-3071: SPIDE Report", "problem": "Memory dumps unrelated to SPIDE report indicating a broader memory bottleneck.", "solution": "Open a new case to address memory dumps unrelated to SPIDE report.", "cause": "Memory bottleneck in the system causing frequent dumps not specific to SPIDE report.", "processed_at": "2025-06-25T16:09:14.383277"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8b0958deeb835e1c7d05f7510bd0cd94", "title": "SAP Readiness check for S4 HANA", "problem": "Error implementing notes for SAP Readiness Check for S4 HANA.", "solution": "Update ST-PI and ST-A/PI components. Implement Note 2758146, 2745851, and 2399707. Address prerequisites for Note 2758146, including Note 1668882 and 2971435.", "cause": "Some SAP notes and prerequisites required for the readiness check are outdated or cannot be implemented.", "processed_at": "2025-06-25T16:09:20.167869"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8b0958deeb835e1c7d05f7510bd0cd94", "title": "SAP Readiness check for S4 HANA", "problem": "Connection error: 'Name or Password incorrect' during system access.", "solution": "Verify and update credentials in the secure area. Re-test connection to ensure credentials are correct.", "cause": "Incorrect or outdated credentials causing connection failure.", "processed_at": "2025-06-25T16:09:20.167877"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/efeba195ebaad2507d05f7510bd0cd69", "title": "DUMP NO_ROLL_MEMORY during readiness check", "problem": "DUMP NO_ROLL_MEMORY occurs during SAP readiness check, canceling the job RC_BW_4_HANA:NEW_CODE_ANALYSIS.", "solution": "Implemented the latest version of SAP note 3396444, excluding certain namespace from data collection, and if unsuccessful, provided a pilot note to deactivate call-stack determination in code scan.", "cause": "High memory consumption due to intricate dependencies of /DAVIL/-entities related to Abudaco Software.", "processed_at": "2025-06-25T16:09:24.188451"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6083cee2c38ed6d4ca3f1c0bb001312f", "title": "Readiness check report issue while implement note 2972792", "problem": "Error 'Function group /SDF/FDQ_ASSESSMENT does not exist' while implementing SAP Note 2972792.", "solution": "Verify if all prerequisite notes are implemented successfully. Check SAP_APPL and SAP_FIN versions. Ensure SAP Note 2758146 is implemented and its version is the latest. Confirm settings in RTCCTOOL for FDQ are correctly configured.", "cause": "Missing prerequisite function group or incorrect implementation sequence for SAP Note 2972792.", "processed_at": "2025-06-25T16:09:29.045789"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6083cee2c38ed6d4ca3f1c0bb001312f", "title": "Readiness check report issue while implement note 2972792", "problem": "Connection error 'route permission denied' when connecting to ECS via R/3 connection.", "solution": "Refer to SAP Note 1178546 for resolution steps related to route permission denied error. Open R/3 connection for further investigation by SAP support.", "cause": "SAProuter configuration issue leading to route permission denial.", "processed_at": "2025-06-25T16:09:29.045816"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3cbef2ce3b6a5a546f96634a85e45a1e", "title": "RC_COLLECT_ANALYSIS_DATA report | Financial data quality error", "problem": "Exclusion of Financial Data Quality and Integration checks in S/4HANA Readiness report.", "solution": "Confirmed that Financial Data Quality and Integration analysis are optional checks and can be excluded without issues.", "cause": "Customer's Basis release version is 7.1 which requires application of over 70 SAP notes to include these checks.", "processed_at": "2025-06-25T16:09:33.041294"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/957005a63b869a94d537962a85e45a76", "title": "Unable to view SAP Readiness Check in ALM", "problem": "Unable to view SAP Readiness Check in SAP Cloud ALM.", "solution": "User needs to create a new Readiness Check in their SAP Cloud ALM tenant and upload the data again.", "cause": "Readiness Check created in SAP4Me is not synchronized with the customer's SAP Cloud ALM tenant.", "processed_at": "2025-06-25T16:09:37.234963"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/142a746deb229a5010affe540bd0cd10", "title": "No findings while executing RC_VALUE_DISCOVERY_COLL_DATA report", "problem": "No findings while executing RC_VALUE_DISCOVERY_COLL_DATA report; No KPIs listed.", "solution": "1. Refer to KBA 0003278527 for the issue of empty dropdown list in Business Process Analytics KPIs. 2. Implement note 0002745851 for Business Process Improvement Content in SAP Readiness Check 2.0/SAP Signavio Process Insights, Discovery Edition. 3. Update ST-PI and ST-A/PI components and check using RTCCTOOL.", "cause": "Dropdown list empty for Business Process Analytics KPIs in the report.", "processed_at": "2025-06-25T16:09:41.609916"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3dc28b9ceb3ed29010affe540bd0cd38", "title": "Issues implementing SAP Readiness Check for SAP S/4HANA", "problem": "Error implementing SAP Note 2972792 due to missing function group /SDF/FDQ_ASSESSMENT.", "solution": "Create function group /SDF/FDQ_ASSESSMENT under package /SDF/FDQ_API using transaction SE37, then reimplement the SAP Note 2972792.", "cause": "The function group /SDF/FDQ_ASSESSMENT was not automatically created during the note implementation process.", "processed_at": "2025-06-25T16:09:48.945951"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3dc28b9ceb3ed29010affe540bd0cd38", "title": "Issues implementing SAP Readiness Check for SAP S/4HANA", "problem": "Issues implementing SAP Note 2502552 with status 'Incompletely implemented'.", "solution": "Implement prerequisite notes 3093855 and 3296107, check SNOTE functionality, and attempt the implementation again.", "cause": "Not all required corrections were implemented due to missing prerequisite notes.", "processed_at": "2025-06-25T16:09:48.945965"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3dc28b9ceb3ed29010affe540bd0cd38", "title": "Issues implementing SAP Readiness Check for SAP S/4HANA", "problem": "Confusion regarding the mandatory notes for SAP Readiness Check for SAP S/4HANA.", "solution": "Clarify that either the notes listed in 2913617 can be implemented for specific scopes or the Note Analyzer tool as outlined in 3308130 can be used to analyze all scopes.", "cause": "The documentation in SAP Notes 2913617 and 3308130 was not clear on whether all notes need to be implemented.", "processed_at": "2025-06-25T16:09:48.945969"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3dc28b9ceb3ed29010affe540bd0cd38", "title": "Issues implementing SAP Readiness Check for SAP S/4HANA", "problem": "Dump error during implementation of SAP Note 3304656 resulting in a RAISE_EXCEPTION error.", "solution": "Apply workaround described in note 2717209 to de-implement and retry note implementation.", "cause": "An exception condition 'CNTL_ERROR' occurred due to a RAISE statement in the ABAP program during note implementation.", "processed_at": "2025-06-25T16:09:48.945972"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/882cc2613ba252540870d44a85e45a3a", "title": "Readiness Check Report for S/4HANA", "problem": "Program RC_COLLECT_ANALYSIS_DATA does not exist when attempting to execute Readiness Check Report for S/4HANA.", "solution": "Implement SAP Note 2758146 to enable the existence of the program RC_COLLECT_ANALYSIS_DATA. Additionally, ensure all notes listed in SAP Note 2913617 are implemented with the latest version, particularly notes 2758146 and 2399707, and check each note's prerequisites.", "cause": "SAP Note 2758146, which delivers the data collection framework controlled via report RC_COLLECT_ANALYSIS_DATA, is not implemented.", "processed_at": "2025-06-25T16:09:53.784002"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f2c376d8c3fe969022195030a0013130", "title": "S4 Upgrade version to 2023", "problem": "Customer requests analysis and impact clarification of configuration changes during S4 upgrade from 1909 to 2023.", "solution": "Informed customer that request is outside the scope of product support, advised to redirect the question to TQM or SAP contact.", "cause": "Request for landscape-specific advice and consulting services, not covered by standard SAP support.", "processed_at": "2025-06-25T16:09:57.563341"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2dc7b02cc3ae9690553db11f0501316e", "title": "Custom code analysis jobs are running for long time and not completing for Readiness", "problem": "Custom code analysis jobs are running for an extended time and not completing for readiness.", "solution": "Delete manually created check variant S4HANA_READINESS_2023 and implement SAP Note 3365357. Execute the custom code analysis with report RC_COLLECT_ANALYSIS_DATA again.", "cause": "Likely issue due to manually created check variant conflicting with expected configurations.", "processed_at": "2025-06-25T16:10:02.691346"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2dc7b02cc3ae9690553db11f0501316e", "title": "Custom code analysis jobs are running for long time and not completing for Readiness", "problem": "System time zone found in TTZCU is not valid, causing potential issues with job execution.", "solution": "Investigate and correct the invalid system time zone settings as suggested in communications. Coordinate with time zone team for resolution.", "cause": "Invalid configuration of system time zone settings in TTZCU.", "processed_at": "2025-06-25T16:10:02.691354"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/329cd5e4ebea12107d05f7510bd0cd06", "title": "Syntax error in SAP program RC_VALUE_DISCOVERY_COLL_DATA", "problem": "Syntax error in SAP program RC_VALUE_DISCOVERY_COLL_DATA after running report RC_COLLECT_ANALYSIS_DATA.", "solution": "Follow steps outlined in KBA 3283120 to resolve syntax error dump in class /SDF/CL_S4RC20 when executing program RC_COLLECT_ANALYSIS_DATA.", "cause": "Note 2745851 might be applied incorrectly.", "processed_at": "2025-06-25T16:10:06.790844"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/310f52b5c3e65e1488b12875e00131a4", "title": "No data collected in Petst03n file", "problem": "No data collected in Petst03n file after running SAP Signavio report.", "solution": "1. Review the SAP Signavio Process Insights troubleshooting guide KBA 0002977422, specifically issue 2. 2. Provide screenshots of available data in ST03. 3. Open R/3 connection and maintain a user in the secure area for remote investigation.", "cause": "", "processed_at": "2025-06-25T16:10:10.758742"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4dde5275c3e65e1488b12875e00131d9", "title": "program /SDF/RC_START_CHECK does not exist", "problem": "The program /SDF/RC_START_CHECK does not exist in the SAP system.", "solution": "Implement SAP note ********** in the latest version and verify if the issue persists.", "cause": "Missing implementation of specific SAP note required for the program to run.", "processed_at": "2025-06-25T16:10:14.629398"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1c01a59d3b1a1a546f96634a85e45a20", "title": "Error in Readiness Check report", "problem": "Customer encounters error when attempting to schedule Readiness Check data collection due to missing prerequisite SAP Note 3010669.", "solution": "Customer applied SAP Note 3010669 once it became applicable, resolving the error in the report.", "cause": "Initial condition where SAP Note 3010669 was incorrectly reported as missing but not applicable to the system.", "processed_at": "2025-06-25T16:10:20.308672"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1c01a59d3b1a1a546f96634a85e45a20", "title": "Error in Readiness Check report", "problem": "Error message when activating Readiness Check report: 'The data object LS_DD04V does not have a component called NOHISTORY.'", "solution": "Suggested workaround was to create and execute a local report using provided content, upgrade SAP Note 2972792 to the newest version, and implement it again.", "cause": "Prerequisite SAP Note 2909538 was required for systems with ST-PI release 740, but the customer's ST-PI version did not meet this requirement.", "processed_at": "2025-06-25T16:10:20.308697"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1c01a59d3b1a1a546f96634a85e45a20", "title": "Error in Readiness Check report", "problem": "Cannot implement SAP Note 2972792, resulting in error message 'Prerequisite up-to-date SAP Note 2972792 for Financial Data Quality is not installed.'", "solution": "Ensure all prerequisites under section 'Reason and Prerequisites' in note 2972792 are fulfilled, as implementation of the note itself may not be possible.", "cause": "Prerequisites outlined in SAP Note 2972792 were not completely fulfilled, leading to implementation issues.", "processed_at": "2025-06-25T16:10:20.308704"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1c01a59d3b1a1a546f96634a85e45a20", "title": "Error in Readiness Check report", "problem": "Failed to log onto system ACD due to network timeout error.", "solution": "Need to ensure working connections and possibly involve network colleagues to resolve the issue.", "cause": "Network interface component NI encountered a timeout, preventing access to the system.", "processed_at": "2025-06-25T16:10:20.308711"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7d55eb1bc36e925030037d9f050131db", "title": "Error while implementing note 2758146", "problem": "Error while implementing SAP Note 2758146 due to invalid characteristics of object 'M_TEXT_P'.", "solution": "De-implement SAP Note 2758146 and ensure selection of objects marked with yellow in the status column to be overwritten. Implement the latest version of related SAP Notes 1668882 and 2971435, ensure SAP Note 2310438 is not implemented, and download the latest version of 2758146 before re-implementation.", "cause": "SAP Note implementation causing inconsistencies due to invalid characteristics of object 'M_TEXT_P'.", "processed_at": "2025-06-25T16:10:24.945715"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/199bcc43eb8352507d05f7510bd0cdc0", "title": "SAP Readiness Check Flat File Integration upload has error", "problem": "Error during SAP Readiness Check Flat File Integration upload in Production system.", "solution": "1. Verify implementation of SAP Note 2758146 and ensure latest version is applied. 2. Open remote connection for system access. 3. Enable and run file interface discovery tool using transaction ST13. 4. Activate aggregated authorization traces. 5. Collect data for a defined period. 6. Download and deactivate traces. 7. Upload XML file to SAP Readiness Check.", "cause": "Incomplete or incorrect data collection from the file interface discovery tool.", "processed_at": "2025-06-25T16:10:31.264383"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/199bcc43eb8352507d05f7510bd0cdc0", "title": "SAP Readiness Check Flat File Integration upload has error", "problem": "Remote connection issues preventing SAP support from accessing the Production system.", "solution": "1. Ensure R/3 and HTTP connections are open as per SAP Notes 812732, 592085, and 1773689. 2. Verify correct system ID and number for remote connection. 3. Open OSS connection for SAP support access.", "cause": "", "processed_at": "2025-06-25T16:10:31.264405"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/50668604477216901cccb7da216d4374", "title": "How to add authorization object SM_BPM_DET in current YAGEO ECC", "problem": "Missing authorization object SM_BPM_DET in YAGEO ECC system.", "solution": "1. Ensure the latest versions of ST-PI and ST-A-PI are installed. 2. Run report RTCCTOOL on SE38 and follow recommendations. 3. Implement SAP Notes 0002745851 and 0002758146. 4. Check and correct R/3 connection error if the authorization object SM_BPM_DET still does not exist.", "cause": "The authorization object SM_BPM_DET is not present in the ECC system, preventing manual addition.", "processed_at": "2025-06-25T16:10:35.443321"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b0df03d5c3929e1422195030a00131f2", "title": "Error implementing 2758146 - Characteristics of object 'M_TEXT_P' not valid", "problem": "Error implementing SAP Note 2758146 - Characteristics of object 'M_TEXT_P' not valid.", "solution": "1. De-implement the SAP Note. 2. Delete the MIME files via t-code SE80. 3. Re-implement the SAP Note and verify the result.", "cause": "Implement error.", "processed_at": "2025-06-25T16:10:39.131621"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7a469272476e921036bf68be436d43be", "title": "Syntax erros while implementing the SAPNOTE 2758146", "problem": "Syntax errors encountered while implementing SAP Note 2758146 in SAP S/4HANA system.", "solution": "1. De-implement SAP Note 2745851. 2. Check consistency of note 2745851 using program ZNOTE_2745851_REPAIR from SAP note 3205320. 3. De-implement SAP Note 2758146. 4. Implement SAP Note 2758146, manually activate all yellow warning objects to overwrite them to the latest version. 5. Implement SAP Note 2745851, manually activate all yellow warning objects to overwrite them to the latest version.", "cause": "Sequence of implementing the SAP Notes was incorrect, causing syntax errors and dumps.", "processed_at": "2025-06-25T16:10:43.733962"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d699f9fbc3cb9a90ca3f1c0bb001318f", "title": "RC_COLLECT_ANALYSIS_DATA job getting failed", "problem": "RC_COLLECT_ANALYSIS_DATA job fails due to memory dumps.", "solution": "Followed SAP Note 2818045 which includes optimizing memory allocation and handling large KPIs.", "cause": "The job TMW_RC_BPA_DATA_COLL fails due to memory dumps caused by processing large KPIs.", "processed_at": "2025-06-25T16:10:49.795759"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d699f9fbc3cb9a90ca3f1c0bb001318f", "title": "RC_COLLECT_ANALYSIS_DATA job getting failed", "problem": "Unauthorized HTTP status error when connecting to SAP-SUPPORT_PORTAL.", "solution": "Set up the direct connection to SAP by following the backbone update guidelines on https://support.sap.com/backbone-update.", "cause": "HTTP status error code 401 Unauthorized due to incorrect or missing authorization credentials.", "processed_at": "2025-06-25T16:10:49.795772"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d699f9fbc3cb9a90ca3f1c0bb001318f", "title": "RC_COLLECT_ANALYSIS_DATA job getting failed", "problem": "Error when opening RFC connection due to communication failure.", "solution": "Checked and updated RFC destinations via SAP Solution Manager customizing.", "cause": "RFC communication failure due to product-specific errors and connection issues with legacy ABAP/RFC destinations.", "processed_at": "2025-06-25T16:10:49.795776"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d699f9fbc3cb9a90ca3f1c0bb001318f", "title": "RC_COLLECT_ANALYSIS_DATA job getting failed", "problem": "Internal session terminated with runtime error DATA_OFFSET_NEGATIVE.", "solution": "Implemented SAP Note 2909065 to address the ABAP programming error.", "cause": "ABAP program CL_CVI_READINESS_CHECK encountered an unexecutable statement, leading to runtime error DATA_OFFSET_NEGATIVE.", "processed_at": "2025-06-25T16:10:49.795779"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d699f9fbc3cb9a90ca3f1c0bb001318f", "title": "RC_COLLECT_ANALYSIS_DATA job getting failed", "problem": "Internal session terminated with runtime error PXA_NO_FREE_SPACE.", "solution": "Resolved by increasing PXA storage space as per note 2968380 troubleshooting guide.", "cause": "Resource shortage due to insufficient PXA storage space.", "processed_at": "2025-06-25T16:10:49.795782"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1a71e3e2c3f252d4553db11f050131ac", "title": "Laufzeitfehler beim Ausführen des Report", "problem": "Report RC_COLLECT_ANALYSIS_DATA aborted due to syntax error in SAP Standard.", "solution": "1. Implemented SAP Note 3261488 to correct the affected code segment. 2. Verified that the correction resolved the issue.", "cause": "Syntax error in program /SSA/EKF causing the report to fail.", "processed_at": "2025-06-25T16:10:55.019560"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1a71e3e2c3f252d4553db11f050131ac", "title": "Laufzeitfehler beim Ausführen des Report", "problem": "Unable to install SAP Note 3321793.", "solution": "Reviewed release compatibility and identified correct SAP Note to apply, ensuring system met prerequisites for implementation.", "cause": "Note 3321793 was not compatible with the current system release ST-A/PI 01V_731 0001 SAPKITABC4.", "processed_at": "2025-06-25T16:10:55.019585"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1226a1f73b2a1e50d537962a85e45a57", "title": "Issue Populating Financial Data Quality Data In SAP Readiness Check Dashboard", "problem": "Error in SAP Readiness Check portal preventing Financial Data Quality analysis for S/4 HANA conversion.", "solution": "Reviewed KBA 0003440226 for error in Financial Data Quality. Ensured online collectors were activated and RTCCTOOL report showed no errors. Addressed RTCCTOOL connection error using KBA 0003418015.", "cause": "Technical issue occurred due to errors in reading FI-GL and XML data with specific FDQ keys.", "processed_at": "2025-06-25T16:10:59.524818"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/914747b6c3cf9e1013c299bc7a0131fc", "title": "Erro SAP NOTE 2972792", "problem": "Error implementing SAP Note 2972792 due to missing function group /SDF/FDQ_ASSESSMENT.", "solution": "1. Implement SAP Note 2909538 and perform manual steps. 2. Create function group /SDF/FDQ_ASSESSMENT manually. 3. Reimplement SAP Note 2972792. 4. Verify implementation success and transport function group if necessary.", "cause": "Function group /SDF/FDQ_ASSESSMENT was not created during initial implementation steps.", "processed_at": "2025-06-25T16:11:04.627966"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8c2664c13b1a5254d537962a85e45a8d", "title": "SAP Readiness Check for SAP S/4HANA upgrades", "problem": "Readiness check report in SAP S/4 systems is not showing actual object names in upgrade scope.", "solution": "Customer was informed to select every 'Custom Code Topic' separately to export them. Further documentation was provided through a link to SAP Readiness Check documentation.", "cause": "Lack of detailed object/program name display in the readiness check report.", "processed_at": "2025-06-25T16:11:08.631237"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f86794c1c39e561488b12875e0013156", "title": "SAP Readiness Check Jobs Running Longer - FDQ Jobs Specifically", "problem": "SAP Readiness Check FDQ jobs are running longer than expected.", "solution": "Implement additional SAP notes and create an index on the database to improve job performance.", "cause": "The jobs for Financial Data Quality (FDQ) related to General Ledger (GL) are running long due to inadequate database indexing and missing SAP notes.", "processed_at": "2025-06-25T16:11:12.016401"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1c75dbd547f21ad036bf68be436d43ec", "title": "OBJECTS_OBJREF_NOT_ASSIGNED_NO dumps - RC_COLLECT_ANALYSIS_DATA Job", "problem": "OBJECTS_OBJREF_NOT_ASSIGNED_NO runtime error during RC_COLLECT_ANALYSIS_DATA job execution in SAP ERP system RP1.", "solution": "Verify latest version of SAP note 2758146 is implemented. Implement SAP note 3233821 to resolve the runtime error. Delete buffered data in RC report before executing a new data collection.", "cause": "Access using a 'NULL' object reference in ABAP program CL_CVI_READINESS_CHECK caused the program to terminate.", "processed_at": "2025-06-25T16:11:17.240641"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1c75dbd547f21ad036bf68be436d43ec", "title": "OBJECTS_OBJREF_NOT_ASSIGNED_NO dumps - RC_COLLECT_ANALYSIS_DATA Job", "problem": "Connection error preventing login to ERP system RP1.", "solution": "Maintain a user for RP1 in the secure area to open R/3 connection.", "cause": "", "processed_at": "2025-06-25T16:11:17.240663"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/218d752ec3cbd210ca3f1c0bb00131d7", "title": "SPIDE report submitted but limited data returned for Process Flow", "problem": "Limited data returned for SAP Signavio Process Insights report on process ID KPPURCH268.", "solution": "1. Investigated the selection criteria for the report and found no matching entries in table EKPO. 2. Suggested de-implementation and re-implementation of specific SAP Notes (2745851 and 2758146) in correct versions. 3. Ensured authorization object SM_BPM_DET was assigned to the user. 4. Ran the report RC_VALUE_DISCOVERY_COLL_DATA with specified criteria and uploaded results for further analysis.", "cause": "The selection criteria used for data collection did not find any matching entries in the database table EKPO.", "processed_at": "2025-06-25T16:11:23.076179"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2dd58a40477216901cccb7da216d43f0", "title": "No HCM Authorizations objects", "problem": "Missing authorization object SM_BPM_DET in SAP ECC system.", "solution": "The issue was forwarded to the SV-SCS-S4R team for further analysis. Customer was advised to hold PRD case until resolution on DEV system is verified. Once resolved, similar steps should be attempted on PRD system. If unsuccessful, further feedback was requested.", "cause": "The customer attempted to configure Business Scenario Recommendations (BSR) in their ECC system but encountered a problem with a missing authorization object that cannot be manually added.", "processed_at": "2025-06-25T16:11:27.481428"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2dd58a40477216901cccb7da216d43f0", "title": "No HCM Authorizations objects", "problem": "Incident opened in the wrong component.", "solution": "The incident was redirected to the appropriate team for further assistance.", "cause": "The customer initially opened the incident in an incorrect component for the issue.", "processed_at": "2025-06-25T16:11:27.481456"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a728d506c3479a50cebe6610a001317e", "title": "Note 2758146 implementation error", "problem": "Error 'Format of correction instructions unable to read corr. instruct.' encountered while implementing SAP Note 2758146.", "solution": "Add missing TADIR entries for SMIM objects in the system, as advised by SAP Support.", "cause": "Missing TADIR entries for SMIM objects related to SAP Note 2758146.", "processed_at": "2025-06-25T16:11:32.190056"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a728d506c3479a50cebe6610a001317e", "title": "Note 2758146 implementation error", "problem": "Import with SAINT is blocking the implementation of SAP Note 2745851.", "solution": "Wait for the SAINT import to complete before attempting the implementation again.", "cause": "Ongoing SAINT import process blocking further note implementations.", "processed_at": "2025-06-25T16:11:32.190086"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f916db5d47f21ad036bf68be436d4346", "title": "Authorization issue", "problem": "Authorization issue during the readiness check process; unable to upload data using SAP Readiness Check.", "solution": "Follow the SAP Note 3310759 for the revised authorization concept for SAP Readiness Check. Clear cache and cookies of the browser as per SAP Note 3402492.", "cause": "Missing authorization for the S-user in the SAP Readiness Check portal.", "processed_at": "2025-06-25T16:11:36.696505"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3492db62c33e9694ecd47d9f0501313d", "title": " Readiness Check performance problem", "problem": "Performance issue with SAP S/4HANA Readiness Check, particularly with the Financial Data Quality option.", "solution": "1) Verify RTCCTOOL settings based on SAP Note 3479180. 2) Restart Readiness Check reports after adjustments. 3) Wait for FDQ data collection job to complete due to its longer processing time.", "cause": "Incomplete RTCCTOOL settings and high volume of SD invoices affecting Financial Data Quality checks.", "processed_at": "2025-06-25T16:11:43.147626"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/051af96a4792d2501cccb7da216d4300", "title": "Issue in implementing  note  2972792", "problem": "Issue in implementing SAP note 2972792 during readiness check.", "solution": "Customer was advised to open an R/3 connection for further investigation.", "cause": "Potential new programming bug suspected but not confirmed.", "processed_at": "2025-06-25T16:11:46.712940"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/de8348f5c3ba5214b118f71ad00131f0", "title": "Long running Readiness Check", "problem": "Long running SAP S/4HANA readiness check job TMW_RC_FDQ_DATA_COLL.", "solution": "Perform preliminary checks as documented in KBA 3479180. Change analysis settings to single fiscal year per job to improve performance. Review and apply relevant Notes 3430973 and 3369764. Cancel the current analysis and restart after changes.", "cause": "The long running time is expected functionality due to the large number of financial documents being processed.", "processed_at": "2025-06-25T16:11:51.030501"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e81ab96a4792d2501cccb7da216d435c", "title": "SAP Readiness Check for SAP S/4HANA - copy ST03N data", "problem": "SAP Readiness Check for SAP S/4HANA only retrieves 5 weeks of ST03N data instead of the selected 12 months.", "solution": "Check the ST03 transaction to verify if it contains more than 5 weeks of data. Ideally, it should encompass the past 3 months of data. Provide connection details for further analysis if the issue persists.", "cause": "", "processed_at": "2025-06-25T16:11:54.820184"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bc46ffa5c376d254ca3f1c0bb00131f4", "title": "RC_COLLECT_ANALYSIS_DATA job running since long time", "problem": "RC_COLLECT_ANALYSIS_DATA job running for a long time and causing memory dumps.", "solution": "Request the customer to open remote connection and provide full dump details along with maintaining logon data securely.", "cause": "", "processed_at": "2025-06-25T16:11:58.383561"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2d5142343ba61e90d537962a85e45ac0", "title": "Error in implementing latest versions of SAP note 2745851 and 2758146", "problem": "Error in implementing latest versions of SAP note 2745851 and 2758146.", "solution": "Customer was advised to review Knowledge Base Article 0003434412 related to errors implementing SAP Note 2745851. Customer was requested to open R/3 connection to the system and maintain a user in the secure area for further investigation.", "cause": "", "processed_at": "2025-06-25T16:12:02.467441"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3a26cf493bea9ad06f96634a85e45a17", "title": "RC_COLLECT_ANALYSIS_DATA getting failed", "problem": "The job RC_COLLECT_ANALYSIS_DATA is failing due to prerequisites notes not being ready for Financial Data Quality check.", "solution": "Update ST-A/PI and implement necessary FDQ SAP Notes as per RTCCTOOL recommendations. Run the Readiness Check report RC_COLLECT_ANALYSIS_DATA twice in the productive client of P11, first without selecting Financial Data Quality, and then with only FDQ selected.", "cause": "Prerequisite notes for Financial Data Quality check were not implemented.", "processed_at": "2025-06-25T16:12:07.062754"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e597e500c383d21822195030a001313c", "title": "S/4HANA 2023 readiness check RFC results", "problem": "The SAP S/4HANA readiness check reports function modules as unavailable in the target release, although they do not exist in the customer's current system.", "solution": "The function modules were confirmed as irrelevant by the responsible team. Customer was advised to ignore these modules as they will not affect the upgrade. Future enhancements were promised.", "cause": "The function modules were incorrectly flagged due to historical data collection by ST03R during system upgrade, leading to erroneous reporting in the readiness check.", "processed_at": "2025-06-25T16:12:12.160129"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e597e500c383d21822195030a001313c", "title": "S/4HANA 2023 readiness check RFC results", "problem": "Customer requested confirmation and adjustment of the data collection period in the SAP readiness check.", "solution": "Customer was informed that the data collection period cannot be changed. It was recommended to consult the function module team for further clarification.", "cause": "", "processed_at": "2025-06-25T16:12:12.160151"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6900e0bdc3361a54553db11f050131c9", "title": "S4HANA upgrade simplification item list", "problem": "Connection error between R/3 and PRD system due to timeout.", "solution": "Resolved connection error by referring to SAP Note 1178624 and ensuring proper network settings.", "cause": "Network interface timeout error during connection attempt.", "processed_at": "2025-06-25T16:12:18.003409"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6900e0bdc3361a54553db11f050131c9", "title": "S4HANA upgrade simplification item list", "problem": "Silent Data Migration (SDMI) issue where SDMI CL_SDM_STXH_CUP_CIG is not uptime-capable in client 001.", "solution": "Ensure a technical user with sufficient authorization to run the SDMI background job in client 001. Verify logs using SDM_MON and assure migration progress is 100%.", "cause": "SDMI is still marked as initial, indicating potential issues with background job execution.", "processed_at": "2025-06-25T16:12:18.003427"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6900e0bdc3361a54553db11f050131c9", "title": "S4HANA upgrade simplification item list", "problem": "Unresolved red errors in Readiness Check after updating SAP Notes.", "solution": "Reviewed Readiness Check results before and after SAP Notes update, and advised running SI Check in client 000 to address red errors.", "cause": "Red errors persist due to incomplete migration processes and initial status of SDMI in specific clients.", "processed_at": "2025-06-25T16:12:18.003430"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ab3db6f0c337d69c553db11f05013136", "title": "No data collected in Petst03n file in SPIDE(SAP Signavio Process Insights, discovery edition)", "problem": "No data collected in 'ppi_petst03n01.json' file in SAP Signavio Process Insights.", "solution": "Re-run the data collection program to populate the 'ppi_petst03n01' file with data.", "cause": "Insufficient data available for the selected time period, as only recent data was available after 09/02.", "processed_at": "2025-06-25T16:12:26.147731"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c511dcf9c3fe5a1430037d9f0501319f", "title": "Readiness check - 2972792 - FDQ Implementation Issue", "problem": "Syntax error in program SAPLFIN_AA_CORR, field 'RC' is unknown.", "solution": "Implemented solution from KBA 0003345451, addressing error in transport of orders. Ensure the latest correction version of note 0002909538 is applied, followed by re-implementation of note 0002972792.", "cause": "Field 'RC' is not defined in the specified tables or by a 'DATA' statement.", "processed_at": "2025-06-25T16:12:32.599045"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c511dcf9c3fe5a1430037d9f0501319f", "title": "Readiness check - 2972792 - FDQ Implementation Issue", "problem": "Error: Function group /SDF/FDQ_ASSESSMENT does not exist.", "solution": "Manually create Function group /SDF/FDQ_ASSESSMENT under package /SDF/FDQ_API until a final solution is provided. Open system connection and store logon data for further analysis.", "cause": "", "processed_at": "2025-06-25T16:12:32.599068"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a855951aeb569a947d05f7510bd0cd25", "title": "Not able to access Readiness Check Dashboard", "problem": "Not able to access Readiness Check Dashboard due to missing authorizations.", "solution": "1. Verify the authorization assigned to the user's S-user ID. 2. Follow the steps in KBA 0002891528 to request needed authorizations. 3. Ensure Display SAP Readiness Check Analysis (RC_ANA_DIS) authorization is granted. 4. Confirm authorizations documented in KBA 0003455928 are correctly applied.", "cause": "Required authorization not assigned to the user's S-user ID.", "processed_at": "2025-06-25T16:12:38.168965"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1d460ec0477216901cccb7da216d436f", "title": "Missing function group when applying note 2972792", "problem": "Missing function group when applying SAP note 2972792.", "solution": "1. Implement the current version no 12 of note 2909538. 2. Re-implement note 2972792 afterwards.", "cause": "The prerequisite notes required for note 2972792 were not successfully implemented, causing missing function group error.", "processed_at": "2025-06-25T16:12:42.237673"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/05f593b8c34b5658cebe6610a0013161", "title": "S/4 Hana Readiness  check analysis report is failing at last stage with Financial data analysis", "problem": "S/4 Hana Readiness check analysis report failing at last stage with Financial Data analysis due to runtime errors.", "solution": "1. Apply SAP Note 3130905, and perform manual post implementation steps. 2. Apply SAP Note 3129858, perform pre-manual steps, implement automatic correction instructions, and follow manual post-implementation step instructions.", "cause": "Runtime Errors SAPSQL_INVALID_TABLENAME during Financial Data Quality data collection in SAP Readiness Check Report.", "processed_at": "2025-06-25T16:12:47.150133"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/05f593b8c34b5658cebe6610a0013161", "title": "S/4 Hana Readiness  check analysis report is failing at last stage with Financial data analysis", "problem": "Error in CL_RC_FDQ_FI_AA_MONITOR during job TMW_RC_FDQ_DATA_COLL.", "solution": "Ensure prerequisites for FDQ check in RC_COLLECT_ANALYSIS_DATA are met.", "cause": "Issue caused by not meeting prerequisites.", "processed_at": "2025-06-25T16:12:47.150154"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7ce38079c3ba5214b118f71ad0013194", "title": "SAP Readiness Check long Runtime FIN_CORR_REC_ANALYSIS_*YEAR*", "problem": "SAP Readiness Check for Financial Data Quality analysis takes exceptionally long to complete.", "solution": "Verify preliminary checks as documented in KBA 3479180. Adjust analysis settings to process one fiscal year per job. Apply Notes 3430973 and 3369764 if applicable. Cancel and restart the analysis after making changes.", "cause": "Expected functionality; long runtime due to the number of financial documents processed.", "processed_at": "2025-06-25T16:12:50.858857"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/391e5dddc32e9250b118f71ad00131f8", "title": "/SDF/HDB_SIZING_SM job cancelled", "problem": "HANA sizing background job /SDF/HDB_SIZING_SM cancelled during SAP Readiness Check.", "solution": "1. Applied the latest version of the HANA sizing correction note 3494871. 2. Cleared the buffered data for HANA sizing. 3. Reran the data collection for HANA sizing. 4. Recommended monitoring the job and updating the case with results.", "cause": "Background job cancellation due to potential errors in RFC calls exceeding the limit, as noted in the MESSAGE_TYPE_X dump.", "processed_at": "2025-06-25T16:12:55.960643"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f353c961c37e9254553db11f05013158", "title": "SAP S/4HANA Readiness Check Clarification", "problem": "Clarification needed for SAP S/4HANA readiness check execution and scope selection.", "solution": "1. Verify the target S/4HANA version intended for the DEV system. 2. Review SAP Note ********** for scope selection and ensure all prerequisite notes are implemented correctly. 3. Confirm safe transport of changes by verifying consistent software versions across DEV, QAS, and PRD environments.", "cause": "Missing prerequisite notes for SAP S/4HANA readiness check.", "processed_at": "2025-06-25T16:13:02.480907"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f353c961c37e9254553db11f05013158", "title": "SAP S/4HANA Readiness Check Clarification", "problem": "Concern about safety of transporting changes after deploying notes in DEV system.", "solution": "Ensure the same software component versions across DEV, QAS, and PRD environments, including ST-PI & ST-A/PI addons, to safely transport notes and changes.", "cause": "", "processed_at": "2025-06-25T16:13:02.480929"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a65630bec3ae1210553db11f050131c4", "title": "Users with permissions already assigned unable to open readiness check analysis", "problem": "Users with permissions already assigned unable to open readiness check analysis.", "solution": "1. Verify permissions assigned to S-USER accounts. 2. Inform customer permission changes may take several hours to activate. 3. Suggest clearing browser cache or using incognito mode. 4. Request customer to provide response from specific network request if issue persists.", "cause": "", "processed_at": "2025-06-25T16:13:07.012069"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5d41d87dc3fe5a1430037d9f050131aa", "title": "SAP Readiness Check for SAP S/4HANA", "problem": "SAP Readiness Check report does not support SP-level upgrades for S/4HANA.", "solution": "Verify system version in catalog using S-User, as RC report supports version upgrades only. Adjust expectations accordingly.", "cause": "The Readiness Check report is designed to support version upgrades only, not service pack level updates.", "processed_at": "2025-06-25T16:13:11.428916"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5d41d87dc3fe5a1430037d9f050131aa", "title": "SAP Readiness Check for SAP S/4HANA", "problem": "Dropdown list for target S/4HANA version missing or empty in Readiness Check.", "solution": "Access catalog overview, select Source Validity and Target Validity, and download the result.", "cause": "Target service pack level (S4HANA 2020 SP9) not supported by Readiness Check, only major version upgrades are listed.", "processed_at": "2025-06-25T16:13:11.428938"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/569233513bba5a146f96634a85e45a6a", "title": "Error in Readiness Check", "problem": "Error in SAP Readiness Check report not working.", "solution": "Implemented SAP Note 1668882 and 875986, then reimplemented SAP Note 2758146.", "cause": "Issues with the implementation of SAP Note 2758146 or 2399707.", "processed_at": "2025-06-25T16:13:15.000174"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/58bb9c5bc31a5210b118f71ad001317a", "title": "We are running the report RC_COLLECT_ANALYSIS_DATA and it's getting cancelled with the following dump: OBJECTS_OBJREF_NO", "problem": "Report RC_COLLECT_ANALYSIS_DATA is getting cancelled with the dump OBJECTS_OBJREF_NOT_ASSIGNED.", "solution": "Reimplement SAP note 2758146. Implement prerequisite notes for FDQ scope. Delete buffered data and schedule a new report.", "cause": "Missing prerequisite notes for Financial Data Quality (FDQ) scope.", "processed_at": "2025-06-25T16:13:18.788874"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/41f7c8ae4722d6d036bf68be436d4364", "title": "TSV_TNEW_PAGE_ALLOC_FAILED error extracting data from report RC_VALUE_DISCOVERY_COLL_DATA", "problem": "TSV_TNEW_PAGE_ALLOC_FAILED error while extracting data from report RC_VALUE_DISCOVERY_COLL_DATA.", "solution": "1. Apply SAP Note 3462648 on the system ERP. 2. Remove KPI KPSD000190 from the report as per SAP KBA 3238485. 3. Re-run the report and monitor results. 4. Increase memory using RSMEMORY if resource issues persist as per SAP KBA 2180736.", "cause": "Memory allocation failure due to excessive data processing, possibly exacerbated by invalid KPIs or insufficient system resources.", "processed_at": "2025-06-25T16:13:25.457371"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/41f7c8ae4722d6d036bf68be436d4364", "title": "TSV_TNEW_PAGE_ALLOC_FAILED error extracting data from report RC_VALUE_DISCOVERY_COLL_DATA", "problem": "ITAB_TOO_MANY_LINES runtime error during job execution BUZ_SCENARIO_SIGNAVIO_PI_COL.", "solution": "Increase memory allocation for relevant ABAP parameters as per SAP KBA 2180736.", "cause": "Exceeding the maximum row number for an internal table due to resource bottleneck.", "processed_at": "2025-06-25T16:13:25.457394"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9cba35543bc7d6580870d44a85e45a82", "title": "Upgrade of S4HANA 2023 Custom Code Analysis data not available", "problem": "Custom Code Analysis data not available after S4HANA upgrade attempt.", "solution": "Ensure ABAP Test Cockpit is correctly implemented using SAP Notes 2436688 and 3293011. Implement the relevant custom code check variant for the target release, e.g., SAP Note 3365357. Verify simplification database content is current as per SAP Note 2241080. Run the RC_COLLECT_ANALYSIS_DATA program: select target SAP S/4HANA version, opt for Custom Code Analysis, schedule analysis. After job completion, download and upload analysis file to SAP Readiness Check.", "cause": "No data available in Custom Code Analysis due to missing implementation of required SAP Notes and configurations.", "processed_at": "2025-06-25T16:13:30.762414"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2efc62b93b2e52546f96634a85e45a21", "title": "s/4 Hana readiness check jobs running long time in FE2", "problem": "S/4 Hana readiness check jobs are running for an extended period in system FE2, with minimal progress in processing financial documents.", "solution": "1. Review KBA 3479180 which explains that long-running analysis is expected and provides guidelines for improving performance, such as setting analysis to run for single fiscal years per job. 2. Check if SAP Notes 3430973 and 3369764 are applicable and implement them if so. 3. Cancel current analysis and restart with new settings.", "cause": "The readiness check jobs have to process a very large number of financial documents, which is causing them to run slowly.", "processed_at": "2025-06-25T16:13:35.188867"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/eb26750e3b729654d537962a85e45a18", "title": "CALL_FUNCTION_NOT_ACTIVE", "problem": "CALL_FUNCTION_NOT_ACTIVE error occurs when executing report RC_VALUE_DISCOVERY_COLL_DATA in the QAS system after implementing SAP SNOTE 2758146 and 2745851.", "solution": "Review the KBAs 3093810, 2977422, and 2968380 which address this issue. Follow guidance provided in these KBAs to resolve the error.", "cause": "The function module '/SDF/BPM_7X_S4RC20_GET_KPIS' cannot be found in its function group, which may be due to it not being activated, incorrect function library entry, or missing code.", "processed_at": "2025-06-25T16:13:40.241409"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/89f788ae4722d6d036bf68be436d4364", "title": "Errors extracting data from report RC_VALUE_DISCOVERY_COLL_DATA", "problem": "Job cancellation when running RC_VALUE_DISCOVERY_COLL_DATA report due to resource shortage.", "solution": "1. Updated all system notes including ST-A/PI 01W_731. 2. Increased system memory. 3. Changed server for report execution. 4. Assigned high priority to job BUZ_SCENARIO_SIGNAVIO_PI_COL.", "cause": "Resource shortage leading to runtime error TSV_TNEW_PAGE_ALLOC_FAILED indicating no more storage space for extending an internal table.", "processed_at": "2025-06-25T16:13:45.546692"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/89f788ae4722d6d036bf68be436d4364", "title": "Errors extracting data from report RC_VALUE_DISCOVERY_COLL_DATA", "problem": "Job cancellation due to internal kernel error in SAP system.", "solution": "Investigated termination point in ABAP program CL_SXML_STRING_WRITER within the main program RC_VALUE_DISCOVERY_COLL_DATA. Collected termination data for further analysis.", "cause": "ABAP processor detected an internal system error leading to process termination by signal 11.", "processed_at": "2025-06-25T16:13:45.546716"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/59256a9fc31696103331583bb001313c", "title": "Readiness Check Job RC_COLLECT_ANALYSIS_DATA taking too much time to finish", "problem": "Readiness Check Job RC_COLLECT_ANALYSIS_DATA taking too much time to finish in the Production system.", "solution": "Increase the number of TAANA instances to run parallel jobs for each table to be analyzed: 1. Execute transaction ST13. 2. Enter DVM_SETUP. 3. Click on the plus sign near TAANA under Job Control to increase parallel TAANA jobs. 4. After readiness check completion, revert to the original value. Reference KBA 0003464919 for further details.", "cause": "The job is long-running due to the number of tables being analyzed, which can be optimized by increasing parallel processing.", "processed_at": "2025-06-25T16:13:50.453450"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/78280935c3bed2144daa2875e00131ea", "title": "SAP ECC to S/4 Hana conversion", "problem": "Unable to perform readiness check during SAP ECC to S/4 Hana conversion.", "solution": "Refer to SAP Community for guidance on readiness check by implementing note 2913617 - SAP Readiness Check for SAP S/4HANA. Use provided SAP Community links for further how-to questions.", "cause": "", "processed_at": "2025-06-25T16:13:54.418003"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0ba94a96c3fade144daa2875e0013167", "title": "S/4 Readiness Check Implementation", "problem": "Syntax error in SAPLFIN_AA_CORR program, field 'RC' is unknown.", "solution": "Refer to KBA 0003345451 for resolution steps. Complete manual checks as per SNOTE 3345451.", "cause": "Field 'RC' is not defined in the specified tables or by a 'DATA' statement.", "processed_at": "2025-06-25T16:13:58.321791"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2bf583b33b2252900870d44a85e45a68", "title": "SAP Readiness Check for SAP S/4HANA upgrades", "problem": "Missing 'Other Tools' and 'Launch Note Analyzer' options in SNOTE transaction for SAP readiness check.", "solution": "Implemented SAP Note 3200109; verified options appear in SNOTE transaction following guide from SAP Note 3365856.", "cause": "Required SAP Note 3200109 was not initially implemented.", "processed_at": "2025-06-25T16:14:04.111848"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2bf583b33b2252900870d44a85e45a68", "title": "SAP Readiness Check for SAP S/4HANA upgrades", "problem": "SAP Readiness Check fails with message 'Prerequisite for Integration not fulfilled'.", "solution": "Followed steps in SAP Note 3107511; deselected 'Integration' checkbox to collect check data.", "cause": "Prerequisite for integration scenario was not met.", "processed_at": "2025-06-25T16:14:04.111872"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2bf583b33b2252900870d44a85e45a68", "title": "SAP Readiness Check for SAP S/4HANA upgrades", "problem": "ABAP dumps occur when executing RC_COLLECT_ANALYSIS_DATA program with exception 'SMDB_CONTNET_NOT_FOUND'.", "solution": "Applied SAP Note 3376638; resolved dump issue by following provided steps.", "cause": "Exception 'SMDB_CONTNET_NOT_FOUND' raised in program TMW_RC_DOWNLOAD_ANALYSIS_DATA.", "processed_at": "2025-06-25T16:14:04.111880"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2bf583b33b2252900870d44a85e45a68", "title": "SAP Readiness Check for SAP S/4HANA upgrades", "problem": "Readiness report generated by RC_COLLECT_ANALYSIS_DATA is difficult to read as it downloads in XML format.", "solution": "Recommended opening a separate incident for formatting issue; suggested reading SAP notes for guidance.", "cause": "Report output format was XML, not suitable for easy readability.", "processed_at": "2025-06-25T16:14:04.111886"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/834988fbc396525030037d9f050131ff", "title": "S/4 HANA Rediness check is running from long time", "problem": "S/4 HANA Readiness Check is running for a long time without completion.", "solution": "Review preliminary checks and adjust analysis settings to a single fiscal year per job. Apply Notes 3430973 and 3369764 if applicable. Cancel and restart the analysis after changes.", "cause": "Long running FDQ analysis due to expected functionality and the number of financial documents.", "processed_at": "2025-06-25T16:14:09.299748"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/834988fbc396525030037d9f050131ff", "title": "S/4 HANA Rediness check is running from long time", "problem": "Uploading sandbox results to SAP Readiness Check shows 'No Data Available' for several analysis areas.", "solution": "Verify the correctness of the RC_COLLECT_ANALYSIS_DATA result output. Ensure all necessary data is included.", "cause": "", "processed_at": "2025-06-25T16:14:09.299771"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/834988fbc396525030037d9f050131ff", "title": "S/4 HANA Rediness check is running from long time", "problem": "Missing 'Update Analysis' option in program SYCM_DOWNLOAD_REPOSITORY_INFO for adding SYCM zip-file to SAP Readiness Check analysis.", "solution": "Refer to the correct steps in Note 2913617 for Custom Code Analysis. Ensure the right procedure is followed for including the SYCM zip-file.", "cause": "Incorrect procedure or misunderstanding of steps outlined in Note 2913617.", "processed_at": "2025-06-25T16:14:09.299776"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/df194ed2c3fade144daa2875e00131ff", "title": "Implementing note 2972792 for ereadiness check will fail", "problem": "Implementing SAP note 2972792 fails because function group /SDF/FDQ_ASSESSMENT does not exist.", "solution": "Perform the steps outlined in Knowledge Base Article 3514476 related to the non-existence of the function group during the note implementation.", "cause": "The prerequisite note 2909538 did not create the necessary function group during its implementation.", "processed_at": "2025-06-25T16:14:13.481210"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e10c60f24778755489fa66ce436d435d", "title": "S4 Readiness Check inconsitency", "problem": "Inconsistent results from SAP Readiness Check, specifically incorrect statements about active company codes and NEW GL activation.", "solution": "1. Request the customer to open the r/3 service system connection to System BUP for further investigation by SAP development. 2. Support team to reproduce the issue using the customer's non-productive environment if necessary.", "cause": "Inaccurate data or misconfiguration within the system leading to incorrect results in the Readiness Check.", "processed_at": "2025-06-25T16:14:20.793774"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d5117e5897d4bd1030c531f71153af7a", "title": "Data collectors for SAP Readiness Check for SAP S/4HANA upgrade", "problem": "The RC_COLLECT_ANALYSIS_DATA report only shows 'SAP S/4 HANA 2021 (Initial shipment stack)' as a target version on the QA system, while the development system displays multiple target versions.", "solution": "Refer to SAP Note 2951527 and follow the resolution steps to manually upload the catalog.", "cause": "The system is unable to download the relevant catalogs using the connection to SAP.", "processed_at": "2025-06-25T16:14:25.376199"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/88b057e6c3187954a1b6d2af050131a0", "title": "SAP Readiness Check - Bricht ab", "problem": "SAP Readiness Check aborts due to error in Financial Data Quality data collection job.", "solution": "Remove and reinstall SAP Note #2758146 as per troubleshooting guide #2968380. Activate checkboxes for yellow marked objects during reinstallation to import changes.", "cause": "System exception 'ERROR_MESSAGE' during Financial Data Quality data collection job TMW_RC_FDQ_DATA_COLL.", "processed_at": "2025-06-25T16:14:31.712138"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/88b057e6c3187954a1b6d2af050131a0", "title": "SAP Readiness Check - Bricht ab", "problem": "Error in reinstallation of SAP Note #2758146 related to missing component 'BPI_TRIAL'.", "solution": "Use SAP Note #3308795 to address the error 'Name is not unique' during implementation of SAP Note #2758146. Ensure all required objects are correctly marked for overwrite during note reinstallation.", "cause": "Data object 'C_BPA_SCENARIO' does not have a component named 'BPI_TRIAL'.", "processed_at": "2025-06-25T16:14:31.712161"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/872243ca97383dd0cdaf79171153af72", "title": "No ppi_petst03n01 data when collecting data via RC_VALUE_DISCOVERY_COLL_DATA for SAP Signavio Process Insights", "problem": "Missing data for file 'ppi_petst03n01' when collecting data via RC_VALUE_DISCOVERY_COLL_DATA for SAP Signavio Process Insights.", "solution": "1. Update ST-A/PI to version 01U SP3 or preferably to 01V, the latest available. 2. Refer to documentation at https://bpi-discovery-proxy.cfapps.eu10.hana.ondemand.com/request/BSR/ for further guidance.", "cause": "Outdated ST-A/PI version causing missing objects during data collection.", "processed_at": "2025-06-25T16:14:35.615242"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8933878bc300b194b118f71ad001319c", "title": "Readiness Check: jobs FIN_CORR_REC_ANALYSIS* don't appear", "problem": "Jobs FIN_CORR_REC_ANALYSIS* are not executed in productive client 052 when starting report RC_COLLECT_ANALYSIS_DATA, while they are executed in client 142.", "solution": "1. Investigate differences between client 052 and client 142 configurations. 2. Verify job scheduling and permissions in client 052. 3. Ensure necessary credentials and configurations are set correctly for client 052.", "cause": "", "processed_at": "2025-06-25T16:14:40.027835"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4c42e4819789391c7bcf59081153afc8", "title": "Dumps on running RC_VALUE_DISCOVERY_COLL_DATA", "problem": "Memory dumps occur when running the RC_VALUE_DISCOVERY_COLL_DATA report.", "solution": "1. Reproduce the issue in a non-production environment. 2. Gather system logs and memory dump details. 3. Analyze memory usage when executing the report. 4. Provide access to the system for further analysis. 5. Investigate the RC_VALUE_DISCOVERY_COLL_DATA and related components.", "cause": "Memory issues during report execution.", "processed_at": "2025-06-25T16:14:44.461582"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6027247ac3d139185fb42c65e001315a", "title": "SAP Innovation and Optimization Pathfinder", "problem": "Path Finder report generated blank.", "solution": "Customer executed pathfinder data collection for PS4 and was advised to implement the latest versions of SAP note 2758146 and 2745851. After implementation, the report RC_VLUE_DISCOVERY_COLL_DATA should be triggered again and the job log and data download provided if the issue persists.", "cause": "Outdated versions of SAP note 2758146 and 2745851 were implemented.", "processed_at": "2025-06-25T16:14:54.253533"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6027247ac3d139185fb42c65e001315a", "title": "SAP Innovation and Optimization Pathfinder", "problem": "User SAPSUPPORT giving 'Name or password is incorrect' error.", "solution": "Requested customer to reset the user SAPSUPPORT or maintain a different user in the secure area for PS4. Additionally, suggested extending the R/3 connection as it was scheduled to expire.", "cause": "Incorrect credentials for user SAPSUPPORT and imminent expiration of R/3 connection.", "processed_at": "2025-06-25T16:14:54.253555"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5ab7f1bdc3bcb9d09109d31f050131ec", "title": "No ppi_petst03n01 data when collecting data via RC_VALUE_DISCOVERY_COLL_DATA for SAP Signavio Process Insights", "problem": "ppi_petst03n01 data missing during collection using RC_VALUE_DISCOVERY_COLL_DATA for SAP Signavio Process Insights.", "solution": "Update system SE9 ST-A/PI version to 01U SP3 or higher. Implement SAP note 2745851 in version 55, ensuring previous version is de-implemented first. Refer to How To guide documentation at provided URL.", "cause": "Current system version ST-A/PI 01U_731 0002 is below required version for proper data collection.", "processed_at": "2025-06-25T16:14:58.373005"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fea16a75c33cb9d0c52cb32f050131fe", "title": "BSR readiness report running long", "problem": "BSR readiness report running longer than expected in production system compared to quality system.", "solution": "Analyzed and confirmed that long runtimes are common due to large amounts of business data. Suggested not canceling the job unless there are indicators of inactivity. Checked and verified the job completion and availability of data collection ZIP files.", "cause": "Large volume of data in production system leading to longer processing times.", "processed_at": "2025-06-25T16:15:02.224536"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9fd4370dc32cf514a1b6d2af050131b7", "title": "Provide access to SID QS1", "problem": "Request for access to system QS1 related to another incident.", "solution": "Verify related incident 319814/2023 status and confirm resolution of original issue. Inform customer that original incident was resolved and suggest closing current incident.", "cause": "", "processed_at": "2025-06-25T16:15:05.988938"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c75f5c564749799ccb4df2b5536d43d2", "title": "Maintenance Planner Status Is Blocked in SAP Readiness Check for SAP BW/4HANA", "problem": "Maintenance Planner status is blocked in SAP Readiness Check for SAP BW/4HANA.", "solution": "1. Run SAP BW Note Analyzer and ensure scenarios are green. 2. Verify the system in Maintenance Planner for errors. 3. Manually transfer system information to Maintenance Planner using SAP KBA 2287046, then re-run the report.", "cause": "Incorrect system identity in Maintenance Planner; system was identified as ERP instead of Netweaver.", "processed_at": "2025-06-25T16:15:10.946410"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/553beb8747c0b5941cccb7da216d43e7", "title": "Report RC_COLLECT_ANALYSYS_DATA  - no DROP DOWN list parameter", "problem": "Empty drop-down list for 'Target S/4HANA Version' parameter in RC_COLLECT_ANALYSIS_DATA report.", "solution": "Ensure that the system is updated with the latest SAP notes and verify that the SAP S/4HANA version list is correctly maintained in the system settings.", "cause": "Missing or outdated configuration for SAP S/4HANA version parameter settings.", "processed_at": "2025-06-25T16:15:18.384979"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8edd547ec3f83190fabf018dc00131dc", "title": "Dropdown list empty for target release S/4 HANA", "problem": "Dropdown list is empty for target release in SAP S/4HANA readiness check report.", "solution": "1. Refer to SAP Note 2951527 and perform the manual steps provided to update the catalog version. 2. If the issue persists, refer to SAP Note 3213713 and update the system with the latest catalog information.", "cause": "Missing catalog information in the system after upgrade.", "processed_at": "2025-06-25T16:15:22.537967"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/734f6def9701b5d0cdaf79171153afc7", "title": "BPA XML file didn’t contain any data - Pathfinder report", "problem": "BPA XML file is empty when generating Pathfinder report.", "solution": "1. Verify the user running the RC_VALUE_DISCOVERY_COLL_DATA program has the authorization object SM_BPM_DET with the characteristic OBJECT_MS. 2. Implement the latest versions of SAP Notes 2758146, 2745851, 2549411, and 2557474. 3. Change the browser to Internet Explorer or alter browser settings to view XML content.", "cause": "The issue was related to missing authorization and outdated SAP notes, with potential browser settings affecting the XML file view.", "processed_at": "2025-06-25T16:15:28.766157"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/734f6def9701b5d0cdaf79171153afc7", "title": "BPA XML file didn’t contain any data - Pathfinder report", "problem": "Unable to connect to the customer system PEC due to route permission denied error.", "solution": "Add the necessary entries in the active 'saprouttab' file of the SAProuter server and activate the new saprouttab using the command 'saprouter -n'.", "cause": "Route permission was denied due to missing configurations in the SAProuter settings.", "processed_at": "2025-06-25T16:15:28.766179"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/36383abec32c3110c52cb32f05013139", "title": "PS4, How to implement report: RC_VALUE_DISCOVERY_COLL_DATA", "problem": "Unable to implement SAP notes 2745851 and 2758146 for Process Discovery in S4HANA systems.", "solution": "Download and implement the latest versions of SAP notes 2745851 and 2758146.", "cause": "Older versions of SAP notes 2745851 and 2758146 were downloaded, preventing implementation.", "processed_at": "2025-06-25T16:15:32.766524"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/732362fa97d1355ccdaf79171153af3e", "title": "Export Pre-Check and Custom code migration report", "problem": "Precheck and custom code migration report shows incorrect version after upgrade from SAP S/4HANA 2020 FPS02 to SAP S/4HANA 2021 FPS02.", "solution": "Update ST-A/PI to version 01U SP02 or later. Implement SAP Note 3072059 and SAP Note 69455. Reset and re-implement SAP Note 2758146, then re-run Report RC_COLLECT_ANALYSIS_DATA.", "cause": "ST-A/Pi version was too low, causing incorrect version to be displayed in the SAP Readiness Check.", "processed_at": "2025-06-25T16:15:37.068218"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dc929128c38ebd98b9946ecd2b013153", "title": "Error installing KBA 2758146", "problem": "Error installing KBA 2758146 in HANA Enterprise Cloud Application Hosting environment.", "solution": "1. Delete duplicate objects 'ana_sys_add_info.xsl' and 'fui-site.css' from SE80 before implementing the note. 2. Implement SAP Note 1668882 and 2971435 if missing. 3. De-implement SAP Note 2758146. 4. Manually delete objects not removed during de-implementation. 5. Re-implement SAP Note 2758146 to ensure all objects are created without errors.", "cause": "Known issue from ST-PI740 SP21/SP22 causing conflict with objects 'ana_sys_add_info.xsl' and 'fui-site.css'.", "processed_at": "2025-06-25T16:15:41.767639"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9fb41fdac33875505fb42c65e0013180", "title": "S4 Readynesscheck", "problem": "Open connection required for SAP readiness check on EUR system.", "solution": "Provided links to SAP Readiness Check documentation and SAP ONE Support Launchpad note 3059197 for guidance on S/4HANA upgrades.", "cause": "", "processed_at": "2025-06-25T16:15:45.051144"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/251c5a3ec3243110c52cb32f05013107", "title": "Errors after implementing note 2758146", "problem": "Syntax error when running report RC_VALUE_DISCOVERY_COLL_DATA after implementing SAP Note 2758146.", "solution": "1. De-implement SAP Note 2758146 and its predecessor 2310438 if implemented. 2. Delete remaining objects manually if not removed during de-implementation (programs and reports in SE38 like TMW_RC_COLLECT_BPA and TMW_RC_COLLECT_CUSTOM_CODE, and objects in SE24). 3. Switch off 'Modification Assistant' if deletion is blocked. 4. Implement the latest version of SAP Note 2758146 and manually activate any warnings.", "cause": "Conflict due to multiple 'REPORT', 'PROGRAM', or 'FUNCTION-POOL' statements in ABAP programs after implementing SAP Note 2758146.", "processed_at": "2025-06-25T16:15:49.325246"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ed1e5472c33c3190fabf018dc0013142", "title": "Syntax Error in Include  /SDF/LFDQ_ASSESSMENTUXX for missing . after INCLUDE /SDF/LFDQ_ASno. 29", "problem": "Syntax error due to missing '.' after line 29 in Include /SDF/LFDQ_ASSESSMENTUXX during SAP Note 2972792 implementation.", "solution": "Customer was advised to download and implement the latest version (71) of SAP Note 2972792.", "cause": "Implementation of an outdated version of SAP Note 2972792, which lacks necessary syntax corrections.", "processed_at": "2025-06-25T16:15:53.237609"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d13954a6c37039505fb42c65e00131a9", "title": "Regarding the execution of S/4 HANA readiness check on the ERP Quality system which got refreshed with Production Data in the last Quarter", "problem": "Error while submitting an incident via SAP for Me platform.", "solution": "<PERSON> clarified that 'SAP for Me' is in the test phase and suggested using SAP Launchpad instead. However, <PERSON> agreed to create the incident for the customer this time.", "cause": "The customer was unaware that 'SAP for Me' was still in the test phase and misunderstood its current status.", "processed_at": "2025-06-25T16:15:57.639422"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d13954a6c37039505fb42c65e00131a9", "title": "Regarding the execution of S/4 HANA readiness check on the ERP Quality system which got refreshed with Production Data in the last Quarter", "problem": "Execution of S/4 HANA readiness check on the ERP Quality system with outdated data.", "solution": "<PERSON> recommended refreshing the Quality Assurance System (QAS) with the latest finished month's data before executing the Readiness Check to ensure comprehensive results.", "cause": "The QAS was refreshed with production data from the last quarter, which may not provide comprehensive results for the readiness check.", "processed_at": "2025-06-25T16:15:57.639445"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/83e7b69047453558cb4df2b5536d4356", "title": "SAP Readiness Check for SAP S/4HANA & Process Discovery", "problem": "HANA Sizing Data checkbox is grayed out in the SAP readiness check report RC_COLLECT_ANALYSIS_DATA.", "solution": "Ensure SAP Note 2758146 is completely implemented and in the latest version. Verify system prerequisites for running the sizing analysis using SAP Note 1872170.", "cause": "System prerequisites to enable the HANA sizing analysis option are not met.", "processed_at": "2025-06-25T16:16:03.579545"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/83e7b69047453558cb4df2b5536d4356", "title": "SAP Readiness Check for SAP S/4HANA & Process Discovery", "problem": "Target S/4HANA version dropdown list is empty while performing SAP readiness check.", "solution": "Check connectivity to SAP backend as per SAP Note 2951527 and ensure proper network configuration.", "cause": "Missing connectivity to SAP backend preventing the display of target S/4HANA versions.", "processed_at": "2025-06-25T16:16:03.579567"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/83e7b69047453558cb4df2b5536d4356", "title": "SAP Readiness Check for SAP S/4HANA & Process Discovery", "problem": "Prerequisites for extended integration impact analysis are not fulfilled during SAP readiness check.", "solution": "Refer to SAP Note 3061414 for 'Reason and Prerequisites' and ensure the minimum version of ST-A/PI is installed.", "cause": "ST-A/PI component is missing or not updated to the required minimum version.", "processed_at": "2025-06-25T16:16:03.579573"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c76f61e4c3e835909109d31f050131b4", "title": "Error in Custom Code Analysis tile of S/4Hana Readiness Check", "problem": "Technical error encountered in the Custom Code Analysis tile during S/4Hana Readiness Check after uploading zip-file.", "solution": "Request customer to retry uploading the zip-file, as backend system bug affecting SYCM result calculation was identified and addressed.", "cause": "Bug in backend system affecting SYCM result calculation processing.", "processed_at": "2025-06-25T16:16:08.504563"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7c2525fac3a075d4fabf018dc00131cf", "title": "At least one note was not implemented completely", "problem": "Incomplete implementation of SAP Note 2758146 due to error 'At least one note was not implemented completely'.", "solution": "Follow KBA 3308795 to resolve the issue: 1. Check the implementation status of notes 1668882, 2971435, and 2310438. 2. Delete duplicate MIME objects using MR_DELETE_MIME for the specified GUIDs. 3. Re-implement SAP Note 2758146.", "cause": "Duplicate MIME objects named 'ana_sys_add_info.xsl' and 'fui-site.css' causing non-unique naming errors during note implementation.", "processed_at": "2025-06-25T16:16:13.389600"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1fae224bc3e4f5105fb42c65e00131aa", "title": "SAP Readiness Check isn't worklng (https://me.sap.com/readinesscheck)", "problem": "SAP Readiness Check page displays blank with a cloud smiley.", "solution": "Review KB article 3330283 for workaround. Ensure third-party cookies are enabled as per company security guidelines.", "cause": "Third-party cookies are disabled due to company security guidelines.", "processed_at": "2025-06-25T16:16:17.300933"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/07b288ba47b8f554cb4df2b5536d43f9", "title": "RC_COLLECT_ANALYSIS_DATA", "problem": "RC_COLLECT_ANALYSIS_DATA job fails due to incomplete tax code.", "solution": "Follow the resolution of issue #2 in SAP KBA 2968380 and ensure SAP KBA 2758146 is implemented.", "cause": "Tax code 1 is incomplete.", "processed_at": "2025-06-25T16:16:21.296394"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c53465574790b9d489fa66ce436d433a", "title": "S4HANA READINESS CHECK", "problem": "Syntax error in the ABAP program '/SDF/CL_RC_SIMPLE_CHK_DB' leading to a runtime issue during the SAP S/4HANA readiness check.", "solution": "Re-implement SAP Note 2399707 in the latest version. Resolve any yellow warnings from SNOTE during implementation. Refer to the blog 'SAP Note Simplification Item Check Implementation Problem' for additional guidance.", "cause": "Field 'LV_CURSOR' is unknown, causing a syntax error in the ABAP program.", "processed_at": "2025-06-25T16:16:25.289376"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7b6b29d597013d5c30c531f71153af88", "title": "Implement Note 2758146 issue", "problem": "Syntax errors encountered during the implementation of SAP Note 2758146.", "solution": "1. Review the steps provided in SAP Note 2758146. 2. Consult SAP Knowledge Articles 3221283 and 2968380 for troubleshooting guidance. 3. Forwarded case to note owner team for further investigation.", "cause": "Unknown field and type errors related to function module /SDF/GEN_FUNCS_S4_IP_CHKS, possibly due to missing prerequisites or incorrect implementation steps.", "processed_at": "2025-06-25T16:16:31.147120"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7b6b29d597013d5c30c531f71153af88", "title": "Implement Note 2758146 issue", "problem": "Warnings related to the use of CLIENT SPECIFIED addition in cross-client tables during the implementation of SAP Note 2758146.", "solution": "Check implementation steps for compliance with client-specific and cross-client table rules as outlined in SAP documentation.", "cause": "Misuse of CLIENT SPECIFIED addition in cross-client tables such as TSTC, TRDIR, AQGSCAT, and *PERSON*.", "processed_at": "2025-06-25T16:16:31.147141"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/24be2b8f47897d5c1d1ff2b5536d4336", "title": "Readiness Check not accessible anymore", "problem": "Readiness Check is not accessible for Schuler and SAP internal users.", "solution": "Verified authorization settings and customer numbers. Corrected the customer number to match the system connection, enabling access again.", "cause": "Incorrect customer number assignment led to access issues.", "processed_at": "2025-06-25T16:16:35.607684"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/24be2b8f47897d5c1d1ff2b5536d4336", "title": "Readiness Check not accessible anymore", "problem": "SAP Readiness Check migration caused authorization issues.", "solution": "Instructed users to use SAP for Me platform for access. Provided SAP Note 3310759 for enhanced authorization concept.", "cause": "Migration of SAP Readiness Check to SAP for Me changed the authorization requirements.", "processed_at": "2025-06-25T16:16:35.607712"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/aab21487c381b95c5fb42c65e0013133", "title": "Executing report RC_VALUE_DISCOVERY_COLL_DATA immediately results in an ABAP Dump CX_SY_DYN_CALL_ILLEGAL_FUNC", "problem": "Executing report RC_VALUE_DISCOVERY_COLL_DATA results in an ABAP Dump CX_SY_DYN_CALL_ILLEGAL_FUNC due to missing function module /SDF/BPM_7X_S4RC20_GET_KPIS.", "solution": "1. Verify report RC_VALUE_DISCOVERY_COLL_DATA works in DEV system. 2. Write Function Group /SDF/BPM_7X to a transport request. 3. Release transport request to PROD system.", "cause": "Inconsistencies in function module /SDF/BPM_7X_S4RC20_GET_KPIS as part of /SDF/BPM_7X.", "processed_at": "2025-06-25T16:16:41.878228"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/aab21487c381b95c5fb42c65e0013133", "title": "Executing report RC_VALUE_DISCOVERY_COLL_DATA immediately results in an ABAP Dump CX_SY_DYN_CALL_ILLEGAL_FUNC", "problem": "Customer cannot repair function group as client is locked against changes in production server.", "solution": "1. De-implement and re-implement notes 2758146 and 2745851 in source system. 2. Transport notes to production system again.", "cause": "Client in production system is locked against changes.", "processed_at": "2025-06-25T16:16:41.878249"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/********************************", "title": "syntax error implementing note", "problem": "Syntax error occurs when implementing SAP Note 2745851 in SNOTE.", "solution": "Reviewed SAP Note 3205320 and performed cleanup procedures to resolve the syntax error.", "cause": "Conflict with ST-A/PI component version ST-A/PI 01V_700.", "processed_at": "2025-06-25T16:16:46.326603"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/********************************", "title": "syntax error implementing note", "problem": "Syntax error dump in class /SDF/CL_S4RC20 when executing program RC_COLLECT_ANALYSIS_DATA.", "solution": "Followed steps in SAP Knowledge Base Article 3283120 to resolve syntax error.", "cause": "Method 'ADDTOJSON' not declared or inherited in class '/SDF/CL_S4RC'.", "processed_at": "2025-06-25T16:16:46.326627"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bb91e427976cb95030c531f71153af15", "title": "Readiness Report after Migration to SAP for Me", "problem": "Unable to access SAP Readiness Check after migration to SAP for Me, encountering a cloud image instead of the report.", "solution": "1. Open a new browser session and access the link https://alm.me.sap.com/login/amalthea?tenant=pr. 2. Log in using the s-user credentials and complete the authorization process. 3. Open https://me.sap.com/readinesscheck in the same browser window. 4. If prompted for login again, use the same s-user credentials.", "cause": "Known issue with SAP4Me affecting multiple users.", "processed_at": "2025-06-25T16:16:52.363038"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bb91e427976cb95030c531f71153af15", "title": "Readiness Report after Migration to SAP for Me", "problem": "SAP Readiness Check not accessible despite having display rights.", "solution": "Forwarded the case to Development Support for further investigation.", "cause": "Authorization concept issues despite correct roles being assigned.", "processed_at": "2025-06-25T16:16:52.363378"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6ba061c4c3c53d145fb42c65e00131d1", "title": "unable to manage readiness check", "problem": "Unable to manage SAP readiness check due to authorization issues.", "solution": "1. Customer advised to follow steps in SAP Note 3310759 for revised authorization concept. 2. Recommended checking further using KBA 3137140 for authorization issues in readiness check integration sub-items.", "cause": "Authorization setup not completed as per revised guidelines in SAP Note 3310759.", "processed_at": "2025-06-25T16:16:56.315921"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0a9912a2c354bd549109d31f050131e3", "title": "Custom code analysis issue", "problem": "Technical error encountered while executing custom code analysis via SAP Readiness Check.", "solution": "Ensure open connections to the affected system and provide a user for issue recreation.", "cause": "", "processed_at": "2025-06-25T16:17:00.072901"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8902ca0597cd79d8cdaf79171153af0b", "title": "SAP Readiness Check for SAP S/4HANA upgrades||Runtime error", "problem": "Syntax error in SAP program RC_COLLECT_ANALYSIS_DATA during S/4HANA readiness check.", "solution": "Follow Solution 4, 5, and 6 in KBA 3221283 to resolve the syntax error.", "cause": "Incomplete implementation of SAP Note 2758146 leading to syntax error.", "processed_at": "2025-06-25T16:17:07.194529"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8902ca0597cd79d8cdaf79171153af0b", "title": "SAP Readiness Check for SAP S/4HANA upgrades||Runtime error", "problem": "Background job processes occupied, preventing sub-jobs from completing data collection.", "solution": "Cancel running RC jobs, increase number of BTC work processes, restart the system, and rerun the readiness check.", "cause": "Limited BTC work processes causing resource contention among active jobs.", "processed_at": "2025-06-25T16:17:07.194555"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6792c2cd97cd79d8cdaf79171153afae", "title": "readiness check dashboard issue", "problem": "Unable to access SAP Readiness Check dashboard due to SAP4Me migration issues.", "solution": "1. Open a new browser session and access https://alm.me.sap.com/login/amalthea?tenant=pr. 2. Log in using your s-user credentials. 3. Confirm user authorization by checking for a blue bar. 4. Access https://me.sap.com/readinesscheck in the same browser window. 5. Log in again if prompted using the same s-user credentials.", "cause": "Known issue following SAP4Me migration causing instability and access errors.", "processed_at": "2025-06-25T16:17:14.339099"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6792c2cd97cd79d8cdaf79171153afae", "title": "readiness check dashboard issue", "problem": "SAML error encountered when trying to access the SAP Readiness Check application.", "solution": "Attempted to access application by disabling SAML via the URL parameter (saml2=disabled), but it did not resolve the issue.", "cause": "SAML configuration issue related to SAP for Me migration.", "processed_at": "2025-06-25T16:17:14.339115"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c9f50bd047cdb9541d1ff2b5536d4390", "title": "Custom code analysis issue", "problem": "No result returned for Custom Code analysis in Readiness check dashboard.", "solution": "Attempt to upload the Custom Code zip file to Portal again to see if the error persists. If the error continues, provide written approval for SAP to reproduce the issue and open a remote connection to the issue system.", "cause": "Inconsistency in the backend system.", "processed_at": "2025-06-25T16:17:18.813342"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/60dd945dc3e43114c52cb32f050131f5", "title": " GL Part of the Readiness Checks Not Populating", "problem": "GL part of the readiness checks not populating in the SAP Readiness Check Portal.", "solution": "1. Verify prerequisite SAP Note 2909538 is implemented. 2. Implement SAP Notes 2972792, 2755360, and 3053181 required for FI-GL Analysis. 3. Ensure support packages including these notes are installed correctly.", "cause": "Missing implementation of FI-GL Analysis setup as per SAP Note 2972792.", "processed_at": "2025-06-25T16:17:22.955131"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/61653e8047b4fd901d1ff2b5536d431a", "title": "Readiness Check for Cloud ALM doesn't accept my input file", "problem": "SAP Readiness Check for Cloud ALM does not accept the input file, showing the error 'Uploaded file not valid'.", "solution": "Reimplement SAP Note 3236443 in its latest version. Delete the buffered data and execute a new data collection process. Confirm the output is now accepted.", "cause": "The data archive contains an outdated file structure (ana_sys_add_info.xml instead of alm_sys_add_info.xml) due to an earlier version of the SAP Note.", "processed_at": "2025-06-25T16:17:28.387907"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6b287ad447453558cb4df2b5536d43b9", "title": "RC_COLLECT_ANALYSIS_DATA ERROR", "problem": "Error 'Prerequisite for integration not fulfilled' when executing RC_COLLECT_ANALYSIS_DATA program in SAP ERP.", "solution": "Update the ST-A/PI add-on to version 01U* SP02 or later and implement SAP Note 3072059.", "cause": "Outdated version of the ST-A/PI component not meeting integration prerequisites.", "processed_at": "2025-06-25T16:17:33.435961"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/95b7664047347d9089fa66ce436d4327", "title": "Readiness no authorization", "problem": "Authorization missing for SAP Readiness Check in SAP Fiori for SAP ERP HCM.", "solution": "1. Confirmed no authorization data found for user *S-USER*. 2. Advised customer to check authorization request and approval according to SAP Note 3310759.", "cause": "User *S-<PERSON><PERSON>* hasn't been assigned the necessary authorization in the backend.", "processed_at": "2025-06-25T16:17:36.994699"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/860d31abc385f9549109d31f0501315a", "title": "SAP Readiness check analysis page issue", "problem": "SAP Readiness Check analysis page displays a cloud mark and is not accessible.", "solution": "Ensure the user has the necessary authorizations as per the revised authorization concept for SAP Readiness Check. Follow the steps provided in KBA 3310759 to request authorizations. Check if the S-user is locked and remove the lock if necessary.", "cause": "Lack of required authorizations due to the new authorization concept in SAP for Me.", "processed_at": "2025-06-25T16:17:42.750937"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/860d31abc385f9549109d31f0501315a", "title": "SAP Readiness check analysis page issue", "problem": "Bold360 Chat could not be loaded; given Chat ID might be invalid.", "solution": "Verify the validity of the Chat ID used for Bold360 Chat and ensure the correct ID is entered.", "cause": "Invalid Chat ID was provided for Bold360 Chat interaction.", "processed_at": "2025-06-25T16:17:42.750970"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9f52eb7c472431d0cb4df2b5536d43d1", "title": "RC_COLLECT_ANALYSIS_DATA cancelled", "problem": "RC_COLLECT_ANALYSIS_DATA job cancelled due to system exception ERROR_MESSAGE.", "solution": "Implement latest versions of relevant SAP notes (2758146, 3010669, and 2811183), reset the user credentials for D01, ensure valid user for R/3 connection, and download the latest data collection to generate RC report.", "cause": "Field Postal Bank Curr.Acc contained blanks, causing system exception ERROR_MESSAGE.", "processed_at": "2025-06-25T16:17:47.821872"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9f52eb7c472431d0cb4df2b5536d43d1", "title": "RC_COLLECT_ANALYSIS_DATA cancelled", "problem": "Unable to access RC_COLLECT_ANALYSIS_DATA report due to user access issues.", "solution": "Verify and update logon credentials for D01 system on SAP service marketplace.", "cause": "Valid user missing for D01 R/3 connection.", "processed_at": "2025-06-25T16:17:47.821889"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e9822cc59789391c7bcf59081153af79", "title": "RC_HCM_COLLECT_ANALYSIS_DATA", "problem": "SQL error 'SQL code: 1555' occurred while accessing table 'PA2002' during execution of RC_HCM_COLLECT_ANALYSIS_DATA.", "solution": "Increase the size of rollback segments to ensure they are large enough to handle the data snapshots required for the operation.", "cause": "The error is caused by insufficient rollback segment size, leading to 'snapshot too old' error when trying to access data from table 'PA2002'.", "processed_at": "2025-06-25T16:17:52.022495"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/94d6b901c3bcb9509109d31f05013126", "title": "Issues with the SAP Readiness Analysis check", "problem": "Errors encountered with 'Add-on compatibility' and 'Active Business Functions' during SAP Readiness Analysis check.", "solution": "Refer to Knowledge Article 2847830 for resolving issues with empty tiles in SAP Readiness Check 2.0 regarding 'Add-On Compatibility' and 'Active Business Functions'.", "cause": "", "processed_at": "2025-06-25T16:17:55.890727"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7704d923970175147bcf59081153afa5", "title": "S/4 Readiness Check - *PERSON* Add-on-Kompatibilität und Aktive Business Fuctions", "problem": "Error message in S/4 Readiness Check related to Add-on compatibility and active business functions; results not visible.", "solution": "Delete A22 system entry in the Maintenance Planner and re-create it via a system upload from Solution Manager. Run verification of A22 in the Maintenance Planner and check the behavior in the S/4 Readiness Check analysis again.", "cause": "Issue with the A22 system entry in the Maintenance Planner.", "processed_at": "2025-06-25T16:17:59.804388"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/df389a6c4724f99089fa66ce436d43d5", "title": "Readiness Check for SAP S/4HANA - Simplification Check  FI-AA Error notification.", "problem": "FI-AA error notification during Readiness Check for SAP S/4HANA migration in development system.", "solution": "Clarified that FI-AA component is not relevant for the HCM system being checked and suggested verifying system configuration settings to ensure accurate readiness checks.", "cause": "Misconfiguration or incorrect inclusion of FI-AA component in readiness check settings despite its non-implementation in the HCM system.", "processed_at": "2025-06-25T16:18:03.861337"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b54453b4c3247d905fb42c65e0013159", "title": "Note 2758146 implementation giving error in Dev", "problem": "Implementation of SAP Note 2758146 results in an error in the Dev system.", "solution": "Step 1: Implement SAP Note 1668882 and 2971435 if missing. Step 2: De-implement SAP Note 2758146. Step 3: Manually delete undeleted objects from Note 2758146 during de-implementation. Step 4: Re-implement SAP Note 2758146 ensuring all objects are correctly activated.", "cause": "The error occurs due to ABAP programs containing more than one 'REPORT', 'PROGRAM', or 'FUNCTION-POOL' statement, possibly caused by remnants of previous implementations not being fully cleaned.", "processed_at": "2025-06-25T16:18:10.950465"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c7f76883c3d4fd94fabf018dc0013142", "title": "we need implement note 3308795 but no exist", "problem": "Customer needs to implement SAP note 3308795 which does not exist.", "solution": "SAP identified that 3308795 is a Knowledge Base Article (KBA) and not a note. Customer was advised to follow the resolution steps in the KBA manually in their system.", "cause": "3308795 is not a downloadable SAP Note; it is a Knowledge Base Article.", "processed_at": "2025-06-25T16:18:16.105727"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c7f76883c3d4fd94fabf018dc0013142", "title": "we need implement note 3308795 but no exist", "problem": "Error encountered while implementing SAP note 2758146 related to a message in note 2968380.", "solution": "Involve SV-SCS-S4R team for assistance. As a workaround, implement the latest SNOTE correction notes 1668882 and 2971435. If note 2310438 was previously implemented, revert it before retrying note 2758146. Manually delete MIME objects causing errors using the report MR_DELETE_MIME.", "cause": "Implementation of note 2758146 requires specific corrections and object deletions due to existing system configurations.", "processed_at": "2025-06-25T16:18:16.105744"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f74e1c11c3283114c52cb32f050131fa", "title": "S4 HANA Readiness Check report not executing", "problem": "S4 HANA Readiness Check report RC_COLLECT_ANALYSIS_DATA is not executing due to a warning message regarding SAP Note 2827612.", "solution": "The issue is a known problem and the solution is provided through SAP Note 2758146. Refer to Known issue #13 in SAP KBA 2968380 for troubleshooting.", "cause": "SAP Note 2827612 is not applicable because the system is already on a higher ST-PI version than required. This results in a warning message despite the system being up-to-date.", "processed_at": "2025-06-25T16:18:20.224319"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2b1f962047557d54cb4df2b5536d43a1", "title": "RC_COLLECT_ANALYSIS_DATA runtime error", "problem": "Runtime error OBJECTS_OBJREF_NOT_ASSIGNED in program RC_COLLECT_ANALYSIS_DATA during SAP Readiness Check.", "solution": "1. Implement SAP Note 2758146 related to SAP Readiness Check. 2. If issue persists, execute report RC_VALUE_DISCOVERY_COLL_DATA as per resolution in SAP Note 3093810.", "cause": "Access via 'NULL' object reference in ABAP program leading to execution failure.", "processed_at": "2025-06-25T16:18:24.910722"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2b1f962047557d54cb4df2b5536d43a1", "title": "RC_COLLECT_ANALYSIS_DATA runtime error", "problem": "Dumps occurring when discovering Introscope Enterprise Manager 10.8 in SAP Solution Manager 7.2.", "solution": "Follow SAP Note 3294661 for steps to resolve the dump OBJECTS_OBJREF_NOT_ASSIGNED error.", "cause": "Upgrading Introscope EM to version 10.8 causing ABAP program errors during discovery and user management functions.", "processed_at": "2025-06-25T16:18:24.910740"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/772d3805474dfdd889fa66ce436d4377", "title": "TMW_RC_FDQ_DATA_COLL extended run", "problem": "TMW_RC_FDQ_DATA_COLL job is running for an extended period while extracting Financial data quality check reports.", "solution": "1. Check if SAP Note 3320691 can be implemented to the system. 2. If applicable, implement the note, cancel the current RC jobs, and run a new RC job. 3. Monitor the behavior again. 4. Refer to SAP Note 2972792 for further information on Financial Data Quality checks.", "cause": "Extended run time may be normal depending on the amount of data to be collected.", "processed_at": "2025-06-25T16:18:29.646831"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6181b993471cb1d4cb4df2b5536d43b3", "title": "SAP Readiness Check Uploaded File is not Valid", "problem": "SAP Readiness Check uploaded file is marked as not valid when trying to map progress from Solution Manager to SAP Cloud ALM.", "solution": "1. Update SAP note 3236443 to the latest version. 2. Run the report again to generate a new zip file. 3. Upload the newly generated zip file.", "cause": "Outdated version of SAP note 3236443 was used, leading to incompatibility issues with the uploaded file.", "processed_at": "2025-06-25T16:18:34.032130"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9b857ec047b4fd901d1ff2b5536d43ed", "title": "Readiness Check hochgeladen - Aktuelle Analysen seit längerer Zeit in Status \"in Vorbereitung\"", "problem": "Readiness check analysis remains in 'In Preparation' status for an extended period.", "solution": "Re-upload the readiness check zip file to SAP link and trigger a recover action on 'In Preparation' analyses.", "cause": "Potential instability in the connection to the on-premise system.", "processed_at": "2025-06-25T16:18:38.060844"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c63fb1579758fd94cdaf79171153af00", "title": "SAP Readiness check report- error 2399707 is not installed", "problem": "Error 2399707 is not installed when scheduling analysis using SAP Readiness Check report.", "solution": "1. Verify installation of Note 2399707 in the test system IMK. 2. Ensure the note is correctly released in IMK. 3. Check compatibility and prerequisites for the note installation. 4. Reinstall the note if necessary.", "cause": "Note 2399707 was not properly installed or released in the IMK system despite being installed in the test environment.", "processed_at": "2025-06-25T16:18:42.416228"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/18688e79c3e039509109d31f050131d1", "title": "Readiness Check", "problem": "User unable to access results of SAP Readiness Check via SAP for Me.", "solution": "Provided Knowledge Base Article 3330283 addressing the issue of SAP Readiness Check not opening in SAP for Me.", "cause": "Redirection issue with the URL link in SAP for Me, leading to no results being displayed.", "processed_at": "2025-06-25T16:18:46.264987"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b910e26bc39075105fb42c65e001315f", "title": "Error SMDB content in local DB not found", "problem": "Error SMDB content in local DB not found when running report RC_BW_COLLECT_ANALYSIS_DATA.", "solution": "Follow SAP Note 2951527 to update the catalog information manually, ensuring the system has the latest data for the target release.", "cause": "Missing catalog information in the system, leading to an empty dropdown list for target release selection.", "processed_at": "2025-06-25T16:18:50.179214"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ef62fe55c334f590fabf018dc001310e", "title": "TMW_RC_FDQ_DATA_COLL job getting hung status", "problem": "TMW_RC_FDQ_DATA_COLL job getting hung status during Financial Data Quality scope selection.", "solution": "Switch to Expert Mode, select Parallel Analysis option under Data collection mode for Financial Areas, then schedule analysis.", "cause": "Job gets stuck in FI-ML reconciliation analysis phase when Sequential Analysis is selected due to internal errors.", "processed_at": "2025-06-25T16:18:55.408563"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ef62fe55c334f590fabf018dc001310e", "title": "TMW_RC_FDQ_DATA_COLL job getting hung status", "problem": "Internal session terminated with runtime error RUNT_INTERNAL_ERROR during FI-ML reconciliation analysis.", "solution": "Refer to SAP Note 1540034 - Short dump TSV_UNIQUE_NUMBER_OVERFLOW for resolution steps.", "cause": "The short dump occurs if the threshold for unique table body numbers is exceeded in the roll area.", "processed_at": "2025-06-25T16:18:55.408580"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9888ef7cc391f1d05fb42c65e001319b", "title": "<PERSON> Dump in 3236443", "problem": "Short dump occurs during execution of RC_ALM_ANALYSIS_DATA_COLLECTION due to RFC check.", "solution": "Upgrade SAP Note 3236443 to the latest version to resolve the issue.", "cause": "The job checks the registered program of WEBADM, causing a runtime error.", "processed_at": "2025-06-25T16:18:59.499322"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d60d46529709f55030c531f71153af23", "title": "Error occurs with HANA Sizing data collection job TMW_RC_HANAS_DATA_COLL.", "problem": "Error occurs with HANA Sizing data collection job TMW_RC_HANAS_DATA_COLL.", "solution": "Update ST-PI to the latest version and implement note 2062012 to include the missing LOAD parameter.", "cause": "ST-PI version is on an old SP level missing required coding for /SDF/TRIGGER_HDB_SIZING.", "processed_at": "2025-06-25T16:19:03.625820"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d60d46529709f55030c531f71153af23", "title": "Error occurs with HANA Sizing data collection job TMW_RC_HANAS_DATA_COLL.", "problem": "SNC processing failed with error message SncProcessInput.", "solution": "Check the connection to DEV and maintain it properly.", "cause": "", "processed_at": "2025-06-25T16:19:03.625845"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/23f8a54197b8395030c531f71153afce", "title": "Custom Code Analyzer List Uploading Issue | Readiness Check", "problem": "Error occurs when uploading S4HMigrationRepositoryInfo<SID>.zip file to the SAP readiness check portal.", "solution": "Upload the file to an existing SAP Readiness Check report.", "cause": "The file generated by SYCM_DOWNLOAD_REPOSITORY_INFO is part of an existing SAP Readiness Check report and cannot be uploaded separately.", "processed_at": "2025-06-25T16:19:07.596765"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/57715628979dfd94cdaf79171153af9c", "title": "S4 HANA Migration", "problem": "Customer requires information on the applicability of Readiness Checks for S/4 HANA migration.", "solution": "Provided SAP Note 2913617 which details the supported source releases for SAP Readiness Check for S/4 HANA: SAP ERP 6.0 (Enhancement Package 0 to 8), SAP S/4HANA Finance 1503 and 1605. Re-routed the case to the SV-SCS-S4R component for further expert involvement.", "cause": "Lack of clarity on which business suite systems are supported by SAP Readiness Check for S/4 HANA migration.", "processed_at": "2025-06-25T16:19:11.713369"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a0c334f9c3b4b1d0a1b6d2af0501313a", "title": "Technical Error on Readiness Check", "problem": "Technical error in SAP Readiness Check preventing full evaluation due to issues with 'Recommended SAP Fiori Apps' and 'Add-on Compatibility'.", "solution": "1. Refer to KBA 2847830 for 'Add-On Compatibility' issue. 2. Ensure latest version of Note 2758146 is implemented for 'Recommended SAP Fiori Apps'. 3. Run report RC_COLLECT_ANALYSIS_DATA to collect data. 4. Upload the zip file to Readiness Check again. 5. Provide analysis link and zip file if issue persists. 6. Open remote connection and maintain logon data securely for further investigation.", "cause": "Empty tiles in SAP Readiness Check due to missing system data on Maintenance Planner and potentially outdated system notes.", "processed_at": "2025-06-25T16:19:16.334225"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f113b6fac3c5355c5fb42c65e0013107", "title": "Fehler Readiness Check 2.0", "problem": "Syntax errors occur when running the RC_COLLECT_ANALYSIS_DATA report after installing OSS note 2758146.", "solution": "Implement SAP Note 2758146; ensure all correction instructions are completely applied. Verify report execution and check for errors post-implementation.", "cause": "Incomplete implementation of correction instructions from SAP Note 2758146 leading to syntax errors.", "processed_at": "2025-06-25T16:19:22.304423"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f113b6fac3c5355c5fb42c65e0013107", "title": "Fehler Readiness Check 2.0", "problem": "Multiple versions of code section 'AT SELECTION-SCREEN OUTPUT' causing issues during manual correction.", "solution": "Avoid manual corrections due to risk of unpredicted issues and potential system modification complications.", "cause": "Presence of multiple versions of the 'AT SELECTION-SCREEN OUTPUT' code section in the program.", "processed_at": "2025-06-25T16:19:22.304440"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f113b6fac3c5355c5fb42c65e0013107", "title": "Fehler Readiness Check 2.0", "problem": "Logon issues preventing SAP Support from accessing the system for further analysis.", "solution": "Ensure that the system is open and logon data is correctly stored in the secure area for SAP Support access.", "cause": "Logon credentials not accessible or incorrectly configured in the secure area.", "processed_at": "2025-06-25T16:19:22.304442"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3f3f36b1c3e0f1545fb42c65e00131d0", "title": "Dump when running RC_COLLECT_ANALYSIS_DATA for Readiness Checks", "problem": "Runtime error RAISE_EXCEPTION with exception SMDB_CONTNET_NOT_FOUND when executing RC_COLLECT_ANALYSIS_DATA for Readiness Checks.", "solution": "1. Refer to SAP Note 2951527 for resolution steps. 2. Manually download SIC content and upload into the system as per SAP KBA 2951527.", "cause": "The SUPPORT_PORTAL destination is not set up.", "processed_at": "2025-06-25T16:19:26.304467"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9e6dd234c324b9905fb42c65e00131f5", "title": "SAP Readiness Check for SAP SuccessFactors Solutions", "problem": "Report RC_HCM_COLLECT_ANALYSIS_DATA is unavailable, and SAP notes show cannot be implemented for SAP Readiness Check.", "solution": "1. Re-download Note 3193560 and confirm applicability. 2. Apply the note correction. 3. Import RC_HCM_COLLECT_ANALYSIS_DATA. 4. Ensure user ID has Activity 16 authorization for S_DEVELOP.", "cause": "Uncompleted installation or update in t-code SAINT & t-code SPAM.", "processed_at": "2025-06-25T16:19:30.598341"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/17922b70476431d0cb4df2b5536d433f", "title": "Error while loading file to SAP Readiness Check", "problem": "Error message appears when uploading the Readiness Check zip file for Solution Manager.", "solution": "The ticket was forwarded to the correct component (SV-SCS-S4R) for expert analysis. SAP Note 3236443 was referenced for guidance on SAP Readiness Check for SAP Cloud ALM.", "cause": "Incorrect component initially selected by the customer for the issue.", "processed_at": "2025-06-25T16:19:34.480507"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/61c6b1809769f51430c531f71153af59", "title": "SAP Readiness Check for S/4HANA fails with message \"Prerequisite for Integration not fulfilled. Check SAP Note 3061414 for more details", "problem": "SAP Readiness Check for S/4HANA fails with message 'Prerequisite for Integration not fulfilled'.", "solution": "Verify prerequisites as per SAP Note 3061414. If prerequisites are fulfilled as per KBA 3107511, then escalate the issue to the SV-SCS-S4R team for further investigation.", "cause": "The system prerequisites for integration were not correctly fulfilled or verified.", "processed_at": "2025-06-25T16:19:40.665400"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/61c6b1809769f51430c531f71153af59", "title": "SAP Readiness Check for S/4HANA fails with message \"Prerequisite for Integration not fulfilled. Check SAP Note 3061414 for more details", "problem": "Permission to start external process '/usr/bin/osascript' has been denied.", "solution": "Open the connection and store logon data in a secure area to allow access. Ensure the necessary permissions are granted.", "cause": "Permission settings did not allow the execution of external processes required for system access.", "processed_at": "2025-06-25T16:19:40.665426"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e980f9a1c38df598c52cb32f05013115", "title": "BPA Xml file Data Missing for Pathfinder Report Request for SAP Readiness.", "problem": "BPA XML file data is missing for the Pathfinder report request.", "solution": "Apply SAP Notes 3270847 and 3261488 in the ZPD system and execute the report RC_VALUE_DISCOVERY_COLL_DATA again.", "cause": "Authorization errors and note version issues.", "processed_at": "2025-06-25T16:19:45.298863"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e980f9a1c38df598c52cb32f05013115", "title": "BPA Xml file Data Missing for Pathfinder Report Request for SAP Readiness.", "problem": "Missing authorization for user executing RC_VALUE_DISCOVERY_COLL_DATA program.", "solution": "Ensure the user ID executing RC_COLLECT_ANALYSIS_DATA has authorization object SM_BPM_DET with 'OBJECT_MS'.", "cause": "User ID does not have the required authorization object.", "processed_at": "2025-06-25T16:19:45.298879"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e0ca038bc3683910fabf018dc00131d4", "title": "Problem with SAP Note Implementation", "problem": "Unable to implement SAP Note 2758146 due to error 'Content data for object interface.xsl not found'.", "solution": "Manually corrected MIME objects of the note and saved changes in transport TP1K900083.", "cause": "Missing content data for specific MIME objects required for note implementation.", "processed_at": "2025-06-25T16:19:49.734081"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e0ca038bc3683910fabf018dc00131d4", "title": "Problem with SAP Note Implementation", "problem": "Download service not available in destination SM_QSMCLNT001_TRUSTED, unable to connect to SAP Backbone.", "solution": "Ensure access rules are set correctly in SAProuter and verify system connection settings.", "cause": "Connection issues due to incorrect SAProuter configuration and permissions.", "processed_at": "2025-06-25T16:19:49.734096"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/644ee26ac38539909109d31f05013146", "title": "Missing *PERSON* analysis", "problem": "Customer NIS cannot access their previously created *PERSON* analysis on the spotlight.cloud.sap platform.", "solution": "Access to the *PERSON* online solution was provided for 2 years, and the project was deleted in February 2023. The customer can use the PDF summary sent via email. For new analysis, they need to request Process Discovery & *PERSON* via www.sap.com/Pathfinder.", "cause": "The project was deleted in February 2023, and the customer was under EU sanctions from 15.5.22 until 31.12.22.", "processed_at": "2025-06-25T16:19:54.275045"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c45986bac38dfd5cc52cb32f050131c0", "title": "Snote 2758146 incompletely implemented", "problem": "SAP Note 2758146 is incompletely implemented causing issues in the S4Hana server configuration for SAP Innovation and Optimization Pathfinder on Spotlight.", "solution": "Refer to the steps provided in KBA 3308795 to resolve the issue with SAP Note 2758146 implementation.", "cause": "The root cause seems to be related to non-unique file names in the system environment, specifically 'ana_sys_add_info.xsl' and 'fui-site.css'.", "processed_at": "2025-06-25T16:19:58.716863"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1201aac39764391030c531f71153af33", "title": "Already grant auth. according to 3310759 but users still cannot access SAP Readiness Check", "problem": "Users cannot access SAP Readiness Check despite having assigned authorizations.", "solution": "Incident was forwarded to the next support level for further investigation.", "cause": "Permissions were granted, but users still experience access issues.", "processed_at": "2025-06-25T16:20:02.076773"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9ac8f8a747453d9c1d1ff2b5536d437b", "title": "ATC finding error in readiness check", "problem": "Error in SAP Readiness Check showing issues with Addon compatibility and Active business function after uploading ATC findings.", "solution": "Check if DS4 system is present on Maintenance Planner (MP). Utilize KBA 2847830 to resolve empty tiles issue for Add-On Compatibility and Active Business Functions.", "cause": "System missing on <PERSON> Planner (MP).", "processed_at": "2025-06-25T16:20:06.576501"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/642f4d96c33531544daa2875e0013114", "title": "S/4 HANA Readiness check note 2909538 issue", "problem": "Cannot execute report /SDF/NOTE_2909538 as it is older than 60 days.", "solution": "Verify that DDIC objects like package /SDF/FDQ_API and function group /SDF/FDQ_ASSESSMENT have been created, indicating the report has been executed. If so, no need to re-execute the report.", "cause": "Report /SDF/NOTE_2909538 has already been executed previously and does not need to be run again.", "processed_at": "2025-06-25T16:20:15.126472"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/642f4d96c33531544daa2875e0013114", "title": "S/4 HANA Readiness check note 2909538 issue", "problem": "Error regarding Transport Based Correction (TCI) while implementing note 3200109.", "solution": "Download the TCI package SAPK750011CPSAPBASIS from Software downloads and upload it into the system JED, then implement note 3200109.", "cause": "The TCI package required for implementing note 3200109 is not available in the system.", "processed_at": "2025-06-25T16:20:15.126492"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/642f4d96c33531544daa2875e0013114", "title": "S/4 HANA Readiness check note 2909538 issue", "problem": "Unable to launch Note analyzer option as 'other tools' option is not present in SNOTE.", "solution": "Follow resolution steps from KBA 2971891, selecting 'other tool' instead of 'Download note' in step 6, and proceed with the rest of the steps.", "cause": "", "processed_at": "2025-06-25T16:20:15.126497"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/642f4d96c33531544daa2875e0013114", "title": "S/4 HANA Readiness check note 2909538 issue", "problem": "Deserialization error at tree abap.values.1.7.2.5: Data loss occurred when converting A, M while loading XML file in SE38 via SCWN_NOTE_ANALYZER report.", "solution": "Exit Note analyzer, open transaction SE38 or SA38, and run UDO report NOTE_3007273.", "cause": "Older version of Note Analyzer is being used.", "processed_at": "2025-06-25T16:20:15.126502"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/65cd6222c38539909109d31f05013141", "title": "Error after SAPNOTE Implementation - 3193560", "problem": "Error after implementing SAPNOTE 3193560 related to ST-A/PI component version.", "solution": "Refer to SAP note 3193560 and check 'Reason and Prerequisites' section, specifically note 3072059. Ensure that the version of ST-A/PI meets the minimum requirements.", "cause": "Version of ST-A/PI does not meet the minimum required version.", "processed_at": "2025-06-25T16:20:19.064414"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/662b679bc3a8b150fabf018dc00131be", "title": "RC_COLLECT_ANALYSIS_DATA job issues", "problem": "RC_COLLECT_ANALYSIS_DATA job is stuck and subsequently cancelled.", "solution": "Cancel TMW* jobs as per SAP Note 2913617, then reschedule RC_COLLECT_ANALYSIS_DATA job and wait for completion.", "cause": "Job manually cancelled due to repeated waiting for data collection jobs causing process termination.", "processed_at": "2025-06-25T16:20:23.849786"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/662b679bc3a8b150fabf018dc00131be", "title": "RC_COLLECT_ANALYSIS_DATA job issues", "problem": "SAP Notes cannot be implemented in the system, potentially affecting job execution.", "solution": "Choose options for analysis while scheduling RC_COLLECT_ANALYSIS_DATA without implementing certain SAP Notes, as some are FAQ notes without coding.", "cause": "Certain SAP Notes are FAQ notes or cannot be implemented due to system constraints.", "processed_at": "2025-06-25T16:20:23.849795"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ad98467dc3e039509109d31f0501310b", "title": "RC_COLLECT_ANALYSIS_DATA job issues", "problem": "RC_COLLECT_ANALYSIS_DATA job is getting cancelled repeatedly due to extended waiting for data collection jobs.", "solution": "Cancel other jobs as described in SAP Note 2913617 and reschedule RC_COLLECT_ANALYSIS_DATA from the beginning. Wait for the execution to finish.", "cause": "Manual cancellation of RC_COLLECT_ANALYSIS_DATA job due to prolonged waiting periods for other data collection jobs.", "processed_at": "2025-06-25T16:20:28.535797"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/40f38801c35535d05fb42c65e00131a1", "title": "Error CONVT_NO_NUMBER SAP Readiness Check", "problem": "Error CONVT_NO_NUMBER occurs when executing the RC_COLLECT_ANALYSIS_DATA program in QAS after successful execution in DEV.", "solution": "1. Checked the dump, which occurs in the validation of field 'Tax number 1' in form CHECK_TAXCODE_PE for country Peru. 2. Recommended avoiding country-specific tax number validation for Peru. 3. Advised to use transaction OY17, select country PE, and unmark checkbox 'Other data'. 4. Implemented note 2919704 to prevent job termination due to tax number validation errors.", "cause": "Tax validation not necessary for the job RC_COLLECT_ANALYSIS_DATA causing a dump in validation of field 'Tax number 1' for country Peru.", "processed_at": "2025-06-25T16:20:32.994392"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ec4d06e647a535101cccb7da216d436e", "title": "SAP Readiness Check for SAP Cloud ALM", "problem": "SAP Readiness Check for SAP Cloud ALM fails due to ABAP dump in temporary subroutine pool.", "solution": "1. Verify the RFC connection 'WEBADMIN' is working properly using SAP Solution Manager Configuration application (tx. solman_setup). 2. Implement the latest version of note 3236443 and rerun the readiness check.", "cause": "The job failed due to an outdated version of note 3236443 being implemented and possibly issues with the RFC connection.", "processed_at": "2025-06-25T16:20:37.127061"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/51cfe86fc3e8f1505fb42c65e001313f", "title": "SAP Signavio Process Insights, discovery edition - BJ's Wholesale Club", "problem": "No ppi_petst03n01 data when collecting data via RC_VALUE_DISCOVERY_COLL_DATA for SAP Signavio Process Insights.", "solution": "Update SAP note 2745851 to version 56 and ensure ST-PI/ST-A/PI levels are updated to the latest version. Verify updates using RTCCTOOL.", "cause": "Outdated SAP note and ST-PI/ST-A/PI versions preventing proper data collection.", "processed_at": "2025-06-25T16:20:40.878408"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9cac7ae44749f198cb4df2b5536d43aa", "title": "Data not available in RC Check 2.0", "problem": "Data not available in RC Check 2.0 after job completion", "solution": "Analyze logs and dumps for TMW_RC_BPA_DATA_COLL job execution. Request customer to provide job logs and dumps for further analysis.", "cause": "", "processed_at": "2025-06-25T16:20:44.598046"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9763a2f5c3647994a1b6d2af050131bc", "title": "Getting Not Authorized in SAP Readiness Check", "problem": "User receives 'Not Authorized' message when accessing SAP Readiness Check despite having correct authorizations.", "solution": "User was advised to clear browser cache and cookies, then attempt re-logon using private mode.", "cause": "Potential browser cache issue or session persistence.", "processed_at": "2025-06-25T16:20:49.451866"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/864652f597ec39547bcf59081153af11", "title": "Getting error while executing RC_COLLECT_ANALYSIS_DATA", "problem": "Getting runtime dump error SYNTAX_ERROR in production system when executing RC_COLLECT_ANALYSIS_DATA during S4 pre-conversion step.", "solution": "1. Implement SAP Note 2745851 to version 56. 2. Update ST-PI level from SP0017 to SP0032. 3. Recheck RC_COLLECT_ANALYSIS_DATA execution.", "cause": "Syntax error in program '/SDF/SAPLBPM_7X' due to missing INCLUDE report '/SDF/LBPM_7XU04'.", "processed_at": "2025-06-25T16:20:53.884536"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/018efcad472cfdd01d1ff2b5536d438b", "title": "Getting dump while executing RC_COLLECT_ANALYSIS_DATA", "problem": "Getting dump while executing RC_COLLECT_ANALYSIS_DATA during S4 pre conversion step in production system.", "solution": "Identified as duplicate of case 368889/2023. Case closed by customer.", "cause": "", "processed_at": "2025-06-25T16:20:57.672026"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/166dde3fc3493550c52cb32f05013131", "title": "authorizations to access to SAP Readiness Check not working", "problem": "User cannot access SAP Readiness Check despite having correct authorizations.", "solution": "Check third-party cookies setting in the web browser and disable the option to block third-party cookies.", "cause": "Browser settings blocking third-party cookies.", "processed_at": "2025-06-25T16:21:01.476644"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1e0372d9c334f590fabf018dc0013129", "title": "Getting issue in SV-SMG-DVM application component", "problem": "Syntax errors and missing implementations in transport requests after upgrade.", "solution": "Deimplement SAP note 2972792 as it is not applicable for S/4HANA systems and refer to note 3059197 for the upgrade process.", "cause": "SAP note 2972792 is designed for ERP 6.0 systems, not applicable for S/4HANA.", "processed_at": "2025-06-25T16:21:06.175101"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d06fe10a47b0f5101d1ff2b5536d43e4", "title": "SAP Readinesscheck Upload zip file how to with sap for me", "problem": "Missing 'new analysis' button in SAP Readiness Check.", "solution": "Verify user authorization settings. Ensure roles are assigned according to SAP Note 3310759. Re-logon using private mode to confirm changes.", "cause": "User authorization roles not properly assigned in SAP Support Portal, preventing access to specific functionalities.", "processed_at": "2025-06-25T16:21:09.664130"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e163341fc385b5dcc52cb32f05013129", "title": "Readiness check report submitted in preparation stage for long time", "problem": "Readiness check report stuck in preparation stage in SAP portal.", "solution": "1. Recommended to create a new analysis using the same zip file. 2. Deleted the old analysis and started a new analysis with the existing collected data. 3. Confirmed the new analysis processed successfully.", "cause": "Data missing due to SAP system changes prevented processing.", "processed_at": "2025-06-25T16:21:13.348676"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/17fb3c1cc389f154fabf018dc00131c7", "title": "SAP Readiness check Issues in Q50", "problem": "Financial Data Quality job of S/4 HANA readiness check report running for an extended period in the Q50 system.", "solution": "1. Schedule the Financial Data Quality check independently from other data collectors. 2. Use the Upload function to append results to the analysis session created with the original ZIP archive post completion.", "cause": "The volume of financial data causing prolonged execution compared to other checks.", "processed_at": "2025-06-25T16:21:17.576790"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/17fb3c1cc389f154fabf018dc00131c7", "title": "SAP Readiness check Issues in Q50", "problem": "Excessive runtime of Financial Data Quality check affecting multiple company codes.", "solution": "Switch data collection mode from sequential to parallel using Expert Mode settings.", "cause": "", "processed_at": "2025-06-25T16:21:17.576803"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/be27063a475539d8cb4df2b5536d43f9", "title": "s/4 hana conversion and migration", "problem": "Customer requires information on SAP S/4HANA conversion and migration, including business suite systems needed for readiness checks.", "solution": "1. Provided links to SAP Readiness Check documentation and central notes for SAP S/4HANA Conversion and Upgrades. 2. Suggested consulting SAP Help Portal for additional information. 3. Forwarded the case to the SV-SCS-S4R component for further analysis.", "cause": "Customer is planning SAP S/4HANA migration and needs guidance on prerequisites and associated readiness checks.", "processed_at": "2025-06-25T16:21:22.535927"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/afc60ef2475539d8cb4df2b5536d43ae", "title": "Recommended SAP Fiori Apps error in readiness check 2.0", "problem": "Error message 'Technical Error Occurred' received under 'Recommended SAP Fiori Apps' tile in SAP Readiness Check 2.0.", "solution": "Execute the report RC_COLLECT_ANALYSIS_DATA again after ensuring data is collected in ST03N for the past 3 months by following SAP Note 2568736.", "cause": "Insufficient ST03N data collected in the system.", "processed_at": "2025-06-25T16:21:27.254419"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/afc60ef2475539d8cb4df2b5536d43ae", "title": "Recommended SAP Fiori Apps error in readiness check 2.0", "problem": "Incorrect component selection for issue reporting related to SAP Fiori Apps.", "solution": "Redirect the issue to the correct team and component SV-SCS-S4R as specified in the error message.", "cause": "Component CA-FLP-ABA selected for SAP Fiori Launchpad instead of the specific app-related component.", "processed_at": "2025-06-25T16:21:27.254439"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a4a86227c33479189109d31f0501317d", "title": "The “Planned Downtime Calculator” card does not populate any info", "problem": "The 'Planned Downtime Calculator' card does not populate any information in SAP Readiness Check.", "solution": "The issue was addressed by reviewing the fetched data from the system and confirming that the card shows detailed information for a standard conversion. If no references exist for the same DB type in the reference database, a reference run of a conversion is needed where the downtime data is uploaded to the Technical Downtime Optimization app for future use in other Readiness Checks.", "cause": "Insufficient number of related references for the specific database type in the reference database, preventing detailed downtime calculation.", "processed_at": "2025-06-25T16:21:31.232874"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/03c3cb8347d5b51ccb4df2b5536d431c", "title": "Readiness Check Data Missing", "problem": "Missing data in SAP Readiness Check report for 'Add-On Compatibility' and 'Active Business Functions'.", "solution": "Check and upload missing data in Maintenance Planner as per SAP Note 2847830. Create a new analysis if necessary.", "cause": "Data was missing in Maintenance Planner.", "processed_at": "2025-06-25T16:21:35.531389"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/02fa298647c3bd181cccb7da216d438c", "title": "Financial Data Quality - no background work processes for parallel jobs available", "problem": "Financial Data Quality Check cannot run in parallel due to no background work processes available in customer systems.", "solution": "Implemented SAP note 2972792 to fix the bug in FDQ report collection which prevents the system from calculating background processes correctly.", "cause": "A bug in the FDQ report collection and language-specific differences in the function module TH_WPINFO.", "processed_at": "2025-06-25T16:21:40.011313"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/efccb343c34d359c5fb42c65e00131cd", "title": "Issue SAP Note Readiness Check", "problem": "Unable to implement SAP Note 2758146 due to dependency on obsolete Note 2310438, causing readiness check program RC_COLLECT_ANALYSIS_DATA to dump.", "solution": "Execute steps from KBA 3093810 related to report RC_VALUE_DISCOVERY_COLL_DATA, then attempt running RC_COLLECT_ANALYSIS_DATA again.", "cause": "Note 2310438 is obsolete and cannot be unimplemented, leading to an ABAP dump during readiness check execution.", "processed_at": "2025-06-25T16:21:44.365921"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dc248c56c389f1d85fb42c65e001314b", "title": "RC_COLLECT_ANALYSIS_DATA report failed", "problem": "RC_COLLECT_ANALYSIS_DATA report execution fails due to missing analytics data.", "solution": "Install the latest ST-A/PI version as per SAP-Note 69455, then check if the issue persists.", "cause": "ST-A/PI Release 01S_731 is lower than required 01T, making Business Process Analytics data collection impossible.", "processed_at": "2025-06-25T16:21:48.237034"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/86a2e27e9791355ccdaf79171153af6f", "title": "Financial Data Quality: Trigger Data Collection Reports to check in SAP Readiness check", "problem": "Financial data extraction from Asset Accounting and Material Ledger is incomplete despite completing SAP Readiness Check prerequisites for ECC to S/4HANA conversion.", "solution": "Ensure all necessary SAP Notes, including 2913617 and 2972792, are implemented correctly; select the Financial Data Quality option during the RC_COLLECT_ANALYSIS_DATA run.", "cause": "SAP Note 2913617 was not ready, and the Financial Data Quality check was deselected by default.", "processed_at": "2025-06-25T16:21:52.834152"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/71bce6fd97c13d9c7bcf59081153af9a", "title": "Object is not unique error while implementing SAP Readiness check note", "problem": "Error 'Object is not unique' encountered while implementing SAP Readiness Check Note 2758146.", "solution": "1. De-implement SAP Note 2758146. 2. Re-implement the note and select all warning objects to continue. 3. Ignore the 'not unique' error messages and proceed with implementation. 4. Activate all objects to achieve 'Completely implemented' status.", "cause": "The files ana_sys_add_info.xsl and fui-site.css were released with ST-PI 740 SP21, causing the objects to be marked as not unique.", "processed_at": "2025-06-25T16:21:57.499616"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/88a2715ac3fe7194ca3f1c0bb0013107", "title": "Readiness check page not opening", "problem": "Readiness Check page opening as a blank page in SAP S/4HANA.", "solution": "1. Verify S-user authorization requirements using SAP Note 3310759. 2. Clear browser cache and history. 3. Add me.sap.com to Allow-tab in browser settings and enable third-party cookies. 4. Refer to SAP Note 1511008 for guidance on adding or changing S-user ID authorizations.", "cause": "Authorization issues with S-user preventing access to SAP Readiness Check.", "processed_at": "2025-06-25T16:22:01.740687"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/82e28efec31eb51422195030a001318d", "title": "SAP S/4 Readiness Check - best. Analyse wird vermisst", "problem": "Missing analyses for General Ledger and Material Ledger in the Financial Data Quality section after migration to SAP for Me.", "solution": "1. Confirmed missing analyses for PR1, PR7, and PR4 systems. 2. Verified that new analyses could be generated using provided ZIP files. 3. Requested customer to validate and confirm restored analyses.", "cause": "Transition from Readiness Check to SAP for Me resulted in missing analyses for specific financial data.", "processed_at": "2025-06-25T16:22:07.092559"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/82e28efec31eb51422195030a001318d", "title": "SAP S/4 Readiness Check - best. Analyse wird vermisst", "problem": "Analysis for PR1 system shows no relevant financial data.", "solution": "1. Investigated ZIP file and confirmed absence of relevant financial data. 2. Advised customer on inability to generate financial data analysis due to missing data in provided ZIP file.", "cause": "ZIP file used for analysis did not contain the financial data required for the analysis.", "processed_at": "2025-06-25T16:22:07.092573"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6608a1dac386f910b9946ecd2b0131db", "title": "RC_COLLECT_ANALYSIS_DATA", "problem": "The customer wants to automate the deletion of buffered data in the program RC_COLLECT_ANALYSIS_DATA before running the weekly ReadinessCheck for S/4HANA conversion.", "solution": "SAP Support confirmed there is no standard way to automate the deletion of buffered data. The customer needs to manually delete the buffer each time before the job runs.", "cause": "SAP does not provide a standard automated option to delete buffered data in the RC_COLLECT_ANALYSIS_DATA program.", "processed_at": "2025-06-25T16:22:10.997907"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2dd43a6783f6bd580f3ba780ceaad388", "title": "Error in S/4 readiness analysis in portal", "problem": "Error in S/4 readiness analysis portal affecting add-on compatibility and active business function tiles.", "solution": "1. Recheck the readiness check portal and ensure that all data in the maintenance planner is updated. 2. Perform a refresh of the RC to resolve potential replication issues.", "cause": "Possible replication problem leading to errors in specific tiles.", "processed_at": "2025-06-25T16:22:14.448001"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d71ee81ac336f99488b12875e001314c", "title": "Readiness report", "problem": "The atc_setting or UDP file is not valid when uploading ATC zip file to the readiness portal.", "solution": "The issue was forwarded to DEV colleagues for further assistance and was fixed, allowing the customer to upload the ATC zip file again.", "cause": "", "processed_at": "2025-06-25T16:22:18.106980"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2eb24367c37e795830037d9f05013196", "title": "Custom Code Analysis not reflecting in SAP report due to technical error.", "problem": "Custom Code Analysis data missing in SAP Readiness Check report.", "solution": "Request customer to provide the Custom Code Analysis ZIP file for verification. Ensure ZIP file is correctly generated and uploaded to SAP Backend.", "cause": "Custom Code Analysis data collection file was not provided or not correctly processed.", "processed_at": "2025-06-25T16:22:22.420495"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bce1b2c4c3c7319822195030a00131df", "title": "Unable to reset the SNOTE Implementation ( 2758146 )", "problem": "Unable to reset and fully implement SAP Note 2758146 due to syntax errors and duplicate code entries.", "solution": "De-implement and reimplement SAP Note 2758146 following the recommendations in SAP Note 3221283. Ensure objects are deleted during de-implementation and manually activate yellow warnings in SNOTE to override objects to the latest version.", "cause": "Inconsistencies with objects during SAP Note 2758146 implementation, leading to syntax errors such as 'object already declared'.", "processed_at": "2025-06-25T16:22:28.401733"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bce1b2c4c3c7319822195030a00131df", "title": "Unable to reset the SNOTE Implementation ( 2758146 )", "problem": "SAP Note 1668882 implementation required to avoid issues with SNOTE transactions.", "solution": "Implement SAP Note 1668882 as it contains the latest corrections to backend programs and modules critical for SNOTE operations.", "cause": "SAP Note 1668882 was not implemented, leading to potential issues during note implementations and de-implementations.", "processed_at": "2025-06-25T16:22:28.401757"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c0beaaeec3feb1d430037d9f0501317a", "title": "SAP HANA Readiness Check", "problem": "Error 'Prerequisite up-to-date SAP Note 2769657 for interface is not installed' during SAP S4 HANA readiness check.", "solution": "Verify ST-A/PI version compatibility for SAP Note 2769657. If incompatible, check SAP Note 2968380 for alternative resolution steps. De-implement and re-implement SAP Note 2758146 completely, ensuring all required checkboxes are selected during re-implementation.", "cause": "The SAP Note 2769657 is not applicable due to an invalid ST-A/PI version level in the system.", "processed_at": "2025-06-25T16:22:35.056131"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c0beaaeec3feb1d430037d9f0501317a", "title": "SAP HANA Readiness Check", "problem": "ABAP dump 'Type CL_SCWN_UTILITY is unknown' when transporting TRs to Quality system.", "solution": "Identify and include missing object 'CL_SCWN_UTILITY' in transport request (TR) and re-transport to Quality system.", "cause": "Missing class 'CL_SCWN_UTILITY' in transported TR.", "processed_at": "2025-06-25T16:22:35.056140"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c0beaaeec3feb1d430037d9f0501317a", "title": "SAP HANA Readiness Check", "problem": "Syntax error 'C_FILE_NAME does not have a component called FDQ' in report RC_COLLECT_ANALYSIS_DATA.", "solution": "De-implement and re-implement SAP Note 2758146 to resolve inconsistencies. Apply solutions from KB 3221283 to address syntax errors, focusing on solutions 4, 5, and 6.", "cause": "Inconsistent objects in SAP Note 2758146 implementation.", "processed_at": "2025-06-25T16:22:35.056142"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d43d6a75c33231548710df00a00131a3", "title": "Readiness Check FI", "problem": "Need clarification on implementing SAP notes for readiness check financials and their impact on production system.", "solution": "Read and implement SAP Note 0002972792, ensuring manual activities and prerequisites are completed first. Check for other necessary notes.", "cause": "Uncertainty regarding the necessity and impact of implementing multiple SAP notes.", "processed_at": "2025-06-25T16:22:38.716146"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4e12ea4583ea7d140f3ba780ceaad397", "title": "Issues with importing the Simplification Database", "problem": "Unable to upload the Simplification Database ZIP file during S4 upgrade preparation.", "solution": "Follow the guidance in SAP KBA 2968380 - SAP Readiness Check Report 2.0 troubleshooting guide.", "cause": "Simplification item catalog not found; potential issue with SAP-SUPPORT_PORTAL connection or manual upload version.", "processed_at": "2025-06-25T16:22:42.782956"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/18835cf8c3667d90cebe6610a0013161", "title": "SAP Readiness Check note 3193560 no program RC_HCM_COLLECT_ANALYSIS_DATA", "problem": "SAP Readiness Check note 3193560 cannot find program RC_HCM_COLLECT_ANALYSIS_DATA.", "solution": "Create package PAOC_SFR, then use SNOTE to install correction instructions from the note, which creates the program RC_HCM_COLLECT_ANALYSIS_DATA in the system.", "cause": "Program RC_HCM_COLLECT_ANALYSIS_DATA was not initially present in the system after applying the note.", "processed_at": "2025-06-25T16:22:46.604541"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0c28846cc30f35d88710df00a001310d", "title": "ST03 not active", "problem": "ST03 workload monitor data missing in BSR report.", "solution": "Ensure the SAP_COLLECTOR_FOR_PERFMONITOR job is scheduled for a minimum of one month to collect data. Adjust the data collection period in ST03 settings to at least one month.", "cause": "The SAP_COLLECTOR_FOR_PERFMONITOR job was only scheduled for 5 days, which is insufficient for monthly data collection.", "processed_at": "2025-06-25T16:22:50.499407"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0f66dbdec3ae3d9488b12875e00131b1", "title": "Error SAP Rediness Check - Addon item", "problem": "The add-on scan could not be performed during the SAP Readiness Check due to a technical error.", "solution": "1. Ask the customer to provide documentation and screenshots of the error message. 2. Request the Readiness Check data collection ZIP file for review. 3. Transfer the incident to the correct SV-SCS-S4R component area for further processing.", "cause": "Incorrect component was initially selected by the customer, leading to misrouting of the support ticket.", "processed_at": "2025-06-25T16:22:54.128688"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d5257532c396b51422195030a0013115", "title": "Unable to upload ATC results to SAP Readiness dashbaord", "problem": "Unable to upload ATC results to SAP Readiness dashboard due to invalid atc_setting or UDP file.", "solution": "1. SAP identified a bug related to the upload process. 2. Development team worked on fixing the bug. 3. Customer informed to retry upload after fix was implemented.", "cause": "Bug in SAP system causing atc_setting or UDP file to be considered invalid.", "processed_at": "2025-06-25T16:22:59.094046"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/21efe333c3988ed43331583bb001313c", "title": "Missing custom code adjustments in Readiness report", "problem": "Missing custom code adjustments in SAP Readiness report for system conversion to SAP S/4HANA.", "solution": "Verify that all SAP Readiness pre-requisite notes have been applied correctly. Ensure that custom code analysis results are uploaded properly to the system.", "cause": "No custom code analysis results were uploaded, preventing the necessary data from appearing in the report.", "processed_at": "2025-06-25T16:23:03.009610"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/20cef5d0c303795c553db11f05013161", "title": "RC_BW_COLLECT_ANALYSIS_DATA not collected data for custom code", "problem": "RC_BW_COLLECT_ANALYSIS_DATA report did not collect data for custom code during BW4HANA readiness check.", "solution": "1. Verify system version compatibility with SAP_BW 750 Patch 19 and ST_PI 740 Patch 12. 2. Ensure valid user credentials to access system HBT for further investigation. 3. Check job RS_B4HANA_CODE_SCAN to confirm successful completion and investigate missing ca_***.xml files.", "cause": "Potential incompatibility with system versions or incorrect execution environment causing data collection failure.", "processed_at": "2025-06-25T16:23:08.579090"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/793dabf1c3df3d90ca3f1c0bb0013165", "title": "S/4 Hana upgrade from S/4Hana 1709 to 2022 FP1", "problem": "Confirmation of requirement for ATC report during S/4 Hana upgrade from 1709 to 2022 FP1.", "solution": "Refer to SAP Note 3059197 for guidance. Perform Custom Code Analysis and export results using ABAP Test Cockpit for SAP Readiness Check.", "cause": "Upgrade guide recommends uploading ATC report in Readiness Check, causing confusion about necessity for upgrades versus conversions.", "processed_at": "2025-06-25T16:23:12.521087"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/731f3468c38779d830037d9f0501311a", "title": "SAP Readiness Check problem", "problem": "Error 'Project EXC does not exist in system' during execution of RC_COLLECT_ANALYSIS_DATA.", "solution": "1. Implement SAP Notes 2758146, 2399707, and 2745851. 2. Verify if ST-A/PI version is updated to meet prerequisites. 3. If issue persists, escalate to SV-SMG-MON-BPM-ANA team for further investigation.", "cause": "Improper user handling and outdated ST-A/PI version not meeting prerequisites.", "processed_at": "2025-06-25T16:23:18.729572"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/731f3468c38779d830037d9f0501311a", "title": "SAP Readiness Check problem", "problem": "Error 'KWPM000290: Object does not exist' when collecting Business Process Improvement analysis data.", "solution": "Update ST-A/PI to the latest release as per SAP Note 69455 to meet prerequisites for Business Process Improvement analysis data.", "cause": "ST-A/PI version in system was outdated, not meeting required prerequisites.", "processed_at": "2025-06-25T16:23:18.729580"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/731f3468c38779d830037d9f0501311a", "title": "SAP Readiness Check problem", "problem": "Password incorrect error when attempting to connect to system.", "solution": "Update the system credentials to correct values and verify with SAP support team.", "cause": "", "processed_at": "2025-06-25T16:23:18.729581"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/960d6fa5c3be7d14ecd47d9f05013162", "title": "SAP Readiness Check for SAP S/4HANA upgrade from 1809 to Latest", "problem": "Need to perform SAP Readiness Check for S/4HANA upgrade from version 1809 to the latest version.", "solution": "1. Implement required notes as per SAP note 3059197. 2. Run the Readiness Check analysis using the RC_COLLECT_ANALYSIS_DATA program on the target system.", "cause": "Additional information regarding SAP S/4HANA system upgrade required.", "processed_at": "2025-06-25T16:23:22.642798"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0194fa6383f6bd580f3ba780ceaad3a2", "title": "Need to activate SAP Backbone and HANA Readiness check", "problem": "Concern about interference of SAP Backbone and HANA Readiness check with ongoing functional developments.", "solution": "SAP support confirmed that implementation of SAP Readiness Check related SAP Notes is not expected to interfere with running functional developments.", "cause": "Implementation of SAP Backbone and HANA Readiness check typically involves downloading digitally signed notes which do not affect functionalities outside of SNOTE.", "processed_at": "2025-06-25T16:23:26.406239"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/97780b5fc34f39148710df00a00131d8", "title": "SAP Readiness Check SAP-SuccessFactors", "problem": "Execution of SAP Readiness Check for SuccessFactors results in a dump error in the report rc_hcm_collect_analysis.", "solution": "Correct infotype 9104 and any other custom infotypes following provided guidelines.", "cause": "ABAP runtime error SAPSQL_PARSE_ERROR due to incorrect custom infotype table requirements.", "processed_at": "2025-06-25T16:23:31.119691"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/97780b5fc34f39148710df00a00131d8", "title": "SAP Readiness Check SAP-SuccessFactors", "problem": "Custom infotype 9100 table PA9100 does not meet required infotype table standards.", "solution": "Review and correct table PA9100 to follow proper infotype table requirements, or delete unnecessary custom infotypes using transaction PM01.", "cause": "Creation of custom infotype table PA9100 without adhering to required standards, causing runtime errors.", "processed_at": "2025-06-25T16:23:31.119706"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f775b5f2c396b51422195030a0013192", "title": "readiness check report not showing in SAP ME", "problem": "Readiness check report not showing in SAP ME after uploading a zip file.", "solution": "Customer resolved the issue by using Firefox browser to upload the zip file in SAP ME readiness-check portal.", "cause": "Browser compatibility issue with the portal.", "processed_at": "2025-06-25T16:23:34.688253"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e1b0416c3def5d0b118f71ad001312c", "title": "S4HANA READINESS CHECK REPORT NOT RUNNING", "problem": "S4HANA Readiness Check report not running due to missing prerequisite for Integration - OData Services.", "solution": "Advise customer to uncheck the OData service option in the RC_COLLECT_ANALYSIS_DATA report as a workaround. Recommend upgrading NetWeaver component and installing SAP_GWFND for full functionality.", "cause": "SAP_GWFND component is not installed on SAP NetWeaver 731, which is required for OData Services collection.", "processed_at": "2025-06-25T16:23:39.432132"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2234b33bc34e7990b118f71ad0013166", "title": "Readiness Checks report not able to see", "problem": "Unable to view Readiness Check report in the Recent section after generating it using the Readiness Check zip file.", "solution": "Attempt to upload the analysis again and verify for any error messages. Ensure formal authorization to access the analysis and provide the ZIP file of the analysis.", "cause": "", "processed_at": "2025-06-25T16:23:43.887015"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9771c080c3de7994cebe6610a001310d", "title": "SAP REadiness check issue", "problem": "Readiness check report is not visible despite successful creation message.", "solution": "1. Request customer to retry creating the analysis from their side. 2. Manually create the analysis using the ZIP file provided by the customer. 3. Investigate the issue with development team. 4. Update customer once development team provides further information.", "cause": "The readiness check analysis was not properly created or displayed in the system, possibly due to an issue on SAP's site.", "processed_at": "2025-06-25T16:23:48.154931"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/561e98a2c35a71148710df00a001311a", "title": "Unable to create Readiness analysis", "problem": "Unable to create Readiness analysis after uploading data via RC_COLLECT_ANALYSIS_DATA report.", "solution": "Request customer to retry creating the Analysis from their side. Attempted to create Analysis using provided ZIP without issues. Customer confirmed that it is now working after retry.", "cause": "Initial technical glitch or temporary issue affecting the backend process.", "processed_at": "2025-06-25T16:23:51.716208"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/20075a9ec31b711430037d9f0501314e", "title": "Readiness Check does not show target S4 HANA version", "problem": "Readiness Check does not show the target S/4 HANA version in the dropdown list.", "solution": "1. Manually download the simplification item catalog archive from the specified site. 2. Start report /SDF/RC_START_CHECK in transaction SA38. 3. Manually upload the simplification item catalog from the file to resolve the dropdown issue. Refer to SAP KBA 2968380 for detailed steps.", "cause": "The dropdown list issue was a known problem, possibly due to outdated or missing catalog data. Manual upload of the latest catalog resolves the issue.", "processed_at": "2025-06-25T16:23:55.849370"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b167f726c3e679d430037d9f050131f8", "title": "Add-On Compatibility empty in Readiness Check", "problem": "Add-On Compatibility and Business Functions tiles are empty in SAP Readiness Check.", "solution": "1. Verify that the system SID matches in the Maintenance Planner. 2. Ensure no errors are present in the System Verification status. 3. Follow SAP Note 2847830 to ensure Verification Status is OK.", "cause": "Verification is showing errors in Maintenance Planner.", "processed_at": "2025-06-25T16:23:59.773774"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b17cec31c3d77990ca3f1c0bb0013133", "title": "In SAP Readiness check Dashboard do not have option to Start New Analysis", "problem": "SAP Readiness Check Dashboard does not have option to Start New Analysis.", "solution": "Customer was trying to use a Process Discovery (BSR) data collection triggered via report RC_VALUE_DISCOVERY_COLL_DATA, which is not applicable for Readiness Check. Customer was advised to use the related web portal to upload the file if intending to receive a BSR report.", "cause": "Misuse of Process Discovery data collection instead of the correct Readiness Check procedure.", "processed_at": "2025-06-25T16:24:05.701212"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b17cec31c3d77990ca3f1c0bb0013133", "title": "In SAP Readiness check Dashboard do not have option to Start New Analysis", "problem": "Unable to implement SAP Notes for readiness check.", "solution": "Customer was referred to SAP Note 0003355872 for resolving runtime error GEN_TOO_MANY_CBS when activating /SSA/EKP. After following the solution approach, they were advised to re-attempt scheduling a data collection via report RC_COLLECT_ANALYSIS_DATA.", "cause": "Runtime error GEN_TOO_MANY_CBS due to SAP Note implementation issue.", "processed_at": "2025-06-25T16:24:05.701229"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b17cec31c3d77990ca3f1c0bb0013133", "title": "In SAP Readiness check Dashboard do not have option to Start New Analysis", "problem": "Incorrect procedure for Custom Code Analysis data collection.", "solution": "Customer was advised to either use report program SYCM_DOWNLOAD_REPOSITORY_INFO if SAP_BASIS release exceeds SAP Notes validity or use ATC functionality if ABAP system is based on SAP_BASIS 7.52 or higher. Queries specific to ATC should be addressed to component BC-DWB-CEX in a separate case.", "cause": "Incorrect understanding of the procedure required for Custom Code Analysis data collection.", "processed_at": "2025-06-25T16:24:05.701232"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/353323633b8d4ed00b700f3a85e45aa3", "title": "Process Discovery-TTCODE.XML file empty", "problem": "The ttcode.xml file is empty, preventing Process Discovery data collection.", "solution": "Verify ST03N data availability for the last three months. Ensure data collection is performed on the production system, not a system copy. Reference SAP Note 0002568736 for potential data transfer solutions. Review troubleshooting guide in SAP Note 0002977422.", "cause": "ST03N data is outdated, lacking recent entries necessary for Process Discovery collection.", "processed_at": "2025-06-25T16:24:12.283413"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bd8a33d0c3877d5c553db11f05013152", "title": "Berechtig SAP Readiness Check", "problem": "User cannot access SAP Readiness Check despite having permission assigned.", "solution": "1. Verify the permissions according to SAP Note 3310759. 2. Provide explicit permission assignment again. 3. Refer to SAP KBA 3318975 for known issues and a workaround: clear browser cache, enable cookies for 'me.sap.com', and allow third-party cookies.", "cause": "Permissions did not migrate correctly during SAP Readiness Check update, and restrictive browser settings on the company computer.", "processed_at": "2025-06-25T16:24:20.485113"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f70cfebac3d3f15430037d9f05013104", "title": "Readiness check for S/4 HANA", "problem": "Request for S/4 HANA Readiness check.", "solution": "Conduct S/4 HANA Readiness check using SAP Readiness Check Report 2.0 and Simplification Item Check. Follow troubleshooting guides 2968380, 2399707, and 3059197 for detailed steps.", "cause": "", "processed_at": "2025-06-25T16:24:25.609508"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c3e7a7c783deb5100f3ba780ceaad32c", "title": "Unable to access redinesscheck for *S-USER*", "problem": "Unable to access SAP Readiness Check despite having required role assignments for S-USER.", "solution": "Resolved via access request and approval process.", "cause": "", "processed_at": "2025-06-25T16:24:30.968815"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0b4c3a88c397795088b12875e00131e7", "title": "Dependency Notes Incomplete when implementing SAP Notes 2758146 SAP Readiness Check for SAP S/4HANA & Process Discovery", "problem": "SAP Note 2758146 implementation incomplete due to dependency notes (3436688, 2437332, 3046934) not available.", "solution": "Confirmed availability status: 3436688 does not exist, 2437332 and 3046934 released for customers.", "cause": "Dependency notes required for complete implementation were not initially released.", "processed_at": "2025-06-25T16:24:37.058395"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0b4c3a88c397795088b12875e00131e7", "title": "Dependency Notes Incomplete when implementing SAP Notes 2758146 SAP Readiness Check for SAP S/4HANA & Process Discovery", "problem": "Error 'Name <MIME objects> is not unique' during SAP Note 2758146 implementation.", "solution": "Follow steps in KBA 3308795 to address unique name error by ensuring documents do not exist with different keys.", "cause": "Error due to existing documents with non-unique keys.", "processed_at": "2025-06-25T16:24:37.058456"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0b4c3a88c397795088b12875e00131e7", "title": "Dependency Notes Incomplete when implementing SAP Notes 2758146 SAP Readiness Check for SAP S/4HANA & Process Discovery", "problem": "SAP Note 2758146 implementation error 'At least one note was not implemented completely.'", "solution": "Ensure implementation of latest version of prerequisite notes, complete pre- and post-manual activities, avoid implementation with warning/error signs.", "cause": "Incomplete implementation due to not following prerequisite and manual activity instructions.", "processed_at": "2025-06-25T16:24:37.058459"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/6c21f733c3f6b91c553db11f0501319d", "title": "Pathfinder 2758146", "problem": "Duplicate object inconsistency (SMIM) encountered during implementation of SAP Note 2758146.", "solution": "1. Implement the latest version of relevant SAP Notes, specifically 1668882 and 2971435. 2. Revert SAP Note 2310438 if previously implemented, before applying SAP Note 2758146. 3. Locate object GUID using SE80 -> MIME Repository -> SAP/PUBLIC/BC/ABAP/<file_name> -> Right click -> Object Directory Entry -> Object GUID. 4. Manually delete existing MIME Objects using report MR_DELETE_MIME. 5. Re-implement SAP Note 2758146.", "cause": "Duplicate object inconsistency in MIME objects during note implementation process.", "processed_at": "2025-06-25T16:24:41.985765"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/317320013b6146d0bf2a703a85e45ae6", "title": "No Data Available for Custom Code Compatibility", "problem": "No Data Available for Custom Code Compatibility during SAP Readiness Check for SAP BW/4HANA Conversion.", "solution": "1) Verified the execution of <PERSON>_BW_COLLECT_ANALYSIS_DATA program. 2) Checked the RSB4HANADETAILS table which contained entries. 3) Confirmed the execution status of Collector NEW_CODE_ANALYSIS. 4) Customer closed the case as resolved.", "cause": "The analysis returned an error indicating the Results Database Table 'RSB4HANADETAILS' was empty despite containing entries, possibly due to incorrect data collection or processing.", "processed_at": "2025-06-25T16:24:48.802353"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2b65d66dc39bb590553db11f05013163", "title": "SAP Readiness Check Financial Data Quality issues", "problem": "Financial Data Quality check in SAP Readiness Check runs for an extended period and blocks further analysis due to the volume of financial data.", "solution": "Schedule the Financial Data Quality check separately from other data collectors. Once complete, append results using the Upload function to the analysis session created with the original ZIP archive as per note 2913617.", "cause": "The extended runtime is expected due to the amount of financial data in the system, as noted in SAP documentation.", "processed_at": "2025-06-25T16:24:54.458071"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2b65d66dc39bb590553db11f05013163", "title": "SAP Readiness Check Financial Data Quality issues", "problem": "The job RC_COLLECT_ANALYSIS_DATA was canceled due to the long runtime of FIN* jobs.", "solution": "Run RC_COLLECT_ANALYSIS_DATA without the Financial portion and schedule the Financial portion separately. Use the Upload function to manually include the financial results into the analysis data result after completion.", "cause": "FIN* jobs have a long processing time due to the volume of financial data, causing delays in the overall SAP Readiness Check.", "processed_at": "2025-06-25T16:24:54.458085"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/69b92fe6c32ef5d488b12875e0013104", "title": "ATC findings upload not working", "problem": "ATC findings upload not working; system reports no custom code analysis despite successful local checks.", "solution": "Confirmed with colleagues that message 'The check reports that no custom code analysis is implemented in the system' is correct behavior when ATC results have 0 findings.", "cause": "ATC findings file contained no results because all objects passed checks successfully; system behavior is correct for 0 findings.", "processed_at": "2025-06-25T16:25:00.592891"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8de6f63847008a581cccb7da216d438b", "title": "RC_COLLECT_ANALYSIS_DATA is running over days", "problem": "RC_COLLECT_ANALYSIS_DATA job is running for an extended period and hangs during data collection.", "solution": "1. Implement SAP Notes 2758146 and 2745851 in their latest versions. 2. Cancel the active job via SM37 and reschedule the Readiness Check data collection with corrected notes. 3. Ensure the user executing the job has the necessary authorizations.", "cause": "The job hangs due to missing implementations of SAP Notes and insufficient user authorizations.", "processed_at": "2025-06-25T16:25:09.345966"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8de6f63847008a581cccb7da216d438b", "title": "RC_COLLECT_ANALYSIS_DATA is running over days", "problem": "Implementation of SAP Note 2745851 fails with error 'Klassenschnittstelle /SDF/IF_S4RC20 not found'.", "solution": "1. Check the SAP Note 3205320 for corrections. 2. Engage colleagues from component SV-SMG-MON-BPM-ANA for assistance. 3. Ensure system D16 remains accessible for troubleshooting.", "cause": "The error arises from missing or incorrect implementation of SAP Note 2745851.", "processed_at": "2025-06-25T16:25:09.345989"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8de6f63847008a581cccb7da216d438b", "title": "RC_COLLECT_ANALYSIS_DATA is running over days", "problem": "Job RC_COLLECT_ANALYSIS_DATA dumps on system D16.", "solution": "1. De-implement and re-implement SAP Notes 2758146 and 2745851 in correct order. 2. Verify SAP Note 2972792 is not necessary unless FDQ option is used.", "cause": "Incomplete implementation of required SAP Notes leading to system dump.", "processed_at": "2025-06-25T16:25:09.345994"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/16b74f3fc3f2fd58ecd47d9f050131bc", "title": "Unable to run SYCM_UPLOAD_SIMPLIFIC_INFO for Custom code analyser zip file", "problem": "Unable to run SYCM_UPLOAD_SIMPLIFIC_INFO for Custom code analyser zip file on SAP NetWeaver 7.0.", "solution": "Inform customer that SYCM_UPLOAD_SIMPLIFIC_INFO and SYCM_UPLOAD_REPOSITORY_INFO are not available on SAP NetWeaver 7.0 and recommend checking SAP Notes 2265883 and 2228777 for systems with SAP_BASIS 750 or higher.", "cause": "Programs SYCM_UPLOAD_SIMPLIFIC_INFO and SYCM_UPLOAD_REPOSITORY_INFO require SAP_BASIS 7.50, not available on SAP NetWeaver 7.0.", "processed_at": "2025-06-25T16:25:17.742159"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0480a26ec37ab1d4b118f71ad0013136", "title": "Readiness Checks (RC_COLLECT_ANALYSIS_DATA) Throwing ABAP Dump (SMDB_CONTNET_NOT_FOUND)", "problem": "ABAP dump error 'SMDB_CONTNET_NOT_FOUND' when executing Readiness Check report RC_COLLECT_ANALYSIS_DATA during S/4HANA upgrade.", "solution": "Follow step #7 from SAP KBA 2968380 - SAP Readiness Check Report 2.0 - troubleshooting guide.", "cause": "Known issue related to Readiness Check Report in SAP environment.", "processed_at": "2025-06-25T16:25:24.355183"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0480a26ec37ab1d4b118f71ad0013136", "title": "Readiness Checks (RC_COLLECT_ANALYSIS_DATA) Throwing ABAP Dump (SMDB_CONTNET_NOT_FOUND)", "problem": "Incorrect component area reported in the support ticket for the ABAP dump error.", "solution": "Route the incident to the correct application component team, SV-SMG-CM, for further handling.", "cause": "The runtime error belongs to the application component SV-SMG-CM, not the initially reported component.", "processed_at": "2025-06-25T16:25:24.355206"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c573a77fc33e395822195030a0013182", "title": "Having error when downloading \"Generate Extract for SAP Cloud ALM report\"", "problem": "Error occurred while generating Excel file for 'Generate Extract for SAP Cloud ALM'.", "solution": "Recommended to create a support incident under the component SV-SCS-S4R.", "cause": "An exception occurred during Excel file generation.", "processed_at": "2025-06-25T16:25:33.480281"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c573a77fc33e395822195030a0013182", "title": "Having error when downloading \"Generate Extract for SAP Cloud ALM report\"", "problem": "Error when uploading ATC findings for SAP Readiness Check: 'The atc_setting or UDP file is not valid'.", "solution": "Checked SAP Note 2758146 for resolution, fix went live, and should now enable successful upload.", "cause": "Invalid ATC setting or UDP file.", "processed_at": "2025-06-25T16:25:33.480304"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c573a77fc33e395822195030a0013182", "title": "Having error when downloading \"Generate Extract for SAP Cloud ALM report\"", "problem": "Unexpected error occurred while accessing 'Maintenance Planner' through me.sap.com.", "solution": "Advised to create a new SAP support ticket with detailed information.", "cause": "", "processed_at": "2025-06-25T16:25:33.480308"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/34fe6a40c357fd54553db11f0501312a", "title": "SMP Readiness check for CALM job failing", "problem": "SMP readiness check for CALM job failing due to API call failure.", "solution": "Implemented the latest version 20 of SAP Note 3236443 to fix the issue and retry the report.", "cause": "Call to ALM data collection API failed, causing job cancellation due to system exception.", "processed_at": "2025-06-25T16:25:37.774944"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/34fe6a40c357fd54553db11f0501312a", "title": "SMP Readiness check for CALM job failing", "problem": "Page loading issues after report upload and analysis availability.", "solution": "Investigated page loading times and ensured successful job completion post-note implementation.", "cause": "", "processed_at": "2025-06-25T16:25:37.774967"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e4fd876283e6bd180f3ba780ceaad38f", "title": "RC_ALM_ANALYSIS_DATA_COLLECTION - Job Failed", "problem": "The scheduled job RC_ALM_ANALYSIS_DATA_COLLECTION failed with the error message 'RC call ALM data collection API failed'.", "solution": "Ask the customer to de-implement SAP Note 3236443 'SAP Readiness Check for SAP Cloud ALM' completely and re-implement it in its latest version (20), then re-check the situation.", "cause": "The failure was due to issues related to SAP Note implementation for SAP Cloud ALM readiness check.", "processed_at": "2025-06-25T16:25:43.331219"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/05c24767c37e795830037d9f0501318b", "title": "Error While Executing RC_COLLECT_ANALYSIS_DATA", "problem": "Error while executing report RC_COLLECT_ANALYSIS_DATA resulting in ABAP dump CX_SY_DYN_CALL_ILLEGAL_FUNC.", "solution": "Start transaction SE37, select Menu 'Utilities' and choose 'Repair Function Group'. Enter function group '/SDF/BPM_7X' in the form field and execute. Re-attempt executing report RC_VALUE_DISCOVERY_COLL_DATA to check resolution.", "cause": "Function module '/SDF/BPM_7X_S4RC20_GET_KPIS' was not activated in its function group.", "processed_at": "2025-06-25T16:25:50.660209"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8f42cb23c37e795830037d9f050131f5", "title": "No TTcode data when collecting data via RC_VALUE_DISCOVERY_COLL_DATA", "problem": "No TTcode data available when collecting data via RC_VALUE_DISCOVERY_COLL_DATA.", "solution": "The issue needs to be addressed by configuring the ST03 workload monitoring settings. A support incident is created to route the problem to the appropriate support component.", "cause": "ST03 is not collecting any workload data.", "processed_at": "2025-06-25T16:25:54.095293"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/96e85457c37a791830037d9f050131a8", "title": "RC_COLLECT_ANALYSIS_DATA runs far too long in production environment", "problem": "RC_COLLECT_ANALYSIS_DATA program runs for an excessive duration jeopardizing production operations.", "solution": "Apply SAP Notes 2869147 and 2721530 for performance improvement. Increase background work processes and configure parallel job execution. Run Readiness Check without FDQ initially, then append FDQ data subsequently.", "cause": "High volume of tasks causing timeouts and dependencies in job execution.", "processed_at": "2025-06-25T16:25:59.694181"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/96e85457c37a791830037d9f050131a8", "title": "RC_COLLECT_ANALYSIS_DATA runs far too long in production environment", "problem": "Logon failure for user in client during execution of child task /SDF/HDB_SIZING_SM, leading to task cancellation.", "solution": "Apply SAP Note 1853143 to resolve logon issue and ensure user has necessary authorizations.", "cause": "Authorization issues preventing successful logon for user in client.", "processed_at": "2025-06-25T16:25:59.694203"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8e6f8941473f3d501cccb7da216d43eb", "title": "Readiness Report on Integration issue", "problem": "Integration issue with SAP Readiness Check where sizing report is executed in client 000 but Readiness Check looks for it in client 001.", "solution": "Implement SAP Note 3379231 to correct the client lookup issue and ensure the sizing report runs in the correct client. Re-run the sizing report after implementation.", "cause": "The readiness check was configured to look for results in a different client than where the sizing report was executed.", "processed_at": "2025-06-25T16:26:07.556597"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8e6f8941473f3d501cccb7da216d43eb", "title": "Readiness Report on Integration issue", "problem": "Empty hana_sizing.xml file resulting in zero values and inability to find check results on the portal site.", "solution": "Confirm implementation of SAP Note 3338309 and ensure data collection jobs RC_COLLECT_ANALYSIS_DATA and TMW_RC_HANAS_DATA_COLL are executed correctly.", "cause": "Potential misconfiguration or incomplete execution of data collection jobs leading to empty sizing file.", "processed_at": "2025-06-25T16:26:07.556622"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8e6f8941473f3d501cccb7da216d43eb", "title": "Readiness Report on Integration issue", "problem": "Error during processing of table SXMSCLUP in sizing job due to TIME_OUT runtime error.", "solution": "Check ST22 for details on the TIME_OUT error and optimize the resource allocation for the sizing job to prevent timeout.", "cause": "ABAP programming error causing a TIME_OUT during the processing of a specific table.", "processed_at": "2025-06-25T16:26:07.556628"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8e6f8941473f3d501cccb7da216d43eb", "title": "Readiness Report on Integration issue", "problem": "Authorization issue preventing execution of transactions SE38, SM37, SE16.", "solution": "Maintain authorization profiles to grant necessary permissions for executing required transactions.", "cause": "Missing authorization profiles for necessary transactions in the system.", "processed_at": "2025-06-25T16:26:07.556633"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/301e7f77c3beb91c553db11f05013156", "title": "Error message when uploading ATC_findings to readiness check for ATC analysis", "problem": "Error message when uploading ATC_findings zip file to SAP Readiness Check indicating that atc_setting or UDP file is not valid.", "solution": "Development team is working on a fix. A KBA 3361913 will be updated with the solution or workaround.", "cause": "Invalid atc_setting or UDP file configuration.", "processed_at": "2025-06-25T16:26:13.130659"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a727e9be8396f1140f3ba780ceaad3a4", "title": "Error while implementing note 2758146", "problem": "Error while implementing SAP Note 2758146 due to non-unique MIME object names.", "solution": "Follow the resolution steps provided in SAP Note 3308795 to address the error message stating 'Name <MIME objects> is not unique'.", "cause": "The error is caused by documents already existing with different keys.", "processed_at": "2025-06-25T16:26:17.843526"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/526e7326c307fd10b118f71ad0013135", "title": "S4HANA readiness check", "problem": "Customer needs guidance on important SAP notes to apply for the S4HANA readiness check.", "solution": "Refer to note 2913617 which contains a table with SAP Notes guiding the implementation of each check type for SAP Readiness Check for SAP S/4HANA. Implement checks in the development system first and transport changes to production. Ensure the recommended minimum version of relevant SAP Notes is implemented before scheduling data collectors.", "cause": "Lack of clarity on which notes are crucial for S4HANA readiness check.", "processed_at": "2025-06-25T16:26:23.652654"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/526e7326c307fd10b118f71ad0013135", "title": "S4HANA readiness check", "problem": "Customer is facing issues implementing SAP notes for readiness check.", "solution": "Investigate the cause of the implementation issues and reference the 'Associated Component' column for the recommended application component when creating an SAP incident.", "cause": "", "processed_at": "2025-06-25T16:26:23.652678"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a76d83cbc3d6f110ecd47d9f0501312d", "title": "Missing SAP readiness check access in our S-user ID", "problem": "Missing SAP Readiness Check access for S-user ID.", "solution": "Refer to SAP Note 3310759 for revised authorization concepts and SAP Note 2891528 for self-service authorization requests. Contact Super-Admin-S-User for assigning the necessary authorizations.", "cause": "Lack of proper authorization assigned to the S-user ID for accessing SAP Readiness Check.", "processed_at": "2025-06-25T16:26:27.810549"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a74af85447cbf5141cccb7da216d4308", "title": "ATC findings upload failed in SAP Readiness Check", "problem": "ATC findings upload failed in SAP Readiness Check due to invalid atc_setting or UDP file.", "solution": "The development team is working on a fix, and a Knowledge Base Article (KBA) 3361913 will be updated with the solution or workaround. Monitor the KBA for updates and apply the fix once available.", "cause": "The atc_setting or UDP file is not valid, as indicated in the error message and referenced in SAP Note 2758146.", "processed_at": "2025-06-25T16:26:37.162361"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f6216e08c317b5d0cebe6610a00131d7", "title": "S4HANA readiness check result inquriy", "problem": "Inability to select specific company code for analysis in SAP S4HANA readiness check results.", "solution": "1. Confirmed that selection of specific company code is not possible in Process Discovery. 2. Suggested data cleaning as the company codes shown might not be valid from organizational perspective. 3. Advised to contact a specific team or email for further questions.", "cause": "The readiness check automatically extracts information from the system without the option to select specific company codes.", "processed_at": "2025-06-25T16:26:42.547396"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f6216e08c317b5d0cebe6610a00131d7", "title": "S4HANA readiness check result inquriy", "problem": "Inquiry about analyzing all company codes instead of top 5 in S4HANA readiness check.", "solution": "Consulted internally whether analyzing all company codes is possible. No clear resolution provided as further discussion with development team was necessary.", "cause": "", "processed_at": "2025-06-25T16:26:42.547418"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3978b27dc383f59c4daa2875e0013196", "title": "Error when executing report for SAP Readiness Check", "problem": "Error when executing report for SAP Readiness Check due to missing prerequisite SAP Note 2972792.", "solution": "De-implement and reimplement SAP Note 2972792. Delete objects during de-implementation. Manually activate yellow warnings in SNOTE during reimplementation to override objects to the latest version. Follow the 'Reason and Prerequisites' section.", "cause": "Objects inconsistency during transport. Reason and Prerequisites not fully performed.", "processed_at": "2025-06-25T16:26:48.190040"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3978b27dc383f59c4daa2875e0013196", "title": "Error when executing report for SAP Readiness Check", "problem": "Syntax error in program CL_RC_FDQ_FI_AA_MONITOR when executing Readiness Check report.", "solution": "Ensure implementation of SAP Note 3250447 and check prerequisites for Financial Data Quality.", "cause": "", "processed_at": "2025-06-25T16:26:48.190080"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f64229a7c3a671d822195030a00131e9", "title": "After Update of Note 2758146 program dumps", "problem": "Program RC_VALUE_DISCOVERY_COLL_DATA dumps with OBJECTS_OBJREF_NOT_ASSIGNED error after updating SAP Note 2758146.", "solution": "The user was advised to follow the solution provided in SAP notes 3081723 and 2981833 to address errors in program /SDF/CL_S4RC20_FACTORY while running SAP Readiness Check 2.0. If issues persist, implement note 3205320 to repair syntax errors.", "cause": "The issue is caused by accessing a 'NULL' object reference in ABAP program /SDF/CL_S4RC20_FACTORY after the update of SAP Note 2758146.", "processed_at": "2025-06-25T16:26:54.478205"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f64229a7c3a671d822195030a00131e9", "title": "After Update of Note 2758146 program dumps", "problem": "Program RC_COLLECT_ANALYSIS_DATA dumps due to a syntax error related to missing component 'FDQ'.", "solution": "Ensure all prerequisites are met as per SAP notes 2913617 and 2972792 concerning Financial Data Quality checks for SAP Readiness Check. Implement missing notes and essential steps.", "cause": "Syntax error in program RC_COLLECT_ANALYSIS_DATA due to missing component 'FDQ', which indicates incomplete implementation of prerequisites for Financial Data Quality checks.", "processed_at": "2025-06-25T16:26:54.478228"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/47e9ba15c3177590b118f71ad0013161", "title": "error applying note 2758146", "problem": "Error applying SAP note 2758146 due to unknown field 'VC_MIN_ST_A_PI_VERS'.", "solution": "1. Completely de-implement note 2758146 from the system. 2. Implement the latest version of note 2758146. 3. Manually activate any yellow warning objects during implementation.", "cause": "Inconsistency between the code and note context.", "processed_at": "2025-06-25T16:27:02.683649"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/47e9ba15c3177590b118f71ad0013161", "title": "error applying note 2758146", "problem": "Unable to delete items 'CL_TMW_RC_COLLECT_BPA' and 'IF_TMW_RC_COLLECT_BPA' due to private mode restriction.", "solution": "1. Attempt manual deletion of items. 2. If deletion fails, use a workaround: Implement note 2758146 in another system without these items and transport the 'Transport Request' to the affected system.", "cause": "Items are in private mode and should have been deleted during de-implementation of note 2758146 but were not.", "processed_at": "2025-06-25T16:27:02.683673"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/47e9ba15c3177590b118f71ad0013161", "title": "error applying note 2758146", "problem": "Syntax error when implementing SAP note 2758146 related to 'CV_MIN_ST_A_PI_VERS' field being in private visibility.", "solution": "Check and implement prerequisite notes completely before attempting to implement note 2758146.", "cause": "Missing prerequisite notes causing the field 'CV_MIN_ST_A_PI_VERS' to remain in private visibility.", "processed_at": "2025-06-25T16:27:02.683679"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/47e9ba15c3177590b118f71ad0013161", "title": "error applying note 2758146", "problem": "User authorization expired for connecting to ES1 system.", "solution": "Maintain and update user authorization for ES1 system.", "cause": "User authorization credentials were outdated.", "processed_at": "2025-06-25T16:27:02.683683"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/47e9ba15c3177590b118f71ad0013161", "title": "error applying note 2758146", "problem": "RFC issue encountered when downloading note after de-implementation of note 2928592.", "solution": "Investigate and resolve RFC connectivity issues to proceed with note download.", "cause": "", "processed_at": "2025-06-25T16:27:02.683688"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/16444e8ec3df7dd03331583bb001313f", "title": "Readiness Check Financial Data Quality problem", "problem": "Executing RC_COLLECT_ANALYSIS_DATA results in a runtime dump due to an invalid table name in Open SQL command.", "solution": "1. Implement SAP Note 3075335 and follow the manual steps outlined. 2. Complete pre and post manual steps and implement SAP Note 2896400.", "cause": "Manual steps within SAP Notes 3075335 and 2896400 were not executed, leading to missing DDIC table definition.", "processed_at": "2025-06-25T16:27:08.976165"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/16444e8ec3df7dd03331583bb001313f", "title": "Readiness Check Financial Data Quality problem", "problem": "TMW_RC_FDQ_DATA_COLL job fails with SAPSQL_INVALID_TABLENAME runtime error due to invalid table name specified in Open SQL.", "solution": "1. Implement SAP Note 3130905 for ML Reconciliation Report DDIC object creation. 2. Execute manual pre and post steps as per SAP Note 3129858 and implement it.", "cause": "Incomplete implementation of SAP Notes 3130905 and 3129858, resulting in missing table definitions for Material Ledger.", "processed_at": "2025-06-25T16:27:08.976194"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/77fbead5c39b759030037d9f0501316c", "title": "BW/4HANA - BW Bridge readiness check fails (Note 3061594, RC_BW_COLLECT_ANALYSIS_DATA)", "problem": "BW Bridge readiness check fails with error 'SMDB Content in Local DB Not Found'.", "solution": "Download and implement the latest version 42 of SAP Note 3061594 and run the report again.", "cause": "Bug in the previous version of SAP Note 3061594.", "processed_at": "2025-06-25T16:27:14.436655"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2a5af5be834fb5900f3ba780ceaad341", "title": "xml files generated for spotlight report are blank .", "problem": "XML files generated for the Spotlight report are blank.", "solution": "Confirmed that the current ST-A/PI version 01U_731 SP00 should suffice for the Spotlight report generation based on SAP Notes 0002745851 and 0002758146. However, recommended updating to the latest version.", "cause": "Potential outdated ST-A/PI version impacting report generation.", "processed_at": "2025-06-25T16:27:18.223902"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/24884f5fc34f39148710df00a001319d", "title": "SAP Readiness Check: techn. problem occured -> create a support message", "problem": "Technical problem in SAP Readiness Check affecting Financial Data Quality.", "solution": "1) Ask the customer to provide Readiness Check data collection ZIP file. 2) Verify FDQ files are present. 3) Check system Z19 for data collection status. 4) Run RC_COLLECT_ANALYSIS_DATA report via transaction SE38 with only 'Financial Data Quality' option checked. 5) Delete buffered data for selected items. 6) Check all desired options and schedule data collection. 7) Wait for completion and re-attempt Readiness Check Analysis.", "cause": "Suspected issues with RTCCTOOL 'Online Collectors' affected initial data collection.", "processed_at": "2025-06-25T16:27:24.048849"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/59b84863c31abd10b9946ecd2b0131f2", "title": "S4HANA Readiness Check job RC_COLLECT_ANALYSIS_DATA Failed", "problem": "S4HANA Readiness Check job RC_COLLECT_ANALYSIS_DATA failed due to HANA DB sizing sub job timeout.", "solution": "Schedule another Readiness Check data collection via RC_COLLECT_ANALYSIS_DATA with the same options as before after confirming job /SDF/HDB_SIZING_SM has finished. Ensure not to delete any buffers during the process.", "cause": "Job TMW_RC_HANAS_DATA_COLL waits for job /SDF/HDB_SIZING_SM which finished late, causing timeout.", "processed_at": "2025-06-25T16:27:27.897619"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/82ae360bc316f110b118f71ad0013156", "title": "Error while Enabling Financial Data Quality Analysis", "problem": "Error when running manual post steps for implementing financial data quality analysis note.", "solution": "Implement note 3053181 according to guidelines, verify and maintain FIN_FB_CORR message class.", "cause": "FIN_FB_CORR message class not maintained.", "processed_at": "2025-06-25T16:27:32.783378"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/70f318a8c38e8214b32e1c3ed40131a1", "title": "Readiness check execution error", "problem": "Error when uploading Readiness Check data.", "solution": "Resolve RTCCTOOL RFC connection issue.", "cause": "The uploaded file is not valid; file not downloaded using the SAP Readiness Check Data Collector.", "processed_at": "2025-06-25T16:27:39.566867"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/70f318a8c38e8214b32e1c3ed40131a1", "title": "Readiness check execution error", "problem": "Dumps in SNOTE transaction.", "solution": "Create a new case in component BC-UPG-NA for assistance.", "cause": "Independent issue with SNOTE causing ABAP program termination.", "processed_at": "2025-06-25T16:27:39.566891"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/70f318a8c38e8214b32e1c3ed40131a1", "title": "Readiness check execution error", "problem": "RTCCTOOL RFC connection error.", "solution": "Review systems sending RFC calls to SAP and deactivate unused RFC destinations using SM59 transaction.", "cause": "SAPOSS RFC connection is shut off but still configured in the system.", "processed_at": "2025-06-25T16:27:39.566898"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0b1037c5c3323dd0cebe6610a00131e2", "title": "The ‘Planned Downtime Calculator' in SAP Readiness Check Dashboard has missing data", "problem": "Missing data in 'Planned Downtime Calculator' section of SAP Readiness Check Dashboard.", "solution": "Use the original data collection ZIP file and update the analysis using the 'Update Analysis' function in the top left corner of the screen.", "cause": "Insufficient number of closely matching customer references in the database due to the transition from the old backend to SAP for Me.", "processed_at": "2025-06-25T16:27:44.451264"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fdfedbb9c35f75d03331583bb00131fb", "title": "Readiness Check report : missing analysis on Material Ledger", "problem": "Readiness check report shows 'No Data Submitted for Analysis' for Material Ledger in production system.", "solution": "Execute report RC_COLLECT_ANALYSIS_DATA in Expert Mode, selecting only Material Ledger option separately from other data collectors. Append the results to the analysis session using the original ZIP archive.", "cause": "The check for Material Ledger data was not performed correctly in the production environment.", "processed_at": "2025-06-25T16:27:49.795927"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/36c00947c33b7d98ca3f1c0bb0013199", "title": "Custom Code upload file is not valid", "problem": "Custom Code upload file atc_findings.zip is not valid for SAP Readiness Check.", "solution": "Ensure SAP Note 3059197 is followed: 1. Execute ABAP test cockpit with correct variant. 2. Export results using 'Export to File for SAP Readiness Check'. 3. Update analysis on dashboard with exported file. Implement SAP Note 2781766 if SAP BASIS is below 7.53 SP3 or 7.52 SP5. If issue persists, attach file for further analysis.", "cause": "Incorrect format of the atc_findings.zip file due to improper export from ABAP test cockpit or missing prerequisites in SAP Note 2781766.", "processed_at": "2025-06-25T16:27:56.693479"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/36c00947c33b7d98ca3f1c0bb0013199", "title": "Custom Code upload file is not valid", "problem": "Error after applying SAP Note 3293011 when uploading atc_findings.zip.", "solution": "Re-download and implement the corrected version of SAP Note 3293011, then re-export ATC findings and upload again.", "cause": "Validity of correction instruction in SAP Note 3293011 was incorrectly set, requiring a re-release.", "processed_at": "2025-06-25T16:27:56.693504"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/36c00947c33b7d98ca3f1c0bb0013199", "title": "Custom Code upload file is not valid", "problem": "Connection issues to DV1 system for SAP Readiness Check.", "solution": "Verify and update application server IP address, ensure R/3 and HTTP connections are open and valid user credentials are provided.", "cause": "Application server IP changed, leading to connection failures.", "processed_at": "2025-06-25T16:27:56.693509"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7d5d5a75c3c7fd5cb118f71ad00131c7", "title": "No *PERSON* data when collecting data via RC_VALUE_DISCOVERY_COLL_DATA for SAP Business Scenario Recommendations", "problem": "Empty *PERSON*.XML file in SAP Business Scenario Recommendations data collection due to missing workload data.", "solution": "1. Confirm the transactional workload monitoring data is not present in PRD using ST03 transaction. 2. Ensure ST03 workload monitoring is correctly activated. 3. Wait until at least one full named month or preferably three months of data is collected. 4. Re-trigger the BSR data collection and re-submit the data.", "cause": "ST03 transaction does not hold any recent workload monitoring data, which is required to populate the *PERSON*.XML file.", "processed_at": "2025-06-25T16:28:00.787596"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3c56a69fc3e2b55830037d9f050131f6", "title": "SAP ECC 6.0 to S/4 conversion", "problem": "Cannot implement SAP Note 1668882 in SAP ERP 6.0 system as a prerequisite for S/4HANA readiness check.", "solution": "Skip the implementation of SAP Note 1668882 and proceed with the readiness check preparation according to SAP Note 2913617.", "cause": "SAP Note 1668882 is not applicable to the current SAP BASIS release version 700 SPS 32.", "processed_at": "2025-06-25T16:28:05.092633"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e9d71ccfc32af518b118f71ad0013112", "title": "Note 2972792 not implementable - S4 readiness check", "problem": "SAP note 2972792 cannot be implemented due to syntax errors.", "solution": "1. De-implement SAP note 2972792. 2. Refer to SAP note 3289850 to check and clean inconsistent objects. 3. Re-implement SAP note 2972792 and handle any overwrite warnings during implementation.", "cause": "Previous inconsistent implementations of SAP note 2972792 left objects that should have been deleted, causing syntax errors during reimplementation.", "processed_at": "2025-06-25T16:28:09.745631"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2894b07cc3363d90ca3f1c0bb0013179", "title": "RC_COLLECT_ANALYSIS_DATA Fehlermeldung", "problem": "Unable to execute program RC_COLLECT_ANALYSIS_DATA due to missing requirements related to SAP Note 2072792.", "solution": "1. Ensure SAP Note 2072792 is implemented correctly. 2. Verify all related notes and prerequisites are implemented. 3. Execute the program with the correct variant and settings.", "cause": "Obsolete or incorrectly implemented version of SAP Note 2072792.", "processed_at": "2025-06-25T16:28:16.544737"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2894b07cc3363d90ca3f1c0bb0013179", "title": "RC_COLLECT_ANALYSIS_DATA Fehlermeldung", "problem": "Failed to implement the latest version of SAP Note 2972792 due to incomplete prior implementation.", "solution": "1. De-implement the existing version of SAP Note 2972792. 2. Execute program /SDF/NOTE_2909538 in 'Update and Activate' mode. 3. Implement all prerequisite SAP Notes. 4. Implement the latest version of SAP Note 2972792 using Snote_old transaction.", "cause": "Incomplete implementation of code corrections in SAP Note 2972792.", "processed_at": "2025-06-25T16:28:16.544744"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2894b07cc3363d90ca3f1c0bb0013179", "title": "RC_COLLECT_ANALYSIS_DATA Fehlermeldung", "problem": "Transport errors when moving readiness check from system WAT to WAQ due to missing data elements.", "solution": "Create a new transport including all necessary DDIC objects from report /SDF/NOTE_2909538 and import it to WAQ.", "cause": "Missing DDIC objects in the transport leading to errors in system WAQ.", "processed_at": "2025-06-25T16:28:16.544746"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/99b9bfd9476ebd141cccb7da216d439a", "title": "Job/Program is Canceled: RC_ALM_ANALYSIS_DATA_COLLECTION", "problem": "Job RC_ALM_ANALYSIS_DATA_COLLECTION is cancelled on the production system PPB.", "solution": "Update the SAP note containing the program.", "cause": "Outdated SAP note related to the program.", "processed_at": "2025-06-25T16:28:20.508004"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/99b9bfd9476ebd141cccb7da216d439a", "title": "Job/Program is Canceled: RC_ALM_ANALYSIS_DATA_COLLECTION", "problem": "Incorrect analysis data for Cloud ALM indicating wrong usage scenarios.", "solution": "Update SAP note to correct analysis data generation; validate usage scenarios manually if necessary.", "cause": "Misconfigured or incorrect data mapping in the program resulting in wrong analysis output.", "processed_at": "2025-06-25T16:28:20.508027"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a13f298fc3d7795422195030a0013117", "title": "Probleme mit e<PERSON><PERSON><PERSON> von Hinweis 2972792", "problem": "Customer faced syntax and object-related errors while implementing SAP Note 2972792.", "solution": "Access was granted to the managed system for SAP Support to reproduce and diagnose the issue. Errors were resolved, allowing the note to be successfully implemented.", "cause": "Initial implementation attempts encountered syntax and object-related errors.", "processed_at": "2025-06-25T16:28:25.428170"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a13f298fc3d7795422195030a0013117", "title": "Probleme mit e<PERSON><PERSON><PERSON> von Hinweis 2972792", "problem": "Unable to implement SAP Note 2758146 due to coding inconsistencies and errors.", "solution": "Manually delete specific objects related to the previous note implementation and re-implement SAP Note 2758146. Check and ensure consistent coding in the system.", "cause": "Obsolete version of the note was implemented, causing inconsistencies in coding.", "processed_at": "2025-06-25T16:28:25.428183"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a3504741c3e7355822195030a00131c3", "title": "SAP HANA Readiness check access", "problem": "Unable to access SAP HANA readiness check in support portal.", "solution": "1. Created analysis for the customer and provided a link to SAP Readiness Check - SAP for Me. 2. Requested the customer to provide screenshots and use web developer tools to capture network traffic.", "cause": "Incorrect SAP component selection for the request.", "processed_at": "2025-06-25T16:28:30.590826"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a3504741c3e7355822195030a00131c3", "title": "SAP HANA Readiness check access", "problem": "Difficulty in uploading readiness output and generating results.", "solution": "Customer uploaded the latest generated output and SAP provided a link to the analysis along with generated documents.", "cause": "", "processed_at": "2025-06-25T16:28:30.590848"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a3fe20f4c3e679d022195030a0013183", "title": "Performance problem executing Readiness Check", "problem": "Performance problem executing Readiness Check due to Financial Data Quality jobs not finishing.", "solution": "Analyzed the performance logs and optimized the job execution parameters to enhance processing speed.", "cause": "The Financial Data Quality jobs were consuming excessive resources, leading to prolonged execution time.", "processed_at": "2025-06-25T16:28:34.588788"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5d4eb4eac3c73510ecd47d9f050131ef", "title": "Performance problem executing Readiness Check", "problem": "The Readiness Check jobs are running for more than 8 days without completion, causing significant performance issues in the production system.", "solution": "Schedule the Readiness Check job excluding the finance data quality option first, which will be quicker. Then, schedule another job specifically for the finance data quality option. Append the results of the finance check to the initial report using the Upload function as per SAP note 2913617.", "cause": "The finance data quality jobs are designed to take a long time depending on the volume of active financial data, which leads to extended execution times.", "processed_at": "2025-06-25T16:28:39.874498"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/764c257e47c7f1d81cccb7da216d43d5", "title": "Implementing SAP S/4HANA readiness check reports", "problem": "Runtime error occurring when executing SA38 report RC_COLLECT_ANALYSIS_DATA after implementing SAP Notes 2758146 and 2745851.", "solution": "1. De-implement SAP Note 2972792. 2. Implement SAP Note 2909538 and execute the manual instructions described in the note by running transaction SA38, entering the program name /SDF/NOTE_2909538, and executing the program in 'Update and Activate' mode. 3. Re-implement SAP Note 2972792. 4. Re-run the RC_COLLECT_ANALYSIS_DATA report.", "cause": "Missing implementation of SAP Note 2909538 which is required before implementing SAP Note 2972792.", "processed_at": "2025-06-25T16:28:44.715585"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3569847ec3973d143331583bb0013179", "title": "Process Discovery for SAP S/4HANA Transformation", "problem": "Process Discovery report in production system is generating blank files.", "solution": "Check if the ZIP file was uploaded correctly and confirm the upload. Refer to the Process Discovery How-To PDF for detailed steps. Ensure that SAP will send the results via email, which may take up to 5 business days.", "cause": "Incorrect or incomplete upload process of the ZIP file.", "processed_at": "2025-06-25T16:28:49.643773"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3569847ec3973d143331583bb0013179", "title": "Process Discovery for SAP S/4HANA Transformation", "problem": "Clarification needed on applying SAP notes 2758146 and 2745851 in production system.", "solution": "Apply SAP notes 2758146 and 2745851 directly in the production system using SNote, as they are valid for the system. Refer to KBA 2977422 for further guidance on Process Discovery.", "cause": "Uncertainty regarding the method of implementing SAP notes in the production system.", "processed_at": "2025-06-25T16:28:49.643796"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3f26be4bc3073190ecd47d9f050131af", "title": "No TTcode data when collecting data via RC_VALUE_DISCOVERY_COLL_DATA for SAP Business Scenario Recommendations’", "problem": "No TTcode data when collecting data via RC_VALUE_DISCOVERY_COLL_DATA for SAP Business Scenario Recommendations.", "solution": "Follow the solution steps described in KBA 2977422 to address missing TTCODE.xml data in the process discovery zip file.", "cause": "The system is a recent copy of the production system; hence work stats are not available, which results in missing TTCODE.xml data.", "processed_at": "2025-06-25T16:28:53.477503"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/82cc6f3dc39f3d90ca3f1c0bb001317f", "title": "SAP Readiness Check", "problem": "Execution of report RC_COLLECT_ANALYSIS_DATA ends with short dump TSV_TNEW_PAGE_ALLOC_FAILED.", "solution": "1. Adjust memory parameters as per SAP Note 2180736 recommendations. 2. Deselect Web Service Interface option during report execution. 3. Investigate Web Service data collection if issue persists.", "cause": "Memory-related parameter configuration issue leading to exhaustion of Extended and Heap Memory quotas.", "processed_at": "2025-06-25T16:28:58.655097"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/189804c6c3d77d90ca3f1c0bb0013171", "title": "CHaRM enablement in Cloud ALM for BTP applications", "problem": "Confusion regarding CHaRM enablement for BTP applications in Cloud ALM.", "solution": "Clarified that CHaRM in Cloud ALM is different from Solution Manager; provided blog links for guidance on integrating SAP Integration Suite with SAP Cloud ALM and using Features in CDM.", "cause": "Misunderstanding that CHaRM in Solution Manager is directly applicable to Cloud ALM.", "processed_at": "2025-06-25T16:29:02.775363"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/189804c6c3d77d90ca3f1c0bb0013171", "title": "CHaRM enablement in Cloud ALM for BTP applications", "problem": "Configuration of CTS or CHaRM in BTP system requires Solution Manager version 7.2 SP10 or higher.", "solution": "Confirmed that configuration is not possible with BTP applications if Solution Manager version is less than SP10.", "cause": "Specific version requirement for Solution Manager to enable configuration with BTP applications.", "processed_at": "2025-06-25T16:29:02.775463"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/68d5a6c7c3cf3d10cebe6610a0013189", "title": "Readiness check for S/4 HANA Migration", "problem": "Readiness check 2.0 for S/4 HANA migration does not include custom code analysis.", "solution": "1. Implement SAP Note 2185390 for Custom Code Analyzer. 2. Run program SYCM_DOWNLOAD_REPOSITORY_INFO. 3. Alternatively, use ABAP Test Cockpit as per SAP Note 2781766.", "cause": "Custom code analysis is not part of the executed readiness check report.", "processed_at": "2025-06-25T16:29:08.200081"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/68d5a6c7c3cf3d10cebe6610a0013189", "title": "Readiness check for S/4 HANA Migration", "problem": "Missing S4HANA_READINESS_CHECK ATC variant in the development system.", "solution": "Follow SAP Note 2913617 to ensure correct components and procedures are applied for readiness check.", "cause": "Query on SAP Readiness Check for SAP S/4HANA, not related to HANA DB.", "processed_at": "2025-06-25T16:29:08.200089"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/eb5c0871c3a6359422195030a0013169", "title": "HANA READINESS REPORT", "problem": "RC_COLLECT_ANALYSIS_DATA report execution causes job TMW_RC_BPA_DATA_COLL to fail with dump TSV_TNEW_PAGE_ALLOC_FAILED.", "solution": "Follow resolution steps for known issues #6 in SAP KBA 2968380 - SAP Readiness Check Report 2.0 troubleshooting guide.", "cause": "Insufficient storage space available for extending an internal table.", "processed_at": "2025-06-25T16:29:12.700433"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/81530db2c34bf150b118f71ad001311f", "title": "Syntax error while execute program by tcode se38 then execute rc_value_discovery_coll data", "problem": "Syntax error when executing program RC_VALUE_DISCOVERY_COLL_DATA using transaction code SE38.", "solution": "Reviewed SAP Notes 0003283120, 0003205320, and 0003081723 to identify potential fixes for syntax errors and ensure SAP Note 2745851 is correctly implemented.", "cause": "The ABAP application program contained an undeclared or non-inherited method 'ADDTOJSON' in class '/SDF/CL_S4RC20'.", "processed_at": "2025-06-25T16:29:16.912291"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a72ab13e834fb5900f3ba780ceaad3a7", "title": "S/4 *PERSON* in U2K2", "problem": "RC_COLLECT_ANALYSIS_DATA program generates excessive database locks leading to job cancellation.", "solution": "Refer to SAP Note 2913617 for Readiness Check for SAP S/4HANA and apply the latest Data Collectors notes.", "cause": "Job was canceled manually due to excessive log utilization exceeding 150GB, and many dumps not directly related to Readiness Check process.", "processed_at": "2025-06-25T16:29:21.083755"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/441fc3b2c33a3dd43331583bb001316f", "title": "Syntax error in program \"/SDF/SAPLBPM_7X", "problem": "Syntax error in program '/SDF/SAPLBPM_7X' due to missing include report.", "solution": "1. Uninstall Readiness Check 1.0 related SAP Notes such as 0002443236 or 0002310438 and transport changes through the landscape. 2. Uninstall SAP Note 0002745851 (RC2.0) and transport through the landscape. 3. Re-install SAP Note 0002745851 again (RC2.0) and transport through the landscape.", "cause": "Obsolete SAP Note related to Readiness Check 1.0 was implemented on the source system but not transported to the target system, leading to inconsistencies and missing function modules.", "processed_at": "2025-06-25T16:29:25.998643"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3c4adeadc3be7d14cebe6610a0013110", "title": "Syntax error in program \"/SDF/SAPLBPM_7X", "problem": "Syntax error in program /SDF/SAPLBPM_7X.", "solution": "The issue was identified as a duplicate of another case (6008787/2023) and was closed. The investigation was continued in the original case.", "cause": "The incident was a duplicate of an existing case.", "processed_at": "2025-06-25T16:29:29.931202"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/44a17ec4c372795022195030a00131e6", "title": "2758146 95 SAP Readiness Check for SAP S/4HANA & Process Discovery", "problem": "Error while implementing SAP note 2758146 - 'Name <MIME objects> is not unique'.", "solution": "Refer to KBA 3308795 which provides steps to resolve the 'Name <MIME objects> is not unique' error during SAP note 2758146 implementation.", "cause": "Documents with different keys already exist, causing the uniqueness error.", "processed_at": "2025-06-25T16:29:33.589599"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2f5af0f183d779900f3ba780ceaad367", "title": "SAP Readiness Check for SAP S/4HANA", "problem": "Customer requests SAP Readiness Check report for upgrading to S/4HANA.", "solution": "SAP Support suggested checking configuration of readiness check report as documented in SAP note 2913617, and referred to SAP note 83020 for further guidance.", "cause": "", "processed_at": "2025-06-25T16:29:37.126827"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8f3a38b183d779900f3ba780ceaad357", "title": "Revised Authorization Concept for SAP Readiness Check", "problem": "Super administrator unable to access SAP Readiness Check due to 'not authorized' error.", "solution": "Confirm that the user is not a Super-Admin-S-User. Refer to SAP Note 0003310759 for steps to request relevant authorization from responsible Super-Admin-S-User.", "cause": "User was incorrectly presumed to be a Super-Admin-S-User, leading to authorization issues.", "processed_at": "2025-06-25T16:29:41.977456"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8b36f5ebc3ebb55422195030a0013137", "title": "SAP Innovation and Optimization Pathfinder on Spotlight for *PERSON* Cia Tecnica Internacional SACI", "problem": "Program RC_VALUE_DISCOVERY_COLL_DATA execution results in incomplete data due to missing BPA XML file data.", "solution": "1. Ensure user has authorization Object 'SM_BPM_DET' with Characteristic 'OBJECT_MS'. 2. Download and apply latest versions of notes 2758146, 2745851, 2549411, and 2557474. 3. Re-run RC_VALUE_DISCOVERY_COLL_DATA and generate a zip file. 4. Create a new request with the complete data file on www.sap.com/pathfinder2.", "cause": "Missing authorization for Object 'SM_BPM_DET' with required Characteristic 'OBJECT_MS'.", "processed_at": "2025-06-25T16:29:47.705290"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8b36f5ebc3ebb55422195030a0013137", "title": "SAP Innovation and Optimization Pathfinder on Spotlight for *PERSON* Cia Tecnica Internacional SACI", "problem": "RC_COLLECT_ANALYSIS_DATA program lacks options in 'Target S/4HANA Version' field.", "solution": "Apply SAP notes 2502552 and 2399707 to resolve the issue with dropdown list.", "cause": "Dropdown list empty due to missing updates reflected in SAP notes.", "processed_at": "2025-06-25T16:29:47.705321"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8b36f5ebc3ebb55422195030a0013137", "title": "SAP Innovation and Optimization Pathfinder on Spotlight for *PERSON* Cia Tecnica Internacional SACI", "problem": "Connection attempts running into timeout error.", "solution": "Ensure connection is open and access data provided, then retry connection.", "cause": "", "processed_at": "2025-06-25T16:29:47.705326"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/3724468ec3df7dd03331583bb001319b", "title": "Unable to view the Recent Analyses of SAP Readiness check", "problem": "User unable to view SAP Readiness Check reports due to authorization error.", "solution": "1. Verify the S-user's authorization status for SAP Readiness Check Portal. 2. Refer to SAP Note 3310759 for revised authorization concept. 3. Request the Super-Admin-S-User to assign the required authorizations. 4. <PERSON><PERSON> accessing the Readiness Check Analysis after authorization assignment.", "cause": "The S-user does not have the necessary authorizations assigned according to the revised authorization concept for SAP Readiness Check.", "processed_at": "2025-06-25T16:29:51.875588"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5d979b59c38b395cb9946ecd2b01312a", "title": "readinesscheck Custom Code Analysis Technical Error Occurred", "problem": "Custom Code Analysis Technical Error Occurred during SAP Readiness Check.", "solution": "1. Confirmed the error was due to missing Custom Code Analysis data collection. 2. Instructed customer to perform the Custom Code Analysis data collection separately as per SAP Note 3059197. 3. Provided steps to execute the ATC checks and export results using SYCM_ATC_EXPORT program. 4. Customer confirmed issue was resolved after following provided instructions.", "cause": "The Custom Code Analysis data collection was not performed, leading to a technical error in the Readiness Check.", "processed_at": "2025-06-25T16:29:56.204311"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a53b319ac39f39d0b9946ecd2b013150", "title": "Error \"Whitelabel Error Page\" showed when downloading readiness check result", "problem": "Error 'Whitelabel Error Page' appears when downloading readiness check results in SAP Support Portal.", "solution": "The development team investigated similar cases and identified it as a bug. The issue was fixed in Production, allowing the customer to generate and download the document successfully.", "cause": "The error was due to an internal server issue, specifically a lack of explicit mapping for /error, resulting in a fallback 'Whitelabel Error Page'.", "processed_at": "2025-06-25T16:30:00.002796"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c12cd503c366f518ecd47d9f0501316f", "title": "Problem with SAPNote import", "problem": "Error occurs during SAP Note 2758146 implementation with message 'Name <MIME objects> is not unique'.", "solution": "Follow the steps provided in SAP KBA 3308795 to resolve the error.", "cause": "Documents already exist with a different key, causing the uniqueness error.", "processed_at": "2025-06-25T16:30:04.210906"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/eb391989c3f6f1d0ecd47d9f05013140", "title": "Prerequisite up-to-date SAP Note 2972792 for Financial Data Quality is not installed", "problem": "Prerequisite up-to-date SAP Note 2972792 for Financial Data Quality is not installed when executing RC_COLLECT_ANALYSIS_DATA.", "solution": "Uncheck the Financial Data Quality when executing the Readiness Check report as advised in SAP Note 3186098.", "cause": "The system is not in the described scope for SAP Note 2972792, thus it is not suitable to implement this note.", "processed_at": "2025-06-25T16:30:08.582934"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/836eccadc38ff91cecd47d9f050131fd", "title": "SAP Readiness RC_COLLECT_ANALYSIS_DATA TMW_RC_FDQ_DATA_COLL dump", "problem": "Short dump occurs when running SAP Readiness Check for S/4HANA report RC_COLLECT_ANALYSIS_DATA with Financial Data Quality data job TMW_RC_FDQ_DATA_COLL.", "solution": "Run the report using 'Schedule Analysis' button instead of 'Execute in Background'. Refer to SAP KBA 3344180 for further guidance.", "cause": "Report execution using 'Execute in Background' button leads to ABAP Dump due to NULL object reference.", "processed_at": "2025-06-25T16:30:12.248627"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/efe659edc3c4061cecd47d9f050131b9", "title": "Process Discovery for SAP S/4HANA Transformation", "problem": "The TTCODE.XML file is empty in the Process Discovery data collection for SAP S/4HANA Transformation.", "solution": "1. Verify that the ST-A/PI version is 01V_731 or higher as recommended. 2. Ensure ST03 Workload Monitoring is activated to collect at least three full months of data. 3. Execute the data collection with the program RC_VALUE_DISCOVERY_COLL_DATA after ensuring complete ST03 data for May, June, and July. 4. If ST03 data is available, re-upload the ZIP file and monitor for validation messages.", "cause": "Incomplete ST03 data due to missing workload monitoring or incorrect data collection settings.", "processed_at": "2025-06-25T16:30:17.947653"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/efe659edc3c4061cecd47d9f050131b9", "title": "Process Discovery for SAP S/4HANA Transformation", "problem": "Missing data in the BSR report despite uploading the ZIP file.", "solution": "1. Confirm that the latest BSR report contains at least one entry with TASKTYPE=01 (dialogue mode). 2. Upload the updated report to the BSR team for processing.", "cause": "Initial BSR report lacked sufficient entries with TASKTYPE=01 for processing.", "processed_at": "2025-06-25T16:30:17.947674"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b4b65edac31b711430037d9f05013147", "title": "RC_VALUE_DISCOVERY_COLL_DATA for SAP Business Scenario Recommendations", "problem": "Incomplete data provided during SAP S/4HANA migration process using RC_VALUE_DISCOVERY_COLL_DATA.", "solution": "Ensure the analysis is run on a productive system as per the prerequisites. If using a productive system, refer to KBA 2977422 for resolution steps related to known issues.", "cause": "The analysis was executed on a non-productive system, which does not support full data collection.", "processed_at": "2025-06-25T16:30:23.366532"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b4b65edac31b711430037d9f05013147", "title": "RC_VALUE_DISCOVERY_COLL_DATA for SAP Business Scenario Recommendations", "problem": "Missing *PERSON*.XML file when collecting data via RC_VALUE_DISCOVERY_COLL_DATA.", "solution": "Open an incident using component SV-SCS-S4R and attach all relevant information and files regarding the missing *PERSON* data.", "cause": "", "processed_at": "2025-06-25T16:30:23.366553"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b4b65edac31b711430037d9f05013147", "title": "RC_VALUE_DISCOVERY_COLL_DATA for SAP Business Scenario Recommendations", "problem": "Missing TTCODE.XML data in process discovery zip file download.", "solution": "Follow solution steps in KBA 2977422 for resolving missing TTCODE.XML data issues.", "cause": "Known issue related to process discovery data collection.", "processed_at": "2025-06-25T16:30:23.366561"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d8410e54c3ee929088b12875e0013160", "title": "SAP Readiness Check for SAP S/4HANA", "problem": "Syntax error in program RC_COLLECT_ANALYSIS_DATA during SAP Readiness Check for SAP S/4HANA.", "solution": "1. Reviewed relevant KBAs 3440064 and 3374727 for potential solutions to the syntax error. 2. Requested customer to provide system access via R/3 connection and maintain credentials for further investigation.", "cause": "Syntax error occurs in program /SDF/SAPLBPM_7X when executing report RC_COLLECT_ANALYSIS_DATA, leading to job cancellation.", "processed_at": "2025-06-25T16:30:28.491715"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/10be0c5cc322169013c299bc7a0131e4", "title": "ATC Checks Missing in SAP Readiness Report", "problem": "ATC Check Overview missing in SAP Readiness Report for S/4 HANA Conversion.", "solution": "Ensure all steps in the SAP Readiness Check process are completed, specifically Step 14: ENABLE CUSTOM CODE ANALYSIS VIA ABAP TEST COCKPIT.", "cause": "Skipped Step 14 in the SAP Readiness Check process leading to absence of ATC Check Overview.", "processed_at": "2025-06-25T16:30:33.529743"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/10be0c5cc322169013c299bc7a0131e4", "title": "ATC Checks Missing in SAP Readiness Report", "problem": "Confusion on whether to execute Step 14 or Step 15 for Custom Code Analysis.", "solution": "Follow either Step 14 for ABAP Test Cockpit results or Step 15 for SYCM_DOWNLOAD_REPOSITORY_INFO job results. Update the Readiness Check Analysis with results from one of these methods.", "cause": "Misunderstanding of SAP Note 2913617 guidance on Custom Code Analysis options.", "processed_at": "2025-06-25T16:30:33.529761"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a91410d03bea52906f96634a85e45a64", "title": "Readiness Check", "problem": "The customer is unable to upload readiness check results on SAP for Me, experiencing a timeout error after waiting for more than 2 hours.", "solution": "SAP Support successfully uploaded the .zip file, indicating the problem might be user-specific. Requested customer to provide error message screenshots and maintain R/3 connection credentials for further investigation.", "cause": "Potential user-specific issue or incorrect system setup, as SAP Support was able to successfully upload the file.", "processed_at": "2025-06-25T16:30:37.576837"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d8fd8e08eb04de504c89f02dcad0cd5a", "title": "Dump CALL_FUNCTION_NOT_ACTIVE in report RC_COLLECT_ANALYSIS_DATA", "problem": "CALL_FUNCTION_NOT_ACTIVE dump occurs when executing the RC_COLLECT_ANALYSIS_DATA report.", "solution": "1. Execute transaction SE37. 2. Select Menu 'Utilities' and choose 'Repair Function Group'. 3. Enter function group '/SDF/BPM_7X_S4RC20_GET_KPIS' in the form field. 4. Execute the repair procedure. 5. Reimplement SAP note 2758146 in the latest version without errors. 6. Activate any yellow warning objects manually by ticking all boxes to ensure they are overwritten to the latest.", "cause": "Improper implementation of SAP Note 2758146 resulting in a damaged function module.", "processed_at": "2025-06-25T16:30:42.025575"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8c0ca81d97449ad07bcf59081153af93", "title": "Schedule Analysis button missing when execute RC_COLLECT_ANALYSIS_DATA report in QL3 system", "problem": "Schedule Analysis button missing in RC_COLLECT_ANALYSIS_DATA report in QL3 system.", "solution": "Update SAP Note 2758146 to version 119 and transport it to QL3 system.", "cause": "Previous version 118 of SAP Note 2758146 contained bugs affecting functionality.", "processed_at": "2025-06-25T16:30:46.073080"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e3ea058c3ae121030037d9f0501315b", "title": "Unable to reset or reimplement note 2758146 for SPAU", "problem": "Unable to reset or reimplement SAP Note 2758146 in SPAU.", "solution": "Manual deletion of objects created by the note, then de-implementation followed by reimplementation using transport request VD4K903437.", "cause": "Syntax errors in class CL_RC_COLLECT_CLIENT_HANDLER prevented correct implementation of the note.", "processed_at": "2025-06-25T16:30:51.087824"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e3ea058c3ae121030037d9f0501315b", "title": "Unable to reset or reimplement note 2758146 for SPAU", "problem": "SAP Note 2758146 causing issues for other notes.", "solution": "Adjust SPAU and implement dependencies using the correct sequence of notes or corrections.", "cause": "Inactive objects and errors due to dependencies between notes causing implementation failures.", "processed_at": "2025-06-25T16:30:51.087845"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/e70c9654c3aa9210ca3f1c0bb0013158", "title": "SAP Readiness Check for SAP S/4HANA", "problem": "Syntax error encountered in background job RC_COLLECT_ANALYSIS_DATA during SAP Readiness Check for SAP S/4HANA.", "solution": "1. Request access to the managed system SFO and valid logon credentials for remote access. 2. Attach relevant knowledge article KB0769088 for reference. 3. Investigate and diagnose the error using the provided dump file and screenshots.", "cause": "ABAP programming error leading to SYNTAX_ERROR in the program SAPLCVI_READINESS_CHECK.", "processed_at": "2025-06-25T16:30:55.253162"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d3d2dc543baa52906f96634a85e45a5a", "title": "Custom Code Analysis - Readiness Check", "problem": "Custom Code Analysis ZIP from non-prod System cannot be uploaded into Readiness Check Analysis of prod System.", "solution": "Refer to SAP note 3059197 for proper file format requirements for S/4HANA upgrades and ensure only ATC result files are used.", "cause": "The uploaded file from SYCM_DOWNLOAD_REPOSITORY_INFO is not valid for SAP Readiness Check for S/4HANA upgrades scenario.", "processed_at": "2025-06-25T16:30:59.807063"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/d3d2dc543baa52906f96634a85e45a5a", "title": "Custom Code Analysis - Readiness Check", "problem": "There is no menu option called 'Export to' in the ABAP Test Cockpit, preventing ATC export.", "solution": "Follow the steps provided in SAP note 2781766 to manually export ATC results using SYCM_ATC_EXPORT program.", "cause": "The ATC menu option 'Export to' is missing, requiring alternative methods for exporting results.", "processed_at": "2025-06-25T16:30:59.807084"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/99ef38bd47d0dad036bf68be436d4353", "title": "No Option/selection when execute program RC_COLLECT_ANALYSIS_DATA", "problem": "No target SAP S/4HANA version option available when executing program RC_COLLECT_ANALYSIS_DATA.", "solution": "Follow resolution steps outlined in SAP Note 0002951527 to resolve the issue of an empty dropdown list for target release S/4HANA or BW/4HANA.", "cause": "The dropdown list for selecting the target version is empty due to a known issue documented in KBA **********.", "processed_at": "2025-06-25T16:31:03.872798"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/48055f92c3d25ed4b118f71ad001317e", "title": "<PERSON><PERSON> is coming in the background job", "problem": "Memory dump occurs when running RC_VALUE_DISCOVERY_COLL_DATA report in background.", "solution": "1. Identify the KPI responsible for the dump using KBA 0002874082. 2. Exclude the KPI from analysis and collect data outside of readiness check as per KBA 0002818045.", "cause": "Memory limit reached, causing TSV_NEW_PAGE_ALLOC_FAILED dump.", "processed_at": "2025-06-25T16:31:08.337768"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/48055f92c3d25ed4b118f71ad001317e", "title": "<PERSON><PERSON> is coming in the background job", "problem": "Connection error prevents investigation into background job dump.", "solution": "Resolve the connection issue to enable further investigation into the SAP system.", "cause": "", "processed_at": "2025-06-25T16:31:08.337776"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9352f108eb809e504c89f02dcad0cd36", "title": "ATC: Missing authorization check", "problem": "Running ATC on System RCL results in 'Missing authority check in ABAP report' error message 11A1.", "solution": "The issue was closed by the customer. No specific steps or resolutions were provided in the ticket.", "cause": "", "processed_at": "2025-06-25T16:31:12.597175"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9352f108eb809e504c89f02dcad0cd36", "title": "ATC: Missing authorization check", "problem": "Problem during case creation for the chat.", "solution": "Close the ticket as the original case (7612305/2024) will be handled by colleagues.", "cause": "", "processed_at": "2025-06-25T16:31:12.597203"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5812bd04eb809e504c89f02dcad0cd20", "title": "ATC: Missing authorization check", "problem": "Missing authorization check in ABAP report during ATC run on System RCL.", "solution": "Review the Security Checks for ABAP documentation. Verify the authorization check placement in the report as per guidelines.", "cause": "Incorrect implementation of authorization check in the ABAP report.", "processed_at": "2025-06-25T16:31:16.181299"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/48bc2f94470a9a901cccb7da216d432c", "title": "Other way to implement readiness check", "problem": "Customer wishes to perform a faster Readiness Check data collection for a conversion project from SAP ERP 6 to SAP S/4HANA.", "solution": "Refer to SAP Note 2913617 'SAP Readiness Check for SAP S/4HANA' and follow the instructions outlined to prepare the system for the relevant Readiness Check.", "cause": "Customer is seeking alternative methods to expedite the readiness check process due to perceived lack of progress in the current approach.", "processed_at": "2025-06-25T16:31:20.363781"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/105fb8f8ebcc5690a95ff847bad0cde1", "title": "Syntax Error appling SAP NOTE 2758146", "problem": "Syntax error in SAP program RC_VALUE_DISCOVERY_COLL_DATA when applying SAP Note 2758146.", "solution": "Uninstall the current version of SAP Note 2758146 and reinstall the latest version (version 119).", "cause": "Unknown field 'ABAP_TRUE' error not specified in Note 3221283.", "processed_at": "2025-06-25T16:31:24.093660"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f3b26faac3c6121030037d9f05013196", "title": "No all options in - RC_VALUE_DISCOVERY_COLL_DATA", "problem": "Missing fields in SIGNAVIO report generation using RC_VALUE_DISCOVERY_COLL_DATA.", "solution": "1. Implement SAP Note 0002745851 - Business Process Improvement Content for 'SAP Readiness Check 2.0' in version 70. 2. Ensure ST-A/PI Version is up-to-date with Release 01V or higher, as per SAP Note 0000069455.", "cause": "Incorrect or outdated KPI selection and missing process flows due to removed versions in SAP Notes.", "processed_at": "2025-06-25T16:31:29.186108"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f3b26faac3c6121030037d9f05013196", "title": "No all options in - RC_VALUE_DISCOVERY_COLL_DATA", "problem": "Invalid Business Process Analytics KPI error when scheduling analysis.", "solution": "Implemented SAP Notes 2758146 and 2745851 as recommended, and confirmed resolution.", "cause": "Default KPI selection error and missing process flow options.", "processed_at": "2025-06-25T16:31:29.186130"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/91535e5c97a6da90b86630200153af17", "title": "can't upload sap readiness check", "problem": "User cannot upload new SAP Readiness Check analysis to the portal.", "solution": "Verify if the manage authorization object is assigned to the user as per KBA 0003310759. Check if another user has more access to uploads to the portal.", "cause": "Missing or incorrect user authorization for managing SAP Readiness Check uploads.", "processed_at": "2025-06-25T16:31:33.501839"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/91535e5c97a6da90b86630200153af17", "title": "can't upload sap readiness check", "problem": "User can only see recent analyses on the SAP Readiness Check page.", "solution": "Confirm if the user is supposed to have access to more analyses. Check other users' access levels for disparities.", "cause": "Limited access possibly due to user authorization settings or portal configuration.", "processed_at": "2025-06-25T16:31:33.501866"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/48b020b8978c569030c531f71153af82", "title": "2758146 - Readiness Check Framework", "problem": "Error during implementation of SAP Note 2758146 for SAP Readiness Check, specifically with Program RC_VALUE_DISCOVERY_COLL_DATA Field 'ABAP_TRUE' being unknown.", "solution": "Customer followed guidance from KBA 0003469289 to resolve the issue. SAP Note 2758146 (version 119) was uploaded correctly following the recommended steps.", "cause": "Field 'ABAP_TRUE' is not recognized in the specified tables nor defined by a 'DATA' statement, causing a red error in Note Assistant.", "processed_at": "2025-06-25T16:31:38.612370"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5e08e84ac36c9ed0ca3f1c0bb00131e4", "title": "SAP Note 2758146 implementation failed due to errors", "problem": "SAP Note 2758146 implementation failed due to errors during implementation of SAP Signavio Process Insights discovery edition.", "solution": "Attempted to implement SAP Note 2758146 from the SAP side, where it was successfully implemented without errors.", "cause": "Issue cannot be reproduced.", "processed_at": "2025-06-25T16:31:43.316575"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5e08e84ac36c9ed0ca3f1c0bb00131e4", "title": "SAP Note 2758146 implementation failed due to errors", "problem": "SAP Note 2745851 implementation failed due to syntax error and incomplete de-implementation.", "solution": "Followed steps 1-3 of SAP Note 3205320 to resolve the syntax error and status issue but the note still shows as incompletely implemented.", "cause": "", "processed_at": "2025-06-25T16:31:43.316597"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/49759016974c1250cdaf79171153afdd", "title": "The field \"PIT_DETAIL_LIST\" is unknown, but there is a field with the similar name \"PCT_DETAIL_LIST\".", "problem": "Error 'The field PIT_DETAIL_LIST is unknown' during implementation of SAP note 2745851.", "solution": "Download and implement version 70 of SAP note 2745851.", "cause": "Version 69 of the note did not include the field 'PIT_DETAIL_LIST', leading to a syntax error.", "processed_at": "2025-06-25T16:31:47.550686"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/759358a2c3f85a5cecd47d9f050131c9", "title": "FDQ service", "problem": "Issue with enabling FDQ services due to confusion over required steps from SAP notes.", "solution": "Skip bootstrap TCI implementation as stated in the PDF attached to Note 0002502552 and continue with the remaining steps specified in the note for SAP S/4HANA Conversion & Upgrade new Simplification Item Checks.", "cause": "Misunderstanding regarding the necessity to implement certain preparation tasks in the 'TCI_for_Customer.pdf' and confusion over the applicability of suggested notes.", "processed_at": "2025-06-25T16:31:51.972013"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/df15e707970896947bcf59081153afe7", "title": "Hana  Readiness report Job  Failure", "problem": "Hana Readiness report Job RC_VALUE_DISCOVERY_COLL_DATA failure due to data collection errors KFFI000202 and KFFI000302.", "solution": "Implement SAP Note 2748075 to address message error 'Error while data collection' in job log. If issues persist, apply corrections from SAP Note 3404710 for ST-A/PI 01V SP3 framework.", "cause": "Data collection failure during job execution leading to specific error codes.", "processed_at": "2025-06-25T16:31:56.734337"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/df15e707970896947bcf59081153afe7", "title": "Hana  Readiness report Job  Failure", "problem": "BUZ_SCENARIO_SIGNAVIO_PI_COL job finished with errors KFFI000202 and KFFI000302 in the job log.", "solution": "Re-execute the BUZ_SCENARIO_SIGNAVIO_PI_COL job and verify successful completion while monitoring for error codes. Apply relevant SAP Notes for resolution if errors persist.", "cause": "Errors during data collection phase within the job execution.", "processed_at": "2025-06-25T16:31:56.734416"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1763c06e3b129218d537962a85e45a21", "title": "Inconsistency in Open Items in SAP Readiness Check Report", "problem": "Inconsistent documents under Open Items in SAP Readiness Check report.", "solution": "1. Requested further details including selections during the execution of RC_COLLECT_ANALYSIS_DATA, the analysis zip file, and the link to the analysis in SAP Readiness Check. 2. Forwarded the incident to the SV-SCS-S4R team for further analysis. 3. Provided contact details for SAP Support for further assistance.", "cause": "The root cause was not explicitly identified in the ticket.", "processed_at": "2025-06-25T16:32:00.775479"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bd98f991c3e09e9030037d9f05013163", "title": "Client roadmap for transformation to S/4 HANA Migration and plan on-prem HCM solutions to Cloud", "problem": "Inquiry about the roadmap for transformation to S/4 HANA migration and moving on-prem HCM solutions to the cloud.", "solution": "The case was identified as a consulting request, not technical support. Recommended contacting SAP Account Manager for service offerings and referring to SAP Notes 83020 and 2626344 for guidance on consulting services.", "cause": "Consulting request for best practices and enhancements during transformation to cloud solutions.", "processed_at": "2025-06-25T16:32:05.824247"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cc814ed4c3ee929088b12875e0013164", "title": "Cannot View the Result of S4Hana Readiness Check", "problem": "Cannot view the result of S4Hana Readiness Check after uploading it successfully.", "solution": "1. Confirmed the absence of error messages during the upload process. 2. Checked accessibility of the SAP Readiness URL and ensured it was accessible. 3. Re-uploaded the report after gaining access. 4. Verified the status 'In Preparation' and awaited its availability. 5. Addressed error status in specific sections by referring to relevant KBA documentation.", "cause": "The SAP Readiness URL was initially inaccessible, preventing the display of results.", "processed_at": "2025-06-25T16:32:11.040466"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cc814ed4c3ee929088b12875e0013164", "title": "Cannot View the Result of S4Hana Readiness Check", "problem": "Add-On Compatibility and Active Business Functions tiles are empty in the SAP Readiness Check 2.0.", "solution": "Referenced KBA 0002847830 to address the issue with empty tiles.", "cause": "Specific tiles in SAP Readiness Check 2.0 were not populated.", "processed_at": "2025-06-25T16:32:11.040511"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/06c09cfe3b5cd2d00870d44a85e45a41", "title": "SAP Readiness Check", "problem": "The catalog of target version is not updated when running the report RC_COLLECT_ANALYSIS_DATA for SAP Readiness Check.", "solution": "1. Refer to SAP Note 2951527 to address the empty dropdown list issue for target release S/4HANA or BW/4HANA. 2. Follow the steps provided in the KBA to update the target version catalog.", "cause": "Connectivity to SAP via RFC destination SAPOSS was shut down, affecting updates to the target version catalog.", "processed_at": "2025-06-25T16:32:15.161714"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/faa7261a475e5e101cccb7da216d438a", "title": "B4HANA 2023 readiness check ABAP Dump", "problem": "Execution of SAP Job RC_BW_4_HANA:NEW_CODE_ANALYSIS fails with ABAP dump TSV_TNEW_PAGE_ALLOC_FAILED.", "solution": "1. Implement latest version of SAP Note 3396444. 2. Enhance filtering of namespaces of SAP/3rd party add-ons to reduce memory consumption. 3. Execute the collector again after implementing the note. 4. Increase memory allocation using RSMEMORY as needed.", "cause": "High memory consumption due to vast interdependencies of 3rd party objects (/SNP/ and /RSC/) leading to memory overload during execution.", "processed_at": "2025-06-25T16:32:22.437534"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/faa7261a475e5e101cccb7da216d438a", "title": "B4HANA 2023 readiness check ABAP Dump", "problem": "Format 'X_PAPER' not defined for output device '$HH01' during job execution.", "solution": "Define the format 'X_PAPER' for the output device '$HH01' in the system configuration.", "cause": "Missing format configuration for the specified output device in the system settings.", "processed_at": "2025-06-25T16:32:22.437561"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/faa7261a475e5e101cccb7da216d438a", "title": "B4HANA 2023 readiness check ABAP Dump", "problem": "Timeout occurred when logging into BWD system.", "solution": "Ensure all network connections are properly configured and working. Involve network colleagues if necessary.", "cause": "", "processed_at": "2025-06-25T16:32:22.437568"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fff75dd4eb269e507d05f7510bd0cd8a", "title": "Program Error - TSV_TNEW_PAGE_ALLOC_FAILED", "problem": "Program RC_COLLECT_ANALYSIS_DATA in ECC Production System fails with TSV_TNEW_PAGE_ALLOC_FAILED dump.", "solution": "Ensure all central notes related to SAP Readiness Check are updated to the latest versions as per KBA 2913617.", "cause": "Central notes are outdated and do not meet the recommended minimum note version.", "processed_at": "2025-06-25T16:32:26.636397"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b9a7fb08c3eade5088b12875e001317a", "title": "S4 *PERSON* Process - ST22 Dump", "problem": "ST22 dump occurs when running S4C readiness check in SAP Signavio Process Insights.", "solution": "Refer to SAP Note 0002981833 for error in program /SDF/CL_S4RC20_FACTORY while running SAP Readiness Check 2.0. Reset SAP Note 0002745851 and implement the latest version.", "cause": "ABAP programming error in the report used by SAP Signavio Process Insights, discovery edition.", "processed_at": "2025-06-25T16:32:30.932184"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7b558c45c32c1290ca3f1c0bb00131bf", "title": "background job get cancelled", "problem": "Background job RC_COLLECT_ANALYSIS_DATA encounters ABAP dump due to SQL error 1205 accessing table FIN_AA_CORR_PACK.", "solution": "Ensure no other processes are accessing the table FIN_AA_CORR_PACK by stopping any old instances or separate processes. Wait for data collection processes to complete as expected long runtimes due to large data volumes.", "cause": "The table FIN_AA_CORR_PACK was locked by another application, causing intended behavior to prevent data inconsistencies.", "processed_at": "2025-06-25T16:32:35.333955"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7b558c45c32c1290ca3f1c0bb00131bf", "title": "background job get cancelled", "problem": "Finance job running for over 8 days without completion.", "solution": "Monitor work processes and job logs for updates. Confirm that data collection is still running and wait for process completion due to large data volumes.", "cause": "Large volume of financial data present on the system causing long runtimes.", "processed_at": "2025-06-25T16:32:35.333963"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2f2329924724ded036bf68be436d4395", "title": "High runtime readiness report", "problem": "High runtime of Readiness Check for Financial Data Quality, running for more than 21 days.", "solution": "1. Confirmed long runtime is expected due to high volume of financial data. 2. Recommended running more parallel jobs by splitting company code and fiscal year combinations. 3. Suggested scheduling one fiscal year per job to improve performance. 4. Advised checking implementation of note 2972792 and ensuring notes 3130905 and 3374323 show green light in RTCCTOOL settings. 5. Proposed running Financial Data Quality check in isolation, appending results using Upload function.", "cause": "Big volume of financial data that needs to be processed.", "processed_at": "2025-06-25T16:32:40.014107"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/31998f083bead2900870d44a85e45ac8", "title": "Not able to take <PERSON><PERSON><PERSON> report", "problem": "Unable to generate <PERSON><PERSON><PERSON> report due to background job failure.", "solution": "Verify the scheduling of RC_VALUE_DISCOVER_COLL_DATA job; ensure valid logon credentials and access to the production client of system ASP.", "cause": "Background job RC_VALUE_DISCOVER_COLL_DATA failing/dumping while collecting SPIDE data.", "processed_at": "2025-06-25T16:32:44.803816"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/31998f083bead2900870d44a85e45ac8", "title": "Not able to take <PERSON><PERSON><PERSON> report", "problem": "Timeout error when attempting to log onto system ASP.", "solution": "Provide working connections and involve network colleagues if necessary.", "cause": "Network interface timeout during connection attempt to system ASP.", "processed_at": "2025-06-25T16:32:44.803841"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9128cc9ac3c45e14d1371c3ed40131a9", "title": "SAP Note 2758146 implementation failed due to errors", "problem": "SAP Note 2758146 implementation failed due to errors.", "solution": "Customer referred to ticket 577772/2024 which contains error attachments. Case 577772/2024 will be processed further as the main ticket for resolution. Remote connection to the system was requested for issue replication.", "cause": "", "processed_at": "2025-06-25T16:32:49.390713"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9128cc9ac3c45e14d1371c3ed40131a9", "title": "SAP Note 2758146 implementation failed due to errors", "problem": "Duplicated case created for documentation purpose.", "solution": "Customer informed that the case was created for documentation and advised to close it. The main case 577772/2024 will be processed for resolution.", "cause": "Case was duplicated with 577772/2024 for documentation purposes.", "processed_at": "2025-06-25T16:32:49.390736"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2078f324c36a1e1030037d9f05013176", "title": "DTEL  /SDF/FDQ_CHAR8  Deleted.", "problem": "DTEL /SDF/FDQ_CHAR8 was deleted, causing activation failure of /SDF/FDQ_ASSESSMENT.", "solution": "Implement SAP Note 2972792 related to Financial Data Quality checks and recheck the system behavior.", "cause": "DTEL /SDF/FDQ_CHAR8 missing, leading to a syntax error in the function module /SDF/FDQ_ALL_FY.", "processed_at": "2025-06-25T16:32:53.258392"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/552ec3dbc3d21a10ecd47d9f0501316b", "title": "SAP Readiness check error", "problem": "SAP Readiness Check data collection fails with ABAP runtime error CONVT_NO_NUMBER.", "solution": "1. Implement SAP Note 2919704 on the ERX system. 2. Retrigger the data collection using report RC_COLLECT_ANALYSIS_DATA. 3. Monitor the job TMW_RC_FDQ_DATA_COLL to ensure completion without errors.", "cause": "The error CONVT_NO_NUMBER indicates a conversion issue due to invalid numeric data in the program SAPLSPRI during the readiness check data collection.", "processed_at": "2025-06-25T16:32:57.321044"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/38387f88c3eade5088b12875e00131c9", "title": "SAP S/4 HANA Readiness check tile shows an error", "problem": "Unable to see tiles on SAP S/4 HANA Readiness Check page.", "solution": "Refer to Note 3330283 - SAP Readiness Check could not open in SAP for Me. Try using an anonymous tab or a different browser to access the platform.", "cause": "Using a web browser which blocks Third-Party Cookies.", "processed_at": "2025-06-25T16:33:01.252536"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4331e3883b2616900870d44a85e45a7a", "title": "Custom Code ZIP can not be uploaded to Redinesscheck", "problem": "Custom Code ZIP file cannot be uploaded to SAP Readiness Check.", "solution": "1. Verify the ZIP file was created based on the latest coding and prerequisites according to SAP Note 3059197. 2. Ensure all relevant SAP Notes, particularly SAP Note 3293011, are implemented in their latest version. 3. Rerun the Custom Code Data collection and generate a new ZIP file. 4. Attempt to update the Readiness Check Analysis using the 'Update Analysis' button.", "cause": "The ZIP file was created based on outdated coding, and SAP Note 3293011 was not entirely up to date.", "processed_at": "2025-06-25T16:33:05.948819"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/108f1926474012901d1ff2b5536d435d", "title": "Getting the Error while running the report \"RC_COLLECT_ANALYSIS_DATA\"", "problem": "Error when running the report RC_COLLECT_ANALYSIS_DATA.", "solution": "1. Verify the ST-A/PI version as per SAP Note 3107511. 2. Check SAP Note 3061414 for integration prerequisites. 3. Ensure correct version of ST-A/PI is installed by referring to KBA 0003240048 for downloading other versions.", "cause": "Incompatible ST-A/PI version (01T_731 instead of required 01U* SP02) causing integration prerequisites not to be fulfilled.", "processed_at": "2025-06-25T16:33:10.267453"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7c3643fdc30ed29430037d9f0501314b", "title": "Ejecución reporte Process Discovery", "problem": "Job BUZ_SCENARIO_SIGNAVIO_PI_COL cancelled due to process termination in ERP QAS system.", "solution": "Increase abap/heaplimit parameter to 4GB and review SAP Notes 3393415 and 3404710.", "cause": "Heap memory limit exceeded during job execution.", "processed_at": "2025-06-25T16:33:15.992100"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7c3643fdc30ed29430037d9f0501314b", "title": "Ejecución reporte Process Discovery", "problem": "Error with Business Process Analytics data collection job TMW_RC_BPA_DATA_COLL, causing job cancellation due to process termination.", "solution": "Exclude KPI KSD000306 from execution and rerun the report, following steps in KBA 0002897369.", "cause": "KPI KSD000306 failure during processing.", "processed_at": "2025-06-25T16:33:15.992125"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7c3643fdc30ed29430037d9f0501314b", "title": "Ejecución reporte Process Discovery", "problem": "Error in RC_COLLECT_ANALYSIS_DATA job execution related to SAP Readiness Check for SAP S/4HANA.", "solution": "Refer to KBA ********** for documented error resolution steps.", "cause": "Documented error in readiness check report.", "processed_at": "2025-06-25T16:33:15.992132"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4b51eed5c324da904daa2875e0013112", "title": "Urgent: <PERSON><PERSON><PERSON> Encountered in S/4 Readiness Chec", "problem": "Error during the S/4 readiness check due to missing SAP Note 2811183.", "solution": "Refer to SAP Note 2968380 troubleshooting guide. De-implement SAP Note 2758146 completely and re-implement it in its latest version, ensuring relevant check boxes are selected as described.", "cause": "Old coding not considered during the implementation/update of SAP Note 2758146.", "processed_at": "2025-06-25T16:33:19.992863"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dfc7a2b23b1e1ed40870d44a85e45ae3", "title": "Custom Code Analysis tab is not getting updated", "problem": "Custom Code Analysis tab in SAP Readiness Check is not getting updated despite successful job execution.", "solution": "1. Ensure both required files, 'RC2AnalysisData*' and 'S4HMigrationRepositoryInfo*', are uploaded for the Readiness Check Analysis. 2. Refer to SAP Note ********** for detailed procedure in the FAQ section. 3. Verify the analysis creation using the specified files.", "cause": "Only one file, 'RC_COLLECT*', was initially uploaded, leading to incomplete data for updating the Custom Code Analysis.", "processed_at": "2025-06-25T16:33:24.076842"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a36e85b3c31ad25030037d9f05013105", "title": "SAP Process insight discovery report generation issue", "problem": "SAP Process insight discovery report generation issue due to outdated ST-A/PI version.", "solution": "Implement SAP Notes 2758146 and 2745851. Upgrade ST-A/PI to version 01V SP00 or higher. After upgrade, run RC_VALUE_DISCOVERY_COLL_DATA report in SA38 for data extraction.", "cause": "The system component ST-A/PI version was below the required 01V SP level 1.", "processed_at": "2025-06-25T16:33:29.370263"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a36e85b3c31ad25030037d9f05013105", "title": "SAP Process insight discovery report generation issue", "problem": "Issue accessing prerequisite documents for SAP Process insight discovery report.", "solution": "Provide SAP Notes 2758146 and 2745851 to the customer for guidance.", "cause": "Lack of access to necessary documents and SAP Notes needed for report generation.", "processed_at": "2025-06-25T16:33:29.370285"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cb8e0408472ed6501cccb7da216d4359", "title": "Unable to Execute RC_COLLECT_ANALYSIS_DATA Due to Uneditable Target S/4HANA Version Field", "problem": "Unable to execute RC_COLLECT_ANALYSIS_DATA due to uneditable Target S/4HANA Version field.", "solution": "Refer to SAP KBA 2951527 for the solution steps to populate the target release dropdown menu and ensure it is selectable. Recheck the situation after implementing the steps.", "cause": "The target release dropdown menu is not populated, preventing selection of the target release for SAP S/4HANA.", "processed_at": "2025-06-25T16:33:33.439440"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9bc54809c32c1290ca3f1c0bb0013150", "title": "RC for S/4HANA encounter error: Function /SDF/READ_HDB_SIZING_RESULTS_2 does not exists - Install SAP Note 1872170 first", "problem": "Error in SAP S/4HANA Readiness Check: Function /SDF/READ_HDB_SIZING_RESULTS_2 does not exist.", "solution": "Ensure SAP Note 1872170 is implemented. Verify implementation of SAP Notes ********** and 0002758146, following their instructions. Open R/3 connection for further analysis if needed.", "cause": "Missing or incorrect implementation of required SAP Notes for the Readiness Check function.", "processed_at": "2025-06-25T16:33:37.330041"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/79cee59feb441694a95ff847bad0cd76", "title": "When should the “readiness check” best be carried out?", "problem": "Determining the optimal timing for performing the 'Readiness Check - simplification item check' during the S/4HANA conversion process.", "solution": "Perform the Readiness Check after Unicode conversion to ensure the correct database and operating system version, and then once more immediately before starting the S/4HANA conversion process.", "cause": "Uncertainty about whether to perform the Readiness Check after Unicode conversion or immediately before S/4HANA conversion.", "processed_at": "2025-06-25T16:33:41.792167"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/18ede8ef3b1e9a10d537962a85e45a36", "title": "SAP Readiness Check for SAP S/4HANA note - 2913617 Cannot be implemented", "problem": "SAP Note 2913617 for SAP Readiness Check cannot be implemented.", "solution": "Inform the customer that SAP Note 2913617 is a guidance note and cannot be implemented directly. Provide a roadmap and list of necessary notes to implement for running the Readiness Check. Ensure all prerequisites and manual steps in related notes are followed.", "cause": "SAP Note 2913617 is designed for orientation, not direct implementation.", "processed_at": "2025-06-25T16:33:46.911896"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/18ede8ef3b1e9a10d537962a85e45a36", "title": "SAP Readiness Check for SAP S/4HANA note - 2913617 Cannot be implemented", "problem": "Program RC_COLLECT_ANALYSIS_DATA does not return lists for Target S/4HANA Version.", "solution": "Refer the customer to KBA 2968380 for troubleshooting guidance related to the readiness check implementation and execution. Suggest opening a new case for further support if needed.", "cause": "", "processed_at": "2025-06-25T16:33:46.911910"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/98dd206bc3d6da103331583bb00131d7", "title": "SAP Readiness Check for SAP S/4HANA note - 2913617 Cannot be implemented", "problem": "The customer is unable to implement SAP Note 2913617 because SNOTE shows it as 'Cannot be implemented'.", "solution": "Inform the customer that SAP Note 2913617 is a guidance note for orientation purposes and cannot be implemented. Advise them to check all prerequisites and follow manual steps in related notes for the SAP Readiness Check.", "cause": "The note 2913617 is intended for guidance and cannot be implemented directly.", "processed_at": "2025-06-25T16:33:52.137371"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/98dd206bc3d6da103331583bb00131d7", "title": "SAP Readiness Check for SAP S/4HANA note - 2913617 Cannot be implemented", "problem": "After implementing several notes as per SAP Note 2913617, the customer is not getting lists against 'Target S/4HANA Version'.", "solution": "Recommend the customer to refer to KBA 2968380 for troubleshooting guidance on issues related to the readiness check implementation and execution.", "cause": "", "processed_at": "2025-06-25T16:33:52.137399"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c8fb19003b891adc0870d44a85e45a1b", "title": "SAP readiness check: Business Process Discovery error", "problem": "Business Process Discovery analysis in SAP Readiness Check failed to populate.", "solution": "1. Requested the Readiness Check data collection ZIP file from the customer. 2. Verified the file contents and found no issues. 3. Obtained access to the Managed System. 4. Identified a bug in the Readiness Check portal. 5. Fixed the bug, allowing Business Process Discovery analysis to populate correctly.", "cause": "A bug in the Readiness Check portal prevented the Business Process Discovery analysis from populating.", "processed_at": "2025-06-25T16:33:57.396182"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c8fb19003b891adc0870d44a85e45a1b", "title": "SAP readiness check: Business Process Discovery error", "problem": "Failed to log onto the PED system due to network interface timeout error.", "solution": "1. Customer was asked to provide open and working connections along with valid logon credentials. 2. Customer reopened the connection using sap router and provided depot credentials.", "cause": "Network interface timeout due to closed connections and missing logon credentials.", "processed_at": "2025-06-25T16:33:57.396208"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Unable to run SAP Readiness Check report for S4HANA due to outdated SAP Notes.", "solution": "Ensure all SAP Notes are updated to their latest versions. Check the system for SAP Notes like 2745851 and 2972792, and update them accordingly.", "cause": "SAP Notes required for the Readiness Check were not up-to-date, causing the report to fail.", "processed_at": "2025-06-25T16:34:06.903400"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "SAP Readiness Check data collection fails with PXA_NO_FREE_SPACE error.", "solution": "Investigate the memory bottleneck issue that caused the PXA_NO_FREE_SPACE error and ensure the system has sufficient memory resources.", "cause": "Memory bottleneck in the system led to the PXA_NO_FREE_SPACE error during data collection.", "processed_at": "2025-06-25T16:34:06.903416"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Dropdown list for target release is empty during SAP Readiness Check setup.", "solution": "Ensure that the RFC connection SAP-SUPPORT_PORTAL is functional and re-check the dropdown list for target release following SAP Note 2951527.", "cause": "The RFC connection SAP-SUPPORT_PORTAL was not functional, preventing the dropdown list from displaying the latest options.", "processed_at": "2025-06-25T16:34:06.903419"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Custom Code Analysis section shows blank results despite running the analysis.", "solution": "Refer to SAP Notes 2185390 and 2577424, and check the implementation of program EU_INIT. Verify if the program SAPRSEUB needs to be run in the DEV or PRD system.", "cause": "Misconfiguration or missing steps in the program execution led to blank results in the Custom Code Analysis section.", "processed_at": "2025-06-25T16:34:06.903422"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Error related to incomplete implementation of SAP Note 2502552.", "solution": "Follow the instructions in SAP Note 2502552 and ensure all required dependencies are implemented correctly.", "cause": "", "processed_at": "2025-06-25T16:34:06.903425"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Unable to see latest options in /SDF/RC_START_CHECK after transporting TR.", "solution": "Verify the implementation state of SAP Notes, ensure transport request is correctly applied in QA and PRD systems, and troubleshoot synchronization issues.", "cause": "Potential synchronization issue or incomplete SAP Note implementation across systems after transporting the TR.", "processed_at": "2025-06-25T16:34:06.903428"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b4fdc75bc3d21a10ecd47d9f050131f6", "title": "Technical Fit Questionnaire", "problem": "Customer is unable to reach their Account Manager via email to get a Technical Fit Questionnaire filled out, necessary for renewal preparation.", "solution": "Customer was advised to contact another person directly via provided email as the initial contact's email bounced back.", "cause": "Email sent to Account Manager bounced back, and follow-up emails did not receive a response.", "processed_at": "2025-06-25T16:34:11.972621"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b4fdc75bc3d21a10ecd47d9f050131f6", "title": "Technical Fit Questionnaire", "problem": "Customer's ticket was not categorized in the most appropriate function/component, causing confusion regarding who could assist.", "solution": "The customer was informed that their request reached an incorrect component focused on SAP Readiness Check. The case was suggested to be closed, and a new case raised with correct component/function.", "cause": "The ticket was initially raised under a component dealing with SAP Readiness Check, which was not related to the customer's request.", "processed_at": "2025-06-25T16:34:11.972630"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fe8c79e5c38612944daa2875e00131ed", "title": "SAP Readiness Check for SAP SuccessFactors Solutions", "problem": "Customer is unable to implement SAP Note 3297478 due to errors.", "solution": "Ignore the error during implementation of note 3297478, as it affects only the XSL files and not the analysis result. Execute the report RC_HCM_COLLECT_ANALYSIS_DATA successfully without running checks for Interfaces.", "cause": "Error caused by missing folder RC_HCM, affecting only XSL files in the result zip file.", "processed_at": "2025-06-25T16:34:17.704122"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fe8c79e5c38612944daa2875e00131ed", "title": "SAP Readiness Check for SAP SuccessFactors Solutions", "problem": "Customer cannot execute report RC_HCM_COLLECT_ANALYSIS_DATA due to outdated ST-A/PI version.", "solution": "Update ST-A/PI to the latest available version in the marketplace, which is ST-A/PI 01V_731, before running the report.", "cause": "Current version of ST-A/PI is lower than the required ST-A/PI 01U* SP02, preventing report execution.", "processed_at": "2025-06-25T16:34:17.704149"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fe8c79e5c38612944daa2875e00131ed", "title": "SAP Readiness Check for SAP SuccessFactors Solutions", "problem": "SAP Note 3193560 cannot be implemented due to version mismatch.", "solution": "De-implement pilot note 3297478 and use the officially released note 3193560. Ensure necessary authorizations are assigned for SAP to investigate further in the system.", "cause": "Version mismatch with the customer's system, requiring pilot note 3297478 to support the older version.", "processed_at": "2025-06-25T16:34:17.704154"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e2a2abeeb449694b6ebf1c6cad0cd31", "title": "SPIDE Report Issue", "problem": "Customer unable to generate SPIDE Report.", "solution": "Customer was informed that the issue is a duplicate of case 371498/2024 and advised to close the current case and follow up on the existing case. Developer notified to expedite resolution.", "cause": "Duplicate case submitted for the same issue.", "processed_at": "2025-06-25T16:34:21.321092"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ac8b88d2c312d694553db11f050131cf", "title": "Note 2913617 - One Upload not valid, other S/4 Uploads are correct.", "problem": "Invalid upload file for Readiness Check Analysis due to improper data collection process.", "solution": "1. Customer advised to perform Readiness Check and Custom Code Analysis data collections separately as per SAP Note 3059197. 2. Customer confirmed following proper procedure, resulting in a valid ZIP file upon re-check.", "cause": "Customer did not perform separate data collections for Readiness Check and Custom Code Analysis, leading to an invalid file.", "processed_at": "2025-06-25T16:34:25.769716"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f418f58dc3e1dad030037d9f050131c4", "title": "runtime error while processing report RC_VALUE_DISCOVERY_COLL_DATA fora SAP Signavio Process Insights.", "problem": "Runtime error TSV_TNEW_PAGE_ALLOC_FAILED during execution of report RC_VALUE_DISCOVERY_COLL_DATA.", "solution": "1. Checked memory parameters including ztta/roll_extension, ztta/max_memreq_MB, abap/heap_area_total, abap/heap_area_dia, abap/heap_area_nondia, em/initial_size_MB, abap/shared_objects_size_MB. 2. Proposed solution to extend Extended Memory quota and Heap Memory quota. 3. Recommended to implement latest version 117 of SAP Note 2758146. 4. Removed PPI causing memory dump from data collection per SAP Note 2818045.", "cause": "Insufficient memory allocation for internal table operations leading to resource bottleneck.", "processed_at": "2025-06-25T16:34:33.309426"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f418f58dc3e1dad030037d9f050131c4", "title": "runtime error while processing report RC_VALUE_DISCOVERY_COLL_DATA fora SAP Signavio Process Insights.", "problem": "Job ends with error 'No Business Process Analytics data returned from /SDF/BPM_7X_S4RC20_GET_DATA'.", "solution": "Implemented the latest version 117 of SAP Note 2758146.", "cause": "Outdated version of SAP Note 2758146 affecting data retrieval process.", "processed_at": "2025-06-25T16:34:33.309449"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/077cc166c3d61ad4ecd47d9f0501319a", "title": "App Availability on Readiness Check could not be performed.", "problem": "App Availability on Readiness Check could not be performed due to missing workload data.", "solution": "Request customer to provide Readiness Check data collection ZIP file. Verify usage_data.xml for workload data. Ensure ST03 holds at least 3 full months of workload data. Advise customer to rerun Readiness Check data collection.", "cause": "System not gathering and storing enough ST03 data, common in development and QAS systems.", "processed_at": "2025-06-25T16:34:37.353170"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1eea4d2ec39612d4553db11f050131f3", "title": "Recommended SAP Fiori Apps on Readiness Check could not be performed.", "problem": "Recommended SAP Fiori Apps on Readiness Check could not be performed due to missing data in certain sections/tiles.", "solution": "Requested customer to provide Readiness Check data collection ZIP file. Verified that transaction_usage.xml lacks workload data. Instructed customer to ensure ST03 holds at least 3 full months of workload data before rerunning Readiness Check data collection.", "cause": "Insufficient ST03 workload data storage in development and QAS systems, causing transaction_usage.xml to lack necessary data for analysis.", "processed_at": "2025-06-25T16:34:41.416526"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/deea85a24796dad436bf68be436d4311", "title": "Recommended SAP Fiori Apps on Readiness Check could not be performed.", "problem": "Recommended SAP Fiori Apps section in Readiness Check did not populate as expected.", "solution": "1. Requested customer to provide Readiness Check data collection ZIP file for review. 2. Analyzed the transaction_usage.xml file within the ZIP. 3. Informed customer that the file lacks workload data due to insufficient ST03 data collection. 4. Advised ensuring ST03 contains at least 3 full months of data for rerunning Readiness Check.", "cause": "Insufficient ST03 data collection typically in development and QAS systems leading to missing workload data in transaction_usage.xml.", "processed_at": "2025-06-25T16:34:49.404112"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/84ab6d9ec31e9ad44daa2875e0013177", "title": "Getting syntax error while implementing  2758146", "problem": "Getting syntax error while implementing SAP note 2758146.", "solution": "1. Update SAP notes 1668882 and 2971435 to their latest versions. 2. Ensure all prerequisite notes for 2758146 are up-to-date.", "cause": "Incomplete implementation of SAP note 2758146 or inconsistencies with imported objects.", "processed_at": "2025-06-25T16:34:53.917137"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/48d21c633b129a10d537962a85e45ae4", "title": "Issue while implementing SAP Note 2758146", "problem": "Error message 'Name <MIME objects> is not unique' when implementing SAP Note 2758146.", "solution": "1. Identify existing objects in the system using SE80. 2. Manually delete the objects that are causing the conflict. 3. Re-implement SAP Note 2758146. 4. Follow steps in KBA 3308795.", "cause": "SAP Note 2758146 was previously de-implemented without removing created MIME objects, causing a conflict when re-implementing.", "processed_at": "2025-06-25T16:34:58.040831"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ac9040c43be61e500870d44a85e45af1", "title": "We need business scenario recommendations report", "problem": "Request for Business Scenario Recommendations report for migration planning.", "solution": "Inform customer about replacement with SAP Signavio Process Insights, discovery edition. Provide link to landing page for data collection and report application.", "cause": "Business Scenario Recommendations report has been replaced with a new tool.", "processed_at": "2025-06-25T16:35:01.672908"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b0d120cdc3e0de50b118f71ad0013100", "title": "Financial Data Quality data collection job failed for S4 Readiness Check", "problem": "Financial Data Quality data collection job failed for S4 Readiness Check due to 'Cannot get collector list : error with collector list key', 'Collector list not found'.", "solution": "1. Ensure Online Collectors in RTCCTOOL are enabled and operational as indicated in SAP Note 2972792. 2. Update SAP Note 2972792 to the latest version and verify all relevant notes for FI-GL, FI-AA, and ML are implemented. 3. Execute RC_COLLECT_ANALYSIS_DATA again, select Financial Data Quality option, and clear buffer using 'Delete buffered data for selected items'.", "cause": "Online Collectors is not working.", "processed_at": "2025-06-25T16:35:08.261114"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/175cda18c3aa9210ca3f1c0bb0013135", "title": "RC_COLLECT_ANALYSIS_DATA, FI_GL analysis & TMW_RC_FDQ_DATA_COLL still executing after canceling FDQ jobs", "problem": "RC_COLLECT_ANALYSIS_DATA and TMW_RC_FDQ_DATA_COLL jobs continue executing after cancellation.", "solution": "Cancel the FDQ jobs and re-execute RC_COLLECT_ANALYSIS_DATA with FDQ disabled. Once complete, upload the analysis to the portal. Then, re-run RC_COLLECT_ANALYSIS_DATA with FDQ enabled for a single fiscal year.", "cause": "System exception ERROR_MESSAGE caused TMW_RC_FDQ_DATA_COLL cancellation.", "processed_at": "2025-06-25T16:35:13.848594"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/175cda18c3aa9210ca3f1c0bb0013135", "title": "RC_COLLECT_ANALYSIS_DATA, FI_GL analysis & TMW_RC_FDQ_DATA_COLL still executing after canceling FDQ jobs", "problem": "FIN_CORR_REC_ANALYSIS jobs for multiple fiscal years are executed despite selecting a single year.", "solution": "Implement OSS notes 3369764 and 3430973 to address SAPSQL_ARRAY_INSERT_DUPREC error, and configure job execution settings to ensure only one fiscal year is processed per job.", "cause": "Misconfiguration in job execution settings leading to multiple fiscal year processing.", "processed_at": "2025-06-25T16:35:13.848629"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/175cda18c3aa9210ca3f1c0bb0013135", "title": "RC_COLLECT_ANALYSIS_DATA, FI_GL analysis & TMW_RC_FDQ_DATA_COLL still executing after canceling FDQ jobs", "problem": "FDQ reports stalled and did not complete timely.", "solution": "Apply two OSS notes, which reduced FIN_CORR_REC_ANALYSIS job execution time and prevented dumps. Implement archiving for more objects to potentially reduce execution time further.", "cause": "Large document size and processing limits causing delays in job completion.", "processed_at": "2025-06-25T16:35:13.848635"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2301f0e2c3d69a9422195030a00131ac", "title": "SAP Readiness Check", "problem": "FDQ jobs related to SAP Readiness Check are running for an extended period in a test system.", "solution": "1. Confirm that the priority of the case does not fit a business down situation and adjust it to 'High'. 2. Advise the customer on running the Financial Data Quality check separately and appending its results to the analysis session later. 3. Suggest avoiding cancellation of current jobs to prevent restarting the process from the beginning.", "cause": "Long run time is due to the volume of financial data available in the SBX system.", "processed_at": "2025-06-25T16:35:19.411851"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2301f0e2c3d69a9422195030a00131ac", "title": "SAP Readiness Check", "problem": "Issue with FI-AA component where program complains about an account belonging to depreciation area with incorrect posting control.", "solution": "1. Advise the customer to open a separate case for the issue in component FI-AA.", "cause": "", "processed_at": "2025-06-25T16:35:19.411873"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/acd1da43c356d218cebe6610a001312a", "title": "Customer Vendor Integration Analysis", "problem": "Customer Vendor Integration (CVI) data missing in SAP Readiness Check analysis file.", "solution": "1. Implement SAP Note ********** as a prerequisite for SAP Note **********. 2. Complete manual steps in SAP Note ********** related to Customer Vendor Integration Analysis. 3. Verify that the CVI job option is enabled during analysis to ensure CVI data is logged.", "cause": "CVI job option may have been disabled during analysis, resulting in absence of CVI data in the analysis file.", "processed_at": "2025-06-25T16:35:23.693212"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b521ece2c3b09a5cecd47d9f05013167", "title": "SAP Readniness check error to analyse Business Process Discovery", "problem": "SAP Readiness Check error in Business Process Discovery for SAP S/4HANA readiness report.", "solution": "Wait for the bug correction in SAP for Me by the development team and rerun the analysis with the analysis file.", "cause": "Bug in SAP for Me affecting Business Process Discovery.", "processed_at": "2025-06-25T16:35:29.210500"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b521ece2c3b09a5cecd47d9f05013167", "title": "SAP Readniness check error to analyse Business Process Discovery", "problem": "Error related to ST-A/PI version affecting the SAP Readiness Check.", "solution": "Update ST-A/PI to the minimum required version (01V SP 00) as per SAP Note 0002745851 before rerunning the report.", "cause": "ST-A/PI version not meeting the minimum requirement for BPA check.", "processed_at": "2025-06-25T16:35:29.210517"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b521ece2c3b09a5cecd47d9f05013167", "title": "SAP Readniness check error to analyse Business Process Discovery", "problem": "Incorrect component assigned for SAP Readiness Check issue.", "solution": "Reroute the case to the correct component SV-SCS-S4R.", "cause": "Misclassification of the issue related to component BC-CTS instead of SV-SCS-S4R.", "processed_at": "2025-06-25T16:35:29.210520"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c21ebf75eba056d07d05f7510bd0cd9c", "title": "SAP Readiness Check Not Authorized", "problem": "User received 'You are not authorized' error when creating Readiness Check Analysis for Integration section in SAP S/4HANA.", "solution": "Refer to SAP KBA 3137140 for resolution steps on addressing authorization issues in SAP Readiness Check Analysis.", "cause": "Authorization issues for sub-items in the Integration section of SAP Readiness Check Analysis.", "processed_at": "2025-06-25T16:35:33.212407"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/23cddc8ac3de12d4b118f71ad001312c", "title": "Note Analyzer \"Download all notes\" doesnt download notes", "problem": "Note Analyzer 'Download all notes' option does not download all required notes.", "solution": "1. Use the 'select all' option to download notes from sub-trees. 2. Check the versions of SAP notes and manually download any missing ones. 3. Verify and implement corrections for Digitally Signed Notes, Note 2869143.", "cause": "The 'Download all notes' feature in Note Analyzer fails to execute properly, resulting in incomplete downloads and incorrect tool status indications.", "processed_at": "2025-06-25T16:35:39.055330"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/23cddc8ac3de12d4b118f71ad001312c", "title": "Note Analyzer \"Download all notes\" doesnt download notes", "problem": "Note Analyzer incorrectly rates some notes as green despite them not being implemented.", "solution": "1. Manually download the notes indicated by the Note Analyzer. 2. Ensure the XML input file is correctly uploaded and executed.", "cause": "Note Analyzer tool failed to accurately assess and highlight the status of unimplemented notes, leading to incorrect traffic light indications.", "processed_at": "2025-06-25T16:35:39.055416"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e53d9e7c3a456d4ecd47d9f05013183", "title": "issue in Readiness Portal for App Availability - SV-SCS-S4R.", "problem": "Readiness Check Analysis does not populate certain sections/tiles as expected, particularly App Availability.", "solution": "1. Ask customer to provide Readiness Check data collection ZIP file. 2. Check provided ZIP file; Confirm usage_data.xml is empty. 3. Trigger new data collection using 'App Availability' option; Result remains empty. 4. Identify that SAP Note 2758146 version 118 contains bugs; Request customer to update to version 119. 5. Guide customer to apply version 120 of note 2758146. 6. Check usage data in ST03N; If needed, copy ST03N data from PRD to SB1 system using note 2568736.", "cause": "Known bugs in SAP Note 2758146 version 118 causing empty XML file during data collection.", "processed_at": "2025-06-25T16:35:45.105821"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e53d9e7c3a456d4ecd47d9f05013183", "title": "issue in Readiness Portal for App Availability - SV-SCS-S4R.", "problem": "Report TMW_RC_MANAGE_ST03N_DATA in PRD only pulls data for 4 weeks instead of 3 months.", "solution": "1. Confirm only 4 weeks of complete usage data present in PRD. 2. Recommend applying latest version of SAP Note 2758146 on PRD and run data collection. 3. Suggest executing readiness check in parts; App Availability can run directly in production system.", "cause": "Report downloads data on a weekly basis and weeks without data are excluded. Only 4 weeks of complete usage data available in PRD.", "processed_at": "2025-06-25T16:35:45.105839"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/85bb635e3b9212d40870d44a85e45a99", "title": "Error During Custom Code Analysis S/4 Hana 2023 Upgrade", "problem": "Error During Custom Code Analysis for S/4 Hana 2023 Upgrade due to missing check variant for ABAP test cockpit.", "solution": "Run the readiness check without custom code and verify if issue persists. Implement the corresponding ABAP test cockpit custom code check variant for the target release based on SAP Note 3365357.", "cause": "Missing check variant for the ABAP test cockpit for the target release in the system.", "processed_at": "2025-06-25T16:35:51.018781"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/85bb635e3b9212d40870d44a85e45a99", "title": "Error During Custom Code Analysis S/4 Hana 2023 Upgrade", "problem": "Authorization issue during the readiness check process.", "solution": "Follow option 2 in SAP Note 3396086 to upload Custom Code Analysis ZIP from non-prod system to prod system readiness check analysis.", "cause": "Authorization settings not properly configured for uploading custom code analysis from non-production to production systems.", "processed_at": "2025-06-25T16:35:51.018797"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8873326197869a54b86630200153af8c", "title": "SAP Readiness Check Report  issue", "problem": "SAP Readiness Check report not working correctly for non-production systems.", "solution": "Inform the customer that SAP Readiness Check is only supported for production systems as per SAP Note **********.", "cause": "Readiness Check is designed to support only production systems, not development or sandbox environments.", "processed_at": "2025-06-25T16:35:54.578517"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0c61b5f6eb125ad410affe540bd0cdd7", "title": "Dump CALL_FUNCTION_NOT_ACTIVE in report RC_COLLECT_ANALYSIS_DATA", "problem": "CALL_FUNCTION_NOT_ACTIVE dump occurs when executing RC_COLLECT_ANALYSIS_DATA report.", "solution": "Check if SAP note 2972792 has been applied correctly. Ensure prerequisites are met and reapply the note if necessary.", "cause": "/SDF/FDQ_CHECK_PREREQUISITES cannot be found in its function group, potentially due to missing or improperly implemented SAP note.", "processed_at": "2025-06-25T16:35:59.010056"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0c61b5f6eb125ad410affe540bd0cdd7", "title": "Dump CALL_FUNCTION_NOT_ACTIVE in report RC_COLLECT_ANALYSIS_DATA", "problem": "Inconsistency with function module /SDF/BPM_7X causing a different dump error.", "solution": "Repair the function group /SDF/BPM_7X using transaction SE37. Reimplement SAP note 2758146 in its latest version.", "cause": "Damaged function module related to readiness check Note 2758146.", "processed_at": "2025-06-25T16:35:59.010077"}]