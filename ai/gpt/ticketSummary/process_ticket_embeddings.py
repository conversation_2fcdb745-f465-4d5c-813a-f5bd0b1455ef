import json
import requests
import psycopg2
import uuid
from typing import List, Dict, Any

def get_embedding(texts: List[str]) -> requests.Response:
    """Generate embeddings for given texts using AI Core service"""
    base_url = "https://aicore-llm-proxy.internal.cfapps.eu12.hana.ondemand.com/"
    api_key = "sk-58f4db0abc225fb9fa70a80e71ec732bfd086076ddc44c83"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    data = {
        "model": "text-embedding-ada-002-2",
        "input": texts
    }

    return requests.post(f"{base_url}v1/embeddings",
                         headers=headers,
                         json=data)

def get_database_connection():
    """Establish connection to PostgreSQL database"""
    try:
        connection = psycopg2.connect(
            user="184024b4abe8",
            password="cce6e3ded99172321cf1dd7e681ae92",
            host="127.0.0.1",
            port="8861",
            database="CxgapCTAJQku"
        )
        return connection
    except (Exception, psycopg2.Error) as error:
        print(f"Error connecting to PostgreSQL database: {error}")
        return None

def verify_ticket_summary_table():
    """Verify that the ticket_summary table exists"""
    try:
        connection = get_database_connection()
        if connection is None:
            return False

        cursor = connection.cursor()

        # Check if table exists
        check_table_query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_name = 'ticket_summary'
        );
        """

        cursor.execute(check_table_query)
        table_exists = cursor.fetchone()[0]

        if table_exists:
            print("Table ticket_summary exists and is ready for use")
            return True
        else:
            print("Error: Table ticket_summary does not exist")
            return False

    except (Exception, psycopg2.Error) as error:
        print(f"Error checking table: {error}")
        return False
    finally:
        if connection:
            cursor.close()
            connection.close()

def insert_ticket_with_embedding(ticket_data: Dict[str, Any], embedding: List[float]) -> bool:
    """Insert a single ticket record with its embedding into the database"""
    try:
        connection = get_database_connection()
        if connection is None:
            return False

        cursor = connection.cursor()

        # Generate UUID for the record
        record_id = str(uuid.uuid4())

        # Insert query - matching the existing table structure
        postgres_insert_query = """
        INSERT INTO ticket_summary (id, url, title, problem, solution, cause, processed_at, embedding)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """

        record_to_insert = (
            record_id,
            ticket_data.get('url', ''),
            ticket_data.get('title', ''),
            ticket_data.get('problem', ''),
            ticket_data.get('solution', ''),
            ticket_data.get('cause', ''),
            ticket_data.get('processed_at', ''),
            embedding
        )

        cursor.execute(postgres_insert_query, record_to_insert)
        connection.commit()

        print(f"Record inserted successfully with ID: {record_id}")
        return True

    except (Exception, psycopg2.Error) as error:
        print(f"Failed to insert record: {error}")
        return False
    finally:
        if connection:
            cursor.close()
            connection.close()

def process_tickets_batch(tickets: List[Dict[str, Any]], batch_size: int = 10):
    """Process tickets in batches to generate embeddings and insert into database"""
    total_tickets = len(tickets)
    successful_inserts = 0
    failed_inserts = 0
    
    print(f"Processing {total_tickets} tickets in batches of {batch_size}")
    
    for i in range(0, total_tickets, batch_size):
        batch = tickets[i:i + batch_size]
        batch_problems = [ticket.get('problem', '') for ticket in batch]
        
        # Filter out empty problems
        valid_batch = [(ticket, problem) for ticket, problem in zip(batch, batch_problems) if problem.strip()]
        
        if not valid_batch:
            print(f"Batch {i//batch_size + 1}: No valid problems found, skipping...")
            continue
            
        valid_tickets, valid_problems = zip(*valid_batch)
        
        try:
            print(f"Batch {i//batch_size + 1}: Generating embeddings for {len(valid_problems)} problems...")
            
            # Generate embeddings for the batch
            response = get_embedding(list(valid_problems))
            
            if response.status_code == 200:
                embeddings_data = response.json()
                embeddings = [item['embedding'] for item in embeddings_data['data']]
                
                # Insert each ticket with its embedding
                for ticket, embedding in zip(valid_tickets, embeddings):
                    if insert_ticket_with_embedding(ticket, embedding):
                        successful_inserts += 1
                    else:
                        failed_inserts += 1
                        
                print(f"Batch {i//batch_size + 1}: Completed successfully")
                
            else:
                print(f"Batch {i//batch_size + 1}: Failed to generate embeddings. Status: {response.status_code}")
                failed_inserts += len(valid_tickets)
                
        except Exception as e:
            print(f"Batch {i//batch_size + 1}: Error processing batch: {e}")
            failed_inserts += len(valid_tickets)
    
    print(f"\nProcessing completed:")
    print(f"Successfully inserted: {successful_inserts}")
    print(f"Failed insertions: {failed_inserts}")
    print(f"Total processed: {successful_inserts + failed_inserts}")

def load_ticket_analysis_json(file_path: str) -> List[Dict[str, Any]]:
    """Load and parse the ticket analysis JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            tickets = json.load(file)
        print(f"Successfully loaded {len(tickets)} tickets from {file_path}")
        return tickets
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return []

def main():
    """Main function to process ticket analysis and generate embeddings"""
    print("Starting ticket analysis embedding process...")

    # Verify table exists
    print("Verifying ticket_summary table...")
    if not verify_ticket_summary_table():
        print("Table verification failed. Exiting.")
        return

    # Load ticket data
    json_file_path = "ticket_analysis_20250625.json"
    tickets = load_ticket_analysis_json(json_file_path)

    if not tickets:
        print("No tickets loaded. Exiting.")
        return

    # Process tickets and generate embeddings
    process_tickets_batch(tickets, batch_size=5)  # Smaller batch size for stability

    print("Ticket analysis embedding process completed!")

if __name__ == "__main__":
    main()
