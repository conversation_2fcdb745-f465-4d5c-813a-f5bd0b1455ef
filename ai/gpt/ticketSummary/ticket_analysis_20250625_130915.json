[{"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/91535e5c97a6da90b86630200153af17", "title": "can't upload sap readiness check", "problem": "Unable to upload new SAP Readiness Check and can only see recent analyses on the portal.", "solution": "- Verified if the \"manage authorization\" object is assigned to the user. - Checked whether other users have different access levels. - Provided Knowledge Base Article 0003310759 for reference on authorization requirements.", "cause": "Missing or insufficient authorization to upload or edit analyses on the SAP Readiness Check portal.", "processed_at": "2025-06-25T13:09:17.955296"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/48b020b8978c569030c531f71153af82", "title": "2758146 - Readiness Check Framework", "problem": "Issue with implementing SAP Note 2758146 related to SAP Readiness Check for SAP S/4HANA & SAP Signavio Process Insights, Discovery Edition.", "solution": "- Followed the solution part in KBA 0003469289. - Uploaded SAP Note 2758146 (version 119) correctly, resolving the implementation issue.", "cause": "The problem was due to an unknown field \"ABAP_TRUE\" in the program RC_VALUE_DISCOVERY_COLL_DATA not being recognized by the system, possibly due to missing data definitions.", "processed_at": "2025-06-25T13:09:22.138053"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/48b020b8978c569030c531f71153af82", "title": "2758146 - Readiness Check Framework", "problem": "Red Error in Display check result in Note Assistant when implementing SAP Note 2758146.", "solution": "- Utilized Note Assistant documentation (Note 875986) for SAP_BASIS up to 702. - Applied relevant fixes associated with ECC 6.00, ST-PI 2008_1 SP36, ST-A/PI 01W_700, SAPBASIS 70041.", "cause": "Compatibility issues with existing system components and unrecognized program fields during the Note implementation process.", "processed_at": "2025-06-25T13:09:22.138067"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5e08e84ac36c9ed0ca3f1c0bb00131e4", "title": "SAP Note 2758146 implementation failed due to errors", "problem": "SAP Note 2758146 implementation failed due to syntax errors.", "solution": "- Attempted to reproduce the issue by implementing the note on behalf of the customer. - Successfully implemented the note without errors from the support side. - Instructed the customer to proceed with further actions as the note was successfully implemented.", "cause": "Issue could not be reproduced from the support side.", "processed_at": "2025-06-25T13:09:25.757252"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/5e08e84ac36c9ed0ca3f1c0bb00131e4", "title": "SAP Note 2758146 implementation failed due to errors", "problem": "SAP Note 2745851 implementation resulted in syntax errors and incomplete implementation.", "solution": "- Steps 1-3 from SAP Note 3205320 were followed to address the syntax errors. - Despite de-implementation attempts, the report status indicated incomplete implementation. - Suggested raising a new case under the responsible component SV-SMG-MON-BPM-ANA for further assistance.", "cause": "The note was incompletely implemented even after de-implementation efforts, preventing further processing.", "processed_at": "2025-06-25T13:09:25.757267"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/49759016974c1250cdaf79171153afdd", "title": "The field \"PIT_DETAIL_LIST\" is unknown, but there is a field with the similar name \"PCT_DETAIL_LIST\".", "problem": "The field \"PIT_DETAIL_LIST\" is unknown, causing an error during the implementation of SAP note 2745851.", "solution": "- Implement the latest version (version 70) of SAP note 2745851 to resolve the issue. - Confirm that no errors appear during the implementation process.", "cause": "A syntax error in the previous version (version 69) of the SAP note 2745851, which did not recognize the field \"PIT_DETAIL_LIST\" correctly. The note version needed to be updated to address this discrepancy.", "processed_at": "2025-06-25T13:09:28.860886"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/759358a2c3f85a5cecd47d9f050131c9", "title": "FDQ service", "problem": "SAP FDQ service enablement issues due to unclear instructions regarding bootstrap TCI implementation.", "solution": "- Confirmed that bootstrap TCI implementation is not required as per the attached screenshot. - Advised to skip the steps in the PDF related to bootstrap TCI. - Instructed to proceed with the remaining steps from Note 0002502552 for SAP S/4HANA Conversion & Upgrade new Simplification Item Checks.", "cause": "Misinterpretation of the requirement to implement bootstrap TCI based on the initial instructions.", "processed_at": "2025-06-25T13:09:33.034796"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/759358a2c3f85a5cecd47d9f050131c9", "title": "FDQ service", "problem": "Confusion regarding preparation tasks described in the attachment \"TCI_for_Customer.pdf.\"", "solution": "- Provided guidance on the necessary actions for the preparation tasks. - Listed specific notes (2536585, 2606986, 26155270, 2569813) as non-critical for the current SAP Basis version (750 SP26). - Clarified that no action is needed on those notes if they are not applicable.", "cause": "Uncertainty about whether specific SAP notes are applicable or require action for the current system configuration.", "processed_at": "2025-06-25T13:09:33.034809"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/df15e707970896947bcf59081153afe7", "title": "Hana  Readiness report Job  Failure", "problem": "Hana Readiness report job RC_VALUE_DISCOVERY_COLL_DATA failed due to errors during data collection, delaying migration discovery and business case assessment.", "solution": "- Referenced SAP Note ##2748075 for resolution steps regarding the 'Error while data collection' in job log. - If unresolved, implement SAP Note ##3404710 for corrections in ST-A/PI 01V SP3 framework.", "cause": "The errors KFFI000202 and KFFI000302 during data collection suggest issues within the framework version of ST-A/PI used during the job execution.", "processed_at": "2025-06-25T13:09:37.148189"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/df15e707970896947bcf59081153afe7", "title": "Hana  Readiness report Job  Failure", "problem": "BUZ_SCENARIO_SIGNAVIO_PI_COL job finished successfully, but errors during data collection were still logged, indicating potential issues with data collection integrity despite job completion.", "solution": "- Ensure implementation of corrective measures outlined in SAP Note ##3404710, as these address framework-related errors potentially causing discrepancies in log outputs.", "cause": "Data collection errors persisted due to framework issues, as indicated by ST-A/PI Release logs and specific error codes, even though job execution was technically successful.", "processed_at": "2025-06-25T13:09:37.148202"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1763c06e3b129218d537962a85e45a21", "title": "Inconsistency in Open Items in SAP Readiness Check Report", "problem": "Inconsistency in Open Items in SAP Readiness Check Report.", "solution": "- The incident was assigned to the Readiness Check Support team (SV-SCS-S4R) for further analysis. - Requested additional details including selections made during the execution of RC_COLLECT_ANALYSIS_DATA, the analysis zip file, and the link to the analysis. - Forwarded the incident to the relevant team to continue with the analysis. - Awaiting feedback on whether an SAP Note needs to be applied and how to identify the actual documents in the system.", "cause": "Currently undetermined, further analysis required by the Readiness Check Support team. Note: The ticket does not indicate a final resolution or a definitive cause, suggesting ongoing investigation and communication with the customer.", "processed_at": "2025-06-25T13:09:40.535033"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bd98f991c3e09e9030037d9f05013163", "title": "Client roadmap for transformation to S/4 HANA Migration and plan on-prem HCM solutions to Cloud", "problem": "The client is seeking guidance on best practices for transforming their SAP environment, specifically migrating to S/4 HANA and moving on-premise HCM solutions to the cloud, while simultaneously addressing new enhancements and developments.", "solution": "- Recommend reviewing existing documentation on SAP transformation best practices. - Contact SAP Account Manager for consulting services. - Reference SAP Notes ********** and ********** for understanding support versus consulting services.", "cause": "The request involves consulting services beyond the scope of standard technical support.", "processed_at": "2025-06-25T13:09:45.009159"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/bd98f991c3e09e9030037d9f05013163", "title": "Client roadmap for transformation to S/4 HANA Migration and plan on-prem HCM solutions to Cloud", "problem": "Evaluation of enhancements and improvements to current on-premise HCM solutions during the transition phase to avoid potential conflicts or re-work.", "solution": "- Advise that enhancements should be considered within the context of the overall migration plan. - Address audit and risk concerns with specific enhancements. - Provide documentation: SAP Readiness Check and SAP Notes for further evaluation.", "cause": "Business requires assurance that enhancements will not conflict with the migration process.", "processed_at": "2025-06-25T13:09:45.009165"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cc814ed4c3ee929088b12875e0013164", "title": "Cannot View the Result of S4Hana Readiness Check", "problem": "Cannot view the result of the S4Hana Readiness Check after uploading the report into the SAP Readiness URL.", "solution": "- Verified the accessibility of the SAP Readiness URL. - Recommended checking Knowledge Base Article (KBA) 3330283 for SAP Readiness Check access issues. - The customer was able to access the URL. - Suggested re-uploading the report file and verified it appeared in the \"Recent Analyses\" section. - Confirmed the report status changed to \"Available\".", "cause": "The URL was initially inaccessible to the user, preventing the results from being displayed.", "processed_at": "2025-06-25T13:09:48.694980"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cc814ed4c3ee929088b12875e0013164", "title": "Cannot View the Result of S4Hana Readiness Check", "problem": "\"Add-On Compatibility\" and \"Active Business Functions\" tiles are empty in the SAP Readiness Check results.", "solution": "- Directed the customer to check KBA 0002847830, which addresses the issue of empty tiles for \"Add-On Compatibility\" and \"Active Business Functions\".", "cause": "Specific technical issues related to the Readiness Check tiles not displaying data, as outlined in the referenced KBA.", "processed_at": "2025-06-25T13:09:48.695007"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/06c09cfe3b5cd2d00870d44a85e45a41", "title": "SAP Readiness Check", "problem": "The catalog of target SAP S/4HANA versions in the report RC_COLLECT_ANALYSIS_DATA was not updated, causing issues in running the SAP Readiness Check.", "solution": "- Consulted SAP Note 2951527 which addresses the issue of an empty dropdown list for target releases. - Recommended performing the steps outlined in the Knowledge Base Article 0002951527 to update the target version list.", "cause": "The connectivity to SAP using RFC destination SAPOSS was shut down as part of the Support Backbone Update, impacting the ability to fetch updated target version catalogs.", "processed_at": "2025-06-25T13:09:52.776843"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/06c09cfe3b5cd2d00870d44a85e45a41", "title": "SAP Readiness Check", "problem": "The RC_COLLECT_ANALYSIS_DATA report does not display the same product version as intended for the upgrade to SAP S/4HANA 2022 SPS03.", "solution": "- Advised using the Simplification Item Check (SIC) instead, as detailed in SAP Note 0002399707. - Provided guidance based on SAP Note 0003059197 that the report is designed for upgrading to a new product version, not displaying the existing version.", "cause": "The RC_COLLECT_ANALYSIS_DATA report is specifically designed for upgrading to a new product version, not for displaying the current version in the system upgrade context.", "processed_at": "2025-06-25T13:09:52.776860"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/faa7261a475e5e101cccb7da216d438a", "title": "B4HANA 2023 readiness check ABAP Dump", "problem": "Execution of SAP Job RC_BW_4_HANA:NEW_CODE_ANALYSIS results in ABAP dump due to runtime error TSV_TNEW_PAGE_ALLOC_FAILED.", "solution": "- Implement SAP Note 3396444 to adjust the filtering methodology. - Enhanced exclusion list of the code scan collector to ignore /SNP/ and /RSC/ objects causing high memory consumption. - Re-run the job after implementing the note.", "cause": "Excessive memory consumption due to large interdependencies within /SNP/ objects, leading to runtime error.", "processed_at": "2025-06-25T13:09:57.443777"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/faa7261a475e5e101cccb7da216d438a", "title": "B4HANA 2023 readiness check ABAP Dump", "problem": "Timeout error when logging into the BWD system, preventing access.", "solution": "- Ensure connections are opened and stable. - Request access to the production client and valid logon credentials.", "cause": "Network interface error causing timeout during connection attempt.", "processed_at": "2025-06-25T13:09:57.443794"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/faa7261a475e5e101cccb7da216d438a", "title": "B4HANA 2023 readiness check ABAP Dump", "problem": "Format \"X_PAPER\" not defined for output device \"$HH01\" during job execution.", "solution": "Review and define the correct format for output devices in the system settings as needed.", "cause": "Misconfiguration or missing format definition in system settings for output devices.", "processed_at": "2025-06-25T13:09:57.443797"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fff75dd4eb269e507d05f7510bd0cd8a", "title": "Program Error - TSV_TNEW_PAGE_ALLOC_FAILED", "problem": "The program RC_COLLECT_ANALYSIS_DATA in SAP ECC Production System (EP0) fails with a short dump TSV_TNEW_PAGE_ALLOC_FAILED, causing job cancellations.", "solution": "- Review the central SAP notes associated with SAP Readiness Check. - Ensure all notes are updated to the latest versions as per KBA 2913617. - Update the following SAP notes to their latest versions: - Note 2758146: Update to version 119. - Note 2399707: Update to version 167. - Note 2502552: Update to version 109. - Re-run the RC_COLLECT_ANALYSIS_DATA job after updating the notes.", "cause": "Central notes were not up-to-date, leading to the failure of RC_COLLECT_ANALYSIS_DATA when the minimum note version was not met. Final Outcome: The case was auto closed after providing guidance on updating central notes, indicating resolution steps were suggested but not confirmed by the customer within the ticket.", "processed_at": "2025-06-25T13:10:01.659788"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b9a7fb08c3eade5088b12875e001317a", "title": "S4 *PERSON* Process - ST22 Dump", "problem": "ST22 dump occurs when running the SAP Readiness Check or SAP Signavio Process Insights, Discovery Edition, specifically related to the ABAP program /SDF/CL_S4RC20_FACTORY.", "solution": "- Refer to SAP Note 2981833 for error resolution in the program. - Reset and implement SAP Note 2745851 again, ensuring the latest version is used. - Forwarded the issue to the correct component SV-SCS-S4R for further assistance.", "cause": "ABAP programming error related to OBJECTS_OBJREF_NOT_ASSIGNED exception, causing CX_SY_REF_IS_INITIAL error in the specified program.", "processed_at": "2025-06-25T13:10:06.281406"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b9a7fb08c3eade5088b12875e001317a", "title": "S4 *PERSON* Process - ST22 Dump", "problem": "Incorrect component selection for handling the issue initially, causing delays in addressing the ST22 dump.", "solution": "- Identified the wrong component as BPI-SIG-PM-EXP and corrected it to SV-SCS-S4R. - Forwarded the ticket to the appropriate team for resolution.", "cause": "Misclassification of the problem component, as the initial choice did not align with the nature of the error related to SAP Readiness Check.", "processed_at": "2025-06-25T13:10:06.281415"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7b558c45c32c1290ca3f1c0bb00131bf", "title": "background job get cancelled", "problem": "Background job cancellation due to SQL error 1205 when accessing table FIN_AA_CORR_PACK.", "solution": "- Identified SQL error 1205 as a deadlock issue. - Suggested ensuring no other applications are accessing the table FIN_AA_CORR_PACK. - Recommended stopping any old instances of the Readiness Check or Financial Reconciliation that might be causing the lock. - Waited for the data collection process to complete due to the large volume of data (12 years' worth).", "cause": "Table FIN_AA_CORR_PACK was locked by another application, leading to a deadlock situation.", "processed_at": "2025-06-25T13:10:10.495835"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7b558c45c32c1290ca3f1c0bb00131bf", "title": "background job get cancelled", "problem": "SAP standard report RC_COLLECT_ANALYSIS_DATA causing ABAP dump with SQL error 1205.", "solution": "- Reduced the number of parallel processes on RC_COLLECT_ANALYSIS_DATA to mitigate deadlock occurrences. - Reviewed relevant SAP notes (e.g., note 111291) related to MSSQL deadlock issues. - Forwarded the case to component SV-SCS-S4R for further investigation.", "cause": "Potential issues with the new version of RC_COLLECT_ANALYSIS_DATA behaving differently on current ECC 606 (NW 7.31 SP Level 17) on MSSQL server.", "processed_at": "2025-06-25T13:10:10.495851"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2f2329924724ded036bf68be436d4395", "title": "High runtime readiness report", "problem": "Readiness Check for Financial Data Quality running for more than 21 days.", "solution": "- Confirmed that long running time is expected due to high volume of financial data as per SAP Note 2913617. - Suggested running more parallel jobs by splitting company code and fiscal year combinations, as per KBA 3423140. - Recommended scheduling one fiscal year per job to improve performance. - Advised to check implementation status of SAP Note 2972792 in the latest version. - Provided steps to verify note implementation using RTCCTOOL: - Navigate to SE38 and open RTCCTOOL. - Go to SETTINGS and SELECT FDQ SCENARIO. - Ensure notes 3130905 and 3374323 show a green light. - Proposed scheduling Financial Data Quality check separately to expedite Readiness Check.", "cause": "The large volume of financial data being processed, making the long runtime expected. This structured response highlights the main problem, solution steps, and underlying cause, providing clear guidance to the customer on addressing the long runtime issue with the readiness report.", "processed_at": "2025-06-25T13:10:14.479602"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/31998f083bead2900870d44a85e45ac8", "title": "Not able to take <PERSON><PERSON><PERSON> report", "problem": "SPIDE report cannot be generated due to failure/dumping of the background job RC_VALUE_DISCOVER_COLL_DATA.", "solution": "- Verified the environment details including system version and ID. - Requested access to the managed system ASP to investigate further. - Ensured valid logon credentials and access to the production client. - Reviewed attachments provided by the customer and documentation followed.", "cause": "The background job RC_VALUE_DISCOVER_COLL_DATA scheduled for SPIDE data collection is failing, possibly due to system configuration or scheduling errors.", "processed_at": "2025-06-25T13:10:17.945357"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/31998f083bead2900870d44a85e45ac8", "title": "Not able to take <PERSON><PERSON><PERSON> report", "problem": "Timeout error when attempting to log onto system ASP.", "solution": "- Attempted to log onto system ASP, encountering a network interface timeout. - Suggested ensuring working connections and involving network colleagues for assistance.", "cause": "Network interface timeout during logon, potentially due to connectivity issues or network configuration errors.", "processed_at": "2025-06-25T13:10:17.945363"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9128cc9ac3c45e14d1371c3ed40131a9", "title": "SAP Note 2758146 implementation failed due to errors", "problem": "SAP Note 2758146 implementation failed due to errors.", "solution": "- Confirmed the case was duplicated with case number 577772/2024. - Redirected resolution efforts to the original case (577772/2024). - Customer was advised to close the duplicate case for documentation purposes.", "cause": "The issue was a duplicated case entry, referring to the same problem already being handled under case 577772/2024.", "processed_at": "2025-06-25T13:10:21.016057"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2078f324c36a1e1030037d9f05013176", "title": "DTEL  /SDF/FDQ_CHAR8  Deleted.", "problem": "Unable to activate the function group /SDF/FDQ_ASSESSMENT due to missing data element DTEL /SDF/FDQ_CHAR8.", "solution": "- Implement SAP Note 0002972792 to address the missing data element issue. - Perform a recheck of the system behavior post-note implementation.", "cause": "The data element /SDF/FDQ_CHAR8 was deleted, causing a syntax error in the function group /SDF/FDQ_ASSESSMENT, preventing activation.", "processed_at": "2025-06-25T13:10:24.804703"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2078f324c36a1e1030037d9f05013176", "title": "DTEL  /SDF/FDQ_CHAR8  Deleted.", "problem": "Go Live Delay due to inability to proceed with SPAU adjustments.", "solution": "- Requested SAP support for analysis and proper team assignment to address the issue. - Incident was initially opened in the wrong component area and reassigned for proper handling.", "cause": "The incident was opened under an incorrect component, delaying resolution and impacting the SPAU process.", "processed_at": "2025-06-25T13:10:24.804710"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/552ec3dbc3d21a10ecd47d9f0501316b", "title": "SAP Readiness check error", "problem": "SAP Readiness Check data collection fails due to a dump error \"CONVT_NO_NUMBER\" in the development system ERX.", "solution": "- Requested and received access to the Managed System ERX. - Implemented SAP Note 2919704 addressing the termination of the Background Job due to error messages from underlying APIs. - Retriggered the data collection using the report RC_COLLECT_ANALYSIS_DATA. - Observed the job TMW_RC_FDQ_DATA_COLL running successfully without issues after implementation.", "cause": "The failure was caused by an ABAP programming error resulting in a conversion issue, where a non-numeric value was attempted to be converted to a number, leading to runtime error CONVT_NO_NUMBER. Final Outcome: The SAP Note implementation resolved the issue, allowing the Readiness Check data collection to proceed successfully in the development system. The ticket remains open to ensure smooth transition to the production system.", "processed_at": "2025-06-25T13:10:28.409922"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/38387f88c3eade5088b12875e00131c9", "title": "SAP S/4 HANA Readiness check tile shows an error", "problem": "Unable to view tiles on SAP S/4 HANA Readiness Check page in the SAP Support Portal.", "solution": "- Investigate the issue by checking known KBAs, specifically Note 3330283. - Recommend using an anonymous tab or a different browser to access the platform. - Confirm if the issue affects other users or is specific to one machine. - Request screenshots of the issue for further analysis. - Inform the customer that the issue is likely caused by the web browser blocking Third-Party Cookies.", "cause": "The web browser used is blocking Third-Party Cookies, preventing the display of tiles on the Readiness Check page.", "processed_at": "2025-06-25T13:10:31.535707"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4331e3883b2616900870d44a85e45a7a", "title": "Custom Code ZIP can not be uploaded to Redinesscheck", "problem": "Unable to upload Custom Code ZIP file to SAP Readiness Check, with the ZIP file marked as invalid when Custom Code Analysis is included.", "solution": "- Refer to SAP Note ##3059197 for performing Readiness Check data collection without the Custom Code Analysis activated. - Create and upload the ZIP file to SAP for Me for initial analysis. - Retrigger Readiness Check with only Custom Code Analysis activated and use the \"Update Analysis\" button in SAP for Me dashboard. - Confirm that prerequisites are correctly configured and SAP Notes are implemented in their latest versions, especially SAP Note ##3293011. - Rerun the Custom Code Data collection and provide the new ZIP file.", "cause": "The ZIP file was created based on outdated coding and SAP Note ##3293011 was not entirely up to date, leading to an invalid ZIP file for the Readiness Check.", "processed_at": "2025-06-25T13:10:35.227540"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/108f1926474012901d1ff2b5536d435d", "title": "Getting the Error while running the report \"RC_COLLECT_ANALYSIS_DATA\"", "problem": "Error encountered when running the report \"RC_COLLECT_ANALYSIS_DATA\".", "solution": "- Verified prerequisites and uploaded required SAP notes. - Checked SAP Note 3061414 for prerequisites related to integration. - Identified the ST-A/PI version mismatch; customer needs version 01U* SP02. - Recommended checking SAP Note 3240048 for downloading the correct ST-A/PI version.", "cause": "The current ST-A/PI version (01T_731 0003 SAPKITAB9X) does not fulfill the integration prerequisites as per SAP Note 3107511.", "processed_at": "2025-06-25T13:10:39.192670"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/108f1926474012901d1ff2b5536d435d", "title": "Getting the Error while running the report \"RC_COLLECT_ANALYSIS_DATA\"", "problem": "Clarification needed on business impact related to report execution error.", "solution": "- Adjusted priority from Very High to High as the situation does not qualify for system down or critical business impact. - Requested customer to provide detailed business impact information, including Go Live details if applicable.", "cause": "Miscommunication or misunderstanding of the priority level and business impact associated with the error.", "processed_at": "2025-06-25T13:10:39.192676"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7c3643fdc30ed29430037d9f0501314b", "title": "Ejecución reporte Process Discovery", "problem": "Execution of the Readiness Check report RC_COLLECT_ANALYSIS_DATA job is failing, resulting in a canceled job and subsequent errors.", "solution": "- Exclude KPI KSD000306 from the execution of the report as per KBA 0002897369. - The resolution steps outlined in KBA 0002897369 were followed to address the issue with the TMW_RC_BPA_DATA_COLL job.", "cause": "The job cancellation was due to a process termination error related to heap memory limits being exceeded, as shown in the system logs. Specifically, the issue was with the KPI KSD000306 during processing.", "processed_at": "2025-06-25T13:10:44.776925"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7c3643fdc30ed29430037d9f0501314b", "title": "Ejecución reporte Process Discovery", "problem": "Execution of Process Discovery report RC_VALUE_DISCOVERY_COLL_DATA job BUZ_SCENARIO_SIGNAVIO_PI_COL fails after 3 hours, resulting in system exceptions and memory errors.", "solution": "- Increase the parameter abap/heaplimit from 100Mb to 2Gb, and then to 4Gb. - Implement SAP Notes 3393415 and 3404710 for relevant corrections.", "cause": "The job cancellation was caused by exceeding heap memory limits, as indicated by the error codes and logs provided. The errors relate to database internal operations and memory allocations exceeding the set limits.", "processed_at": "2025-06-25T13:10:44.776940"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/4b51eed5c324da904daa2875e0013112", "title": "Urgent: <PERSON><PERSON><PERSON> Encountered in S/4 Readiness Chec", "problem": "Error encountered during S/4 readiness check due to missing prerequisite SAP Note 2811183 for Business Partner.", "solution": "- De-implement SAP Note 2758146 completely. - Re-implement SAP Note 2758146 in its latest version. - Refer to SAP Readiness Check troubleshooting guide in KBA 2968380 for further steps. - Ensure correct check boxes are selected during implementation as outlined in the troubleshooting guide.", "cause": "Old coding not considered during the implementation/update of SAP Note 2758146, leading to compatibility issues with the prerequisite Note 2811183.", "processed_at": "2025-06-25T13:10:48.164283"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dfc7a2b23b1e1ed40870d44a85e45ae3", "title": "Custom Code Analysis tab is not getting updated", "problem": "Custom Code Analysis tab is not getting updated after running the Readiness Check Analysis in SAP for Me.", "solution": "- Verify the Readiness Check data collection and Custom Code data collection files. - Ensure both required files (\"RC2AnalysisData*\" and \"S4HMigrationRepositoryInfo*\") are correctly uploaded. - Refer to SAP Note 0002913617 for the correct procedure. - Re-upload the necessary files to the portal.", "cause": "Incorrect or missing file uploads required to update the Custom Code Analysis.", "processed_at": "2025-06-25T13:10:51.942500"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/dfc7a2b23b1e1ed40870d44a85e45ae3", "title": "Custom Code Analysis tab is not getting updated", "problem": "Uncertainty regarding the necessity of uploading both Readiness Check and Custom Code files for subsequent system updates.", "solution": "- Confirmed that both files are required for the update process. - Customer advised to continue uploading both files for future operations.", "cause": "Misunderstanding of the file upload requirements for successful updates.", "processed_at": "2025-06-25T13:10:51.942506"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a36e85b3c31ad25030037d9f05013105", "title": "SAP Process insight discovery report generation issue", "problem": "SAP Process insight discovery report generation issue.", "solution": "- Implement SAP Notes 2758146 and 2745851 as recommended. - Upgrade the ST-A/PI version to 01V SP 00 or higher. - Run the data extraction report RC_VALUE_DISCOVERY_COLL_DATA in SA38. - Download the analysis file from the completed job.", "cause": "The customer was using an outdated ST-A/PI version (01U_731) which is not recommended for running the report, leading to errors and difficulty accessing prerequisite documents for the report generation.", "processed_at": "2025-06-25T13:10:55.833067"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/a36e85b3c31ad25030037d9f05013105", "title": "SAP Process insight discovery report generation issue", "problem": "Inquiry about executing reports on ST-A/PI version 01U_731.", "solution": "- Advised not to run the report on ST-A/PI version 01U_731 due to potential errors. - Recommended upgrading to version 01V to avoid issues and improve efficiency.", "cause": "Running reports on earlier ST-A/PI versions (01U_731) can lead to technical errors due to incompatibility with newer report requirements.", "processed_at": "2025-06-25T13:10:55.833081"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/cb8e0408472ed6501cccb7da216d4359", "title": "Unable to Execute RC_COLLECT_ANALYSIS_DATA Due to Uneditable Target S/4HANA Version Field", "problem": "The customer is unable to execute the RC_COLLECT_ANALYSIS_DATA program for the SAP Readiness Check due to the \"Target S/4HANA Version\" field being uneditable.", "solution": "- Refer to SAP KBA 2951527, which addresses the issue of an empty dropdown list for the target release S/4HANA or BW/4HANA. - Follow the steps outlined in the solution section of the KBA to populate the dropdown list. - Recheck the situation after implementing these steps to ensure the field becomes editable.", "cause": "The dropdown list for the target release is not populated, preventing the selection of a target release in the readiness check.", "processed_at": "2025-06-25T13:10:59.485684"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/9bc54809c32c1290ca3f1c0bb0013150", "title": "RC for S/4HANA encounter error: Function /SDF/READ_HDB_SIZING_RESULTS_2 does not exists - Install SAP Note 1872170 first", "problem": "Error encountered when executing SAP Readiness Check for S/4HANA: \"Function /SDF/READ_HDB_SIZING_RESULTS_2 does not exist.\"", "solution": "- Implement SAP Note 1872170 as advised. - Ensure SAP Notes 2913617 and 2758146 are implemented on the latest version. - Verify written instructions on these notes for correct implementation. - Keep R/3 connection open for further analysis if required.", "cause": "SAP Note 1872170 was not fully effective or additional notes were required for resolution. The system might have been on an incompatible version or missing necessary updates.", "processed_at": "2025-06-25T13:11:03.513254"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/79cee59feb441694a95ff847bad0cd76", "title": "When should the “readiness check” best be carried out?", "problem": "Timing for executing the “Readiness Check - simplification item check” in the S/4HANA conversion process.", "solution": "- Recommended to run the Readiness Check after Unicode conversion and before starting the S/4HANA conversion. - Ensures correct DB/OS version and addresses simplification items early.", "cause": "Uncertainty about the optimal timing in the conversion sequence to ensure a smooth transition to S/4HANA.", "processed_at": "2025-06-25T13:11:07.335138"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/79cee59feb441694a95ff847bad0cd76", "title": "When should the “readiness check” best be carried out?", "problem": "The misrouting of the case to the incorrect component within SAP Support.", "solution": "- Confirmation needed from the correct support team for further analysis. - Opened a case to Development for further investigation.", "cause": "Misrouting led to delays in obtaining the correct guidance for the readiness check timing.", "processed_at": "2025-06-25T13:11:07.335157"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/18ede8ef3b1e9a10d537962a85e45a36", "title": "SAP Readiness Check for SAP S/4HANA note - 2913617 Cannot be implemented", "problem": "The customer is unable to implement SAP Note 2913617 using the SNOTE transaction, as it is showing a status of \"Cannot be implemented.\"", "solution": "- Clarified that SAP Note 2913617 provides guidance and cannot be implemented directly. - Directed the customer to review the roadmap and implement necessary notes to run the SAP Readiness Check. - Advised checking prerequisites and manual steps in each note to avoid issues. - Suggested referring to KBA 2968380 for troubleshooting guidance or opening a new case for additional support.", "cause": "SAP Note 2913617 is a guidance note and not meant to be implemented directly; it serves as orientation for SAP Readiness Check.", "processed_at": "2025-06-25T13:11:11.510276"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/18ede8ef3b1e9a10d537962a85e45a36", "title": "SAP Readiness Check for SAP S/4HANA note - 2913617 Cannot be implemented", "problem": "After implementing several notes mentioned in SAP Note 2913617 and running the program RC_COLLECT_ANALYSIS_DATA, the customer is not getting lists against \"Target S/4HANA Version.\"", "solution": "- Proposed review of prerequisites and manual steps to ensure proper implementation. - Recommended consulting KBA 2968380 for troubleshooting specific issues related to the target version during the readiness check execution.", "cause": "The customer may not have followed all prerequisite steps or manual actions required by the implemented notes, resulting in incomplete readiness check results.", "processed_at": "2025-06-25T13:11:11.510289"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/98dd206bc3d6da103331583bb00131d7", "title": "SAP Readiness Check for SAP S/4HANA note - 2913617 Cannot be implemented", "problem": "SAP Note 2913617 cannot be implemented in SAP S/4HANA Readiness Check, displaying \"Cannot be implemented\" in SNOTE.", "solution": "- Confirm that SAP Note 2913617 is a guidance note, not meant for direct implementation. - Inform the customer that this note provides orientation for running the SAP Readiness Check. - Direct the customer to review the prerequisites and manual steps in the related notes for implementation guidance. - Suggest referring to Knowledge Base Article 2968380 for troubleshooting guidance.", "cause": "The note is intended for orientation and guidance, not direct implementation.", "processed_at": "2025-06-25T13:11:15.414019"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/98dd206bc3d6da103331583bb00131d7", "title": "SAP Readiness Check for SAP S/4HANA note - 2913617 Cannot be implemented", "problem": "Customer not receiving lists against \"Target S/4HANA Version\" after implementing related notes and running RC_COLLECT_ANALYSIS_DATA.", "solution": "- Verify that all prerequisites and steps mentioned in the guidance note and related notes are followed correctly. - Ensure the customer runs the SAP Readiness Check as per the guidance provided in the note. - Recommend checking troubleshooting guidance in KBA 2968380 for issues related to the target version.", "cause": "Improper implementation or execution steps, possibly missing prerequisites or incorrect setup steps as outlined in the guidance note.", "processed_at": "2025-06-25T13:11:15.414030"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c8fb19003b891adc0870d44a85e45a1b", "title": "SAP readiness check: Business Process Discovery error", "problem": "Business Process Discovery analysis in SAP Readiness Check not populated as expected.", "solution": "- Requested customer to provide Readiness Check data collection ZIP file. - Reviewed ZIP file and found no issues; requested access to the Managed System. - Access granted; identified bug in Readiness Check portal. - Communicated with development team; bug fixed. - Business Process Discovery analysis successfully populated.", "cause": "A bug in the SAP Readiness Check portal prevented the Business Process Discovery analysis from being populated with valid data.", "processed_at": "2025-06-25T13:11:19.691250"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c8fb19003b891adc0870d44a85e45a1b", "title": "SAP readiness check: Business Process Discovery error", "problem": "Unable to log onto the PED system due to network timeout error.", "solution": "- Attempted to log onto PED system but encountered a timeout error. - Informed customer to ensure connections are open and working, and logon credentials are maintained in the remote logon depot. - Customer re-opened SAP remote connection using SAP router \"SCC-AWSRTAP01\".", "cause": "Closed connections and missing logon credentials in the remote logon depot leading to timeout errors during system access.", "processed_at": "2025-06-25T13:11:19.691264"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Unable to run SAP Readiness check report due to outdated SAP Notes in the system.", "solution": "- Refer to SAP Note 2913617 for required SAP Notes. - Check and implement all prerequisite SAP Notes. - Ensure SAP Notes are updated to their latest versions. - De- and re-implement SAP Notes if necessary, especially note 2745851.", "cause": "SAP Notes not up to date, causing errors when executing readiness check reports.", "processed_at": "2025-06-25T13:11:27.473066"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Readiness Check data collection attempts failed with memory-related errors (PXA_NO_FREE_SPACE).", "solution": "- Investigate memory bottlenecks in the system. - Monitor system memory allocation and usage during data collection. - Address memory allocation issues to prevent future errors.", "cause": "Memory bottleneck leading to PXA_NO_FREE_SPACE error during data collection.", "processed_at": "2025-06-25T13:11:27.473073"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Custom Code Analysis section shows blank results after running readiness check.", "solution": "- Refer to SAP Notes 2185390 and 2577424 for guidance on running related programs. - Execute program SAPRSEUB and ensure it is run in the correct system environment (DEV or PRD). - Run program SYCM_DOWNLOAD_REPOSITORY_INFO for custom code analyser details.", "cause": "Uncertainty about program execution environment and potentially missing configuration steps.", "processed_at": "2025-06-25T13:11:27.473074"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Dropdown list for target release in readiness check report is empty.", "solution": "- Ensure RFC - SAP-SUPPORT_PORTAL is functional. - Follow SAP Note 2951527 to resolve dropdown list issues. - Verify system configurations and connections to ensure data synchronization.", "cause": "Non-functional RFC connection preventing data sync for dropdown list options.", "processed_at": "2025-06-25T13:11:27.473075"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e483e3dc3c6da14553db11f05013174", "title": "Unable to run SAP Readiness check report for S4HANA", "problem": "Incomplete implementation of SAP Note 2502552 causing errors.", "solution": "- Follow instructions in SAP Note 2502552 carefully for complete implementation. - Check dependency notes such as 2187425 and 3304656. - Use transaction SNOTE for note implementation and troubleshooting.", "cause": "Incomplete implementation of SAP Note 2502552 due to missing steps or dependencies.", "processed_at": "2025-06-25T13:11:27.473077"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b4fdc75bc3d21a10ecd47d9f050131f6", "title": "Technical Fit Questionnaire", "problem": "The customer is unable to contact their Account Manager to fill out a Technical Fit Questionnaire required for renewal due to bounced emails and no response.", "solution": "- Customer attempted to contact the Account Manager via provided email addresses, but emails bounced. - SAP Support advised the customer to contact a specific person directly who might assist with the issue. - A Swarm session was created to investigate the case internally.", "cause": "The email for the Account Manager was incorrect or outdated, and the customer was unable to reach the appropriate contact for assistance.", "processed_at": "2025-06-25T13:11:31.569879"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b4fdc75bc3d21a10ecd47d9f050131f6", "title": "Technical Fit Questionnaire", "problem": "The customer is unsure about the routing/escalation process for their request regarding the Technical Fit Questionnaire.", "solution": "- SAP Support informed the customer about the specific component they reached (SV-SCS-S4R) and noted its focus on SAP Readiness Check. - SAP Support promised to find out who could assist with the customer's request and to provide an update once available. - SAP Support suggested the customer might need to raise a new case with a different function/component for escalation.", "cause": "Misalignment between the customer’s request and the SAP component they initially contacted, leading to uncertainty on the correct escalation path.", "processed_at": "2025-06-25T13:11:31.569891"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fe8c79e5c38612944daa2875e00131ed", "title": "SAP Readiness Check for SAP SuccessFactors Solutions", "problem": "Unable to implement SAP Note 3297478 in the production system, encountering errors during execution.", "solution": "- Confirmed that the error during SAP Note 3297478 implementation is due to the missing RC_HCM folder, affecting only the XSL files in the result zip file, but not the analysis result itself. - Advised the customer to run the report RC_HCM_COLLECT_ANALYSIS_DATA successfully, except for interfaces, which are affected by the missing folder.", "cause": "Missing RC_HCM folder leading to partial implementation error.", "processed_at": "2025-06-25T13:11:36.086473"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fe8c79e5c38612944daa2875e00131ed", "title": "SAP Readiness Check for SAP SuccessFactors Solutions", "problem": "Unable to implement SAP Note 3193560 due to version mismatch; pilot note 3297478 released to address this issue.", "solution": "- Suggested de-implementing pilot note 3297478 and using the officially released note 3193560. - Ensured necessary authorizations (SNOTE and SE80) were provided for further checks.", "cause": "Version mismatch preventing the implementation of SAP Note 3193560.", "processed_at": "2025-06-25T13:11:36.086486"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/fe8c79e5c38612944daa2875e00131ed", "title": "SAP Readiness Check for SAP SuccessFactors Solutions", "problem": "Unable to execute report RC_HCM_COLLECT_ANALYSIS_DATA due to outdated ST-A/PI version.", "solution": "- Identified that the current version of ST-A/PI in the system is lower than the required ST-A/PI 01U* SP02. - Recommended updating ST-A/PI to the latest available version in the marketplace, ST-A/PI 01V_731.", "cause": "Outdated ST-A/PI version in the system preventing report execution.", "processed_at": "2025-06-25T13:11:36.086489"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e2a2abeeb449694b6ebf1c6cad0cd31", "title": "SPIDE Report Issue", "problem": "The customer was unable to generate the SPIDE Report, and the process was hanging, taking longer than expected.", "solution": "- SAP support identified that the issue was a duplication of another case (371498/2024). - The customer was advised to close the current ticket and follow up on the original case (371498/2024). - The customer was requested to open an R3 connection for remote system checks. - SAP support triggered a notification to the developer assigned to the original case to expedite resolution.", "cause": "Duplicate case raised under the wrong component, delaying the resolution process.", "processed_at": "2025-06-25T13:11:40.289008"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e2a2abeeb449694b6ebf1c6cad0cd31", "title": "SPIDE Report Issue", "problem": "Miscommunication due to raising the incident under the wrong component.", "solution": "- Customer acknowledged the mistake and decided to continue under the correct case number (7675772/2024). - SAP support confirmed that the original case (371498/2024) was already in the development support team's queue.", "cause": "The incident was initially raised under an incorrect component, leading to confusion and delays in addressing the issue.", "processed_at": "2025-06-25T13:11:40.289031"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ac8b88d2c312d694553db11f050131cf", "title": "Note 2913617 - One Upload not valid, other S/4 Uploads are correct.", "problem": "Invalid upload of Readiness Check Analysis including Custom Code Analysis in SAP for Me.", "solution": "- Ask the customer to provide separate Readiness Check data collection and Custom Code Analysis ZIP files. - Verify the provided ZIP files. - Identify that the customer did not perform the Readiness Check data collection and Custom Code Analysis separately. - Advise the customer to perform separate data collections as per SAP Note 3059197. - Customer followed instructions from Note 3059197 to select only the Custom Code Analysis option and schedule the analysis.", "cause": "The customer combined Readiness Check data collection and Custom Code Analysis data collection in one file, resulting in an invalid file.", "processed_at": "2025-06-25T13:11:44.019497"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f418f58dc3e1dad030037d9f050131c4", "title": "runtime error while processing report RC_VALUE_DISCOVERY_COLL_DATA fora SAP Signavio Process Insights.", "problem": "The report RC_VALUE_DISCOVERY_COLL_DATA for SAP Signavio Process Insights resulted in a runtime error TSV_TNEW_PAGE_ALLOC_FAILED due to insufficient memory allocation for an internal table.", "solution": "- Reviewed and adjusted memory-related parameters such as `abap/heap_area_dia`, `abap/heap_area_nondia`. - Removed the PPI causing the memory dump from the data collection as per SAP Note 2818045. - Implemented the latest version (117) of SAP Note 2758146.", "cause": "The memory allocation limits were reached while processing the report, causing insufficient memory for internal table operations.", "processed_at": "2025-06-25T13:11:48.480453"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/f418f58dc3e1dad030037d9f050131c4", "title": "runtime error while processing report RC_VALUE_DISCOVERY_COLL_DATA fora SAP Signavio Process Insights.", "problem": "After resolving the initial memory dump issue, the job ends with a system-exception error message indicating no Business Process Analytics data returned from `/SDF/BPM_7X_S4RC20_GET_DATA`.", "solution": "- Patched the component ST-A/PI 01V to the latest version. - Implemented the latest version (117) of SAP Note 2758146, which was recommended in SAP Signavio Process Insights troubleshooting guide.", "cause": "The implementation of SAP Note 2758146 version 116 was outdated, necessitating an upgrade to version 117. The patching of component ST-A/PI 01V was needed to address compatibility issues.", "processed_at": "2025-06-25T13:11:48.480466"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/077cc166c3d61ad4ecd47d9f0501319a", "title": "App Availability on Readiness Check could not be performed.", "problem": "App Availability on Readiness Check could not be performed due to missing workload data.", "solution": "- Customer was requested to provide the Readiness Check data collection ZIP file for review. - SAP Support reviewed the provided data collection ZIP file. - Identified that the usage_data.xml file did not contain any workload-related data. - Advised the customer to ensure ST03 is holding at least 3 full named months of workload data. - Suggested rerunning the Readiness Check data collection after ensuring sufficient ST03 data.", "cause": "The system did not gather and store enough ST03 data, which is typical for development and QAS systems, leading to incomplete data for Readiness Check analysis.", "processed_at": "2025-06-25T13:11:51.850010"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/1eea4d2ec39612d4553db11f050131f3", "title": "Recommended SAP Fiori Apps on Readiness Check could not be performed.", "problem": "Recommended SAP Fiori Apps on Readiness Check could not be performed.", "solution": "- Request customer to provide Readiness Check data collection ZIP file for review and verification. - Confirmed that the transaction_usage.xml file within the ZIP does not contain any workload-related data. - Advise customer to ensure that ST03 is holding at least 3 full months of ST03 workload data. - Suggest rerunning the Readiness Check data collection after ensuring sufficient ST03 data is available.", "cause": "The development system did not gather and store enough ST03 data, which is common for development and QAS systems.", "processed_at": "2025-06-25T13:11:55.256249"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/deea85a24796dad436bf68be436d4311", "title": "Recommended SAP Fiori Apps on Readiness Check could not be performed.", "problem": "Recommended SAP Fiori Apps section on Readiness Check Analysis in SAP for Me did not get populated.", "solution": "- Requested customer to provide Readiness Check data collection ZIP file for review. - Analyzed provided data collection ZIP file and identified that the transaction_usage.xml file lacked workload-related data. - Informed the customer that the absence of ST03 workload data is common for development and QAS systems. - Suggested ensuring ST03 has at least 3 full named months of workload data before rerunning the Readiness Check data collection for results.", "cause": "The system did not gather and store enough ST03 workload data, which is necessary for populating the Recommended SAP Fiori Apps section in the Readiness Check analysis. This is typical for development and QAS systems.", "processed_at": "2025-06-25T13:11:59.217613"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/84ab6d9ec31e9ad44daa2875e0013177", "title": "Getting syntax error while implementing  2758146", "problem": "Syntax error encountered while implementing SAP Note 2758146.", "solution": "- Update prerequisite SAP Notes 1668882 and 2971435 to their latest versions. - Ensure that Note 2758146 is completely implemented without inconsistencies.", "cause": "The syntax error is triggered due to incomplete implementation of SAP Note 2758146 or inconsistencies with imported objects. Prerequisite notes were outdated, causing errors during implementation. The issue was resolved by updating the necessary prerequisites, leading to successful implementation of the note.", "processed_at": "2025-06-25T13:12:02.494978"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/48d21c633b129a10d537962a85e45ae4", "title": "Issue while implementing SAP Note 2758146", "problem": "Error message \"Name <MIME objects> is not unique\" when implementing SAP Note 2758146 for S/4 HANA Readiness Check.", "solution": "- Investigate the existing objects in the system which were created by SAP Note 2758146. - Determine that these objects were not removed due to a de-implementation of the note. - Manually delete the existing objects to re-implement the SAP Note successfully. - Follow the steps provided in Knowledge Base Article 3308795 to resolve the error.", "cause": "The SAP Note 2758146 was previously de-implemented but the associated objects were not removed, leading to a conflict when attempting to implement the note again. Final Outcome: The issue was resolved after manually deleting the conflicting objects and following the provided resolution steps, allowing successful implementation of the note.", "processed_at": "2025-06-25T13:12:06.575806"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/ac9040c43be61e500870d44a85e45af1", "title": "We need business scenario recommendations report", "problem": "Request for Business Scenario Recommendations report for migration planning from ECC to S/4 HANA 2023.", "solution": "- Inform the customer that the Business Scenario Recommendations report has been replaced by the SAP Signavio Process Insights, discovery edition. - Provide a link to the landing page where they can access the new report and a \"how-to guide\" for data collection.", "cause": "The requested report has been deprecated and replaced by a new tool/process.", "processed_at": "2025-06-25T13:12:09.426605"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b0d120cdc3e0de50b118f71ad0013100", "title": "Financial Data Quality data collection job failed for S4 Readiness Check", "problem": "Financial Data Quality data collection job failed for SAP S/4HANA Readiness Check.", "solution": "- Ensure Online Collectors in RTCCTOOL are enabled and functioning. - Update SAP Note 2972792 to the latest version, ensuring the relevant FI-GL, FI-AA, and ML notes are implemented. - Execute RC_COLLECT_ANALYSIS_DATA again with the Financial Data Quality option and clear the buffer using \"Delete buffered data for selected items\".", "cause": "Online Collectors are not working, preventing the collection of Financial Data Quality data.", "processed_at": "2025-06-25T13:12:12.734183"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/175cda18c3aa9210ca3f1c0bb0013135", "title": "RC_COLLECT_ANALYSIS_DATA, FI_GL analysis & TMW_RC_FDQ_DATA_COLL still executing after canceling FDQ jobs", "problem": "The RC_COLLECT_ANALYSIS_DATA and TMW_RC_FDQ_DATA_COLL jobs continue executing after being canceled, preventing further analysis and uploads to the SAP Readiness Check portal.", "solution": "- Cancel the FDQ jobs. - Allow RC_COLLECT_ANALYSIS_DATA jobs to complete without FDQ data. - Upload the completed analysis data to the portal. - Re-run FDQ jobs for one fiscal year in expert mode, ensuring sequential job execution. - Apply OSS notes 3369764 & 3430973 to prevent SAPSQL_ARRAY_INSERT_DUPREC errors.", "cause": "The FDQ jobs were not properly canceled, causing dependency issues with RC_COLLECT_ANALYSIS_DATA. SAPSQL_ARRAY_INSERT_DUPREC errors were due to missing OSS notes.", "processed_at": "2025-06-25T13:12:17.688474"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/175cda18c3aa9210ca3f1c0bb0013135", "title": "RC_COLLECT_ANALYSIS_DATA, FI_GL analysis & TMW_RC_FDQ_DATA_COLL still executing after canceling FDQ jobs", "problem": "FIN_CORR_REC_ANALYSIS_* jobs are executing more fiscal years than intended, leading to extended processing times.", "solution": "- Implement OSS notes to enhance job performance. - Limit the analysis to one fiscal year per job using expert mode. - Utilize parallel jobs for efficiency, adjusting the number of jobs per financial area.", "cause": "Misconfiguration in job execution settings, leading to multiple fiscal years being processed simultaneously instead of sequentially.", "processed_at": "2025-06-25T13:12:17.688487"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/175cda18c3aa9210ca3f1c0bb0013135", "title": "RC_COLLECT_ANALYSIS_DATA, FI_GL analysis & TMW_RC_FDQ_DATA_COLL still executing after canceling FDQ jobs", "problem": "Errors and job cancellations due to system exceptions during FDQ data collection.", "solution": "- Review system logs for error details. - Implement correction notes to address known issues. - Test and validate in lower environments before applying in production.", "cause": "System exceptions and errors, possibly due to configuration issues or missing corrections in system settings.", "processed_at": "2025-06-25T13:12:17.688502"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2301f0e2c3d69a9422195030a00131ac", "title": "SAP Readiness Check", "problem": "Long-running FDQ jobs during SAP Readiness Check for SAP S/4HANA.", "solution": "- Confirmed that long execution time for FDQ jobs can occur due to large volumes of financial data. - Suggested scheduling FDQ check separately and appending results to the original analysis session. - Recommended ensuring SAP Note 0002758146 is up-to-date to prevent errors during FDQ analysis. - Advised running Readiness Check report without FDQ analysis initially and uploading results later.", "cause": "High volume of financial data leading to extended FDQ job execution times.", "processed_at": "2025-06-25T13:12:27.498662"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2301f0e2c3d69a9422195030a00131ac", "title": "SAP Readiness Check", "problem": "Incorrect priority setting for SAP Readiness Check issue in a test system.", "solution": "- Adjusted priority from 'Very High' to 'High' as the issue does not fit a business down situation. - Referenced SAP Note 0000067739 for priority guidelines.", "cause": "Misclassification of business impact and priority in a non-productive test system.", "processed_at": "2025-06-25T13:12:27.498709"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/2301f0e2c3d69a9422195030a00131ac", "title": "SAP Readiness Check", "problem": "Simplification Item Check issue separate from FDQ job issue.", "solution": "- Directed customer to open a separate case for Simplification Item Check issues. - Provided references to SAP Notes 0002399707 and 0002618023 for Simplification Item Check guidance.", "cause": "Simplification Item Check mistakenly included in the FDQ job case, requiring separate attention.", "processed_at": "2025-06-25T13:12:27.498710"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/acd1da43c356d218cebe6610a001312a", "title": "Customer Vendor Integration Analysis", "problem": "Missing Customer Vendor Integration (CVI) data in the SAP Readiness Check analysis.", "solution": "- Implement SAP Note 0002399707 fully to ensure prerequisites are met. - Complete manual steps in SAP Note 0003010669 related to CVI analysis. - Verify that the CVI job option is enabled during the analysis to ensure CVI data is included.", "cause": "The CVI data was not included in the analysis due to potentially disabled CVI job options or incomplete implementation of prerequisite notes.", "processed_at": "2025-06-25T13:12:31.740348"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/acd1da43c356d218cebe6610a001312a", "title": "Customer Vendor Integration Analysis", "problem": "<PERSON>k of clear issue description and missing attachment in the support ticket.", "solution": "- Request the customer to provide a detailed description of the issue. - Ask the customer to re-upload the missing image attachment for better context.", "cause": "Initial communication did not contain sufficient information or the necessary attachment to clearly define the issue.", "processed_at": "2025-06-25T13:12:31.740355"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b521ece2c3b09a5cecd47d9f05013167", "title": "SAP Readniness check error to analyse Business Process Discovery", "problem": "Error in SAP Readiness Check for Business Process Discovery section.", "solution": "- The error is associated with the ST-A/PI version being below the required minimum (01V SP 00). - Customer advised to update ST-A/PI version as documented in SAP Note 0002745851. - Customer instructed to open R/3 connection to the system PE0 for further investigation. - SAP Support requested customer to review and implement prerequisites from SAP Note 0002913617.", "cause": "The customer's system was on a higher release (01W_731), but not the specific version needed for the Business Process Analysis (BPA) check.", "processed_at": "2025-06-25T13:12:36.548140"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/b521ece2c3b09a5cecd47d9f05013167", "title": "SAP Readniness check error to analyse Business Process Discovery", "problem": "Bug in SAP for Me affecting Business Process Discovery analysis.", "solution": "- Development team identified a bug in SAP for Me. - The bug was scheduled to be corrected by the next afternoon. - Customer advised to wait for the fix and then create a new analysis with the analysis file on SAP for Me.", "cause": "A coding issue related to the ST-A/PI version 01W causing the Business Process Discovery analysis error.", "processed_at": "2025-06-25T13:12:36.548154"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/c21ebf75eba056d07d05f7510bd0cd9c", "title": "SAP Readiness Check Not Authorized", "problem": "SAP Readiness Check shows \"You are not authorized\" for IDoc in the Integration section.", "solution": "- Refer to SAP Knowledge Base Article (KBA) ##3137140. - Follow the solution approach outlined in the KBA to resolve authorization issues for sub-items in the Integration section.", "cause": "Authorization issues in SAP Readiness Check for sub-items in Integration section as documented in KBA ##3137140.", "processed_at": "2025-06-25T13:12:41.166237"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/23cddc8ac3de12d4b118f71ad001312c", "title": "Note Analyzer \"Download all notes\" doesnt download notes", "problem": "The SAP Note Analyzer's \"Download all notes\" feature does not download all the required notes, leading to incorrect status indications in the tool.", "solution": "- Attempt downloading notes by selecting all from subtrees as a workaround. - Check and ensure the correct versions of SAP notes are downloaded. - Refer to the SAP community link for detailed guidance on using the Note Analyzer. - Verify manually downloaded notes and their implementation status.", "cause": "The tool's \"Download all notes\" function failed, possibly due to incorrect implementation of notes or system misconfigurations.", "processed_at": "2025-06-25T13:12:46.473756"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/23cddc8ac3de12d4b118f71ad001312c", "title": "Note Analyzer \"Download all notes\" doesnt download notes", "problem": "The Note Analyzer tool incorrectly rated some notes as implemented (green), despite them not being installed in the system.", "solution": "- Manually download missing notes to correct the status. - Confirm the presence and version of specific notes within the system. - Open R/3 connection for further inspection by SAP experts.", "cause": "Potential misinterpretation of system status by Note Analyzer, possibly due to incomplete or incorrect note downloads.", "processed_at": "2025-06-25T13:12:46.473769"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e53d9e7c3a456d4ecd47d9f05013183", "title": "issue in Readiness Portal for App Availability - SV-SCS-S4R.", "problem": "The Readiness Check Analysis in SAP for Me does not populate certain sections/tiles, specifically \"App Availability\".", "solution": "- Customer was asked to provide Readiness Check data collection ZIP file for review. - ZIP file showed usage_data.xml was empty. Checked SB1 system with same result. - Identified SAP Note 2758146 version 118 had bugs; advised to update to version 119. - Customer updated to version 119 but still faced issues. - Final advice was to apply v120 of note 2758146 and check usage data in ST03N. If necessary, copy ST03N data from PRD to SB1 system.", "cause": "The SAP Note 2758146 version 118 contained bugs causing the XML file to be empty, preventing the Readiness Check analysis from populating correctly.", "processed_at": "2025-06-25T13:12:53.727746"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e53d9e7c3a456d4ecd47d9f05013183", "title": "issue in Readiness Portal for App Availability - SV-SCS-S4R.", "problem": "The TMW_RC_MANAGE_ST03N_DATA report is pulling only 4 weeks of data instead of the intended 3 months, affecting data collection for App Availability.", "solution": "- Explained that the report downloads data weekly, excluding weeks without data. - Advised that data should be collected on the actual production system where complete data is available. - Suggested updating the SAP Note to ensure proper data collection and running checks in the correct environment.", "cause": "The data collection period setting in the report was not aligning with the actual data available in the production system, leading to incomplete data retrieval.", "processed_at": "2025-06-25T13:12:53.727754"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/7e53d9e7c3a456d4ecd47d9f05013183", "title": "issue in Readiness Portal for App Availability - SV-SCS-S4R.", "problem": "Customer is unable to upload data from ST03N for App Availability in the Readiness Check Analysis.", "solution": "- Recommended running the Readiness Check directly in the production system, as SB1 data was too old. - Advised configuring ST03 to store 3 months of data and performing checks in the production environment.", "cause": "Attempting data collection from a system copy (SB1) led to issues due to outdated data and system differences. The customer closed the case after applying the suggested solutions and addressing the issues related to SAP Note updates and data collection strategies. The business impact was categorized as go-live impacted, with no serious losses anticipated if resolved before go-live.", "processed_at": "2025-06-25T13:12:53.727755"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/85bb635e3b9212d40870d44a85e45a99", "title": "Error During Custom Code Analysis S/4 Hana 2023 Upgrade", "problem": "Error during Custom Code Analysis S/4 Hana 2023 Upgrade, \"There is no check variant for the ABAP test cockpit available for the target release\".", "solution": "- Implement the corresponding ABAP test cockpit custom code check variant for the target release in the system. - Reference SAP Note 3365357 for the check variant since the target version is S/4 HANA 2023. - Run the readiness check without custom code and verify if the issue persists as suggested in SAP Note 3059197.", "cause": "The system lacks the necessary check variant for the ABAP test cockpit aligned with the target release, causing the error.", "processed_at": "2025-06-25T13:12:59.543923"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/85bb635e3b9212d40870d44a85e45a99", "title": "Error During Custom Code Analysis S/4 Hana 2023 Upgrade", "problem": "Authorization issue during the readiness check process.", "solution": "- Follow Option 2 provided in SAP Note 3396086 to address the authorization issue. - Verify and adjust authorizations as necessary.", "cause": "Insufficient authorization settings preventing the custom code analysis ZIP from being uploaded into Readiness Check Analysis.", "processed_at": "2025-06-25T13:12:59.544040"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/85bb635e3b9212d40870d44a85e45a99", "title": "Error During Custom Code Analysis S/4 Hana 2023 Upgrade", "problem": "Dropdown list empty for target release S/4HANA during Custom Code Analysis.", "solution": "- Verify using the latest simplification item catalog. - Download the content from the Simplification Item Catalog and upload it locally in the system. - Execute the check framework report /SDF/RC_START_CHECK for S/4HANA.", "cause": "The dropdown list was empty due to outdated or missing Simplification Item Catalog content.", "processed_at": "2025-06-25T13:12:59.544132"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8873326197869a54b86630200153af8c", "title": "SAP Readiness Check Report  issue", "problem": "Readiness Check Report in Development System not functioning correctly, impacting the S/4HANA upgrade.", "solution": "- Referenced SAP Note 0002913617 which specifies the readiness check only supports production systems. - Escalated the case to prioritize and expedite resolution due to business impact and impending upgrade deadline.", "cause": "The readiness check was performed on a non-production (Development) system where it is not supported, leading to discrepancies in simplification items.", "processed_at": "2025-06-25T13:13:03.099327"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/8873326197869a54b86630200153af8c", "title": "SAP Readiness Check Report  issue", "problem": "Discrepancies in simplification items between sandbox and development systems after a refresh from production.", "solution": "- Identified the updated readiness check notes as a possible cause of discrepancies. - Requested clarification on impact and recommended actions to rectify discrepancies.", "cause": "Recent update of readiness check notes after systems were refreshed from production, causing differences in simplification items.", "processed_at": "2025-06-25T13:13:03.099340"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0c61b5f6eb125ad410affe540bd0cdd7", "title": "Dump CALL_FUNCTION_NOT_ACTIVE in report RC_COLLECT_ANALYSIS_DATA", "problem": "CALL_FUNCTION_NOT_ACTIVE Dump in report RC_COLLECT_ANALYSIS_DATA", "solution": "- Check if SAP note 2972792 has been applied correctly. - Ensure all prerequisites for SAP note 2972792 are implemented. - Repair the function module /SDF/BPM_7X by executing transaction SE37. - Select Utilities > Repair Function Group. - Enter function group \"/SDF/BPM_7X\" and execute. - Reimplement SAP note 2758146 in its latest version.", "cause": "The function module /SDF/FDQ_CHECK_PREREQUISITES was not found in its function group leading to the dump. This was linked to improper implementation of the SAP note and its prerequisites.", "processed_at": "2025-06-25T13:13:07.783831"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0c61b5f6eb125ad410affe540bd0cdd7", "title": "Dump CALL_FUNCTION_NOT_ACTIVE in report RC_COLLECT_ANALYSIS_DATA", "problem": "Inconsistency with function module /SDF/BPM_7X", "solution": "- Follow the resolution steps outlined in Note 3205320 for repairing the report related to syntax errors. - Monitor the situation and reproduce the inconsistency if possible.", "cause": "Previous repair steps did not fully resolve the issue or the inconsistency presented again after initial resolution.", "processed_at": "2025-06-25T13:13:07.783860"}, {"url": "https://sap.service-now.com/now/cwf/agent/record/x_sapda_ra_inf_case_infodoc/0c61b5f6eb125ad410affe540bd0cdd7", "title": "Dump CALL_FUNCTION_NOT_ACTIVE in report RC_COLLECT_ANALYSIS_DATA", "problem": "New error related to readiness check note 0002758146", "solution": "- Follow troubleshooting steps for issue #18 in KBA 0002968380.", "cause": "The error was caused by a damaged function module associated with the readiness check note.", "processed_at": "2025-06-25T13:13:07.783865"}]