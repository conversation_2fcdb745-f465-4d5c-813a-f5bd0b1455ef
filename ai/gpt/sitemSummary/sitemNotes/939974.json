{"Request": {"Number": "939974", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 242, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016088982017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000939974?language=E&token=FC60A9D9FC65A3ABA656EF8BB5C94F06"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000939974", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000939974/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "939974"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.08.2011"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-CN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Contract"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Contract", "value": "RE-FX-CN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-CN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "939974 - FAQ: Contract, contract offer, conditions, cash flow"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note contains references to consulting notes and troubleshooting notes for contracts in RE-FX.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Real estate contract, contract, cash flow, condition<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>For further information, see the \"Related Notes\" section.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-FX-CF (Conditions and Cashflow)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D002072)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D002072)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000939974/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939974/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939974/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939974/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939974/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939974/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939974/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939974/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000939974/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "952495", "RefComponent": "RE-FX", "RefTitle": "RE-FX: Sales/purchases tax change: Steps to take,", "RefUrl": "/notes/952495"}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971"}, {"RefNumber": "931848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/931848"}, {"RefNumber": "926520", "RefComponent": "RE-FX-CN", "RefTitle": "Consulting note: Change contract partner", "RefUrl": "/notes/926520"}, {"RefNumber": "917843", "RefComponent": "RE-FX-CN", "RefTitle": "Consulting note: Cash flow update", "RefUrl": "/notes/917843"}, {"RefNumber": "907742", "RefComponent": "RE-FX-CN", "RefTitle": "Sample implementation for requirements of FAS13 and U.S. GAAP", "RefUrl": "/notes/907742"}, {"RefNumber": "881220", "RefComponent": "RE-FX-CN", "RefTitle": "BAdI for date determination on the basis of working days", "RefUrl": "/notes/881220"}, {"RefNumber": "703047", "RefComponent": "RE-FX-CN", "RefTitle": "Rounding possibilities in RE-FX", "RefUrl": "/notes/703047"}, {"RefNumber": "1828392", "RefComponent": "RE-FX-CF", "RefTitle": "Differences and rounding in cash flow", "RefUrl": "/notes/1828392"}, {"RefNumber": "1709711", "RefComponent": "RE-FX-CN", "RefTitle": "Total measurements in contracts", "RefUrl": "/notes/1709711"}, {"RefNumber": "1635539", "RefComponent": "RE-FX-CN", "RefTitle": "Runtime: \"Date Until Which the Cash Flow Is Locked\"", "RefUrl": "/notes/1635539"}, {"RefNumber": "1618971", "RefComponent": "RE-FX-BP", "RefTitle": "FAQ: Partner", "RefUrl": "/notes/1618971"}, {"RefNumber": "1610804", "RefComponent": "RE-FX-CF", "RefTitle": "Consulting note: Date of first posting", "RefUrl": "/notes/1610804"}, {"RefNumber": "1604196", "RefComponent": "RE-FX-CN", "RefTitle": "RECN: Detailed display for differing measurements", "RefUrl": "/notes/1604196"}, {"RefNumber": "1603630", "RefComponent": "RE-FX-CN", "RefTitle": "Consulting: Due date of follow-up postings", "RefUrl": "/notes/1603630"}, {"RefNumber": "1592640", "RefComponent": "RE-FX-CN", "RefTitle": "Earmarked funds and internal contracts", "RefUrl": "/notes/1592640"}, {"RefNumber": "1581640", "RefComponent": "RE-FX-BD", "RefTitle": "Worklist for objects with deletion flag also", "RefUrl": "/notes/1581640"}, {"RefNumber": "1579609", "RefComponent": "RE-FX-AJ", "RefTitle": "Editable fields for condition adjustment", "RefUrl": "/notes/1579609"}, {"RefNumber": "1574892", "RefComponent": "RE-FX-IS", "RefTitle": "Info System: Cash Flow - Generating cash flow", "RefUrl": "/notes/1574892"}, {"RefNumber": "1574699", "RefComponent": "RE-FX-CN", "RefTitle": "FAQ: Vacancy, notice, renewal", "RefUrl": "/notes/1574699"}, {"RefNumber": "1574292", "RefComponent": "RE-FX-AJ", "RefTitle": "Enhancement of rounding function for condition adjustment", "RefUrl": "/notes/1574292"}, {"RefNumber": "1560008", "RefComponent": "RE-FX-CN", "RefTitle": "Enhancement: Acct assignment change/earmarked funds document", "RefUrl": "/notes/1560008"}, {"RefNumber": "1551044", "RefComponent": "RE-FX-CF", "RefTitle": "BAdI condition: New method for changing due date", "RefUrl": "/notes/1551044"}, {"RefNumber": "1541744", "RefComponent": "RE-FX-CN", "RefTitle": "API for renewal", "RefUrl": "/notes/1541744"}, {"RefNumber": "1519440", "RefComponent": "RE-FX-CN", "RefTitle": "Changing reason for notice despite activated notice", "RefUrl": "/notes/1519440"}, {"RefNumber": "1500322", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Layouts in navigation tree", "RefUrl": "/notes/1500322"}, {"RefNumber": "1492062", "RefComponent": "RE-FX-CN", "RefTitle": "Changing the person responsible for reminder dates", "RefUrl": "/notes/1492062"}, {"RefNumber": "1489921", "RefComponent": "RE-FX-CN", "RefTitle": "Generating reminder dates for real estate contracts", "RefUrl": "/notes/1489921"}, {"RefNumber": "1485205", "RefComponent": "RE-FX", "RefTitle": "BAdI BADI_RE_IS_RM, method GET_RECORD, DISPLAY_RECORD", "RefUrl": "/notes/1485205"}, {"RefNumber": "1457190", "RefComponent": "RE-FX-CN", "RefTitle": "Conditions and object validity", "RefUrl": "/notes/1457190"}, {"RefNumber": "1454188", "RefComponent": "RE-FX-CN", "RefTitle": "Example implem.: Prevent changes to advance payt conditions", "RefUrl": "/notes/1454188"}, {"RefNumber": "1451336", "RefComponent": "RE-FX-CN", "RefTitle": "Preventing entries for date for first posting at cond. level", "RefUrl": "/notes/1451336"}, {"RefNumber": "1426012", "RefComponent": "RE-FX-CN", "RefTitle": "Contract: Exit added for search help", "RefUrl": "/notes/1426012"}, {"RefNumber": "1399271", "RefComponent": "RE-FX-OR", "RefTitle": "Proposed conditions for contract offers", "RefUrl": "/notes/1399271"}, {"RefNumber": "1369912", "RefComponent": "RE-FX-CP", "RefTitle": "Smart Forms missing for contract offer and lease abstract", "RefUrl": "/notes/1369912"}, {"RefNumber": "1366739", "RefComponent": "RE-FX-CN", "RefTitle": "New option for pro rata calculation for cash flow", "RefUrl": "/notes/1366739"}, {"RefNumber": "1348742", "RefComponent": "RE-FX-IS", "RefTitle": "Security deposits in foreign crcy: Dialog and info system", "RefUrl": "/notes/1348742"}, {"RefNumber": "1309430", "RefComponent": "RE-FX-CN", "RefTitle": "Conditions: Extension with own fields", "RefUrl": "/notes/1309430"}, {"RefNumber": "1306665", "RefComponent": "RE-FX-CN", "RefTitle": "Condition transfer: Enhancement of BADI_RECD_CONDITION", "RefUrl": "/notes/1306665"}, {"RefNumber": "1259663", "RefComponent": "RE-FX-CN", "RefTitle": "BAdI cash flow: New method", "RefUrl": "/notes/1259663"}, {"RefNumber": "1259402", "RefComponent": "RE-FX-CN", "RefTitle": "Condition calculation by area for parcels", "RefUrl": "/notes/1259402"}, {"RefNumber": "1226288", "RefComponent": "RE-FX-CN", "RefTitle": "Legal succession in the real estate contract", "RefUrl": "/notes/1226288"}, {"RefNumber": "1177235", "RefComponent": "RE-FX-OR", "RefTitle": "Offered object: No BAdI for search for RE search request", "RefUrl": "/notes/1177235"}, {"RefNumber": "1158904", "RefComponent": "RE-FX-RA", "RefTitle": "Changing the due date in the cash flow (BAdI example)", "RefUrl": "/notes/1158904"}, {"RefNumber": "1136424", "RefComponent": "RE-FX-BD", "RefTitle": "Marking objects that are not valid for current today", "RefUrl": "/notes/1136424"}, {"RefNumber": "1005831", "RefComponent": "RE-FX-BD", "RefTitle": "BAdI for customer-specific authorization check for RE obj", "RefUrl": "/notes/1005831"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "907742", "RefComponent": "RE-FX-CN", "RefTitle": "Sample implementation for requirements of FAS13 and U.S. GAAP", "RefUrl": "/notes/907742 "}, {"RefNumber": "1579609", "RefComponent": "RE-FX-AJ", "RefTitle": "Editable fields for condition adjustment", "RefUrl": "/notes/1579609 "}, {"RefNumber": "1574292", "RefComponent": "RE-FX-AJ", "RefTitle": "Enhancement of rounding function for condition adjustment", "RefUrl": "/notes/1574292 "}, {"RefNumber": "1715078", "RefComponent": "RE-FX-CF", "RefTitle": "Tax date independent of period of service", "RefUrl": "/notes/1715078 "}, {"RefNumber": "952495", "RefComponent": "RE-FX", "RefTitle": "RE-FX: Sales/purchases tax change: Steps to take,", "RefUrl": "/notes/952495 "}, {"RefNumber": "939971", "RefComponent": "RE-FX", "RefTitle": "FAQ and consulting notes for RE-FX", "RefUrl": "/notes/939971 "}, {"RefNumber": "926520", "RefComponent": "RE-FX-CN", "RefTitle": "Consulting note: Change contract partner", "RefUrl": "/notes/926520 "}, {"RefNumber": "1828392", "RefComponent": "RE-FX-CF", "RefTitle": "Differences and rounding in cash flow", "RefUrl": "/notes/1828392 "}, {"RefNumber": "1541744", "RefComponent": "RE-FX-CN", "RefTitle": "API for renewal", "RefUrl": "/notes/1541744 "}, {"RefNumber": "1226288", "RefComponent": "RE-FX-CN", "RefTitle": "Legal succession in the real estate contract", "RefUrl": "/notes/1226288 "}, {"RefNumber": "1709711", "RefComponent": "RE-FX-CN", "RefTitle": "Total measurements in contracts", "RefUrl": "/notes/1709711 "}, {"RefNumber": "1610804", "RefComponent": "RE-FX-CF", "RefTitle": "Consulting note: Date of first posting", "RefUrl": "/notes/1610804 "}, {"RefNumber": "1635539", "RefComponent": "RE-FX-CN", "RefTitle": "Runtime: \"Date Until Which the Cash Flow Is Locked\"", "RefUrl": "/notes/1635539 "}, {"RefNumber": "1457190", "RefComponent": "RE-FX-CN", "RefTitle": "Conditions and object validity", "RefUrl": "/notes/1457190 "}, {"RefNumber": "1574699", "RefComponent": "RE-FX-CN", "RefTitle": "FAQ: Vacancy, notice, renewal", "RefUrl": "/notes/1574699 "}, {"RefNumber": "1618971", "RefComponent": "RE-FX-BP", "RefTitle": "FAQ: Partner", "RefUrl": "/notes/1618971 "}, {"RefNumber": "1574892", "RefComponent": "RE-FX-IS", "RefTitle": "Info System: Cash Flow - Generating cash flow", "RefUrl": "/notes/1574892 "}, {"RefNumber": "1309430", "RefComponent": "RE-FX-CN", "RefTitle": "Conditions: Extension with own fields", "RefUrl": "/notes/1309430 "}, {"RefNumber": "1604196", "RefComponent": "RE-FX-CN", "RefTitle": "RECN: Detailed display for differing measurements", "RefUrl": "/notes/1604196 "}, {"RefNumber": "1485205", "RefComponent": "RE-FX", "RefTitle": "BAdI BADI_RE_IS_RM, method GET_RECORD, DISPLAY_RECORD", "RefUrl": "/notes/1485205 "}, {"RefNumber": "1177235", "RefComponent": "RE-FX-OR", "RefTitle": "Offered object: No BAdI for search for RE search request", "RefUrl": "/notes/1177235 "}, {"RefNumber": "1369912", "RefComponent": "RE-FX-CP", "RefTitle": "Smart Forms missing for contract offer and lease abstract", "RefUrl": "/notes/1369912 "}, {"RefNumber": "1399271", "RefComponent": "RE-FX-OR", "RefTitle": "Proposed conditions for contract offers", "RefUrl": "/notes/1399271 "}, {"RefNumber": "1158904", "RefComponent": "RE-FX-RA", "RefTitle": "Changing the due date in the cash flow (BAdI example)", "RefUrl": "/notes/1158904 "}, {"RefNumber": "1136424", "RefComponent": "RE-FX-BD", "RefTitle": "Marking objects that are not valid for current today", "RefUrl": "/notes/1136424 "}, {"RefNumber": "1551044", "RefComponent": "RE-FX-CF", "RefTitle": "BAdI condition: New method for changing due date", "RefUrl": "/notes/1551044 "}, {"RefNumber": "1005831", "RefComponent": "RE-FX-BD", "RefTitle": "BAdI for customer-specific authorization check for RE obj", "RefUrl": "/notes/1005831 "}, {"RefNumber": "1560008", "RefComponent": "RE-FX-CN", "RefTitle": "Enhancement: Acct assignment change/earmarked funds document", "RefUrl": "/notes/1560008 "}, {"RefNumber": "1581640", "RefComponent": "RE-FX-BD", "RefTitle": "Worklist for objects with deletion flag also", "RefUrl": "/notes/1581640 "}, {"RefNumber": "1592640", "RefComponent": "RE-FX-CN", "RefTitle": "Earmarked funds and internal contracts", "RefUrl": "/notes/1592640 "}, {"RefNumber": "1259402", "RefComponent": "RE-FX-CN", "RefTitle": "Condition calculation by area for parcels", "RefUrl": "/notes/1259402 "}, {"RefNumber": "1259663", "RefComponent": "RE-FX-CN", "RefTitle": "BAdI cash flow: New method", "RefUrl": "/notes/1259663 "}, {"RefNumber": "1306665", "RefComponent": "RE-FX-CN", "RefTitle": "Condition transfer: Enhancement of BADI_RECD_CONDITION", "RefUrl": "/notes/1306665 "}, {"RefNumber": "1348742", "RefComponent": "RE-FX-IS", "RefTitle": "Security deposits in foreign crcy: Dialog and info system", "RefUrl": "/notes/1348742 "}, {"RefNumber": "1366739", "RefComponent": "RE-FX-CN", "RefTitle": "New option for pro rata calculation for cash flow", "RefUrl": "/notes/1366739 "}, {"RefNumber": "1454188", "RefComponent": "RE-FX-CN", "RefTitle": "Example implem.: Prevent changes to advance payt conditions", "RefUrl": "/notes/1454188 "}, {"RefNumber": "1451336", "RefComponent": "RE-FX-CN", "RefTitle": "Preventing entries for date for first posting at cond. level", "RefUrl": "/notes/1451336 "}, {"RefNumber": "1426012", "RefComponent": "RE-FX-CN", "RefTitle": "Contract: Exit added for search help", "RefUrl": "/notes/1426012 "}, {"RefNumber": "1489921", "RefComponent": "RE-FX-CN", "RefTitle": "Generating reminder dates for real estate contracts", "RefUrl": "/notes/1489921 "}, {"RefNumber": "1500322", "RefComponent": "RE-FX-BD", "RefTitle": "RE80: Layouts in navigation tree", "RefUrl": "/notes/1500322 "}, {"RefNumber": "1492062", "RefComponent": "RE-FX-CN", "RefTitle": "Changing the person responsible for reminder dates", "RefUrl": "/notes/1492062 "}, {"RefNumber": "1519440", "RefComponent": "RE-FX-CN", "RefTitle": "Changing reason for notice despite activated notice", "RefUrl": "/notes/1519440 "}, {"RefNumber": "881220", "RefComponent": "RE-FX-CN", "RefTitle": "BAdI for date determination on the basis of working days", "RefUrl": "/notes/881220 "}, {"RefNumber": "931848", "RefComponent": "RE-FX-CN", "RefTitle": "Consulting note: Changes relevant for follow-up posting", "RefUrl": "/notes/931848 "}, {"RefNumber": "1603630", "RefComponent": "RE-FX-CN", "RefTitle": "Consulting: Due date of follow-up postings", "RefUrl": "/notes/1603630 "}, {"RefNumber": "917843", "RefComponent": "RE-FX-CN", "RefTitle": "Consulting note: Cash flow update", "RefUrl": "/notes/917843 "}, {"RefNumber": "703047", "RefComponent": "RE-FX-CN", "RefTitle": "Rounding possibilities in RE-FX", "RefUrl": "/notes/703047 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}