{"Request": {"Number": "2527020", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 236, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019569032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=37B93B9D146305D3E36E51E42148DD21"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2527020"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.02.2019"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2527020 - G/L accounts with the G/L account type \"Secondary Costs\" in general ledger accounting"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In SAP S/4HANA, the journal enters all posting-relevant transactions in financial accounting (FI) and Controlling (CO) as posting documents. For more information about the introduction of the journal, see SAP Note 2428741.</p>\r\n<p>As a result of this approach, all postings to G/L accounts with the G/L account time \"Secondary Costs\" are visible in general ledger accounting. This is in contrast to SAP Business Suite, where postings to secondary cost elements are not visible in general ledger accounting.</p>\r\n<p>This SAP Note describes how you can handle this difference.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>\r\n<li>All postings to G/L accounts with the G/L account type \"Secondary Costs\" are always generated with debit and credit in the same G/L account. Thus, these postings always balance to zero from the account view.<br /><br /></li>\r\n<li>From the controlling view (segment, profit center, and so on), the amounts might naturally not balance to zero, since the debit side might be posted to segment A, for example, and the credit side to segment B.<br />If the real-time integration of FI and CO is active in SAP Business Suite (this corresponds to the situation in an S/4HANA system where FI and CO are integrated automatically by the journal), a posting of this kind is also transferred to general ledger accounting. The difference is that the SAP Business Suite transfer is summarized and is made to an alternative parallel account (SAP Implementation Guide -&gt; Financial Accounting (New) -&gt; Financial Accounting Global Settings (New) -&gt; Real-Time Integration of Controlling with Financial Accounting -&gt; Define Account Determination for Real-Time Integration).<br />If the G/L accounts with the G/L account type \"Secondary Costs\" are listed in reporting in the same place as the corresponding parallel accounts, there are no longer any decisive differences between SAP Business Suite and S/4HANA. <br /><br /></li>\r\n<li>If you want to completely ignore postings to G/L accounts with the G/L account type \"Secondary Costs\" in general ledger reporting, you must make sure that these postings always balance to zero from the controlling view. You can use the following procedure to do this:</li>\r\n<ol>\r\n<li>You define your own P&amp;L statement account type (SAP Implementation Guide -&gt; Financial Accounting -&gt; General Ledger Accounting -&gt; Master Data -&gt; G/L Accounts -&gt; Preparations -&gt; Define Retained Earnings Account) with your own retained earnings account and assign this P&amp;L statement account type to all G/L accounts with the G/L account type \"Secondary Costs\".</li>\r\n<li>You define your own company code clearing account and define this for cross-company-code processes to G/L accounts with the G/L account type \"Secondary Costs\" (SAP Implementation Guide -&gt; Controlling -&gt; Cost Center Accounting -&gt; Manual Actual Postings -&gt; Additional Transaction-Related Postings -&gt; Assign Intercompany Clearing Accounts).<br />Note the following:<br />This configuration is visible only if ENTERPRISE_BUSINESS_FUNCTIONS&#x00A0;\"<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">FINS_CO_ICO_PROC_ENH_101_RS</span>\" is active (SAP Customizing Implementation Guide -&gt; Activate Business Functions).</li>\r\n<li>You define your own document splitting zero-balance clearing account and define this for the account key \"001\" (SAP Implementation Guide -&gt; Financial Accounting -&gt; General Ledger Accounting -&gt; Business Transactions -&gt; Document Splitting -&gt; Define Zero-Balance Clearing Account).<br />Note the following:<br />This account key must be assigned to the business transaction variant <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">2000-0002</span>.</li>\r\n<li>Assign the business transaction variant <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">2000-0001</span> to the document types for CO postings with secondary costs (SAP Customizing Implementation Guide &gt; Financial Accounting &gt;&#x00A0;General Ledger Accounting &gt;&#x00A0;Business Transactions &gt;&#x00A0;Document Splitting &gt;&#x00A0;Classify Document Types for Document Splitting).<br />In the case of postings with this special business transaction variant, the system forms separate zero-balance clearing items with the zero-balance clearing account for the account key assigned to the business transaction variant <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">2000-0002</span> for secondary costs. However, for posting lines that do not post to secondary cost types, the formation of the zero-balance clearing items takes place in the same document with the zero-balance clearing account for the account key assigned to the business transaction variant <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">2000-0001</span>.</li>\r\n<li>In reporting, you always group the G/L accounts with the G/L account type \"Secondary Costs\" with your G/L accounts listed above. As a result, there is always a zero balance per account assignment for these groupings.</li>\r\n</ol></ol>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL-FL (Flexible Structures)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D003377)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026506)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2428741", "RefComponent": "FI-GL", "RefTitle": "Universal Journal FAQ", "RefUrl": "/notes/2428741"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2431747", "RefComponent": "FI-GL-GL", "RefTitle": "General Ledger: Incompatible changes in S/4HANA compared to classic ERP releases", "RefUrl": "/notes/2431747 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}