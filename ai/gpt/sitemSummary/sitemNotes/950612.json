{"Request": {"Number": "950612", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 344, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005592212017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000950612?language=E&token=FDC03A44505639D8092AAB00F16DB616"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000950612", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000950612/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "950612"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.10.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DST"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Staging"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Staging", "value": "BW-WHM-DST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "950612 - P9; request archiving: Archiving multiple requests as initial"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to archive multiple requests as initial. This is a labor-intensive activity for the administrator because </p>\r\n<ol>1. the archive may not contain more than 10,000 to 20,000 requests. </ol><ol>2. It is not immediately clear how many requests have accrued in a particular period. </ol>\r\n<p>Therefore, the report <br />RSREQARCH_WRITE<br />was revised with Support Package 9.   <br />Furthermore, this correction eliminates several minor problems with archiving requests. </p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Program error </p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>BW 7.0</li>\r\n</ul>\r\n<p> Import Support Package 09 for 7.0 (BW 7.0 Patch 09 or <strong>SAPKW70009</strong>) into your BW system. The Support Package is available once <strong>SAP Note 914303</strong> \"SAPBINews BW 7.0 Support Package 09\", which describes this Support Package in more detail, is released for customers.<br /><br />The report RSREQARCH_WRITE now has the following additional selection fields:</p>\r\n<ol>1. Requests Older than (months)</ol><ol>2. Only Archive New Requests</ol><ol>3. Only Archive Reloaded Requests</ol><ol>4. Archive All Requests</ol><ol>5. Min. Number Requests</ol>\r\n<p><br />This correction adds these fields/radio buttons to the well-known selection field \"Selection Date of Request\". <br />The program for choosing the requests to be selected proceeds as follows:<br />In ascending order, it selects from the most recent request to all requests in the field \"Selection date of requests\" that have not yet been archived (if this selection remains empty, all requests are selected). <br />The system then checks whether this request is older than the monthly selection in the field \"Requests Older than\". <br />This filed must contain at least a \"1\" for a month. <br />Months are always calculated with 31 days. <br />The request that you may want to archive is then checked against the three radio buttons \"Only Archive New Requests\", <br />\"Only Archive Reloaded Requests\" and<br />\"Archive All Requests\". <br />If the button \"Only Archive New Requests\" is set, requests that were archived once before but were then reloaded from the archive are not archived again. <br />If the button \"Only Archive Reloaded Requests\" is set, ONLY those requests that have already been archived once and reloaded again are archived. <br />The button set to \"Archive All Requests\" archives all the requests that are not currently archived, regardless of whether these were already archived once and were reloaded again or not. <br />We recommend that you set the button \"Only Archive New Requests\" for initial archiving because after several months you can check whether archived requests had to be reloaded again. <br />For example, this is necessary for a reconstruction from the  Persistent Staging Area (PSA). <br />There is still the parameter \"Min. Number Requests\"  An archive is only created if the report finds at least this number of requests that it can archive. <br />The report collects the first 10,000 requests that meet the criteria and archives them.  The report then stops. <br />This guarantees that an archive does not have too many requests.<br />Caution: You must save all requests that you want to archive in transaction SM12.  This means that an enqueue lock is set for each request before it can be written to the archive. <br />Therefore, you must set the BW so that at least 12,000 enqueues can be set or created at the same time. <br />If there is not enough memory for the enqueues (not enough can be allocated), then the process hangs. <br />Ensure that the profile parameters<br />enque/table_size<br />and<br />abap/shared_objects_size_MB<br />that you choose are large enough (10...100 MByte).<br /><br />Procedure:<br />Preferably, you can use transaction SARA to periodically schedule the report RSREQARCH_WRITE daily.  The first 10,000 requests will then be archived <br />on the first day.  On the second day the next 10,000 requests will be archived, and so on. <br /><br />After the next few days/weeks, the write reports archived all the old requests and only creates a new archive when at least \"Min. Number Requests\" is met. <br />Therefore, it is now sufficient to periodically schedule the write-delete action once (daily) in transaction SARA.  The program then processes the requests automatically. <br />We do NOT recommend that you set the parameter \"Min. Number Requests\" to a value lower than 1,000. If you have too many requests, you should set the parameter to 9,999. <br />We recommend that you let the deletion report run automatically after the write report in transaction SARA because you cannot access (for example, with a reconstruction process) a request that has ONLY been written but not yet deleted. <br />A dump then occurs in BW. </p>\r\n<p>To be able to periodically schedule the archiving sessions it is best to select a large time interval.</p>\r\n<p>1.1.1990 - 31.12.2050</p>\r\n<p>and 'Older than X Months'</p>\r\n<p>As a result, during each run, all requests that are older than X months are archived.<br />The next archiving run starts to archive the day on which the previous run has been completed and chronologically archives the next 10,000 requests (if the minimum number of requests is 9999, which should be the default).</p>\r\n<p>This automatic archiving works if you activate the automatic start of the delete run in transaction AOBJ.</p>\r\n<p>In the variant of the write report, the 'Delete with Test Variant' indicator must be deactivated.</p>\r\n<p>&#x00A0;</p>\r\n<p>It does not make sense to select small time intervals as time intervals (daily intervals) because the end of the time interval (the last day of the interval) is not archived until the end.<br />The archiving run terminates when it reaches the last day of the time interval as soon it has found 1,000 requests.</p>\r\n<p><br /><br />Note:<br />If you want to implement this advance correction, proceed as follows:<br />Create the following domain:<br />RSREQARCHINT in the package RSREQARCH with the following data:<br />Data type: INT1<br />No. characters: 3<br />Decimal places: 0<br />Output length: 2<br />Without signs<br /><br />You must also create the following data elements in the package RSREQARCH:</p>\r\n<ol>1. RSREQARCHBOTH with the domain CHAR1</ol><ol>2. RSREQARCHLOWLIMIT with the domain NUMC4</ol><ol>3. RSREQARCHNUMC2 with the domain RSREQARCHINT</ol><ol>4. RSREQARCHONLYNEW with the domain CHAR1</ol><ol>5. RSREQARCHONLYOLD with the domain CHAR1</ol>\r\n<p>Also, create the following ABAP Dictionary structure:<br />RSREQARCHSTRUCT. Give it the following fields:</p>\r\n<ol>1. OLDER RSREQARCHNUMC2 INT1 3</ol><ol>2. ONLY_NEW RSREQARCHONLYNEW CHAR 1</ol><ol>3. ONLY_OLD RSREQARCHONLYOLD CHAR 1</ol><ol>4. BOTH RSREQARCHBOTH CHAR 1</ol><ol>5. LOWLIMIT RSREQARCHLOWLIMIT NUMC 4</ol>\r\n<p><br />Note: The following is very important:<br />In transaction AOBJ, you need to change the archiving object<br />BWREQARCH as follows:</p>\r\n<ol>1. The indicator \"Interruption Possible\" after the write report RSREQARCH_WRITE must be DEACTIVATED. </ol><ol>2. The indicator \"Do Not Start Before End of Write Phase\" after the deletion report RSREQARCH_DELETE must be ACTIVATED. </ol><ol>3. The indicator \"Prohibit New Session During Loading\" after the reload report RSREQARCH_RELOAD must be DEACTIVATED. </ol>\r\n<p><br />In the Customizing settings, it is important to make sure that the fields<br />\"Maximum Size in MB\" and<br />\"Maximum Number of Data Objects\"<br />are blank or have the value \"0\".<br /><br />Furthermore, the radio button \"Delete Jobs\" must be set to \"Start Automatically\".<br /> <br /><br />Caution: The following is very important:<br />Handle your archive files with care and never delete them; in particular, the archive files that have been created by the archiving object BWREQARCH must be kept and must NEVER be deleted.<br />BW must always be able to reload individual requests from the archive files if necessary.<br /><br />To provide information in advance, the SAP Notes mentioned above might already be available before the Support Package is released. In this case, the short text of the SAP Note will contain the words \"Preliminary version\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023663)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023663)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000950612/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000950612/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000950612/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000950612/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000950612/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000950612/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000950612/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000950612/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000950612/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2960150", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "How to verify if a BW archiving administration request was archived", "RefUrl": "/notes/2960150 "}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 700", "SupportPackage": "SAPK-70009INVCBWTECH", "URL": "/supportpackage/SAPK-70009INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70009", "URL": "/supportpackage/SAPKW70009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "NumberOfCorrin": 1, "URL": "/corrins/0000950612/654"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "ValidFrom": "700", "ValidTo": "700", "Number": "923896 ", "URL": "/notes/923896 ", "Title": "P8: Archiving requests: Removing old REBU_.... requests", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "ValidFrom": "700", "ValidTo": "700", "Number": "936901 ", "URL": "/notes/936901 ", "Title": "P8: REQARCH: Delete job starts before write job finishes", "Component": "BW-WHM-DST"}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "ValidFrom": "700", "ValidTo": "700", "Number": "940700 ", "URL": "/notes/940700 ", "Title": "P8: Dumps when using SARA to reload requests", "Component": "BW-WHM-DST"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}