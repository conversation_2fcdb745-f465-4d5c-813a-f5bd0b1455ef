{"Request": {"Number": "2268304", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 228, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018245412017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002268304?language=E&token=64DBDA674A883CFB1FF832E648D063F8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002268304", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002268304/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2268304"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.06.2016"}, "SAPComponentKey": {"_label": "Component", "value": "MFG-MII"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Manufacturing Integration and Intelligence"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Manufacturing", "value": "MFG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MFG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Manufacturing Integration and Intelligence", "value": "MFG-MII", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MFG-MII*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2268304 - Release note for MII 15.1 SP01 FP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>-</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>MII 15.1, OEE_MII 15.1, OEE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>&#160;MII Workbench:</strong></p>\r\n<p>New functions added:</p>\r\n<ul>\r\n<li>Expression function supported for tag category in PIC Analysis mode of Catalog query.</li>\r\n<li>Digital Settings checkbox (visible only for PCo queries) to display the data type of the \"Value\" column of the generated virtual table in HANA studio as string.</li>\r\n</ul>\r\n<p>Enhancements:</p>\r\n<ul>\r\n<li>Alias column for the catalog query to provide alias name for the catalog tags.</li>\r\n<li>Enhanced HANA SDA connection to support metadata properties as columns.</li>\r\n</ul>\r\n<p><em><span style=\"font-size: 14px;\">Display Templates</span></em></p>\r\n<ul>\r\n<li>Enhanced i5Grid constructor to support grid type and query template.<em><br /></em></li>\r\n<li>A transparent background is enabled for the gauge chart area.</li>\r\n<li>Two additional shapes are available for the gauge chart, a vertical rectangle and a horizontal rectangle.</li>\r\n</ul>\r\n<p><strong>Catalog Services</strong></p>\r\n<ul>\r\n<li>The binding of the composite KPI object with PIC is supported.</li>\r\n<li>Added <em>Column Filter</em> table in <em>Plant Information Maintenance</em> screen to select the columns to be displayed on the screen.</li>\r\n<li>Included node name based sorting in <em>Plant Information Catalog</em> and <em>Plant Information Maintenance</em> screens.</li>\r\n<li>Added Sorting on Category and Property Sets.</li>\r\n<li>Usability improvement in editing the property values in&#160;<em>Plant Information Maintenance.</em></li>\r\n</ul>\r\n<p><strong>Plant Connectivity</strong></p>\r\n<p>Changed the destination name from <em>SAP ESP Destination</em> to <em>Data Streaming</em></p>\r\n<p><strong>MII Self Service Composition Environment</strong></p>\r\n<p><em>General Enhancements</em></p>\r\n<ul>\r\n<li>A default CSS File for dashboards is available in General Settings.</li>\r\n<li>The Source Code tab is hidden by default. Only users assigned with action XMII_SSCE_DEVELOPER can edit the source code.</li>\r\n<li>Consistent PIC tree across Selft Service Composition Environment with search option.</li>\r\n<li>Java doc for all chart objects and SSCE objects.</li>\r\n<li>Launch the dashboard from open dialog box. &#160;</li>\r\n</ul>\r\n<p><em>UI Elements</em></p>\r\n<p>The following modifications were done:</p>\r\n<ul>\r\n<li>The i5Command property is available for the Button control in UI Controls.</li>\r\n<li>List is available in the UI Controls.</li>\r\n<li>Included <em>Configure Query Parameters</em> to configure the query template in <em>DropdownBox.</em></li>\r\n</ul>\r\n<p><em>Plant Information Catalog</em></p>\r\n<p>Included zoom settings at design to view the spots or the data on the map.</p>\r\n<p><em>MDO/KPI Objects</em></p>\r\n<p>Following changes were done:</p>\r\n<ul>\r\n<li>Added <em>Choose Background Tile Color</em> to display the KPI values in white and highlight the background.</li>\r\n<li>Added Gauge Visualization for KPI.</li>\r\n<li>Included a functionality to create events for the KPI that is converted to tile or gauge.</li>\r\n</ul>\r\n<p><em>Query Templates</em></p>\r\n<p>Added:</p>\r\n<ul>\r\n<li>Drill Down function for dashboards of Energy Monitoring and Analytics (EMA).</li>\r\n<li>Ability to convert query into i5Grid visualization.</li>\r\n</ul>\r\n<p><em>Resource Files</em></p>\r\n<p>Following modifications were done:</p>\r\n<ul>\r\n<li>Added <em>Event Configuration</em> to the selected area on top of an image.&#160;</li>\r\n<li>Display the query as gauge chart on top of an image. You can configure display parameters to the gauge chart.</li>\r\n<li>Included <em>Available Tags</em> field to display the path of the selected tag and <em>Show Default</em> field to define a default rule for the selected part on the VDS file.</li>\r\n<li>Create events when tag is viewed as gauge on top of an image.</li>\r\n<li>Show KPI trend for KPI query on top of an image.</li>\r\n</ul>\r\n<p><strong>Overall Equipment Effectiveness (OEE):</strong></p>\r\n<ul>\r\n<li>Option to choose&#160;between SAP ERP or SAP S/4 HANA, on-premise edition 1511&#160;for OEE. A customization is provided where selection can be made&#160;between ERP EHP System or SAP S/4 HANA, on-premise edition 1511. The&#160;SAP S/4HANA System can work with extended material numbers.</li>\r\n<ul>\r\n<li>Note: DIMP LAMA customers should be on SAP S/4 HANA on-premise edition 1511 before deploying OEE.&#160;</li>\r\n</ul>\r\n<li>In Plant Maintenance Notification, changes can be made to a notification after it is already created in ERP. Changes or updates made to the notification in ERP can also be synced back into OEE.</li>\r\n<li>Activities ACT_REV_ORD_GRAPH and ACT_REV_SFT_GRAPH are provided. These can be configured to show consolidated data for an order in graphical display and list of orders executed for the given shift and their associated data.</li>\r\n<li>Activity ACT_DOWN_LIST activity has the option ALLOW_TABS to help configure the user interface for Manage Downtime screen. This option allows restrict the different downtime tabs that are displayed on the screen.</li>\r\n<li>Orders with status 'Create' in ERP are also transferred to the ERP-Shop Floor Integration framework. Orders with 'Create' status are displayed on the Dispatch Quantity screen and are not processed further.</li>\r\n<li>Goods Issue (GI) and Goods Receipt (GR) apps can perform goods issue and goods receipt with the SAP Extended Warehouse Management (EWM) system.</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I046253"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I043918)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002268304/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002268304/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002268304/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002268304/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002268304/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002268304/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002268304/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002268304/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002268304/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2934265", "RefComponent": "MFG-ME", "RefTitle": "SAP NetWeaver 7.4 End of Mainstream Maintenance Impact on SAP Manufacturing Suite", "RefUrl": "/notes/2934265"}, {"RefNumber": "2342025", "RefComponent": "MFG-MII", "RefTitle": "MII 15.1 is also supported on NW 7.40 SP15 (JAVA SP13)", "RefUrl": "/notes/2342025"}, {"RefNumber": "2284723", "RefComponent": "MFG-MII", "RefTitle": "upgrade to 15.1 failing due to changlist table change", "RefUrl": "/notes/2284723"}, {"RefNumber": "2251629", "RefComponent": "MFG-MPM", "RefTitle": "SAP MII 15.1 (including OEE)  is on SAP NW 7.5", "RefUrl": "/notes/2251629"}, {"RefNumber": "2146857", "RefComponent": "MFG-MII", "RefTitle": "Recommendations for deployment of OEEMII component using SUM tool", "RefUrl": "/notes/2146857"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2495662", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2495662 "}, {"RefNumber": "2376061", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1610: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2376061 "}, {"RefNumber": "2241931", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA, on-premise edition 1511: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2241931 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}