{"Request": {"Number": "2019485", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2339, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017876172017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002019485?language=E&token=F215EEA6A9C1B55CB3D93D1724C268E1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002019485", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2019485"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.07.2014"}, "SAPComponentKey": {"_label": "Component", "value": "QM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Quality Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Quality Management", "value": "QM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'QM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2019485 - QM-basis where-used-checks before blocking of partner"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<div class=\"longtext\" style=\"font-size: 100.01%;\">\r\n<p>The application&#160;Quality Managment&#160;can contain data, which is related to a customer, &#160;vendor or contact person. This note describes the changes, which will prevent blocking of a customer, vendor or contact person, if this customer,&#160;vendor or contact person&#160;is still assigned to QM data.</p>\r\n</div>\r\n<p>\r\n<script language=\"javascript\" type=\"text/javascript\">// <![CDATA[\r\nfunction get_tst( ){\r\n    var min = 5;\r\n    var max = 10;\r\n    var x = (Math.random() * (max - min)) + min;\r\n    x = x * 10000;\r\n    x = Math.round(x);\r\n    return x;\r\n}\r\nfunction change_cm(pointer) {\r\nx = get_tst();\r\n  window.open('/sap/bc/bsp/spn/sno_corr/NNF_Gui_show_cm_overview.sap?tst='+x+'&pointer='+pointer,'Launch_CWB','location=0,status=0,scrollbars=0, width=100,height=100');\r\n}\r\nfunction launch_msg(pointer) {\r\nx = get_tst();\r\n  window.open('/sap/bc/bsp/spn/sno_corr/NNF_Gui_launch_msg.sap?tst='+x+'&pointer='+pointer,'Launch_CWB','location=0,status=0,scrollbars=0, width=100,height=100');\r\n}\r\n// ]]></script>\r\n</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, Contact person, Manufacturer, KUNNR, LIFNR, PARNR, QALS-LIFNR, QALS-HERSTELLER, QALS-KUNNR, QALS-SELLIFNR, QALS-SELKUNNR, QALS-SELHERST, QASH-LSLIFNR, QASH-LSHERST, QASH-LSKUNNR, QCEM-PARNR, QCEP-PARNR, QCPR-LIFNR, QCPR-HERSTELLER, QDQL-LIFNR, QDQL-HERSTELLER, QDQL-KUNNR, QINF-LIEFERANT, QIWL-LIFNR, QIWL-KUNNR, QPRN-KUNNR, QPRN-LIFNR, QPRN-HRSTL, QDVM-KUNNR, QMSM-PARNR, QMUR-PARNR</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>See note 2007926</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The new functionality for application&#160;Quality Management will be available with release SAP_APPL 617 and support package SP05.<br /><br />The functionality consists of the following main features:</p>\r\n<p>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</p>\r\n<p>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 136px; padding-left: 30px; width: 931px;\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ID Type</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">Application Name</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">Application Description</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">1 - Customer Master Data</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_IM</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">Quality Management without notification</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">1 - Customer Master Data</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">Quality notification</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">2 - Vendor Master Data</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_IM</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">Quality Management without notification</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">2 - Vendor Master Data</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">Quality notification</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">3 - Contact Person</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">Quality notification</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 203px; width: 897px;\">\r\n<tbody>\r\n<tr>\r\n<td>ID Type</td>\r\n<td>Application Name</td>\r\n<td>\r\n<p>Item number</p>\r\n</td>\r\n<td>Registered Class for EoP Checks</td>\r\n<td>General</td>\r\n<td>Comp.Code</td>\r\n</tr>\r\n<tr>\r\n<td>1 - Customer Master Data</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_IM</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">&#160;0</p>\r\n</td>\r\n<td>CL_WUC_QM_IM_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>1 - Customer Master Data</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">&#160;0</p>\r\n</td>\r\n<td>CL_WUC_QM_QN_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>1 - Customer Master Data</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">&#160;1</p>\r\n</td>\r\n<td>CL_WUC_PM_WOC_MN_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>1 - Customer Master Data</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">&#160;2</p>\r\n</td>\r\n<td>CL_WUC_PM_EQM_FL_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>2 - Vendor Master Data</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_IM</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">&#160;0</p>\r\n</td>\r\n<td>CL_WUC_QM_IM_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>2 - Vendor Master Data</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">&#160;0</p>\r\n</td>\r\n<td>CL_WUC_QM_QN_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>2 - Vendor Master Data</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">&#160;1</p>\r\n</td>\r\n<td>CL_WUC_PM_WOC_MN_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>2 - Vendor Master Data</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">&#160;2</p>\r\n</td>\r\n<td>CL_WUC_PM_EQM_FL_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">3 - Contact Person</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>&#160;0</td>\r\n<td>CL_WUC_QM_QN_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">3 - Contact Person</p>\r\n</td>\r\n<td>\r\n<p dir=\"ltr\" style=\"margin-right: 0px;\">ERP_QM_QN</p>\r\n</td>\r\n<td>&#160;1</td>\r\n<td>CL_WUC_PM_EQM_FL_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The application&#160;Quality Management delivers the following class registered for the end of purpose check of customer, vendor and contact person:</p>\r\n<p>&#160;CL_WUC_QM_IM_EOP_CHECK&#160;&#160;-'Where-Used-Check QM (without notification)'<br />&#160;CL_WUC_QM_QN_EOP_CHECK - 'Where-Used-Check QM (notification)'</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D031456)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022841)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002019485/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002019485/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002019485/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002019485/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002019485/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002019485/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002019485/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002019485/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002019485/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2035204", "RefComponent": "LO-MD-BP-DP", "RefTitle": "Company code data not considered by where-used-check before blocking of vendor/customer data", "RefUrl": "/notes/2035204"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2007926", "RefComponent": "LO-MD-BP-DP", "RefTitle": "Simplified Blocking and Deletion of Customer / Vendor Master Data", "RefUrl": "/notes/2007926 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 617", "SupportPackage": "SAPKH61705", "URL": "/supportpackage/SAPKH61705"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}