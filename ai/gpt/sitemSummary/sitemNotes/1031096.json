{"Request": {"Number": "1031096", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 418, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016243392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001031096?language=E&token=81D6978C4506EBBC8DEE854EBDCAF21F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001031096", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001031096/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1031096"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 40}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.02.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-HAG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Host Agent"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Host Agent", "value": "BC-CCM-HAG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-HAG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1031096 - Installing Package SAPHOSTAGENT"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to install or upgrade the SAP Host Agent</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>saphostexec, sapstartsrv<br />=======================================================================</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Differentiation between platforms:<br />SAP Host Agent runs on all SAP supported platforms. The differences in using the SAP Host Agent are the following.</p>\r\n<ul>\r\n<li>Host Agent directories:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Unix and IBM i:<br />/usr/sap/hostctrl/exe<br />/usr/sap/hostctrl/work</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Windows:<br />%ProgramFiles%\\SAP\\hostctrl\\exe<br />%ProgramFiles%\\SAP\\hostctrl\\work</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Host Exec Executable:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Unix: saphostexec</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Windows: saphostexec.exe</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Needed user permissions:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Unix: root permissions</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Windows: Administrator permissions + elevated mode</li>\r\n</ul>\r\n</ul>\r\n<p><br />This guide will be using the Unix paths and commands as examples. The procedure is the same for Windows.<br />=======================================================================</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Installing SAP Host Agent</p>\r\n<p>Please check the SAP Host Agent standalone documentation at:&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/product/hostagent\">https://help.sap.com/viewer/product/hostagent</a></p>\r\n<p>for up to date instructions on how to download and install SAP Host Agent.</p>\r\n<p>=======================================================================</p>\r\n<p>Installation for Unix and Windows</p>\r\n<ol>\r\n<li><span style=\"font-size: 14px;\">Download the latest SAP Host Agent 7.22 from the&#160;SAP Software Download Center at&#160;</span><a target=\"_blank\" href=\"https://support.sap.com/swdc\" style=\"font-size: 14px;\">https://support.sap.com/swdc<br /><br /></a>In the navigation bar, choose:<br />&#160;-&gt; SAP Software Download Center<br />&#160;-&gt; Support Packages and Patches<br />&#160;-&gt; Browse our Download Catalog<br />&#160;-&gt; SAP Technology Components<br />&#160;-&gt; SAP HOST AGENT<br />&#160;-&gt; SAP HOST AGENT 7.22<br />&#160;-&gt; &lt;your operating system&gt;<br /><br /></li>\r\n<li><span style=\"font-size: 14px;\">Create a temporary directory (e.g. /tmp/ha), and copy the package SAPHOSTAGENT&lt;PatchNr&gt;.SAR to this directory<br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">Change directory to the temporary directory (cd /tmp/ha)<br /><br /></span></li>\r\n<li>In this directory, extract the SAPHOSTAGENT&lt;PatchNr&gt;.SAR via sapcar:<br /><br /><strong>[PATH_TO_SAPCAR]/SAPCAR -xvf SAPHOSTAGENT&lt;PatchNr&gt;.SAR<br /><br /></strong></li>\r\n<li><span style=\"font-size: 14px;\">Use<br /><br /></span><strong>./saphostexec -install<br /><br /></strong>and follow the installation procedure You can now remove the temporary directory with his content (rm -rf /tmp/ha)</li>\r\n</ol>\r\n<p>=======================================================================<br /><br />Upgrading SAP Host Agent<br />To upgrade the Host Agent, follow the steps 1 to 4 of the installation and use <strong>./saphostexec -upgrade</strong> as fifth step.</p>\r\n<p>Alternatively, extracting the SAP Host Agent SAR archive can be omitted by using this upgrade command:<br /><strong>[SAP Host Agent EXE DIR]/saphostexec -upgrade -archive &lt;Path to SAP Host Agent SAR&gt;</strong></p>\r\n<p><br />=======================================================================<br /><br />Installation for IBM i<br />Prerequisites</p>\r\n<ul>\r\n<li><span style=\"font-size: 14px;\">You must be logged on as a user profile with special authorities SECADM and *ALLOBJ, for example as user profile QSECOFR.<br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">Prior to SAPHOSTAGENT release 7.21, patch level 43, you must ensure that user profile QSECOFR is enabled and has a valid, non-expired password in order to install and run SAPHOSTAGENT. With SAPHOSTAGENT release 7.21, patch level 43, this restriction has been lifted.<br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">You have downloaded the SAPHOSTAGENT&lt;SP-version&gt;.SAR archive.<br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">Option 33 of the operating system must be installed. Use menu GO LICPGM to check whether the option is installed and install it if required. If option 33 of the operating system was installed during this step, we recommend that you install the latest PTFs for your operating system release according to <a target=\"_blank\" href=\"/notes/83292\">SAP Note 83292</a>.<br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">The system startup program (specified in system value QSTRUPPGM) must contain the STRSBS command to start subsystem QSYS/QUSRWRK. This is needed because SAPHOSTAGENT will be started as an auto-start job in subsystem QSYS/QUSRWRK.<br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">If user profile R3GROUP does not exist on your server, it will be created during the installation of the Host Agent. If you have already installed SAP systems on other servers, we recommend that you use the same group ID (GID) for all sapsys and R3GROUP groups in the system landscape. To obtain the group ID (GID) for R3GROUP on another IBM i server in your landscape, enter the command DSPUSRPRF USRPRF(R3GROUP) and scroll down until you see the value for Group ID number.</span></li>\r\n</ul>\r\n<p>Installation</p>\r\n<ol>\r\n<li><span style=\"font-size: 14px;\">If it does not exist on your server, create the directory /tmp/hostctrl and then copy or move the downloaded archive SAPHOSTAGENT&lt;SP-version&gt;.SAR and SAPCAR into the directory /tmp/hostctrl.<br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">Enter the command CALL PGM(QP2TERM) to start a PASE interactive terminal session.<br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">Make the directory that contains SAPCAR and the archive SAPHOSTAGENT&lt;SP-version&gt;.SAR your current directory using the following command:<br /><br /><strong>cd /tmp/hostctrl</strong><br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">Decompress the archive SAPHOSTAGENT&lt;SP-version&gt;.SAR with the SAPCAR tool using the following command:<br /> <br /><strong>[PATH_TO_SAPCAR]/SAPCAR -xvf SAPHOSTAGENT&lt;SP-version&gt;.SAR</strong><br /><br /></span></li>\r\n<li><span style=\"font-size: 14px;\">To install saphostcontrol enter the following command:<br /><br /></span><strong>./saphostexec -install -gid &lt;gid&gt;<br /><br /></strong></li>\r\n<li><span style=\"font-size: 14px;\">If you have already installed SAP systems on other servers, we recommend that you use the same group ID (GID) for all sapsys or R3GROUP groups in the system landscape. To do this, enter your landscape system GID into &lt;gid&gt; on the above command. If user profile R3GROUP already exists, or if you want the saphostcontrol installation to automatically generate a new group ID, enter the command:<br /><br /></span><strong>saphostexec -install<br /><br /></strong>without the addition<br /><br /><strong>-gid &lt;gid&gt;<br /><br /></strong></li>\r\n<li><span style=\"font-size: 14px;\">Leave the PASE interactive terminal session using function key F3.</span></li>\r\n</ol>\r\n<p><br />If it did not already exist, user profile R3GROUP was created during the installation. Even though SAP Host Agent does not require special authorities, we recommend that you grant the required authorities for system APIs that need to be authorized for user profile R3GROUP for your SAP system now. For more information, see <a target=\"_blank\" href=\"/notes/175852\">SAP Note 175852</a>.<br /><br />Note:<br />The SAPHOSTAGENT is running in subsystem QUSRWRK. Make sure that your system startup program is starting subsystem QUSRWRK after an IPL.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CCM-MON (CCMS Monitoring & Alerting)"}, {"Key": "Other Components", "Value": "BC-CCM-MON-OS (Operating System Monitoring Tool)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D042506)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D042506)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001031096/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001031096/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001031096/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001031096/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001031096/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001031096/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001031096/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001031096/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001031096/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "893215", "RefComponent": "BC-OP-AS4", "RefTitle": "SAP Start Service on iSeries", "RefUrl": "/notes/893215"}, {"RefNumber": "684106", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684106"}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227"}, {"RefNumber": "1887279", "RefComponent": "BC-OP-S390", "RefTitle": "DB2-z/OS: sapdbctrl: Prerequisites and Configuration", "RefUrl": "/notes/1887279"}, {"RefNumber": "1881267", "RefComponent": "BC-OP-S390", "RefTitle": "DB2-z/OS: SAP Host Agent problem on z/OS", "RefUrl": "/notes/1881267"}, {"RefNumber": "1841977", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1841977"}, {"RefNumber": "1813548", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1813548"}, {"RefNumber": "1798556", "RefComponent": "BC-VCM-LVM", "RefTitle": "SAP Landscape Virtualization Management 2.0 standard edition", "RefUrl": "/notes/1798556"}, {"RefNumber": "1783702", "RefComponent": "BC-VCM-LVM", "RefTitle": "SAP Landscape Virtualization Management 2.0 - Enterprise Edition", "RefUrl": "/notes/1783702"}, {"RefNumber": "1776801", "RefComponent": "BC-INS", "RefTitle": "Inst.Systems Based on NW 7.0 incl. EHPs:IBM i", "RefUrl": "/notes/1776801"}, {"RefNumber": "1738283", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "SAP Solution Manager - Monitoring of database clusters", "RefUrl": "/notes/1738283"}, {"RefNumber": "1709155", "RefComponent": "BC-VCM-LVM", "RefTitle": "System Provisioning with SAP Landscape Management", "RefUrl": "/notes/1709155"}, {"RefNumber": "1707012", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: Prepare Installation Kits for Usage with DCK Kernel", "RefUrl": "/notes/1707012"}, {"RefNumber": "1692228", "RefComponent": "MDM-FN-CON", "RefTitle": "The specified SAP instance does not exist on server <server>", "RefUrl": "/notes/1692228"}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173"}, {"RefNumber": "1666481", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1666481"}, {"RefNumber": "1656096", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Automounting QFILESvr.400 links using SAPINIT", "RefUrl": "/notes/1656096"}, {"RefNumber": "1651892", "RefComponent": "SV-SMG-DIA", "RefTitle": "SAP Solution Manager 7.1 E2E RCA Setup for IBM WebSphere", "RefUrl": "/notes/1651892"}, {"RefNumber": "1641062", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "Single component update and patch scenarios in SUM", "RefUrl": "/notes/1641062"}, {"RefNumber": "1638811", "RefComponent": "BC-CST-STS", "RefTitle": "Potential remote code execution in SAP Startup Service", "RefUrl": "/notes/1638811"}, {"RefNumber": "1633036", "RefComponent": "SV-SMG-DIA", "RefTitle": "SAP Solution Manager 7.2 E2E RCA Setup for Apache Tomcat", "RefUrl": "/notes/1633036"}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755"}, {"RefNumber": "1630050", "RefComponent": "BC-VCM-LVM", "RefTitle": "SAP NetWeaver Landscape Virtualization Management 1.0, standard edition", "RefUrl": "/notes/1630050"}, {"RefNumber": "1625203", "RefComponent": "HAN-DB", "RefTitle": "saphostagent / sapdbctrl for NewDB", "RefUrl": "/notes/1625203"}, {"RefNumber": "1622665", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: SAP Database Performance Collector", "RefUrl": "/notes/1622665"}, {"RefNumber": "1611933", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1611933"}, {"RefNumber": "1581170", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Security concept on IBM i", "RefUrl": "/notes/1581170"}, {"RefNumber": "1527538", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1527538"}, {"RefNumber": "1502670", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide - SAP NetWeaver 7.3", "RefUrl": "/notes/1502670"}, {"RefNumber": "1489787", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1489787"}, {"RefNumber": "1481805", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: ST06 does not display any hardware information", "RefUrl": "/notes/1481805"}, {"RefNumber": "1462332", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.3 - Adaptive Computing Controller Collect. Note", "RefUrl": "/notes/1462332"}, {"RefNumber": "1439391", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAPOSCOL Service deleted after SAPHostAgent Installation", "RefUrl": "/notes/1439391"}, {"RefNumber": "1393207", "RefComponent": "BI-BIP-INS", "RefTitle": "Configuring SAP BusinessObjects Edge 3.1 Monitoring", "RefUrl": "/notes/1393207"}, {"RefNumber": "1375863", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.2 - Adaptive Computing Controller Collect. Note", "RefUrl": "/notes/1375863"}, {"RefNumber": "1375494", "RefComponent": "BC-OP-NT", "RefTitle": "SAP system does not start after applying SAP kernel patch", "RefUrl": "/notes/1375494"}, {"RefNumber": "1357901", "RefComponent": "SV-SMG-DIA", "RefTitle": "RCA: Managed System Setup for SAP BusinessObjects Enterprise", "RefUrl": "/notes/1357901"}, {"RefNumber": "1315499", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide - SAP NetWeaver PI 7.1 EHP1", "RefUrl": "/notes/1315499"}, {"RefNumber": "1306787", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Standalone installation of CCMS agent", "RefUrl": "/notes/1306787"}, {"RefNumber": "1250285", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAP Host Agent problems on HP-UX with NSS LDAP", "RefUrl": "/notes/1250285"}, {"RefNumber": "1180797", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Error messages in SM21 and SOAMANAGER (host agent)", "RefUrl": "/notes/1180797"}, {"RefNumber": "1153726", "RefComponent": "BC-DB-SDB", "RefTitle": "LiveUpdate hangs", "RefUrl": "/notes/1153726"}, {"RefNumber": "1119595", "RefComponent": "BC-VCM-LVM-HP", "RefTitle": "SAP ACC with HP Storage Essentials SRM", "RefUrl": "/notes/1119595"}, {"RefNumber": "1119255", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide - SAP NetWeaver PI 7.1", "RefUrl": "/notes/1119255"}, {"RefNumber": "1113545", "RefComponent": "BC-CCM-HAG", "RefTitle": "Problems with SAP Host Agent", "RefUrl": "/notes/1113545"}, {"RefNumber": "1102124", "RefComponent": "BC-OP-LNX", "RefTitle": "SAPOSCOL on Linux: Enhanced function", "RefUrl": "/notes/1102124"}, {"RefNumber": "1064968", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "ST06: Unable to start saposcol", "RefUrl": "/notes/1064968"}, {"RefNumber": "1039455", "RefComponent": "BC-CCM-MON", "RefTitle": "AL11: DIR_CCMS and DIR_SAPHOSTAGENT", "RefUrl": "/notes/1039455"}, {"RefNumber": "1008828", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.1 PI / Adaptive Computing Controller Collective Note", "RefUrl": "/notes/1008828"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2280946", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Error: \"Invalid root in registry key\" when registering SUM to SAP Host Agent", "RefUrl": "/notes/2280946 "}, {"RefNumber": "2189669", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "INPUT-OS-USER-PASSWORDS and sapcontrol: SapSS<PERSON><PERSON>ni<PERSON> failed -SSSLERR_LIB_NOT_FOUND / SSSLERR_PSE_ERROR", "RefUrl": "/notes/2189669 "}, {"RefNumber": "3214491", "RefComponent": "BC-INS-AS4", "RefTitle": "SWPM: step adjustNametab finished with status TST_ERROR", "RefUrl": "/notes/3214491 "}, {"RefNumber": "2129260", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "Incorrect IP Address Assignment for Host Availability Check in Technical Monitoring", "RefUrl": "/notes/2129260 "}, {"RefNumber": "2348537", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "How to troubleshoot error \"cannot connect to service sapstartsrv\" in Solution manager 7.2", "RefUrl": "/notes/2348537 "}, {"RefNumber": "2949666", "RefComponent": "BC-UPG-SLC", "RefTitle": "Starting SUM tool hangs or fails with \"Permission policy violation\"", "RefUrl": "/notes/2949666 "}, {"RefNumber": "2882177", "RefComponent": "BC-CCM-HAG", "RefTitle": "ERROR => Select failed, an hang up occurred, probably connection broken [HostExecServ 404]\" while the SAP Host Agent has an error to connect to Diagnostic Agent - Solution Manager 7.2", "RefUrl": "/notes/2882177 "}, {"RefNumber": "2507791", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Error \"NameNotFoundException: Object not found in lookup of HOST_AGENT_STATUS_RECORDER.\" While there is no data in transaction report - Solution Manager 7.1", "RefUrl": "/notes/2507791 "}, {"RefNumber": "2678620", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Error: \"The command 'hostexecstart' from tool 'hostexecstart' could not be executed successfully\" during SUM upgrade", "RefUrl": "/notes/2678620 "}, {"RefNumber": "2828626", "RefComponent": "HAN-LM-PLT", "RefTitle": "Web user interface hangs when validating the sidadm password from hdblcm", "RefUrl": "/notes/2828626 "}, {"RefNumber": "2823026", "RefComponent": "BC-DB-SYB", "RefTitle": "saphostctrl update failed for when updating ASE -  SAP ASE for BS", "RefUrl": "/notes/2823026 "}, {"RefNumber": "2501433", "RefComponent": "SV-SMG-MON-ALR-PRA", "RefTitle": "Error : \"WebServiceInvocationException: JsfOpenShm failed: object not found in the Diagnostic Agent log\" - Solution Manager 7.2", "RefUrl": "/notes/2501433 "}, {"RefNumber": "2775397", "RefComponent": "BC-OP-AS4", "RefTitle": "SAPOSCOL failing to start automatically on startup on IBM i", "RefUrl": "/notes/2775397 "}, {"RefNumber": "2789210", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Error \"'disp+work' in dir '<current tool path>' does not exist\" in upgrade phase TOOLVERSXML_KERNELPARAMS_ROADMAP with hostagent 721 PL 41", "RefUrl": "/notes/2789210 "}, {"RefNumber": "2284028", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "SUM SL Common UI : Troubleshooting problems with the new SUM UI", "RefUrl": "/notes/2284028 "}, {"RefNumber": "2725568", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Error: The command 'disp+work' from tool 'disp+work' could not be executed", "RefUrl": "/notes/2725568 "}, {"RefNumber": "1928584", "RefComponent": "BC-CCM-HAG", "RefTitle": "\"Start of HostService failed\" error - SAPHOSTAGENT start failed", "RefUrl": "/notes/1928584 "}, {"RefNumber": "1627564", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "SAPOSCOL:  Clearing Shared Memory and Updating Saposcol", "RefUrl": "/notes/1627564 "}, {"RefNumber": "2659092", "RefComponent": "BC-OP-AIX", "RefTitle": "Zombie processes left by saposcol in AIX", "RefUrl": "/notes/2659092 "}, {"RefNumber": "2384542", "RefComponent": "BC-DB-SYB", "RefTitle": "saphostctrl failed with failed with 'exit code 1' or 'exit code 255'  -SAP ASE for BS", "RefUrl": "/notes/2384542 "}, {"RefNumber": "2616761", "RefComponent": "BC-INS-AS4", "RefTitle": "CPD0084  ILE not valid for parameter MODE", "RefUrl": "/notes/2616761 "}, {"RefNumber": "2164753", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS Monitoring of non-SAP Server (eg. Standalone DB)", "RefUrl": "/notes/2164753 "}, {"RefNumber": "2569563", "RefComponent": "BC-CCM-HAG", "RefTitle": "A Physical Host is report as Logical Host in LMDB \"Name Type : Logical\" - Solution Manager 7.2", "RefUrl": "/notes/2569563 "}, {"RefNumber": "2528633", "RefComponent": "BC-CCM-HAG", "RefTitle": "Host Agent version cannot be checked due to HTTP Code 500 in the SAPHostControl web service", "RefUrl": "/notes/2528633 "}, {"RefNumber": "2542604", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAP Host Agent outsidedicovery.log shows message 'DiscoverSDA: SDA Path not available, Discovery not possible'", "RefUrl": "/notes/2542604 "}, {"RefNumber": "2559618", "RefComponent": "SV-SMG-DIA-WLY-EMS", "RefTitle": "Error: \"Status of retrieval of existing installation: Path is missing (path , agent N/A), No agent found (N/A)\" While Importing Introscope Enterprise Manager to SAP Solution Manager", "RefUrl": "/notes/2559618 "}, {"RefNumber": "2536017", "RefComponent": "SV-SMG-DIA-APP", "RefTitle": "Error \"Time out when waiting for Host Agent to answer an already running process/call.This call is cancelled.\" While the Sap Host Agent is not connecting to the Diagnostic Agent - Solution Manager 7.2", "RefUrl": "/notes/2536017 "}, {"RefNumber": "2510439", "RefComponent": "BC-CCM-HAG", "RefTitle": "Error: \"soap_check_permission authentication: ( sapadm, GetComputerSystem ) FAILED [DefaultOpera 163]  Invalid Credentials pam_authenticate ( sapadm ) failed : Permission denied\" While Sap Host Agent is not connect - Solution manager 7.2", "RefUrl": "/notes/2510439 "}, {"RefNumber": "2366969", "RefComponent": "SV-SMG-MON-ALR-PRA", "RefTitle": "Technical Monitoring: Wrong values for Free Memory metric", "RefUrl": "/notes/2366969 "}, {"RefNumber": "2216601", "RefComponent": "SV-SMG-INS-DIA", "RefTitle": "Invalid Response Code (403) error occurs when checking SAP Host Agent in step \"Assign Diagnostics Agent\" - SAP Solution Manager 7.1 and 7.2", "RefUrl": "/notes/2216601 "}, {"RefNumber": "2455499", "RefComponent": "SV-SMG-DIA-APP", "RefTitle": "Error: \"Caused by: javax.xml.ws.soap.SOAPFaultException: Permission denied / soap_check_permission authentication\" while metric collection by Diagnostic Agent is not working - Solution Manager 7.2", "RefUrl": "/notes/2455499 "}, {"RefNumber": "2384817", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Error: \"java.io.NotSerializableException: com.sun.xml.internal.messaging.saaj.soap.ver1_1.SOAPPart1_1Impl.\" While OS Command extractor is not working - Solution manager 7.2", "RefUrl": "/notes/2384817 "}, {"RefNumber": "2472854", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "Troubleshooting Metrics in System Monitoring where the SAP Host Agent is the Data Supplier", "RefUrl": "/notes/2472854 "}, {"RefNumber": "2467887", "RefComponent": "BC-DB-SYB", "RefTitle": "SUM error SQL2812 in phase MAIN_SWITCH/PARMVNT_VIEW - SAP ASE for Business Suite", "RefUrl": "/notes/2467887 "}, {"RefNumber": "2463083", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "VMware errors in saposcol dev_coll trace file on Linux", "RefUrl": "/notes/2463083 "}, {"RefNumber": "3246237", "RefComponent": "BC-CCM-HAG", "RefTitle": "Accumulation of /usr/sap/hostctrl/work/shaprotected########## directories", "RefUrl": "/notes/3246237 "}, {"RefNumber": "2130510", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAP Host Agent 7.21", "RefUrl": "/notes/2130510 "}, {"RefNumber": "3139184", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: systemd integration for sapstartsrv and SAP Host Agent", "RefUrl": "/notes/3139184 "}, {"RefNumber": "3093121", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAP Host Agent 7.22 PL54", "RefUrl": "/notes/3093121 "}, {"RefNumber": "3076443", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAP Host Agent 7.22 PL53", "RefUrl": "/notes/3076443 "}, {"RefNumber": "3061768", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Illegal instruction(coredump) during saphostexec -install or saphostexec -upgrade", "RefUrl": "/notes/3061768 "}, {"RefNumber": "3061366", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAP Host Agent 7.22", "RefUrl": "/notes/3061366 "}, {"RefNumber": "3011978", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAP Host Agent PL51", "RefUrl": "/notes/3011978 "}, {"RefNumber": "2993150", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAP Host Agent 7.21 PL50", "RefUrl": "/notes/2993150 "}, {"RefNumber": "2847437", "RefComponent": "BC-DB-ORA", "RefTitle": "Older Versions:  SAP Software and Oracle Exadata", "RefUrl": "/notes/2847437 "}, {"RefNumber": "2800495", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: How to perform an SAP ASE rolling update with SAP Host Agent 7.21 PL43 and later", "RefUrl": "/notes/2800495 "}, {"RefNumber": "2800483", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Upgrade / Update SAP ASE with SAP Host Agent 7.21 PL43 and later", "RefUrl": "/notes/2800483 "}, {"RefNumber": "2666134", "RefComponent": "BC-CCM-HAG", "RefTitle": "Sapdsigner: change handling of zip archives", "RefUrl": "/notes/2666134 "}, {"RefNumber": "1877524", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: SAP LT Replication Server: Use of IBM Db2 for i (DB4) as non-SAP source", "RefUrl": "/notes/1877524 "}, {"RefNumber": "2567804", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS/ST06: OS-Monitoring of SAP HANA or standalone databases", "RefUrl": "/notes/2567804 "}, {"RefNumber": "2455973", "RefComponent": "BC-CCM-HAG", "RefTitle": "IBM i: Corrections in CIM Provider Classes SAP_ITSAMOSProcess and SAP_ITSAMFileSystem", "RefUrl": "/notes/2455973 "}, {"RefNumber": "2293960", "RefComponent": "BC-CCM-HAG", "RefTitle": "Upgrade to SAP Host Agent 7.21 Patch 11", "RefUrl": "/notes/2293960 "}, {"RefNumber": "2293992", "RefComponent": "BC-CCM-HAG", "RefTitle": "Error when accessing SL Common UI of SUM via SAP Host Agent", "RefUrl": "/notes/2293992 "}, {"RefNumber": "1982469", "RefComponent": "BC-DB-SYB", "RefTitle": "SYB: Updating SAP ASE with saphostctrl", "RefUrl": "/notes/1982469 "}, {"RefNumber": "2203184", "RefComponent": "BC-CCM-MON-SHM", "RefTitle": "CCMS: agents fail to display permitted file content, logical directory or profile, wrong measurement of ResponseTimeDialogRFC", "RefUrl": "/notes/2203184 "}, {"RefNumber": "1309499", "RefComponent": "SV-SMG-SER", "RefTitle": "Hardware Capacity Analysis in SAP Services", "RefUrl": "/notes/1309499 "}, {"RefNumber": "2180367", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "How to run saposcol OS collector correctly", "RefUrl": "/notes/2180367 "}, {"RefNumber": "2179798", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Saposcol 720 run out of maintenance", "RefUrl": "/notes/2179798 "}, {"RefNumber": "2125774", "RefComponent": "BC-CCM-MON-SHM", "RefTitle": "CCMS: SAPHOSTAGENT does not send communication status to Central Monitoring System", "RefUrl": "/notes/2125774 "}, {"RefNumber": "2104359", "RefComponent": "BC-OP-AS4", "RefTitle": "OS/400 SAPHOSTAGENT terminates after a few days", "RefUrl": "/notes/2104359 "}, {"RefNumber": "2047924", "RefComponent": "BC-OP-S390", "RefTitle": "DB2-z/OS:CCMS:HAG: CIM-Provider Enablement for z/OS", "RefUrl": "/notes/2047924 "}, {"RefNumber": "2048156", "RefComponent": "BC-OP-ZLNX", "RefTitle": "DB2-z/OS:CCMS:HAG: CIM-Provider Enablement for Linux on IBM z Systems", "RefUrl": "/notes/2048156 "}, {"RefNumber": "2039994", "RefComponent": "BC-VCM-LVM", "RefTitle": "Managing system landscapes with SAP Landscape Management Standard Edition", "RefUrl": "/notes/2039994 "}, {"RefNumber": "2039615", "RefComponent": "BC-VCM-LVM", "RefTitle": "Managing system landscapes with SAP Landscape Management Enterprise Edition", "RefUrl": "/notes/2039615 "}, {"RefNumber": "1590515", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP Software and Oracle Exadata", "RefUrl": "/notes/1590515 "}, {"RefNumber": "1862333", "RefComponent": "SV-SMG-DIA-SRV-AGT", "RefTitle": "Common Host Agent issues displayed in Agent Administration", "RefUrl": "/notes/1862333 "}, {"RefNumber": "1453112", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agent and kernel patches", "RefUrl": "/notes/1453112 "}, {"RefNumber": "1499408", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: Installation Fails Due to Incomplete Kernel Update", "RefUrl": "/notes/1499408 "}, {"RefNumber": "1937227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Missing history data in ST06", "RefUrl": "/notes/1937227 "}, {"RefNumber": "1113545", "RefComponent": "BC-CCM-HAG", "RefTitle": "Problems with SAP Host Agent", "RefUrl": "/notes/1113545 "}, {"RefNumber": "1776801", "RefComponent": "BC-INS", "RefTitle": "Inst.Systems Based on NW 7.0 incl. EHPs:IBM i", "RefUrl": "/notes/1776801 "}, {"RefNumber": "1709155", "RefComponent": "BC-VCM-LVM", "RefTitle": "System Provisioning with SAP Landscape Management", "RefUrl": "/notes/1709155 "}, {"RefNumber": "1625203", "RefComponent": "HAN-DB", "RefTitle": "saphostagent / sapdbctrl for NewDB", "RefUrl": "/notes/1625203 "}, {"RefNumber": "1315499", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide - SAP NetWeaver PI 7.1 EHP1", "RefUrl": "/notes/1315499 "}, {"RefNumber": "1119255", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide - SAP NetWeaver PI 7.1", "RefUrl": "/notes/1119255 "}, {"RefNumber": "1630050", "RefComponent": "BC-VCM-LVM", "RefTitle": "SAP NetWeaver Landscape Virtualization Management 1.0, standard edition", "RefUrl": "/notes/1630050 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1502670", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide - SAP NetWeaver 7.3", "RefUrl": "/notes/1502670 "}, {"RefNumber": "1887279", "RefComponent": "BC-OP-S390", "RefTitle": "DB2-z/OS: sapdbctrl: Prerequisites and Configuration", "RefUrl": "/notes/1887279 "}, {"RefNumber": "1102124", "RefComponent": "BC-OP-LNX", "RefTitle": "SAPOSCOL on Linux: Enhanced function", "RefUrl": "/notes/1102124 "}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755 "}, {"RefNumber": "1881267", "RefComponent": "BC-OP-S390", "RefTitle": "DB2-z/OS: SAP Host Agent problem on z/OS", "RefUrl": "/notes/1881267 "}, {"RefNumber": "1641062", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "Single component update and patch scenarios in SUM", "RefUrl": "/notes/1641062 "}, {"RefNumber": "1622665", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: SAP Database Performance Collector", "RefUrl": "/notes/1622665 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "19227", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "Retrieving the latest saposcol", "RefUrl": "/notes/19227 "}, {"RefNumber": "1633036", "RefComponent": "SV-SMG-DIA", "RefTitle": "SAP Solution Manager 7.2 E2E RCA Setup for Apache Tomcat", "RefUrl": "/notes/1633036 "}, {"RefNumber": "1375863", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.2 - Adaptive Computing Controller Collect. Note", "RefUrl": "/notes/1375863 "}, {"RefNumber": "1651892", "RefComponent": "SV-SMG-DIA", "RefTitle": "SAP Solution Manager 7.1 E2E RCA Setup for IBM WebSphere", "RefUrl": "/notes/1651892 "}, {"RefNumber": "1707012", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: Prepare Installation Kits for Usage with DCK Kernel", "RefUrl": "/notes/1707012 "}, {"RefNumber": "1462332", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.3 - Adaptive Computing Controller Collect. Note", "RefUrl": "/notes/1462332 "}, {"RefNumber": "1738283", "RefComponent": "BC-DB-DB6-CCM", "RefTitle": "SAP Solution Manager - Monitoring of database clusters", "RefUrl": "/notes/1738283 "}, {"RefNumber": "1692228", "RefComponent": "MDM-FN-CON", "RefTitle": "The specified SAP instance does not exist on server <server>", "RefUrl": "/notes/1692228 "}, {"RefNumber": "1257635", "RefComponent": "BC-DB-DB4", "RefTitle": "DB4: DBSL Direct Drive for DB2 for IBM i (DB2/400)", "RefUrl": "/notes/1257635 "}, {"RefNumber": "1306787", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Standalone installation of CCMS agent", "RefUrl": "/notes/1306787 "}, {"RefNumber": "1656096", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Automounting QFILESvr.400 links using SAPINIT", "RefUrl": "/notes/1656096 "}, {"RefNumber": "1119595", "RefComponent": "BC-VCM-LVM-HP", "RefTitle": "SAP ACC with HP Storage Essentials SRM", "RefUrl": "/notes/1119595 "}, {"RefNumber": "1039455", "RefComponent": "BC-CCM-MON", "RefTitle": "AL11: DIR_CCMS and DIR_SAPHOSTAGENT", "RefUrl": "/notes/1039455 "}, {"RefNumber": "1357901", "RefComponent": "SV-SMG-DIA", "RefTitle": "RCA: Managed System Setup for SAP BusinessObjects Enterprise", "RefUrl": "/notes/1357901 "}, {"RefNumber": "1581170", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Security concept on IBM i", "RefUrl": "/notes/1581170 "}, {"RefNumber": "1393207", "RefComponent": "BI-BIP-INS", "RefTitle": "Configuring SAP BusinessObjects Edge 3.1 Monitoring", "RefUrl": "/notes/1393207 "}, {"RefNumber": "1064968", "RefComponent": "BC-CCM-MON-OS", "RefTitle": "ST06: Unable to start saposcol", "RefUrl": "/notes/1064968 "}, {"RefNumber": "966719", "RefComponent": "BC-INS-AS4", "RefTitle": "SAP NetWeaver Inst. Based on Kernel 7.10: IBM DB2 for i5/OS", "RefUrl": "/notes/966719 "}, {"RefNumber": "1439391", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAPOSCOL Service deleted after SAPHostAgent Installation", "RefUrl": "/notes/1439391 "}, {"RefNumber": "1481805", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: ST06 does not display any hardware information", "RefUrl": "/notes/1481805 "}, {"RefNumber": "893215", "RefComponent": "BC-OP-AS4", "RefTitle": "SAP Start Service on iSeries", "RefUrl": "/notes/893215 "}, {"RefNumber": "1008828", "RefComponent": "BC-AC-7XX", "RefTitle": "ACC 7.1 PI / Adaptive Computing Controller Collective Note", "RefUrl": "/notes/1008828 "}, {"RefNumber": "1250285", "RefComponent": "BC-CCM-HAG", "RefTitle": "SAP Host Agent problems on HP-UX with NSS LDAP", "RefUrl": "/notes/1250285 "}, {"RefNumber": "1180797", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Error messages in SM21 and SOAMANAGER (host agent)", "RefUrl": "/notes/1180797 "}, {"RefNumber": "1153726", "RefComponent": "BC-DB-SDB", "RefTitle": "LiveUpdate hangs", "RefUrl": "/notes/1153726 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAPHOSTAGENT", "From": "7.10", "To": "7.10", "Subsequent": "X"}, {"SoftwareComponent": "SAPHOSTAGENT", "From": "7.11", "To": "7.11", "Subsequent": "X"}, {"SoftwareComponent": "SAPHOSTAGENT", "From": "7.20", "To": "7.20", "Subsequent": "X"}, {"SoftwareComponent": "SAPHOSTAGENT", "From": "7.21", "To": "7.21", "Subsequent": "X"}, {"SoftwareComponent": "SAPHOSTAGENT", "From": "7.22", "To": "7.22", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP HOST AGENT 7.20", "SupportPackage": "SP192", "SupportPackagePatch": "000192", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200011329&support_package=SP192&patch_level=000192"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}