{"Request": {"Number": "846106", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 478, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015896672017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000846106?language=E&token=A04C2C8E9A4E0416A485203E70E241C0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000846106", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000846106/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "846106"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.11.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-ISR-LOG"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Logistic"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Retail and Consumer Products", "value": "BW-BCT-ISR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-ISR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Logistic", "value": "BW-BCT-ISR-LOG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-ISR-LOG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "846106 - Exploding structured articles"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Data records are deleted from the data package in the start routines of InfoSources 2LIS_02_SCL, 2LIS_03_BX and 2LIS_03_BF.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>2lIS_02_SCL;2LIS_03_BF;2LIS_03_BX</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The start routines were implemented to explode structured articles in mySAP ERP 2005.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The start routines prevent the extracted transaction data in SAP BW from being updated several times, which would lead to incorrect values.<br /><br />The explosion of structured articles in mySAP ERP 2005 is possible for InfoSources 2LIS_02_SCL, 2LIS_03_BF and 2LIS_03_BX. For this, the WRF_BWEXT_STRUKTART implementation of the RS05_SAPI_BADI Business Add-In must be activated. This implementation explodes a document record into the individual components of a structured article.<br /><br />To ensure that the data is correct in the connected data targets also after the Business Add-In has been activated, the InfoSources specified above contain a start routine that separates the \"exploded\" and the \"unexploded\" data records. This separation occurs in the RSBCT_RFASH_MATERIAL_EXPLO function module that is called. The E_T_DATA_NOR parameter contains the unexploded structured article records and the E_T_DATA_EXP parameter contains the exploded structured article records. You can use the SPLIT parameter to control if the system returns the exploded records at all. If you set this parameter, the system also returns the exploded records while they are deleted in the standard call.<br /><br />The following scenarios are possible:<br />Case 1: You want to analyze only the unexploded structured article records in SAP BW.<br /><br />Solution: You can use the delivered Content without any further adjustments because the function module mentioned above ensures that the records are separated.<br /><br />Case 2: You want to analyze both the unexploded structured article records and the exploded structured article records in SAP BW.<br /><br />Solution: In this case, you should create separate data targets for the unexploded and the exploded structured article records. You can then implement the same source code in the start routine of the update of the relevant data target as is delivered in the start routine of the transfer rule in the Business Content. The E_T_DATA_EXP parameter is updated in the data target update of the exploded structured article records and the E_T_DATA_NOR parameter is updated in the data target update of the unexploded structured article records.<br />As the last step, you can then delete the start routine from the transfer rules.<br />Important: If you delete the start routine from the transfer rules, the data must be separated in all connected data targets because otherwise incorrect data may be created.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D023610)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D037986)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000846106/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846106/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846106/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846106/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846106/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846106/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846106/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846106/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000846106/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1325650", "RefComponent": "BW-BCT-ISR", "RefTitle": "Start routine missing from transformation for 2LIS_02_SCL", "RefUrl": "/notes/1325650"}, {"RefNumber": "1240567", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Start routine missing according to Note 846106", "RefUrl": "/notes/1240567"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}, {"RefNumber": "1325650", "RefComponent": "BW-BCT-ISR", "RefTitle": "Start routine missing from transformation for 2LIS_02_SCL", "RefUrl": "/notes/1325650 "}, {"RefNumber": "1240567", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Start routine missing according to Note 846106", "RefUrl": "/notes/1240567 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "353", "To": "353", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "702", "To": "702", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}