{"Request": {"Number": "3120536", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 222, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001721722021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003120536?language=E&token=EC3257498A190388B6A8F223AF3B4C12"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003120536", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003120536/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3120536"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.12.2021"}, "SAPComponentKey": {"_label": "Component", "value": "FI-LOC-FI-RU"}, "SAPComponentKeyText": {"_label": "Component", "value": "Russia"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Localizations", "value": "FI-LOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-LOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Financials", "value": "FI-LOC-FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-LOC-FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Russia", "value": "FI-LOC-FI-RU", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-LOC-FI-RU*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3120536 - S4TWL - Russia Manage Additional Payment Attributes app"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion or an upgrade to SAP S/4HANA, on-premise 1909 edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Russia, Payment order, additional information, Payment details,&#160;&#160;J_3RF_PDOC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note is relevant for you when your start release for the system conversion is an ERP release or SAP S/4HANA on-premise 1809 edition or lower releases.</p>\r\n<p>It is not relevant if your start release is S/4HANA on-premise 1909 or later.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Business Value</strong></p>\r\n<p>New solution to maintain document attributes for Payments to tax authorities before output&#160;in Payment Medium.</p>\r\n<p>New&#160;Manage Additional Payment Attributes&#160;app offers you the following benefits:</p>\r\n<ul>\r\n<li>Unified and simplified user experience via Fiori interface</li>\r\n<li>Improved Mobility. The switch to&#160;Fiori delivers advantages to mobile employees. Fiori&#8217;s simple interface lets users work with touch screens.</li>\r\n<li>Increases in Productivity &#8211; Fiori-designed interfaces are easier, so they enable more productive work.</li>\r\n<li>Integration with a Manage Journal Entry app that is main report to see and check postings.&#160;</li>\r\n</ul>\r\n<p><strong>Description</strong></p>\r\n<p>For tax payments attributes in S/4HANA on-premise 1909 and future releases&#160;Manage Additional Payment Attributes&#160;app has to be used:</p>\r\n<ul>\r\n<li>Find your Payment document in the&#160;Manage Journal Entries&#160;app and click on the journal entry number.</li>\r\n<li>Select&#160;Manage Additional Payment Attributes&#160;and enter payment attributes or change existing one.&#160;</li>\r\n</ul>\r\n<p>For more information see&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/45765e03c5d4435bb383396ab34279b2/2020.000/en-US/6be3639aedde466b9d85f58d207e7337.html\">Tax Payments</a>&#160;help page.</p>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>Manage Additional Payment Attributes app replaces the solution:</p>\r\n<ul>\r\n<li>Payment attributes screen during Financial Document posting and Financial Document change (Open FI Functional Modules J_3RF_PDOCV_2218 and J_3RF_PDOC1430)</li>\r\n</ul>\r\n<p>Note that depending on your SAP S/4HANA release and planned end of support date previous solution will become obsolete. After end of support date&#160;these solutions will not be updated by new legal changes or corrections. We highly recommend that you use the&#160;Fiori&#160;based&#160;apps&#160;instead of the old&#160;previous solution.</p>\r\n<p><strong>SAP S/4HANA on-premise 1809 and previous releases</strong><br />No planned end of support date</p>\r\n<p><strong>SAP S/4HANA on-premise 1909 and future releases</strong><br />Only&#160;Manage Tax Payment Attributes Fiori&#160;will be&#160;supported.&#160;<br />Payment attributes screen during Financial Document posting and Financial Document change (Open FI Functional Modules J_3RF_PDOCV_2218 and J_3RF_PDOC1430) is obsolete within these releases.</p>\r\n<p><strong>Required and Recommended Actions:</strong></p>\r\n<ul>\r\n<li>Set up new Fiori app &#8220;Manage Additional Payment Attributes (Refer to SAP Note 2931354).</li>\r\n<li>If you have made customer enhancements to the obsoleted Open FI function modules, check if you need to reimplement them in the new Fiori App. Custom logic can be implemented in the&#160;BAdI FIGLO_PMNT_FIELDS.</li>\r\n<li>Train your users.</li>\r\n</ul>\r\n<p><strong>How to Determine Relevancy</strong></p>\r\n<p>Check that you have configured the Open FI module&#160;J_3RF_PDOCV_2218&#160;for the event 2218 and Open FI module J_3RF_PDOC1430 for the event 1430 or you have entries in Payment Attributes table J_3RF_PLAT.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I067287)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I067287)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003120536/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003120536/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003120536/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003120536/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003120536/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003120536/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003120536/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003120536/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003120536/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2931354", "RefComponent": "FI-LOC-FI-RU", "RefTitle": "SAP RU-FI: Manage Additional Payment Attributes", "RefUrl": "/notes/2931354"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}