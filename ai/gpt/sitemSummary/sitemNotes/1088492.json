{"Request": {"Number": "1088492", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1312, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016361862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001088492?language=E&token=CEB20047C8CBA5A19716EBFB7099A448"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001088492", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001088492/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1088492"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.11.2007"}, "SAPComponentKey": {"_label": "Component", "value": "PY-AT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Austria"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "PY-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1088492 - Year-end legal change 2007/08: Heavy labor positions according to §5 VO of BMSG"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1088492&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1088492/D\" target=\"_blank\">/notes/1088492/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The report &quot;ELDA Notifications for Heavy Labor Position Acc. to Sec. 5 VO of BMSG&quot; (RPTSWAA0) must be started for the first time for 2007 in February 2008.<br />in the first place. This report is delivered for the year-end legal change 2007/2008.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Year-end legal change 2007/2008<br />Heavy Labor Position Acc. to Sec. 5 Reg. of BMSG<br />V_T5A4S<br />T5A4S<br />Infotype 3205<br />&quot;Heavy Labor Position A&quot;</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>With this SAP Note, you can download the attachment that is suitable for your relevant release:<br />For SAP ERP 2005:        L7DKnnnnnn.SAR<br />For SAP ERP 2004:        L6DKnnnnnn.SAR<br />For SAP R/3 Enterprise:  L6BKnnnnnn.SAR<br />For SAP 4.6C:            L9CKnnnnnn.SAR</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Action required for you:<br />To be able to generate the ELDA dataset correctly according to the report, preparatory Customizing is required.<br />Classify all your heavy labor positions according to the guidelines of § 1 Para. 1 Heavy Labor Ordinance.<br />After you have implemented the advance delivery in your system in accordance with this SAP Note, execute transaction SM30 for the view V_T5A4S and choose &quot;Documentation&quot;.<br /></p> <b>Submission deadline 28.02.2008 (transitional rule)</b><br /> <p>According to Mr Gottfried Kaspar (OÖ-GKK) at the ÖPWZ annual seminar on 13.11.2007 in Vienna, late reported heavy worklists 2007 are not sanctioned in 2008. If there are doubts about heavy jobs, they should be reported as such for safety reasons. Again, there are no consequences for the employer.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner", "Value": "D001888"}, {"Key": "Processor", "Value": "<PERSON> (D022643)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001088492/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "L9CK233795.SAR", "FileSize": "114", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000541212007&iv_version=0013&iv_guid=0F0265175FC908408D5D876CC5F9D169"}, {"FileName": "L6DK076669.SAR", "FileSize": "110", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000541212007&iv_version=0013&iv_guid=92D910BF403BE44F9780A0E1658F2AA3"}, {"FileName": "L6BK146131.SAR", "FileSize": "110", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000541212007&iv_version=0013&iv_guid=8A5FAA41D59E6447AF1DFF9DBB7025B9"}, {"FileName": "L7DK058499.SAR", "FileSize": "120", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000541212007&iv_version=0013&iv_guid=9DFB9EE4FB699B4F8CC96092FA7C150F"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1142236", "RefComponent": "PY-AT-PS", "RefTitle": "RPUELDA0: Record type SM and ELDA: Incorrect project abbreviation", "RefUrl": "/notes/1142236"}, {"RefNumber": "1141615", "RefComponent": "PY-AT", "RefTitle": "RPTSWAA0 Heavy Labor Position Sec. 5 VO BMSG (Various Adjustments)", "RefUrl": "/notes/1141615"}, {"RefNumber": "1129826", "RefComponent": "PY-AT", "RefTitle": "Pilot customer note: Advance delivery of year-end legal change 2007/2008", "RefUrl": "/notes/1129826"}, {"RefNumber": "1095338", "RefComponent": "PY-AT", "RefTitle": "Advance notice for year-end legal change 2007/2008 Austria", "RefUrl": "/notes/1095338"}, {"RefNumber": "1063776", "RefComponent": "PA-PA-XX", "RefTitle": "Infotypes in number range 3*** cannot be used", "RefUrl": "/notes/1063776"}, {"RefNumber": "1001295", "RefComponent": "PY-AT", "RefTitle": "Obligation to report heavy labor positions according to §5 VO of BMSG", "RefUrl": "/notes/1001295"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1063776", "RefComponent": "PA-PA-XX", "RefTitle": "Infotypes in number range 3*** cannot be used", "RefUrl": "/notes/1063776 "}, {"RefNumber": "1095338", "RefComponent": "PY-AT", "RefTitle": "Advance notice for year-end legal change 2007/2008 Austria", "RefUrl": "/notes/1095338 "}, {"RefNumber": "1141615", "RefComponent": "PY-AT", "RefTitle": "RPTSWAA0 Heavy Labor Position Sec. 5 VO BMSG (Various Adjustments)", "RefUrl": "/notes/1141615 "}, {"RefNumber": "1142236", "RefComponent": "PY-AT-PS", "RefTitle": "RPUELDA0: Record type SM and ELDA: Incorrect project abbreviation", "RefUrl": "/notes/1142236 "}, {"RefNumber": "1129826", "RefComponent": "PY-AT", "RefTitle": "Pilot customer note: Advance delivery of year-end legal change 2007/2008", "RefUrl": "/notes/1129826 "}, {"RefNumber": "1001295", "RefComponent": "PY-AT", "RefTitle": "Obligation to report heavy labor positions according to §5 VO of BMSG", "RefUrl": "/notes/1001295 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CD0", "URL": "/supportpackage/SAPKE46CD0"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47075", "URL": "/supportpackage/SAPKE47075"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50041", "URL": "/supportpackage/SAPKE50041"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60024", "URL": "/supportpackage/SAPKE60024"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1088492&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1088492/D\" target=\"_blank\">/notes/1088492/D</a>."}}}}