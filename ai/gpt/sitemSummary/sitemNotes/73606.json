{"Request": {"Number": "73606", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 716, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014502962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000073606?language=E&token=3413038D21241B152140A1BC414A2AE1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000073606", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000073606/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "73606"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 312}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.11.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-I18"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internationalization (I18N)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internationalization (I18N)", "value": "BC-I18", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "73606 - Supported Languages and Code Pages"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Which languages are supported in</p>\r\n<ul>\r\n<li>a&#160;Unicode SAP system</li>\r\n</ul>\r\n<ul>\r\n<li>a&#160;non-Unicode SAP system</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Afrikaans, Albanian, Arabic, Basque,&#160;Bosnian, Bulgarian, Burmese, Catalan, Chinese, Croatian, Czech, Danish, Dutch, English, Estonian, Farsi, Finnish, French, German, Greek, Hebrew, Hindi, Hungarian, Icelandic, Indonesian, Irish, Italian, Japanese, Kazakh, Korean, Kurdish, Lao, Latvian, Lithuanian, Malay, Norwegian, Polish, Portuguese, Raeto-Romance, Romanian, Russian, Serbian, Slovak, Slovenian, Sorbian, Spanish, Swahili, Swedish,&#160;Tagalog, Thai, Turkish, Ukrainian, Vietnamese, Walloon, <br />eurojapan, asianuni, asian unification, SAPunification, nagamasa, silkroad, trans siberian, diocletian, language combinations, blended code page, code page, single code page, CONVERSION_NOT_POSSIBLE, language installation, MDMP system installation, NLS, MDSP, MNLS,<br />RSCPINST, Unicode, non-Unicode</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Information about possible languages and language combinations in SAP systems. Statement about the current status of all internationalization solutions.</p>\r\n<p>This note does not handle&#160;localization (country specific development). Please refer to <a target=\"_blank\" href=\"http://service.sap.com/globalization\">http://service.sap.com/globalization</a>&#160;for further information about localization.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>General information</strong></p>\r\n<p>Details about language support in SAP Unicode- and non-Unicode systems are available in the file \"Supportedlanguages.zip\" which is attached to this note. It contains a list of all supported languages with information about the SAP language code, country, script, code pages, support level in SAP systems, and available translations.&#160;<br />Rules for the existence of an SAP language code for a language are:</p>\r\n<ul>\r\n<li>The language code must be defined in the ISO-639-1 or ISO-639-2 standard.</li>\r\n<li>The language code of a country-specific language must be defined as an official state language.<br />&#160;</li>\r\n</ul>\r\n<p><strong>Language support levels</strong></p>\r\n<p>In the attached language list \"Supportedlanguages.zip\" the following support levels are used:</p>\r\n<ul>\r\n<li>\"supported\"&#160;<br />Language can be used in SAP systems without relevant restrictions.</li>\r\n<li>\"supported with restrictions\"<br />Language can be used, but there is an SAP note that describes some restrictions.</li>\r\n<li>\"not supported\": <br />Language cannot be used in SAP systems, even though the character repertoire of the used script is covered by Unicode. Typical reasons are: Complex script with missing font or incomplete support in the frontend components and/or for printing.</li>\r\n<li>Empty field: <br />Information for this language is not yet available.</li>\r\n</ul>\r\n<p>Note: If a language cannot be used because font support is missing in (Windows-based) SAP frontends, do not use&#160;3rd party solutions offering input methods and fonts that are not based on Unicode. Only texts encoded in Unicode can be handled in a Unicode&#160;SAP system.</p>\r\n<p><strong>Activation of language codes <br /></strong><br />Any language code you want to use must be <strong>activated</strong> in the system via transaction I18N -&gt; I18N Customizing -&gt; I18N System configuration. Please read the online help (i-button) and SAP Note 42305 before you start activating languages. Per default, only English is active in I18N system configuration. The activation of language codes which are available only in Unicode systems is described in SAP note 895560.</p>\r\n<ul>\r\n<li>In non-Unicode systems 41 language codes&#160;are available for activation.</li>\r\n<li>In Unicode systems SAP language codes are available for all languages which have an ISO 639-2 language&#160;code (474 native and historical languages), and for 86 language-country combinations. However, the number of language codes&#160;which can be activated per system is limited. In SAP Basis 6.20/6.40 it is a maximum of 50 languages. As of SAP Basis 7.00 you can activate up to 200 or&#160; 240&#160;languages, depending on the exact Release and Support Package. See SAP Note 1021395 for details.</li>\r\n</ul>\r\n<p>Note:<br />Do not mistake the activation of language codes&#160;for a language import! Activating a language code in a system only means that it can be used in the system (e.g. to log on or enter data in this language). Translations must be imported separately. Please read SAP Note 330104 for details about available language packages.</p>\r\n<p><strong>Unicode Systems<br /></strong><br />For basic information about Unicode SAP systems please download the attachment \"Unicodesystems.pdf\".</p>\r\n<p>For information about languages which are technically supported in Unicode Systems, see file \"Supportedlanguages.zip\" which is attached to this note.</p>\r\n<p>Detailed information about Unicode development at SAP is available in the SAP Developer Network (SDN) Community within the SAP Community Network at https://sdn.sap.com/irj/sdn/i18n. There is a section on Unicode where you will find links to the official documentation for the conversion of existing non-Unicode SAP systems to Unicode. See SAP Note 745030 for the current status of Unicode and SAP solutions.</p>\r\n<p><strong>Conversion of non-Unicode Systems to Unicode</strong></p>\r\n<p>Conversion of Single Code Page SAP systems to Unicode is easy and requires only a few preparation steps. Please read SAP Note 1051576 and use the Single Code Page Conversion Guide attached to the Note for a Single Code Page Conversion Project.</p>\r\n<p>Conversion of MDMP SAP systems&#160;&#160;is a complex procedure which should not be done without using the Unicode Conversion Guide for MDMP systems! Read SAP Note 551344 and use the applicable MDMP Conversion Guide attached to the Note. Support of an experienced SAP consultant is also recommended. For target release SAP NetWeaver 7.0 and SAP NetWeaver 7.0 including EhP 1 and 2 you can use the Combined Upgrade &amp; Unicode Conversion solution. Read SAP Note 928729 for details.</p>\r\n<p><strong>Non-Unicode Systems</strong></p>\r\n<p>Release dependent restrictions for non-Unicode systems:</p>\r\n<ul>\r\n<li>Non-Unicode&#160;is not&#160;supported for all systems based on a release greater than SAP Netweaver 7.40 (see SAP Note 2033243 for details).<br />Upgrades up to SAP Netweaver 7.40 (including all Enhancement Packages)&#160;are not recommended but currently still possible.<br /><br /></li>\r\n<li>Starting with SAP NetWeaver 7.0 SR3 SAP no longer allows the installation of new non-Unicode SAP systems. This rule applies also for all applications (like ERP, SRM, SCM, CRM, PLM ...) on top of such a SAP NetWeaver release.<br /><br /></li>\r\n<li>Starting with SAP NetWeaver 7.0 support for MDMP and Blended Code Page systems is stopped. SAP systems with more than one system code page and/or with Blended Code Pages must be converted to Unicode using reliable tools delivered by SAP. An additional option is to combine the Unicode conversion with a release upgrade, thus keeping system downtime at a minimum. Read SAP Note 928729 for more information about combined upgrade and Unicode conversion solutions.<br />Lower releases: MDMP configurations are not supported by mySAP CRM (SAP Note 718324), mySAP BW (SAP Note 563975), my SAP SRM (SAP Note 819426) and mySAP SCM (SAP Note 452762).<br /><br /></li>\r\n<li>In addition, Unicode is the mandatory system type:</li>\r\n<ul>\r\n<li>For SAP systems which deploy Java applications (for example J2EE applications, WebDynpro applications)</li>\r\n<li>For SAP ABAP systems which communicate with Java components (for example via the SAP Java Connector).<br /><span style=\"text-decoration: underline;\">Note:</span> On technical level the connection is possible for a limited set of code pages which is supported by the SAP Java Connector. See SAP Note 794411 for details. From business perspective, however, communication between ABAP and Java components is subject to many limitations, so that reliable business processes cannot be guaranteed. SAP assumes no responsibility or liability of any kind for problems that may arise from that. For more information see SAP Note 975768.</li>\r\n</ul>\r\n</ul>\r\n<p>For more information about the current status of blended code pages and MDMP installations read the attached document \"I18nCode_Page_Support.ppt\".</p>\r\n<p>You can find all information about non-Unicode system configuration in the file 'R3languages.pdf' which is attached to this note. It can also be downloaded from the SAP Service Marketplace <a target=\"_blank\" href=\"http://service.sap.com/\">http://service.sap.com</a>. Logon to SAP Service Marketplace, select \"SAP Notes\" in the support area. Search for the SAP Note 73606, and then select the Tab \"Attachments\" (scroll to the right to see and select the tab).</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-I18-UNI (Unicode)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021965)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I545153)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000073606/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000073606/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000073606/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000073606/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000073606/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000073606/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000073606/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000073606/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000073606/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Unicodesystems.pdf", "FileSize": "649", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008538842001&iv_version=0312&iv_guid=0E5A9EC190FFF346BDBBBFA684E43529"}, {"FileName": "R3languages.pdf", "FileSize": "376", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008538842001&iv_version=0312&iv_guid=15308D8FA4C6C14297E0BDFFA39C40A1"}, {"FileName": "I18nCode_Page_Support.ppt", "FileSize": "159", "MimeType": "application/vnd.ms-powerpoint", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008538842001&iv_version=0312&iv_guid=989B8BD7C635FE449EAFD6576D0CCF1B"}, {"FileName": "Supportedlanguages.xls", "FileSize": "173", "MimeType": "application/vnd.ms-excel", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008538842001&iv_version=0312&iv_guid=00109B36BC261EDD98B9B0702EA69E5C"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "991481", "RefComponent": "BC-I18-UNI", "RefTitle": "Database is using UTF-8 and not UTF-16 ?", "RefUrl": "/notes/991481"}, {"RefNumber": "988787", "RefComponent": "EHS-BD-TLS", "RefTitle": "Exporting phrase texts and specifications", "RefUrl": "/notes/988787"}, {"RefNumber": "975768", "RefComponent": "BC-I18", "RefTitle": "Deprecation of Java features with non-Unicode Backend", "RefUrl": "/notes/975768"}, {"RefNumber": "971364", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/971364"}, {"RefNumber": "96953", "RefComponent": "XX-CSC-PL", "RefTitle": "Implementing the Polish country version", "RefUrl": "/notes/96953"}, {"RefNumber": "959698", "RefComponent": "BC-I18-UNI", "RefTitle": "Twin Upgrade & Unicode Conversion FAQ", "RefUrl": "/notes/959698"}, {"RefNumber": "95190", "RefComponent": "FI-AP-AP-B", "RefTitle": "RFFOJP_T - change for blended code page", "RefUrl": "/notes/95190"}, {"RefNumber": "94724", "RefComponent": "BC-I18", "RefTitle": "User Definable Characters of SJIS, BIG5, GB2312-80", "RefUrl": "/notes/94724"}, {"RefNumber": "938738", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using ambiguous blended code page", "RefUrl": "/notes/938738"}, {"RefNumber": "938737", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using blended code page", "RefUrl": "/notes/938737"}, {"RefNumber": "928729", "RefComponent": "BC-I18-UNI", "RefTitle": "Combined Upgrade & Unicode Conversion (CU&UC)", "RefUrl": "/notes/928729"}, {"RefNumber": "895560", "RefComponent": "BC-I18-UNI", "RefTitle": "Support for languages only available in Unicode systems", "RefUrl": "/notes/895560"}, {"RefNumber": "881781", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode/non-Unicode RFC language and code page assignment", "RefUrl": "/notes/881781"}, {"RefNumber": "85363", "RefComponent": "BC-I18", "RefTitle": "Database Scan Tool for basis release < 6.20", "RefUrl": "/notes/85363"}, {"RefNumber": "845233", "RefComponent": "BC-I18-UNI", "RefTitle": "Use of Input Method Editors (IME) in Unicode System", "RefUrl": "/notes/845233"}, {"RefNumber": "83196", "RefComponent": "BC-I18", "RefTitle": "TEXTENV_CODEPAGE_ / _LANGUAGE_NOT_ALLOWED", "RefUrl": "/notes/83196"}, {"RefNumber": "823110", "RefComponent": "SLL-LEG", "RefTitle": "GTS: UNICODE and Multi Display Multi Processing (MDMP)", "RefUrl": "/notes/823110"}, {"RefNumber": "819426", "RefComponent": "SRM-EBP", "RefTitle": "MDMP implementation not supported for mySAP SRM", "RefUrl": "/notes/819426"}, {"RefNumber": "81892", "RefComponent": "XX-CSC-XX", "RefTitle": "Implementating a new country version", "RefUrl": "/notes/81892"}, {"RefNumber": "80727", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting non-LATIN-1 texts", "RefUrl": "/notes/80727"}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991"}, {"RefNumber": "794411", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "Supported codepages of SAP Java Connector 2.1 and 6.x", "RefUrl": "/notes/794411"}, {"RefNumber": "78015", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on the language import 3.1H", "RefUrl": "/notes/78015"}, {"RefNumber": "765475", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: Troubleshooting", "RefUrl": "/notes/765475"}, {"RefNumber": "764480", "RefComponent": "BC-I18", "RefTitle": "SAP WinGUI I18N Trouble Shooting", "RefUrl": "/notes/764480"}, {"RefNumber": "747036", "RefComponent": "BC-I18", "RefTitle": "mySAP ERP 2004 Upgrade for R/3 MDMP Customers", "RefUrl": "/notes/747036"}, {"RefNumber": "745030", "RefComponent": "BC-I18-UNI", "RefTitle": "MDMP - Unicode Interfaces: Solution Overview", "RefUrl": "/notes/745030"}, {"RefNumber": "718324", "RefComponent": "CRM", "RefTitle": "Support restrictions for MDMP implementations of mySAP CRM", "RefUrl": "/notes/718324"}, {"RefNumber": "71103", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/71103"}, {"RefNumber": "700887", "RefComponent": "BC-I18", "RefTitle": "Printing: Language configuration", "RefUrl": "/notes/700887"}, {"RefNumber": "690105", "RefComponent": "BC-I18", "RefTitle": "Conversion of SAP ambiguous blended code pages", "RefUrl": "/notes/690105"}, {"RefNumber": "685023", "RefComponent": "BC-I18-BID", "RefTitle": "Material numbers must be left-to-right", "RefUrl": "/notes/685023"}, {"RefNumber": "614550", "RefComponent": "BC-I18", "RefTitle": "Troubleshooting BC-I18", "RefUrl": "/notes/614550"}, {"RefNumber": "587150", "RefComponent": "BC-I18-BID", "RefTitle": "Support of Arabic-script languages", "RefUrl": "/notes/587150"}, {"RefNumber": "579747", "RefComponent": "BC-I18", "RefTitle": "Support of HongKong Chinese", "RefUrl": "/notes/579747"}, {"RefNumber": "578293", "RefComponent": "BC-I18", "RefTitle": "TCPDB empty: SE92 has errors, which are hard to debug", "RefUrl": "/notes/578293"}, {"RefNumber": "563975", "RefComponent": "BW-SYS", "RefTitle": "BW and MDMP/BW and blended code pages not supported", "RefUrl": "/notes/563975"}, {"RefNumber": "548016", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion to Unicode", "RefUrl": "/notes/548016"}, {"RefNumber": "53665", "RefComponent": "BC-I18", "RefTitle": "How to generate xxxxyyyy.CDP files", "RefUrl": "/notes/53665"}, {"RefNumber": "45619", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/45619"}, {"RefNumber": "452762", "RefComponent": "BC-I18", "RefTitle": "Transliteration from Latin-2 to Latin-1", "RefUrl": "/notes/452762"}, {"RefNumber": "44219", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/44219"}, {"RefNumber": "437732", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/437732"}, {"RefNumber": "434586", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/434586"}, {"RefNumber": "434582", "RefComponent": "BC-INS-AS4", "RefTitle": "UPDNLSTBL allows more languages", "RefUrl": "/notes/434582"}, {"RefNumber": "432922", "RefComponent": "BC-I18", "RefTitle": "Support of GB-18030    (SAP-8401)", "RefUrl": "/notes/432922"}, {"RefNumber": "432115", "RefComponent": "BC-OP-AIX", "RefTitle": "AIX 4.3.2 and non-latin-1 ISO8859 languages", "RefUrl": "/notes/432115"}, {"RefNumber": "42305", "RefComponent": "BC-I18", "RefTitle": "RSCPINST (I18N configuration tool)", "RefUrl": "/notes/42305"}, {"RefNumber": "40815", "RefComponent": "BC-I18", "RefTitle": "Single or Double byte installed? (nls_check)", "RefUrl": "/notes/40815"}, {"RefNumber": "39763", "RefComponent": "BC-I18", "RefTitle": "setlocale on Windows/NT and table TCP0C", "RefUrl": "/notes/39763"}, {"RefNumber": "39745", "RefComponent": "BC-ABA-NL", "RefTitle": "setlocale on ReliantUNIX (SINIX) and table TCP0C", "RefUrl": "/notes/39745"}, {"RefNumber": "39739", "RefComponent": "BC-I18", "RefTitle": "setlocale on Solaris (SunOS) and table TCP0C", "RefUrl": "/notes/39739"}, {"RefNumber": "39718", "RefComponent": "BC-I18", "RefTitle": "setlocale on AIX and table TCP0C", "RefUrl": "/notes/39718"}, {"RefNumber": "395985", "RefComponent": "EHS", "RefTitle": "EH&S Native Language Support", "RefUrl": "/notes/395985"}, {"RefNumber": "39309", "RefComponent": "BC-I18", "RefTitle": "setlocale on Digital UNIX (OSF/1) and table TCP0C", "RefUrl": "/notes/39309"}, {"RefNumber": "387361", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/387361"}, {"RefNumber": "386792", "RefComponent": "BC-INS-AS4", "RefTitle": "INST: R/3 Installation on AS/400 (ASCII) 4.6C SR2", "RefUrl": "/notes/386792"}, {"RefNumber": "38677", "RefComponent": "BC-I18", "RefTitle": "setlocale on HP-UX and table TCP0C", "RefUrl": "/notes/38677"}, {"RefNumber": "328895", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/328895"}, {"RefNumber": "328885", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/328885"}, {"RefNumber": "322982", "RefComponent": "BC-CTS-LAN", "RefTitle": "Install language by means of the upgrade", "RefUrl": "/notes/322982"}, {"RefNumber": "31280", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/31280"}, {"RefNumber": "302228", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/302228"}, {"RefNumber": "2764571", "RefComponent": "BC-I18", "RefTitle": "I18n Language Configuration: Missing Language Codes", "RefUrl": "/notes/2764571"}, {"RefNumber": "217028", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217028"}, {"RefNumber": "214735", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/214735"}, {"RefNumber": "207101", "RefComponent": "BC-I18", "RefTitle": "Customer programs: Enhancements for MDMP", "RefUrl": "/notes/207101"}, {"RefNumber": "2051310", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Myanmar (Burmese)", "RefUrl": "/notes/2051310"}, {"RefNumber": "2051308", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Lao", "RefUrl": "/notes/2051308"}, {"RefNumber": "2033243", "RefComponent": "BC-I18-UNI", "RefTitle": "End of non-Unicode Support: Release Details", "RefUrl": "/notes/2033243"}, {"RefNumber": "200399", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/200399"}, {"RefNumber": "198489", "RefComponent": "BC-I18", "RefTitle": "Latin-4 support (Baltic languages)", "RefUrl": "/notes/198489"}, {"RefNumber": "195490", "RefComponent": "BC-I18", "RefTitle": "Automatic selection of GUI frontend code page", "RefUrl": "/notes/195490"}, {"RefNumber": "1904609", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Kazakh", "RefUrl": "/notes/1904609"}, {"RefNumber": "187864", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Locale Support on Linux", "RefUrl": "/notes/187864"}, {"RefNumber": "1843698", "RefComponent": "BC-I18", "RefTitle": "Locale check tool nls_check", "RefUrl": "/notes/1843698"}, {"RefNumber": "1824605", "RefComponent": "BC-I18", "RefTitle": "Suddenly many ########## in a line on the screen", "RefUrl": "/notes/1824605"}, {"RefNumber": "1753671", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Hindi", "RefUrl": "/notes/1753671"}, {"RefNumber": "174366", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/174366"}, {"RefNumber": "174268", "RefComponent": "EHS-SAF", "RefTitle": "EH&S 2.5B Support Packages: Additional documentation", "RefUrl": "/notes/174268"}, {"RefNumber": "168869", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/168869"}, {"RefNumber": "168083", "RefComponent": "BC-I18", "RefTitle": "ASSIGN_LENGTH_0 during Hot Package Import", "RefUrl": "/notes/168083"}, {"RefNumber": "168007", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/168007"}, {"RefNumber": "161667", "RefComponent": "BC-I18", "RefTitle": "Case mapping in multibyte systems", "RefUrl": "/notes/161667"}, {"RefNumber": "161662", "RefComponent": "BC-I18", "RefTitle": "MDMP: TANSLATE uses incorrect default code page", "RefUrl": "/notes/161662"}, {"RefNumber": "1564138", "RefComponent": "RE-FX", "RefTitle": "Message split incorrectly in case of complex code page", "RefUrl": "/notes/1564138"}, {"RefNumber": "155333", "RefComponent": "BC-ABA-LA", "RefTitle": "SORT ... AS TEXT leads to short dump or syslog entry", "RefUrl": "/notes/155333"}, {"RefNumber": "1489357", "RefComponent": "BC-I18-BID", "RefTitle": "No support for code page 1810 in release 7.00 and higher", "RefUrl": "/notes/1489357"}, {"RefNumber": "1437750", "RefComponent": "EHS-SRC", "RefTitle": "SRC 1.1; SPRC 2.0: Supported Languages", "RefUrl": "/notes/1437750"}, {"RefNumber": "1431088", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1431088"}, {"RefNumber": "142274", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/142274"}, {"RefNumber": "1398852", "RefComponent": "EHS-SRC", "RefTitle": "SAP PRC 2.0: Release strategy", "RefUrl": "/notes/1398852"}, {"RefNumber": "1389856", "RefComponent": "EHS-SRC", "RefTitle": "SAP PRC 2.0: Release information", "RefUrl": "/notes/1389856"}, {"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438"}, {"RefNumber": "1322715", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode FAQs", "RefUrl": "/notes/1322715"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}, {"RefNumber": "1318329", "RefComponent": "BC-DWB-UTL", "RefTitle": "Warning: Incompatible code page", "RefUrl": "/notes/1318329"}, {"RefNumber": "131181", "RefComponent": "BC-UPG-ADDON", "RefTitle": "@5B@ Add. Info: Installation Addon P1T TURKEY 4.0B", "RefUrl": "/notes/131181"}, {"RefNumber": "131180", "RefComponent": "BC-UPG-ADDON", "RefTitle": "@5B@ Add. Info: Upgrade Addon P1T TURKEY to 4.0B", "RefUrl": "/notes/131180"}, {"RefNumber": "1280236", "RefComponent": "BC-I18-BID", "RefTitle": "RTL languages not known to the SAP system", "RefUrl": "/notes/1280236"}, {"RefNumber": "1159020", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "SAP NetWeaver 7.0 EHP1/Business Suite 7: IBM DB2 for z/OS", "RefUrl": "/notes/1159020"}, {"RefNumber": "114910", "RefComponent": "PP-SFC-EXE-CON", "RefTitle": "CO13, CO14: Goods movements for confirmation", "RefUrl": "/notes/114910"}, {"RefNumber": "1146910", "RefComponent": "BC-I18", "RefTitle": "Hong Kong Chinese in Unicode Systems", "RefUrl": "/notes/1146910"}, {"RefNumber": "112612", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/112612"}, {"RefNumber": "109975", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/109975"}, {"RefNumber": "1069443", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Different sort sequence", "RefUrl": "/notes/1069443"}, {"RefNumber": "104385", "RefComponent": "BC-ABA-NL", "RefTitle": "500-599 reserved for batch processing", "RefUrl": "/notes/104385"}, {"RefNumber": "101864", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/101864"}, {"RefNumber": "101220", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on language import 3.1I (+ SR1)", "RefUrl": "/notes/101220"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2068736", "RefComponent": "BC-SEC-AUT-PFC", "RefTitle": "Invalid characters (e.g. #) shown in the user menu", "RefUrl": "/notes/2068736 "}, {"RefNumber": "1700723", "RefComponent": "FI-GL-GL-N", "RefTitle": "Account short text/long text not changeable in FS00/FSP0", "RefUrl": "/notes/1700723 "}, {"RefNumber": "3082111", "RefComponent": "CA-FLP-ABA", "RefTitle": "General issues with displaying of Fiori Tile Text like Titles and Subtitles", "RefUrl": "/notes/3082111 "}, {"RefNumber": "3039073", "RefComponent": "HAN-DB-ENG-TXT", "RefTitle": "HANA Language codes for Text join Calculation Views", "RefUrl": "/notes/3039073 "}, {"RefNumber": "1935497", "RefComponent": "BC-CTS-LAN", "RefTitle": "How to finish a language import by SMLT", "RefUrl": "/notes/1935497 "}, {"RefNumber": "2068675", "RefComponent": "BC-CTS-LAN", "RefTitle": "Extend Language Configuration [VIDEO]", "RefUrl": "/notes/2068675 "}, {"RefNumber": "2772594", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "How to determine the supported languages for an Add-on or Support Package", "RefUrl": "/notes/2772594 "}, {"RefNumber": "2132888", "RefComponent": "BC-SRV-SCR", "RefTitle": "INVALID_TEXT_ENVIRONMENT short dump when editing SAPScript in foreign language", "RefUrl": "/notes/2132888 "}, {"RefNumber": "2672208", "RefComponent": "BC-ABA", "RefTitle": "test", "RefUrl": "/notes/2672208 "}, {"RefNumber": "2582947", "RefComponent": "BC-SRV-FP", "RefTitle": "Report FP_TEST_00 returns error FPRUNX400 - \"Cannot set language environment for language DE\"", "RefUrl": "/notes/2582947 "}, {"RefNumber": "2601899", "RefComponent": "EPM-BPC-NW", "RefTitle": "How to add or delete a preference language for BPC web?", "RefUrl": "/notes/2601899 "}, {"RefNumber": "2479477", "RefComponent": "CA-FLP-ABA", "RefTitle": "Using custom login language breaks Fiori Launchpad startup with 'sLanguage must be a valid BCP47 language tag'", "RefUrl": "/notes/2479477 "}, {"RefNumber": "2471578", "RefComponent": "BC-INS-MIG", "RefTitle": "Error \"Convert MDMP to Unicode during export only\" during Import", "RefUrl": "/notes/2471578 "}, {"RefNumber": "2455962", "RefComponent": "BC-CTS-LAN", "RefTitle": "Using the SAP system on a language that is different from the logon language", "RefUrl": "/notes/2455962 "}, {"RefNumber": "3273181", "RefComponent": "HAN-DB", "RefTitle": "A Language Fails to be set to Session Variable \"LOCALE\" and \"SAP_LOCALE\" on HANA Database", "RefUrl": "/notes/3273181 "}, {"RefNumber": "3020882", "RefComponent": "BC-I18", "RefTitle": "Language code for Sorani/Central Kurdish; RTL properties of some language codes", "RefUrl": "/notes/3020882 "}, {"RefNumber": "2864953", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Analysis Office: One character sap language codes and non uppercase two character language codes always return code page 1100", "RefUrl": "/notes/2864953 "}, {"RefNumber": "2246019", "RefComponent": "LO-SLC", "RefTitle": "SAP Solution Sales Configuration Commerce - Frequently Asked Questions", "RefUrl": "/notes/2246019 "}, {"RefNumber": "2391768", "RefComponent": "PLM-RM-SPE", "RefTitle": "Enterprise Search returns unexpected results due to regional language codes", "RefUrl": "/notes/2391768 "}, {"RefNumber": "2148354", "RefComponent": "BC-I18-UNI", "RefTitle": "R3load conversions from Unicode to non-Unicode not recommended", "RefUrl": "/notes/2148354 "}, {"RefNumber": "1978213", "RefComponent": "MOB-UIA-OTH", "RefTitle": "SAP Fiori UI and non-Unicode back end", "RefUrl": "/notes/1978213 "}, {"RefNumber": "2051310", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Myanmar (Burmese)", "RefUrl": "/notes/2051310 "}, {"RefNumber": "2051308", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Lao", "RefUrl": "/notes/2051308 "}, {"RefNumber": "928729", "RefComponent": "BC-I18-UNI", "RefTitle": "Combined Upgrade & Unicode Conversion (CU&UC)", "RefUrl": "/notes/928729 "}, {"RefNumber": "1753671", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Hindi", "RefUrl": "/notes/1753671 "}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "1375438", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization Collection Note", "RefUrl": "/notes/1375438 "}, {"RefNumber": "1398852", "RefComponent": "EHS-SRC", "RefTitle": "SAP PRC 2.0: Release strategy", "RefUrl": "/notes/1398852 "}, {"RefNumber": "1905902", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "JCo: NullPointerException while connecting to non-UC system", "RefUrl": "/notes/1905902 "}, {"RefNumber": "1904609", "RefComponent": "BC-I18", "RefTitle": "Restrictions and recommendations for Kazakh", "RefUrl": "/notes/1904609 "}, {"RefNumber": "39718", "RefComponent": "BC-I18", "RefTitle": "setlocale on AIX and table TCP0C", "RefUrl": "/notes/39718 "}, {"RefNumber": "432922", "RefComponent": "BC-I18", "RefTitle": "Support of GB-18030    (SAP-8401)", "RefUrl": "/notes/432922 "}, {"RefNumber": "53665", "RefComponent": "BC-I18", "RefTitle": "How to generate xxxxyyyy.CDP files", "RefUrl": "/notes/53665 "}, {"RefNumber": "195490", "RefComponent": "BC-I18", "RefTitle": "Automatic selection of GUI frontend code page", "RefUrl": "/notes/195490 "}, {"RefNumber": "1843698", "RefComponent": "BC-I18", "RefTitle": "Locale check tool nls_check", "RefUrl": "/notes/1843698 "}, {"RefNumber": "1069443", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Different sort sequence", "RefUrl": "/notes/1069443 "}, {"RefNumber": "187864", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: Locale Support on Linux", "RefUrl": "/notes/187864 "}, {"RefNumber": "42305", "RefComponent": "BC-I18", "RefTitle": "RSCPINST (I18N configuration tool)", "RefUrl": "/notes/42305 "}, {"RefNumber": "80727", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting non-LATIN-1 texts", "RefUrl": "/notes/80727 "}, {"RefNumber": "198489", "RefComponent": "BC-I18", "RefTitle": "Latin-4 support (Baltic languages)", "RefUrl": "/notes/198489 "}, {"RefNumber": "386792", "RefComponent": "BC-INS-AS4", "RefTitle": "INST: R/3 Installation on AS/400 (ASCII) 4.6C SR2", "RefUrl": "/notes/386792 "}, {"RefNumber": "548016", "RefComponent": "BC-I18-UNI", "RefTitle": "Conversion to Unicode", "RefUrl": "/notes/548016 "}, {"RefNumber": "765475", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: Troubleshooting", "RefUrl": "/notes/765475 "}, {"RefNumber": "1322715", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode FAQs", "RefUrl": "/notes/1322715 "}, {"RefNumber": "1431088", "RefComponent": "FI-LOC-I18", "RefTitle": "Globalization FAQs", "RefUrl": "/notes/1431088 "}, {"RefNumber": "40815", "RefComponent": "BC-I18", "RefTitle": "Single or Double byte installed? (nls_check)", "RefUrl": "/notes/40815 "}, {"RefNumber": "700887", "RefComponent": "BC-I18", "RefTitle": "Printing: Language configuration", "RefUrl": "/notes/700887 "}, {"RefNumber": "1824605", "RefComponent": "BC-I18", "RefTitle": "Suddenly many ########## in a line on the screen", "RefUrl": "/notes/1824605 "}, {"RefNumber": "838402", "RefComponent": "BC-I18-UNI", "RefTitle": "Problems within non-Unicode systems and  landscapes", "RefUrl": "/notes/838402 "}, {"RefNumber": "85363", "RefComponent": "BC-I18", "RefTitle": "Database Scan Tool for basis release < 6.20", "RefUrl": "/notes/85363 "}, {"RefNumber": "579747", "RefComponent": "BC-I18", "RefTitle": "Support of HongKong Chinese", "RefUrl": "/notes/579747 "}, {"RefNumber": "690105", "RefComponent": "BC-I18", "RefTitle": "Conversion of SAP ambiguous blended code pages", "RefUrl": "/notes/690105 "}, {"RefNumber": "989183", "RefComponent": "CO-OM-OPA-F", "RefTitle": "Detail list of settlement of order: Overlapping columns", "RefUrl": "/notes/989183 "}, {"RefNumber": "94724", "RefComponent": "BC-I18", "RefTitle": "User Definable Characters of SJIS, BIG5, GB2312-80", "RefUrl": "/notes/94724 "}, {"RefNumber": "1389856", "RefComponent": "EHS-SRC", "RefTitle": "SAP PRC 2.0: Release information", "RefUrl": "/notes/1389856 "}, {"RefNumber": "895560", "RefComponent": "BC-I18-UNI", "RefTitle": "Support for languages only available in Unicode systems", "RefUrl": "/notes/895560 "}, {"RefNumber": "614550", "RefComponent": "BC-I18", "RefTitle": "Troubleshooting BC-I18", "RefUrl": "/notes/614550 "}, {"RefNumber": "1489357", "RefComponent": "BC-I18-BID", "RefTitle": "No support for code page 1810 in release 7.00 and higher", "RefUrl": "/notes/1489357 "}, {"RefNumber": "1159020", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "SAP NetWeaver 7.0 EHP1/Business Suite 7: IBM DB2 for z/OS", "RefUrl": "/notes/1159020 "}, {"RefNumber": "685023", "RefComponent": "BC-I18-BID", "RefTitle": "Material numbers must be left-to-right", "RefUrl": "/notes/685023 "}, {"RefNumber": "452762", "RefComponent": "BC-I18", "RefTitle": "Transliteration from Latin-2 to Latin-1", "RefUrl": "/notes/452762 "}, {"RefNumber": "733416", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI and Unicode", "RefUrl": "/notes/733416 "}, {"RefNumber": "1564138", "RefComponent": "RE-FX", "RefTitle": "Message split incorrectly in case of complex code page", "RefUrl": "/notes/1564138 "}, {"RefNumber": "988787", "RefComponent": "EHS-BD-TLS", "RefTitle": "Exporting phrase texts and specifications", "RefUrl": "/notes/988787 "}, {"RefNumber": "1437750", "RefComponent": "EHS-SRC", "RefTitle": "SRC 1.1; SPRC 2.0: Supported Languages", "RefUrl": "/notes/1437750 "}, {"RefNumber": "745030", "RefComponent": "BC-I18-UNI", "RefTitle": "MDMP - Unicode Interfaces: Solution Overview", "RefUrl": "/notes/745030 "}, {"RefNumber": "1045465", "RefComponent": "EHS-BD", "RefTitle": "Fehlersuche von EH&S-spezifischen NLS-Problemen", "RefUrl": "/notes/1045465 "}, {"RefNumber": "764480", "RefComponent": "BC-I18", "RefTitle": "SAP WinGUI I18N Trouble Shooting", "RefUrl": "/notes/764480 "}, {"RefNumber": "975768", "RefComponent": "BC-I18", "RefTitle": "Deprecation of Java features with non-Unicode Backend", "RefUrl": "/notes/975768 "}, {"RefNumber": "959698", "RefComponent": "BC-I18-UNI", "RefTitle": "Twin Upgrade & Unicode Conversion FAQ", "RefUrl": "/notes/959698 "}, {"RefNumber": "889850", "RefComponent": "BC-INS", "RefTitle": "OBSOLETE: SAP NetWeaver 2004s based ABAP Inst/Upgrade for Asia", "RefUrl": "/notes/889850 "}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991 "}, {"RefNumber": "1318329", "RefComponent": "BC-DWB-UTL", "RefTitle": "Warning: Incompatible code page", "RefUrl": "/notes/1318329 "}, {"RefNumber": "1146910", "RefComponent": "BC-I18", "RefTitle": "Hong Kong Chinese in Unicode Systems", "RefUrl": "/notes/1146910 "}, {"RefNumber": "39739", "RefComponent": "BC-I18", "RefTitle": "setlocale on Solaris (SunOS) and table TCP0C", "RefUrl": "/notes/39739 "}, {"RefNumber": "578293", "RefComponent": "BC-I18", "RefTitle": "TCPDB empty: SE92 has errors, which are hard to debug", "RefUrl": "/notes/578293 "}, {"RefNumber": "322982", "RefComponent": "BC-CTS-LAN", "RefTitle": "Install language by means of the upgrade", "RefUrl": "/notes/322982 "}, {"RefNumber": "155333", "RefComponent": "BC-ABA-LA", "RefTitle": "SORT ... AS TEXT leads to short dump or syslog entry", "RefUrl": "/notes/155333 "}, {"RefNumber": "434582", "RefComponent": "BC-INS-AS4", "RefTitle": "UPDNLSTBL allows more languages", "RefUrl": "/notes/434582 "}, {"RefNumber": "845233", "RefComponent": "BC-I18-UNI", "RefTitle": "Use of Input Method Editors (IME) in Unicode System", "RefUrl": "/notes/845233 "}, {"RefNumber": "794411", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "Supported codepages of SAP Java Connector 2.1 and 6.x", "RefUrl": "/notes/794411 "}, {"RefNumber": "881781", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode/non-Unicode RFC language and code page assignment", "RefUrl": "/notes/881781 "}, {"RefNumber": "991481", "RefComponent": "BC-I18-UNI", "RefTitle": "Database is using UTF-8 and not UTF-16 ?", "RefUrl": "/notes/991481 "}, {"RefNumber": "96953", "RefComponent": "XX-CSC-PL", "RefTitle": "Implementing the Polish country version", "RefUrl": "/notes/96953 "}, {"RefNumber": "101220", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on language import 3.1I (+ SR1)", "RefUrl": "/notes/101220 "}, {"RefNumber": "39309", "RefComponent": "BC-I18", "RefTitle": "setlocale on Digital UNIX (OSF/1) and table TCP0C", "RefUrl": "/notes/39309 "}, {"RefNumber": "718324", "RefComponent": "CRM", "RefTitle": "Support restrictions for MDMP implementations of mySAP CRM", "RefUrl": "/notes/718324 "}, {"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364 "}, {"RefNumber": "938737", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using blended code page", "RefUrl": "/notes/938737 "}, {"RefNumber": "938738", "RefComponent": "BC-I18", "RefTitle": "Syslog CP6: still using ambiguous blended code page", "RefUrl": "/notes/938738 "}, {"RefNumber": "925688", "RefComponent": "SV-SMG", "RefTitle": "Double-Byte environment: Error while loading SAPKITL412", "RefUrl": "/notes/925688 "}, {"RefNumber": "747036", "RefComponent": "BC-I18", "RefTitle": "mySAP ERP 2004 Upgrade for R/3 MDMP Customers", "RefUrl": "/notes/747036 "}, {"RefNumber": "168083", "RefComponent": "BC-I18", "RefTitle": "ASSIGN_LENGTH_0 during Hot Package Import", "RefUrl": "/notes/168083 "}, {"RefNumber": "207101", "RefComponent": "BC-I18", "RefTitle": "Customer programs: Enhancements for MDMP", "RefUrl": "/notes/207101 "}, {"RefNumber": "819426", "RefComponent": "SRM-EBP", "RefTitle": "MDMP implementation not supported for mySAP SRM", "RefUrl": "/notes/819426 "}, {"RefNumber": "823110", "RefComponent": "SLL-LEG", "RefTitle": "GTS: UNICODE and Multi Display Multi Processing (MDMP)", "RefUrl": "/notes/823110 "}, {"RefNumber": "83196", "RefComponent": "BC-I18", "RefTitle": "TEXTENV_CODEPAGE_ / _LANGUAGE_NOT_ALLOWED", "RefUrl": "/notes/83196 "}, {"RefNumber": "39763", "RefComponent": "BC-I18", "RefTitle": "setlocale on Windows/NT and table TCP0C", "RefUrl": "/notes/39763 "}, {"RefNumber": "38677", "RefComponent": "BC-I18", "RefTitle": "setlocale on HP-UX and table TCP0C", "RefUrl": "/notes/38677 "}, {"RefNumber": "161662", "RefComponent": "BC-I18", "RefTitle": "MDMP: TANSLATE uses incorrect default code page", "RefUrl": "/notes/161662 "}, {"RefNumber": "161667", "RefComponent": "BC-I18", "RefTitle": "Case mapping in multibyte systems", "RefUrl": "/notes/161667 "}, {"RefNumber": "437732", "RefComponent": "SCM-TEC", "RefTitle": "SAP APO : MDMP and globalization support", "RefUrl": "/notes/437732 "}, {"RefNumber": "563975", "RefComponent": "BW-SYS", "RefTitle": "BW and MDMP/BW and blended code pages not supported", "RefUrl": "/notes/563975 "}, {"RefNumber": "432115", "RefComponent": "BC-OP-AIX", "RefTitle": "AIX 4.3.2 and non-latin-1 ISO8859 languages", "RefUrl": "/notes/432115 "}, {"RefNumber": "395985", "RefComponent": "EHS", "RefTitle": "EH&S Native Language Support", "RefUrl": "/notes/395985 "}, {"RefNumber": "131181", "RefComponent": "BC-UPG-ADDON", "RefTitle": "@5B@ Add. Info: Installation Addon P1T TURKEY 4.0B", "RefUrl": "/notes/131181 "}, {"RefNumber": "174268", "RefComponent": "EHS-SAF", "RefTitle": "EH&S 2.5B Support Packages: Additional documentation", "RefUrl": "/notes/174268 "}, {"RefNumber": "78015", "RefComponent": "BC-CTS-LAN", "RefTitle": "Current note on the language import 3.1H", "RefUrl": "/notes/78015 "}, {"RefNumber": "81892", "RefComponent": "XX-CSC-XX", "RefTitle": "Implementating a new country version", "RefUrl": "/notes/81892 "}, {"RefNumber": "95190", "RefComponent": "FI-AP-AP-B", "RefTitle": "RFFOJP_T - change for blended code page", "RefUrl": "/notes/95190 "}, {"RefNumber": "104385", "RefComponent": "BC-ABA-NL", "RefTitle": "500-599 reserved for batch processing", "RefUrl": "/notes/104385 "}, {"RefNumber": "39745", "RefComponent": "BC-ABA-NL", "RefTitle": "setlocale on ReliantUNIX (SINIX) and table TCP0C", "RefUrl": "/notes/39745 "}, {"RefNumber": "131180", "RefComponent": "BC-UPG-ADDON", "RefTitle": "@5B@ Add. Info: Upgrade Addon P1T TURKEY to 4.0B", "RefUrl": "/notes/131180 "}, {"RefNumber": "44219", "RefComponent": "BC", "RefTitle": "Collective note Localization Development Kit", "RefUrl": "/notes/44219 "}, {"RefNumber": "101864", "RefComponent": "BC", "RefTitle": "Localization Development Kit: collective note", "RefUrl": "/notes/101864 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "754", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "774", "To": "774", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62055", "URL": "/supportpackage/SAPKB62055"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62065", "URL": "/supportpackage/SAPKB62065"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64014", "URL": "/supportpackage/SAPKB64014"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64023", "URL": "/supportpackage/SAPKB64023"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70004", "URL": "/supportpackage/SAPKB70004"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70017", "URL": "/supportpackage/SAPKB70017"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70005", "URL": "/supportpackage/SAPKB70005"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70002", "URL": "/supportpackage/SAPKB70002"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70016", "URL": "/supportpackage/SAPKB70016"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70102", "URL": "/supportpackage/SAPKB70102"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71007", "URL": "/supportpackage/SAPKB71007"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71101", "URL": "/supportpackage/SAPKB71101"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74026", "URL": "/supportpackage/SAPKB74026"}, {"SoftwareComponentVersion": "SAP_BASIS 750", "SupportPackage": "SAPK-75021INSAPBASIS", "URL": "/supportpackage/SAPK-75021INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75113INSAPBASIS", "URL": "/supportpackage/SAPK-75113INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75209INSAPBASIS", "URL": "/supportpackage/SAPK-75209INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75307INSAPBASIS", "URL": "/supportpackage/SAPK-75307INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75405INSAPBASIS", "URL": "/supportpackage/SAPK-75405INSAPBASIS"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}