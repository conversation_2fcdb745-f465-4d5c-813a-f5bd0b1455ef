{"Request": {"Number": "1173260", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 409, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007085212017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001173260?language=E&token=396A1C6367380EA1415B60866A32C15E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001173260", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001173260/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1173260"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "PY-PT-PS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Public Sector Payroll"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Portugal", "value": "PY-PT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Public Sector Payroll", "value": "PY-PT-PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-PT-PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1173260 - Legal Change for CGA Magnetic File report"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The CGA Magnetic File report supports version 1.2 of the layout and filling rules defined by CGA Institution.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>CGA, RPCCGAPT0PBS, CGA Magnetic File, legal change</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The CGA Magnetic File report should also support a new version (1.3) of the CGA file layout and its new rules of filling.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td>4</td>\r\n<td>July 20, 2016</td>\r\n<td>SAR files were removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The CGA Magnetic File report will support both layout versions and<br />filling rules of the situation codes.<br /><br />The TemSe Display/Download Utility for Public Sector (RPUTPSP0) report supports the new CGA file layout (version 1.3). The Advanced Delivery delivers the following customization:<br /><br />View V_T52B4<br /><br />Attrib.| Value&#160;&#160; | Name<br />(PATTR)| (PWERT) |(WTEXT)<br />--------------------------<br />TOA&#160;&#160;&#160;&#160;|CCGAP1&#160;&#160; |CGA Ded. Magnetic File Layout v.1.3<br /><br />View T52B5<br /><br />Class&#160;&#160;| Obj.Name&#160;&#160;&#160;&#160; |Attrib | Value<br />(POTYP)| (PONAM)&#160;&#160;&#160;&#160; |PATTR) |(PWERT)<br />---------------------------------------<br />TMS1 &#160;&#160; |RPUTPSP019&#160;&#160; |TOA&#160;&#160;&#160;&#160;| CCGAP1<br />TMS2 &#160;&#160; |CCGAP1&#160;&#160;&#160;&#160;&#160;&#160; |TOE&#160;&#160;&#160;&#160;| RPUTPSP0<br />TMS2 &#160;&#160; |CCGAP1&#160;&#160;&#160;&#160;&#160;&#160; |TOS&#160;&#160;&#160;&#160;| RPUTPSP0<br /><br /><strong>Important</strong>: the note 1159263 (Splitting TemSe report for public and<br />private sector) has to be applied before installing this note.<br /><br />The new CGA file layout version 1.3 requires the following customizing in the V_T5PFD:<br /><br />- Mandatory: entity property \"NIFRL\" to allow the filling of the field NIF Representante Legal&#160;&#160;(\"N&#250;mero Identifica&#231;&#227;o Fiscal\") in the header record<br />- Optional: entity property \"COMPA\" to allow the filling of the field Entity Name (\"Raz&#227;o Social da Entidade\")<br /><br />Note that the logical entities depend on the feature PENTT.<br /><br />Some fields of the detail record type 2 depend on the employee work time schedule to be filled or not. The work time schedule may be customized on view V_T5PPBS9L (Work Models and Schedules). For the Work Schedule Rule chosen, the option Schedule Type (on Social Balance frame) should have one of the following values:<br />- Fixed Schedule (value 1) - Full time workers<br />- Flextime (value 2) - Variable time workers<br />- Partial Time (value 8) - Partial time workers<br /><br />If the employee's Work Schedule Rule is not customized on the view V_T5PPBS9L, the CGA Magnetic File report assumes that this employee works in a full time regime.<br /><br />If the employee's Work Schedule Rule is not customized on the view V_T5PPBS9L, the CGA Magnetic File report assumes as default schedule for this employee the \"Full time workers\" schedule.<br /><br />The CGA Magnetic File report is using, from now on, the Evaluation class 15 for the customizing of the following situation codes: 01, 08, 10, 20, 30, 32, and 81 to 90. The new Evaluation class and its values can be created by installing the Advanced Delivery file on note 1222993. See note 1222993 for more details about this installation.<br /><br />An Advanced Delivery including changes done in the Data Dictionary and ABAP code is available in the attached files according to the following list (\"xxxxxx\" means numbers):<br /><br />- L7DKxxxxxx_600_SYST.CAR - Release 600 (ERP 2005)<br /><br />An Advanced Delivery including Customizing changes is available in the attached files according to the following list(\"xxxxxx\" means numbers):<br /><br />- L7DKxxxxxx_600_CUST.CAR - Release 600 (ERP 2005)<br /><br />For more details about Advance Delivery installation procedure please read the notes listed in \"Related Notes\".<br /><br /><strong>Important</strong>: Be aware of an Advance Delivery delivers the last version of the object. It means that if you do not have the last HR Support Package installed in your system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the code changes manually according to the Correction Instructions available in this note.<br /><br />The correction described in this note will be included in an HR Support Package. The support package includes the following:<br />The following objects were changed:<br /><br />Report Source Code<br /><br />RPCCGAPT0PBS, RPCCGAPTDPBS, RPCCGAPTFPBS, RPCCGAPTSPBS, RPUTPSP0, RPUTPSPD, RPUTPSPF<br /><br />The following objects were created:<br /><br />Documentation<br /><br />NAHRPBSPT_PBPS118, NAHRPBSPT_PBPS119, NAHRPBSPT_PBPS120, NAHRPBSPT_PBPS121<br /> <br />Single Message<br /><br />HRPBSPT_PBPS118, HRPBSPT_PBPS119, HRPBSPT_PBPS120 ,HRPBSPT_PBPS121<br /> <br />Report Texts<br /><br />RPCCGAPT0PBS<br /> <br />Data Element<br /><br />PPTP_MFENTNA, PPTP_MFFI002 ,PPTP_MFFI030, PPTP_MFFI134, PPTP_MFFUTIM, PPTP_MFLAYO1, PPTP_MFLAYO2, PPTP_MFNIF, PPTP_MFNUDAY<br /><br />Table<br /><br />PPTP_MAGDM_01, PPTP_MAGFT_01, PPTP_MAGHD_01</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I812659)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I827735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173260/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173260/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173260/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173260/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173260/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173260/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173260/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173260/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001173260/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1285737", "RefComponent": "PY-PT-PS", "RefTitle": "RPCCGAPT0PBS: Performance issues in CGA Magnetic File report", "RefUrl": "/notes/1285737"}, {"RefNumber": "1222993", "RefComponent": "PY-PT-PS", "RefTitle": "Evaluation classes 18, 19 and 20 replaced", "RefUrl": "/notes/1222993"}, {"RefNumber": "1159263", "RefComponent": "PY-PT", "RefTitle": "Splitting TemSe report for public and private sector", "RefUrl": "/notes/1159263"}, {"RefNumber": "1077282", "RefComponent": "PY-PT-PS", "RefTitle": "HCM PT PS: Report Magnetic File with CGA deductions made", "RefUrl": "/notes/1077282"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1285737", "RefComponent": "PY-PT-PS", "RefTitle": "RPCCGAPT0PBS: Performance issues in CGA Magnetic File report", "RefUrl": "/notes/1285737 "}, {"RefNumber": "1222993", "RefComponent": "PY-PT-PS", "RefTitle": "Evaluation classes 18, 19 and 20 replaced", "RefUrl": "/notes/1222993 "}, {"RefNumber": "1159263", "RefComponent": "PY-PT", "RefTitle": "Splitting TemSe report for public and private sector", "RefUrl": "/notes/1159263 "}, {"RefNumber": "1077282", "RefComponent": "PY-PT-PS", "RefTitle": "HCM PT PS: Report Magnetic File with CGA deductions made", "RefUrl": "/notes/1077282 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCPT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HRCPT 600", "SupportPackage": "SAPK-60037INSAPHRCPT", "URL": "/supportpackage/SAPK-60037INSAPHRCPT"}, {"SoftwareComponentVersion": "SAP_HRCPT 604", "SupportPackage": "SAPK-60403INSAPHRCPT", "URL": "/supportpackage/SAPK-60403INSAPHRCPT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HRCPT", "NumberOfCorrin": 2, "URL": "/corrins/0001173260/6497"}, {"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 1, "URL": "/corrins/0001173260/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 8, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1144888 ", "URL": "/notes/1144888 ", "Title": "RPCCGAPTFPBS: correction of the number of absences", "Component": "PY-PT-PS"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1144912 ", "URL": "/notes/1144912 ", "Title": "New customizing for Sit. Code 01,30,32 of CGA Magnetic File", "Component": "PY-PT-PS"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1148968 ", "URL": "/notes/1148968 ", "Title": "Adjust in situation codes from CGA Magnetic File", "Component": "PY-PT-PS"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1150898 ", "URL": "/notes/1150898 ", "Title": "RPCCGAPT0PBS: Employees associated to a non CGA regime.", "Component": "PY-PT-PS"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1152967 ", "URL": "/notes/1152967 ", "Title": "Seniority ded. and retrocalculation process in CGA Mag. File", "Component": "PY-PT-PS"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1155867 ", "URL": "/notes/1155867 ", "Title": "RPCCGAPT0PBS:filling of field remun. for sit. cod. 81 to 90", "Component": "PY-PT-PS"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1159263 ", "URL": "/notes/1159263 ", "Title": "Splitting TemSe report for public and private sector", "Component": "PY-PT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "600", "ValidTo": "600", "Number": "1161144 ", "URL": "/notes/1161144 ", "Title": "RPCCGAPT0PBS: Employees with different CGA number in payroll", "Component": "PY-PT-PS"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}