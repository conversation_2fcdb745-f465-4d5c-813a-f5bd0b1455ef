{"Request": {"Number": "851445", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 266, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015906912017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000851445?language=E&token=7183CF6E6C3500F5323827BFA5D15807"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000851445", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000851445/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "851445"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.03.2010"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "851445 - BP_CVI: Details about reports to be executed for ERP 2005"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In Release SAP_APPL 6.0, the customer vendor integration, which had already been delivered in previous releases, was redesigned. The changed functionality and enhanced function mean it is necessary to execute various migration reports.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>FI customer, FI vendor, business partner, DKI, CVI, tax data, roles, views, search help, BD001, BC001, CVI_CUST_LINK, CVI_VEND_LINK, DFKKBPTAXNUM, KNA1, KNAS, LFA1, LFAS, CVI_ADJUST_ROLE_CUSTOMIZING, CVI_MIGRATE_CUST_LINKS, CVI_MIGRATE_VEND_LINKS, CVI_ANALYZE_CUST_TAX_NUMBERS, CVI_ANALYZE_VEND_TAX_NUMBERS, CVI_SYNC_CUST_TAX_NUMBERS, CVI_SYNC_VEND_TAX_NUMBERS</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>When you changed from releases with active customer or vendor integration (for example, Bank/CFM 2.0, EA-FINSERV 1.10/2.0, ERP2004, IS-M 4.63/4.64) to ERP2005 (SAP_APPL ECC6.00), the function for the integration of customers and vendors with the SAP Business Partner was changed and the functionality enhanced.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Important:<br />During the migration, there are always problems due to existing inconsistencies in the dataset, and migration reports trigger a short dump as a result.<br />Before you start the migration, you must check the dataset for inconsistencies. Note 1105558 delivers four reports with which inconsistencies can be discovered.<br />Execute the migration using the migration reports specified below only after eliminating any inconsistencies that may have been found.<br /><br />The following reports were created for Release SAP_APPL 6.0 and must be executed after an upgrade. You can find more detailed information about the individual reports in the relevant documentation. Adhere to the relevant sequence to ensure that the data is migrated correctly without any errors.</p> <OL>1. CVI_ANALYZE_CUST_TAX_NUMBERS</OL> <p>The report checks the customer and business partner tax data in the system. This report must be executed before the report that synchronizes the business partner and customer tax data (CVI_SYNC_CUST_TAX_NUMBERS) and before the report that migrates the business partner and customer link table (CVI_MIGRATE_CUST_LINKS).</p> <OL>2. CVI_ANALYZE_VEND_TAX_NUMBERS</OL> <p>The report checks the vendor and business partner tax data in the system. This report must be executed before the report that synchronizes the business partner and vendor tax data (CVI_SYNC_VEND_TAX_NUMBERS) and before the report that migrates the business partner and vendor link table (CVI_MIGRATE_VEND_LINKS).</p> <OL>3. CVI_SYNC_CUST_TAX_NUMBERS</OL> <p>The report synchronizes the customer and business partner tax data in the system. This report must be executed before the report that migrates the business partner and customer link table (CVI_MIGRATE_CUST_LINKS).</p> <OL>4. CVI_SYNC_VEND_TAX_NUMBERS</OL> <p>The report synchronizes the vendor and business partner tax data in the system. This report must be executed before the report that migrates the business partner and vendor link table (CVI_MIGRATE_VEND_LINKS).</p> <OL>5. CVI_MIGRATE_CUST_LINKS</OL> <p>The report migrates the customer and business partner links in the system from the table BD001 to the table CVI_CUST_LINK. Since the system continues to read from the table BD001, this report does not necessarily have to be executed.&#x00A0;&#x00A0;Migrating and then deleting the contents of the table BD001 can lead to improved performance. Old entries can only be deleted after the reports that analyze and synchronize the business partner and customer tax data (CVI_ANALYZE_CUST_TAX_NUMBERS and CVI_SYNC_CUST_TAX_NUMBERS) have been executed.<br /><br />You use the 'Partners by different attributes' search help to search for a customer that is linked to a business partner. If you make an entry in the customer field only, the search help refers to the table CVI_CUST_LINK only. The data from the table BD001 is not read.<br />You have to execute this report to determine the required partner.</p> <OL>6. CVI_MIGRATE_VEND_LINKS</OL> <p>The report migrates the vendor and business partner links in the system from the table BC001 to the table CVI_VEND_LINK. Since the system continues to read from the table BC001, this report does not necessarily have to be executed.&#x00A0;&#x00A0;Migrating and then deleting the contents of the table BC001 can lead to improved performance. Old entries can only be deleted after the reports that analyze and synchronize the business partner and vendor tax data (CVI_ANALYZE_VEND_TAX_NUMBERS and CVI_SYNC_VEND_TAX_NUMBERS) have been executed.<br /><br />You use the 'Partners by different attributes' search help to search for a vendor that is linked to a business partner. If you make an entry in the vendor field only, the search help refers to the table CVI_VEND_LINK only. The data from the table BC001 is not read.<br />You have to execute this report to determine the required partner.</p> <OL>7. CVI_ADJUST_ROLE_CUSTOMIZING</OL> <p>This report migrates the role Customizing of customer-specific business partner roles for the current customer-vendor integration. The report can be executed independently of the other reports.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D027661"}, {"Key": "Processor                                                                                           ", "Value": "D027661"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000851445/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000851445/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851445/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851445/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851445/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851445/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851445/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851445/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000851445/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "956054", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Customer/vendor integration as of ERP 6.00", "RefUrl": "/notes/956054"}, {"RefNumber": "946391", "RefComponent": "RE-FX-BP", "RefTitle": "Search help \"Customer by real estate contract\" without hits", "RefUrl": "/notes/946391"}, {"RefNumber": "937416", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Transfer of validity for bank details", "RefUrl": "/notes/937416"}, {"RefNumber": "923833", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Performance problem with tax data conversion", "RefUrl": "/notes/923833"}, {"RefNumber": "907860", "RefComponent": "IS-M-MD-BP", "RefTitle": "ERP 2005: Business partner administration details", "RefUrl": "/notes/907860"}, {"RefNumber": "852991", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Extension options of CVI in ERP 2005", "RefUrl": "/notes/852991"}, {"RefNumber": "851444", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Data assignment of CVI in ERP 2005", "RefUrl": "/notes/851444"}, {"RefNumber": "1105558", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Migration report from Note 851445 terminates", "RefUrl": "/notes/1105558"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3099100", "RefComponent": "AP-MD-BF-SYN", "RefTitle": "How to  delete the entries from the tables BD001 and BC001", "RefUrl": "/notes/3099100 "}, {"RefNumber": "2601760", "RefComponent": "AP-MD-BP-SYN", "RefTitle": "What's the difference for the entries in BD001/BC001 and CVI_CUST_LINK/CVI_VEND_LINK?", "RefUrl": "/notes/2601760 "}, {"RefNumber": "937416", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Transfer of validity for bank details", "RefUrl": "/notes/937416 "}, {"RefNumber": "956054", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Customer/vendor integration as of ERP 6.00", "RefUrl": "/notes/956054 "}, {"RefNumber": "946391", "RefComponent": "RE-FX-BP", "RefTitle": "Search help \"Customer by real estate contract\" without hits", "RefUrl": "/notes/946391 "}, {"RefNumber": "907860", "RefComponent": "IS-M-MD-BP", "RefTitle": "ERP 2005: Business partner administration details", "RefUrl": "/notes/907860 "}, {"RefNumber": "1105558", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Migration report from Note 851445 terminates", "RefUrl": "/notes/1105558 "}, {"RefNumber": "851444", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Data assignment of CVI in ERP 2005", "RefUrl": "/notes/851444 "}, {"RefNumber": "923833", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Performance problem with tax data conversion", "RefUrl": "/notes/923833 "}, {"RefNumber": "852991", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Extension options of CVI in ERP 2005", "RefUrl": "/notes/852991 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}