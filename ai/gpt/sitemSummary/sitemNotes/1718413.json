{"Request": {"Number": "1718413", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1129, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017439362017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001718413?language=E&token=D312A3EC86237A4FA2C66EABAE80B561"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001718413", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001718413/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1718413"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2016.06.13"}, "SAPComponentKey": {"_label": "Component", "value": "BC-INS-NT"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Netweaver based Solutions on Windows"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation Tools (SAP Note 1669327)", "value": "BC-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Netweaver based Solutions on Windows", "value": "BC-INS-NT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS-NT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1718413 - Inst. SAP Systems Based on NW 7.0 incl. EHPs: Windows"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You install the following SAP systems based on SAP NetWeaver 7.0 including EHPs on Windows with SQL Server with Software Provisioning Manager 1.0 (SWPM), which was formerly know as SAPinst:</p>\r\n<ul>\r\n<li>SAP NetWeaver 7.0 incl. EHP3</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.0 incl. EHP2</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.0 incl. EHP1 SR1</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver 7.0 SR3</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Business Suite 7i 2011 (SAP ERP 6.0 EHP6, SAP CRM 7.0 EHP2, SAP SCM 7.0 EHP2, SAP SRM 7.0 EHP2) based on SAP NetWeaver 7.0 incl. EHP3</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Business Suite 7i 2010 (SAP ERP 6.0 EHP5, SAP CRM 7.0 EHP1, SAP SCM 7.0 EHP1, SAP SRM 7.0 EHP1) based on SAP NetWeaver 7.0 incl. EHP2</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Business Suite 7 SR1 (SAP ERP 6.0 EHP4, SAP CRM 7.0, SAP SCM 7.0, SAP SRM 7.0) based on SAP NetWeaver 7.0 incl. EHP1 SR1</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Business Suite 2005 SR3 (SAP ERP 6.0 SR3, SAP CRM 5.0 SR3, SAP SRM 5.0 SR3, SAP SCM 5.0 SR3) based on SAP NetWeaver 7.0 SR3</li>\r\n</ul>\r\n<p><br />This SAP note replaces the former SAP notes 1565346, 1387319, 1326698, and 1108852.<br /><br />For SAP Business Suite 7i 2011 / SAP Business Suite 7i 2010 / SAP Business Suite 7 SR1 on SAP NetWeaver 7.3 Java Hubs on Windows, see SAP Note 1710950.<br /><br />Make sure that you always use the latest documentation version of the installation guides for your product at:</p>\r\n<p><a target=\"_blank\" href=\"http://service.sap.com/sltoolset\">http://service.sap.com/sltoolset</a></p>\r\n<p><a target=\"_blank\" href=\"http://service.sap.com/installnw70\">http://service.sap.com/installnw70</a><br />http://service.sap.com/erp-inst<br />http://service.sap.com/crm-inst<br />http://service.sap.com/srm-inst<br />http://service.sap.com/instguides -&gt; SAP Business Suite Applications -&gt; SAP SCM -&gt;&#160;&#160;SAP SCM Server</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>software provisioning manager 1.0, SAPinst, SWPM</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Installation problems for an SAP system based on SAP NetWeaver 7.0 including EHPs on Windows discovered after the publication of the installation guide.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Contents<br /><br />1&#160;&#160;&#160;&#160; General<br />2&#160;&#160;&#160;&#160; Pre-Installation<br />3&#160;&#160;&#160;&#160; Installation<br />4&#160;&#160;&#160;&#160; Post-Installation<br />5&#160;&#160;&#160;&#160; High Availability (HA)<br /><br /><br />Each entry is marked with a prefix, to make clear for which SAP systems, software units, and technical stacks (ABAP, Java, ABAP+Java), the information applies to. If there is no prefix, the information applies to all SAP systems.<br /><br />Date&#160;&#160;&#160;&#160;&#160;&#160;Section&#160;&#160; Description<br />--------------------------------------------------------------------------------------------------------------------------------------</p>\r\n<p>13 JUN/16&#160;&#160;&#160; 5&#160;&#160;&#160;&#160;&#160; HA (Java, ABAP+Java): Temporary license expires 30 minutes after failover</p>\r\n<p>27/JUL/15&#160;&#160;&#160; 1&#160;&#160;&#160;&#160;&#160; Do not use the &lt;sapsid&gt;adm user for the installation of the SAP system</p>\r\n<p>10/DEC/14&#160;&#160; 5&#160;&#160;&#160;&#160;&#160;&#160; HA: Validation of shared drive fails with localized Windows version</p>\r\n<p>19/MAY/11&#160;&#160;&#160;&#160;4&#160;&#160;&#160;&#160;&#160;&#160;Caution with&#160;Uninstall option with regard to SAP profiles<br />22/SEP/10&#160;&#160;&#160;&#160;3&#160;&#160;&#160;&#160;&#160;&#160;Java: Java engine does not start<br />13/JUL/10&#160;&#160;&#160;&#160;3&#160;&#160;&#160;&#160;&#160;&#160;Do not use Japanese characters in SAP system profiles<br />29/APR/10&#160;&#160;&#160;&#160;3&#160;&#160;&#160;&#160;&#160;&#160;Japanese Windows version: Error in step: changeUserInstall<br />12/MAR/10&#160;&#160;&#160;&#160;4&#160;&#160;&#160;&#160;&#160;&#160;Java (EPC): Error messages in callsdmviasapinst*.log files after installation finished successfully<br />19/MAR/09&#160;&#160;&#160;&#160;4&#160;&#160;&#160;&#160;&#160;&#160;CRM: Errors in sapinst_dev.log<br />25/MAR/08&#160;&#160;&#160;&#160;3&#160;&#160;&#160;&#160;&#160;&#160;SCM, ABAP: liveCache Client in SCM Installations<br />--------------------------------------------------------------------------------------------------------------------------------------</p>\r\n<p><strong>1&#160;&#160;&#160;&#160; General</strong></p>\r\n<p>----------------------------&lt;27/JUL/15, D036707&gt;-----------------------------------------------------------</p>\r\n<p>Do not use the &lt;sapsid&gt;adm user for the installation of the SAP system.</p>\r\n<p>In the \"New Features\" section of the Introduction in the installation guide, it is erroneously written that it is allowed to use the &lt;sapsid&gt;adm user for the installation of your SAP system. This is not correct. Make sure that you do not use the &lt;sapsid&gt;adm user to install the SAP system.</p>\r\n<p><br /><strong>2&#160;&#160;&#160;&#160; Pre-Installation</strong></p>\r\n<p><br /><strong>3&#160;&#160;&#160;&#160; Installation</strong></p>\r\n<p><br />----------------------&lt;22/SEP/10, C5093457&gt;-------------------------<br />Java: Java engine does not start<br />During the installation with SWPM, you might get an error message like \"connection refused\" or \"connection broken\" after phase \"Start Java engine\". Due to insufficient virtual memory, the Java engine might be down. Use the SAP MMC to check its status. If it is down, increase the virtual memory and restart the instance with the SAP MMC. Continue the installation with SWPM.<br /><br />--------------------&lt;13/JUL/10, D029385&gt;-------------------------<br />Do not use Japanese characters in SAP system profiles<br />You made entries (for example, comments) in SAP system profiles in:<br />\\usr\\sap\\&lt;SAPSID&gt;\\profile.<br />You get an error message that a profile parameter is not set.<br />To solve the problem, proceed as follows:</p>\r\n<ol>\r\n<li>Remove all Japanese characters from the SAP system profiles.</li>\r\n<li>Restart the installation.</li>\r\n<li>On the \"What do you want to do?\" screen, choose \"Run a new option\".</li>\r\n</ol>\r\n<p>-------------------&lt;29/04/10, D033923&gt;---------------------------------<br />Japanese Windows version: Error in step: changeUserInstall<br />With the Japanese Windows version, you might get the following error in step: changeUserInstall<br />To solve this problem, skip this step as follows:</p>\r\n<ol>\r\n<li>Stop the installation.</li>\r\n<li>In the installation folder, open the \"keydb.xml \" file.</li>\r\n<li>Search for the table \"tDialogStatus\".</li>\r\n<li>ook for the last row where it says: \"changeUserInstall step\".</li>\r\n<li>Edit the field STATUS and write OK like this:<br />&lt;strval&gt;&lt;![CDATA[OK]]&gt;<br />&lt;/strval&gt;</li>\r\n<li>&#160;Save the changed \"keydb.xml\" file.</li>\r\n<li>Restart the installation, choose the same installation option as before, and choose \"Continue the installation\".</li>\r\n</ol>\r\n<p><br />--------------------&lt;25/MAR/08, D036661&gt;-----------------------<br />SCM, ABAP: liveCache Client in SCM Installations<br />When you install an SCM system, the liveCache client software is automatically installed, even if you choose not to install liveCache server during the installation.<br />Currently there is no solution availabe. However, this has no effect on the functioning of your SCM system.</p>\r\n<p><strong>4&#160;&#160;&#160;&#160; Post-Installation</strong></p>\r\n<p>------------------------&lt;19/MAY/11, D050445&gt;----------------------<br />Before you use the \"Uninstall\" option in Software Provisioning Manager, make sure that the profile directory of the relevant SAP system to be deleted, only contains profiles that belong to this SAP system. Otherwise, if there are valid profiles of other SAP systems available in this profile directory, there might be the risk of data loss for these other systems.<br /><br />--------------------&lt;12/MAR/10, I032375&gt;-----------------------<br />EPC: Error messages in callsdmviasapinst*.log files after installation finished successfully<br />The installation of an SAP Java system with usage type EPC (without usage type EP) has finished successfully. However, you find error messages in the callsdmviasapinst*.log files as described in SAP Note 1445384.<br />You can ignore these error messages.<br /><br />------------------------&lt;19/MAR/09, I027641&gt;---------------------<br />CRM: Errors in sapinst_dev.log<br />The installation of CRM 7.0 EHP3 Java components finished successfully. However, some error messages may occur in sapinst_dev.log, for example:<br />***********************************************************************<br />java.rmi.RemoteException: Error occurred while starting application sap.com/ess/tra/trp/trt and wait. ...<br />***********************************************************************<br />Cause:<br />The WebDynpro applications have a lazy starting mechanism. This means that they are started upon request. It is normal that some of them need to be stopped after server start. They will be started when a request is sent to them. The reason of the error messages listed in the log file is, that the applications were requested before the deployment of all referenced applications had finished. When a Web Dynpro application tries to start, it tries first to start all referenced components. The traces show that some of the applications are not available, because they were not deployed at that moment.<br /><br />Solution:<br />You can ignore these error messages if you are able to start the applications listed in the error message manually.<br /><br /><br /><strong>5&#160;&#160;&#160;&#160; High Availability (HA)</strong></p>\r\n<p>------------------------&lt;13/JUN/16, D035306&gt;-------------------------</p>\r\n<p>HA (Java, ABAP+Java): Temporary license expires 30 minutes after failover</p>\r\n<p>If you do a failover of the SAP (A)SCS instance from one cluster node to another node, and you do not have a permanent license on this node, the generated temporary license is only valid for 30 minutes. Java application servers will shutdown themselves after 30 minutes of operation.</p>\r\n<p>To prevent this, apply a permanent license key as soon as possible.</p>\r\n<p>&#160;</p>\r\n<p>------------------------&lt;10/DEC/14, I072929&gt;-------------------------<br />Validation of shared drive fails with localized Windows version</p>\r\n<p>If you use a localized Windows version (e.g. German version), the name for the \"Available Storage\" group is different than the original English name.<br />During the <em>First Cluster Node</em> installation with SWPM, on the \"SAP System Cluster Parameters\" dialog window, the validation of the shared drive fails and returns the following error:</p>\r\n<p>&#160;'A disk not belonging to \"Available Storage\" group is an invalid destination drive.'</p>\r\n<p>To solve the problem, do the following:</p>\r\n<ol>\r\n<li>Create a cluster group with the English name \"Available Storage\".</li>\r\n<li>Move the drive to the new group.</li>\r\n<li>Continue the installation.<br />The disk validation succeeds, and it is not necessary to restart the installation from scratch.</li>\r\n</ol>\r\n<p>The newly created \"Available Storage\" group is empty after the <em>First Cluster Node</em> installation has finished.&#160; You can delete it unless you plan to install another SAP system on the same cluster node.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D036707)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D035306)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001718413/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001718413/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001718413/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001718413/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001718413/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001718413/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001718413/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001718413/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001718413/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1732161", "RefComponent": "BC-OP-NT", "RefTitle": "SAP Systems on Windows Server 2012 (R2)", "RefUrl": "/notes/1732161"}, {"RefNumber": "1723084", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1723084"}, {"RefNumber": "1718415", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1718415"}, {"RefNumber": "1718414", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP Systems Based on NW 7.0 incl. EHPs: SQL Server", "RefUrl": "/notes/1718414"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3346502", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 SP38 - covers no longer supported Operating Systems", "RefUrl": "/notes/3346502 "}, {"RefNumber": "3220901", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 SP35 - covers no longer supported CPU and operation system versions", "RefUrl": "/notes/3220901 "}, {"RefNumber": "2505142", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for *RMOS*.SAR of Software Provisioning Manager 1.0 - covers no longer supported operation system versions", "RefUrl": "/notes/2505142 "}, {"RefNumber": "1680045", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 (recommended: SWPM 1.0 SP40)", "RefUrl": "/notes/1680045 "}, {"RefNumber": "962955", "RefComponent": "BC-CST-NI", "RefTitle": "Use of virtual or logical TCP/IP host names", "RefUrl": "/notes/962955 "}, {"RefNumber": "1732161", "RefComponent": "BC-OP-NT", "RefTitle": "SAP Systems on Windows Server 2012 (R2)", "RefUrl": "/notes/1732161 "}, {"RefNumber": "1718414", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP Systems Based on NW 7.0 incl. EHPs: SQL Server", "RefUrl": "/notes/1718414 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SOFTWARE_PROVISIONING_MANAGER", "From": "1.0", "To": "1.0", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}