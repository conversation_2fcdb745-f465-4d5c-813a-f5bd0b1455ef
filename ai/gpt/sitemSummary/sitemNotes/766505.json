{"Request": {"Number": "766505", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 403, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004192422017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000766505?language=E&token=56FC18DD35BA8A2DEBBA8E1C1748F9DF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000766505", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000766505/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "766505"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "HotNews"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.04.2014"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-NET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Network connection"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Network connection", "value": "XX-SER-NET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "766505 - OSS1: Changes to RFC connection SAPOSS"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The previously valid definition of the RFC connection to the SAPNet R/3 front-end system will be changed in future, to provide better availability of system access. To enable this, the code of transaction OSS1 will be changed, because the destination is redefined when the data is saved with Parameters -&gt; Techn. Settings -&gt; Change.<br/>If you have a release level later than 6.40 (SAP_BASIS) or if the changes are already in your system as a result of your Support Package level, you may still have to regenerate the destination SAPOSS with transaction OSS1, as described at the end of this SAP Note. The changes resulting from importing this SAP Note are not needed in this case, however.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>OSS1, SAPOSS, RFC to SAPNet R/3 front end, OSS_RFC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The previous logon to the main instance is being replaced by a group logon to the EWA group, which will provide for better logon options for all RFC calls.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Note (now valid as of 4.0):<br/>-----------------------------------------------------------------------<br/>SAP R/3 Release &lt; 4.0*<br/>If you use an SAP R/3 Release &lt; 4.0*, you can ignore the changes in this SAP Note. When you log on to the SAPNet R/3 front-end system from a release earlier than 4.0, direct logon to the main server is still allowed; this type of logon is rejected for all other releases.<br/>-----------------------------------------------------------------------<br/><br/>If you implement the corrections with transaction SNOTE, however, in addition to the specified source code corrections, you have to call transaction SE37 and enhance the interface for the function module EPS_UPDATE_RFC_DESTINATION with the following IMPORT parameters:<br/><br/></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Parameter</td>\r\n<td>Type</td>\r\n<td>Associated Type</td>\r\n<td>Default value</td>\r\n<td>Optional</td>\r\n<td>Pass Value</td>\r\n</tr>\r\n<tr>\r\n<td>IV_LOADB</td>\r\n<td>LIKE</td>\r\n<td>RFCOPT-RFCLBFLAG</td>\r\n<td>&#39;X&#39;</td>\r\n<td>X</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>IV_GROUP</td>\r\n<td>LIKE</td>\r\n<td>RFCDISPLAZ-RFCLOAD</td>\r\n<td>&#39;EWA&#39;</td>\r\n<td>X</td>\r\n<td>X</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>(In earlier releases, the last column is called &quot;Reference&quot; and you must leave it blank.)<br/><br/>Enter the short texts &quot;RFC via Load Balancing&quot; for IV_LOADB and &quot;Logon Group for Load Balancing Mode&quot; for IV_GROUP.<br/>In all cases - that is, for manual changes, for the import with SNOTE and additional interface change, and with change via Support Package - when you change the data in transaction OSS1 (Parameters -&gt; Techn. Settings -&gt; Change if necessary and then -&gt; Save) and then activate, the destination SAPOSS is saved in a new form, namely with load balancing in the EWA group and with logon to client 001.<br/><br/>Consider the following:<br/>When you transport the source code changes implemented through the SAP Note or the patches, the changed RFC destination is not transported. It is not enough to merely import the Support Packages, either. Once the source code is present in the system, you have to execute the following action in each involved system:<br/>Transaction OSS1 -&gt; Parameters -&gt; Techn. Settings -&gt; Change mode -&gt; Save. The destination SAPOSS is updated automatically when you save.<br/>To check whether your changes were successful, you can call transaction SM59 to display the RFC destination SAPOSS, which should then be defined with load balancing and the logon group EWA.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D033304)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D054149)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000766505/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000766505/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766505/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766505/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766505/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766505/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766505/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766505/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766505/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "883401", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "RFC destination SAPOSS without description", "RefUrl": "/notes/883401"}, {"RefNumber": "883111", "RefComponent": "SV-SMG-SDD", "RefTitle": "Deactivating old EarlyWatchAlert (Transaction SCUI)", "RefUrl": "/notes/883111"}, {"RefNumber": "812386", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "RFC connections to SAPNet R/3 front end", "RefUrl": "/notes/812386"}, {"RefNumber": "802659", "RefComponent": "SV-SMG-SUP-INT", "RefTitle": "Update job RNOTIFUPDATE01 no longer works", "RefUrl": "/notes/802659"}, {"RefNumber": "797001", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/797001"}, {"RefNumber": "770020", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/770020"}, {"RefNumber": "455709", "RefComponent": "BC-MID-RFC", "RefTitle": "Message server in SM59 is too short (shorter than 100)", "RefUrl": "/notes/455709"}, {"RefNumber": "33135", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Guide for OSS1", "RefUrl": "/notes/33135"}, {"RefNumber": "2000132", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2000132"}, {"RefNumber": "182308", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/182308"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "33135", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Guide for OSS1", "RefUrl": "/notes/33135 "}, {"RefNumber": "883111", "RefComponent": "SV-SMG-SDD", "RefTitle": "Deactivating old EarlyWatchAlert (Transaction SCUI)", "RefUrl": "/notes/883111 "}, {"RefNumber": "812386", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "RFC connections to SAPNet R/3 front end", "RefUrl": "/notes/812386 "}, {"RefNumber": "455709", "RefComponent": "BC-MID-RFC", "RefTitle": "Message server in SM59 is too short (shorter than 100)", "RefUrl": "/notes/455709 "}, {"RefNumber": "883401", "RefComponent": "XX-SER-NET-RCSC", "RefTitle": "RFC destination SAPOSS without description", "RefUrl": "/notes/883401 "}, {"RefNumber": "802659", "RefComponent": "SV-SMG-SUP-INT", "RefTitle": "Update job RNOTIFUPDATE01 no longer works", "RefUrl": "/notes/802659 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46A", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B62", "URL": "/supportpackage/SAPKH45B62"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B57", "URL": "/supportpackage/SAPKB46B57"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C48", "URL": "/supportpackage/SAPKB46C48"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D38", "URL": "/supportpackage/SAPKB46D38"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61041", "URL": "/supportpackage/SAPKB61041"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62044", "URL": "/supportpackage/SAPKB62044"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64009", "URL": "/supportpackage/SAPKB64009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 2, "URL": "/corrins/0000766505/41"}, {"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000766505/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}