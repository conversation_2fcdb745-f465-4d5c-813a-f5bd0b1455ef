{"Request": {"Number": "3016862", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 519, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000649942021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003016862?language=E&token=6C0D6EA9D4086F8DE4FBE83DA82219E0"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003016862", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003016862/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3016862"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 78}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "CA-LT-NAT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Note Analyzer"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Landscape Transformation", "value": "CA-LT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Note Analyzer", "value": "CA-LT-NAT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT-NAT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3016862 - DMIS Note Analyzers with separated scenarios for ABAP-based Migration and Replication Technology  (DMIS2011/DMIS2018/DMIS2020/SAP S/4HANA)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You intend to check whether all correction notes for the ABAP-based Migration and Replication Technology (addon DMIS and respective components in S/4HANA) are implemented.<br /><br />-&#160;&#160;&#160;&#160;&#160;&#160; This SAP Note 3016862 has replaced note <a target=\"_blank\" href=\"/notes/2596411\">2596411</a> and the assigned report DMC_NOTE_ANALYZER.<br />-&#160;&#160;&#160;&#160;&#160;&#160; Note 3016862 and its assigned component CA-LT-NAT only covers the functionality provided for the scenarios listed in the table below.<br />-&#160;&#160;&#160;&#160;&#160;&#160; Regarding to issues or remarks in respect of the new &#8220;SNOTE: Note Analyzer&#8221; provided by note 3200109 please refer to component BC-UPG-NA.<br /><br />Incident creation:</p>\r\n<p>-&#160; &#160; &#160; &#160;regarding to issues during implementation of this note&#160;3016862 or during the execution of the programs listed below use component CA-LT-NAT<br />-&#160; &#160; &#160; &#160;regarding to issues&#160;during implementation of the list of recommended notes use the component the note belongs to<br />-&#160; &#160; &#160; &#160;regarding to issues to connect to SAP Support backbone use component&#160;BC-UPG-NA</p>\r\n<p>The following table shows all scenarios and the related program and transaction codes:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Scenario</strong></td>\r\n<td><strong>Program Name</strong></td>\r\n<td><strong>Transaction Code</strong></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Object Based Transformation (OBT)</p>\r\n</td>\r\n<td>CNV_NOTE_ANALYZER_OBT</td>\r\n<td>CNV_NA_OBT</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>ABAP Integration for SAP Data Intelligence (DI)</p>\r\n</td>\r\n<td>CNV_NOTE_ANALYZER_DI</td>\r\n<td>CNV_NA_DI</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>S4HANA Migration Cockpit (MC)</p>\r\n</td>\r\n<td>CNV_NOTE_ANALYZER_MC_EXT</td>\r\n<td>CNV_NA_MC</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SAP Landscape Transformation (SLT) Replication Server</p>\r\n</td>\r\n<td>CNV_NOTE_ANALYZER_SLT</td>\r\n<td>CNV_NA_SLT</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Near Zero Downtime Technology (NZDT)</p>\r\n</td>\r\n<td>CNV_NOTE_ANALYZER_NZDT</td>\r\n<td>CNV_NA_NZDT</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Each program checks whether all notes relevant for its scenario are installed.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>LT Replication Server, SLT, replication, replicator, trigger, realtime, DMIS 2011,&#160;DMIS 2018, DMIS 2020, notes, central system, source system,&#160;NZDT,&#160;SLT, Near-Zero Downtime Technology, <br />sFIN, sLOG, S/4HANA, simple finance, simple logistics, upgrade, IUUC, Incremental Upgrade and Unicode Conversion, trigger-based data replication; NZDT for Repeatable Delta Conversion</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>DMIS 2011 SP11 or higher, DMIS 2018,&#160;DMIS 2020 or S/4HANA 1610, 1709, 1809, 1909 or higher.</p>\r\n<p>Furthermore, at least&#160;release SAP_BASIS 640 is required. Some of the SNOTE corrections below might be required as well:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 919px; height: 118px;\">\r\n<tbody>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\" width=\"64\"><strong>SAP Note</strong></td>\r\n<td class=\"xl65\" width=\"752\"><strong>Title of SAP Note</strong></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl67\" height=\"20\"><a target=\"_blank\" href=\"/notes/1668882\" title=\"1668882  - Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753\">1668882</a></td>\r\n<td class=\"xl69\"><a target=\"_blank\" href=\"/notes/1668882\" title=\"1668882  - Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753\">Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753</a></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"64\"><a target=\"_blank\" href=\"/notes/875986\" title=\"875986  - Note Assistant: Important notes for SAP_BASIS up to 702\">875986</a></td>\r\n<td class=\"xl68\"><a target=\"_blank\" href=\"/notes/875986\" title=\"875986  - Note Assistant: Important notes for SAP_BASIS up to 702\">Note Assistant: Important notes for SAP_BASIS up to 702</a></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"64\"><a target=\"_blank\" href=\"/notes/1734555\" title=\"1734555  - SNOTE: Performance fix in Interface enhancement\">1734555</a></td>\r\n<td class=\"xl68\"><a target=\"_blank\" href=\"/notes/1734555\" title=\"1734555  - SNOTE: Performance fix in Interface enhancement\">SNOTE: Performance fix in Interface enhancement</a></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"64\"><a target=\"_blank\" href=\"/notes/1273082\" title=\"1273082  - SNOTE: Error when implementing ENHO objects\">1273082</a></td>\r\n<td class=\"xl68\"><a target=\"_blank\" href=\"/notes/1273082\" title=\"1273082  - SNOTE: Error when implementing ENHO objects\">SNOTE: Error when implementing ENHO objects</a></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"20\" width=\"64\"><a target=\"_blank\" href=\"/notes/1273082\" title=\"1273082  - SNOTE: Error when implementing ENHO objects\">2872565&#160;</a></td>\r\n<td class=\"xl68\"><a target=\"_blank\" href=\"/notes/2872565 \">Prepare FM SCWB_NOTES_RESOLVE_NECESSARY for SAP Support backbone change</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"longtext\" style=\"font-size: 100.01%;\">\r\n<p>For the analysis, the latest version of the central note is downloaded, and all relevant note numbers are extracted.<br />The latest versions of the corresponding notes are downloaded automatically.<br />The runtime depends on the number of notes that need to be downloaded and checked.</p>\r\n<p>The central note number for your release is used as default input parameter. <br />Furthermore, you can choose between Source and Central System&#160;(SLT) or Source, Central, Proxy and Target system (NZDT). <br />Note, the scenario NZDT also includes all required SLT SAP Notes.</p>\r\n<p>After the execution, all notes&#160;you should install are listed. <br />There is also an indicator for notes that require manual activities. Via a left click at one of the note numbers, you are redirected to transaction 'SNOTE' and <br />can install the selected note directly.<br />(This option is only available on the local system, i.e. for RFC connection NONE).</p>\r\n<p>Note: This note must be implemented in the target system of the RFC connection, in case an RFC connection is specified when starting Note Analyzer.</p>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D040066)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I533994)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003016862/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003016862/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003016862/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003016862/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003016862/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003016862/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003016862/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003016862/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003016862/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "CNV_NOTE_ANALYZER_TX_EN_DI.txt", "FileSize": "1226", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900000299382021&iv_version=0078&iv_guid=00109B36BC6E1EDBB69BE4F63B4660E9"}, {"FileName": "CNV_NOTE_ANALYZER_TX_EN_NZDT.txt", "FileSize": "1286", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900000299382021&iv_version=0078&iv_guid=00109B36D5921EDBB69BE64BAEC760E5"}, {"FileName": "CNV_NOTE_ANALYZER_TX_EN_OBT.txt", "FileSize": "1085", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900000299382021&iv_version=0078&iv_guid=00109B36D5C21EDBB69BE727FBACE0E5"}, {"FileName": "CNV_NOTE_ANALYZER_TX_EN_SLT.txt", "FileSize": "1241", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900000299382021&iv_version=0078&iv_guid=00109B36BC1E1EDBBDD0FD9F595C60EC"}, {"FileName": "CNV_NOTE_ANALYZER_TX_EN_MC_EXT.txt", "FileSize": "1962", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900000299382021&iv_version=0078&iv_guid=00109B36D66A1EECA28098A558E7D6DE"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2596411", "RefComponent": "CA-LT-NAT", "RefTitle": "Note Analyzer for ABAP-based Migration and Replication Technology (DMIS2011/DMIS2018/DMIS2020/S/4HANA)", "RefUrl": "/notes/2596411"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2716746", "RefComponent": "HAN-DP-LTR", "RefTitle": "Error: SLT central note could not be parsed with DMC_NOTE_ANALYZER", "RefUrl": "/notes/2716746 "}, {"RefNumber": "2633598", "RefComponent": "CA-LT-SLT", "RefTitle": "Required information for troubleshooting SLT issues", "RefUrl": "/notes/2633598 "}, {"RefNumber": "2254376", "RefComponent": "CA-LT-SLT", "RefTitle": "SLT trigger(s) in an inconsistent state", "RefUrl": "/notes/2254376 "}, {"RefNumber": "3291483", "RefComponent": "CA-LT-MC", "RefTitle": "Important Corrections for Data Migration Objects of SAP S/4HANA 2022 FPS0 and FPS1 delivery", "RefUrl": "/notes/3291483 "}, {"RefNumber": "2707731", "RefComponent": "BC-UPG-DTM-TLA", "RefTitle": "Prerequisites and restrictions of Zero Downtime Option of SUM for SAP S/4HANA", "RefUrl": "/notes/2707731 "}, {"RefNumber": "2596411", "RefComponent": "CA-LT-NAT", "RefTitle": "Note Analyzer for ABAP-based Migration and Replication Technology (DMIS2011/DMIS2018/DMIS2020/S/4HANA)", "RefUrl": "/notes/2596411 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DMIS", "From": "2010_1_640", "To": "2010_1_640", "Subsequent": "X"}, {"SoftwareComponent": "DMIS", "From": "2011_1_640", "To": "2011_1_640", "Subsequent": "X"}, {"SoftwareComponent": "DMIS", "From": "2011_1_700", "To": "2011_1_700", "Subsequent": "X"}, {"SoftwareComponent": "DMIS", "From": "2011_1_710", "To": "2011_1_710", "Subsequent": "X"}, {"SoftwareComponent": "DMIS", "From": "2011_1_730", "To": "2011_1_730", "Subsequent": "X"}, {"SoftwareComponent": "DMIS", "From": "2011_1_731", "To": "2011_1_731", "Subsequent": "X"}, {"SoftwareComponent": "DMIS", "From": "2018_1_752", "To": "2018_1_752", "Subsequent": "X"}, {"SoftwareComponent": "DMIS", "From": "2020", "To": "2020", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "108", "To": "108", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0003016862/19773"}, {"SoftwareComponent": "DMIS", "NumberOfCorrin": 1, "URL": "/corrins/0003016862/1365"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; DMIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Test Data M...|<br/>| Release 2010_1_640&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2011_1_700&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2011_1_710&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2011_1_730&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2011_1_731&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2018_1_752&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2020&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 105&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 106&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 107&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 108&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Caution:<br/>When implementing this Note, SNOTE might tell you that objects already  exists and not all changes can be applied. You must set the flag in the  checkbox (next to the yellow triangle symbol in the popup) to override  the existing objects in order to get the newest version delivered by this NOTE.<br/><br/>Creating of function group CNVNA<br/>The function group CNVNA comes with DMIS 2018 SP05 resp. S/4HANA 2020  FPS02.<br/>If the function group CNVNA does not exist in the system then please use  transaction SE80 to create the function group manually:<br/>- Call transaction SE80<br/>- Out of the drop down list choose FUNCTION GROUP and enter CNVNA as the  function group name<br/>- Click to the display button and confirm the upcoming popup to create the object<br/>- Enter a short text (e.g. ’Note Analyser New’) and press SAVE<br/>- Finally choose CNV_BASIS_UTILITIES as the package<br/>- Activate the function group<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; DMIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Test Data M...|<br/>| Release 2010_1_640&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2011_1_700&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2011_1_710&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2011_1_730&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2011_1_731&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2018_1_752&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 2020&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>|Software Component&nbsp;&nbsp; S4CORE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 102&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 103&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 104&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 105&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 106&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 107&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 108&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>TEXT ELEMENTS<br/><br/>Depending on which scenario you intend to execute one or more of the  reports given within the table. Use the corresponding files with the text elements (TX) for the reports.<br/><br/>For a particular report the appropriate file with the attached text  elements as shown in the following:<br/><br/>CNV_NOTE_ANALYZER_DI   &lt;&gt; CNV_NOTE_ANALYZER_TX_EN_DI.txt<br/><br/>CNV_NOTE_ANALYZER_MC_EXT  &lt;&gt; CNV_NOTE_ANALYZER_TX_EN_MC_EXT.txt<br/><br/>CNV_NOTE_ANALYZER_NZDT   &lt;&gt; CNV_NOTE_ANALYZER_TX_EN_NZDT.txt<br/><br/>CNV_NOTE_ANALYZER_OBT  &lt;&gt; CNV_NOTE_ANALYZER_TX_EN_OBT.txt<br/><br/>CNV_NOTE_ANALYZER_SLT   &lt;&gt; CNV_NOTE_ANALYZER_TX_EN_SLT.txt<br/><br/><br/>(1) Go to transaction SE38<br/>(2) Open the appropriate report<br/>(3) Open Text symbols via Goto &gt; Text Elements &gt; Text Symbols (in the  navigation bar)<br/>(4) Switch to edit mode.<br/>(5) Create all Text Symbols of the attached file (via copy and paste).<br/>(6) Save and activate the new Text Symbols.<br/><br/><br/>TRANSACTION CODES<br/><br/>- Call transaction SE80, choose \"Package\".<br/>- Enter \"CNV_BASIS_UTILITIES\" into the input field.<br/>- Expand the node \"Programs\" and select the program for which<br/>&nbsp;&nbsp;you to create the transaction code<br/>- click right mouse button and choose \"Create\" -&gt; \"Transaction\"<br/>Make the following assignments between scenario, transaction code and  program/report name:<br/><br/><br/>Scenario  Transaction code  Report name / Program<br/>DI    CNV_NA_DI     CNV_NOTE_ANALYZER_DI<br/>MC    CNV_NA_MC    CNV_NOTE_ANALYZER_MC_EXT<br/>OBT  CNV_NA_OBT   CNV_NOTE_ANALYZER_OBT<br/>NZDT   CNV_NA_NZDT   CNV_NOTE_ANALYZER_NZDT<br/>SLT  CNV_NA_SLT   CNV_NOTE_ANALYZER_SLT<br/><br/><br/>- Assign a short text like \"Note Analyser: Scenario &lt;scenario&gt;<br/>- In box \"Start object\" choose \"Program and selection screen<br/>&nbsp;&nbsp;(report transaction)\"<br/>- As the selection screen enter \"1000\"<br/>- For GUI support mark all three check boxes<br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "DMIS", "ValidFrom": "2010_1_640", "ValidTo": "2018_1_752", "Number": "3016862 ", "URL": "/notes/3016862 ", "Title": "DMIS Note Analyzers with separated scenarios for ABAP-based Migration and Replication Technology  (DMIS2011/DMIS2018/DMIS2020/SAP S/4HANA)", "Component": "CA-LT-NAT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "108", "Number": "3016862 ", "URL": "/notes/3016862 ", "Title": "DMIS Note Analyzers with separated scenarios for ABAP-based Migration and Replication Technology  (DMIS2011/DMIS2018/DMIS2020/SAP S/4HANA)", "Component": "CA-LT-NAT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}