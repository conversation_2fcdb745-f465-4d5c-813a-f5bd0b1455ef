{"Request": {"Number": "684106", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 608, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000684106?language=E&token=4DF2B4BCB3E17EDE3DF72098ECA50C72"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000684106", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000684106/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "684106"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 42}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "2010/09/13"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-NT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Windows"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Windows", "value": "BC-OP-NT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-NT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "684106 - Microsoft runtime DLLs"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=684106&TargetLanguage=EN&Component=BC-OP-NT&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/684106/D\" target=\"_blank\">/notes/684106/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>32-bit programs as of SAP Release 6.40 or 64-bit programs as of SAP Release SAP NetWeaver 7.1 cannot be started because Microsoft runtime DLLs are missing.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<p>msvcr71.dll, msvcp71.dll, mfc71.dll, mfc71u.dll,msvcr80.dll, winsxs, _popen, popen, vcredist, c-runtime, dbghelp.dll, side-by-side, 14001</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<p>In SAP Release SAP NetWeaver &#39;04 (6.40) and SAP NetWeaver 2004s (7.0), the Visual Studio .NET 2003 compiler for Windows x86 (32-bit) is used. For SAP Releases 4.6D EX2, 6.40 EX2, 7.01, 7.10, and later, the Visual Studio 2005 compiler is used on all Windows platforms.<br /><br />The required C runtime DLLs are usually installed during the installation with SAPinst. However, if you only use individual programs from 6.40 or higher, the required DLLs may be missing in your system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3>\r\n<p>Install the missing DLLs as follows:<br /><br />For SAP Releases 6.40 and 7.0, unpack the archive R3DLLINST.ZIP (C-runtime 7.1) contained in this SAP Note and execute &quot;R3DLLINS.EXE&quot; in the subdirectory NTPATCH.<br />The attachment R3DLLINST.ZIP has been updated.<br /><br />For SAP Releases 4.6D EX2, Web AS 6.40 EX2, SAP NetWeaver 7.01, and 7.10 and higher, download the installation program vcredist_&lt;platform>.exe (C-runtime 8.0; this is contained in the Microsoft Visual C++ 2005 SP1 Redistributables) from the Microsoft download page. Then run it. The vcredist_&lt;platform> installation packages are also delivered on the installation master DVDs of SAP Releases 7.01 and 7.10 and are located in the directory NTPATCH.<br /><br />Perform the following steps:</p>\r\n<ol><ol>1. Go to the following Internet address:</ol></ol><ol>http://www.microsoft.com/technet/security/bulletin/MS09-035.mspx</ol><ol>2. On the page, scroll to the &quot;Affected Software&quot; section.</ol><ol><ol>3. Click on the link:</ol></ol><ol>Microsoft Visual C++ 2005 Service Pack 1 Redistributable Package (KB973544)</ol><ol>4. Scroll down to the &quot;Files in This Download&quot; section.</ol><ol>5. Download one of the following platform-specific files:</ol>\r\n<ul>\r\n<ul>\r\n<li>Itanium System: vcredist_IA64.exe</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>64-bit SAP system on Intel 64-bit: vcredist_x64.exe</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>32-bit SAP system on Intel 64-bit: vcredist_x86.exe</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP system on Intel 32-bit: vcredist_x86.exe</li>\r\n</ul>\r\n</ul>\r\n<ol>6. Execute the downloaded file and follow the installation instructions.</ol>\r\n<p><br /><strong>Known issues:</strong><br />Handleleak for _popen call on an x86 platform (SAP Release 6.40 or 7.0)<br /><br />The problem is solved with MS KB 892086. The files attached to this SAP Note contain this patch.<br /><br />dbghelp.dll:<br />dbghelp.dll does not belong to the Microsoft C-Runtime Library, but is provided as an attachment to the download in this SAP Note.<br />dbghelp.dll should already be included in the current operating systems (Windows Server 2003 R2, Windows Vista, Windows Server 2008).<br />The dbghelp.dll installation is performed automatically with SAPinst for the SAP server products and does not have to be installed manually.<br />Check the existence of the file in the \\windows\\system32 and \\windows\\syswow64 directories and replace the file only if the version provided here is more recent than the existing one. The version of the file can be determined using Windows Explorer (right mouse button, properties, version).<br />Caution: dbghelp.dll in the system32 directory must not be replaced if &quot;Windows System Fileprotection&quot; is activated for the file. The &quot;Windows System Fileprotection&quot; is set for dbghelp.dll if this file is available again in the subdirectory %█ r\\system32\\<strong>dllcache</strong>.<br />Make sure that you install dbghelp.dll twice on the 64-bit Windows platforms:<br />The DLL contained in the archives dbghelp_x64.zip (for AMD64 or EM64T system) or dbghelp_ia64.zip (for IA64 systems) is copied to the \\Windows\\Sytem32 directory. The DLL contained in dbghelp_i386.zip is copied to \\windows\\syswow64 on 64-bit Windows systems, and to \\windows\\System32 on 32-bit systems.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON><PERSON> (D035306)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON><PERSON> (D035306)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000684106/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "dbghelp_i386.zip", "FileSize": "452", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000245872003&iv_version=0042&iv_guid=20E822D8773C964FBEE83DD185BB6B31"}, {"FileName": "dbghelp_ia64.zip", "FileSize": "1295", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000245872003&iv_version=0042&iv_guid=30808DEDAD2AFD42BE5227738BB81755"}, {"FileName": "dbghelp_x64.zip", "FileSize": "506", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000245872003&iv_version=0042&iv_guid=73A5ECC7F7058C4FA0D9019E4F7F5D12"}, {"FileName": "r3dllinst.zip", "FileSize": "1312", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000245872003&iv_version=0042&iv_guid=A8B6820513AB5542AD945C8BF5B5884B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "967088", "RefComponent": "SRM-CAT-MDM", "RefTitle": "SRM-MDM Catalog 1.0 Installation &amp; Configuration", "RefUrl": "/notes/967088"}, {"RefNumber": "954960", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRARCHIVE/BRBACKUP backups on Windows terminate", "RefUrl": "/notes/954960"}, {"RefNumber": "940550", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Installation of V8.1/V8.2 FixPaks (up to FP8) on Windows", "RefUrl": "/notes/940550"}, {"RefNumber": "900313", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "Installation of 32-bit 4.6D SAP kernel with 64-bit MaxDB 7.5/7.6", "RefUrl": "/notes/900313"}, {"RefNumber": "863813", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/863813"}, {"RefNumber": "857475", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for SCM 5.0", "RefUrl": "/notes/857475"}, {"RefNumber": "857360", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "WAD: Crash when saving larger templates as of FEP 16", "RefUrl": "/notes/857360"}, {"RefNumber": "82751", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/82751"}, {"RefNumber": "782181", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "SAP Java Connector Service Release 2.1.4", "RefUrl": "/notes/782181"}, {"RefNumber": "774161", "RefComponent": "BC-DB-SDB", "RefTitle": "Access to DB50 or LC10 is not possible.", "RefUrl": "/notes/774161"}, {"RefNumber": "770982", "RefComponent": "SCM-TEC", "RefTitle": "SAP SCM 4.0 SP Stack 10/2004: Release/Information Notes", "RefUrl": "/notes/770982"}, {"RefNumber": "761432", "RefComponent": "BC-DB-SDB", "RefTitle": "Error \\&quot;program dbmrfc@sapdb not registered\\&quot;", "RefUrl": "/notes/761432"}, {"RefNumber": "722516", "RefComponent": "SCM-APO-OPT", "RefTitle": "Optimizer does not start - Message: Unable To Locate DLL", "RefUrl": "/notes/722516"}, {"RefNumber": "703774", "RefComponent": "SCM-APO-OPT", "RefTitle": "SCM 4.1: Importing an APO Optimizer Version", "RefUrl": "/notes/703774"}, {"RefNumber": "694057", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agents and kernel: <PERSON><PERSON> 2004 (composite SAP Note)", "RefUrl": "/notes/694057"}, {"RefNumber": "664679", "RefComponent": "BC-CST", "RefTitle": "Installation of 6.40 kernel in SAP Web AS 6.10/6.20", "RefUrl": "/notes/664679"}, {"RefNumber": "657129", "RefComponent": "BC-MID-BUS", "RefTitle": "Business Connector 4.7 Service Release 9 (SR9)", "RefUrl": "/notes/657129"}, {"RefNumber": "603981", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Installation of V8.2 FixPaks (as of FP9) on Win NT/Win 2000", "RefUrl": "/notes/603981"}, {"RefNumber": "520423", "RefComponent": "BC-MID-BUS", "RefTitle": "Business Connector 4.6 Service Release 9a (SR9a)", "RefUrl": "/notes/520423"}, {"RefNumber": "413708", "RefComponent": "BC-MID-RFC", "RefTitle": "Current RFC library", "RefUrl": "/notes/413708"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "New archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "1900479", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing SCM Optimizer Version 11.0", "RefUrl": "/notes/1900479"}, {"RefNumber": "1780433", "RefComponent": "BC-DB-MSS", "RefTitle": "System copy of SAP systems to Windows Server 2012 (R2): SQL Server", "RefUrl": "/notes/1780433"}, {"RefNumber": "1778109", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing SCM Optimizer Version 10.0", "RefUrl": "/notes/1778109"}, {"RefNumber": "1686826", "RefComponent": "SCM-APO-OPT", "RefTitle": "Help for Importing an SCM Optimizer Version", "RefUrl": "/notes/1686826"}, {"RefNumber": "1686825", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM optimizer for TM 9.0", "RefUrl": "/notes/1686825"}, {"RefNumber": "1559307", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for SCM 7.0 EhP2 and TM 8.1", "RefUrl": "/notes/1559307"}, {"RefNumber": "1498369", "RefComponent": "BC-IAM-IDM", "RefTitle": "SAP NetWeaver Identity Management 7.2", "RefUrl": "/notes/1498369"}, {"RefNumber": "1491669", "RefComponent": "CA-CAD-LIB-PRO", "RefTitle": "HRE: SAP PLM Integration Unicode Character Sets", "RefUrl": "/notes/1491669"}, {"RefNumber": "1489763", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM optimizer for TM 8.0", "RefUrl": "/notes/1489763"}, {"RefNumber": "1480843", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1480843"}, {"RefNumber": "1451454", "RefComponent": "BC-IAM-IDM", "RefTitle": "SAP NetWeaver Identity Management 7.1 SP5", "RefUrl": "/notes/1451454"}, {"RefNumber": "1330450", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for EHP1 for SCM 7.0", "RefUrl": "/notes/1330450"}, {"RefNumber": "1256600", "RefComponent": "BC-UPG-OCS", "RefTitle": "Installation of SAP ERP 6.0 EHP4 using transaction SAINT", "RefUrl": "/notes/1256600"}, {"RefNumber": "1234400", "RefComponent": "BC-SEC-SSF", "RefTitle": "SAPSSOEXT Patch 3: Corrections for Windows 64bit", "RefUrl": "/notes/1234400"}, {"RefNumber": "1216588", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for SCM 7.0 and TM 7.0", "RefUrl": "/notes/1216588"}, {"RefNumber": "1170069", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1170069"}, {"RefNumber": "1158215", "RefComponent": "BC-TRX", "RefTitle": "TREX: Operating System Requirements", "RefUrl": "/notes/1158215"}, {"RefNumber": "1129577", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS Agents and Kernel: <PERSON><PERSON> 2008", "RefUrl": "/notes/1129577"}, {"RefNumber": "1086956", "RefComponent": "BC-CST", "RefTitle": "46D_EX2 kernel", "RefUrl": "/notes/1086956"}, {"RefNumber": "1058988", "RefComponent": "BC-CST", "RefTitle": "640_EX2 kernel", "RefUrl": "/notes/1058988"}, {"RefNumber": "1040335", "RefComponent": "BC-SEC-SSF", "RefTitle": "SAPSSOEXT Patch 4: Corrections and Enhancements", "RefUrl": "/notes/1040335"}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installation of package SAPHOSTAGENT", "RefUrl": "/notes/1031096"}, {"RefNumber": "1019666", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for SCM 5.1", "RefUrl": "/notes/1019666"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "2443697", "RefComponent": "BC-TRX", "RefTitle": "Cannot start TREXAdmin.bat", "RefUrl": "/notes/2443697 "}, {"RefNumber": "2835896", "RefComponent": "SCM-APO-OPT", "RefTitle": "Installation of SCM Optimizer Version 14.0", "RefUrl": "/notes/2835896 "}, {"RefNumber": "2316985", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing SCM Optimizer Version 13.0", "RefUrl": "/notes/2316985 "}, {"RefNumber": "2256415", "RefComponent": "BC-FES-AIT-CTR", "RefTitle": "Adjustment of RFC controls (logon, function, table and BAPI) for using the SAP NetWeaver RFC library", "RefUrl": "/notes/2256415 "}, {"RefNumber": "2214822", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing SCM Optimizer Version 12.0", "RefUrl": "/notes/2214822 "}, {"RefNumber": "1587886", "RefComponent": "EHS-BD-TLS", "RefTitle": "EHS Web Interface: Central Note", "RefUrl": "/notes/1587886 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installation of a 7.20 kernel on SAP Web Application Server 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1031096", "RefComponent": "BC-CCM-HAG", "RefTitle": "Installation of package SAPHOSTAGENT", "RefUrl": "/notes/1031096 "}, {"RefNumber": "1158215", "RefComponent": "BC-TRX", "RefTitle": "TREX: Operating System Requirements", "RefUrl": "/notes/1158215 "}, {"RefNumber": "1900479", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing SCM Optimizer Version 11.0", "RefUrl": "/notes/1900479 "}, {"RefNumber": "1778109", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing SCM Optimizer Version 10.0", "RefUrl": "/notes/1778109 "}, {"RefNumber": "1498369", "RefComponent": "BC-IAM-IDM", "RefTitle": "SAP NetWeaver Identity Management 7.2", "RefUrl": "/notes/1498369 "}, {"RefNumber": "1559307", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for SCM 7.0 EhP2 and TM 8.1", "RefUrl": "/notes/1559307 "}, {"RefNumber": "549268", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "Release and Support Strategy for SAP JCo 2.x", "RefUrl": "/notes/549268 "}, {"RefNumber": "1686826", "RefComponent": "SCM-APO-OPT", "RefTitle": "Help for Importing an SCM Optimizer Version", "RefUrl": "/notes/1686826 "}, {"RefNumber": "413708", "RefComponent": "BC-MID-RFC", "RefTitle": "Current RFC library", "RefUrl": "/notes/413708 "}, {"RefNumber": "857475", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for SCM 5.0", "RefUrl": "/notes/857475 "}, {"RefNumber": "1019666", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for SCM 5.1", "RefUrl": "/notes/1019666 "}, {"RefNumber": "1216588", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for SCM 7.0 and TM 7.0", "RefUrl": "/notes/1216588 "}, {"RefNumber": "1330450", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM Optimizer for EHP1 for SCM 7.0", "RefUrl": "/notes/1330450 "}, {"RefNumber": "1489763", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM optimizer for TM 8.0", "RefUrl": "/notes/1489763 "}, {"RefNumber": "1686825", "RefComponent": "SCM-APO-OPT", "RefTitle": "Importing the SCM optimizer for TM 9.0", "RefUrl": "/notes/1686825 "}, {"RefNumber": "1094412", "RefComponent": "BC-MID-BUS", "RefTitle": "Release and Support Strategy for SAP Business Connector 4.8", "RefUrl": "/notes/1094412 "}, {"RefNumber": "603981", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Installation of V8.2 FixPaks (as of FP9) on Win NT/Win 2000", "RefUrl": "/notes/603981 "}, {"RefNumber": "940550", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Installation of V8.1/V8.2 FixPaks (up to FP8) on Windows", "RefUrl": "/notes/940550 "}, {"RefNumber": "782181", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "SAP Java Connector Service Release 2.1.4", "RefUrl": "/notes/782181 "}, {"RefNumber": "1058988", "RefComponent": "BC-CST", "RefTitle": "640_EX2 kernel", "RefUrl": "/notes/1058988 "}, {"RefNumber": "1129577", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS Agents and Kernel: <PERSON><PERSON> 2008", "RefUrl": "/notes/1129577 "}, {"RefNumber": "694057", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS agents and kernel: <PERSON><PERSON> 2004 (composite SAP Note)", "RefUrl": "/notes/694057 "}, {"RefNumber": "703774", "RefComponent": "SCM-APO-OPT", "RefTitle": "SCM 4.1: Importing an APO Optimizer Version", "RefUrl": "/notes/703774 "}, {"RefNumber": "1086956", "RefComponent": "BC-CST", "RefTitle": "46D_EX2 kernel", "RefUrl": "/notes/1086956 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "New archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1242735", "RefComponent": "BC-MID-BUS", "RefTitle": "Business Connector 4.8 does not start after installation", "RefUrl": "/notes/1242735 "}, {"RefNumber": "1491669", "RefComponent": "CA-CAD-LIB-PRO", "RefTitle": "HRE: SAP PLM Integration Unicode Character Sets", "RefUrl": "/notes/1491669 "}, {"RefNumber": "1451454", "RefComponent": "BC-IAM-IDM", "RefTitle": "SAP NetWeaver Identity Management 7.1 SP5", "RefUrl": "/notes/1451454 "}, {"RefNumber": "657129", "RefComponent": "BC-MID-BUS", "RefTitle": "Business Connector 4.7 Service Release 9 (SR9)", "RefUrl": "/notes/657129 "}, {"RefNumber": "722516", "RefComponent": "SCM-APO-OPT", "RefTitle": "Optimizer does not start - Message: Unable To Locate DLL", "RefUrl": "/notes/722516 "}, {"RefNumber": "1256600", "RefComponent": "BC-UPG-OCS", "RefTitle": "Installation of SAP ERP 6.0 EHP4 using transaction SAINT", "RefUrl": "/notes/1256600 "}, {"RefNumber": "520423", "RefComponent": "BC-MID-BUS", "RefTitle": "Business Connector 4.6 Service Release 9a (SR9a)", "RefUrl": "/notes/520423 "}, {"RefNumber": "1040335", "RefComponent": "BC-SEC-SSF", "RefTitle": "SAPSSOEXT Patch 4: Corrections and Enhancements", "RefUrl": "/notes/1040335 "}, {"RefNumber": "1234400", "RefComponent": "BC-SEC-SSF", "RefTitle": "SAPSSOEXT Patch 3: Corrections for Windows 64bit", "RefUrl": "/notes/1234400 "}, {"RefNumber": "967088", "RefComponent": "SRM-CAT-MDM", "RefTitle": "SRM-MDM Catalog 1.0 Installation &amp; Configuration", "RefUrl": "/notes/967088 "}, {"RefNumber": "954960", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRARCHIVE/BRBACKUP backups on Windows terminate", "RefUrl": "/notes/954960 "}, {"RefNumber": "857360", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "WAD: Crash when saving larger templates as of FEP 16", "RefUrl": "/notes/857360 "}, {"RefNumber": "774161", "RefComponent": "BC-DB-SDB", "RefTitle": "Access to DB50 or LC10 is not possible.", "RefUrl": "/notes/774161 "}, {"RefNumber": "664679", "RefComponent": "BC-CST", "RefTitle": "Installation of 6.40 kernel in SAP Web AS 6.10/6.20", "RefUrl": "/notes/664679 "}, {"RefNumber": "770982", "RefComponent": "SCM-TEC", "RefTitle": "SAP SCM 4.0 SP Stack 10/2004: Release/Information Notes", "RefUrl": "/notes/770982 "}, {"RefNumber": "761432", "RefComponent": "BC-DB-SDB", "RefTitle": "Error \\&quot;program dbmrfc@sapdb not registered\\&quot;", "RefUrl": "/notes/761432 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=684106&TargetLanguage=EN&Component=BC-OP-NT&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/684106/D\" target=\"_blank\">/notes/684106/D</a>."}}}}