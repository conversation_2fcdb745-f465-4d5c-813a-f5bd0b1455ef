{"Request": {"Number": "885955", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 317, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015961792017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000885955?language=E&token=800BFF29A9714FF84C51A4BC5D729849"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000885955", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000885955/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "885955"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.10.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade - general"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "885955 - Language content in DDIC import not imported (Upg 640/700)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>After upgrading to Basis 640 and Basis 700, some texts are missing, in the navigation for example. Other components may also be affected.<br /><br />During the dictionary import in the (upgrade) shadow system, language contents for languages other than German and English are not imported, even though the languages were installed prior to the upgrade. However, the corresponding languages are imported in the main import.<br /><br />This problem only occurs for packages that were included in the upgrade, for example Add-Ons, Support Packages, and so on.<br />The problem already occurred for Add-Ons such as SEM-BW 4.0 and FIN-BASIS 3.0.<br /><br />The symptom becomes apparent in the dictionary import log at the following line of the R3trans log:<br /><br />the following languages will be imported: DE<br /><br />further languages are not logged and are not imported either, even though the original system had installed further languages.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>TLSY7 language import SMLT RSTLAN_IMPORT_OCS postlanguageimport SEM-BW400 FIN-BASIS300<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by an error when generating the shadow system in the upgrade. The contents of a management table are not initialized correctly. This means that languages other than German and English from included packages are not imported in the dictionary import.<br /><br />This concerns upgrades of source release Basis 620 to target release Basis 640.<br /><br />The products concerned are</p> <UL><LI>ECC 5.00 and ECC5.00 SR1</LI></UL> <UL><LI>SCM 4.1</LI></UL> <UL><LI>SRM 400 and SRM 400 SR1</LI></UL> <UL><LI>Netweaver &#x00B4;04/WebAS 640 and WebAS 640 SR1</LI></UL> <p><br />It also affects (pre ramp-up) upgrades to Basis 700 (for example, GTS 7.0)</p> <UL><LI>CRM 5.0</LI></UL> <UL><LI>ECC 6.0/ERP2005</LI></UL> <UL><LI>SCM 5.0</LI></UL> <UL><LI>SRM 5.0/SRM Server 5.5</LI></UL> <UL><LI>NW2004s</LI></UL> <p><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The correction is delivered by using the FixBuffer. Only use the most recent FixBuffer to start the upgrades to the products mentioned above. The date of the FixBuffer must be at least October 06, 2005. The respective minimum versions are listed below:<br /></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Product</TH><TH ALIGN=LEFT> Fixbuffer version</TH><TH ALIGN=LEFT> Basis</TH><TH ALIGN=LEFT> </TH></TR> <TR><TD>ECC 5.0</TD><TD> 33</TD><TD> 640</TD></TR> <TR><TD>ECC 5.0 SR1</TD><TD> 18</TD><TD> .</TD></TR> <TR><TD>SCM 4.1</TD><TD> 28</TD><TD> .</TD></TR> <TR><TD>SRM 4.0</TD><TD> 28</TD><TD> .</TD></TR> <TR><TD>SRM 4.0 SR1</TD><TD> 13</TD><TD> .</TD></TR> <TR><TD>WebAs 640</TD><TD> 33</TD><TD> .</TD></TR> <TR><TD>WebAs 640 SR1</TD><TD> 17</TD><TD> .</TD></TR> <TR><TD></TD></TR> <TR><TD>CRM 5.0</TD><TD> 2</TD><TD> 700</TD></TR> <TR><TD>ECC 6.0/ERP2005</TD><TD> 2</TD><TD> .</TD></TR> <TR><TD>SCM 5.0</TD><TD> 2</TD><TD> .</TD></TR> <TR><TD>SRM 5.0</TD><TD> 2</TD><TD> .</TD></TR> <TR><TD>Netweaver2004S</TD><TD> 8</TD><TD> .</TD></TR> </TABLE> <p><br />If the upgrade is already completed and you only notice that the language content is missing afterwards, there are the following options:<br /></p> <OL>1. Import the language again, in other words import all language packages, even those of Add-Ons again and then call RSTLAN_IMPORT_OCS. To do this, see notes 195442 and 352941.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the Add-On includes the language in the add-on package, so does not have its own language package, then you should import the language again by using <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"tp postlanguageimport\" <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This option is the preferred method, since the automatic post-import is supported with the exception of Add-Ons without a language package. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<OL>2. Manually import the language content of the packages concerned by using \"tp postlanguageimport\". The packages concerned are in the /usr/sap/put/log directory. This affects all logs starting with SAPH* except for the SAPHL* packages (these are the language packages that must not be processed).</OL> <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can find the package name by replacing SAPH with SAPK and leaving out the abbreviation for &lt;sid&gt;. Note 432272 contains a short description for the \"tp postlanguageimport\" command. <p><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The import sequence required is set by the chronological sequence of the log files. However, this option is only recommended for a few packages due to the rate of errors and the amount of manual effort required. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The tp command is then <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;tp postlanguageimport &lt;req&gt; &lt; SID&gt; pf=&lt;pfad-zu-TP_DOMAIN_xxx.PFL&gt; <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; clientcascade=yes ctc=0 <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;for &lt;req&gt; , you should insert the package name <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;for &lt;SID&gt; , you should insert the SystemId <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;xxx must be replaced by the corresponding SID. <p><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CTS-LAN (Language Transport Tools (application server ABAP))"}, {"Key": "Responsible                                                                                         ", "Value": "D001330"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022030)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000885955/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000885955/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885955/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885955/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885955/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885955/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885955/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885955/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000885955/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "890202", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/890202"}, {"RefNumber": "882871", "RefComponent": "FIN-SEM", "RefTitle": "SEM-BW400/FINBASIS300: New languages/missing translations", "RefUrl": "/notes/882871"}, {"RefNumber": "877049", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Subsequent language installation for BI_CONT", "RefUrl": "/notes/877049"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "813658", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrades to products based on SAP NW 2004s AS", "RefUrl": "/notes/813658"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "724419", "RefComponent": "FIN-SEM", "RefTitle": "Upgrade to SAP SEM Release 4.0", "RefUrl": "/notes/724419"}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240"}, {"RefNumber": "432272", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting language data using tp/R3trans", "RefUrl": "/notes/432272"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "813658", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrades to products based on SAP NW 2004s AS", "RefUrl": "/notes/813658 "}, {"RefNumber": "889850", "RefComponent": "BC-INS", "RefTitle": "OBSOLETE: SAP NetWeaver 2004s based ABAP Inst/Upgrade for Asia", "RefUrl": "/notes/889850 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "877049", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Subsequent language installation for BI_CONT", "RefUrl": "/notes/877049 "}, {"RefNumber": "432272", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting language data using tp/R3trans", "RefUrl": "/notes/432272 "}, {"RefNumber": "882871", "RefComponent": "FIN-SEM", "RefTitle": "SEM-BW400/FINBASIS300: New languages/missing translations", "RefUrl": "/notes/882871 "}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240 "}, {"RefNumber": "724419", "RefComponent": "FIN-SEM", "RefTitle": "Upgrade to SAP SEM Release 4.0", "RefUrl": "/notes/724419 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}