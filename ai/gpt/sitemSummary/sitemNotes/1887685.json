{"Request": {"Number": "1887685", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 3451, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017690012017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=7D2CE3614BFE1D69FF9D11BA18F475EC"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1887685"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "Released Internally"}, "ReleasedOn": {"_label": "Released On", "value": "17.01.2017"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-REO"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Reorganization"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization", "value": "FI-GL-REO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1887685 - FAQs: Profit Center Reorganization"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1887685&TargetLanguage=EN&Component=FI-GL-REO&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1887685/D\" target=\"_blank\">/notes/1887685/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note addresses questions about the PRCTR reorganization.<br />The last update was on 08.03.2016.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<p>PRCTR reorganization, PCA reorganization<br />FAQ<br />New G/L</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<p>Frequently asked questions and answers (=> FAQ) about the profit center reorganization in new General Ledger Accounting. Before you start your reorganization project, see SAP Note 1810605.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3>\r\n<ul>\r\n<li><strong>Where can you find further documentation?</strong></li>\r\n<ul>\r\n<li>On the SAP Help Portal at:<br />http://help.sap.com<br />Search for &#39;PRCTR Reorganization&#39;<br /><br /><a target=\"_blank\" class=\"title\" href=\"http://help.sap.com/saphelp_sfin100/helpdata/de/69/f789d4b09b48909f6bbf55d43e5fe9/frameset.htm\">Reorganization of Payables and Receivables - SAP Documentation</a><br /><br />Reorganization of Payables and Receivables, follow the link</li>\r\n<li>or follow the following links:<br />in German <br />PRCTR Reorganization<br /><a target=\"_blank\" href=\"http://help.sap.com/erp2005_ehp_05/helpdata/DE/FB/84A48D665E4FA9A2DC7B60ADEB7C7D/frameset.htm\">http://help.sap.com/erp2005_ehp_05/helpdata/DE/FB/84A48D665E4FA9A2DC7B60ADEB7C7D/frameset.htm</a><br />Segment Reorganization<br /><a target=\"_blank\" href=\"http://help.sap.com/erp2005_ehp_06/helpdata/DE/0C/78C515757F4B409DFCEA9845DBBB3D/frameset.htm\">http://help.sap.com/erp2005_ehp_06/helpdata/DE/0C/78C515757F4B409DFCEA9845DBBB3D/frameset.htm</a><br /><br />In English<br />PRCTR Reorganization<br /><a target=\"_blank\" href=\"http://help.sap.com/erp2005_ehp_05/helpdata/EN/FB/84A48D665E4FA9A2DC7B60ADEB7C7D/frameset.htm\">http://help.sap.com/erp2005_ehp_05/helpdata/EN/FB/84A48D665E4FA9A2DC7B60ADEB7C7D/frameset.htm</a><br />Segment Reorganization<br /><a target=\"_blank\" href=\"http://help.sap.com/erp2005_ehp_06/helpdata/EN/0C/78C515757F4B409DFCEA9845DBBB3D/frameset.htm\">http://help.sap.com/erp2005_ehp_06/helpdata/EN/0C/78C515757F4B409DFCEA9845DBBB3D/frameset.htm</a></li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong>SAP Note 1801767 (&quot;Profit center reorganization: Features&quot;) contains the functions of the business function &quot;FI-GL (New), Profit Center Reorganization, and Segment Reports (FIN_GL_REORG_1).</strong></li>\r\n</ul>\r\n</ul>\r\n<p> </p>\r\n<ul>\r\n<li><strong>Which prerequisites do you need to perform a PRCTR reorganization?</strong></li>\r\n<ul>\r\n<li><strong>Please read SAP Note 1810605 - General notes on profit center reorganization</strong></li>\r\n<li>Read the documentation on profit center reorganization mentioned above.</li>\r\n<li>Read the documentation for the process steps (activities) in Customizing:<br />Financial Accounting (New)<br />-> General Ledger Accounting (New)<br />--> Reorganization<br /><br /></li>\r\n</ul>\r\n<li><strong>Where can I find documentation for the document split to better understand error information?</strong><br />SAP Note 1085921 (&quot;Document splitting&quot;) informs you. Also read the attachments.<br /><br /></li>\r\n<li><strong>What are the execution options for the profit center reorganization?</strong><br />You have the following options:</li>\r\n<ul>\r\n<li>Execution directly in your ERP system using the corresponding transactions (ERP business function FIN_GL_REORG_1). Note that even if you use this option to perform your profit center reorganization, you require a valid license for the SAP standard product SAP Landscape Transformation (SAP LT) since the end of 2016 DM&amp;LT (Data Management &amp; Landscape Transformation).<br />- <strong>The license is required only for productive systems.</strong> Customers do not require a license for test or non-production systems.</li>\r\n<li>Run via Solution Manager and SAP LT, which requires LT installation. For more information about license acquisition, installation, and use of SAP LT in connection with the contained solution for profit center reorganization, see SAP Notes 1524246 and 1534197.<br /><br /></li>\r\n</ul>\r\n<li><strong>SAP-LT Development Support</strong><br />In accordance with SAP Note &quot;1463386 - SAP LT: Development Support (DEV-SUP)&quot;, production conversions are to be logged on under the component CA-LT-CNV to plan and ensure weekend support. Note that no support for the PRCTR or segment reorganization is transferred.<br />&quot;<strong>Therefore, support for the profit center reorganization is also not part of Development Support</strong>. Unlike the SAP LT solutions, where the entire history is converted or migrated, the reorganization is a future-oriented key date-based approach in which functions are called from the ERP business function. The reorganization performed as part of this function is not covered by SAP LT Development Support.&quot;<br /><br /></li>\r\n<li><strong>How can you check whether a PRCTR reorganization has been performed in the system</strong>?</li>\r\n<ul>\r\n<li>A prerequisite for the execution of the PRCTR reorganization is the activation of the &quot;Business Function&quot; &quot;FIN_GL_REORG_1&quot; (see also SAP Note 1534197). You can check the activation using the following transactions:</li>\r\n<ul>\r\n<li>SFW5:<br />..ENTERPRISE_BUSINESS_FUNCTIONS<br />    Function FIN_GL_REORG_1<br />PS: You can also call the documentation and the release information there.</li>\r\n<li>SFW1:<br />Switch: FAGL_FIN_REORGANIZATION_NS<br />&quot;Attributes&quot; tab -> double-click &quot;Global Status: on&quot;<br />-> Overview of the clients in which the function is available</li>\r\n</ul>\r\n<li>Start the task list overview with</li>\r\n<ul>\r\n<li>Transaction SE80 -> Package FAGL_REORGANIZATION -> Web Dynpro -> Web Dynpro Applicat. -> FAGL_R_PLAN_OVERVIEW -> Test in Business Client -> HTML Client or alternatively</li>\r\n<li>Via Favorites: Reorganization Plans: Overview (FAGL_R_PLAN_OVERVIEW)</li>\r\n<ul>\r\n<li>A new window opens with an overview of open and closed plans.<br /><br /></li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<li><strong>Where is the URL for the Web Dynpro entered?</strong></li>\r\n<ul>\r\n<li>Transaction SE80: Package: FAGL_REORGANIZATION_FW</li>\r\n<li>Object Name: Web Dynpro : FPM Applications : FPM Applications<br />FAGL_R_PLAN_OVERVIEW: Folder attributes: <br />URL: for example, <a target=\"_blank\" href=\"https://ldciqi3.wdf.sap.corp:44375/sap/bc/webdynpro/sap/fagl_r_plan_overview\">https://ldciqi3.wdf.sap.corp:44375/sap/bc/webdynpro/sap/fagl_r_plan_overview</a></li>\r\n</ul>\r\n</ul>\r\n<p> </p>\r\n<ul>\r\n<li>\r\n<p><strong>How are the favorites created for the PRCTR reorganization?</strong></p>\r\n</li>\r\n<ul>\r\n<li>\r\n<p>Right-click &quot;Favorites&quot; -> &quot;Add Other Objects&quot; -> &quot;Web Dynpro Applications&quot;.</p>\r\n</li>\r\n<li>\r\n<p>Web Dynpro Applications: FAGL_R_PLAN_OVERVIEW<br />Description: Reorganization Plans: Overview</p>\r\n</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Web Dynpro Applications: FAGL_R_OBJLIST_OVERVIEW<br />Description: Object Lists: Overview for Object Owner<br /><br /></li>\r\n</ul>\r\n<li><strong>Can multiple plans be created in parallel at the same time?<br /></strong>A &#39;new&#39; plan for a controlling area can only be created if there is no open plan with the same reorganization date. Legacy plans must be completed in advance. If you have a 1: 1 relationship between controlling area and company code, you can create a separate plan with the same reorganization date for each controlling area.<br />In the standard system, you cannot have two open plans for a controlling area with a different reorganization date.<br /><br /></li>\r\n<li> <strong>How does the reorganization process take place? Which process steps (activities) are to be taken into account?</strong></li>\r\n<ul>\r\n<li>Use the documentation on SAP Help Portal at:<br />- Profit center reorganization<br />-- Reorganization Process</li>\r\n<ul>\r\n<li>Preparation of the Reorganization Plan</li>\r\n<li>Generation of Object Lists</li>\r\n<li>Assignment of the new PRCTR for first-level objects by the object owner</li>\r\n<li>Reassignment of Objects</li>\r\n<li>Transfer of Stocks</li>\r\n</ul>\r\n<li>See SAP Note 1810605 - General notes on profit center reorganization.</li>\r\n<li>Our consultants will be happy to assist you with the complex project of the PRCTR or segment reorganization</li>\r\n</ul>\r\n</ul>\r\n<p> </p>\r\n<ul>\r\n<li><strong>How are the transfer postings made</strong><strong>?<br /></strong>Is there an entry in <strong>table FAGL_SPLINFO</strong> for the transfer postings?<br />Only for accounts that have the indicator for &quot;Open Item Management&quot; (SKB1-XOPVW) set, an entry is also created in the table FAGL_SPLINFO for the transfer posting.  For reconciliation accounts of receivables and payables (SKB1-MITKZ), the system does NOT create an entry in the table FAGL_SPLINFO.<br />When is ledger <strong>or Ledger Group</strong> Transferred?<br />Only foreign currency valuation transfer postings (by object type AP/AR) and WIP transfer postings are posted with a ledger group. The reorganization follows the logic of the original posting. <br />The reason for this is that the foreign currency valuation is always posted with a ledger group. WIP can be posted to a ledger group with an accounting principle and/or the account can have &quot;open item management&quot; and the transfer posting must be cleared in a subsequent process.</li>\r\n</ul>\r\n<p> </p>\r\n<ul>\r\n<li><strong>If necessary, do the special periods (13-16) also have to be closed in order to carry out the reassignment of the reorganization?</strong></li>\r\n<ul>\r\n<li>In general, SAP recommends that you complete the plan at the end of the reorganization month. The following risks exist if the plan remains open:</li>\r\n<ul>\r\n<li>The month-end closing of the reorganization month cannot be performed. Z. B. Settlement of orders or projects is not possible.</li>\r\n<li>Transfer postings are made with the reorganization date (in particular, material) - so that the accumulated stocks of the subsequent period are also made, which is not desired.</li>\r\n</ul>\r\n<li>Before the reassignment, the system checks whether the previous period was closed, but it does NOT check whether the special periods have been closed. The check is restricted to the regular periods.<br /><br /></li>\r\n</ul>\r\n<li><strong>How can objects be excluded from the reorganization?</strong></li>\r\n<ul>\r\n<li>In addition to the information below, see SAP Notes 2115768 and 2270249.</li>\r\n<li>First, how is the assignment of the &#39;New PRCTRs&#39; for first-level objects to take place?</li>\r\n<ul>\r\n<li>The reorganization tool knows two roles: the project owner and the object owner.</li>\r\n<li>In the standard system, you assign the objects on the first level to the object owner and set the status to enable the object owner to edit his or her list. In the second step, the object owner decides whether to assign the new PRCTR or whether the object participates in the reorganization.</li>\r\n<li>Since there can be a large number of first-level objects, SAP Consulting provides an individual solution with note:<br />2280287 - Excel upload for first level objects in profit center reorganization with new G/L</li>\r\n</ul>\r\n<li>Only first-level objects can be excluded from the reorganization. (If you want lower-level objects to appear as first-level objects, see the questions &quot;When will objects become 2. Hierarchy level (2nd Level Objects Included in Plan? What are First and Second-Level Objets?&quot; and &quot;How can you force an object to always appear as a first-level object?&quot; in this SAP Note.)</li>\r\n<li>Under &quot;General Restrictions&quot;, the following PRCTR assignment is required:</li>\r\n</ul>\r\n<ul>\r\n<li>Direct Assignment of PRCTR</li>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Old PRCTR</strong></td>\r\n<td><strong>New PRCTR</strong></td>\r\n<td><strong>Status is set automatically</strong></td>\r\n</tr>\r\n<tr>\r\n<td>PRCTR A</td>\r\n<td>PRCTR A</td>\r\n<td>Status &#39;Not being reorganized&#39; (60)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul dir=\"ltr\" style=\"margin-right: 0px;\">\r\n<ul>\r\n<li>Assignment with Choice of Future PRCTR<br />Only the object owner can make the selection in the &#39;Object List: Overview of Object Owners&#39;:</li>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Old PRCTR</strong></td>\r\n<td><strong>New PRCTR</strong></td>\r\n<td><strong>Choose PRCTR and set status</strong></td>\r\n</tr>\r\n<tr>\r\n<td>PRCTR A</td>\r\n<td>PRCTR A</td>\r\n<td>Select the status &quot;Not being reorganized&quot;.</td>\r\n</tr>\r\n<tr>\r\n<td>PRCTR A</td>\r\n<td>PRCTR B</td>\r\n<td>Set Requested PRCTR&#39; + &#39;Approved for Further Processing&#39;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p style=\"padding-left: 30px;\"><br />After the release by the object owner, the status of the object changes to &#39;Not being reorganized&#39; or &#39;Approved for further processing&#39;. Afterwards, the assignment can no longer be changed. If you want to make subsequent changes, contact your consultant for the profit center reorganization.</p>\r\n<ul>\r\n<li>You can exclude objects already during &#39;GENERATE&#39; by restrictions in the &#39;Specific Restrictions&#39;. The default settings for the restriction characteristics are defined in Customizing for each object type:<br />Financial Accounting (New)<br />-> General Ledger Accounting (New)<br />---> Reorganization<br />----> Basic Settings<br />-----> Restriction Characteristics<br />------> Specify Restriction Characteristics for Each Reorganization Object Type<br /><br /><strong>Example:</strong> <br />You only want to reorganize materials using certain plants and use the specific restrictions. At the same time, you want to allow SD orders (cross-company) for the same plants. Since both object types MAT and SOI are only first-level objects, both must be excluded. Possible configuration:<br /><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Obj. Type</td>\r\n<td>Table</td>\r\n<td>Field</td>\r\n<td>Included</td>\r\n<td>Option</td>\r\n<td>Selection</td>\r\n<td>Comment</td>\r\n</tr>\r\n<tr>\r\n<td>MAT</td>\r\n<td>MARC</td>\r\n<td>WERKS</td>\r\n<td>I</td>\r\n<td>EQ</td>\r\n<td>Plant A</td>\r\n<td rowspan=\"4\">Use the option NE (not equal to) and Included= &#39;I&#39; only for one plant. If you want to exclude other plants from n, the selection is not unique. Combinations of EQ (equal) and NE (not equal) also do not lead to the desired result. </td>\r\n</tr>\r\n<tr>\r\n<td>MAT</td>\r\n<td>MARC</td>\r\n<td>WERKS</td>\r\n<td>I</td>\r\n<td>EQ</td>\r\n<td>Plant B</td>\r\n</tr>\r\n<tr>\r\n<td>SOI</td>\r\n<td>VBAP</td>\r\n<td>WERKS</td>\r\n<td>I</td>\r\n<td>EQ</td>\r\n<td>Plant A</td>\r\n</tr>\r\n<tr>\r\n<td>SOI</td>\r\n<td>VBAP</td>\r\n<td>WERKS</td>\r\n<td>I</td>\r\n<td>EQ</td>\r\n<td>Plant B</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</li>\r\n</ul>\r\n<ul>\r\n<li><strong>Where is the data of the error log saved?</strong></li>\r\n<ul>\r\n<li>The messages are saved in job and application logs. Job logs can be identified using the reorganization plan if they still exist. Application logs are displayed for object &#39;FAGL&#39;, subobject &#39;REORG&#39; with transaction SLG1, the log is displayed for object &#39;FAGL&#39;.</li>\r\n<li>You can use the analysis report (SAP Note 2257740) to view the error log for each job.<br /><br /></li>\r\n</ul>\r\n<li><strong>You have already completed generation and reassignment for individual object types. Despite this, you receive a yellow traffic light. Why?<br /></strong>The object list displays a yellow traffic light as long as potential objects exist for reassignment. <br />For example, you have completely reassigned AP/AR. The attachments, sales orders, and purchase orders are not yet completed. By reassigning these objects, the system can find additional receivables and payables that need to be reassigned.<br /><br /></li>\r\n<li><strong>You use profit center substitution for sales and distribution documents in the standard system. <br />Therefore, your derivation logic does not fit into the standard logic of the report MAT -> SO.<br /></strong>Define the sales orders (SO) as first-level objects and remove them below the material. Use the BAdI FAGL_R_GENERATE within the enhancement spot FAGL_REORGANIZATION and call the substitution of the sales document yourself to determine the new profit center.<br />Contact your professor reorganization consultant if you have any questions. (see also SAP Note 2287346)<br /><br /></li>\r\n<li><strong>Why do clearing documents that are posted after a completed reorganization have different account assignments to the original document in the general ledger view?<br /></strong>The reorganization does not change the general ledger view of a document (table FAGLFLEXA). Only the document splitting table (FAGL_SPLINFO) is adjusted so that follow-on processes can take the new account assignment into account in the general ledger view. The account assignments in the entry view (table BSEG) remain unchanged for accounts managed on an open item basis during follow-on processes, that is, these fields are either initial or they contain the original account assignments.<br /><br /></li>\r\n<li><strong>You are a public sector customer and use the totals record table FMGLFLEXT in new General Ledger Accounting. As described in SAP Note 923687, the field ZZPRCTR is assigned as a category 2 field in the update of new General Ledger Accounting. Is it possible to execute a profit center reorganization for this table group?<br /></strong>Yes: In the source applications of the objects to be reorganized, such as in Logistics, Controlling, or in the entry view of General Ledger Accounting and in the table of document splitting (FAGL_SPLINFO), this is still the field PRCTR. The reorganization changes only at the level of the field PRCTR. If the reorganization generates adjustment postings, they clear them in the entry view PRCTR &#39;A&#39; to PRCTR &#39;B&#39;. With regard to the update in General Ledger Accounting, this is - due to the logic described in SAP Note 923687 - then automatically a transfer posting from ZZPRCTR &#39;A&#39; to ZZPRCTR &#39;B&#39;.<br /><br /></li>\r\n<li><strong>When can a plan be closed?<br /></strong>The reorganization plan can be closed for each object type in your hierarchy once all process steps have been processed completely.<br />In principle, it is also possible to close incomplete plans in test systems (see SAP Note 1765330). However, this procedure leads to inconsistencies in your system because the status in which the plan was closed cannot be determined afterwards. For this reason, we strongly recommend that you do not close incomplete reorganization plans in the production system. SAP does not provide support in the case of inconsistencies caused by the closure of incomplete reorganization plans.<br /><br /></li>\r\n<li><strong>When are objects on the second hierarchy level (level 2 objects) included in a reorganization plan? What are first and second level objects?<br /></strong>Starting from the hierarchy plan, all object types listed there are generated first. During generation, the system checks whether an object is considered as a first-level or second-level object. Objects on the first level are characterized by the fact that the profit center was entered directly in the object and was not derived and therefore there is no reference to another object type. Objects on the second level appear as lower-level objects after the higher-level object has been unsubscribed. SAP Note 2177720/2248773 provided the option of creating objects of the second level.<br /><br />You can see the hierarchy for the plan in Customizing under:<br />Financial Accounting (New)<br />-> General Ledger Accounting (New)<br />--> Reorganization<br />---> Basic Settings<br />----> Remove Object Types from Derivation Hierarchy<br /><br />Choose your plan type and hierarchy version. With &#39;&amp;SAPNID&#39; in the command line, the node numbers for the object are displayed. <br /><br /><br /><strong>An overview of all objects is only possible after the reassignment or the simulation of the reassignment.</strong><br />In the <strong>list display</strong>, the objects appear as a total of all objects of the first and all sublevels of the respective object type. In the <strong>hierarchy display</strong>, the objects appear below the object type from which the object derived the PRCTR.<br /><br />For example, you reorganize a material A with PRCTR A. You have used this material in purchase orders and SD orders as well as in the dependent invoices/payments.<br /><br />In the reassignment process, the dependent objects are generated and then appear in the hierarchy under the material.<br /><br />The following is a section of the standard derivation hierarchy (object list hierarchy display - expand material) (see also SAP Note 2115768)<br />(All objects marked with * have further subobjects):</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>* Material</td>\r\n<td> </td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td>* SD document</td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td> </td>\r\n<td>* Purchase order (account assigned)</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td> </td>\r\n<td> </td>\r\n<td>Liability</td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td> </td>\r\n<td>Receivable</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td> </td>\r\n<td>Liability</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td>*Purchase Order (Unassigned)</td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td> </td>\r\n<td>Liability</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>* Purchase order (without account assignment)</td>\r\n<td> </td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td>Liability</td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>* Purchase order (account assigned)</td>\r\n<td> </td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td>Liability</td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>* SD document</td>\r\n<td> </td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td>* Purchase order (account assigned)</td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td> </td>\r\n<td>Liability</td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td>Receivable</td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td> </td>\r\n<td>Liability</td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Receivable</td>\r\n<td> </td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n<tr>\r\n<td>Liability</td>\r\n<td> </td>\r\n<td> </td>\r\n<td> </td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p style=\"padding-left: 60px;\">Example (see SAP Note 2115768)</p>\r\n<ul>\r\n<li><strong>How can I force an object to always appear as a first-level object?</strong><br />Copy the standard hierarchy (derivation hierarchy) and adjust it to your requirements by deleting the lower hierarchy level.<br /><br />Note that reorganization plans that have already been created for<br />Use the valid derivation hierarchy at the time of creation.<br />If you use the derivation hierarchy changed by this SAP Note,<br />you have to create a new reorganization plan<br />(and, if necessary, delete the existing reorganization plan). See also SAP Notes 1761156 and 1693804)<br /><br /></li>\r\n<li><strong>What are the most important tables of the PRCTR reorganization?</strong></li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Information</td>\r\n<td>Table</td>\r\n<td>Description</td>\r\n</tr>\r\n<tr>\r\n<td>Plan</td>\r\n<td>FAGL_R_PL</td>\r\n<td>Reorganization Plan</td>\r\n</tr>\r\n<tr>\r\n<td>Plan</td>\r\n<td>FAGL_R_PL_RES_G</td>\r\n<td>General restrictions, for example: KOKRS, BUKRS.</td>\r\n</tr>\r\n<tr>\r\n<td>Plan</td>\r\n<td>FAGL_R_PL_RES_S</td>\r\n<td>Special restrictions, for example: AUFNR which fields are selected in Customizing</td>\r\n</tr>\r\n<tr>\r\n<td>Object</td>\r\n<td>FAGL_R_PL_OBJECT</td>\r\n<td>All Reorganization Objects (1st Level by GENERATED / 2nd Level with UMKONTIEREN)</td>\r\n</tr>\r\n<tr>\r\n<td>Object</td>\r\n<td>FAGL_R_APAR</td>\r\n<td>Object list for receivables and payables for segment reorganization additional GLOI</td>\r\n</tr>\r\n<tr>\r\n<td>Object</td>\r\n<td>FAGL_R_BLNCE</td>\r\n<td>Balances/Values to Be Transferred (FAGL_R_BLNCE_VAL)</td>\r\n</tr>\r\n<tr>\r\n<td>Plan</td>\r\n<td>FAGL_R_PL_DERH</td>\r\n<td>Reorganization: Derivation Hierarchy of Plan</td>\r\n</tr>\r\n<tr>\r\n<td>Plan</td>\r\n<td>FAGL_R_JBPARTN</td>\r\n<td>Reorganization: Packages to be Executed for a Dispatching Job</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ul>\r\n<li><strong>You notice that material-related postings with account assignment to Profitability Analysis or pure FI postings do not follow the PRCTR reorganization logic (old PRCT in the old period, new PRCTR in the new period). This can e.g. B. Goods issue and billing document for the sales order or price differences for purchase orders.</strong></li>\r\n<ul>\r\n<li>Even though the material and the sales document are contained in the reorganization, they are not taken into account for the corresponding postings. This is because the profitability segment or a pure FI posting (without CO account assignment) is not part of the reorganization. If you want these objects to be handled correctly, you can contact SAP Consulting.</li>\r\n<li>SAP Consulting provides a workaround that can handle these postings correctly using FI substitution. Please note that this consulting service is a paid consulting service.<br /><br /></li>\r\n</ul>\r\n<li><strong>You also want to use customer-specific fields (for example: ZZHUGO) or change the behavior of the derivation hierarchy.</strong> </li>\r\n<ul>\r\n<li>The reorganization framework allows you to develop your own object types. For more information, see the documentation for the BAdI FAGL_R_OBJ_TYPE_BADI in the enhancement spot FAGL_REORGANIZATION. There you can define your own object types or adjust the behavior of existing object types (also within the derivation hierarchy) without modification. Test your development very carefully.</li>\r\n<li>Contact SAP Consulting. SAP Consulting provides a customer-specific solution. Please note that this consulting service is a paid consulting service.<br /><br /></li>\r\n</ul>\r\n<li><strong>Revenue Recognition: You are using SD Revenue Recognition. This is not supported in the reorganization (SAP Note 1801767 - Profit center reorganization: Features) <br /></strong>Contact SAP Consulting. SAP Consulting provides a customer-specific solution that can be used to reorganize revenue recognition. Please note that this consulting service is a paid consulting service.<br /><br /></li>\r\n<li><strong>Special stocks: You derive the profit center for special stocks from the sales document or project. <br /></strong>Implement SAP Note 1955532 - PRCTR: Material stock determination for cost element category 90.</li>\r\n</ul>\r\n<p> </p>\r\n<p><strong>Questions about special object types</strong><br /><br /><strong>Cost Center:</strong></p>\r\n<ul>\r\n<li><strong>You want to change the assignment of a cost center to a profit center in master data maintenance. The system responds with error message FAGL_REORGANIZATION 601.<br /><br /></strong>As soon as the business function FIN_GL_REORG_1 has been activated in your system and the reorganization plan type &#39;001&#39; is active, you can only change the profit center of the cost center using an active reorganization plan. For further cases, check the following SAP Notes:<br /><br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"1\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>2005313</td>\r\n<td>PRCTR: Message Fagl_reorganization 601 (maintenance in central system)</td>\r\n</tr>\r\n<tr>\r\n<td>1900735</td>\r\n<td>1900735 - Reorganization: Profit center in cost center cannot be changed (III)</td>\r\n</tr>\r\n<tr>\r\n<td>1897608</td>\r\n<td>KS12: Message FAGL_REORGANIZATION 601 is unjustified</td>\r\n</tr>\r\n<tr>\r\n<td>1881515</td>\r\n<td>Reorganization: Profit center in cost center cannot be changed (II)</td>\r\n</tr>\r\n<tr>\r\n<td>1684679</td>\r\n<td>Reorganization: Profit center in cost center cannot be changed</td>\r\n</tr>\r\n<tr>\r\n<td>1574285</td>\r\n<td>PRCTR: Cost center not removed from reorganization plan</td>\r\n</tr>\r\n<tr>\r\n<td>1482830</td>\r\n<td>PRCTR: Cost center change due to Reorg. message not possible</td>\r\n</tr>\r\n<tr>\r\n<td>1358080</td>\r\n<td>KS02:  Profit center change is not allowed in General Ledger Accounting (new)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</li>\r\n</ul>\r\n<p> </p>\r\n<ul>\r\n<li><strong>Can &#39;Specific Restrictions&#39; also be set for cost centers?</strong></li>\r\n<ul>\r\n<li>The reorganization of cost centers differs in its behavior from the other object types. They are included in the current reorganization plan directly using transaction KS02.<br /><br />For more explanations, see SAP Help Portal at:<br /><br />Profit Center Reorganization<br />..Specific Functions for Reorganizable Objects<br />....Reorganization of Cost Centers<br /><br /></li>\r\n</ul>\r\n<li><strong>How is the cost center reassigned/reposted?<br /><br /></strong>There is no reassignment or posting for the cost center.</li>\r\n<ul>\r\n<li>Example: A reorganization plan was created with the reorganization date 01. Created August:</li>\r\n<ul>\r\n<li>Old PRCTR &#39;A&#39; was assigned to new PRCTR &#39;B&#39; in the reorganization plan.</li>\r\n<li>A new analysis period was created for cost center as of reorganization date 01. August with new PRCTR &#39;B&#39; created (TA: KS02 or KS12). </li>\r\n<ul>\r\n<li>This assignment can be made as long as no postings have been made to the cost center on the old PRCTR in the new analysis period.<br />(The system checks this and issues message FAGL_REORGANIZATION 603 if an error occurs.) <br />Ideally, the analysis period is changed before the reorganization date.</li>\r\n</ul>\r\n<li>The current CO period can be opened on the reorganization date after the cost center has been maintained. (TA OKP1)</li>\r\n<li>Depending on the posting date, you post before the reorganization date with the old PRCTR (old analysis period of the cost center) and as of the reorganization date with the new PRCTR (new analysis period of the cost center).</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p><strong>Debugging</strong></p>\r\n<ul>\r\n<li>Follow the instructions from SAP Note 2009252 - PRCTR: Checkpoint group for dialog indicator</li>\r\n<li>Alternatively, set an external breakpoint in the method CL_FAGL_R_PLAN: IS_XDIALOG_P and set the parameter  rv_xdialog = &#39;X&#39;.<br />This procedure can only be used if the customer has allowed debugging authorization and changes in the debugger.</li>\r\n</ul>\r\n<p><br /><strong><br /><br />Legend:</strong> <br />Release status: KF - customer release, PI - pilot (this release status is fixed)</p>\r\n<p><strong>News in the area of receivables and payables</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Release</strong></td>\r\n<td><strong>Topic</strong></td>\r\n<td><strong>Note</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Q1/16</td>\r\n<td>PI</td>\r\n<td>Additional FAQ</td>\r\n<td>2270249 - PRCTR/SEG: FAQ: &quot;artificial level 1&quot; objects for payables or receivables</td>\r\n</tr>\r\n<tr>\r\n<td>Q1/16</td>\r\n<td>PI</td>\r\n<td>\r\n<p>Specific Restrictions</p>\r\n</td>\r\n<td>2273175 - PRCTR/SEG: Incorrect exclusion of receivables and payables on 1st Hierarchy Level II</td>\r\n</tr>\r\n<tr>\r\n<td>Q1/16</td>\r\n<td>KF</td>\r\n<td>New fields in the table FAGL_R_SPL</td>\r\n<td>\r\n<p>2265202 - PRCTR/SEG: FAGL_REORGANIZATION 566 (XXIX)<br />2200318 - PRCTR/SEG: FAGL_REORGANIZATION 566 (XXVI)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Q1/16</td>\r\n<td>KF</td>\r\n<td>\r\n<p>Short dump during document splitting</p>\r\n</td>\r\n<td>2255494 - CONVT_OVERFLOW in INCLUDE LGLT0F61</td>\r\n</tr>\r\n<tr>\r\n<td>Q1/16</td>\r\n<td>KF</td>\r\n<td>Too many entries in FAGL_R_OI_TRACK1</td>\r\n<td>2268500 - PRCTR/SEG: Open items are collected for further processing despite closed reorganization plan</td>\r\n</tr>\r\n<tr>\r\n<td>Q1/16</td>\r\n<td>PI</td>\r\n<td>Reset Generation AP/AR/GLOI</td>\r\n<td>2264203 - PRCTR/SEG: Utility report for resetting a document after note implementation</td>\r\n</tr>\r\n<tr>\r\n<td>Q4/15</td>\r\n<td>KF</td>\r\n<td>Foreign Currency Valuation</td>\r\n<td>2211157 - PRCTR/SEG: Incorrect amounts after reorganization transfer posting of foreign currency valuation</td>\r\n</tr>\r\n<tr>\r\n<td>Q4/15</td>\r\n<td>KF</td>\r\n<td>Local Currencies</td>\r\n<td>2228806 - PRCTR/SEG: FAGL_REORGANIZATION 566 (XXVII) - local currencies</td>\r\n</tr>\r\n<tr>\r\n<td>Q4/15</td>\r\n<td>KF</td>\r\n<td>During the reassignment of AR/AP, the system issues errors <br />Recognized in document splitting, but reassignment still takes place</td>\r\n<td>2219599 - PRCTR/SEG: FAGL_REORGANIZATION 545<br />2227550 - PRCTR/SEG: FAGL_REORGANIZATION 552</td>\r\n</tr>\r\n<tr>\r\n<td>Q4/15</td>\r\n<td>KF</td>\r\n<td>Evaluation of Job Log</td>\r\n<td>2176373 - PRCTR/SEG: Support for AP/AR runtime analysis</td>\r\n</tr>\r\n<tr>\r\n<td>Q4/15</td>\r\n<td>KF</td>\r\n<td>Cash Discount Clearing Line</td>\r\n<td>2236750 - PRCTR/SEG: Incorrect calculation of cash discount clearing line</td>\r\n</tr>\r\n<tr>\r\n<td>Q2/15</td>\r\n<td>KF</td>\r\n<td>Reassignment Simulation</td>\r\n<td>2177720 - Reassignment simulation<br />2248773 - 2248773 - PRCTR/SEG: Simulation of reassignment AP/AR/GLOI objects</td>\r\n</tr>\r\n<tr>\r\n<td>Q2/15</td>\r\n<td>KF</td>\r\n<td>TA: FAGLL03 Open Items - Handling</td>\r\n<td>2207357 - PRCTR/SEG: Display of customer/vendor reconciliation accounts as &quot;open&quot; after transfer posting</td>\r\n</tr>\r\n<tr>\r\n<td>Q1/15</td>\r\n<td>KF</td>\r\n<td>Parallel Processing of Generation</td>\r\n<td>2125431 - Generation: Parallel processing per company code, number of parallel jobs<br />2276845 - PRCTR/SEG: Generation: Parallelization per Company Code AP/AR/GLOI</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>News in the segment reorganization area</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Date</strong></td>\r\n<td><strong>Release</strong></td>\r\n<td><strong>Topic</strong></td>\r\n<td><strong>Note</strong></td>\r\n</tr>\r\n<tr>\r\n<td>Q1/16</td>\r\n<td>KF</td>\r\n<td>Items with Clearing Specific to Ledger Groups</td>\r\n<td>\r\n<p>2262311 SEG - Transfer posting document for items with clearing specific to ledger groups is not cleared<br />2262612 SEG - Transfer posting document for items with clearing specific to ledger groups is posted to a ledger group<br />2251240 SEG - Items with clearing specific to ledger groups not reorganized as object GLOI<br />2271699 SEG - Items with clearing specific to ledger groups are not stored in delta table<br />2256298 SEG - Accounts with clearing specific to ledger groups are incorrectly reorganized as G/L object (pilot - correction report)</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-LT-CNV (Landscape Transformation: Conversion)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON><PERSON> (D035526)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON><PERSON> (D021290)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1810605", "RefComponent": "FI-GL-REO", "RefTitle": "General Notes on Profit Center Reorganization", "RefUrl": "/notes/1810605"}, {"RefNumber": "1534197", "RefComponent": "CA-LT-CNV", "RefTitle": "SAP LT: Transformation Solution - ProfitCenter Reorganization", "RefUrl": "/notes/1534197"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1810605", "RefComponent": "FI-GL-REO", "RefTitle": "General Notes on Profit Center Reorganization", "RefUrl": "/notes/1810605 "}, {"RefNumber": "1534197", "RefComponent": "CA-LT-CNV", "RefTitle": "SAP LT: Transformation Solution - ProfitCenter Reorganization", "RefUrl": "/notes/1534197 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1887685&TargetLanguage=EN&Component=FI-GL-REO&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1887685/D\" target=\"_blank\">/notes/1887685/D</a>."}}}}