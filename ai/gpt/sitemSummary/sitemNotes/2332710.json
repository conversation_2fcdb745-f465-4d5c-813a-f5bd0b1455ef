{"Request": {"Number": "2332710", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 210, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018351742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002332710?language=E&token=C07E8063ABFAD1AEBB97FFB78D4C7986"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002332710", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002332710/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2332710"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.05.2021"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-RFQ"}, "SAPComponentKeyText": {"_label": "Component", "value": "RFQ/Quotation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "RFQ/Quotation", "value": "MM-PUR-RFQ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-RFQ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2332710 - S4TWL-RFQ Simplified Transaction"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion from SAP&#160;ERP&#160;to SAP&#160;S/4HANA&#160;or an upgrade from a lower to a higher SAP&#160;S/4HANA&#160;release and are using the functionality described in this note. The following SAP&#160;S/4HANA&#160;Simplification Item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP&#160;S/4HANA,System Conversion, Upgrade, Request for Quotation, Supplier Quotation</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The previous concept was based on requests for quotations (RFQs) that included a mandatory supplier field. In addition, the number of suppliers that could be entered for each RFQ was limited to one. This restriction doesn&#8217;t fit to multiple sourcing-related integration scenarios, such as Sourcing with SAP Ariba Sourcing.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>New concept enables the system to send RFQs, that were created in a Fiori app, to external sourcing platforms without mentioning exact supplier. On the sourcing platform, the registered suppliers can submit their quotations which are later available to the purchaser.</p>\r\n<p>Following IDOCS and transactions related to RFQ are not considered as the target architecture. This means that the functionality is currently available in SAP S/4HANA but it is not considered as future technology and a functional equivalent is not available.</p>\r\n<p>These IDocs are replaced with&#160;cXML messages:</p>\r\n<ul>\r\n<li>REQOTE (Basic IDoc type = ORDERS05) is the IDoc for RFQ</li>\r\n<li>QUOTES (Basic IDoc type = ORDERS05) is the IDoc for supplier quotation</li>\r\n</ul>\r\n<p>It is planned to introduce SOAP-based messages to further replace cXML messages for RFQ and supplier quotation.<span style=\"font-size: 16px; font-family: Arial, sans-serif;\">&#160;</span></p>\r\n<p>The following transactions are deprecated in SAP S/4HANA:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 616px; height: 272px;\">\r\n<tbody>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME41</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>Create</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME42</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>Change</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME43</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>Display</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME44</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>Maintain Supplement</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME45</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>Release</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME47</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>Maintain</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME48</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>Display</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME49</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>Price Comparison List</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME4B</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>RFQs by Requirement Tracking Number</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME4C</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>RFQs by Material Group</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME4L</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>RFQs by Vendor</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME4M</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>RFQs by Material</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME4N</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>RFQs by RFQ Number</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\r\n<p>ME4S</p>\r\n</td>\r\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\r\n<p>RFQs per Collective Number</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Business Process Related Information</strong></p>\r\n<p>If you want to use functionality related to sourcing of goods and services, you can use the following apps:</p>\r\n<ul>\r\n<li>Manage RFQs (F2049)</li>\r\n<li>Monitor RFQ Items (F2425)</li>\r\n<li>Request for Quotation Types (F4149)</li>\r\n<li>Manage Supplier Quotations (F1991)</li>\r\n<li>Compare Supplier Quotations (F2324)</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">Before you start to use Fiori apps mentioned above you need to close your open RFQs, that&#160;were created via old transactions, and set them to status \"Completed\".</span></p>\r\n<p><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; color: black; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><strong><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; color: black; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\">Required and Recommended Action(s)</span></strong></span></span></span></span></span></span></p>\r\n<p><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; color: black; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 12pt; font-family: 'Arial',sans-serif; color: black; line-height: 107%; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-GB; mso-fareast-language: DE; mso-bidi-language: AR-SA;\"><span lang=\"EN-GB\" style=\"font-size: 11pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: Calibri; mso-ansi-language: EN-GB; mso-fareast-language: EN-US; mso-fareast-theme-font: minor-latin; mso-bidi-language: AR-SA;\">You have to maintain the user roles.</span></span></span></span></span></span></span></span>&#160;The new Fiori apps are accompanied by the standard roles that contain relevant authorization objects. The relevant business roles are:</p>\r\n<ul>\r\n<li>SAP_BR_PURCHASER (Purchaser) for the apps:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Manage RFQs (F2049)</li>\r\n<li>Monitor RFQ Items (F2425)</li>\r\n<li>Manage Supplier Quotations (F1991)</li>\r\n<li>Compare Supplier Quotations (F2324)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>SAP_BR_BUYER (Strategic Buyer) for the app:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Request for Quotation Types (F4149)</li>\r\n</ul>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-FIO-PUR-RFQ (Fiori UI for RFQ/Quotation)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023163)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I063779)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002332710/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002332710/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002332710/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002332710/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002332710/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002332710/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002332710/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002332710/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002332710/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}