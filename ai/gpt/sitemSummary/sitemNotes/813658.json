{"Request": {"Number": "813658", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 286, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015839932017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000813658?language=E&token=3F54D1FEF8758EED77FA250800B1CAA1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000813658", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000813658/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "813658"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.04.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "813658 - Repairs for upgrades to products based on SAP NW 2004s AS"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to implement corrections for upgrade programs or upgrade control files.<br />This note is valid for all products based on SAP Netweaver Application Server 7.0.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>700, upgrade, shadow system, shadow instance, SHD_FIX_IMP, TOOLIMP4_FIX, TOOLIMP6_FIX, TOOLFIX_CHK, IS_MOVES.LST<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note describes how to add the repairs to the upgrade procedure.<br />Applying this note prevents the occurrence of various errors, upgrade shutdowns, loss of data and long upgrade runtimes.<br /><B>It is essential that you apply this note because, if you do not do so, you risk losing data, data inconsistencies being created and the upgrade shutting down.</B><br />Also refer to note <B>821032</B> <B>for</B> the the correct SAPup version (ABAP only).<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The upgrade correction is provided as SAR archive that you must integrate into the upgrade. Each SAR archive is valid for only one product (= exactly one delivered CD set).<br /><br />The upgrade control program displays the correct name of the fix archive you have to apply.<br /><br />For products based on SAP Netweaver 7.00 the SAR archives are available at the following SAP Service Marketplace area:<br />http://service.sap.com/patches -&gt; Entry by Application Group -&gt; Additional Components -&gt; Upgrade Tools -&gt; Corrections for Upgrade -&gt; CORRECTIONS FOR UPGRADE 7.00 -&gt; #OS independent<br />or directly at<br />http://www.service.sap.com/~form/handler?_APP=00200682500000001943&amp; _EVENT=DISPHIER&amp;HEADER=N&amp;FUNCTIONBAR=N&amp;EVENT=TREE&amp;TMPL=01200314690200004805&amp;V=MAINT<br /><br />For products based on SAP Netweaver 7.01 the SAR archives are available at the following SAP Service Marketplace area:<br />http://service.sap.com/patches -&gt; Entry by Application Group -&gt; Additional Components -&gt; Upgrade Tools -&gt; Corrections for Upgrade -&gt; CORRECTIONS FOR UPGRADE 7.01 -&gt; #OS independent .<br /><br />For products based on SAP Netweaver 7.02 the SAR archives are available at the following SAP Service Marketplace area:<br />http://service.sap.com/patches -&gt; Entry by Application Group -&gt; Additional Components -&gt; Upgrade Tools -&gt; Corrections for Upgrade -&gt; CORRECTIONS FOR UPGRADE 7.02 -&gt; #OS independent .<br /><br /><br /></p> <b>Selecting the correction package</b><br /> <p>The following table tells you which package you need</p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Product</TH><TH ALIGN=LEFT> ABAP package name</TH><TH ALIGN=LEFT> Java package name</TH></TR> <TR><TD>NW AS 7.0 </TD><TD> FIX_NWAS700.UPG</TD><TD> FIX_J_NW04S.UPG</TD></TR> <TR><TD>ERP 2005 </TD><TD> FIX_ERP2005.UPG</TD><TD> FIX_J_BS05.UPG</TD></TR> <TR><TD>SRM 5.0 </TD><TD> FIX_SRM50.UPG</TD><TD> FIX_J_BS05.UPG</TD></TR> <TR><TD>(SRM Server 5.5)</TD></TR> <TR><TD>CRM 5.0</TD><TD> FIX_CRM50.UPG</TD><TD> FIX_J_BS05.UPG</TD></TR> <TR><TD>SCM 5.0</TD><TD> FIX_SCM50.UPG</TD><TD> FIX_J_BS05.UPG</TD></TR> <TR><TD></TD></TR> <TR><TD>NW AS 7.0 SR1</TD><TD> FIX_NWAS700SR1.UPG</TD><TD> FIX_J_NW04SSR1.UPG</TD></TR> <TR><TD>ERP 2005 SR1</TD><TD> FIX_ERP2005SR1.UPG</TD><TD> </TD></TR> <TR><TD>SRM 5.0 SR1</TD><TD> FIX_SRM50SR1.UPG</TD><TD> </TD></TR> <TR><TD>(SRM Server 5.5)</TD></TR> <TR><TD>CRM 5.0 SR1</TD><TD> FIX_CRM50SR1.UPG</TD><TD> </TD></TR> <TR><TD>SCM 5.0 SR1</TD><TD> FIX_SCM50SR1.UPG</TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD>NW AS 7.0 SR2</TD><TD> FIX_NWAS700SR2.UPG</TD><TD> FIX_J_NW04SSR2.UPG</TD></TR> <TR><TD>ERP 2005 SR2</TD><TD> FIX_ERP2005SR2.UPG</TD><TD> </TD></TR> <TR><TD>SRM 5.0 SR2</TD><TD> FIX_SRM50SR2.UPG</TD><TD> </TD></TR> <TR><TD>(SRM Server 5.5)</TD></TR> <TR><TD>CRM 5.0 SR2</TD><TD> FIX_CRM50SR2.UPG</TD><TD> </TD></TR> <TR><TD>SCM 5.0 SR2</TD><TD> FIX_SCM50SR2.UPG</TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD>SRM 6.0</TD><TD> FIX_SRM60.UPG</TD><TD> FIX_J_SRM60.UPG</TD></TR> <TR><TD>CRM 6.0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIX_CRM60.UPG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIX_J_CRM60.UPG</TD></TR> <TR><TD></TD></TR> <TR><TD>NW AS 7.0 SR3</TD><TD> FIX_NWAS700SR3.UPG</TD><TD> FIX_J_NW04SSR3.UPG</TD></TR> <TR><TD>ERP 2005 SR3</TD><TD> FIX_ERP2005SR3.UPG</TD><TD> </TD></TR> <TR><TD>SRM 5.0 SR3</TD><TD> FIX_SRM50SR3.UPG</TD><TD> </TD></TR> <TR><TD>(SRM Server 5.5)</TD></TR> <TR><TD>CRM 5.0 SR3</TD><TD> FIX_CRM50SR3.UPG</TD></TR> <TR><TD>SCM 5.0 SR3</TD><TD> FIX_SCM50SR3.UPG</TD></TR> <TR><TD></TD></TR> <TR><TD>NW AS 7.0 EHP1</TD><TD> FIX_NW701.UPG</TD><TD> FIX_J_NW04SEHP1.UPG</TD></TR> <TR><TD></TD></TR> <TR><TD>NW AS 7.0 EHP2</TD><TD> FIX_NW702.UPG</TD><TD> FIX_J_BS7I2010.UPG</TD></TR> <TR><TD></TD></TR> </TABLE> <b>Comments:</b><br /> <UL><LI>The archives on the service market place have a slightly different naming convention, i. e. the leading \"FIX_\" is stripped and the extension is \".SAR\".</LI></UL> <UL><LI>For all products there exists exactly one correction package.</LI></UL> <UL><LI>Some products are known under different names and can be used differently. For example, NW-AS 7.00 which is part of Netweaver 04s is the successor product to WEB-AS as well as to the BW product family. However, there is only <B>one</B> fix archive regardless on how you use the system.</LI></UL> <UL><LI>Similarly, other products are based on Netweaver, but there are separate fix archives for each of them. In essence, only <B>one</B> archive must be used.<br /></LI></UL> <b>Integration into the upgrade</b><br /> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Download the correction package for your product.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Store the SAR archive in the main upgrade directory.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Unpack the SAR archive using \"SAPCAR -xvf &lt;name&gt;. SAR\" You will get two files, one of them is &lt;Correction package name&gt;.UPG, the other one contains a history of changes.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During the KEY_CHK phase, SAPup or SAPJup searches the upgrade directory for the correct correction package. If SAPup/SAPJup is successful, the package is automatically integrated into the upgrade. If SAPup/SAPJup does not find a valid package, the system displays a user dialog requesting that you place the correction package in the upgrade directory. You can use \"Retry\" to repeat the search<br /><br />The UPG archive contains data files and cofiles for the transport requests and the corresponding buffer files (ABAP upgrade only), and control files of the upgrade.<br /><br />The corrections to the ABAP tools of the source release are imported in the TOOLIMP4_FIX, TOOLIMP6_FIX phases (\"Extension\" PREPARE module) (provided these exist), while the corrections for the shadow system are imported in the SHD_FIX_IMP phase (upgrade). The transport requests <B>are</B> imported <B>automatically</B>. <B>You must never manually import requests into the system!</B><br /><br />The corrections to the J2EE upgrade program and control files are included automatically and the program is restarted to make the changes become active.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D038245)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D038245)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000813658/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813658/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813658/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813658/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813658/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813658/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813658/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813658/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000813658/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "FIX_WEBAS640SR1.SAR", "FileSize": "134", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000086782005&iv_version=0014&iv_guid=2481823D2B09224A99971F8F4996962E"}, {"FileName": "FIX_WEBAS640.SAR", "FileSize": "205", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000086782005&iv_version=0014&iv_guid=7056186CE65D67449E97FF9B462AC16F"}, {"FileName": "FIX_SCM410.SAR", "FileSize": "241", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000086782005&iv_version=0014&iv_guid=1153158A7F674E4EAD26FBA93A7381BB"}, {"FileName": "FIX_ECC500SR1.SAR", "FileSize": "114", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000086782005&iv_version=0014&iv_guid=5A5F358BCC64754795B3D4D8DE899F03"}, {"FileName": "FIX_SRM40SR1.SAR", "FileSize": "260", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000086782005&iv_version=0014&iv_guid=A9847202A37DC746ADCC3C798043E250"}, {"FileName": "FIX_ECC500.SAR", "FileSize": "247", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000086782005&iv_version=0014&iv_guid=FB2FDFC0CC1E464296A2E5A42E63666B"}, {"FileName": "FIX_SRM40.SAR", "FileSize": "413", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000086782005&iv_version=0014&iv_guid=DA29558CDACF1947A95DEA4D0126B49C"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "984153", "RefComponent": "BC-DWB-DIC", "RefTitle": "Short dumps with SYSTEM_TYPE_NOT_ELEMENTARY", "RefUrl": "/notes/984153"}, {"RefNumber": "981804", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SRM LAC 6.0", "RefUrl": "/notes/981804"}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861"}, {"RefNumber": "970039", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NW 7.0 SR2 Java", "RefUrl": "/notes/970039"}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513"}, {"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "961511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961511"}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783"}, {"RefNumber": "947991", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947991"}, {"RefNumber": "925240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925240"}, {"RefNumber": "922454", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/922454"}, {"RefNumber": "918921", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/918921"}, {"RefNumber": "918850", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/918850"}, {"RefNumber": "918849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/918849"}, {"RefNumber": "913972", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913972"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "913849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913849"}, {"RefNumber": "913848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913848"}, {"RefNumber": "905029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905029"}, {"RefNumber": "890202", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/890202"}, {"RefNumber": "885955", "RefComponent": "BC-UPG", "RefTitle": "Language content in DDIC import not imported (Upg 640/700)", "RefUrl": "/notes/885955"}, {"RefNumber": "867193", "RefComponent": "BC-I18-UNI", "RefTitle": "ABAP and kernel patches for CU&UC in 46C", "RefUrl": "/notes/867193"}, {"RefNumber": "836198", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/836198"}, {"RefNumber": "826488", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826488"}, {"RefNumber": "826487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826487"}, {"RefNumber": "826093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826093"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "825146", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.0 on SQL Server", "RefUrl": "/notes/825146"}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322"}, {"RefNumber": "815202", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions upgrade to SAP NW AS 7.0", "RefUrl": "/notes/815202"}, {"RefNumber": "716377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Problem analysis in the upgrade: SPACECHK phases", "RefUrl": "/notes/716377"}, {"RefNumber": "1502824", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NW 7.0 EHP3 Java", "RefUrl": "/notes/1502824"}, {"RefNumber": "1300205", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM LAC 7.0 SR1", "RefUrl": "/notes/1300205"}, {"RefNumber": "1297804", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 Java Components", "RefUrl": "/notes/1297804"}, {"RefNumber": "1297803", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 Java Components", "RefUrl": "/notes/1297803"}, {"RefNumber": "1297742", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 Java Comp.", "RefUrl": "/notes/1297742"}, {"RefNumber": "1283141", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NetWeaver 7.0 EHP1 SR1 Java", "RefUrl": "/notes/1283141"}, {"RefNumber": "1267573", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NW 7.0 EHP2 Java", "RefUrl": "/notes/1267573"}, {"RefNumber": "1157238", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 Java Components", "RefUrl": "/notes/1157238"}, {"RefNumber": "1157237", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 Java Components", "RefUrl": "/notes/1157237"}, {"RefNumber": "1157236", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 Java Comp.", "RefUrl": "/notes/1157236"}, {"RefNumber": "1156843", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM LAC 7.0", "RefUrl": "/notes/1156843"}, {"RefNumber": "1131483", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 5.0 SR3 Java Components", "RefUrl": "/notes/1131483"}, {"RefNumber": "1130151", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM LAC 5.0 SR3", "RefUrl": "/notes/1130151"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1108700", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108700"}, {"RefNumber": "1108510", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR3 ABAP", "RefUrl": "/notes/1108510"}, {"RefNumber": "1102065", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.0 SR3 Java", "RefUrl": "/notes/1102065"}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}, {"RefNumber": "1072161", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 2007 Java", "RefUrl": "/notes/1072161"}, {"RefNumber": "1071404", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1071404"}, {"RefNumber": "1040363", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 5.1 Java", "RefUrl": "/notes/1040363"}, {"RefNumber": "1039395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1039395"}, {"RefNumber": "1010762", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1010762"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "825146", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.0 on SQL Server", "RefUrl": "/notes/825146 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841 "}, {"RefNumber": "1267573", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NW 7.0 EHP2 Java", "RefUrl": "/notes/1267573 "}, {"RefNumber": "1283141", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NetWeaver 7.0 EHP1 SR1 Java", "RefUrl": "/notes/1283141 "}, {"RefNumber": "970039", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NW 7.0 SR2 Java", "RefUrl": "/notes/970039 "}, {"RefNumber": "1297742", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 Java Comp.", "RefUrl": "/notes/1297742 "}, {"RefNumber": "1072161", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 2007 Java", "RefUrl": "/notes/1072161 "}, {"RefNumber": "1040363", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 5.1 Java", "RefUrl": "/notes/1040363 "}, {"RefNumber": "1297804", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 Java Components", "RefUrl": "/notes/1297804 "}, {"RefNumber": "1157238", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 Java Components", "RefUrl": "/notes/1157238 "}, {"RefNumber": "1102065", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP NetWeaver 7.0 SR3 Java", "RefUrl": "/notes/1102065 "}, {"RefNumber": "1157237", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 Java Components", "RefUrl": "/notes/1157237 "}, {"RefNumber": "1157236", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 Java Comp.", "RefUrl": "/notes/1157236 "}, {"RefNumber": "1297803", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 Java Components", "RefUrl": "/notes/1297803 "}, {"RefNumber": "1131483", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 5.0 SR3 Java Components", "RefUrl": "/notes/1131483 "}, {"RefNumber": "815202", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions upgrade to SAP NW AS 7.0", "RefUrl": "/notes/815202 "}, {"RefNumber": "1502824", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NW 7.0 EHP3 Java", "RefUrl": "/notes/1502824 "}, {"RefNumber": "867193", "RefComponent": "BC-I18-UNI", "RefTitle": "ABAP and kernel patches for CU&UC in 46C", "RefUrl": "/notes/867193 "}, {"RefNumber": "716377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Problem analysis in the upgrade: SPACECHK phases", "RefUrl": "/notes/716377 "}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783 "}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "664712", "RefComponent": "XX-PROJ-CS-BC", "RefTitle": "Migration Cable Solution V46C.1A -> DIMP / ECC-DIMP", "RefUrl": "/notes/664712 "}, {"RefNumber": "1300205", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM LAC 7.0 SR1", "RefUrl": "/notes/1300205 "}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513 "}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861 "}, {"RefNumber": "981804", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SRM LAC 6.0", "RefUrl": "/notes/981804 "}, {"RefNumber": "1108510", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR3 ABAP", "RefUrl": "/notes/1108510 "}, {"RefNumber": "1130151", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM LAC 5.0 SR3", "RefUrl": "/notes/1130151 "}, {"RefNumber": "1156843", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM LAC 7.0", "RefUrl": "/notes/1156843 "}, {"RefNumber": "984153", "RefComponent": "BC-DWB-DIC", "RefTitle": "Short dumps with SYSTEM_TYPE_NOT_ELEMENTARY", "RefUrl": "/notes/984153 "}, {"RefNumber": "885955", "RefComponent": "BC-UPG", "RefTitle": "Language content in DDIC import not imported (Upg 640/700)", "RefUrl": "/notes/885955 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}