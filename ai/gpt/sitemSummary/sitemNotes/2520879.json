{"Request": {"Number": "2520879", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 298, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019605932017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=FB4FDF0A152F7E8DED5152D62857E8A2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2520879"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05/09/2018"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-FSCM-TRM-TM-TF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Trade Finance"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Financial Supply Chain Management", "value": "FIN-FSCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Treasury and Risk Management", "value": "FIN-FSCM-TRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transaction Manager", "value": "FIN-FSCM-TRM-TM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM-TM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Trade Finance", "value": "FIN-FSCM-TRM-TM-TF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM-TM-TF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2520879 - Migration of Financial Documents from SD-FT to Trade Finance in Treasury and Risk Management"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The SAP Note contains summary information for migrating financial documents from SD-FT to Trade Finance in&#160;Treasury and Risk Management as of SAP S/4HANA 1709.</p>\r\n<p>(1) Preparations for Migration</p>\r\n<p>It is highly recommendated that you check and correct the letters of credit information before you start the system conversion to SAP S/4HANA, as the financial document related transactions are disabled in the destination system landscapes.</p>\r\n<p>(2) Migration Steps</p>\r\n<p>Please follow the migration steps according to the sequence specified below:</p>\r\n<ul>\r\n<li><strong>Preparations and Migration of Customizing</strong></li>\r\n</ul>\r\n<p>You prepare and check the migration customizing. It is highly recommendated that you make the customizing&#160;with the specified sequence under IMG node&#160;<em><strong>Preparations for Migration of Financial Documents to Trade Finance</strong></em>(IMG path: SAP Customizing Implementation Guide -&gt; Conversion of Accounting to SAP S/4HANA -&gt; Preparations and Migration of Customizing -&gt; Preparations for Migration of Financial Documents to Trade Finance). Please be aware that the letters of credit creation in Trade Finance&#160;will be&#160;blocked&#160;during the migration.</p>\r\n<ul>\r\n<li><strong>Data Migration</strong></li>\r\n</ul>\r\n<p>You can start to migrate data once the preparations are completed correctly. It is also highly recommendated that you start the migration by using the Customizing activities in the specified sequence under IMG node <em><strong>Migration of Financial Documents to Trade Finance&#160;</strong></em>(IMG path: SAP Customizing Implementation Guide -&gt; Conversion of Accounting to SAP S/4HANA -&gt;&#160;Data Migration -&gt; Migration of Financial Documents to Trade Finance).</p>\r\n<p>Please be aware that the following major data is to be migrated into Trade Finance, and for each of the data migration steps, you can check the migration status.</p>\r\n<ol>\r\n<li>Master Data of Letter of Credit&#160;</li>\r\n<li>Assignments of SD documents to Letter of Credit</li>\r\n<li>Risk Check Decisions for the blocked sales and delivery documents by the letter of credit compliance check</li>\r\n</ol>\r\n<p>Please note that before you can&#160;migrate the SD document assignments or initialize the risk check decisions, you must have migrated the master data of letters of credit successfully.</p>\r\n<p>Once the major data are migrated and you have finished all activities required for a complete migration, you set migration to completed in the Customizing&#160;<em><strong>Complete Migration of Financial Documents to Trade Finance&#160;</strong></em>(IMG path: SAP Customizing Implementation Guide -&gt; Conversion of Accounting to SAP S/4HANA -&gt;&#160;Data Migration -&gt; Complete Migration of Financial Documents to Trade Finance). Please be aware that the migration steps cannot be re-started once the migration has been set to Completed. After the migration is completed, you are allowed to create letters of credit transactions in Trade Finance.</p>\r\n<ul>\r\n<li><strong>Activities after Migration</strong></li>\r\n</ul>\r\n<p>After migration is set to <em>Completed</em>, you can use manual activies to complete migration for the unmigrated data (IMG path: SAP Customizing Implementation Guide -&gt; Conversion of Accounting to SAP S/4HANA -&gt;&#160;Activities after Migration -&gt; Manual Activities for Trade Finance -&gt; Complete Migration of Financial Documents for Unmigrated Data).</p>\r\n<p>(3) Restrictions</p>\r\n<ul>\r\n<li>Only received Letters of Credit are supported to be migrated.</li>\r\n<li>The financial documents with status <em>'Document is closed and can be reorganized'</em> or <em>'Document has been cancelled'</em> are not supported to be migrated.</li>\r\n<li>Since the amount field length in TRM (10 digits) is less than the amount field in Sales (13 digits), thus the letters of credit with amount larger than the maximum amount supported in TRM are not supported to be migrated.</li>\r\n<li>To make the migration work smoothly, we have enabled some auto-corrections during migration (please refer to the documentation of IMG node <strong>Map SD-FT Banks with Business Partners </strong>and<strong> Migrate Master Data of Financial Documents </strong>for the details). You will get tips for these auto-corrections during migration, and you are able to make further adjustments in TRM after migration is completed.</li>\r\n<li>Please do apply note&#160;<strong>2516205</strong><strong> - </strong><strong>Set Default </strong><strong>Number Range </strong><strong>Interval for RCD &amp; LCNUM</strong><strong>&#160;</strong>to generate internals automatically. Otherwise overlaps of the financial document&#160;numbers&#160;between SD-FT and Trade Finance&#160;may be caused, which will&#160;lead to migration failure for those overlapped L/C.</li>\r\n</ul>\r\n<ul>\r\n<li>If you migrate financial documents (SD-FT) from SAP ERP 6.0 EhP7 SP10 and higher to Trade Finance in SAP S/4HANA 1709 FPS00, your data in structure INCO_L in table AKKP will be lost after you migrate to SAP S/4HANA system. <strong>This is planned to be solved with SAP S/4HANA 1709 FPS01 and higher.</strong> For more information, see SAP Note&#160;2517188.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Migration, Financial Document, Foreign Trade, SD-FT, Sales, Trade Finance, Letter of Credit, L/C, TRM, Migration of financial documents to Trade Finance</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Summary note for migrating&#160;financial document data from SD-FT to Trade Finance in Treasury and Risk Management.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>If you want to migrate financial documents in SD-FT from ERP to Trade Finance in SAP S/4HANA 1709 FPS00, we highly recommend that you implement the following SAP notes in the sequence indicated as below before the migration.</p>\r\n<p>Howerver, if you have already upgraded to SAP S/4HANA 1709 FPS01 or higher before you do the migration, you don't need to implement these SAP notes as the changes&#160;will be imported into the&#160;Feature Package as of SAP S/4HANA 1709 FPS01.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 243px; width: 808px;\">\r\n<tbody>\r\n<tr>\r\n<td>2511637</td>\r\n<td>Fix the Issues of Financial Document Migration</td>\r\n</tr>\r\n<tr>\r\n<td>2497858</td>\r\n<td>Fix the Issues of Financial Document Migration</td>\r\n</tr>\r\n<tr>\r\n<td>2462049</td>\r\n<td>Issues when initializing risk check decisions</td>\r\n</tr>\r\n<tr>\r\n<td>2525464</td>\r\n<td>\r\n<div class=\"WordSection1\">\r\n<p>DDIC note for Note&#160;2515759</p>\r\n</div>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2515759</td>\r\n<td>\r\n<div class=\"WordSection1\">\r\n<p>Correct the Settlement BAPI for Financial Document Migration</p>\r\n</div>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2512605</td>\r\n<td>Incorrect or missing messages during Trade Finance migration</td>\r\n</tr>\r\n<tr>\r\n<td>2527584</td>\r\n<td>Type mapping entries with abnormal status of financial document can be saved</td>\r\n</tr>\r\n<tr>\r\n<td>2526985</td>\r\n<td>Incorrect or missing messages during Trade Finance migration</td>\r\n</tr>\r\n<tr>\r\n<td>2532118</td>\r\n<td>Changing Delivery Class from Application to Customizing for Migration</td>\r\n</tr>\r\n<tr>\r\n<td>2532119</td>\r\n<td>Duplicated Error Messages in Check Customizing Settings Activity</td>\r\n</tr>\r\n<tr>\r\n<td>2554418</td>\r\n<td>\r\n<p>DDIC Note for Note 2532118</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />If you have already upgraded to SAP S/4HANA 1709 FPS02 or higher before you do the migration, you don't need to implement these SAP notes as the changes&#160;will be imported into the&#160;Feature Package as of SAP S/4HANA 1709 FPS02.<br /><br /></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"width: 802px; height: 25px;\">\r\n<tbody>\r\n<tr>\r\n<td>2547893</td>\r\n<td>\r\n<p>Company Code Specific Conversion to S/4HANA - Financial documents to Trade Finance</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2601242</td>\r\n<td>\r\n<p>Two Risk Check Decisions for one delivery</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2615975</td>\r\n<td>\r\n<p>Company Code Specific Conversion to S/4HANA: Financial Documents to Trade Finance</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you have already upgraded to SAP S/4HANA 1709 FPS03 or higher before you do the migration, you don't need to implement these SAP notes as the changes&#160;will be imported into the&#160;Feature Package as of SAP S/4HANA 1709 FPS03.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"width: 802px; height: 25px;\">\r\n<tbody>\r\n<tr>\r\n<td>2625806</td>\r\n<td>\r\n<p>Assignment not migrated for some financial documents</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2627716&#160;</td>\r\n<td>\r\n<p>Missing Mapping of Opening Bank and Business Partner Results in Migration Failure</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I075726)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (I062803)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2521086", "RefComponent": "FIN-FSCM-TRM-TM-TF", "RefTitle": "Letter of Credit: Risk Check Does not work for Incoterms Fields", "RefUrl": "/notes/2521086"}, {"RefNumber": "2517188", "RefComponent": "SD-BF-CM", "RefTitle": "No INCO_L structure in table AKKP", "RefUrl": "/notes/2517188"}, {"RefNumber": "2517123", "RefComponent": "FIN-FSCM-TRM-TM-TR", "RefTitle": "Transaction Order is Created with BAPI_FTR_LC_CREATE", "RefUrl": "/notes/2517123"}, {"RefNumber": "2516205", "RefComponent": "FIN-FSCM-TRM-TM-TF", "RefTitle": "Set Default Number Range Interval for RCD & LCNUM", "RefUrl": "/notes/2516205"}, {"RefNumber": "2515759", "RefComponent": "FIN-FSCM-TRM-TM-TF", "RefTitle": "Correct the BAPI for Contract Settlement During the Migration of Financial Documents", "RefUrl": "/notes/2515759"}, {"RefNumber": "2512771", "RefComponent": "SD-BF-CM", "RefTitle": "Updates of data in assigned trade finance transaction during migration may cause data inconsistency", "RefUrl": "/notes/2512771"}, {"RefNumber": "2512605", "RefComponent": "SD-BF-CM", "RefTitle": "Incorrect or missing messages during Trade Finance migration", "RefUrl": "/notes/2512605"}, {"RefNumber": "2511637", "RefComponent": "FIN-FSCM-TRM-TM-TF", "RefTitle": "Fix the Issues of Financial Document Migration", "RefUrl": "/notes/2511637"}, {"RefNumber": "2497970", "RefComponent": "FIN-FSCM-TRM-TM-TF", "RefTitle": "Fix the Issue of Trade Finance Integration with S/4HANA Sales", "RefUrl": "/notes/2497970"}, {"RefNumber": "2497858", "RefComponent": "FIN-FSCM-TRM-TM-TF", "RefTitle": "Fix the Issues of Financial Document Migration", "RefUrl": "/notes/2497858"}, {"RefNumber": "2474764", "RefComponent": "SD-BF-CM", "RefTitle": "Error message when navigating back to SD document from trade finance transaction", "RefUrl": "/notes/2474764"}, {"RefNumber": "2462049", "RefComponent": "SD-BF-CM", "RefTitle": "Issues when initializing risk check decisions", "RefUrl": "/notes/2462049"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2267310", "RefComponent": "SD-FT", "RefTitle": "S4TWL - Foreign Trade", "RefUrl": "/notes/2267310 "}, {"RefNumber": "2495932", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709 - application specific notes in system conversion / upgrade follow-on phase", "RefUrl": "/notes/2495932 "}, {"RefNumber": "2517984", "RefComponent": "SD-BF-CM", "RefTitle": "Integration of Trade Finance with SAP S/4HANA Sales", "RefUrl": "/notes/2517984 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}