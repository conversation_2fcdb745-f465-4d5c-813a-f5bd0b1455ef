{"Request": {"Number": "2395579", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 394, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018442232017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002395579?language=E&token=96A3E1D7EDDDE5C1E24FDC1A3EC0AFB6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002395579", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002395579/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2395579"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.09.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT."}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT.", "value": "BW-BCT-GEN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-GEN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2395579 - SAP BW/4HANA Content - Additional information on delivered variables"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are looking for additional information on variables which are delivered the with SAP BW/4HANA and SAP BW/4HANA Content, like naming convention, or description and definition of variables.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP BW/4HANA Content Basis Add-On, SAP BW/4HANA Content Add-On, BW4CONT, BW4CONTB, Variables</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>- Delivered variables&#160;for SAP BW/4HANA 1.0 (shipped with software components DW4CORE 100, BW4CONTB 100&#160;or BW4CONT 100)</p>\r\n<p>- Delivered variables for SAP BW/4HANA 2.0 (shipped with software components DW4CORE 200, BW4CONTB 200 or BW4CONT 200)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>You can find&#160;information about the&#160;delivered variables in the attached Excel file 'BW4HANA_Content_Variables.xlsx':</p>\r\n<p>- Tab 'List of variables'&#160;contains a list of all delivered variables with&#160;detailed information such as technical name and description of variable, InfoObject, variable properties&#160;and gives also examples for the usage of delivered customer exit variables. The tab also lists&#160;for the two product versions BW/4HANA 1.0 and BW/4HANA 2.0 with which software component and&#160;in which&#160;support package the variable was first included.</p>\r\n<p>- Tab 'Naming conventions'&#160;explains the used naming conventions.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D028763)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D028763)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002395579/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002395579/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002395579/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002395579/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002395579/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002395579/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002395579/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002395579/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002395579/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "BW4HANA_Content_Variables_SPlatest.xlsx", "FileSize": "59", "MimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001376162016&iv_version=0011&iv_guid=00109B36D6221ED9B7BCB306FC8AC0D3"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2400685", "RefComponent": "BW-BCT-GEN", "RefTitle": "SAP BW/4HANA Content 1.0 - Recommended SAP BW/4HANA 1.0 support package and SAP Notes", "RefUrl": "/notes/2400685"}, {"RefNumber": "2400585", "RefComponent": "BW-BCT-GEN", "RefTitle": "Collective Note: SAP BW/4HANA Content 1.0 (BW4CONT 100 & BW4CONTB 100)", "RefUrl": "/notes/2400585"}, {"RefNumber": "2397520", "RefComponent": "BW-BCT-GEN", "RefTitle": "SAP BW/4HANA Content - Differences to SAP HANA optimized BI Content delivered with BI CONT 7.57", "RefUrl": "/notes/2397520"}, {"RefNumber": "2395613", "RefComponent": "BW-BCT-GEN", "RefTitle": "SAP BW/4HANA Content - Additional information on delivered InfoObjects", "RefUrl": "/notes/2395613"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3301981", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Function Modules for SAP Exit Variables part of BI_CONT included with BW4CONT 200 SP13", "RefUrl": "/notes/3301981 "}, {"RefNumber": "2785514", "RefComponent": "BW-BCT-GEN", "RefTitle": "FAQ: SAP BW/4HANA Content (BW4CONT & BW4CONTB)", "RefUrl": "/notes/2785514 "}, {"RefNumber": "2785525", "RefComponent": "BW-BCT-GEN", "RefTitle": "Collective Note: SAP BW/4HANA Content 2.0 (BW4CONT 200 & BW4CONTB 200)", "RefUrl": "/notes/2785525 "}, {"RefNumber": "2400685", "RefComponent": "BW-BCT-GEN", "RefTitle": "SAP BW/4HANA Content 1.0 - Recommended SAP BW/4HANA 1.0 support package and SAP Notes", "RefUrl": "/notes/2400685 "}, {"RefNumber": "2400585", "RefComponent": "BW-BCT-GEN", "RefTitle": "Collective Note: SAP BW/4HANA Content 1.0 (BW4CONT 100 & BW4CONTB 100)", "RefUrl": "/notes/2400585 "}, {"RefNumber": "2397520", "RefComponent": "BW-BCT-GEN", "RefTitle": "SAP BW/4HANA Content - Differences to SAP HANA optimized BI Content delivered with BI CONT 7.57", "RefUrl": "/notes/2397520 "}, {"RefNumber": "2395613", "RefComponent": "BW-BCT-GEN", "RefTitle": "SAP BW/4HANA Content - Additional information on delivered InfoObjects", "RefUrl": "/notes/2395613 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}