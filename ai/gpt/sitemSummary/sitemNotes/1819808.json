{"Request": {"Number": "1819808", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 564, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017599592017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001819808?language=E&token=EFD6F42B5D9519630F6C75C902E5BC95"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001819808", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001819808/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1819808"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.05.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC-LGN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authentication"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authentication", "value": "BC-SEC-LGN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC-LGN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1819808 - SPNego ABAP: Collective Corrections"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note bundles several corrections for the SPNego authentication mechanism:</p> <OL>1. On some platforms (e.g. AIX) it was not possible to maintain KeyTab entries using transaction SPNEGO.</OL> <OL>2. The exception handling and tracing of SPNego was significantly improved. There should be no exceptions visible within transaction SPNEGO with empty fields (e.g. for C-function).</OL> <OL>3. During SPNego authentication the determined Kerberos User Principal Name (consisting of two parts: user name part and domain part, separated by the @ sign) needs to be converted into a SNC name in order to utilize the existing SNC mappings. Previously this was not configurable.<br /></OL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SPNego, Kerberos, SAP NetWeaver Single-Sign-On, GSSAPI, Simple and Protected GSSAPI Negotiation Mechanism, logon, login, Single Sign-On, SSO, authentication, security</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. The kernel was using an outdated internal representation of a data structure which was used in the interface between ABAP and kernel.</OL> <OL>2. Detailed error information was not propagated along the callstack inside the kernel.</OL> <OL>3. It has turned out that the ability of using arbitrary Kerberos-based SNC products in combination with SPNego is important. Unfortunately, the various SNC products differ in the way how they construct a SNC name for a given Kerberos User Name. This imposes the requirement for a configuration option.<br /></OL><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>A new kernel is required (for all points listed above):</p> <UL><LI>7.21 (EXT) with PL 110 (or higher), respectively</LI></UL> <UL><LI>7.40 with PL 7 (or higher)</LI></UL> <p><br />For the new configuration option an ABAP correction (Support Package or manual correction) is required in addition.<br /></p> <b>Manual correction</b><br /> <p>Use transaction RZ11 and switch to maintenance mode (via the menu item \"Goto\"-&gt;\"Maintenance Mode\"). Then press Create to add a new profile parameter (after applying the kernel patch, see above) named \"spnego/construct_SNC_name\" (caution: case-sensitive).<br /><br />Attributes:</p> <UL><LI>short text = \"Construct SNC name for given Kerberos User Name\"</LI></UL> <UL><LI>application area = \"Logon\"</LI></UL> <UL><LI>parameter type = \"Character String\"</LI></UL> <UL><LI>\"Change permitted\"</LI></UL> <UL><LI>\"All operating systems\"</LI></UL> <UL><LI>enable checkboxes \"dynam. switchable\" and \"same on all servers\"</LI></UL> <p></p> <b>Parameter documentation</b><br /> <p>Valid values for spnego/construct_SNC_name consist of 3 digits (format: XYZ) with</p> <UL><LI>the first digit (X) denoting the <B>prefix</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT><B>Value</TH><TH ALIGN=LEFT> Meaning</B></TH></TR> <TR><TD>1</TD><TD> Prefix 'p:CN='</TD></TR> <TR><TD>2</TD><TD> Prefix 'p:'</TD></TR> </TABLE></UL> <p></p> <UL><LI>the second digit (Y) defining the conversion of the <B>user name part</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT><B>Value</TH><TH ALIGN=LEFT> Meaning</B></TH></TR> <TR><TD>0</TD><TD> User name part: do not convert</TD></TR> <TR><TD>1</TD><TD> User name part: convert to upper case</TD></TR> <TR><TD>2</TD><TD> User name part: convert to lower case</TD></TR> </TABLE></UL> <p></p> <UL><LI>the third digit (Z) defining the conversion of the <B>domain part</B></LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT><B>Value</TH><TH ALIGN=LEFT> Meaning</B></TH></TR> <TR><TD>0</TD><TD> Domain part: do not convert</TD></TR> <TR><TD>1</TD><TD> Domain part: convert to upper case</TD></TR> <TR><TD>2</TD><TD> Domain part: convert to lower case</TD></TR> </TABLE></UL> <p><br /><B><U>Examples:</U></B><br /><br />spnego/construct_SNC_name = 200 will convert the Kerberos User Name '<EMAIL>' into the SNC name 'p:<EMAIL>'.<br /><br />For the same Kerberos User Name spnego/construct_SNC_name = 111 would result in 'p:CN=<EMAIL>'.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D046187)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021767)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001819808/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001819808/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001819808/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001819808/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001819808/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001819808/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001819808/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001819808/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001819808/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1832706", "RefComponent": "BC-SEC-LGN", "RefTitle": "SPNego ABAP: Fixes for Algorithms AES128, AES256, DES", "RefUrl": "/notes/1832706"}, {"RefNumber": "1798979", "RefComponent": "BC-SEC-LGN", "RefTitle": "SPNego ABAP: Downport", "RefUrl": "/notes/1798979"}, {"RefNumber": "1732610", "RefComponent": "BC-SEC-LGN-SPN", "RefTitle": "SPNego ABAP: Troubleshooting Note", "RefUrl": "/notes/1732610"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2721210", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "SAP SSO 3.0 through Active Directory and Kerberos when AD usernames differ from SAP user IDs", "RefUrl": "/notes/2721210 "}, {"RefNumber": "2665649", "RefComponent": "BC-IAM-SSO-SL", "RefTitle": "Entries cannot be saved in transaction SPNEGO", "RefUrl": "/notes/2665649 "}, {"RefNumber": "1798979", "RefComponent": "BC-SEC-LGN", "RefTitle": "SPNego ABAP: Downport", "RefUrl": "/notes/1798979 "}, {"RefNumber": "1832706", "RefComponent": "BC-SEC-LGN", "RefTitle": "SPNego ABAP: Fixes for Algorithms AES128, AES256, DES", "RefUrl": "/notes/1832706 "}, {"RefNumber": "1732610", "RefComponent": "BC-SEC-LGN-SPN", "RefTitle": "SPNego ABAP: Troubleshooting Note", "RefUrl": "/notes/1732610 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.40", "To": "7.40", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73108", "URL": "/supportpackage/SAPKB73108"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74003", "URL": "/supportpackage/SAPKB74003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.40 64-BIT", "SupportPackage": "SP014", "SupportPackagePatch": "000014", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200022526&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.40 64-BIT UNICODE", "SupportPackage": "SP014", "SupportPackagePatch": "000014", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67838200100200019652&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP110", "SupportPackagePatch": "000110", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1798979", "RefTitle": "SPNego ABAP: Downport", "RefUrl": "/notes/0001798979"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}