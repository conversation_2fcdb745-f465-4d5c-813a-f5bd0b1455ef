{"Request": {"Number": "2804474", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 227, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001700112019"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=C9BDB063409A0CF5BE813012B593F558"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2804474"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.07.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FI-RA-CP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Revenue Accounting Contract Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Revenue Accounting", "value": "FI-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Revenue Accounting Contract Processing", "value": "FI-RA-CP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-RA-CP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2804474 - Revenue Accounting and Reporting with SAP S/4HANA 1909: Release Information Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Release Information Note (RIN) contains information and references to notes for using Revenue Accounting and Reporting with SAP S/4HANA 1909.</p>\r\n<p>Starting with SAP S/4HANA 1809, the former Revenue Accounting and Reporting add-on including the add-on SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0 had become an integral part of SAP S/4HANA.</p>\r\n<p>Support notes available in the RAR 1.3 Support Package 09 are considered to be available in SAP S/4HANA 1909.</p>\r\n<p>Please note that some of the referenced notes below refer to RAR 1.3. As far as the notes mention additional information, this information also applies to SAP S/4HANA 1909.</p>\r\n<p><strong>Note:</strong>&#160;This SAP note is subject to change. Check this note for changes on a regular basis. All important changes are documented in section \"Important Changes after Release of SAP S/4HANA 1909\".</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to use Revenue Accounting and Reporting with S/4HANA 1909.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note covers the following topics:</p>\r\n<ul>\r\n<li><a target=\"_self\" href=\"#Important\">Important Information</a></li>\r\n<li><a target=\"_self\" href=\"#Activation\">Activation of Revenue Accounting and Reporting</a></li>\r\n<li><a target=\"_self\" href=\"#Technical_Landscape\">Technical System Landscape</a></li>\r\n<li><a target=\"_self\" href=\"#Important_Changes\">Important changes with S/4HANA 1809&#160;and S/4HANA 1909</a></li>\r\n<li><a target=\"_self\" href=\"#Implementation_Information\">Implementation information after upgrade</a></li>\r\n<li><a target=\"_self\" href=\"#Important_Notes\">Important SAP Notes</a></li>\r\n<li><a target=\"_self\" href=\"#Classic\">Classic Contract Management versus Contract Management</a></li>\r\n<li><a target=\"_self\" href=\"#application_help\">Application Help</a></li>\r\n<li><a target=\"_self\" href=\"#FIORI\">FIORI applications with S/4HANA 1909</a></li>\r\n<li><a target=\"_self\" href=\"#SD_REVREC\">SD Revenue Recognition and S/4HANA</a></li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"Important\"></a>&#65279;Important Information</strong></p>\r\n<p>To integrate with sender components such as SAP Sales and Distribution or other sender components, Revenue Accounting uses RFCs as part of the Inbound Processing configuration.</p>\r\n<p>For proper integration, you have to either implement SAP Note&#160;<a target=\"_blank\" href=\"/notes/2957643\">2957643 - RFC Fast Serialization Error in compatibility mode</a>&#160;or you must not use RFC Destination NONE.</p>\r\n<p><strong><a target=\"_blank\" name=\"Activation\"></a>&#65279;</strong><strong>Activation of Revenue Accounting and Reporting</strong></p>\r\n<p>Revenue Accounting and Reporting was subject to a release restriction&#160;(similar to the add-on early adopter care process).</p>\r\n<p>Depending on the support package you have applied, you may get&#160;information message FARR_FOUNDATION 301, which prevents the configuration of accounting principle information for Revenue Accounting.</p>\r\n<p>In the meanwhile, Revenue Accounting and Reporting can be implemented and used without restrictions. Please implement note 2675322. The correction in the note will remove the above mentioned message and unlock the configuration of accounting principle settings for Revenue Accounting.</p>\r\n<p><strong><a target=\"_blank\" name=\"Technical_Landscape\"></a>&#65279;Technical System Landscape</strong></p>\r\n<div>\r\n<p>As of SAP S/4HANA 1809, the former SAP Revenue Accounting and Reporting add-on has become an integral part of SAP S/4HANA. This relates to product version SAP REVENUE ACCOUNTING including software component version REVREC.</p>\r\n<p>The Revenue Accounting and Reporting functionality still needs to be integrated into operational components which send order and billing information to Revenue Accounting. With SAP S/4HANA 1809, the following operational components, or products, support integration with Revenue Accounting:</p>\r\n<ul>\r\n<li>Sales and Distribution (SD)</li>\r\n<li>Billing and Revenue Innovation Management (BRIM), also known as SAP Hybris Billing</li>\r\n<li>SAP Customer Relationship Management (CRM)</li>\r\n</ul>\r\n<p>For the integration with Sales and Distribution, the integration functionality previously deployed through the software component SAP Sales Integration with SAP Revenue Accounting and Reporting 1.0 (SAP SALES INTEGR SAP RAR 1.0) has also been added to the SAP S/4HANA 1809 stack and following S/4HANA releases.</p>\r\n<p>For more information especially on distributed system scenarios, please refer to the product assistance for S/4 HANA</p>\r\n<p>Choose Enterprise Business Applications -&gt; Finance -&gt; Accounting and Financial Close -&gt; Revenue Accounting and Reporting -&gt; Integration of Sender Components -&gt; Technical System Landscape</p>\r\n<p><strong><a target=\"_blank\" name=\"Important_Changes\"></a>&#65279;Important changes with S/4HANA 1809 and S/4HANA 1909</strong></p>\r\n<p>From SAP S/4HANA OP 1809 on, SAP changed the data types for the contract and performance obligation IDs from NUMC14 to CHAR14 and from NUMC16 to CHAR16 respectively. The change from NUMC to CHAR was done to simplify and optimize (performance) the internal handling of temporary IDs. There is no migration needed. Data will be migrated on the fly where needed. Nevertheless, this change might have an impact on your custom code. Please follow the guidelines in note&#160;<a target=\"_blank\" href=\"/notes/2672794\">2672794</a>.</p>\r\n<p><strong><a target=\"_blank\" name=\"Implementation_Information\"></a>&#65279;Implementation Information after upgrade</strong></p>\r\n<p>A direct upgrade from Revenue Accounting and Reporting 1.2 (anyDB or S/4HANA) is not supported. If you had previously deployed the add-on, you will first have to upgrade the add-on to RAR 1.3. RAR 1.3 is generally available but is subject to a similar activation process as implemented in S/4HANA 1809 or S/4HANA 1909 (see SAP Note <a target=\"_blank\" href=\"/notes/2750710\">2750710</a>).</p>\r\n<p>An upgrade from Revenue Accounting 1.3 on anyDB or an earlier S/4HANA release follow the common upgrade process. There are no specific procedures in the case of Revenue Accounting. Especially no changes requiring mandatory migration steps with regards to database tables are expected. The same applies to the user interface and process flows.</p>\r\n<p>Depending on the feature pack of RAR 1.3 you upgrade from, you may need to regenerate the RAI classes as new fields had been added:</p>\r\n<p>To regenerate the RAI classes, you need to take the following steps:</p>\r\n<ul>\r\n<li>Start transaction FARR_RAI_CONF.</li>\r\n<li>Mark a Revenue Accounting Item Class.</li>\r\n<li>Press the Selected entries button.</li>\r\n</ul>\r\n<p>The system checks whether the configuration of the revenue accounting item class contains all fields that are available in the interface components.</p>\r\n<p>If none of the applied interface components were enhanced, it is not necessary to update the configuration and the configuration keeps the status active. In such a case, you can stop here and immediately proceed with the next revenue accounting item class.</p>\r\n<p>If an interface component was enhanced during the upgrade, the system detects this and automatically updates the configuration. In this case, the configuration of the revenue accounting item class sets the status to Modified. You then need to take the following steps:</p>\r\n<ol>\r\n<li>Save the updated configuration of your revenue accounting item class.</li>\r\n<li>Activate the configuration. Afterwards you can see that the status of the configuration of the revenue accounting item class is active.</li>\r\n<li>Start transaction FARR_RAI_GEN by choosing Environment -&gt; Generation.</li>\r\n<li>Mark the updated revenue accounting item class and press Generate.</li>\r\n<li>Choose Yes when you are asked whether you want to run the generation immediately.</li>\r\n<li>Choose No when you are asked whether you want to delete revenue accounting items.</li>\r\n</ol>\r\n<p><strong>Note</strong></p>\r\n<p>You cannot delete revenue accounting items in production systems. You are therefore not asked whether you want to delete revenue accounting items that are available in production systems. Once this step is complete, you will receive a popup with the generated results. All status icons should be green.</p>\r\n<p>7. Repeat these steps for all revenue accounting item classes.</p>\r\n<div>\r\n<p><strong><a target=\"_blank\" name=\"Important_Notes\"></a>&#65279;Important SAP Notes</strong></p>\r\n<p>Please read the following SAP Notes before you start the implementation of SAP Revenue Accounting and Reporting as part of S/4HANA 1909.</p>\r\n<p>Make sure that you have the most up-to-date version of each SAP Note, which you can find on the SAP Support Portal at&#160;<a target=\"_blank\" href=\"https://support.sap.com/notes\">https://support.sap.com/notes</a>&#160;Information published on SAP site.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"4\" cellspacing=\"2\" style=\"width: 1028px; height: 1362px;\"><colgroup><col width=\"138\" /> <col span=\"2\" width=\"64\" /> </colgroup>\r\n<tbody>\r\n<tr>\r\n<td height=\"19\" width=\"138\"><strong>SAP Component</strong></td>\r\n<td width=\"64\"><strong>Number</strong></td>\r\n<td width=\"64\"><strong>Title</strong></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-VAL</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2735529\">2735529</a></td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2735529\">RAR : Prevent allocation flag is not displayed in FARR_CONTR_MON</a></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-VAL</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2744567\">2744567</a></td>\r\n<td class=\"xl66\">Inflight Check C06/C09 - Enhancements</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-IP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2785484\">2785484</a></td>\r\n<td class=\"xl66\">RA Unable to process Fullfilment Revenue Accounting Item</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-ANA</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2789482\">2789482</a></td>\r\n<td class=\"xl66\">RAR: Report FARR_BIZ_RECON does not show invoiced amount and Order details</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-PC</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2792719\">2792719</a></td>\r\n<td class=\"xl66\">Improve Lock mechanism in FARR_REVENUE_POSTING and FARR_REVENUE_REPOST</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-IP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2794609\">2794609</a></td>\r\n<td class=\"xl66\">RA Compound Header deleted incorrectly</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-ANA</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2796749\">2796749</a></td>\r\n<td class=\"xl66\">BW Delta extraction failure (0FARR_POB_ATTR, 0FARR_CONTR_ATTR).</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-FF</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2797848\">2797848</a></td>\r\n<td class=\"xl66\">PoC Fulfillment from CO Integration - Wrong Actual Quantity</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2802453\">2802453</a></td>\r\n<td class=\"xl66\">NWBC GUI: Slow performance when displaying contracts with many POBs</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-INV</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2805014\">2805014</a></td>\r\n<td class=\"xl66\">For mass updated invoices, skip the revenue calculation</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-VAL</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2805515\">2805515</a></td>\r\n<td class=\"xl66\">C28 New Inflight check for multiple Group GUID</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2807568\">2807568</a></td>\r\n<td class=\"xl66\">Error FARR_CONTRACT_MAIN237 for high level POBs with zero allocable price</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-IP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2809186\">2809186</a></td>\r\n<td class=\"xl66\">SSP is empty for compound POB</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-PC</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2809759\">2809759</a></td>\r\n<td class=\"xl66\">Contract shift: Runtime Error EXPORT_TOO_MUCH_DATA</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2810585\">2810585</a></td>\r\n<td class=\"xl66\">FARR_SET_POB_COMPLETE: performance is not optimal</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-IP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2814235\">2814235</a></td>\r\n<td class=\"xl66\">Timeout when processing order RAIs with predecessor</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-VAL</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2814730\">2814730</a></td>\r\n<td class=\"xl66\">RAR : Transaction price and allocation amount are different even if prevent allocation flag is marked</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2817568\">2817568</a></td>\r\n<td class=\"xl66\">New POBs Receive Existing POB Data in BRF+</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-ANA</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2821364\">2821364</a></td>\r\n<td class=\"xl66\">UDO Report for SAP Note 2796749</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2821995\">2821995</a></td>\r\n<td class=\"xl66\">Adding previously removed linked POB terminates with GETWA_NOT_ASSIGNED error.</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-FF</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2822710\">2822710</a></td>\r\n<td class=\"xl66\">Manual Fulfillment UI: Leading POB cannot be manually fulfilled despite its Linked POB is Time-based one w/ SDT3 and 'MA' Event Type</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2823403\">2823403</a></td>\r\n<td class=\"xl66\">Contract search with multiselect parameters doesn't find contracts</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl66\"><a target=\"_blank\" href=\"/notes/2824350\">2824350</a></td>\r\n<td class=\"xl66\">Incorrect Change Type triggered to RA contract</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl65\"><a target=\"_blank\" href=\"/notes/2829332\">2829332</a></td>\r\n<td class=\"xl65\">Missing Validation of Customizing Activity 'Open and Close Revenue Accounting Periods'</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl65\"><a target=\"_blank\" href=\"/notes/2834596\">2834596</a></td>\r\n<td class=\"xl65\">Restriction of changes in the maintenance view FARR_V_CODE_SWIT</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl65\"><a target=\"_blank\" href=\"/notes/2835140\">2835140</a></td>\r\n<td class=\"xl65\">Revenue Accounting and Reporting - Avoiding of a Repeated Activation Request</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl65\"><a target=\"_blank\" href=\"/notes/2842212\">2842212</a></td>\r\n<td class=\"xl65\">Introduction of new field RAR_VERSION_CODE in table FARR_D_CONTRACT</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl65\"><a target=\"_blank\" href=\"/notes/2884099\">2884099</a></td>\r\n<td class=\"xl65\">Wrong initial effective date on performance obligations after contract change</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl65\">\r\n<div><a target=\"_blank\" href=\"/notes/2930049\">2930049</a></div>\r\n</td>\r\n<td class=\"xl65\">Wrong currency field for the transaction price in the performance obligation change type table</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-INV</td>\r\n<td class=\"xl65\"><a target=\"_blank\" href=\"/notes/3146039\">3146039</a></td>\r\n<td class=\"xl65\">Invoice RAIs processed incorrectly</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl65\"><a target=\"_blank\" href=\"/notes/2930049\">3142187</a></td>\r\n<td class=\"xl65\">Revenue is not recognized after revenue derecognition within the same fiscal period</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-CP</td>\r\n<td class=\"xl65\"><a target=\"_blank\" href=\"/notes/3284376\">3284376</a></td>\r\n<td class=\"xl65\">Fix endless loop in invoice processing due to invalid reconkey buffer</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl65\" height=\"20\">FI-RA-IP</td>\r\n<td><a target=\"_blank\" href=\"/notes/3284376\">3296936</a></td>\r\n<td class=\"xl65\">RA Contract Combination locking issue</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><a target=\"_blank\" name=\"Classic\"></a></strong><strong>Classic Contract Management versus Contract Management</strong></p>\r\n<div>\r\n<p>Starting with S/4HANA 1909, customers can use an optimized version of the Revenue Accounting contract management.</p>\r\n<p>As a result, the existing contract management as known under Revenue Accounting 1.3 or in S/4HANA 1809 will be referred to as Contract Management (Classic).</p>\r\n<p>In Customizing you can find the settings for the classic contract management under:&#160;Revenue Accounting -&gt;&#160;Revenue Accounting Contracts (Classic)</p>\r\n<p>The Customizing for the optimized contract management you can find under:&#160;Revenue Accounting -&gt;&#160;Revenue Accounting Contracts</p>\r\n<p>You can find more information for the optimized contract management in the application help <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/1909.000/en-US/4020cd6e8f82478b92c1edb5d6cb2f4a.html\">here</a>.</p>\r\n<p>To reduce the upgrade risk, the optimized contract management will be introduced parallel to the existing Classic Contract Management as known since RAR 1.3. The optimized contract management focuses on performance optimizations and additional features, such as day-based contract modifications.&#160;This requires some changes in the table structures of Revenue Accounting, but in general existing database tables and processes should remain stable, which is also important to not endanger the running processes and to reduce risk.</p>\r\n<p>The optimized contract management can be released for new contracts based on a new contract category defined in customizing:&#160;Revenue Accounting -&gt;&#160;Revenue Accounting Contracts -&gt;&#160;Select Contract Management for Contract Categories</p>\r\n<p>For 1909 change processes are only supported coming from the operational system. Cost recognition is currently not supported with the Optimized Contract Management.</p>\r\n<p>Classic contract management will continue to run for the existing contracts parallel to the optimized contract management for new contracts to allow stepwise introduction of the new features.</p>\r\n<p>To achieve a better performance, coding had to be significantly reworked and simplified.&#160;Custom code would need to be adopted for these new contracts if classic contract management already has been used.</p>\r\n<p>The following table provides a high-level overview of changes between classic and optimized contract management. Note that this table will be adjusted subsequently with new releases in S/4HANA.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Functional Area</strong></td>\r\n<td>\r\n<p><strong>In scope with S/4HANA 1909 for</strong></p>\r\n<p><strong>Optimized Contract Management</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Out of scope with S/4HANA 1909&#160;</strong></p>\r\n<p><strong>(compared to existing RAR 1.3)</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>Change to existing BAdIs or Customizing&#160;</strong></p>\r\n<p><strong>(compared to RAR 1.3)</strong></p>\r\n</td>\r\n<td><strong>Differences in System Behaviour</strong></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Contract Management</p>\r\n<p>Data Modelling</p>\r\n</td>\r\n<td>\r\n<ul>\r\n<li>POB attributes</li>\r\n<li>Leading / Linked POBs</li>\r\n</ul>\r\n</td>\r\n<td>\r\n<ul>\r\n<li>POBs with hierarchies (like compound or BoM)</li>\r\n<li>POB with cost</li>\r\n</ul>\r\n</td>\r\n<td>n/a</td>\r\n<td>n/a</td>\r\n</tr>\r\n<tr>\r\n<td>Account Determination</td>\r\n<td>\r\n<p>All accounts for supported processes and related derivation rules in RAR 1.3</p>\r\n</td>\r\n<td>n/a</td>\r\n<td>n/a</td>\r\n<td>\r\n<p>Under RAR 1.3:</p>\r\n<p>Accounts are re-determined at POB change</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Allocation</td>\r\n<td>\r\n<ul>\r\n<li>SSP determination</li>\r\n<li>POB excluded from allocation</li>\r\n<li>Default implementation:</li>\r\n<ul>\r\n<li>SSP based allocation with SSP tolerances</li>\r\n<li>Residual allocation</li>\r\n</ul>\r\n</ul>\r\n</td>\r\n<td>Manual price allocation</td>\r\n<td>\r\n<p>Following RAR 1.3 BAdIs are not used:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_BADI_ALLOCATION_ENGINE</li>\r\n<li>FARR_BADI_ALLOCATION_METHOD</li>\r\n</ul>\r\n</div>\r\n<div>Following New BAdI will be added:</div>\r\n<ul>\r\n<li>FARR_BADI_PRICE_ALLOCATION</li>\r\n</ul>\r\n<p>Already implemented customer logic has to be re-implemented</p>\r\n</td>\r\n<td>\r\n<p>Under RAR 1.3:</p>\r\n<p>Leading POB is first allocated with other POBs. And then the leading POB is allocated with other linked POBs in a group</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>Fulfillment</td>\r\n<td>\r\n<ul>\r\n<li>Event-based POB with quantity-based fulfillment from invoice or goods issue</li>\r\n<li>Time-based POB with start date type = 1 and 2.</li>\r\n<li>Deferral methods:</li>\r\n<ul>\r\n<li>1 (365/366 day per year)</li>\r\n<li>2 (360 day per year)</li>\r\n<li>S (12 periods based per year)</li>\r\n<li>F (First period only)</li>\r\n<li>L (Last period only)</li>\r\n</ul>\r\n<li>Value-based fulfillments from customer invoice or goods issue</li>\r\n<li>Manual PoC based fulfillments</li>\r\n</ul>\r\n</td>\r\n<td>\r\n<ul>\r\n<li>Time-based POB with start date type = 3</li>\r\n<li>POC based fulfillments with result analysis integration</li>\r\n<li>Deferral method:</li>\r\n<ul>\r\n<li>3 (Same as 2)</li>\r\n<li>4 (Period with fraction in first/last period)</li>\r\n</ul>\r\n</ul>\r\n</td>\r\n<td>\r\n<p>Following RAR 1.3 BAdI is not used:</p>\r\n<ul>\r\n<li>FARR_BADI_DEFERRAL_METHOD</li>\r\n</ul>\r\n<p><br /> Following New BAdI will be added:</p>\r\n<ul>\r\n<li>FARR_BADI_DEFERRAL_METHOD_V2</li>\r\n</ul>\r\n<p><br /> Already implemented customer logic has to be re-implemented</p>\r\n</td>\r\n<td>\r\n<p>Under RAR 1.3 fractions/nominator/denominator are used to present quantities per period.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Contract change process</p>\r\n<p>(Day-based contract modification)</p>\r\n</td>\r\n<td>\r\n<ul>\r\n<li>Day-based contract modification</li>\r\n<li>Determine change type:</li>\r\n<li>Change reason from operational system:</li>\r\n<ul>\r\n<li>Prospective treatment: unrecognized&#160;revenue will be re-allocated and no catch-up is calculated&#160;&#160;</li>\r\n<li>Retrospective treatment: all contractual price will be re-allocated and catch-up is calculated</li>\r\n</ul>\r\n</ul>\r\n</td>\r\n<td>Manual spreading and its API</td>\r\n<td>\r\n<p>Following RAR 1.3 BAdIs are not used:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_CHANGE_MODE_DETERMINATION</li>\r\n<li>FARR_BADI_TM_REMAINING_PERC</li>\r\n</ul>\r\n</div>\r\n<p>Following New BAdI will be added:</p>\r\n<div>\r\n<ul>\r\n<li>FARR_BADI_CHANGE_TYPE_DETN</li>\r\n</ul>\r\n</div>\r\n<p>Already implemented customer logic has to be re-implemented<br /> <br /> (*) there is no need to reimplement FARR_BADI_TM_REMAINING_PERC as the effective date is introduced to calculate the remaining percentage</p>\r\n<p>Changes are tracked in table&#160;FARR_D_POB_CTYPE (Performance Obligation Change Type)</p>\r\n</td>\r\n<td>\r\n<p>Under RAR 1.3:</p>\r\n<ul>\r\n<li>No day-based contract modification</li>\r\n<li>Change in FARR_D_CHG_TYPE is period-based</li>\r\n<li>Interface of BADI: Determine change type:</li>\r\n<ul>\r\n<li>Contract level change type</li>\r\n<li>Change type: P/R/'Empty'</li>\r\n<li>No change reason</li>\r\n</ul>\r\n<li>Default implementation: 50+ rules for retrospective and prospective.</li>\r\n<li>Standard has overwrite logic</li>\r\n<li>Catch-up is merged in recognized revenue</li>\r\n</ul>\r\n<p>Changes are tracked in table&#160;FARR_D_CHG_TYPE (Table of Change Type)</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>\r\n<p>Beside what was already mentioned before, the following functionality in Classic Contract Management&#160;is currently not supported in&#160;Optimized Contract Management:</p>\r\n<div>\r\n<div>\r\n<ul>\r\n<li>\r\n<div>Migration from Legacy Systems (planned with feature pack)</div>\r\n</li>\r\n<li>Transition to new Accounting Principles</li>\r\n<li>Results Analysis Integration</li>\r\n<li>Simplified Invoicing</li>\r\n<li>Manual POB Reassignment between revenue contracts</li>\r\n<li>Create Contracts from SD Invoices</li>\r\n<li>Drop Shipment</li>\r\n<li>Intercompany Billing</li>\r\n<li>Condition-based Contract Acquisition Costs (Contract Acquisition Cost POBs are still supported)</li>\r\n<li>\r\n<div>Inactive condition types with zero amounts from Sales and Distribution (SD) can still be sent from integration component) to the optimized inbound processing when an active price condition is missing. For these condition types, you have to define the main condition flag in BadI&#160;FARRIC_BADI_ORDER (method ORDER_DATA_TO_ARL) since BadI FARR_BADI_RAI0 is not supported anymore in the optimized inbound processing.</div>\r\n</li>\r\n<li>Fixed exchange rate method</li>\r\n</ul>\r\n</div>\r\n</div>\r\n<p>For classic contract management you can find the following information in the <a target=\"_blank\" href=\"/notes/2675360\">Release Information Note for Revenue Accounting and Reporting with S/4HANA 1809</a>:</p>\r\n<ul>\r\n<li>&#65279;Sizing Information</li>\r\n<li>&#65279;Inflight Checks</li>\r\n<li>&#65279;Data Validation Checks</li>\r\n<li>&#65279;Migration and Transition</li>\r\n<li>&#65279;Integrating with external sender components</li>\r\n<li>&#65279;Integrating with SAP Sales and Distribution (SD)</li>\r\n<li>&#65279;Security&#160;Information</li>\r\n<li>Standard Roles</li>\r\n<li>Standard Authorization Objects</li>\r\n<li>Additional relevant Authorization Objects</li>\r\n</ul>\r\n<p><strong><a target=\"_blank\" name=\"FIORI\"></a>&#65279;FIORI applications with S/4HANA 1909</strong></p>\r\n<p>Starting with S/4HANA 1909, the following FIORI apps are provided in combination with the optimized contract management. Please note that these FIORI applications exclusively work with the optimized contract management.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>FIORI ID</strong></td>\r\n<td><strong>FIORI App Name</strong></td>\r\n<td><strong>FIORI Type</strong></td>\r\n</tr>\r\n<tr>\r\n<td>F3881</td>\r\n<td>Manual Fulfillment</td>\r\n<td>Object Pages (Transactional)</td>\r\n</tr>\r\n<tr>\r\n<td>F3882</td>\r\n<td>Revenue Schedule</td>\r\n<td>Analytical</td>\r\n</tr>\r\n<tr>\r\n<td>F3883</td>\r\n<td>Manage Revenue Contracts</td>\r\n<td>Object Pages (Transactional)</td>\r\n</tr>\r\n<tr>\r\n<td>F4067</td>\r\n<td>Overview Page for Revenue Accountant</td>\r\n<td>Analytical</td>\r\n</tr>\r\n<tr>\r\n<td>F4068</td>\r\n<td>Disaggregation of Revenue</td>\r\n<td>Analytical</td>\r\n</tr>\r\n<tr>\r\n<td>F4069</td>\r\n<td>Quick Combination for Revenue Contracts</td>\r\n<td>Object Pages (Transactional)</td>\r\n</tr>\r\n<tr>\r\n<td>F4102</td>\r\n<td>Manage Performance Obligation Details</td>\r\n<td>Object Pages (Transactional)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>You will need to activate and maintain your OData services in the frontend system. For this, you can use transaction /IWFND/MAINT_SERVICE.</p>\r\n<p>For the FIORI apps above the following external services should be registered and linked system alias of your backend system.</p>\r\n<ul>\r\n<li>FARR_COMBINE_CONTRACTS_SRV</li>\r\n<li>FARR_CONTRACT_OVERVIEW</li>\r\n<li>FARR_MANAGE_PERF_OBLGN</li>\r\n<li>FARR_MANUAL_FULFILLMENT</li>\r\n<li>FARR_OVERVIEW_PAGE_SRV</li>\r\n<li>FARR_REVENUE_SCHEDULE</li>\r\n</ul>\r\n<p>For the Overview Page, in addition you will need to maintain below OData service alias (Tc: /IWFND/MAINT_SERVICE) :</p>\r\n<ul>\r\n<li>C_NmbrOfRAContractsBySts_CDS</li>\r\n<li>C_RAGlobalFilterOvw_CDS</li>\r\n<li>C_RARecgdRevnByFlfmtType_CDS</li>\r\n<li>C_RARecgdRevnByQuarterOvw_CDS</li>\r\n<li>C_RARecgdRevnTrendOvw_CDS</li>\r\n</ul>\r\n<p>In addition, you will need to generate the mapping of date and period information in the system as part of the general Financials setup.</p>\r\n<p>If you use the classic contract management, you will continue to use the existing WebDynpro applications for the revenue accoutant and SAPGUI transactions for administrative tasks such as the Revenue Accounting Item Monitor.</p>\r\n<p>To consume these UIs, you can either:</p>\r\n<ul>\r\n<li>use the SAP Business Client (aka NWBC) with the predefined roles outlined in the Release Information Note: 2675360 - Revenue Accounting and Reporting with SAP S/4HANA 1809: Release Information Note or</li>\r\n<li>use the FIORI Launchpad also for the SAPGUI and WebDynpro transactions (instead of NWBC). In that case, you will consume the existing transactions which have been visually harmonized (Visual Harmonization). Visual Harmonization means that each user can use the SAP Fiori Launchpad as their single entry point for all their work with SAP S/4HANA, and they have a harmonized visual user experience when calling up the various apps.</li>\r\n</ul>\r\n<p>This also helps adoption for customers migrating from the SAP Business Suite to SAP S/4HANA. It helps customers protect their investments, especially since all custom-built SAP GUI transactions and Web Dynpro ABAP apps automatically get the new visual theme, and it also allows you to take time with educating your users on new UIs.</p>\r\n<p><strong><a target=\"_blank\" name=\"application_help\"></a>&#65279;Application Help</strong></p>\r\n<p>You can find the application help for Revenue Accounting in S/4HANA 1909 under <em>Enterprise Business Applications -&gt; Finance -&gt; Accounting and Financial Close -&gt; Revenue Accounting and Reporting</em>&#160;or directly <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/1909.000/en-US/c7ce61533be7ff4fe10000000a44176d.html\">here</a>.</p>\r\n<p>For the optimized contract management you can find the information <a target=\"_blank\" href=\"https://help.sap.com/viewer/f63dd39a28bb4b90adbf9e608aff58ea/1909.000/en-US/4020cd6e8f82478b92c1edb5d6cb2f4a.html\">here</a>:</p>\r\n<p>Please note, that the information in the node Contract Change After Contract Combination is not valid for the optimized contract management and will be removed from the application help.</p>\r\n<p><strong style=\"font-size: 10px;\"><span style=\"font-size: 14px;\"><a target=\"_blank\" name=\"SD_REVREC\"></a>&#65279;SD Revenue Recognition and S/4HANA</span></strong></p>\r\n</div>\r\n<div><span style=\"font-size: 14px;\">If you are currently on ECC and using SD Revenue Recognition and want to upgrade to S/4HANA, during the upgrade a SIC check will notify you that SD Revenue Recognition has been deprecated with S/4HANA. T</span><span style=\"font-size: 14px;\">he SIC check has been implemented to make sure SAP's customers are aware of the fact that the SD Revenue Recognition solution as a whole is no longer available in the S/4HANA code line (S4CORE) and that the successor product SAP Revenue Accounting &amp; Reporting is available to continue even the legacy processes where they were left off.</span></div>\r\n<div>\r\n<p>Some important references for the Sales integration component (REVRECSD Addon for ECC):</p>\r\n<p><a target=\"_blank\" href=\"/notes/2341717\">2341717</a> FAQ: Future of SD Revenue Recognition after IFRS15 is released</p>\r\n<p><a target=\"_blank\" href=\"/notes/2569950\">2569950</a> FAQ: Migration &amp; Operational Load in the SD Integration Component</p>\r\n<p><a target=\"_blank\" href=\"/notes/2733866\">2733866</a> Migration Guide for SD Revenue Recognition to Revenue Accounting and Reporting</p>\r\n<p><a target=\"_blank\" href=\"/notes/2703761\">2703761</a> SAP Notes for SD-BIL-RA</p>\r\n<p>The migration process from SD Revenue Recognition is&#160;<strong>only possible from ECC source systems</strong>, because throughout the migration the SD Revenue Recognition programs and posting logic are used to calculate the legacy data of each process and to trigger possible correction postings in financials to ensure data consistency. These programs are not available in the S/4HANA code line&#160;(S4CORE), therefore the migration is no longer possible if the source system has already been upgraded from ECC to S/4HANA.</p>\r\n<p>The upgrade normally happens is the following:</p>\r\n<ol>\r\n<li>SAP RAR 1.3 is installed and&#160;configured in the ECC</li>\r\n<li>SD Revenue Recognition processes which are open, partially completed or processes where subsequent follow-on documents are expected (like credit memos, cancellations or return orders) are migrated to SAP RAR 1.3 on ECC using the standard operational load and initial load programs</li>\r\n<li>ECC is upgraded to S/4HANA 1809/1909</li>\r\n</ol>\r\n<p>Some important references about the RAR solultion (REVREC Addon for ECC):</p>\r\n<p><a target=\"_blank\" href=\"/notes/2582784\">2582784</a> Revenue Accounting and Reporting with SAP RAR 1.3 and S/4HANA 1809 - FAQ's and Guidance</p>\r\n<p><a target=\"_blank\" href=\"/notes/2656669\">2656669</a> Activation of Revenue Accounting for SAP S/4HANA 1809 and later</p>\r\n<p>It is NOT possible to migrate open SD Revenue Recognition processes to SAP RAR directly in S/4HANA&#160;<strong>as SAP's Standard tools</strong>&#160;cannot support that scenario. Furthermore all SD Revenue Recognition transactions are blacklisted in S/4HANA so customers would not be able to continue working with any documents which are relevant for that solution if you do perform the upgrade without operational load first.</p>\r\n<p><strong><a target=\"_blank\" name=\"SD_REVREC\"></a>&#65279;Recommended Preparation for the upgrade to Revenue Accounting and Reporting&#160; in SAP S/4 HANA 2020<br /></strong></p>\r\n<p>The new field RAR_VERSION_CODE on contract level indicates whether the contract has been created in Contract Management Classic or in Contract Management. Starting with Release SAP S/4 HANA 2020, this new field is used for the determination of a contract version.</p>\r\n<p>By default, the new field&#160;is populated automatically after upgrade to \"On Premise 2020\", making use of Silent Data Migration infrastructure.&#160;You can&#160;already accelerate the phase of Silent Data Migration in Revenue Accounting and Reporting in SAP S/4HANA 1909 by implementing&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/2842212\">2842212&#160;Introduction of new field RAR_VERSION_CODE in table FARR_D_CONTRACT</a>,&#160;before the upgrade to Revenue Accounting and Reporting SAP S/4HANA 2020. Then all new Revenue Accounting Contracts will already have the field correctly populated. As a result, you can already see in SAP S/4 HANA 1909 where the contract has been created.</p>\r\n<p>&#160;</p>\r\n</div>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SD-BIL-RA (SD Integration Revenue Accounting & Reporting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON>k<PERSON> (D028561)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030097)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2930049", "RefComponent": "FI-RA-CP", "RefTitle": "Wrong currency field for the transaction price in the performance obligation change type table", "RefUrl": "/notes/2930049"}, {"RefNumber": "2829332", "RefComponent": "FI-RA-CP", "RefTitle": "Missing Validation of Customizing Activity 'Open and Close Revenue Accounting Periods'", "RefUrl": "/notes/2829332"}, {"RefNumber": "2800818", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909: Release Information Note for Finance", "RefUrl": "/notes/2800818"}, {"RefNumber": "2675322", "RefComponent": "FI-RA-IP", "RefTitle": "Activation of Revenue Accounting for SAP S/4HANA 1809 and later", "RefUrl": "/notes/2675322"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2682903", "RefComponent": "FI-RA", "RefTitle": "Implementing higher support packages without implementing mid support packages.", "RefUrl": "/notes/2682903 "}, {"RefNumber": "2799003", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909: Restriction Note", "RefUrl": "/notes/2799003 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}