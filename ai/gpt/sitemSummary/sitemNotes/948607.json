{"Request": {"Number": "948607", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 361, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016101742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000948607?language=E&token=049B2A153CE267ACE9ED5EE8D6135ECC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000948607", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000948607/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "948607"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.06.2018"}, "SAPComponentKey": {"_label": "Component", "value": "PY-IT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Italy"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Italy", "value": "PY-IT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-IT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "948607 - HR-IT: 770 2006 Legal Change"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>HR-IT: Modello 770 2006 Legal Change</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RPC770I0 7702006 770 semplificato</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Modello 770 semplificato for 2006.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>VERSIONING</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Date</strong>&#160;</td>\r\n<td><strong>Reason</strong></td>\r\n</tr>\r\n<tr>\r\n<td>02</td>\r\n<td>March 8, 2010</td>\r\n<td>Released for Customer</td>\r\n</tr>\r\n<tr>\r\n<td>03</td>\r\n<td>June&#160;21, 2018</td>\r\n<td>CAR files removed.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Abstract</strong></p>\r\n<p>This note gives an overview over the new modello 770 semplificato for the year 2006. It provides advance transports of the new report, sample customizing and a detailed explanation of the changes in comparison to 2005.</p>\r\n<p><strong>Contents of the note</strong></p>\r\n<ol>1. Prerequisites</ol><ol>2. New/Renamed boxes in the modello 770 2006</ol><ol>3. Boxes without value in SAP</ol><ol>4. Selection-screen</ol><ol>5. Interface changes</ol><ol><ol>a) Titoli TFR</ol></ol><ol><ol>b) TFR Fondi pensione</ol></ol><ol>6. PART B - Section 'TFR'</ol><ol><ol>a) 'Spezzature' for box 0077</ol></ol><ol><ol>b) 'Incentivo all'esodo' boxes</ol></ol><ol><ol>c) 'Periodo di commisurazione' boxes</ol></ol><ol><ol>d) 'Periodo convenzionale' box 0110</ol></ol><ol><ol>e) TFR section visualization</ol></ol><ol>7. PART B - Heirs(Eredi) data changes</ol><ol>8. PART B - Section 'Annotazioni'</ol><ol>9. Unchanged parts</ol>\r\n<p><strong>1. Prerequisites</strong></p>\r\n<ul>\r\n<li>You must be on the latest HR support package level for your release.</li>\r\n</ul>\r\n<ul>\r\n<li>Complete year 2005 calculated with SAP-Payroll and consistent results in the clusters (RI/B2/PC).</li>\r\n</ul>\r\n<ul>\r\n<li>No modifications to the clusters RI/B2/PC after the run of RPCALCI0.</li>\r\n</ul>\r\n<ul>\r\n<li>No master data changes to the info types without having re-done the corresponding payroll calculation.</li>\r\n</ul>\r\n<ul>\r\n<li>You have successfully run the CUD 2005.</li>\r\n</ul>\r\n<ul>\r\n<li>The Annotations produced by CUD must contain the code in the formats '(COD xx)' or '(COD x)', where 'x' is a character (for further details, please, read the hint \"Section 'Annotazioni'\" below).</li>\r\n</ul>\r\n<ul>\r\n<li>You have the last versions of the CUD 2006 (Program RPCIUDI0) and 770 2006 installed. The new 770 is included in the attachments to this note.</li>\r\n</ul>\r\n<p><strong>2. New/Renamed boxes in the modello 770 2006</strong></p>\r\n<p><br />This year a lot of boxes have changed their name. Here is the complete list of changes:</p>\r\n<ul>\r\n<li>Part B. (see attached file 'parte_b_renamed_boxes.pdf')</li>\r\n</ul>\r\n<ul>\r\n<li>Part C. Old boxes from 108 to 113 have changed to 79-84</li>\r\n</ul>\r\n<p><strong>3. Boxes without value in SAP</strong></p>\r\n<ul>\r\n<li>Part B. box 67, \"Codice Fiscale del Sostituto\". This box must be filled only in rare cases. Moreover, this information is not present in SAP master data. So, this field is only supported by user-exit 107.</li>\r\n</ul>\r\n<ul>\r\n<li>Part B. box 78, \"Codice Fiscale del Sostituto\". This box must be filled only in rare cases. Moreover, this information is not present in SAP master data. So, this field is only supported by user-exit 108.</li>\r\n</ul>\r\n<ul>\r\n<li>Part B. New box 90, \"Codice Fiscale del Sostituto\". This box must be filled only in rare cases. Moreover, this information is not present in SAP master data. So, this field is only supported by user-exit 113. This user-exit allow you to add or modify data at end of employee elaboration.</li>\r\n</ul>\r\n<ul>\r\n<li>Part B. New box 108, \"Codice Fiscale del Sostituto\". This box must be filled only in rare cases. Moreover, this information is not present in SAP master data. So, this field is only supported by user-exit 110.</li>\r\n</ul>\r\n<p><strong>4. Selection-screen</strong></p>\r\n<p>Parameters 'Sede lavorativa' and 'Codice inps' have not a great importance for 770. In the new version these parameters are not mandatory for the program execution.<br />Leaving empty these parameters, 770 run for all data without filter; otherwise run filtering data by parameters.</p>\r\n<p><strong>5. Interface changes</strong></p>\r\n<p><br />In order to accomodate changes in the box numbers the interface \"Parte<br />B: titoli TFR\" has been changed as follows:</p>\r\n<p><strong>-&gt; A.. \"Parte B: titoli TFR\"</strong></p>\r\n<p>Pernr&#160;&#160; : 8 characters (CID number ex: 00002001)<br /> Box087&#160;&#160; : 1 character&#160;&#160; (Value of casella&#160;&#160;87)<br /> Box097&#160;&#160; : 1 character&#160;&#160; (Value of casella&#160;&#160;97)<br /> Box103&#160;&#160; : 1 character&#160;&#160; (Value of casella 103)<br /> Box115&#160;&#160; : 1 character&#160;&#160; (Value of casella 115)<br /> Box120&#160;&#160; : 1 character&#160;&#160; (Value of casella 120)<br /><br />In order to accomodate changes in the box numbers the interface \"Parte B: TFR fondi\" has been changed as follows:</p>\r\n<p><strong>-&gt; B.. \"Parte B: TFR fondi\"</strong></p>\r\n<p>Pernr&#160;&#160; : 8 characters (CID number ex: 00002001)<br /> Box125&#160;&#160;: 20 characters (Value of casella 125) ex.'0510'<br /> &#160;&#160;from 1 to 2 bytes 'YEARS'&#160;&#160;ex.'05'<br /> &#160;&#160;from 3 to 4 bytes 'MONTHS' ex.'10'<br /> Box126&#160;&#160;: 20 characters (Value of casella 126)<br /> Box127&#160;&#160;: 1 character&#160;&#160; (Value of casella 127)<br /> Box128&#160;&#160;: 20 characters (Value of casella 128)<br /> Box129&#160;&#160;: 20 characters (Value of casella 129)<br /> Box130&#160;&#160;: 20 characters (Value of casella 130)<br /> Box131&#160;&#160;: 20 characters (Value of casella 131)<br /> Box132&#160;&#160;: 20 characters (Value of casella 132)<br /> Box133&#160;&#160;: 20 characters (Value of casella 133)<br /> Box134&#160;&#160;: 20 characters (Value of casella 134)<br /> Box135&#160;&#160;: 20 characters (Value of casella 135)<br /> Box136&#160;&#160;: 1 characters (Value of casella 136)<br /> Box137&#160;&#160;: 20 characters (Value of casella 137)<br /> Box138&#160;&#160;: 20 characters (Value of casella 138)<br /> Box139&#160;&#160;: 20 characters (Value of casella 139)<br /> Box146&#160;&#160;: 20 characters (Value of casella 146)</p>\r\n<p><strong>6. Section 'TFR'</strong><br /> <strong>-&gt; A.. 'Spezzature' for box 0077</strong></p>\r\n<p>This year, CUD data doesn't contain information about 'Tempo determinato', therefore 770 has a new function based on EMENS algorithm to provide it.<br />If, in elaboration year, employee has more than one contract type, 770 will duplicate \"parte B\".</p>\r\n<p><strong>-&gt; B.. 'Incentivo all'esodo' boxes (0106-0123)-(0107-0124)</strong></p>\r\n<p>Last year, there was a box containing a value for all \"Incentivo all'esodo\". This year, 770 needs to split the amount in two periods: before and after 31.12.2000.<br />Customizing table V_T5ITW5 for box 0106 with a wage type(/108), 770 will calculate the right value for this box and for box 0123 using \"TFR maturato\" data (boxes 0095-0113). See the following example:<br />Ex.<br />box 0106 =&#160;&#160;/108 * [box 0095 / (box 0113 + box 0095)]<br />box 0123 =&#160;&#160;/108 - box 0106<br />The same algorithm will be applied to calculate box 0107 and box 0124, customizing box 0107 with a custom wage type(XXXX):<br />Ex.<br />box 0107 =&#160;&#160;XXXX * [box 0095 / (box 0113 + box 0095)]<br />box 0124 =&#160;&#160;XXXX - box 0107</p>\r\n<p><strong>-&gt; C.. 'Periodo di commisurazione' boxes (0091-0109)</strong></p>\r\n<p>Last year, there was a box containing a value for all \"Periodo di commisurazione\". This year, 770 needs to split \"Periodo di commisurazione\" in two boxes before and after 31.12.2000.<br />Customizing table V_T5ITW5 for box 0091 with a wage type(/TB2-/TB3), report will calculate the right value filling box 0091 and box 0109 if employee is hired until 31.12.2000, filling box 0109 if employee is hired since 01.01.2001.</p>\r\n<p><strong>-&gt; D.. 'Periodo convenzionale' box (0110)</strong></p>\r\n<p>This is a new box customized using a wage type(default /TBY) containing number of months of \"Periodo convenzionale\".<br />770 report calculate years and months to fill this box.</p>\r\n<p><strong>-&gt; E.. 'TFR section visualization'</strong></p>\r\n<p>Boxes of section \"Dati relativi al rapporto di lavoro\" (from 0073 to 0090)are shown if there is at least one of its amount box.<br />Boxes of section \"TFR ed altre indennit&#224; maturate al 31.12.2000\" (from 0091 to 0108)are shown if there is at least one of its amount box.<br />Boxes of section \"TFR ed altre indennit&#224; maturate dal 01.01.2001\" (from 0109 to 0124)are shown if there is at least one of its amount box.</p>\r\n<p><strong>7. Heirs(Eredi) data changes</strong></p>\r\n<p>This year, if employee is defunct, 770 has to show heir's data in a different way.<br />Defunct declaration doesn't have to contain data about TFR (from 0073 to 0156), only one row for each heir must be shown using boxes 0158 and 0159.<br />Each heir's declaration must contain data about defunct's TFR (from 0073 to 0156) and box 0157 with defunct's \"codice fiscale\".</p>\r\n<p><strong>8. Section 'Annotazioni'</strong></p>\r\n<p><br />The section 'Annotazioni' must contain the codes of annotations reported by CUD 2006 model. These codes have been introduced with CUD 2005 and they are composed by one or two characters. For further details on the annotation codes, please, take a look to the note nr. 881477 for CUD 2006.<br />The 770 model contains in the part B boxes 160-177. These fields must be filled with the codes of annotations generated by the CUD report. In order to allow a correct generation of these boxes, the annotations in the CUD temse file must contain the token \"(COD xx)\" or \"(COD x)\" (see the note 881477). Otherwise, 770 report is not able to generate the correct values for the 'Annotazioni' boxes.<br />Boxes 160-177 are in the format DBxxxYYY, where xxx (0-999) represents the number of repetition. In fact, if the 770 report finds (in the temse file) more annotations with the same code, the related box of 770 will be reported the same number of times, changing only the repetition number xxx.</p>\r\n<p><strong>9. Unchanged parts</strong></p>\r\n<p><br />All the rest of 770 model remains unchanged in respect of the previous year. See note nr. 853349 (770 2005) for further details.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I859065"}, {"Key": "Processor                                                                                           ", "Value": "I862000"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948607/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948607/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948607/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948607/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948607/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948607/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948607/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948607/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000948607/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "parte_b_renamed_boxes.pdf", "FileSize": "45", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000174272006&iv_version=0003&iv_guid=50602725213B484E86E9DCC282992954"}, {"FileName": "user_exit_113_example.pdf", "FileSize": "108", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000174272006&iv_version=0003&iv_guid=E692CB9AAAD6724B955D64D1F3A8077B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CB3", "URL": "/supportpackage/SAPKE46CB3"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CG1", "URL": "/supportpackage/SAPKE46CG1"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47058", "URL": "/supportpackage/SAPKE47058"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50024", "URL": "/supportpackage/SAPKE50024"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60007", "URL": "/supportpackage/SAPKE60007"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}