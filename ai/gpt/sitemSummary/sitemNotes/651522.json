{"Request": {"Number": "651522", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 623, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003429902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000651522?language=E&token=40BDCF88ED59658B2C0E6BEDCB774BD4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000651522", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000651522/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "651522"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.06.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-LO-LIS"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Logistics Information System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Logistics - General", "value": "BW-BCT-LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Logistics Information System", "value": "BW-BCT-LO-LIS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-LO-LIS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "651522 - XPRA RMCSBWXP_COM: Business Content incomplete"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Parts of the Business Contents for the Logistics BW extraction are missing. That is, after a PlugIn upgrade with target release 2003.1 or an initial installation of PI 2003.1, fields may be missing in the following extract structures: Extraktstruktur&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Datasource<br />MC02M_0ITM&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_02_ITM<br />MC02M_0SCL&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_02_SCL<br />MC03BF0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2LIS_03_BF<br />MC03UM0&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 2LIS_03_UM<br />MC04P_0MAT&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_04_P_MATNR<br />MC17I00NTF&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_17_I0NOTIF<br />MC18I00NTF&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_18_I0NOTIF<BR/> MC45W_0LST (ab 4.6C)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;2LIS_45_LST (ab 4.6C)<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>PI 2003.1, 2003_1_40B, 2003_1_45B, 2003_1_46B, 2003_1_46C, 2003_1_470, MC02M_0SCL, 2LIS_02_SCL, MC02M_0ITM, 2LIS_02_ITM, MC02M_0SCL, 2LIS_02_SCL, MC03BF0, 2LIS_03_BF, MC03UM0, 2LIS_03_UM, MC04P_0MAT, 2LIS_04_P_MATNR, MC17I00NTF, 2LIS_17_I0NOTIF, MC18I00NTF, 2LIS_18_I0NOTIF, RMCSBWXP_COM, upgrade, XPRA<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Release notes for the PI component PI are not delivered correctly. As a result, the extract structures listed above are not generated, and so they remain in the system with the status from PI 2002.2 or 2002.1 on 2003_1_470 as of Support Package 2.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Post-upgrade activities have to be performed (see also the related note 597635, paragraph on postprocessing).</p> <OL>1. Ensure that the prerequisites for the upgrade listed in note 597635 are fulfilled (in particular I.4. Updates, see also note 328181).</OL> <OL>2. Implement the corrections with the prerequisite note 648565.</OL> <OL>3. Implement the attached corrections in your system.</OL> <OL>4. Call transaction SE38 (ABAP Workbench) and execute report RMCSBWXP_COM.</OL> <OL>5. Repeat the previous step until the report runs without errors. Process any error messages that appear using the long texts and note 640066.</OL> <p><br />Notes:<br />What happens if you <B>do</B> <B>not carry out the postprocessing?</B><br />Fields may be missing in the extract structures listed above. These cannot then be extracted to BW. A subsequent development for reporting in BW with these fields is possible if you make up for the postprocessing that you have not carried out, but this would necessitate <B>the setting</B> <B>up of statistical data</B> in R/3 (-&gt; <B>down</B> <B>time</B>) for the application in question and a subsequent delta initialization.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-IM (BW only - Inventory Management)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-PUR (BW only - Purchase)"}, {"Key": "Other Components", "Value": "BW-BCT-ISR-GT (BW only - Global Trade)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-BW (BW only - MM Content im BW System)"}, {"Key": "Other Components", "Value": "BW-BCT-CS (BW only - Customer Service)"}, {"Key": "Other Components", "Value": "BW-BCT-ISR-AB (BW only - Agency Business)"}, {"Key": "Other Components", "Value": "BW-BCT-PP-BW (BW only - PP Content in the BW System)"}, {"Key": "Other Components", "Value": "BW-BCT-PP-PP (BW only - Production Planning)"}, {"Key": "Other Components", "Value": "BW-BCT-PM (BW only - Plant Maintenance)"}, {"Key": "Other Components", "Value": "BC-UPG (Upgrade - general)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D036721)"}, {"Key": "Processor                                                                                           ", "Value": "C5041990"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000651522/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000651522/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000651522/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000651522/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000651522/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000651522/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000651522/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000651522/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000651522/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "701179", "RefComponent": "BW-BCT", "RefTitle": "BW-BCT composite SAP note: PI 2004.1 upgrade or SP", "RefUrl": "/notes/701179"}, {"RefNumber": "681187", "RefComponent": "BW-BCT-PM", "RefTitle": "AUSBS, AUSVN, AUZTB & AUZTV fields missing in ext. structure", "RefUrl": "/notes/681187"}, {"RefNumber": "648565", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP_COM does not generate anything", "RefUrl": "/notes/648565"}, {"RefNumber": "643166", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP* - activation error stops the upgrade", "RefUrl": "/notes/643166"}, {"RefNumber": "640066", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Upgrade: Return code 6 with XPRA RMCSBWXP_COM", "RefUrl": "/notes/640066"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "623411", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "LBWE: Gener. terminates with D0 322 (end phase 002)", "RefUrl": "/notes/623411"}, {"RefNumber": "614603", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Skipping releases during the upgrade (jump upgrade)", "RefUrl": "/notes/614603"}, {"RefNumber": "611115", "RefComponent": "BW-BCT", "RefTitle": "Composite BW-BCT note: PI 2003.1 upgrade", "RefUrl": "/notes/611115"}, {"RefNumber": "597635", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/597635"}, {"RefNumber": "489259", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP ... terminates with error message D0 322", "RefUrl": "/notes/489259"}, {"RefNumber": "328181", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Changes to extract structures in the Customizing Cockpit", "RefUrl": "/notes/328181"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "328181", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Changes to extract structures in the Customizing Cockpit", "RefUrl": "/notes/328181 "}, {"RefNumber": "701179", "RefComponent": "BW-BCT", "RefTitle": "BW-BCT composite SAP note: PI 2004.1 upgrade or SP", "RefUrl": "/notes/701179 "}, {"RefNumber": "611115", "RefComponent": "BW-BCT", "RefTitle": "Composite BW-BCT note: PI 2003.1 upgrade", "RefUrl": "/notes/611115 "}, {"RefNumber": "681187", "RefComponent": "BW-BCT-PM", "RefTitle": "AUSBS, AUSVN, AUZTB & AUZTV fields missing in ext. structure", "RefUrl": "/notes/681187 "}, {"RefNumber": "640066", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Upgrade: Return code 6 with XPRA RMCSBWXP_COM", "RefUrl": "/notes/640066 "}, {"RefNumber": "643166", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP* - activation error stops the upgrade", "RefUrl": "/notes/643166 "}, {"RefNumber": "648565", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP_COM does not generate anything", "RefUrl": "/notes/648565 "}, {"RefNumber": "614603", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Skipping releases during the upgrade (jump upgrade)", "RefUrl": "/notes/614603 "}, {"RefNumber": "623411", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "LBWE: Gener. terminates with D0 322 (end phase 002)", "RefUrl": "/notes/623411 "}, {"RefNumber": "489259", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP ... terminates with error message D0 322", "RefUrl": "/notes/489259 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PI", "From": "2003_1_40B", "To": "2003_1_470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "PI 2003_1_40B", "SupportPackage": "SAPKIPZH14", "URL": "/supportpackage/SAPKIPZH14"}, {"SoftwareComponentVersion": "PI 2003_1_45B", "SupportPackage": "SAPKIPZH24", "URL": "/supportpackage/SAPKIPZH24"}, {"SoftwareComponentVersion": "PI 2003_1_46B", "SupportPackage": "SAPKIPZH34", "URL": "/supportpackage/SAPKIPZH34"}, {"SoftwareComponentVersion": "PI 2003_1_46C", "SupportPackage": "SAPKIPZH44", "URL": "/supportpackage/SAPKIPZH44"}, {"SoftwareComponentVersion": "PI 2003_1_470", "SupportPackage": "SAPKIPZH54", "URL": "/supportpackage/SAPKIPZH54"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "PI", "NumberOfCorrin": 2, "URL": "/corrins/0000651522/48"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "PI", "ValidFrom": "2003_1_40B", "ValidTo": "2003_1_470", "Number": "648565 ", "URL": "/notes/648565 ", "Title": "XPRA RMCSBWXP_COM does not generate anything", "Component": "BW-BCT-LO-LIS"}, {"SoftwareComponent": "PI", "ValidFrom": "2003_1_470", "ValidTo": "2003_1_470", "Number": "648565 ", "URL": "/notes/648565 ", "Title": "XPRA RMCSBWXP_COM does not generate anything", "Component": "BW-BCT-LO-LIS"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}