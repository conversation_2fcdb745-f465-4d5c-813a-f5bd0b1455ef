{"Request": {"Number": "3015013", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 199, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000884812021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=012D9BCBE1A88EAAF1F7B931DAB01E41"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3015013"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.06.2021"}, "SAPComponentKey": {"_label": "Component", "value": "FI-SL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Special Purpose Ledger"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Special Purpose Ledger", "value": "FI-SL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-SL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3015013 - S4TWL - Special Purpose Ledger"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p id=\"\">You are doing a system conversion from SAP&#160;ERP&#160;to SAP&#160;S/4HANA&#160;or an upgrade from a lower to a higher SAP&#160;S/4HANA&#160;release and are using the functionality described in this note. The following SAP&#160;S/4HANA&#160;Simplification Item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP&#160;S/4HANA, Compatibility Scope, Compatibility Package, System Conversion, Upgrade, Special Ledger, ID 430</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Description<br /><br /></strong>The usage of special purpose ledger is partly included in the SAP&#160;S/4HANA&#160;compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it&#8217;s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, this topic is listed under the ID 430.&#160;<br />This note shall provide information on the restrictions that apply for special purpose ledger in SAP S/4HANA.</p>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>You find a description of the functionality in compatibility scope in the SAP S/4HANA Feature Scope Description&#160;&gt; section <a target=\"_blank\" href=\"https://help.sap.com/viewer/029944e4a3b74446a9099e8971c752b9/2020/en-US/ca44a656980ff62ae10000000a4450e5.html\">Special Ledger (FI-SL)</a>.</p>\r\n<p>Generally, special purpose ledger as framework is not part of compatibility scope, which means it will be available and supported beyond the compatibility scope expiry date. However, special ledgers and respective tables and reports which have been defined for applications that are part of the compatibility scope will not be supported beyond expiry date. This applies to the following applications</p>\r\n<ul>\r\n<li>EC-PCA - Classical profit center accounting (compatibility scope ID 427)</li>\r\n<li>Cost of Sales ledger&#160;(compatibility scope ID 430)</li>\r\n<li>Consolidation preparation - Closing Operations&#160;(compatibility scope ID 430)</li>\r\n</ul>\r\n<p>Custom-defined ledgers&#160;based on the applications listed above will also not be support beyond expire date. Custom-defined ledgers based on any other SAP applications or custom-built tables will be supported.<br />Refer to the attached spreadsheet for detailed availability description of the SAP delivered ledgers and related tables.</p>\r\n<p><strong>Required and Recommended Action(s)<br /></strong><br />Refer to the following notes and simplification items for further information on the handling the deprecated applications.</p>\r\n<ul>\r\n<li>Classical profit center accounting - note 2993220</li>\r\n<li>Cost of Sales ledger - note&#160;3006586</li>\r\n<li>Consolidation preparation - Closing Operations&#160;- note 2999249</li>\r\n</ul>\r\n<p>For all other special purpose ledgers no actions are required.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023098)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D002766)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Special_Ledger_Status_Overview.pdf", "FileSize": "8", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900000225352021&iv_version=0001&iv_guid=00109B36D5921EDBB3D682B0F3B020E5"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3006586", "RefComponent": "FI-SL-SL", "RefTitle": "S4TWL - Cost of Sales Ledger", "RefUrl": "/notes/3006586"}, {"RefNumber": "2999249", "RefComponent": "EC-CS", "RefTitle": "S4TWL - Consolidation and Preparation for Consolidation", "RefUrl": "/notes/2999249"}, {"RefNumber": "2993220", "RefComponent": "EC-PCA", "RefTitle": "S4TWL - EC-PCA - Classical profit center accounting", "RefUrl": "/notes/2993220"}, {"RefNumber": "2269324", "RefComponent": "XX-SER-REL", "RefTitle": "Compatibility Scope Matrix for SAP S/4HANA", "RefUrl": "/notes/2269324"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3373215", "RefComponent": "FI-GL", "RefTitle": "S4TWL - Use of ALE for document distribution in Financials", "RefUrl": "/notes/3373215 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}