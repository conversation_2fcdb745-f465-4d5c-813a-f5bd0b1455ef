{"Request": {"Number": "1876296", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 789, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000011090862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001876296?language=E&token=D2D7A1A0BF784560F4A55E9F2BE08F18"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001876296", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001876296/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1876296"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.07.2013"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AP-AP-B1"}, "SAPComponentKeyText": {"_label": "Component", "value": "Payment transfer (w/o DE,  US)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Accounts Payable", "value": "FI-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AP-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Payment transfer (w/o DE, US)", "value": "FI-AP-AP-B1", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP-B1*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1876296 - CBR-PT: Portugal CBR Development"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Legal Change for Portugal Central Bank Reporting(CBR)</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Portugal, PT, CBR, Central Bank Reporting, Bank of Portugal, BoP</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Legal Change for Portugal Central Bank Reporting(CBR)<br />The Portuguese Central Bank-Bank of Portugal (BoP) requests all the financial and non-financial entities in Portugal to deliver a report for all the international transactions.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Steps:<br />1:Carry out the manual activities attached in the document IMG_PT_CBR_pre_implementation_steps_updated.zip before implementing the note. Download the document from the link:<br />https://sapmats-de.sap-ag.de/download/download.cgi?id=O6HSSY6C0YV7EOZBIHJ58ECSAZYE5FPA3UH7VJ9K9L0JP9OFH8<br /><br />2. Carry out the manual activities attached in the pdf \"PT_CBR_Class_Creation_Steps.pdf\" (Only 46C release)<br /><br />3: Implement the correction instruction attached to the note.<br /><br />4: Carry out the manual activities in the pdf document IMG_PT_CBR_Post_imple_steps.pdf\" attached to the note after implementing the correction instructions.<br />-------------------------------------------------------------<br /> Limitations of the solution:<br /><br /> 1.If a single payment document has multiple bank accounts from which the payment are made, then there is no link to decide which bank is to be reported in the file for the current transaction.<br /> 2.The cases of g/l payments and g/l postings through the bank account cannot be handled as there is no information to decide if the payment is to/from a foreign business partner or not.<br /><br />However, if you have such cases, they can be handled in the user exit method 'PROCESS_OTHER_PAYMENTS' under the BADI 'BADI_RFFOPT_CBR'.<br />For more information, refer to the document 'exit_implementation.docx' attached to the note.<br /><br /><br />To speed up the process of implementation, we are providing SAR file for the CBR development.<br /></p> <b>Steps to be followed while using SAR file:</b><br /> <p>1. Use the SAR file for your SPECIFIC release to carry out the pre implementation steps.(The names of the SAR files refer to the corresponding release)<br />2. Download the \"IMG_PT_CBR_pre_implementation_steps_updated.pdf\" file.<br />3. Create the message class FIAPPT_CBR (Page 131 of pre implementation document).<br />4. Create the Transformation FIAPPT_CBR_XML_TRANS (Page 131 of pre implementation document).<br />Note: This step is not valid for release 46C.<br />5. Create the Function Group FIAPPT_CBR (Page 91 of pre implementation document)<br />6. Create the view FIAPPTV_BAL_SCB (Page 100 of pre implementation document)<br />7. Create the transaction code FIAPPT_BAL for the view FIAPPTV_BAL_SCB (Page 105 of pre implementation document)<br />8. Check if all the BADI methods along with its parameters are present in your system. If not create the same. (Page 134, 138 of pre implementation document)<br />9. Implement the correction instruction attached to the note.<br />10. Carry out the manual activities in the document \"IMGPTCBR_Post_implement_steps.pdf\" attached to the note.<br /></p> <b>Solution for the release 46C has the following features:</b><br /> <p>1. The output is shown in an ALV for the transactions and balances<br />2. Due to technical limitations the XML file would not be generated.<br />3. Due to the different functionality the selection screen is also different. Hence certain steps in the manual activities may not be applicable.<br />For e.g.: When creating the \"text elements\" of the report RFFOPT_CBR, few fields will not be a part of the report.<br /><br /><br />Below are the points which should be taken care while applying the SAR file:<br />Risk and Restrictions inherent in Transport Files<br />-------------------------------------------------<br />If you use a Transport (SAR) file instead of installing the appropriate Support Package or CLC Package, please note the following:<br />1) Read carefully SAP Note 1318389 , where conditions and risks of using Transport files are explained in detail.<br />2) There are no updates to Transport files when any object in them are modified. Objects contained in Transport files may become obsolete without warning.<br />3) Transport files are not valid once their content is available via Support Packages or CLC Packages. The changes may then be installed only via the Packages.<br />4) Text objects are provided in the language in which they were created. Translation is available only via the Packages.<br />5) Changes to the SAP Easy Access menu and Implementation Guide (IMG) are provided only via the Packages.<br /><br />Caution: Handling Transport files<br />----------------------------------<br />1) When you apply more than one Transport file, the order of implementation must be followed as indicated. A wrong sequence will cause transports to fail.<br />2) Once a Transport file has been installed, future installations of Support Packages (or CLC Packages for the HR components modified by the Transport file) must include the Packages that delivered the changes contained in the Transport file. Otherwise objects may be replaced by older versions.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-PT (Portugal)"}, {"Key": "MaxIM Note", "Value": "Yes"}, {"Key": "Responsible                                                                                         ", "Value": "Celine A (I072139)"}, {"Key": "Processor                                                                                           ", "Value": "I075573"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001876296/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001876296/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001876296/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001876296/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001876296/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001876296/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001876296/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001876296/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001876296/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "exit_implementation.ZIP", "FileSize": "7", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000449732013&iv_version=0004&iv_guid=83A259252489854F83FA6A6620720944"}, {"FileName": "SAR_File.zip", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000449732013&iv_version=0004&iv_guid=66E149891AB02140B3F2301F7FDA061C"}, {"FileName": "PT_CBR_Class_Creation_Steps.pdf", "FileSize": "954", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000449732013&iv_version=0004&iv_guid=2413B69A7B3F3C4D902320B68E75D9E3"}, {"FileName": "P9C_SAR_File.zip", "FileSize": "42", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000449732013&iv_version=0004&iv_guid=F12D5B70A6F2FB46BC09F8F66426A3E4"}, {"FileName": "RFFOPT_CBR(Report_Documentation).pdf", "FileSize": "133", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000449732013&iv_version=0004&iv_guid=3A41AD6AE26FD749B9B6708BC4FF986B"}, {"FileName": "IMG_PT_CBR_Post_imple_steps.pdf", "FileSize": "852", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000449732013&iv_version=0004&iv_guid=5494E7B3A4882442ACFA7AFA8CAD5D8A"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1824438", "RefComponent": "FI-AP-AP-B1", "RefTitle": "PT:CBR: Central Bank Reporting Information note", "RefUrl": "/notes/1824438"}, {"RefNumber": "1791853", "RefComponent": "FI-AP-AP-B1", "RefTitle": "Portugal Central Bank Reporting(CBR): 5 digit SCB Indicator", "RefUrl": "/notes/1791853"}, {"RefNumber": "1781847", "RefComponent": "XX-CSC-PT", "RefTitle": "LC: Bank of Portugal - Monthly reporting COPE", "RefUrl": "/notes/1781847"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1824438", "RefComponent": "FI-AP-AP-B1", "RefTitle": "PT:CBR: Central Bank Reporting Information note", "RefUrl": "/notes/1824438 "}, {"RefNumber": "1781847", "RefComponent": "XX-CSC-PT", "RefTitle": "LC: Bank of Portugal - Monthly reporting COPE", "RefUrl": "/notes/1781847 "}, {"RefNumber": "1791853", "RefComponent": "FI-AP-AP-B1", "RefTitle": "Portugal Central Bank Reporting(CBR): 5 digit SCB Indicator", "RefUrl": "/notes/1791853 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C66", "URL": "/supportpackage/SAPKH46C66"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47040", "URL": "/supportpackage/SAPKH47040"}, {"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50029", "URL": "/supportpackage/SAPKH50029"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 3, "URL": "/corrins/0001876296/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 46C&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH46C65&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 470&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH47039&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH50001 - SAPKH50028&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>1. Carry out the manual activities attached in the document \"IMG_PT_CBR_pre_implementation_steps.pdf\" before implementing the  automatic correction instructions.<br/>Download the document from the below link: https://sapmats-de.sap-ag.de/download/download.cgi?id=O6HSSY6C0YV7EOZBIHJ58ECSAZYE5FPA3UH7VJ9K9L0JP9OFH8<br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_APPL&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application&nbsp;&nbsp; |<br/>| Release 46C&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH46C65&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 470&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPKH47039&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKH50001 - SAPKH50028&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>1.After implementing the automatic correction instructions, carry out  the manual activities as described in the attached document \"IMG_PT_CBR_Post_imple_steps.pdf\"<br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 2, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "470", "Number": "1876296 ", "URL": "/notes/1876296 ", "Title": "CBR-PT: Portugal CBR Development", "Component": "FI-AP-AP-B1"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "1876296 ", "URL": "/notes/1876296 ", "Title": "CBR-PT: Portugal CBR Development", "Component": "FI-AP-AP-B1"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}