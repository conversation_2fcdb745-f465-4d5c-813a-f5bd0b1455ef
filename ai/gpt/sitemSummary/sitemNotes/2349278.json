{"Request": {"Number": "2349278", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 257, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018376062017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=560EA9619322BC618035E639C302B265"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2349278"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.08.2023"}, "SAPComponentKey": {"_label": "Component", "value": "CO-PA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Profitability Analysis"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Profitability Analysis", "value": "CO-PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2349278 - S4TWL - Profitability Analysis"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p dir=\"ltr\">You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Account-based CO-PA Margin Analysis S/4HANA S/4 HANA Migration Profitability Analysis</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Task: Check if Profitability Analysis is activated in your system.</p>\r\n<p>Procedure: Go to implementation guide (Transaction SPRO) --&gt; Controlling --&gt; Profitability Analysis --&gt; Flows of Actual Values --&gt; Activate Profitability Analysis. If there is an activation flag in the columns &#8220;costing-based&#8221; or &#8220;margin analysis&#8221; this note is relevant for you.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Options for Profitability Analysis in SAP S/4HANA on-premise edition</strong></p>\r\n<p>The SAP S/4HANA on-premise edition allows for two different profitability analysis solutions:</p>\r\n<ul>\r\n<li>Margin Analysis<br /> Profitability Analysis within the Universal Journal addressing the needs of internal and external accounting, supporting real-time derivation of reporting dimensions and embedded analytics. Designed for use in combination with event-based revenue recognition.</li>\r\n<li>Costing-based CO-PA<br /> Groups revenues and costs according to value fields and applies costing-based valuation approaches. Data is recorded in a dedicated persistency apart from external accounting. Designed for use in combination with results analysis.</li>\r\n</ul>\r\n<p>SAP recommends using Margin Analysis in SAP S/4HANA. Margin Analysis has been extended broadly over the past releases and we continue to conduct new developments in this area only. It is perfectly integrated into the overall concept of the Universal Journal and is therefore fully in line with our Financial Accounting development strategy. Margin Analysis is active if there is an activation flag for 'margin analysis' in the IMG procedure mentioned above.</p>\r\n<p>In addition to Margin Analysis, SAP also offers Costing-based CO-PA. Before the conversion to SAP S/4HANA on-premise, SAP recommends that you verify whether Margin Analysis can already cover all requirements and costing-based CO-PA can remain de-activated.</p>\r\n<p dir=\"ltr\"><strong>Business Value</strong></p>\r\n<p dir=\"ltr\">The universal journal (ACDOCA) is the heart of Accounting and includes all Margin Analysis characteristics in order to allow multi-dimensional reporting by market segment. The universal journal includes columns for the standard characteristics and all additional characteristics included in an organization&#8217;s operating concern (up to sixty characteristics, including the fixed characteristics). If you have already configured an operating concern for Margin Analysis, columns will be created for each of the characteristics during migration.</p>\r\n<p dir=\"ltr\">The benefit of this approach is that all revenue and cost of goods sold postings are automatically assigned to the relevant characteristics <strong>at the time of posting</strong> and that allocations and settlements are made to the characteristics at period close. It is also possible to configure some expense postings such that the characteristics are derived automatically, so that material expenses assigned to a WBS element (account asssignment) are automatically assigned to the characteristics entered as settlement receivers for the WBS element. This automatic derivation of the characteristics can reduce the number of settlements and allocations to be performed at period close and provide <strong>real-time visibility</strong> into spending during the period. If you use <strong>event-based revenue recognition</strong>, the journal entries for the recognized revenue and/or recognized COGS will also be assigned to the characteristics, so that you can report on the revenue adjustments without the need to settle. You will still need to settle if you work with <strong>results analysis</strong>&#160;for long-running projects and orders to move the calculated values to Margin Analysis at period close.</p>\r\n<p dir=\"ltr\">There are also <strong>no reconciliation</strong> issues between the general ledger and Margin Analysis. Be aware, however, that revenue is recognized at the time of invoicing and cost of goods sold when the delivery is made. If there is a time gap between delivery and invoic&#237;ng, costs may be visible in Margin Analysis for which no revenue has yet been posted (matching principle). From SAP S/4HANA 2021 you can use event-based revenue recognition to recognize revenue with the outbound delivery or to delay the COGS posting until the contract is completed.</p>\r\n<p dir=\"ltr\"><strong>Business Process related information</strong></p>\r\n<p dir=\"ltr\">If you currently work with costing-based CO-PA, you can continue to run both approaches in parallel, by making the appropriate settings per controlling area. The essential difference is that costing-based CO-PA is a <strong>key figure</strong> based model, rather than an <strong>account</strong>-based model. Margin Analysis covers record types F (billing documents), B (direct FI postings), C (settlement) and D (assessment) in costing-based CO-PA. A different approach is taken for record type A (incoming sales orders) where predictive accounting documents are created to represent the sales order prior to delivery and invoicing in a make-to-stock environment. Predictive accounting does not yet support record type I (order-related projects) in a make-to-order environment. In the case of record type C (settlement), you may be able to use a different approach for revenue recognition, if you switch to the event-based approach.</p>\r\n<p dir=\"ltr\">There is <strong>no direct migration</strong> from costing-based CO-PA to Margin Analysis. Please bear in mind the following general principles as you consider a conversion. These principles may result in a change in the valuation approach and the value flows compared to systems working only with costing-based CO-PA:</p>\r\n<ul>\r\n<li>Revenues and sales deductions are included in Margin Analysis whenever the price conditions are mapped to an <strong>account </strong>and result in postings to Financial Accounting. Statistical price conditions are&#160;included from SAP S/4HANA 1809 (or 1808 cloud edition).</li>\r\n</ul>\r\n<ul>\r\n<li>Cost of goods sold (COGS) postings are assigned to an account/cost element at the time of the goods issue from the warehouse. New functions are available to split the COGS posting to multiple accounts in accordance with the relative weight of the assigned cost components. This split can be updated with the results of actual costing from SAP S/4HANA 1809. Functions to support the matching principle between revenues and COGS are available. In sell-from-stock scenarios the COGS valuation is based on the material's valuation price at the time of the goods issue. In drop-shipment scenarios the COGS valuation is based on the amount in the supplier invoice.</li>\r\n</ul>\r\n<ul>\r\n<li>Production order variances can be split to multiple accounts to separate scrap, price variances, quantity variances, and so on.</li>\r\n</ul>\r\n<ul>\r\n<li>Assessment cycles and settlement will update under secondary cost elements. Depending on how many value fields you used before, you may need to define additional cost elements and adjust your cycles/settlement profiles to give you the required detail.</li>\r\n</ul>\r\n<ul>\r\n<li>You may be able to replace some assessment cycles and settlements by having the system derive the characteristics at the time of posting instead of moving them from the cost center, order or project at period close.<br /><br /></li>\r\n<li>If you work with event-based revenue recognition instead of results analysis, you will no longer need to settle to move the values to Profitability Analysis.</li>\r\n</ul>\r\n<ul>\r\n<li>Top-down distribution works in Margin Analysis, but you must include the account/cost element in the selection criteria. Reference data for the distribution must be account-based.</li>\r\n</ul>\r\n<ul>\r\n<li>New fields are included in the universal journal for the invoice quantities. BADIs can be used to convert e.g. the quantity from the invoice to a base unit of measure and from there to a reporting unit. Note that only three additional quantity fields are supported, though convertible quantities (e.g. grams to kilograms) can be handled in the same field.</li>\r\n<li>\r\n<p>It is not possible to summarize G/L fields such as profit center, partner profit center, etc, and account assignments such as WBS element and internal order.</p>\r\n</li>\r\n<li>In many processes the system automatically sets an account assignment to a profitability segment. The availability of this account assignment may prevent the system from deriving default account assignments e. g. from OKB9 or may change the behavior by following the substitutions defined in OKC9. Please refer to note 2516083 for further information.</li>\r\n<li>Variances for production orders are inherently available within Margin Analysis (optionally including a split into variance categories; see above), without any further configuration in the settlement profile.</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\r\n</td>\r\n<td>\r\n<p>All transactions available, but some transactions (e.g. KE27) only <br />support costing-based CO-PA.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p dir=\"ltr\"><strong>Required and Recommended Action(s)</strong></p>\r\n<p dir=\"ltr\">Analyze existing reporting requirements to understand how market segment reporting is handled today. Also include transfer of profitability information to other systems, including data warehouses and consolidation. During conversion, the system will create columns in the universal journal for all characteristics in the operating concern.</p>\r\n<p dir=\"ltr\">If costing-based CO-PA is currently used, analyze what workarounds are being used to reconcile the profit and loss statement with costing-based CO-PA to determine whether these will be required in the future. Check whether the functions available with Margin Analysis can already satisfy your reporting requirements or whether you will continue to need costing-based CO-PA for an interim period. If combined Profitability Analysis is currently used, check whether the functions available with Margin Analysis can already satisfy your requirements.</p>\r\n<p dir=\"ltr\">Existing reporting transactions, such as KE30 and KE24, will continue to work, but also investigate possible usage of the Fiori apps to display information by market segment, including additional currencies and data from the extension ledgers.</p>\r\n<p dir=\"ltr\"><strong>Creation of Profitability Segments and Account Determination for COGS</strong></p>\r\n<p dir=\"ltr\">If you only used costing-based CO-PA but no Margin Analysis before the conversion you need to understand when a profitability segment is created in the sales process and how COGS are posted during the delivery. In Margin Analysis the billing and the delivery are recorded as <strong>separate</strong> documents and both journal entries are assigned to a profitability segment. This means a change to include the assignment to a profitability segment in the COGS posting and the need to change the account determination to select an account/cost element.</p>\r\n<ul>\r\n<li>The profitability segment is always derived for the sales order item and referenced during the delivery. Since the delivery document is now assigned to a profitability segment, you will need to ensure that you are updating an account of type P (primary costs and revenues) rather than an account of type N (non-operating expenses)&#160;. Check the account determination for transaction GBB (Offsetting Entry for Inventory Posting) and account modification constants VAX and VAY. COGS posted before the conversion are posted without a cost element (type N), but after the conversion with a cost element (type P). To avoid issues, assign an account of type N to account modification VAX and an account of type P to account modification VAY (Margin Analysis works with VAY as a default).&#160;</li>\r\n<li>If you only worked with costing-based CO-PA and did not use incoming sales orders (record type A) prior to the migration, you will have open sales order items and delivery documents with no assignment to a profitability segment, even though this is required once Margin Analysis is active and the system will issue a message that the assignment to a profitability segment is missing if&#160;you reschedule the sales order item or post a goods issue after the migration.&#160; You can use transaction KE4F to derive the missing profitability segments for a small numbers of sales order items.&#160; For larger volumes of data, run the transaction in parallel over multiple packets of different sales orders (see SAP Note 2225831).&#160;</li>\r\n<li>Where sales orders are partially delivered at the time of conversion, it is recommended to cancel the deliveries and create new ones as this will result in the profitability segment being selected with reference to the sales order during delivery. However, this solution is not practical where goods movements have already been posted with reference to the delivery.&#160; The COGS for these goods movements will not be visible in Margin Analysis.&#160;</li>\r\n<li>If you chooose to use the same G/L account for account modification constants VAX and VAY, you should also cancel all deliveries posted prior to the conversion and create new ones to ensure that the assignment to the profitability segment is correct.&#160;</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D002766)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D025751)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2805090", "RefComponent": "CO-PA", "RefTitle": "Changed display options for types of profitability analysis", "RefUrl": "/notes/2805090"}, {"RefNumber": "2413524", "RefComponent": "CO-PA-ACT", "RefTitle": "S/4 HANA Finance: Additional unit of measures not displayed", "RefUrl": "/notes/2413524"}, {"RefNumber": "2225831", "RefComponent": "CO-PA-ACT", "RefTitle": "Activation of incoming sales orders or account-based PA in case of large sales order volumes", "RefUrl": "/notes/2225831"}, {"RefNumber": "1972819", "RefComponent": "CO-OM", "RefTitle": "Setup SAP BPC optimized for S/4 HANA Finance and Embedded BW Reporting (aka Integrated Business Planning for Finance)", "RefUrl": "/notes/1972819"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3373215", "RefComponent": "FI-GL", "RefTitle": "S4TWL - Use of ALE for document distribution in Financials", "RefUrl": "/notes/3373215 "}, {"RefNumber": "2805090", "RefComponent": "CO-PA", "RefTitle": "Changed display options for types of profitability analysis", "RefUrl": "/notes/2805090 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}