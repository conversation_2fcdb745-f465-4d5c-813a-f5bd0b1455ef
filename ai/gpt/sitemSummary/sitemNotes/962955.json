{"Request": {"Number": "962955", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 247, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016126722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000962955?language=E&token=3DA171D294BD3DD6E1DF2AD9B9706391"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000962955", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000962955/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "962955"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.10.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CST-NI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Network Interface"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client/Server Technology", "value": "BC-CST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Network Interface", "value": "BC-CST-NI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CST-NI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "962955 - Use of virtual or logical TCP/IP host names"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to use one or more virtual or logical host names for SAP software within an SAP server landscape to hide the physical network identities for the pieces of software from each other. This may be useful when moving SAP servers or complete server landscapes to other new hardware within a short time frame without having to carry out a reinstallation or complicated reconfiguration. This procedure is also used in failover cluster solutions, for example.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Failover cluster, system copy, system cloning, DMZ</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You require a new IP address for each virtual or logical host name. The steps described below apply only to SAP server software based on SAP NetWeaver.</p>\r\n<p>Note that the use of virtualized or logical host names described here is independent of the use of completely virtualized server environments (such as VMWare and XEN).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>To decouple the SAP servers securely and permanently from their physical network identities (TCP host name + IP address), you require a new network identity. If required, you can move this with the SAP server software and its configuration to new hardware. This new network identity must consist of a new host name AND a new IP address in order to complete the decoupling from the physical server. <br />We recommend that you avoid using TCP alias names within a server infrastructure (see below). Since this new network identity will no longer be bound to the physical hardware, it is generally referred to as a \"virtual\" or \"logical\" network identity. In the SAP Solution Manager, it is also called the \"logical host name\".<br /><br />Caution: The corresponding steps of this procedure that are required for Microsoft Windows are described in detail by Note 1282975. For IBM i (OS/400),the relevant steps are contained in Note 1624061.<br /><br /><br />Procedure:<br />===============</p>\r\n<ol>1. Set up a new IP address on the SAP server. In general, this can be done via ifconfig (Unix).</ol><ol>2. On UNIX, additional IP addresses for network adapters are addressed for the adapter, for example, via eth0:1 or lan0:1. For more details, refer to the ifconfig Manual Pages.</ol><ol>3. Register a new host name for the IP address on the DNS servers or in the /etc/hosts file.</ol><ol>Example:</ol><ol>&lt;ip1&gt; physhost.domain. corp physhost</ol><ol>&lt;ip2&gt; virthost1.domain. corp virthost</ol><ol>If you only use the hosts file, you must enter the information on all servers that are to use the new virtual host.</ol><ol>You can test the IP address &lt;-&gt; TCP/IP host name resolution using the SAP tool niping with the following syntax:</ol><ol>niping -v -H &lt;virthost&gt;. In the output, you will find the resolutions TCP/IP host name -&gt; IP address (NiHostToAddr) and IP address -&gt; TCP/IP host name (NiAddrToHost) of the local host name and of the transferred host name &lt;virthost&gt;. The result for NiAddrToHost for the IP address of &lt;virthost&gt; must correspond again to the transferred host name &lt;virthost&gt;. SAP Note 799428 describes where you can find the latest version of niping.</ol><ol>4. Next, install or configure your SAP server using the new \"virtual\" or \"logical\" network identity. Information about this is available in the installation guide at https://support.sap.com/sltoolset -&gt; System Provisioning.</ol><ol>To ensure that SAP processes within an SAP instance use an alternative network identity of this kind, the following profile parameters must be included in the start and instance profile of the instance in question. The installation of SAP systems on the basis of SAP NetWeaver 7.1 and above with the Software Provisioning Manager already does this if you assign virtual host names to the relevant SAP instance using the \"Host Name\" input field of the corresponding &lt;Instance Name&gt; instance dialog. You can use this type of assignment as an alternative to manual assignment with the SAPinst option SAPINST_USE_HOSTNAME:</ol><ol>SAPLOCALHOST = &lt;virtual host name&gt;</ol><ol>SAPLOCALHOSTFULL = &lt; fully qualified virtual host name&gt;</ol><ol>Note: For SAP NetWeaver versions lower than 7.01, the parameter SAPLOCALHOSTFULL is not automatically added during the installation and must be manually set in the relevant instance profile after the installation. For ABAP Platform 2020 (SAP_BASIS 7.55/SAP kernel 7.81) and later releases, SAP recommends to remove the parameter&#160;SAPLOCALHOSTFULL from the profiles and instead set the parameters SAPLOCALHOST and SAPFQDN accordingly;&#160;also see&#160;SAP note 2979460.</ol>\r\n<p>All SAP processes within the SAP instance that require SAP profiles to start will then use the virtual network identity for future communication.</p>\r\n<p><br /><br />Additional information:<br /><br />Do not use any TCP network alias names to virtualize physical host names within the intra-server communication. This is due to the host name and IP address resolution that is sometimes executed repeatedly by server applications, and which usually results in the actual physical host name instead of the virtual name. Refer to Note 129997.<br /><br />However, network aliases can normally be used to decouple end-user communication from the physical server infrastructure (for example, aliases for connections between a user and an ABAP application server via HTTP WebServer or SAP GUI).</p>\r\n<p>Please note that the hostname as configured via the parameter SAPLOCALHOST/SAPLOCALHOSTFULL is used to identify the SAP server instance that&#160;runs on the host.&#160;This means that the SAP server presents itself on this host and can be accessed via the hostname in question. However, for an outgoing connection initiated from the SAP server, the IP address used by the outgoing connection is determined by the operating system by default, independent of the configured hostname via the above parameter. This is the default and recommended behavior. Under certain circumstances, for example, due to firewall restrictions, you may need to use a specific IP address for outgoing connections. In this case, you can use the parameters as documented in note 2157220 and the referenced notes to configure the IP address for outgoing connections, either globally via is/local_addr for all kernel components or individually via the parameter for the specific kernel component, for example, gw/local_addr for gateway. Please refer to the relevant note for the minimum kernel release and patch level that supports the parameter.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-INS (Installation Tools (SAP Note 1669327))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I823274)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I823279)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000962955/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962955/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962955/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962955/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962955/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962955/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962955/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962955/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000962955/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "969660", "RefComponent": "BC-INS-RMP", "RefTitle": "6.40 Patch Collection Installation : Unix", "RefUrl": "/notes/969660"}, {"RefNumber": "824722", "RefComponent": "BC-CST-GW", "RefTitle": "Gateway parameter: gw/local_addr", "RefUrl": "/notes/824722"}, {"RefNumber": "786608", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/786608"}, {"RefNumber": "785927", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Software on UNIX: OS Dependencies 6.40 SR1", "RefUrl": "/notes/785927"}, {"RefNumber": "785925", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Web AS 6.40 SR1 ABAP Installation on UNIX", "RefUrl": "/notes/785925"}, {"RefNumber": "785888", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/785888"}, {"RefNumber": "785850", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Web AS 6.40 SR1 Java Installation on UNIX", "RefUrl": "/notes/785850"}, {"RefNumber": "773830", "RefComponent": "BC-CST-IC", "RefTitle": "FQHN determination in ICM", "RefUrl": "/notes/773830"}, {"RefNumber": "611361", "RefComponent": "BC-NET", "RefTitle": "Hostnames of SAP ABAP Platform servers", "RefUrl": "/notes/611361"}, {"RefNumber": "360515", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/360515"}, {"RefNumber": "2979460", "RefComponent": "BC-CST-NI", "RefTitle": "AS ABAP: Default value of profile parameter SAPLOCALHOSTFULL changed with ABAP Platform 2020.", "RefUrl": "/notes/2979460"}, {"RefNumber": "2157220", "RefComponent": "BC-CST", "RefTitle": "Kernel parameter is/local_addr", "RefUrl": "/notes/2157220"}, {"RefNumber": "1858920", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Diagnostics Agent installation with Software Provisioning Manager", "RefUrl": "/notes/1858920"}, {"RefNumber": "1829024", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1829024"}, {"RefNumber": "1776801", "RefComponent": "BC-INS", "RefTitle": "Inst.Systems Based on NW 7.0 incl. EHPs:IBM i", "RefUrl": "/notes/1776801"}, {"RefNumber": "1762956", "RefComponent": "BC-CST-IC", "RefTitle": "Binding outbound connections to a network interface", "RefUrl": "/notes/1762956"}, {"RefNumber": "1748985", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1748985"}, {"RefNumber": "1718413", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP Systems Based on NW 7.0 incl. EHPs: Windows", "RefUrl": "/notes/1718413"}, {"RefNumber": "1714491", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1714491"}, {"RefNumber": "1710950", "RefComponent": "BC-INS-NT", "RefTitle": "Inst. SAP Systems Based on NW 7.1 and higher: Windows", "RefUrl": "/notes/1710950"}, {"RefNumber": "1704753", "RefComponent": "BC-INS-UNX", "RefTitle": "Inst.Systems Based on NetWeaver on UNIX  - Using Software Provisioning Manager 1.0", "RefUrl": "/notes/1704753"}, {"RefNumber": "1680045", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 (recommended: SWPM 1.0 SP40)", "RefUrl": "/notes/1680045"}, {"RefNumber": "1624061", "RefComponent": "BC-CST-NI", "RefTitle": "Use of virtual TCP/IP host names on IBM i (OS/400)", "RefUrl": "/notes/1624061"}, {"RefNumber": "129997", "RefComponent": "BC-NET", "RefTitle": "Hostname and IP address lookup", "RefUrl": "/notes/129997"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3216549", "RefComponent": "BC-CST", "RefTitle": "Hostname standards for SAP Java stack systems", "RefUrl": "/notes/3216549 "}, {"RefNumber": "3078909", "RefComponent": "SV-FRN-INF-SSI", "RefTitle": "SSI: Unable to find a physical or virtual host for host '<host>' in LMDB namespace '<namespace>' - FRUN", "RefUrl": "/notes/3078909 "}, {"RefNumber": "2676550", "RefComponent": "BC-INS-NT", "RefTitle": "Issue during start of sapinst with \"You can use SAPINST_GUI_HOSTNAME to override the webserver hostname for the GUI \" using Software Provisioning Manager Tool", "RefUrl": "/notes/2676550 "}, {"RefNumber": "3035329", "RefComponent": "SV-SMG-DIA-WLY", "RefTitle": "The Wily EM hostname cannot be resolved by the agent running on <hostname>", "RefUrl": "/notes/3035329 "}, {"RefNumber": "3008199", "RefComponent": "BC-OP-NT", "RefTitle": "SAPMMC shows physical node name instead of virtrual hostname in windows failover cluster environment", "RefUrl": "/notes/3008199 "}, {"RefNumber": "2168178", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Error occurs in upgrade job RUN_FDC4UPG_PREPROC: A2EESCRR :136 Cannot read server list", "RefUrl": "/notes/2168178 "}, {"RefNumber": "2783905", "RefComponent": "BC-UPG-DTM-TLA", "RefTitle": "Error in SUM phase RUN_RSCPFROM_CRR: \"RFC_COMMUNICATION_FAILURE\"", "RefUrl": "/notes/2783905 "}, {"RefNumber": "2425142", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "Software Update Manager (SUM) Error in the step: CHECK-DETECTED-OR-REQUEST-MISSING-INSTANCE-PREFIXES", "RefUrl": "/notes/2425142 "}, {"RefNumber": "2718300", "RefComponent": "BC-NET", "RefTitle": "Physical and Virtual hostname length limitations", "RefUrl": "/notes/2718300 "}, {"RefNumber": "2646514", "RefComponent": "BC-CST-STS", "RefTitle": "System fails at startup with error \"DpMsAttach: incorrect value for SAPLOCALHOST\"", "RefUrl": "/notes/2646514 "}, {"RefNumber": "2591258", "RefComponent": "BC-INS-SUI", "RefTitle": "ERROR in Selftest. Return code: 3 during start of SWPM Tool (sapinst)", "RefUrl": "/notes/2591258 "}, {"RefNumber": "2193394", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "INPUT-OS-USER-PASSWORDS: \" com.sap.sdt.tools.sapcontrolws.SapSystemInstance.getInstanceNumber() of a null object loaded from local variable 'instance'\"", "RefUrl": "/notes/2193394 "}, {"RefNumber": "1851156", "RefComponent": "SV-SMG-DIA-SRV-AGT", "RefTitle": "Diagnostics Agent installation: Unique IP address for every single virtual/logical hostname required? - Solution Manager 7.1 & 7.2", "RefUrl": "/notes/1851156 "}, {"RefNumber": "2442943", "RefComponent": "SV-SMG-DIA", "RefTitle": "Logical Host List empty while connecting Diagnostic Agent on the Fly - SAP Solution Manager 7.1 & 7.2", "RefUrl": "/notes/2442943 "}, {"RefNumber": "2926115", "RefComponent": "SV-SMG-DIA-SRV-AGT", "RefTitle": "Unable to Trust Diagnostics Agent", "RefUrl": "/notes/2926115 "}, {"RefNumber": "2979460", "RefComponent": "BC-CST-NI", "RefTitle": "AS ABAP: Default value of profile parameter SAPLOCALHOSTFULL changed with ABAP Platform 2020.", "RefUrl": "/notes/2979460 "}, {"RefNumber": "2120226", "RefComponent": "HAN-LM-INS-SAP", "RefTitle": "SAP HANA client software and entries in hdbuserstore when using virtual host names for  SAP application servers", "RefUrl": "/notes/2120226 "}, {"RefNumber": "2025885", "RefComponent": "BC-OP-AIX", "RefTitle": "AIX: Use of virtual TCP/IP host names", "RefUrl": "/notes/2025885 "}, {"RefNumber": "1858920", "RefComponent": "SV-SMG-INS-AGT", "RefTitle": "Diagnostics Agent installation with Software Provisioning Manager", "RefUrl": "/notes/1858920 "}, {"RefNumber": "972262", "RefComponent": "BC-INS-UNX", "RefTitle": "OBSOLETE: Inst.NW 7.0(2004s)SR2/Business Suite 2005 SR2-UNIX", "RefUrl": "/notes/972262 "}, {"RefNumber": "969660", "RefComponent": "BC-INS-RMP", "RefTitle": "6.40 Patch Collection Installation : Unix", "RefUrl": "/notes/969660 "}, {"RefNumber": "966416", "RefComponent": "BC-INS-UNX", "RefTitle": "OBSOLETE: Inst. SAP NetWeaver based on Kernel 7.10 - UNIX", "RefUrl": "/notes/966416 "}, {"RefNumber": "1762956", "RefComponent": "BC-CST-IC", "RefTitle": "Binding outbound connections to a network interface", "RefUrl": "/notes/1762956 "}, {"RefNumber": "785850", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Web AS 6.40 SR1 Java Installation on UNIX", "RefUrl": "/notes/785850 "}, {"RefNumber": "785925", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Web AS 6.40 SR1 ABAP Installation on UNIX", "RefUrl": "/notes/785925 "}, {"RefNumber": "785927", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Software on UNIX: OS Dependencies 6.40 SR1", "RefUrl": "/notes/785927 "}, {"RefNumber": "1624061", "RefComponent": "BC-CST-NI", "RefTitle": "Use of virtual TCP/IP host names on IBM i (OS/400)", "RefUrl": "/notes/1624061 "}, {"RefNumber": "773830", "RefComponent": "BC-CST-IC", "RefTitle": "FQHN determination in ICM", "RefUrl": "/notes/773830 "}, {"RefNumber": "129997", "RefComponent": "BC-NET", "RefTitle": "Hostname and IP address lookup", "RefUrl": "/notes/129997 "}, {"RefNumber": "611361", "RefComponent": "BC-NET", "RefTitle": "Hostnames of SAP ABAP Platform servers", "RefUrl": "/notes/611361 "}, {"RefNumber": "824722", "RefComponent": "BC-CST-GW", "RefTitle": "Gateway parameter: gw/local_addr", "RefUrl": "/notes/824722 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}