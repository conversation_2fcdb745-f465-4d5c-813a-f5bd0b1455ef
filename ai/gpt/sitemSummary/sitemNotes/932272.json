{"Request": {"Number": "932272", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 332, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001126672020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=2BF030C8960F7F08D770295A88107E1D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "932272"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.07.2020"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "932272 - FAQ: CSA reporting in G/L / discontinuation of FI-SL ledger 0F"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>FAQ: Discontinuation of FI-SL ledger 0F through introduction of CSA reporting in G/L</p>\r\n<p><br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Migration, General Ledger Accounting (new), SAP S/4HANA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Procedure questions</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In classic General Ledger Accounting, the functional area is always managed and reported only in FI-SL.</p>\r\n<p>In General Ledger Accounting (new), the functional area can be managed in the G/L as long as corresponding scenarios for the update of the functional area in the G/L were used since the first posting or, during the migration, the procedure for the correct implementation of the functional area in the G/L is taken into account. Another condition is that the functional area in CO and from postings of real-time integration was always used accordingly.</p>\r\n<p>&#x00A0;</p>\r\n<p>In the context of the General Ledger Accounting (new) migration, SAP Notes 1154791 and 1070629 must be taken into account before the migration key date.</p>\r\n<p>For example, if the migration key date was January 1, 2010 (the first day of the new fiscal year), the functional area derivation needed to have been adjusted and tested with regard to the new substitution time in autumn 2009.</p>\r\n<p>At the latest on January 1, 2010, at 00:00 or on the key date of the fiscal year change, the settings for the update of the functional area in the totals record table of CO, the derivation time for the functional area in the entry view, and the new functional area substitution - event 6 - had to be active so that the first posting to the new fiscal year could take this account assignment into account. Unfortunately, there is no time-dependent setting possibility here.</p>\r\n<p>&#x00A0;</p>\r\n<p>In other words, as of the first posting in the new year: </p>\r\n<p>These indicators had to be active in the table FAGL_ACTIVEC for the derivation and persistence of the functional area in: 1. FAREA_FROM_COBL. This is the only way for the functional area to be derived at the time of document entry, to be known in the FI document, and to be consistent and clear with regard to the CO posting and to the FI-SL posting. This indicator brings about the use of substitution event 6 instead of event 5, as previously the case.</p>\r\n<p>These indicators had to be active in the table FAGL_ACTIVEC for the derivation and persistence of the functional area in: 2. AREA_IN_CO_SUM. This is the only way that the CO totals records could ensure in follow-on processes (such as real-time integration after an allocation, and so on) that the causing functional area was transferred.</p>\r\n<p>These indicators had to be active in the table FAGL_ACTIVEC for the derivation and persistence of the functional area in 3. FAGL_ACTIVEC-FAREA_Obligatory (optional). This enabled the validation that the functional area account assignment took place completely in AC-INT postings and, for example, the substitution at event 6 also subsequently supplemented the functional area not transmitted from CO account assignments and so on later on.</p>\r\n<p>This customizing is therefore also effective if classic General Ledger Accounting is still active. This setting is required despite the use of classic General Ledger Accounting in order to enable the posting data for a migration with a clean update of the functional area in General Ledger Accounting (new).</p>\r\n<p>&#x00A0;</p>\r\n<p>All of this customizing had to be present in the production system on January 1, 2010 (the migration key date in the example). This is the only way in which the migration could migrate the functional area to General Ledger Accounting (new) for documents of the new year.</p>\r\n<p>&#x00A0;</p>\r\n<p>In phase 1 of the General Ledger Accounting (new) migration, so while classic General Ledger Accounting is still being used, the real-time integration of General Ledger Accounting (new) can already be customized instead of the reconciliation ledger and used for CO postings of the new year. Customizing for real-time integration is available in the IMG either as described in SAP Note 1070629 using the report RFAGL_SWAP_IMG_SEW or by direct access using transaction FAGLCOFIIMG.</p>\r\n<p>&#x00A0;</p>\r\n<p>Only if all postings of the new year:</p>\r\n<p>&#x2022; contain the functional area in CO and;</p>\r\n<p>&#x2022; only if all postings from CO use this functional area via a suitable variant of real-time integration;</p>\r\n<p>&#x2022; can - once the General Ledger Accounting (new) migration has finished and, during the migration, the scenario FIN_UKV for the update of the functional area in the General Ledger Accounting (new) ledger to be introduced has been explicitly assigned -;</p>\r\n<p>functional area reporting take place in the G/L for times after the migration key date.</p>\r\n<p>&#x00A0;</p>\r\n<p>If not all postings of the migration year were updated in CO with the functional area, or if the derivation time of the functional area was not customized correctly before the first posting, the functional area reporting in the FI-SL ledger 0F should still be used for a transition period.</p>\r\n<p>In contrast to G/L account assignments such as the profit center, business area, or segment, the functional area of the cost of sales accounting procedure has the advantage that it is not carried forward in the balance carryforward. The scenario for the update of the functional area of the cost of sales accounting procedure can therefore be activated in General Ledger Accounting (new) by the subsequent assignment of FIN_UKV and full customizing of substitution event 6 without a migration. In the current year, follow-on processes such as negative postings will still have incomplete account assignments, so you still choose CSA reporting in FI-SL for a transition period of 1-2 years.</p>\r\n<p>FI-SL has the decisive advantage that the postings can be transferred selectively and, if necessary, manual adjustment postings in FI-SL are possible without affecting FI and CO processes.</p>\r\n<p>With regard to an S/4 migration, the use of CSA reporting is dependent on the customizing of the previous system during the execution of relevant postings. If classic General Ledger Accounting or General Ledger Accounting (new) without CSA reporting was active in the G/L, CSA reporting cannot be used in S/4 for the years in question, either. Thus, after an S/4 migration, CSA reporting would initially need to be done in the FI-SL ledger 0F - like for General Ledger Accounting (new) without a (complete) update of the functional area in the G/L. Once the correct derivation of a functional area from CO account assignment objects, master data, and the new substitution event 6 (compared with classic General Ledger Accounting) has been ensured, CSA reporting should still take place in FI-SL for a transition period of at least one further year. Only once the incomplete account assignments in Accounting have been eliminated by new postings with current persistence in the G/L and CO can CSA reporting take place in the G/L of SAP S/4HANA in subsequent years, since these follow-on years are not influenced by balance carryforward values in the functional area.</p>\r\n<p>In order to enable the complete assignment of allocations and postings from CO in S/4, SAP Note 2680760 introduces the processing of functional area derivation for company code clearing lines. This is particularly important if - as makes sense for cross-company-code postings - the company code clearing lines are P&amp;L accounts (and are defined in the corresponding new account determination for cross-company-code CO processes).</p>\r\n<p>Advantage of using CSA reporting in FI-SL:</p>\r\n<p>As of SAP Note 2165793, SAP S/4HANA COFI postings are also transferred to FI-SL. However, in SL, you can exclude individual documents or complete transactions from the update. Postings of the FI-SL ledger 0F can be (selectively) deleted and transferred again by means of a subsequent posting. For the new derivation of functional area account assignments, you can define a field movement exit. Alternatively, you can use transaction GB01 to make the necessary manual adjustment postings. What is important is that local adjustments in FI-SL do not influence the accounting of ACDOCA.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CO-OM (Overhead Cost Controlling)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028660)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D033549)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3006586", "RefComponent": "FI-SL-SL", "RefTitle": "S4TWL - Cost of Sales Ledger", "RefUrl": "/notes/3006586 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}