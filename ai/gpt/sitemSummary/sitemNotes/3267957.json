{"Request": {"Number": "3267957", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 707, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001499452022"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=E42A1DDE08ECC8B169C750806302FFFB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3267957"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.05.2023"}, "SAPComponentKey": {"_label": "Component", "value": "IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "Utilities"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Utilities", "value": "IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3267957 - Natural Gas and Heat Emergency Aid Act - EWSG"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Due to the tensions and distortions in the energy market, the German Government is enacting new laws on very short notice to support end consumers with their energy expenses, in particular for gas and district heating. Suppliers in the German utilities market play a key role in these measures. This SAP Note provides information about the current status regarding implementation in the standard IS-U function, provides recommendations for the implementation, and will be updated.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>IS-U, gas price cap, EWSG</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The Act on Emergency Aid to Final Consumers of Piped Natural Gas and Customers of Heat (Natural Gas and Heat Emergency Aid Act - EWSG) has entered into force. Suppliers in the German utilities market must adapt their IT systems, in particular SAP IS-U, to the new legal requirements. This SAP Note describes the scope of the further developments for IS-U and provides information about the implementation to be performed by SAP customers.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Change on 11/22/2022: Update of the document with a description of the solution (Version 0.2, draft stage) in the attachment to this SAP Note, SAP Note text updated<br />Change on 11/24/2022: The delivery of the solution has begun, SAP Notes for the first part of the solution are now available. All SAP Notes refer to this SAP Note, so they can be found under the references below. Further SAP Notes will follow.<br />Change on 11/29/2022: Update of the document attached (v0.4)<br />Change on 12/5/2022: Update of the document attached (v1.0)<br />Change on 12/6/2022: Update of the document attached (v1.1)<br />Change on 12/13/2022: Update of the document attached (v1.2)<br />Change on 1/16/2023: Update of the document attached (v1.3)<br />Change on 2/17/2023: Update of the document attached (v1.4)<br />Change on 5/17/2023: Update of the document attached (v1.5)</p>\r\n<p>----------------------------------------------------------------------------------------------------------------</p>\r\n<p>The measures of the federal government consist of two stages:</p>\r\n<ol>\r\n<li>Suspension of December discount for gas and heat according to the EWSG (Natural Gas and Heat Emergency Aid Act) This stage is in technical implementation at SAP.</li>\r\n<li>Price cap (for 80% of consumption ...): This second stage becomes relevant in 2023. There is still no reliable information (no passed law), so no statements are possible yet.</li>\r\n</ol>\r\n<p><span style=\"font-size: 14px;\">The EWSG stipulates that, in simplified form, the Federal Government is to take over the December discount for final consumers of gas and district heating. We at SAP are analyzing the resulting requirements for SAP IS-U, are working on an implementation strategy, and are already going into implementation. The delivery of enhancements in IS-U takes the form of SAP Notes, which we will include here in this SAP Note as reference as soon as they are available.</span></p>\r\n<p><span style=\"font-size: 14px;\">The following functions are to be taken into account for the first level:</span></p>\r\n<ul>\r\n<li><span style=\"font-size: 14px;\">Selection and management of the contracts for which the law applies&#x00A0;</span></li>\r\n<li><span style=\"font-size: 14px;\">Suspension of the December discount</span></li>\r\n<li><span style=\"font-size: 14px;\">Determination of the relevant annual consumption quantities (from September)&#x00A0;</span></li>\r\n<li><span style=\"font-size: 14px;\">Calculation of the credit amount (billing simulation based on 1/12 of the annual consumption quantity and the prices on December&#x00A0;1 ...)</span></li>\r\n<li><span style=\"font-size: 14px;\">Provision of data for the request for advance payment to utility companies</span></li>\r\n<li><span style=\"font-size: 14px;\">Credit memo for the credit amounts to the customer contract accounts</span></li>\r\n<li><span style=\"font-size: 14px;\">...</span></li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D044535)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D044535)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_IS-U_EWSG_v1.5.pdf", "FileSize": "1337", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900001602262022&iv_version=0017&iv_guid=00109B36D58A1EDDBD8DC871455014F9"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3303158", "RefComponent": "IS-U", "RefTitle": "Quick help: Termination of RE_EPB_WORK_REPROCESS during second reprocessing", "RefUrl": "/notes/3303158 "}, {"RefNumber": "3296923", "RefComponent": "IS-U", "RefTitle": "Correction of quick help for contracts of EPB_WORK* tables that have already been processed", "RefUrl": "/notes/3296923 "}, {"RefNumber": "3294518", "RefComponent": "IS-U-BI", "RefTitle": "Gaspreisbremse: Storno des Abrechnungsbelegs für die Soforthilfe bricht ab", "RefUrl": "/notes/3294518 "}, {"RefNumber": "3291914", "RefComponent": "IS-U", "RefTitle": "Price cap: Correction of request report RECA_ANTRAG_VORBEREITEN (application log)", "RefUrl": "/notes/3291914 "}, {"RefNumber": "3289426", "RefComponent": "IS-U", "RefTitle": "Energy Price Cap: Interface enhancement of BAdI method for delivery category", "RefUrl": "/notes/3289426 "}, {"RefNumber": "3289287", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Selection enhancement for the report RE_EPB_REPORTING_FILE_CREATE", "RefUrl": "/notes/3289287 "}, {"RefNumber": "3288850", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Runtime error MESSAGE_TYPE_X of report ISU_EPB_SIMULATION", "RefUrl": "/notes/3288850 "}, {"RefNumber": "3284768", "RefComponent": "IS-U", "RefTitle": "Price Brake - Change Requests", "RefUrl": "/notes/3284768 "}, {"RefNumber": "3275205", "RefComponent": "IS-U-BI", "RefTitle": "Determination of credit amount for move-out in simulation period", "RefUrl": "/notes/3275205 "}, {"RefNumber": "3286153", "RefComponent": "IS-U-BI", "RefTitle": "Emergency aid for heating does not find September budget billing amount", "RefUrl": "/notes/3286153 "}, {"RefNumber": "3286046", "RefComponent": "IS-U", "RefTitle": "Price cap: Report RE_EPB_WORK_FILL does not take contract exclusions into account", "RefUrl": "/notes/3286046 "}, {"RefNumber": "3285720", "RefComponent": "IS-U-BI", "RefTitle": "No EWSG immediate aid for tax exemption", "RefUrl": "/notes/3285720 "}, {"RefNumber": "3285027", "RefComponent": "IS-U", "RefTitle": "Incorrect annual quantity for heating in reimbursement application", "RefUrl": "/notes/3285027 "}, {"RefNumber": "3284951", "RefComponent": "IS-U", "RefTitle": "Budget billing processing status prevents request of credit amounts", "RefUrl": "/notes/3284951 "}, {"RefNumber": "3284211", "RefComponent": "IS-U", "RefTitle": "Price cap: Error in payment lot program RECA_ZAHLSTAPEL_VERTRAG", "RefUrl": "/notes/3284211 "}, {"RefNumber": "3282669", "RefComponent": "IS-U-IN-BB", "RefTitle": "Updating the work table if several budget billing plans exist for the same contract in the adjustment period", "RefUrl": "/notes/3282669 "}, {"RefNumber": "3282424", "RefComponent": "IS-U-IN-BB", "RefTitle": "Update of work table if no budget billing plan exists", "RefUrl": "/notes/3282424 "}, {"RefNumber": "3282458", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Output of Determination Procedures", "RefUrl": "/notes/3282458 "}, {"RefNumber": "3282299", "RefComponent": "IS-U", "RefTitle": "Price cap - New field in EPB_WORK for amount determination procedure", "RefUrl": "/notes/3282299 "}, {"RefNumber": "3281958", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Corrections and enhancements for the report ISU_EPB_SIMULATION", "RefUrl": "/notes/3281958 "}, {"RefNumber": "3281853", "RefComponent": "IS-U-BI", "RefTitle": "Program ISU_EPB_SIMULATION: Error during amount determination for rate category with backbilling/final billing", "RefUrl": "/notes/3281853 "}, {"RefNumber": "3281218", "RefComponent": "IS-U", "RefTitle": "Price cap: Report for determining contracts withdrawn retroactively", "RefUrl": "/notes/3281218 "}, {"RefNumber": "3280464", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Discrepancies in payment request file", "RefUrl": "/notes/3280464 "}, {"RefNumber": "3279921", "RefComponent": "IS-U", "RefTitle": "Energy price cap: No budget billing amount was determined for the heating division.", "RefUrl": "/notes/3279921 "}, {"RefNumber": "3279875", "RefComponent": "IS-U", "RefTitle": "Implementation of energy price caps in SAP IS-U", "RefUrl": "/notes/3279875 "}, {"RefNumber": "3276561", "RefComponent": "IS-U-IN-BB", "RefTitle": "Suspension of December discount and update of worktable for multiple contracts", "RefUrl": "/notes/3276561 "}, {"RefNumber": "3279290", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Dump CX_SY_ZERODIVIDE of LEPREISBREMSEF02", "RefUrl": "/notes/3279290 "}, {"RefNumber": "3279010", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Incorrect calculation of credit amount for heating division", "RefUrl": "/notes/3279010 "}, {"RefNumber": "3278477", "RefComponent": "IS-U", "RefTitle": "Energy price cap: ALV Grid display for report ISU_EPB_SIMULATION", "RefUrl": "/notes/3278477 "}, {"RefNumber": "3277956", "RefComponent": "IS-U", "RefTitle": "Energy price cap: The output for new fields in the work table EPB_WORK", "RefUrl": "/notes/3277956 "}, {"RefNumber": "3277955", "RefComponent": "IS-U", "RefTitle": "Energy price cap: New BAdI method for selecting profile roles", "RefUrl": "/notes/3277955 "}, {"RefNumber": "3277640", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Troubleshooting for report ISU_EPB_SIMULATION", "RefUrl": "/notes/3277640 "}, {"RefNumber": "3274827", "RefComponent": "IS-U-BI", "RefTitle": "Energy price cap: Error during implementation of SAP Note 3273011", "RefUrl": "/notes/3274827 "}, {"RefNumber": "3277342", "RefComponent": "IS-U", "RefTitle": "Energy price cap: account determination adjustment", "RefUrl": "/notes/3277342 "}, {"RefNumber": "3277340", "RefComponent": "IS-U", "RefTitle": "Energy price cap: payment lot", "RefUrl": "/notes/3277340 "}, {"RefNumber": "3276800", "RefComponent": "IS-U-MD", "RefTitle": "Gaspreis: Report RE_EPB_REQUEST_MANUAL_CHANGE für manuelle Änderungen von Anträgen auf Entlastungsbeträge", "RefUrl": "/notes/3276800 "}, {"RefNumber": "3276746", "RefComponent": "IS-U", "RefTitle": "Price cap: Additional Fields Table EBP_WORK", "RefUrl": "/notes/3276746 "}, {"RefNumber": "3274724", "RefComponent": "IS-U", "RefTitle": "Gas price cap - processing option for work table", "RefUrl": "/notes/3274724 "}, {"RefNumber": "3276319", "RefComponent": "IS-U", "RefTitle": "Price cap: Report RE_EPB_WORK_FILL - additional requirements", "RefUrl": "/notes/3276319 "}, {"RefNumber": "3276043", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Lock object", "RefUrl": "/notes/3276043 "}, {"RefNumber": "3275706", "RefComponent": "IS-U-BI", "RefTitle": "Energy price brake: Incorrect budget billing amount selected.", "RefUrl": "/notes/3275706 "}, {"RefNumber": "3276027", "RefComponent": "IS-U-BI", "RefTitle": "Termination in ISU_EPB_SIMULATION during amount determination", "RefUrl": "/notes/3276027 "}, {"RefNumber": "3274662", "RefComponent": "IS-U-MD", "RefTitle": "Gas Price Cap - Provision of a request table and basic functions", "RefUrl": "/notes/3274662 "}, {"RefNumber": "3275203", "RefComponent": "IS-U", "RefTitle": "Energy price cap: Application for credit amounts", "RefUrl": "/notes/3275203 "}, {"RefNumber": "3275236", "RefComponent": "IS-U-IN-BB", "RefTitle": "Customer-specific checks for suspension of December budget billing amount", "RefUrl": "/notes/3275236 "}, {"RefNumber": "3269209", "RefComponent": "IS-U-BI", "RefTitle": "New BAdI for Manipulation of Quantity to Be Billed", "RefUrl": "/notes/3269209 "}, {"RefNumber": "3273011", "RefComponent": "IS-U-BI", "RefTitle": "Report for determining the annual consumption quantity and the credit amount", "RefUrl": "/notes/3273011 "}, {"RefNumber": "3269231", "RefComponent": "IS-U-IN-BB", "RefTitle": "Report for Suspension of December Discount", "RefUrl": "/notes/3269231 "}, {"RefNumber": "3269168", "RefComponent": "IS-U-MD", "RefTitle": "Gas Price Cap - Provision of working table and basic functions", "RefUrl": "/notes/3269168 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-UT", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "807", "To": "807", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}