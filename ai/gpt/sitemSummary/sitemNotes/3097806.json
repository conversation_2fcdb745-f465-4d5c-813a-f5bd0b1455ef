{"Request": {"Number": "3097806", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 455, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001380012021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003097806?language=E&token=10206019276153F6017E520E7E79263D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003097806", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3097806"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.10.2021"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-MGM-PRC-IMD"}, "SAPComponentKeyText": {"_label": "Component", "value": "IMDS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP EHS Management", "value": "EHS-MGM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Compliance Management", "value": "EHS-MGM-PRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-PRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IMDS", "value": "EHS-MGM-PRC-IMD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-PRC-IMD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3097806 - IMDS Import Creates Duplicate Supplier Components"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n\r\n<p>You are using the&#160;Product Compliance&#160;functionality.</p>\r\n<p>You download and import supplier Material Data Sheets (MDS) from International Material Data System (IMDS). This step does not assign any supplier component to the MDS in IMDS Supplier Center. Afterwards you create the supplier component in your system (i.e. by executing a BOM Transfer). Now you start the import at the MDS record in IMDS Supplier Center. The system creates an additional supplier component from the data of the MDS records instead of assigning and updating the existing supplier component.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n\r\n<p>OBJECT_MATCH, CMSAll, CMSDaily, EHPRC_CPM_BOMBOS 073, EHPC_CPM_BOMBOS 096, EHPRC_CPI04, EHPRC_CPI03, R_EHPRC_IMP_APPL, R_EHPRC_IMP_APPL_BATCH</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n\r\n<p>The symptom is caused by a program error.</p>\r\n<p>Prerequisites can be found in the relevant correction instructions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n\r\n<p>Implement this SAP Note or import either of the corresponding Support Packages for the component extension of SAP EHS Management or the Feature Packages Stacks for SAP S/4HANA.</p>\r\n\r\n<p>The import of the MDS record again tries to find the compliance data object of the component by IMDS Node ID, material, supplier and manufacturer part number (MPN). The import stops without assigning any compliance data object or specification, if the component cannot be identified uniquely. Assign the correct supplier component manually and restart the import. Following messages are reported:</p>\r\n<ul>\r\n<li>No compliance data object has been found at all: \"<em>No compliance data object found for selection ID = &lt;IMDS Module ID&gt;</em>\"</li>\r\n<li>Multiple compliance data objects have been found: \"<em>More than one compliance object created for material <em>&lt;IMDS Module ID&gt;</em></em>\"</li>\r\n</ul>\r\n<p>BOM Transfer prefers any component used by the IMDS import, if there are multiple components with the correct information on material, supplier and MPN. The redundant components are removed from the product structure to be excluded from compliance analysis. The existing functionality on unused components takes care of these redundant components.</p>\r\n<p><span style=\"text-decoration: underline;\">Note:</span> Check and adjust your custom version of exit function EHPRC_CP_IM52S_OBJECT_MATCH. Refer to the correction instructions for details.</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D054665)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D054619)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003097806/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003097806/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003097806/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003097806/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003097806/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003097806/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003097806/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003097806/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003097806/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3013456", "RefComponent": "EHS-MGM-PRC-IMD", "RefTitle": "IMDS v13 - Support For Functional Changes in AI Interface", "RefUrl": "/notes/3013456"}, {"RefNumber": "2841559", "RefComponent": "EHS-MGM-PRC", "RefTitle": "Must-Known Fundamentals on Mass BOM Transfer", "RefUrl": "/notes/2841559"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3383695", "RefComponent": "EHS-MGM-PRC-IMD", "RefTitle": "IMDS Import has an insufficient Performance", "RefUrl": "/notes/3383695 "}, {"RefNumber": "3297244", "RefComponent": "EHS-MGM-PRC", "RefTitle": "IMDS Import Fails With Multiple Objects Assigned To Same MDS", "RefUrl": "/notes/3297244 "}, {"RefNumber": "3278895", "RefComponent": "EHS-MGM-PRC-IMD", "RefTitle": "Must-Known Fundamentals and SAP Notes concerning IMDS", "RefUrl": "/notes/3278895 "}, {"RefNumber": "2147718", "RefComponent": "XX-SER-REL", "RefTitle": "Component Extension 6.0 for SAP EHS Management: RIN", "RefUrl": "/notes/2147718 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10210INS4CORE", "URL": "/supportpackage/SAPK-10210INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10308INS4CORE", "URL": "/supportpackage/SAPK-10308INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10406INS4CORE", "URL": "/supportpackage/SAPK-10406INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 105", "SupportPackage": "SAPK-10504INS4CORE", "URL": "/supportpackage/SAPK-10504INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 106", "SupportPackage": "SAPK-10601INS4CORE", "URL": "/supportpackage/SAPK-10601INS4CORE"}, {"SoftwareComponentVersion": "EHSM 400", "SupportPackage": "SAPK-40007INEHSM", "URL": "/supportpackage/SAPK-40007INEHSM"}, {"SoftwareComponentVersion": "EHSM 500", "SupportPackage": "SAPK-50006INEHSM", "URL": "/supportpackage/SAPK-50006INEHSM"}, {"SoftwareComponentVersion": "EHSM 600", "SupportPackage": "SAPK-60007INEHSM", "URL": "/supportpackage/SAPK-60007INEHSM"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 7, "URL": "/corrins/0003097806/19773"}, {"SoftwareComponent": "EHSM", "NumberOfCorrin": 3, "URL": "/corrins/0003097806/9587"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 10, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 11, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "1955218 ", "URL": "/notes/1955218 ", "Title": "Performance issues with BOMBOS transfer", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2061268 ", "URL": "/notes/2061268 ", "Title": "Missing IMDS data after second run of BOM transfer", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2325787 ", "URL": "/notes/2325787 ", "Title": "Import: Avoid dumps due to messages without message type", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2443585 ", "URL": "/notes/2443585 ", "Title": "Repeat BOM Transfer when locking errors occur", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2892527 ", "URL": "/notes/2892527 ", "Title": "Supplier MDS Refers To Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2954395 ", "URL": "/notes/2954395 ", "Title": "IMDS Import Runtime Error", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2989340 ", "URL": "/notes/2989340 ", "Title": "Supplier MDS Mapped to Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "3013456 ", "URL": "/notes/3013456 ", "Title": "IMDS v13 - Support For Functional Changes in AI Interface", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "3015088 ", "URL": "/notes/3015088 ", "Title": "Analysis Functionality for CMS File Import", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "3087714 ", "URL": "/notes/3087714 ", "Title": "Runtime error when executing the program R_EHPRC_IMDS_CMS_VISUALIZER", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2325787 ", "URL": "/notes/2325787 ", "Title": "Import: Avoid dumps due to messages without message type", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2443585 ", "URL": "/notes/2443585 ", "Title": "Repeat BOM Transfer when locking errors occur", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2892527 ", "URL": "/notes/2892527 ", "Title": "Supplier MDS Refers To Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2954395 ", "URL": "/notes/2954395 ", "Title": "IMDS Import Runtime Error", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2989340 ", "URL": "/notes/2989340 ", "Title": "Supplier MDS Mapped to Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2325787 ", "URL": "/notes/2325787 ", "Title": "Import: Avoid dumps due to messages without message type", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2892527 ", "URL": "/notes/2892527 ", "Title": "Supplier MDS Refers To Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2954395 ", "URL": "/notes/2954395 ", "Title": "IMDS Import Runtime Error", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2989340 ", "URL": "/notes/2989340 ", "Title": "Supplier MDS Mapped to Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "3015088 ", "URL": "/notes/3015088 ", "Title": "Analysis Functionality for CMS File Import", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "3087714 ", "URL": "/notes/3087714 ", "Title": "Runtime error when executing the program R_EHPRC_IMDS_CMS_VISUALIZER", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2325787 ", "URL": "/notes/2325787 ", "Title": "Import: Avoid dumps due to messages without message type", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2892527 ", "URL": "/notes/2892527 ", "Title": "Supplier MDS Refers To Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2954395 ", "URL": "/notes/2954395 ", "Title": "IMDS Import Runtime Error", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2989340 ", "URL": "/notes/2989340 ", "Title": "Supplier MDS Mapped to Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2892527 ", "URL": "/notes/2892527 ", "Title": "Supplier MDS Refers To Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2954395 ", "URL": "/notes/2954395 ", "Title": "IMDS Import Runtime Error", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2989340 ", "URL": "/notes/2989340 ", "Title": "Supplier MDS Mapped to Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "3015088 ", "URL": "/notes/3015088 ", "Title": "Analysis Functionality for CMS File Import", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "3087714 ", "URL": "/notes/3087714 ", "Title": "Runtime error when executing the program R_EHPRC_IMDS_CMS_VISUALIZER", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2541384 ", "URL": "/notes/2541384 ", "Title": "IMDS: Corrections in import of own and external Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2892527 ", "URL": "/notes/2892527 ", "Title": "Supplier MDS Refers To Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2954395 ", "URL": "/notes/2954395 ", "Title": "IMDS Import Runtime Error", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2989340 ", "URL": "/notes/2989340 ", "Title": "Supplier MDS Mapped to Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "3015088 ", "URL": "/notes/3015088 ", "Title": "Analysis Functionality for CMS File Import", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "3087714 ", "URL": "/notes/3087714 ", "Title": "Runtime error when executing the program R_EHPRC_IMDS_CMS_VISUALIZER", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2892527 ", "URL": "/notes/2892527 ", "Title": "Supplier MDS Refers To Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2954395 ", "URL": "/notes/2954395 ", "Title": "IMDS Import Runtime Error", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2989340 ", "URL": "/notes/2989340 ", "Title": "Supplier MDS Mapped to Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "3015088 ", "URL": "/notes/3015088 ", "Title": "Analysis Functionality for CMS File Import", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "3087714 ", "URL": "/notes/3087714 ", "Title": "Runtime error when executing the program R_EHPRC_IMDS_CMS_VISUALIZER", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2892527 ", "URL": "/notes/2892527 ", "Title": "Supplier MDS Refers To Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2954395 ", "URL": "/notes/2954395 ", "Title": "IMDS Import Runtime Error", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2989340 ", "URL": "/notes/2989340 ", "Title": "Supplier MDS Mapped to Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "3015088 ", "URL": "/notes/3015088 ", "Title": "Analysis Functionality for CMS File Import", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "3087714 ", "URL": "/notes/3087714 ", "Title": "Runtime error when executing the program R_EHPRC_IMDS_CMS_VISUALIZER", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "105", "ValidTo": "105", "Number": "2954395 ", "URL": "/notes/2954395 ", "Title": "IMDS Import Runtime Error", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "105", "ValidTo": "105", "Number": "2989340 ", "URL": "/notes/2989340 ", "Title": "Supplier MDS Mapped to Purchased Component", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "105", "ValidTo": "105", "Number": "3015088 ", "URL": "/notes/3015088 ", "Title": "Analysis Functionality for CMS File Import", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "105", "ValidTo": "105", "Number": "3087714 ", "URL": "/notes/3087714 ", "Title": "Runtime error when executing the program R_EHPRC_IMDS_CMS_VISUALIZER", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "106", "ValidTo": "106", "Number": "3087714 ", "URL": "/notes/3087714 ", "Title": "Runtime error when executing the program R_EHPRC_IMDS_CMS_VISUALIZER", "Component": "EHS-MGM-PRC-IMD"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3108488", "RefTitle": "Import Of Supplier MDS Fails", "RefUrl": "/notes/0003108488"}, {"RefNumber": "3199555", "RefTitle": "Cannot Import MDS To Selected Specification", "RefUrl": "/notes/0003199555"}]}}}}}