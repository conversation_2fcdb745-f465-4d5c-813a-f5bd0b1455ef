{"Request": {"Number": "1724704", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 266, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017448272017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001724704?language=E&token=06B479F4A2D01FA3A33A8BC53F828616"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001724704", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1724704"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.11.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SRV-COM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Communication Services: Mail, Fax, SMS, Telephony"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis Services/Communication Interfaces", "value": "BC-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Communication Services: Mail, Fax, SMS, Telephony", "value": "BC-SRV-COM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV-COM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1724704 - SCOT: Settings for TLS and SMTP AUTH"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note concerns the settings for the support of TLS and SMTP AUTH in transaction SCOT.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Previously, this function was not supported.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Import the relevant Support Package and use at least Kernel 7.21.<br /><br />As a result, the SAPconnect SMTP interface supports both sending and receiving via a secure connection (TLS, Transport Layer Security) and a procedure for authenticating on the SMTP server (SMTP AUTH).<br /><br />You can make relevant settings for outgoing messages in transaction SCOT in the relevant SMTP node. This SAP Note contains further information about this.<br /><br />For incoming messages, the settings are carried out using a profile parameter. The related SAP Notes contain more information about this.<br /></p> <b>Information about the settings in the node</b><br /> <p></p> <b>Note that the fields described in the following are displayed in the node only if you use Kernel 7.21 or higher even after you import Support Packages</b><br /> <p><br />After you select a node, you can make security settings in the SMTP connection area using the settings pushbutton. In the following dialog box, specify the circumstances in which the TLS should be used.<br /><br />The selection depends on what the relevant SMTP server supports. We recommend that you choose the safest variant (use of TLS is mandatory, check host name, check server certificate). If errors occur here, first deactivate the check for host name and server certificate because these checks are not always possible in the SMTP world. However, you should use the setting \"Use TLS where possible\" only in exceptional cases because a secure connection cannot be guaranteed with this.<br /><br />In addition, select one of the proposed client identities. These are managed as SSL client identities in transaction STRUST and contain the certificate that the SAP system uses to identify or authenticate itself.<br /><br />If an authentication should be carried out, two procedures are supported:<br /><br />PLAIN - using the logon data specified in the authentication area.<br />EXTERNAL - using the client certificate that is assigned to the selected client identity.<br /><br />The latter is possible only if the client identity is not \"ANONYM\".<br /><br />The procedure that is used depends on what is supported by the SMTP server. If both procedures are possible from the perspective of the server, PLAIN is used if logon data is maintained; otherwise EXTERNAL is used. Other procedures are not supported.<br /><br />We strongly recommend that you do not set up an authentication using logon data unless you are sending data via a secure connection in all cases, that is, the use of the TLS is mandatory. Otherwise, the system issues a relevant warning in the node because logon data may be transferred in an insecure way.<br /><br />The F1 help for the relevant fields in the SAPconnect nodes contains further information.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CST-<PERSON><PERSON> (Internet Communication Manager)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D025574)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D025574)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001724704/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001724704/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001724704/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001724704/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001724704/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001724704/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001724704/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001724704/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001724704/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1747180", "RefComponent": "BC-CST-IC", "RefTitle": "SMTP via TLS and SMTP authentication", "RefUrl": "/notes/1747180"}, {"RefNumber": "1702785", "RefComponent": "BC-CST-IC", "RefTitle": "Error diagnosis for SMTP using TLS and SMTP authentication", "RefUrl": "/notes/1702785"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3064215", "RefComponent": "BC-SRV-COM", "RefTitle": "Error message XS816 'SMTP communication error' for SMTP messages in SOST", "RefUrl": "/notes/3064215 "}, {"RefNumber": "3060528", "RefComponent": "BC-SRV-COM", "RefTitle": "SAP do not support port 465 for sending SSL mail", "RefUrl": "/notes/3060528 "}, {"RefNumber": "3015811", "RefComponent": "BC-SRV-COM", "RefTitle": "Error XS612 \"451 Error starting TLS\" occurred when sending mail via SMTP node", "RefUrl": "/notes/3015811 "}, {"RefNumber": "2718594", "RefComponent": "BC-SRV-COM", "RefTitle": "\"MTA Open Mail Relaying Allowed\" message for SMTP port in SAP system", "RefUrl": "/notes/2718594 "}, {"RefNumber": "2690360", "RefComponent": "BC-SRV-COM", "RefTitle": "XS812 - 550 5.7.60 SMTP; Client does not have permissions to send as this sender", "RefUrl": "/notes/2690360 "}, {"RefNumber": "1950670", "RefComponent": "BC-SRV-COM", "RefTitle": "Error XS856 \"No delivery to &1, authentication required\" occurs while sending mails through SMTP node", "RefUrl": "/notes/1950670 "}, {"RefNumber": "1702632", "RefComponent": "BC-SRV-COM", "RefTitle": "Authentication with user and password while sending mail through SAPconnect using SMTP", "RefUrl": "/notes/1702632 "}, {"RefNumber": "2439601", "RefComponent": "BC-SRV-COM", "RefTitle": "Password length limited to 20 characters - SCOT", "RefUrl": "/notes/2439601 "}, {"RefNumber": "2000465", "RefComponent": "BC-CST-IC", "RefTitle": "Support of AUTH LOGIN as SMTP client", "RefUrl": "/notes/2000465 "}, {"RefNumber": "1747180", "RefComponent": "BC-CST-IC", "RefTitle": "SMTP via TLS and SMTP authentication", "RefUrl": "/notes/1747180 "}, {"RefNumber": "1702785", "RefComponent": "BC-CST-IC", "RefTitle": "Error diagnosis for SMTP using TLS and SMTP authentication", "RefUrl": "/notes/1702785 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73106", "URL": "/supportpackage/SAPKB73106"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74001", "URL": "/supportpackage/SAPKB74001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}