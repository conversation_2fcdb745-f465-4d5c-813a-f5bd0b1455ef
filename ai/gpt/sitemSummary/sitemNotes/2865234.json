{"Request": {"Number": "2865234", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 307, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002102592019"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002865234?language=E&token=1E0EA44D5F2322F65CB4798EC2D9F626"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002865234", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002865234/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2865234"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 21}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.06.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-CEX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Customer Extensions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Customer Extensions", "value": "BC-DWB-CEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-CEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2865234 - SAP S/4HANA custom code checks show different number of findings in different check runs"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You perform the SAP S/4HANA custom code checks. The results of the check runs show different number of findings.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">&#160;SAP S/4HANA custom code checks</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>If you compare results in different ATC (central) check systems, ensure that in both systems the latest version of SAP Notes mentioned in SAP Note \"<em>2436688 - Recommended SAP Notes for using S/4HANA custom code checks in ATC or Custom Code Migration app\" are applied.</em></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The number of findings reported by the SAP S/4HANA custom code checks depend on various aspects in your SAP system. If one of these aspects is changed between the check runs, the number of findings might be different.</p>\r\n<p>The following list describes these aspects:</p>\r\n<ol start=\"1\" style=\"list-style-type: upper-roman;\">\r\n<li>The ATC run series has been changed:</li>\r\n<ol>\r\n<li>The check variant has been changed</li>\r\n<li>The object set has been changed</li>\r\n</ol>\r\n<li>The parameters of the check variant have been changed</li>\r\n<li>You uploaded a new version of the Simplification Database. <br />You can check the current version and the import date by processing the following steps:</li>\r\n<ol>\r\n<li>Start transaction SYCM</li>\r\n<li>Choose \"Simplification Database &gt; Show Information\"</li>\r\n</ol>\r\n<li>You upgraded the systemYou applied support packages</li>\r\n<li>You applied SAP Notes related to SAP S/4HANA custom code checks to your SAP system (refer to SAP Note&#160;2436688)</li>\r\n<li>You made changes to your custom code which might lead</li>\r\n<ul>\r\n<li>(obviously) to fewer findings, if findings of previous check runs have been fixed manually or by applying a quickfix</li>\r\n<li>to more findings, if new code has been added</li>\r\n<li>to more findings, if syntax errors have been fixed (programs with syntax errors show no findings)</li>\r\n<li>to consequential findings, if findings of previous findings have been fixed</li>\r\n</ul>\r\n<li>You upgraded your system and you get no findings related to the checks &#8220;S/4HANA: Field length extension&#8221; and &#8220;S/4HANA: Search for database operations&#8221; after the upgrade.<br />In this case, upload the Simplification Database in your upgraded system, again. Even if the latest version has already been uploaded before the upgrade.</li>\r\n<li>You run the checks against systems with the same custom code by based on different SAP releases (e.g., SAP ERP and SAP S/4HANA). The checks &#8220;S/4HANA: Field length extension&#8221; and&#160;&#8220;Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY&#8221; might show different results in both releases, because the check results also depend on SAP's code.</li>\r\n</ol>\r\n<p><span style=\"font-size: 14px;\">&#160;</span></p>\r\n<p>Furthermore, the custom code checks have been improved over time to suppress false-positive findings. These improvements are listed here:&#8232;</p>\r\n<ul>\r\n<li>SELECT statements related to table VBFA are shown only if the statement contains the addition ORDER BY PRIMARY KEY (see also SAP Note&#160;2738251/2768987)</li>\r\n<li>If you plan to use the material number length with 18 or less characters in SAP S/4HANA, the check&#160;<em>S/4HANA: Field length extension</em> will report fewer findings.&#160;If you do not plan to use other field length extensions (like the extended amount length), the check reports also&#160;fewer findings.<br /><strong>Note:</strong> The check<em> S/4HANA: Field length extension</em>&#160;allows the option to specify the used material number field length and other field length extensions by processing the following steps:</li>\r\n<ol>\r\n<li>Copy check variant S4HANA_READINESS_* you want to use to create your own check variant</li>\r\n<li>Change the parameters of the check&#160;<em>S/4HANA: Field length extension </em>in your own check variant</li>\r\n<li>Use your own check variant to execute the SAP S/4HANA custom code checks</li>\r\n</ol>\r\n<li>Findings related to simplification item \"2438110 - Material Number Field Length Extension: Code Adaptions for usages of RFC enabled function modules\" are only shown if CALL FUNCTION statements use the addition DESTINATION (see also SAP Note&#160;2874255)</li>\r\n<li>Findings related to simplification item&#160;\"2768887 - S4TWL - SD Billing Document Draft\" are only shown if the where clause does not specify the fields VBELN or DRAFT</li>\r\n<li>The checks&#160;<em>S/4HANA: Search for ABAP Dictionary enhancements&#160;</em>and&#160;<em>S/4HANA: Search for base tables of ABAP Dictionary views&#160;</em>show less false-positive findings for some tables and views (see also SAP Note&#160;2771060)</li>\r\n<li>The check&#160;<em>S/4HANA: Field length extensions</em> reports less fale-psotives for&#160;calls to SAP objects with generic parameters&#160;(see also SAP Note 2898180)&#160;</li>\r\n<li>The check&#160;<em>S/4HANA: Field length extensions </em>behaved wrong for multi-purpose fields (see also SAP Note&#160;2971294)&#160;<em><br /></em></li>\r\n<li>In releases SAP S/4HANA 1809 (SAP_BASIS 7.53) or higher, findings related to the simplification item category \"Change of existing functionality with performance impact\" won't be reported any more if you choose any delivered check variant starting with S4HANA_READINESS*.</li>\r\n<li>The check&#160;<em>S/4HANA: Field length extensions&#160;</em>did not consider type conflicts in BAdI calls (see also SAP Note 2364938 (version 67))</li>\r\n<li>The check&#160;<em>S/4HANA: Field length extensions </em>detects usages of extended fields in CONCATENATE statements. A new finding \"CONCATENATE detected\" has been introduced&#160;(see also SAP Note&#160;2364938 (version 68))&#160;</li>\r\n<li>The check&#160;<em>S/4HANA: Field length extensions&#160;</em>does not report findings with message title \"Related Type\" anymore (see also SAP Note 3032974)</li>\r\n<li>The default configuration of the ATC check variants for the SAP S/4HANA custom code checks (check variantsstarting with&#160;S4HANA_READINESS*)&#160;has been changed (see also SAP Note&#160;3057501)</li>\r\n<li>S/4HANA: Search for Usages of Simplified Objects' ATC check returns too many findings if only ABAP Dictionary fields are simplified (see SAP note&#160;3039646)</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D034650)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D033124)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002865234/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002865234/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002865234/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002865234/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002865234/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002865234/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002865234/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002865234/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002865234/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3032974", "RefComponent": "BC-DWB-CEX", "RefTitle": "ATC check 'S/4HANA: Field Length Extension\" reports findings with message type \"Related Type\"", "RefUrl": "/notes/3032974"}, {"RefNumber": "2898180", "RefComponent": "BC-DWB-CEX", "RefTitle": "ATC check 'S/4HANA: Field Length Extension' reports false-positives for calls with generic parameters to SAP objects", "RefUrl": "/notes/2898180"}, {"RefNumber": "2874255", "RefComponent": "BC-DWB-CEX", "RefTitle": "False positives in ATC check 'S/4HANA: Search for usages of simplified objects'", "RefUrl": "/notes/2874255"}, {"RefNumber": "2771060", "RefComponent": "BC-DWB-CEX", "RefTitle": "ATC S/4HANA ABAP Dictionary Checks - Redirection to dedicated items in Simplification Database patch level 8 and higher", "RefUrl": "/notes/2771060"}, {"RefNumber": "2738251", "RefComponent": "BC-DWB-CEX", "RefTitle": "Quick Fixes for the S/4HANA Custom Code Checks", "RefUrl": "/notes/2738251"}, {"RefNumber": "2364938", "RefComponent": "BC-ABA-LA", "RefTitle": "Downport of the infrastructure of the SAP S/4HANA readiness checks", "RefUrl": "/notes/2364938"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2436688", "RefComponent": "BC-DWB-CEX-CCM", "RefTitle": "Recommended SAP Notes for Using S/4HANA Custom Code Checks in ATC or Custom Code Migration App", "RefUrl": "/notes/2436688 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}