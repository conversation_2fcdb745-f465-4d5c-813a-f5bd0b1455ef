{"Request": {"Number": "2267878", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 304, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018243992017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002267878?language=E&token=BA05F4F69A90CC5F86258DE998252570"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002267878", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002267878/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2267878"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.02.2020"}, "SAPComponentKey": {"_label": "Component", "value": "CA-CL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Classification"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Classification", "value": "CA-CL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-CL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2267878 - S4TWL - Classification"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>With SAP S/4HANA, on-premise edition 1511&#160;transactions of the&#160;Classification&#160;are renovated so that transactions may have changed functionality or may be completely removed within&#160;SAP Product Lifecycle Management (SAP PLM).</p>\r\n<ul>\r\n<li>User Defined Data Type&#160;(031)&#160;for characteristics has been removed.</li>\r\n<li>Rename Characteristic functionality has been disabled.</li>\r\n<li>Parameter Effectivity has been hidden.</li>\r\n<li>Due to security reasons batch import has been limited and batch import is possible only from presentation server.</li>\r\n</ul>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>Parameter Effectivity has been hidden for classification in the SAP S/4HANA, on-premise edition 1511 shipment. As an alternative solution, Variant Configuration shall be used with object based dependencies.</p>\r\n<p>For batch input and direct input, use SAP S/4HANA Migration Cockpit.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"414\">\r\n<p><strong>Transaction not available since SAP S/4HANA on-premise edition 1511</strong></p>\r\n</td>\r\n<td valign=\"top\" width=\"414\">\r\n<p><strong>Available alternative transactions and reports</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"414\">\r\n<p>CL20&#160;&#160;&#160;&#160;&#160;&#160; Assign Object to Classes</p>\r\n<p>CL22&#160;&#160;&#160;&#160;&#160;&#160; Allocate Class to Classes</p>\r\n<p>CL24&#160;&#160;&#160;&#160;&#160;&#160; Assign Objects to One Class</p>\r\n<p>CL6E&#160;&#160;&#160;&#160;&#160;&#160; Copy DIN Standard</p>\r\n<p>CL6F&#160;&#160;&#160;&#160;&#160;&#160; Copy DIN Characteristic Data</p>\r\n<p>CL6G&#160;&#160;&#160;&#160;&#160; Create Material w. DIN Char. Data</p>\r\n<p>CL6R&#160;&#160;&#160;&#160;&#160;&#160; Direct Input for Classes</p>\r\n<p>CLB1 &#160;&#160;&#160;&#160;&#160; Batch Input for Classification</p>\r\n<p>CLB2 &#160;&#160;&#160;&#160;&#160; Direct Input for Classification</p>\r\n<p>CLB3 &#160;&#160;&#160;&#160;&#160; Display Classification File</p>\r\n<p>GENC &#160;&#160;&#160;&#160; Generate Source Code</p>\r\n<p>O021 &#160;&#160;&#160;&#160; Class Data Transfer</p>\r\n<p>O023 &#160;&#160;&#160;&#160; Display Class Maintenance Data File</p>\r\n<p>O024 &#160;&#160;&#160;&#160; Transfer Class Data: Direct Input</p>\r\n<p>CLNA&#160;&#160;&#160;&#160; Namespace f. Classes / Characteristics</p>\r\n<p>CT01&#160;&#160;&#160;&#160;&#160;&#160; Create Characteristic</p>\r\n<p>CT02&#160;&#160;&#160;&#160;&#160;&#160; Change Characteristic</p>\r\n<p>CT03&#160;&#160;&#160;&#160;&#160;&#160; Display Characteristic</p>\r\n<p>CT05&#160;&#160;&#160;&#160;&#160;&#160; Create Characteristic</p>\r\n<p>CT06&#160;&#160;&#160;&#160;&#160;&#160; Display Characteristic</p>\r\n<p>CT11&#160;&#160;&#160;&#160;&#160; Where-Used List for Characteristics / Characteristic Values</p>\r\n<p>CT21&#160;&#160;&#160;&#160;&#160; Batch Input for Characteristics</p>\r\n<p>CT22&#160;&#160;&#160;&#160;&#160; Maint. Seq. File for Characteristics</p>\r\n<p>CT23&#160;&#160;&#160;&#160;&#160; Display Chars for Change Number</p>\r\n<p>CT24&#160;&#160;&#160;&#160;&#160; Display Change Numbers Used</p>\r\n<p>CT25&#160;&#160;&#160;&#160;&#160; Where-Used List of Chars in Deps</p>\r\n<p>CTCP&#160;&#160;&#160;&#160;&#160; Copy C tables for characteristics</p>\r\n<p>CL6B&#160;&#160;&#160;&#160;&#160;&#160; Object List</p>\r\n<p>CL6D&#160;&#160;&#160;&#160;&#160; Classes Without Superior Class</p>\r\n<p>CL6M&#160;&#160;&#160;&#160; Delete Class (with Assignments)</p>\r\n<p>CL6O&#160; &#160;&#160;&#160; Plus-Minus Object Display</p>\r\n<p>CL2B&#160;&#160;&#160;&#160;&#160;&#160; Class Types</p>\r\n<p>CL6A&#160;&#160;&#160;&#160;&#160; Class List</p>\r\n<p>CL6Q&#160;&#160;&#160;&#160;&#160; Where-Used List for Classes</p>\r\n<p>CL6T&#160;&#160;&#160;&#160;&#160;&#160; Copy Text for Classes</p>\r\n<p>CLCP&#160;&#160;&#160;&#160;&#160;&#160; Copy Classification C Tables</p>\r\n<p>CL21&#160;&#160;&#160;&#160;&#160;&#160; Display Object in Classes</p>\r\n<p>CL23&#160;&#160;&#160;&#160;&#160;&#160; Display Class for Classes</p>\r\n<p>CL25&#160;&#160;&#160;&#160;&#160;&#160; Display Objects in Class</p>\r\n<p>CL30&#160;&#160;&#160;&#160;&#160;&#160; Find Objects in Classes</p>\r\n</td>\r\n<td valign=\"top\" width=\"414\">\r\n<p>CL20N&#160;&#160;&#160;&#160;&#160;&#160; Assign Object to Classes</p>\r\n<p>CL22N&#160;&#160;&#160;&#160;&#160;&#160; Assign Class to Superior Classes</p>\r\n<p>CL24N&#160;&#160;&#160;&#160;&#160;&#160; Assign Objects / Classes to Class</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>CT04&#160;&#160;&#160;&#160;&#160;&#160; Characteristics</p>\r\n<p>CT04&#160;&#160;&#160;&#160;&#160;&#160; Characteristics</p>\r\n<p>CT04&#160;&#160;&#160;&#160;&#160;&#160; Characteristics</p>\r\n<p>CT04&#160;&#160;&#160;&#160;&#160;&#160; Characteristics</p>\r\n<p>CT04&#160;&#160;&#160;&#160;&#160;&#160; Characteristics</p>\r\n<p>CT12&#160;&#160;&#160;&#160;&#160;&#160; Where-Used List for Characteristics / Characteristic Values</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>CC04&#160;&#160;&#160;&#160;&#160;&#160; Product Structure Browser</p>\r\n<p>CC04&#160;&#160;&#160;&#160;&#160;&#160; Product Structure Browser</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>CL6BN&#160;&#160;&#160;&#160;&#160;&#160; Object List</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>O1CL&#160;&#160;&#160;&#160;&#160;&#160; Class Types</p>\r\n<p>CL6AN&#160;&#160;&#160;&#160; Class List</p>\r\n<p>CC04&#160;&#160;&#160;&#160;&#160;&#160; Product Structure Browser</p>\r\n<p>-</p>\r\n<p>-</p>\r\n<p>CL20N&#160;&#160;&#160;&#160;&#160;&#160; Assign Object to Classes</p>\r\n<p>CL22N&#160;&#160;&#160;&#160;&#160;&#160; Assign Class to Superior Classes</p>\r\n<p>CL24N&#160;&#160;&#160;&#160;&#160;&#160; Assign Objects / Classes to Class</p>\r\n<p>CL30N&#160;&#160;&#160;&#160;&#160;&#160; Find Objects in Classes</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Accessible via Customizing</strong></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>O005 &#160;&#160;&#160;&#160; C CL Characteristic Default Settings</p>\r\n<p>S_ABA_72000139</p>\r\n<p>S_ABA_72000140</p>\r\n<p>S_ALR_87003718 &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O1CL</p>\r\n<p>S_ALR_87003725 &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O008</p>\r\n<p>S_ALR_87003732&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O016</p>\r\n<p>S_ALR_87003741&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O002</p>\r\n<p>S_ALR_87003752&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000SMOD</p>\r\n<p>S_ALR_87003765&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000MERK</p>\r\n<p>S_ALR_87003777&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000KLAS</p>\r\n<p>S_ALR_87003789&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO0002239</p>\r\n<p>S_ALR_87003802&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O12A</p>\r\n<p>S_ALR_87003812&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O003</p>\r\n<p>S_ALR_87003831&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O005</p>\r\n<p>S_ALR_87003842&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O004</p>\r\n<p>S_ALR_87003852&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O017</p>\r\n<p>S_ALR_87003863&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: SIMG_CFMENUO000O006</p>\r\n<p>S_ALR_87009587</p>\r\n<p>S_AX6_42000002&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: BWCLBW_CTBW</p>\r\n<p>S_S6B_86000002&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: CTWUL_LOCDEP</p>\r\n<p>S_S6B_86000003&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; IMG Activity: CTWUL_LOCDEP</p>\r\n<p>S_S6B_86000004</p>\r\n<p>S_S6B_86000005</p>\r\n<p>S_S6B_86000006</p>\r\n<p>S_ABA_72000141</p>\r\n<p>S_ALR_87009588</p>\r\n<p>S_ALR_87011758</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<p>No special actions required.</p>\r\n<p><strong>Related SAP Notes</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"302\">\r\n<p>Custom Code related information</p>\r\n</td>\r\n<td valign=\"top\" width=\"302\">\r\n<p>&#160;SAP note:<span class=\"urTxtStd\" style=\"white-space: nowrap;\">2213569</span></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I035505)"}, {"Key": "Processor                                                                                           ", "Value": "I070736"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002267878/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267878/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267878/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267878/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267878/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267878/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267878/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267878/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267878/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2213569", "RefComponent": "CA-CL", "RefTitle": "SAP S/4 HANA: Recommendations for adaptations of custom code in the classification system", "RefUrl": "/notes/2213569"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2550904", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW: Presentation server file could not be read/found", "RefUrl": "/notes/2550904 "}, {"RefNumber": "2451899", "RefComponent": "CA-CL-CL", "RefTitle": "SYSTEM_ABAP_ACCESS_DENIED dump when accessing some Classification / Variant Configuration Transactions.", "RefUrl": "/notes/2451899 "}, {"RefNumber": "2504458", "RefComponent": "CA-CL", "RefTitle": "\"Application Server\"  option not available when using RCCLBI03", "RefUrl": "/notes/2504458 "}, {"RefNumber": "2897424", "RefComponent": "CA-CL", "RefTitle": "Erroneous entry in blacklist for SAP S/4HANA on premise", "RefUrl": "/notes/2897424 "}, {"RefNumber": "2698659", "RefComponent": "IS-MP", "RefTitle": "S/4HANA for Cable industry - Solution and Implementation facts", "RefUrl": "/notes/2698659 "}, {"RefNumber": "2419382", "RefComponent": "QM-PT-BD", "RefTitle": "Termination SYSTEM_ABAP_ACCESS_DENIED when you call a class characteristic from a master inspection characteristic", "RefUrl": "/notes/2419382 "}, {"RefNumber": "2370481", "RefComponent": "PLM-FIO-CL", "RefTitle": "Restrictions for Classification embedded in Fiori applications", "RefUrl": "/notes/2370481 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}