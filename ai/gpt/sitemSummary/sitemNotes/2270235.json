{"Request": {"Number": "2270235", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 283, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000650722020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002270235?language=E&token=AD8117563D6B19C88A3960A9989452AD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002270235", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002270235/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2270235"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.07.2022"}, "SAPComponentKey": {"_label": "Component", "value": "PP-PI-PMA-PMC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Browser based PI Sheet / Cockpit"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Production Planning and Control", "value": "PP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Production Planning for Process Industries", "value": "PP-PI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP-PI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Process Management", "value": "PP-PI-PMA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP-PI-PMA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Browser based PI Sheet / Cockpit", "value": "PP-PI-PMA-PMC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PP-PI-PMA-PMC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2270235 - S4TWL - Process Operator Cockpits"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>POC; Process Manufacturing Cockpit;&#160;Transactions COPOC, O06C, O06S; Packages&#160;COPOC, CMX_POC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Description</strong></p>\r\n<p>Process Operator Cockpit (POC) is still available in SAP S/4HANA, but not considered as future technology&#160;and therefore part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it&#8217;s expiry date (end of usage right) and links to further information please refer to SAP note <a target=\"_blank\" href=\"/notes/2269324\">2269324 Compatibility Scope Matrix for SAP S/4HANA</a>.&#160;You can find the the respective scope item under item ID&#160;456 in the attached document with Compatibility Scope matrix details.</p>\r\n<p>The Process Operator Cockpits, similar as the browser-based PI Sheets (see note <a target=\"_blank\" href=\"/notes/2268070\">2268070 S4TWL - S4TWL - Browser-based Process Instruction Sheets/Electronic Work Instructions</a>), require SAP-GUI for Windows as UI technology and therefore are dependent on MS Internet Explorer (technical prerequisite for HTML viewer).</p>\r\n<p>SAP Digital Manufacturing Cloud (DMC) will provide similar functionality as Process Operator Cockpits in future. For example, the Production Operator Dashboard (POD), which is based on standard SAP UI5 and SAP Fiori technology, will allow API calls into SAP S/4HANA and other systems. The DMC POD Designer will offer flexible and customer individual configuration of the worker UI. DMC for Insights and DMC Resource Orchestration will provide dashboards and production monitoring based on SAP standard and custom specific manufacturing key figures. Additionally, DMC will provide an extensibility framework to allow customer individual solutions.</p>\r\n<p>For timelines please check the official&#160;<a target=\"_blank\" href=\"https://roadmaps.sap.com/board?PRODUCT=73555000100800001492&amp;range=CURRENT-LAST\">roadmap for SAP Digital Manufacturing Cloud</a>&#160;(https://roadmaps.sap.com).</p>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>It will not be possible to migrate existing process instruction based Process Operator Cockpits (POC) into a future worker UIs in SAP DMC.</p>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<p>You can continue to use your existing cockpits, but not invest anymore into new cockpits. <br /> Consider to evaluate the possibilities of SAP DMC instead.</p>\r\n<p><strong><strong>Custom Code Check / Adaption&#160;</strong>&#160;</strong></p>\r\n<p>Custom enhancements and modifications of POCs in SAP S/4HANA will not work for successor UIs/PODs in SAP DMC.</p>\r\n<p>Due to the&#160;tight integration of Production Operator Cockpit and browser-based Process Instruction Sheets/Electronic Work Instructions (see note&#160;<a target=\"_blank\" href=\"/notes/2268070\">2268070 S4TWL - Browser-based Process Instruction Sheets/Electronic Work Instructions</a>) functionality please perform a combined custom code check.</p>\r\n<p>After switching from process instruction based POC's to a successor solution in SAP DMC Digital Manufacturing Cloud check your custom code in context of POCs which transfer data to whatever receiver and&#160;clean-up your custom code that makes use of POC functionality.&#160;The below (incomplete) list is an entry point for the analysis of your custom code. Make sure that with end of support of the compatibility scope item no data is transferred from a POC with any custom code and that no custom code initiates save of data (create/update) to respective database tables.</p>\r\n<p>Transactions</p>\r\n<ul>\r\n<li>COPOC Process Manufacturing Cockpit</li>\r\n<li>O06C Define Process Manufacturing Cockpit</li>\r\n<li>O06S Define Process Manufacturing Cockpit</li>\r\n</ul>\r\n<p>Database tables&#160;(no save of data through custom code)</p>\r\n<ul>\r\n<li>POC_DB_APPL_STEP Domain Model: Application Steps</li>\r\n<li>POC_DB_ARC_CONN Optical Archiving: Link Data (ArchiveLink)</li>\r\n<li>POC_DB_C_COMMAND Domain Model: Command Collection</li>\r\n<li>POC_DB_C_COMMENT Domain Model: Comment Collection</li>\r\n<li>POC_DB_C_DEVIAT Domain Model: Deviation Collection</li>\r\n<li>POC_DB_C_DEVVAR Domain model: Collection for variable setting (deviation)</li>\r\n<li>POC_DB_C_METADAT Domain Model: Local Value Collection</li>\r\n<li>POC_DB_C_OPER Domain Model: Operation Collection</li>\r\n<li>POC_DB_C_PARTIT Domain Model: Partition Element Collection</li>\r\n<li>POC_DB_C_PREDEC Domain Model: Predecessor Table</li>\r\n<li>POC_DB_C_SERVICE Domain Model: Service Operation Collection</li>\r\n<li>POC_DB_C_STEP Domain Model: Step Collection</li>\r\n<li>POC_DB_C_SYTABLE Domain Model: Variable Collection (Symbol Table)</li>\r\n<li>POC_DB_C_TRIGGER Domain Model: Trigger Collection</li>\r\n<li>POC_DB_C_VALUE Domain Model: Value Container Collection</li>\r\n<li>POC_DB_COMMAND Domain Model: Table for Step Commands</li>\r\n<li>POC_DB_CONF_REQ Domain Model: Table for Signature Requests</li>\r\n<li>POC_DB_DATA_REQ Domain Model: Table for Data Requests</li>\r\n<li>POC_DB_DEV_SIGN Signature Key: Metadata Signature Process Step</li>\r\n<li>POC_DB_DOMAIN Domain Model: Database Table for Domains</li>\r\n<li>POC_DB_EVT_SIGN Signature Keys: Metadata for Event Log</li>\r\n<li>POC_DB_EXEC_REQ Domain Model: Table for Execution Request</li>\r\n<li>POC_DB_EXEC_STRA Domain Model: Table for Execution Strategies</li>\r\n<li>POC_DB_MESS_REQ Domain Model: Table for Message Requests</li>\r\n<li>POC_DB_OPERATION Domain Model: Table for Operations</li>\r\n<li>POC_DB_PARTITION Domain Model: Partition</li>\r\n<li>POC_DB_PROC_STEP Domain Model: Table for Process Steps</li>\r\n<li>POC_DB_PROTOCOL Domain Model: Event Log for Process Steps</li>\r\n<li>POC_DB_REAS_SIGN Signature Key: Meta Data Reason for Value Change</li>\r\n<li>POC_DB_SIG_HANDL Domain Model: Signature Handler</li>\r\n<li>POC_DB_SIGNATURE Domain Model: Signature</li>\r\n<li>POC_DB_STEP Domain Model: Table for Step Basic Data</li>\r\n<li>POC_DB_STEP_SIGN Signature Key: Metadata Signature Process Step</li>\r\n<li>POC_DB_TRIG_PROT Logging: Trigger for Event in Domain Model</li>\r\n<li>POC_DB_VALUE Domain Model: DB Table for Value Container</li>\r\n</ul>\r\n<p>Programs/Reports&#160;(no call or clone in custom code)</p>\r\n<ul>\r\n<li>RCOPOC_START Start Process Manufacturing Cockpit</li>\r\n<li>RCOPOC_COCKPIT_CLIENT_COPY Copy Process Manufacturing Cockpits Between Clients</li>\r\n<li>RCOPOC_COCKPIT_DELETE Delete Process Manufacturing Cockpit</li>\r\n<li>RCOPOC_COCKPIT_GENERATE Generate Process Manufacturing Cockpit</li>\r\n<li>RCOPOC_COCKPIT_PLANT_COPY Copy Process Manufacturing Cockpits Between Plants</li>\r\n<li></li>\r\n</ul>\r\n<p>Function Modules&#160;(no call or clone in custom code)</p>\r\n<ul>\r\n<li>POC_GET_PISHEET_DATA_XML NOTRANSL: Holen des Daten XML f&#252;r Herstellanweisung</li>\r\n<li>POC_PRINT_SHEET_IN_EBR_STYLE NOTRANSL: Anzeige der Herstellanweisung im Druckbildlayout des EBR</li>\r\n<li>POC_SHOW_PMA_CONT_ARCH_LAYOUT Display PI Sheet/Work Instruction in Archiving Layout</li>\r\n</ul>\r\n<p>BAdI's with interface methods&#160;(check for custom implementation)</p>\r\n<ul>\r\n<li>BADI_CMX_POC_ASYNC_SIGN&#160;Handle XML change error</li>\r\n<ul>\r\n<li>IF_BADI_CMX_POC_ASYNC_SIGN&#126;CHANGE_ABORT_MESSAGE</li>\r\n</ul>\r\n<li>BADI_CMX_POC_DYN_SIGN_STRAT&#160;Enhancements for Dynamic Selection of Signature Strategies<br />Interface methods IF_BADI_CMX_POC_DYN_SIGN_STRAT&#126;</li>\r\n<ul>\r\n<li>ADJUST_PI_CHECK_MESSAGE Check Process Instruction: Set Suited Message Texts</li>\r\n<li>CHECK_DYN_SIGN_STRAT_ALLOWED Check that Dynamic Signature Strategy Function is Allowed</li>\r\n<li>EXECUTE_INSTR_SEMANTIC_CHECK Executes Additional Checks during Semantic Check</li>\r\n<li>EXECUTE_XS_INSTR_CONTENT_CHECK XStep-based Process Instruction: Executes Additional Checks</li>\r\n<li>GET_CUST_DOMAIN_SUBCLASS Value Search: Returns Information on Characteristic &amp; Subcl.</li>\r\n<li>HANDLE_FAILED_VALUE_SEARCH Search for Selected Signature Strategy: Error Handling</li>\r\n<li>RESET_STRAT_TO_PLACEHOLDER Reset Signature Strategy to Placeholder Value</li>\r\n</ul>\r\n<li>BADI_CMX_POC_REASON_VAL_CHG&#160;Processing of Reasons for Value Changes<br />Interface methods IF_BADI_CMX_POC_REASON_VAL_CHG&#126;</li>\r\n<ul>\r\n<li>CHECK_COMMENT_OF_REASON Checks the Entered Comment of Reason for Value Change</li>\r\n<li>CHECK_REASON_VAL_CHG_ALLOWED Checks that Processing of Reasons is Allowed</li>\r\n</ul>\r\n</ul>\r\n<p>Enhancement Spots (check for custom implementation)</p>\r\n<ul>\r\n<li>ES_RCOPOC_WKLT</li>\r\n<li>ES_SSAPLCMX_PII_CP_SRV_DST_PMC Enhancement PI Interpreter (Name of Destination)</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025847)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025847)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002270235/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002270235/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002270235/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002270235/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002270235/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002270235/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002270235/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002270235/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002270235/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2999251", "RefComponent": "PP-PI-PMA", "RefTitle": "FAQ: Microsoft Internet Explorer 11 lifecycle", "RefUrl": "/notes/2999251"}, {"RefNumber": "2269324", "RefComponent": "XX-SER-REL", "RefTitle": "Compatibility Scope Matrix for SAP S/4HANA", "RefUrl": "/notes/2269324"}, {"RefNumber": "2268070", "RefComponent": "PP-PI-PMA-PMC", "RefTitle": "S4TWL - Browser-based Process Instruction Sheets/Electronic Work Instructions", "RefUrl": "/notes/2268070"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}