{"Request": {"Number": "765983", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 555, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015744902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000765983?language=E&token=4F8B64225AD307F38F229BF0C2E0DBFD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000765983", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000765983/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "765983"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.08.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB2"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for z/OS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for z/OS", "value": "BC-DB-DB2", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB2*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "765983 - DB2/zOS: Reducing number of DSNACCOR REORG recommendations"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You employ DB2 on z/OS Version 7 and higher, and have installed real time statistics and stored procedure DSNACCOR according to the instructions in SAP Note 507824. When scheduling action \"Online reorg on suggested tablespaces\", a large number of tablespaces is reorganized each time. This behavior can result in interference with production workload.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DB2 MVS OS390 ABAP CC/390 V7 Stored Procedure DSNACCOR SM21 CCMS Monitor RZ20 collecting bootstrap hourly daily 5 minutes DB13 REORG suggested RUNSTATS needing DB12 V8<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Stored procedure DSNACCOR recommends tablespaces for REORG regardless of their size. For small tablespaces, a REORG can be omitted without negative consequences on performance or unnecessary use of space.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Based on customer suggestions, two new thresholds for DSNACCOR were invented that are enforced by SAP alert collectors:<br /></p> <b>Absolute number of changed rows since last REORG</b><br /> <p>This value is the absolute number of inserts, deletes, or updates since the last REORG. It is not used by DSNACCOR itself, but SAP uses this threshold to suppress alerts that are generated on small tables to reduce the number of REORG recommendations.<br />Let me give an example: Imagine a small tablespace with one table containing ten rows. If two rows are changed, a threshold of 20% for changed rows is reached. On the other hand, the absolute number of changes would not make a REORG necessary.<br />The number of INSERTs, UPDATEs, and DELETEs since the last REORG on the tablespace or partition is compared to this threshold. If it is less than this value, a recommendation of DSNACCOR to run REORG on the tablespace is suppressed by SAP.<br />The default for this value is 50. A good value would be 50-300, but heavily depends on your environment and your need to reduce the number of REORG alerts.<br /></p> <b>REORG TS: Number of active pages</b><br /> <p>This value is not used by DSNACCOR itself either, but SAP suppresses alerts that are generated on small table spaces to reduce the number of REORG recommendations.<br />The number of active pages in the tablespace or partition is compared to this threshold. If it is less than this value, a recommendation of DSNACCOR to run REORG on the tablespace is suppressed by SAP.<br />The default is 0, so the system will behave like it has in the past unless you decide to use this function. A good value would be 20-30, but will depend very much on your environment and your need to reduce the number of REORG alerts.<br /><br /><br />Important:</p> <UL><LI>Both thresholds take effect only as long as the recommendation was not given based on the number of unclustered inserts, overflow records or extents or due to advisory REORG status. This is because these types of alerts are a severe problem that should be resolved regardless of tablespace size.</LI></UL> <UL><LI>Both thresholds should not be too high, otherwise fragmentation within small TS resp. possible space savings will remain undetected.</LI></UL> <p><br /></p> <b>Configuring the thresholds DSNACCOR uses</b><br /> <OL>1. Call transaction ST04 or DB2.</OL> <OL>2. Choose Button \"DB Alert Settings\".</OL> <OL>3. Choose Button \"Change\". This enables you to change all the thresholds DSNACCOR and SAP use. Under normal circumstances, the default settings should be sufficient. For more information on each of the values and their roles in the calculation for recommendations, refer to the online help and to the IBM documentation \"DB2 UDB for OS/390 and z/OS V7 Utility Guide and Reference\".</OL> <p><br /></p> <b>Necessary Transports</b><br /> <p>***********************************************************************<br />Make sure that you have properly imported the transports described in Note 101217 into your R/3 Release, in the correct order. Since the note Note 101217 into your R/3 Release, in the correct order. Since the note referred to by Note 101217 is regularly updated, you should check it from time to time for changes or additions. In this way, you can always keep your system at the newest maintenance level. ***********************************************************************<br />Import the right transport for your Release as described in Note 13719:</p> <UL><LI>Release 6.20: SAPK620OCP and subsequent (see note 427748) </LI></UL> <p><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5027389)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D000269)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000765983/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765983/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765983/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765983/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765983/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765983/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765983/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765983/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000765983/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "83335", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/83335"}, {"RefNumber": "776547", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: DB13 upload with stored procedure DSNACCDS", "RefUrl": "/notes/776547"}, {"RefNumber": "717935", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: FTP replacement by Stored Procedures", "RefUrl": "/notes/717935"}, {"RefNumber": "598471", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/598471"}, {"RefNumber": "593469", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Usage of global TEMP tables and related problems", "RefUrl": "/notes/593469"}, {"RefNumber": "551104", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: SAPLS_CCDSNU_DB2 terminates due to a timeout", "RefUrl": "/notes/551104"}, {"RefNumber": "507824", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390 V7: Real Time Statistics and DSNACCOR", "RefUrl": "/notes/507824"}, {"RefNumber": "496904", "RefComponent": "FS-AM", "RefTitle": "Performance notes database parameters FS-AM for DB2 z/OS", "RefUrl": "/notes/496904"}, {"RefNumber": "457512", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: DB2 V7 Features", "RefUrl": "/notes/457512"}, {"RefNumber": "427748", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: CCMS corrections (6.10, 6.20, 6.40, 7.x)", "RefUrl": "/notes/427748"}, {"RefNumber": "388676", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/388676"}, {"RefNumber": "324739", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/324739"}, {"RefNumber": "217093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/217093"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "132424", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Optimizing access to the TRFC Tables", "RefUrl": "/notes/132424"}, {"RefNumber": "116698", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/116698"}, {"RefNumber": "113008", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Maintaining Catalog Statistics", "RefUrl": "/notes/113008"}, {"RefNumber": "101217", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: Overview of transports and corrections", "RefUrl": "/notes/101217"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "427748", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: CCMS corrections (6.10, 6.20, 6.40, 7.x)", "RefUrl": "/notes/427748 "}, {"RefNumber": "496904", "RefComponent": "FS-AM", "RefTitle": "Performance notes database parameters FS-AM for DB2 z/OS", "RefUrl": "/notes/496904 "}, {"RefNumber": "113008", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Maintaining Catalog Statistics", "RefUrl": "/notes/113008 "}, {"RefNumber": "101217", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: Overview of transports and corrections", "RefUrl": "/notes/101217 "}, {"RefNumber": "717935", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: FTP replacement by Stored Procedures", "RefUrl": "/notes/717935 "}, {"RefNumber": "551104", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: SAPLS_CCDSNU_DB2 terminates due to a timeout", "RefUrl": "/notes/551104 "}, {"RefNumber": "593469", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Usage of global TEMP tables and related problems", "RefUrl": "/notes/593469 "}, {"RefNumber": "507824", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390 V7: Real Time Statistics and DSNACCOR", "RefUrl": "/notes/507824 "}, {"RefNumber": "457512", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: DB2 V7 Features", "RefUrl": "/notes/457512 "}, {"RefNumber": "132424", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Optimizing access to the TRFC Tables", "RefUrl": "/notes/132424 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "776547", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/zOS: DB13 upload with stored procedure DSNACCDS", "RefUrl": "/notes/776547 "}, {"RefNumber": "598471", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: CCMS-Korrekturen (7.10)", "RefUrl": "/notes/598471 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "640", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}