{"Request": {"Number": "1393114", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 525, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001393114?language=E&token=76F118F345BE62DC1B0C6AD15D093063"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001393114", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001393114/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1393114"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "09.03.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-FES-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP GUI for Windows"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Frontend Services (SAP Note 1322184)", "value": "BC-FES", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP GUI for Windows", "value": "BC-FES-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-FES-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1393114 - New features in SAP GUI for Windows 7.20"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>What advantages does SAP GUI for Windows 7.20 offer in comparison with<br />the previous release 7.10?</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SAP GUI, SAPGUI, features, new features</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>New features in SAP GUI for Windows 7.20:</p> <UL><LI>Support for Windows 7 operating system</LI></UL> <UL><LI>Support for Office 2010 package (32bit version only)</LI></UL> <UL><LI>Build with Visual Studio 2008 (which is supported until April 2013) - therefore SAP GUI for Windows 7.20 can be supported longer than release 7.10.</LI></UL> <UL><LI>SAP Logon: Hierarchical organization of items.</LI></UL> <UL><LI>SAP Logon: Central storage of .ini files possible.</LI></UL> <UL><LI>Option Dialog: All relevant options have been merged into a single dialog and have been structured in a sensible, user-friendly way (replacing Tweak SAP GUI and the former options dialog). Additionally a search has been added to the options dialog.</LI></UL> <UL><LI>I18N Rendering: As of release 7.20 the Internationalization mode is activated by default (unification).</LI></UL> <UL><LI>SAP Signature Design: The SAP Signature Design is selected by default unless the user has decided to use a different design already. Of course the other designs are still offered.</LI></UL> <UL><LI>High Contrast Design for SAP Signature Design: A new high contrast mode based on the SAP Signature Design is now available. This is intended for visually impaired persons.</LI></UL> <UL><LI>Security Enhancements: A set of security rules which allow administrators and users to prevent unwanted access to the client PCs from the SAP System (like deleting files or folders) have been defined and a maintenance dialog for these rules has been included in the new options dialog.</LI></UL> <UL><LI>Non-transparent frame: Implementation of a non-transparent frame for SAP Signature Design (increases performance on WTS machines).</LI></UL> <UL><LI>Animated Focus Frame: Improved visualization of the focus.</LI></UL> <UL><LI>NWBC: SAP GUI Scripting Access: Better integration of SAP GUI into NetWeaver Business Client.</LI></UL> <UL><LI>SAPWorkDir: The location of SAPWorkDir has been changed according to Windows standard and the contents of this folder have been split into several dedicated subfolders used for specific items.</LI></UL> <UL><LI>Frontend screenshot functionality: The SAP System can now transfer screenshots of the current SAP GUI state to the server.</LI></UL> <UL><LI>CTRL+A (mark all), CTRL+Z (undo) and CTRL+Y (redo) is now implemented for input fields</LI></UL> <UL><LI>SNC logon without SSO (delivered with 7.20 Compilation 3 (Patch 7)): more details see note 1580808</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030047)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D030047)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001393114/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393114/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393114/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393114/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393114/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393114/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393114/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393114/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001393114/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "1442028", "RefComponent": "BC-FES-OFFI", "RefTitle": "SAP GUI for Windows / Desktop Office Integration: Support for Office 64bit editions", "RefUrl": "/notes/1442028"}, {"RefNumber": "1296465", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.20", "RefUrl": "/notes/1296465"}, {"RefNumber": "1296464", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.1", "RefUrl": "/notes/1296464"}, {"RefNumber": "1296463", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.0", "RefUrl": "/notes/1296463"}, {"RefNumber": "1296419", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 2004", "RefUrl": "/notes/1296419"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2185432", "RefComponent": "BC-FES-GUI", "RefTitle": "How to disable security popup", "RefUrl": "/notes/2185432 "}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "1442028", "RefComponent": "BC-FES-OFFI", "RefTitle": "SAP GUI for Windows / Desktop Office Integration: Support for Office 64bit editions", "RefUrl": "/notes/1442028 "}, {"RefNumber": "1296463", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.0", "RefUrl": "/notes/1296463 "}, {"RefNumber": "1509421", "RefComponent": "BC-FES-GUI", "RefTitle": "Main Browser Note for NW 7.3", "RefUrl": "/notes/1509421 "}, {"RefNumber": "1296465", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.20", "RefUrl": "/notes/1296465 "}, {"RefNumber": "1296464", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 7.1", "RefUrl": "/notes/1296464 "}, {"RefNumber": "1296419", "RefComponent": "BC-FES", "RefTitle": "Main Browser Note for NW 2004", "RefUrl": "/notes/1296419 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BC-FES-GUI", "From": "7.20", "To": "7.20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}