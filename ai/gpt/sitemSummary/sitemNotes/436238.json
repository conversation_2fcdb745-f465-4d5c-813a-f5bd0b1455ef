{"Request": {"Number": "436238", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 552, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015073042017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000436238?language=E&token=07307C0AAF0F9AF3716809F83E999FC3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000436238", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000436238/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "436238"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.09.2019"}, "SAPComponentKey": {"_label": "Component", "value": "IS-ADEC-GPD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Grouping, Pegging & Distribution"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Ind.-Spec.Comp. Aerospace&Defense / Engineering&Construction", "value": "IS-ADEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-ADEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Grouping, Pegging & Distribution", "value": "IS-ADEC-GPD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-ADEC-GPD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "436238 - GPD: Restrictions, Limitations, Recommendations"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The GPD processes can be complex and dependent on a variety of system settings. Besides this not all processes are supported. This note gives you an overview of various restrictions, performance issues and hints on how to avoid problems. In case of questions, please contact your SAP consultant.<br /><br />We will update this note allways when new information are available:</p>\r\n<ul>\r\n<li>Please consider the restrictions concerning the size of projects as described in note 206264! Main purpose of grouping is the advantage of larger lotsizes - grouping enables not a larger project related data volume.</li>\r\n</ul>\r\n<ul>\r\n<li>In case that you don't use the 'automatic requirements grouping' you have to maintain the assignment of individual wbs elements to group wbs element manually with transactions GRM3 or GRM4. In case of type 2 group wbs elements you have to use additionally transaction GRM5 in order to assign plant and mrp groups to the group wbs element. Because of the datastructure of grouping it is not posible to include all consistency checks in these transactions. Please run transaction GRM6 on a regulary base in order to detect problems in the assignment of your wbs elements to group wbs elements.</li>\r\n</ul>\r\n<ul>\r\n<li>Grouping is not a replacement for plant stock or a kind of plant stock. Assigning thousands or more wbs elements to one group wbs element can cause performance problems in the grouping itself, the MRP run, the availability check and the pegging run. It can also slow down the posting of material movements.</li>\r\n</ul>\r\n<ul>\r\n<li>Never(!) choose a group wbs element as wbs element for network components - also in the case where the group wbs element is only valid for other materials and not for the material of the network component itself, especially not if you want to use pegging and distribution. Such a setting leads to several conflicts allready with grouping. It would not work with pegging (changes would be necessary which would slow down the performance) and it would be much harder to verify the correct result after the cost distribution run. A network component should allways be assigned to a individual wbs element - never to a group wbs element.</li>\r\n</ul>\r\n<ul>\r\n<li>You are using grouping to leverage the benefits of larger lotsizes and an easier handling compared to project stock for individual wbs elements. One of the decisions you have to make is which materials you are going to manage in the grouped project stock. Usually the materials at the top of a bill of material are so unique that they are managed in the stock of individual wbs elements. An individual wbs element is a wbs element that is not a grouping wbs element. Materials at medium levels in the bill of material or externally procured materials where you can leverage the advantages of larger lotsizes are usually grouped materials. But it is recommended to use plant stock for lower value materials to reduce the number of materials relevant for grouping and pegging. Grouping together with pegging and distribution is not a replacement for plant stock!</li>\r\n</ul>\r\n<ul>\r\n<li>Avoid groups valid for an entire plant if you are going to use pegging. Pegging processes materials according to their MRP low level code from the top to the bottom. Group WBS elements which defining together a hierarchy for the materials to be grouped have to be processed together in one pegging run. That means if you are&#160;&#160;using group wbs elements valid for an entire plant you have to process the entire plant in one pegging run. Depending from the datavolume and the hardware this will not be possible above a given datavolume or lead to severe performance issues. Hence please setup your groups only valid for a number of your projects instead the entire plant so that you can split the datavolume.</li>\r\n</ul>\r\n<ul>\r\n<li>Try to avoid transfer postings with transaction TBLP_TRLO between independent groups (independent groups means groups which don't have to be processed together in one pegging run).&#160;&#160;Using this transaction pegging shifts the costs accordingly. The huge shortcoming of this is that you have to process both groups together in all future pegging runs. Instead of using transaction TBLP_TRLO please 'loose' the material to be transfered in one group and 'find' it for the new group using - as example - movement types 501/502. Shortcoming of this is that the costs (unvaluated project stock) are staying at the old group and not beeing transfered to the new group wbs element. Where possible accept this inaccuracy or correct the cost manually. SAP is going to overcome this problem probably with the next release.</li>\r\n</ul>\r\n<ul>\r\n<li>It is recommended in case of huge datavolumes or if it's not required for your business process to switch off the pegging for purchase requisitions and planned orders. Both objects are never debited with costs hence it has no effect on the cost distribution. Technically the pegging run skipps the reading and processing for purchase requisition and planned orders with external procurement (PLAF-BESKZ = 'F'). Planned orders with internal procurement have to be read and processed in order to process dependent purchase orders or production orders. The assignments for the planned orders are not stored at the database. If purchase orders or planned orders are processed and stored at the database is controlled in the general settings for pegging - transaction 0PEG01.</li>\r\n</ul>\r\n<ul>\r\n<li>Pegging does not support material to material transfer postings for internally procured materials. As a workaround please create manually a production order for the new material number and issue the old material to the order and deliver the new material into the stock.</li>\r\n</ul>\r\n<ul>\r\n<li>Pegging has several help tables required for the peging process. One of these tables is the stock table (peg_tstk) - storing the information which order or purchase order has which quantity in stock. This table has allways to be synchronized with the project stock table mspr. If the system detects an inconsistency during a goods movements it's raisesa x message to avoid the database update. Nevertheless there is a check report (transaction PEG10) which finds inconsistencies. It is recommended to run this report on a regular base especially during the migration of existing data. The flag 'compare at batch level' should be switched on.</li>\r\n</ul>\r\n<ul>\r\n<li>Pegging does not support cross plant MRP or cross plant postings in release 4.6C/2. Please consider this before implementing pegging. Other restrictions can be found in note 406362.</li>\r\n</ul>\r\n<ul>\r\n<li>Cost distribution itself can allways be carried out for a single group wbs element, a number of group wbs elements (selection variant) or an entire plant. In case of a huge datavolume split all your group wbs elements in a number of selection variants and run these different jobs at the same time. Please make sure that one group is only in one selection variant.</li>\r\n</ul>\r\n<ul>\r\n<li>Switch off the distribution for commitments in case of huge data volumes or in case the distribution of commitments is not required for your business process. Switching the distribution of commitments off or on is controlled in the distribution profile - transaction DIS01. The same is valis for payments. The flag for the payment distribution is only necessary for postings updated by the PS cash management. The distribution of value type '12' cash postings is not influenced by this flag but by the flag for the cost distribution.</li>\r\n</ul>\r\n<ul>\r\n<li>You can't use breakpoints together with valuated project stock because of the standard value flow of the valuated project stock. Valuated project stock together with breakpoints would lead to wrong reporting figures.</li>\r\n</ul>\r\n<ul>\r\n<li>If you use grouping, pegging and distribution only for externally procured materials it is strongly recommended to switch the distribution of costs, cash and commitment for internally procured materials off. Vice versa the same is true if grouping, pegging and distribution are only used for internally procured materials.</li>\r\n</ul>\r\n<ul>\r\n<li>Pegging does not see the serial number of a material. So a grouped material which is in stock will be pegged, but without taking its serial number into consideration.</li>\r\n</ul>\r\n<ul>\r\n<li>The use of budget management, budget-related reports and availability control is limited in the GPD environment. Unlike settlement cost records, the distribution cost records are not treated specially by the availability control. From the budget availability control perspective, the actual cost on the grouping WBS element or production orders will be zero after distribution. This can lead to unexpected remaining order plan values. We do not recommend including grouping WBS elements in the budget availability control and budget-related reports.</li>\r\n</ul>\r\n<ul>\r\n<li>Use of circular or recursive assignments of groups is only valid for grouping WBS elements of type 2. The groups involved should have different Plant and MRP group combination.</li>\r\n</ul>\r\n<ul>\r\n<li>For New General Ledger Accounting (Available in release ECC-DIMP 500 and above) to work with GPD (especially GPD cost distribution DIS01), please apply the following notes on your system if necessary: 1. Note 830901 (CO enhancement) 2. Note 1067455 (GPD enhancement).</li>\r\n</ul>\r\n<ul>\r\n<li>If you use GPD, you are not allowed to perform a goods issue from blocked stock.The system would give an error message MIGO 043 \"Check combination of movement type and stock type\". You must first move the blocked stock to status 'unrestricted' before you can perform a goods issue. You can scrap from blocked stock, however.</li>\r\n<li>Pegging (old and new) does not support multiple schedule lines for purchase orders generating requirements (typically stock transport and core subcontracting orders).&#160;</li>\r\n<li>Stock transport orders (inter/intra-company) using regular purchase order types (EKPO-PSTYP = '0')</li>\r\n<li>Related to SFIN2</li>\r\n<ul>\r\n<li>Valuated GPD will not work with sFIN2 because multiple CO Versions are not supported by sFIN2. A solution will be investigated in the near future.&#160; Non-Valuated GPD has been tested with sFIN2 on&#160;ECC&#160;and tested with S4 HANA with&#160;success.</li>\r\n</ul>\r\n</ul>\r\n<p><br />Related to GPD is the Transfer Borrow Loan Payback process.</p>\r\n<ul>\r\n<li>The Borrow Loan Payback part is only supported for individual WBS elements but not for group wbs elements.</li>\r\n</ul>\r\n<ul>\r\n<li>Sending and receiving WBS element have to be in the same company code.</li>\r\n</ul>\r\n<ul>\r\n<li>Only requirements from network activities or production orders are supported.</li>\r\n</ul>\r\n<ul>\r\n<li>Transfer or loan can be performed for a material that's supposed to have serial numbers. You have to enter the serial number when you perform the transfer or loan.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p>As part of the transition from ECC to S/4HANA, SAP is in the process of developing Project Manufacturing Management &amp; Optimization (PMMO), which is planned to replace the Grouping, Pegging &amp; Distribution (GPD) component in future. The new component PMMO is being developed as a native component in S/4HANA OnPremise taking advantage of the optimizations, performance improvements and new capabilities that S/4HANA delivers. PMMO is planned to be available in S/4HANA only, in upcoming S/4HANA OnPremise releases. As such, GPD is planned to receive continuous maintenance to address productive customer issues, and major features and integration enhancements are planned to&#160; be considered on PMMO only. Features between PMMO and GPD may not be necessarily identical and migration from GPD to PMMO is planned to only be supported for standard objects across the two components.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Grouping, Pegging, Distribution, GPD, PEG01, DIS01, TBLP, Transfer<br />Borrow Loan Payback</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are using the pegging and distribution in Release DI 4.6C/2. Note<br />that this function is only released for customers after SAP has been<br />consulted.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note will be updated as soon as new information are available.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-ADEC (Ind.-Spec.Comp. Aerospace&Defense / Engineering&Construction)"}, {"Key": "Responsible                                                                                         ", "Value": "I013850"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I037481)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000436238/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000436238/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000436238/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000436238/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000436238/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000436238/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000436238/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000436238/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000436238/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "797046", "RefComponent": "IS-ADEC-GPD", "RefTitle": "Prevent GPDS/P entries from contributing to assigned values", "RefUrl": "/notes/797046"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2933435", "RefComponent": "IS-ADEC-GPD", "RefTitle": "S4TWL - Grouping Pegging Distribution (GPD) in SAP S/4HANA", "RefUrl": "/notes/2933435 "}, {"RefNumber": "2237157", "RefComponent": "IS-ADEC-GPD", "RefTitle": "Stock transport orders using regular purchase order types are not supported by GPD", "RefUrl": "/notes/2237157 "}, {"RefNumber": "797046", "RefComponent": "IS-ADEC-GPD", "RefTitle": "Prevent GPDS/P entries from contributing to assigned values", "RefUrl": "/notes/797046 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "DIMP", "From": "471", "To": "471", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "ECC-DIMP", "From": "605", "To": "605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}