{"Request": {"Number": "822379", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1153, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015855582017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000822379?language=E&token=F94FF670857C5361CEF7EE64B27B242A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000822379", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000822379/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "822379"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 62}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.08.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-OCS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr)", "value": "BC-UPG-OCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-OCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "822379 - Known problems with support packages in SAP NW 7.0x AS ABAP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note provides information about problems that can occur when you use the Support Package Manager to import Support Packages for ABAP components. On the initial screen of the Support Package Manager, choose the 'i' button to find more information about the Support Package Manager.<br /><br /><strong>Caution: This SAP Note is updated regularly. Therefore, always download the latest version from SAP Service Marketplace.</strong><br /><br />This SAP Note also concerns problems of components based on NetWeaver 7.0 Enhancement Package.<br /><br />You may have to carry out a SPAM/SAINT update before you can import a certain Support Package. In turn, a SPAM/SAINT update may require a certain R3trans or tp version to be installed in your system. This SAP Note also tells you if a SPAM/SAINT update, R3trans or tp is required.<br /><br />Note also that, due to the new Support Package stack concept, dependencies can be defined between the different Support Package types (for example, BW Support Package SAPKW70001 requires Basis and ABA Support Packages SAPKB70001 and SAPKA70001). For details about the Support Package stack concept and previously defined Support Package stacks, see SAP Service Marketplace under http://service.sap.com/sp-stacks.<br />Basis Support Packages may also require SAP kernels with a minimum patch level. Currently, the following interdependencies exist:<br />SAPKB70014: Kernel 7.00, patch number 133</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>OCS, SPAM, SPAM/SAINT UPDATE, CONFLICT RESOLUTION TRANSPORT, SUPPORT PACKAGE, CRT, LOAD_PROGRAM_LOST, SAPSQL_SELECT_WA_TOO_SMALL, SAPSQL_WA_TOO_SMALL, CALL_FUNCTION_PARM_UNKNOWN, XPRA_EXECUTION, TP_FAILURE, TESTIMPORT, DDIC ACTIVATION<br /><br /><strong>Change history</strong><br />- FEB/25/2022: SPAM/SAINT Version 0080 withdrawn: Bug in the after-import method if TCI changes are transported<br />- DEC/20/2013: SAPKB70029, SAPKB70114, SAPKB70214: SAP Note <strong>1910464</strong> regarding avoidance of problems in transaction SPDD (modification adjustment for ABAP Dictionary objects)<br />- DEC/20/2013: Incorrect handling of cluster tables (for example, BSEG) -&gt; see SAP Note <strong>1861585</strong><br />- DEC/20/2013: SAPKE60454, SAPKE60455: Implement SAP Note <strong>1834396 </strong>before importing the Support Package.<br />- DEC/20/2013: SAPKB70014 (or EHP updates to higher SAP NetWeaver Releases): See SAP Note <strong>1281521</strong> if syntax errors occur in SAPLPA_PACKAGE_CHECKS.<br />- DEC/20/2013: <strong>DB2 for z/OS</strong> with SAP kernel 7.20 (DCK) -&gt; see SAP Note <strong>1708929</strong><br />- FEB/07/2013: SAPKB70028 incorrect delivery -&gt; Use the corrected version of the Support Package!<br />- FEB/01/2013: SAPKB70027, SAPKB70112, SAPKB70211, SAPKB70212: There is a syntax error in the program SAPLSDSB if SAP Note <strong>1749824</strong> is implemented - you MUST refer to SAP Note <strong>1809597</strong>.<br />- DEC/12/2012: Runtime error COMPUTE_INT_PLUS_OVERFLOW during the ABAP Dictionary activation -&gt; refer to SAP Note <strong>1678047</strong>.<br />- DEC/12/2012: Refer to SAP Note <strong>1614802</strong> to avoid database deadlocks in the ABAP Dictionary activation program.<br />- DEC/12/2012: SAPKB70028, SAPKB70113, SAPKB70212: A cancelled import cannot be continued due to a syntax error in SAPLSUU1.<br />- DEC/12/2012: Refer to SAP Note <strong>1476315</strong> to avoid errors during the Switch Framework and enhancement object activation.<br />- DEC/12/2012: <strong>DB2 for IBM i (AS400)</strong>: SAPKB70026, SAPKB70111, SAPKB70210: Login no longer possible -&gt; Refer to SAP Note 1724702.<br />- DEC/12/2012: <strong>MS SQL Server: </strong>Basis Support Packages lower than SAPKB70027, SAPKB70112, SAPKB70212: If SAP Note <strong>1660078</strong> is implemented, it is essential that you deimplement it before you import Basis Support Packages.<br />- DEC/12/2012: Refer to and implement SAP Note <strong>1020887</strong> to avoid ABAP Dictionary activation errors for the installed add-on DMIS_CNT.<br />- DEC/12/2012: SAPKB70027, SAPKB70112, SAPKB70212: Error in the modification adjustment (SPAU)<br />- DEC/12/2012: 'UNKNOWN_ERROR' in Importphase Disassemble: See SAP Note 1778145.<br />- DEC/11/2012: It is not possible to continue the import in the Support Package Manager due to syntax errors in CL_SUSR_BASIC_TOOLS===========CP.<br />- DEC/11/2012: SAPKA70026: DDIC activation error when importing into a CRM 6.0 system<br />- DEC/11/2012: SAPK-60442INSAPHR, SAPK-60443INSAPHR: Error in the phase XPRA_EXECUTION<br />- DEC/11/2012: SAPKB70025: Refer to SAP Note 1596680 if SAPKB70026 is not yet implemented.<br />- NOV/14/2011: Basis Support Packages lower than SAPKB70025, SAPKB70110, SAPKB70209: If SAP Note <strong>1411877</strong> is implemented, it is essential that you deimplement it before you import Basis Support Packages.<br />- NOV/14/2011: Refer to SAP Notes 1420290 and 1558728 to prevent long runtimes during the phase XPRA_EXECUTION.<br />- NOV/11/2011: If you are using the 720 kernel, you must refer to SAP Note 1619001 (potential data loss during the transport).<br />- NOV/11/2011: SAP_BW and BI_CONT Support Packages: See SAP Note 1488647 to prevent data loss during the import.<br />- NOV/11/2011: SAPKB70023, SAPKB70108, SAPKB70206: Runtime error CONNE_IMPORT_CONVERSION_ERROR in phase XPRA_EXECUTION<br />- NOV/11/2011: SAPKE60073 (SAPK-60073INSAPHRCDE): See SAP Note 1629659.<br />- NOV/11/2011: <strong>Oracle DB</strong>: Long import runtimes in DB Releases 9.2.0.7, 9.2.0.8, 10.2.0.2 - see SAP Note 1028099<br />- NOV/07/2011: SAPKH60501: Termination with rc 0012 in phase XPRA_EXECUTION<br />- NOV/07/2011: SAPKB70208, SAPKB70209: Generation errors that can be ignored - see SAP Note 1630554<br />- SEP/14/2011: <strong>SQL Server</strong>: Refer to SAP Note 1630727 to prevent SQL error 207: \"Invalid column name 'area'\"<br />- APR/07/2011: <strong>Oracle DB:</strong>  Refer to SAP Note 1514063 to prevent ORA-1007 errors from occurring in the phase IMPORT_PROPER.<br />- APR/07/2011: SAPKB70023, SAPKB70108, SAPKB70206: There are problems with the generation of indexes on <strong>MS SQL Server 2000</strong>.<br />- APR/07/2011: SAPKB70107, SAPKB70204: If SAP Note <strong>1473316</strong> is implemented, it is essential that you deimplement it before you import the specified Basis Support Packages.<br />- APR/07/2011: Refer to SAP Notes 1521903 and 1548839 to correct problems with BAdIs after you import Support Packages or Enhancement Package updates and to prevent these problems from occurring.<br />- APR/07/2011: SAPKE60064, SAPKE60430: See SAP Note 1510174.<br />- APR/07/2011: SAPKGPHD64, SAPK-60243INEAHR, SAPK-60338INEAHR, SAPK-60430INEAHR: See SAP Note 1510174.<br />- APR/07/2011: The description of potential problems when importing SPAM/SAINT updates has been extended.<br />- JAN/03/2011: Use an R3trans version from July 20, 2010 or later to prevent import errors (see SAP Note 1491290).<br />- JAN/03/2011: To avoid DDIC activation errors, implement the corrections contained in SAP Notes 1301446 and 1354957.<br />- JAN/03/2011: SAPKB70023, SAPKB70108: Syntax error in the central program MENUSYST (see SAP Note 1285869).<br />- JAN/03/2011: SAPKB70022, SAPKB70023: Problems with Switch Framework BAdIs (see SAP Note 1532661).<br />- JAN/03/2011: SAPKB70106, SAPKB70107: Problems with Switch Framework BAdIs (see Note 1532661).<br />- JAN/03/2011: SAPKB70202 - SAPKB70206: Problems with Switch Framework BAdIs (see Note 1532661).<br />- JAN/03/2011: SAPKB70022, SAPKB70107, SAPKB70205: If SAP Note <strong>1420178</strong> is implemented, it is essential that you deimplement it before you import the specified Basis Support Packages.<br />- JAN/03/2011: SAPKA70023, SAPKA70108, SAPKA70205: Converting the table BP1030<br />- JAN/03/2011: SAPKB70022, SAPKB70107: Screen generation error for the screens SAPLENH_BADI_EDT_DEF 2200 and SAPLENH_EDT_BADI 2100.<br />- JUL/25/2010: <strong>DB6 database</strong>: Make sure that you read HotNews <strong>1406740</strong>.<br />- JUL/25/2010: Correction of the problem description for SAP Note <strong>1108326</strong>.<br />- JUN/30/2010: SAPKB70020 - 22, SAPKB70105 - 07, SAPKB70202 - 04: If SAP Note <strong>1108326</strong> is implemented, it is essential that you deimplement it before you import the specified Basis Support Packages.<br />- JUN/29/2010: Use an R3trans version from May 12, 2010 to prevent import errors (see Notes 1417505, 1468464, and 1469610).<br />- JUN/29/2010: SPAM/SAINT update - Version 0039: incorrect conflict check between FI-CA and IS-UT<br />- JUN/29/2010: SAPKB70020, SAPKB70105: The validity check for the ALE distribution model contains errors.<br />- JUN/29/2010: SAPKE60039, SAPKE60404: Activation problems with the view V_FBN_T74HB<br />- JAN/25/2010: Implement SAP Note <strong>1262653</strong> to prevent objects from being deleted during the modification adjustment.<br />- JAN/22/2010: To avoid DDIC activation errors, implement the corrections contained in Notes 1315662 and 1263115.<br />- JAN/22/2010: SAPKB70015 - SAPKB70018: When you import Support Packages, texts for domain fixed values may be lost.<br />- JAN/22/2010: up to SAPKB70103: When you import Support Packages, texts for domain fixed values may be lost.<br />- JAN/22/2010: SAPKH60401 - SAPKH60403: Inconsistent data types in DDIC<br />- JAN/19/2010: SAPK-60013INISOIL, SAPK-60015INISOIL: DDIC activation error for tables OIUX4_BALANCE, OIUX4_BALANCE_H<br />- AUG/28/2009: <strong>DB4:</strong> SAPKB70019: To avoid disk storage overflow with too many database journal entries, see Note 1366799.<br />- JUN/15/2009: To avoid DDIC activation errors, implement the corrections contained in Notes 1256384 and 1321756.<br />- JUN/15/2009: R3trans termination with return code 7777 on the Windows or Oracle platform: See Notes 1288862 and 1332780.<br />- JUN/15/2009: <strong>SQL Server 2000</strong> database: Syntax error 170 when creating an index.<br />- JUN/15/2009: SAPKB70011, SAPKB70013, SAPKB70018, SAPKB70103: If you have implemented SAP Note <strong>1273082</strong>, you MUST implement the latest version of this SAP Note.<br />- JUN/15/2009: Using the new parallel processing function as of SAPM/SAINT Version 0035: You must use an R3trans version from December 19, 2008 or later.<br />- JUN/15/2008: Avoiding errors in the Switch Framework after you upgrade to NetWeaver 7.0 Enhancement Package 1: Implement Notes 1237062 and 1319582.<br />- DEC/16/2008: Error in the DDIC activation program - see Note 1283197<br />- DEC/16/2008: SAPKB70014, SAPKB70016: Syntax error in the class CL_SFW_SYSTEM_SETTINGS<br />- DEC/16/2008: Test import errors that you can ignore are no longer listed in this SAP Note. This is because as of <strong>SPAM/SAINT Version 0032</strong>, the system recognizes and ignores these automatically during the import. As of <strong>SAPM/SAINT Version 0032</strong>, if the system reports test import errors, you <strong>cannot ignore them</strong> and <strong>must</strong> investigate them.<br />- DEC/16/2008: Queue calculation with EA-HR Country Legal Change (CLC) packages<br />- DEC/16/2008: SAPKE60035: DDIC activation error for table T5J73T<br />- DEC/16/2008: SAPKE60037, SAPKE60403: Generation error in the phase ABAP_GENERATION<br />- DEC/15/2008: <strong>SQL Server 2008</strong> database: Import error if dbsl-lib- is obsolete<br />- NOV/19/2008: SAPKB70017: Termination of phase XPRA_EXECUTION with return code 0012<br />- NOV/19/2008: SAPKB70013: Transaction SFW5 does not check the Support Package level<br />- SEP/19/2008: SAPKB70011 - SAPKB70016: Prior to implementing the Support Package, see Note 1249184.<br />- SEP/19/2008: SAPKE60031: Long runtime when importing a Support Package.<br />- SEP/19/2008: SPAM/SAINT Version 0029: TP_FAILURE 0212 in Phase IMPORT_OBJECT_LIST<br />- JUL/18/2008: When you import SAP_BW Support Packages, the BI Content is no longer available.<br />- JUL/18/2008: Implementation of Notes 1086728 and 1142128 to prevent problems in SPAU<br />- AUG/18/2008: <strong>DB2/DB6</strong> database: SQL error -107 in the phase IMPORT_PROPER<br />- JUL/18/2008: <strong>DB2 for AS400</strong> database: tp terminates after the main import.<br />- JUL/18/2008: Long runtime of the import phase CLEAR_OLD_REPORTS<br />- JUL/18/2008: SAPKE60016 - SAPKE60021: Duplicate key error during the import<br />- JUL/18/2008: SAPKE60026: TrueType fonts were deleted.<br />- MAR/03/2008: You cannot import Support Packages after you have imported ERP Enhancement Packages.<br />- MAR/03/2008: SAPKB70010 Termination in the phase AUTO_MOD_SPAU <br />- MAR/03/2008: To avoid syntax errors in classes after SPAU, implement Note 970837.<br />- MAR/03/2008: To avoid ABAP Dictionary activation errors, implement the corrections contained in SAP Notes 791984, 1015068, 1022755, and 1113868.<br />- MAR/03/2008: SPAM/SAINT Version 0026: Long running background job when you import Support Packages<br />- MAR/03/2008: General note about errors in the phase XPRA_EXECUTION<br />- MAR/03/2008: <strong>DB2/DB6:</strong> SAPKB70012: Error during the step DDIC Distribution (tp-step \"S\") due to the table DB6PMSQ_DB<br />- MAR/03/2008: SAPK-60204INEAHR: Error in the phase DDIC_ACTIVATION<br />- MAR/03/2008: SAPKGPHD26: Error when generating ABAP<br />- MAR/03/2008: SAPKE60024, SAPKE60025: Test import error in SAPKE60025<br />- MAR/03/2008: SAPK-60010INISM, SAPK-60011INISM: Test import error in SAPK-60011INISM<br />- DEC/10/2007: SAPKB70013: In transaction SFW5, the system displays incorrect switch values for business functions.<br />- DEC/10/2007: SAPKB70014 requires kernel 700 patch level 133.<br />- NOV/29/2007: SAPKB70014: Administrative activities in the web service configuration must be carried out. <br />- NOV/07/2007: A system termination and standstill occur when you import Support Package SAPKB70013 after having implemented Note 1023163 (see also HotNews 1112065).<br />- OCT/27/2007: SAPKA70012: Important: The system overwrites all field modification settings for the business partner category.<br />- OCT/27/2007: SAPK-60009INECCDIMP: Implement Note 1070942 after you import the Support Package to prevent payroll errors.<br />- OCT/27/2007: SAPKW70013, SAPKW70014/SAPKW70015/SAPKW70016: Test import error in SAPKW70014/SAPKW70015/SAPKW70016<br />- OCT/27/2007: SAPKB70009, SAPKB70012/SAPKB70014: Test import error in SAPKB70012/SAPKB70014<br />- JUL/27/2007: On <strong>MSSQL Server</strong> systems: The import step for the table conversion (tp step \"N\") terminates with the runtime error SAPSQL_SUBQUERY_ILL_TABLE_TYPE.<br />- JUL/27/2007: To avoid ABAP Dictionary activation errors, implement the corrections contained in SAP Notes 791984, 1015068, 1022755, and 1113868.<br />- JUL/27/2007: SAPKB70013: It takes a very long time to refresh the TMS QA worklist.<br />- JUL/27/2007: SAPKE60013: Incorrect print screen when you print SAPscript forms<br />- JUL/27/2007: SAPKB70012: <strong>MS SQL Server DB</strong>: Error when executing backup jobs<br />- JUL/27/2007: SAPKB70012: ABAP generation errors during import into a CRM system<br />- JUL/27/2007: SAPKB70011: After the import, implement the corrections contained in Note 1037232.<br />- JUL/27/2007: SAPK-60008INISPSCA, SAPK-60009INISPSCA: Test import error in SAPK-60009INISPSCA<br />- JUL/27/2007: SAPKGPAD07, SAPKGPAD08: Test import error in SAPKGPAD08<br />- JUL/27/2007: SAPKGPHD04, SAPKGPHD09: Test import error in SAPKGPHD09<br />- JAN/16/2007: SAPKB70011: Termination in the IMPORT_PROPER phase after the main import has been successfully completed<br />- JAN/16/2007: SAPK-60006INISOIL, SAPK-60007INISOIL: Test import error in SAPK-60007INISOIL<br />- DEC/21/2006: SAPKE60009: It may take a long time to create new indexes for the PPOIX and PPOPX tables.<br />- DEC/21/2006: SAPKB70006, SAPKB70010: Test import error in SAPKB70010<br />- OCT/27/2006: SAPKB70002, SAPKB70005, SAPKB70006: Syntax error in the Note Assistant and modification adjustment after the import<br />- OCT/27/2006: SAPKB70005, SAPKB70008: Test import error in SAPKB70008<br />- SEP/06/2006: SAPKB70009: Incorrect delivery -&gt; Use the corrected version of the Support Package!<br />- SEP/06/2006: Termination when importing in downtime-minimized mode on MS SQL Server<br />- JUL/28/2006: SAPKH60005: DDIC activation error that can be ignored<br />- JUL/28/2006: SAPKGPFD03, SAPKGPFD05: Test import error in SAPKGPDD05<br />- MAY/11/2006: Serious error in the CRM SP Manager<br />- MAY/11/2006: After you import SAPKB70006, table conversions terminate.<br />- MAY/11/2006: SAPKB70002, SAPKB70004: Errors you can ignore in DDIC activation<br />- MAY/11/2006: SAPKITL412: Error in phase XPRA_EXECUTION<br />- MAY/11/2006: SAPKGPDD02, SAPKGPDD04: Test import error in SAPKGPDD04<br />- MAY/11/2006: SAPK-60004INISUT: Runtime of phase XPRA_EXECUTION may be longer<br />- MAY/11/2006: SAR archives cannot be loaded and unpacked using the function \"Load from front end\"<br />- MAY/11/2006: SAPCAR error: format error in header<br />- FEB/17/2006: SAPKH60003: Error in phase XPRA_EXECUTION<br />- FEB/17/2006: SAPKE60002: Import prerequisites corrected<br />- FEB/17/2006: SAPKH60001, SAPKGPHD02: Test import error in SAPKGPHD02<br />- JAN/09/2006: Only import Application Support Packages after you import SAPKB70005 completely<br />- JAN/09/2006: SAPKW70004, SAPKW70007: Test import error in SAPKW70006 and SAPKW70007<br />- JAN/04/2006: SAPKA70006  Error during the execution of the after-import methods<br />- JAN/04/2006: If you use the downtime-minimized import mode, you <strong>must</strong> use at least the R3trans version from December 06, 2005.<br />- JAN/04/2006: SAPKB70004 - Error during the execution of the after-import methods, if Java VM containers are activated<br />- JAN/04/2006: SAPKW70003 - Import error on MaxDB 7.6<br />- AUG/18/2005: <strong>Oracle DB:</strong> After you implement SAP Note 865198, there is a risk of <strong>data loss </strong>when you import the Support Package. <strong>You must take into account Note 871846.</strong><br />- MAY/12/2005: SAPKB70001 - Serious error! All background jobs may be deleted. <strong>You must take into account Notes 715355 and 837691.</strong><br />- FEB/24/2005: Note was created again<br /><br /><strong>SAP recommendation</strong><br />We strongly recommend that you always import the latest version of the SPAM/SAINT update before you import any other Support Packages.<br />We recommend that you download the latest versions of the tp and R3trans executables from SAP Service Marketplace (see Note 19466).<br /><br /><strong>V3 update entries:</strong> Process your V3 update entries before you import Support Packages. Otherwise, there is a risk that you will no longer be able to update the update entries if changes in the interface structures of the V3 update modules are delivered by Support Packages. As of SPAM/SAINT Version 0024, the Support Package Manager checks whether there are any open orders and you are then informed of this.<br /><br />If you are using several application servers, make sure that profile parameter <strong>rdisp/bufrefmode</strong> is set to <strong>sendon,exeauto</strong>, and if you are using only one application server, make sure that the profile parameter is set to <strong>sendoff,exeauto</strong>. Otherwise, you only have the option of executing the \"/$sync\" command to make the Support Package changes become effective.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The problem is generally caused by a program error or the Support Package type.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>1. SPAM/SAINT updates</strong></p>\r\n<ul>\r\n<li><strong>Symptom:</strong> When you import the SPAM/SAINT update, a range of different runtime errors may occur:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>LOAD_PROGRAM_LOST</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>LOAD_PROGRAM_CLASS_MISMATCH</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>LOAD_TEXTPOOL_LOST</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAPSQL_SELECT_WA_TOO_SMALL</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAPSQL_SELECT_TAB_TOO_SMALL</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAPSQL_WA_TOO_SMALL</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>DDIC_STRUCLENG_INCONSISTENT</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RUNT_ILLEGAL_SWITCH</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>CALL_FUNCTION_PARM_UNKNOWN</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">These errors occur because the source code of the Support Package Manager is modified by the import during the run. CALL_FUNCTION_PARM_UNKNOWN, LOAD_PROGRAM_LOST, LOAD_PROGRAM_CLASS_MISMATCH, and LOAD_TEXTPOOL_LOST occur if the ABAP load or text elements are to be loaded back into the local buffer, and there is a different version in the database.<br />However, SAPSQL_SELECT_WA_TOO_SMALL, SAPSQL_SELECT_TAB_TOO_SMALL, SAPSQL_WA_TOO_SMALL, DDIC_STRUCLENG_INCONSISTENT and RUNT_ILLEGAL_SWITCH  occur if changes are made on the SPAM/SAINT data structures with the SPAM/SAINT update.<br /><br /><strong>Solution:</strong> None of these errors will occur again if you restart the Support Package Manager (transaction SPAM) and continue to import the SPAM/SAINT update.</p>\r\n<ul>\r\n<li><strong>Symptom:</strong> The import of a SPAM/SAINT update has terminated at the end as described above. When you restart the Support Package Manager, it looks as though the SPAM/SAINT update has been imported completely, and the traffic light icon in the \"Status\" frame on the right-hand side shows a green traffic light. However, when you call the Add-On Installation Tool, the system displays the error message TN 401 \"OCS locked by user xxx with transaction SPAM\".<br /><br />Reason: Due to the termination with a runtime error, inconsistent table entries may remain in the system. These have to be cleaned up.<br /><br /><strong>Solution:</strong> Start the Support Package Manager and choose \"Support Package\" -&gt; \"Import SPAM/SAINT Update\". Since the SPAM/SAINT update has already been imported completely, no other actions will be carried out except for cleaning up possible inconsistencies.<br />If another SPAM/SAINT update is offered for import, you can cancel it at this point; the inconsistencies are cleaned up despite this.</li>\r\n<li><strong>SPAM/SAINT Version 0026</strong><br /><strong>Symptom:</strong> When you import Support Packages with SPAM/SAINT Version 0026, the system schedules an unnecessarily long background job (SAPRUPGM). If you import several Support Package queues in succession, deadlocks may occur that block all background processes.<br /><br /><strong>Solution:</strong> Implement Notes 1132422 and 1138351 to ensure an optimized runtime of the background job SAPRUPGM and avoid deadlocks.</li>\r\n<li><strong>SPAM/SAINT Version 0029</strong><br /><strong>Symptom:</strong> The import terminates in the phase IMPORT_OBJECT_LIST and the system reports the following error:<br /> TP_FAILURE, 0212, could not access file as supposed<br /><br /><strong>Solution:</strong> Delete the current queue and implement the corrections contained in Note 1232804. You can then redefine the queue and implement it.</li>\r\n<li><strong>SPAM/SAINT update - Version 0039:</strong><br /><strong>Symptom:</strong> During the conflict check in SPAM/SAINT or in the prepare phase of the Enhancement Package Installer, conflicts between the components FI-CA and IS-UT are identified.<br /><br />Reason: Between April 29, 2010 and May 04, 2010, an incorrect version of the SPAM/SAINT update - Version 0039 was provided on SAP Service Marketplace. This version incorrectly finds conflicts between the components FI-CA and IS-UT.<br /><br /><strong>Solution:</strong> Download the latest version of the SPAM/SAINT update - Version 0039 from SAP Service Marketplace; after you unpack the SAR archive, the files CSR0120031469_0043722.PAT (for SAPKD70039) and CSR0120031469_0043724.PAT (for SAPKD70139) are located in the EPS inbox.<br />If the conflict is reported in SPAM/SAINT, delete the current queue and then implement the SPAM/SAINT update. When you import the queue again, the system no longer reports the conflict.<br />If the conflict was reported in the EHPI, implement the SPAM/SAINT update and continue the EHPI process. The conflict is no longer reported.</li>\r\n<li><strong>'UNKNOWN_ERROR' in import phase DISASSEMBLE</strong><br /><strong>Symptom:</strong> Importing HR Support Packages terminates in the phase DISASSEMBLE with the error UNKNOWN_ERROR'. The system displays the following error message in the activation log:<br /> \"Internal error: No valid source context supplied\"<br /><br /><strong>Solution:</strong> Proceed according to the correction instructions in SAP Note 1778145.</li>\r\n<li><strong>SPAM/SAINT Version 0080:</strong></li>\r\n<ul>\r\n<li><strong>Problem:</strong>&#x00A0;The SPAM/SAINT update contains a serious bug in the after-import method of the transport object R3TR CPKM that is used during the transport of TCI changes. This bug leads to a runtime error when such transports are imported, which leads to the termination of the import. For more information, see SAP Note 3153186.</li>\r\n<li><strong>Solution:</strong>&#x00A0;The SPAM/SAINT update was withdrawn and replaced with the next SPAM/SAINT Version 0081. If you have already imported SPAM/SAINT Version 0080 or are planning to do so, use the SPAM/SAINT update Version 0081 and import it.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>2. All Support Packages - problems during ABAP Dictionary activation</strong></p>\r\n<ul>\r\n<li><strong>Avoiding ABAP Dictionary activation errors when importing SPs</strong><br />To avoid DDIC activation errors when you import Support Package queues (especially long queues), you must implement the following SAP Notes BEFORE you implement the corrections.<br /><strong>791984 </strong>\"Simultaneous activation of view append and appending view\"<br /><strong>1015068</strong> \"Deleting and re-creating Dictionary objects\"<br /><strong>1022755</strong> \"Language import and deletion of dictionary objects\"<br /><strong>1113868 </strong>\"Activation error due to an object that does not exist\"<br /><strong>1256384</strong> \"View/structure not active: Key field of type string\"<br /><strong>1263115</strong> \"Technical settings cannot be activated\"<br /><strong>1301446</strong> \"Incorrect return code for mass activation during transport\"<br /><strong>1315662</strong> \"Activation errors due to extension indexes\"<br /><strong>1321756</strong> \"Mass activation: Object activated instead of deleted\"<br /><strong>1354957</strong> \"Tables with more than 3 long string fields\"<br /><strong>1614802</strong> \"Mass activation hangs during INSERT in the table DDFTX\"<br /><strong>1678047</strong> \"COMPUTE_INT_PLUS_OVERFLOW during activation\"<br />Alternatively, check whether they have already been implemented as part of a Basis Support Package.</li>\r\n<li><strong>Error in ABAP Dictionary activation program leads to conversion of cluster tables</strong><br />Symptom: When you activate DDIC objects, information in runtime objects may be lost under certain circumstances. As a result, cluster tables are converted. This is incorrect.<br /><br />Solution: See SAP Note <strong>1283197</strong> and implement the correction instructions specified in it before you import Support Packages.</li>\r\n<li><strong>Avoiding activation errors if DMIS_CNT is installed</strong><br />Solution: If the add-on DMIS_CNT is installed, refer to SAP Note 1020887 to avoid errors in the phase DDIC_ACTIVATION.</li>\r\n<li><strong>Avoiding errors during handling of cluster tables</strong><br />Symptom: In certain circumstances (cluster table enhanced by industry enhancement), an error can occur during the handling of cluster tables. This results in activation errors during the ABAP Dictionary activation.<br /><br />Solution: See SAP Note <strong>1861585</strong> and implement the correction instructions specified in it before you import Support Packages.</li>\r\n</ul>\r\n<p><strong>3. All Support Packages - problems in the transport tools tp and R3trans</strong></p>\r\n<ul>\r\n<li><strong>Downtime-minimized import only with R3trans from December 06, 2005 (or later)</strong><br />The downtime-minimized import mode in the Support Package Manager may only be used with an R3trans version from December 06, 2005 (or later). If you use an older R3trans version, ABAP Object methods may not be imported correctly. Note 906008 describes this phenomenon and the symptoms that occur.<br />As of Version 0020, the Support Package Manager checks whether the minimum R3trans version exists.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>BD2 for AS400 database: Termination of tp after the main import</strong><br />Symptom: When you import Support Packages, tp terminates after the main import is successfully performed (phase IMPORT_PROPER).<br /><br />Solution: Follow the instructions that are contained in Note 1173174, and install a tp with Version 372.04.36 or higher.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>Windows or Oracle platform: R3trans terminates with return code 7777</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Symptom: Importing Support Packages terminates in the main import phase with return code 7777. For more information, see Notes 1332780 and 1288862.<br /><br />Solution: Implement the solution contained in Note 1332780 before you import the Support Package or swap the version of R3trans for the version specified in Note 1288862.</p>\r\n<ul>\r\n<li><strong>Minimum R3trans version requited for parallel import of Support Packages</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">If you want to use the function for importing with parallel R3trans processes, which has been available since SAPM/SAINT Version 0035, you must use an R3trans version from December 19, 2008 or later to avoid import errors and terminations (see SAP Note 1127194).</p>\r\n<ul>\r\n<li><strong>Preventing various errors in R3trans</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Solution: Use an R3trans version from July 20, 2010 or later to prevent various R3trans errors. For more details about the problems, see the following SAP Notes:<br /><strong>1417505</strong> \"Incomplete import of (ABAP Dictionary) objects\"<br /><strong>1468464</strong> \"Access to non-existing tables DD43L and DD43T\"<br /><strong>1469610</strong> \"Missing ABAP programs after \"Downtime minimized import\"\"<br /><strong>1491290</strong> \"Missing synchronization after importing include deletions\"</p>\r\n<ul>\r\n<li><strong>Kernel Release 720: Potential data loss during the transport</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">If you use kernel Release 720, it is essential that you refer to SAP Note 1619001 to prevent potential data loss during the transport.</p>\r\n<p><strong>4. All Support Packages - problems during the modification adjustment</strong></p>\r\n<ul>\r\n<li><strong>Syntax errors in classes after modification adjustment of SAP Notes</strong><br />To avoid syntax errors in classes after the modification adjustment, it is essential that you implement Note 970837 before you import Support Packages.</li>\r\n<li><strong>Deleting objects during adjustment of obsolete SAP Notes</strong><br />Symptom: If you combine the modification adjustment of several update or upgrade processes (for example, upgrade, then import of Support Package using the Support Package Manager), objects may be deleted during the adjustment of obsolete SAP Notes. This is incorrect. However, this affects only objects that were newly created by the SAP Note.<br /><br />Solution: Before you start the second update process, you must implement the corrections contained in Note 1262653, or you must include the relevant Basis Support Package (SAPKB70021 for SAP_BASIS 700 or SAPKB70106 for SAP_BASIS 701) in the second update process.<br />As an alternative, you can process the entire modification adjustment of SAP Notes after each update or upgrade process. The problem does not occur as a result.</li>\r\n<li><strong>Changes contained in obsolete SAP Notes are completely removed</strong><br />Symptom: After you import Support Packages, changes to Web Dynpro objects and enhancement objects are removed when you perform the modification adjustment.<br /><br />Solution: Before you import Support Packages, implement the corrections from Notes 1086728 and 1142128 in your system.<br />If you already are in the process of performing the modification adjustment and the system offers Web Dynpro objects or enhancement objects for removal, cancel this process and open a problem message under the component BC-UPG-NA.</li>\r\n</ul>\r\n<p><strong>5. All Support Packages - other problems</strong></p>\r\n<ul>\r\n<li><strong>Serious error in the CRM SP Manager</strong><br />In CRM systems, you must use the special <strong>CRM SP Manager</strong> to import Support Packages, since it provides some necessary special functions.<br />If you have imported queues in test mode using the CRM SP Manager, you must close the CRM SP Manager and restart it before you import a Support Package queue in standard mode. This is the only way to ensure that important internal statuses are initialized properly and that the import is correct and complete. For detailed information about this problem, refer to Note 810081.</li>\r\n<li><strong>Oracle DB: Risk of data loss after you implement SAP Note 865198</strong><br />If you have implemented the correction instructions from SAP Note 865198, make sure to take into account SAP Note 871846 before you import the Support Package and, where necessary, revise the correction as specified in the SAP Note. <strong>Otherwise, data may be lost when you import the Support Package.</strong></li>\r\n<li><strong>\"SAPCAR error: format error in header\" when extracting an SAR archive</strong><br />Symptom: When you unpack an SAR archive, the SAPCAR utility terminates with the error message \"SAPCAR error: format error in header\".<br /><br />Reason: The SAR archive was packed using SAPCAR Version 700. Since changes were made to the SAR archive format in this version, older SAPCAR versions can no longer unpack the archive (also refer to SAP Note 892842).<br /><br />Solution: Use SAPCAR Version 700 or SAPCAR Version 640 patch level 4. These versions can process the new archive format (refer to SAP Note 892842).</li>\r\n<li><strong>SAR archives cannot be loaded and unpacked using the function \"Load from front end\"</strong><br />Symptom: You want to unpack an SAR archive using the function \"Load from front end\". The system refuses the archive with the message \"File '???.SAR' is not a CAR or SAPCAR file'.<br /><br />Reason: The SAR archive was packed using SAPCAR Version 700. Since changes were made to the SAR archive format in this version, and the archive format versions were also changed, the Support Package Manager does not recognize the file as a valid SAR archive.<br /><br />Solution: SPAM/SAINT Version 700/0021 contains an adjustment to the new SAR archive format.<br />If the new SPAM/SAINT version is not yet available, unpack the SAR archive at operating system level using the SAPCAR utility. Note that you may require a new SAPCAR version (refer to the problem described above, or to SAP Note 892842).</li>\r\n<li><strong>Termination when importing in downtime-minimized mode on Microsoft SQL Server</strong><br />Symptom: The program may terminate in the IMPORT_PROPER phase when you import Support Packages using the downtime-minimized import mode:<br /> Phase: IMPORT_PROPER<br /> Reason for error : TP_FAILURE<br /> Return code: 0234<br /> Error message: uncaught error -&gt; function err_exit called<br /><br />Solution: The error is corrected with the 700 kernel patch level 75.<br />Update your system to at least patch level 75, and then start importing Support Packages.</li>\r\n<li><strong>Termination of table conversion (tp step \"N\") on MSSQL systems</strong><br />Symptom: When you import Support Packages on MSSQL Server systems, the import step for the table conversion (tp step \"N\") terminates. The job log of the associated background job RDDGEN0L contains the runtime error SAPSQL_SUBQUERY_ILL_TABLE_TYPE.<br /><br />Solution: To avoid the error, implement the corrections contained in Note 1042435 before you import the Support Package.<br />If you have already encountered the error, proceed as described in Note 1042435.</li>\r\n<li><strong>Procedure for termination in the phase XPRA_EXECUTION</strong><br />Symptom: The importing of Support Packages terminates in the phase XPRA_EXECUTION with return code 0012. In the related import log, you are referred to the job log of the XPRA job, in which runtime errors (for example, SYNTAX_ERROR) are listed as the cause of the termination.<br /><br />Solution: It is possible that the errors are caused by note corrections that were implemented before you imported the Support Packages and that were partially deimplemented when you imported the Support Package queue. You can correct such problems by performing a modification adjustment and by implementing the SAP Notes that are still valid (yellow or green traffic light in SPAU) again. The modification adjustment is already prepared at this point and it can be called easily in the Support Package Manager by choosing the menu path 'Extras'-&gt;'Modification Adjustment'.<br />To avoid such problems when you subsequently import in the production system, you can include the modification adjustment transports that were created in the development system in the Support Package queue. For more information, see the online documentation of the Support Package Manager ('i' button).</li>\r\n<li><strong>Support packages cannot be imported after implementation of ERP Enhancement Packages</strong><br />Symptom: You can no longer import the Support Packages relating to product ERP 600 after you have imported ERP Enhancement Packages. When you try to define a Support Package queue, the system displays the following error message:<br />OCS package ... does not match the current software component vector <br /><br />Solution: Due to several software components being upgraded to a new version, enhanced import conditions are necessary for Support Packages. Notes 1118803 and 1119856 describe how you can easily load these enhanced import conditions into your systems.</li>\r\n<li><strong>DB2/DB6 database: Termination with SQL error -107 in IMPORT_PROPER</strong><br />As of SAP NetWeaver 7.0 SR3 (SAP_BASIS Support Package 14), you can install systems on DB2/DB6 with the options \"Row Compression\" and \"Deferred Table Creation\". In systems that have been installed with these two options, you must implement the corrections that are contained in Note 1126127 before you import Support Packages. Otherwise, terminations may occur in the phase IMPORT_PROPER due to SQL error -107.</li>\r\n<li><strong>Very long runtime of phase CLEAR_OLD_REPORTS</strong><br />Symptom: When you use the downtime-minimized import mode, the phase CLEAR_OLD_REPORTS has a long runtime.<br /><br />Solution: In transaction SE11, create a non-unique index (for the table REPOSRC) with the name SPM using the fields R3STATE and PROGNAME (in this sequence). This index is delivered with Basis Support Package SAPKB70016 by default (see Note 1137503).</li>\r\n<li><strong>SQL Server 2008 database:  Import error if dbsl-lib- is obsolete</strong><br />Symptom: For more information, see Note 1260968.<br /><br />Solution: To avoid import errors in SQL Server 2008, you must upgrade the status of dbsl-lib to at least the status described in SAP Note 1152240 before you import Support Packages. Dbsl-lib is available on SAP Service Marketplace and has the description lib_dbsl_&lt;patch&gt;-&lt;xxxxxx&gt;.sar. Note 19466 contains information on downloading kernel patches.</li>\r\n<li><strong>SQL Server 2000 database:  Syntax error 170 when creating an index.</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Symptom: The phase IMPORT_PROPER terminates with an error in the \"Move Nametab\" step (step \"P\"). The log contains the following error messages:<br />Database error 170 at EXE<br />&gt; Line 1: Incorrect syntax near '('.<br /><br />Solution: Implement the solution contained in Note 1180553.</p>\r\n<ul>\r\n<li><strong>DB6 database: Unreported rollback during import</strong></li>\r\n</ul>\r\n<p>Make sure that you read HotNews <strong>1406740</strong> to avoid serious errors when importing Support Packages.</p>\r\n<ul>\r\n<li><strong>Oracle DB: Error ORA-1007 in import phase IMPORT_PROPER</strong></li>\r\n</ul>\r\n<p>Refer to SAP Note <strong>1514063</strong> and implement the corrections before you import Support Packages (if the correction instructions are still valid in your system).</p>\r\n<ul>\r\n<li><strong>Error in the Switch Framework after upgrading to NetWeaver 7.0 Enhancement Package 1.</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Symptom: After you upgrade to SAP NetWeaver 7.0 Enhancement Package 1 (this is part of ERP 6.0 EHP4), errors occur in the Switch Framework. As a result, inactive components may be inadvertently reported as active, which means that unnecessary CRTs must be included in a Support Package queue.<br /><br />Solution: To correct and avoid errors in the Switch Framework, use the Note Assistant to implement the following SAP Notes:<br /><strong>1237062</strong> \"SFW-HELPER ignores the components SRM_EXT and SRM_EXPL\"<br /><strong>1319582</strong> \"CL_SFW_HELPER-GET_SWITCHED_COMPONENTS is incorrect\"</p>\r\n<ul>\r\n<li><strong>Problems with BAdIs after you import Support Packages or Enhancement Package updates</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Refer to SAP Notes <strong>1521903 and 1548839</strong> to correct problems with BAdIs after you import Support Packages and Enhancement Package updates and to prevent these problems from occurring.</p>\r\n<ul>\r\n<li><strong>Performance improvements in phase XPRA_EXECUTION</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">SAP Notes 1420290 and 1558728 contain corrections that improve the performance of the after-import method of enhancement implementations and reduce the runtime of the import phase XPRA_EXECUTION as a result. Implement the correction instructions delivered with these SAP Notes <strong>before</strong> you import Support Packages.</p>\r\n<ul>\r\n<li><strong>Oracle DB: Long import runtimes<br /></strong>The main import (import phase IMPORT_PROPER) can take an extremely long time. This affects the DB Releases 9.2.0.7, 9.2.0.8, and 10.2.0.2. See Note 1028099 to solve the problem.</li>\r\n<li><strong>Syntax error in CL_SUSR_BASIC_TOOLS===========CP<br /></strong>Symptom: If SAP Note 1663177 is implemented in your system, it might not be possible for the import in the Support Package Manager to be continued because a syntax error occurs in CL_SUSR_BASIC_TOOLS===========CP when you import Basis Support Packages.<br /><br />Reason: If the Support Packages that contain the correction instructions (SAPKB70027, SAPKB70112 or SAPKB70212) are not part of the Support Package queue, there is a partial downgrade that causes the syntax error in user administration. Since the Support Package Manager checks the DDIC user by default at the start of the import process, this syntax error stops the import from continuing and therefore stops the error from being triggered.<br /><br />Solution: To avoid the error, the DDIC user check can be deactivated in the settings of the Support Package Manager (option \"Check for active DDIC user\"). This is possible as of SPAM/SAINT Version 0043.<br />If the error has already occurred, the deactivation can take place only in the configuration table PATCHECK.  For the entries with CHECK_NAME = \"DDIC_USER_REQ\", set the value of the field ACTIVE to \" \" (space/blank character). Then you can continue importing the Support Package queue.<br />After you import the Support Package queue, you can activate the check again in the settings.</li>\r\n<li><strong>Incorrect error messages during Switch Framework activation<br /></strong>Implement the corrections from SAP Note 1476315 before you import Support Packages to avoid errors during the Switch Framework and enhancement object activation.</li>\r\n<li><strong>Problems with implementation on DB2 for z/OS database with downward-compatible Kernel 7.20</strong><br />To avoid problems when importing Support Packages on a <strong>DB2 for z/OS </strong>database, implement the corrections from SAP Note 1708929.</li>\r\n</ul>\r\n<p><strong>6. Basis 7.00 Support Packages (component SAP_BASIS 700)</strong></p>\r\n<ul>\r\n<li><strong>SAPKB70001</strong><br />Symptom: After you import Basis Support Package SAPKB70001, the background system administration tools select background jobs incorrectly. As a result, background jobs may be deleted by mistake.<br /><br />Solution: You must take into account SAP Notes 715355 and 837691.<br />To solve the problem, import Basis Support Package SAPKB70002 or implement the corrections from the latest version of Note 715355.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70004</strong><br />Symptom: When you import Basis Support Package SAPKB70004, an error occurs in phase XPRA_EXECUTION. In the log of the after-import methods execution, you find:<br /> Installation of configuration VMC_TEST_APP/CACHE (0) has failed<br /> Installation of configuration VMC_TEST_APP/TEST (0) has failed<br /> Errors occurred during post-handling SVMCRT_TRANS_JCFG_AFTER_IMP for<br /> JCFG L<br /> The errors affect the following components:<br /> BC-VMC (Virtual Machine Container)<br /><br />Solution: Ensure that you use a kernel with patch level 18 or higher. If the error also occurs with a current kernel, refer to SAP Note 871402.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70002 - SAPKB70004</strong><br />Symptom: If you import Basis Support Packages SAPKB70002 and SAPKB70004 together in a single queue, the import process may terminate in the step DDIC_ACTIVATION.<br />The activation log displays:<br /><br />Activate index SXMSMSGINDLOG-001 (DDIC/17.03.06/05:49)<br />Table SXMSMSGINDLOG is not active in the ABAP/4 Dictionary<br />Index SXMSMSGINDLOG-001 could not be activated<br /><br />Solution: Continue with the process of importing the Support Package queue. When you repeat the DDIC activation, the error no longer occurs and the objects are activated correctly.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70002, SAPKB70005, SAPKB70006</strong><br />Symptom: After you import the Support Package mentioned, syntax errors occur in the Note Assistant and in the modification adjustment.<br /><br />Reason: This problem only occurs if you implement the corrections from Note 948389 in Version 0003 (or lower) before the import. Support Packages SAPKB70002, SAPKB70005, and SAPKB70006 partly undo the corrections, which leads to syntax errors.<br /><br />Solution: You can prevent the problem in different ways:<br />1. Import the Support Packages mentioned together with Support Package SAPKB70009 in a queue.<br />2. Note 948389 should be implemented at least in Version 0004. As of this version, the corrections that build on each other are compatible, so no syntax errors occur after you import Support Packages SAPKB70002, SAPKB70005, and SAPKB70006. For this reason, check, whether Note 948389 is already implemented in Version 0004 (or higher). If this is not the case, download the latest version with the Note Assistant and implement it.<br />If you have already encountered the error, you can correct it by importing Support Package SAPKB70009. If this is not possible, contact SAP Support. In this case, log a Support message under component BC-UPG-NA.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70006</strong><br />Symptom: After you import Basis Support Package SAPKB70006, table conversions terminate. The syslog contains error messages for the runtime error TSV_TNEW_PAGE_ALLOC_FAILED.<br /><br />Solution: Import Basis Support Package SAPKB70007 as soon as possible, or implement the correction from Note 917999 using Note Assistant.<br />If the error occurs during the table conversion process when you import a Support Package, you must manually implement the correction from Note 917999, since Note Assistant is locked at this point.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70005 - SAPKB70008</strong><br />Symptom: If you import Basis Support Packages SAPKB70005 and SAPKB70008 together in a queue, the TEST_IMPORT step returns an error for Support Package SAPKB70008. The following error message occurs in the test import log of SAPKB70008:<br />Function TREX_BUILD_COMP_DESCR (TREX_UTIL_EXT 02) does not fit<br /> into the existing function group ((TREX_UTIL 23))<br />Function TREX_FILL_ATTRDEF_BY_TABNAME (TREX_UTIL_EXT 03) does not fit <br /> into the existing function group ((TREX_UTIL 25))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70009</strong><br />Symptom: If you import Basis Support Package SAPKB70009 in the same queue as other Support Packages, or if you import further Support Packages after successfully importing SAPKB70009, the import may terminate in phase XPRA_EXECUTION with return code 0012. The after import method log records that the process terminated with a runtime error after the after import method SRM_FILL_KC_TABLES_AFTER_IMP was started for SRHL objects.<br /><br />Reason: For a short time (September 02, 2006 - September 05, 2006), a version of SAPKB70009 was available, which contained an incorrect implementation of the after import method SRM_FILL_KC_TABLES_AFTER_IMP. This is indicated by the EPS file name CSN0120061532_0024341.PAT in the downloaded CAR archive.<br /><br />Solution: <strong>Do not use the incorrect versions of Support Package SAPKB70009!</strong> A corrected version has been made available and can be recognized by the EPS file CSN0120061532_0024352.PAT.<br />If you have already encountered the error, implement Note 967821 to correct the incorrect after import method SRM_FILL_KC_TABLES_AFTER_IMP. This correction is also available in Basis Support Package SAPKB70010.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70010</strong><br />Symptom: The importing of Basis Support Package SAPKB70010 terminates in the phase AUTO_MOD_SPAU because a syntax error occurred in program SAPLSEOK (\"Field 'NOTE_ASSISTANT_MODE' is unknown\").<br /><br />Reason: This problem occurs only if you implemented Note 1011608 before importing the Support Packages, and Basis Support Package SAPKB70012 was not part of the queue.<br /><br />Solution: To avoid the problem, it is essential that you import the Basis Support Packages SAPKB70010 and SAPKB70012 together in the same queue.<br />If you have already  encountered the problem, use the version management of the version to restore the include LSEOKTOP before you import Support Package SAPKB70010. This repairs the syntax error and you can continue to import the queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70006 - SAPKB70010</strong><br />Symptom: If you import Basis Support Packages SAPKB70006 and SAPKB70010 together in a queue, the TEST_IMPORT step returns an error for Support Package SAPKB70010. The following error message occurs in the test import log of SAPKB70010:<br />Function TREX_EXT_GET_TRACE_FILE_LIST (TREX_EXT_TRACE 03)<br /> does not fit into the existing function group<br /> ((TREX_EXT_ADMINISTRATION)<br />Function TREX_RFC_GET_TRACE_FILE (TREX_RFC_TRACE 01)<br /> does not fit into the existing function group<br /> ((TREX_RFC_ADMINISTRATION 23))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70011</strong><br />Symptom: When you import Support Package SAPKB70011, the Support Package Manager may terminate in the IMPORT_PRPOPER phase with the DBIF_RSQL_SQL_ERROR runtime error. At this time, the main import of the entire Support Package queue has already been completed. The long text of the short dump informs you that the SQL error occurred during the access to the TMSCSYS table.<br /><br />Solution: Call the Support Package Manager again and continue importing the Support Package queue. The error is corrected after the automatic recompilation of the ABAP Coding that is affected by the changes to the TMSCSYS table.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70011</strong><br />Symptom: After you import Support Package SAPKB70011, errors occur when you generate classes in the enhancement framework.<br /><br />Solution: Implement the corrections contained in Note 1037232 or import Support Package SAPKB70012.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70009 - SAPKB70012</strong><br />Symptom: If you import Basis Support Packages SAPKB70009 and SAPKB70012 together in a queue, the TEST_IMPORT step returns an error for Support Package SAPKB70012. The test import log of SAPKB70012 contains the following error message:<br /> Function \"MEREP_CASCADE_DELETE (MEREP_RUNTIME 42)\" does not fit<br /> into the existing function group \"(MEREP_RUNTIME_MASS 02)\"<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70010 - SAPKB70012 (SAPKB70014)</strong><br />Symptom: If you import Basis Support Packages SAPKB70010 and SAPKB70012 together in a queue, the TEST_IMPORT step returns an error for Support Package SAPKB70012. The test import log of SAPKB70012 contains the following error message:<br />Function SPCPC_I_BUILD_DEVICE (SPCPCCLI 04) does not fit into<br /> the existing function group ((SPCPC 19))<br />Function SPCPC_I_DELETE_DEVICE (SPCPCCLI 05) does not fit into<br /> the existing function group ((SPCPC 20))<br />If the Support Package SAPKB70014 is also in the queue, the same test import error occurs there.<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70012</strong><br />Symptom: After you import Support Package SAPKB70012, jobs that were scheduled using DB13 or DBACOCKPIT terminate on MSSQL Server systems and the system issues the following error message:<br />function ... expects parameter '@dbList', which was not<br /> supplied. [SQLSTATE 42000] (Error 201). The step failed.<br /><br />Solution: After you import the Support Package, you must delete DB13 jobs and then recreate them. The exact procedure is described in Note 1057855.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70012</strong><br />Symptom: If ABAP generation is activated when you import Support Packages, ABAP generation errors may occur when you import Support Package SAPKB70012 into a CRM system. In the generation log, you find the following error messages:<br /> Program CRM_SE30_CMP, include RSAT0F01: Syntax error in line 000085<br /> Field TIME_T_EVENT is unknown. It is neither in one of the<br /> specified tables nor defined by a 'DATA' statement<br /><br />Solution: This program is not used. Therefore, you can use 'Extras'-&gt;'Ignore generation errors' to ignore this generation error and continue the import.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70012 (only on DB2/DB6)</strong><br />Symptom: The importing of Support Package SAPKB70012 terminates in the phase IMPORT_PROPER with an error in the tp step \"S\". The related log DS&lt;YY&gt;&lt;MM&gt;&lt;DD&gt;.&lt;SID&gt; displays the following error message:<br />2EEDA300 No active nametab exists for \"DB6PMSQ_DB\"<br />This error can occur only on systems that run on a DB2/DB6 database.<br /><br />Reason: Depending on the system history, the table DB6PMSQ_DB may exist on the database, but no reference exists in DDIC.<br /><br />Solution: If you have already encountered the error, delete the table DB6PMSQ_DB on the database.<br />To avoid the error, check whether the table DB6PMSQ_DB exists on the database but not in DDIC (SE11) before you import the Support Package. If this is the case, delete the database table. </li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70013</strong><br />Symptom: If you implemented the corrections from Note 1023163, a termination may occur and the system may become unusable when you import the Support Package SAPKB70013.<br /><br />Reason: When you import the Support Package SAPKB70013, the corrections from Note 1023163 are partially undone. This causes such a large inconsistency that the system becomes unusable.<br /><strong>If you import the Support Packages SAPKB70013 and SAPKB70014 in a queue, this error does not occur because SAPKB70014 contains all of the corrections from Note 1023163.</strong><br /><br />Solution: Proceed as described in SAP Note 1112065: <strong>Before</strong> you import Support Package SAPKB70013, deimplement SAP Note 1023163.<br />If the error has already occurred, use the transport attached to Note 1112065 to return the system to a consistent state.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70013</strong><br />Symptom: After you import Support Package SAPKB70013, it may take a very long time to refresh the TMS QA worklist in the Transport Management System (transaction STMS).<br /><br />Solution: Proceed as described in Note 1019572.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70013</strong><br />Symptom: After you import Support Package SAPKB70013, the system displays incorrect switch values for business functions in transaction SWF5. As a result, you have the option of activating incompatible business functions or of switching to an incompatible business function set.<br /><br />Solution: Implement the corrections contained in Note 1082597 or import Basis Support Package SAPKB70014.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70013</strong><br />Symptom: After importing the Support Package SAPKB70013, the system does not check the software-logistic requirements of the affected software components when you activate a business function set in transaction Sfw5. For this reason, software components may be activated even if these do not have the required Support Package level. This may result in errors in the affected applications.<br /><br />Solution: Implement the corrections contained in Note 1144412 or import Basis Support Package SAPKB70016.<br />If you activated business function sets in the Basis Support Package levels 13 to 15 in transaction SFW5, check the Support Package levels of the affected software components (ECC-DIMP, FI-CA, FI-CAX, INSURANCE, IS-CWM, IS-H, IS-M, IS-OIL, IS-PS-CA, IS-UT) Support Package Manager. The levels must be the same as for component SAP_APPL. Import the missing Support Packages if required.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70014</strong><br />Note the following: Support Package SAPKB70014 provides new functions in the areas web service runtime, web service configuration and web service monitoring. To use these new functions, you must carry out several administrative activities.  More information is available at: <br />http://help.sap.com -&gt; SAP NetWeaver -&gt; SAP NetWeaver 7.0 (2004s) -&gt; Support Package Stack 14 -&gt; System Administration -&gt; Technical Operations Manual -&gt; Administration of SAP NetWeaver IT Scenarios -&gt; Enabling Enterprise Services<br />The information is also available in the document attached to this SAP Note.</li>\r\n<li><strong>SAPKB70014</strong><br />If syntax errors occur in the program SAPLPA_PACKAGE_CHECKS during or after the import of Basis Support Package SAPKB70014, refer to the solution information in SAP Note 1281521.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70011 - SAPKB70016</strong><br />Symptom: After you import the Support Packages SAPKB70011, SAPKB70012, SAPKB70013, SAPKB70014, SAPKB70015, and SAPKB70016, syntax errors occur in the class CL_GUI_ALV_GRID because corrections from SAP Notes have been partially overwritten. This may cause important Basis functions (such as the background systems) to no longer work.<br /><br />Solution: Before you import the specified Basis Support Packages, see the information in Note 1249184.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70014 - SAPKB70016</strong><br />Symptom: If importing a queue with the Support Packages SAPKB70014 and SAPKB70016 terminates after the main import in the phase IMPORT_PROPER, a syntax error occurs in the class CL_SFW_SYSTEM_SETTINGS. As a result, you can no longer call the Support Package Manager or the Add-On Installation Tool and the import cannot be continued.<br /><br />Solution: Implement the solution options contained in Note 1281321.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70017</strong><br />Symptom: When importing a queue that contains the Support Package SAPKB70017, errors may occur in phase XPRA_EXECUTION during the after-import-method execution for other Support Packages also in the queue. The relevant AIM logs contain a note regarding the termination of the batch job and the job log for that batch job contains a SYNTAX_ERROR as cause for the termination. The corresponding \"SYNTAX_ERROR\" contains<br /> \"CONTEXT\" addition is not permitted for BAdI<br /> \"CLS_CHARACTERIZATION\"<br />as cause for the error, which can be traced back to an inactive change in the BAdI CLS_CHARACTERIZATION.<br /><br />Solution: Continue the import. During the first run of XPRA_EXECUTION, the BAdI CLS_CHARACTERIZATION was activated, so that the syntax error no longer occurs during a repeat run.<br />To prevent this error from the start, import Support Package SAPKB70017 together with Support Package SAPKB70018 in one queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70011, SAPKB70013, SAPKB70018</strong><br />Symptom: If you have implemented SAP Note <strong>1273082</strong> and you import a Support Package queue that contains Support Package SAPKB70011, SAPKB70013, or SAPKB70018, serious problems can occur in the Enhancement Framework following the import. These problems cannot be solved without help from SAP.<br /><br />Solution: Since the corrections from Note 1273082 are contained in the Support Package SAPKB70019, you must include this Support Package in the queue.<br />If you cannot do this, you must read the SAP Notes in the latest version of SAP Note 1273082 and, if necessary, use the Note Assistant to implement the enhanced corrections.<br />Alternatively, you can use the Note Assistant to deimplement SAP Note 1273082 before you import the Support Package queue and then implement this SAP Note again afterwards.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70015 - SAPKB70018</strong><br />Symptom: If your system is at a 7.00 Basis Support Package level between 0015 and 0018, language-dependent parts (for example, the short text) of domain fixed values may be lost when the system transports domains (with normal TMS transports or with Support Packages).</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Solution: The problem is solved with Basis Support Package SAPKB70019. You can also avoid the problem by implementing the corrections from Notes 1109375, 1162171 and 1306409.<br />If the problem has already occurred in your system, proceed as described in Notes 1407057 and 1420693.</p>\r\n<ul>\r\n<li><strong>SAPKB70019 (only on DB4)</strong><br />Symptom: Affects only systems with database DB4 (DB2 for IBM i). When you import Support Package SAPKB70019, many database journal entries may be created.  This can cause the disk storage to overflow.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Solution: Before you import the Support Package, you must check your database journal settings using Note 1366799.</p>\r\n<ul>\r\n<li><strong>SAPKB70020</strong><br />Symptom: After you import the Basis Support Package SAPKB70020, problems occur in the validity check of the ALE distribution models. See Note 1398380 for detailed information.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Solution: Implement the corrections contained in SAP Note 1398380 and follow the instructions in the SAP Note.<br />You can prevent errors by importing the Basis Support Package SAPKB70022 in conjunction with SAPKB70020.</p>\r\n<ul>\r\n<li><strong>SAPKB70020 - SAPKB70022</strong><br />Symptom: If you have implemented SAP Note <strong>1108326</strong> and you import a Support Package queue that contains Support Package SAPKB70020, SAPKB70021, or SAPKB70022, serious problems can occur in the Enhancement Framework following the import. These problems cannot be solved without help from SAP.<br /><br />Solution: Since the corrections from Note 1108326 are contained in the Support Package SAPKB70023, you must include this Support Package in the queue.<br />If this is not possible, you must deimplement the SAP Note before you import the Support Packages. You can implement the SAP Note again after you import the Support Packages.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70022</strong><br />Symptom: If you have implemented Note 1420178, errors occur in the user administration (syntax error in class CL_SUSR_BASIC_TOOLS) after you import Support Package SAPKB70022 and before the modification adjustment. As a result, user name checks in the Support Package Manager no longer work also and the importing of the Support Package cannot be terminated. <br /><br />Solution:<br />a) You can avoid the problem if you import the subsequent Support Package SAPKB70023 together with SAPKB70022 because the complete correction of Note 1420178 is then implemented.<br />b) As an alternative to solution a), you can try to deimplement Note 1420178 before the Support Package import. You can then implement it again. <strong>Note:</strong>  However, if SAP Note 1450079 has also been implemented, you cannot deimplement the SAP Note due to dependencies between the two SAP Notes. If this is the case, you must use solution a).</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70022</strong><br />Symptom: When you import Support Package SAPKB70022 with ABAP or screen generation activated, generation errors occur in the phase ABAP_GENERATION for the screens SAPLENH_BADI_EDT_DEF 2200 and SAPLENH_EDT_BADI 2100.<br /><br />Solution: You can ignore these generation errors.  Note 1498113 provides details about this problem and also contains a solution to prevent this generation error.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70023</strong><br />Symptom: After you import Support Package SAPKB70023, a syntax error may occur in the central program MENUSYST. As a result, you can no longer log on to the SAP system.<br /><br />Solution: Proceed according to the solution provided in Note 1285869.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70022, SAPKB70023</strong><br />Symptom: When you import and after you import Support Packages SAPKB70022 and SAPKB70023, errors and terminations may occur in the Switch Framework because syntax errors occur due to missing data types.<br /><br />Solution: Follow the instructions contained in Note 1532661 and implement the corrections contained in Notes 1414700 and 1436598 that are specified there; do this before you import the Support Packages.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70023 (only on Microsoft SQL Server 2000)</strong><br />Symptom: After you import Support Package SAPKB70023, errors occur during the index generation for tables in systems on MS SQL Server 2000. The SQL error occurs with the message \"Line 1: Incorrect syntax near 'OFF'\". This error may occur when you activate tables in transaction SE11 and also when you import transports or Support Packages.<br /><br />Solution: You must implement the corrections from Note 1542671 before you import any further Support Packages or transports. As an alternative, you can also import Support Package SAPKB70024 together with SAPKB70023.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70023</strong><br />Symptom: When you import Basis Support Package SAPKB70023, a termination (rc 0012) occurs in the phase XPRA_EXECUTION. The job log of the background job RDDEXECL displays that the runtime error CONNE_IMPORT_CONVERSION_ERROR has occurred.<br /><br />Solution: See Note 1572314 (especially solution option 4.)</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70018 - SAPKB70024</strong><br />Symptom: If you have implemented Note 1411877, errors occur in background processing during the import of the Basis Support Packages. As a result, the import of the Support Package queue cannot be completed.<br /><br />Solution: The problem is prevented if Support Package SAPKB70025 is included in the Support Package queue.<br />If this is not possible, you <strong> must</strong> <strong> deimplement </strong> SAP Note 1411877 <strong>again before the </strong> import of the Support Package queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70025</strong><br />Symptom: After you import Basis Support Package SAPKB70025, enhancement options are displayed incorrectly in the ABAP editor and they lead to errors when saving and activating.<br /><br />Solution: The problem is prevented if Support Package SAPKB70026 is included in the Support Package queue.<br />Alternatively, you can implement the corrections from SAP Note 1596680.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70016 - SAPKB70026 (only on Microsoft SQL Server)</strong><br />Symptom: If SAP Note 1660078 is implemented, there are system problems after you import the Basis Support Packages. As a result, the import of the Support Package queue cannot be completed.<br /><br />Solution: The problem is prevented if Support Package SAPKB70026 is included in the Support Package queue.<br />If this is not possible, you <strong> must</strong> <strong> deimplement </strong> SAP Note 1660078 <strong>again before the </strong> import of the Support Package queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70026 (on DB2 for IBM i (AS400))</strong><br />Symptom: After you import SAPKB70026, you can no longer log in. The logon fails with the error message:<br /> Maximum number of internal sessions reached.<br /><br />Solution: You can avoid this problem if you import Support Package SAPKB70029 together with SAPKB70026.<br />If the error already occurred, follow the solution steps from SAP Note 1724702.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70027</strong><br />Symptom: After you import Support Package SAPKB70027, errors occur when you save function modules and methods for the modification adjustment (transaction SPAU).<br /><br />Solution: You can avoid this problem if you import Support Package SAPKB70028 in a queue with SAPKB70027.<br />If the error already occurred, implement the current version of SAP Note 1680204 in your system.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70027</strong><br />Symptom: If an old version of SAP Note <strong>1749824</strong> is implemented, a syntax error occurs in the function group SDSB after you import SAPKB70027. As a result, you can no longer log in to the system.<br /><br />Solution: To avoid this error, you have to implement the latest version of SAP Note 1749824 (Version 5). Check this in the Note Assistant.<br />For details about this error and for solution options if you have already encountered this error, refer to SAP Note <strong>1809597</strong>.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70028</strong><br />Symptom: If the import of the Support Package SAPKB70028 terminates in the phase IMPORT_PROPER, it cannot be continued in the Support Package Manager because this leads to a syntax error in SAPLSUU1:<br />The row type from \"USRXX_DELETE\" cannot be converted to the table \"USR07\" in the key part.<br /><br />Solution: To avoid the error, the ABAP Dictionary user check can be deactivated in the settings of the Support Package Manager (option \"Check for active DDIC user\"). This is possible as of SPAM/SAINT Version 0043.<br />If the error has already occurred, the deactivation can take place only in the configuration table PATCHECK. For the entries with CHECK_NAME = \"DDIC_USER_REQ\", set the value of the field ACTIVE to \" \" (space/blank character). Then you can continue importing the Support Package queue.<br />After you import the Support Package queue, you can activate the check again in the settings.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70028</strong><br />Symptom: After you implement Basis Support Package SAPKB70028, a termination occurs when you call the Note Assistant due to a syntax error in the function group SCWN.<br /><br />Reason: For a short time (21.01.2013 - 07.02.2013), a version of SAPKB70028 was available that contained this function group in an incorrect state. You can recognize this Support Package version by the EPS file name CSR0120031469_0072158.PAT in the downloaded SAR archive.<br />Solution: Do NOT use the incorrect version of the Support Package SAPKB70028. You can recognize the corrected version by the EPS file name CSN0120061532_0074202.PAT.<br /><br />If you have already experienced the error, you can implement SAP Note 1821879 to solve the problem yourself.</li>\r\n<li><strong>SAPKB70029<br /></strong>Symptom: Following the import of Basis Support Package SAPKB70029, SAP Notes that do not contain ABAP Dictionary object corrections can be displayed for adjustment during the modification adjustment of ABAP Dictionary objects (SPDD).<br /><br />Solution: Refer to SAP Note 1910464 and implement the corrections delivered there. Alternatively, you can import Basis Support Package SAPKB70030.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following Basis Support Package queues:<br />SAPKB70001 - SAPKB700.. (highest available Basis Support Package).<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, you need instructions on optimal or permitted queues from those people who produce the add-ons or CRTs used.</p>\r\n<p><strong>7. Basis 7.01 Support Packages (component SAP_BASIS 701)</strong></p>\r\n<ul>\r\n<li><strong>Up to SAPKB70103</strong><br />Symptom: If your system is at a 7.01 Basis Support Package level between 0000 and 0003, language-dependent parts (for example, the short text) of domain fixed values may be lost when the system transports domains (with normal TMS transports or with Support Packages).</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Solution: The problem is solved with Basis Support Package SAPKB70104. You can also avoid the problem by implementing the corrections from Notes 1109375, 1162171 and 1306409.<br />If the problem has already occurred in your system, proceed as described in SAP Notes 1407057 and 1420693.</p>\r\n<ul>\r\n<li><strong>SAPKB70103</strong><br />Symptom: If you have implemented SAP Note <strong>1273082</strong> and you import a Support Package queue that contains Support Package SAPKB70103, serious problems can occur in the Enhancement Framework following the import. These problems cannot be solved without help from SAP.<br /><br />Solution: Since the corrections from Note 1273082 are contained in the Support Package SAPKB70104, you must include this Support Package in the queue.<br />If you cannot do this, you must read the information in the latest version of SAP Note 1273082 and, if necessary, use the Note Assistant to implement the enhanced corrections.<br />Alternatively, you can use the Note Assistant to deimplement SAP Note 1273082 before you import the Support Package queue and then implement this SAP Note again afterwards.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70105</strong><br />Symptom: After you import the Basis Support Package SAPKB70105, problems occur in the validity check of the ALE distribution models. See SAP Note 1398380 for detailed information.</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Solution: Implement the corrections contained in SAP Note 1398380 and follow the instructions in the SAP Note.<br />You can prevent errors by importing the Basis Support Package SAPKB70107 in conjunction with SAPKB70105.</p>\r\n<ul>\r\n<li><strong>SAPKB70105 - SAPKB70107</strong><br />Symptom: If you have implemented SAP Note <strong>1108326</strong> and you import a Support Package queue that contains Support Package SAPKB70105, SAPKB70106, or SAPKB70107, serious problems can occur in the Enhancement Framework following the import. These problems cannot be solved without help from SAP.<br /><br />Solution: Since the corrections from Note 1108326 are contained in the Support Package SAPKB70108, you must include this Support Package in the queue.<br />If this is not possible, you must deimplement the SAP Note before you import the Support Packages. You can implement the SAP Note again after you import the Support Packages.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70106 - SAPKB70111 (only on Microsoft SQL Server)</strong><br />Symptom: If SAP Note 1660078 is implemented, there are system problems after you import the Basis Support Packages. As a result, the import of the Support Package queue cannot be completed.<br /><br />Solution: The problem is prevented if Support Package SAPKB70112 is included in the Support Package queue.<br />If this is not possible, you <strong> must</strong> <strong>deimplement </strong> SAP Note 1660078 again <strong>before</strong> the import of the Support Package queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70107</strong><br />Symptom: If you have implemented Note 1420178, errors occur in the user administration (syntax error in class CL_SUSR_BASIC_TOOLS) after you import Support Package SAPKB70107 and before the modification adjustment. As a result, user name checks in the Support Package Manager no longer work also and the importing of the Support Package cannot be terminated. <br /><br />Solution:<br />a) You can prevent the problem from occurring if you import the subsequent Support Package SAPKB70108 together with SAPKB70107 because the complete correction of Note 1420178 is then implemented.<br />b) As an alternative to solution a), you can try to deimplement SAP Note 1420178 before the Support Package import. You can then implement it again. <strong>Caution:</strong>  However, if SAP Note 1450079 has also been implemented, you cannot deimplement the SAP Note due to dependencies between the two SAP Notes. If this is the case, you must use solution a).</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70107</strong><br />Symptom: If you have implemented SAP Note <strong>1473316</strong> and you import a Support Package queue that contains Support Package SAPKB70107, serious problems can occur in the Enhancement Framework during the import. These problems cannot be solved without help from SAP.<br /><br />Solution: Since the corrections from Note 1473316 are contained in the Support Package SAPKB70108, you must include this Support Package in the queue.<br />If this is not possible, you must deimplement the SAP Note before you import the Support Packages. You can implement the SAP Note again after you import the Support Packages.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70107</strong><br />Symptom: When you import Support Package SAPKB70107 with ABAP or screen generation activated, generation errors occur in the phase ABAP_GENERATION for the screens SAPLENH_BADI_EDT_DEF 2200 and SAPLENH_EDT_BADI 2100.<br /><br />Solution: You can ignore these generation errors. SAP Note 1498113 provides details about this problem and also contains a solution to prevent this generation error.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70106, SAPKB70107</strong><br />Symptom: When you import and after you import Support Packages SAPKB70106 and SAPKB70107, errors and terminations may occur in the Switch Framework because syntax errors occur due to missing data types.<br /><br />Solution: Follow the instructions contained in SAP Note 1532661 and implement the corrections contained in SAP Notes 1414700, 1366282, and 1436598 which are specified there; do this before you import the Support Packages.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70108</strong><br />Symptom: After you import Support Package SAPKB70108, a syntax error may occur in the central program MENUSYST. As a result, you can no longer log on to the SAP system.<br /><br />Solution: Proceed in accordance with the solution provided in SAP Note 1285869.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70108 (only on Microsoft SQL Server 2000)</strong><br />Symptom: After you import Support Package SAPKB70108, errors occur during the index generation for tables in systems on MS SQL Server 2000. The SQL error occurs with the message \"Line 1: Incorrect syntax near 'OFF'\". This error may occur when you activate tables in transaction SE11 and also when you import transports or Support Packages.<br /><br />Solution: You must implement the corrections from Note 1542671 before you import any further Support Packages or transports. As an alternative, you can also import Support Package SAPKB70109 together with SAPKB70109.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70108</strong><br />Symptom: When you import Basis Support Package SAPKB70108, a termination (rc 0012) occurs in the phase XPRA_EXECUTION. The job log of the background job RDDEXECL displays that the runtime error CONNE_IMPORT_CONVERSION_ERROR has occurred.<br /><br />Solution: See SAP Note 1572314 (especially solution option 4).</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70107 - SAPKB70109</strong><br />Symptom: If you have implemented Note 1411877, errors occur in background processing during the import of the Basis Support Packages. As a result, the import of the Support Package queue cannot be completed.<br /><br />Solution: The problem is prevented if Support Package SAPKB70110 is included in the Support Package queue.<br />If this is not possible, you <strong>must</strong> <strong>deimplement</strong> SAP Note 1411877 again <strong>before</strong> the import of the Support Package queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70111 (on DB2 for IBM i (AS400))</strong><br />Symptom: After you import SAPKB70111, you can no longer log in. The logon fails with the error message:<br /> Maximum number of internal sessions reached.<br /><br />Solution: You can avoid this problem if you import Support Package SAPKB70113 together with SAPKB70111.<br />If the error has already occurred, follow the solution steps from SAP Note 1724702.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70112</strong><br />Symptom: After you import Support Package SAPKB70112, errors occur when you save function modules and methods for the modification adjustment (transaction SPAU).<br /><br />Solution: You can avoid this problem if you import Support Package SAPKB70113 in a queue with SAPKB70112.<br />If the error has already occurred, implement the current version of SAP Note 1680204 in your system.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70112</strong><br />Symptom: If an old version of SAP Note <strong>1749824</strong> is implemented, a syntax error occurs in the function group SDSB after you import SAPKB70112. As a result, you can no longer log onto the system.<br /><br />Solution: To avoid this error, you have to implement the latest version of SAP Note 1749824 (Version 5). Check this in the Note Assistant.<br />For details about this error and for solution options if you have already encountered this error, refer to SAP Note <strong>1809597</strong>.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70113</strong><br />Symptom: If the import of the Support Package SAPKB70113 terminates in the phase IMPORT_PROPER, it cannot be continued in the Support Package Manager because this leads to a syntax error in SAPLSUU1:<br />The row type from \"USRXX_DELETE\" cannot be converted to the table \"USR07\" in the key part.<br /><br />Solution: To avoid the error, you can deactivate the ABAP Dictionary user check in the settings of the Support Package Manager (option \"Check for active DDIC user\"). This is possible as of SPAM/SAINT Version 0043.<br />If the error has already occurred, the deactivation can take place only in the configuration table PATCHECK. For the entries with CHECK_NAME = \"DDIC_USER_REQ\", set the value of the field ACTIVE to \" \" (space/blank character). Then you can continue importing the Support Package queue.<br />After you import the Support Package queue, you can activate the check again in the settings.</li>\r\n<li><strong>SAPKB70114<br /></strong>Symptom: Following the import of Basis Support Package SAPKB70114, SAP Notes that do not contain ABAP Dictionary object corrections can be displayed for adjustment during the modification adjustment of ABAP Dictionary objects (SPDD).<br /><br />Solution: Refer to SAP Note 1910464 and implement the corrections delivered there. Alternatively, you can import Basis Support Package SAPKB70115.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we have the following recommendations for Basis Support Package queues:<br />SAPKB70101 - SAPKB701.. (highest available Basis Support Package).<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the add-ons or CRTs used should provide information about the optimal or permitted queues.</p>\r\n<p><strong>8. Basis 7.02 Support Packages (component SAP_BASIS 702)</strong></p>\r\n<ul>\r\n<li><strong>SAPKB70202 - SAPKB70204</strong><br />Symptom: If you have implemented SAP Note <strong>1108326</strong> and you import a Support Package queue that contains Support Package SAPKB70202, SAPKB70203, or SAPKB70204, serious problems can occur in the Enhancement Framework following the import. These problems cannot be solved without help from SAP.<br /><br />Solution: Since the corrections from Note 1108326 are contained in the Support Package SAPKB70205, you must include this Support Package in the queue.<br />If this is not possible, you must deimplement the SAP Note before you import the Support Packages. You can implement the SAP Note again after you import the Support Packages.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70202, SAPKB70206</strong><br />Symptom: When you import and after you import Support Packages SAPKB70202 - SAPKB70206, errors and terminations may occur in the Switch Framework because syntax errors occur due to missing data types.<br /><br />Solution: Follow the instructions contained in SAP Note 1532661 and implement the corrections contained in SAP Notes 1414700, 1366282, and 1436598, which are specified there; do this before you import the Support Packages.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70204, SAPKB70205</strong><br />Symptom: If you have implemented Note 1420178, errors occur in the user administration (syntax error in class CL_SUSR_BASIC_TOOLS) after you import Support Packages SAPKB70204 and SAPKB70205, and before the modification adjustment. As a result, user name checks in the Support Package Manager no longer work also and the importing of the Support Packages cannot be terminated. <br /><br />Solution:<br />a) You can prevent the problem from occurring if you import the subsequent Support Package SAPKB70206 together with SAPKB70204 and SAPKB70205 because the complete correction of Note 1420178 is then implemented.<br />b) As an alternative to solution a), you can try to deimplement SAP Note 1420178 before the Support Package import. You can then implement it again. <strong>Caution:</strong>  However, if SAP Note 1450079 has also been implemented, you cannot deimplement the SAP Note due to dependencies between the two SAP Notes. If this is the case, you must use solution a).</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70204</strong><br />Symptom: If you have implemented SAP Note <strong>1473316</strong> and you import a Support Package queue that contains Support Package SAPKB70204, serious problems can occur in the Enhancement Framework during the import. These problems cannot be solved without help from SAP.<br /><br />Solution: Since the corrections from Note 1473316 are contained in the Support Package SAPKB70205, you must include this Support Package in the queue.<br />If this is not possible, you must deimplement the SAP Note before you import the Support Packages. You can implement the SAP Note again after you import the Support Packages.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70206 (only on Microsoft SQL Server 2000)</strong><br />Symptom: After you import Support Package SAPKB70206, errors occur during the index generation for tables in systems on MS SQL Server 2000. The SQL error occurs with the message \"Line 1: Incorrect syntax near 'OFF'\". This error may occur when you activate tables in transaction SE11 and also when you import transports or Support Packages.<br /><br />Solution: You must implement the corrections from Note 1542671 before you import any further Support Packages or transports. As an alternative, you can also import Support Package SAPKB70207 together with SAPKB70206.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70206</strong><br />Symptom: When you import Basis Support Package SAPKB70206, a termination (rc 0012) occurs in the phase XPRA_EXECUTION. The job log of the background job RDDEXECL displays that the runtime error CONNE_IMPORT_CONVERSION_ERROR has occurred.<br /><br />Solution: See SAP Note 1572314 (especially solution option 4).</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70203 - SAPKB70211 (only on Microsoft SQL Server)</strong><br />Symptom: If SAP Note 1660078 is implemented, there are system problems after you import the Basis Support Packages. As a result, the import of the Support Package queue cannot be completed.<br /><br />Solution: The problem is prevented if Support Package SAPKB70212 is included in the Support Package queue.<br />If this is not possible, you <strong>must</strong> <strong>deimplement</strong> SAP Note 1660078 again <strong>before</strong> the import of the Support Package queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70204 - SAPKB70208 (Microsoft SQL Server)</strong><br />To avoid the error documented in SAP Note 1630727 (SQL error 207: \"Invalid column name 'area'\"), import the Support Packages in blocks:<br />SAPKB70201 - SAPKB70207<br />SAPKB70208 - SAPKB702xxx (highest available Support Package)</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70204 - SAPKB70208</strong><br />Symptom: If you have implemented Note 1411877, errors occur in background processing during the import of the Basis Support Packages. As a result, the import of the Support Package queue cannot be completed.<br /><br />Solution: The problem is prevented if Support Package SAPKB70209 is included in the Support Package queue.<br />If this is not possible, you <strong>must</strong> <strong>deimplement</strong> SAP Note 1411877 again <strong>before</strong> the import of the Support Package queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70210 (on DB2 for IBM i (AS400))</strong><br />Symptom: After you import SAPKB70210, you can no longer log in. The logon fails with the error message:<br /> \"Maximum number of internal sessions reached\".<br /><br />Solution: You can avoid this problem if you import Support Package SAPKB70213 together with SAPKB70210.<br />If the error has already occurred, follow the solution steps from SAP Note 1724702.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70211, SAPKB70212</strong><br />Symptom: If an old version of SAP Note <strong>1749824</strong> is implemented, a syntax error occurs in the function group SDSB after you import SAPKB70211 or SAPKB70212. As a result, you can no longer log onto the system.<br /><br />Solution: To avoid this error, you have to implement the latest version of SAP Note 1749824 (Version 5). Check this in the Note Assistant.<br />For details about this error and for solution options if you have already encountered this error, refer to SAP Note <strong>1809597</strong>.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70212</strong><br />Symptom: After you import Support Package SAPKB70212, errors occur when you save function modules and methods for the modification adjustment.<br /><br />Solution: You can avoid this problem if you import Support Package SAPKB70213 in a queue with SAPKB70212.<br />If the error has already occurred, implement the current version of SAP Note 1680204 in your system.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70212</strong><br />Symptom: If the import of the Support Package SAPKB70212 terminates in the phase IMPORT_PROPER, it cannot be continued in the Support Package Manager because this leads to a syntax error in SAPLSUU1:<br />The row type from \"USRXX_DELETE\" cannot be converted to the table \"USR07\" in the key part.<br /><br />Solution: To avoid the error, you can deactivate the ABAP Dictionary user check in the settings of the Support Package Manager (option \"Check for active DDIC user\"). This is possible as of SPAM/SAINT Version 0043.<br />If the error has already occurred, the deactivation can take place only in the configuration table PATCHECK. For the entries with CHECK_NAME = \"DDIC_USER_REQ\", set the value of the field ACTIVE to \" \" (space/blank character). Then you can continue importing the Support Package queue.<br />After you import the Support Package queue, you can activate the check again in the settings.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKB70214<br /></strong>Symptom: Following the import of Basis Support Package SAPKB70214, SAP Notes that do not contain ABAP Dictionary object corrections can be displayed for adjustment during the modification adjustment of ABAP Dictionary objects (SPDD).<br /><br />Solution: Refer to SAP Note 1910464 and implement the corrections delivered there. Alternatively, you can import Basis Support Package SAPKB70215.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we have the following recommendations for Basis Support Package queues:<br />SAPKB70201 - SAPKB702.. (highest available Basis Support Package).<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the add-ons or CRTs used should provide information about the optimal or permitted queues.</p>\r\n<p><strong>9. ABA 7.00 Support Packages (component SAP_ABA 700)</strong></p>\r\n<ul>\r\n<li><strong>SAPKA70006</strong><br />Symptom: When you import Support Package SAPKA70006 into an SAP ERP system or an SAP SRM system, an error occurs in phase XPRA_EXECUTION. In the log of the after-import methods execution, you find:<br /> Post-import method SDOK_CONTREP_AFTER_IMP started for SIRS L<br /> Table BDSCONT22 for document contents does not exist<br /> Postprocessing necessary when upgrade has been completed<br /><br />Depending on the system environment, tables other than table BDSCONT22 or other additional tables can be specified as non-existent.<br /><br />Solution: This message is a serious warning that editing can also be carried out after the Support Package queue is imported. You can continue with the import.<br />After you carry out the import, refer to SAP Note 725845 and eliminate the inconsistencies in the Content Repository.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKA70012</strong><br />Symptom: After you import Support Package SAPKA70012, the system overwrites all field modification settings for the business partner category or resets them to the standard SAP settings.<br /><br />Solution: Note 1107216 describes the cause of the problem and a solution. Take the actions described in this SAP Note <strong>before</strong> you import the Support Package.<br />If you have any questions about this problem, create a problem message under the component CA-GTF-BDT.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKA70023</strong><br />When you import Support Package SAPKA70023, the table BP1030 may be converted. If the table has a very large number of entries, this can take a long time and can critically extend the Support Package import process.<br />Check the number of entries in the table BP1030. If the table has more than 1 million entries, see the information in Note 1527430.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKA70026</strong><br />Symptom: When you import Support Package SAPKA70026 to a CRM 6.0 system, a DDIC activation error occurs if the enhancements from SAP Note 1591908 or Support Package SAPKU60010.<br />Field CITY2 is specified twice in structure CRMT_BUPA_IL_SEARCH_ADDR.<br /><br />Solution: You can avoid this problem if you import Support Package SAPKU60011 in a queue with SAPKA70026. This corresponds to the Support Package stack definition.<br />If you have already encountered the error, proceed as described in SAP Note 1619052. </li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the previous comments, we recommend the following for ABA Support Package queues:<br />SAPKA70001 - SAPKA700.. (highest available Support Package ABA)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the add-ons or CRTs used should provide information about the optimal or permitted queues.</p>\r\n<p><strong>10. ABA 7.01 Support Packages (component SAP_ABA 701)</strong></p>\r\n<ul>\r\n<li><strong>SAPKA70108</strong><br />When you import Support Package SAPKA70108, the table BP1030 may be converted. If the table has a very large number of entries, this can take a long time and can critically extend the Support Package import process.<br />Check the number of entries in the table BP1030. If the table has more than 1 million entries, see the information in SAP Note 1527430.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the previous comments, we recommend the following for ABA Support Package queues:<br />SAPKA70101 - SAPKA701.. (highest available Support Package ABA)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.</p>\r\n<p><strong>11. ABA 7.02 Support Packages (component SAP_ABA 702)</strong></p>\r\n<ul>\r\n<li><strong>SAPKA70205</strong><br />When you import Support Package SAPKA70205, the table BP1030 may be converted. If the table has a very large number of entries, this can take a long time and can critically extend the Support Package import process.<br />Check the number of entries in the table BP1030. If the table has more than 1 million entries, see the information in SAP Note 1527430.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the previous comments, we recommend the following for ABA Support Package queues:<br />SAPKA70201 - SAPKA702.. (highest available Support Package ABA)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.</p>\r\n<p><strong>12. BW Support Packages (component SAP_BW)</strong></p>\r\n<ul>\r\n<li><strong>All SAP_BW Support Packages</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Symptom: After you import SAP_BW Support Packages, all transformations of SAP BI Content are no longer available.<br /><br />Solution: Implement the corrections from Note 1171293.<br />If the problem has already occurred in your system, create a problem message under the component BW-WHM-DST-TRF.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Symptom: When you import BI_CONT Support Packages, a DDIC activation error occurs because the component type /BI0/OITCTLSTCHG is not yet active.<br /><br />Solution: Follow the instructions that are contained in Note 1152612.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Symptom: During the import of BW objects, objects of the type R3TR DTRF, DROU, and DSFO may be deleted inadvertently.<br /><br />Solution: See SAP Note 1488647 and implement the solution that is described there.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKW70003</strong><br />Symptom: When you import BW Support Package SAPKW70003 into a system that runs on a MaxDB 7.6, a termination may occur in phase IMPORT_PROPER. In the import log, you find the following error message:<br />sap_dext called with msgnr 1: db call info<br />function: db_xrtab<br />fcode: RT_MODIFY<br />tabname: RSZWBTMPDATA<br />len (char): 46<br />key: 0ANALYSIS_PATTERN D000<br />retcode: 1<br />SQL error -602 accessing : POS(1) System error:<br />AK System error: VAK507 5<br /><br />Solution: Refer to SAP Note 864482 and update the MaxDB to at least Version 7.6.0 Build 15. Then continue to import the Support Package.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKW70004 - SAPKW70006, SAPKW70007</strong><br />Symptom: If you import the BW Support Packages SAPKW70004 and SAPKW70006 or SAPKW70007 together in a single queue, the TEST_IMPORT step returns an error for Support Packages SAPKW70006 and SAPKW70007. The test import log for SAPKW70006 and SAPKW70007 contains the following error message:<br />Function RSO_GET_MASTER_DATA (RSTC_EXTR 03) does not fit<br /> into the existing function group ((RSO5 01))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKW70013 - SAPKW70014 (SAPKW70015, SAPKW70016)</strong><br />Symptom: If the BW Support Packages SAPKW70013 and SAPKW70014 are imported together in a queue, an error occurs in Support Package SAPKW70014 in the step TEST_IMPORT. The test import log of Support Package SAPKW70014 contains the following error message:<br />Function RSDDTREX_INDEX_CHECK (RSDDTREX_CHECK 01) does not fit<br /> into the existing function group ((RSDDTREX 06))<br />If BW Support Package SAPKW70015 or SAPKW70016 is also in the queue, the same test import error occurs there.<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for BW Support Package queues:<br />SAPKW70001 - SAPKW700.. (highest available BW Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.</p>\r\n<p><strong>13. Solution Manager Support Packages (ST, ST-*)</strong></p>\r\n<ul>\r\n<li><strong>SAPKITL412</strong><br />Symptom: The phase XPRA_EXECUTION terminates when you import Support Package SAPKITL412. The job log of the background job RDDEXECL lists the runtime error CONNE_IMPORT_CONVERSION_ERROR as the cause of the termination.<br /><br />Solution: To solve the problem, proceed as described in SAP Note 925688.</li>\r\n</ul>\r\n<p><strong>14. Application Support Packages, component SAP_APPL 600</strong></p>\r\n<p style=\"padding-left: 30px;\">Symptom: Due to an error in the basis layer of the SAP NetWeaver AS ABAP before Basis Support Package SAPKB70005, when you import Support Packages, the add-on conflict verification may not find all conflicts with installed add-ons and activated Industry Extensions. In other words, in this case, the Support Package Manager does not make one-hundred-percent sure that all necessary CRTs are in the Support Package queue.<br /><br />Solution: Completely Import Basis Support Package SAPKB70005 into your system before you import Application Support Packages. Only in this way can you be absolutely certain that the Support Package queue is complete.</p>\r\n<ol>If you have not installed any modifying add-ons and have not activated any of the delivered Industry Extensions either (for example, IS-OIL, IS-H, IS-M, ECC-DIMP, ...), you do not need to divide the queue in this way.</ol>\r\n<ul>\r\n<li><strong>SAPKH60003</strong><br />Symptom: When you import Support Package SAPKH60003, the importing process stops in the phase XPRA_EXECUTION. The after-import method log contains the return code 0006 and the following error message:<br /> BAdI implementation FMFG_CCR_VEND_NO_ARC must still be<br /> migrated<br /> Postprocessing necessary when upgrade has been completed<br /> Migrate implementation FMFG_CCR_VEND_NO_ARC using<br /> transaction SPAU<br />Solution: Continue the import, it will finish successfully.<br />If you use functions of the Application Extension EA-PS, import the EA-PS of Support Package SAPKGPPD03. This imports the adjustments necessary for BAdI migration. Even if you have not modified any SAP objects from the EA-PS environment, the system prompts you to adjust your modifications in the phase RUN_SPAU. However, the SPAU list is empty, since the migration has been carried out by importing the EA-PS Support Package. Simply confirm the processing of the modification adjustment, and continue with the import.<br />You can prevent the error occurring in the XPRA_EXECUTION, and RUN_SPAU from stopping by importing the Support Packages SAPKH60003 and SAPKGPPD03 together in a queue.<br />If you do not use the Application Extension EA-PS, you can ignore the above error, further action is not necessary in this case.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKH60005</strong><br />Symptom: An error occurs in the DDIC_ACTIVATION phase when you import Support Package SAPKH600005. The activation log contains the following error messages:<br />Check table EKKO does not exist or has no key fields<br />Table MERE_OUTTAB1_ME49 could not be activated<br /><br />The system may refer to other tables (MERE_OUTTAB2_ME49, MMPURUI_PR_STY) as impossible to activate, rather than table MERE_OUTTAB1_ME49. The log specifies check table EKKO as the cause of the problem for all of these problems.<br /><br />Solution: Start the import process and repeat the DDIC activation. The error does not recur when you repeat the DDIC activation.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKH60014, SAPKH60016</strong><br />Symptom: The problem of unwanted conversion of large cluster tables, which is described in the general section about ABAP Dictionary activation problems, occurs when you import Support Package SAPKH60014 or SAPKH60016 for the table BSEG.<br /><br />Solution: Before you import the Support Package, it is imperative that you implement the corrections contained in Note 1283197.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPKH60001 - SAPKH600.. (highest available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.</p>\r\n<p><strong>15. Application Support Packages, component SAP_APPL 604</strong></p>\r\n<ul>\r\n<li><strong>SAPKH60401 - SAPKH60403</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Symptom: After you import Support Packages SAPKH60401 to SAPKH60403 in a queue, the following data types<br />BUPA_BPERPADDR_INFO_CK_QRY_S16<br />BUPA_BPERPBY_ELMNTS_RSP_S_BP17<br />BUPA_BPERPBY_IDRSP_S_BPADDR_15<br />BUPA_BPERPCK_QRY_S_BPADDR_IN15<br />BUPA_BPERPCRTE_CONF_S_BPADDR15<br />BUPA_BPERPCRTE_REQ_S_BPADDR_10<br />BUPA_BPERPUPDT_CONF_S_BPADDR15<br />BUPA_BPERPUPDT_REQ_S_BPADDR_15<br /><br />always experience the following error when activated:<br />Field ADDRESS_ID: Component type or domain used not active or does not exist<br /><br />You do not notice the error when you import Support Packages, but only in the application or when certain actions occur, for example, a Unicode conversion.<br />Refer to Note 1374980 for more details.<br /> Solution: Import Support Package SAPKH60404. If that is not possible, proceed according to the solution in Note 1374980.</p>\r\n<p><br /><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPKH60401 - SAPKH604.. (highest available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues are only valid if you have not installed any add-ons or do not have to include any CRTs in the queue. Otherwise, you need instructions on optimal or permitted queues from those people who produce the add-ons or CRTs used.</p>\r\n<p><strong>16. Application Support Packages, component SAP_APPL 605</strong></p>\r\n<ul>\r\n<li><strong>SAPKH60501</strong></li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Symptom: The import of a Support Package queue with Support Package SAPKH60501 terminates in the phase XPRA_EXECUTION with the return code 0012. Transaction ST22 contains the short dumps for the runtime error SYNTAX_ERROR in the ABAP program CL_GLE_ECS_BRF_ADAPTER========CP.<br />Solution: Use the Note Assistant to implement the correction from Note 1519773. You can then continue importing the Support Package queue.</p>\r\n<p><strong>17. HR Support Packages, component SAP_HR 600</strong></p>\r\n<ul>\r\n<li><strong>SAPKE60002</strong><br />Symptom: The import prerequisites for the first version of the HR Support Package SAPKE60002 (PAT-file CSN0120061532_0022565.PAT) were not correct. If you import this version of the Support Package together with the SAP_APPL of Support Package SAPKH60001 in a queue, an incorrect importing sequence may result.<br /><br />Solution: The import prerequisites were corrected. You can recognize the new version of the Support Package by the PAT file name CSN0120061532_0022713.PAT. Only use this version!</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60009</strong><br />Problem: HR Support Package SAPKE50026 contains the new database indexes for the PPOIX and PPOPX  tables. Since the tables may contain a large number of entries, depending upon the application environment, it can take the system a long time to create the indexes on the database during the import process.<br /><br />Solution: See SAP Note 1012176 and follow the instructions mentioned there <strong>before the import</strong>.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60013</strong><br />Symptom: After you import HR Support Package SAPKE60013, SAPscript forms that have their own TrueType fonts are no longer printed correctly. Instead of the customer-defined font types, other font types are printed, but these are formatted incorrectly (for example, some letters overlap each other).<br /><br />Solution: Proceed as described in Note 1030460.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60016 - SAPKE60021</strong><br />Symptom: When you import Support Packages SAPKE60016 and SAPKE60021 in a queue, a duplicate key error occurs for the table T7DEPBSXXAWE0A during the import.<br /><br />Solution: Note 1074030 describes this and other problems. You require an R3trans with release date 16.06.08 or later.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60024 - SAPKE60025</strong><br />Symptom: If the Support Packages SAPKE60024 and SAPKE60025 are imported together in a queue, the TEST_IMPORT step returns an error for Support Package SAPKE60025. The test import log of SAPKE60025 displays the following error message:<br />Function HRCCE_EXT_EMP_ATTR_CE (HRCCE_BI_CE 02) does not fit into<br /> the existing function group ((HRCCE_CALENDAR_FUNCTIONS 05)).<br />This error message recurs for two other function modules of the function groups HRCCE_BI_CE and HRCCE_CALENDAR_FUNCTIONS.<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60026</strong><br />Symptom: After you import the HR Support Package, the following error is displayed when you execute the report RPCP01Q0/RPCPSPQ0:<br /> Font Family 'Arial' is not maintained<br /> Font Family 'Arial_B' is not maintained<br /><br />Solution: The TrueType fonts were deleted by the HR Support Package; for more information, see Note 1132685.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60031</strong><br />Symptom: It takes a long time to import the Support Package SAPKE60031. The import process hangs in the phase AUTO_MOD_SPAU for a long time.<br /><br />Solution: Before you import the Support Package, implement Note 1236444 or import the Support Packages SAPKE60031 and SAPKE60034 together in a queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60035</strong><br />Symptom: When you import the HR Support Package SAPKE60035, the phase DDIC_ACTIVATION terminates with an activation error. The activation log displays:<br /> Table T5J73T could not be activated.<br /> (Error- T5J73T-RETMF: The assignment for the field SPRSL<br /> does not exist in the search help H_T5J73)<br /><br />Solution: To avoid the error, import the HR Support Packages SAPKE60035 and SAPKE60036 in a queue.<br />If you have already encountered the error, proceed as described in Note 1265242. </li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60037, SAPKE60403</strong><br />Symptom: When you import the HR Support Package SAPKE60037 or SAPKE60403, if automatic ABAP generation or screen generation is active, the system issues the following message in the phase ABAP_GENERATION:<br /> Program HMXCEDT0_CE, include RPCEDD09_CE_MX: Syntax error<br /> in line 000473 'XBT-IBAN' has already been declared.<br /><br />Solution: See Note 1279262.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60038, SAPKE60404</strong><br />Symptom: When you import the HR Support Package SAPKE60038 or SAPKE60404, or you import the corresponding CLCs SAPK-60038INSAPHRCNL or SAPK-60404INSAPHRCNL, the following errors may occur in the phase DDIC_ACTIVATION:<br /> View field V_FBN_T74HB-CHG_BPLAN (domains of the data<br /> elements not equal).<br /> View V_FBN_T74HB could not be activated.<br /><br />Solution: For more information, see Note 1289778.<br />To prevent the error, include the follow-on Support Packages SAPKE60039 and SAPKE60405 in the same queue.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60064, SAPKE60430</strong><br />Solution: Refer to Note 1510174 and implement the corrections from the security notes listed there to prevent problems from occurring after you import the HR Support Packages.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKE60073 (SAPK-60073INSAPHRCDE)</strong><br />Symptom: After you import the HR Support Package SAPKE60073 (or the CLC SAPK-60073INSAPHRCDE), payroll errors occur for the German Pension Reform.<br /><br />Solution: See SAP Note 1629659 and implement the solution that is described in the SAP Note.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPKE60002 - SAPKE600.. (highest available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.</p>\r\n<p><strong>18. HR Support Packages, component SAP_HR 604</strong></p>\r\n<ul>\r\n<li><strong>SAPKE60454, SAPKE60455</strong><br />Symptom: When you import the HR Support Packages SAPKE60454 and SAPKE60455, an error in the postprocessing of enhancement objects (BAdIs) can occur in the phase XPRA_EXECUTION.<br /><br />Solution: Implement the corrections delivered with SAP Note <strong>1834396 before importing the Support Packages</strong>.<br />If the problem has already occurred in your system, create a problem message with the component BC-DWB-CEX-BAD.</li>\r\n</ul>\r\n<p><strong>19. Support Packages for Application Extension EA-APPL</strong></p>\r\n<ul>\r\n<li><strong>SAPKGPAD07 - SAPKGPAD08</strong><br />Symptom: If you import Support Packages SAPKGPAD07 and SAPKGPAD08 together in a queue, the TEST_IMPORT step returns an error for Support Package SAPKGPAD08. The test import log for SAPKGPAD08 contains the following error message:<br />Function REIT_GUI_TC_BASE_POPUP (REIT_GUI_TC_BASE_POPUP 01) does not<br /> fit into the existing function group ((REIT_GUI_TC_BASE 02))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPKGPAD02 - SAPKGPAD.. (highest available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.</p>\r\n<p><strong>20. Support Packages for Application Extension EA-HR 600</strong></p>\r\n<ul>\r\n<li><strong>General note about EA_HR CLCs</strong><br />Symptom: In the Support Package Manager, if you want to calculate a queue for an EA-HR CLC, the queue calculator may include the entire SAP_HR Support Package in the queue instead of just the required SAP_HR CLC.<br /><br />Solution: See Note 1279090 for solution suggestions.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKH60001, SAPKGPHD02</strong><br />Symptom: If you import the Support Packages SAPKH60001 and SAPKGPHD02 together in a queue, the TEST_IMPORT step returns an error for Support Package SAPKGPHD02. You can find the following error message in the test import log of SAPKGPHD02:<br />Function HRF_READ_ORG_UNIT (HRF_READ_STARS 10) does not fit<br />into the existing function group ((HRF_READ_DIMS 10))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKGPHD04 - SAPKGPHD09</strong><br />Symptom: If you import Support Packages SAPKGPHD04 and SAPKGPHD09 together in a queue, the TEST_IMPORT step returns an error for Support Package SAPKGPHD09. The test import log of SAPKGPHD09 contains the following error message:<br />Function HR_ASR_GS_FLD_NAME_EXIT (HRASR00VGS 02) does not<br /> fit into the existing function group ((HRASR00FSCN_SH 08))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKGPHD26</strong><br />Symptom: If you import Support Package SAPKGPHD26 in a system with SAP_BASIS Support Package Level 0014, a generation error occurs if the ABAP generation is activated:<br />Program CL_LSO_CRP_FKK_HISTORY========CP, Include CL_LSO_CRP_FKK_HISTORY========CM008: Syntax error in line 000037<br />Method 'REFRESH_DISPLAY_HISTORY' does not exist.<br /><br />Solution: If you have already encountered the error, ignore it by choosing 'Extras'-&gt;' 'Ignore generation errors'' and continue importing. Then import Basis Support Package SAPKB70015 to correct the problem completely.<br />To avoid the error, import Basis Support Package SAPKB70015 in advance or together with EA-HR SP SAPKGPHD26.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>SAPKGPHD64</strong><br />Solution: Refer to Note 1510174 and implement the corrections from the security notes listed there to prevent problems from occurring after you import the EA-HR Support Package.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPKGPHD02 - SAPKGPHD.. (highest available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.</p>\r\n<p><strong>21. Support Packages for Application Extension EA-HR 602, 603, 604</strong></p>\r\n<ul>\r\n<li><strong>General note about EA_HR CLCs</strong><br />Symptom: In the Support Package Manager, if you want to calculate a queue for an EA-HR CLC, the queue calculator may include the entire SAP_HR Support Package in the queue instead of just the required SAP_HR CLC.<br /><br />Solution: See Note 1279090 for solution suggestions.</li>\r\n<li><strong>SAPK-60204INEAHR</strong><br />Symptom: The importing of Support Package SAPK-60204INEAHR terminates in the phase DDIC_ACTIVATION. In the activation log, you find that the table T706S could not be activated because the field NO_TAX_CHAIN was defined twice.<br /><br />Reason: This error occurs only if you imported EA-HR 600 Support Package SAPKGPHD25 before you upgraded the component EA-HR to Version 602.<br /><br />Solution: You can avoid the error by importing Support Package SAPK-60204INEAHR together with the subsequent Support Package SAPK-60205INEAHR in a queue.<br />If you have already encountered the error, proceed as described in SAP Note 1130693 to correct it.</li>\r\n<li><strong>SAPK-60243INEAHR, SAPK-60338INEAHR, SAPK-60430INEAHR</strong><br />Solution: Refer to Note 1510174 and implement the corrections from the security notes listed there to prevent problems from occurring after you import the EA-HR Support Packages.</li>\r\n<li><strong>SAPK-60442INSAPHR, SAPK-60443INSAPHR</strong><br />Symptom: When you import the EA-HR Support Packages SAPK-60442INSAPHR and SAPK-60443INSAPHR, a termination may occur in the phase XPRA_EXECUTION. The import log contains an error in the postprocessing of the BC set HRLOCUN_T512T_S.<br /><br />Solution: Proceed according to the correction instructions in SAP Note 1671354.</li>\r\n</ul>\r\n<p><strong>22. Support Packages for Application Extension EA-DFPS</strong></p>\r\n<ul>\r\n<li><strong>SAPKGPDD02, SAPKGPDD04</strong><br />Symptom: If you import Support Packages SAPKGPDD02 and SAPKGPDD04 together in a single queue, the TEST_IMPORT step returns an error for Support Package SAPKGPDD04. The test import log for SAPKGPDD04 contains the following error message:<br />Function /ISDFPS/ME_PM_MNTREL (/ISDFPS/ME_CA_FE 10) does<br />not fit into the existing function group<br />((/ISDFPS/ME_PM_MNTREL 01))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPKGPDD02 - SAPKGPDD.. (highest available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.</p>\r\n<p><strong>23. Support Packages for Application Extension EA-FINSERV</strong></p>\r\n<ul>\r\n<li><strong>SAPKGPFD03, SAPKGPFD05</strong><br />Symptom: If you import Support Packages SAPKGPFD03 and SAPKGPFD05 together in a single queue, the TEST_IMPORT step returns an error for Support Package SAPKGPFD05. You can find the following error message in the test import log of SAPKGPFDO5:<br />Function FTI_LDB_GET_SWAP_DETAILS (FTI_LDB_RAPI 01) does<br />not fit into the existing function group<br />((FTI_LDB_GET_OTC_DETAILS 01))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPKGPFD02 - SAPKGPFD.. (highest available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.<br />These recommended queues only apply if you have not installed any add-ons or do not need to incorporate any CRTs into the queue. Otherwise, the producers of the relevant add-ons or CRTs should provide information on optimal or permitted queues.</p>\r\n<p><strong>24. Support Packages for Industry Extension IS-UT</strong></p>\r\n<ul>\r\n<li><strong>SAPK-60004INISUT</strong><br />When you import the Support Package SAPK-60004INISUT, the system executes an XPRA, which may result in longer runtimes for the phase XPRA_EXECUTION. See Note 924885 for detailed information.</li>\r\n</ul>\r\n<p><strong>25. Support Packages for Industry Extension IS-OIL</strong></p>\r\n<ul>\r\n<li><strong>SAPK-60006INISOIL - SAPK-60007INISOIL</strong><br />Symptom: If you import Support Packages SAPK-60006INISOIL and SAPK-60007INISOIL together in a queue, the TEST_IMPORT step returns an error for Support Package SAPK-60007INISOIL. The following error messages are displayed in the test import log:<br />Function OIUREP_GET_RECENT_ROYHIST (OIUREP_ROY_HISTORY 05)<br /> does not fit into the existing function group<br /> (OIUREP_ROY_REPORTING 09)<br />Function OIUREP_GET_RECENT_VOLHIST (OIUREP_ROY_HISTORY 06)<br /> does not fit into the existing function group<br /> (OIUREP_ROY_REPORTING 10)<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n<li><strong>SAPK-60013INISOIL - SAPK-60015INISOIL</strong><br />Symptom: If you import Support Packages SAPK-60013INISOIL and SAPK-60015INISOIL together in a queue, DDIC activation errors may occur for the tables OIUX4_BALANCE and OIUX4_BALANCE_H. The DDIC activation program reports that two indexes of the table have identical fields. <br /><br />Solution: Proceed according to the correction instructions in Note 1391715.<br />You can avoid the error by importing the Basis Support Package specified in Note 1391715 before importing the IS-OIL Support Packages.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPK-60001INISOIL - SAPK-600..INISOIL (most recent available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.</p>\r\n<p><strong>26. Support Packages for Industry extension IS-PS-CA</strong></p>\r\n<ul>\r\n<li><strong>SAPK-60008INISPSCA, SAPK-60009INISPSCA</strong><br />Symptom: If you import Support Packages SAPK-60008INISPSCA and SAPK-60009INISPSCA together in a queue, the TEST_IMPORT step returns an error for Support Package SAPK-60009INISPSCA. The following error messages are displayed in the test import log:<br />Function FMCA_EVENT_2611 (FMCA_INV_EVENTS 01) does not<br />fit into the existing function group ((FMCA_CH 13))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPK-60001INISPSCA - SAPK-600..INISPSCA (latest available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.</p>\r\n<p><strong>27. Support Packages for Industry extension ECC-DIMP</strong></p>\r\n<ul>\r\n<li><strong>SAPK-60009INECCDIMP</strong><br />Symptom: The Support Package SAPK-60009INECCDIMP contains an error that may cause a payroll error if you activate the industry extension ECC-DIMP.<br /><br />Solution: Implement the advance corrections from Note 1070942 or import Support Package SAPK-60011INECCDIMP.</li>\r\n</ul>\r\n<p><strong>Recommended queues:</strong><br />Based on the remarks above, we recommend the following for Support Package queues:<br />SAPK-60001INECCDIMP - SAPK-600..INECCDIMP (latest available Support Package)<br /><br />You can define shorter queues but you must pay attention to the split points.</p>\r\n<p><strong>28. Support Packages for Industry extension IS-M</strong></p>\r\n<ul>\r\n<li><strong>SAPK-60010INISM - SAPK-60011INISM</strong><br />Symptom: If you import Support Packages SAPK-60010INISM and SAPK-60011INISM together in a queue, the TEST_IMPORT step returns an error for Support Package SAPK-60011INISM. The following error message is displayed in the test import log:<br />Function ISM_BUP_CCARD_READ_MEMORY (JGBPCC_READ 03) does not fit<br /> into the existing function group ((JF11 02))<br /><br />Solution: You can ignore this error by choosing 'Extras'-&gt;'Ignore test-import error'. The error will not occur during the later import.</li>\r\n</ul>\r\n<p><strong>29. Support Packages for other Application Extensions and Industry Extensions</strong></p>\r\n<ol>No further problems are known, so we are not yet providing any recommendations for split points; maximum queues are allowed.</ol>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028597)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D057967)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000822379/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822379/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822379/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822379/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822379/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822379/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822379/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822379/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000822379/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Configuring_the_Web_Service.zip", "FileSize": "62", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000183992005&iv_version=0062&iv_guid=00B934F71062804E8E00F04AD6ABBF7B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620"}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861"}, {"RefNumber": "970837", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Creating methods with Note Assistant - mod infos created", "RefUrl": "/notes/970837"}, {"RefNumber": "967821", "RefComponent": "BC-SRV-RM", "RefTitle": "Incorrect definition of SRM_FILL_KC_TABLES_AFTER_IMP", "RefUrl": "/notes/967821"}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513"}, {"RefNumber": "961512", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961512"}, {"RefNumber": "961511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/961511"}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783"}, {"RefNumber": "948937", "RefComponent": "PY-XX", "RefTitle": "Syntax Error in Payroll in ERP2005 after SAPKH60001", "RefUrl": "/notes/948937"}, {"RefNumber": "947991", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/947991"}, {"RefNumber": "945855", "RefComponent": "SV-SMB-AIO-PFW-SB", "RefTitle": "Additional information about BP-INSTASS 600V1", "RefUrl": "/notes/945855"}, {"RefNumber": "930901", "RefComponent": "FS-PM", "RefTitle": "Enhancements for FSPM 3.10 NW04s installations", "RefUrl": "/notes/930901"}, {"RefNumber": "925688", "RefComponent": "SV-SMG", "RefTitle": "Double-Byte environment: Error while loading SAPKITL412", "RefUrl": "/notes/925688"}, {"RefNumber": "925240", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925240"}, {"RefNumber": "924885", "RefComponent": "IS-U-IDE", "RefTitle": "Incorrect generation date for data exchange tasks", "RefUrl": "/notes/924885"}, {"RefNumber": "923524", "RefComponent": "FS-RI", "RefTitle": "Upgrade to SAP ECC 600 with FS-RI 600", "RefUrl": "/notes/923524"}, {"RefNumber": "923523", "RefComponent": "FS-RI", "RefTitle": "Installing FS-RI 600 in SAP ECC 600", "RefUrl": "/notes/923523"}, {"RefNumber": "920016", "RefComponent": "PA", "RefTitle": "Contents & applying HR Support Packages ERP6.00", "RefUrl": "/notes/920016"}, {"RefNumber": "917999", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "PARCONV_UPG terminates with TSV_TNEW_PAGE_ALLOG_FAILED", "RefUrl": "/notes/917999"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "913849", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913849"}, {"RefNumber": "913848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/913848"}, {"RefNumber": "906008", "RefComponent": "BC-UPG-OCS", "RefTitle": "Missing method implementation after Support Package import", "RefUrl": "/notes/906008"}, {"RefNumber": "905029", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905029"}, {"RefNumber": "892842", "RefComponent": "BC-INS", "RefTitle": "SAPCAR error: format error in header", "RefUrl": "/notes/892842"}, {"RefNumber": "890202", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/890202"}, {"RefNumber": "879289", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide - SAP NetWeaver 7.0", "RefUrl": "/notes/879289"}, {"RefNumber": "871402", "RefComponent": "BC-VMC", "RefTitle": "Incorrect activation for transports w/ deleted config.", "RefUrl": "/notes/871402"}, {"RefNumber": "865198", "RefComponent": "BC-DB-ORA", "RefTitle": "Triggers as database objects", "RefUrl": "/notes/865198"}, {"RefNumber": "864482", "RefComponent": "BC-DB-SDB", "RefTitle": "Import process using TP terminates with -109, -102", "RefUrl": "/notes/864482"}, {"RefNumber": "837691", "RefComponent": "BC-CCM-BTC", "RefTitle": "Background jobs are deleted in unintentionally", "RefUrl": "/notes/837691"}, {"RefNumber": "826488", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826488"}, {"RefNumber": "826487", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826487"}, {"RefNumber": "826093", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826093"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "822380", "RefComponent": "BC-UPG-OCS", "RefTitle": "Problems with add-on installation/upgrade in SAP NW 7.0x AS ABAP", "RefUrl": "/notes/822380"}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322"}, {"RefNumber": "810081", "RefComponent": "CRM-MW-SPM", "RefTitle": "Import of Support Packages fails", "RefUrl": "/notes/810081"}, {"RefNumber": "725845", "RefComponent": "SRM-EBP-TEC-UPG", "RefTitle": "SRM upgrade - messages in the LONGPOST.LOG log file", "RefUrl": "/notes/725845"}, {"RefNumber": "715355", "RefComponent": "BC-CCM-BTC", "RefTitle": "BP_JOB_SELECT: Optimizing the job selection", "RefUrl": "/notes/715355"}, {"RefNumber": "1910464", "RefComponent": "BC-DWB-CEX", "RefTitle": "Processing of SPDD-relevant note corrections", "RefUrl": "/notes/1910464"}, {"RefNumber": "1865464", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 7.0 SP-Stack 14 - Release Information Note", "RefUrl": "/notes/1865464"}, {"RefNumber": "1861585", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "RSUMOD20/func sumo_is_adjust: handling for cluster table", "RefUrl": "/notes/1861585"}, {"RefNumber": "1850409", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Installation & system copy with DB2 11", "RefUrl": "/notes/1850409"}, {"RefNumber": "1834396", "RefComponent": "BC-DWB-CEX-BAD", "RefTitle": "Error in import of deleted BAdI implementations", "RefUrl": "/notes/1834396"}, {"RefNumber": "1821879", "RefComponent": "BC-UPG-NA", "RefTitle": "Launch of Snote dumps in case of inconsistent notes", "RefUrl": "/notes/1821879"}, {"RefNumber": "1809597", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Syntax error in SAPLSDSB or SAPLSDB2", "RefUrl": "/notes/1809597"}, {"RefNumber": "1778145", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "'UNKNOWN_ERROR' in phase Disassemble during SP import", "RefUrl": "/notes/1778145"}, {"RefNumber": "1724702", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: DB_EXISTS_TABLE does not require regeneration", "RefUrl": "/notes/1724702"}, {"RefNumber": "1708929", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS:Rebuild Index/Member Cluster failed w/Kernel 7.20", "RefUrl": "/notes/1708929"}, {"RefNumber": "1678047", "RefComponent": "HAN-DB", "RefTitle": "COMPUTE_INT_PLUS_OVERFLOW during activation", "RefUrl": "/notes/1678047"}, {"RefNumber": "1630727", "RefComponent": "BC-DB-MSS", "RefTitle": "Invalid column name 'area' during 702 SPAM", "RefUrl": "/notes/1630727"}, {"RefNumber": "1630554", "RefComponent": "BC-TWB-TST-ECA", "RefTitle": "eCATT:Syntax error in TEST_ECATT_API_DEMO,TEST_SDC_API_DEMO", "RefUrl": "/notes/1630554"}, {"RefNumber": "1629659", "RefComponent": "PY-DE-NT-CI", "RefTitle": "HR Support Packg 60073: No German Pension Reform in payroll", "RefUrl": "/notes/1629659"}, {"RefNumber": "1619001", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting view data does not work", "RefUrl": "/notes/1619001"}, {"RefNumber": "1614802", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Mass activation hangs during INSERT in the table DDFTX", "RefUrl": "/notes/1614802"}, {"RefNumber": "1596680", "RefComponent": "BC-DWB-TOO-ABA", "RefTitle": "Editor: Displaying implicit enhancement options", "RefUrl": "/notes/1596680"}, {"RefNumber": "1572314", "RefComponent": "BC-DWB-TOO-MEN", "RefTitle": "Phase XPRA_UPG: Termination CONNE_IMPORT_CONVERSION_ERROR", "RefUrl": "/notes/1572314"}, {"RefNumber": "1558728", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Performance improvement of after import method", "RefUrl": "/notes/1558728"}, {"RefNumber": "1548839", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Transport of Enhancement Spots cause BAdI impl. to be lost", "RefUrl": "/notes/1548839"}, {"RefNumber": "1542671", "RefComponent": "BC-DB-MSS", "RefTitle": "Incorrect syntax near 'OFF'", "RefUrl": "/notes/1542671"}, {"RefNumber": "1532661", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Problems with SFW-BAdIs during upgrades and EHP installation", "RefUrl": "/notes/1532661"}, {"RefNumber": "1527430", "RefComponent": "FS-BP", "RefTitle": "BP_LEG: Database conversion of the table BP1030", "RefUrl": "/notes/1527430"}, {"RefNumber": "1521903", "RefComponent": "BC-DWB-CEX-BAD", "RefTitle": "Regenerate all inconsistent BAdI's", "RefUrl": "/notes/1521903"}, {"RefNumber": "1514063", "RefComponent": "BC-DB-ORA", "RefTitle": "Termination in SPAM import proper phase w/ error ORA 01007", "RefUrl": "/notes/1514063"}, {"RefNumber": "1510174", "RefComponent": "PY-XX", "RefTitle": "HCM: dependency of Support Packages to Netweaver Notes", "RefUrl": "/notes/1510174"}, {"RefNumber": "1500074", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Installation & system copy with DB2 10", "RefUrl": "/notes/1500074"}, {"RefNumber": "1498113", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "Generation error in screen SAPLENH_BADI_EDT_DEF 2200", "RefUrl": "/notes/1498113"}, {"RefNumber": "1492733", "RefComponent": "BC-UPG-ADDON", "RefTitle": "GRC NFE 10.0 installation on NW 7.02/7.0 3", "RefUrl": "/notes/1492733"}, {"RefNumber": "1491290", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing synchronization after importing include deletions", "RefUrl": "/notes/1491290"}, {"RefNumber": "1488647", "RefComponent": "BW-BCT-GEN", "RefTitle": "Objects of the type DTRF deleted after method is executed", "RefUrl": "/notes/1488647"}, {"RefNumber": "1476315", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Error in activation protocol for switch framework", "RefUrl": "/notes/1476315"}, {"RefNumber": "1469610", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing ABAP programs after \"Downtime minimized import\"", "RefUrl": "/notes/1469610"}, {"RefNumber": "1468464", "RefComponent": "BC-CTS-TLS", "RefTitle": "Access to non-existing tables DD43L and DD43T", "RefUrl": "/notes/1468464"}, {"RefNumber": "1461232", "RefComponent": "BC-DWB-TOO", "RefTitle": "After importing a Support Package: No logon", "RefUrl": "/notes/1461232"}, {"RefNumber": "1460321", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide-SAP NetWeaver 7.0 including EHP2", "RefUrl": "/notes/1460321"}, {"RefNumber": "1420290", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Performance when importing EHP/SP for enhancements", "RefUrl": "/notes/1420290"}, {"RefNumber": "1417505", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete import of (ABAP Dictionary) objects", "RefUrl": "/notes/1417505"}, {"RefNumber": "1406740", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Potentially unnoticed rollback during array operation", "RefUrl": "/notes/1406740"}, {"RefNumber": "1391715", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Mass activatn program terminates: Indexes do not match table", "RefUrl": "/notes/1391715"}, {"RefNumber": "1377262", "RefComponent": "IS-OIL-DS-OGSD", "RefTitle": "OGSD 6.1 Installation / Delta Upgrade on SAP ECC 600", "RefUrl": "/notes/1377262"}, {"RefNumber": "1374980", "RefComponent": "BC-UPG-OCS", "RefTitle": "Inconsistent DDIC objects after importing SAPKH60403", "RefUrl": "/notes/1374980"}, {"RefNumber": "1366799", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Over 600 GB of journal receiver data for SAPKB70019", "RefUrl": "/notes/1366799"}, {"RefNumber": "1354957", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Tables with more than 3 long string fields", "RefUrl": "/notes/1354957"}, {"RefNumber": "1332780", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1332780"}, {"RefNumber": "1321756", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Mass activation: Object activated instead of deleted", "RefUrl": "/notes/1321756"}, {"RefNumber": "1319582", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "CL_SFW_HELPER-GET_SWITCHED_COMPONENTS is incorrect", "RefUrl": "/notes/1319582"}, {"RefNumber": "1306409", "RefComponent": "BC-DWB-DIC", "RefTitle": "Missing fixed value texts", "RefUrl": "/notes/1306409"}, {"RefNumber": "1301446", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Incorrect return code for mass activation during transport", "RefUrl": "/notes/1301446"}, {"RefNumber": "1293745", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NetWeaver 7.0 EHP 1 SR1 (ABAP)", "RefUrl": "/notes/1293745"}, {"RefNumber": "1293387", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM Server 7.0 SR1 ABAP", "RefUrl": "/notes/1293387"}, {"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071"}, {"RefNumber": "1292070", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 ABAP", "RefUrl": "/notes/1292070"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1288862", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans terminates during import with RC=7777 (Windows)", "RefUrl": "/notes/1288862"}, {"RefNumber": "1285869", "RefComponent": "BC-DWB-TOO-MEN", "RefTitle": "Program MENUSYST: Syntax error or session termination", "RefUrl": "/notes/1285869"}, {"RefNumber": "1283197", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Cluster tables: Unnecessary conversion", "RefUrl": "/notes/1283197"}, {"RefNumber": "1281521", "RefComponent": "BC-DWB-TOO-PAK", "RefTitle": "Syntax error in SAPLPA_PACKAGE_CHECKS", "RefUrl": "/notes/1281521"}, {"RefNumber": "1281321", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Syntax error when importing Support Packages", "RefUrl": "/notes/1281321"}, {"RefNumber": "1279262", "RefComponent": "PY-XX", "RefTitle": "Generation error in programm HMXCEDT0_CE", "RefUrl": "/notes/1279262"}, {"RefNumber": "1279090", "RefComponent": "BC-UPG-OCS", "RefTitle": "HCM: Queue Calc.: CLC of EA-HRC<xx> requires HRSP of SAP_HR", "RefUrl": "/notes/1279090"}, {"RefNumber": "1276895", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. about Upgrading to SAP Solution Manager 7.0 EHP1", "RefUrl": "/notes/1276895"}, {"RefNumber": "1265242", "RefComponent": "PA-PA-JP", "RefTitle": "DDIC Activation Error on Tables T5J73T and T5J74", "RefUrl": "/notes/1265242"}, {"RefNumber": "1262653", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: New object is deleted after note is reset", "RefUrl": "/notes/1262653"}, {"RefNumber": "1256384", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "View/structure not active: Key field of type string", "RefUrl": "/notes/1256384"}, {"RefNumber": "1249184", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV Grid: SYNTAX_ERROR in class CL_GUI_ALV_GRID", "RefUrl": "/notes/1249184"}, {"RefNumber": "1246037", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide-SAP NetWeaver 7.0 including EHP1", "RefUrl": "/notes/1246037"}, {"RefNumber": "1237062", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "SFW-HELPER ignores the components SRM_EXT and SRM_EXPL", "RefUrl": "/notes/1237062"}, {"RefNumber": "1232804", "RefComponent": "BC-UPG-OCS", "RefTitle": "SPAM/SAINT: TP_FAILURE 0212 in phase IMPORT_OBJECT_LIST", "RefUrl": "/notes/1232804"}, {"RefNumber": "1180553", "RefComponent": "BC-DB-MSS", "RefTitle": "Syntax error 170 during index creation on SQL 2000", "RefUrl": "/notes/1180553"}, {"RefNumber": "1173174", "RefComponent": "BC-DB-DB4", "RefTitle": "DB2/400: Import terminates w/o warning after main import", "RefUrl": "/notes/1173174"}, {"RefNumber": "1171293", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP19)/BI7.1SPS07: Content disappears", "RefUrl": "/notes/1171293"}, {"RefNumber": "1162171", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "DDIC: Texts of domain fixed values are lost after transport", "RefUrl": "/notes/1162171"}, {"RefNumber": "1156970", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 ABAP", "RefUrl": "/notes/1156970"}, {"RefNumber": "1156969", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 ABAP", "RefUrl": "/notes/1156969"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1156844", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM Server 7.0", "RefUrl": "/notes/1156844"}, {"RefNumber": "1152612", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Incorrect component type in structure WRMA_S_RESULT_DATA", "RefUrl": "/notes/1152612"}, {"RefNumber": "1147591", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "Improvements for Web Servcies / Proxy Generation with SP14", "RefUrl": "/notes/1147591"}, {"RefNumber": "1146580", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1146580"}, {"RefNumber": "1142128", "RefComponent": "BC-UPG", "RefTitle": "Note Assistant: Obsolete notes are incorrectly deimplemented", "RefUrl": "/notes/1142128"}, {"RefNumber": "1138351", "RefComponent": "BC-UPG-OCS", "RefTitle": "Long runtime of background job SAPRUPGM", "RefUrl": "/notes/1138351"}, {"RefNumber": "1137503", "RefComponent": "BC-UPG-OCS", "RefTitle": "Secondary index for table REPOSRC", "RefUrl": "/notes/1137503"}, {"RefNumber": "1132685", "RefComponent": "PY-XX", "RefTitle": "Deletion of Arial True type font used in Payment/ETP summary", "RefUrl": "/notes/1132685"}, {"RefNumber": "1132422", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "ENH_CREATE_ENHCROSS: DEAD LOCK", "RefUrl": "/notes/1132422"}, {"RefNumber": "1130693", "RefComponent": "FI-TV", "RefTitle": "Generation error in upgrade from ERP 6.0 to EhP2", "RefUrl": "/notes/1130693"}, {"RefNumber": "1126127", "RefComponent": "BC-DB-DB6-SYS", "RefTitle": "DB6: Deferred table creation and row compression", "RefUrl": "/notes/1126127"}, {"RefNumber": "1120534", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "WS Patch collection (1) for WS tracing and alerting", "RefUrl": "/notes/1120534"}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856"}, {"RefNumber": "1118803", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC 600 Support Packages and ERP enhancement packages", "RefUrl": "/notes/1118803"}, {"RefNumber": "1109375", "RefComponent": "BC-DWB-DIC", "RefTitle": "Values from fixed value append are missing in the domain", "RefUrl": "/notes/1109375"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1108510", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR3 ABAP", "RefUrl": "/notes/1108510"}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841"}, {"RefNumber": "1086728", "RefComponent": "BC-DWB-CEX", "RefTitle": "Unreqd deletions in SPAU comparison after importing SP/EHP", "RefUrl": "/notes/1086728"}, {"RefNumber": "1082597", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Not enough business functions activated in SFW5", "RefUrl": "/notes/1082597"}, {"RefNumber": "1074030", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: \"Duplicate key\" composite SAP Note (as of Release 6.10)", "RefUrl": "/notes/1074030"}, {"RefNumber": "1071404", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1071404"}, {"RefNumber": "1057855", "RefComponent": "BC-DB-MSS", "RefTitle": "DB13/DBACOCKPIT database jobs fail after SP12", "RefUrl": "/notes/1057855"}, {"RefNumber": "1039395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1039395"}, {"RefNumber": "1020887", "RefComponent": "CA-TDM-BUS-ERP", "RefTitle": "Generated views in receiver system not deleted correctly", "RefUrl": "/notes/1020887"}, {"RefNumber": "1012176", "RefComponent": "BW-BCT-PY", "RefTitle": "Indexes for tables PPOIX and PPOPX", "RefUrl": "/notes/1012176"}, {"RefNumber": "1010762", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1010762"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2717408", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Method \"CHECK_USER_GRP_REQUIRED\" is unknown or PROTECTED or PRIVATE", "RefUrl": "/notes/2717408 "}, {"RefNumber": "2476307", "RefComponent": "BC-DWB-DIC", "RefTitle": "TABLE GLOFAAASSETDATA could not be activated during DDIC_ACTIVATION import phase", "RefUrl": "/notes/2476307 "}, {"RefNumber": "3232785", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of HR Renewal 2.0 / ECC 6.0 EhP8 for HCM", "RefUrl": "/notes/3232785 "}, {"RefNumber": "3152939", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "DB2-z/OS:v13: Installation & system copy with Db2 13", "RefUrl": "/notes/3152939 "}, {"RefNumber": "2986607", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 7.0 SP-Stack 23 - Release Information Note", "RefUrl": "/notes/2986607 "}, {"RefNumber": "2728953", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 7.0 SP-Stack 21 - Release Information Note", "RefUrl": "/notes/2728953 "}, {"RefNumber": "2647434", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of HR Renewal 2.0 / ECC 6.0 EhP8 for HCM", "RefUrl": "/notes/2647434 "}, {"RefNumber": "2575624", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 7.0 SP-Stack 20 - Release Information Note", "RefUrl": "/notes/2575624 "}, {"RefNumber": "2455875", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 7.0 SP-Stack 19 - Release Information Note", "RefUrl": "/notes/2455875 "}, {"RefNumber": "2158103", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Known problems with Support Packages in SAP NW 7.50 AS ABAP", "RefUrl": "/notes/2158103 "}, {"RefNumber": "2307817", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 7.0 SP-Stack 18 - Release Information Note", "RefUrl": "/notes/2307817 "}, {"RefNumber": "2303045", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "DB2-z/OS:v12: Installation & system copy with DB2 12", "RefUrl": "/notes/2303045 "}, {"RefNumber": "1976498", "RefComponent": "PY-XX", "RefTitle": "HRSP Information of HR Renewal 2.0 / ECC 6.0 EhP8 for HCM", "RefUrl": "/notes/1976498 "}, {"RefNumber": "1961728", "RefComponent": "IS-REA", "RefTitle": "IS-REA 6.10: Installation of Support Package 04", "RefUrl": "/notes/1961728 "}, {"RefNumber": "1969163", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 7.0 SP-Stack 15 - Release Information Note", "RefUrl": "/notes/1969163 "}, {"RefNumber": "1843157", "RefComponent": "BC-UPG-OCS", "RefTitle": "Known problems with support packages in SAP NW 7.40 AS ABAP", "RefUrl": "/notes/1843157 "}, {"RefNumber": "1843158", "RefComponent": "BC-UPG-OCS", "RefTitle": "Problems with add-on installation/upgrade to SAP NW 7.40 AS ABAP", "RefUrl": "/notes/1843158 "}, {"RefNumber": "1285869", "RefComponent": "BC-DWB-TOO-MEN", "RefTitle": "Program MENUSYST: Syntax error or session termination", "RefUrl": "/notes/1285869 "}, {"RefNumber": "1865464", "RefComponent": "CRM-BF", "RefTitle": "SAP CRM 7.0 SP-Stack 14 - Release Information Note", "RefUrl": "/notes/1865464 "}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1572314", "RefComponent": "BC-DWB-TOO-MEN", "RefTitle": "Phase XPRA_UPG: Termination CONNE_IMPORT_CONVERSION_ERROR", "RefUrl": "/notes/1572314 "}, {"RefNumber": "1377262", "RefComponent": "IS-OIL-DS-OGSD", "RefTitle": "OGSD 6.1 Installation / Delta Upgrade on SAP ECC 600", "RefUrl": "/notes/1377262 "}, {"RefNumber": "1850409", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v11: Installation & system copy with DB2 11", "RefUrl": "/notes/1850409 "}, {"RefNumber": "1292071", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 SR1 ABAP", "RefUrl": "/notes/1292071 "}, {"RefNumber": "1156970", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP SCM 7.0 ABAP", "RefUrl": "/notes/1156970 "}, {"RefNumber": "1492733", "RefComponent": "BC-UPG-ADDON", "RefTitle": "GRC NFE 10.0 installation on NW 7.02/7.0 3", "RefUrl": "/notes/1492733 "}, {"RefNumber": "1500074", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS:v10: Installation & system copy with DB2 10", "RefUrl": "/notes/1500074 "}, {"RefNumber": "1678047", "RefComponent": "HAN-DB", "RefTitle": "COMPUTE_INT_PLUS_OVERFLOW during activation", "RefUrl": "/notes/1678047 "}, {"RefNumber": "1246037", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide-SAP NetWeaver 7.0 including EHP1", "RefUrl": "/notes/1246037 "}, {"RefNumber": "1460321", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide-SAP NetWeaver 7.0 including EHP2", "RefUrl": "/notes/1460321 "}, {"RefNumber": "1821879", "RefComponent": "BC-UPG-NA", "RefTitle": "Launch of Snote dumps in case of inconsistent notes", "RefUrl": "/notes/1821879 "}, {"RefNumber": "1099841", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 3 ABAP", "RefUrl": "/notes/1099841 "}, {"RefNumber": "1809597", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Syntax error in SAPLSDSB or SAPLSDB2", "RefUrl": "/notes/1809597 "}, {"RefNumber": "1498113", "RefComponent": "BC-DWB-TOO-SCR", "RefTitle": "Generation error in screen SAPLENH_BADI_EDT_DEF 2200", "RefUrl": "/notes/1498113 "}, {"RefNumber": "1778145", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "'UNKNOWN_ERROR' in phase Disassemble during SP import", "RefUrl": "/notes/1778145 "}, {"RefNumber": "1724702", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: DB_EXISTS_TABLE does not require regeneration", "RefUrl": "/notes/1724702 "}, {"RefNumber": "1020887", "RefComponent": "CA-TDM-BUS-ERP", "RefTitle": "Generated views in receiver system not deleted correctly", "RefUrl": "/notes/1020887 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "1630727", "RefComponent": "BC-DB-MSS", "RefTitle": "Invalid column name 'area' during 702 SPAM", "RefUrl": "/notes/1630727 "}, {"RefNumber": "923523", "RefComponent": "FS-RI", "RefTitle": "Installing FS-RI 600 in SAP ECC 600", "RefUrl": "/notes/923523 "}, {"RefNumber": "923524", "RefComponent": "FS-RI", "RefTitle": "Upgrade to SAP ECC 600 with FS-RI 600", "RefUrl": "/notes/923524 "}, {"RefNumber": "930901", "RefComponent": "FS-PM", "RefTitle": "Enhancements for FSPM 3.10 NW04s installations", "RefUrl": "/notes/930901 "}, {"RefNumber": "1614802", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Mass activation hangs during INSERT in the table DDFTX", "RefUrl": "/notes/1614802 "}, {"RefNumber": "1476315", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Error in activation protocol for switch framework", "RefUrl": "/notes/1476315 "}, {"RefNumber": "1086728", "RefComponent": "BC-DWB-CEX", "RefTitle": "Unreqd deletions in SPAU comparison after importing SP/EHP", "RefUrl": "/notes/1086728 "}, {"RefNumber": "1521903", "RefComponent": "BC-DWB-CEX-BAD", "RefTitle": "Regenerate all inconsistent BAdI's", "RefUrl": "/notes/1521903 "}, {"RefNumber": "1109375", "RefComponent": "BC-DWB-DIC", "RefTitle": "Values from fixed value append are missing in the domain", "RefUrl": "/notes/1109375 "}, {"RefNumber": "1162171", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "DDIC: Texts of domain fixed values are lost after transport", "RefUrl": "/notes/1162171 "}, {"RefNumber": "1283197", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Cluster tables: Unnecessary conversion", "RefUrl": "/notes/1283197 "}, {"RefNumber": "1514063", "RefComponent": "BC-DB-ORA", "RefTitle": "Termination in SPAM import proper phase w/ error ORA 01007", "RefUrl": "/notes/1514063 "}, {"RefNumber": "1279262", "RefComponent": "PY-XX", "RefTitle": "Generation error in programm HMXCEDT0_CE", "RefUrl": "/notes/1279262 "}, {"RefNumber": "1619001", "RefComponent": "BC-CTS-TLS", "RefTitle": "Transporting view data does not work", "RefUrl": "/notes/1619001 "}, {"RefNumber": "715355", "RefComponent": "BC-CCM-BTC", "RefTitle": "BP_JOB_SELECT: Optimizing the job selection", "RefUrl": "/notes/715355 "}, {"RefNumber": "970837", "RefComponent": "BC-DWB-TOO-CLA", "RefTitle": "Creating methods with Note Assistant - mod infos created", "RefUrl": "/notes/970837 "}, {"RefNumber": "1596680", "RefComponent": "BC-DWB-TOO-ABA", "RefTitle": "Editor: Displaying implicit enhancement options", "RefUrl": "/notes/1596680 "}, {"RefNumber": "1548839", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Transport of Enhancement Spots cause BAdI impl. to be lost", "RefUrl": "/notes/1548839 "}, {"RefNumber": "1558728", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Performance improvement of after import method", "RefUrl": "/notes/1558728 "}, {"RefNumber": "1469610", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing ABAP programs after \"Downtime minimized import\"", "RefUrl": "/notes/1469610 "}, {"RefNumber": "725845", "RefComponent": "SRM-EBP-TEC-UPG", "RefTitle": "SRM upgrade - messages in the LONGPOST.LOG log file", "RefUrl": "/notes/725845 "}, {"RefNumber": "1630554", "RefComponent": "BC-TWB-TST-ECA", "RefTitle": "eCATT:Syntax error in TEST_ECATT_API_DEMO,TEST_SDC_API_DEMO", "RefUrl": "/notes/1630554 "}, {"RefNumber": "1629659", "RefComponent": "PY-DE-NT-CI", "RefTitle": "HR Support Packg 60073: No German Pension Reform in payroll", "RefUrl": "/notes/1629659 "}, {"RefNumber": "1420290", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "Performance when importing EHP/SP for enhancements", "RefUrl": "/notes/1420290 "}, {"RefNumber": "879289", "RefComponent": "BC-UPG-RDM", "RefTitle": "Support Package Stack Guide - SAP NetWeaver 7.0", "RefUrl": "/notes/879289 "}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856 "}, {"RefNumber": "1319582", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "CL_SFW_HELPER-GET_SWITCHED_COMPONENTS is incorrect", "RefUrl": "/notes/1319582 "}, {"RefNumber": "1281321", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Syntax error when importing Support Packages", "RefUrl": "/notes/1281321 "}, {"RefNumber": "1301446", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Incorrect return code for mass activation during transport", "RefUrl": "/notes/1301446 "}, {"RefNumber": "1542671", "RefComponent": "BC-DB-MSS", "RefTitle": "Incorrect syntax near 'OFF'", "RefUrl": "/notes/1542671 "}, {"RefNumber": "1488647", "RefComponent": "BW-BCT-GEN", "RefTitle": "Objects of the type DTRF deleted after method is executed", "RefUrl": "/notes/1488647 "}, {"RefNumber": "1532661", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Problems with SFW-BAdIs during upgrades and EHP installation", "RefUrl": "/notes/1532661 "}, {"RefNumber": "945855", "RefComponent": "SV-SMB-AIO-PFW-SB", "RefTitle": "Additional information about BP-INSTASS 600V1", "RefUrl": "/notes/945855 "}, {"RefNumber": "1553367", "RefComponent": "IS-REA", "RefTitle": "IS-REA 6.10: Installing Support Package 03", "RefUrl": "/notes/1553367 "}, {"RefNumber": "1249184", "RefComponent": "BC-SRV-ALV", "RefTitle": "ALV Grid: SYNTAX_ERROR in class CL_GUI_ALV_GRID", "RefUrl": "/notes/1249184 "}, {"RefNumber": "1292070", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 SR1 ABAP", "RefUrl": "/notes/1292070 "}, {"RefNumber": "1293387", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM Server 7.0 SR1 ABAP", "RefUrl": "/notes/1293387 "}, {"RefNumber": "1293745", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NetWeaver 7.0 EHP 1 SR1 (ABAP)", "RefUrl": "/notes/1293745 "}, {"RefNumber": "1510174", "RefComponent": "PY-XX", "RefTitle": "HCM: dependency of Support Packages to Netweaver Notes", "RefUrl": "/notes/1510174 "}, {"RefNumber": "1406740", "RefComponent": "BC-DB-DB6", "RefTitle": "DB6: Potentially unnoticed rollback during array operation", "RefUrl": "/notes/1406740 "}, {"RefNumber": "1276895", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. about Upgrading to SAP Solution Manager 7.0 EHP1", "RefUrl": "/notes/1276895 "}, {"RefNumber": "810081", "RefComponent": "CRM-MW-SPM", "RefTitle": "Import of Support Packages fails", "RefUrl": "/notes/810081 "}, {"RefNumber": "1391715", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Mass activatn program terminates: Indexes do not match table", "RefUrl": "/notes/1391715 "}, {"RefNumber": "1527430", "RefComponent": "FS-BP", "RefTitle": "BP_LEG: Database conversion of the table BP1030", "RefUrl": "/notes/1527430 "}, {"RefNumber": "960783", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 7.0 Support Rel. 2 ABAP", "RefUrl": "/notes/960783 "}, {"RefNumber": "818322", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP", "RefUrl": "/notes/818322 "}, {"RefNumber": "1491290", "RefComponent": "BC-CTS-TLS", "RefTitle": "Missing synchronization after importing include deletions", "RefUrl": "/notes/1491290 "}, {"RefNumber": "1321756", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Mass activation: Object activated instead of deleted", "RefUrl": "/notes/1321756 "}, {"RefNumber": "1074030", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans: \"Duplicate key\" composite SAP Note (as of Release 6.10)", "RefUrl": "/notes/1074030 "}, {"RefNumber": "1156969", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP CRM 7.0 ABAP", "RefUrl": "/notes/1156969 "}, {"RefNumber": "1461232", "RefComponent": "BC-DWB-TOO", "RefTitle": "After importing a Support Package: No logon", "RefUrl": "/notes/1461232 "}, {"RefNumber": "1354957", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Tables with more than 3 long string fields", "RefUrl": "/notes/1354957 "}, {"RefNumber": "1468464", "RefComponent": "BC-CTS-TLS", "RefTitle": "Access to non-existing tables DD43L and DD43T", "RefUrl": "/notes/1468464 "}, {"RefNumber": "1156844", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. on Upgrading to SAP SRM Server 7.0", "RefUrl": "/notes/1156844 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "1180553", "RefComponent": "BC-DB-MSS", "RefTitle": "Syntax error 170 during index creation on SQL 2000", "RefUrl": "/notes/1180553 "}, {"RefNumber": "1417505", "RefComponent": "BC-CTS-TLS", "RefTitle": "Incomplete import of (ABAP Dictionary) objects", "RefUrl": "/notes/1417505 "}, {"RefNumber": "1306409", "RefComponent": "BC-DWB-DIC", "RefTitle": "Missing fixed value texts", "RefUrl": "/notes/1306409 "}, {"RefNumber": "994195", "RefComponent": "IS-REA", "RefTitle": "IS-REA: Upgrading IS-REA 4.61 to IS-REA 6.10", "RefUrl": "/notes/994195 "}, {"RefNumber": "1256384", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "View/structure not active: Key field of type string", "RefUrl": "/notes/1256384 "}, {"RefNumber": "1237062", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "SFW-HELPER ignores the components SRM_EXT and SRM_EXPL", "RefUrl": "/notes/1237062 "}, {"RefNumber": "1138351", "RefComponent": "BC-UPG-OCS", "RefTitle": "Long runtime of background job SAPRUPGM", "RefUrl": "/notes/1138351 "}, {"RefNumber": "1126127", "RefComponent": "BC-DB-DB6-SYS", "RefTitle": "DB6: Deferred table creation and row compression", "RefUrl": "/notes/1126127 "}, {"RefNumber": "920016", "RefComponent": "PA", "RefTitle": "Contents & applying HR Support Packages ERP6.00", "RefUrl": "/notes/920016 "}, {"RefNumber": "1262653", "RefComponent": "BC-DWB-CEX", "RefTitle": "SPAU: New object is deleted after note is reset", "RefUrl": "/notes/1262653 "}, {"RefNumber": "889850", "RefComponent": "BC-INS", "RefTitle": "OBSOLETE: SAP NetWeaver 2004s based ABAP Inst/Upgrade for Asia", "RefUrl": "/notes/889850 "}, {"RefNumber": "1132422", "RefComponent": "BC-DWB-TOO-ENH", "RefTitle": "ENH_CREATE_ENHCROSS: DEAD LOCK", "RefUrl": "/notes/1132422 "}, {"RefNumber": "1366799", "RefComponent": "BC-DB-DB4", "RefTitle": "IBM i: Over 600 GB of journal receiver data for SAPKB70019", "RefUrl": "/notes/1366799 "}, {"RefNumber": "1374980", "RefComponent": "BC-UPG-OCS", "RefTitle": "Inconsistent DDIC objects after importing SAPKH60403", "RefUrl": "/notes/1374980 "}, {"RefNumber": "961513", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR2 ABAP", "RefUrl": "/notes/961513 "}, {"RefNumber": "975861", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 6.0 ABAP", "RefUrl": "/notes/975861 "}, {"RefNumber": "1108510", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info.: Upgrade to SAP SRM Server 5.5 SR3 ABAP", "RefUrl": "/notes/1108510 "}, {"RefNumber": "1152612", "RefComponent": "BW-BCT-ISR-RSL", "RefTitle": "Incorrect component type in structure WRMA_S_RESULT_DATA", "RefUrl": "/notes/1152612 "}, {"RefNumber": "1288862", "RefComponent": "BC-CTS-TLS", "RefTitle": "R3trans terminates during import with RC=7777 (Windows)", "RefUrl": "/notes/1288862 "}, {"RefNumber": "1147591", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "Improvements for Web Servcies / Proxy Generation with SP14", "RefUrl": "/notes/1147591 "}, {"RefNumber": "1132685", "RefComponent": "PY-XX", "RefTitle": "Deletion of Arial True type font used in Payment/ETP summary", "RefUrl": "/notes/1132685 "}, {"RefNumber": "1279090", "RefComponent": "BC-UPG-OCS", "RefTitle": "HCM: Queue Calc.: CLC of EA-HRC<xx> requires HRSP of SAP_HR", "RefUrl": "/notes/1279090 "}, {"RefNumber": "1265242", "RefComponent": "PA-PA-JP", "RefTitle": "DDIC Activation Error on Tables T5J73T and T5J74", "RefUrl": "/notes/1265242 "}, {"RefNumber": "1167549", "RefComponent": "IS-REA", "RefTitle": "IS-REA 6.10: Installation Support Package 02", "RefUrl": "/notes/1167549 "}, {"RefNumber": "1171293", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP19)/BI7.1SPS07: Content disappears", "RefUrl": "/notes/1171293 "}, {"RefNumber": "822380", "RefComponent": "BC-UPG-OCS", "RefTitle": "Problems with add-on installation/upgrade in SAP NW 7.0x AS ABAP", "RefUrl": "/notes/822380 "}, {"RefNumber": "1232804", "RefComponent": "BC-UPG-OCS", "RefTitle": "SPAM/SAINT: TP_FAILURE 0212 in phase IMPORT_OBJECT_LIST", "RefUrl": "/notes/1232804 "}, {"RefNumber": "1118803", "RefComponent": "BC-UPG-ADDON", "RefTitle": "ECC 600 Support Packages and ERP enhancement packages", "RefUrl": "/notes/1118803 "}, {"RefNumber": "1173174", "RefComponent": "BC-DB-DB4", "RefTitle": "DB2/400: Import terminates w/o warning after main import", "RefUrl": "/notes/1173174 "}, {"RefNumber": "994534", "RefComponent": "IS-REA", "RefTitle": "IS-REA: Upgrading IS-REA 5.10 to IS-REA 6.10", "RefUrl": "/notes/994534 "}, {"RefNumber": "994353", "RefComponent": "IS-REA", "RefTitle": "IS-REA: Upgrading IS-REA 4.71 to IS-REA 6.10", "RefUrl": "/notes/994353 "}, {"RefNumber": "994150", "RefComponent": "IS-REA", "RefTitle": "IS-REA 6.10: Installation note", "RefUrl": "/notes/994150 "}, {"RefNumber": "1137503", "RefComponent": "BC-UPG-OCS", "RefTitle": "Secondary index for table REPOSRC", "RefUrl": "/notes/1137503 "}, {"RefNumber": "837691", "RefComponent": "BC-CCM-BTC", "RefTitle": "Background jobs are deleted in unintentionally", "RefUrl": "/notes/837691 "}, {"RefNumber": "906008", "RefComponent": "BC-UPG-OCS", "RefTitle": "Missing method implementation after Support Package import", "RefUrl": "/notes/906008 "}, {"RefNumber": "1120534", "RefComponent": "BC-ESI-WS-ABA", "RefTitle": "WS Patch collection (1) for WS tracing and alerting", "RefUrl": "/notes/1120534 "}, {"RefNumber": "1142128", "RefComponent": "BC-UPG", "RefTitle": "Note Assistant: Obsolete notes are incorrectly deimplemented", "RefUrl": "/notes/1142128 "}, {"RefNumber": "1057855", "RefComponent": "BC-DB-MSS", "RefTitle": "DB13/DBACOCKPIT database jobs fail after SP12", "RefUrl": "/notes/1057855 "}, {"RefNumber": "1114930", "RefComponent": "IS-REA", "RefTitle": "IS-REA 6.10: Installation Support Package 01", "RefUrl": "/notes/1114930 "}, {"RefNumber": "1130693", "RefComponent": "FI-TV", "RefTitle": "Generation error in upgrade from ERP 6.0 to EhP2", "RefUrl": "/notes/1130693 "}, {"RefNumber": "1082597", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "Not enough business functions activated in SFW5", "RefUrl": "/notes/1082597 "}, {"RefNumber": "892842", "RefComponent": "BC-INS", "RefTitle": "SAPCAR error: format error in header", "RefUrl": "/notes/892842 "}, {"RefNumber": "1012176", "RefComponent": "BW-BCT-PY", "RefTitle": "Indexes for tables PPOIX and PPOPX", "RefUrl": "/notes/1012176 "}, {"RefNumber": "864482", "RefComponent": "BC-DB-SDB", "RefTitle": "Import process using TP terminates with -109, -102", "RefUrl": "/notes/864482 "}, {"RefNumber": "998903", "RefComponent": "IS-DFS", "RefTitle": "Prerequisites for DFPS 610 Support Package 3.", "RefUrl": "/notes/998903 "}, {"RefNumber": "967821", "RefComponent": "BC-SRV-RM", "RefTitle": "Incorrect definition of SRM_FILL_KC_TABLES_AFTER_IMP", "RefUrl": "/notes/967821 "}, {"RefNumber": "948937", "RefComponent": "PY-XX", "RefTitle": "Syntax Error in Payroll in ERP2005 after SAPKH60001", "RefUrl": "/notes/948937 "}, {"RefNumber": "917999", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "PARCONV_UPG terminates with TSV_TNEW_PAGE_ALLOG_FAILED", "RefUrl": "/notes/917999 "}, {"RefNumber": "924885", "RefComponent": "IS-U-IDE", "RefTitle": "Incorrect generation date for data exchange tasks", "RefUrl": "/notes/924885 "}, {"RefNumber": "925688", "RefComponent": "SV-SMG", "RefTitle": "Double-Byte environment: Error while loading SAPKITL412", "RefUrl": "/notes/925688 "}, {"RefNumber": "97620", "RefComponent": "BC-UPG-OCS", "RefTitle": "OCS info: Overview of important OCS SAP Notes", "RefUrl": "/notes/97620 "}, {"RefNumber": "871402", "RefComponent": "BC-VMC", "RefTitle": "Incorrect activation for transports w/ deleted config.", "RefUrl": "/notes/871402 "}, {"RefNumber": "865198", "RefComponent": "BC-DB-ORA", "RefTitle": "Triggers as database objects", "RefUrl": "/notes/865198 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "700", "To": "702", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}