{"Request": {"Number": "2349294", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 302, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018376112017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002349294?language=E&token=37A254B7473BEEEF7323B7A6FFBC2FF2"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002349294", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002349294/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2349294"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2016.10.31"}, "SAPComponentKey": {"_label": "Component", "value": "CO-PC-PCP-REF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Reference and Simulation Costing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Cost Controlling", "value": "CO-PC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Cost Planning", "value": "CO-PC-PCP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PC-PCP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Reference and Simulation Costing", "value": "CO-PC-PCP-REF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PC-PCP-REF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2349294 - S4TWL - Reference and Simulation Costing"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Base Planning Object</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p dir=\"ltr\"><strong>Description</strong></p>\r\n<p dir=\"ltr\">You are currently using base planning objects to store cost estimates that can be included in cost estimates for projects, sales orders and so on as items of type B (base planning object). The creation of cost estimates with reference to base planning objects is no longer supported in SAP S/4HANA. Instead you are recommended to use transaction CKECP to prepare ad-hoc cost estimates and CKUC to prepare multilevel material cost estimates where BOMs and routings are not available for the material in question.</p>\r\n<p dir=\"ltr\">Task: Check, if base planning objects are used in the customer system.</p>\r\n<p dir=\"ltr\">Procedure: Call transaction se16 and select CKHS. On the selection screen you have to enter BZOBJ ='1' &#160;and use action 'Number of Entries' to determine the number of base planning objects.</p>\r\n<p dir=\"ltr\">Rating:<br />Simplification item and note is not relevant for the customer, if no CKHS entries exists with BZOBJ = '1'.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p dir=\"ltr\"><strong>Business Process related information</strong></p>\r\n<p dir=\"ltr\">Check your existing processes for cost estimation to determine whether you use costing items of type B (base planning object) as references for other cost estimates.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\r\n</td>\r\n<td>Transaction Code</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Create Base Planning Object</p>\r\n</td>\r\n<td>KKE1</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Change&#160;Base Planning Object</p>\r\n</td>\r\n<td>KKE2</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Display&#160;Base Planning Object</p>\r\n</td>\r\n<td>KKE3</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Revaluate&#160;Base Planning Object</p>\r\n</td>\r\n<td>KKEB</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Base Planning Object vs. Other Unit Cost Estimate</p>\r\n</td>\r\n<td>KKEC</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Costed Multilevel BOM (only Base Planning Object Exploded)</p>\r\n</td>\r\n<td>KKED</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Itemization</p>\r\n</td>\r\n<td>KKB4</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Overview of Base Planning Objects</p>\r\n</td>\r\n<td>\r\n<p>S_ALR_87013028</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Where Used List of Base Planning Objects</p>\r\n</td>\r\n<td>\r\n<p>S_ALR_87013029</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>Multilevel Explosion of Base Planning Object</p>\r\n</td>\r\n<td>\r\n<p>S_ALR_87013036</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p dir=\"ltr\"><strong>Required and Recommended Action(s)</strong></p>\r\n<p dir=\"ltr\">Remove above transactions from existing roles and train users to work with alternative transactions.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D002766)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D056103)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002349294/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349294/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349294/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349294/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349294/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349294/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349294/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349294/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002349294/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2945702", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Release Information Note for Finance", "RefUrl": "/notes/2945702 "}, {"RefNumber": "2742613", "RefComponent": "CO-OM", "RefTitle": "Obsolete or replaced transaction codes and programs in Finance applications of S/4", "RefUrl": "/notes/2742613 "}, {"RefNumber": "2270335", "RefComponent": "FIN-MIG", "RefTitle": "S4TWL - Replaced Transaction Codes and Programs in FIN", "RefUrl": "/notes/2270335 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}