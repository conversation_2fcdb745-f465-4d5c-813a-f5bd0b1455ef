{"Request": {"Number": "2589723", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 219, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002589723?language=E&token=541FEF9551D08A3FCF6494F76A986A3F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002589723", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002589723/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2589723"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.06.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-MM-IM"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Inventory Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2589723 - MCNB does not fill setup table for 2LIS_03_BX"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Transaction MCNB (report RMCBINIT_BW, which's used to fill setup table for 2LIS_03_BX) works fine and informs you that a number of records were selected for BW extraction. However, transaction RSA3 returns no records for DataSource 2LIS_03_BX in full mode and table MC03BX0SETUP remains empty.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>Source System of BW or BW4HANA system</li>\r\n<li>SAP ERP Central Component</li>\r\n<li>SAP ERP</li>\r\n<li>SAP enhancement package for SAP ERP</li>\r\n<li>SAP enhancement package for SAP ERP, version for SAP HANA</li>\r\n<li>SAP S/4HANA</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<ol>\r\n<li>Execute transaction&#160;MCNB (report&#160;RMCBINIT_BW)</li>\r\n<li>Systems issues message \"X stock data records were selected for BW extraction\" (Message no. MCBW016)</li>\r\n<li>Systems issues message \"( Z * Y) + X = N stock data records were extracted for BW\" (Message no. MCBW015)</li>\r\n<li>Simulate a full 2LIS_03_BX extraction with transaction RSA3</li>\r\n<li>Systems issues message&#160;\"0 data records selected\" (Message no. RJ007)</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>In order to execute MCNB successfully, the update for the extract structure 2LIS_03_BF or 2LIS_03_UM must be active (see SAP Note <a target=\"_blank\" href=\"/notes/631345\">631345</a>) <strong>and</strong> <strong>at least one</strong> of the following conditions must be true:</p>\r\n<div class=\"longtext\">\r\n<ul>\r\n<li>At least one ODP subscription for 2LIS_03_BF or 2LIS_03_UM queue exists in transaction ODQMON with 'Subscription Only' mode.</li>\r\n<li>The source system is connected to a BW system through BW Service API, i.e. an entry for the BW system exists in table RSBASIDOC.</li>\r\n</ul>\r\n<p>The issue occurs if&#160;no 2lis_03* is active or if none of the conditions above is met.</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<ol>\r\n<li>Activate at least&#160;2LIS_03_BF or&#160;2LIS_03_UM&#160;in transaction LBWE</li>\r\n<li>Indicate that at least one target system requires that data, depending on the API you intend to use:</li>\r\n<ol style=\"list-style-type: lower-alpha;\">\r\n<li>ODP - trigger a valid delta initialization for 2LIS_03_BF or&#160;2LIS_03_UM using <strong>one of</strong> the following methods</li>\r\n<ol style=\"list-style-type: upper-roman;\">\r\n<li>Simulate a subscription by running report RODPS_REPL_TEST<sup>1</sup> with the following parameters:</li>\r\n<ul>\r\n<li>ODP Context: 'DataSources/Extractors'</li>\r\n<li>ODP Name: the complete DataSource name, e.g.&#160;2LIS_03_BF or 2LIS_03_UM</li>\r\n<li>Replication Mode: 'Extraction of Last Delta'</li>\r\n</ul>\r\n<li>If the subscriber is a BW, trigger an init request for 2LIS_03_BF/2LIS_03_UM</li>\r\n<li>If the subscriber is a Business Objects Data Service (BODS), trigger a CDC request for 2LIS_03_BF/2LIS_03_UM</li>\r\n</ol>\r\n<li>If you intend to use BW Service API only, connect the BW system to the source system</li>\r\n</ol></ol>\r\n<p><sup>1</sup>Remark: Using report RODPS_REPL_TEST will create a dummy subscription in transaction ODQMON. Do not forget to reset that subscription after the setup table filling process is complete by using the \"Reset Delta\" button to prevent the performance issues explained in SAP Note <a target=\"_blank\" href=\"/notes/2300483\">2300483</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p><a target=\"_blank\" href=\"/notes/631345\">631345</a> - Initialization of opening stocks: no data for 2LIS_03_BX</p>\r\n<p><a target=\"_blank\" href=\"/notes/2386514\">2386514</a> - ODP: No extraction structure active or no BW connected (Message no. M2630)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>MCBW016,&#160;MCBW015,&#160;RJ007, setup table, NPRT, zero record,</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I074505)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I074505)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002589723/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002589723/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002589723/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002589723/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002589723/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002589723/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002589723/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002589723/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002589723/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2678507", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "SAP ERP / S/4HANA Inventory Management in SAP BW/4HANA (Composite SAP Note)", "RefUrl": "/notes/2678507 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP BW/4HANA all versions "}, {"Product": "SAP ERP Central Component all versions "}, {"Product": "SAP ERP all versions "}, {"Product": "SAP NetWeaver all versions "}, {"Product": "SAP S/4HANA all versions "}, {"Product": "SAP enhancement package for SAP ERP all versions "}, {"Product": "SAP enhancement package for SAP ERP, version for SAP HANA all versions "}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "2 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 5.0, "Quality-Votes": 3, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 3}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}