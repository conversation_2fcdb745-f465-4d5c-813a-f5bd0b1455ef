{"Request": {"Number": "2970611", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 273, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001575582020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002970611?language=E&token=CBB72A6FD68C4CDDEF92BAFA28926FFA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002970611", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002970611/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2970611"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "IS-MP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Mill Products"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Mill Products", "value": "IS-MP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-MP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2970611 - SAP S/4HANA On Premise 2020 release: Mill Products Restriction Note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are using SAP S/4HANA 2020 for Mill Products and this note informs you about restrictions in this release.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<div class=\"longtext\">\r\n<p>Release restrictions, SAP S/4HANA 2020, Mill Products.</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note provides information about the restrictions&#160;that exist for SAP S/4HANA 2020 release.</p>\r\n<p>Note: This SAP Note is subject to change.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><span style=\"text-decoration: underline;\"><strong>Restrictions for SAP S/4HANA 2020</strong></span></p>\r\n<p><strong>Released with restrictions:</strong></p>\r\n<ul>\r\n<li>Extended Batch Assignment does not work for processes associated with transaction CO11N (Enter time ticket for Production Order).</li>\r\n</ul>\r\n<p><strong>Following is not supported:</strong></p>\r\n<div class=\"OutlineElement Ltr SCXW101703933 BCX8\">\r\n<p class=\"Paragraph SCXW101703933 BCX8\" style=\"padding-left: 30px;\"><strong><span style=\"text-decoration: underline;\">ATP:</span>&#160;</strong></p>\r\n<p class=\"Paragraph SCXW101703933 BCX8\" style=\"padding-left: 30px;\">- Rules Based ATP with Characteristics is not supported</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW101703933 BCX8\">\r\n<p class=\"Paragraph SCXW101703933 BCX8\" style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\"><strong>Batch determination:</strong></span></p>\r\n<p class=\"Paragraph SCXW101703933 BCX8\" style=\"padding-left: 30px;\">- In Extended Batch Assignment Routine 004:</p>\r\n</div>\r\n<p style=\"padding-left: 90px;\">- Alternative unit of measure for sales/delivery process.</p>\r\n<p style=\"padding-left: 90px;\">- Alternative unit of measure for component batches in production order.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\"><strong>Non Ferrous Metals solution&#160;<strong>(NFM)</strong>:</strong></span></p>\r\n<div class=\"OutlineElement Ltr SCXW195198447 BCX8\">\r\n<div class=\"OutlineElement Ltr SCXW195198447 BCX8\">\r\n<p class=\"Paragraph SCXW195198447 BCX8\" style=\"padding-left: 30px;\">- Purchasing Scheduling agreements with NF Metals.</p>\r\n<p style=\"padding-left: 30px;\">- Returns, which belongs to a sales order with NF Metals.</p>\r\n<p style=\"padding-left: 30px;\">- Purchase order with provision: If you change the rate determination key of a purchase order with provision in the invoice verification for NF metals, a stock correction for the NF provision will take place.&#160;Also&#160;it will not&#8239;&#8239; be checked if the provision is&#160;booked correct, during the goods receipt or if a post-run update is created. In this situation the provision stock can be in short term higher.</p>\r\n<p style=\"padding-left: 30px;\">- Coverage process in combination with BAdI: Changes to Document Data of NF Metal Processing.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\"><strong>Logistics:</strong></span></p>\r\n<p style=\"padding-left: 30px;\">&#160;- Single unit batch material for Outsourced Manufacturing (Business Function LOG_MM_OM_1).</p>\r\n</div>\r\n</div>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\"><strong>Outbound delivery:</strong></span></p>\r\n<p style=\"padding-left: 30px;\">Delivery dependent trading unit change (DTUC):</p>\r\n<p style=\"padding-left: 60px;\">-&#160;DTUC process for handling unit managed storage locations.</p>\r\n<p style=\"padding-left: 60px;\">- Alternative Unit of Measure in Inventory and DTUC.</p>\r\n<p style=\"padding-left: 60px;\">- Creation of LE-WM transfer requirement from storage bin to cutting area: You can use only LE-WM transfer orders. See setup in DTUC customizing.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\"><strong>PP/DS:</strong></span></p>\r\n<p style=\"padding-left: 30px;\">- Multi Item Orders</p>\r\n<p style=\"padding-left: 30px;\">- Mixed Out-Put Planning (MOP).</p>\r\n<p style=\"padding-left: 30px;\">- Combined Orders cannot be used in connection with SAP APO, but with S/4HANA Advanced Planning (Embedded PP/DS)</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\"><strong><strong>EWM:</strong></strong></span></p>\r\n<p style=\"padding-left: 30px;\">- Extended Warehouse Management does not support&#160;batch specific&#160;unit of measure (product&#160;/&#160;single unit batch), see SAP note&#160;<a target=\"_blank\" href=\"/notes/2432414\">2432414</a>.</p>\r\n<p style=\"padding-left: 30px;\">- Embedded EWM or stand alone cannot&#160;be used for Delivery dependent trading unit change (DTUC), as cutting /rewinding process is based at batch specific unit of measure.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\"><strong>Transportation&#160;Management:</strong></span></p>\r\n<p style=\"padding-left: 60px;\">- Fast Entry of Characteristics in Purchase Order for ERP-TMS: Order Integration (Business Functions: LOG_TM_ORD_INT and LOG_TM_ORD_INT_*) is not supported.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "Naresh VVSR (I045321)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022238)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002970611/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002970611/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002970611/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002970611/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002970611/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002970611/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002970611/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002970611/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002970611/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2943206", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Restriction Note", "RefUrl": "/notes/2943206"}, {"RefNumber": "2820840", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2820840"}, {"RefNumber": "2815395", "RefComponent": "IS-MP-NF", "RefTitle": "Enhancement of transaction BP with Non Ferrous Metals (NFM) \"exchange key\" field", "RefUrl": "/notes/2815395"}, {"RefNumber": "2698659", "RefComponent": "IS-MP", "RefTitle": "S/4HANA for Cable industry - Solution and Implementation facts", "RefUrl": "/notes/2698659"}, {"RefNumber": "2432414", "RefComponent": "SCM-EWM-DLP", "RefTitle": "Materials / Products with batch-specific material units of measure", "RefUrl": "/notes/2432414"}, {"RefNumber": "2270403", "RefComponent": "IS-MP-NF", "RefTitle": "S4TWL - Non Ferrous Metal Processing", "RefUrl": "/notes/2270403"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2943206", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Restriction Note", "RefUrl": "/notes/2943206 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}