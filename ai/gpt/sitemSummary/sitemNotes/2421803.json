{"Request": {"Number": "2421803", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 673, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018507102017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002421803?language=E&token=9D778DB37764580161A380EDBBEFACF7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002421803", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002421803/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2421803"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.12.2018"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-MD-BP-IF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Exchange CRM Online <-> R/3"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2421803 - Industry sector data replication issue"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Industry sector code data is not correctly replicated between ERP and CRM systems. Because of the mapping incompatibilities, data is not correctly stored and correctly replicated back. If there is a CVI synchronization active, then the Industry sector data is not mapped correctly. There is no issue with the direct replication of CRM BP to ERP customer.</p>\r\n<p>But the problem arises during the CVI synchronization where CRM BP is synchronized with ERP customer and then to ERP BP. Since BP is the leading object in S4 scenarios, proper synchronization of data has to happen from ERP BP to CRM BP.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>CVI, Industry Code, KNA1-BRAN1,KNA1-BRAN2,KNA1-BRAN3,KNA1-BRAN4, KNA1-BRAN5, BUT0IS, KNA1-BRSCH, CRM Classification, Industry key, S4HANA.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Reason:</p>\r\n<ul>\r\n<li>\r\n<div>Industry sector data is not correctly mapped and maintained during inbound and outbound.</div>\r\n</li>\r\n</ul>\r\n<p>Prerequisites:</p>\r\n<ul>\r\n<li>CVI synchronization should be active.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the attached correction instructions.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I048854)"}, {"Key": "Processor                                                                                           ", "Value": "D047682"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002421803/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002421803/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002421803/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002421803/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002421803/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002421803/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002421803/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002421803/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002421803/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2285062", "RefComponent": "CRM-MD-BP-IF", "RefTitle": "S4TWL: Business partner data exchange between SAPCRM and S/4 HANA, on-premise edition", "RefUrl": "/notes/2285062 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10004INS4CORE", "URL": "/supportpackage/SAPK-10004INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10102INS4CORE", "URL": "/supportpackage/SAPK-10102INS4CORE"}, {"SoftwareComponentVersion": "SAP_APPL 617", "SupportPackage": "SAPKH61714", "URL": "/supportpackage/SAPKH61714"}, {"SoftwareComponentVersion": "SAP_APPL 618", "SupportPackage": "SAPK-61807INSAPAPPL", "URL": "/supportpackage/SAPK-61807INSAPAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0002421803/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 2, "URL": "/corrins/0002421803/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2383569 ", "URL": "/notes/2383569 ", "Title": "Short dump during replication of a business partner from CRM to ECC with active CVI", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2404030 ", "URL": "/notes/2404030 ", "Title": "Enable Text data exchange", "Component": "LO-MD-BP"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "101", "Number": "2421803 ", "URL": "/notes/2421803 ", "Title": "Industry sector data replication issue", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2383569 ", "URL": "/notes/2383569 ", "Title": "Short dump during replication of a business partner from CRM to ECC with active CVI", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2404030 ", "URL": "/notes/2404030 ", "Title": "Enable Text data exchange", "Component": "LO-MD-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "1968132 ", "URL": "/notes/1968132 ", "Title": "Business partner replication between CRM and ECC with active CVI", "Component": "LO-MD-BP-CM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2070408 ", "URL": "/notes/2070408 ", "Title": "Incorrect entries in the table CRMKUNNR", "Component": "LO-MD-BP-CM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2351897 ", "URL": "/notes/2351897 ", "Title": "Replication of longtexts from CRM BP to ECC Customer Master", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "618", "ValidTo": "618", "Number": "2351897 ", "URL": "/notes/2351897 ", "Title": "Replication of longtexts from CRM BP to ECC Customer Master", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "618", "ValidTo": "618", "Number": "2383569 ", "URL": "/notes/2383569 ", "Title": "Short dump during replication of a business partner from CRM to ECC with active CVI", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "618", "ValidTo": "618", "Number": "2404030 ", "URL": "/notes/2404030 ", "Title": "Enable Text data exchange", "Component": "LO-MD-BP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}