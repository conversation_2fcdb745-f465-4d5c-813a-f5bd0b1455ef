{"Request": {"Number": "2445069", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 448, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018952062017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002445069?language=E&token=EF056E62C26B33EEAA257F64505665AF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002445069", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002445069/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2445069"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.07.2017"}, "SAPComponentKey": {"_label": "Component", "value": "PPM-PRO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Project Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Portfolio and Project Management", "value": "PPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project Management", "value": "PPM-PRO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PPM-PRO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2445069 - S4TC CPRXRPM Master Check for S/4 System Conversion Checks  (new Simplification Item Checks)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Simplification Item Checks (SIC-checks) formerly called Pre-conversion checks for SAP Portfolio and Project Management need to be executed before the conversion to SAP Portfolio and Project Management for S/4HANA.</p>\r\n<p>As a follow up of the pre-conversion check or to prepare a migration, the currently used authorizations maybe need to be analyzed.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S4TC, S/4 Conversion, S/4 Portfolio and Project Management <br />Authorization Inheritance Information&#160;Migration</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the correction instructions in this SAP Note in order to enable a SIC-checks. Perform a detailed check via the new readyness check <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#readiness\">https://launchpad.support.sap.com/#readiness</a>&#160;to make&#160;the system easily ready for the S/4HANA conversion.</p>\r\n<p><strong>SIC-checks to prepare a conversion to SAP Portfolio and Project Management for S/4HANA</strong></p>\r\n<p>The following checks will be executed during the&#160;SIC-check:</p>\r\n<p><span style=\"text-decoration: underline;\">Simplified ACLs (Access Control Lists) concept in SAP Portfolio and Project Management for SAP S/4HANA</span><br />Details can be found in SAP Note 2343374, below the messages that could be raised during the check.</p>\r\n<ul>\r\n<li>'Authorizations on Organizational unit level are not supported anymore (for details please check SAP Note 2343374).' (Check ID: PPM_FUNC_ACL_01_03)</li>\r\n<li>'Option \"None\" is not supported anymore on authorization level (for details please check SAP Note&#160;2343374).' &#160;(Check ID: PPM_FUNC_ACL_01_04)</li>\r\n<li>'Object Type-Related Activities for Authorization Check contain changes in fix activity hierarchy Admin -&gt; Write -&gt; Read (for details please check SAP Note 2343374).' &#160;(Check ID: PPM_FUNC_ACL_01_01)</li>\r\n<li>'Hierarchies of User Groups to User Group for Authorization are only allowed up to a level of 3 (for details please check SAP Note 2343374).' &#160;(Check ID: PPM_FUNC_ACL_01_02)</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Control Plans in SAP Portfolio and Project Management for SAP S/4HANA</span><br />Details can be found in SAP Note 2353885 and in consulting note 2363785, below the messages that could be raised during the check.</p>\r\n<ul>\r\n<li>'The control plan integration&#160;is not available anymore&#160;(for details please check SAP Note&#160;2353885).' (Check ID: PPM_FUNC_CNTR_PLAN_01)</li>\r\n<li>'The&#160;control&#160;plan&#160;UI&#160;field&#160;customizing&#160;is&#160;deprecated&#160;(for&#160;details&#160;please&#160;check&#160;SAP&#160;Note&#160;2363785).'(Check ID: PPM_FUNC_CNTR_PLAN_02)</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">KM Documents in SAP Portfolio and Project Management for SAP S/4HANA<br /></span>Details can be found in SAP Note 2354985, below the messages that could be raised during the check.<span style=\"text-decoration: underline;\"><br /></span></p>\r\n<ul>\r\n<li>'The&#160;usage&#160;of&#160;Knowledge&#160;Management&#160;(KM)&#160;for&#160;Document&#160;Management&#160;is&#160;no&#160;longer&#160;available&#160;(for&#160;details&#160;please&#160;check&#160;SAP&#160;Note&#160;2354985).'&#160;(Check ID: PPM_INTGR_KM_DOC_01)</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">cFolders not available anymore in SAP Portfolio and Project Management</span></p>\r\n<p>Details can be found in SAP Note 2348430, below the messages that could be raised during the check.</p>\r\n<ul>\r\n<li>'SAP&#160;cFolders&#160;is&#160;not&#160;available&#160;anymore&#160;(for&#160;details&#160;please&#160;check&#160;SAP&#160;Note&#160;2348430).' (Check ID:&#160;PPM_FUNC_CFOLDERS_01)</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">SRM Integration in SAP Portfolio and Project Management for SAP S/4HANA</span></p>\r\n<p><br />Details can be found in SAP Note 2321885, below the messages that could be raised during the check.</p>\r\n<ul>\r\n<li>The SRM integration is not available anymore (for details please check SAP Note 2359354). (Check ID: PPM_INTGR_SRM_01)</li>\r\n</ul>\r\n<p><strong>Detail analysis of used authorizations</strong></p>\r\n<p>In addition, this SAP Note delivers the detailed readyness check, which can be used to further analyze the used authorizations. The section below describes how a customer can use the detailed readyness check.</p>\r\n<p><span style=\"line-height: 1.5;\">To&#160;identify&#160;</span><span style=\"text-decoration: underline; line-height: 1.5;\">before</span>&#160;the conversion to&#160;SAP S/4 HANA&#160;if&#160;your&#160;definition of authorization in PPM is affected by the described changes,&#160;you can execute the detailed readyness check and inspect the execution result of the readyness check. The following execution results may be displayed (Changes to projects authorizations should be made before data migration in case return code is 4, return code 0 signals that all prerequisite for migration of Authorization Inheritance Information are met):</p>\r\n<p>&#160;a) 'Authorizations on Organizational unit level not supported anymore':</p>\r\n<p>Depending on the displayed client logon to&#160;the client of the system mentioned. Display the project element&#160;related to the following Object types DPO (Project), PPO (Phase), TTO (Task), CTO (Checklist), ITO (Checklist Item), RAG (Checklist reference) by navigating to the work center Project Management. Choose Service -&gt; Search.&#160;On the appearing search popup choose \"Search for:\" according to the Object type listed. Fill the listed EXTERNAL_ID (Number) into the Search Criteria field Project Number/Phase Number/Number&#160;and click \"Find button\". Click on the search result to display the project element detail screen. Navigate to the tab \"Authorizations\" -&gt; \"Organizational Units\". Be aware that the maintained Organizational Unit(s) are not considered in S/4 PPM.</p>\r\n<p><br />&#160;b) 'Option \"None\" is not supported anymore on authorization level':</p>\r\n<p>Depending on the displayed client logon to&#160;the client of the system mentioned. Display the project element&#160;related to the following Object types DPO (Project), PPO (Phase), TTO (Task), CTO (Checklist), ITO (Checklist Item), RAG (Checklist reference) by navigating to the work center Project Management. Choose Service -&gt; Search.&#160;On the appearing search popup choose \"Search for:\" according to the Object type listed. Fill the listed EXTERNAL_ID (Number) into the Search Criteria field Project Number/Phase Number/Number&#160;and click \"Find button\". Click on the search result to display the project element detail screen. Navigate to the tabs</p>\r\n<p>\"Authorizations\" -&gt; \"Users\"<br />\"Authorizations\" -&gt; \"User Groups\"<br />\"Authorizations\" -&gt; \"Organizational Units\"<br />\"Authorizations\" -&gt; \"Roles\"</p>\r\n<p>Be aware that the functionality maintained&#160;by checking the \"None\" flag&#160;is not&#160;supported in S/4 PPM anymore. Versions of a project are not listed in detail in the report protocol. In case versions to a project exist the protocol may&#160;contain duplicate entries related to a project number.</p>\r\n<p>The impact of the new concept (without 'no Auth') is that 'strictly confidential' documents after changing the authorizations in a project can now be accessed by users that should not have access according <br />to business reasons. Be aware of that and take that into account when changing authorizations as result of pre-check or the authorization analysis report results.</p>\r\n<p><br />&#160;c) 'Object Type-Related Activities for Authorization Check contain changes in fix activity hierarchy Admin -&gt; Write -&gt; Read'</p>\r\n<p>Depending on the displayed client logon to&#160;the client of the system mentioned. Call transaction ACO3 to display \"Object Type-Related Activities for Authorization Check\".&#160;Remember the content of column OBJECT_TYPE listed in the result of the pre-check report and click on the button \"Position ...\" of the view. Enter&#160;the OBJECT_TYPE into the field Object Category and confirm the value. Be aware that the Object Type-Related Activities for Authorization Check have a fixed activity hierarchy Admin -&gt; Write -&gt; Read'. This restiction is valid for the fiori UI's. There are no&#160;restrictions&#160;regarding the&#160;Web dynpro UI.</p>\r\n<p><br />&#160;d) 'Hierarchies of User Groups to User Group for Authorization are only allowed up to a level of 3':</p>\r\n<p>Depending on the displayed client logon to&#160;the client of the system mentioned. Navigate to the work center \"Portfolio and Project Administration\" and choose \"Project User Groups\". Click button \"Open\" and enter the displayed User Group Name listed in the result of the report into the field \"Name\". Be aware that the User Group Hierarchy is only evaluated up to a level of 3.</p>\r\n<p>In order to maintain the project user groups call transaction /NNWBC and navigate to workcenter Portfolio and Project Administration -&gt; Project User Groups</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027589)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D065365)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002445069/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002445069/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002445069/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002445069/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002445069/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002445069/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002445069/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002445069/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002445069/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2399707", "RefComponent": "CA-TRS-PRCK", "RefTitle": "Simplification Item Check", "RefUrl": "/notes/2399707"}, {"RefNumber": "2368899", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - Deletion of obsolete packages", "RefUrl": "/notes/2368899"}, {"RefNumber": "2361657", "RefComponent": "PPM-PRO", "RefTitle": "Moving from PPM Releases to SAP Portfolio and Project Management 1.0 for SAP S/4HANA", "RefUrl": "/notes/2361657"}, {"RefNumber": "2359662", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - Global Settings in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2359662"}, {"RefNumber": "2358463", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - Microsoft Project Import/Export in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2358463"}, {"RefNumber": "2358376", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - Web Dynpro Applications for Resource Maintenance in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2358376"}, {"RefNumber": "2353984", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - HTTP-Based Document Management in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2353984"}, {"RefNumber": "2348430", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - cFolders not available anymore in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2348430"}, {"RefNumber": "2343374", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - Simplified ACLs (Access Control Lists) concept in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2343374"}, {"RefNumber": "2340075", "RefComponent": "PPM-PRO", "RefTitle": "FAQs - SAP Portfolio and Project Management 1.0 for SAP S/4HANA", "RefUrl": "/notes/2340075"}, {"RefNumber": "2321885", "RefComponent": "PPM-PRO", "RefTitle": "S4TC CPRXRPM Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2321885"}, {"RefNumber": "2182725", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2182725"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2348430", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - cFolders not available anymore in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2348430 "}, {"RefNumber": "2359354", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - SRM Integration in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2359354 "}, {"RefNumber": "2353885", "RefComponent": "PPM-PRO", "RefTitle": "S4TWL - Control Plans in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2353885 "}, {"RefNumber": "2354985", "RefComponent": "PPM-PFM", "RefTitle": "S4TWL - KM Documents in SAP Portfolio and Project Management for SAP S/4HANA", "RefUrl": "/notes/2354985 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CPRXRPM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "CPRXRPM", "From": "450_700", "To": "450_700", "Subsequent": ""}, {"SoftwareComponent": "CPRXRPM", "From": "500_702", "To": "500_702", "Subsequent": ""}, {"SoftwareComponent": "CPRXRPM", "From": "600_740", "To": "600_740", "Subsequent": ""}, {"SoftwareComponent": "CPRXRPM", "From": "610_740", "To": "610_740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "CPRXRPM", "NumberOfCorrin": 5, "URL": "/corrins/0002445069/381"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}