{"Request": {"Number": "322580", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 235, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014878402017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000322580?language=E&token=C16F660448992E21B50A86CE410BED9D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000322580", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000322580/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "322580"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.02.2002"}, "SAPComponentKey": {"_label": "Component", "value": "TR-CB-IS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Information System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Treasury", "value": "TR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cash Budget Management", "value": "TR-CB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TR-CB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Information System", "value": "TR-CB-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TR-CB-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "322580 - Hierarchy in standard reports"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are using release 4.6 cash budget management module. The old functionality that the whole commitment-item hierarchy is displayed when you execute standard reports (FMR1 to FMR4, FMR5a, and fmr6a) is no longer visible, you only see one level of the hierarchy. The commitment items are only grouped by Open Balances/Revenues/Expenses/Closing Balances. Also you cannot drill down to line items from a balance commitment item.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>fmr1, fmr2, fmr3, fmr4, fmr5a (fmr6a for 4.6C).</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>ABAP List Viewer (ALV) tool cannot handle more than two-level hierarchy.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In release 46B, the following parts of note 195995 have to be implemented manually, prior to importing the transport given below:</p> <UL><LI>Extension of structure FMMP by GEBER field.</LI></UL> <UL><LI>Add the select-option S_GEBER in the selection screen of the logical data base C1F (Include DBC1FSEL).</LI></UL> <p>The different levels of the hierarchy are incorporated in the standard reports. They are displayed in different colors as the 'open/close' option cannot be achieved. It is also possible to drill down to line items from a balance commitment item.<br />If you want the whole hierarchy of your commitment items to be displayed when you run a CBM standard report, please download the Reporting development classe FFCBR which contains the standard reports and the line-item report RFFMEP1M from SAPSERV3 (transport ALRK284086 in directory \\general\\R3server\\abap\\note.0322580). Please refer to the attached note 13719 which explains how to import the request.<br />After you have successfully transported the programs, please implement note 400321.<br />The transport does not contain all the text element. It may be necessary to get the missing selection texts from DDIC.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "TR-CB (Cash Budget Management)"}, {"Key": "Operating system", "Value": "AIX 4.3.1"}, {"Key": "Database System", "Value": "ORACLE 7.3"}, {"Key": "Transaction codes", "Value": "CASH"}, {"Key": "Transaction codes", "Value": "FMR4"}, {"Key": "Transaction codes", "Value": "FMR2"}, {"Key": "Transaction codes", "Value": "FMR3"}, {"Key": "Transaction codes", "Value": "FMR1"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D026221)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D026221)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000322580/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000322580/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000322580/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000322580/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000322580/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000322580/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000322580/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000322580/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000322580/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "694126", "RefComponent": "TR-CB-IS", "RefTitle": "FMR5A and FMR6A lose colors after refresh", "RefUrl": "/notes/694126"}, {"RefNumber": "682047", "RefComponent": "TR-CB-IS", "RefTitle": "Sorting in standard reports", "RefUrl": "/notes/682047"}, {"RefNumber": "421320", "RefComponent": "TR-CB-IS", "RefTitle": "Standard reporting damaged", "RefUrl": "/notes/421320"}, {"RefNumber": "400321", "RefComponent": "TR-CB-IS", "RefTitle": "Transaction fmr2: amounts are not properly formated", "RefUrl": "/notes/400321"}, {"RefNumber": "322506", "RefComponent": "TR-CB-IS", "RefTitle": "Drilldown to line items for balance", "RefUrl": "/notes/322506"}, {"RefNumber": "195995", "RefComponent": "TR-CB", "RefTitle": "Organizational dimension in cash budget management", "RefUrl": "/notes/195995"}, {"RefNumber": "191821", "RefComponent": "TR-CB", "RefTitle": "Cash Budget Management in Release 4.6", "RefUrl": "/notes/191821"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "195995", "RefComponent": "TR-CB", "RefTitle": "Organizational dimension in cash budget management", "RefUrl": "/notes/195995 "}, {"RefNumber": "421320", "RefComponent": "TR-CB-IS", "RefTitle": "Standard reporting damaged", "RefUrl": "/notes/421320 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "694126", "RefComponent": "TR-CB-IS", "RefTitle": "FMR5A and FMR6A lose colors after refresh", "RefUrl": "/notes/694126 "}, {"RefNumber": "682047", "RefComponent": "TR-CB-IS", "RefTitle": "Sorting in standard reports", "RefUrl": "/notes/682047 "}, {"RefNumber": "191821", "RefComponent": "TR-CB", "RefTitle": "Cash Budget Management in Release 4.6", "RefUrl": "/notes/191821 "}, {"RefNumber": "400321", "RefComponent": "TR-CB-IS", "RefTitle": "Transaction fmr2: amounts are not properly formated", "RefUrl": "/notes/400321 "}, {"RefNumber": "322506", "RefComponent": "TR-CB-IS", "RefTitle": "Drilldown to line items for balance", "RefUrl": "/notes/322506 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}