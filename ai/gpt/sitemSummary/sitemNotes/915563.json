{"Request": {"Number": "915563", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 4437, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016303282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000915563?language=E&token=93EFBF445703CA0A3F3D40702C506BF8"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000915563", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000915563/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "915563"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 32}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade Info"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.02.2022"}, "SAPComponentKey": {"_label": "Component", "value": "CA-CAD-LIB-INV"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP PLM Integration for Inventor"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CAD Integration", "value": "CA-CAD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-CAD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CAD Function Libraries", "value": "CA-CAD-LIB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-CAD-LIB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP PLM Integration for Inventor", "value": "CA-CAD-LIB-INV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-CAD-LIB-INV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "915563 - SAP PLM Interface to Inventor, CAD desktop - patches"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=915563&TargetLanguage=EN&Component=CA-CAD-LIB-INV&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/915563/D\" target=\"_blank\">/notes/915563/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note provides patches for the SAP PLM interface to Inventor.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<p>SAP PLM, SAP PLM Interface to Inventor, SAP PLM Interface to AutoCAD, SAP PLM Interface to MicroStation, SAP PLM Interface to Solid Edge, SAP PLM Interface to SolidWorks, CDESK, CDESK_CUS, Customizing, CAD Desktop, CAD Desktop, Inventor, AutoCAD, Vanilla, Mechanical, MicroStation, SolidWorks, Solid Edge, ACAD, ACADM, INV, Autodesk, Bentley, SAP Product Lifecycle, CAD, Siemens</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<p>The SAP PLM Interface to AutoCAD in Version 6.1 is a prerequisite for this SAP Note.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3>\r\n<p>This SAP Note lists changes to existing functions, new functions, and corrected errors in the current release or Support Package.<br />This SAP Note is updated for each delivery of a new version.<br /><br /></p>\r\n<p><strong>Download the software from the SAP ONE Support Launchpad:</strong></p>\r\n<p>URL: <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/softwarecenter\">https://launchpad.support.sap.com/#/softwarecenter</a> (login required)</p>\r\n<p>Starting from the central navigation, select the following entries one after the other:</p>\r\n<p style=\"padding-left: 30px;\">-> Support Packages and Patches<br />-> By Alphabetical Index (A-Z)<br />-> Letter P (CAD desktop interfaces)<br />-> SAP PLM INTEGRATIONS<br />-> SAP PLM INTERFACE TO INVENTOR<br />-> PLM INTEGRATION INVENTOR / SAP PLM I. TO INVENTOR CD 6.1 / SAP PLM I. TO INVENTOR CD 6.1</p>\r\n<p><strong>Important:</strong> </p>\r\n<p><strong>This SAP Note is no longer updated. Please refer to SAP Knowledge Base Article <a target=\"_blank\" href=\"/notes/3142729\">3142729</a>.</strong></p>\r\n<p>The rapid-deployment package (SAP software component CIDCAD) contains proposals for the implementation of various functions and should be imported into the SAP system and activated.</p>\r\n<p>Rapid Deployment Package (RDP) - <a target=\"_blank\" href=\"/notes/1781990\">1781990</a></p>\r\n<p>If enhancements are activated in RDP, existing implementations with the same function must be deactivated. If these manual implementations are not deactivated, problems may occur with regard to BAdI implementations that run several times.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-CAD (CAD Integration)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (C5062008)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5062009)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000915563/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "SAPPLMINV_VERSINFO_DE.pdf", "FileSize": "1192", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000047332006&iv_version=0032&iv_guid=00109B36BC6E1EDBBAC45E1EF142A0EA"}, {"FileName": "SAPPLMINV_VERSINFO_EN.pdf", "FileSize": "1213", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000047332006&iv_version=0032&iv_guid=00109B36D5921EDBBAC45EE13989E0E6"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "919774", "RefComponent": "CA-CAD-LIB-ACD", "RefTitle": "SAP PLM Interface to AutoCAD, CAD Desktop - Patches", "RefUrl": "/notes/919774"}, {"RefNumber": "629480", "RefComponent": "CA-CAD", "RefTitle": "CDESK: Documentation (Incl. Customizing) for CAD Desktop 2.x", "RefUrl": "/notes/629480"}, {"RefNumber": "606491", "RefComponent": "CA-CAD", "RefTitle": "CDESK: CAD Desktop 2.x Availability and Prerequisites", "RefUrl": "/notes/606491"}, {"RefNumber": "1938579", "RefComponent": "CA-CAD-LIB-ACD", "RefTitle": "Updating SAP software components related to SAP PLM UI to Inventor, AutoCAD, and so on", "RefUrl": "/notes/1938579"}, {"RefNumber": "1781990", "RefComponent": "CA-CAD-LIB-WKS", "RefTitle": "Rapid deployment package for CAD integrations", "RefUrl": "/notes/1781990"}, {"RefNumber": "1493137", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1493137"}, {"RefNumber": "1488041", "RefComponent": "CA-CAD-LIB-EDG", "RefTitle": "SAP PLM Interface to Solid Edge, CAD desktop - patches", "RefUrl": "/notes/1488041"}, {"RefNumber": "1488039", "RefComponent": "CA-CAD-LIB-WKS", "RefTitle": "SAP PLM Interface to SOLIDWORKS, CAD Desktop - Patches", "RefUrl": "/notes/1488039"}, {"RefNumber": "1156829", "RefComponent": "CA-CAD-LIB", "RefTitle": "Important SAP Notes for the SAP PLM Interface", "RefUrl": "/notes/1156829"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "3145061", "RefComponent": "CA-CAD-LIB-ACD", "RefTitle": "Update of SAP software components related to SAP PLM Interface to Inventor, AutoCAD, and so on.", "RefUrl": "/notes/3145061 "}, {"RefNumber": "3142729", "RefComponent": "CA-CAD-LIB-INV", "RefTitle": "SAP PLM Interface to Inventor, CAD desktop - patch information", "RefUrl": "/notes/3142729 "}, {"RefNumber": "2648617", "RefComponent": "PLM-ECC", "RefTitle": "ECTR: Upgrade of SAP PLM Interfaces for AutoCAD, Inventor, Solid Edge, SOLIDWORKS", "RefUrl": "/notes/2648617 "}, {"RefNumber": "751145", "RefComponent": "CA-CAD-LIB", "RefTitle": "Support process for CIDEON Software &amp; Services GmbH &amp; Co. KG Products resold by SAP", "RefUrl": "/notes/751145 "}, {"RefNumber": "1488039", "RefComponent": "CA-CAD-LIB-WKS", "RefTitle": "SAP PLM Interface to SOLIDWORKS, CAD Desktop - Patches", "RefUrl": "/notes/1488039 "}, {"RefNumber": "1488041", "RefComponent": "CA-CAD-LIB-EDG", "RefTitle": "SAP PLM Interface to Solid Edge, CAD desktop - patches", "RefUrl": "/notes/1488041 "}, {"RefNumber": "919774", "RefComponent": "CA-CAD-LIB-ACD", "RefTitle": "SAP PLM Interface to AutoCAD, CAD Desktop - Patches", "RefUrl": "/notes/919774 "}, {"RefNumber": "1156829", "RefComponent": "CA-CAD-LIB", "RefTitle": "Important SAP Notes for the SAP PLM Interface", "RefUrl": "/notes/1156829 "}, {"RefNumber": "629480", "RefComponent": "CA-CAD", "RefTitle": "CDESK: Documentation (Incl. Customizing) for CAD Desktop 2.x", "RefUrl": "/notes/629480 "}, {"RefNumber": "606491", "RefComponent": "CA-CAD", "RefTitle": "CDESK: CAD Desktop 2.x Availability and Prerequisites", "RefUrl": "/notes/606491 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=915563&TargetLanguage=EN&Component=CA-CAD-LIB-INV&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/915563/D\" target=\"_blank\">/notes/915563/D</a>."}}}}