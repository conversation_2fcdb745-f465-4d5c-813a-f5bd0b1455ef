{"Request": {"Number": "987150", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 650, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016166902017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000987150?language=E&token=BCA0F01D625133D74165EAFFE5A4A83B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000987150", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000987150/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "987150"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.04.2016"}, "SAPComponentKey": {"_label": "Component", "value": "IS-A-GR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Goods Receipt Process"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Automotive", "value": "IS-A", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Goods Receipt Process", "value": "IS-A-GR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-A-GR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "987150 - ERP 2005 Automotive Upgrade Info reg. User Exits / BAdIs"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />You are using SAP for Automotive regarding Goods Receipt Processing based on inbound deliveries and you are planning to upgrade to ERP 2005.<br /><br />If you are additionally using User Exits and BAdIs available before ERP 2005 it is urgently recommended to study that note carefully regarding the replacement of User Exits with ERP 2005.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br />BORGR, BORES, User Exit, BAdI, EXIT_SAPMBORGR_001, USER_EXIT_004, USER_EXIT_011, USER_EXIT_012, USER_EXIT_013, BORES_IDOC_INPUT_DESADV1, BORES_IDOC_INPUT_SHIPMENT, DESADV, SHPMNT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br />To integrate the inbound delivery processing of Service Parts Management with the Industry Solution for Automotive, the program structure of inbound delivery processing was redesigned with ERP 2005.<br /><br />Before ERP 2005, the Industry Solution for Automotive provided a framework for UI (User Interface) - transaction BORGR and the pre-configured transactions BORGR_V, BORGR_B, BORGR_C - and a framework for IDOC processing. Both frameworks were independently from each other in completing the delivery data entered, validating the data and in collecting messages during processing. Both frameworks met only by using the same core function modules for database updates.<br /><br />With ERP 2005, a layer called ID_HANDLING (Inbound Delivery Handling) is integrated between UI, IDOC processing or BAPI interface on the one hand and the core function modules for database update on the other hand.<br /><br />The ID_HANDLING provides the preparation and fulfillment of all actions on the inbound delivery provided for UI, IDOC processing including the enrichment of delivery data, the validation of delivery data, the message collection and the dispatching of the delivery data to other systems. The ID_HANDLING thus takes over the main functionality of the former UI or IDOC processing frameworks of the Industry Solution for Automotive.<br /><br />The ID_HANDLING layer as a single point of entry ensures an equal treatment of inbound delivery data independent from the data entry.<br /><br />Compatible to the new ID_HANDLING layer, the UI and the IDOC processing framework was redesigned with impact on customer enhancements. The current note provides information about</p>\r\n<ul>\r\n<li>which User Exits and BAdIs (Business Add-Ins) available with SAP for Automotive before ERP 2005 with the UI framework and IDOC processing framework are not available any more for Automotive customers</li>\r\n</ul>\r\n<ul>\r\n<li>which BAdIs available with SAP for Automotive in ERP 2005 should be used instead of User Exits and BAdIs not longer available.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong>User Exits and Business Add-Ins (BAdIs) for UI (transaction BORGR)</strong><br /><br />The following user exits and BAdIs are available with SAP for Automotive before ERP 2005 by the configurable transaction BORGR or the pre-configured transaction BORGR_V, BORGR_B or BORGR_C.<br /><br />1. User Exit EXIT_SAPMBORGR_001 (function group XBORGR)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;As the purpose of that User Exit is the same as of the Business Add-In (BAdI) BORGR_DIALOG, that BAdI should be used instead with SAP for Automotive in ERP 2005.<br /><br />2. User Exit EXIT_SAPMBORGR_002 (function group XBORGR)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;That User Exit exists but is not used with SAP for Automotive before ERP 2005. Therefore it is not necessary to replace it.<br /><br />3. Business Add-In BORGR_DIALOG<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The Business Add-In (BAdI) BORGR_DIALOG allows you to use your own classes instead of standard classes in the User Interface of Goods Receipt Processing for Automotive. The User Interface could be adjusted to a great extent by means of inheritance that is without modifying the program.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The BAdI could still be used for that purpose with SAP for Automotive ERP 2005.<br /><br />4. Business Add-In BORGR_POD_DETERMIN<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The Business Add-In (BAdI) BORGR_POD_DETERMIN allows you to influence the purchase order determination in the user interface of Goods Receipt Processing for Automotive or in IDOC processing.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The BAdI could still be used for that purpose with SAP for Automotive in ERP 2005.<br /><br />5. Business Add-In BORGR_REGISTRATION<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The Business Add-In (BAdI) is intended for triggering actions or events when inbound deliveries are registered, setting the delivery status \"In Plant\" (or \"In Yard\").<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The BAdI could still be used for that purpose with SAP for Automotive in ERP 2005.<br /><br /><strong><strong>User Exits and Business Add-Ins (BAdIs) for IDOC processing</strong></strong><br /><br />With SAP for Automotive before ERP 2005, the IDOC processing of inbound deliveries is executed by function module BORES_IDOC_INPUT_DESADV1 of function group BORES.<br /><br />With SAP for Automotive in ERP 2005, the IDOC processing of inbound deliveries is executed by new function module /SPE/IDOC_INPUT_DESADV1.<br /><br />As the same subroutines of function group BORES are used also for IDOC processing of inbound shipments if inbound deliveries to be processed are included in the shipment IDOC data, the new function module /SPE/IDOC_INPUT_DESADV1 is also used for processing of inbound delivery data included in shipment IDOC data.<br /><br />The following user exits are used with SAP for Automotive before ERP 2005 by function module BORES_IDOC_INPUT_DESADV1.<br /><br />1. User Exit USER_EXIT_004 (program SAPLV55K)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;That User Exit allows you to cancel the IDOC processing or to manipulate the IDOC data directly before the parsing of the IDOC data.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The User Exit \"004\" is not available for IDOC processing with SAP for Automotive in ERP 2005.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Instead, method PROCESS_IDOC_DELVRY03 of BAdI /SPE/BADI_IBDLV_CONTROL in class /SPE/CL_IBDLV_CONTROL should be used. See more details in SAP Note 1045312.<br /><br />2. User Exit USER_EXIT_011 (program SAPLV55K)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;That User Exit is called directly after parsing each IDOC data segment to the application-related internal tables. The data of the internal tables could be manipulated by using that User Exit.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The User Exit \"011\" is not available for IDOC processing with SAP for Automotive in ERP 2005.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Instead, method PARSING_IDOC of BAdI /SPE/INB_ID_HANDLING should be used.<br /><br />3. User Exit USER_EXIT_012 (program SAPLV55K)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;That User Exit is called directly after the determination of the default delivery type and allows the overall manipulation of the parsed delivery data, including the special case of manipulating the delivery type.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The User Exit \"012\" is not available for IDOC processing with SAP for Automotive in ERP 2005.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The existing methods ENRICH_HEAD and ENRICH of BAdI /SPE/INB_ID_HANDLING could be used instead for IDOC related manipulation of the delivery creation. The method ENRICH_ITEM is only reached for dialog processing (BORGR/VL60).<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;All these BAdI methods allow the manipulation of the delivery data after the SAP standard enrichment has been performed.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;These BAdI methods should allow the manipulation of the delivery data with the same result as with the former User Exit \"012\".<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Additional BAdI methods called before the SAP standard enrichment of delivery data is performed will be available soon and stated in that note as well. Please check on future updates of the current note.<br /><br />4. User Exit USER_EXIT_013 (program SAPLV55K)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;That User Exit is called after processing the function module GN_DELIVERY_CREATE to create the inbound delivery and allows additional actions together with the inbound delivery creation (e.g. update on additional tables) or the manipulation of the processing status or status messages of the IDOC.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The User Exit \"013\" is not available for IDOC processing with SAP for Automotive in ERP 2005.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The method SAVE of BAdI /SPE/INB_ID_HANDLING could be used instead for additional actions (e.g. update on additional tables) based on the delivery creation or update.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;An additional BAdI method called after parsing and handling of the inbound delivery for manipulation of the IDOC status and IDOC status messages will be available after you apply the note <a target=\"_blank\" class=\"th-lk\" href=\"/notes/0002126235\" id=\"C46_W142_V143_solutions_table[1].numm\" title=\"2126235\">2126235&#160;</a>.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-A (Automotive)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023655)"}, {"Key": "Processor                                                                                           ", "Value": "D002243"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000987150/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000987150/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000987150/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000987150/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000987150/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000987150/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000987150/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000987150/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000987150/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "989611", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound control framework switch redesign", "RefUrl": "/notes/989611"}, {"RefNumber": "865969", "RefComponent": "IS-ADEC", "RefTitle": "Current release restrictions for ERP 2005 - ECC-DIMP", "RefUrl": "/notes/865969"}, {"RefNumber": "2126235", "RefComponent": "LO-SPM-INB", "RefTitle": "IDoc - BAdI idoc_status does not work in function module /SPE/IDOC_INPUT_DESADV1", "RefUrl": "/notes/2126235"}, {"RefNumber": "1333354", "RefComponent": "LO-SPM-INB", "RefTitle": "BADI: Method PARSING_IDOC does not allow error to be issued", "RefUrl": "/notes/1333354"}, {"RefNumber": "1045312", "RefComponent": "LO-SPM-INB", "RefTitle": "New BAdI for user-exit EXIT_SAPLV55K_004", "RefUrl": "/notes/1045312"}, {"RefNumber": "1044725", "RefComponent": "LO-SPM-INB", "RefTitle": "IDOC's status 52 is missing in /SPE/IDOC_INPUT_DESADV1", "RefUrl": "/notes/1044725"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2404011", "RefComponent": "IS-A-GR", "RefTitle": "S4TWL - Change in default configuration of IDoc inbound processing with message type DESADV", "RefUrl": "/notes/2404011 "}, {"RefNumber": "865969", "RefComponent": "IS-ADEC", "RefTitle": "Current release restrictions for ERP 2005 - ECC-DIMP", "RefUrl": "/notes/865969 "}, {"RefNumber": "989611", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound control framework switch redesign", "RefUrl": "/notes/989611 "}, {"RefNumber": "1333354", "RefComponent": "LO-SPM-INB", "RefTitle": "BADI: Method PARSING_IDOC does not allow error to be issued", "RefUrl": "/notes/1333354 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ECC-DIMP", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}