{"Request": {"Number": "820183", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 384, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015853352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000820183?language=E&token=1DD88CD7686EC4F1FDC0DBDD45882813"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000820183", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000820183/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "820183"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.10.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-OLAP-AUT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Authorizations"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analyzing Data", "value": "BW-BEX-OT-OLAP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Authorizations", "value": "BW-BEX-OT-OLAP-AUT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-OLAP-AUT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "820183 - New authorization concept in BI"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Authorizations for data analysis<br /><br />There is a new authorization system for authorizing query data in<br />transaction LISTCUBE and the RSDRI modules for reading data in the BI system.</p> <OL>1. During an upgrade, problems may occur for users with SAP_ALL authorizations.</OL> <OL>2. \"No authorization\" messages (and variants of this message) are issued for users with limited authorizations for reporting.</OL> <OL>3. Allegedly, the new authorization concept requires S_RS_AUTH with authorization 0BI_ALL \"for all\".</OL> <OL>4. There is an entry in the log that implies a check is carried out for NOT IN A, although value A is authorized (or several values, intervals or selection options).</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Authorizations, reporting, analysis, query authorizations, insufficient authorization, 0BI_ALL, SU53, ST01<br />BRAIN 804, BRA<PERSON> 819, <PERSON><PERSON><PERSON> 816, <PERSON><PERSON><PERSON> 007, <PERSON><PERSON><PERSON> 016, <PERSON><PERSON><PERSON> 019<br /><PERSON>AIN804, BRAIN819, <PERSON>A<PERSON>816, <PERSON><PERSON><PERSON>007, <PERSON>YE016, EYE019, 0BI_ALL, 0TCAKYFNM, 0TCAIPROV, 0TCAACTVT, 0CTAVALID, LISTCUBE, NOT IN<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>- Upgrading to NetWeaver BI 2004s.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>See also the release notes for NetWeaver 2004s BI.<br />(Transaction SE61: Document RELN name: BW_BEX_70_AUTHORIZE)<br />Link:<br />(German) http://help.sap.com/saphelp_nw04s/helpdata/de/80/d71042f664e22ce10000000a1550b0/frameset.htm<br />(English)<br />http://help.sap.com/saphelp_nw04s/helpdata/en/80/d71042f664e22ce10000000a1550b0/frameset.htm<br /><br />The BI authorizations are no longer based on authorization objects; rather they are BI-specific objects.<br />In addition, a new authorization object called S_RS_AUTH was created. Its entries are reporting authorizations for a user.<br /><br />Users who have SAP_ALL authorization should be able to execute queries without authorization messages.&#x00A0;&#x00A0;For this purpose, an authorization is automatically added for the S_RS_AUTH object into which the *-authorization is inserted. This gives an SAP_ALL user all BI authorizations for reporting, analysis, and in particular, also for the 0BI_ALL authorization, which was artificially created and which permits all data accesses.</p> <OL>1. If \"No authorization\" messages are issued for SAP_ALL-users or users that should see all data, proceed as follows:<br />Execute the point OBI_ALL or function module RSEC_GENERATE_BI_ALL in transaction RSECADMIN under \"Extras\". The \"Regenerate SAP_ALL\" button in transaction SU21 can be used to update the SAP_ALL profile. (The sequence is irrelevant.)<br />In certain cases you then need to delete the buffers, you should therefore execute $sync if possible, which deletes ALL buffers.<br />Activating a characteristic has the same effect because in this case the function module is also executed automatically and the relevant buffers are updated.</OL> <OL>2. If problems occur with restricted users:<br />Queries for which you actually want restrictions no longer run unless you adjust the authorizations.<br />The BI authorizations for reporting and analysis include a special authorization called 0BI_ALL, which includes all authorization-relevant characteristics. This authorization is automatically updated when you activate any BI InfoObject. This authorization is also executed when you upgrade after you start transaction RSA1 using XPRA for the first time.<br />A user who has this authorization can therefore execute all queries. If you do not want to place any restrictions on data access, you can assign the 0BI_ALL authorization to the relevant users. There are a number of options for this, based on the explanations above:</OL> <OL><OL>a) Create a role with authorization object S_RS_AUTH and enter authorization * (asterisk) or 0BI_ALL and assign the role to all relevant users.<br />You can also adjust the standard roles and profiles in this case. The authorization object should be transported automatically in S_RS_ALL.<br />Check this or insert the S_RSEC and S_RS_AUTH authorization objects in the respective profile.</OL></OL> <p></p> <OL><OL>b) The following option is particularly useful if you have a small number of individual users: Use transaction RSU01 to assign 0BI_ALL to the users directly.</OL></OL> <p><br />In each case you must activate the characteristics 0TCAIPROV, 0TCAVALID and 0TCAACTVT and mark them as authorization-relevant.<br />(However, the system always checks these characteristics).<br />When you first call the Data Warehouse Workbench (transaction RSA1), these and other characteristics that should be automatically active are copied from the Content and activated. You must flag these characteristics as authorization-relevant manually. They are returned as not authorization-relevant, but you must mark them as authorization-relevant.</p> <OL>3. Miscellaneous:<br /></OL> <OL><OL>a) It is not correct that authorizations are only granted if the user has been assigned 0BI_ALL. People may think this is so because an irrelevant entry appears in transaction SU53 or transaction ST01 informing you of an unsuccessful check on S_RS_AUTH with the value 0BI_ALL.<br />This entry is of no significance.<br />Transaction SU53 (ST01) does not contain any entries that are relevant for analysis authorizations.<br />This entry merely means that the user has restricted authorizations, which may be as intended and is completely normal. This is as it should be and therefore the analysis authorization check does not deny access.<br /><B>The reason for an authorization message usually lies in the fact that S_RS_AUTH requires the authorization 0BI_ALL.</B><br />The entry is usually generated as a result of a technical optimization: The system checks if the user has 0BI_ALL to speed up processing. The quickest way to do this is using an ABAP command, which checks this authorization and then, unfortunately, leaves this entry in transaction SU53 (ST01).</OL></OL> <OL><OL>b) The 0TCAKYFNM characteristic must always be active. If it is not, the system issues an error message (characteristic 0TCAKYFNM does not exist in Version A. Check the authorizations).<br />If it is authorization-relevant, it is always checked. If you do not need authorization relevance, deselect the authorization relevance checkbox in InfoObject maintenance (transaction RSD1) on the Business Explorer tab page.<br /></OL></OL> <OL><OL>c) The 0INFOPROV characteristic is part of every query on a MultiProvider. If you do not want the system to check this characteristic, you should deactivate the authorization relevancy. The characteristic must always be active because the 0TCAIPROV characteristic references it.</OL></OL> <OL><OL>d) If there is a selection for NOT IN {A,B, C} in the authorization log, or something similar is checked, this means the following:<br />Everything is selected in the query without any restrictions. The authorization check takes each of the authorizations in turn and checks whether they are part of the selection. The remainder of the selection, that is, the part that is not yet authorized, is treated in the same way.<br />If you select '*' for whatever reason and values A,B and C are valid, these values are removed from the selection (intersection). The remainder, that is 'everything except A,B and C', is checked. This remainder, 'everything except A,B and C', is the same as the NOT IN {A,B,C} expression in SQL language. It therefore means all authorized values (intervals and selection options).<br />If you want to select just one authorized value for display purposes, you can usually use a variable on the characteristic which is filled from the authorizations. The query selection is then restricted to the authorized values.</OL></OL> <OL><OL>e) A semi-automatic migration tool is available. In urgent cases, you can also switch back to the old authorization concept. However, you must have a good reason for doing so. The old concept is still delivered with this release, but it will not be developed further. However, it is supplied by corrections for BW 3.0/3.1 systems and NetWeaver 2004 (BW 3.5) and it is regularly updated. For further information, see Note 923176.</OL></OL> <OL><OL>f) Special case: In the old authorization concept, you could use customer exit variables for the assignment of hierarchy authorizations in roles (and therefore in profiles). In the new concept of the BI analysis authorizations, the artificial 'Hierarchy authorization' objects are no longer available. Therefore, these can no longer be assigned by variables. Normally, however, not the whole hierarchy authorization is variable, rather only the hierarchy node. Therefore, in Release 7.0, you can use customer exit variables as before for this.</OL></OL> <p><br />Further information is available in the SAP Help Portal in the general documentation for authorizations:<br /><br />German documentation:<br />http://help.sap.com/saphelp_nw04s/helpdata/de/66/019441b8972e7be10000000a1550b0/frameset.htm<br /><br />English documentation<br />http://help.sap.com/saphelp_nw04s/helpdata/en/66/019441b8972e7be10000000a1550b0/frameset.htm<br /><br />For general information and information about the support situation in authorizations and during migration, This problem is caused by Note 923176.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-OT-OLAP (Analyzing Data)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041044)"}, {"Key": "Processor                                                                                           ", "Value": "D045945"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000820183/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000820183/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820183/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820183/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820183/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820183/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820183/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820183/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000820183/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990"}, {"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447"}, {"RefNumber": "1291204", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Loading with DTP: Failed authorization check on 0BI_ALL", "RefUrl": "/notes/1291204"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}, {"RefNumber": "1053989", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Intervals and Patterns [(*),(+)] in Analysis Authorizations", "RefUrl": "/notes/1053989"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2969185", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Keyfigure attributes not checked against analysis authorizations", "RefUrl": "/notes/2969185 "}, {"RefNumber": "2478384", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Reporting Authorizations", "RefUrl": "/notes/2478384 "}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "1053989", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Intervals and Patterns [(*),(+)] in Analysis Authorizations", "RefUrl": "/notes/1053989 "}, {"RefNumber": "1291204", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Loading with DTP: Failed authorization check on 0BI_ALL", "RefUrl": "/notes/1291204 "}, {"RefNumber": "955990", "RefComponent": "BW", "RefTitle": "BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "RefUrl": "/notes/955990 "}, {"RefNumber": "949447", "RefComponent": "CRM-BF", "RefTitle": "mySAP CRM 2005 SP Stack 05", "RefUrl": "/notes/949447 "}, {"RefNumber": "917772", "RefComponent": "SCM-BAS-TDL", "RefTitle": "TDL: Authorization concept", "RefUrl": "/notes/917772 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70009", "URL": "/supportpackage/SAPKW70009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}