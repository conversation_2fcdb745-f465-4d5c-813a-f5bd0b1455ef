{"Request": {"Number": "216952", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 419, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014825422017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000216952?language=E&token=522037BDF1C1843DD034F6522DE34290"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000216952", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000216952/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "216952"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 244}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.03.2017"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "216952 - Service Data Control Center (SDCC) - FAQ"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>For the planned execution of a service session you want to provide SAP with statistical data on the use of the R/3 System. For this purpose you want&#160;&#160;to use the Service Data Control Center<br />(Transaction SDCC)<br />Operating instructions for SDCC are available in the attached document.<br />For questions regarding SDCCN please refer to note 763561.<br />Typical problems with SDCC are described below.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RFC Download, EarlyWatch, GoingLive Check, Service, EarlyWatch Alert,<br />SDCC, Data Collection, Data Transfer, Solution Manager</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>While using the Service Data Control Center (SDCC) for preparation of a pending EarlyWatch, GoingLive check session, or similar, you have encountered&#160;&#160;problems</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>************************************************************************<br />Do not import any transport files containing SDCC into systems with R/3 Releases 6* and higher, and/or systems where ST-PI has already been implemented.<br />************************************************************************<br /><br />Below you will find a list of answers to question which are often raised in connection with the Service Data Control Center.</p>\r\n<ol><ol>1. Q: Which version of the Service Data Control Center should I use, SDCC or SDCCN?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: SDCCN is the newer version, this should be activated, especially in SAP Solution Manager systems where SDCCN controls the data collections for NON-ABAP satellite systems.</ol></ol><ol><ol>You can find information about SDCCN in SAP Note 763561. SDCC should only be used if it is not possible to activate SDCCN. SAP Note 792941 explains which formats have been used to deliver the Service Data Control Center for the different basis releases.</ol></ol><ol><ol>2. Basic use of SDCC</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: How does one download a session with SDCC?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: To use SDCC successfully it is important that the preparations described in note 91488 have been executed. the newest version of SDCC.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>a) Call Transaction SDCC. You may be prompted with two pop-ups which ask if you would like to run a Service Preparation Check and/or if you would like to schedule the Automatic Session Manager (see below).</ol></ol>\r\n<p><br /><br /><strong>SERVICE PREPARATION CHECK:</strong></p>\r\n<ol><ol>This will take you to RTCCTOOL; a tool which lists which notes may yet have to be implemented to obtain the best possible data collection (see note 69455). Skipping this step by hitting the 'No' button means that you can perform a download, but you can not be sure that all necessary notes have been implemented. You may find errors in the data collection message log which could have been avoided.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>ASM: This functionality should be scheduled as follows:</ol></ol>\r\n<p><br /><br /><strong>AUTOMATIC SESSION MANAGER (ASM)</strong><br /><br /></p>\r\n<ol><ol>The easiest method to use SDCC for service data collection and transfer is to use the Automatic Session Manager (ASM). The ASM is a periodic background job which you can trigger as follows:</ol></ol><ol><ol>I. Call Transaction SDCC.</ol></ol><ol><ol>II. In the menu, select 'Autom. Sess. Manager' -&gt; Controls.</ol></ol><ol><ol>III. Enter the start date and time. Please choose a time when the load on the system is low. Please avoid times when other jobs start (i.e choose 00:17:47 rather than 00:00:00). Enter also the ASM period.</ol></ol><ol><ol>Please run the ASM daily.</ol></ol><ol><ol>IV. Select the pushbutton 'Schedule the ASM'.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Use of the ASM is strongly recommended.</ol></ol>\r\n<p><br /><br /><strong>EARLYWATCH ALERT SERVICE.</strong></p>\r\n<ol><ol>The ASM should be active for this service to be supported sufficiently. Please refer to note 91488 and note 207223 for further details on Earlywatch Alert.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>a) A list display containing the session you would like to perform should appear after the first run of the ASM. If the session is not there, use 'Maintenance -&gt; Refresh -&gt; Session overview' to load it. If it still doesn't appear, check below for possible error sources. (questions 2 ff.).</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>b) If the session you plan to prepare has overall status 'green' it means that either the ASM or a member of the SAP Service Delivery Team have already completed the download for this session. If the status is not 'green' proceed by selecting the session in the list.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>c) Then click on the button ' Download'. In the pop-up that appears you can choose to either schedule the download immediately, or at a time of your choice. This should only be necessary if the ASM is not set up.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>d) If you have chosen to do the download immediately, a little red and white propeller icon appears while the download is running. You can follow it's progress in the main SDCC message log (Information -&gt; Display message log ) or the session message log (double click on the relevant 'paper' icon at the right hand side of the session overview).</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>e) After the download is complete, the propeller icon disappears and is replaced (after a screen refresh) by a status light for data collection, data transfer and overall status. Mostly the transfer status is 'green'.The data collection status is occasionally yellow or red. This does not mean that data collection on customer systems has failed altogether (see below for details). In most cases the majority of data has been collected and may well be sufficient to perform a session.</ol></ol><ol><ol>3.</ol></ol>\r\n<p><strong>SERVICE DEFINITION REFRESH</strong></p>\r\n<ol><ol>A regular service definition refresh from SAPNET R/3 Frontend (formerly OSS) is recommended for systems on basis release 4.x with ST-PI, and systems on basis release 6.x. You can refresh the service definitions by calling 'SDCC -&gt; Maintenance -&gt; Refresh -&gt;&#160;&#160;Service Definitions'.You should check in the main message log if&#160;&#160;this was done successfully.</ol></ol>\r\n<p><br /><br /><strong>Systems on basis release 4.x without ST-PI MUST not refresh the Service Definitions from SAPNET R/3 Frontend after April 2004, as this will cause a shortdump.</strong><br /><strong>Note 713674 must be implemented to prevent or repair this.</strong><br /><br /><strong>Also systems on basis release 3.x&#160;&#160;MUST not refresh the Service Definitions from SAPNET R/3 Frontend after April 2004, as this will cause a shortdump.SDCC then probably can not be used anymore.</strong><br /><strong>Note 712757 must be implemented to prevent or repair this.</strong><br /><br /><strong>Whenever in the following text a service definition refresh is recommended please check the above text for the appropriate action.</strong></p>\r\n<ol><ol>4. Session data have not arrived in the Solution Manager system</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: I have downloaded data from the satellite system without apparent errors, yet no download can be found in the Solution Manager.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please ensure that you are using the current version of SDCC, at least version 2.3 (see note 91488). Please ensure also that the destination which connects the satellite system to the Solution Manager system is set up according to the instructions in the Solution Manager Installation Guide. Please ensure also that the user in this destination has been given the necessary authorisations (see the Installation Guide as well as note 546283).</ol></ol><ol><ol>5. Q: I have collected data for a session, but when I drill down in the download in the Service Data Viewer I get the error message No logical functional names found for current select'.</ol></ol><ol><ol>Has data been collected?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: The Service Data Viewer in certain releases (see below) can&#160;&#160;not display download data if the server name contains an '_' (underscore). Please check the SDCC message log for this&#160;&#160;session. If there is no error message regarding the function module, the data was collected. In this case there should be no problem for the system were the session is performed. Another option is to rename the server, and collect data again when enough data has been accumulated under the new server name.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>ST-PI 003D_4* , Support package 30 (610) and&#160;&#160;20 (620 ) will contain the new Service Data Viewer 2.0, where this problem has been resolved.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>The problem can occur in Release 4.X Solution Tools Plug-In&#160;&#160;(Version A, B and C), Release 610 (Basis Support Package 23-29), Release 620 (Basis Support Package 8-19)</ol></ol><ol><ol>6. During a data collection an error message or a warning for a particular</ol></ol><ol><ol>function module appears in the SDCC session log.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: Where can I find more information about this?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: For more information about problems with function modules please refer to note 781680.</ol></ol><ol><ol>7. Q: You try to use SDCC&#160;&#160;but fail with a shortdump CONNE_ILLEGAL_TRANSPORT_VERS.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Systems on Basis Release 3.x and systems on Basis Release 4.x without ST-PI MUST NOT refresh their Service Definitions from SAPNET R/3 Frontend ( formerly OSS) anymore. If they do, the above shortdump occurs, SDCC can not be used anymore.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Systems on Basis Release 3.x :</ol></ol><ol><ol>Please advise the customer to implement note 712757</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Systems on Basis Release 4.x without ST-PI</ol></ol><ol><ol>Please advise the customer to implement note 713674</ol></ol><ol><ol>8. Shortdump CONNE_IMPORT_WRONG_COMP_LENG (Error when attempting to IMPORT object \"BDL_TABLE\" )</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: Either when trying to do a Service Definition Refresh (Maintenance -&gt; Refresh -&gt; Servive Definitions), when calling Transaction SDCC, or at the start of each ASM run I get a short dump.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please get the newest version of SDCC implemented, at least version 2.3. The service definitions should be&#160;&#160;maintained as described in Question 2 of this note, (SERVICE DEFINITION REFRESH)</ol></ol><ol><ol>9. Faulty initialization of SDCC for R/3 release 4.6D</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: When I run SDCC on a 4.6D system I get error messages like \"RFC dest. CSD : does not exist\" and plenty of type 'D' messages in the SDCC Message Log.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please implement the newest version of SDCC, at least version 2.3.</ol></ol><ol><ol>10. \"No valid service\" in SDCC Message Log</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: \"No valid service\" appears in SDCC Message Log during report BDLCOLL. Data collections fails because of this error.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: It is likely that the service definition tables are not filled. Please ensure that the newest version of SDCC is implemented, at least version 2.3. The service definitions should be&#160;&#160;maintained as described in Question 2 of this note, (SERVICE DEFINITION REFRESH)</ol></ol><ol><ol>11. No authorization to perform a 'Service Definition Refresh' (R/3 &gt;=4.6B)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: When I try to use 'Maintenance -&gt;Refresh -&gt; Service Definitions' I am prompted with the message ' You do not have customizing permission', and the refresh is not carried out.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please note that Service Definitions may only be refreshed on systems with basis release 4.x with ST-PI, and basis release 6.x. For all other systems please switch off the service definition refreshes as described in notes 712757 or 713674, as appropriate.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>For systems where service definition refreshes are allowed please ensure that your user has the profile 'S_SDCC_ADMIN'. Alternatively use transaction SE38 and call report BDLTRA (/BDL/TRA if SDCC has been implemented by ST-PI, see note 91488).</ol></ol><ol><ol>12. Error in RFC destination of SDCC (CSD_PETER) - 4.6B customers only</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: How can I fix the faulty RFC destination?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please implement the newest SDCC version, at least version 2.3. Before changing the SDCC version, please fix the destination as described in note 209203.</ol></ol><ol><ol>13. Problems with systems on Basis Rel 3.0D</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: On this system calling SDCC results in syntax errors being displayed.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please implement SDCC version 2.3, and maintain the Service definitions as described in note 712757.</ol></ol><ol><ol>14. No authorization to call SDCC (R/3 release &gt;= 4.6B)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: How can I get the missing authorization to call SDCC?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: If your user contains the profile SAP_ALL, you should have no problems. If you do not have SAP_ALL, please add S_SDCC_ADMIN into the profile list for your user.</ol></ol><ol><ol>15. Q: ASM schedules several SASM_&lt;sessionnr&gt;_COLL_TRANS&#160;&#160;jobs for</ol></ol><ol><ol>one session. The jobs interrupt each other, in the SDCC message log you find the error&#160;&#160;&gt; collect/transfer jobs for session already active</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: The background job AUTO_SESSION_MANAGER carries out multiple scheduling of sessions.&#160;&#160;The job does not query the complete time window asking whether a SASM_*_COLL_TRANS job already exists.</ol></ol><ol><ol>Please refer to the solution in note 624427.</ol></ol><ol><ol>16. RFC breakdown just after the ASM has started</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: In the SDCC message log the entry \" Maximale Anzahl von CPIC-Clients erreiched \" oder \" Maximum number of CPIC-clients reached \" appears. How can this be fixed?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: There can be several reasons for this:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>a) If it happens during the run of the Automatic Session Manager: The default starting time for the ASM was 22:00:00 for SDCC up to version 2.0. So, if the starting time is not changed before the ASM is started, it will attempt a Service Definition Refresh through an RFC call to OSS every day shortly after 22:00. If too many customers attempt such a call at the same time, there can be an overload on OSS. To avoid this, please delete the next ASM job, using 'Autom. Sess. Manager -&gt; Controls (leads you to sm37), and reschedule it at another time. Please choose a time when there is little load on the system.</ol></ol><ol><ol>NOTE: Service definitions should be only be refreshed from OSS where appropriate, as described in Question 2 of this note, (SERVICE DEFINITION REFRESH)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>b) Otherwise, the logon group for the RFC destination SAPOSS may not be used properly. To force the system to use it, go into 'SDCC-&gt;Maintenance-&gt;Customizing-&gt;General' and</ol></ol>\r\n<ul>\r\n<li>if the input box for 'Logon group' is empty, fill in 'EWA'.</li>\r\n</ul>\r\n<ul>\r\n<li>if the input box for 'Logon group' already contains 'EWA', and the box for 'RFC destination' contains SAPOSS, replace SAPOSS with SAPNET_RFC.</li>\r\n</ul>\r\n<ol><ol>17. RFC destination SAPOSS works, it's copy SAPNET_RFC does not work</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: I get connection errors in SM59 RFC destination SAPNET_RFC. Therefore data transfer to SAP or service definition refreshes fail.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please note that only systems on basis release 4.x with ST-PI and systems on basis release 3.x&#160;&#160;may refresh service definitions from SAPNET R/3 Frontend (OSS). For all other systems please check Question 2 of this note, (SERVICE DEFINITION REFRESH)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>SAPNET_RFC is a copy of SAPOSS which uses load balancing through logon group EWA. It is created automatically by SDCC. If, however, SAPOSS is changed ( for example, if the 'Message Server' string there is changed, or because the entries 'Technical parameters' in transaction OSS1 were updated) it is necessary to adjust SAPNET_RFC. You can do this by simply deleting SAPNET_RFC. A fresh copy of SAPOSS will be created when SDCC tries to connect to SAP. If this does not work, and you get an error of type \"hostname ... unknown/CPI-C error CM_PRODUCT_SPECIFIC_ERROR\" you should refer to note 316221 for advice.</ol></ol><ol><ol>18. Submitting the data collection or transfer background job fails</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: When going through the standard procedure of collecting or transmitting data, an error message 'error during JOB_CLOSE, see SYSLOG' appears, and no job is submitted.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Both the collection of session data and its transfer to the SAP Service System are done through class C background work processes. Usually the customer system looks for any free background work process on any server and starts the collections and/or transfer jobs there. Problems can occur when:</ol></ol>\r\n<ul>\r\n<li>the 'Background server' parameter in 'Maintenance -&gt; Remote Environment -&gt; Default destinations' is not empty but set to a server that doesn't exist anymore, contains no background work processes, whose background work processes have been removed, or whose background work processes do not permit class C background jobs.</li>\r\n</ul>\r\n<ul>\r\n<li>on the entire system all background work processes are in use.</li>\r\n</ul>\r\n<ul>\r\n<li>the user under which the background work process is scheduled does not have authorization to do so (the user EARLYWATCH can have this problem). If the background work processes you tried to start (Session -&gt; Display -&gt; Coll./trans. jobs) are in status 'SCHEDULED' and you try to release them, you'll get an error message.</li>\r\n</ul>\r\n<ol><ol>19. Run of AUTO_SESSION_MANAGER failed ( in R/3 rel 4.*)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: During run of ASM following error message is shown in job log</ol></ol><ol><ol>(SM37): \"Variant &amp;************* does not exist\"</ol></ol><ol><ol>A: Go to transaction sm37, select job AUTO_SESSION_MANAGER. Select the next future job ( there should be only one). Go into change mode (Job -&gt; Change). Then press button 'Steps'. Select program name (BDLATRUN or /BDL/ATRUN with ST-PI) and go to Change mode. Change field entry \"Parameters\" from&#160;&#160;&amp;************* to 'Space' and save. Subsequent ASM jobs should be executed without the paramater, and run through.</ol></ol><ol><ol>20. Oracle error 1039 and/or 942 appear in the SYSLOG during execution of a SDCC download</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: While the SDCC report BDLCOLL is running Oracle error 1039 and/or 942 appears in the SYSLOG (Datenbankfehler 1039, Datenbankfehler 942). What should be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: These errors occur when the function module SQL_CACHE_ANALYSIS_READ is called. This module calls a kernel C routine which make EXPLAINs on SQL statements. The kernel routine returns the error codes.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>This SYSLOG entry causes no disruption for the Service. It also causes no short-dumps. Therefore it can be ignored. PLease see also note 200463.</ol></ol><ol>.</ol><ol><ol>21. Data collection gets stuck</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: What should I do when a data collection is going on for a long time (hours), or simply stops without the 'Data collection completed' entry in the SDCC Message Log?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Data collection can take very long when, for example, the system consists of many servers, or the system performance is poor. However, it is possible that some of the function modules called by SDCC lead to errors, very long loop times etc. These errors can occur in a variety of circumstances which need to be examined on a case by case basis. They often indicate some general problems in the system, or in some of the function modules called. If they are not contained in the scenarios outlined below you should seek specialist advice by opening a message in component SV-SMG-SDD.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>a)SDCC batch jobs fails. Typically the SYSLOG (SM21) contains an error message of type</ol></ol><ol><ol>\"F61 K TemSe input/output to unopened file\".</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>In SM37 (SDCC -&gt; Session -&gt; Display -&gt; Coll./trans.) I get the error \"Error occurred when reading job log JOBLGX...X\"&#160;&#160;when I try to look at the job log.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Please refer to Note 21661. Further information is, if required, in Note 48400. If the problem is a full file system, please refer to Note 16513.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>b) If the system has R/3 release 3.0D have a look in transaction ST22. Look for short dumps 'CALL_FUNCTION_PARM_MISSING' for the user who is running SDCC. If the error analysis shows that the error occured while calling function 'DDIF_NAMETAB_GET', parameter \"X031L_TAB\", the situation is as follows:</ol></ol><ol><ol>The version of the DDIC function module 'DDIF_NAMETAB_GET' is not up to date.&#160;&#160;For old R/3 releases in function 'DDIF_NAMETAB_GET' (it is used to get DDIC information) the 'optional' flag is not set for tables parameter \"X031L_TAB\".</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Solution:&#160;&#160;Set the tables parameter \"X031L_TAB\" of function 'DDIF_NAMETAB_GET' optional and generate the function. After that&#160;&#160;you can run transaction SDCC as normal.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>c) For AS400 systems data collection stops, and in the SYSLOG (SM21) an entry appears \"DB error -901 at DEL access to ...\".</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Solution: A DB patch is required. The customer should seek specialist AS400 advice through a message in component BC-DB-DB4.</ol></ol><ol><ol>22. RFC destination not found in SDCC</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: When trying to transfer data in SDCC the following messages appear.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&#160;&#160; -&#160;&#160;No data transfer possible: destination info not found</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&#160;&#160; -&#160;&#160;No RFC destination found: transfer not possible</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&#160;&#160;in the SDCC Message Log and the data are not transferred.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please check if the RFC destination has been accepted by SDCC.</ol></ol><ol><ol>Highlight the session, then do 'Maintenance -&gt; Remote Environment -&gt; Custom target dest.' If no target destination is implemented, you can do it in this screen for this single session.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>If you need a general solution for future sessions please do 'Maintenance-&gt; Remote environment -&gt; Default target destination'. Here you have to change the current destinations to any different destination and save. After this you have to reimplement the original destination once again. Please check again if the RFC destination has now been accepted ( check as described above). If this does not work, please open a message in component SV-SMG-SDD.</ol></ol><ol><ol>23. RFC errors</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: I tried to refresh the 'Service definitions' and/or the 'Session overview' but I am prompted with errors 'Session update via RFC failed' (SD-140) and/or 'Service definition update via RFC failed' (SD-141).</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>or</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Data collection was successful, but data transfer failed (RFC problems)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>NOTE: Service definitions should be only be refreshed from OSS where appropriate, as described in Question 2 of this note, (SERVICE DEFINITION REFRESH)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Ideally the system should have a working RFC connection from each of its servers to the SAP Service System. You can check whether this is the case by using Transaction SM59 -&gt; R/3 destinations, then choosing SAPOSS and doing 'Test -&gt; Authorisation'. From SDCC version 1.7 on, a tool for checking RFC destination SAPOSS is available through Information -&gt; Service prep. check -&gt; Status of RFC dest. (from outside SDCC it can be called through Transaction SE38 -&gt; BDLPING(or /BDL/PING,if SDCC was implemented with ST-PI))</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A list with SAP instances is displayed, and for each instance it is</ol></ol><ol><ol>shown whether</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>a) there are background work processes on the instance (key functions of SDCC run as background jobs!)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>b) whether the RFC connections to SAPOSS/SAPNET_RFC are working.</ol></ol><ol><ol>(Please note that this only checks if a 'ping ' can be exchanged,&#160;&#160;not the authorization of the user sending it. Please check Question 29 for information on how to maintain the destinations.)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>What you need is an instance where both these requirements are rated 'green'; the server assigned to that instance can be used by SDCC.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Case 1: Some servers have functional SAPOSS RFC destinations</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>If a working SAPOSS RFC destination exists in one server (but not in others) and this server contains background work processes, this server can be explicity set in 'Maintenance -&gt; Customizing -&gt; General' under 'Background server' for use by SDCC. This makes sure that the SDCC core data collection and transfer processes can run and that the 'Session Overview Refresh' that is triggered by the ASM is working.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>In terms of doing a Service Definition Refresh, or a Session Overview Refresh (which run on dialog work processes), you have to switch to a usable server with Transaction SM51.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>NOTE: Service definitions should be only be refreshed from OSS where appropriate, as described in Question 2 of this note, (SERVICE DEFINITION REFRESH)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>If a working RFC destination exists in one server but this server does not contain background work processes, you can call report BDLSEND (/BDL/SEND where SDCC is implemented by ST-PI) with SE38 on the functional server and transmit data manually. In the input screen for BDLSEND (or /BDL/SEND...) you usually only need to fill the fields SESSNR (with .Sess. num..), MANDANT (with the .Ext..) and DEST (with the RFC Destination&#160;&#160; in 'Maintenance -&gt; Customizing -&gt; General'). However, this should only be done as an exception! For the normal use of the Earlywatch Alert a sufficient number of background work processes should be set up.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Case 2: No server has a functional SAPOSS RFC destination</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>If the system has no working SAPOSS RFC destination it can have a number of reasons (see Note 33135, Appendix A):</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>I. Error ' service sapdp99 unknown' or 'error opening an RFC connection'occurs</ol></ol><ol><ol>The services file may not be maintained correctly.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>(i) Add the entry sapdp99&#160;&#160;&#160;&#160;3299/tcp&#160;&#160;in the 'services' file of the customer system which is usually UNIX: /etc/service , NT:</ol></ol><ol><ol>&lt;windir&gt;\\system32\\drivers\\etc\\services. (If it is not already there; here 3299 is the port number and tcp is the protocol)</ol></ol><ol><ol>This method should be preferred to option ii).</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>(ii) Alternatively you can change from 'sapdp99' to '3299' directly in SAPOSS.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>After this you can proceed as in case 1.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>II. If there is a saprouter or firewall problem (route permission denied)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>The file saprouttab is not correct, refer to note 30289. If all efforts fail, please open a message (component BC-MID-RFC).</ol></ol><ol><ol>24. Dedicated background server can't be set in SDCC</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: After entering the dedicated background server in SDCC -&gt; Maintenance-&gt; Remote Environment -&gt; Default Target Destinations, and leaving the window with the OK Button the server name is automatically changed into capital letters. This makes processing of the download in the background impossible. In some cases warnings in SDCC log appear : \"ASM: SASM_Z0000000xx_COLL_TRANS failed\". The reason for this warning is that the job SASM_Z0000000xx_COLL_TRANS is scheduled without a start date and time due to the badly implemented background server.</ol></ol><ol><ol>What can be done?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: Please implement the latest SDCC version, at least SDCC 2.3 (see note91488). If the problem persists, please open a message in component SV-SMG-SDD.</ol></ol><ol><ol>25. Getting kicked out of SDCC immediately after calling it</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: After calling SDCC a pop up shows up : \" YOU ARE NOT AUTHORIZED TO LOG ON TO TARGET SYSTEM\" or \"KEINE BERECHTIGUNG ZUR ANMELDUNG AM ZIELSYSTEM\" before throwing me out. What should I do?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: The RFC destinations SAPOSS or SAPNET_RFC are not properly maintained, most likely because the user or password are not maintained correctly.You will find information about how to maintain these destinations correctly in the next question.</ol></ol><ol><ol>26. SAPOSS, SAPNET_RFC, SAPNET_RTCC are not properly maintained</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: These destinations look unusual in SM59; is there a template I can check them against?</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: SAPOSS is configured automatically on the basis of data in OSS1.</ol></ol><ol><ol>If SAPOSS isn&#180;t working, please make sure that the entries for 'Technical settings' in OSS1 are correct. Refer to Notes 33135 and 30289 for more information on OSS1. To ensure SAPOSS is maintained with 'load distribution' set to 'yes', please follow note 766505.</ol></ol><ol><ol>SAPNET_RFC and SAPNET_RTCC are copies of SAPOSS. They get created automatically when the tools SDCC (SAPNET_RFC) and RTCCTOOL ( SAPNET_RTCC) connect to OSS the first time. They get created with 'Loadbalancing' switched on. This makes the entry in the field 'Target Host' appear very short - please note that the full string as described below can only be maintained if 'Loadbalancing' is set to 'No'.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Please note that after an update of SAPOSS via TA OSS1 the client may change to 000 - please update this manually to 001.</ol></ol><ol><ol>Also, after an update of SAPOSS via TA OSS1 SAPNET_RFC and SAPNET_RTCC do NOT get updated automatically. The easiest way to ensure they are updated correctly is to delete both destinations, and then start a connection to OSS from the tools ( SAPNET_RFC: SDCC -&gt; Maintenance -&gt; Refresh -&gt; Service Overview. SAPNET_RTCC: SE38 -&gt; RTCCTOOL)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Often ( but not always - systems with 2 saprouters can be different) the parameters of a working SAPOSS or SAPNET_RFC destination (where Load balancing is set to 'No' ) are set up like this:</ol></ol>\r\n<ul>\r\n<li>Target host: /H/X1/S/sapdp99/H/X2/S/sapdp99/H/oss001<br />with<br />X1 = customer saprouter IP address<br />X2 = IP address of sapservX<br /><br />possible entries for sapservX:<br />sapserv1 (***************) Internet VPN connection<br />sapserv2 (*************)&#160;&#160;Internet SNC connection<br />sapserv3 (***********)&#160;&#160;&#160;&#160;for customers connected to Germany.<br />sapserv4 (************)&#160;&#160;&#160;&#160;for customers in Americas.<br />sapserv5 (************)&#160;&#160;&#160;&#160;for customers connected to Japan.<br />sapserv6 (*************)&#160;&#160;for Australia and New Zealand customers.<br />sapserv7 (*************)&#160;&#160; for Asia customers.<br /><br /></li>\r\n</ul>\r\n<p>SAPOSS and SAPNET_RFC</p>\r\n<ul>\r\n<li>System number = 01</li>\r\n</ul>\r\n<ul>\r\n<li>Client = 001</li>\r\n</ul>\r\n<ul>\r\n<li>User = OSS_RFC</li>\r\n</ul>\r\n<ul>\r\n<li>Password = CPIC</li>\r\n</ul>\r\n<p>SAPNET_RTCC</p>\r\n<ul>\r\n<li>Client = 001</li>\r\n</ul>\r\n<ul>\r\n<li>User = ST14_RTCC</li>\r\n</ul>\r\n<ul>\r\n<li>Password gets set automatically when the destination is created. In case of problems with the password please delete SAPNET_RTCC and recreate it.</li>\r\n</ul>\r\n<ol><ol>27. Data transfer job aborts with a short dump TSV_NEW_PAGE_ALLOC_FAILED</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: When I collect and transfer data for a download the SDCC message log shows that data collection is ok, but ends with 'Data transfer started'. Additionally a short dump TSV_NEW_PAGE_ALLOC_FAILED is created ( see ST22) and the transfer job is aborted.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: In table BDLCUST (/BDL/CUST where SDCC has been implemented via ST-PI) the entry for SENDSTYL has to be changed from SINGLE_RFC to MULTIPLE_RFC. You can do this in SE38, with report BDLSETUP (/BDL/SETUP where SDCC has been implemented by ST-PI), using KEY : SENDSTYL and VALUE: MULTIPLE_RFC. Then delete the data collection from the download viewer and redo the complete download. For more details, especially regarding ADDON data see note 312834.</ol></ol><ol><ol>28. RFC related errors</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q1: While collecting data the message log shows the following error:</ol></ol><ol><ol>&#160;&#160; -&#160;&#160;&lt;function name&gt; : no matching interface found</ol></ol><ol><ol>&#160;&#160; -&#160;&#160;BDL_GET_DDIC_INFO exception 2 wrong_function_parameter</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: On systems with more than one server each server should have the same entry in the 'services' file for the message server. Change&#160;&#160;the entry 'sapms&lt;SID&gt;' of the 'services' file on all servers of the system to the identical value '36xx/tcp' (xx is a two digit number), The 'services' file is usually</ol></ol><ol><ol>UNIX: /etc/service ,</ol></ol><ol><ol>NT: &lt;windir&gt;\\system32\\drivers\\etc\\services.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q2: ST22 shows ABAP runtime errors &#180;RFC_NO_AUTHORITY&#180; for the user that executed SDCC. User &#180;...&#180; has no RFC authorization for function group</ol></ol><ol><ol>&#180;BDL...&#180;.</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>A: The user authorizations have to be adjusted accordingly.</ol></ol><ol><ol>29. Transport errors ( ONLY relevant if SDCC was implemented by note 560630 - NEVER if it was implemented with ST-PI)</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>Q: Implementing transport SAPKVSDB* ended with RC8, and the following message: BDLFUVER2 table class is 'L'. Entries must not be replaced.</ol></ol>\r\n<p style=\"padding-left: 90px;\">A: Please re-implement the transport with unconditional mode u168. Please be careful NEVER to implement any transport containing SDCC into a system where ST-PI has already been implemented, or in a system with R/3 release 6*</p>\r\n<p style=\"padding-left: 90px;\">30. Administration of&#160;EarlyWatch Alert processed at SAP in SDCC</p>\r\n<p style=\"padding-left: 90px;\">Q: How can I activate the EarlyWatch Alert processed at SAP as described in SAP Note 207223?</p>\r\n<p style=\"padding-left: 90px;\">A:&#160;a) Call Transaction SDCC.<br />b) Please update the service definitions via the menu path<br />'SDCC -&gt; Maintenance -&gt; Refresh -&gt; Service definitions'. During this update, the newest definitions of all remote services are fetched from SAP.<br />c) You can create a service session following the menu path 'Session -&gt; Create -&gt; Dynamic session' and choosing the service 'EarlyWatch Alert'.<br />To ensure a speedy transfer of the data we recommend to send EWA data between Wednesday and Saturday, so the 'start date' for the first session should be chosen accordingly.<br />The recommended frequency for sending EarlyWatch Alert data to SAP is 'every 7 days'.<br />Please note only that only one Earlywatch Alert session per system per<br />day can be processed at SAP.<br />The start time for the data download for the EWA is determined by the customizing in the Automatic Session Manager.</p>\r\n<p style=\"padding-left: 90px;\">Q: What is the Automatic Session Manager (ASM)?</p>\r\n<p style=\"padding-left: 90px;\">A: Automatic Session Manager<br />The use of the Automatic Session Manager (ASM) is recommended, because it allows the data download for the Earlywatch Alert sessions to be executed automatically.<br />a) Activation of the ASM<br />The ASM is activated via the path 'Autom. Sess. Manager -&gt;&#160; Controls'. In the displayed window you schedule a background job which takes over the data collecting and sending jobs for upcoming sessions. During scheduling, please make sure that the activation flag is set.<br />It should be set by default. If it is not, it can be activated under the point 'Customizing'. Please schedule the ASM DAILY (!) between 10 pm and midnight.<br />b) Customizing<br />Via the path SDCC -&gt; Autom. Sess. Manager -&gt; Settings -&gt; Customizing, you can make the following settings:<br />- Earliest date for session data processing (0-3 days earlier)<br />- Hour for processing session data (00-23)<br />- Whether data may be collected prior to the Monday of the session<br />&#160; week (recommendation: No)<br /><br />Q:&#160;How do I manual collect and send&#160;data?</p>\r\n<p style=\"padding-left: 90px;\">A: Alternatively, you can also send the data to SAP manually, but please note that in this case, this process as well as the refresh of the service definitions must be done&#160; BEFORE EVERY service session.<br />To collect and send the data, mark the desired session in the SDCC and then click on the button 'Download' in the menu bar.<br />In the next window you can schedule the collecting and sending of the data for the desired time.<br /><br />Q: I run into problems during collecting or sending of&#160;data. What should I do?</p>\r\n<p style=\"padding-left: 90px;\">A: Should you experience problems during collecting or sending of the data, these will be indicated via the status traffic light during the session in question. More detailed information about these problems can be found in the SDCC session log. By double-clicking on the respective entry in the SDCC session log, in many cases help texts for correcting the error will be shown. In Note 216952 you can find additional information on the most frequent problems in SDCC.<br /><br />Q: How do I deactivate the \"EWA Sessions for SAP\"?</p>\r\n<p style=\"padding-left: 90px;\"><br />A: These sessions can be stopped by deleting all entries in the respective table.<br />For systems on basis release 4* with ST-PI 003*, the entries can be deleted from Table /BDL/CUSTSES using Report /BDL/DESCHEDULE_EWA_FROM_ASM.<br />For all other systems ( which are not using ST-PI 2005_1*) the entries can be deleted from Table BDLCUSTSES using Report BDL_DESCHEDULE_EWA_FROM_ASM.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "TIME"}, {"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Transaction codes", "Value": "COPY"}, {"Key": "Transaction codes", "Value": "MODE"}, {"Key": "Transaction codes", "Value": "SE37"}, {"Key": "Transaction codes", "Value": "SE38"}, {"Key": "Transaction codes", "Value": "SUCH"}, {"Key": "Transaction codes", "Value": "ICON"}, {"Key": "Transaction codes", "Value": "FULL"}, {"Key": "Transaction codes", "Value": "MEAN"}, {"Key": "Transaction codes", "Value": "SM59"}, {"Key": "Transaction codes", "Value": "SM37"}, {"Key": "Transaction codes", "Value": "ST22"}, {"Key": "Transaction codes", "Value": "SM21"}, {"Key": "Transaction codes", "Value": "SDCC"}, {"Key": "Transaction codes", "Value": "SM51"}, {"Key": "Transaction codes", "Value": "SESS"}, {"Key": "Transaction codes", "Value": "ST14"}, {"Key": "Transaction codes", "Value": "STFK"}, {"Key": "Transaction codes", "Value": "YTCC"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D073660)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D073660)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000216952/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000216952/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000216952/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000216952/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000216952/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000216952/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000216952/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000216952/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000216952/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "792941", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/792941"}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680"}, {"RefNumber": "762696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/762696"}, {"RefNumber": "713674", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/713674"}, {"RefNumber": "712757", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/712757"}, {"RefNumber": "712511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/712511"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604"}, {"RefNumber": "594721", "RefComponent": "SV-SMG-OP", "RefTitle": "Generation error for import of ST 2.10 SP6", "RefUrl": "/notes/594721"}, {"RefNumber": "588128", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/588128"}, {"RefNumber": "574177", "RefComponent": "BC-DB-LVC", "RefTitle": "DBIF_DSQL2_CONNECTSTR_ERROR for LCA in non-APO systems", "RefUrl": "/notes/574177"}, {"RefNumber": "516520", "RefComponent": "SV-SMG-SDD", "RefTitle": "Short dump in program RSBDLPUTL with Transaction SDCC", "RefUrl": "/notes/516520"}, {"RefNumber": "452407", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "CALL_FUNCTION_NOT_ACTIVE for STUO function modules", "RefUrl": "/notes/452407"}, {"RefNumber": "412391", "RefComponent": "XX-SER-LAS", "RefTitle": "System Measurement: Message \"Data locked for transfer\"", "RefUrl": "/notes/412391"}, {"RefNumber": "312834", "RefComponent": "SV-SMG-SDD", "RefTitle": "ABAP dump TSV_NEW_PAGE_ALLOC_FAILED in SAPLBDL3", "RefUrl": "/notes/312834"}, {"RefNumber": "311303", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/311303"}, {"RefNumber": "309551", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/309551"}, {"RefNumber": "209830", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: Error in user count", "RefUrl": "/notes/209830"}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223"}, {"RefNumber": "200463", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/200463"}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939"}, {"RefNumber": "178631", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/178631"}, {"RefNumber": "1639050", "RefComponent": "SV-SMG-SDA", "RefTitle": "Collective Note for SDA on ST 710", "RefUrl": "/notes/1639050"}, {"RefNumber": "1470375", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1470375"}, {"RefNumber": "1062557", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1062557"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "812386", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "RFC connections to SAPNet R/3 front end", "RefUrl": "/notes/812386 "}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "1639050", "RefComponent": "SV-SMG-SDA", "RefTitle": "Collective Note for SDA on ST 710", "RefUrl": "/notes/1639050 "}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604 "}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223 "}, {"RefNumber": "1470375", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Service Definitions -- When to Refresh them ?", "RefUrl": "/notes/1470375 "}, {"RefNumber": "594721", "RefComponent": "SV-SMG-OP", "RefTitle": "Generation error for import of ST 2.10 SP6", "RefUrl": "/notes/594721 "}, {"RefNumber": "516520", "RefComponent": "SV-SMG-SDD", "RefTitle": "Short dump in program RSBDLPUTL with Transaction SDCC", "RefUrl": "/notes/516520 "}, {"RefNumber": "412391", "RefComponent": "XX-SER-LAS", "RefTitle": "System Measurement: Message \"Data locked for transfer\"", "RefUrl": "/notes/412391 "}, {"RefNumber": "452407", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "CALL_FUNCTION_NOT_ACTIVE for STUO function modules", "RefUrl": "/notes/452407 "}, {"RefNumber": "309551", "RefComponent": "SV-SMG-SDD", "RefTitle": "How to implement a new function module for a check in <PERSON><PERSON><PERSON>", "RefUrl": "/notes/309551 "}, {"RefNumber": "209830", "RefComponent": "XX-SER-LAS", "RefTitle": "System measurement: Error in user count", "RefUrl": "/notes/209830 "}, {"RefNumber": "1062557", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Download without RFC connection", "RefUrl": "/notes/1062557 "}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939 "}, {"RefNumber": "574177", "RefComponent": "BC-DB-LVC", "RefTitle": "DBIF_DSQL2_CONNECTSTR_ERROR for LCA in non-APO systems", "RefUrl": "/notes/574177 "}, {"RefNumber": "312834", "RefComponent": "SV-SMG-SDD", "RefTitle": "ABAP dump TSV_NEW_PAGE_ALLOC_FAILED in SAPLBDL3", "RefUrl": "/notes/312834 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}