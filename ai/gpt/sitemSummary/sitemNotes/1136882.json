{"Request": {"Number": "1136882", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1739, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016457312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001136882?language=E&token=E8786A17A0779E512EA85B001C4389EA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001136882", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001136882/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1136882"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1136882 - SAPBWNews BW 7.00 ABAP SP 18"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Support Package 18 for BI Release 7.0</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.0, BW 7.0, BW 7.00, BW Patches, BI, BI 7.00, SAPBINews, NW2004s, 2004s</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This SAP Note contains the SAPBINews for <strong><strong>Support Package 18 </strong></strong> of BI Release 7.0. Here, you will find a list of all SAP Notes describing corrections or enhancements in <strong>Support Package 18</strong>. This note will be updated when other notes are added. <br /><br />Note that BI 7.0 ABAP SP18 is a part of <strong>SAP NW 7.0 SPS16</strong>.<br /><br />The information is divided into the following areas:</p>\r\n<ul>\r\n<li><strong>Manual actions that may be necessary:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Factors you must take into account when you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors that may occur after you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>General information:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors corrected by this Support Package; Enhancements delivered with this Support Package.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>See the release and information notes.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Factors you must take into account when you import the Support Package:</strong><br /><br /></p>\r\n<p>Before you import the Support Package, check if there are inactive objects in your system: Call transaction SE80, select \"Inactive objects\" and enter * in the user field.  If objects that are assigned to the SAP namespace are displayed in the section \"Transportable Objects\", you must activate these before you import the Support Package.  If you find objects, proceed as described in consulting Note 1131831.  You must never delete inactive objects from the transport request as this would result in extensive subsequent problems after you import the Support Package.  For more detailed information, refer to Note 822379.<br /><br />Read up on the latest SAP notes that should be kept in mind and possibly implemented after you import <strong>Support Package 18</strong>.<br />Do not just indiscriminately implement all notes available after you import the corresponding Support Package. However, always implement \"Hot News\" and priority 1 notes.<br />In the search function in the SAP Notes database or in SAP Service Marketplace, use the following search term: <strong>SAPKW70019</strong>, and in \"Search Criteria --&gt; Extended Search Criteria --&gt; Priority\", choose \"Hot News\" and \"Correction with high priority\".<br />You can also restrict the search to a specific component by entering the relevant specifications in the \"Applic. Area\" field, for example BW-WHM*.</p>\r\n<p>Following the import of a Support Package, <strong>post-implementation steps</strong> are normally <strong>required</strong> in transaction SNOTE. SAP Notes that have already been implemented can become inconsistent and can cause <strong>functions that work incorrectly or syntax errors</strong>. Call transaction <strong>SNOTE</strong> and <strong>reimplement</strong> the SAP Notes that have become inconsistent. When you have done this, your BW system is operative and consistent again.<br /><br />Import the SNOTE corrections first and read the composite SAP Note 875986, which contains important notes about Note Assistant.<br /><br />You should implement Notes 932065 and 948389 before you use transaction SNOTE to implement advance corrections. Otherwise, problems may occur when you try to deimplement notes again. For more information, see Notes 932065 and 948389.<br /><br /><br />If documents have already been migrated to the portal, you may have to repeat the migration. You can find more information in Note 950877.<br /><br />If you have questions about downloading Support Package stacks, see Note 911032 'FAQ - SAP Support Package Stack Download'. For more information, see Note 911032.<br /><br />For information about minimal revisions in the BI Accelerator, see Note 1141136.<br /><br /></p>\r\n<p><strong>Errors that may occur after you import the Support Package:</strong></p>\r\n<p><br />Extracting data from a DataStore object fails with the error message \"Key figure &lt;key figure name&gt; unknown in InfoProvider &lt;InfoProvider name&gt;\". This occurs when the DataStore object, which acts at the source, contains a referencing key figure. See Note 1243987.<br /><br />Delivered transformations are deleted and can no longer be transferred from the content. The active and saved versions of the transformation are not affected. For more information, see Note 1171293.<br /><br /><br />The activation of new MultiProviders no longer works. For more information, see Note 1169902.<br /><br />Incorrect data in integrated planning. For more information, see Note 1179076.<br /><br />1121202 Planning functions: When distributing with keys, terminations may occur in planning functions or input-ready queries in relation to characteristic relationships. Notes 1222910 and 1228108 correct these subsequent problems.<br /><br />After you implement Note 1146957 Releasing memory OLAP_CACHE, terminations may occur in input-ready queries in relation to characteristic relationships.<br /> Note 1226479 corrects these subsequent problems.<br /><br />No data displayed with node selections in input-ready query. For more information, see Note 1235446.<br /><br />A dump occurs when you open a MultiProvider in the ABAP workbench. For more information, see Note 1236075.<br /><br /><br /></p>\r\n<p><strong>Errors corrected in this Support Package: </strong><br /><br /></p>\r\n<p>Due to an error implemented with Note 1085394, <br />an InfoPackage that is executed in the background may <br />request a repeat. As a result, there may be duplicate data in the data targets. For more information, see Note 1159011.<br /><br />A query on an InfoProvider, for which the property \"Nearline Storage Should Be Read As Well\" is set, delivers incorrect results when you use restricted key figures and/or structure elements.  For more information, see Note 1155195.<br /><br />Delivered transformations are deleted and can no longer be transferred from the content. The active and saved versions of the transformation are not affected. For more information, see Note 1171293.<br /><br /></p>\r\n<p><strong>Enhancements delivered with this Support Package:</strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX (Business Explorer)"}, {"Key": "Other Components", "Value": "BW-BEX-ET (Enduser Technology)"}, {"Key": "Other Components", "Value": "BW-SYS-GUI (BW Frontend and GUI)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001136882/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001136882/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001136882/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001136882/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001136882/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001136882/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001136882/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001136882/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001136882/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1332791", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI 7.0 SP 22 BIA Revision Kompatiblitätsprüfung", "RefUrl": "/notes/1332791"}, {"RefNumber": "1298444", "RefComponent": "BC-DWB-CEX", "RefTitle": "Error when executing transformation with formula", "RefUrl": "/notes/1298444"}, {"RefNumber": "1277919", "RefComponent": "BW-BEX-ET-BC-PREC", "RefTitle": "Termination in report RS_PREC_PLAN", "RefUrl": "/notes/1277919"}, {"RefNumber": "1259935", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Variable intervals transferred incorrectly from history", "RefUrl": "/notes/1259935"}, {"RefNumber": "1259934", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Saving favorites does not work for compounding", "RefUrl": "/notes/1259934"}, {"RefNumber": "1259892", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Input help (F4) termination when saving favorites", "RefUrl": "/notes/1259892"}, {"RefNumber": "1259786", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 21 BIA: Revision: Compatibility check", "RefUrl": "/notes/1259786"}, {"RefNumber": "1235934", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "You can change reusable filters without authorization", "RefUrl": "/notes/1235934"}, {"RefNumber": "1235467", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 20: BIA: Revision: Compatibility check", "RefUrl": "/notes/1235467"}, {"RefNumber": "1230306", "RefComponent": "BW-BEX-ET", "RefTitle": "HOTFIX TEMPLATE: SAP NetWeaver 7.X BI Frontend in 7.10", "RefUrl": "/notes/1230306"}, {"RefNumber": "1230262", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Control characters cause errors in input help", "RefUrl": "/notes/1230262"}, {"RefNumber": "1180155", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "F4 dialog box truncated when opened", "RefUrl": "/notes/1180155"}, {"RefNumber": "1178989", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "WAD 7.0: Div tag warning messages", "RefUrl": "/notes/1178989"}, {"RefNumber": "1178266", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Application error:Language Dependent Text", "RefUrl": "/notes/1178266"}, {"RefNumber": "1175354", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "RFC_MODIFY_R3_DESTINATION does not execute a COMMIT", "RefUrl": "/notes/1175354"}, {"RefNumber": "1172176", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction of minor usability problem", "RefUrl": "/notes/1172176"}, {"RefNumber": "1172047", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in SAPLRRK0, form CACHE_CREATE_NEW_FF-01-", "RefUrl": "/notes/1172047"}, {"RefNumber": "1171296", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Destination to the BW is not checked", "RefUrl": "/notes/1171296"}, {"RefNumber": "1170818", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Additional trace when deleting jobs and logs", "RefUrl": "/notes/1170818"}, {"RefNumber": "1169659", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process variants not entered by BDLS", "RefUrl": "/notes/1169659"}, {"RefNumber": "1169453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSR_MULTIPROV_CHECK: Messages are repeated", "RefUrl": "/notes/1169453"}, {"RefNumber": "1169074", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check with hierarchies takes too long", "RefUrl": "/notes/1169074"}, {"RefNumber": "1168770", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replaced by time-dependent characteristics", "RefUrl": "/notes/1168770"}, {"RefNumber": "1166664", "RefComponent": "BW-BEX-OT-ISET", "RefTitle": "System error in program CL_RSR and form GET_CHANMID-02-", "RefUrl": "/notes/1166664"}, {"RefNumber": "1166658", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Duplicate records in BIA dimension index", "RefUrl": "/notes/1166658"}, {"RefNumber": "1166594", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cum. key figs incorrect w/ init. non-cum. excep. aggreg.", "RefUrl": "/notes/1166594"}, {"RefNumber": "1166297", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Condition on a structure element with date key figure", "RefUrl": "/notes/1166297"}, {"RefNumber": "1166294", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSR, form GET_CHANMID-02-", "RefUrl": "/notes/1166294"}, {"RefNumber": "1166241", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSR, form GET_COB_PRO-01-", "RefUrl": "/notes/1166241"}, {"RefNumber": "1166237", "RefComponent": "BW-BEX-OT", "RefTitle": "BRAIN X299 in class CL_RSD_multiprov, form Factory-02-", "RefUrl": "/notes/1166237"}, {"RefNumber": "1166052", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Repair deficiencies of OLAP cache in new cache", "RefUrl": "/notes/1166052"}, {"RefNumber": "1166051", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data object size for data archiving process is too large", "RefUrl": "/notes/1166051"}, {"RefNumber": "1165627", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Bursting: Process terminates if error occurs for recipient", "RefUrl": "/notes/1165627"}, {"RefNumber": "1165626", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Invalidation of reload request terminates with RSDA 337", "RefUrl": "/notes/1165626"}, {"RefNumber": "1165614", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Formula variables that are replaced from attribute display 0", "RefUrl": "/notes/1165614"}, {"RefNumber": "1165520", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPLRRK0 and form RGC_PFLEGEN_03-02-", "RefUrl": "/notes/1165520"}, {"RefNumber": "1165514", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: OBJECTS_OBJREF_NOT_ASSIGNED in CL_RSPLS_ALVL", "RefUrl": "/notes/1165514"}, {"RefNumber": "1165359", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P19:EHP1:PC: Repeat for InfoPackage not executed", "RefUrl": "/notes/1165359"}, {"RefNumber": "1165194", "RefComponent": "BW-SYS-DB-MGR", "RefTitle": "Heterogeneous system integration RS_BW_POST_MIGRATION", "RefUrl": "/notes/1165194"}, {"RefNumber": "1165172", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Jobs not deallocated during deletion transport", "RefUrl": "/notes/1165172"}, {"RefNumber": "1165128", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect elimination of internal business vol.: Read mode X", "RefUrl": "/notes/1165128"}, {"RefNumber": "1165008", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Display attribute \"Validity Interval\" not available", "RefUrl": "/notes/1165008"}, {"RefNumber": "1164864", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in program SAPLRRK0; form LRECH_AGGR_ELSE-01-", "RefUrl": "/notes/1164864"}, {"RefNumber": "1164704", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No conversion into SID if parent defined by selection option", "RefUrl": "/notes/1164704"}, {"RefNumber": "1164386", "RefComponent": "BW-PLA-IP", "RefTitle": "BRAIN 299 in CL_RSD_MULTIPROV; GET_PART_IOBJNM-03-", "RefUrl": "/notes/1164386"}, {"RefNumber": "1164372", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Personalized variables are displayed in 3.X workbook", "RefUrl": "/notes/1164372"}, {"RefNumber": "1164361", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination ASSIGN_LENGTH_NEGATIVE program \"SAPLRRSI\"", "RefUrl": "/notes/1164361"}, {"RefNumber": "1164189", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "X299 BRAIN in CL_RSR_RRK0_PARTITION; FLUSH_CACHE-03-", "RefUrl": "/notes/1164189"}, {"RefNumber": "1163991", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA - DBMAN015 - Message not required for BIA", "RefUrl": "/notes/1163991"}, {"RefNumber": "1163970", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: \"Always\" not executed if status is red", "RefUrl": "/notes/1163970"}, {"RefNumber": "1163928", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P18:PSA:DS:REQARCH: Posting request from PSA using scheduler", "RefUrl": "/notes/1163928"}, {"RefNumber": "1163815", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Transporteintrag bei Impact", "RefUrl": "/notes/1163815"}, {"RefNumber": "1163757", "RefComponent": "SCM-APO-FCS", "RefTitle": "APO: Writing to aggreates fails", "RefUrl": "/notes/1163757"}, {"RefNumber": "1163670", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: BATCH: Server/host/group not transferred from default", "RefUrl": "/notes/1163670"}, {"RefNumber": "1163551", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Incorrect data in a specific situation", "RefUrl": "/notes/1163551"}, {"RefNumber": "1163320", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Incorrect text in context menu of BEx Analyzer", "RefUrl": "/notes/1163320"}, {"RefNumber": "1163209", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 CL_RSR_RRK0_PARTITION, NOTIFY_READ_REQUEST-01-", "RefUrl": "/notes/1163209"}, {"RefNumber": "1163140", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "BRAIN 418: The variable ... is used incorrectly", "RefUrl": "/notes/1163140"}, {"RefNumber": "1163116", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Error in MultiProvider with stock cubes", "RefUrl": "/notes/1163116"}, {"RefNumber": "1163099", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Manage:Red requests in administration due to QM action", "RefUrl": "/notes/1163099"}, {"RefNumber": "1163037", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:BATCH: Unnecessary BP_JOB_DELETE in batch manager", "RefUrl": "/notes/1163037"}, {"RefNumber": "1163009", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Using BIA in check center even if it has status \"Inactive\"", "RefUrl": "/notes/1163009"}, {"RefNumber": "1162986", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P18:PC:PSA process:New DS:Error when activating proc. chain", "RefUrl": "/notes/1162986"}, {"RefNumber": "1162968", "RefComponent": "BW-WHM", "RefTitle": "Improving message BRAIN 290", "RefUrl": "/notes/1162968"}, {"RefNumber": "1162825", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Message BRAIN 418 for variables on InfoSets", "RefUrl": "/notes/1162825"}, {"RefNumber": "1162808", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data for a query with virtual characteristics", "RefUrl": "/notes/1162808"}, {"RefNumber": "1162790", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Program RSDDK_CHECK_AGGREGATE for key figures of type FLTP", "RefUrl": "/notes/1162790"}, {"RefNumber": "1162788", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18) Error during loading using third party tool", "RefUrl": "/notes/1162788"}, {"RefNumber": "1162738", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Exception reporting - Creating alerts: Error BL 207", "RefUrl": "/notes/1162738"}, {"RefNumber": "1162723", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Tabelarische Anzeige dumpt", "RefUrl": "/notes/1162723"}, {"RefNumber": "1162641", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA: Incorrect data after compression terminates", "RefUrl": "/notes/1162641"}, {"RefNumber": "1162528", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "P18:SDL:BWAPPL: InfoCubes can be loaded if BWAPPL <> APO", "RefUrl": "/notes/1162528"}, {"RefNumber": "1162433", "RefComponent": "BW-WHM-DST", "RefTitle": "Error 18 in update (Support Package 17)", "RefUrl": "/notes/1162433"}, {"RefNumber": "1162426", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Field RSDS-HASHNUMBER is empty", "RefUrl": "/notes/1162426"}, {"RefNumber": "1162365", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Collective note: BIA and incorrect data", "RefUrl": "/notes/1162365"}, {"RefNumber": "1162364", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Relational Browse not working with Cum and Ncum providers", "RefUrl": "/notes/1162364"}, {"RefNumber": "1162324", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Virtual referencing key figure does not display any data", "RefUrl": "/notes/1162324"}, {"RefNumber": "1162290", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query displays incorrect data; execution in \"Safe Mode\"", "RefUrl": "/notes/1162290"}, {"RefNumber": "1162231", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Administration authorization required for information worker", "RefUrl": "/notes/1162231"}, {"RefNumber": "1162230", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Value for hierarchy node variable is not set correctly", "RefUrl": "/notes/1162230"}, {"RefNumber": "1162188", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Runtime: temporal hierarchy join and many node restrictions", "RefUrl": "/notes/1162188"}, {"RefNumber": "1162134", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "BI7.0(SP18) Log incomplete", "RefUrl": "/notes/1162134"}, {"RefNumber": "1162113", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination: CL_RSPLS_CR_CONTROLLER and form CONSTRUCTOR-04-", "RefUrl": "/notes/1162113"}, {"RefNumber": "1161967", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA shadow index: Enhancement of analysis option", "RefUrl": "/notes/1161967"}, {"RefNumber": "1161963", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Duplicate records in BIA dimension indexes: Incorrect data", "RefUrl": "/notes/1161963"}, {"RefNumber": "1161676", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0 (SP 18) Problems when you change time-reference char.", "RefUrl": "/notes/1161676"}, {"RefNumber": "1161528", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Values with double quotes (\") are not filled in variable", "RefUrl": "/notes/1161528"}, {"RefNumber": "1161444", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "\"Runtime object for InfoSet contains errors\" (RSQBW 805)", "RefUrl": "/notes/1161444"}, {"RefNumber": "1161254", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA: Master data reorganization for Y tables is not adjusted", "RefUrl": "/notes/1161254"}, {"RefNumber": "1161224", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "F4 returns incorrect values", "RefUrl": "/notes/1161224"}, {"RefNumber": "1161111", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect (plan) values in very specific situation", "RefUrl": "/notes/1161111"}, {"RefNumber": "1161088", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Activating BIA index: Termination SAPSQL_ARRAY_INSERT_DUPREC", "RefUrl": "/notes/1161088"}, {"RefNumber": "1161082", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: CSV default data not replaced", "RefUrl": "/notes/1161082"}, {"RefNumber": "1160984", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "BRAIN 686 \"The hierarchy with HIESID 0...was not found\"", "RefUrl": "/notes/1160984"}, {"RefNumber": "1160949", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Syntax error when activating transfer rule", "RefUrl": "/notes/1160949"}, {"RefNumber": "1160927", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 19: BIA: Revision: Compatibility check", "RefUrl": "/notes/1160927"}, {"RefNumber": "1160415", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Dumps in the function RSZ_P_DB_ELT_SET_IMMEDIATELY", "RefUrl": "/notes/1160415"}, {"RefNumber": "1160291", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Not changeable but changes allowed", "RefUrl": "/notes/1160291"}, {"RefNumber": "1160247", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BIA index incorrect after cancelation request", "RefUrl": "/notes/1160247"}, {"RefNumber": "1160125", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP18:To Display string fields using Filter option in PSA", "RefUrl": "/notes/1160125"}, {"RefNumber": "1160105", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Termination in XPRA RS_PERS_XPRA_1", "RefUrl": "/notes/1160105"}, {"RefNumber": "1159978", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error in the template RSTMPLWIDTP for Support Package 16", "RefUrl": "/notes/1159978"}, {"RefNumber": "1159976", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in workbook content activation", "RefUrl": "/notes/1159976"}, {"RefNumber": "1159939", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in design item navigation block", "RefUrl": "/notes/1159939"}, {"RefNumber": "1159918", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Transportversionen werden nicht korrekt gelöscht", "RefUrl": "/notes/1159918"}, {"RefNumber": "1159899", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Calendar yr cannot be selected as time slice characteristic", "RefUrl": "/notes/1159899"}, {"RefNumber": "1159765", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Only one timeshift variable works in currency translation", "RefUrl": "/notes/1159765"}, {"RefNumber": "1159659", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P18: PSA: Deleting requests when managing PSA", "RefUrl": "/notes/1159659"}, {"RefNumber": "1159642", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "URL generation for document links takes a long time", "RefUrl": "/notes/1159642"}, {"RefNumber": "1159610", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Exception/cond. transferred incorrectly in Query Designer", "RefUrl": "/notes/1159610"}, {"RefNumber": "1159565", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Manage: Message RSENQ 009 when you start compression", "RefUrl": "/notes/1159565"}, {"RefNumber": "1159481", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA change run: Incorrect data after upgrade to >=BI SP16", "RefUrl": "/notes/1159481"}, {"RefNumber": "1159436", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Activating a hierarchy: '_ADD_OVERLAPPING_LEAF_INTVL'", "RefUrl": "/notes/1159436"}, {"RefNumber": "1159435", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSRT: Check data of the virtual provider", "RefUrl": "/notes/1159435"}, {"RefNumber": "1159373", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Euro character not loaded during file upload", "RefUrl": "/notes/1159373"}, {"RefNumber": "1159306", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Multiple issues with variables on compounded infoobjects", "RefUrl": "/notes/1159306"}, {"RefNumber": "1159299", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Text for data element RSBOOK4 translated incorrectly", "RefUrl": "/notes/1159299"}, {"RefNumber": "1159268", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Only one authorization log for templates w/ several queries", "RefUrl": "/notes/1159268"}, {"RefNumber": "1159260", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Counter for BIA read accesses", "RefUrl": "/notes/1159260"}, {"RefNumber": "1159216", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Logical documents not displayed after upgrade to 7.X", "RefUrl": "/notes/1159216"}, {"RefNumber": "1159211", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Backup version of query or query component is not deleted", "RefUrl": "/notes/1159211"}, {"RefNumber": "1159011", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:SDL:PC: Repeat request possible with process chain", "RefUrl": "/notes/1159011"}, {"RefNumber": "1158959", "RefComponent": "BW-BCT-TCT", "RefTitle": "Deleting statistics data: RSDDSTAT", "RefUrl": "/notes/1158959"}, {"RefNumber": "1158871", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18) Problems when loading to dest. using DB table", "RefUrl": "/notes/1158871"}, {"RefNumber": "1158831", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculation before aggregation with BIA and aggregates", "RefUrl": "/notes/1158831"}, {"RefNumber": "1158829", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replacement and compound keys", "RefUrl": "/notes/1158829"}, {"RefNumber": "1158605", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error RAISE_EXCEPTION (exception RSFO_NO_ENTRY)", "RefUrl": "/notes/1158605"}, {"RefNumber": "1158498", "RefComponent": "BW-WHM", "RefTitle": "Characteristic value not plausible (BRAIN 027)", "RefUrl": "/notes/1158498"}, {"RefNumber": "1158476", "RefComponent": "BW-BEX-ET-BC-PREC", "RefTitle": "Interval variables in broadcasting settings", "RefUrl": "/notes/1158476"}, {"RefNumber": "1158432", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Too many values authorized for hierarchy with intervals", "RefUrl": "/notes/1158432"}, {"RefNumber": "1158429", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Java Web: Input help does not work in the variable screen", "RefUrl": "/notes/1158429"}, {"RefNumber": "1158394", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Aggregation level & exception CX_RS_VERSION_NOT_FOUND", "RefUrl": "/notes/1158394"}, {"RefNumber": "1158317", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Further optimization of authorization check", "RefUrl": "/notes/1158317"}, {"RefNumber": "1158218", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Obsolete planned date replaced by SY-DATUM", "RefUrl": "/notes/1158218"}, {"RefNumber": "1158185", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Exception \"IOBJ_VALUE_NOT_VALID\" is not caught", "RefUrl": "/notes/1158185"}, {"RefNumber": "1158063", "RefComponent": "BW-WHM", "RefTitle": "P18:Security Note:RSSM_EXEC_COMMAND converted to RSBDCOS0", "RefUrl": "/notes/1158063"}, {"RefNumber": "1157947", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P18:7.01:Background repair for delta requests in background", "RefUrl": "/notes/1157947"}, {"RefNumber": "1157922", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "System error in CL_RSR_HIERARCHY_DIM and SELECT_SIDS_3", "RefUrl": "/notes/1157922"}, {"RefNumber": "1157873", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Message RSO 252 using transport connection", "RefUrl": "/notes/1157873"}, {"RefNumber": "1157803", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (Support Package 18): Dump when saving or activating", "RefUrl": "/notes/1157803"}, {"RefNumber": "1157796", "RefComponent": "BW-WHM", "RefTitle": "Characteristic 0REQUEST must reference 0REQUID", "RefUrl": "/notes/1157796"}, {"RefNumber": "1157689", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA - RSRV check: Negative DIMIDs", "RefUrl": "/notes/1157689"}, {"RefNumber": "1157651", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Accessing BW from SEM may cause deadlocks in RSZ* tables", "RefUrl": "/notes/1157651"}, {"RefNumber": "1157615", "RefComponent": "BW-BCT-TCT", "RefTitle": "Entries in RSDDSTATLOGGING are written", "RefUrl": "/notes/1157615"}, {"RefNumber": "1157604", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 813 -only allowed to display the hierarchy to level xx", "RefUrl": "/notes/1157604"}, {"RefNumber": "1157577", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:MON: Reposting several packages in the monitor", "RefUrl": "/notes/1157577"}, {"RefNumber": "1157409", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:You have to choose <PERSON><PERSON> twice before dialog box closes", "RefUrl": "/notes/1157409"}, {"RefNumber": "1157225", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Property 'aggregation direction' is not evaluated correctly", "RefUrl": "/notes/1157225"}, {"RefNumber": "1157168", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Incorrect interval check in selections with \"#\"", "RefUrl": "/notes/1157168"}, {"RefNumber": "1157070", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P18:DSO:Dump if you activate too many requests together", "RefUrl": "/notes/1157070"}, {"RefNumber": "1156982", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Memory overflow during Master Data Deletion", "RefUrl": "/notes/1156982"}, {"RefNumber": "1156948", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: Unicode indicator incorrect", "RefUrl": "/notes/1156948"}, {"RefNumber": "1156897", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Optimization of memory release in ABAP, planning functions", "RefUrl": "/notes/1156897"}, {"RefNumber": "1156832", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Temporal hierarchy join: Termination ITAB_DUPLICATE_KEY", "RefUrl": "/notes/1156832"}, {"RefNumber": "1156802", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:REQARCH: Archiving starts many dialog/batch processes", "RefUrl": "/notes/1156802"}, {"RefNumber": "1156736", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Slow performance because of missing distribution stats", "RefUrl": "/notes/1156736"}, {"RefNumber": "1156681", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Enhancements in MultiProvider hint RSRMULTIPROVHINT", "RefUrl": "/notes/1156681"}, {"RefNumber": "1156628", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Syntax error in the program RSDDS_CHANGERUN_MONITOR", "RefUrl": "/notes/1156628"}, {"RefNumber": "1156602", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "MultiProvider hint not used", "RefUrl": "/notes/1156602"}, {"RefNumber": "1156599", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Correction: 3.x DataSources are not collected", "RefUrl": "/notes/1156599"}, {"RefNumber": "1156593", "RefComponent": "BW-WHM-DST", "RefTitle": "Accessibility issues", "RefUrl": "/notes/1156593"}, {"RefNumber": "1156212", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Master Data load hangs during update of timestamp", "RefUrl": "/notes/1156212"}, {"RefNumber": "1156152", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: API...GET_ERRORS delivers too many messages", "RefUrl": "/notes/1156152"}, {"RefNumber": "1156093", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA Monitor: Improving the authorization check", "RefUrl": "/notes/1156093"}, {"RefNumber": "1155856", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: TOO_MANY_ERRORS if exception handling is off", "RefUrl": "/notes/1155856"}, {"RefNumber": "1155855", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: MESSAGE_TYPE_X during tree configuration", "RefUrl": "/notes/1155855"}, {"RefNumber": "1155839", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RSAR 535 when you execute formula function DATE_FISCPER", "RefUrl": "/notes/1155839"}, {"RefNumber": "1155796", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: OBJREF_NOT_ASSIGNED during synchronous execution", "RefUrl": "/notes/1155796"}, {"RefNumber": "1155740", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPLRRK<PERSON> and form CUMUL_S_07-", "RefUrl": "/notes/1155740"}, {"RefNumber": "1155723", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:PC: Deleting overlapping DTP requests in PC", "RefUrl": "/notes/1155723"}, {"RefNumber": "1155639", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P20: P18:DTP:PC: Dump \"sid_too_low\" for parallel DTPs", "RefUrl": "/notes/1155639"}, {"RefNumber": "1155629", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Mapping errors in rule", "RefUrl": "/notes/1155629"}, {"RefNumber": "1155519", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Dependent variables not updated", "RefUrl": "/notes/1155519"}, {"RefNumber": "1155385", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Record is not deleted before conversion", "RefUrl": "/notes/1155385"}, {"RefNumber": "1155382", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Restarting cancelled broadcast settings", "RefUrl": "/notes/1155382"}, {"RefNumber": "1155340", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:REQARCH: Table control CTRL_SEL is not defined", "RefUrl": "/notes/1155340"}, {"RefNumber": "1155265", "RefComponent": "BW-WHM-DBA-SDEL", "RefTitle": "Enhancing the delete function for BW archiving", "RefUrl": "/notes/1155265"}, {"RefNumber": "1155253", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: RMC_COMMUNICATION_FAILURE when scrolling", "RefUrl": "/notes/1155253"}, {"RefNumber": "1155245", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy Activation Dumps", "RefUrl": "/notes/1155245"}, {"RefNumber": "1155235", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination w/IF_RSR_VAR_RUNTIME~GET_DEPHIER_FOR_VARIABLE-1-", "RefUrl": "/notes/1155235"}, {"RefNumber": "1155195", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Incorrect query results with restricted key figures", "RefUrl": "/notes/1155195"}, {"RefNumber": "1154763", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Error when checking time-dependent hierarchy nodes", "RefUrl": "/notes/1154763"}, {"RefNumber": "1154745", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Text element display does not provide complete information", "RefUrl": "/notes/1154745"}, {"RefNumber": "1154733", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Filter check in planning returns incorrect result", "RefUrl": "/notes/1154733"}, {"RefNumber": "1154708", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Key figure attributes are displayed with a unit or currency", "RefUrl": "/notes/1154708"}, {"RefNumber": "1154671", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Invalid property after migration or copy", "RefUrl": "/notes/1154671"}, {"RefNumber": "1154626", "RefComponent": "BW-BEX", "RefTitle": "New checks available in RSRV", "RefUrl": "/notes/1154626"}, {"RefNumber": "1154560", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) / BI7.1SPS06 Interner Mappingfehler", "RefUrl": "/notes/1154560"}, {"RefNumber": "1154491", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Initialization of the message administration is missing", "RefUrl": "/notes/1154491"}, {"RefNumber": "1154373", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Error with TLSHIFT in non-unique hierarchies", "RefUrl": "/notes/1154373"}, {"RefNumber": "1154318", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Dump w/automatic index deletion and DTP request check", "RefUrl": "/notes/1154318"}, {"RefNumber": "1154309", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Formulas: Function CURC and decimal places", "RefUrl": "/notes/1154309"}, {"RefNumber": "1154272", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No dialog box for \"Extras\"->\"3.X Archiving\" in display mode", "RefUrl": "/notes/1154272"}, {"RefNumber": "1153787", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA: Improving the usability in Support Package 18", "RefUrl": "/notes/1153787"}, {"RefNumber": "1153756", "RefComponent": "BW-PLA-IP", "RefTitle": "Template: BI 7.0 (SP18) / BI 7.1 SPS06", "RefUrl": "/notes/1153756"}, {"RefNumber": "1153605", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Accessibility error in data archiving process maintenance", "RefUrl": "/notes/1153605"}, {"RefNumber": "1153548", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1153548"}, {"RefNumber": "1153399", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:POSTING_ILLEGAL_STATEMENT during transactional writing", "RefUrl": "/notes/1153399"}, {"RefNumber": "1153393", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Performance: Authorization and query with node selection", "RefUrl": "/notes/1153393"}, {"RefNumber": "1153359", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Statistics event \"Open workbook\" is not closed", "RefUrl": "/notes/1153359"}, {"RefNumber": "1153342", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "'Server for scheduling' ignored when server type is 'Host'", "RefUrl": "/notes/1153342"}, {"RefNumber": "1153245", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) InfoObjekte werden nicht mitgesammelt", "RefUrl": "/notes/1153245"}, {"RefNumber": "1153244", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "No data as a result of incorrect WHERE condition", "RefUrl": "/notes/1153244"}, {"RefNumber": "1153235", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Short dump when you import shadow version (SHDS)", "RefUrl": "/notes/1153235"}, {"RefNumber": "1153093", "RefComponent": "BW-WHM", "RefTitle": "Quantity object for InfoObjects does not support InfoSets", "RefUrl": "/notes/1153093"}, {"RefNumber": "1153041", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "falsche Daten: Ausnahmeaggreagtion + temporaler <PERSON>er. Join", "RefUrl": "/notes/1153041"}, {"RefNumber": "1153018", "RefComponent": "BW-BEX-OT", "RefTitle": "Dump ASSERTION_FAILED in class CL_RSDRC_INFOPROV_UNIVERSE", "RefUrl": "/notes/1153018"}, {"RefNumber": "1152998", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Log improvements for the precalculation server", "RefUrl": "/notes/1152998"}, {"RefNumber": "1152929", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Log enhancements for RRMX", "RefUrl": "/notes/1152929"}, {"RefNumber": "1152805", "RefComponent": "BW-WHM-AWB", "RefTitle": "P18:DWWB: No message issued when you delete InfoArea", "RefUrl": "/notes/1152805"}, {"RefNumber": "1152804", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Falsche oder unvollständige Meldung - Anlegen", "RefUrl": "/notes/1152804"}, {"RefNumber": "1152687", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "InfoProvider not read (KIDSEL = true)", "RefUrl": "/notes/1152687"}, {"RefNumber": "1152599", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect val for FLOOR, CEIL and so on w/negative argument", "RefUrl": "/notes/1152599"}, {"RefNumber": "1152582", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Classification of messages", "RefUrl": "/notes/1152582"}, {"RefNumber": "1152557", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "\"Key Figure Definition\" uses wrong key date", "RefUrl": "/notes/1152557"}, {"RefNumber": "1152555", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination \"INPUT_NOT_LEGAL\" in program SAPLRRBA", "RefUrl": "/notes/1152555"}, {"RefNumber": "1152453", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination generating InfoCube write program", "RefUrl": "/notes/1152453"}, {"RefNumber": "1152445", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Report RSDDTZA_BIA_OVERLOAD_EMAIL terminates", "RefUrl": "/notes/1152445"}, {"RefNumber": "1152387", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Syntax error in CL_RSROA_BI_QUERY, method SET_QUERYDATA", "RefUrl": "/notes/1152387"}, {"RefNumber": "1152366", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Precalculation of value set: Query variant is ignored", "RefUrl": "/notes/1152366"}, {"RefNumber": "1152229", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "711: Improving load monitor performance in BW 7.11", "RefUrl": "/notes/1152229"}, {"RefNumber": "1152186", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 Brain in CL_RSR_FIPT_SID2VAL_PREFETCH; TRANSFORM-01-", "RefUrl": "/notes/1152186"}, {"RefNumber": "1152166", "RefComponent": "BW-BCT-TCT", "RefTitle": "Maintain statistics settings: Authorization", "RefUrl": "/notes/1152166"}, {"RefNumber": "1152136", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error during master data reading (ERROR_IN_MD_BUFFERING)", "RefUrl": "/notes/1152136"}, {"RefNumber": "1152069", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Incorrect length for application component", "RefUrl": "/notes/1152069"}, {"RefNumber": "1151982", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Error message is unclear in nested chains", "RefUrl": "/notes/1151982"}, {"RefNumber": "1151859", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "DEL_UNUSED_DIMS is obsolete but still read during deletion", "RefUrl": "/notes/1151859"}, {"RefNumber": "1151843", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in a very specific situation", "RefUrl": "/notes/1151843"}, {"RefNumber": "1151720", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:Long wait time before incorrect request turns red", "RefUrl": "/notes/1151720"}, {"RefNumber": "1151694", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Changes are not displayed", "RefUrl": "/notes/1151694"}, {"RefNumber": "1151566", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "No value displayed for 0REQUID in input help", "RefUrl": "/notes/1151566"}, {"RefNumber": "1151462", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Not all fields are updated", "RefUrl": "/notes/1151462"}, {"RefNumber": "1151306", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Scaling cannot be displayed", "RefUrl": "/notes/1151306"}, {"RefNumber": "1151176", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:BAPI:Message RH 230 with '-' in hierarchy name", "RefUrl": "/notes/1151176"}, {"RefNumber": "1151076", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Sending links: Invalid reference to DataProvider", "RefUrl": "/notes/1151076"}, {"RefNumber": "1150831", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "BI 7.00 EHP 1: Delete flexible PSA", "RefUrl": "/notes/1150831"}, {"RefNumber": "1150754", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Authorizations for InfoSet chars. ignored in input help", "RefUrl": "/notes/1150754"}, {"RefNumber": "1150546", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Performance improvement for cube delta write", "RefUrl": "/notes/1150546"}, {"RefNumber": "1150540", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Parallel filling of aggregates terminates with a lock", "RefUrl": "/notes/1150540"}, {"RefNumber": "1150514", "RefComponent": "BW-PLA-IP", "RefTitle": "Decimal places shifted during planning of amounts", "RefUrl": "/notes/1150514"}, {"RefNumber": "1150499", "RefComponent": "BW", "RefTitle": "Directory traversal in BW statistics", "RefUrl": "/notes/1150499"}, {"RefNumber": "1150436", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Process step continues all open requests", "RefUrl": "/notes/1150436"}, {"RefNumber": "1150341", "RefComponent": "BW-WHM-DST", "RefTitle": "BYY: Additional secondary index on RSSELDONE for BYD", "RefUrl": "/notes/1150341"}, {"RefNumber": "1150154", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "RSDODSO E201: \"No InfoProvider exists with name \"&1\"\"", "RefUrl": "/notes/1150154"}, {"RefNumber": "1150152", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Reclustering: Label not associated with input field", "RefUrl": "/notes/1150152"}, {"RefNumber": "1150148", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: BI Migration: Range-part. to MDC: RSDCUBE not updated", "RefUrl": "/notes/1150148"}, {"RefNumber": "1149844", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Access to non-compound keys very slow", "RefUrl": "/notes/1149844"}, {"RefNumber": "1149760", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Do not use BIA index for queries with virtual key figures", "RefUrl": "/notes/1149760"}, {"RefNumber": "1149660", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSR_RRK0_CACHE; form COMMIT-01-", "RefUrl": "/notes/1149660"}, {"RefNumber": "1149614", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL: Old routine maintenance editor jumps to first line", "RefUrl": "/notes/1149614"}, {"RefNumber": "1149582", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon cannot close InfoPackage request", "RefUrl": "/notes/1149582"}, {"RefNumber": "1149558", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: DTP: No selection conditions for DTP request", "RefUrl": "/notes/1149558"}, {"RefNumber": "1149556", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Characteristic ... has no master data for \"...\"", "RefUrl": "/notes/1149556"}, {"RefNumber": "1149545", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18): Open hub destination: Check is incomplete", "RefUrl": "/notes/1149545"}, {"RefNumber": "1149543", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0 (SP 18): Dump when you maintain a rule", "RefUrl": "/notes/1149543"}, {"RefNumber": "1149521", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error message RSAOLTP 18 during data request", "RefUrl": "/notes/1149521"}, {"RefNumber": "1149504", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Messages on variable screen", "RefUrl": "/notes/1149504"}, {"RefNumber": "1149393", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSRT: Warning for redefined cells and help cells", "RefUrl": "/notes/1149393"}, {"RefNumber": "1149365", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "UNCAUGHT exception when using ABAP exits", "RefUrl": "/notes/1149365"}, {"RefNumber": "1149348", "RefComponent": "BW-PLA-IP", "RefTitle": "BRAIN 299 in CL_RSR_RRK0_RQTS; form GET_R_DELTA_BUF-01-", "RefUrl": "/notes/1149348"}, {"RefNumber": "1149337", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Main memory consumption and planning functions", "RefUrl": "/notes/1149337"}, {"RefNumber": "1149178", "RefComponent": "BW-WHM-DBA-DMA", "RefTitle": "Deadlock when extracting a data target to itself", "RefUrl": "/notes/1149178"}, {"RefNumber": "1149083", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data displayed when you expand a hierarchy node", "RefUrl": "/notes/1149083"}, {"RefNumber": "1149012", "RefComponent": "BW-WHM-AWB", "RefTitle": "Deleting authorizations transfer rule BI7.0SP18(SPS16)", "RefUrl": "/notes/1149012"}, {"RefNumber": "1148857", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Replication with dialog box", "RefUrl": "/notes/1148857"}, {"RefNumber": "1148656", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "RSAPO_SWITCH_TRANS_TO_BATCH", "RefUrl": "/notes/1148656"}, {"RefNumber": "1148336", "RefComponent": "BW-BEX-ET-WEB-GRAPH", "RefTitle": "Enhanced correction report for charts (7.X)", "RefUrl": "/notes/1148336"}, {"RefNumber": "1148150", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Long runtime during quantity conversion", "RefUrl": "/notes/1148150"}, {"RefNumber": "1147998", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Document check \"No authorization\" despite 0BI_ALL/SAP_ALL", "RefUrl": "/notes/1147998"}, {"RefNumber": "1147985", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections to the report RSDDTREX_REORGANIZE_LANDSCAPE", "RefUrl": "/notes/1147985"}, {"RefNumber": "1147984", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA random query check terminates with error message", "RefUrl": "/notes/1147984"}, {"RefNumber": "1147924", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcasting: Distributing multi channels for mandatory var.", "RefUrl": "/notes/1147924"}, {"RefNumber": "1147867", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unexpected message RSPLS 504 for characteristic dependency", "RefUrl": "/notes/1147867"}, {"RefNumber": "1147768", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: BI-CCMS dispatcher runs in the incorrect client", "RefUrl": "/notes/1147768"}, {"RefNumber": "1147766", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P18:PC:DTP:Select only full DTPs for overlapping deletion", "RefUrl": "/notes/1147766"}, {"RefNumber": "1147544", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Interface corrections/unclear descriptions and so on...", "RefUrl": "/notes/1147544"}, {"RefNumber": "1147536", "RefComponent": "BW-BEX-ET", "RefTitle": "Personalization of variables in NW2004s does not work", "RefUrl": "/notes/1147536"}, {"RefNumber": "1147466", "RefComponent": "BW-SYS", "RefTitle": "RSPOR_T_PORTAL: Standard font to create a PDF file", "RefUrl": "/notes/1147466"}, {"RefNumber": "1147365", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon not started (RSCRT 340)", "RefUrl": "/notes/1147365"}, {"RefNumber": "1147326", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:SDL:MON:Request IDoc terminates with error mess. R3 018", "RefUrl": "/notes/1147326"}, {"RefNumber": "1147161", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:MON: Subsequently posting data packages & error message", "RefUrl": "/notes/1147161"}, {"RefNumber": "1147123", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Report QUERY_CHECK may cause deadlocks in RSZ* tables", "RefUrl": "/notes/1147123"}, {"RefNumber": "1146957", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Releasing memory OLAP_CACHE", "RefUrl": "/notes/1146957"}, {"RefNumber": "1146932", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: No warning for indicator \"Duplicate data recs.\"", "RefUrl": "/notes/1146932"}, {"RefNumber": "1146851", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "BI 7.0 (SP 18): Error RSAU 484 when generating update", "RefUrl": "/notes/1146851"}, {"RefNumber": "1146849", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA metadata change: Termination _CHECK_SID_LOCK", "RefUrl": "/notes/1146849"}, {"RefNumber": "1146834", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: RSBATCH_WRITE_PROT_TO_APPLLOG and job log S messages", "RefUrl": "/notes/1146834"}, {"RefNumber": "1146656", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP for RDA does not filter records", "RefUrl": "/notes/1146656"}, {"RefNumber": "1146596", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: DataSources remain after source system deleted", "RefUrl": "/notes/1146596"}, {"RefNumber": "1146590", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:Manage: Dump in function module RSSM_RSMONIPTAB_READ", "RefUrl": "/notes/1146590"}, {"RefNumber": "1146577", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Web service DataSource: Error when assigning InfoPackage", "RefUrl": "/notes/1146577"}, {"RefNumber": "1146316", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Erweiterung des Query-Check", "RefUrl": "/notes/1146316"}, {"RefNumber": "1146242", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No success message when you save a data archiving process", "RefUrl": "/notes/1146242"}, {"RefNumber": "1146094", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "The most current variable values in planning functions", "RefUrl": "/notes/1146094"}, {"RefNumber": "1146030", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSRV - Missing test description for 'Check Time Intervals'", "RefUrl": "/notes/1146030"}, {"RefNumber": "1145990", "RefComponent": "BW-WHM-DST", "RefTitle": "711: Navigator for DTP requests when calling from management", "RefUrl": "/notes/1145990"}, {"RefNumber": "1145975", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "READMODE initial leads to READMODE = A for MultiProviders", "RefUrl": "/notes/1145975"}, {"RefNumber": "1145972", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "No data for non-cumulative key figures & virtual key figures", "RefUrl": "/notes/1145972"}, {"RefNumber": "1145940", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Manage: All requests yellow if auto QM action inactive", "RefUrl": "/notes/1145940"}, {"RefNumber": "1145927", "RefComponent": "BW-WHM-DST-AUT", "RefTitle": "Correction: Error during authorization check for new chains", "RefUrl": "/notes/1145927"}, {"RefNumber": "1145881", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Bursting with characteristic variables for compound char", "RefUrl": "/notes/1145881"}, {"RefNumber": "1145848", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:STATMAN: Performance: Complete deletion of all requests", "RefUrl": "/notes/1145848"}, {"RefNumber": "1145781", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1145781"}, {"RefNumber": "1145725", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Bursting: <PERSON><PERSON> addresses truncated after 40 characters", "RefUrl": "/notes/1145725"}, {"RefNumber": "1145706", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1145706"}, {"RefNumber": "1145664", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error for MultiProv.query that delivers no characteristics", "RefUrl": "/notes/1145664"}, {"RefNumber": "1145610", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "SP18:CCMS:MAs are filled though monitoring is deactivated", "RefUrl": "/notes/1145610"}, {"RefNumber": "1145559", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination in program SAPLRRS2 and form GET_SID-1-", "RefUrl": "/notes/1145559"}, {"RefNumber": "1145554", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP 18) OBJECTS_OBJREF_NOT_ASSIGNED during transport", "RefUrl": "/notes/1145554"}, {"RefNumber": "1145326", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Incorr line feed when loading text from server", "RefUrl": "/notes/1145326"}, {"RefNumber": "1145297", "RefComponent": "BW-SYS-DB", "RefTitle": "Activation of transported InfoCube fails", "RefUrl": "/notes/1145297"}, {"RefNumber": "1145170", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL: Empty mssge dialog box after simulatn during start", "RefUrl": "/notes/1145170"}, {"RefNumber": "1145163", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Confusing screen design for selective deletion", "RefUrl": "/notes/1145163"}, {"RefNumber": "1145108", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Brain A264, A320 or A348 when you generate a query", "RefUrl": "/notes/1145108"}, {"RefNumber": "1145041", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "RSAU 484 when executing InfoPackage", "RefUrl": "/notes/1145041"}, {"RefNumber": "1145014", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Characteristic displayed as compounded even though unique", "RefUrl": "/notes/1145014"}, {"RefNumber": "1144979", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Deactivating attribute display in value help", "RefUrl": "/notes/1144979"}, {"RefNumber": "1144874", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSD_MULTIPROV_CHECK; _CHECK_CMP_MESS-01-", "RefUrl": "/notes/1144874"}, {"RefNumber": "1144702", "RefComponent": "BW-PLA-IP", "RefTitle": "Memory release, additional corrections to Note 1101187", "RefUrl": "/notes/1144702"}, {"RefNumber": "1144574", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Termination when transferring values to input-ready query", "RefUrl": "/notes/1144574"}, {"RefNumber": "1144453", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Entering # for compounded values does not work", "RefUrl": "/notes/1144453"}, {"RefNumber": "1144308", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Parallelization for predefined times", "RefUrl": "/notes/1144308"}, {"RefNumber": "1144253", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Repartitioning: Error when adding partitions", "RefUrl": "/notes/1144253"}, {"RefNumber": "1144131", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when refreshing inconsistent embedded views", "RefUrl": "/notes/1144131"}, {"RefNumber": "1143924", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A125 BRAIN: Field G_S_indicator-Z____nnnn_$W$ unknown", "RefUrl": "/notes/1143924"}, {"RefNumber": "1143904", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:SDL:SMQS:load distribution in data transfer: source->BW", "RefUrl": "/notes/1143904"}, {"RefNumber": "1143887", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Short dump in SAP BI reporting", "RefUrl": "/notes/1143887"}, {"RefNumber": "1143867", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termnatn RSDDK_CHECK_AGGREGATE with SAPSQL_INVALID_TABLENAME", "RefUrl": "/notes/1143867"}, {"RefNumber": "1143771", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Not all variables displayed as text elements", "RefUrl": "/notes/1143771"}, {"RefNumber": "1143745", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Variablenbelegung im Broadcaster anlegen: Personalisierung", "RefUrl": "/notes/1143745"}, {"RefNumber": "1143710", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Delete requests from master data/text InfoProviders", "RefUrl": "/notes/1143710"}, {"RefNumber": "1143698", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Branching from ALV to BEx: Currencies/units not correct", "RefUrl": "/notes/1143698"}, {"RefNumber": "1143616", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: No time dependence (SAP, BI source system)", "RefUrl": "/notes/1143616"}, {"RefNumber": "1143557", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hierarchy set so that remaining nodes are suppressed", "RefUrl": "/notes/1143557"}, {"RefNumber": "1143465", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving write-optimized DataStore objects", "RefUrl": "/notes/1143465"}, {"RefNumber": "1143411", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA, virtual key figures, characteristics", "RefUrl": "/notes/1143411"}, {"RefNumber": "1143392", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 does not display any values (CX_RS_SQL_ERROR_SILENT)", "RefUrl": "/notes/1143392"}, {"RefNumber": "1143300", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "NetWeaver 2004s BI Web Appl.Designer:SET_EXCEPTION inpt help", "RefUrl": "/notes/1143300"}, {"RefNumber": "1143281", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:BAPI_IPAK_CHANGE, selection fld with NUMC,conv exit", "RefUrl": "/notes/1143281"}, {"RefNumber": "1143176", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions; formulas; reference data", "RefUrl": "/notes/1143176"}, {"RefNumber": "1143105", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:MDT: Faster deletion of too many master data requests", "RefUrl": "/notes/1143105"}, {"RefNumber": "1142942", "RefComponent": "BW-BEX-OT", "RefTitle": "GETWA_NOT_ASSIGNED_RANGE for query on MultiProvider", "RefUrl": "/notes/1142942"}, {"RefNumber": "1142738", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cumulative query with AV1 or AV2 aggregation and sel opt", "RefUrl": "/notes/1142738"}, {"RefNumber": "1142638", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "DBIF_RSQL_INVALID_RSQL in CL_RSDMD_LOOKUP_MASTER_DATA", "RefUrl": "/notes/1142638"}, {"RefNumber": "1142550", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0 (SP 18) Master data not read correctly", "RefUrl": "/notes/1142550"}, {"RefNumber": "1142507", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSDRC_MULTIPROV; form _SET_SFC_KEYRET-01-", "RefUrl": "/notes/1142507"}, {"RefNumber": "1142481", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: GEN_BRANCHOFFSET_LIMIT_REACHED for segm. DataSce", "RefUrl": "/notes/1142481"}, {"RefNumber": "1142252", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error SAPSQL_AMBIGUOUS_FIELDNAME during extraction", "RefUrl": "/notes/1142252"}, {"RefNumber": "1142143", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "3.x DataSource: \"A\" version for hierarchy without intervals", "RefUrl": "/notes/1142143"}, {"RefNumber": "1141969", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Repeated saving of planning data", "RefUrl": "/notes/1141969"}, {"RefNumber": "1141933", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Too much data in a query/termination in MEGA_SORT_14-01-", "RefUrl": "/notes/1141933"}, {"RefNumber": "1141917", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Queries on DataStore objects with star_transformation HINT", "RefUrl": "/notes/1141917"}, {"RefNumber": "1141850", "RefComponent": "BW-BCT-TCT", "RefTitle": "\"Not assigned\" time calculated from query RUNTIME", "RefUrl": "/notes/1141850"}, {"RefNumber": "1141798", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Analysis of RRMX/RSAH_Launch_Excel problems", "RefUrl": "/notes/1141798"}, {"RefNumber": "1141750", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Do not use RSRV test \"Rebuild all master data indexes...\"", "RefUrl": "/notes/1141750"}, {"RefNumber": "1141716", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:DSO: Write-optimized DSO and overtaking requests", "RefUrl": "/notes/1141716"}, {"RefNumber": "1141585", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Analysis authorizations in the input help are ignored", "RefUrl": "/notes/1141585"}, {"RefNumber": "1141576", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Cannot execute search; hierarchy is too large", "RefUrl": "/notes/1141576"}, {"RefNumber": "1141369", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Long runtime occurs in method CL_RSR_RRK0_PARTITION; S1_2_S2", "RefUrl": "/notes/1141369"}, {"RefNumber": "1141361", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:STATMAN: Multiple deletion of a request; dump", "RefUrl": "/notes/1141361"}, {"RefNumber": "1141331", "RefComponent": "BW-BCT-TCT", "RefTitle": "OLAP statistics records assigned to incorrect HANDLEID", "RefUrl": "/notes/1141331"}, {"RefNumber": "1141314", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Routine & formula IDs changed during replication", "RefUrl": "/notes/1141314"}, {"RefNumber": "1141260", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Transfer rules PERFORM_PARAMETER_MISSING", "RefUrl": "/notes/1141260"}, {"RefNumber": "1141158", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation rule test: Generated for formulas", "RefUrl": "/notes/1141158"}, {"RefNumber": "1141136", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 18 BIA revision: Compatibility check", "RefUrl": "/notes/1141136"}, {"RefNumber": "1141039", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The system displays only 16 periods for 0FISCPER3", "RefUrl": "/notes/1141039"}, {"RefNumber": "1141033", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Sorting by selection and totals suppression", "RefUrl": "/notes/1141033"}, {"RefNumber": "1141004", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in BEx Analyzer dropdown item", "RefUrl": "/notes/1141004"}, {"RefNumber": "1140969", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor: Icon for check long text not displayed", "RefUrl": "/notes/1140969"}, {"RefNumber": "1140837", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18: SDL: <PERSON><PERSON><PERSON> und individual selection in the scheduler", "RefUrl": "/notes/1140837"}, {"RefNumber": "1140825", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in transaction RSPRECADMIN", "RefUrl": "/notes/1140825"}, {"RefNumber": "1140808", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions executed with incorrect selection", "RefUrl": "/notes/1140808"}, {"RefNumber": "1140799", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RSAR 051 when transporting transformations", "RefUrl": "/notes/1140799"}, {"RefNumber": "1140737", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Sorting by a value column does not work", "RefUrl": "/notes/1140737"}, {"RefNumber": "1140701", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Creation of BIA index terminates with X message", "RefUrl": "/notes/1140701"}, {"RefNumber": "1140685", "RefComponent": "BW-WHM-DST", "RefTitle": "Message RSAR 438: Source systems in long text misleading", "RefUrl": "/notes/1140685"}, {"RefNumber": "1140672", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Display and text type not adhered to in input help", "RefUrl": "/notes/1140672"}, {"RefNumber": "1140535", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Error when editing the updaterule - assigned package attr.", "RefUrl": "/notes/1140535"}, {"RefNumber": "1140458", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination: Program SAPLRRS2; form FAC_CHAVLVAR_REPLACE-1-", "RefUrl": "/notes/1140458"}, {"RefNumber": "1140323", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in CL_RSDM_READ_MASTER_DATA _FILL_TXT_N-03-", "RefUrl": "/notes/1140323"}, {"RefNumber": "1140243", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "RSECADMIN: Hierarchy is incorrect after hierarchy search", "RefUrl": "/notes/1140243"}, {"RefNumber": "1140214", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18): Incorrect red message in the DTP monitor", "RefUrl": "/notes/1140214"}, {"RefNumber": "1140051", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18): Dump MESSAGE_TYPE_X during activation", "RefUrl": "/notes/1140051"}, {"RefNumber": "1139924", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18): System names converted incorrectly", "RefUrl": "/notes/1139924"}, {"RefNumber": "1139836", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Link generation: Deletion of variable assignment", "RefUrl": "/notes/1139836"}, {"RefNumber": "1139821", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Incorrect lock on RSICCONT for text table update", "RefUrl": "/notes/1139821"}, {"RefNumber": "1139791", "RefComponent": "BW-BEX-OT", "RefTitle": "Statistics: Wait times in F4 counted for OLAP initialization", "RefUrl": "/notes/1139791"}, {"RefNumber": "1139770", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Key figure properties are displayed incorrectly", "RefUrl": "/notes/1139770"}, {"RefNumber": "1139749", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in search framework in workbooks", "RefUrl": "/notes/1139749"}, {"RefNumber": "1139617", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:DSO: Write-opt: No loading for MOVE data as for cube", "RefUrl": "/notes/1139617"}, {"RefNumber": "1139586", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "BW function RSDDK_AGGRCAT_SORT does not supply NUM_ENTRIES", "RefUrl": "/notes/1139586"}, {"RefNumber": "1139585", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Performance optimization: Selects on table RSZGLOBV", "RefUrl": "/notes/1139585"}, {"RefNumber": "1139448", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "ASSERTION_FAILED in CL_RSAR_PSA _UPDATE_DIRECTORY_TABLES", "RefUrl": "/notes/1139448"}, {"RefNumber": "1139318", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "COMMIT executed in function mod. RSNDI_MD_ATTRIBUTES_UPDATE", "RefUrl": "/notes/1139318"}, {"RefNumber": "1138948", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "DTP Messages written to Application Log", "RefUrl": "/notes/1138948"}, {"RefNumber": "1138922", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Termination for delete request from non-cumulative InfoCube", "RefUrl": "/notes/1138922"}, {"RefNumber": "1138874", "RefComponent": "BW-BCT-TCT-BAC", "RefTitle": "BIAC: User wait times no longer included in Front End times", "RefUrl": "/notes/1138874"}, {"RefNumber": "1138865", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Time stamp missing from group RSAPO (delta cache)", "RefUrl": "/notes/1138865"}, {"RefNumber": "1138848", "RefComponent": "FIN-SEM-BCS", "RefTitle": "X299 Brain in CL_RSR_RRK0_RQTS; form _DELTA_MINUS_AGGR-06-", "RefUrl": "/notes/1138848"}, {"RefNumber": "1138708", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Unauthorized data is displayed: \"Not assigned\" (#)", "RefUrl": "/notes/1138708"}, {"RefNumber": "1138601", "RefComponent": "BW-EI-APD", "RefTitle": "BL252 Message number 999999 reached. Log is full", "RefUrl": "/notes/1138601"}, {"RefNumber": "1138513", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Check of intervals in input help incorrect", "RefUrl": "/notes/1138513"}, {"RefNumber": "1138410", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Manage:Rpt for deleting zombie requests from data target", "RefUrl": "/notes/1138410"}, {"RefNumber": "1138408", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "<PERSON>in External-Key mehr bei allen Indizes im BIA", "RefUrl": "/notes/1138408"}, {"RefNumber": "1138361", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Different data for bookmark with pre-query", "RefUrl": "/notes/1138361"}, {"RefNumber": "1138344", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Dump during check in the modeler", "RefUrl": "/notes/1138344"}, {"RefNumber": "1138339", "RefComponent": "BW", "RefTitle": "RSEURO conversion of key figs. w/ NO1 aggregation terminates", "RefUrl": "/notes/1138339"}, {"RefNumber": "1138199", "RefComponent": "BW-BEX-OT", "RefTitle": "X299 BRAIN in CL_RSD_MULTIPROV; form GET_PART_IOBJNM-01-", "RefUrl": "/notes/1138199"}, {"RefNumber": "1138172", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "CX_SY_CONVERSION_OVERFLOW in RRBA_CONVERT_PACKED_NUMBER", "RefUrl": "/notes/1138172"}, {"RefNumber": "1138161", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RSBK 223: \"Status 'Processed with Errors' (user ..)\"", "RefUrl": "/notes/1138161"}, {"RefNumber": "1138080", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: WO-DSO: Archiving the write-optimized DataStore object", "RefUrl": "/notes/1138080"}, {"RefNumber": "1137994", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Re-optimizing a query", "RefUrl": "/notes/1137994"}, {"RefNumber": "1137674", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT results displ. terminates for structures w/ NUMC fields", "RefUrl": "/notes/1137674"}, {"RefNumber": "1137553", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Analyseberechtigungen funktionieren nicht", "RefUrl": "/notes/1137553"}, {"RefNumber": "1137543", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation prog:Srce code for InfoObject routine missing", "RefUrl": "/notes/1137543"}, {"RefNumber": "1137447", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "ASSERTION_FAILED when the transformation is activated", "RefUrl": "/notes/1137447"}, {"RefNumber": "1137299", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Views are not displayed in query WHERE-USED list", "RefUrl": "/notes/1137299"}, {"RefNumber": "1137243", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query terminates: CL_RSR_HIERARCHY_BINCL BU_SUBSET_CHECK-3-", "RefUrl": "/notes/1137243"}, {"RefNumber": "1137210", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Estimation of index time in RSRV is incorrect", "RefUrl": "/notes/1137210"}, {"RefNumber": "1137159", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Check missing for Content transfer", "RefUrl": "/notes/1137159"}, {"RefNumber": "1137084", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Processing not adjusted correctly to new TAREA concept", "RefUrl": "/notes/1137084"}, {"RefNumber": "1137081", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT 'NULL' object reference when exiting CATT wizard", "RefUrl": "/notes/1137081"}, {"RefNumber": "1136945", "RefComponent": "BW-WHM-AWB", "RefTitle": "Search for InfoObject terminates with dump (BI7.0/SPS16)", "RefUrl": "/notes/1136945"}, {"RefNumber": "1136925", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18) Path in destination not checked", "RefUrl": "/notes/1136925"}, {"RefNumber": "1136924", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Dump during content creation for conversion DS", "RefUrl": "/notes/1136924"}, {"RefNumber": "1136735", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query setting not set to InfoProivder setting", "RefUrl": "/notes/1136735"}, {"RefNumber": "1136469", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P18:DSO:Activating a DSO and many overlapping checks", "RefUrl": "/notes/1136469"}, {"RefNumber": "1136053", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:DWWB: Renaming and saving an InfoPackage", "RefUrl": "/notes/1136053"}, {"RefNumber": "1136000", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P18:PC:PSA: Error in PSA process for multiple InfoPackages", "RefUrl": "/notes/1136000"}, {"RefNumber": "1135996", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:RDA: No real-time InfoPackages w/o access methods", "RefUrl": "/notes/1135996"}, {"RefNumber": "1135954", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Non-authorized attributes displayed in F4 help", "RefUrl": "/notes/1135954"}, {"RefNumber": "1135887", "RefComponent": "BW-BEX-OT", "RefTitle": "Max. value for fiscal period is 9999999 instead of 9999012", "RefUrl": "/notes/1135887"}, {"RefNumber": "1135045", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Job BI_WRITE_PROT_TO_APPLLOG is scheduled several times", "RefUrl": "/notes/1135045"}, {"RefNumber": "1134688", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Allowed type changes with near-line archiving", "RefUrl": "/notes/1134688"}, {"RefNumber": "1134179", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Dynamic determination of time interval in input help", "RefUrl": "/notes/1134179"}, {"RefNumber": "1132269", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "\"Start Automatically\" option for delete jobs during ADK DAP", "RefUrl": "/notes/1132269"}, {"RefNumber": "1124407", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Lookup API: Problem w/differing field names and aggregation", "RefUrl": "/notes/1124407"}, {"RefNumber": "1123408", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Incorrect data with KIDSEL and formula exception aggregation", "RefUrl": "/notes/1123408"}, {"RefNumber": "1120358", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Upgrade from 3.x to 7.x - Exceptions converted incorrectly", "RefUrl": "/notes/1120358"}, {"RefNumber": "1116005", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Long runtimes for consistency checks of X or Y tables", "RefUrl": "/notes/1116005"}, {"RefNumber": "1110324", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Irrelevant message after attempt to delete Master Data", "RefUrl": "/notes/1110324"}, {"RefNumber": "1105334", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: \"Max. statement length exceeded\"", "RefUrl": "/notes/1105334"}, {"RefNumber": "1094799", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Corrections in the BEx Analyzer server runtime", "RefUrl": "/notes/1094799"}, {"RefNumber": "1067027", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Initial entries from master data and texts are not extracted", "RefUrl": "/notes/1067027"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1143710", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Delete requests from master data/text InfoProviders", "RefUrl": "/notes/1143710 "}, {"RefNumber": "1143411", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA, virtual key figures, characteristics", "RefUrl": "/notes/1143411 "}, {"RefNumber": "1164372", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Personalized variables are displayed in 3.X workbook", "RefUrl": "/notes/1164372 "}, {"RefNumber": "1138874", "RefComponent": "BW-BCT-TCT-BAC", "RefTitle": "BIAC: User wait times no longer included in Front End times", "RefUrl": "/notes/1138874 "}, {"RefNumber": "1158831", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Calculation before aggregation with BIA and aggregates", "RefUrl": "/notes/1158831 "}, {"RefNumber": "1152453", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination generating InfoCube write program", "RefUrl": "/notes/1152453 "}, {"RefNumber": "1162738", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Exception reporting - Creating alerts: Error BL 207", "RefUrl": "/notes/1162738 "}, {"RefNumber": "1139586", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "BW function RSDDK_AGGRCAT_SORT does not supply NUM_ENTRIES", "RefUrl": "/notes/1139586 "}, {"RefNumber": "1140323", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in CL_RSDM_READ_MASTER_DATA _FILL_TXT_N-03-", "RefUrl": "/notes/1140323 "}, {"RefNumber": "1148336", "RefComponent": "BW-BEX-ET-WEB-GRAPH", "RefTitle": "Enhanced correction report for charts (7.X)", "RefUrl": "/notes/1148336 "}, {"RefNumber": "1155245", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy Activation Dumps", "RefUrl": "/notes/1155245 "}, {"RefNumber": "1156681", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Enhancements in MultiProvider hint RSRMULTIPROVHINT", "RefUrl": "/notes/1156681 "}, {"RefNumber": "1152387", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Syntax error in CL_RSROA_BI_QUERY, method SET_QUERYDATA", "RefUrl": "/notes/1152387 "}, {"RefNumber": "1135996", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:RDA: No real-time InfoPackages w/o access methods", "RefUrl": "/notes/1135996 "}, {"RefNumber": "1159260", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Counter for BIA read accesses", "RefUrl": "/notes/1159260 "}, {"RefNumber": "1139770", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Key figure properties are displayed incorrectly", "RefUrl": "/notes/1139770 "}, {"RefNumber": "1150341", "RefComponent": "BW-WHM-DST", "RefTitle": "BYY: Additional secondary index on RSSELDONE for BYD", "RefUrl": "/notes/1150341 "}, {"RefNumber": "1150831", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "BI 7.00 EHP 1: Delete flexible PSA", "RefUrl": "/notes/1150831 "}, {"RefNumber": "1138708", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Unauthorized data is displayed: \"Not assigned\" (#)", "RefUrl": "/notes/1138708 "}, {"RefNumber": "1151859", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "DEL_UNUSED_DIMS is obsolete but still read during deletion", "RefUrl": "/notes/1151859 "}, {"RefNumber": "1159610", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Exception/cond. transferred incorrectly in Query Designer", "RefUrl": "/notes/1159610 "}, {"RefNumber": "1156599", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Correction: 3.x DataSources are not collected", "RefUrl": "/notes/1156599 "}, {"RefNumber": "1152929", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Log enhancements for RRMX", "RefUrl": "/notes/1152929 "}, {"RefNumber": "1094799", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Corrections in the BEx Analyzer server runtime", "RefUrl": "/notes/1094799 "}, {"RefNumber": "1170818", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Additional trace when deleting jobs and logs", "RefUrl": "/notes/1170818 "}, {"RefNumber": "1155235", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination w/IF_RSR_VAR_RUNTIME~GET_DEPHIER_FOR_VARIABLE-1-", "RefUrl": "/notes/1155235 "}, {"RefNumber": "1172047", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in SAPLRRK0, form CACHE_CREATE_NEW_FF-01-", "RefUrl": "/notes/1172047 "}, {"RefNumber": "1162365", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Collective note: BIA and incorrect data", "RefUrl": "/notes/1162365 "}, {"RefNumber": "1138601", "RefComponent": "BW-EI-APD", "RefTitle": "BL252 Message number 999999 reached. Log is full", "RefUrl": "/notes/1138601 "}, {"RefNumber": "1332791", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI 7.0 SP 22 BIA Revision Kompatiblitätsprüfung", "RefUrl": "/notes/1332791 "}, {"RefNumber": "1259786", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 21 BIA: Revision: Compatibility check", "RefUrl": "/notes/1259786 "}, {"RefNumber": "1166051", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data object size for data archiving process is too large", "RefUrl": "/notes/1166051 "}, {"RefNumber": "1143557", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Hierarchy set so that remaining nodes are suppressed", "RefUrl": "/notes/1143557 "}, {"RefNumber": "1144131", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when refreshing inconsistent embedded views", "RefUrl": "/notes/1144131 "}, {"RefNumber": "1140969", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor: Icon for check long text not displayed", "RefUrl": "/notes/1140969 "}, {"RefNumber": "1147326", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:SDL:MON:Request IDoc terminates with error mess. R3 018", "RefUrl": "/notes/1147326 "}, {"RefNumber": "1165172", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Jobs not deallocated during deletion transport", "RefUrl": "/notes/1165172 "}, {"RefNumber": "1298444", "RefComponent": "BC-DWB-CEX", "RefTitle": "Error when executing transformation with formula", "RefUrl": "/notes/1298444 "}, {"RefNumber": "1154626", "RefComponent": "BW-BEX", "RefTitle": "New checks available in RSRV", "RefUrl": "/notes/1154626 "}, {"RefNumber": "1147768", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: BI-CCMS dispatcher runs in the incorrect client", "RefUrl": "/notes/1147768 "}, {"RefNumber": "1143904", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:SDL:SMQS:load distribution in data transfer: source->BW", "RefUrl": "/notes/1143904 "}, {"RefNumber": "1151694", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Changes are not displayed", "RefUrl": "/notes/1151694 "}, {"RefNumber": "1162433", "RefComponent": "BW-WHM-DST", "RefTitle": "Error 18 in update (Support Package 17)", "RefUrl": "/notes/1162433 "}, {"RefNumber": "1166594", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cum. key figs incorrect w/ init. non-cum. excep. aggreg.", "RefUrl": "/notes/1166594 "}, {"RefNumber": "1277919", "RefComponent": "BW-BEX-ET-BC-PREC", "RefTitle": "Termination in report RS_PREC_PLAN", "RefUrl": "/notes/1277919 "}, {"RefNumber": "1141576", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Cannot execute search; hierarchy is too large", "RefUrl": "/notes/1141576 "}, {"RefNumber": "1160927", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 19: BIA: Revision: Compatibility check", "RefUrl": "/notes/1160927 "}, {"RefNumber": "1145554", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (SP 18) OBJECTS_OBJREF_NOT_ASSIGNED during transport", "RefUrl": "/notes/1145554 "}, {"RefNumber": "1145297", "RefComponent": "BW-SYS-DB", "RefTitle": "Activation of transported InfoCube fails", "RefUrl": "/notes/1145297 "}, {"RefNumber": "1143887", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Short dump in SAP BI reporting", "RefUrl": "/notes/1143887 "}, {"RefNumber": "1110324", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Irrelevant message after attempt to delete Master Data", "RefUrl": "/notes/1110324 "}, {"RefNumber": "1235467", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 20: BIA: Revision: Compatibility check", "RefUrl": "/notes/1235467 "}, {"RefNumber": "1138922", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Termination for delete request from non-cumulative InfoCube", "RefUrl": "/notes/1138922 "}, {"RefNumber": "1259892", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Input help (F4) termination when saving favorites", "RefUrl": "/notes/1259892 "}, {"RefNumber": "1259934", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Saving favorites does not work for compounding", "RefUrl": "/notes/1259934 "}, {"RefNumber": "1259935", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Variable intervals transferred incorrectly from history", "RefUrl": "/notes/1259935 "}, {"RefNumber": "1149556", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Characteristic ... has no master data for \"...\"", "RefUrl": "/notes/1149556 "}, {"RefNumber": "1116005", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Long runtimes for consistency checks of X or Y tables", "RefUrl": "/notes/1116005 "}, {"RefNumber": "1142550", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0 (SP 18) Master data not read correctly", "RefUrl": "/notes/1142550 "}, {"RefNumber": "1160949", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Syntax error when activating transfer rule", "RefUrl": "/notes/1160949 "}, {"RefNumber": "1155639", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P20: P18:DTP:PC: Dump \"sid_too_low\" for parallel DTPs", "RefUrl": "/notes/1155639 "}, {"RefNumber": "1145972", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "No data for non-cumulative key figures & virtual key figures", "RefUrl": "/notes/1145972 "}, {"RefNumber": "1151462", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Not all fields are updated", "RefUrl": "/notes/1151462 "}, {"RefNumber": "1230306", "RefComponent": "BW-BEX-ET", "RefTitle": "HOTFIX TEMPLATE: SAP NetWeaver 7.X BI Frontend in 7.10", "RefUrl": "/notes/1230306 "}, {"RefNumber": "1159481", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA change run: Incorrect data after upgrade to >=BI SP16", "RefUrl": "/notes/1159481 "}, {"RefNumber": "1161224", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "F4 returns incorrect values", "RefUrl": "/notes/1161224 "}, {"RefNumber": "1138408", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "<PERSON>in External-Key mehr bei allen Indizes im BIA", "RefUrl": "/notes/1138408 "}, {"RefNumber": "1235934", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "You can change reusable filters without authorization", "RefUrl": "/notes/1235934 "}, {"RefNumber": "1159216", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Logical documents not displayed after upgrade to 7.X", "RefUrl": "/notes/1159216 "}, {"RefNumber": "1159976", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in workbook content activation", "RefUrl": "/notes/1159976 "}, {"RefNumber": "1143392", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 does not display any values (CX_RS_SQL_ERROR_SILENT)", "RefUrl": "/notes/1143392 "}, {"RefNumber": "1157803", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0 (Support Package 18): Dump when saving or activating", "RefUrl": "/notes/1157803 "}, {"RefNumber": "1162790", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Program RSDDK_CHECK_AGGREGATE for key figures of type FLTP", "RefUrl": "/notes/1162790 "}, {"RefNumber": "1162290", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query displays incorrect data; execution in \"Safe Mode\"", "RefUrl": "/notes/1162290 "}, {"RefNumber": "1169074", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization check with hierarchies takes too long", "RefUrl": "/notes/1169074 "}, {"RefNumber": "1160415", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Dumps in the function RSZ_P_DB_ELT_SET_IMMEDIATELY", "RefUrl": "/notes/1160415 "}, {"RefNumber": "1156948", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: Unicode indicator incorrect", "RefUrl": "/notes/1156948 "}, {"RefNumber": "1168770", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replaced by time-dependent characteristics", "RefUrl": "/notes/1168770 "}, {"RefNumber": "1136000", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P18:PC:PSA: Error in PSA process for multiple InfoPackages", "RefUrl": "/notes/1136000 "}, {"RefNumber": "1141369", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Long runtime occurs in method CL_RSR_RRK0_PARTITION; S1_2_S2", "RefUrl": "/notes/1141369 "}, {"RefNumber": "1149521", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error message RSAOLTP 18 during data request", "RefUrl": "/notes/1149521 "}, {"RefNumber": "1156212", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Master Data load hangs during update of timestamp", "RefUrl": "/notes/1156212 "}, {"RefNumber": "1230262", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Control characters cause errors in input help", "RefUrl": "/notes/1230262 "}, {"RefNumber": "1139791", "RefComponent": "BW-BEX-OT", "RefTitle": "Statistics: Wait times in F4 counted for OLAP initialization", "RefUrl": "/notes/1139791 "}, {"RefNumber": "1163970", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: \"Always\" not executed if status is red", "RefUrl": "/notes/1163970 "}, {"RefNumber": "1156602", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "MultiProvider hint not used", "RefUrl": "/notes/1156602 "}, {"RefNumber": "1138344", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Dump during check in the modeler", "RefUrl": "/notes/1138344 "}, {"RefNumber": "1163320", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Incorrect text in context menu of BEx Analyzer", "RefUrl": "/notes/1163320 "}, {"RefNumber": "1152186", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 Brain in CL_RSR_FIPT_SID2VAL_PREFETCH; TRANSFORM-01-", "RefUrl": "/notes/1152186 "}, {"RefNumber": "1152136", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error during master data reading (ERROR_IN_MD_BUFFERING)", "RefUrl": "/notes/1152136 "}, {"RefNumber": "1175354", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "RFC_MODIFY_R3_DESTINATION does not execute a COMMIT", "RefUrl": "/notes/1175354 "}, {"RefNumber": "1142942", "RefComponent": "BW-BEX-OT", "RefTitle": "GETWA_NOT_ASSIGNED_RANGE for query on MultiProvider", "RefUrl": "/notes/1142942 "}, {"RefNumber": "1161254", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA: Master data reorganization for Y tables is not adjusted", "RefUrl": "/notes/1161254 "}, {"RefNumber": "1153235", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Short dump when you import shadow version (SHDS)", "RefUrl": "/notes/1153235 "}, {"RefNumber": "1169659", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process variants not entered by BDLS", "RefUrl": "/notes/1169659 "}, {"RefNumber": "1163116", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Error in MultiProvider with stock cubes", "RefUrl": "/notes/1163116 "}, {"RefNumber": "1137994", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Re-optimizing a query", "RefUrl": "/notes/1137994 "}, {"RefNumber": "1180155", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "F4 dialog box truncated when opened", "RefUrl": "/notes/1180155 "}, {"RefNumber": "1158871", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18) Problems when loading to dest. using DB table", "RefUrl": "/notes/1158871 "}, {"RefNumber": "1165359", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P19:EHP1:PC: Repeat for InfoPackage not executed", "RefUrl": "/notes/1165359 "}, {"RefNumber": "1145940", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Manage: All requests yellow if auto QM action inactive", "RefUrl": "/notes/1145940 "}, {"RefNumber": "1178989", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "WAD 7.0: Div tag warning messages", "RefUrl": "/notes/1178989 "}, {"RefNumber": "1163009", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Using BIA in check center even if it has status \"Inactive\"", "RefUrl": "/notes/1163009 "}, {"RefNumber": "1178266", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Application error:Language Dependent Text", "RefUrl": "/notes/1178266 "}, {"RefNumber": "1155195", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Incorrect query results with restricted key figures", "RefUrl": "/notes/1155195 "}, {"RefNumber": "1124407", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Lookup API: Problem w/differing field names and aggregation", "RefUrl": "/notes/1124407 "}, {"RefNumber": "1154733", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Filter check in planning returns incorrect result", "RefUrl": "/notes/1154733 "}, {"RefNumber": "1149558", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: DTP: No selection conditions for DTP request", "RefUrl": "/notes/1149558 "}, {"RefNumber": "1161111", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect (plan) values in very specific situation", "RefUrl": "/notes/1161111 "}, {"RefNumber": "1140672", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Display and text type not adhered to in input help", "RefUrl": "/notes/1140672 "}, {"RefNumber": "1105334", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: \"Max. statement length exceeded\"", "RefUrl": "/notes/1105334 "}, {"RefNumber": "1166294", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSR, form GET_CHANMID-02-", "RefUrl": "/notes/1166294 "}, {"RefNumber": "1141933", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Too much data in a query/termination in MEGA_SORT_14-01-", "RefUrl": "/notes/1141933 "}, {"RefNumber": "1171296", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Destination to the BW is not checked", "RefUrl": "/notes/1171296 "}, {"RefNumber": "1165626", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Invalidation of reload request terminates with RSDA 337", "RefUrl": "/notes/1165626 "}, {"RefNumber": "1143465", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving write-optimized DataStore objects", "RefUrl": "/notes/1143465 "}, {"RefNumber": "1166052", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Repair deficiencies of OLAP cache in new cache", "RefUrl": "/notes/1166052 "}, {"RefNumber": "1149545", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18): Open hub destination: Check is incomplete", "RefUrl": "/notes/1149545 "}, {"RefNumber": "1152582", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Classification of messages", "RefUrl": "/notes/1152582 "}, {"RefNumber": "1155856", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: TOO_MANY_ERRORS if exception handling is off", "RefUrl": "/notes/1155856 "}, {"RefNumber": "1172176", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction of minor usability problem", "RefUrl": "/notes/1172176 "}, {"RefNumber": "1155382", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Restarting cancelled broadcast settings", "RefUrl": "/notes/1155382 "}, {"RefNumber": "1169453", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSR_MULTIPROV_CHECK: Messages are repeated", "RefUrl": "/notes/1169453 "}, {"RefNumber": "1147984", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA random query check terminates with error message", "RefUrl": "/notes/1147984 "}, {"RefNumber": "1137084", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT: Processing not adjusted correctly to new TAREA concept", "RefUrl": "/notes/1137084 "}, {"RefNumber": "1137081", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT 'NULL' object reference when exiting CATT wizard", "RefUrl": "/notes/1137081 "}, {"RefNumber": "1137674", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT results displ. terminates for structures w/ NUMC fields", "RefUrl": "/notes/1137674 "}, {"RefNumber": "1163757", "RefComponent": "SCM-APO-FCS", "RefTitle": "APO: Writing to aggreates fails", "RefUrl": "/notes/1163757 "}, {"RefNumber": "1162968", "RefComponent": "BW-WHM", "RefTitle": "Improving message BRAIN 290", "RefUrl": "/notes/1162968 "}, {"RefNumber": "1140685", "RefComponent": "BW-WHM-DST", "RefTitle": "Message RSAR 438: Source systems in long text misleading", "RefUrl": "/notes/1140685 "}, {"RefNumber": "1161967", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA shadow index: Enhancement of analysis option", "RefUrl": "/notes/1161967 "}, {"RefNumber": "1165128", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect elimination of internal business vol.: Read mode X", "RefUrl": "/notes/1165128 "}, {"RefNumber": "1162324", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Virtual referencing key figure does not display any data", "RefUrl": "/notes/1162324 "}, {"RefNumber": "1157947", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P18:7.01:Background repair for delta requests in background", "RefUrl": "/notes/1157947 "}, {"RefNumber": "1165614", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Formula variables that are replaced from attribute display 0", "RefUrl": "/notes/1165614 "}, {"RefNumber": "1166664", "RefComponent": "BW-BEX-OT-ISET", "RefTitle": "System error in program CL_RSR and form GET_CHANMID-02-", "RefUrl": "/notes/1166664 "}, {"RefNumber": "1166658", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Duplicate records in BIA dimension index", "RefUrl": "/notes/1166658 "}, {"RefNumber": "1155839", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RSAR 535 when you execute formula function DATE_FISCPER", "RefUrl": "/notes/1155839 "}, {"RefNumber": "1159011", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:SDL:PC: Repeat request possible with process chain", "RefUrl": "/notes/1159011 "}, {"RefNumber": "1141136", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI Support Package 18 BIA revision: Compatibility check", "RefUrl": "/notes/1141136 "}, {"RefNumber": "1166297", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Condition on a structure element with date key figure", "RefUrl": "/notes/1166297 "}, {"RefNumber": "1165514", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: OBJECTS_OBJREF_NOT_ASSIGNED in CL_RSPLS_ALVL", "RefUrl": "/notes/1165514 "}, {"RefNumber": "1160125", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SP18:To Display string fields using Filter option in PSA", "RefUrl": "/notes/1160125 "}, {"RefNumber": "1160247", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BIA index incorrect after cancelation request", "RefUrl": "/notes/1160247 "}, {"RefNumber": "1166237", "RefComponent": "BW-BEX-OT", "RefTitle": "BRAIN X299 in class CL_RSD_multiprov, form Factory-02-", "RefUrl": "/notes/1166237 "}, {"RefNumber": "1164864", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in program SAPLRRK0; form LRECH_AGGR_ELSE-01-", "RefUrl": "/notes/1164864 "}, {"RefNumber": "1166241", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSR, form GET_COB_PRO-01-", "RefUrl": "/notes/1166241 "}, {"RefNumber": "1163991", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA - DBMAN015 - Message not required for BIA", "RefUrl": "/notes/1163991 "}, {"RefNumber": "1134179", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Dynamic determination of time interval in input help", "RefUrl": "/notes/1134179 "}, {"RefNumber": "1165627", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Bursting: Process terminates if error occurs for recipient", "RefUrl": "/notes/1165627 "}, {"RefNumber": "1153041", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "falsche Daten: Ausnahmeaggreagtion + temporaler <PERSON>er. Join", "RefUrl": "/notes/1153041 "}, {"RefNumber": "1165520", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPLRRK0 and form RGC_PFLEGEN_03-02-", "RefUrl": "/notes/1165520 "}, {"RefNumber": "1165194", "RefComponent": "BW-SYS-DB-MGR", "RefTitle": "Heterogeneous system integration RS_BW_POST_MIGRATION", "RefUrl": "/notes/1165194 "}, {"RefNumber": "1165008", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Display attribute \"Validity Interval\" not available", "RefUrl": "/notes/1165008 "}, {"RefNumber": "1164386", "RefComponent": "BW-PLA-IP", "RefTitle": "BRAIN 299 in CL_RSD_MULTIPROV; GET_PART_IOBJNM-03-", "RefUrl": "/notes/1164386 "}, {"RefNumber": "1164704", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No conversion into SID if parent defined by selection option", "RefUrl": "/notes/1164704 "}, {"RefNumber": "1145108", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Brain A264, A320 or A348 when you generate a query", "RefUrl": "/notes/1145108 "}, {"RefNumber": "1141331", "RefComponent": "BW-BCT-TCT", "RefTitle": "OLAP statistics records assigned to incorrect HANDLEID", "RefUrl": "/notes/1141331 "}, {"RefNumber": "1163099", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Manage:Red requests in administration due to QM action", "RefUrl": "/notes/1163099 "}, {"RefNumber": "1163551", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Incorrect data in a specific situation", "RefUrl": "/notes/1163551 "}, {"RefNumber": "1146851", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "BI 7.0 (SP 18): Error RSAU 484 when generating update", "RefUrl": "/notes/1146851 "}, {"RefNumber": "1157796", "RefComponent": "BW-WHM", "RefTitle": "Characteristic 0REQUEST must reference 0REQUID", "RefUrl": "/notes/1157796 "}, {"RefNumber": "1164361", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination ASSIGN_LENGTH_NEGATIVE program \"SAPLRRSI\"", "RefUrl": "/notes/1164361 "}, {"RefNumber": "1149337", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Main memory consumption and planning functions", "RefUrl": "/notes/1149337 "}, {"RefNumber": "1164189", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "X299 BRAIN in CL_RSR_RRK0_PARTITION; FLUSH_CACHE-03-", "RefUrl": "/notes/1164189 "}, {"RefNumber": "1163670", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: BATCH: Server/host/group not transferred from default", "RefUrl": "/notes/1163670 "}, {"RefNumber": "1163928", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P18:PSA:DS:REQARCH: Posting request from PSA using scheduler", "RefUrl": "/notes/1163928 "}, {"RefNumber": "1163815", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Transporteintrag bei Impact", "RefUrl": "/notes/1163815 "}, {"RefNumber": "1158429", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Java Web: Input help does not work in the variable screen", "RefUrl": "/notes/1158429 "}, {"RefNumber": "1163140", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "BRAIN 418: The variable ... is used incorrectly", "RefUrl": "/notes/1163140 "}, {"RefNumber": "1162788", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18) Error during loading using third party tool", "RefUrl": "/notes/1162788 "}, {"RefNumber": "1159765", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Only one timeshift variable works in currency translation", "RefUrl": "/notes/1159765 "}, {"RefNumber": "1163209", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 CL_RSR_RRK0_PARTITION, NOTIFY_READ_REQUEST-01-", "RefUrl": "/notes/1163209 "}, {"RefNumber": "1153393", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Performance: Authorization and query with node selection", "RefUrl": "/notes/1153393 "}, {"RefNumber": "1161963", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Duplicate records in BIA dimension indexes: Incorrect data", "RefUrl": "/notes/1161963 "}, {"RefNumber": "1162188", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Runtime: temporal hierarchy join and many node restrictions", "RefUrl": "/notes/1162188 "}, {"RefNumber": "1149760", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Do not use BIA index for queries with virtual key figures", "RefUrl": "/notes/1149760 "}, {"RefNumber": "1154491", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Initialization of the message administration is missing", "RefUrl": "/notes/1154491 "}, {"RefNumber": "1162231", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Administration authorization required for information worker", "RefUrl": "/notes/1162231 "}, {"RefNumber": "1162230", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Value for hierarchy node variable is not set correctly", "RefUrl": "/notes/1162230 "}, {"RefNumber": "1162825", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Message BRAIN 418 for variables on InfoSets", "RefUrl": "/notes/1162825 "}, {"RefNumber": "1163037", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:BATCH: Unnecessary BP_JOB_DELETE in batch manager", "RefUrl": "/notes/1163037 "}, {"RefNumber": "1145975", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "READMODE initial leads to READMODE = A for MultiProviders", "RefUrl": "/notes/1145975 "}, {"RefNumber": "1162986", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P18:PC:PSA process:New DS:Error when activating proc. chain", "RefUrl": "/notes/1162986 "}, {"RefNumber": "1162808", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data for a query with virtual characteristics", "RefUrl": "/notes/1162808 "}, {"RefNumber": "1147985", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Corrections to the report RSDDTREX_REORGANIZE_LANDSCAPE", "RefUrl": "/notes/1147985 "}, {"RefNumber": "1162528", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "P18:SDL:BWAPPL: InfoCubes can be loaded if BWAPPL <> APO", "RefUrl": "/notes/1162528 "}, {"RefNumber": "1162641", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA: Incorrect data after compression terminates", "RefUrl": "/notes/1162641 "}, {"RefNumber": "1162723", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Tabelarische Anzeige dumpt", "RefUrl": "/notes/1162723 "}, {"RefNumber": "1162134", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "BI7.0(SP18) Log incomplete", "RefUrl": "/notes/1162134 "}, {"RefNumber": "1155629", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Mapping errors in rule", "RefUrl": "/notes/1155629 "}, {"RefNumber": "1162426", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Field RSDS-HASHNUMBER is empty", "RefUrl": "/notes/1162426 "}, {"RefNumber": "1162364", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Relational Browse not working with Cum and Ncum providers", "RefUrl": "/notes/1162364 "}, {"RefNumber": "1146957", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Releasing memory OLAP_CACHE", "RefUrl": "/notes/1146957 "}, {"RefNumber": "1159659", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P18: PSA: Deleting requests when managing PSA", "RefUrl": "/notes/1159659 "}, {"RefNumber": "1160291", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Not changeable but changes allowed", "RefUrl": "/notes/1160291 "}, {"RefNumber": "1159565", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Manage: Message RSENQ 009 when you start compression", "RefUrl": "/notes/1159565 "}, {"RefNumber": "1147123", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Report QUERY_CHECK may cause deadlocks in RSZ* tables", "RefUrl": "/notes/1147123 "}, {"RefNumber": "1162113", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination: CL_RSPLS_CR_CONTROLLER and form CONSTRUCTOR-04-", "RefUrl": "/notes/1162113 "}, {"RefNumber": "1161676", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0 (SP 18) Problems when you change time-reference char.", "RefUrl": "/notes/1161676 "}, {"RefNumber": "1161528", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Values with double quotes (\") are not filled in variable", "RefUrl": "/notes/1161528 "}, {"RefNumber": "1144253", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Repartitioning: Error when adding partitions", "RefUrl": "/notes/1144253 "}, {"RefNumber": "1155519", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Dependent variables not updated", "RefUrl": "/notes/1155519 "}, {"RefNumber": "1154708", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Key figure attributes are displayed with a unit or currency", "RefUrl": "/notes/1154708 "}, {"RefNumber": "1161444", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "\"Runtime object for InfoSet contains errors\" (RSQBW 805)", "RefUrl": "/notes/1161444 "}, {"RefNumber": "1120358", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Upgrade from 3.x to 7.x - Exceptions converted incorrectly", "RefUrl": "/notes/1120358 "}, {"RefNumber": "1161088", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Activating BIA index: Termination SAPSQL_ARRAY_INSERT_DUPREC", "RefUrl": "/notes/1161088 "}, {"RefNumber": "1159939", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in design item navigation block", "RefUrl": "/notes/1159939 "}, {"RefNumber": "1141850", "RefComponent": "BW-BCT-TCT", "RefTitle": "\"Not assigned\" time calculated from query RUNTIME", "RefUrl": "/notes/1141850 "}, {"RefNumber": "1161082", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: CSV default data not replaced", "RefUrl": "/notes/1161082 "}, {"RefNumber": "1160984", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "BRAIN 686 \"The hierarchy with HIESID 0...was not found\"", "RefUrl": "/notes/1160984 "}, {"RefNumber": "1151306", "RefComponent": "BW-BEX-ET-RT", "RefTitle": "Scaling cannot be displayed", "RefUrl": "/notes/1151306 "}, {"RefNumber": "1160105", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Termination in XPRA RS_PERS_XPRA_1", "RefUrl": "/notes/1160105 "}, {"RefNumber": "1158317", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Further optimization of authorization check", "RefUrl": "/notes/1158317 "}, {"RefNumber": "1159642", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "URL generation for document links takes a long time", "RefUrl": "/notes/1159642 "}, {"RefNumber": "1158432", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Too many values authorized for hierarchy with intervals", "RefUrl": "/notes/1158432 "}, {"RefNumber": "1159268", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Only one authorization log for templates w/ several queries", "RefUrl": "/notes/1159268 "}, {"RefNumber": "1158476", "RefComponent": "BW-BEX-ET-BC-PREC", "RefTitle": "Interval variables in broadcasting settings", "RefUrl": "/notes/1158476 "}, {"RefNumber": "1139749", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in search framework in workbooks", "RefUrl": "/notes/1139749 "}, {"RefNumber": "1159211", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Backup version of query or query component is not deleted", "RefUrl": "/notes/1159211 "}, {"RefNumber": "1153244", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "No data as a result of incorrect WHERE condition", "RefUrl": "/notes/1153244 "}, {"RefNumber": "1148656", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "RSAPO_SWITCH_TRANS_TO_BATCH", "RefUrl": "/notes/1148656 "}, {"RefNumber": "1141917", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Queries on DataStore objects with star_transformation HINT", "RefUrl": "/notes/1141917 "}, {"RefNumber": "1159978", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error in the template RSTMPLWIDTP for Support Package 16", "RefUrl": "/notes/1159978 "}, {"RefNumber": "1146849", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA metadata change: Termination _CHECK_SID_LOCK", "RefUrl": "/notes/1146849 "}, {"RefNumber": "1159373", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Euro character not loaded during file upload", "RefUrl": "/notes/1159373 "}, {"RefNumber": "1159899", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Calendar yr cannot be selected as time slice characteristic", "RefUrl": "/notes/1159899 "}, {"RefNumber": "1159918", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Transportversionen werden nicht korrekt gelöscht", "RefUrl": "/notes/1159918 "}, {"RefNumber": "1138080", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: WO-DSO: Archiving the write-optimized DataStore object", "RefUrl": "/notes/1138080 "}, {"RefNumber": "1147544", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Interface corrections/unclear descriptions and so on...", "RefUrl": "/notes/1147544 "}, {"RefNumber": "1157070", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P18:DSO:Dump if you activate too many requests together", "RefUrl": "/notes/1157070 "}, {"RefNumber": "1159299", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Text for data element RSBOOK4 translated incorrectly", "RefUrl": "/notes/1159299 "}, {"RefNumber": "1159435", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSRT: Check data of the virtual provider", "RefUrl": "/notes/1159435 "}, {"RefNumber": "1155740", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error in program SAPLRRK<PERSON> and form CUMUL_S_07-", "RefUrl": "/notes/1155740 "}, {"RefNumber": "1159436", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Activating a hierarchy: '_ADD_OVERLAPPING_LEAF_INTVL'", "RefUrl": "/notes/1159436 "}, {"RefNumber": "1158605", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error RAISE_EXCEPTION (exception RSFO_NO_ENTRY)", "RefUrl": "/notes/1158605 "}, {"RefNumber": "1158959", "RefComponent": "BW-BCT-TCT", "RefTitle": "Deleting statistics data: RSDDSTAT", "RefUrl": "/notes/1158959 "}, {"RefNumber": "1157922", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "System error in CL_RSR_HIERARCHY_DIM and SELECT_SIDS_3", "RefUrl": "/notes/1157922 "}, {"RefNumber": "1146316", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Erweiterung des Query-Check", "RefUrl": "/notes/1146316 "}, {"RefNumber": "1159306", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Multiple issues with variables on compounded infoobjects", "RefUrl": "/notes/1159306 "}, {"RefNumber": "1156736", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Slow performance because of missing distribution stats", "RefUrl": "/notes/1156736 "}, {"RefNumber": "1158829", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replacement and compound keys", "RefUrl": "/notes/1158829 "}, {"RefNumber": "1158394", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Aggregation level & exception CX_RS_VERSION_NOT_FOUND", "RefUrl": "/notes/1158394 "}, {"RefNumber": "1156832", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Temporal hierarchy join: Termination ITAB_DUPLICATE_KEY", "RefUrl": "/notes/1156832 "}, {"RefNumber": "1158498", "RefComponent": "BW-WHM", "RefTitle": "Characteristic value not plausible (BRAIN 027)", "RefUrl": "/notes/1158498 "}, {"RefNumber": "1157651", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Accessing BW from SEM may cause deadlocks in RSZ* tables", "RefUrl": "/notes/1157651 "}, {"RefNumber": "1157225", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Property 'aggregation direction' is not evaluated correctly", "RefUrl": "/notes/1157225 "}, {"RefNumber": "1137299", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Views are not displayed in query WHERE-USED list", "RefUrl": "/notes/1137299 "}, {"RefNumber": "1158185", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Exception \"IOBJ_VALUE_NOT_VALID\" is not caught", "RefUrl": "/notes/1158185 "}, {"RefNumber": "1156802", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:REQARCH: Archiving starts many dialog/batch processes", "RefUrl": "/notes/1156802 "}, {"RefNumber": "1157577", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:MON: Reposting several packages in the monitor", "RefUrl": "/notes/1157577 "}, {"RefNumber": "1158218", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Obsolete planned date replaced by SY-DATUM", "RefUrl": "/notes/1158218 "}, {"RefNumber": "1157689", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA - RSRV check: Negative DIMIDs", "RefUrl": "/notes/1157689 "}, {"RefNumber": "1152687", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "InfoProvider not read (KIDSEL = true)", "RefUrl": "/notes/1152687 "}, {"RefNumber": "1067027", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Initial entries from master data and texts are not extracted", "RefUrl": "/notes/1067027 "}, {"RefNumber": "1157873", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Message RSO 252 using transport connection", "RefUrl": "/notes/1157873 "}, {"RefNumber": "1157604", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BRAIN 813 -only allowed to display the hierarchy to level xx", "RefUrl": "/notes/1157604 "}, {"RefNumber": "1157615", "RefComponent": "BW-BCT-TCT", "RefTitle": "Entries in RSDDSTATLOGGING are written", "RefUrl": "/notes/1157615 "}, {"RefNumber": "1157409", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:You have to choose <PERSON><PERSON> twice before dialog box closes", "RefUrl": "/notes/1157409 "}, {"RefNumber": "1156982", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Memory overflow during Master Data Deletion", "RefUrl": "/notes/1156982 "}, {"RefNumber": "1157168", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Incorrect interval check in selections with \"#\"", "RefUrl": "/notes/1157168 "}, {"RefNumber": "1143616", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: No time dependence (SAP, BI source system)", "RefUrl": "/notes/1143616 "}, {"RefNumber": "1156897", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Optimization of memory release in ABAP, planning functions", "RefUrl": "/notes/1156897 "}, {"RefNumber": "1153093", "RefComponent": "BW-WHM", "RefTitle": "Quantity object for InfoObjects does not support InfoSets", "RefUrl": "/notes/1153093 "}, {"RefNumber": "1153018", "RefComponent": "BW-BEX-OT", "RefTitle": "Dump ASSERTION_FAILED in class CL_RSDRC_INFOPROV_UNIVERSE", "RefUrl": "/notes/1153018 "}, {"RefNumber": "1156628", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Syntax error in the program RSDDS_CHANGERUN_MONITOR", "RefUrl": "/notes/1156628 "}, {"RefNumber": "1156593", "RefComponent": "BW-WHM-DST", "RefTitle": "Accessibility issues", "RefUrl": "/notes/1156593 "}, {"RefNumber": "1154671", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Invalid property after migration or copy", "RefUrl": "/notes/1154671 "}, {"RefNumber": "1156093", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA Monitor: Improving the authorization check", "RefUrl": "/notes/1156093 "}, {"RefNumber": "1155265", "RefComponent": "BW-WHM-DBA-SDEL", "RefTitle": "Enhancing the delete function for BW archiving", "RefUrl": "/notes/1155265 "}, {"RefNumber": "1156152", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: API...GET_ERRORS delivers too many messages", "RefUrl": "/notes/1156152 "}, {"RefNumber": "1153605", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Accessibility error in data archiving process maintenance", "RefUrl": "/notes/1153605 "}, {"RefNumber": "1154272", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No dialog box for \"Extras\"->\"3.X Archiving\" in display mode", "RefUrl": "/notes/1154272 "}, {"RefNumber": "1148150", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Long runtime during quantity conversion", "RefUrl": "/notes/1148150 "}, {"RefNumber": "1155855", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: MESSAGE_TYPE_X during tree configuration", "RefUrl": "/notes/1155855 "}, {"RefNumber": "1155723", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:PC: Deleting overlapping DTP requests in PC", "RefUrl": "/notes/1155723 "}, {"RefNumber": "1138339", "RefComponent": "BW", "RefTitle": "RSEURO conversion of key figs. w/ NO1 aggregation terminates", "RefUrl": "/notes/1138339 "}, {"RefNumber": "1155796", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: OBJREF_NOT_ASSIGNED during synchronous execution", "RefUrl": "/notes/1155796 "}, {"RefNumber": "1151982", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Error message is unclear in nested chains", "RefUrl": "/notes/1151982 "}, {"RefNumber": "1150154", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "RSDODSO E201: \"No InfoProvider exists with name \"&1\"\"", "RefUrl": "/notes/1150154 "}, {"RefNumber": "1145041", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "RSAU 484 when executing InfoPackage", "RefUrl": "/notes/1145041 "}, {"RefNumber": "1154763", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Error when checking time-dependent hierarchy nodes", "RefUrl": "/notes/1154763 "}, {"RefNumber": "1154745", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Text element display does not provide complete information", "RefUrl": "/notes/1154745 "}, {"RefNumber": "1155340", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:REQARCH: Table control CTRL_SEL is not defined", "RefUrl": "/notes/1155340 "}, {"RefNumber": "1155253", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: RMC_COMMUNICATION_FAILURE when scrolling", "RefUrl": "/notes/1155253 "}, {"RefNumber": "1155385", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Record is not deleted before conversion", "RefUrl": "/notes/1155385 "}, {"RefNumber": "1153787", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA: Improving the usability in Support Package 18", "RefUrl": "/notes/1153787 "}, {"RefNumber": "1144702", "RefComponent": "BW-PLA-IP", "RefTitle": "Memory release, additional corrections to Note 1101187", "RefUrl": "/notes/1144702 "}, {"RefNumber": "1150148", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: BI Migration: Range-part. to MDC: RSDCUBE not updated", "RefUrl": "/notes/1150148 "}, {"RefNumber": "1145014", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Characteristic displayed as compounded even though unique", "RefUrl": "/notes/1145014 "}, {"RefNumber": "1152229", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "711: Improving load monitor performance in BW 7.11", "RefUrl": "/notes/1152229 "}, {"RefNumber": "1154318", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Dump w/automatic index deletion and DTP request check", "RefUrl": "/notes/1154318 "}, {"RefNumber": "1151176", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:BAPI:Message RH 230 with '-' in hierarchy name", "RefUrl": "/notes/1151176 "}, {"RefNumber": "1153399", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:POSTING_ILLEGAL_STATEMENT during transactional writing", "RefUrl": "/notes/1153399 "}, {"RefNumber": "1141314", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Routine & formula IDs changed during replication", "RefUrl": "/notes/1141314 "}, {"RefNumber": "1154560", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) / BI7.1SPS06 Interner Mappingfehler", "RefUrl": "/notes/1154560 "}, {"RefNumber": "1151843", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data in a very specific situation", "RefUrl": "/notes/1151843 "}, {"RefNumber": "1154373", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Error with TLSHIFT in non-unique hierarchies", "RefUrl": "/notes/1154373 "}, {"RefNumber": "1154309", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Formulas: Function CURC and decimal places", "RefUrl": "/notes/1154309 "}, {"RefNumber": "1153756", "RefComponent": "BW-PLA-IP", "RefTitle": "Template: BI 7.0 (SP18) / BI 7.1 SPS06", "RefUrl": "/notes/1153756 "}, {"RefNumber": "1153359", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Statistics event \"Open workbook\" is not closed", "RefUrl": "/notes/1153359 "}, {"RefNumber": "1149012", "RefComponent": "BW-WHM-AWB", "RefTitle": "Deleting authorizations transfer rule BI7.0SP18(SPS16)", "RefUrl": "/notes/1149012 "}, {"RefNumber": "1153245", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) InfoObjekte werden nicht mitgesammelt", "RefUrl": "/notes/1153245 "}, {"RefNumber": "1153342", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "'Server for scheduling' ignored when server type is 'Host'", "RefUrl": "/notes/1153342 "}, {"RefNumber": "1147924", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcasting: Distributing multi channels for mandatory var.", "RefUrl": "/notes/1147924 "}, {"RefNumber": "1152599", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect val for FLOOR, CEIL and so on w/negative argument", "RefUrl": "/notes/1152599 "}, {"RefNumber": "1149844", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Access to non-compound keys very slow", "RefUrl": "/notes/1149844 "}, {"RefNumber": "1152366", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Precalculation of value set: Query variant is ignored", "RefUrl": "/notes/1152366 "}, {"RefNumber": "1152998", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Log improvements for the precalculation server", "RefUrl": "/notes/1152998 "}, {"RefNumber": "1152445", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Report RSDDTZA_BIA_OVERLOAD_EMAIL terminates", "RefUrl": "/notes/1152445 "}, {"RefNumber": "1141798", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Analysis of RRMX/RSAH_Launch_Excel problems", "RefUrl": "/notes/1141798 "}, {"RefNumber": "1152805", "RefComponent": "BW-WHM-AWB", "RefTitle": "P18:DWWB: No message issued when you delete InfoArea", "RefUrl": "/notes/1152805 "}, {"RefNumber": "1152804", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Falsche oder unvollständige Meldung - Anlegen", "RefUrl": "/notes/1152804 "}, {"RefNumber": "1150546", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Performance improvement for cube delta write", "RefUrl": "/notes/1150546 "}, {"RefNumber": "1150152", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Reclustering: Label not associated with input field", "RefUrl": "/notes/1150152 "}, {"RefNumber": "1151566", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "No value displayed for 0REQUID in input help", "RefUrl": "/notes/1151566 "}, {"RefNumber": "1152557", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "\"Key Figure Definition\" uses wrong key date", "RefUrl": "/notes/1152557 "}, {"RefNumber": "1152069", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Incorrect length for application component", "RefUrl": "/notes/1152069 "}, {"RefNumber": "1143698", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Branching from ALV to BEx: Currencies/units not correct", "RefUrl": "/notes/1143698 "}, {"RefNumber": "1150540", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Parallel filling of aggregates terminates with a lock", "RefUrl": "/notes/1150540 "}, {"RefNumber": "1141750", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Do not use RSRV test \"Rebuild all master data indexes...\"", "RefUrl": "/notes/1141750 "}, {"RefNumber": "1152555", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination \"INPUT_NOT_LEGAL\" in program SAPLRRBA", "RefUrl": "/notes/1152555 "}, {"RefNumber": "1152166", "RefComponent": "BW-BCT-TCT", "RefTitle": "Maintain statistics settings: Authorization", "RefUrl": "/notes/1152166 "}, {"RefNumber": "1148857", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Replication with dialog box", "RefUrl": "/notes/1148857 "}, {"RefNumber": "1151720", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:Long wait time before incorrect request turns red", "RefUrl": "/notes/1151720 "}, {"RefNumber": "1149660", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSR_RRK0_CACHE; form COMMIT-01-", "RefUrl": "/notes/1149660 "}, {"RefNumber": "1144453", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Entering # for compounded values does not work", "RefUrl": "/notes/1144453 "}, {"RefNumber": "1150436", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Process step continues all open requests", "RefUrl": "/notes/1150436 "}, {"RefNumber": "1138848", "RefComponent": "FIN-SEM-BCS", "RefTitle": "X299 Brain in CL_RSR_RRK0_RQTS; form _DELTA_MINUS_AGGR-06-", "RefUrl": "/notes/1138848 "}, {"RefNumber": "1141039", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The system displays only 16 periods for 0FISCPER3", "RefUrl": "/notes/1141039 "}, {"RefNumber": "1151076", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Sending links: Invalid reference to DataProvider", "RefUrl": "/notes/1151076 "}, {"RefNumber": "1138172", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "CX_SY_CONVERSION_OVERFLOW in RRBA_CONVERT_PACKED_NUMBER", "RefUrl": "/notes/1138172 "}, {"RefNumber": "1150754", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Authorizations for InfoSet chars. ignored in input help", "RefUrl": "/notes/1150754 "}, {"RefNumber": "1149083", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data displayed when you expand a hierarchy node", "RefUrl": "/notes/1149083 "}, {"RefNumber": "1147867", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unexpected message RSPLS 504 for characteristic dependency", "RefUrl": "/notes/1147867 "}, {"RefNumber": "1149504", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions: Messages on variable screen", "RefUrl": "/notes/1149504 "}, {"RefNumber": "1150514", "RefComponent": "BW-PLA-IP", "RefTitle": "Decimal places shifted during planning of amounts", "RefUrl": "/notes/1150514 "}, {"RefNumber": "1141969", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Repeated saving of planning data", "RefUrl": "/notes/1141969 "}, {"RefNumber": "1149393", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSRT: Warning for redefined cells and help cells", "RefUrl": "/notes/1149393 "}, {"RefNumber": "1149365", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "UNCAUGHT exception when using ABAP exits", "RefUrl": "/notes/1149365 "}, {"RefNumber": "1149178", "RefComponent": "BW-WHM-DBA-DMA", "RefTitle": "Deadlock when extracting a data target to itself", "RefUrl": "/notes/1149178 "}, {"RefNumber": "1149582", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon cannot close InfoPackage request", "RefUrl": "/notes/1149582 "}, {"RefNumber": "1146094", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "The most current variable values in planning functions", "RefUrl": "/notes/1146094 "}, {"RefNumber": "1146596", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: DataSources remain after source system deleted", "RefUrl": "/notes/1146596 "}, {"RefNumber": "1149614", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL: Old routine maintenance editor jumps to first line", "RefUrl": "/notes/1149614 "}, {"RefNumber": "1142738", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cumulative query with AV1 or AV2 aggregation and sel opt", "RefUrl": "/notes/1142738 "}, {"RefNumber": "1149543", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI 7.0 (SP 18): Dump when you maintain a rule", "RefUrl": "/notes/1149543 "}, {"RefNumber": "1149348", "RefComponent": "BW-PLA-IP", "RefTitle": "BRAIN 299 in CL_RSR_RRK0_RQTS; form GET_R_DELTA_BUF-01-", "RefUrl": "/notes/1149348 "}, {"RefNumber": "1147998", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Document check \"No authorization\" despite 0BI_ALL/SAP_ALL", "RefUrl": "/notes/1147998 "}, {"RefNumber": "1145664", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Error for MultiProv.query that delivers no characteristics", "RefUrl": "/notes/1145664 "}, {"RefNumber": "1146834", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: RSBATCH_WRITE_PROT_TO_APPLLOG and job log S messages", "RefUrl": "/notes/1146834 "}, {"RefNumber": "1144979", "RefComponent": "BW-BEX-ET-WJR-BICS", "RefTitle": "BICS: Deactivating attribute display in value help", "RefUrl": "/notes/1144979 "}, {"RefNumber": "1147766", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P18:PC:DTP:Select only full DTPs for overlapping deletion", "RefUrl": "/notes/1147766 "}, {"RefNumber": "1147161", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:MON: Subsequently posting data packages & error message", "RefUrl": "/notes/1147161 "}, {"RefNumber": "1145927", "RefComponent": "BW-WHM-DST-AUT", "RefTitle": "Correction: Error during authorization check for new chains", "RefUrl": "/notes/1145927 "}, {"RefNumber": "1141585", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Analysis authorizations in the input help are ignored", "RefUrl": "/notes/1141585 "}, {"RefNumber": "1142252", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Runtime error SAPSQL_AMBIGUOUS_FIELDNAME during extraction", "RefUrl": "/notes/1142252 "}, {"RefNumber": "1146242", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No success message when you save a data archiving process", "RefUrl": "/notes/1146242 "}, {"RefNumber": "1147536", "RefComponent": "BW-BEX-ET", "RefTitle": "Personalization of variables in NW2004s does not work", "RefUrl": "/notes/1147536 "}, {"RefNumber": "1147466", "RefComponent": "BW-SYS", "RefTitle": "RSPOR_T_PORTAL: Standard font to create a PDF file", "RefUrl": "/notes/1147466 "}, {"RefNumber": "1145170", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL: Empty mssge dialog box after simulatn during start", "RefUrl": "/notes/1145170 "}, {"RefNumber": "1143771", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Not all variables displayed as text elements", "RefUrl": "/notes/1143771 "}, {"RefNumber": "1147365", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon not started (RSCRT 340)", "RefUrl": "/notes/1147365 "}, {"RefNumber": "1146656", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP for RDA does not filter records", "RefUrl": "/notes/1146656 "}, {"RefNumber": "1138410", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Manage:Rpt for deleting zombie requests from data target", "RefUrl": "/notes/1138410 "}, {"RefNumber": "1146577", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Web service DataSource: Error when assigning InfoPackage", "RefUrl": "/notes/1146577 "}, {"RefNumber": "1146932", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: No warning for indicator \"Duplicate data recs.\"", "RefUrl": "/notes/1146932 "}, {"RefNumber": "1144574", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Termination when transferring values to input-ready query", "RefUrl": "/notes/1144574 "}, {"RefNumber": "1145990", "RefComponent": "BW-WHM-DST", "RefTitle": "711: Navigator for DTP requests when calling from management", "RefUrl": "/notes/1145990 "}, {"RefNumber": "1146590", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P18:Manage: Dump in function module RSSM_RSMONIPTAB_READ", "RefUrl": "/notes/1146590 "}, {"RefNumber": "1145848", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:STATMAN: Performance: Complete deletion of all requests", "RefUrl": "/notes/1145848 "}, {"RefNumber": "1145881", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Bursting with characteristic variables for compound char", "RefUrl": "/notes/1145881 "}, {"RefNumber": "1146030", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "RSRV - Missing test description for 'Check Time Intervals'", "RefUrl": "/notes/1146030 "}, {"RefNumber": "1138161", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RSBK 223: \"Status 'Processed with Errors' (user ..)\"", "RefUrl": "/notes/1138161 "}, {"RefNumber": "1142507", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSDRC_MULTIPROV; form _SET_SFC_KEYRET-01-", "RefUrl": "/notes/1142507 "}, {"RefNumber": "1144874", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in CL_RSD_MULTIPROV_CHECK; _CHECK_CMP_MESS-01-", "RefUrl": "/notes/1144874 "}, {"RefNumber": "1145610", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "SP18:CCMS:MAs are filled though monitoring is deactivated", "RefUrl": "/notes/1145610 "}, {"RefNumber": "1142638", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "DBIF_RSQL_INVALID_RSQL in CL_RSDMD_LOOKUP_MASTER_DATA", "RefUrl": "/notes/1142638 "}, {"RefNumber": "1145725", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Bursting: <PERSON><PERSON> addresses truncated after 40 characters", "RefUrl": "/notes/1145725 "}, {"RefNumber": "1137553", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Analyseberechtigungen funktionieren nicht", "RefUrl": "/notes/1137553 "}, {"RefNumber": "1145559", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination in program SAPLRRS2 and form GET_SID-1-", "RefUrl": "/notes/1145559 "}, {"RefNumber": "1143924", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A125 BRAIN: Field G_S_indicator-Z____nnnn_$W$ unknown", "RefUrl": "/notes/1143924 "}, {"RefNumber": "1139617", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:DSO: Write-opt: No loading for MOVE data as for cube", "RefUrl": "/notes/1139617 "}, {"RefNumber": "1145163", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Confusing screen design for selective deletion", "RefUrl": "/notes/1145163 "}, {"RefNumber": "1145326", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Incorr line feed when loading text from server", "RefUrl": "/notes/1145326 "}, {"RefNumber": "1144308", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Parallelization for predefined times", "RefUrl": "/notes/1144308 "}, {"RefNumber": "1123408", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Incorrect data with KIDSEL and formula exception aggregation", "RefUrl": "/notes/1123408 "}, {"RefNumber": "1143867", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termnatn RSDDK_CHECK_AGGREGATE with SAPSQL_INVALID_TABLENAME", "RefUrl": "/notes/1143867 "}, {"RefNumber": "1141716", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:DSO: Write-optimized DSO and overtaking requests", "RefUrl": "/notes/1141716 "}, {"RefNumber": "1143281", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:BAPI_IPAK_CHANGE, selection fld with NUMC,conv exit", "RefUrl": "/notes/1143281 "}, {"RefNumber": "1143105", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:MDT: Faster deletion of too many master data requests", "RefUrl": "/notes/1143105 "}, {"RefNumber": "1140737", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Sorting by a value column does not work", "RefUrl": "/notes/1140737 "}, {"RefNumber": "1143745", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Variablenbelegung im Broadcaster anlegen: Personalisierung", "RefUrl": "/notes/1143745 "}, {"RefNumber": "1140837", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18: SDL: <PERSON><PERSON><PERSON> und individual selection in the scheduler", "RefUrl": "/notes/1140837 "}, {"RefNumber": "1141361", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P18:DTP:STATMAN: Multiple deletion of a request; dump", "RefUrl": "/notes/1141361 "}, {"RefNumber": "1143300", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "NetWeaver 2004s BI Web Appl.Designer:SET_EXCEPTION inpt help", "RefUrl": "/notes/1143300 "}, {"RefNumber": "1143176", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions; formulas; reference data", "RefUrl": "/notes/1143176 "}, {"RefNumber": "1139836", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Link generation: Deletion of variable assignment", "RefUrl": "/notes/1139836 "}, {"RefNumber": "1141260", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Transfer rules PERFORM_PARAMETER_MISSING", "RefUrl": "/notes/1141260 "}, {"RefNumber": "1142481", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: GEN_BRANCHOFFSET_LIMIT_REACHED for segm. DataSce", "RefUrl": "/notes/1142481 "}, {"RefNumber": "1142143", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "3.x DataSource: \"A\" version for hierarchy without intervals", "RefUrl": "/notes/1142143 "}, {"RefNumber": "1140799", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RSAR 051 when transporting transformations", "RefUrl": "/notes/1140799 "}, {"RefNumber": "1140458", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination: Program SAPLRRS2; form FAC_CHAVLVAR_REPLACE-1-", "RefUrl": "/notes/1140458 "}, {"RefNumber": "1140243", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "RSECADMIN: Hierarchy is incorrect after hierarchy search", "RefUrl": "/notes/1140243 "}, {"RefNumber": "1140825", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in transaction RSPRECADMIN", "RefUrl": "/notes/1140825 "}, {"RefNumber": "1141004", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error in BEx Analyzer dropdown item", "RefUrl": "/notes/1141004 "}, {"RefNumber": "1140808", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning functions executed with incorrect selection", "RefUrl": "/notes/1140808 "}, {"RefNumber": "1141158", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation rule test: Generated for formulas", "RefUrl": "/notes/1141158 "}, {"RefNumber": "1140701", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Creation of BIA index terminates with X message", "RefUrl": "/notes/1140701 "}, {"RefNumber": "1139585", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Performance optimization: Selects on table RSZGLOBV", "RefUrl": "/notes/1139585 "}, {"RefNumber": "1141033", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Sorting by selection and totals suppression", "RefUrl": "/notes/1141033 "}, {"RefNumber": "1132269", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "\"Start Automatically\" option for delete jobs during ADK DAP", "RefUrl": "/notes/1132269 "}, {"RefNumber": "1139821", "RefComponent": "BW-WHM-DST", "RefTitle": "P18: Incorrect lock on RSICCONT for text table update", "RefUrl": "/notes/1139821 "}, {"RefNumber": "1139318", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "COMMIT executed in function mod. RSNDI_MD_ATTRIBUTES_UPDATE", "RefUrl": "/notes/1139318 "}, {"RefNumber": "1138361", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Different data for bookmark with pre-query", "RefUrl": "/notes/1138361 "}, {"RefNumber": "1140535", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Error when editing the updaterule - assigned package attr.", "RefUrl": "/notes/1140535 "}, {"RefNumber": "1140051", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18): Dump MESSAGE_TYPE_X during activation", "RefUrl": "/notes/1140051 "}, {"RefNumber": "1140214", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18): Incorrect red message in the DTP monitor", "RefUrl": "/notes/1140214 "}, {"RefNumber": "1134688", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Allowed type changes with near-line archiving", "RefUrl": "/notes/1134688 "}, {"RefNumber": "1139924", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18): System names converted incorrectly", "RefUrl": "/notes/1139924 "}, {"RefNumber": "1137543", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation prog:Srce code for InfoObject routine missing", "RefUrl": "/notes/1137543 "}, {"RefNumber": "1139448", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "ASSERTION_FAILED in CL_RSAR_PSA _UPDATE_DIRECTORY_TABLES", "RefUrl": "/notes/1139448 "}, {"RefNumber": "1137447", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "ASSERTION_FAILED when the transformation is activated", "RefUrl": "/notes/1137447 "}, {"RefNumber": "1138513", "RefComponent": "BW-BEX-ET-QDEF-SEL", "RefTitle": "Check of intervals in input help incorrect", "RefUrl": "/notes/1138513 "}, {"RefNumber": "1138199", "RefComponent": "BW-BEX-OT", "RefTitle": "X299 BRAIN in CL_RSD_MULTIPROV; form GET_PART_IOBJNM-01-", "RefUrl": "/notes/1138199 "}, {"RefNumber": "1138948", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "DTP Messages written to Application Log", "RefUrl": "/notes/1138948 "}, {"RefNumber": "1138865", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Time stamp missing from group RSAPO (delta cache)", "RefUrl": "/notes/1138865 "}, {"RefNumber": "1135045", "RefComponent": "BW-WHM-DST", "RefTitle": "P18:Job BI_WRITE_PROT_TO_APPLLOG is scheduled several times", "RefUrl": "/notes/1135045 "}, {"RefNumber": "1136053", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P18:SDL:DWWB: Renaming and saving an InfoPackage", "RefUrl": "/notes/1136053 "}, {"RefNumber": "1136469", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P18:DSO:Activating a DSO and many overlapping checks", "RefUrl": "/notes/1136469 "}, {"RefNumber": "1137159", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Check missing for Content transfer", "RefUrl": "/notes/1137159 "}, {"RefNumber": "1136925", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "BI7.0(SP18) Path in destination not checked", "RefUrl": "/notes/1136925 "}, {"RefNumber": "1136924", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP18) Dump during content creation for conversion DS", "RefUrl": "/notes/1136924 "}, {"RefNumber": "1135954", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Non-authorized attributes displayed in F4 help", "RefUrl": "/notes/1135954 "}, {"RefNumber": "1137243", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query terminates: CL_RSR_HIERARCHY_BINCL BU_SUBSET_CHECK-3-", "RefUrl": "/notes/1137243 "}, {"RefNumber": "1135887", "RefComponent": "BW-BEX-OT", "RefTitle": "Max. value for fiscal period is 9999999 instead of 9999012", "RefUrl": "/notes/1135887 "}, {"RefNumber": "1136735", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query setting not set to InfoProivder setting", "RefUrl": "/notes/1136735 "}, {"RefNumber": "1137210", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Estimation of index time in RSRV is incorrect", "RefUrl": "/notes/1137210 "}, {"RefNumber": "1136945", "RefComponent": "BW-WHM-AWB", "RefTitle": "Search for InfoObject terminates with dump (BI7.0/SPS16)", "RefUrl": "/notes/1136945 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}