{"Request": {"Number": "2324473", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 266, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018340822017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002324473?language=E&token=2C2D42D659B322B0AE85AE2154D5F993"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002324473", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002324473/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2324473"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.02.2024"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2324473 - Integration of SAP CRM with SAP S/4HANA: resolved restrictions"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note provides an overview of&#160;resolved and remaining restrictions for the integration of SAP S/4HANA&#160;with SAP&#160;Customer&#160;Relationship Management (SAP CRM)&#160;&#160;in compatibility mode.&#160;See also SAP Note 2231667.</p>\r\n<p>Note:<br />Where the SAP Customer Relationship Management (SAP CRM) user assistance describes integration with SAP ERP, it can refer to integration with either SAP ERP or SAP S/4HANA. The&#160;user assistance&#160;includes various text types, such as product guides, product assistance on SAP Help Portal,&#160;field labels, field help, message texts, release notes,&#160;Customizing documentation, and SAP Easy Access texts<em>.</em></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP S/4HANA 1511 SP02, SAP S/4HANA 1610, SAP S/4HANA 1709, SAP S/4HANA 1809, SAP S/4HANA 1909, SAP S/4HANA 2020, SAP S/4HANA 2021, SAP S/4HANA 2022,&#160;SAP S/4HANA 2023, process integration</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Several restrictions have been resolved regarding process integration capabilities between SAP S/4HANA 1511 SP02,&#160;1610, 1709, 1809, 1909, 2020, 2021, 2022, or 2023 and&#160;SAP CRM on-premise&#160;in compatibility mode. An overview over the resolved and remaining restrictions is required.</p>\r\n<p><strong>Note:</strong> This SAP Note is subject to change.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The following list provides an overview of resolved and remaining restrictions regarding integrating SAP S/4HANA 1511 SP02 or 1610 or 1709 or 1809 or 1909 or 2020 or 2021&#160;or 2022 or 2023 with SAP enhancement package&#160;3 for SAP CRM 7.0 or SAP enhancement package&#160;4 for SAP CRM 7.0:</p>\r\n<ul>\r\n<li>Restrictions with regard to business partner master data exchange:</li>\r\n<ul>\r\n<li>Key Mapping during generation of BP in S/4 HANA inconsistent with regard to CRM BP keys.\r\n<p>You must implement&#160;SAP Note 2283695 before the Customer Vendor Integration (CVI) is activated and the mass synchronization of customer master or customer vendor data for the generation of business partners is started. If you start the synchronization before SAP Note 2283695 has been implemented, the mapping between business partners that is used in integration scenarios with SAP CRM is irretrievably lost.</p>\r\n</li>\r\n<li>Data exchange of vendors is now supported</li>\r\n<li>Data exchange of SEPA Mandates for customers&#160;is&#160;possible now</li>\r\n<li>Non equal numbers for BP-CRM, BP-S/4 HANA, and Customer master&#160;is now supported</li>\r\n<li>Inbound User Exits (PIDE, PIDV, DE_KTOKD)&#160;are also&#160;supported and&#160;evaluated by object BUPA_MAIN and BUPA_REL</li>\r\n<li>Outbound User Exits (DE_BCS2S) and PIDE settings are now also supported for object BUPA_MAIN and BUPA_REL</li>\r\n<li>Outbound filters, especially regarding sales areas,&#160;also work for object BUPA_MAIN and BUPA_REL</li>\r\n<li>Consumers are now supported in S/4HANA including data exchange with CRM</li>\r\n<li>Pure Business Partners in general role will not be exchanged with Customer Master or Vendor Master via CVI and will also&#160;not flow to CRM</li>\r\n</ul>\r\n<li>Restrictions with regard to Credit Management:</li>\r\n<ul>\r\n<li>Only FSCM is supported in S/4HANA, CRM processes working with old ERP credit management&#160;are now using FSCM credit management</li>\r\n</ul>\r\n<li>Restrictions with regard to Marketing:</li>\r\n<ul>\r\n<li>Integration with CRM Funds Management for Marketing is now supported</li>\r\n</ul>\r\n<li>Restrictions with regard to Returnable Packaging Logistics: There are limitations for packaging material with more than 35 characters in combination with Returnable Packaging Logistics (application component MM-IM-RL or IS-A-RL). These are:\r\n<ul>\r\n<li>EDI processing of returnable packaging account statements (message type ACCSTA).</li>\r\n<li>The IDoc type ACCSTA01 allows the transmission of supplier and customer material number for packaging materials of up to 35 characters.</li>\r\n<li>EDI processing of returnable packaging account statement requests (message type ACCSTAREQ). The IDoc type ACCSTA01 allows the transmission of supplier and customer material number for packaging materials of up to 35 characters.</li>\r\n</ul>\r\n</li>\r\n<li>Restrictions with regard to CS - Customer Service:</li>\r\n<ul>\r\n<li>Customer Interaction Center (CIC) flexible Framework is still not supported.</li>\r\n</ul>\r\n<li>Restrictions with regard to Leasing:</li>\r\n<ul>\r\n<li>Leasing accounting integration (LAE, Lease Accounting Engine) is still not possible</li>\r\n</ul>\r\n<li>Restrictions with regard to Lead to Cash</li>\r\n<ul>\r\n<li>Lead to Cash integration scenarions are&#160;now supported with S/4HANA(LORD2 interface only)</li>\r\n</ul>\r\n<li>Restrictions with regard to CRM Trade Promotion Management</li>\r\n<ul>\r\n<li>The integration of SAP&#160;Trade&#160;Management 3.0 into S/4HANA Settlement Contracts is&#160;supported as of CRM 7.0 EhP4 SP05 and S/4HANA 1610 FP01&#160;</li>\r\n</ul>\r\n<li>Restrictions with regard to Revenue Accounting Engine</li>\r\n<ul>\r\n<li>Revenue Accounting Engine integration scenarios&#160;are still not supported with S/4HANA</li>\r\n</ul>\r\n<li>Restrictions with regard to Intellectual Property Management (CRM-IM-IPM)</li>\r\n<ul>\r\n<li>\r\n<p>The industry solution SAP Media is not part of SAP S/4HANA. Therefore an integrated scenario with SAP S/4HANA is not supported.</p>\r\n</li>\r\n</ul>\r\n</ul>\r\n<p>&#160;</p>\r\n<p>The following list provides an overview of resolved and remaining restrictions regarding integrating SAP S/4HANA 1511 SP02 or 1610 or 1709 or 1809 or 1909 or 2020 or 2021 or 2022 or 2023 with SAP enhancement package&#160;1 for SAP CRM 7.0 or SAP enhancement package&#160;2 for SAP CRM 7.0:</p>\r\n<ul>\r\n<li>Restrictions with regard to business partner master data exchange:</li>\r\n<ul>\r\n<li>Key Mapping during generation of BP in S/4 HANA inconsistent with regard to CRM BP keys.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">You must implement&#160;SAP Note 2283695 before the Customer Vendor Integration (CVI) is activated and the mass synchronization of customer master or customer vendor data for the generation of business partners is started. If you start the synchronization before SAP Note 2283695 has been implemented, the mapping between business partners that is used in integration scenarios with SAP CRM is irretrievably lost.</span></p>\r\n<ul>\r\n<ul>\r\n<li>Data exchange of vendors is now supported</li>\r\n<li>Data exchange of SEPA Mandates for customers&#160;is&#160;possible now</li>\r\n<li>Non equal numbers for BP-CRM, BP-S/4 HANA, and Customer master&#160;is now supported</li>\r\n<li>Inbound User Exits (PIDE, PIDV, DE_KTOKD)&#160;are also&#160;supported and&#160;evaluated by object BUPA_MAIN and BUPA_REL</li>\r\n<li>Outbound User Exits (DE_BCS2S) and PIDE settings are now also supported for object BUPA_MAIN and BUPA_REL</li>\r\n<li>Outbound filters, especially regarding sales areas,&#160;also work for object BUPA_MAIN and BUPA_REL</li>\r\n<li>Consumers are now supported in S/4HANA including data exchange with CRM</li>\r\n<li>Pure Business Partners in general role will not be exchanged with Customer Master or Vendor Master via CVI and will also&#160;not flow to CRM</li>\r\n</ul>\r\n<li>Restrictions with regard to Credit Management:</li>\r\n<ul>\r\n<li>Only FSCM is supported in S/4HANA</li>\r\n</ul>\r\n<li>Restrictions with regard to Marketing:</li>\r\n<ul>\r\n<li>Business Function FIN_CRM_MKT_INTGR: \"Integration with CRM Funds Management for Marketing\" can't be activated</li>\r\n</ul>\r\n<li>Restrictions with regard to Returnable Packaging Logistics: There are limitations for packaging material with more than 35 characters in combination with Returnable Packaging Logistics (application component MM-IM-RL or IS-A-RL). Those are:</li>\r\n<ul>\r\n<li>EDI processing of returnable packaging account statements (message type ACCSTA).</li>\r\n<li>The IDoc type ACCSTA01 allows the transmission of supplier and customer material number for packaging materials of up to 35 characters.</li>\r\n<li>EDI processing of returnable packaging account statement requests (message type ACCSTAREQ). The IDoc type ACCSTA01 allows the transmission of supplier and customer material number for packaging materials of up to 35 characters.</li>\r\n</ul>\r\n<li>Restrictions with regard to CS - Customer Service:</li>\r\n<ul>\r\n<li>Customer Interaction Center (CIC) flexible Framework not supported.</li>\r\n</ul>\r\n<li>Restrictions with regard to Leasing:</li>\r\n<ul>\r\n<li>Leasing accounting integration (LAE, Lease Accounting Engine) not possible</li>\r\n</ul>\r\n<li>Restrictions with regard to Lead to Cash</li>\r\n<ul>\r\n<li>Lead to Cash integration scenarions are not supported with S/4HANA</li>\r\n</ul>\r\n<li>Restrictions with regard to CRM Trade Promotion Management</li>\r\n<ul>\r\n<li>CRM Trade Promotion Management is currently not integrated with SAP Settlement Management</li>\r\n</ul>\r\n<li>Restrictions with regard to Revenue Accounting Engine</li>\r\n<ul>\r\n<li>Revenue Accounting Engine integration scenarios&#160;are not supported with S/4HANA&#160;</li>\r\n</ul>\r\n<li>Restrictions with regard to Intellectual Property Management (CRM-IM-IPM)</li>\r\n<ul>\r\n<li>\r\n<p>The industry solution SAP Media is not part of SAP S/4HANA. Therefore an integrated scenario with SAP S/4HANA is not supported.</p>\r\n</li>\r\n</ul>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D025609)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D053797)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002324473/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002324473/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002324473/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002324473/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002324473/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002324473/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002324473/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002324473/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002324473/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2677010", "RefComponent": "IS-M", "RefTitle": "SAP Media in ERP / ECC 6.X and SAP S/4HANA", "RefUrl": "/notes/2677010"}, {"RefNumber": "2498300", "RefComponent": "SD-SLS-API", "RefTitle": "Blacklisted LORD Function Modules", "RefUrl": "/notes/2498300"}, {"RefNumber": "2231667", "RefComponent": "XX-SER-REL", "RefTitle": "Restrictions for integration of SAP S/4HANA, on-premise edition with SAP CRM", "RefUrl": "/notes/2231667"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3386916", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2023: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/3386916 "}, {"RefNumber": "3106645", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2021: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/3106645 "}, {"RefNumber": "2974130", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2974130 "}, {"RefNumber": "2816584", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2816584 "}, {"RefNumber": "2657067", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1809: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2657067 "}, {"RefNumber": "2491467", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709: Restriction Note", "RefUrl": "/notes/2491467 "}, {"RefNumber": "2495662", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2495662 "}, {"RefNumber": "2241931", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA, on-premise edition 1511: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2241931 "}, {"RefNumber": "2376061", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1610: Process Integration with SAP on-premise Solutions", "RefUrl": "/notes/2376061 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}