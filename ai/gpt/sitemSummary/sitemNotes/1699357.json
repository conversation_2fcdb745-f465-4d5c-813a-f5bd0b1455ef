{"Request": {"Number": "1699357", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1036, "Error": {}, "SAPNote": {"_type": "00200720410000000532", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010098032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001699357?language=E&token=032014FB3EBFB0E70B9FFF88A4854380"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001699357", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1699357"}, "Type": {"_label": "Type", "value": "SAP Security Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.08.2013"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-IC-CHA"}, "SAPComponentKeyText": {"_label": "Component", "value": "use CRM-IC-CHA(Communication Channels)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Application Functions", "value": "CA-GTF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use CRM-IC (Interaction Center WebClient)", "value": "CA-GTF-IC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-IC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use CRM-IC-CHA(Communication Channels)", "value": "CA-GTF-IC-CHA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-IC-CHA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1699357 - Connection to Virus Scan Interface in CA-GTF-IC-CHA"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the Integrated Communication Interface Mail Channel the connection to the Virus Scan Interface is missing. This can allow a malicious user to upload malicious content to the server, and possibly thereby trick legitimate users into downloading its content.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Virus Scan Profile, Virus Scan, Virus Scan Interface</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is a new functional enhancement which enhances the secure storage of files on the SAP share system (e.g. on an application server). The content and the reported type of a stored file will be checked, and storage only permitted for verified application file types.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>Before implementing this note, implement the prerequisite: 1640285.</B><br /><br />Implement the present note.<br /><br />Please note that you must follow the manual pre-implementation steps before implementing the note.<br /><br />In pre-implementation steps the application specific Virus Scan Profile /IC_CCS_MCM/ICI_MAIL will be created.<br /><br />Implementing the correction instructions and manual steps enables a connection to Virus Scan Interface for e-mails that are sent to the CRM Interaction Center via the Integrated Communication Interface.<br />To activate the invocation of the Virus Scan Interface you need to activate the Virus Scan Profile /IC_CCS_MCM/ICI_MAIL in customizing under IMG Path SAP NetWeaver-&gt;Application Server-&gt;System Administration-&gt;Virus Scan Interface-&gt;Define Virus Scan Profiles.<br />After activating the Virus Scan Profile e-mails and their attachments will be passed to the Virus Scan Interface for checking. In case of a negative check result the e-mail respectively the attachment will be replaced by an error message text.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CRM-IC-CHA-EMA (E-Mail Integration)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D044278)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D058566)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001699357/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001699357/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001699357/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001699357/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001699357/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001699357/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001699357/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001699357/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001699357/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ZAP_DEL_SEOSUBCO.zip", "FileSize": "1", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000072192012&iv_version=0001&iv_guid=7E2DCE8B548D294E9AC789B51FB6CD30"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1899450", "RefComponent": "XX-INT-SR", "RefTitle": "SAP Security Patch Day 08/2013", "RefUrl": "/notes/1899450 "}, {"RefNumber": "1640285", "RefComponent": "BC-SEC-VIR", "RefTitle": "Determine MIME type with Virus Scan Interface", "RefUrl": "/notes/1640285 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_ABA", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "CRMUIF", "From": "520", "To": "520", "Subsequent": ""}, {"SoftwareComponent": "CRMUIF", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "712", "To": "712", "Subsequent": ""}, {"SoftwareComponent": "WEBCUIF", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "WEBCUIF", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "WEBCUIF", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "WEBCUIF", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "WEBCUIF", "From": "746", "To": "746", "Subsequent": ""}, {"SoftwareComponent": "CRMIS", "From": "400", "To": "400", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CRMIS 400", "SupportPackage": "SAPK-40013INCRMIS", "URL": "/supportpackage/SAPK-40013INCRMIS"}, {"SoftwareComponentVersion": "CRMUIF 520", "SupportPackage": "SAPK-52013INCRMUIF", "URL": "/supportpackage/SAPK-52013INCRMUIF"}, {"SoftwareComponentVersion": "CRMUIF 600", "SupportPackage": "SAPK-60016INCRMUIF", "URL": "/supportpackage/SAPK-60016INCRMUIF"}, {"SoftwareComponentVersion": "WEBCUIF 700", "SupportPackage": "SAPK-70013INWEBCUIF", "URL": "/supportpackage/SAPK-70013INWEBCUIF"}, {"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70028", "URL": "/supportpackage/SAPKA70028"}, {"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70029", "URL": "/supportpackage/SAPKA70029"}, {"SoftwareComponentVersion": "WEBCUIF 701", "SupportPackage": "SAPK-70110INWEBCUIF", "URL": "/supportpackage/SAPK-70110INWEBCUIF"}, {"SoftwareComponentVersion": "BBPCRM 702", "SupportPackage": "SAPKU70204", "URL": "/supportpackage/SAPKU70204"}, {"SoftwareComponentVersion": "WEBCUIF 730", "SupportPackage": "SAPK-73005INWEBCUIF", "URL": "/supportpackage/SAPK-73005INWEBCUIF"}, {"SoftwareComponentVersion": "WEBCUIF 731", "SupportPackage": "SAPK-73104INWEBCUIF", "URL": "/supportpackage/SAPK-73104INWEBCUIF"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "WEBCUIF", "NumberOfCorrin": 4, "URL": "/corrins/0001699357/6555"}, {"SoftwareComponent": "SAP_ABA", "NumberOfCorrin": 1, "URL": "/corrins/0001699357/44"}, {"SoftwareComponent": "CRMUIF", "NumberOfCorrin": 2, "URL": "/corrins/0001699357/4415"}, {"SoftwareComponent": "CRMIS", "NumberOfCorrin": 1, "URL": "/corrins/0001699357/658"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_ABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKA70004 - SAPKA70027&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/></P> <OL>1. Create a new message class IC_CCS_MCM in Package IC_CCS_MCM</OL> <OL>2. Add the following messages to message class IC_CCS_MCM:</OL> <P></P> <OL><OL>a) Message number: 100</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message short text: E-mail contained malicious content; content has been deleted <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Self explanatory: X <OL><OL>a) Message number: 101</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message short text: There is a problem with the virus scanner; contact your administrator <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Self explanatory: X <OL><OL>a) Message number: 102</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message short text: E-mail contained a blocked file or MIME type; content has been deleted <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Self explanatory: X <OL>1. Ensure that note 1640285 is implemented</OL> <OL>2. Create a new Virus Scan Profile /IC_CCS_MCM/ICI_MAIL (IMG Path SAP  NetWeaver-&gt;Application Server-&gt;System Administration-&gt;Virus Scan  Interface-&gt;Define Virus Scan Profiles) with the following values:</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Scan Profile: /IC_CCS_MCM/ICI_MAIL <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Profile Text: Viren Scan Profil for e-mail via Integrated Communication Interface <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Evaluate Profile Configuration Parameter: X <OL><OL>a) Enter the following Profile Configuration Parameter for the newly created Virus Scan Profile:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CUST_ACTIVE_CONTENT = 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CUST_CHECK_MIME_TYPE = 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CUST_MIME_TYPES_ARE_BLACKLIST = 1 <OL><OL>a) Enter the following MIME-Types for the newly crates Virus Scan Profile:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/ecmascript <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/java-archive <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/javascript <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-jar <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-java-jar <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-javascript <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-shockwave-flash <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-silverlight <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-silverlight-2 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-silverlight-app <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;text/javascript <OL>1. Before implementing this note, please check table SEOSUBCO. Search for entries with</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CLSNAME : CL_CRM_MCM_MAIL_MESSAGE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CMPNAME : CREATE_BY_BCB_MESSAGE. <OL><OL>a) If you find there already 5 entries, please select entry</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CLSNAME&nbsp;&nbsp;&nbsp;&nbsp; CL_CRM_MCM_MAIL_MESSAGE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CMPNAME&nbsp;&nbsp;&nbsp;&nbsp; CREATE_BY_BCB_MESSAGE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SCONAME&nbsp;&nbsp;&nbsp;&nbsp; BCB_MIME_MESSAGE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CMPTYPE&nbsp;&nbsp;&nbsp;&nbsp; 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;MTDTYPE&nbsp;&nbsp;&nbsp;&nbsp; 0 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SCOTYPE&nbsp;&nbsp;&nbsp;&nbsp; 0 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DESCRIPT <OL>1. Delete that entry if it is already there. Otherwise you will run  into issues while implementing the corrections via SNOTE.</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(via small simple report e.g.  as attached to the note) <OL>1. This is only relevant for CRM 5.0 (correction in SAP_ABA) until CRM 7.0 EHP 1 (correction in WEBCUIF).</OL> <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; CRMIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP CRM 4.0 Add...|<br/>| Release 400&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-40012INCRMIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>Create a new message class CRM_IC_MCM in Package CRM_IC_MCM<br/>Add the following messages to message class CRM_IC_MCM:<br/>Message number: 100<br/>Message short text: E-mail contained malicious content; content has been deleted<br/>Self explanatory: X<br/>Message number: 101<br/>Message short text: There is a problem with the virus scanner; contact your administrator<br/>Self explanatory: X<br/>Message number: 102<br/>Message short text: E-mail contained a blocked file or MIME type; content has been deleted<br/>Self explanatory: X<br/>Ensure that note 1640285 is implemented<br/>Create a new Virus Scan Profile /CRM_IC_MCM/ICI_MAIL (IMG Path SAP  NetWeaver-&gt;Application Server-&gt;System Administration-&gt;Virus Scan  Interface-&gt;Define Virus Scan Profiles) with the following values:<br/>Scan Profile: /CRM_IC_MCM/ICI_MAIL<br/>Profile Text: Viren Scan Profil for e-mail via Integrated Communication Interface<br/>Evaluate Profile Configuration Parameter: X<br/>Enter the following Profile Configuration Parameter for the newly created Virus Scan Profile:<br/>CUST_ACTIVE_CONTENT = 1<br/>CUST_CHECK_MIME_TYPE = 1<br/>CUST_MIME_TYPES_ARE_BLACKLIST = 1<br/>Enter the following MIME-Types for the newly crates Virus Scan Profile:<br/>application/ecmascript<br/>application/java-archive<br/>application/javascript<br/>application/x-jar<br/>application/x-java-jar<br/>application/x-javascript<br/>application/x-shockwave-flash<br/>application/x-silverlight<br/>application/x-silverlight-2<br/>application/x-silverlight-app<br/>text/javascript<br/>before implementing this note, please check table SEOSUBCO. Search for entries with<br/>CLSNAME : CL_CRM_MCM_MAIL_MESSAGE<br/>CMPNAME : CREATE_BY_BCB_MESSAGE.<br/>If you find there already 5 entries, please select entry<br/>CLSNAME&nbsp;&nbsp;&nbsp;&nbsp; CL_CRM_MCM_MAIL_MESSAGE<br/>CMPNAME&nbsp;&nbsp;&nbsp;&nbsp; CREATE_BY_BCB_MESSAGE<br/>SCONAME&nbsp;&nbsp;&nbsp;&nbsp; BCB_MIME_MESSAGE<br/><br/>CMPTYPE&nbsp;&nbsp;&nbsp;&nbsp; 1<br/>MTDTYPE&nbsp;&nbsp;&nbsp;&nbsp; 0<br/>SCOTYPE&nbsp;&nbsp;&nbsp;&nbsp; 0<br/>DESCRIPT<br/>Delete that entry if it is already there. Otherwise you will run into issues while implementing the corrections via SNOTE.<br/>(via small simple report e.g. as attached to the note)<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; CRMUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP CRM UIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 520&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-52012INCRMUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60015INCRMUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPK-50001INCRMUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/></P> <OL>1. Create a new message class IC_CCS_MCM in Package IC_CCS_MCM</OL> <OL>2. Add the following messages to message class IC_CCS_MCM:</OL> <OL><OL>a) Message number: 100</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message short text: E-mail contained malicious content; content has been deleted <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Self explanatory: X <OL><OL>a) Message number: 101</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message short text: There is a problem with the virus scanner; contact your administrator <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Self explanatory: X <OL><OL>a) Message number: 102</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message short text: E-mail contained a blocked file or MIME type; content has been deleted <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Self explanatory: X <OL>1. Ensure that note 1640285 is implemented</OL> <OL>2. Create a new Virus Scan Profile /IC_CCS_MCM/ICI_MAIL (IMG Path SAP  NetWeaver-&gt;Application Server-&gt;System Administration-&gt;Virus Scan  Interface-&gt;Define Virus Scan Profiles) with the following values:</OL> <OL><OL>a) Scan Profile: /IC_CCS_MCM/ICI_MAIL</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Profile Text: Viren Scan Profil for e-mail via Integrated Communication Interface <OL><OL>a) Evaluate Profile Configuration Parameter: X</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enter the following Profile Configuration Parameter for the newly created Virus Scan Profile: <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CUST_ACTIVE_CONTENT = 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CUST_CHECK_MIME_TYPE = 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CUST_MIME_TYPES_ARE_BLACKLIST = 1 <OL>1. Enter the following MIME-Types for the newly crates Virus Scan Profile:</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/ecmascript <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/java-archive <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/javascript <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-jar <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-java-jar <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-javascript <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-shockwave-flash <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-silverlight <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-silverlight-2 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-silverlight-app <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;text/javascript <OL>1. before implementing this note, please check table SEOSUBCO. Search for entries with</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CLSNAME : CL_CRM_MCM_MAIL_MESSAGE <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CMPNAME : CREATE_BY_BCB_MESSAGE. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If you find there already 5 entries, please select entry <OL><OL>a) CLSNAME&nbsp;&nbsp;&nbsp;&nbsp; CL_CRM_MCM_MAIL_MESSAGE</OL></OL> <OL><OL>b) CMPNAME&nbsp;&nbsp;&nbsp;&nbsp; CREATE_BY_BCB_MESSAGE</OL></OL> <OL><OL>c) SCONAME&nbsp;&nbsp;&nbsp;&nbsp; BCB_MIME_MESSAGE</OL></OL> <OL><OL>d) </OL></OL> <OL><OL>e) CMPTYPE&nbsp;&nbsp;&nbsp;&nbsp; 1</OL></OL> <OL><OL>f) MTDTYPE&nbsp;&nbsp;&nbsp;&nbsp; 0</OL></OL> <OL><OL>g) SCOTYPE&nbsp;&nbsp;&nbsp;&nbsp; 0</OL></OL> <OL><OL>h) DESCRIPT</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Delete that entry if it is already there. Otherwise you will run into issues while implementing the corrections via SNOTE. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(via small simple report e.g.  as attached to the note) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This is only relevant for CRM 5. 0 (correction in SAP_ABA) until CRM 7.0 EHP 1 (correction in WEBCUIF). <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Pre-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; WEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-70001INWEBCUIF - SAPK-70012INWEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-70103INWEBCUIF - SAPK-70109INWEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-73101INWEBCUIF - SAPK-73103INWEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-73004INWEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/></P> <OL>1. Create a new message class IC_CCS_MCM in Package IC_CCS_MCM</OL> <OL>2. Add the following messages to message class IC_CCS_MCM:</OL> <OL><OL>a) Message number: 100</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message short text: E-mail contained malicious content; content has been deleted <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Self explanatory: X <OL><OL>a) Message number: 101</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message short text: There is a problem with the virus scanner; contact your administrator <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Self explanatory: X <OL><OL>a) Message number: 102</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message short text: E-mail contained a blocked file or MIME type; content has been deleted <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Self explanatory: X <OL>1. Ensure that note 1640285 is implemented</OL> <OL>2. Create a new Virus Scan Profile /IC_CCS_MCM/ICI_MAIL (IMG Path SAP  NetWeaver-&gt;Application Server-&gt;System Administration-&gt;Virus Scan  Interface-&gt;Define Virus Scan Profiles) with the following values:</OL> <OL><OL>a) Scan Profile: /IC_CCS_MCM/ICI_MAIL</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Profile Text: Viren Scan Profil for e-mail via Integrated Communication Interface <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Evaluate Profile Configuration Parameter: X <OL><OL>a) Enter the following Profile Configuration Parameter for the newly created Virus Scan Profile:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CUST_ACTIVE_CONTENT = 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CUST_CHECK_MIME_TYPE = 1 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CUST_MIME_TYPES_ARE_BLACKLIST = 1 <OL><OL>a) Enter the following MIME-Types for the newly crates Virus Scan Profile:</OL></OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/ecmascript <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/java-archive <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/javascript <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-jar <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-java-jar <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-javascript <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-shockwave-flash <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-silverlight <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-silverlight-2 <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;application/x-silverlight-app <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;text/javascript <OL>1. before implementing this note, please check table SEOSUBCO. Search for entries with</OL> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CLSNAME : CL_CRM_MCM_MAIL_MESSAGE<br/>CMPNAME : CREATE_BY_BCB_MESSAGE. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;If you find there already 5 entries, please select entry <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<U>CLSNAME&nbsp;&nbsp;&nbsp;&nbsp; CL_CRM_MCM_MAIL_MESSAGE</U>  <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<U>CMPNAME&nbsp;&nbsp;&nbsp;&nbsp; CREATE_BY_BCB_MESSAGE</U>  <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<U>SCONAME&nbsp;&nbsp;&nbsp;&nbsp; BCB_MIME_MESSAGE</U>  <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<U>CMPTYPE&nbsp;&nbsp;&nbsp;&nbsp; 1</U> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<U>MTDTYPE&nbsp;&nbsp;&nbsp;&nbsp; 0</U> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<U>SCOTYPE&nbsp;&nbsp;&nbsp;&nbsp; 0</U> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<U>DESCRIPT</U> <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Delete that entry if it is already there. Otherwise you will run into issues while implementing the corrections via SNOTE. <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(via small simple report e.g. as attached to the note) <P>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This is only relevant for CRM 5.0 (correction in SAP_ABA) until CRM 7.0 EHP 1 (correction in WEBCUIF). <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_ABA&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Application...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKA70004 - SAPKA70027&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>To activate the profile after implementing all the corrections, please go to SE38 and enter programm name:<br/><br/>RSVSCANCUST_PROFILE<br/><br/>Press F8. Switch to change mode. Select frmo the right hand frame the entry related to ICI_MAIL.<br/><br/>Click on Details button<br/><br/>Finally change check the ACTIVE box and save your changes.<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; CRMUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP CRM UIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 520&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-52012INCRMUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 600&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-60015INCRMUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 500&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPK-50001INCRMUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>To activate the profile after implementing all the corrections, please go to SE38 and enter programm name:<br/><br/>RSVSCANCUST_PROFILE<br/><br/>Press F8. Switch to change mode. Select frmo the right hand frame the entry related to ICI_MAIL.<br/><br/>Click on Details button<br/><br/>Finally change check the ACTIVE box and save your changes.<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; CRMIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP CRM 4.0 Add...|<br/>| Release 400&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-40012INCRMIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>To activate the profile after implementing all the corrections, please go to SE38 and enter programm name:<br/><br/>RSVSCANCUST_PROFILE<br/><br/>Press F8. Switch to change mode. Select frmo the right hand frame the entry related to ICI_MAIL.<br/><br/>Click on Details button<br/><br/>Finally change check the ACTIVE box and save your changes.<br/><br/><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; WEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-70001INWEBCUIF - SAPK-70012INWEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-70103INWEBCUIF - SAPK-70109INWEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-73101INWEBCUIF - SAPK-73103INWEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-73004INWEBCUIF&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>To activate the profile after implementing all the corrections, please go to SE38 and enter programm name:<br/><br/>RSVSCANCUST_PROFILE<br/><br/>Press F8. Switch to change mode. Select frmo the right hand frame the entry related to ICI_MAIL.<br/><br/>Click on Details button<br/><br/>Finally change check the ACTIVE box and save your changes.<br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 8, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 8, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "CRMIS", "ValidFrom": "400", "ValidTo": "400", "Number": "1699357 ", "URL": "/notes/1699357 ", "Title": "Connection to Virus Scan Interface in CA-GTF-IC-CHA", "Component": "CA-GTF-IC-CHA"}, {"SoftwareComponent": "CRMUIF", "ValidFrom": "600", "ValidTo": "600", "Number": "1699357 ", "URL": "/notes/1699357 ", "Title": "Connection to Virus Scan Interface in CA-GTF-IC-CHA", "Component": "CA-GTF-IC-CHA"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "700", "Number": "1699357 ", "URL": "/notes/1699357 ", "Title": "Connection to Virus Scan Interface in CA-GTF-IC-CHA", "Component": "CA-GTF-IC-CHA"}, {"SoftwareComponent": "WEBCUIF", "ValidFrom": "731", "ValidTo": "731", "Number": "1699357 ", "URL": "/notes/1699357 ", "Title": "Connection to Virus Scan Interface in CA-GTF-IC-CHA", "Component": "CA-GTF-IC-CHA"}]}, "CVSS": {"_label": "CVSS", "CVSS_Score": {"_label": "CVSS Score                                        ", "value": "0"}, "CVSS_Vector": {"_label": "CVSS Vector                                       ", "vectorValue": "", "vectorVersion": "3.0", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Attack Vector (AV)", "Value": ""}, {"Key": "Attack Complexity (AC)", "Value": ""}, {"Key": "Privileges Required (PR)", "Value": ""}, {"Key": "User Interaction (UI)", "Value": ""}, {"Key": "<PERSON><PERSON> (S)", "Value": ""}, {"Key": "Confidentiality Impact (C)", "Value": ""}, {"Key": "Integrity Impact (I)", "Value": ""}, {"Key": "Availability Impact (A)", "Value": ""}]}}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}