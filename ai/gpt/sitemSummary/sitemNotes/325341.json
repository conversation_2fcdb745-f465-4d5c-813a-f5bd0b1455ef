{"Request": {"Number": "325341", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 404, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001343142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000325341?language=E&token=FF5E7431D3181CA28C57E24E8D996120"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000325341", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000325341/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "325341"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.02.2002"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "325341 - Incorrect exception processing of user exit LWBON003"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The three components (user exits) of enhancement LWBON003 (Change settlement data before creation of settlement document) enable messages to be transferred to the settlement program via exceptions in the event of an error.<br />The evaluation of the exceptions by the settlement program however is incorrect. The behavior described below does not happen.<br />In particular, the system may display messages<br />MN438 \"Error executing enhancement &amp; (function exit &amp;)\"<br />MN382 \"Settlement data (rebate income) inconsistent (internal error)\".</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (purchasing), volume-based rebate, Transactions MEB4, MER4, reports RWMBON08, RWMBON38</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by a program error.<br />The correct program behavior is:<br />A message in the source code of the user exit with exception ERROR_MSG_xxxxxxxxx_INVALID is meant to terminate the settlement of the arrangement.A message with exception ERROR_MSG_xxxxxxxxx_VALID is copied to the settlement log. xxxxxxxxx is one of three texts \"turn_over\", \"arrang_value\" or \"settlement\" depending on which user exit is used (the historical assigning of names can no longer be changed).The list output contains all the condition records processed up to the termination. Possible further condition records are no longer processed. This exception should be selected if processing more condition records makes no sense, but if it would be useful to process the following arrangements.<br />Exception FATAL_ERROR_MSG_USER_EXIT also terminates the settlement run completely. The following arrangements are no longer processed.The system displays message MN277 \"Report RWMBON01 terminated (see list log)\".<br />Example:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MESSAGE E330(NAA)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WITH L_PLANT_HEADER-BUKRS I_ARRANGEMENT-KNUMA<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; I_CONDITION_RECORD-KSCHL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RAISING ERROR_MSG_TURN_OVER_INVALID.<br />* Message:Tax code (tax-exempt transaction output tax) missing for company code &amp;<br />This situation could also justify exception FATAL_ERROR_MSG_USER_EXIT.A settlement of more arrangements probably results in the same error, so that you should interrupt the settlement in order first to correct the system settings.<br />A message with exception ERROR_MSG_xxxxxxxxx_VALID is copied to the settlement log. xxxxxxxxx is one of three texts \"turn_over\", \"arrang_value\" or \"settlement\" depending on which user exit is used (the historical assigning of names can no longer be changed).The following other condition records to be settled are processed.Whether settlement documents are created, depends on the category of the message.Settlement documents are created for messages of type I, W, S, that is, the business volume data or income data are valid.No settlement documents are created for type E, A, X messages.The processing of the arrangement is terminated in the check run with an error.<br />You also have the option of setting messages in the message log via table T_ERROR_MESSAGES.Type E, A, X messages always cause no settlement documents to be created.Therefore only type I, S or W messages should be entered at this point.<br />Other messages without exception specification, other exceptions cause the settlement to terminate similar to FATAL_ERROR_MSG_USER_EXIT.<br />At the same time the system displays messages MN438 \"Error executing enhancement LWBON003 (function exit &amp;)\" and MN277 \"Report RWMBON&amp; terminated (see list log)\".<br />The three exceptions allow the flexible integration of your source code into the SAP settlement program and at the same time ensure a high degree of security for the processing.<br />Refer to Note 215716 as an application example.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>See the corrections or import the Support Package.<br />Remember:If the implementation and transport are carried out manually, the problem can occur that the active enhancement LWBON003 is no longer executed on certain application servers after importing the repairs into the test or live system.Deactivate and activate the enhancement (the project) as a precaution or transport the project together with the source code corrections.<br />Set the \"Reference\" indicator in the interface of function module EXIT_SAPLWN01_001 (function group XWNX) with parameter E_TURN_OVER.<br />In addition add the exception FATAL_ERROR_MSG_USER_EXIT (important!).<br />Set the \"Reference\" indicator in the interface of function module EXIT_SAPLWN01_002 (function group XWNX) for parameter E_SETTLEMENT_VALUES.<br />In addition add the exception FATAL_ERROR_MSG_USER_EXIT (important!).<br />Set the \"Reference\" indicator in the interface of function module EXIT_SAPLWN01_003 (function group XWNX) for parameter E_SETTLEMENT_VALUES.<br />As of Release 4.5:to the interface of function module MM_ARRANG_COND_REC_EVALUATION (function group WN01).<br />Add exception FATAL_ERROR_MSG_USER_EXIT to the interface of function module MM_ARRANG_SETTLEMENT (function group WN01) (important!).<br />Release 4.0B only: Create the messages.<br />could not be found\"<br />MN789 \"No. range int. &amp;1 \"Coll.<br />processing of cust. bill.<br />docs.\"<br />does not exist\"<br />MN795 \"Scope of statement &amp; (settlement documents) not defined\"<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023678)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000325341/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000325341/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000325341/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000325341/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000325341/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000325341/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000325341/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000325341/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000325341/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716"}, {"RefNumber": "195730", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN684 in rebate arrangement settlement", "RefUrl": "/notes/195730"}, {"RefNumber": "183379", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Composite SAP note subsequent settlement (Purchasing) 4.6", "RefUrl": "/notes/183379"}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725"}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "183379", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Composite SAP note subsequent settlement (Purchasing) 4.6", "RefUrl": "/notes/183379 "}, {"RefNumber": "104668", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.0", "RefUrl": "/notes/104668 "}, {"RefNumber": "152725", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 4.5", "RefUrl": "/notes/152725 "}, {"RefNumber": "195730", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN684 in rebate arrangement settlement", "RefUrl": "/notes/195730 "}, {"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B64", "URL": "/supportpackage/SAPKH40B64"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B43", "URL": "/supportpackage/SAPKH45B43"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B31", "URL": "/supportpackage/SAPKH46B31"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C22", "URL": "/supportpackage/SAPKH46C22"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/0000325341/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 8, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "40A", "ValidTo": "40B", "Number": "122727 ", "URL": "/notes/122727 ", "Title": "Message MN177 with interim settlement", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "169540 ", "URL": "/notes/169540 ", "Title": "MN371 when settling arrangement", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "215716 ", "URL": "/notes/215716 ", "Title": "Message MN186 (settlement of fixed amounts)", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "315769 ", "URL": "/notes/315769 ", "Title": "Subseqnt updatng of bus.volume, price determtn goods receipt", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "407972 ", "URL": "/notes/407972 ", "Title": "Error in check routine for EXIT_SAPLWN01_003", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45A", "ValidTo": "45B", "Number": "197417 ", "URL": "/notes/197417 ", "Title": "Message 06167 when settling an arrangement", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "45B", "Number": "215716 ", "URL": "/notes/215716 ", "Title": "Message MN186 (settlement of fixed amounts)", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "45B", "Number": "315769 ", "URL": "/notes/315769 ", "Title": "Subseqnt updatng of bus.volume, price determtn goods receipt", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "45B", "Number": "324686 ", "URL": "/notes/324686 ", "Title": "Message MN145 during rebate arrangement settlement", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "46C", "Number": "195730 ", "URL": "/notes/195730 ", "Title": "Message MN684 in rebate arrangement settlement", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46B", "ValidTo": "46C", "Number": "215716 ", "URL": "/notes/215716 ", "Title": "Message MN186 (settlement of fixed amounts)", "Component": "MM-PUR-VM-SET"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46B", "ValidTo": "46C", "Number": "324686 ", "URL": "/notes/324686 ", "Title": "Message MN145 during rebate arrangement settlement", "Component": "MM-PUR-VM-SET"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}