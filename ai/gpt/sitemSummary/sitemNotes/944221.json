{"Request": {"Number": "944221", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 254, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016308892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000944221?language=E&token=991F81573BB60249CE693B9E7BB6258B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000944221", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000944221/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "944221"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 33}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.08.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SRV-FP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Forms Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis Services/Communication Interfaces", "value": "BC-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Forms Processing", "value": "BC-SRV-FP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV-FP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "944221 - Error analysis for problems in form processing"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to activate or output a PDF form and receive an error message. You cannot print or display forms in the preview or generate or edit interactive forms, or these processes are defective.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Interactive Forms by Adobe, Adobe document services (ADS), Adobe LiveCycle Designer, Forms Processing</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Causes<br />Possible causes are that the configuration of Adobe document services is incorrect, there are errors in the form or the printer configuration is incorrect.<br /><br />Prerequisites<br />For the <strong>printing or the print preview</strong> of PDF-based forms, you require a PCL-Postscript printer or a ZPL printer and you must use a relevant device type (for example, POST2, HPLJ4, HP9500, PDF1, AZPL203, AZPL300).<br />For more information, see SAP Note 685571 and under \"Printing PDF-Based Forms\" in the SAP Print Handbook (BC-CCM-PRN). See SAP Help Portal under:<br />http://help.sap.com/saphelp_nw70/helpdata/de/25/6fa8fd27837846990a7a6813b06f80/frameset.htm<br />If you want to use a printer with the device type SAPWIN/SWIN,<br />read SAP Note 1444342.<br /><br />To generate <strong>interactive forms</strong>, you require a credential, which you must register as described in the Adobe Configuration Guide.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In order to find the cause of the error, proceed as follows (depending on the scenario that you use): Note that you require administration authorization for some steps.<br /><br />Contents</p>\r\n<p><strong>I ABAP:</strong></p>\r\n<p>1. Test program FP_TEST_00<br />2. Test program FP_PDF_TEST_00, RFC connection to ADS<br />3. Test of user and password<br />4. Test of destination service<br />5. Interactive Forms: Test program FP_TEST_IA_01 (credential test)<br />6. Generated PDF with additional information<br />7. Activating ADS trace on AS Java</p>\r\n<p><br /><strong>II Web Dynpro for ABAP</strong></p>\r\n<p>1. Test program FP_TEST_00<br />2. Test program FP_PDF_TEST_00, RFC connection to ADS<br />3. Test of user and password<br />4. Test of destination service<br />5. Interactive Forms: Test program FP_TEST_IA_01 (credential test)<br />6. Generated PDF with additional information<br />7. Activation of ADS trace on AS Java<br /><br /></p>\r\n<p><strong>III Web Dynpro for Java</strong></p>\r\n<p>1. Test of user and password<br />2. Check of Web service destinationConfigPort_Document<br />3. Check to determine whether credential is installed and configured.<br />4. Generated PDF with additional information<br />5. Activation of ADS trace on AS Java<br /><br /></p>\r\n<p><br /><br /><strong>I ABAP:</strong><br /><br /></p>\r\n<p>The following test steps are aso described on SAP Help Portal in the chapter \"Configuration Check for PDF-Based Forms in ABAP\"<br />of the Adobe Document Services Configuration Guide:<br />SAP NetWeaver 7.0, 7.0x:<br />http://help.sap.com/saphelp_nw70/helpdata/en/43/f31e3082221595e10000000a1553f7/frameset.htm<br /><br />SAP NetWeaver 7.1 and higher:<br />http://help.sap.com/saphelp_nw73/helpdata/en/4b/94e945ea576e82e10000000a421937/frameset.htm<br /><br /><br />1. Use transaction SA38 to execute the program <strong>FP_TEST_00</strong>.</p>\r\n<ul>\r\n<ul>\r\n<li>If the FP_TEST_00 form is displayed in the print preview, the ADS configuration is correct.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; If you use scenarios with an interactive PDF, proceed with point 5. Otherwise, proceed with the test under point 6.</p>\r\n<ul>\r\n<ul>\r\n<li>If the system displays an error message instead of a form, the configuration of ADS is incorrect.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; Carry out the additional tests from point 2 onwards.<br /> <br />2. Use transaction SA38 to execute the program <strong>FP_PDF_TEST_00</strong>.<br /> This enables you to check the <strong>RFC connection to ADS</strong>.</p>\r\n<ul>\r\n<ul>\r\n<li>If the system displays the version number of ADS, the configuration of the RFC connection is correct.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; Proceed to point 4.</p>\r\n<ul>\r\n<ul>\r\n<li>You receive one of the following error messages:</li>\r\n</ul>\r\n</ul>\r\n<p>2.1. If the system displays dialog fields for user and password instead of the version number, the entries for user and password in the RFC connection do not match the entries in user management.<br /> -&gt; Check the user and the password. Continue with point 3.<br /> 2.2. The system displays a \"SYSTEM ERROR\" with the following text:<br /> \"ERROR CODE : 100.101\"<br /> \"ERROR MESSAGE : SOAP Runtime Exception: CSoapExceptionTransport : HTTP send returned with status code\"<br /> oder<br /> \"ERROR MESSAGE : SOAP Runtime Exception: CSoapExceptionTransport : HTTP receive failed with exception communication_failure\"<br /> -&gt; Contact your system administrator and ask them to check or correct the details for target device, service number and path prefix in the RFC connection (transaction SM59).<br /> If all of these entries are correct and the problem still persists, you should check whether the AS Java is started correctly. Read SAP Note 1587941 also.<br /><br /> 2.3. If the system displays an error message stating that there is no valid HTTP connection, then there is no RFC connection to ADS.<br /> -&gt; Contact your system administrator and ask them to correct or create the RFC connection (transaction SM59).<br /> 2. 4 The system does not respond.<br /> -&gt; Follow the instructions in SAP Notes 783185 and 1587941.<br /> <br />3. Check the <strong>user and password</strong> of ADS<br /> <strong>Prerequisite: </strong>To perform this test, you must know the user and password for ADS. If you do not know this information, contact your system administrator and ask them to carry out the test.</p>\r\n<p>Note: These instructions can be found in the documentation for the Adobe Configuration Guide in the chapter \"Checking the User and Password\".</p>\r\n<p>For <strong><strong>SAP NetWeaver 7.0, 7. 01 and higher</strong></strong>, proceed as follows:</p>\r\n<p>1. In your Web browser, enter the following URL:<br /> http://&lt;server&gt;:&lt;port&gt;/AdobeDocumentServices/Config<br /> &lt;server&gt;:&lt;port&gt; are the name and port of the AS Java on which the Adobe Document Services run.<br /> Log on as the administrator.<br /> 2. The Web page of the Web service Adobe Document Services is displayed. Choose \"Test\".<br /> 3. Choose rpdata(test..)<br /> 4. Choose \"Send\". No further parameter entries are required.<br /> 5. Enter \"ADSUser\" for the user and enter the relevant password.<br /> 6. Choose \"Submit\".<br /> As of SAP NetWeaver 7.1 and higher, the test is performed using the Web Service Navigator. To call the Web Service Navigator, enter http://&lt;server&gt;:&lt;port&gt;/wsnavigator. Caution: You require the user and password for the WS Navigator.</p>\r\n<p>In <strong>SAP NetWeaver 7.10</strong>, you filter according to the service \"com.adobe/AdobeDocumentServices_com.adobe_AdobeDocumentServicesVi\" and execute the operation rpData similar to the procedure described above.</p>\r\n<p><strong>As of NetWeaver 7.11 </strong>and higher, enter \"Provider System\" and \"Local AS Java\" as the search type to search for the service interface.</p>\r\n<p>Choose \"AdobeDocumentServicesVi\" from the list of service interfaces. Choose \"Continue\" - no further parameter entries are required - until the system displays the dialog box for the user and password. Then enter \"ADSUser\" and the relevant password.</p>\r\n<p>Result of the test for user and password:</p>\r\n<ul>\r\n<ul>\r\n<li>If the system displays the version number of ADS, the configuration of the user and password is correct.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; Proceed to the next point (4).</p>\r\n<ul>\r\n<ul>\r\n<li>If the system does not display a version number (the page in the Web browser does not change, and submit continues to be displayed), the configuration of ADS is incorrect.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; Contact your system administrator and ask them to correct the configuration for the user and password.<br /> <br />4. Check the <strong>settings for the destination service</strong>.<strong> </strong><br /> To use the destination service on AS Java, settings are required on AS ABAP and AS Java. Therefore, this test contains several steps.</p>\r\n<p>4. 1 In your ABAP system (transaction SA38), execute the following programs in succession:</p>\r\n<p>1. FP_CHECK_DESTINATION_SERVICE<br /> 2. FP_CHECK_HTTP_DATA_TRANSFER (this program only if you bundle your forms).<br /> a) First of all, execute the program without selecting the option \"With Destination Service\".</p>\r\n<p>The system processes a test form in the background and displays the size of the generated PDF. It does not use the destination service in the process.</p>\r\n<p>Caution: If the system issues an error message here, read SAP Note 1587941.</p>\r\n<p>b) Now select the option \"With Destination Service\" and execute the program again. The system processes the test form, using the destination service. </p>\r\n<ul>\r\n<ul>\r\n<li>If the system displays the same message (file size of generated PDF) as it does in a), the configuration of the destination service is correct.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; If you use scenarios with an interactive PDF, proceed with point 5. For print forms, proceed with point 6.</p>\r\n<ul>\r\n<ul>\r\n<li>If the system issues an error message, the configuration of the destination service is incorrect.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; If an error message is issued when you execute FP_CHECK_DESTINATION_SERVICE, execute the following tests 4.2 to 4.4.<br /> -&gt; If an error message is issued when you execute FP_CHECK_HTTP_DATA_TRANSFER, you require the ADS runtime information (trace files) for further troubleshooting. You can find out how to determine this runtime information under point 6. After you have made the settings, call the program again. The system saves the runtime information locally on your front-end PC.<br /> Create a customer message under the component BC-SRV-FP, describe the problem, and attach the trace file with the runtime information to the message.<br /><br /> 4. 2 Check whether the following ICF services are active (transaction SICF):</p>\r\n<ul>\r\n<ul>\r\n<li>/default_host/sap/bc/fp</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>/default_host/sap/bc/fpads</li>\r\n</ul>\r\n</ul>\r\n<p>If this service is not active, activate it. Note: You requre the ICF service fpads as of NW7.0 Support Package Stack 12 for bundling forms in distributed systems.</p>\r\n<p>4. 3 Check the settings for the ICF service \"fp\".</p>\r\n<p>a) Enter the following URL in your Web browser:</p>\r\n<p>http://&lt;server&gt;:&lt;port&gt;/sap/bc/fp/form/layout/fp_test_00.xdp<br /> &lt;server&gt; is the AS ABAP, &lt;port&gt; is the HTTP port of the AS ABAP. (you can determine this information using transaction SICF).<br /> b) In the dialog box for the user, enter ADS_AGENT and the relevant password.</p>\r\n<ul>\r\n<ul>\r\n<li>If the ICF service settings are correct, the browser displays the layout information of the FP_TEST_00 form in XML format.<br />Caution: As of SAP NetWeaver 7.2, the browser tries to start the Adobe Reader. The system issues the following error message: 'Unable to locate the form, because the XFA data lacks.a reference...'. If the system issues this message, the ICF settings are correct.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; Proceed with point 4.4.</p>\r\n<ul>\r\n<ul>\r\n<li>If the browser displays an error message instead of the aforementioned XML file, a configuration error occurred.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; Check whether the user ADS_AGENT exists on the AS ABAP, and whether the user has the required authorizations and roles (SAP_BC_FP_ICF or SAP_BC_FPADS_ICF). Note: Make sure that the relevant traffic lights for the status of the the profile comparison are green.</p>\r\n<p><span style=\"font-size: 14px;\"></span><span style=\"font-size: 14px;\">If all settings are correct, create a SEC_TRACE_ANALYZER trace in accordance with SAP KBA 2333324.</span></p>\r\n<p><br /> 4.4. Check the settings for the destination service on AS Java</p>\r\n<p>a) <strong><strong>Up to SAP NetWeaver 7.0x:</strong></strong> Call the Visual Administrator and navigate to Services -&gt; Destinations -&gt; HTTP<br /><strong>SAP NetWeaver 7.10 and higher:</strong>  Open the SAP NetWeaver Administrator (http://&lt;JEE-Server&gt;:&lt;HTTP-Port&gt;/nwa). Choose \"Configuration Management -&gt; Security -&gt; Destinations\".</p>\r\n<p>Check whether the entry FP_ICF_DATA_&lt;SID&gt; exists and is entered correctly.</p>\r\n<p>Destination Type: HTTP destination</p>\r\n<p>Enhance the entry in the field <strong>URL </strong>under <strong>Connection Settings (SAP NetWeaver 7.0x) or Connection and Transport (SAP NetWeaver 7.10 and higher)</strong> to: http://&lt;server&gt;:&lt;port&gt;<strong>/sap/bc/fp/form/layout/fp_test_00.xdp</strong>.</p>\r\n<p>The existing entries &lt;server&gt; and &lt;port&gt; refer to the AS ABAP. The ABAP HTTP port is provided in transaction SMICM; choose Goto -&gt; Services.</p>\r\n<p>&#x00A0;</p>\r\n<p>b) Up to SAP NetWeaver 7.0x, choose \"Save and Test\"; as of SAP NetWeaver 7.10, choose \"Ping Destination\".</p>\r\n<p>c) The system calls the ABAP system that stores the form templates.</p>\r\n<ul>\r\n<ul>\r\n<li>If the settings are correct, the system issues the message: \"HTTP GET response code 200 Content Type text/xml.\"</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; If you use scenarios with an interactive PDF, proceed with point 5. For print forms, proceed with point 6.</p>\r\n<ul>\r\n<ul>\r\n<li>If the system issues an error message, the configuration of the destination service is incorrect.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; Contact your system administrator and ask them to correct the settings for the destination service.<br /> d) <strong>Do not forget to change the URL back to http://&lt;server&gt;:&lt;port&gt;.</strong></p>\r\n<p>&#x00A0;</p>\r\n<p>5. Check whether the <strong>credential</strong> is configured.<br /> You must perform this step only if you use interactive scenarios. If you only use print scenarios, proceed with point 6.</p>\r\n<p>Use transaction SA38 to execute the program <strong>FP_TEST_IA_01</strong>.</p>\r\n<ul>\r\n<ul>\r\n<li>If the system displays the form, the ReaderRights credential is installed and configured.</li>\r\n</ul>\r\n</ul>\r\n<p>-&gt; Proceed with point 6.</p>\r\n<ul>\r\n<ul>\r\n<li>If you receive an error message, for example, </li>\r\n</ul>\r\n</ul>\r\n<p>ADS: com.adobe.ProcessingException: com.adobe.Processin(200101)<br /> -&gt; Check whether you have installed and registered a credential. See Note 736902 and the Adobe Configuration Guide at:</p>\r\n<p><br /> SAP NetWeaver 7.0</p>\r\n<p><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw70/helpdata/en/56/f2c94a069f44a785b85748e11f82a0/frameset.htm\">http://help.sap.com/saphelp_nw70/helpdata/en/56/f2c94a069f44a785b85748e11f82a0/frameset.htm</a></p>\r\n<p><br /> SAP NetWeaver 7.1 and above</p>\r\n<p><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nwpi711/helpdata/en/56/f2c94a069f44a785b85748e11f82a0/frameset.htm\">http://help.sap.com/saphelp_nwpi711/helpdata/en/56/f2c94a069f44a785b85748e11f82a0/frameset.htm</a></p>\r\n<p><br /><br /><strong>6. Generated PDF with additional information</strong><br /> Call your form and save the generated PDF with additional information locally. You receive a PDF with attachments, which enable you to analyze the problem in detail.</p>\r\n<p>Information about the PDF with additional information is available in the problem analysis guide (PAG) for SAP NetWeaver (TM) at:</p>\r\n<p>http://help.sap.com/saphelp_nw70/helpdata/en/71/7ffb3f6c78ee28e10000000a1550b0/frameset.htm -&gt; \"Usage Type Application Server Java -&gt; Problem Analysis Scenario for Adobe Document Services -&gt; Adobe Rendering Error\"<br /> The following information about the procedure when setting this function can also be found in the documentation on SAP Interactive Forms by Adobe on the Help Portal under:</p>\r\n<p>http://help.sap.com/saphelp_nw70/helpdata/de/f7/a04aee0cb94978a011401311efe603/frameset.htm (German)<br /> http://help.sap.com/saphelp_nw70/helpdata/en/f7/a04aee0cb94978a011401311efe603/frameset.htm (English)<br /> You have the following options:</p>\r\n<p><strong>Setting using transaction SFP</strong></p>\r\n<p>1. Call the Form Builder (transaction SFP).<br /> 2. Choose \"Utilities -&gt; Settings\".<br /> 3. In the dialog box, choose the option \"Very Detailed Trace\" for \"Trace Level\" under \"Runtime Information\".<br /> 4. Enter a file name under which the runtime information is to be saved locally as a trace file on your front-end PC. This file is useful for subsequent troubleshooting.<br /> 5. Set the indicator \"PDF with Additional Information\" and specify a file name if a PDF display or preview is not possible in your application. With this option, the generated PDF with additional information is then saved locally to your front-end PC.<br /> <strong> Setting using user settings</strong></p>\r\n<p>You can also use the user settings to activate the function \"PDF with Additional Information\". Proceed as follows:</p>\r\n<p>1. Call transaction SU3 (\"Maintain User Profile\") or SU01 (\"User Maintenance\").<br /> Note that you require the relevant authorization for the user maintenance of other users.<br /> 2. Choose the tab page 'Parameters', and enter the required parameters and values: <br /> FPTRACELEVEL = 04<br />Setting the trace to the value required for the local saving of runtime information and for the generation of the PDF that contains additional information. <br /> FPTRACEFILE = &lt;directory&gt;\\&lt;file name&gt;<br />File for the local saving of runtime information This specification is optional.<br /> FPSAVEERRORPDF = X<br />The system generates a PDF that contains additional information. <br /> FPERRORPDFFILE = &lt;directory&gt;\\&lt;file name&gt;<br />File for locally saving the PDF that contains additional information<br /> <strong> Caution:</strong> Local saving is possible only for applications that have a GUI connection. It is therefore not supported for Web Dynpro ABAP.<br /> 3. These values then have a fixed assignment to the user. <br /> <strong> You must reset these settings after troubleshooting is completed</strong>.<br /> After you have made these settings, call your application for creating forms. The displayed PDF now contains the additional information that can be used for further troubleshooting. Save the displayed PDF.</p>\r\n<p>Attach these files (PDF with attachment) to an incident under the component BC-SRV-FP with a precise description of the problem.</p>\r\n<p>If you cannot generate a PDF, save the trace files and attach them to your incident as described above.</p>\r\n<p><br /><strong>7. Activate the ADS trace on AS Java.</strong><br /> If you cannot determine the cause of the problem with the aforementioned steps or cannot create a PDF with additional information, we recommend that you activate the ADS trace on AS Java. Reproduce the problem. All actions relating to the Adobe Document Services are then logged on the AS Java.</p>\r\n<p>Create a customer incident under the component BC-SRV-FP with an exact description of the problem and attach the trace files to the incident.</p>\r\n<p>You can find out how to activate the ADS trace in SAP Notes 846610 (NW7.0x) and 1128476 (NW 7.10 and higher).</p>\r\n<p><strong>II Web Dynpro for ABAP</strong><br /><br /></p>\r\n<p>To analyze problems with interactive forms, which are integrated in Web Dynpro for ABAP, you will find detailed information in SAP Note 999998.<br /><br />To determine whether the ADS configuration is correct, execute steps 1 to 5 that are described under I ABAP:<br /><br />1. Test program <strong>FP_TEST_00</strong><br />2. Test program <strong>FP_PDF_TEST_00</strong>, RFC connection to ADS <br />3. Test <strong>user and password</strong><br />4. Test destination service (only <strong>FP_CHECK_DESTINATION_SERVICE</strong>)<br />5. Interactive Forms: Test program <strong>FP_TEST_IA_01</strong>, credential test<br /><br /><strong>6. Generated PDF with additional information</strong><br /> Call your form in the Web Dynpro application and save the PDF with additional information that is generated by Adobe Reader. If the toolbar does not contain the save icon, use the F8 key to display it. You receive a PDF with attachments, which enable you to analyze the problem in detail.</p>\r\n<p><strong> Setting using user settings</strong></p>\r\n<p>You can only use the user settings to activate the function \"PDF with Additional Information\". Proceed as follows:</p>\r\n<p>1. Logon to the corresponding ABAP system.<br /> 2. Call transaction SU3 (\"Maintain User Profile\") or SU01 (\"User Maintenance\").<br /> Note that you require the relevant authorization for the user maintenance of other users.<br /> 3. Choose the 'Parameters' tab page and enter the required parameter and value:<br /> FPTRACELEVEL = 04<br />Sets the trace to the necessary value for generating the PDF with additional information.<br /> 4. The assignment of the value to the user is now fixed. <br /> 5. Exit the Web Dynpro application and logon again so that the user parameter configured previously takes affect.<br /> <strong> Reset these values after troubleshooting is completed</strong>.<br /> -&gt; Attach these files (PDF with attachment) to an incident with the component BC-SRV-FP with a precise description of the problem.</p>\r\n<p><br /><strong>7. Activate the ADS trace on AS Java.</strong><br /> See the description under I ABAP and in SAP Notes 846610 (NW7.0x) and 1128476 (NW 7.10 and higher).</p>\r\n<p><br /><br /></p>\r\n<p><strong>III Web Dynpro for Java</strong><br /><br /></p>\r\n<p>1. Check the <strong>user and password</strong> of ADS<br /> The procedure is the same as in chapter I ABAP \"3. Test user and password\".</p>\r\n<p>This information can also be found in the Adobe Document Services Configuration Guide in the Help Portal under:</p>\r\n<p><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw70/helpdata/de/37/504b8cbc2848a494facfdc09a359b1/frameset.htm\">http://help.sap.com/saphelp_nw70/helpdata/de/37/504b8cbc2848a494facfdc09a359b1/frameset.htm</a> -&gt; Adobe Document Services Configuration Guide -&gt; Configuring the Web Service -&gt; Securing Access to the Web Service -&gt;Configuration Check -&gt; Configuration Check for Interactive Forms in Web Dynpro for Java</p>\r\n<p><strong>2. Check of Web service destinationConfigPort_Document</strong><br /> As of SAP NetWeaver 7.10, the Web service destination ConfigPort_Document must be configured.</p>\r\n<p>Use SAP Note 1443819 to check the configuration.</p>\r\n<p><br /><br /><strong>3. Check whether the credential is configured.</strong><br /> Check whether you have installed and registered a credential. See SAP Note 736902 and the Adobe Configuration Guide at:</p>\r\n<p>SAP NetWeaver 7.0</p>\r\n<p><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw70/helpdata/en/56/f2c94a069f44a785b85748e11f82a0/frameset.htm\">http://help.sap.com/saphelp_nw70/helpdata/en/56/f2c94a069f44a785b85748e11f82a0/frameset.htm</a></p>\r\n<p>SAP NetWeaver 7.1 and above</p>\r\n<p><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nwpi711/helpdata/en/56/f2c94a069f44a785b85748e11f82a0/frameset.htm\">http://help.sap.com/saphelp_nwpi711/helpdata/en/56/f2c94a069f44a785b85748e11f82a0/frameset.htm</a></p>\r\n<p>&#x00A0;</p>\r\n<p>4. <strong>Generated PDF with additional information</strong><br /> As of SAP NetWeaver 7.0 Support Package 06, you can generate a PDF with attachments that enable detailed troubleshooting. The procedure depends on the Support Package level of SAP NetWeaver 7.0.</p>\r\n<p><strong><strong>SAP NetWeaver 7.0 SP09 and higher</strong> </strong></p>\r\n<ul>\r\n<li>Set the Trace Level for Web Dynpro to 'ALL'. You can find detailed information about setting the Trace Level in SAP Note 1976076.<br />Comment: To obtain a PDF with additional information, it suffices if you activate the trace for com.sap.tc.webdynpro.clientserver.uielib.adobe.impl and its subtrees.</li>\r\n</ul>\r\n<ul>\r\n<li>Call your form and save the generated PDF with additional information locally. This PDF with attachments is required later for detailed error analysis. Attach these files to a message with the component BC-WD-JAV, including a precise description of the problem.</li>\r\n</ul>\r\n<ul>\r\n<li>Reset the Web Dynpro Trace level.</li>\r\n</ul>\r\n<p><strong><strong>SAP NetWeaver 7.0 Support Package 06/07/08</strong></strong></p>\r\n<ul>\r\n<li>Extend your application URL with the \"sap-wd-adsTrace=4\" addition.</li>\r\n</ul>\r\n<p>Note that there will then be a display only if the system can create a PDF. If this is not the case, follow the instructions that are contained in Notes 742674 and 846712.</p>\r\n<p><strong><strong>SAP NetWeaver '04</strong></strong></p>\r\n<ul>\r\n<li>In these versions, the system does not display any PDFs with additional information, however, the trace files contain the notes that you require regarding the cause of the problem. Set the Trace Level for Web Dynpro to 'ALL'. You can find detailed information about setting the Trace Level in Note 742674.</li>\r\n</ul>\r\n<ul>\r\n<li>Call your form and save the trace files locally. These are required later for detailed error analysis. Attach these trace files to a message with the component BC-WD-JAV, including a precise description of the problem.</li>\r\n</ul>\r\n<ul>\r\n<li>Reset the Web Dynpro Trace level.</li>\r\n</ul>\r\n<p>Information about the PDF with additional information is available in the SAP NetWeaver Problem Analysis Guide (PAG) at:</p>\r\n<p>http://help.sap.com/saphelp_nw70/helpdata/en/71/7ffb3f6c78ee28e10000000a1550b0/frameset.htm -&gt; \"Usage Type Application Server Java -&gt; Problem Analysis Scenario for Adobe Document Services -&gt; Adobe Rendering Error\"<br /> <br /><strong>5. Activate the ADS trace on AS Java.</strong><br /> See the description under I ABAP and in SAP Notes 846610 (NW7.0x) and 1128476 (NW 7.10 and higher).</p>\r\n<p>&#x00A0;</p>\r\n<p>&#x00A0;</p>\r\n<p><br /><br /></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-WD-JAV (WebDynpro Java)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041271)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D065293)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000944221/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000944221/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000944221/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000944221/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000944221/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000944221/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000944221/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000944221/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000944221/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "959462", "RefComponent": "EP-PCT-MGR-CO", "RefTitle": "ISR/Adobe/Web Dynpro Java: Configuration problems", "RefUrl": "/notes/959462"}, {"RefNumber": "934378", "RefComponent": "XX-CSC-KR", "RefTitle": "SIBUIN: Broken layout in ERP2004 Unicode environment", "RefUrl": "/notes/934378"}, {"RefNumber": "915399", "RefComponent": "BC-SRV-FP", "RefTitle": "com.adobe.ProcessingError: File not found on: URL location:", "RefUrl": "/notes/915399"}, {"RefNumber": "894009", "RefComponent": "BC-SRV-FP", "RefTitle": "Adobe Document Services: Configuration", "RefUrl": "/notes/894009"}, {"RefNumber": "846712", "RefComponent": "BC-WD-JAV", "RefTitle": "Analysis when Adobe Forms does not show values", "RefUrl": "/notes/846712"}, {"RefNumber": "846610", "RefComponent": "BC-SRV-FP", "RefTitle": "How to activate ADS trace", "RefUrl": "/notes/846610"}, {"RefNumber": "842878", "RefComponent": "BC-WD-JAV-RUN", "RefTitle": "Switch on traces for Web Dynpro session management in NW04", "RefUrl": "/notes/842878"}, {"RefNumber": "836174", "RefComponent": "EP-PCT-MGR-CO", "RefTitle": "ISR/Adobe/Web Dynpro Java: Support for troubleshooting", "RefUrl": "/notes/836174"}, {"RefNumber": "742674", "RefComponent": "BC-WD-JAV", "RefTitle": "Required information for web dynpro problem reporting", "RefUrl": "/notes/742674"}, {"RefNumber": "736902", "RefComponent": "BC-SRV-FP", "RefTitle": "Adobe Credentials", "RefUrl": "/notes/736902"}, {"RefNumber": "685571", "RefComponent": "BC-CCM-PRN", "RefTitle": "Printing SAP Interactive Forms by Adobe", "RefUrl": "/notes/685571"}, {"RefNumber": "1589254", "RefComponent": "PY-IN", "RefTitle": "Form 16 central note for ADS configuration", "RefUrl": "/notes/1589254"}, {"RefNumber": "1587941", "RefComponent": "BC-SRV-FP", "RefTitle": "ADS does not respond or error \"CSoapExceptionTransport..\" occurs", "RefUrl": "/notes/1587941"}, {"RefNumber": "1530646", "RefComponent": "GRC-SPC-AD", "RefTitle": "Survey function in GRC", "RefUrl": "/notes/1530646"}, {"RefNumber": "1444342", "RefComponent": "BC-CCM-PRN", "RefTitle": "Device type-independent printing of ADS forms", "RefUrl": "/notes/1444342"}, {"RefNumber": "1317925", "RefComponent": "XX-PART-ADB-IFM", "RefTitle": "Maintaining backward compatibility with Designer", "RefUrl": "/notes/1317925"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}, {"RefNumber": "1258649", "RefComponent": "BC-SRV-FP", "RefTitle": "Composite SAP Note : HCM Forms Documentation", "RefUrl": "/notes/1258649"}, {"RefNumber": "1156407", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Enhan.Pk.4 SP Stack 01(11/2008) - Release Info. Note", "RefUrl": "/notes/1156407"}, {"RefNumber": "1128476", "RefComponent": "BC-SRV-FP", "RefTitle": "How to activate ADS trace in NW7.10 and higher", "RefUrl": "/notes/1128476"}, {"RefNumber": "1097565", "RefComponent": "BC-WD-JAV-ADB", "RefTitle": "Reporting issues on Interactive Form in Web Dynpro for Java", "RefUrl": "/notes/1097565"}, {"RefNumber": "1069027", "RefComponent": "BC-SRV-FP", "RefTitle": "Exception in method rpData from WebServiceNavigator", "RefUrl": "/notes/1069027"}, {"RefNumber": "1052659", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Requirements for customer messages concerning export to PDF", "RefUrl": "/notes/1052659"}, {"RefNumber": "1022238", "RefComponent": "BC-SRV-FP", "RefTitle": "Form FP_TEST_42 for simple test of several fonts", "RefUrl": "/notes/1022238"}, {"RefNumber": "1007116", "RefComponent": "BC-SRV-FP", "RefTitle": "Form output in wrong language", "RefUrl": "/notes/1007116"}, {"RefNumber": "1004321", "RefComponent": "BC-SRV-FP", "RefTitle": "ADS error: Incorrect content type found 'text/html'", "RefUrl": "/notes/1004321"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3267028", "RefComponent": "BC-SRV-FP", "RefTitle": "Error writing output to URL when trying to  print more than one page of a PDF spool", "RefUrl": "/notes/3267028 "}, {"RefNumber": "3338995", "RefComponent": "BC-SRV-FP", "RefTitle": "How to change the password for ADSUSER", "RefUrl": "/notes/3338995 "}, {"RefNumber": "3322152", "RefComponent": "BC-SRV-FP", "RefTitle": "ADS is not working in any other client", "RefUrl": "/notes/3322152 "}, {"RefNumber": "2011358", "RefComponent": "BC-CST-UP", "RefTitle": "Update work process running for long time SAPLHTTP_RUNTIME report", "RefUrl": "/notes/2011358 "}, {"RefNumber": "2374102", "RefComponent": "BC-SRV-FP", "RefTitle": "Unable to locate the form, XFA data lacks reference", "RefUrl": "/notes/2374102 "}, {"RefNumber": "1675976", "RefComponent": "BC-SRV-FP", "RefTitle": "Password pop-up in ADS", "RefUrl": "/notes/1675976 "}, {"RefNumber": "2801054", "RefComponent": "PY-IN", "RefTitle": "Error Layout set HR_INF16_2019_B in language EN does not exist while executing FORM 16", "RefUrl": "/notes/2801054 "}, {"RefNumber": "2800335", "RefComponent": "MM-PUR-ADB", "RefTitle": "Invalid HTTP connection: ADS when using MM_PUR_PURCHASE_ORDER", "RefUrl": "/notes/2800335 "}, {"RefNumber": "2739278", "RefComponent": "PY-IT", "RefTitle": "CU: Generation/Printing Problems in Forms Processed by ADS", "RefUrl": "/notes/2739278 "}, {"RefNumber": "2763565", "RefComponent": "BC-SRV-FP", "RefTitle": "Ping http destination FP_ICF_DATA_<SID> returns 404 code", "RefUrl": "/notes/2763565 "}, {"RefNumber": "2661105", "RefComponent": "PPM-PRO", "RefTitle": "Project Management Status Report cannot be created", "RefUrl": "/notes/2661105 "}, {"RefNumber": "2359109", "RefComponent": "BC-SRV-FP", "RefTitle": "Error occurs when uploading forms in transaction SFP", "RefUrl": "/notes/2359109 "}, {"RefNumber": "1580196", "RefComponent": "SLL-LEG-FUN-PRN", "RefTitle": "KBA: Error occurred when uploading file (unknown file format) - Error message FPUIFB068", "RefUrl": "/notes/1580196 "}, {"RefNumber": "2523958", "RefComponent": "BC-SRV-FP", "RefTitle": "ADS exception after upgrade from 7.0x to 7.3 or higher", "RefUrl": "/notes/2523958 "}, {"RefNumber": "2514677", "RefComponent": "BC-SRV-FP", "RefTitle": "How to generate large PDF files by ADS", "RefUrl": "/notes/2514677 "}, {"RefNumber": "2508800", "RefComponent": "PPM-PRO", "RefTitle": "Error \"You cannot begin approval without an approval document\"", "RefUrl": "/notes/2508800 "}, {"RefNumber": "2472828", "RefComponent": "BC-SRV-FP", "RefTitle": "Issue with special characters in PDF/A document generated by ADS", "RefUrl": "/notes/2472828 "}, {"RefNumber": "1689810", "RefComponent": "FI-LOC-FI-JP-IVS", "RefTitle": "Error message FPRUNX102 occurs in transaction code ISJP_PR", "RefUrl": "/notes/1689810 "}, {"RefNumber": "1906899", "RefComponent": "FI-TV", "RefTitle": "Problems with PDF based forms in travel management", "RefUrl": "/notes/1906899 "}, {"RefNumber": "2108263", "RefComponent": "CA-MDG-APP-FIN", "RefTitle": "MDG-F: Short Dump on Print Forms", "RefUrl": "/notes/2108263 "}, {"RefNumber": "1317925", "RefComponent": "XX-PART-ADB-IFM", "RefTitle": "Maintaining backward compatibility with Designer", "RefUrl": "/notes/1317925 "}, {"RefNumber": "1589254", "RefComponent": "PY-IN", "RefTitle": "Form 16 central note for ADS configuration", "RefUrl": "/notes/1589254 "}, {"RefNumber": "742674", "RefComponent": "BC-WD-JAV", "RefTitle": "Required information for web dynpro problem reporting", "RefUrl": "/notes/742674 "}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "1458510", "RefComponent": "BC-SRV-FP", "RefTitle": "Designer 8.2/9.0: Restriction for ADS 802 or lower", "RefUrl": "/notes/1458510 "}, {"RefNumber": "1097565", "RefComponent": "BC-WD-JAV-ADB", "RefTitle": "Reporting issues on Interactive Form in Web Dynpro for Java", "RefUrl": "/notes/1097565 "}, {"RefNumber": "1530646", "RefComponent": "GRC-SPC-AD", "RefTitle": "Survey function in GRC", "RefUrl": "/notes/1530646 "}, {"RefNumber": "1444342", "RefComponent": "BC-CCM-PRN", "RefTitle": "Device type-independent printing of ADS forms", "RefUrl": "/notes/1444342 "}, {"RefNumber": "1128476", "RefComponent": "BC-SRV-FP", "RefTitle": "How to activate ADS trace in NW7.10 and higher", "RefUrl": "/notes/1128476 "}, {"RefNumber": "915399", "RefComponent": "BC-SRV-FP", "RefTitle": "com.adobe.ProcessingError: File not found on: URL location:", "RefUrl": "/notes/915399 "}, {"RefNumber": "1258649", "RefComponent": "BC-SRV-FP", "RefTitle": "Composite SAP Note : HCM Forms Documentation", "RefUrl": "/notes/1258649 "}, {"RefNumber": "1587941", "RefComponent": "BC-SRV-FP", "RefTitle": "ADS does not respond or error \"CSoapExceptionTransport..\" occurs", "RefUrl": "/notes/1587941 "}, {"RefNumber": "685571", "RefComponent": "BC-CCM-PRN", "RefTitle": "Printing SAP Interactive Forms by Adobe", "RefUrl": "/notes/685571 "}, {"RefNumber": "842878", "RefComponent": "BC-WD-JAV-RUN", "RefTitle": "Switch on traces for Web Dynpro session management in NW04", "RefUrl": "/notes/842878 "}, {"RefNumber": "1156407", "RefComponent": "XX-SER-REL", "RefTitle": "SAP ERP Enhan.Pk.4 SP Stack 01(11/2008) - Release Info. Note", "RefUrl": "/notes/1156407 "}, {"RefNumber": "736902", "RefComponent": "BC-SRV-FP", "RefTitle": "Adobe Credentials", "RefUrl": "/notes/736902 "}, {"RefNumber": "1316393", "RefComponent": "PLM-WUI-APP-TLS", "RefTitle": "Adobe Printing in DMZ scenario", "RefUrl": "/notes/1316393 "}, {"RefNumber": "846610", "RefComponent": "BC-SRV-FP", "RefTitle": "How to activate ADS trace", "RefUrl": "/notes/846610 "}, {"RefNumber": "1004321", "RefComponent": "BC-SRV-FP", "RefTitle": "ADS error: Incorrect content type found 'text/html'", "RefUrl": "/notes/1004321 "}, {"RefNumber": "894009", "RefComponent": "BC-SRV-FP", "RefTitle": "Adobe Document Services: Configuration", "RefUrl": "/notes/894009 "}, {"RefNumber": "836174", "RefComponent": "EP-PCT-MGR-CO", "RefTitle": "ISR/Adobe/Web Dynpro Java: Support for troubleshooting", "RefUrl": "/notes/836174 "}, {"RefNumber": "959462", "RefComponent": "EP-PCT-MGR-CO", "RefTitle": "ISR/Adobe/Web Dynpro Java: Configuration problems", "RefUrl": "/notes/959462 "}, {"RefNumber": "1069027", "RefComponent": "BC-SRV-FP", "RefTitle": "Exception in method rpData from WebServiceNavigator", "RefUrl": "/notes/1069027 "}, {"RefNumber": "1052659", "RefComponent": "BW-BEX-ET-WJR-EXP", "RefTitle": "Requirements for customer messages concerning export to PDF", "RefUrl": "/notes/1052659 "}, {"RefNumber": "934378", "RefComponent": "XX-CSC-KR", "RefTitle": "SIBUIN: Broken layout in ERP2004 Unicode environment", "RefUrl": "/notes/934378 "}, {"RefNumber": "1022238", "RefComponent": "BC-SRV-FP", "RefTitle": "Form FP_TEST_42 for simple test of several fonts", "RefUrl": "/notes/1022238 "}, {"RefNumber": "1007116", "RefComponent": "BC-SRV-FP", "RefTitle": "Form output in wrong language", "RefUrl": "/notes/1007116 "}, {"RefNumber": "846712", "RefComponent": "BC-WD-JAV", "RefTitle": "Analysis when Adobe Forms does not show values", "RefUrl": "/notes/846712 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}