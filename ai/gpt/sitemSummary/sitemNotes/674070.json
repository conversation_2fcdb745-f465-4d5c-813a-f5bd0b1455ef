{"Request": {"Number": "674070", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 403, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015553322017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000674070?language=E&token=A60A79E4A14FC3A6DA3FFBCA9FEA5FDC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000674070", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000674070/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "674070"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.01.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-PRP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade Preparation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Preparation", "value": "BC-UPG-PRP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-PRP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "674070 - Tables in the substitution container after an upgrade"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have either carried out or are planning to carry out an upgrade. Your system has the source Release SAP R/3 4.70 Enterprise Extension Set 1.10 or Support Release 1 of Extension Set 1.10. &#x00B4;<br /><br />The substitution containers have tables that are not actually substituted, which means that, after the upgrade, the old substitution containers are not empty - contrary to the information in the documentation (Post-Upgrade Activities) - and the old substitution containers therefore cannot be deleted.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Upgrade, substitution tables, substitution tablespaces, substitution container, Release 6.20<br /><br />Table names: BTCSED, DVERS, EDMAP, FREF, IXDIT, IXTYT, ST05RESULT, TESCL, TESPO, VBLOG, BCACT, BCCOM, BCSPO, BCTOU, CNTLWARN, DFAXD, E071K, STXRDIR, T705T, TRT1, TRTES, TSTCA_C, BCEX<PERSON>, BCRES, PDSNR, POCTRACE, STXRD<PERSON>, T705X, T77ALE_DEF, T77ALE_IF, T77ALE_IFTTRSTIT, TWPSSO2SGN, TWPSSO2VER, CD0T000, CD0T120TAB, CD0T130TAB, CD0T140TAB, CD0T500TAB, CD2APP0, CD2HV1PTAB, CD2HV2FTAB, DDVE1, DDVEPR, TFLOATCONV, VERPF, VERPF2, VGGT2, VGHT2, VGPT2<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The error only occurs if your system was installed as an SAP R/3 Enterprise 4.70 Extension Set 1.10 or an upgrade was performed from an earlier release to SAP R/3 Enterprise 4.70 Extension Set 1.10.<br /><br />The error only occurs with databases that use different containers (tablespaces, DBSpaces, and so on) for saving data. Caution: The report described in this note is only implemented for the Oracle, Informix and DB2 UDB for UNIX and Windows databases. It is not required for other databases.<br /><br />This problem is caused by a delivery error in the installation of SAP R/3 Enterprise 4.70 Extension Set 1.10 or the upgrade to that release.<br /><br />In this case, tables were inadvertently provided with a substitution data class (\"SLDEF\", \"SLEXC\", \"SSDEF\", \"SSEXC\") and they were created in the substitution container as a result.<br /><br />However, since these tables are not classified as substitution tables in the upgrade, they are not substituted and are still contained in the substitution containers of the old release after the upgrade.<br /><br />If you have carried out an upgrade from an earlier release to SAP R/3 Enterprise 4.70 Extension Set 1.10, only tables that were created again during the upgrade are affected.<br /><br />Caution: If you perform an upgrade without changing the Basis release, for example Extension Set 1.10 to Extension Set 2.00, an \"X\" is added to the names of the new substitution containers that are used after the upgrade, as described in the upgrade documentation:<br />Example: The old substitution container PSAP&lt;SID&gt;620 is used in the live system before the upgrade. The new substitution container PSAP&lt;SID&gt;620X is used in the live system after the upgrade.<br />After the upgrade, do not try to use this note to convert tables from the container used in the live system (PSAP&lt;SID&gt;620X). This could destroy your system.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Download the attached SAR archive (\"Attachments\" tab) and use the relevant SAPCAR to unpack it to your transport directory.</OL> <p></p> <OL>2. Then import the transport B20K8A1ZZI into your system. This contains the report RSCHECKEXC.<br />Caution: To import the transport, you need an R3trans version dating from at least 18.8.05.</OL> <OL>3. Call transaction SE38 and execute the \"RSCHECKEXC\" report directly.</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The report output is attached to the log file &lt; DIR_TRANS&gt;/tmp/RSCHKEXC.&lt;SID&gt;.<br /><br />The various possible uses for the report are explained below:<br /></p> <OL>1. You have not yet performed the upgrade or are in the PREPARE phase. Your source release is Basis 6.20. You want to use this report to determine whether your system contains tables in the substitution contains that will remain there after the upgrade.<br /><br />Solution: Execute the RSCHECKEXC report without entering any parameters.  The report searches your system for tables classified as substitution tables via the data class.<br />If the data class is known, the correct data class is specified for the table. The report also logs whether the table is empty or not and whether it is in a substitution container.<br /></OL> <OL>2. You have not yet performed the release upgrade or you are in the PREPARE and only want to correct the empty tables that were identified as being incorrect.<br /><br />Solution: Set the MODE parameter to \"DRCR\" and execute the report. All empty tables are deleted, the data class is adjusted and the tables are created in the correct database container again.<br />This can result in two problems:</OL> <OL><OL>a) A transaction or program that is running may terminate if it accesses these tables directly.<br />Solution: Perform this action when there is very little system activity.</OL></OL> <OL><OL>b) You maintained separate technical settings for this table. These are ignored when the table is recreated, instead the table is created using the standard settings.<br />Solution: In transaction SE14, you can maintain the storage parameters and create the table again.<br /></OL></OL> <OL>3. You have not yet performed the release upgrade or are in the PREPARE phase and you want to transfer the tables with their contents into the correct containers.<br /><br />Solution: Set the MODE parameter to \"ICNV\" and execute the report. The data class is corrected for the tables with contents and they are added to the ICNV.<br />You can then start the actual conversion with transaction ICNV. Refer also to the online documentation for the transaction. This, in turn, can result in the problems described above.<br /></OL> <OL>4. You have performed the release upgrade and notice the symptom described above: The old substitution containers are not empty.<br /><br />Solution: Use database tools to generate a list of tables that are in the old substitution container and save this list in a text file (one table name on each row). Copy this file to the application server on which you call the RSCHECKEXC report.<br /><br />When you call the report, enter the absolute file name in the \"TABFILE\" field and execute the report.<br /><br />As described above, you can now select the MODE=\"\" (check only), MODE=\"DRCR\" (recreate empty tables) and MODE=\"ICNV\" (add tables with contents to the ICNV) options.<br /><br />Unlike before, where the report selects all of the tables that have one substitution table type, the report now works on the list of tables from the file.<br /></OL> <p>After you have successfully implemented this note, the old exchange container (PSAP&lt;SID&gt;620) should be empty. Use database tools to confirm that this is the case.<br /><br />Also check tables TAORA/IAORA (Oracle) or TAINF/IAINF (Informix) or TADB6/IADB6 (DB2 UDB). These tables should contain no further references to the old exchange container.<br /><br />You can now delete the old exchange container as described in the guide.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D038245)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023890)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000674070/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000674070/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000674070/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000674070/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000674070/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000674070/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000674070/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000674070/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000674070/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "B20K8A1ZZI.SAR", "FileSize": "12", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000781602003&iv_version=0023&iv_guid=FEC5B69BDECB024D91B2C478642B0E2F"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "826488", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/826488"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "689574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/689574"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "523504", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: SAP R/3 Enterprise 4.70 / 4.70 SR1 on UNIX/Ora", "RefUrl": "/notes/523504"}, {"RefNumber": "513536", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/513536"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1715052", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Tablespace cannot be deleted after upgrade", "RefUrl": "/notes/1715052 "}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "492208", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: SAP Web AS 6.20 Installation on UNIX", "RefUrl": "/notes/492208 "}, {"RefNumber": "523504", "RefComponent": "BC-INS-UNX", "RefTitle": "INST: SAP R/3 Enterprise 4.70 / 4.70 SR1 on UNIX/Ora", "RefUrl": "/notes/523504 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "710", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}