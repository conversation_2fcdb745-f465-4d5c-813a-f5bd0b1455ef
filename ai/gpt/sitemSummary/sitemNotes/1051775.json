{"Request": {"Number": "1051775", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1425, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006804642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001051775?language=E&token=0B8FAF2303E8851CD11F108332BA386F"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001051775", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001051775/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1051775"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.05.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AT-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "XX-CSC-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-AT-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-AT-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1051775 - IS-H AT: Wikraf - Override V01 Data Record"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1051775&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1051775/D\" target=\"_blank\">/notes/1051775/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Austria (AT) and only for hospitals that have to submit a notification to the Vienna State Health Fund.<br />The contents of the records with record type V01 (Advance Payment Data) created by the system cannot be changed.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>WIKRAF - V01, Vienna State Health Fund, SCO04</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>New function</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><OL>1. Implement the attached attachment as follows:</OL> <OL><OL>a) Unpack the attached file.</OL></OL> <p>                       HW1051775_600.zip for IS-H Version 6.0 <p>                       HW1051775_472.zip for IS-H Version 4.72 <p>                       Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL><OL>b) Import the unpacked requests into your system.</OL></OL> <OL>2. Now implement the source code corrections from this SAP Note.</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (C5025082)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001051775/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW1051775_600.zip", "FileSize": "33", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000268252007&iv_version=0005&iv_guid=20A8ED626AD5AD4882C334070F1A1192"}, {"FileName": "HW1051775_472.zip", "FileSize": "36", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000268252007&iv_version=0005&iv_guid=7A280E19A7CD1349A12420DA856248DD"}, {"FileName": "V01_DOK_ISHA-7838_SAP.pdf", "FileSize": "87", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000268252007&iv_version=0005&iv_guid=EA5E246EA3C0C54F9BD750EFD31F40DF"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF22", "URL": "/supportpackage/SAPKIPHF22"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60010INISH", "URL": "/supportpackage/SAPK-60010INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 3, "URL": "/corrins/0001051775/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 10, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "788283 ", "URL": "/notes/788283 ", "Title": "IS-H AT: Scoring Changes/Corrections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "794151 ", "URL": "/notes/794151 ", "Title": "IS-H AT: Scoring - Messages are not updated", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "960196 ", "URL": "/notes/960196 ", "Title": "IS-H AT: RNWATSCOMSG - incorrect comment when printing", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "966356 ", "URL": "/notes/966356 ", "Title": "IS-H AT: Scoring - Accepted Errors/Warnings", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "992954 ", "URL": "/notes/992954 ", "Title": "IS-H AT: Scoring - <PERSON><PERSON><PERSON>", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1013982 ", "URL": "/notes/1013982 ", "Title": "IS-H AT: Scoring - Missing Case Block", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1015318 ", "URL": "/notes/1015318 ", "Title": "IS-H AT: Scoring - Consider Case Status", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1025820 ", "URL": "/notes/1025820 ", "Title": "IS-H AT: RNWATSCO80 - Call Online Scoring - NAKR2", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "1051775 ", "URL": "/notes/1051775 ", "Title": "IS-H AT: W<PERSON><PERSON>f - Override V01 Data Record", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "960196 ", "URL": "/notes/960196 ", "Title": "IS-H AT: RNWATSCOMSG - incorrect comment when printing", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "961183 ", "URL": "/notes/961183 ", "Title": "IS-H AT: Scoring - NAKR2 Adjustment for Service Cancellation", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "966356 ", "URL": "/notes/966356 ", "Title": "IS-H AT: Scoring - Accepted Errors/Warnings", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "992954 ", "URL": "/notes/992954 ", "Title": "IS-H AT: Scoring - <PERSON><PERSON><PERSON>", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1013982 ", "URL": "/notes/1013982 ", "Title": "IS-H AT: Scoring - Missing Case Block", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1015318 ", "URL": "/notes/1015318 ", "Title": "IS-H AT: Scoring - Consider Case Status", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1025820 ", "URL": "/notes/1025820 ", "Title": "IS-H AT: RNWATSCO80 - Call Online Scoring - NAKR2", "Component": "XX-CSC-AT-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1051775&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1051775/D\" target=\"_blank\">/notes/1051775/D</a>."}}}}