{"Request": {"Number": "162250", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 312, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000162250?language=E&token=EDCDFBC1A274377D26A0A9EF9F6CB4D4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000162250", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000162250/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "162250"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 57}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "18.08.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB2"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for z/OS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for z/OS", "value": "BC-DB-DB2", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB2*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "162250 - DB2/390: DDIC corrections (3.0F,3.1H,3.1I,4.0B)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes DB2/390-specific corrections and preliminary developments for the Data Dictionary for R/3 Releases 3.0F (VARCHAR), 3.1H, 3.1I and 4.0B.<br /></p> <b>For Releases 4.5A and 4.5B see Note 162818.<br /></b><br /><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DB2, MVS, OS390, 390<br />ABAP, transport, correction, fix, preliminary development,<br />SQL, error, SQLCODE<br />CCMS, SE14, conversion, activation, table, upgrade<br />ALTER, CREATE, NUMC, CLNT, DATS, TIMS, TABLE, ADD, field<br />Buffer pool, BP0, Support Package, SAPKH40B33, SAPKH40B49, Hot Package<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To import the current DDIC correction (kernel patch and transport) into your system, proceed as follows:</p> <OL>1. Composition of the DDIC correction</OL> <OL><OL>a) First, determine the R/3 Release of your system. To do this, choose System -&gt; Status... within the R/3 System. The R/3 Release is displayed under the column \"SAP System data\" on the line \"SAP Release\".</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Caution:</B> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For R/3 Release 3.0F, the following steps may only be carried out for a VARCHAR system. If you are dealing with a FIXCHAR database, this should be converted to VARCHAR (see Notes 82011 and 82026). <OL><OL>b) Now, using the matrix: <BR/> -----------------------------------------------------------<br />| R/3&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| Kernel&#x00A0;&#x00A0;| Min.Kernel&#x00A0;&#x00A0;| DDIC&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| Release&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| Release | Patch level | Transport&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<BR/> |----------------|---------|-------------|----------------|<br />| 3.0F (VARCHAR) | 3.1I&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;540&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| SAPK30FOS4&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| 3.1H&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; | 3.1I&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;540&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| SAPK31HOS8&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| 3.1I&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; | 3.1I&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;540&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| SAPK31IOS9&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<br />| 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; | 4.0B&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;915&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;| SAPK40BOSC (1) |<BR/> -----------------------------------------------------------<BR/> </OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;determine the following data: <UL><LI>Release and lowest required kernel patch level</LI></UL> <UL><LI>Name of the DDIC correction transport</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Caution:</B> <UL><LI><B>Hot Package 49 (SAPKH40B49) overwrites a report that is also contained in the 4.0B correction transport marked with (1). To eliminate the inconsistency, either import Hot Package 61 (SAPKH40B61), or repeat the import of (1) after you have imported SAPKH40B49 (see Note 395256).</B></LI></UL> <OL>2. Update the kernel</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Determine the current patch level of your kernel. To do this, go to the directory /usr/sap/&lt;SAPSID&gt;/SYS/exe/run and call <BR/> &#x00A0;&#x00A0;# disp+work <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the current patch level is lower than the lowest required patch level, switch the kernel. Note 19466 describes how to download the required files dw1_&lt;XXX&gt;.CAR and dw2_&lt;XXX&gt;.CAR (&lt;XXX&gt; = patch level).<br /> <OL>3. Import the DDIC transport</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Check whether the DDIC transport determined in step 1.b) has already been imported: <OL><OL>a) Call Transaction SE09.</OL></OL> <OL><OL>b) Choose Request/Task -&gt; Display individually, and specify the name of the DDIC transport.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the transport is not displayed, you must import it. The corresponding data and cofiles data can be found in a CAR archive with the same name (for example: SAPK30FOS1.CAR for transport SAPK30FOS1). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that earlier DDIC corrections are automatically contained in the current transport. Thus, you must only import the current DDIC transport! Release 3.0F is an exception where transport KDOK000621 must be imported additionally (see Notes 82183 and 105104). <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes 13719 and 480180 describe how to download attachments and how to import them into the R/3 System. <OL>4. Activate the new DB2/390 functions</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Up to now, a time-consuming table conversion was required to extend the VARCHAR table fields. However, you can avoid this conversion by using the new DDL statement. <BR/> &#x00A0;&#x00A0;ALTER TABLE ... ALTER ... SET DATA TYPE VARCHAR ( ... )<BR/> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Therefore, carry out the following steps after you import the transport: <OL><OL>a) Install the APARs PQ16674 and PQ20644 (see Note 81737).</OL></OL> <OL><OL>b) Extend the instance profile of the application server by the entry <BR/> &#x00A0;&#x00A0;dbs/db2/use_set_data_type = 1<BR/> </OL></OL> <b>Details</b><br /> <p>The following fixes and preliminary developments were included in the current DDIC corrections:</p> <OL>1. The correction of various errors</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The errors described in Notes 74009, 83473, 83460, 88339 and 118167 were corrected. The same applies to SQL errors (SQLCODEs -204,-604, -670) which occasionally occurred during the activation or conversion of tables. <OL>2. Modification of Transaction SE14</OL> <OL><OL>a) The storing parameter maintenance was revised.</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Among other things, an error that occured when storage parameters were saved has up to now caused CREATE TABLE statements to be created without the database name (-&gt; SQLCODE -104, ERROR:&#x00A0;&#x00A0;ILLEGAL SYMBOL \".\"). <OL><OL>b) Transaction SE14 can now also be used to maintain pool and cluster tables.</OL></OL> <OL><OL>c) During the database check (menu option: Extras -&gt; Database object -&gt; Check/Display) the default values of the table fields are now also output.</OL></OL> <OL><OL>d) An error that occured during the reconstruction of runtime objects was corrected.</OL></OL> <OL><OL>e) Information on using Transaction SE14 can be found in Note 103682.</OL></OL> <OL>3. Correction for ALTER TABLE .. ADD ...</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Details for this correction can be found in Note 106148. <OL>4. Use of ALTER TABLE .. ALTER .. SET DATA TYPE</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See details under section \"Solution\", step 4 in this note. <OL>5. Correction for checking profile parameters</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The check of the profile paramters (Transactions RZ10 &amp; RZ11) has been extended or modified. The following parameters are affected: <BR/> rsdb/reco_defer_msg<BR/> rsdb/reco_add_error_codes<BR/> rsdb/icli_library<BR/> rsdb/dbsl_library<BR/> rsdb/db2jcl_library<BR/> abap/no_dsql95<BR/> dbs/db2/ssid<BR/> dbs/db2/hosttcp<BR/> dbs/db2/use_set_data_type<br />  <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<OL>6. Adjustment for Support Package SAPKH40B33 (only for Release 4.0B)</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The modification enables the application of Support Package SAPKH40B33 (also refer to Notes 187912 and 192882). <OL>7. Improved Developer Trace</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;With Patch Level 501 (3.1I kernel) and 681 (4.0B kernel), a considerably improved trace is written into the dev_w* files. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The maximum level is activated by the profile parameters <BR/> rdisp/TRACE = 2<BR/> rdisp/TRACE_COMPS = all, CB<BR/> <OL>8. Support for DB2/390 Version 7.1.0 (only 4.0B)</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Transaktion DB2X displays the database version and writes a SYSLOG message. <OL>9. Support for DB2-z/OS Version 8.1.5 (only 3.1I, 4.0B)</OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Operating system", "Value": "NT/INTEL"}, {"Key": "Operating system", "Value": "AIX"}, {"Key": "Operating system", "Value": "OS/390"}, {"Key": "Database System", "Value": "DB2/390"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022631)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022280)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000162250/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000162250/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000162250/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000162250/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000162250/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000162250/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000162250/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000162250/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000162250/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAPK31IOS9.CAR", "FileSize": "78", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700007753562001&iv_version=0057&iv_guid=02783942724A1F41988079266C04FCC9"}, {"FileName": "SAPK40BOSD.CAR", "FileSize": "83", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700007753562001&iv_version=0057&iv_guid=C7CA026AF7ABB7439D3FE0742F27D282"}, {"FileName": "SAPK31HOS8.CAR", "FileSize": "71", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700007753562001&iv_version=0057&iv_guid=8CCA3726541D884E8DEAF39A25386D7B"}, {"FileName": "SAPK30FOS4.CAR", "FileSize": "74", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700007753562001&iv_version=0057&iv_guid=9C3FD766C05A734EA9609F94BC27BCFF"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "90709", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/90709"}, {"RefNumber": "905420", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905420"}, {"RefNumber": "88339", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88339"}, {"RefNumber": "88271", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88271"}, {"RefNumber": "848384", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Inconsistent fields with DDIC type RAW/LRAW/VARC", "RefUrl": "/notes/848384"}, {"RefNumber": "83473", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/83473"}, {"RefNumber": "83460", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/83460"}, {"RefNumber": "82026", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/82026"}, {"RefNumber": "82011", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/82011"}, {"RefNumber": "74009", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/74009"}, {"RefNumber": "679694", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Fixing index names after CCSID switch", "RefUrl": "/notes/679694"}, {"RefNumber": "661252", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/661252"}, {"RefNumber": "640516", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 4.7 Ext.Set 2.00", "RefUrl": "/notes/640516"}, {"RefNumber": "585941", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7 SR1", "RefUrl": "/notes/585941"}, {"RefNumber": "517267", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7", "RefUrl": "/notes/517267"}, {"RefNumber": "497638", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Isolating cluster and pool tables", "RefUrl": "/notes/497638"}, {"RefNumber": "407283", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/407283"}, {"RefNumber": "395256", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Error in R/3 Support Package SAPKH40B49", "RefUrl": "/notes/395256"}, {"RefNumber": "386570", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/386570"}, {"RefNumber": "325647", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/325647"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "192882", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/192882"}, {"RefNumber": "187912", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Second temporary name during conversion", "RefUrl": "/notes/187912"}, {"RefNumber": "163356", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/163356"}, {"RefNumber": "162818", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/162818"}, {"RefNumber": "162559", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/162559"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "118167", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/118167"}, {"RefNumber": "103707", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/103707"}, {"RefNumber": "103682", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/103682"}, {"RefNumber": "101303", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/101303"}, {"RefNumber": "101239", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/101239"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "848384", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Inconsistent fields with DDIC type RAW/LRAW/VARC", "RefUrl": "/notes/848384 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "679694", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Fixing index names after CCSID switch", "RefUrl": "/notes/679694 "}, {"RefNumber": "585941", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7 SR1", "RefUrl": "/notes/585941 "}, {"RefNumber": "517267", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7", "RefUrl": "/notes/517267 "}, {"RefNumber": "640516", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 4.7 Ext.Set 2.00", "RefUrl": "/notes/640516 "}, {"RefNumber": "497638", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Isolating cluster and pool tables", "RefUrl": "/notes/497638 "}, {"RefNumber": "395256", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Error in R/3 Support Package SAPKH40B49", "RefUrl": "/notes/395256 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}