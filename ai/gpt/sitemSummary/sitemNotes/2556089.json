{"Request": {"Number": "2556089", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 402, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000189682018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002556089?language=E&token=2CAF03B3B1AEFB17DF0D729E2496377F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002556089", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2556089"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.01.2018"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-FSCM-TRM-CRM-TM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transaction Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Financial Supply Chain Management", "value": "FIN-FSCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Treasury and Risk Management", "value": "FIN-FSCM-TRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Financial Risk Management for Commodities", "value": "FIN-FSCM-TRM-CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM-CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transaction Management", "value": "FIN-FSCM-TRM-CRM-TM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-FSCM-TRM-CRM-TM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2556089 - S4TWL - Simplification in Position Reporting for Financial Transactions"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Commodity Risk Management, Commodity Exposure Positions</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong><strong>Description</strong></strong></p>\r\n<p>With SAP S/4HANA the Commodity Risk Management provides Position Reporting for the Financial Transaction Management. All analytical data is persisted based on&#160;versioned data records and exposed through Core Data Servcies (DCS) to the analytical tools like SAP Analysis for Microsoft Excel&#160;.</p>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>In the Business Suite the Financial Transaction data that is relevant for the analytical source data for position reporting was stored in the versioned data base table for commodity&#160;exposures (TEXT_CTY_EXPOS) . This data base table was&#160;also used for logistical&#160;data via the Logistics to Treasury Management interface (LOG2TRM). The update happened via a background process.</p>\r\n<p>In S4 1709 FPS01 there is a new data flow to support analytical reporting.&#160;The Financial Transaction data is updated automatically from all relevant&#160;Financial Transaction business processes into a new data base table (CMM_VFIND)&#160; in real time. In case of an error it is possible to monitor messages in the application log and to re-process.</p>\r\n<p>S4 1709 FPS01 the analytical reporting keyfigures are&#160;provided in one single technology that provides a reporting CDS stack in order to allow&#160;commodity position snapshot reporting across commodities.</p>\r\n<p><strong>Functional Restrictions</strong></p>\r\n<p>In S4 1709 FPS01 the Position Reporting for Financial Transactions does not support&#160;option delta factors.</p>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<p>As described in the attached 'Function in Details -&#160;Simplification in Postion Reporting&#160;for&#160;Financial Transactions'&#160; a couple of steps&#160;need to be considered:</p>\r\n<ul>\r\n<li>historical position data can not be migrated to the new S/4HANA analytical data model. The data should be exported BEFORE the S4 upgrade to keep it accessible from an&#160;XLS or BW InfoCube</li>\r\n<li>Financial Transaction data can be&#160;loaded initially into the database table which is used in S4 1709 FPS1 onwards for position reporting</li>\r\n<li>if BEX Queries on top of Operational Data Providers are used in the Business Suite, these BEX Queries need to be converted into CDS Queries in S4 on top of the new CDS InterfaceViews</li>\r\n<li>if CDS Queries have been used for Position Reporting in the Suite, these need to be converted into CDS Queries in S4 on top of the new CDS InterfaceViews</li>\r\n</ul>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FIN-FSCM-TRM-CRM (Financial Risk Management for Commodities)"}, {"Key": "Responsible                                                                                         ", "Value": "RALF MUELLER (D019691)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D002459)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002556089/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002556089/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002556089/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002556089/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002556089/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002556089/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002556089/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002556089/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002556089/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2555990", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Unification of Technologies for Analytical Data Provision", "RefUrl": "/notes/2555990"}, {"RefNumber": "2553281", "RefComponent": "LO-CMM-BF", "RefTitle": "Cookbook Deprecation of Commodity ID in Commodity Management", "RefUrl": "/notes/2553281"}, {"RefNumber": "2547347", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data", "RefUrl": "/notes/2547347"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2226096", "RefComponent": "LO-CMM", "RefTitle": "Simplification List: Commodity Management in SAP S/4HANA OP", "RefUrl": "/notes/2226096 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}