{"Request": {"Number": "2918739", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 409, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000691652020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002918739?language=E&token=F7528DDA67893404CDD3706AA79FBA9D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002918739", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002918739/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2918739"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.04.2020"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-IMP-BIM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Blueprint / Solution Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Implementation / Project and Process Management", "value": "SV-SMG-IMP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-IMP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Blueprint / Solution Documentation", "value": "SV-SMG-IMP-BIM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-IMP-BIM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2918739 - 'Move to other Change Document' does not move the Group elements"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You move change recording from a Change Document /Work Items to another one. If you&#160;withdraw the Change Document / Work Item, the data&#160;in SOLDOC is&#160;released although you have moved it</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Change Control, SOLDOC, Change Recording, Move, Focus Build, Charm, Changed Elements</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The 'Group' elements, which are not visible in the list&#160;view of the Changed Elements, are not deleted in the source Change Document / Work Items</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the correction and the move should be successful.</p>\r\n<p>Please use the following SAP Notes too:</p>\r\n<p>2912334 - SOLDOC: No conflict error message when doing Release Check<br />2667251 - Change Control: Problems when releasing Change Documents<br />2855661 - SOLDOC - Change Control: Resolve Conflicts<br />2730435 - SAP Solution Manager 7.2: Change Request Management - Solution Documentation: attempt to Withdraw Change ends up with a warning</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-CM (Change Request Management)"}, {"Key": "Other Components", "Value": "SV-SMG-OST-FB (Focused Build for SAP Solution Manager)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D033136)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002918739/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002918739/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002918739/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002918739/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002918739/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002918739/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002918739/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002918739/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002918739/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2912334", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "SOLDOC: No conflict error message when doing Release Check", "RefUrl": "/notes/2912334"}, {"RefNumber": "2855661", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "SOLDOC - Change Control: Resolve Conflicts", "RefUrl": "/notes/2855661"}, {"RefNumber": "2730435", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "SAP Solution Manager 7.2: Change Request Management - Solution Documentation: attempt to Withdraw Change ends up with a warning", "RefUrl": "/notes/2730435"}, {"RefNumber": "2667251", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Change Control: Problems when releasing Change Documents", "RefUrl": "/notes/2667251"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2863568", "RefComponent": "SV-SMG-CM", "RefTitle": "Integrating Solution Documentation with Charm Workflow - Solution Manager", "RefUrl": "/notes/2863568 "}, {"RefNumber": "2868884", "RefComponent": "SV-SMG-IMP", "RefTitle": "Collective Note SP10: Process Management - SAP Solution Manager 7.2 SPS 10", "RefUrl": "/notes/2868884 "}, {"RefNumber": "2718374", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Collective Note SP09: Basic Functions - SAP Solution Manager 7.2 SPS 09", "RefUrl": "/notes/2718374 "}, {"RefNumber": "2622551", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Collective Note SP08: Basic Functions - SAP Solution Manager 7.2 SPS 08", "RefUrl": "/notes/2622551 "}, {"RefNumber": "2909637", "RefComponent": "SV-SMG-IMP", "RefTitle": "Collective Note SP11: Process Management - SAP Solution Manager 7.2 SPS 11", "RefUrl": "/notes/2909637 "}, {"RefNumber": "2730435", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "SAP Solution Manager 7.2: Change Request Management - Solution Documentation: attempt to Withdraw Change ends up with a warning", "RefUrl": "/notes/2730435 "}, {"RefNumber": "2912334", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "SOLDOC: No conflict error message when doing Release Check", "RefUrl": "/notes/2912334 "}, {"RefNumber": "2667251", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Change Control: Problems when releasing Change Documents", "RefUrl": "/notes/2667251 "}, {"RefNumber": "2855661", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "SOLDOC - Change Control: Resolve Conflicts", "RefUrl": "/notes/2855661 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "720", "To": "720", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST", "NumberOfCorrin": 1, "URL": "/corrins/0002918739/162"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ST", "ValidFrom": "720", "ValidTo": "720", "Number": "2730435 ", "URL": "/notes/2730435 ", "Title": "SAP Solution Manager 7.2: Change Request Management - Solution Documentation: attempt to Withdraw Change ends up with a warning", "Component": "SV-SMG-IMP-BIM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2730435", "RefTitle": "SAP Solution Manager 7.2: Change Request Management - Solution Documentation: attempt to Withdraw Change ends up with a warning", "RefUrl": "/notes/0002730435"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}