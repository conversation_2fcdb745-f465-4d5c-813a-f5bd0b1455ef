{"Request": {"Number": "3291483", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 347, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": true}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": true}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000071972023"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003291483?language=E&token=4C07D185B435EEF03CDC727E903599A1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003291483", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003291483/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3291483"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "HotNews"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.01.2023"}, "SAPComponentKey": {"_label": "Component", "value": "CA-LT-MC"}, "SAPComponentKeyText": {"_label": "Component", "value": "S/4HANA Migration Cockpit"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Landscape Transformation", "value": "CA-LT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Migration Cockpit", "value": "CA-LT-MC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT-MC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3291483 - Important Corrections for Data Migration Objects of SAP S/4HANA 2022 FPS0 and FPS1 delivery"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have installed SAP S/4HANA 2022 FPS0 or FPS1 either on a new system or by upgrading an existing SAP&#160;S/4HANA&#160;system.</p>\r\n<p>You are using the SAP S/4HANA migration cockpit to migrate data to your system.</p>\r\n<p>You are using the&#160;pre-delivered SAP S/4HANA Data migration content without any modifications</p>\r\n<p>You encounter issues with migration objects (syntax errors, data will not be stored, ...)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP S/4HANA migration cockpit, Migrate Your Data, Staging Table, Direct Transfer</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><span style=\"text-decoration: underline;\">Reason:</span></p>\r\n<p>The assembly of the SAP S/4HANA 2022 FPS0 is incomplete regarding data migration objects. Several data migration development artifacts (rules, structure relations, sender structure definitions, field mappings...) are not included by technical mistake into the SAP S/4HANA 2022 FPS0 and will cause issues when using the migration objects.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>You&#160;<strong>must install or implement</strong>&#160;one of the mentioned&#160;<strong>solutions</strong>&#160;below&#160;<strong>before</strong>&#160;you&#160;<strong>start</strong>&#160;<strong>working</strong>&#160;with the&#160;<strong>SAP S/4HANA migration cockpit</strong>.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>You must either upgrade your system to FPS2 or implement the below mentioned SAP Note Transport-Based Correction Instructions (TCI) to solve the problem:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Data Migration Approach</td>\r\n<td>SAP Note to be implemented</td>\r\n</tr>\r\n<tr>\r\n<td>Staging Tables</td>\r\n<td>Implement TCI Note&#160;<a target=\"_blank\" href=\"/notes/3291767\">3291767</a></td>\r\n</tr>\r\n<tr>\r\n<td>Direct Transfer</td>\r\n<td>Implement TCI Note&#160;<a target=\"_blank\" href=\"/notes/3292846\">3292846</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Be aware that the TCI only corrects the related objects of SAP delivered content. As a result, the generated migration objects will be updated automatically.</p>\r\n<p><strong>Note:</strong>&#160;If you have modified/copied your object, the correction is not done within your modified/copied object.</p>\r\n<p>You may refer KBA&#160;<a target=\"_blank\" href=\"/notes/2543372\">2543372</a>&#160;- \"How to implement a Transport-based Correction Instruction\".</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>We always recommend to implement SAP Note&#160;<a target=\"_blank\" href=\"/notes/3016862\">3016862</a> to get the Note Analyzer for ABAP-based Migration and Replication Technology. Run the Note Analyzer report before opening a ticket related to&#160;SAP S/4HANA migration cockpit and its data migration objects.</p>\r\n<p>The report lists you all missing transports that needs to be implemented before using the SAP S/4HANA migration cockpit and its data migration objects. Please read this SAP Blog for more details:</p>\r\n<p><a target=\"_blank\" href=\"https://blogs.sap.com/2020/05/28/how-to-implement-the-latest-corrections-released-for-migration-cockpit/\">How to implement the latest corrections released for Migration cockpit | SAP Blogs</a></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-DT-MIG (S/4HANA Migration Cockpit - Direct Transfer: Content)"}, {"Key": "Other Components", "Value": "CA-GTF-MIG (SAP S/4HANA Data Migration Cockpit Content)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029057)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I307134)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003291483/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003291483/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003291483/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003291483/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003291483/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003291483/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003291483/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003291483/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003291483/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2543372", "RefComponent": "BC-UPG-NA", "RefTitle": "How to implement a Transport-based Correction Instruction", "RefUrl": "/notes/2543372"}, {"RefNumber": "3292846", "RefComponent": "CA-DT-MIG-S4", "RefTitle": "2nd TCI for migration object content corrections for 2022 FPS00 and FPS01", "RefUrl": "/notes/3292846"}, {"RefNumber": "3291767", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: January 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3291767"}, {"RefNumber": "3016862", "RefComponent": "CA-LT-NAT", "RefTitle": "DMIS Note Analyzers with separated scenarios for ABAP-based Migration and Replication Technology  (DMIS2011/DMIS2018/DMIS2020/SAP S/4HANA)", "RefUrl": "/notes/3016862"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2537549", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)", "RefUrl": "/notes/2537549 "}, {"RefNumber": "3291767", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: January 2023 - Central correction Note for content issues for SAP S/4HANA 2022", "RefUrl": "/notes/3291767 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 107", "SupportPackage": "SAPK-10702INS4CORE", "URL": "/supportpackage/SAPK-10702INS4CORE"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}