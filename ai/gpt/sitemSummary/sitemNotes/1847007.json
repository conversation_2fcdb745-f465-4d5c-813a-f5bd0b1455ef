{"Request": {"Number": "1847007", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 536, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010917622017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=1839AF4C46B944E7B311AB67CE4030A4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1847007"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.04.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-OT-MDX"}, "SAPComponentKeyText": {"_label": "Component", "value": "MDX,OLAP-BA<PERSON>,OLE DB for OLAP"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "OLAP Technology", "value": "BW-BEX-OT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MDX,OLAP-BA<PERSON>,OLE DB for OLAP", "value": "BW-BEX-OT-MDX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-OT-MDX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1847007 - MDX: Nodes that can be posted to and non-compounded keys"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The MDX property \"Key\" (not compounded) displays unexpected data for postable nodes of a display hierarchy. For these postable nodes, the system also displays the value of the compound parent; the display for the leaves of the display hierarchy is correct, that is, without the value for the compound parent.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Property prefix 8 OLE DB OLAP MDX property</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>There is a program error.<br />This problem occurs under the following circumstances:</p>\r\n<ul>\r\n<li>You execute an MDX statement or you call the BAPI GetMembers. You want to display values for the MDX property \"Key\" (not compounded).</li>\r\n</ul>\r\n<ul>\r\n<li>You want to display data for an MDX dimension that is based on a display hierarchy in BW.</li>\r\n</ul>\r\n<ul>\r\n<li>This display hierarchy has postable nodes. In addition, the characteristic on which the display hierarchy is based is compounded because only in this case, the MDX property \"Key\" (not compounded) is also available.</li>\r\n</ul>\r\n<ul>\r\n<li></li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.00<br /><br />Import Support Package 31 for SAP NetWeaver BW 7.00 (SAPKW70031) into your BW system. The Support Package is available once <strong>SAP Note 1782745</strong> \"SAPBWNews NW 7.00 BW ABAP SP31\", which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.01 (SAP NW BW 7.0 Enhancement Package 1)<br /><br />Import Support Package 14 for SAP NetWeaver BW 7.01 (SAPKW70114) into your BW system. The Support Package is available once <strong>SAP Note 1794836</strong> \"SAPBWNews NW 7.01 BW ABAP SP14\", which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.02 (SAP NW BW 7.0 Enhancement Package 2)<br /><br />Import Support Package 14 for SAP NetWeaver BW 7.02 (SAPKW70214) into your BW system. The Support Package is available once <strong>SAP Note 1800952</strong> \"SAPBWNews NW 7.02 BW ABAP SP14\", which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.11<br /><br />Import Support Package 12 for SAP NetWeaver BW 7.11 (SAPKW71112) into your BW system. The Support Package is available once <strong>SAP Note 1797080</strong> \"SAPBWNews NW 7.11 BW ABAP SP12\", which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.30<br /><br />Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010) into your BW system. The Support Package is available once <strong>SAP Note 1810084</strong> \"SAPBWNews NW 7.30 BW ABAP SP10\", which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.31 (SAP NW BW 7.3 Enhancement Package 1)<br /><br />Import Support Package 9 for SAP NetWeaver BW 7.31 (SAPKW73109) into your BW system. The Support Package is available once <strong>SAP Note </strong><strong><strong>1847231</strong></strong> with the short text \"SAPBWNews NW BW 7.31/7.03 ABAP SP9\", which describes this Support Package in more detail, has been released for customers.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.40<br /><br />Import Support Package 03 for SAP NetWeaver BW 7.40 (SAPKW74003) into your BW system. The Support Package is available once <strong>SAP Note</strong><strong><strong> 1818593</strong></strong> (\"SAPBWNews NW BW 7.4 ABAP SP03\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />In urgent cases, you can implement the correction instructions as an advance correction.<br /><br /><strong>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</strong><br /><br />To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note still contains the words \"Preliminary version\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-ET-ODB (OLE DB Provider)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029975)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029975)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1847231", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 09", "RefUrl": "/notes/1847231"}, {"RefNumber": "1818593", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.40 ABAP SP 03", "RefUrl": "/notes/1818593"}, {"RefNumber": "1810084", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 10", "RefUrl": "/notes/1810084"}, {"RefNumber": "1800952", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 14", "RefUrl": "/notes/1800952"}, {"RefNumber": "1797080", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.11 ABAP SP12", "RefUrl": "/notes/1797080"}, {"RefNumber": "1794836", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 14", "RefUrl": "/notes/1794836"}, {"RefNumber": "1782745", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 31", "RefUrl": "/notes/1782745"}, {"RefNumber": "1156101", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Composite SAP Note about incorrect data", "RefUrl": "/notes/1156101"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1800952", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 14", "RefUrl": "/notes/1800952 "}, {"RefNumber": "1810084", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 10", "RefUrl": "/notes/1810084 "}, {"RefNumber": "1794836", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 14", "RefUrl": "/notes/1794836 "}, {"RefNumber": "1813987", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 08", "RefUrl": "/notes/1813987 "}, {"RefNumber": "1782745", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 31", "RefUrl": "/notes/1782745 "}, {"RefNumber": "1156101", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Composite SAP Note about incorrect data", "RefUrl": "/notes/1156101 "}, {"RefNumber": "1818593", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.40 ABAP SP 03", "RefUrl": "/notes/1818593 "}, {"RefNumber": "1797080", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.11 ABAP SP12", "RefUrl": "/notes/1797080 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "711", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "711", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70031", "URL": "/supportpackage/SAPKW70031"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70114", "URL": "/supportpackage/SAPKW70114"}, {"SoftwareComponentVersion": "SAP_BW 702", "SupportPackage": "SAPKW70214", "URL": "/supportpackage/SAPKW70214"}, {"SoftwareComponentVersion": "SAP_BW 711", "SupportPackage": "SAPKW71112", "URL": "/supportpackage/SAPKW71112"}, {"SoftwareComponentVersion": "SAP_BW 730", "SupportPackage": "SAPKW73010", "URL": "/supportpackage/SAPKW73010"}, {"SoftwareComponentVersion": "SAP_BW 731", "SupportPackage": "SAPKW73109", "URL": "/supportpackage/SAPKW73109"}, {"SoftwareComponentVersion": "SAP_BW 740", "SupportPackage": "SAPKW74003", "URL": "/supportpackage/SAPKW74003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 1, "URL": "/corrins/**********/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}