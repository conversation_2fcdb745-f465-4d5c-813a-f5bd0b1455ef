{"Request": {"Number": "1895061", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 508, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017699632017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001895061?language=E&token=B4FDCDFC6D400113ADC5EE32D1E1E710"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001895061", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001895061/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1895061"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.06.2021"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-BD-RDF-WWI"}, "SAPComponentKeyText": {"_label": "Component", "value": "WWI"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Data and Tools", "value": "EHS-BD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-BD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Report Definition", "value": "EHS-BD-RDF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-BD-RDF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "WWI", "value": "EHS-BD-RDF-WWI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-BD-RDF-WWI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1895061 - EH&S WWI: Enhancements for print request processing in Global Label Management"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Module: EH&amp;S Windows Wordprocessor Integration (WWI)<br /><br />SAP EH&amp;S WWI Support Package 31 contains the following enhancements for print request processing in Global Label Management (GLM):</p>\r\n<ul>\r\n<li>Faster dynamic character sizing for labels</li>\r\n<li>Generation of bar codes using barcode.dll as described in SAP Note 25344</li>\r\n<li>Cache for print request data and document templates</li>\r\n<li>High-volume printer driver for optimized printing of labels on label printers</li>\r\n<li>Direct printing of labels via WWI</li>\r\n<li>Increase in performance due to parallel processing in Microsoft Word</li>\r\n</ul>\r\n<p>You can find details about the individual functions below.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>EHSWWISP31<br />Service pack, Support Package, patch, update<br />Performance, fast, faster, quick, quicker, parallel processing<br />GLM GLM+ GLMplus</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Reason<br />This SAP Note delivers enhancements.<br /><br />Prerequisites<br />EH&amp;S WWI (any version)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Faster dynamic character sizing for labels</strong></p>\r\n<p>WWI now offers two different methods for dynamic character sizing (Dyntext):<br />1. Based on a Microsoft Word macro (contained in WWI.DOT)<br />2. Calculated by wwiDyntext.dll (as of WWI Support Package 31)</p>\r\n<p>wwiDyntext.dll is a retrofit from TechniData Genifix.</p>\r\n<p>In WWI.INI, you can configure which method the system should use. In the [Dyntext] section, you can assign the following values to <em>\"mode\"</em>:<br />0: Always use Word macro dynamic text<br />1: Use calculated dynamic text for labels, process other reports with Word macro dynamic text (default setting)<br />2: Always use wwiDyntext.dll</p>\r\n<p>This means a behavior change for the generation of labels. If you want the old behavior, set <em>mode=0</em>.</p>\r\n<p>Functional differences between Word macro dynamic text and wwiDyntext.DLL:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td><strong>Word macro</strong></td>\r\n<td><strong>wwiDyntext.dll</strong></td>\r\n</tr>\r\n<tr>\r\n<td><strong>Tables</strong></td>\r\n<td>Supported</td>\r\n<td>Not supported</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Formatting</strong></td>\r\n<td>All Word formattings are supported.</td>\r\n<td>\r\n<p>Only basic formats (for example, bold, italics, font size, line spacing, line indentation) are supported.<br />The following are not supported: <br />superscript, subscript, all caps, small caps, character spacing.<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Superscript, Subscript, All Caps, Small Caps, Character Spacing&#xFEFF;</span></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Speed</strong></td>\r\n<td>Slow</td>\r\n<td>Very fast</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><strong>Linked text fields</strong></p>\r\n</td>\r\n<td>Not supported</td>\r\n<td>Supported</td>\r\n</tr>\r\n<tr>\r\n<td><strong>Use of INCLUDE symbols in the text field</strong></td>\r\n<td>Supported</td>\r\n<td>Not supported (the system automatically uses the Word macro dynamic text)</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Generation of bar codes using barcode.dll as described in SAP Note 25344</strong></p>\r\n<p>WWI now offers the option of inserting bar codes in reports and labels using the SAPsprint bar code interface.</p>\r\n<p>If you are currently using wwiBarcode.DLL (not part of the WWI delivery) from TechniData Genifix, you can keep using this for a transition period. WWI uses the bar code definition in the report template to determine which bar code library (wwiBarcode.DLL or barcode.DLL) to use. For Genifix, the bar code definition is <em>B:TYPE=</em>, for WWI, it is <em>B:C=</em>.</p>\r\n<p>Note that maintenance by TechniData Genifix is phased out (see SAP Note 1978839), and compatibility with wwiBarcode.DLL will be removed in a future WWI version.</p>\r\n<p><strong>Cache for print request data and document templates</strong></p>\r\n<p>Print request data and document templates or report bodies are cached on the WWI server during GLM print request processing. This function is available only for GLM print request processing (see SAP Note <span class=\"urTxtStd urVt1\" style=\"white-space: nowrap;\">1934253</span>), but not for GLM direct print or report generation.<br />This function is available only in the Unicode version of WWI (see SAP Note 733416).</p>\r\n<p><strong>High-volume printer driver for optimized printing of labels on label printers</strong></p>\r\n<p>The SAP EH&amp;S WWI HVP printer driver is a retrofit of the TechniData printer driver (TechniData Labeling Printer Driver) from Genifix. Note that the printer-specific plugs-ins are not part of the product range.</p>\r\n<p>You can use the TechniData printer driver and the SAP EH&amp;S WWI HVP printer driver in parallel. Note that maintenance by TechniData Genifix is phased out (see SAP Note 1978839), and support for the TechniData printer driver will therefore be removed from a future WWI version.</p>\r\n<p><strong>Direct printing of labels via WWI</strong></p>\r\n<p>As of Support Package 31, WWI can directly print labels, without having to use SAP Spool or the GLM print tool in the front end.</p>\r\n<p>This function is available only with the GLM print request processing (see SAP Note <span class=\"urTxtStd urVt1\" style=\"white-space: nowrap;\">1934253)</span>.</p>\r\n<p><strong>Increase in performance due to parallel processing in Microsoft Word</strong></p>\r\n<p>This enhancement is available for all types of WWI report generation and label generation.</p>\r\n<p>As of Support Package 31, WWI can process multiple reports in parallel. In older WWI versions, the Word processes used by WWI have blocked each other so that always only one process could be active at the same time.<br />Example: If you have installed four WWI services on a PC, all four services can now work completely in parallel, without blocking each other.<br />Note however that WWI is still not multithreaded. This means that one WWI service can process only one job. In order to process multiple jobs in parallel, you require multiple WWI services.</p>\r\n<p><strong>Availability of the enhancements</strong></p>\r\n<p>The enhancements are available with EH&amp;S WWI 3.2/2.7B Support Package 31. Import this or a higher Support Package. You can find information about the availability, storage location, downloading, and importing of Support Packages in SAP Note 568302.<br /><br />New files:<br />wwiBC.dll Version 1.0.1 of JUN/19/2013 or later<br />wwiGen.dll Version 3.2.94 of JUN/19/2013 or later<br />wwiDms.dll Version 1.0.0 of JUN/19/2013 or later<br />wwiDyntext.dll Version 1.0.18 of JUN/19/2013 or later<br />HvpDisp.dll Version 1.3.39 of JUN/19/2013 or later<br />HvpPrint.dll Version 1.3.2 of JUN/19/2013 or later<br />HvpPrintConfig.exe Version 1.3.2 of JUN/19/2013 or later<br />HvpPrintUi.dll Version 1.3.4 of JUN/19/2013 or later</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-FES-GUI (SAP GUI for Windows)"}, {"Key": "Other Components", "Value": "EHS-BD-RDF (Report Definition)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D054757)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D054757)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001895061/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001895061/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001895061/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001895061/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001895061/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001895061/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001895061/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001895061/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001895061/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "568302", "RefComponent": "EHS", "RefTitle": "Current version of EH&S WWI and EH&S Expert or OCC", "RefUrl": "/notes/568302"}, {"RefNumber": "25344", "RefComponent": "BC-CCM-PRN", "RefTitle": "Interface SAPSprint (SAPFprint) barcode DLL (details)", "RefUrl": "/notes/25344"}, {"RefNumber": "1934253", "RefComponent": "EHS-SAF-GLM", "RefTitle": "Enhanced functions for EHS Global Label Management (GLM) - release information note (RIN)", "RefUrl": "/notes/1934253"}, {"RefNumber": "1875812", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: Corrections in WWI SP31", "RefUrl": "/notes/1875812"}, {"RefNumber": "1293379", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: Bar code support", "RefUrl": "/notes/1293379"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3058236", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "GLM: Print requests consume a lot of storage space", "RefUrl": "/notes/3058236 "}, {"RefNumber": "2422215", "RefComponent": "EHS-SAF-GLM", "RefTitle": "GLM: Connect WWI database as SQL server", "RefUrl": "/notes/2422215 "}, {"RefNumber": "1900327", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: HVP plug-in interface", "RefUrl": "/notes/1900327 "}, {"RefNumber": "1934253", "RefComponent": "EHS-SAF-GLM", "RefTitle": "Enhanced functions for EHS Global Label Management (GLM) - release information note (RIN)", "RefUrl": "/notes/1934253 "}, {"RefNumber": "568302", "RefComponent": "EHS", "RefTitle": "Current version of EH&S WWI and EH&S Expert or OCC", "RefUrl": "/notes/568302 "}, {"RefNumber": "1875812", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: Corrections in WWI SP31", "RefUrl": "/notes/1875812 "}, {"RefNumber": "25344", "RefComponent": "BC-CCM-PRN", "RefTitle": "Interface SAPSprint (SAPFprint) barcode DLL (details)", "RefUrl": "/notes/25344 "}, {"RefNumber": "1293379", "RefComponent": "EHS-BD-RDF-WWI", "RefTitle": "EH&S WWI: Bar code support", "RefUrl": "/notes/1293379 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "617", "To": "617", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP GUI FOR WINDOWS 7.30 CORE", "SupportPackage": "SP007", "SupportPackagePatch": "000007", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=01200615320200019595&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}