{"Request": {"Number": "886984", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1433, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005075822017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000886984?language=E&token=970DE0280AC637E0A783B426DB299C9E"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000886984", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000886984/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "886984"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Correction of legal function"}, "Priority": {"_label": "Priority", "value": "Correction with High Priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.11.2005"}, "SAPComponentKey": {"_label": "Component", "value": "PY-AT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Austria"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "PY-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "886984 - Important corrections for E-Card"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=886984&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/886984/D\" target=\"_blank\">/notes/886984/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><OL>1. The batch input session is not processed correctly if the parameter &quot;Copy KS-Exempt after e-Card&quot; is not selected. When you process the BI session, the system issues the error message &quot;Field Q0021-ECARD does not exist in screen MP002100 2003&quot;. If the session is processed in the background, this record is not saved as of November 1, 2005.</OL> <b>You must implement the changes contained in this SAP Note before you use the report RPIECAA0 because data may be lost.</b><br /> <OL>1. In the infotype 0021 (Family), the input field for the authorization number remains ready for input after November 1, 2005 without a field text.</OL> <OL>2. Company pensioners with &quot;Employment Status&quot; = 2 are subject to e-card, which is incorrect. These should generally be excluded from the e-card.</OL> <OL>3. Under certain constellations, the e-card charge is not calculated correctly for payment in lieu of unused leave that extends up to November 15 or beyond.</OL> <OL>4. The e-card settlement rules are not integrated in the subschemas of payroll schema A100 for the public sector.</OL> <OL>5. For payment in lieu of unused leave and leaving in previous months before November 15, the exemption from the e-card according to infotype 0044 is taken into account in November but not in the month of leaving.</OL> <OL>6. The absence &quot;Accident 4tel paid&quot; (U4)  is not taken into account as the exemption reason.</OL> <OL>7. The &quot;Exemption_of_der_e-Card_Fee&quot; field in the &quot;Family&quot; infotype (0021) is deleted again in certain constellations after you select it.</OL><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>RPIECAA0<br />Bach Input<br />e-Card<br />Infotype 0021 (Family)</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Program error</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Import the HR Support Package relevant for your system or implement the attached correction instructions for Release ERP 2005 and ERP 2004. For 4.6B to 4.70, import the attachments in accordance with the attachment:<br />For more information about importing attachments, see the related SAP Notes. For Release 4.70: L6BK102876.SAR<BR/> For Release 4.6C: L9CK196657.SAR<BR/> For Release 4.6B: L9BK131002.CAR<br /></p> <b>In addition to symptom 4, action is required if you have not yet imported the Support Package in accordance with this SAP Note.</b><br /> <p>1. Check whether the subschema ASVU has the calls for the e-card at the following point, or change this manually using transaction PE01 in advance:<br /> Func   Par1 Par2 Par3 Par4 D Text<BR/> -----+----+----+----+----+-+--------------------------------------------<br />COM                         Subschema: Payroll Austria<br />BLOCK BEG                   EU: Payment in lieu of unused leave<br />IF    ASV1                    Payment EU in Current Month?<br />ASV00 3              3          Determination of SI Days Until End of SI<br />ASV00 4              3          Determination of SI Days Inactive<br />IF    AECB                  For-period not November? e-Card &lt;&lt;&lt;&lt;===<br />ACTIO AEC1                  Enter e-Card Fee for DN&lt;&lt;&lt;&lt;===<br />P0021 AEC2 GEN  NOAB        Charge for the e-card for Anteii&lt;&lt;&lt;&lt;===<br />ENDIF                                                         &lt;&lt;&lt;&lt;===<BR/> and so on.<br />BLOCK END                   EU: Payment in lieu of unused leave<BR/> -----+----+----+----+----+-+--------------------------------------------<br />              If you have already imported the HR Support Package according to this SAP Note and do not want any modifications in the subschema ASVU, delete the subschema ASVU in your client that is not 000 using transaction PE01. This resets the SAP standard from client 000.<br /> <p>2. In rule ASV3, add the following line (at any position) for grouping * and wage type ****:<br /> /3ZE         ADDWT *                        - WAGE TYPE ADDITIONAL<br />3. Add the rule ASVD for the grouping * and wage type /3ZE as follows (this change is not relevant for you if you have already imported the latest HR Support Package according to this SAP Note). VarKey FZ T Operation Operation Operation Operation Operation Operation<BR/> -------------+---------+---------+---------+---------+ -----------------<br />                                   - ELIMINATE WAGE TYPE<BR/> -------------+---------+---------+---------+---------+ -----------------<br />4. Add rule AEC1 for grouping A and wage type **** as follows: VarKey. FZ T Operation Operation Operation Operation Operation Operation<BR/> -------------+---------+---------+---------+---------+ -----------------<br />           D AECAR                ABWESENHEITSKENNEZICHEN am 15th ?<br />**                                NO EXEMPTION: LICENSE INDICATOR<br />00           ELIMI *              WAGE PAYMENT &lt;50% ==> NO FEE<br />K4           ELIMI *              PAYMENT FOR WAGE &lt;50% ==> NO FEE<br />MU           ELIMI *              CARENCE               ==> NO FEE<br />PR           ELIMI *              PRESENCE/CIVIL SERVICE ==> NO FEE<br />U4           ELIMI *              UNCASE &lt;50%          ==> NO FEE<br />WO           ELIMI *              WEEK HELP          ==> NO FEE<BR/> ZZ       U *  CONTAIN HERE ALL ABSENCE SEMBOLS THAT ARE ONE<BR/> ZZ       V *  EXEMPTION FROM THE E-CARD THIS ARE<BR/> ZZ       W *  SPECIAL MOTHER PROTECTION, CARENCY, PRESENCE AND CIVIL SERVICES<BR/> ZZ       X *  AND REPLACEMENT WITH LESS THAN 50%<BR/> -------------+---------+---------+---------+---------+ -----------------</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PA-PA-AT (Austria)"}, {"Key": "Owner                                                                                    ", "Value": "D001888"}, {"Key": "Processor                                                                                          ", "Value": "D001888"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000886984/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "L9CK196657.SAR", "FileSize": "34", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000505552005&iv_version=0026&iv_guid=053AD08B482DE04D84E1CE417AFBDCE4"}, {"FileName": "L9BK131002.CAR", "FileSize": "35", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000505552005&iv_version=0026&iv_guid=734F241197943F48B8656566015FAD26"}, {"FileName": "L6BK102876.SAR", "FileSize": "38", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000505552005&iv_version=0026&iv_guid=DF57C7A90317F14986B89F658D6A512B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "897869", "RefComponent": "PY-AT", "RefTitle": "E-Card: No fee in November at the start of the UE", "RefUrl": "/notes/897869"}, {"RefNumber": "865066", "RefComponent": "PY-AT", "RefTitle": "Service Charge for E-Card", "RefUrl": "/notes/865066"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "897869", "RefComponent": "PY-AT", "RefTitle": "E-Card: No fee in November at the start of the UE", "RefUrl": "/notes/897869 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "865066", "RefComponent": "PY-AT", "RefTitle": "Service Charge for E-Card", "RefUrl": "/notes/865066 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46BB2", "URL": "/supportpackage/SAPKE46BB2"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46CA3", "URL": "/supportpackage/SAPKE46CA3"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47048", "URL": "/supportpackage/SAPKE47048"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47049", "URL": "/supportpackage/SAPKE47049"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50014", "URL": "/supportpackage/SAPKE50014"}, {"SoftwareComponentVersion": "SAP_HR 500", "SupportPackage": "SAPKE50015", "URL": "/supportpackage/SAPKE50015"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60002", "URL": "/supportpackage/SAPKE60002"}, {"SoftwareComponentVersion": "SAP_HR 600", "SupportPackage": "SAPKE60003", "URL": "/supportpackage/SAPKE60003"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 5, "URL": "/corrins/0000886984/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 7, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "31I", "ValidTo": "470", "Number": "606804 ", "URL": "/notes/606804 ", "Title": "BMV: Mid-Month Contribution Liability, Factoring", "Component": "PY-AT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "31I", "ValidTo": "470", "Number": "623221 ", "URL": "/notes/623221 ", "Title": "BMV: Special Payments in BMV-Exempt Periods Not BMV-Exempt", "Component": "PY-AT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "31I", "ValidTo": "470", "Number": "630113 ", "URL": "/notes/630113 ", "Title": "RPCALCA0: Division by 0 during BMV (OPABZAV)", "Component": "PY-AT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "40B", "ValidTo": "470", "Number": "696989 ", "URL": "/notes/696989 ", "Title": "SI penalty calculation: &#39;Personnel number is not a malus candidate!&#39;", "Component": "PY-AT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46C", "Number": "865066 ", "URL": "/notes/865066 ", "Title": "Service Charge for E-Card", "Component": "PY-AT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "470", "Number": "691796 ", "URL": "/notes/691796 ", "Title": "Company pension plan, mid-month. Factoring", "Component": "PY-AT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "470", "ValidTo": "600", "Number": "865066 ", "URL": "/notes/865066 ", "Title": "Service Charge for E-Card", "Component": "PY-AT"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "500", "ValidTo": "600", "Number": "886984 ", "URL": "/notes/886984 ", "Title": "Important corrections for E-Card", "Component": "PY-AT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=886984&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/886984/D\" target=\"_blank\">/notes/886984/D</a>."}}}}