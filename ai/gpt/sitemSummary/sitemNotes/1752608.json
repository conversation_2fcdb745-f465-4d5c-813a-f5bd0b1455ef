{"Request": {"Number": "1752608", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1118, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010418042017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001752608?language=E&token=C4B1C9D2F9E3BEF91AFC2F5FA0514B08"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001752608", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1752608"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.08.2012"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-REO-BF"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Reorganization Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization", "value": "FI-GL-REO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization Basic Functions", "value": "FI-GL-REO-BF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO-BF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1752608 - Runtime error ASSERTION_FAILED in CL_FAGL_R_OBJLIST..."}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>During generation or the account assignment change, the runtime error ASSERTION_FAILED occurs in the program CL_FAGL_R_OBJLIST=============CP. The error occurs in the method GET_OBJECT_INFO_P of the class CL_FAGL_R_OBJLIST.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>GET_OBJECT_INFO, GET_OBJECT_INFO_P, object type, calling object list, calling instance</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the corrections.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021112)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D038166)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001752608/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001752608/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001752608/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001752608/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001752608/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001752608/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001752608/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001752608/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001752608/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1627018", "RefComponent": "FI-GL-REO", "RefTitle": "Composite SAP Note for segment reorganization", "RefUrl": "/notes/1627018"}, {"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1471153", "RefComponent": "FI-GL-REO", "RefTitle": "Composite note for profit center and FM reorganization", "RefUrl": "/notes/1471153 "}, {"RefNumber": "1627018", "RefComponent": "FI-GL-REO", "RefTitle": "Composite SAP Note for segment reorganization", "RefUrl": "/notes/1627018 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60509", "URL": "/supportpackage/SAPKH60509"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60605", "URL": "/supportpackage/SAPKH60605"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0001752608/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 26, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1560518 ", "URL": "/notes/1560518 ", "Title": "PRCTR: Too many objects at the first hierarchy level", "Component": "FI-GL-REO"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1597693 ", "URL": "/notes/1597693 ", "Title": "PRCTR/SEG: AP/AR: Various PRCTR/SEG for same account assgnmt", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1609861 ", "URL": "/notes/1609861 ", "Title": "Background processes terminate due to deadlock", "Component": "FI-GL-REO-BF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1612114 ", "URL": "/notes/1612114 ", "Title": "PRCTR: Changes in the program structure", "Component": "FI-GL-REO"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1617885 ", "URL": "/notes/1617885 ", "Title": "PRCTR: Correction to note 1612114", "Component": "FI-GL-REO"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1626389 ", "URL": "/notes/1626389 ", "Title": "PRCTR/SEG: Document splitting not updated (without message)", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1628255 ", "URL": "/notes/1628255 ", "Title": "PRCTR/SEG: Configuring message FAGL_R<PERSON>ORGANIZATION 029", "Component": "FI-GL-REO"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1641528 ", "URL": "/notes/1641528 ", "Title": "Lock entry for processing object list does not exist", "Component": "FI-GL-REO"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1650324 ", "URL": "/notes/1650324 ", "Title": "PRCTR/SEG: Generation terminates: Unstable grouping AP/AR", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1660067 ", "URL": "/notes/1660067 ", "Title": "Runtime error ASSERTION_FAILED: Update of dummy objects", "Component": "FI-GL-REO-BF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1660365 ", "URL": "/notes/1660365 ", "Title": "PRCTR: Purch. orders with acc.ass. sales order", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1660943 ", "URL": "/notes/1660943 ", "Title": "Runtime error DBIF_RSQL_INVALID_RSQL during reassignment", "Component": "FI-GL-REO"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1663555 ", "URL": "/notes/1663555 ", "Title": "PRCTR/SEG: Error message when reassigning AP/AR", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1666478 ", "URL": "/notes/1666478 ", "Title": "PRCTR: Missing purchase orders with SO acct assignment", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1667694 ", "URL": "/notes/1667694 ", "Title": "PRCTR: Purch. orders for SO, special case valuated stock", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1686377 ", "URL": "/notes/1686377 ", "Title": "PRCTR: Error - get_object_info in assgd PO for sales order", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1707428 ", "URL": "/notes/1707428 ", "Title": "BAdI for overwriting the transaction type", "Component": "FI-GL-REO-BF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1723893 ", "URL": "/notes/1723893 ", "Title": "PRCTR: <PERSON>rror 222 while reassigning POA purch.order", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1725063 ", "URL": "/notes/1725063 ", "Title": "PRCTR: Error SAPSQL_INVALID_TABLENAME in select_first_level", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1730611 ", "URL": "/notes/1730611 ", "Title": "PRCTR: Error SAPSQL_INVALID_TABLENAME in select_first in PO", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1733158 ", "URL": "/notes/1733158 ", "Title": "Objects are not reorganized", "Component": "FI-GL-REO-BF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1745135 ", "URL": "/notes/1745135 ", "Title": "PRCTR: multiple improvements in obj. type POA", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1746777 ", "URL": "/notes/1746777 ", "Title": "Reorg job terminates", "Component": "FI-GL-REO-BF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1749843 ", "URL": "/notes/1749843 ", "Title": "Termination when generating payables", "Component": "FI-GL-GL-A"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1751367 ", "URL": "/notes/1751367 ", "Title": "PRCTR/SEG: Reassigned amount is incomplete for AP/AR", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1609861 ", "URL": "/notes/1609861 ", "Title": "Background processes terminate due to deadlock", "Component": "FI-GL-REO-BF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1626389 ", "URL": "/notes/1626389 ", "Title": "PRCTR/SEG: Document splitting not updated (without message)", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1666478 ", "URL": "/notes/1666478 ", "Title": "PRCTR: Missing purchase orders with SO acct assignment", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1685927 ", "URL": "/notes/1685927 ", "Title": "PRCTR/SEG: Error messages use term \"Profit Center\"", "Component": "FI-GL-REO-GL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1686377 ", "URL": "/notes/1686377 ", "Title": "PRCTR: Error - get_object_info in assgd PO for sales order", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1725063 ", "URL": "/notes/1725063 ", "Title": "PRCTR: Error SAPSQL_INVALID_TABLENAME in select_first_level", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1730611 ", "URL": "/notes/1730611 ", "Title": "PRCTR: Error SAPSQL_INVALID_TABLENAME in select_first in PO", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1733158 ", "URL": "/notes/1733158 ", "Title": "Objects are not reorganized", "Component": "FI-GL-REO-BF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1745135 ", "URL": "/notes/1745135 ", "Title": "PRCTR: multiple improvements in obj. type POA", "Component": "FI-GL-REO-MM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "1749843 ", "URL": "/notes/1749843 ", "Title": "Termination when generating payables", "Component": "FI-GL-GL-A"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1749843", "RefTitle": "Termination when generating payables", "RefUrl": "/notes/0001749843"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}