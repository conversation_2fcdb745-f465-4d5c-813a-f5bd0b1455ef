{"Request": {"Number": "321190", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 357, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014875412017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=DBD9A9D476EF7E3DF23F0B87BA07FEE7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "321190"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.08.2010"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-BA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Area Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Area Accounting", "value": "FI-GL-BA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-BA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "321190 - Difference between business area and profit center"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note shows how the \"Business Area\" and \"Profit Center\" objects are positioned in the SAP System.&#x00A0;&#x00A0;The \"Current situation\" section below refers to Release 4.6C and is also valid for R/3 Enterprise and mySAP Financials.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>Review/history:</b><br /> <UL><LI>In addition to the company code, the business area is used as a further unit for the external reporting procedure of the company.&#x00A0;&#x00A0;The area of responsibility of the business areas generally spans several company codes (lines of business, divisions).&#x00A0;&#x00A0;For the display of a business area financial statement, all Controlling objects (cost center, orders, sales orders and so on) and logistics objects (material, fixed assets) are assigned to the relevant business areas.&#x00A0;&#x00A0;Financial Accounting evaluations (financial statements) are now additionally created for the different business areas.&#x00A0;&#x00A0;In addition to the reporting aspect, the business area has controlling and selection functions in accounts receivable and accounts payable accounting.</LI></UL> <UL><LI>Profit Center Accounting was initially implemented only for displaying the results of internal areas of responsibility. For this, all CO objects responsible for sales revenue and costs (cost center, orders, projects, customer orders and so on) were displayed in profit centers and updated with the relevant data affecting net income.&#x00A0;&#x00A0;To display profitability key figures (Return on Investment), financial statement items of the current assets and fixed assets and short-term payable were then also distributed to profit centers.&#x00A0;&#x00A0;In this way, the profit center has adjusted to correspond more and more to the business area with regard to the assignment of financial statement data. The display of a profit center hierarchy, statistical key figures, profit center assessments and distributions, and the clearing for internal transfer prices represent additional developments with regard to the business area.&#x00A0;&#x00A0;However, there is no zero balance setting for each profit enter.<br /></LI></UL> <b>Current situation:</b><br /> <p>The distinction of an externally oriented classification according to business areas and a classification for internal controlling purposes according to profit centers has become less and less required in the course of the convergence of external and internal accounting.&#x00A0;&#x00A0;It is increasingly difficult for the customer to choose between the two entities.<br /></p> <b>Plans:</b><br /> <p>We want to give our customers assurance regarding the further development.</p> <UL><LI>To meet the changing requirements, we will focus the further functional developments in Financial Accounting on the profit center entity. With General Ledger Accounting (new) in Release SAP ERP 2004, you can create financial statements for profit centers.&#x00A0;&#x00A0;See Note 756146 for more detailed information.</LI></UL> <UL><LI>The business area will be retained in the present form. The data and functions will continue to be available.&#x00A0;&#x00A0;In the context of the use of classic General Ledger Accounting, business area accounting (Customizing OB65 for business area financial statements, SAPF181 and SAPF180) will continue to be supported to the known extent; only a further development in the context of classic General Ledger Accounting is not planned.</LI></UL> <UL><LI>In new general ledger accounting (new G/L), you have the option of using the business area either with the known business area accounting function from classic General Ledger Accounting OR as a split criterion of document splitting.</LI></UL> <UL><LI>If the business area is NOT used as a characteristic of document splitting, the business area accounting function known from earlier releases is available in parallel with document splitting (of other document splitting characteristics), at the latest with Note 981775.<br />The balance sheet adjustment can be executed, but no valuation differences can be distributed as the foreign currency valuation function required is no longer available (SAPF100 / Bal.sheet preparation valuatn). In addition, note that the adjustment postings of the report \"Post balance sheet adjustment\" (the report SAPF180 or transaction F.5e) must satisfy the context or check of the document splitting.<br />The profit and loss adjustment (the report SAPF181 or transaction F.50) cannot be executed as CO objects that are also adjusted directly or indirectly (for example, using the profit center) by document splitting, are adjusted.</LI></UL> <UL><LI>If the business area is used as a split criterion of new G/L, the following applies:</LI></UL> <UL><UL><LI>SAPF181 and SAPF180 can no longer be executed.&#x00A0;&#x00A0;These functions are replaced completely by the online document splitting.</LI></UL></UL> <UL><UL><LI>In transaction OB65, the inheritance of business areas can be set in specific cases only, as otherwise this function may collide with document splitting. You are only permitted to set the indicator in transaction OB65 if Customizing for document splitting corresponds to the splitting logic in the balance sheet adjustment for the invoice (derivation of account assignments according to cause). The configuration of the document splitting rules delivered corresponds to this splitting logic. Otherwise, the business area inheritance may destroy the functions of document splitting if additional splitting characteristics are defined in document splitting. As a result, they may remain unassigned in the relevant documents.</LI></UL></UL><UL><UL><LI>The substitution of the business area must not be performed.&#x00A0;&#x00A0;In particular, this applies to subsequent processes (due to the cancellation of the document splitting functions and, as a consequence, the prevention of account assignment according to cause) and, in addition, also if further document splitting characteristics should be defined (as a result of the account assignment string of document splitting).</LI></UL></UL> <p><br />The setting of active business area accounting (OB65) is NOT COMPLETELY compatible to using the business area as a split criterion in new G/L. Since classic business area accounting is based less on cause, the preferred option is to use the business area as a split criterion of document splitting in new G/L.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "EC-PCA (Profit Center Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D017658)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D036528)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2760863", "RefComponent": "FI-GL-BA", "RefTitle": "Business Area is not available in SAP S/4HANA Cloud", "RefUrl": "/notes/2760863 "}, {"RefNumber": "2751285", "RefComponent": "FI-GL-BA", "RefTitle": "inheritance logic for unique business area", "RefUrl": "/notes/2751285 "}, {"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}