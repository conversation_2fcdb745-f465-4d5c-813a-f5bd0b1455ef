{"Request": {"Number": "2447916", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 400, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018759582017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002447916?language=E&token=1446B1031F170FB442EE71F48847B749"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002447916", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002447916/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2447916"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.05.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BW-B4H-CNV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Conversion to SAP BW/4HANA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP BW/4HANA Starter Add-On", "value": "BW-B4H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Conversion to SAP BW/4HANA", "value": "BW-B4H-CNV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H-CNV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2447916 - BW4SL - Real-Time Data Acquisition (RDA)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Real-Time Data Acquisition (RDA)&#160;is not available in&#160;SAP BW/4HANA.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>$RDA, RDAC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are supported or can be converted to SAP BW/4HANA.</p>\r\n<p>See node: Manual Redesign --&gt; BW Object Type</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In BW/4HANA, Real-time Data Acquisition is not available, but instead streaming process chains have to be used. Currently, there is no automatic transfer available yet, which replaces RDA settings with streaming process chains, so please proceed manually as follows. During the procedure, you will not be able to load data in RDA mode.</p>\r\n<p>The first step is to remove and disable RDA settings in the original BW and replace them with standard BW loading. To do this, proceed as follows: The first step is to remove and disable RDA settings in the original BW and replace them with standard BW loading. To do this, proceed as follows:</p>\r\n<ul>\r\n<li>RDA using Service API (S-API)</li>\r\n<ul>\r\n<li>Unassign the RDA daemon in the RDA monitor.</li>\r\n<li>Close the open RDA requests in the RDA monitor if needed.</li>\r\n<li>Delete all processes referring to the RDA InfoPackage. This can be processes for closing RDA requests (RDA_CLOSE), for stopping RDA load processes (RDA_RESET), or for starting RDA load processes (RDA_START). There is no direct replacement for these process types in SAP BW/4HANA as they are considered obsolete when streaming data.</li>\r\n<li>Create a new process chain to replace the RDA process logic which is defined by (a) the RDA InfoPackage, (b) the DTPs assigned to&#160;the corresponding DataSource in the RDA monitor, and&#160;(c) the subsequent process chains assigned to these DTPs in the RDA monitor.</li>\r\n<li>Create a standard delta InfoPackage to replace the RDA InfoPackage (a) and add it to the process chain.</li>\r\n<li>Switch all assigned DTPs (b) to standard mode and add them to the process chain after the InfoPackage.</li>\r\n<li>Create processes for activating data in the&#160;InfoProvider or InfoObject if applicable and add them to the process chain after the corresponding DTP.&#160;</li>\r\n<li>Add the assigned subsequent process chains (c) to the process chain after the corresponding data-activation process or DTP.</li>\r\n<li>Delete the RDA InfoPackage.</li>\r\n</ul>\r\n<li>RDA using Web Service Push</li>\r\n<ul>\r\n<li>Refer to SAP Note <a target=\"_blank\" href=\"/notes/2441826\">2441826</a> on how to deal with source system for Web Service Push.</li>\r\n</ul>\r\n<li>RDA using&#160;ODP&#160;Data Replication API</li>\r\n<ul>\r\n<li>Unassign the RDA daemon in the RDA monitor.</li>\r\n<li>Close the open RDA requests in the RDA monitor if needed.</li>\r\n<li>Delete all processes referring to DTPs for RDA. This can be processes for closing RDA requests (RDA_CLOSE), for stopping RDA load processes (RDA_RESET), or for starting RDA load processes (RDA_START). There is no direct replacement for these process types in BW/4HANA as they are considered obsolete when streaming data.</li>\r\n<li>Create a new process chain to replace the RDA process logic which is defined by (d) the DTP for RDA, and&#160;(e) the subsequent process chains assigned to this DTP in the RDA monitor.</li>\r\n<li>Switch the DTP for RDA&#160;(d) to standard mode and add it to the process chain.</li>\r\n<li>Create a process for activating data in the&#160;InfoProvider or InfoObject if applicable and add it to the process chain.</li>\r\n<li>Add the assigned subsequent process chains (e) to the process chain after the data-activation process or DTP.</li>\r\n</ul>\r\n</ul>\r\n<p>You now have a standard process chain which loads delta data. You can use this process chain, but you should not load it with high frequency. To transfer it to streaming, please do the following:</p>\r\n<p>Run the transfer tool either in In-Place or Remote scenario for your process chain. This will transfer your InfoPackage to DTP (if required) and the SAPI DataSources to ODP DataSources. The resulting chain then can be switched to streaming mode and scheduled accordingly.</p>\r\n<p><strong>Related Information</strong></p>\r\n<p>Refer to <a target=\"_blank\" href=\"https://help.sap.com/viewer/2e90b26cf7484203a523bf0f4b1bc137/7.5.6/en-US/ac029de05e164a12ac1ce08d16180f05.html\">Process Chains for Streaming</a> for more details.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-DST-RDA (Realtime Data Acquisition)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D046539)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D046539)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002447916/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002447916/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002447916/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002447916/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002447916/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002447916/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002447916/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002447916/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002447916/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2441826", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Web Service Source Systems", "RefUrl": "/notes/2441826"}, {"RefNumber": "2421930", "RefComponent": "BW-B4H-CNV", "RefTitle": "Simplification List for SAP BW/4HANA", "RefUrl": "/notes/2421930"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2766983", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "DTP type option \"DTP for Real-Time Data Acquisition\" does appear", "RefUrl": "/notes/2766983 "}, {"RefNumber": "2464367", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL - InfoPackages and Persistent Staging Areas", "RefUrl": "/notes/2464367 "}, {"RefNumber": "2464541", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL - Data Transfer Processes", "RefUrl": "/notes/2464541 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "751", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}