{"Request": {"Number": "1719702", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 506, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017441252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001719702?language=E&token=2B204B231F64632DE0AEB661E0401EEF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001719702", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001719702/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1719702"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.04.2015"}, "SAPComponentKey": {"_label": "Component", "value": "PS-COS-PLN-PLN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Planned Costs"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Project System", "value": "PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Costs", "value": "PS-COS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS-COS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Costs", "value": "PS-COS-PLN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS-COS-PLN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planned Costs", "value": "PS-COS-PLN-PLN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS-COS-PLN-PLN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1719702 - FAQ: New user interface for project cost planning"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note provides information about the new user interfaces for project cost planning.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><PERSON>J40, CJR2, Excel, Analysis Office, BEx Analyzer, Enhancement Package 6, EhP6, CO_PLAN_REN</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Enhancement Package 6 delivers new user interfaces for project planning.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This SAP Note answers questions about the new functions in project planning.</p>\r\n<ul>\r\n<li>Which new user interfaces does EhP6 deliver?</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The SAP delivery contains new Web Dynpro interfaces for year-dependent and year-independent overall planning and for cost element planning of projects.</p>\r\n<ul>\r\n<li>Which functions do the new interfaces provide?</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The \"New Project Planning\" PDF documents available in the \"Attachments\" section of this SAP Note provide a brief overview with screenshots.</p>\r\n<ul>\r\n<li>Does the delivery also contain Excel interfaces for project planning?</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP delivers input-ready BW queries with EhP6. Based on these queries, customers can create their own planning interfaces in Excel with Analysis Office or BEx Analyzer.</p>\r\n<ul>\r\n<li>Are additional licenses required for the new Web Dynpro planning?</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additional licenses are not required for planning with Web Dynpro interfaces. The ERP license is sufficient.</p>\r\n<ul>\r\n<li>Are additional licenses required for the new Excel planning?</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Corresponding licenses are required for Excel planning with Analysis Office. The ERP license is sufficient for Excel planning with BEx Analyzer.</p>\r\n<ul>\r\n<li>The new planning uses BW queries. Is a BW system required for this?</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No. A BW system is not required. The BW queries are delivered as part of the ERP package SAP_APPL. They are activated directly in the ERP system as BW Content.</p>\r\n<ul>\r\n<li>Where are the BW InfoCubes saved for planning?</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The new project planning uses queries that read data directly from the ERP tables and write data to the ERP tables. No BW InfoCubes are used.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CO-OM-OPA-B (Planning)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (D026401)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D002832)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001719702/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001719702/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001719702/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001719702/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001719702/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001719702/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001719702/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001719702/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001719702/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "New_Project_Planning-2of3.pdf", "FileSize": "1680", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000276852012&iv_version=0004&iv_guid=CEC25F91968A3F4FA0F09B549D82CAA4"}, {"FileName": "New_Project_Planning-1of3.pdf", "FileSize": "790", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000276852012&iv_version=0004&iv_guid=E904121FDD13BA48A0EE21F1E93A630D"}, {"FileName": "New_Project_Planning-3of3.pdf", "FileSize": "1639", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000276852012&iv_version=0004&iv_guid=EF05658BD247DF47A2A55988775C3990"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1880317", "RefComponent": "CO-OM-CCA-B", "RefTitle": "FAQ: Initial steps with new cost planning", "RefUrl": "/notes/1880317"}, {"RefNumber": "1836149", "RefComponent": "CO-OM-CCA-B", "RefTitle": "Business functions FIN_CO_CCMGMT, FIN_CO_CCPLAN, and FIN_CO_ORPLAN", "RefUrl": "/notes/1836149"}, {"RefNumber": "1777947", "RefComponent": "CO-OM-CCA-B", "RefTitle": "FAQ: New user interfaces for cost center planning", "RefUrl": "/notes/1777947"}, {"RefNumber": "1746333", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1746333"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2861236", "RefComponent": "CO-OM-CCA-B", "RefTitle": "New User Interfaces Project Cost Planning:  No meaningful messages displayed for projects where planning is forbidden", "RefUrl": "/notes/2861236 "}, {"RefNumber": "2742613", "RefComponent": "CO-OM", "RefTitle": "Obsolete or replaced transaction codes and programs in Finance applications of S/4", "RefUrl": "/notes/2742613 "}, {"RefNumber": "1946054", "RefComponent": "FI-AR", "RefTitle": "SAP Simple Finance, on-premise edition: Transaction codes and programs - Comparison to EHP7 and EHP8 for SAP ERP 6.0", "RefUrl": "/notes/1946054 "}, {"RefNumber": "1880317", "RefComponent": "CO-OM-CCA-B", "RefTitle": "FAQ: Initial steps with new cost planning", "RefUrl": "/notes/1880317 "}, {"RefNumber": "1836149", "RefComponent": "CO-OM-CCA-B", "RefTitle": "Business functions FIN_CO_CCMGMT, FIN_CO_CCPLAN, and FIN_CO_ORPLAN", "RefUrl": "/notes/1836149 "}, {"RefNumber": "1777947", "RefComponent": "CO-OM-CCA-B", "RefTitle": "FAQ: New user interfaces for cost center planning", "RefUrl": "/notes/1777947 "}, {"RefNumber": "1746333", "RefComponent": "PS-COS-PLN-PLN", "RefTitle": "Kostenartenplanung mit mehreren Jahren (Modifikation)", "RefUrl": "/notes/1746333 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}