{"Request": {"Number": "413158", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 311, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001876282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000413158?language=E&token=A947F6BF2EAC5111FD1516374E5777B9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000413158", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000413158/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "413158"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.04.2003"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-PO-GUI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Userinterface - Purchase Orders"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchase Orders", "value": "MM-PUR-PO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-PO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Userinterface - Purchase Orders", "value": "MM-PUR-PO-GUI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-PO-GUI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "413158 - Subsequent change of document type"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the Enjoy transactions of purchasing, you can change the document type subsequently.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>ME21N, ME22N, ME23N, ME51N, ME52N, ME53N, document type, Enjoy fields, order type<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This was implemented up to now.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In Customizing (Define Screen Layout at Document Level), it is allowed to control the display attributes for field \"Document type\" for the Enjoy purchase order and as of Release 4.6C also for the Enjoy purchase requisition.<br /><br />You can carry out the advance correction as follows:<br /><br />First, you have to create the following entries in tables T162, T162X, and T162Y using Transaction SM31:<br /></p> <UL><LI>T162Y: new entry (pushbutton \"Maintain\"): ---------------------------------------------<BR/> |Sprache | Gruppe | Bezeichnung&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<BR/> |Language| Group&#x00A0;&#x00A0;| Description&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |<BR/> |-------------------------------------------|<br />|DE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;30&#x00A0;&#x00A0;&#x00A0;&#x00A0;| Enjoy-Felder&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<br />|EN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;30&#x00A0;&#x00A0;&#x00A0;&#x00A0;| Enjoy fields&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|<BR/> ---------------------------------------------</LI></UL> <p>Confirm the prompt \"Information: Please do not make any changes (data is owned by SAP)\" - also when maintaining T162X.</p> <UL><LI>T162X: new entry (pushbutton \"Maintain\"): ------------------------------------------------------------------<BR/> |Sprache |Tab|Pos|Bezeichnung&#x00A0;&#x00A0;&#x00A0;&#x00A0;|Gruppe|Best|Anfr|Kont|Lfpl|Banf|<BR/> |Language|Tab|Pos|Description&#x00A0;&#x00A0;&#x00A0;&#x00A0;|Group |PO&#x00A0;&#x00A0;|RFQ |Ctr.|SchA|PReq|<BR/> |----------------------------------------------------------------|<br />|DE&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|1&#x00A0;&#x00A0;|142|Belegart&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; |30&#x00A0;&#x00A0;&#x00A0;&#x00A0;|X&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|X&#x00A0;&#x00A0; |<br />|EN&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;|1&#x00A0;&#x00A0;|142|Document type&#x00A0;&#x00A0;|30&#x00A0;&#x00A0;&#x00A0;&#x00A0;|X&#x00A0;&#x00A0; |&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|&#x00A0;&#x00A0;&#x00A0;&#x00A0;|X&#x00A0;&#x00A0; |<BR/> ------------------------------------------------------------------</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Do not make an entry for \"PReq\" in a release earlier than Release 4.6C, in Release 4.6B, leave this field empty.<br /></p> <UL><LI>T162: new entry (pushbutton \"Customizing\"):</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For the required transactions, create the appropriate entry or copy it from a template.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Enter the field selection key (for example, ME23N) in field \"FldSl\" and enter the display attributes for the document type in field selection group \"Enjoy fields\".<br /><br />After this, you can implement the corrections in the program code according to the instructions contained in this note.<br /><br />The required corrections:&#x00A0;&#x00A0;&#x00A0;&#x00A0; Note 368585 (only Release 4.6C)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 313301<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 324447<br />are only used for locating the corresponding program points.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "MM-PUR-REQ-GUI (Userinterface - Purchase Requisitions)"}, {"Key": "Transaction codes", "Value": "TRANSAKTIONEN"}, {"Key": "Transaction codes", "Value": "SM31"}, {"Key": "Responsible                                                                                         ", "Value": "D034866"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D031268)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000413158/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000413158/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413158/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413158/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413158/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413158/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413158/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413158/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000413158/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "491789", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/491789"}, {"RefNumber": "324447", "RefComponent": "MM-PUR", "RefTitle": "Field selection key, error ME028 (II)", "RefUrl": "/notes/324447"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "491789", "RefComponent": "MM-PUR-PO-GUI", "RefTitle": "FAQ: Purchase order (ME21N, ME22N, ME23N)", "RefUrl": "/notes/491789 "}, {"RefNumber": "324447", "RefComponent": "MM-PUR", "RefTitle": "Field selection key, error ME028 (II)", "RefUrl": "/notes/324447 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B31", "URL": "/supportpackage/SAPKH46B31"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C28", "URL": "/supportpackage/SAPKH46C28"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C22", "URL": "/supportpackage/SAPKH46C22"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 3, "URL": "/corrins/0000413158/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "46A", "ValidTo": "46C", "Number": "313301 ", "URL": "/notes/313301 ", "Title": "ME21N Performance improvement to field selection", "Component": "MM-PUR-PO-GUI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46A", "ValidTo": "46C", "Number": "324447 ", "URL": "/notes/324447 ", "Title": "Field selection key, error ME028 (II)", "Component": "MM-PUR"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "368585 ", "URL": "/notes/368585 ", "Title": "ME21N Performance field selection and start of the transact.", "Component": "MM-PUR-PO-GUI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "413158 ", "URL": "/notes/413158 ", "Title": "Subsequent change of document type", "Component": "MM-PUR-PO-GUI"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}