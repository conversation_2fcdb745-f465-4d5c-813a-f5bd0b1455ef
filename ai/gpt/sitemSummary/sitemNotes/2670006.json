{"Request": {"Number": "2670006", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 504, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001433672018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002670006?language=E&token=A161BD595B83D27C428001BF0791315D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002670006", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002670006/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2670006"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.01.2019"}, "SAPComponentKey": {"_label": "Component", "value": "PM-WOC-M<PERSON>"}, "SAPComponentKeyText": {"_label": "Component", "value": "Maintenance Orders"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Plant Maintenance", "value": "PM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Maintenance Order Management", "value": "PM-W<PERSON>", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PM-WOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Maintenance Orders", "value": "PM-WOC-M<PERSON>", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PM-WOC-MO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2670006 - Object List Number Field Length Extension - General Technical Background Information"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The object list number is extended.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Object List Number, Field Length Extension, OBJKN, OBJKNR, BAPI, IDoc, RFC, 10 digits, 19 digits, SER00, SER01, SER02, SER03, SER04, SER05, SER06, SER07, SER08, SER09</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In SAP S/4HANA, the object list number&#160;field length&#160;has been&#160;extended from <span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">10 to 19 characters</span>. This change was first implemented in SAP S/4HANA, on-premise edition 1809 and affects this release and higher releases.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Preface</strong></p>\r\n<p>SAP S/4HANA supports object list numbers with up to&#160;<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">19 characters</span> max value. Therefore, the maximum length of the&#160;fields that handle the object list number has been extended from length&#160;<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">10(int4) to 19(int8)</span>.</p>\r\n<p>This note gives an overview about how this extension&#160;was implemented&#160;and what the consequences of the extension are for customer code. For&#160;details on different aspects, see the referenced notes.</p>\r\n<p>Extending the object list number means that the code in the system must guarantee that all relevant bits of code can handle 19&#160;<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">characters</span>.&#160;It's especially important to&#160;ensure that the object list number&#160;is not&#160;truncated to its former length of <span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">10 characters</span>,&#160;because this would result in data loss. The&#160;corresponding SAP development entities (domains, data elements, structures, table types, and transparent tables, external and internal interfaces, user interfaces, and so on) have been adapted accordingly.</p>\r\n<p>Furthermore, all database fields that may be used to store an object list number also have to be extended.&#160;Where required, automated logic is in place and executed automatically in case a customer converts his current SAP Business Suite system to SAP S/4HANA.</p>\r\n<p>This note and the referenced notes describe which changes were needed to accommodate for the technical length extension of the object list number. As customer and partner code uses the same techniques all described adaptions need also to be done in affected customer coding, irrespective of whether the extended&#160;object list number is actually activated (see below) or whether you stay with&#160;object list of 10 characters.</p>\r\n<p><strong>System-Internal Coding</strong></p>\r\n<p>In an SAP system,&#160;consistent behavior of a data field is usually guaranteed by the fact that all data elements used&#160;are derived from the same domain, the domain defining the technical length of the derived fields. Whenever code uses these fields,&#160;it is automatically adapted when the domain changes. For the&#160;object list number, several domains&#160;that are used to define object list fields have been extended to 19<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">&#160;characters</span>.</p>\r\n<p>Overall, the entire data flow in the system&#160;was analysed to identify all the places&#160;in which an object list number was moved to a field that was not long enough for a&#160;<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">19 character&#160;</span>number. These conflicts have been handled in one of the ways described above. This type of analysis is also required for customer or partner coding (using appropriate tools).</p>\r\n<p>The described changes have also been applied to parameters of all interfaces that are usually called only within one system, such as local function modules, class methods, BAdIs, and so on. In the types and structures that are used as parameters in these local calls, the&#160;object list number has simply been extended to&#160;<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">19 characters</span>. The same is also true for other extended fields. This was usually also done for unreleased remote-enabled function modules,&#160;because the main use case for such function modules is an internal decoupling within one system (e.g. parallel processing).</p>\r\n<p>For interfaces that are usually called remotely, a different way has been chosen. For more information, see the specific chapter below.</p>\r\n<p><strong>Database Representation of the Object List Number</strong></p>\r\n<p>Extending the object list number&#160;in the database means that the field length of the OBJKNR field in the database has been extended from <span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">10(int4) to 19(int8) characters</span>. This has been done in all tables (and all fields within the tables) in which an object list number can be stored.</p>\r\n<p>The way&#160;in which&#160;the object list number content is stored in these database field has not been changed compared to SAP Business Suite.</p>\r\n<p><strong>Released External Interfaces</strong></p>\r\n<p>External interfaces used for integration in a multi-system landscape (with SAP and/or non-SAP systems)&#160;must be&#160;compatible with the old version of the interfaces, because it cannot be assumed that all connected system are able to deal with 19<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">&#160;characters</span> object list numbers immediately.</p>\r\n<p>This&#160;is especially relevant for&#160;the commonly used integration techniques BAPI, RFC, and IDoc,&#160;because these techniques rely on a fixed length and&#160;order of&#160;fields in the transmitted data.&#160;Simply extending the object list number field (or other extended fields) in these interfaces would therefore technically break the version compatibility. For the object list number and other extended fields as described in this note, version compatibility is achieved in the same way that is commonly used for BAPI interfaces: The already existing field keeps its original length and a new field has been added at the end of the structure that allows transmitting 19<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">&#160;characters </span>object list number. Therefore a NUMC20 field was added as&#160;<span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">19 characters&#160;</span>is handled differently in non ABAP languages.</p>\r\n<p>The changes described have&#160;been&#160;made&#160;for BAPIs, IDocs, and released remote-enabled function modules.</p>\r\n<p>For released WebServices,&#160;adding a new field or extending the object list number field was usually not necessary&#160;because these services already allow object list numbers with more than length 19 in their interfaces.</p>\r\n<p>If you have created own external interfaces you have to check if similar compatible changes are needed.</p>\r\n<p><strong>Internal Calls of Released External Interfaces</strong></p>\r\n<p>As described in the previous chapters, different strategies have been chosen for internal and for released external APIs.</p>\r\n<p>If a released external API is called internally, that is, locally within one system,&#160;compatibility is not required. The interfaces already understand and fill the new extended fields. Therefore,&#160;all internal calls of external interfaces must only use the newly added extended fields.</p>\r\n<p>This is also true if structures that are also used in released external interfaces, and which have therefore been changed in the way described, are used internally. Additionally, only the new extended field should be used&#160;for all internal coding in this case.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PP-SFC-EXE (Order Execution)"}, {"Key": "Other Components", "Value": "LE-SHP-GF (Basic Functions)"}, {"Key": "Other Components", "Value": "LO-HU (Handling Unit Management)"}, {"Key": "Other Components", "Value": "SD-SLS (Sales)"}, {"Key": "Other Components", "Value": "MM-IM (Inventory Management)"}, {"Key": "Other Components", "Value": "LO-MD-SN (Serial Numbers)"}, {"Key": "Other Components", "Value": "QM-IM (Quality Inspection)"}, {"Key": "Other Components", "Value": "MM-PUR (Purchasing)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D056745)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D056745)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002670006/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002670006/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002670006/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002670006/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002670006/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002670006/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002670006/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002670006/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002670006/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2669857", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "Object List Number Field Length Extension: Code Adaptions for usages of released RFC and BAPIs", "RefUrl": "/notes/2669857 "}, {"RefNumber": "2669781", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "Object list number Field Length Extension: Code Adaptions for compatibly enhanced local function modules", "RefUrl": "/notes/2669781 "}, {"RefNumber": "2672753", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "Object List Number Field Length Extension - Code Adaption", "RefUrl": "/notes/2672753 "}, {"RefNumber": "2580670", "RefComponent": "PM-WOC-M<PERSON>", "RefTitle": "S4TWL - Object List Number Field Length Extension", "RefUrl": "/notes/2580670 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}