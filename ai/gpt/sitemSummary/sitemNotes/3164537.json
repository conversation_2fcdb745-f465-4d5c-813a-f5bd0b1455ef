{"Request": {"Number": "3164537", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 407, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000928362022"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003164537?language=E&token=5FE989A3947859202FA33D8BBBF3C549"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003164537", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3164537"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.07.2022"}, "SAPComponentKey": {"_label": "Component", "value": "FS-CM-LC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Financial Services Localization"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Claims Management", "value": "FS-CM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-CM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Financial Services Localization", "value": "FS-CM-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-CM-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3164537 - S4TWL - TR insurance localization - deprecation of package /GSINS/TR"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p id=\"\">You are doing a system conversion from SAP ERP to SAP S/4HANA or an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this Note.</p>\r\n<p>The following SAP S/4HANA Simplification Item <strong>SI8: GSFIN_Insurance_TR</strong> is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">Turkey, Insurance, /GSINS/TR, S4DEPREC, deprecation, obsolete</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p id=\"\">Prerequisite: Insurance Localization FS-CM for Turkey installed.</p>\r\n<p>Reason for deprecation:</p>\r\n<ul>\r\n<li>TRAMER functionality could be used to export automotive insurance related information in a specific format as XML in order to submit it to authorities in Turkey.</li>\r\n<li>the functionality is not usable anymore without customer modification, as the export format defined by the Turkish government has changed since initial delivery of the functionality, but it was never adapted to the new format.</li>\r\n<li>hence today customers already use other means to export this data from the system in the required format (eg. export to external text file and perform mapping to a suitable XML format with XSLT or via 3<sup>rd</sup> party, own code&#8230;).</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"longtext\">\r\n<p><strong>Description</strong></p>\r\n<p>Package /GSINS/TR and it contents becomes obsolete because of no customer usage. Objects stored in the package and corresponding subpackages are removed including their references from other objects.</p>\r\n<p>TRAMER functionality has already been put on the blocklist and hence will not be usable anymore without intervention in&#160;S/4HANA OP2022 release.</p>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>Insurance localization for Turkey stored in the package /GSINS/TR and it's subpackages is not&#160;being used and has been deprecated in S/4HANA OP2022 and became obsolete in all the supported lower versions.<br />Regarding S/4HANA OP2022 release, for transition period of 2 years the package and it's subpackages has been moved to SW component 'S4DEPREC' if the restoration of the functionality is needed.</p>\r\n<p>Note content:</p>\r\n<p>1) Existing&#160;<strong>references</strong>&#160;in objects ('Ref. Object Name') pointing to the objects in the package /GSINS/TR and it's subpackages&#160;<strong>were removed</strong>:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Object Name</strong></td>\r\n<td><strong>Package</strong></td>\r\n<td><strong>Application Area</strong></td>\r\n<td><strong>Software Component</strong></td>\r\n<td><strong>Info</strong></td>\r\n<td><strong>Ref. Object Type</strong></td>\r\n<td><strong>Ref. Object Name</strong></td>\r\n</tr>\r\n<tr>\r\n<td>/GSINS/TR_CL_SWITCH_CHECK</td>\r\n<td>/GSINS/TR_REU</td>\r\n<td>FS-CM-LC</td>\r\n<td>INSURANCE</td>\r\n<td>used by</td>\r\n<td>REPS</td>\r\n<td>LICL_MINI_BPF04</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />2) Following&#160;<strong>transactions</strong>&#160;were&#160;<strong>set as obsolete in SU22 transaction</strong>, so these cannot be executed:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 898px; height: 163px;\"><colgroup><col span=\"2\" width=\"346\" /></colgroup>\r\n<tbody>\r\n<tr>\r\n<td class=\"xl67\" height=\"21\" width=\"346\"><strong>Transaction</strong></td>\r\n<td class=\"xl67\" width=\"346\"><strong>Description</strong></td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"21\" width=\"346\">/GSINS/77000006</td>\r\n<td class=\"xl66\" width=\"346\">Define Transformation Definitions</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"21\" width=\"346\">/GSINS/77000007</td>\r\n<td class=\"xl66\" width=\"346\">Assign Internal Claim Types to TRAMER Transformation</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"21\" width=\"346\">/GSINS/77000008</td>\r\n<td class=\"xl66\" width=\"346\">obsolete</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"21\" width=\"346\">/GSINS/77000009</td>\r\n<td class=\"xl66\" width=\"346\">BAdI: XML Structure Modification for Transformation KASKO</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"21\" width=\"346\">/GSINS/77000010</td>\r\n<td class=\"xl66\" width=\"346\">BAdI: XML Structure Modification for Transformation TRAFIK</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"21\" width=\"346\">/GSINS/TR_TRAMER</td>\r\n<td class=\"xl66\" width=\"346\">XML File Generation TRAMER Reporting</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<p>In general, SAP recommends that you install a solution by applying a Support Package. You can find more information about the Note Assistant in SAP Service Marketplace, under&#160;<a target=\"_blank\" href=\"https://service.sap.com/note-assistant\">service.sap.com/note-assistant</a>.</p>\r\n<p>However, if you need to install a solution earlier, use the Note Assistant to implement and follow these steps:</p>\r\n<p><strong>IMPORTANT:</strong></p>\r\n<p>To complete the installation procedure for&#160;<strong>S4TWL -&#160;TR insurance localization - deprecation of package /GSINS/TR</strong>&#160;you have to implement the automatic correction instructions from this Note 3164537.<strong><br /></strong></p>\r\n<p>Remember, that you&#160;cannot use your own coding/functionality anymore which is based on the removed coding/functionality, explained in&#160;this Note 3164537.</p>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-LOC-CA-TR (Turkey)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I041621)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I530436)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003164537/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003164537/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003164537/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003164537/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003164537/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003164537/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003164537/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003164537/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003164537/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "INSURANCE", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "800", "To": "800", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "801", "To": "801", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "802", "To": "802", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "803", "To": "803", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "804", "To": "804", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "805", "To": "805", "Subsequent": ""}, {"SoftwareComponent": "INSURANCE", "From": "806", "To": "806", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "INSURANCE 617", "SupportPackage": "SAPK-61725ININSURANC", "URL": "/supportpackage/SAPK-61725ININSURANC"}, {"SoftwareComponentVersion": "INSURANCE 618", "SupportPackage": "SAPK-61819ININSURANC", "URL": "/supportpackage/SAPK-61819ININSURANC"}, {"SoftwareComponentVersion": "INSURANCE 802", "SupportPackage": "SAPK-80211ININSURANC", "URL": "/supportpackage/SAPK-80211ININSURANC"}, {"SoftwareComponentVersion": "INSURANCE 803", "SupportPackage": "SAPK-80309ININSURANC", "URL": "/supportpackage/SAPK-80309ININSURANC"}, {"SoftwareComponentVersion": "INSURANCE 804", "SupportPackage": "SAPK-80407ININSURANC", "URL": "/supportpackage/SAPK-80407ININSURANC"}, {"SoftwareComponentVersion": "INSURANCE 805", "SupportPackage": "SAPK-80505ININSURANC", "URL": "/supportpackage/SAPK-80505ININSURANC"}, {"SoftwareComponentVersion": "INSURANCE 806", "SupportPackage": "SAPK-80603ININSURANC", "URL": "/supportpackage/SAPK-80603ININSURANC"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "INSURANCE", "NumberOfCorrin": 19, "URL": "/corrins/0003164537/21"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 19, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "INSURANCE", "ValidFrom": "617", "ValidTo": "617", "Number": "1871970 ", "URL": "/notes/1871970 ", "Title": "TR Insurance: Address Change in Claims Management", "Component": "XX-CSC-TR-FS"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}