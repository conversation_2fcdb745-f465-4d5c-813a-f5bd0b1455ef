{"Request": {"Number": "2858316", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 163, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=E44D95160F5E8D63A08DA762B0201558"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2858316"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "CA-MDG-DRF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Replication Framework"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2858316 - SAP S/4HANA Migration Cockpit: Material/Product/Business Partner/Cost Center master does not trigger Communication Arrangements"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ul>\r\n<li>You are migrating data using the&#160;SAP S/4HANA Migration Cockpit and the approach \"Migrate data using staging tables\"</li>\r\n<li>You are migrating <strong>Material or Product master data</strong> via one of the following release-dependent migration objects</li>\r\n<ul>\r\n<li>Material (deprecated) (SIF_MATERIAL)&#160;</li>\r\n<li>Material- extend exist record by new org levels (deprecated)(SIF_MAT_EXTEND)&#160;</li>\r\n<li>Product (SIF_PRODUCT)&#160;</li>\r\n<li>Product - extend existing rec by new org levels (SIF_PROD_EXT, SIF_PROD_EXT_2)</li>\r\n<li>Product - extend existing record with long text (SIF_PROD_LONGTXT)</li>\r\n<li>Service product (SIF_PROD_SERV)</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\"><span style=\"font-size: 14px;\">and the replication via communication arrangement for&#160;</span><em style=\"font-size: 14px;\">Product Integration</em><span style=\"font-size: 14px;\">&#160;(</span><strong style=\"font-size: 14px;\">SAP_COM_0009</strong><span style=\"font-size: 14px;\">) is not triggered.&#160;</span></p>\r\n<ul>\r\n<li><span style=\"font-size: 14px;\">You are migrating <strong>Business Partner (BP)for example \"Customer\" or \"Supplier\"</strong> master data via one of the following migration objects&#160;</span></li>\r\n<ul>\r\n<li>Customer (SIF_CUSTOMER_2)</li>\r\n<li><span style=\"font-size: 14px;\">Supplier (SIF_VENDOR_2)</span></li>\r\n<li><span style=\"font-size: 14px;\">Customer - extend existing record by new org levels (SIF_CUST_EXT_2, SIF_CUST_EXT_3)</span></li>\r\n<li><span style=\"font-size: 14px;\">Supplier - extend existing record by new org levels (SIF_VEND_EXT_2,&#160;SIF_VEND_EXT_3).</span></li>\r\n<li><span style=\"font-size: 14px;\">Customer - extend existing record by credit management data (SIF_CUST_UKM,&#160;SIF_CUST_UKM2)</span></li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">and the replication via communication arrangement for&#160;<em>Business Partner Integration</em>&#160;(<strong>SAP_COM_0008</strong>) is not triggered.</p>\r\n<ul>\r\n<li>You are migrating <strong>Cost center</strong>&#160;master data via one of the following migration objects</li>\r\n<ul>\r\n<li>CO - Cost center (SIF_KOSTL_MASTER)</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">and the replication of the cost center via the following communication arrangements is not triggered:</p>\r\n<ul>\r\n<ul>\r\n<li><em>Accounting Master Data Integration&#160;</em>(<strong>SAP_COM_0179</strong>) or</li>\r\n<li><em>Employee Central Cost Center Integration&#160;</em>(<strong>SAP_COM_0056</strong>) or</li>\r\n<li><em>Employee Central Payroll Integration </em>(<strong>SAP_COM_00</strong><strong>28</strong>) or</li>\r\n<li><em>Master Data Integration</em> (<strong>SAP_COM_0659 or&#160;</strong><strong>S</strong><strong>AP_COM_05</strong><strong>94</strong>)</li>\r\n</ul>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">&#160;For the approach \"Migrate data directly from SAP System\" you must search for a different KBA.</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>SAP S/4HANA</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<ol>\r\n<li>Migrate business partner or product master data or cost center master data into the system&#160;</li>\r\n<li>The replication is not triggered automatically to send out the new BP or product master or cost center master data records.</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>The replication trigger is disabled during migration&#160;since it would cause performance issues and might lead to a heavy load.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>Data migration doesn&#8217;t trigger the replication of the product master and the business partner via Data Replication (DRF). Nevertheless, you can replicate your product master and business partner master data after all product or business partner master data records have been migrated. This can be done with the following apps:</p>\r\n<ul>\r\n<li>Use the&#160;<a target=\"_blank\" class=\"xref\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('DRF_MANUAL_REPLICATION%2520()')/S18\" title=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('DRF_MANUAL_REPLICATION%2520()')/S18\">Replicate by Object Selection</a>&#160;app to specify the product numbers to be replicated and to execute the replication.</li>\r\n<li>Use the&#160;<a target=\"_blank\" class=\"xref\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('DRF_ADHOC_REPLICATION%2520()')/S18\" title=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('DRF_ADHOC_REPLICATION%2520()')/S18\">Replicate by Replication Model</a>&#160;app if many product numbers need to be replicated with the respective filter criteria, if needed.</li>\r\n</ul>\r\n<p class=\"p\" style=\"display: inline !important;\">For details about the apps, check the related SAP S/4HANA Cloud product assistance on SAP Help Portal:</p>\r\n<ul class=\"ul\" id=\"loio289644d401a844878ce84670517dfa98__ul_qz4_dx3_qjb\" style=\"font-size: 14px;\">\r\n<li class=\"li\"><a target=\"_blank\" class=\"xref\" href=\"https://help.sap.com/viewer/a630d57fc5004c6383e7a81efee7a8bb/latest/en-US/4847a6908fda4e3ba6be8c81202e349c.html\" title=\"https://help.sap.com/viewer/a630d57fc5004c6383e7a81efee7a8bb/latest/en-US/4847a6908fda4e3ba6be8c81202e349c.html\">Replicate by Object Selection</a></li>\r\n<li class=\"li\"><a target=\"_blank\" class=\"xref\" href=\"https://help.sap.com/viewer/a630d57fc5004c6383e7a81efee7a8bb/latest/en-US/b8f3bdb0eb51436499a2c6ac88a7d458.html\" title=\"https://help.sap.com/viewer/a630d57fc5004c6383e7a81efee7a8bb/latest/en-US/b8f3bdb0eb51436499a2c6ac88a7d458.html\">Replicate by Replication Model</a></li>\r\n</ul>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>Data Replication Framework, DRF, Material, Customer, Supplier, Business Partner, Product, Material, Migration Cockpit, Migrate your data, Communication Arrangement,&#160;SAP_COM_0009, SAP_COM_0008,&#160;SIF_CUST_EXT_2,&#160;SIF_CUST_EXT_3,&#160;SIF_VEND_EXT_2,&#160;SIF_VEND_EXT_3, SIF_PRODUCT, SIF_PROD_EXT, SIF_PROD_EXT_2, SIF_MATERIAL,&#160;SIF_PROD_LONGTXT,&#160;SIF_PROD_SERV,SAP_COM_0056,&#160;SAP_COM_0179, cost center,&#160;Accounting Master Data Integration,&#160;Employee Central Cost Center Integration, Employee Central Payroll Integration, SAP_COM_0028, Master Data Integration, SAP_COM_0659, SAP_COM_0594, MATERIAL, MAT_EXTEND; staging tables</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "LO-MD-BP-WS (Web Service for Business Partner)"}, {"Key": "Other Components", "Value": "LO-MD-BP (Business Partners)"}, {"Key": "Other Components", "Value": "CA-GTF-MIG (SAP S/4HANA Data Migration Cockpit Content)"}, {"Key": "Other Components", "Value": "LO-MD-M<PERSON> (Material Master)"}, {"Key": "Other Components", "Value": "LO-MD-BP-MIG (Business Partner Migration)"}, {"Key": "Other Components", "Value": "CA-LT-MC (S/4HANA Migration Cockpit)"}, {"Key": "Other Components", "Value": "CO-FIO-CCA (Cost Center Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029057)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "", "RefComponent": "", "RefTitle": "APP: Replicate by Object Selection", "RefUrl": "https://help.sap.com/viewer/a630d57fc5004c6383e7a81efee7a8bb/latest/en-US/4847a6908fda4e3ba6be8c81202e349c.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "APP: Replicate by Replication Model", "RefUrl": "https://help.sap.com/viewer/a630d57fc5004c6383e7a81efee7a8bb/latest/en-US/b8f3bdb0eb51436499a2c6ac88a7d458.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "FIORI Apps Library: Replicate by Replication Model", "RefUrl": "https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('DRF_ADHOC_REPLICATION%252520%252528%252529')/S18"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "FIORI Apps Library: Replicate by Object Selection", "RefUrl": "https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('DRF_MANUAL_REPLICATION%2520()')/S18"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2811788", "RefComponent": "CA-GTF-MIG", "RefTitle": "Migration cockpit: Collective KBA for migration object Material / Product (SIF_MATERIAL / SIF_PRODUCT)", "RefUrl": "/notes/2811788 "}, {"RefNumber": "2848224", "RefComponent": "LO-MD-BP-MIG", "RefTitle": "Migration Cockpit: Collective KBA for Business Partner (Customer, Supplier)", "RefUrl": "/notes/2848224 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP S/4HANA Cloud all versions "}, {"Product": "SAP S/4HANA all versions "}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}