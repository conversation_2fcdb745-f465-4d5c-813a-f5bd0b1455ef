{"Request": {"Number": "2475034", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1633, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019279642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002475034?language=E&token=9938C98E48817467063B91EBC2D3B3D4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002475034", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002475034/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2475034"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 121}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.02.2023"}, "SAPComponentKey": {"_label": "Component", "value": "CA-LT-MC"}, "SAPComponentKeyText": {"_label": "Component", "value": "S/4HANA Migration Cockpit"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Landscape Transformation", "value": "CA-LT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Migration Cockpit", "value": "CA-LT-MC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT-MC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2475034 - Migration Cockpit & SLT on S/4 HANA On-Premise Edition 1709"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to run the SAP&#160;S/4 HANA Migration Cockpit or&#160;SAP Landscape&#160;Transformation Replication Server using S/4 HANA ON-Premise Edition&#160;1709.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP HANA, SLT, LTMC, LTMOM, replication, Migration Cockpit, trigger, cFIN, central Finance</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Introduction<br /></strong>This note describes the behaviour of two different products:<br />- SAP Landscape Transformation Replication Server (SLT),&#160;for real-time replication scenarios&#160;&#160;<br />- S/4HANA Migration Cockpit, for data migration to S/4HANA</p>\r\n<p>These two products are available with different license options. If you own the license of one product, it does not mean you are automatically allowed to use the other product and the functionality.</p>\r\n<p><strong><br />Table of content</strong></p>\r\n<p>1.&#160;Enhancements / Corrections&#160;/ Considerations for SAP&#160;S/4 HANA On-Premise 1709<br />2. Equivalence to DMIS version<br />3. Relevant SAP Notes for S/4 On-Premise 1709</p>\r\n<p><strong>1.&#160;Enhancements / Corrections&#160;/ Considerations for S/4 HANA On-Premise 1709</strong></p>\r\n<p><strong>Major Enhancements</strong></p>\r\n<ul>\r\n<li><span style=\"color: #ff0000;\"><span style=\"color: #000000;\">General availability of the Central Finance scenario (including the&#160;use of SAP Landscape Replication Server&#160;as central SLT server)</span></span></li>\r\n<li><span style=\"color: #ff0000;\"><span style=\"color: #000000;\">General availability of the SAP S/4 HANA Migration Cockpit for S/4 HANA On-Premise</span></span></li>\r\n<ul>\r\n<li><span style=\"color: #ff0000;\"><span style=\"color: #000000;\">Predelivered content including detailed documentation</span></span></li>\r\n<li><span style=\"color: #ff0000;\"><span style=\"color: #000000;\">Guided activity for the process</span></span></li>\r\n</ul>\r\n<li><span style=\"color: #ff0000;\"><span style=\"color: #000000;\">General availability of the SAP S/4 HANA Migration Object Modeler with the feature to add field extensions to the predelivered content</span></span></li>\r\n</ul>\r\n<p><strong>Important Considerations</strong></p>\r\n<ul>\r\n<li><span style=\"color: #ff0000;\"><span style=\"color: #000000;\">If you want to run SLT in the S/4 HANA system, the only supproted scenario is&#160;<strong>Central Finance (cFIN).</strong></span></span></li>\r\n<li><strong><span style=\"color: #ff0000;\"><span style=\"color: #000000;\">For the Central Finance (cFIN) scenario make sure to use the updated content for SP12 and higher according to note 2154420.</span></span></strong></li>\r\n<li><span style=\"color: #ff0000;\"><span style=\"color: #000000;\">If you use S/4 HANA 1709 as the source or target system of a SLT replication, it is recommended that the SAP NetWeaver release of the SLT system is on the same or higher release as the S/4 HANA system. This is necessary to support all data types of the S/4 HANA system in the SLT replication. Otherwise, tables with columns using e.g. data type INT8 cannot be replicated.</span></span></li>\r\n</ul>\r\n<p><span style=\"color: #ff0000;\"><span style=\"color: #000000;\"><strong>2.&#160;Equivalence to DMIS version</strong></span></span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>S/4 HANA OP-Version</strong></td>\r\n<td><strong>DMIS Version</strong></td>\r\n</tr>\r\n<tr>\r\n<td>S4CORE 102 FPS00</td>\r\n<td>\r\n<p>DMIS 2011 SP13</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>S4CORE 102 FPS01&#160;and higher</td>\r\n<td>\r\n<p>DMIS 2011 SP14</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The SAP S/4 HANA version must&#160;at least contain the equivalent DMIS coding. As the release cycles are not identical, the SAP S/4HANA codeline might contain&#160;some more&#160;functions compared to DMIS.</p>\r\n<p>Feature and support packages based on the S/4 1709 OP version contain all relevant fixes identical to new DMIS versions.</p>\r\n<p><span style=\"color: #ff0000;\"><span style=\"color: #000000;\"><strong>3.&#160;Relevant SAP Notes for SAP S/4 HANA On-Premise&#160;1709</strong></span></span></p>\r\n<p><span style=\"color: #ff0000;\"><span style=\"color: #000000;\">For an automated&#160;verification&#160;of all required corrections, you can use the Note Analyzer available via note number <a target=\"_blank\" href=\"/notes/3016862\">3016862</a>.</span></span></p>\r\n<p>The table below gives an overview of all relevant SAP Notes for&#160;SAP S/4 HANA On-Premise&#160;1709 in case of the <strong>Migration Cockpit Scenario</strong>:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 535px; width: 1464px;\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Note</strong></td>\r\n<td><strong>Valid From</strong></td>\r\n<td><strong>Valid To&#160;</strong></td>\r\n<td><strong>Note Title</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2502259</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>S/4HANA 1709 FPS00 - Migration - Error during update of migration content</td>\r\n</tr>\r\n<tr>\r\n<td>2509305</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>S/4HANA 1709 FPS00 - Migration - Activities are not displayed in LTMOM</td>\r\n</tr>\r\n<tr>\r\n<td>2509954</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>S/4HANA&#160;1709 FPS00&#160;- Migration - Add missing export/import of control parameters</td>\r\n</tr>\r\n<tr>\r\n<td>2511939</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>S/4HANA 1709 FPS00 - Migration - Activities are not displayed in LTMOM (see also note 2509305)</td>\r\n</tr>\r\n<tr>\r\n<td>2514124</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>S/4HANA 1709 FPS00 - Migration - Optimized delta sheet handling for MATERIAL object</td>\r\n</tr>\r\n<tr>\r\n<td>2541187</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>S/4HANA 1709 FPS00 - Migration&#160;- Error during Generation user-defined object</td>\r\n</tr>\r\n<tr>\r\n<td>2546026</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 - Migration - After copying a Migration Object in the Migration Object Modeler, the Migration Object cannot be used in the Migration Cockpit</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2574267</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 - Migration - After copying a Migration Object in the Migration Object Modeler, the Migration Object cannot be used in the Migration Cockpit correctly</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2556148</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 - Migration - Reading the correct DMIS or S/4 version</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2576026</td>\r\n<td>FPS00</td>\r\n<td>FPS01</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 / FPS01 - Migration - Migration Object Modeler (LTMOM): Generating Runtime objects fails when You Log in with Language Other Than English</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2596532</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 - Migration - Delta file cannot be downloaded</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2595089</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 - Migration - After copying a Migration Object in the Migration Object Modeler, the Migration Object cannot be processed correctly</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2600293</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 - Migration - Wrong error message when duplicate record exists in file</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2581863</td>\r\n<td>FPS00</td>\r\n<td>FPS01</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 / FPS01 - Migration - Data Transfer Issues for Files with Many Empty Fields or Maximum Application Logs Reached</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2587257</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 - Migration - Correction for Migration Cockpit - Staging Scenario</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2613133</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 - Migration - Incorrect \"Number of Records\" for new user-defined structure</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2617067</td>\r\n<td>FPS00</td>\r\n<td>FPS01</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 / FPS01&#160;- Migration - Corrections for Migration Cockpit-Q2P Scenario</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2575949</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 - Migration - Strange Copy of User-Defined Migration Object appears after Processing a File in LTMC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2628948</td>\r\n<td>FPS00</td>\r\n<td>FPS01</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 / FPS01 &#8211; Migration - Option &#8216;Import Content&#8217; results in an error</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2635232</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 - Migration - Unable to Add field to Source Structure</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2638840</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 - Migration - &#8216;Simulate Import&#8217; step transfers data to the target system</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2643746</td>\r\n<td>FPS02</td>\r\n<td>FPS02</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS02 - Migration - in German language, error when download the file template and upload file</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2668224</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>S/4HANA 1709 FPS01 - Migration - Customer is allowed to open move rule details</td>\r\n</tr>\r\n<tr>\r\n<td>2669829</td>\r\n<td>FPS02</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS02 - Migration - Creating user-defined migration object shows error: Select an active view before you start synchronizing</td>\r\n</tr>\r\n<tr>\r\n<td>2670113</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>S/4HANA 1709 FPS01 - Migration - Error while generating runtime object due converting row to table type</td>\r\n</tr>\r\n<tr>\r\n<td>2657145</td>\r\n<td>FPS00</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS00 / FPS01 / FPS02 - LTMOM - error when creating a user-defined migration</td>\r\n</tr>\r\n<tr>\r\n<td>2675023</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>S/4HANA 1709 FPS01 - Migration - Runtime error UNCAUGHT_EXCEPTION when deleting files</td>\r\n</tr>\r\n<tr>\r\n<td>2680422</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS01 / FPS02 - Runtime generation failed when source table name contains '/' and STRING type field</td>\r\n</tr>\r\n<tr>\r\n<td>2665339</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS01 / FPS02 - Migration - Run an import simulation for a staging migration object</td>\r\n</tr>\r\n<tr>\r\n<td>2675039</td>\r\n<td>FPS02</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS02 - Migration - Corrections for missing data in uploaded file if the number of warning messages exceeds 1000</td>\r\n</tr>\r\n<tr>\r\n<td>2690069</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>S/4HANA 1709 FPS01 - Migration - 500 Internal Server Error occurs when clicking Notifications tab in Migration Project Details page</td>\r\n</tr>\r\n<tr>\r\n<td>2678513</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS01 / FPS02 - Migration - Substructures of complex user-defined migration objects not filled</td>\r\n</tr>\r\n<tr>\r\n<td>2701296</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS01 / FPS02 - Migration - records tracked as successfully transferred but not posted successfully</td>\r\n</tr>\r\n<tr>\r\n<td>2701161</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS01 / FPS02 - Migration - LTMC cannot start, DMC_COBJ SQL error</td>\r\n</tr>\r\n<tr>\r\n<td>2704251</td>\r\n<td>FPS02</td>\r\n<td>SPS03</td>\r\n<td>S/4 HANA 1709 FPS2 &amp; SPS3 Application select is switch on as default for new source structure in MWB and LTMOM</td>\r\n</tr>\r\n<tr>\r\n<td>2708934</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS01 / FPS02 - Migration - Syntax error in runtime object. The definition of structure or table not found</td>\r\n</tr>\r\n<tr>\r\n<td>2711177</td>\r\n<td>FPS02</td>\r\n<td>SPS03</td>\r\n<td>S/4HANA 1709 FPS02 / 1709 SPS03 / 1809 FPS00 - Migration - Support with determining staging table names</td>\r\n</tr>\r\n<tr>\r\n<td>2717386</td>\r\n<td>FPS00</td>\r\n<td>FPS02</td>\r\n<td>SAP S/4 HANA Migration Cockpit - Error when transferring data in to multiple clients</td>\r\n</tr>\r\n<tr>\r\n<td>2722401</td>\r\n<td>FPS01</td>\r\n<td>SPS03</td>\r\n<td>S/4HANA 1709 FPS01/FPS02/SPS03 - Migration - Running an import simulation for a file migration object causes access plan calculation to fail (block size limit exceeded)</td>\r\n</tr>\r\n<tr>\r\n<td>2724189</td>\r\n<td>FPS02</td>\r\n<td>SPS03</td>\r\n<td>S/4HANA 1709 FPS2 &amp; SPS3 1809 FPS0 - Migration - Running an import simulation for a staging migration object causes access plan calculation to fail (block size limit exceeded)</td>\r\n</tr>\r\n<tr>\r\n<td>2724748</td>\r\n<td>FPS01</td>\r\n<td>SPS03</td>\r\n<td>S/4HANA 1709 FPS01/FPS02/SPS03 - Migration - Source Structure SSTRING length not editable</td>\r\n</tr>\r\n<tr>\r\n<td>2725842</td>\r\n<td>FPS01</td>\r\n<td>SPS03</td>\r\n<td>S/4HANA 1709 FPS01/FPS02/SPS03 - Migration -&#160;Change Maximum number of log messages</td>\r\n</tr>\r\n<tr>\r\n<td>2738946</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>S/4HANA 1709 FPS01/FPS02 - Migration - Substructures of complex user-defined migration objects not filled 2</td>\r\n</tr>\r\n<tr>\r\n<td>2771612</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>S/4HANA 1709 FPS01 - SAP S/4HANA Migration Cockpit- Step &#8220;Validate Data&#8221; Runs for a Long Time</td>\r\n</tr>\r\n<tr>\r\n<td>2776252</td>\r\n<td>FPS00</td>\r\n<td>SPS03</td>\r\n<td>S/4HANA 1709 FPS02 / 1709 SPS03 / 1809 FPS00 - Migration - Error when creating staging project in Migration Cockpit after implementing SAP Note 2711177</td>\r\n</tr>\r\n<tr>\r\n<td>2846353</td>\r\n<td>FPS01</td>\r\n<td>FPS03</td>\r\n<td>S/4HANA 1709 FPS01 - FPS03 - Migration - Syntax Error When Generating Function Modules</td>\r\n</tr>\r\n<tr>\r\n<td>2881168</td>\r\n<td>FPS00</td>\r\n<td>FPS05</td>\r\n<td>S/4HANA 1709 FPS00-FPS05 / 1809 FPS00-FPS03 / 1909 FPS00-FPS01 - Migration - Delete Migration Object not possible (transaction LTMOM)</td>\r\n</tr>\r\n<tr>\r\n<td>3004392</td>\r\n<td>FPS01</td>\r\n<td>FPS07</td>\r\n<td>S/4HANA 1709 FPS01-FPS07 - Migration &#8211; DMC_FM_RESTART records are not deleted after deleting a project</td>\r\n</tr>\r\n<tr>\r\n<td>3092950</td>\r\n<td>FPS01</td>\r\n<td>FPS09</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00-FPS09 - Event-Based Rule Assignment Dialog: Show Not Released Rules as well in Value Help</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>3100922</td>\r\n<td>FPS00</td>\r\n<td>FPS09</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00-FPS09 - Migration Rule displayed incorrectly after saving via PopUp</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>3146573</td>\r\n<td>FPS02</td>\r\n<td>FPS09</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS02-2021 - LTMOM - Migration Object Detail Screen: Dump when number of files is over 999</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>3194887</td>\r\n<td>FPS00</td>\r\n<td>FPS09</td>\r\n<td>\r\n<p>Issue with source structure fields, SAP S/4HANA Migration Cockpit (Staging Tables)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>3196996</td>\r\n<td>FPS02</td>\r\n<td>FPS09</td>\r\n<td>\r\n<p>Error while generating runtime object for copied migration object, SAP S/4HANA Migration Cockpit (Staging Tables)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>3231277</td>\r\n<td>FPS10</td>\r\n<td>FPS10</td>\r\n<td>\r\n<p>Runtime Error in Transaction DMCRULE for Rules That Require Approval, SAP S/4HANA Migration Cockpit (Staging Tables) and SAP Migration Object Modeler</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In addition to the SAP notes above, please also check the central note&#160;2537549 for Migration Object related notes and knowledge base articles.</p>\r\n<p>The table below gives an overview of all relevant SAP Notes for&#160;SAP S/4 HANA On-Premise&#160;1709 in case of the <strong>SAP Landscape&#160;Transformation Replication Server Scenario</strong>:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Note</strong></td>\r\n<td><strong><strong>Valid From</strong></strong></td>\r\n<td><strong><strong>Valid To&#160;</strong></strong></td>\r\n<td><strong>Source System&#160;</strong></td>\r\n<td><strong>Replication Server&#160;</strong></td>\r\n<td><strong>Note Title</strong></td>\r\n</tr>\r\n<tr>\r\n<td>2601863</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>S/4HANA 1709 FPS01 - SLT central system - Syntax Error in generated runtime object for access plan calculation&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>2603146</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 - SLT central system - Correction for cFIN Scenario - Missing updates for AUFK object data</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2652832</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 - SLT central system - correction for note 2603146</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2659251</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 - SLT central system - correction for note&#160;2652832</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2603835</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>S/4HANA 1709 FPS01 - SLT central system - Migration objects are not updated according to latest dictionary changes</td>\r\n</tr>\r\n<tr>\r\n<td>2607182</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>S/4HANA 1709 FPS01 - SLT central system - Syntax error in /1LT/DMCACS module due to IF_DMC_WHITELIST</td>\r\n</tr>\r\n<tr>\r\n<td>2621466</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>SAP S/4HANA 1709 FPS00 - SLT central system &#8211; Flag &#8216;Allow Multiple Usage&#8217; is deselected when creating a configuration</td>\r\n</tr>\r\n<tr>\r\n<td>2623565</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SAP S/4HANA 1709 FPS01 - SLT central system &#8211; Correction for cFIN Scenario &#8211; Database trigger missing for Table AUFK</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2632203</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SAP S/4HANA 1709 FPS00 - SLT sender and central system&#8211; Correction for parallel replication and HANA 2.0</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2635691</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SAP S/4HANA 1709 FPS00 - SLT central system&#8211; Correction GET_NEXT_MT_ENTRY</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2653282</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SAP S/4HANA 1709 FPS00 - SLT central system&#8211; Correction for LTRC Replication statistics</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2653554</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 / FPS02 - SLT central system - Redundant housekeeping jobs</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2666064</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 / FPS02 - SLT source system - Using DataProvisioning option \"Create DB View\" with S/4 as source system</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2688097</td>\r\n<td>FPS00</td>\r\n<td>FPS00</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 - SLT source system - Runtime modules in source system are not remote-enabled</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2693503</td>\r\n<td>FPS00</td>\r\n<td>FPS02</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00 / FPS01 / FPS02 - SLT central system - Replication of tables with APPEND-structures fails</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2698869</td>\r\n<td>FPS01</td>\r\n<td>FPS01</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01 - SLT central system - In the creation process for a configuration, the F4 for &#8220;Application&#8221; fails</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2712382</td>\r\n<td>FPS00</td>\r\n<td>SPS03</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS 00, SPS03 - SLT central system - Syntax error in function module in source system (1 to N scenario)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2712546</td>\r\n<td>FPS00</td>\r\n<td>SPS03</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00-SPS03 - SLT source system - Reading type 1 delimitations not possible</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2741785</td>\r\n<td>FPS01</td>\r\n<td>SPS03</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS 00, SPS03 - SLT central system - Central Finance Scenario: Short Dumps SYNTAX_ERROR, LOAD_TYPE_VERSION_MISMATCH, DDIC_TYPE_INCONSISTENCY</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2752620</td>\r\n<td>FPS01</td>\r\n<td>SPS03</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS 01, SPS03 - SLT central system - Central Finance Business Integration (CFIN_PI) scenario: Data Replication isn't working</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2785499</td>\r\n<td>FPS00</td>\r\n<td>SPS04</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00-SPS04 - SLT central system - System incorrectly sets tables to status 'failed' at midnight</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2813327</td>\r\n<td>FPS00</td>\r\n<td>SPS04</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00-SPS04 - SLT central system - Syntax error when generating runtime objects</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2828803</td>\r\n<td>FPS00</td>\r\n<td>SPS04</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS00-SPS04 - SLT source and central system - Report FIN_CFIN_SLT_ADD_LOGTAB fails</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2848767</td>\r\n<td>FPS00</td>\r\n<td>SPS05</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1610 - 1709 - SLT source and central system - IUUC_REDEFINE_DB_TRIGGERS fails to add IUUC_REPL_GROUP field</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2831035</td>\r\n<td>FPS00</td>\r\n<td>SPS04</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>DMIS 2011 / DMIS 2018 / S/4HANA - Correction for Report DMC_UPLOAD_OBJECT</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2909543</td>\r\n<td>FPS00</td>\r\n<td>SPS06</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT Source &amp; Central System ( DMIS 2011, DMIS 2018, S/4HANA OP) : INDX-like table replication runtime error</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2917428</td>\r\n<td>FPS00</td>\r\n<td>SPS06</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT Source &amp; Central System (DMIS 2011 SP15 and DMIS 2018 SP00, S4HANA OP): High SQL utilization in working with LogTable</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2928028</td>\r\n<td>FPS01</td>\r\n<td>SPS06</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>S/4HANA 1709 FPS01-SPS06 - SLT source and central system - Logon and Text Element Languages DIfferences</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2757514</td>\r\n<td>FPS02&#160;</td>\r\n<td>SPS06&#160;</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT source system - Deactivate Active Active Read</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2951556</td>\r\n<td>FPS01</td>\r\n<td>FPS02</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT Source and Central System (S/4HANA 1709 FPS01-FPS02): failed to redefine DB triggers when switching to parallel replication</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2950224</td>\r\n<td>FPS01</td>\r\n<td>SPS06</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT Central System (DMIS 2011 SP14-SP18 / DMIS 2018 SP00-SP03, S/4HANA 1709-1909): CFIN - records in DMC_FM_RESTART are deleted</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2964653</td>\r\n<td>FPS00</td>\r\n<td>SPS06</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT (DMIS 2011/ DMIS 2018 / S/4HANA) Source &amp; Central System: Function module required for IUUC_REDEFINE_DB_TRIGGERS report</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2752710</td>\r\n<td>SPS03</td>\r\n<td>SPS08</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT (DMIS 2011 / DMIS 2018 / S/4HANA 1709/1809) - Trigger creation fails for source running on SAP HANA due to numbers of triggers</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2731629</td>\r\n<td>FPS02</td>\r\n<td>SPS03</td>\r\n<td>no</td>\r\n<td>yes</td>\r\n<td>\r\n<p>Migration Workbench (DMIS 2011 / DMIS 2018 / S4HANA) - Data selection fails if ranges are defined to split the data</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>3095189</td>\r\n<td>FPS00</td>\r\n<td>SPS08</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT (DMIS 2011 / DMIS 2018 / S/4HANA) Source &amp; Central System: Adjustments for function module IUUC_GENERIC_READ</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>3116266</td>\r\n<td>FPS00</td>\r\n<td>SPS06</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT (DMIS 2011 / DMIS 2018 / S/4HANA )&#160;Source &amp; Central System: Syntax error in runtime object for cFin scenario when using long key field names</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>3159763</td>\r\n<td>FPS01</td>\r\n<td>SPS10</td>\r\n<td>yes</td>\r\n<td>yes</td>\r\n<td>\r\n<p>SLT (S/4HANA) - Missing authority check in DMC_RTO_OL_RFC_SERVER_GET_CODE</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;Special SAP Note which might be relevant after the upgrade: 2375857 - SLT/MWB: Internal error when generating object GLOBAL.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-LT-SLT (SAP Landscape Transformation Replication Server (SLT))"}, {"Key": "Other Components", "Value": "CA-LT-MIG (Migration Workbench (MWB))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D053440)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D038472)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002475034/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002475034/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002475034/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002475034/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002475034/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002475034/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002475034/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002475034/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002475034/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2733253", "RefComponent": "CA-LT-MC", "RefTitle": "FAQ for SAP S/4HANA migration cockpit. Migration approach: Transfer / Migrate data using staging tables", "RefUrl": "/notes/2733253 "}, {"RefNumber": "2665195", "RefComponent": "CA-LT-SLT", "RefTitle": "How to check SLT version on a system? - SAP Landscape Transformation Replication Server", "RefUrl": "/notes/2665195 "}, {"RefNumber": "2609156", "RefComponent": "HAN-DP-LTR", "RefTitle": "Creating configuration missing \"DB connection\" option - SLT", "RefUrl": "/notes/2609156 "}, {"RefNumber": "2580573", "RefComponent": "CA-LT-SLT", "RefTitle": "Can S/4 HANA be used with SLT (as the source or target system) or as the central SLT system? - SAP Landscape Transformation Replication Server", "RefUrl": "/notes/2580573 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}