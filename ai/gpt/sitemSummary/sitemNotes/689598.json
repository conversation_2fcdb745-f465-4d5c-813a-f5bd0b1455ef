{"Request": {"Number": "689598", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 696, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003681742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000689598?language=E&token=56AED01D3862791282ACCF57375A009D"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000689598", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000689598/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "689598"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with High Priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.12.2003"}, "SAPComponentKey": {"_label": "Component", "value": "PY-AT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Austria"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "PY-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "689598 - ELDA Payslip Tax: Field FANRL for Tax Offices 01 - 09"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=689598&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/689598/D\" target=\"_blank\">/notes/689598/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant for you if you are from your tax office or Your payslip consultant has been informed that the Payslip Tax has been rejected as incorrect to the tax offices with the numbers 1 to 9 since January 1, 2003.<br />You have already sent the Payslip Tax for mid-year leavings between January 1, 2003 and November 30, 2003 to the ELDA receiving office.<br />The tax office numbers 1 to 9 must be saved with leading zeros (that is, 01 to 09) in the corresponding field of the ELDA data record.<br />Since the tax office number of the electronic payslips you sent in the course of the year seems to have been checked by the receiving office only in December, this fact has only been communicated to us now.<br />The ELDA payslips to all other tax offices are not affected and are accepted as correct.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Payslip Tax<br />ELDA field FANRL, tax office number of payroll office<br />ELDA Data Record</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Program error<br />For the year 2003, you have organizational units that are assigned to a tax office with the number between 1 and 9.</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><OL>1. Implement the corrections in accordance with the attached correction instructions.</OL> <OL>2. Implement the report RPUL16A0 attached to this note from the attached file relevant for your release. For more information, see SAP Note 480180 (download and implementation of attachments to SAP Notes) and SAP Note 13719.<br />The validity of the attachments is as follows:<br />Attachment L6BK054114.SAR for Release 4.70<br />Attachment L9CK151642.SAR for Release 4.6C<br />Attachment L9BK112499.SAR for Release 4.6B<br />You can use report RPUL16A0 to create correct records in table T5A1O from the data medium records already generated for Payslip Tax with an incorrect value so that you can send them to the GKK again.</OL> <OL>3. First start the report RPUL16A0 in test mode and do not restrict the selection. The system then displays the following lists:</OL> <UL><LI>(a.1) All confirmed data records in the table T5A1Q whose value in the ELDA field FANRL is recognized as incorrect.</LI></UL><UL><LI>(b.1) All data records from generated data mediums in table T5A1O whose value in ELDA field FANRL is recognized as incorrect.</LI></UL> <UL><LI>(c) All data records from data mediums that have already been created and that are generated with the status indicator &#39;K&#39; (corrected) in the table T5A1O in the later production run. These data records to be generated are sorted in ascending order according to the date/time of origin of the original data medium according to table T5A1P. This ensures the correct sequence of data records for the receiving office.</LI></UL> <UL><LI>(d.1) All data records in table T5A1O that have not yet been transferred (without a data medium number) that are to be set correctly during the production run.</LI></UL> <OL>4. Then start the report RPUL16A0 productively.<br />If you want to restrict the data selection, note that you can also use it to change the notification sequence.<br />If you want to change the data selection for data records that have not yet been transferred in the <EM>Current Restrict Number</EM>, only fully entered payslip packages that consist of a leading information record and all subsequent notification records are taken into account.<br />Note that you must still send data mediums that have already been created but not yet sent and that contain other valid data records in addition to the incorrect Tax payslips after the correction.</OL> <OL>5. Then start report RPUELDA0 and generate a data medium for the data records from list (c). Under Selection of Data Records for Selection of &#39;Status Indicator&#39;, enter only &#39;K&#39; (corrected). For the selection of the &quot;Sequence Number&quot;, enter the area specified in the list (c).</OL> <OL>6. For the data records for which no data medium has yet been created, you can later generate the normally scheduled data medium. These data records usually have the status &#39;N&#39; (newly created).</OL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "D030823"}, {"Key": "Processor                                                                                          ", "Value": "D001888"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000689598/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "L6BK054114.SAR", "FileSize": "22", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000304902003&iv_version=0004&iv_guid=00CE797ED5C2A740B85A5B0E67D311BD"}, {"FileName": "L9CK151642.SAR", "FileSize": "22", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000304902003&iv_version=0004&iv_guid=3C1A646E16782049A44D13BF5E5FFFA6"}, {"FileName": "L9BK112499.SAR", "FileSize": "22", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000304902003&iv_version=0004&iv_guid=EE52D0FFEB994944BDF2C063E16C4694"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46B89", "URL": "/supportpackage/SAPKE46B89"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C80", "URL": "/supportpackage/SAPKE46C80"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47025", "URL": "/supportpackage/SAPKE47025"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 1, "URL": "/corrins/0000689598/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=689598&TargetLanguage=EN&Component=PY-AT&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/689598/D\" target=\"_blank\">/notes/689598/D</a>."}}}}