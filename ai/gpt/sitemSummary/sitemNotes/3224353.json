{"Request": {"Number": "3224353", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 206, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000943282022"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003224353?language=E&token=B79A967B47D32B39D87D9892DA0F3745"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003224353", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003224353/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3224353"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.09.2022"}, "SAPComponentKey": {"_label": "Component", "value": "PA-RC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Recruitment"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Personnel Management", "value": "PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Recruitment", "value": "PA-RC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PA-RC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3224353 - S4TWL - SAP Recruiting"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion or upgrade to SAP S/4HANA 2022 or higher and want to activate <strong>SAP Human Capital Management for SAP S/4HANA</strong> edition (in the following referred to as SAP HCM for SAP S/4HANA). The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Recruiting, PA-RC, Compatibility Scope, Compatibility Pack, SAP HCM for SAP S/4HANA, PB4000, PB00, PB10, PB20, PB30, PB40, PB50</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>SAP Recruiting (PA-RC) functionality is only available in SAP ERP HCM and SAP HCM in the Compatibility Pack in SAP S/4HANA, which comes with limited usage rights.</p>\r\n<div class=\"OutlineElement Ltr SCXW35825739 BCX9\">\r\n<p class=\"Paragraph SCXW35825739 BCX9\">For more details on the Compatibility Pack and its expiry date and links to further information please refer to SAP Note <a target=\"_blank\" class=\"Hyperlink SCXW35825739 BCX9\" href=\"/notes/2269324\" rel=\"noreferrer noopener\">2269324</a>. In the Compatibility Scope Matrix document attached to SAP Note <a target=\"_blank\" href=\"/notes/2269324\">2269324</a>, this topic is listed under the Compatibility Scope Matrix Item ID 421.</p>\r\n<div class=\"OutlineElement Ltr SCXW18121735 BCX9\">\r\n<p class=\"Paragraph SCXW18121735 BCX9\"><strong>Business Process related information&#x00A0;</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW18121735 BCX9\">\r\n<p class=\"Paragraph SCXW18121735 BCX9\">After the activation of SAP HCM for SAP S/4HANA it will technically not be possible to use the SAP Recruiting functionality anymore. See SAP Note <a target=\"_blank\" href=\"/notes/3091160\">3091160</a>.</p>\r\n</div>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"OutlineElement Ltr SCXW30835820 BCX9\">\r\n<p class=\"Paragraph SCXW30835820 BCX9\">Before the conversion or upgrade to SAP HCM for SAP S/4HANA 2022 or higher, check whether the functionality is used or not. If not, there are no further actions. If the functionality is used and still required, a similar function is provided with SAP E-Recruiting in SAP HCM for SAP S/4HANA or SAP SuccessFactors Recruiting Management which needs to be implemented.</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW30835820 BCX9\">\r\n<p class=\"Paragraph SCXW30835820 BCX9\">You also have the choice to stay on SAP ERP HCM or SAP HCM in Compatibility Pack in SAP S/4HANA until its expiry date.</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW30835820 BCX9\">\r\n<p class=\"Paragraph SCXW30835820 BCX9\"><strong>Additional information for customers using RISE with SAP S/4HANA Cloud, private edition:&#x00A0;</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW30835820 BCX9\">\r\n<p class=\"Paragraph SCXW30835820 BCX9\">SAP E-Recruiting is not available for RISE with SAP S/4HANA Cloud, private edition customers because this is not part of the offering. See SAP Notes <a target=\"_blank\" href=\"/notes/3091160\">3091160</a> and <a target=\"_blank\" href=\"/notes/2383888\">2383888</a>.</p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW30835820 BCX9\">\r\n<p class=\"Paragraph SCXW30835820 BCX9\"><strong>How to Determine Relevancy&#x00A0;</strong></p>\r\n</div>\r\n<div class=\"OutlineElement Ltr SCXW30835820 BCX9\">\r\n<p class=\"Paragraph SCXW30835820 BCX9\">This simplification item is relevant if you have entries in the table PB4000 or transactions starting with PB* are executed. That indicates that you are using SAP Recruiting (PA-RC). If you already use SAP E-Recruiting or an alternative solution such as SAP SuccessFactors Recruiting, this simplification item is not relevant.</p>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D059616)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D059616)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003224353/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003224353/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003224353/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003224353/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003224353/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003224353/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003224353/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003224353/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003224353/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}