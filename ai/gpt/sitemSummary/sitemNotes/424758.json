{"Request": {"Number": "424758", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 492, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015048582017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000424758?language=E&token=D6B05D09C49ECFD09869734B4256BE6D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000424758", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000424758/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "424758"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 40}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.06.2002"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-TEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "In Case of LiveCache Problems: Please use SCM-APO-LCA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "In Case of LiveCache Problems: Please use SCM-APO-LCA", "value": "SCM-TEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-TEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "424758 - APO Support Package 15 for APO Release 3.0A"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes how to import APO Support Package 15 (SAPKY30A15) for APO Release 3.0A into your system.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>APO Support Package, APO patch, APO Release 3.0A, COM routines, liveCache, optimizer, Support Release, Support Package, release strategy<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>1. IMPORTANT: Information about Support Package 15</b><br /> <p></p> <UL><LI> <B>Release restrictions</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer to note 427957. It contains known release restrictions from Support Release 3 (Support Package 14).<br /></p> <UL><LI> <B>Newly recommended procedure with transaction SPAM</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of version level APO Support Release 2 (SR2 includes APO Support Packages 1 - 8), we recommend that you always import several APO Support Packages (from Support Package 9 to the current Support Package) in a single queue.<br /></p> <UL><LI> <B>Prerequisite for SAP R/3 back end</B></LI></UL> <UL><UL><LI>PlugIn 2001.1 (SP01) or higher</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Also refer to:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 415244 (PlugIn 2001.1)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 449677 (PlugIn 2001.2)<br /></p> <UL><LI> <B>liveCache:</B></LI></UL> <UL><UL><LI>current liveCache for Support Package <B>15 =&gt; 7.2.5 build 7</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 379051 Importing a liveCache version &gt;= 7.2.4 B15</p> <UL><LI> <B>COM routines:</B></LI></UL> <UL><UL><LI>current COM routines for Support <B>Package 15 =&gt; build 23</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 430343 SAPAPO 3.0 COM Object Build 23<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; <B>COM build 23 requires liveCache version 7.2.5 build 7</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 326494 List of SAPAPO COM object builds for APO 3.0</p> <UL><LI> <B>SAP R/3 kernel for Release 4.6D:</B></LI></UL> <UL><UL><LI> <B>If you are using liveCache &gt;= 7.2.5 build 4, you will need patch level &gt;= 579</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 406248 liveCache connect problems</p> <UL><LI> <B>Optimizer:</B></LI></UL> <UL><UL><LI>Support Package 15 contains new versions for the following optimizers:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CTM, PP/DS, SNP, ND and VSR.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 428600 APO 3.0 Optimizer Support Package 15<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 300930 APO 3.0 Importing an optimizer version</p> <UL><LI> <B>Special features for APO Support Package &lt;= 6</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If APO Support Package <B>version 6 and lower is installed</B> in your APO system, read and follow the following notes:</p> <UL><UL><LI>=&gt; Note: 314218 Transaction data conversion between Support Packages</LI></UL></UL> <UL><UL><LI>=&gt; Note: 361635 liveCache upgrade APO 3.0A SP7</LI></UL></UL> <UL><LI> <B>Notes that must be implemented manually for APO Support Package 15</B></LI></UL> <UL><UL><LI>Note no.: 441950 Planning version copy ignores non-working hours</LI></UL></UL> <UL><UL><LI>Note no.: 442090 Short dump during the planning with conti-IO</LI></UL></UL> <UL><UL><LI>Note no.: 434010 Errors with the adding operation in the process order</LI></UL></UL> <UL><UL><LI>Note no.: 442747 Version deletion does not work for confirmed orders</LI></UL></UL> <UL><LI> <B>Refer to the following notes and implement them if they are relevant for your system:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Number Component Priority Validity</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0429213 APO-SNP Supply Network Planning (SNP) high SP15/16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0432113 APO-FCS Demand Planning high SP15<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0429213 APO-SNP Supply Network Planning (SNP) high SP15/16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0436372 APO-SNP-OPT Optimization of the SNP plan high SP15/16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0441950 APO-MD-VM Version Management high SP15/16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0451758 APO-SNP Supply Network Planning (SNP) high SP15-SP17<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0452505 APO-FCS Demand Planning high SP15-SP17<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;505308 BC-DB-LCA high SP01 - SP19<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;511867 APO-MD-LO high SP01 - SP05<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;515120 APO-FCS-EXTR high SP01 - SP20<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0415780 APO-INT-STK medium SP15<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0426782 APO-FCS-BF Basic Functions medium SP15<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0441087 APO-INT-PUR Purchasing/PReq, Order medium SP15/16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0447217 APO-COM COM objects in the liveCache low SP15-SP17<br /></p> <b>2. IMPORTING THE COMPONENTS FOR SUPPORT PACKAGE 15</b><br /> <p></p> <UL><LI> <B>Support Package</B></LI></UL> <UL><UL><LI> <B>SPAM update</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before you start to import the Support Packages, you need to update the SPAM manager to the latest version.Download the latest version of the SPAM update from the OSS/SAPNET.For more information about this, go to the initial screen of transaction SPAM and select the 'i' button (Online documentation:Help -&gt; Application help).</p> <UL><UL><LI> <B>Import the BASIS, ABA, BW and APO components</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before you begin to import these components with the SPAM transaction, we recommend that:<br />1. no system activities are running or occurring in parallel<br />2. no more background jobs are running<br />3. a checkpoint has been written with the '/SAPAPO/OM_CHECKPOINT_WRITE' program<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>You should always have the latest version of the components.</B>In this way, you ensure that you can <B>always use</B> the latest corrections (such as performance enhancements, for example). For the current versions of the components, see the download areas specified in point 3.2.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import these Support Package components separately in the following sequence:<br /><B>BASIS 23 (SAPKB46C23) -&gt; ABA (SAPKA46C23) -&gt; BW 17 (SAPKW20B17)</B><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that BW Support <B>Packages cannot be imported in a single queue!</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 484015 Importing BW Support Packages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 328237 Importing Support Packages into a BW 2.0B system</p> <UL><LI> <B>Binary Patches</B></LI></UL> <UL><UL><LI> <B>COM routines - liveCache</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you always import the latest COM <B>object with the liveCache build that</B> is currently released for it.In this way, you ensure that you can always use the latest corrections (such as performance enhancements, for example).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 157265 Exchanging COM objects for liveCache in APO 3.0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 326494 List of SAPAPO COM object builds for APO 3.0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 379051 Importing a liveCache version &gt;= 7.2.4 B15<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer to the following note after a successful liveCache update<br />=&gt; Note: 424886 parameter values as of liveCache version 7.2.5<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> Note also that a new liveCache version may require a suitable 'DBADASLIB' library!</B><br />=&gt; Note: 325402 dbadaslib: How do I install a patch?<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additional sources of information for the UNIX liveCache and COM routines are available in the following:<br />=&gt; Note: 391746 COM routines and liveCache versions on UNIX<br /></p> <UL><UL><LI> <B>SAP kernel, optimizer and front end</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you always import the latest 4.6D kernel, optimizer version and front end.This ensures that you can always use the latest corrections (such as performance enhancements, for example).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 300930 APO 3.0 Importing an optimizer version<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 422446 APO front end patch<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> Note also that a new R/3 kernel (disp+work) may require a suitable 'DBADASLIB' library</B><br />=&gt; Note: 325402 dbadaslib: How do I install a patch?</p> <UL><LI> <B>Actions after importing a Support Package</B></LI></UL> <UL><UL><LI> <B>Generating modified objects</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you execute <B>transaction</B> SGEN after you import Support Packages.If you import several Support Packages directly one after the other, you must apply the procedure only once to the last Support Package.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1. Call transaction SGEN.<br />2. Select the 'Regeneration of existing Loads' option.<br />3. Select the 'Generate objects with invalid load only' option.<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This procedure ensures that the objects changed by the Support Package are regenerated.Read the documentation on the initial screen of transaction SGEN.<br /><br /></p> <b>3. GENERAL INFORMATION</b><br /> <p></p> <b>3.1 General information on APO Support Packages and initial releases</b><br /> <UL><LI> <B>Importing sequence</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you import the Support Package components in the following sequence: <B>BASIS -&gt; ABA -&gt; BW -&gt; APO</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Caution :</B><B> </B><B> Import every APO Support Package in succession.</B></p> <UL><LI> <B>Initial releases</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>APO Support Package 15 (SAPKY30A15) for APO Release 3. 0A requires a complete installation/upgrade (delivery from May 15, 2000).</B><B> </B><B>In addition, you need to import all APO Support Packages from Support Package 1 up to the current version.</B><B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> If the system was set up with Support Release 1 for APO Release 3.0A, you also need to import all Support Packages from Support Package 5 up to the current version.</B><B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> If the system was set up with Support Release 2 for APO Release 3.0A, you also need to import all Support Packages from Support Package 9 up to the current version.</B><B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Support Release 3 contains Support Packages 1 to 14. Delivery is scheduled for calendar week 37.</B><B> </B></p> <UL><LI> <B>Languages supported for APO Release 3.0</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;German, English, French, Spanish, Danish, Finnish, Hungarian, Italian, Japanese, Korean, Dutch, Norwegian, Polish, Portuguese, Swedish, Russian, Czech.<br /><br /></p> <b>3.2 Download areas</b><br /> <UL><LI> <B>Service Marketplace:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The new SAP Service Marketplace offers customers a new way of downloading SAP software from a central location.This Service Marketplace supplies all SAP application components for downloading on a continuous basis, thus replacing both the download areas mentioned (sapservX, OSS) and the old SAPNet download area (http://sapnet.sap.de/ocs-download)!<br />You can currently download the components 'BASIS', 'ABA', 'BW', 'APO', 'front end', 'Kernel' and 'Optimizer' via the Service Marketplace at the following address:<br /><B>http://SERVICE.SAP.COM/SWCENTER-MAIN</B><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The <B>Binary patches for updating the 'COM routines' and the 'liveCache'</B> are not yet available on the Service <B>Marketplace.</B><br /></p> <UL><LI> <B>sapservX (Binary patches):</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- <B>COM routines</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ftp://sapservX/specific/apo/apo30/&lt;SPn&gt;/SAPCOM30_&lt;Build&gt;.SAR<br />SPn = Support Package number (for example, SP15)<br />Build = build number of the COM routines (for example, 23)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example:<br />- ftp://sapservX/specific/apo/apo30/sp15/SAPCOM30_23_NT.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>- </B><B>liveCache</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ftp://sapservX/general/3rdparty/sapdb/LC_VERSIONS/&lt;Build&gt;/ SAPDB-SERVER-&lt;OS&gt;-&lt;32/64-Bit&gt;-&lt;PA&gt;-&lt;Build&gt;.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Build = build number of the liveCache (for example, 72.05.07.full)<br />OS = operating system (for example, NT, WIN)<br />PA = processor architecture (for example, i386)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example: SAPDB-SERVER-WIN-32BIT-&lt;i386-7_2_5_7.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>- </B><B>Optimizer</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ftp://sapservX/specific/apo/apo30/sp13/SAPAPO_n.SAR<br />n = number of the Support Package (for example, 15)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example:<br />- ftp://sapservX/specific/apo/apo30/sp15/SAPAPO_15.SAR</p> <UL><LI> <B>Online Service System = OSS (Support Package):</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can download the following components via transaction SPAM in the Online Service System (OSS):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- BASIS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ABA<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- BW<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- APO<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-OCS (Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr))"}, {"Key": "Transaction codes", "Value": "SPAM"}, {"Key": "Transaction codes", "Value": "SP01"}, {"Key": "Transaction codes", "Value": "SGEN"}, {"Key": "Responsible                                                                                         ", "Value": "D000325"}, {"Key": "Processor                                                                                           ", "Value": "D034322"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000424758/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000424758/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000424758/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000424758/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000424758/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000424758/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000424758/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000424758/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000424758/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076"}, {"RefNumber": "452505", "RefComponent": "SCM-APO-FCS", "RefTitle": "Release DP->SNP: local time zones are not correct", "RefUrl": "/notes/452505"}, {"RefNumber": "451758", "RefComponent": "SCM-APO-SNP", "RefTitle": "Calculation of planned delivery time in SNP", "RefUrl": "/notes/451758"}, {"RefNumber": "447217", "RefComponent": "BC-DB-LCA", "RefTitle": "APO migration defective in SP16", "RefUrl": "/notes/447217"}, {"RefNumber": "441950", "RefComponent": "SCM-APO-MD-VM", "RefTitle": "Planning version copy ignores non-working hours", "RefUrl": "/notes/441950"}, {"RefNumber": "441087", "RefComponent": "SCM-APO-INT-PUR", "RefTitle": "Scheduling goods issue processing", "RefUrl": "/notes/441087"}, {"RefNumber": "436372", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Optimizer: Variant 2 of a resource is ignored", "RefUrl": "/notes/436372"}, {"RefNumber": "432113", "RefComponent": "SCM-APO-FCS-EXT", "RefTitle": "Corrections for note '425376'", "RefUrl": "/notes/432113"}, {"RefNumber": "430343", "RefComponent": "BC-DB-LCA", "RefTitle": "SAPAPO 3.0 COM Object Build 23", "RefUrl": "/notes/430343"}, {"RefNumber": "429213", "RefComponent": "SCM-APO-SNP", "RefTitle": "Time zones post SP15", "RefUrl": "/notes/429213"}, {"RefNumber": "427957", "RefComponent": "SCM-APO", "RefTitle": "Release restrictions for APO 3.0A Support Release 3", "RefUrl": "/notes/427957"}, {"RefNumber": "426782", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "TA./SAPAPO/MC62 number of data records displayed", "RefUrl": "/notes/426782"}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886"}, {"RefNumber": "423685", "RefComponent": "BC-DB-LCA", "RefTitle": "SAPAPO 3.0 COM object Build 22", "RefUrl": "/notes/423685"}, {"RefNumber": "421952", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 optimizer Support Package 14", "RefUrl": "/notes/421952"}, {"RefNumber": "421023", "RefComponent": "SCM-APO-VS-BF", "RefTitle": "Transport error in Transaction /sapapo/vscc", "RefUrl": "/notes/421023"}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002"}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248"}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746"}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047"}, {"RefNumber": "361635", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361635"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "353197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/353197"}, {"RefNumber": "352844", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/352844"}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470"}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237"}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402"}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218"}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002 "}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886 "}, {"RefNumber": "452505", "RefComponent": "SCM-APO-FCS", "RefTitle": "Release DP->SNP: local time zones are not correct", "RefUrl": "/notes/452505 "}, {"RefNumber": "447217", "RefComponent": "BC-DB-LCA", "RefTitle": "APO migration defective in SP16", "RefUrl": "/notes/447217 "}, {"RefNumber": "441950", "RefComponent": "SCM-APO-MD-VM", "RefTitle": "Planning version copy ignores non-working hours", "RefUrl": "/notes/441950 "}, {"RefNumber": "441087", "RefComponent": "SCM-APO-INT-PUR", "RefTitle": "Scheduling goods issue processing", "RefUrl": "/notes/441087 "}, {"RefNumber": "436372", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Optimizer: Variant 2 of a resource is ignored", "RefUrl": "/notes/436372 "}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047 "}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248 "}, {"RefNumber": "430343", "RefComponent": "BC-DB-LCA", "RefTitle": "SAPAPO 3.0 COM Object Build 23", "RefUrl": "/notes/430343 "}, {"RefNumber": "423685", "RefComponent": "BC-DB-LCA", "RefTitle": "SAPAPO 3.0 COM object Build 22", "RefUrl": "/notes/423685 "}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237 "}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746 "}, {"RefNumber": "427957", "RefComponent": "SCM-APO", "RefTitle": "Release restrictions for APO 3.0A Support Release 3", "RefUrl": "/notes/427957 "}, {"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076 "}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265 "}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218 "}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218 "}, {"RefNumber": "432113", "RefComponent": "SCM-APO-FCS-EXT", "RefTitle": "Corrections for note '425376'", "RefUrl": "/notes/432113 "}, {"RefNumber": "451758", "RefComponent": "SCM-APO-SNP", "RefTitle": "Calculation of planned delivery time in SNP", "RefUrl": "/notes/451758 "}, {"RefNumber": "426782", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "TA./SAPAPO/MC62 number of data records displayed", "RefUrl": "/notes/426782 "}, {"RefNumber": "429213", "RefComponent": "SCM-APO-SNP", "RefTitle": "Time zones post SP15", "RefUrl": "/notes/429213 "}, {"RefNumber": "421952", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 optimizer Support Package 14", "RefUrl": "/notes/421952 "}, {"RefNumber": "421023", "RefComponent": "SCM-APO-VS-BF", "RefTitle": "Transport error in Transaction /sapapo/vscc", "RefUrl": "/notes/421023 "}, {"RefNumber": "336470", "RefComponent": "BC-DB-SDB", "RefTitle": "Environment var. DBROOT no longer exists", "RefUrl": "/notes/336470 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APO", "From": "30A", "To": "30A", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}