{"Request": {"Number": "1661251", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 633, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009864802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001661251?language=E&token=73635508CA0ADD315EE95739D58ACF06"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001661251", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001661251/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1661251"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.03.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DBA-MD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Master Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Basis", "value": "BW-WHM-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data", "value": "BW-WHM-DBA-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1661251 - SP31:New Master data deletion - Enhancements"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note provides Enhancements to New Master data deletion. There are four parameters introduced:<br /></p> <UL><LI>Store Master data where-used list: If this flag is checked then the master data usage list is stored in the form of table. The name of the table is /BI0/0W'TEMPTABID'. The table ID 'TEMPTABID' can be found in the table 'RSMDUC_TEMPTABNM' for a given Infoobject with STATUS as 'A' OR in the log of new master data deletion. If this parameter is checked then the master data deletion is triggered in simulation mode.</LI></UL> <UL><LI>Reuse all SIDS in master data where-used list: If this flag is checked then the last existing master data where used list for a given infoobject is considered and all the SIDS present whether used or unused are used for the next deletion.</LI></UL> <UL><LI>Reuse only unused SIDS in master data where-used list: If this flag is checked then the last existing master data where used list for a given infoobject is consisdered and only the SIDS which are unused are used for the next deletion</LI></UL> <UL><LI>Reuse only used SIDS in master data where-used list: If this flag is checked then the last existing master data where used list for a given infoobject is consisdered and only the SIDS which are used are used for the next deletion</LI></UL> <p>These parameters can be seen in the popup for master data deletion when you delete master data from RSA1 and also in the Input parameters for the report 'RSDMDD_DELETE_BATCH' ie.</p> <UL><UL><LI>p_store: for storing master data where-used list</LI></UL></UL> <UL><UL><LI>p_reusea: for reusing all SIDS in the master data where-used list.</LI></UL></UL> <UL><UL><LI>p_reuseu: for reusing used SIDS in the master data where-used list.</LI></UL></UL> <UL><UL><LI>p_reusen: for reusing unused SIDS in the master data where-used list.</LI></UL></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Enhancement</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><LI>SAP NetWeaver BW 7.00<br /><br />Import Support Package 31 for SAP NetWeaver BW 7.00 (SAPKW70031) into your BW system. The Support Package will be available as soon as SAP Note 1782745 with the short text \"SAPBWNews NW BW 7.0 ABAP SP31\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.01 (SAP NW BW7.0 EHP 1)<br /><br />Import Support Package 14 for SAP NetWeaver BW 7.01 (SAPKW70114) into your BW system. The Support Package will be available as soon as SAP Note 1794836 with the short text \"SAPBINews NW7.01 BW ABAP SP14\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.02 (SAP NW BW7.0 EHP 2)<br /><br />Import Support Package 14 for SAP NetWeaver BW 7.02 (SAPKW70214) into your BW system. The Support Package will be available as soon as SAP Note 1800952 with the short text \"Preliminary Version SAPBWNews NW BW 7.02 ABAP SP14\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.30<br /><br />Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010) into your BW system. The Support Package will be available as soon as SAP Note 1810084 with the short text \"SAPBWNews NW7.30 BW ABAP SP10\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.31 (SAP NW BW 7.3 EnhP 1)<br /><br />Import Support Package 9 for SAP NetWeaver BW 7.31 (SAPKW73109) into your BW system. The Support Package will be available as soon as SAP Note 1847231 with the short text \"Preliminary Version SAPBWNews NW BW 7.31/7.03 ABAP SP9\", which describes this Support Package in more detail, is released for customers.</LI></UL> <UL><LI>SAP NetWeaver BW 7.40<br /><br />Import Support Package 3 for SAP NetWeaver BW 7.40 (SAPKW74003) into your BW system. The Support Package will be available as soon as SAP Note 1818593 with the short text \"Preliminary Version SAPBWNews NW BW 7.4 ABAP SP03\", which describes this Support Package in more detail, is released for customers.</LI></UL> <p><br /><br />In urgent cases you can use the correction instructions.<br /><br />Before you use the correction instructions, make sure that you check SAP Note 875986 for transaction SNOTE.<br /><br />This SAP Note might already be available before the Support Package is released. In this case, however, the short text still contains the term \"preliminary version\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I044837)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I044837)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001661251/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001661251/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001661251/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001661251/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001661251/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001661251/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001661251/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001661251/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001661251/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1818593", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.40 ABAP SP 03", "RefUrl": "/notes/1818593"}, {"RefNumber": "1813987", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 08", "RefUrl": "/notes/1813987"}, {"RefNumber": "1810084", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 10", "RefUrl": "/notes/1810084"}, {"RefNumber": "1800952", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 14", "RefUrl": "/notes/1800952"}, {"RefNumber": "1794836", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 14", "RefUrl": "/notes/1794836"}, {"RefNumber": "1782745", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 31", "RefUrl": "/notes/1782745"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1800952", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 14", "RefUrl": "/notes/1800952 "}, {"RefNumber": "1810084", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 10", "RefUrl": "/notes/1810084 "}, {"RefNumber": "1794836", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 14", "RefUrl": "/notes/1794836 "}, {"RefNumber": "1813987", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 08", "RefUrl": "/notes/1813987 "}, {"RefNumber": "1782745", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 31", "RefUrl": "/notes/1782745 "}, {"RefNumber": "1818593", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.40 ABAP SP 03", "RefUrl": "/notes/1818593 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "711", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "701", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "711", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70031", "URL": "/supportpackage/SAPKW70031"}, {"SoftwareComponentVersion": "SAP_BW 701", "SupportPackage": "SAPKW70114", "URL": "/supportpackage/SAPKW70114"}, {"SoftwareComponentVersion": "SAP_BW 702", "SupportPackage": "SAPKW70214", "URL": "/supportpackage/SAPKW70214"}, {"SoftwareComponentVersion": "SAP_BW 730", "SupportPackage": "SAPKW73010", "URL": "/supportpackage/SAPKW73010"}, {"SoftwareComponentVersion": "SAP_BW 731", "SupportPackage": "SAPKW73109", "URL": "/supportpackage/SAPKW73109"}, {"SoftwareComponentVersion": "SAP_BW 740", "SupportPackage": "SAPKW74003", "URL": "/supportpackage/SAPKW74003"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 5, "URL": "/corrins/0001661251/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BW&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Business Inform...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW70025 - SAPKW70030&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW70107 - SAPKW70113&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW70207 - SAPKW70213&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW73101 - SAPKW73107&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 730&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW73001 - SAPKW73009&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 740&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW74001 - SAPKW74002&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>In the table 'RSMDUC_TEMPTABNM' insert the field 'CHABASNM' Type RSCHABASNM after the field 'STATUS'<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 11, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "700", "Number": "1533515 ", "URL": "/notes/1533515 ", "Title": "New MasterData Deletion: Usage check extended to include NLS", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "702", "Number": "1554108 ", "URL": "/notes/1554108 ", "Title": "SP27:Short dump in New MD deletion where used checks", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "702", "Number": "1559843 ", "URL": "/notes/1559843 ", "Title": "SP27:Short dump in one of the processes of new MD deletion", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "711", "Number": "1566941 ", "URL": "/notes/1566941 ", "Title": "SP27:Short dump in new MD deletion during initial check", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "730", "Number": "1594256 ", "URL": "/notes/1594256 ", "Title": "SP28:New MD deletion - Incorrect usage list calculation", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "731", "Number": "1641738 ", "URL": "/notes/1641738 ", "Title": "Problem with new Master Data deletion when used with variant", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "731", "Number": "1760340 ", "URL": "/notes/1760340 ", "Title": "Incorrect index name for temp table during MD deletion", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "740", "Number": "1850326 ", "URL": "/notes/1850326 ", "Title": "SP31:Master Data Deletion from BPC aborts with a short dump", "Component": "EPM-BPC-NW-ADM-DIM"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "701", "ValidTo": "701", "Number": "1533515 ", "URL": "/notes/1533515 ", "Title": "New MasterData Deletion: Usage check extended to include NLS", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "701", "ValidTo": "701", "Number": "1751573 ", "URL": "/notes/1751573 ", "Title": "700SP30:Prevent MasterData Deletion for Technical Infoobject", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "701", "ValidTo": "702", "Number": "1476924 ", "URL": "/notes/1476924 ", "Title": "7.01SP8:Short dump in New master Data deletion", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "702", "ValidTo": "702", "Number": "1533515 ", "URL": "/notes/1533515 ", "Title": "New MasterData Deletion: Usage check extended to include NLS", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "702", "ValidTo": "702", "Number": "1751573 ", "URL": "/notes/1751573 ", "Title": "700SP30:Prevent MasterData Deletion for Technical Infoobject", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "702", "ValidTo": "702", "Number": "1860034 ", "URL": "/notes/1860034 ", "Title": "Extension to the RSNDI_MD_DELETE interface : Count of usages", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "711", "ValidTo": "730", "Number": "1559843 ", "URL": "/notes/1559843 ", "Title": "SP27:Short dump in one of the processes of new MD deletion", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1533515 ", "URL": "/notes/1533515 ", "Title": "New MasterData Deletion: Usage check extended to include NLS", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1554108 ", "URL": "/notes/1554108 ", "Title": "SP27:Short dump in New MD deletion where used checks", "Component": "BW-WHM-DBA-MD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1566941 ", "URL": "/notes/1566941 ", "Title": "SP27:Short dump in new MD deletion during initial check", "Component": "BW-WHM-DBA-MD"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}