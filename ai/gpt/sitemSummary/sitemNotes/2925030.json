{"Request": {"Number": "2925030", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 290, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001186032020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002925030?language=E&token=729BBBBCE8F0766A829CF186C5E28372"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002925030", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002925030/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2925030"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "CA-MDG-APP-CUS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Customer (Central Parts)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data Governance", "value": "CA-MDG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-MDG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Applications", "value": "CA-MDG-APP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-MDG-APP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Customer (Central Parts)", "value": "CA-MDG-APP-CUS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-MDG-APP-CUS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2925030 - Functional restrictions in MDG for Business Partner / Customer / Supplier on SAP S/4HANA 2020"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The following restrictions exist for <strong>Master Data Governance for Business Partner / Customer / Supplier&#160;in&#160;SAP Master Data Governance on SAP S/4HANA 2020.</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Relationships</span></p>\r\n<p>SAP MDG does not support the differentiation type for business partner relationships. All relationship categories using a differentiation type in the related customizing cannot be maintained via SAP MDG. Known relationships categories are \"FI0200\", \"FI0210\", \"FI0300\", \"UKM001\", and \"UKMSB0\". Review the contents of customizing table TBZ9 in your system by selecting relationship categories with attribute \"DFTYP\" greater than zero.</p>\r\n<p><span style=\"text-decoration: underline;\">Replication scenarios using SOAP</span></p>\r\n<p>The SOAP service enables you to create a consolidation process or a change request using data from another source system. When using this scenario and replicating the result back to the source system, ensure that the data model in MDG&#160;is aligned with the data model used in the source system. Otherwise, this could lead to data loss when replicating back the result. You can check the data model for consolidation using transaction code&#160;<em>MDCMODEL </em>and for central governance using report<em>&#160;USMD_DISPLAY_DATA_MODEL</em>.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Central Governance of Master Data</strong></span></p>\r\n<p><span style=\"text-decoration: underline;\">Addresses</span></p>\r\n<p>The usage and validation of regional structure data (especially the city and street files) is not supported. The F4 helps for cities or streets are not displayed, even if the city or street files are loaded in your SAP MDG systems.</p>\r\n<p><span style=\"text-decoration: underline;\">Cleansing Cases</span></p>\r\n<p>If you have activated a business function related to Contract Accounts Receivable and Payable (FI-CA), creating a Cleansing Case for Customer, Supplier, or Business Partner is not supported.<br />See also <a target=\"_blank\" href=\"https://help.sap.com/viewer/e52c8ee6197147ec97dfc2eb8c46a3ad/latest/en-US/98e7c5536a51204be10000000a174cb4.html\">SAP Help</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">Client Maintenance</span></p>\r\n<p>Business function MDG_CUST_ERPCLIENT_1 is obsolete and cannot be activated. The features, included in&#160;transaction XD01 or XD02,&#160;cannot be supported after moving an ERP system to S/4HANA because customers are maintained there by Business Partner (transaction BP) or other FIORI apps. (Business Impact Note:<a target=\"_blank\" class=\"sapMLnk sapMLnkMaxWidth\" href=\"/notes/2265093\" id=\"__link0\" tabindex=\"0\">2265093</a>)</p>\r\n<p><span style=\"text-decoration: underline;\">Data Model Enhancements</span></p>\r\n<p>Due to restrictions of the central business partner functionality, it is not always possible to enhance existing entities of data model BP. Review SAP note 2984675 before you start the implementation of your enhancement.</p>\r\n<p><span style=\"text-decoration: underline;\">Field Properties of Standard Address and Standard Communication Data at Start-up</span></p>\r\n<p>If you create a new business partner in the single object maintenance user interfaces, the UIBB for Business Partner Details contains open input fields for the standard address and its standard communication data. The field properties of these attributes are determined according to the standard business partner grouping for internal numbering.</p>\r\n<p>If you change the grouping, the new field status of the attributes cannot be displayed in the user interface. This is caused by a functional restriction in the BOL/genIL component that prevents the update of field properties for fields not having fully entered keys.</p>\r\n<p>The MDG framework itself uses the correct field properties (e.g., if an email address is set to mandatory, a round-trip will execute the corresponding check).</p>\r\n<p>As soon as you enter valid values and trigger a UI roundtrip (e.g., by pushing ENTER or the CHECK button), the field property is displayed correctly, too.</p>\r\n<p><span style=\"text-decoration: underline;\">File Download and File Upload</span></p>\r\n<p>The file download and file upload are restricted for the following entity types:</p>\r\n<ul>\r\n<li>BP_CUSCLA - Customer: Class Assignment</li>\r\n<li>BP_CUSDDB - Customer: Basic Data for Document Link</li>\r\n<li>BP_CUSVAL - Customer: Characteristic Valuation</li>\r\n<li>BP_VENCLA - Supplier: Class Assignment</li>\r\n<li>BP_VENDDB - Supplier: Basic Data for Document Link</li>\r\n<li>BP_VENVAL - Supplier: Characteristic Valuation</li>\r\n<li>FS_BP001 - Common Data (Financial Services)</li>\r\n<li>FKKVK - Contract&#160;Account:&#160;Header</li>\r\n<li>FKKVKTD - Contract&#160;Account:&#160;Header&#160;(Time&#160;Dependence)</li>\r\n<li>FKKTXT - Contract&#160;Account:&#160;Texts</li>\r\n<li>FKKTAXEX - Contract&#160;Account:&#160;Tax&#160;Exemption</li>\r\n<li>FKKVKP - Contract&#160;Account:&#160;Partner-specific&#160;Data</li>\r\n<li>FKKVKCORR - Contract&#160;Account:&#160;Correspondence</li>\r\n<li>FKKLOCKS - Contract&#160;Account:&#160;Business&#160;Locks</li>\r\n<li>FKKCHGDIS - Contract&#160;Account:&#160;Charges&#160;and&#160;Discounts</li>\r\n</ul>\r\n<p>The filters are implemented by classes CL_MDG_BS_BP_FILE_DOWNLOAD and CL_MDG_BS_BP_FILE_UPLOAD.&#160;Do not change the SAP defined implementation for those entity types. Adding these entity types can cause severe issues in your SAP MDG, Central Governance system.</p>\r\n<p><span style=\"text-decoration: underline;\">FIORI Approval App</span></p>\r\n<p>New fields added to data model BP are not considered in the FIORI Approval App for Customer and Supplier.</p>\r\n<p><span style=\"text-decoration: underline;\">IDoc based Replication of deleted Data</span></p>\r\n<p>SAP always uses the \"current state\" of master data. This causes an issue with IDoc based replication if the last data segment was deleted. Examples are the deletion of the last phone number, classification, etc. In this case, the deleted data cannot be sent. The deletion needs to be done manually in the receiving system, too. Alternatively use the Business Partner Web Services for data replication which fully support deletions.</p>\r\n<p><span style=\"text-decoration: underline;\">Lean Classification</span></p>\r\n<p>Starting with MDG 9.1 Lean Classification for ERP Customers and ERP Suppliers is supported in MDG-C and MDG-S.</p>\r\n<p>However, not all generic classification features are supported. Review SAP note 2479869 for further information about the Lean Classification.</p>\r\n<p><span style=\"text-decoration: underline;\">Longtexts</span></p>\r\n<p>Starting with MDG 9.0 it is possible to maintain longtexts for ERP Customers (on general level, company code level, and sales area level) and ERP Suppliers (on general level, company code level, and purchasing organization level). MDG does not support the display or maintenance of formatted text, i.e., in the MDG UI you can maintain only plain text (ASCII). If you maintain a formatted long text, it will be unformatted to plain ASCII text. This applies to all the longtexts as mentioned before.</p>\r\n<p><span style=\"text-decoration: underline;\">MDG-BP/C/S on SAP S/4HANA Single Tenant Edition</span></p>\r\n<p>Refer to guide<strong>&#160;<a target=\"_blank\" href=\"https://support.sap.com/content/dam/SAAP/SAP_Activate/S4H_019%20Guidance%20for%20Extensibility%20in%20SAP%20S4HANA%20Cloud%20STE.pdf\">Guidance for Extensibility in SAP S/4 HANA Cloud, single tenant edition</a>&#160;</strong>and blog&#160;<strong><a target=\"_blank\" href=\"https://blogs.sap.com/2019/03/19/extensibility-checklist-for-sap-s4hana-cloud-single-tenant-edition/\">Extensibility Checklist for SAP S/4HANA Cloud, single tenant edition</a>.&#160;</strong>These documents explain that&#160;SAP note<strong>&#160;<strong><a target=\"_blank\" href=\"/notes/2308424\">2308424&#160;MDG-BP/C/S: Implementing Custom Validations</a>&#160;</strong></strong>cannot be used.</p>\r\n<p><span style=\"text-decoration: underline;\">Partial Birthday</span></p>\r\n<p>New field birth date status added in S/4HANA 2020 is not replicated using SOA-service or IDOC with S/4HANA 2020 FSP00. The restriction is solved when using SOA-service with&#160;S/4HANA 2020 FSP01.</p>\r\n<p><span style=\"text-decoration: underline;\">Relationships</span></p>\r\n<p>A business partner relationship always consists of two business partners and a corresponding relationship between these business partners. The re-use area functionality of the central business partner requires processing the business partners before the relationship. To fulfill this requirement, a single SAP MDG, Central Governance Change Request, that maintains a relationship, must always contain the complete data consisting of both business partners and the relationship. Parallel processing is not possible.</p>\r\n<p>The customizing of relationship categories defines if a relationship</p>\r\n<ul>\r\n<li>is directional, meaning that partner 1 and partner 2 are fixed positions</li>\r\n<li>supports setting a standard flag</li>\r\n</ul>\r\n<p>An example for a directional relationship category that supports the standard flag is the SAP defined contact person relationship. Partner 2 of this relationship is always the contact person.</p>\r\n<p>Any relationship category that is customized as above results in a restriction of the standard indicator maintenance on the single maintenance UIs. The standard flag can only be set if the user interface is started with business partner 1. It cannot be maintained if the UI is started with partner 2 (e.g. Contact Person), because the relationship list for partner 2 does not display all relationships of partner 1.</p>\r\n<p>Transaction BP supports the maintenance of address related contact person attributes (e.g. department name, function name) without the assignment of a workplace address. This functionality is not supported by SAP MDG, Central Governance. It always requires the assignment of a workplace address first before you can maintain these attributes. Existing data that is stored in table BUT051 might be deleted when using SAP MDG, Central Governance.</p>\r\n<p><span style=\"text-decoration: underline;\">SAP Data Enrichment Service</span></p>\r\n<p>As of Q1 of 2021, the SAP Data Enrichment service is not available for purchase. Existing customers can continue to use SAP Data Enrichment until the end of their license term.</p>\r\n<p><span style=\"text-decoration: underline;\">Tax Numbers</span></p>\r\n<p>The highlight changes functionality cannot support the tooltips (prompts) for changed values correctly. Instead of displaying \"The old value 12345 was changed to the new value 98765\" the tooltip usually shows \"Empty value was changed to new value 98765\". This is caused by the different storage of tax numbers in different fields (TAXNUMBER and TAXNUMBERXL) which is not reflected by the list UIBB for tax numbers in the single maintenance UI. The UI supports a single field only which merges the values of the different fields.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Consolidation and Mass Processing of Master Data</strong></span></p>\r\n<p><span style=\"text-decoration: underline;\">Activation using MDG Change Requests</span></p>\r\n<p>In the activation step for Consolidation or Mass processes you can select the option to create a MDG Change Request instead of activating the data directly.</p>\r\n<p>When choosing a MDG Change Request, consider the differences in the data model of Consolidation and Mass processing vs. the data model BP of MDG Central Governance. Tables and fields that are not part of the data model BP in MDG Central Governance cannot be activated using a change request. The Governance Scope functionality of Central Governance might also exclude fields. Fields that are known by Central Governance but unknown in Consolidation and Mass Processing are considered as \"to be deleted\" due to the \"current state\" approach of the functionality.</p>\r\n<p>If you enhance your data models, ensure that both data models for Central Governance and Consolidation and Mass Processing are synchronized.</p>\r\n<p><span style=\"text-decoration: underline;\">Business Partner Relationships</span></p>\r\n<p>The functional restrictions of previous releases are partially resolved. Still, you cannot use BP relationships in Consolidation processes for Active Data.</p>\r\n<p><span style=\"text-decoration: underline;\">Changed Field Values</span></p>\r\n<p>The FIORI applications for consolidation and mass processing usually show changes on field values. This functionality is disabled for performance reasons if more than 100000 records are to be processed.</p>\r\n<p><span style=\"text-decoration: underline;\">Data Model Enhancements</span></p>\r\n<p>Due to restrictions of the central business partner functionality, it is not always possible to enhance existing tables of data model 147 Business Partner. Review SAP note 2984675 before you start the implementation of your enhancement.</p>\r\n<p><span style=\"text-decoration: underline;\">IDoc based Replication of deleted Data</span></p>\r\n<p>SAP always uses the \"current state\" of master data. This causes an issue with IDoc based replication if the last data segment was deleted. Examples are the deletion of the last phone number, classification, etc. In this case, the deleted data cannot be sent. The deletion needs to be done manually in the receiving system, too. Alternatively use the Business Partner Web Services for data replication which fully support deletions.</p>\r\n<p><span style=\"text-decoration: underline;\">Lean Classification</span></p>\r\n<p>Starting with MDG 9.1 Lean Classification for ERP Customers and ERP Suppliers is supported in MDG-C and MDG-S.</p>\r\n<p>However, not all generic classification features are supported. Review SAP note 2479869 for further information about the Lean Classification.</p>\r\n<p><span style=\"text-decoration: underline;\">Longtexts</span></p>\r\n<p>Starting with MDG 9.0 it is possible to maintain long texts for ERP Customers (on general level, company code level, and sales area level) and ERP Suppliers (on general level, company code level, and purchasing organization level). This data is not supported in the Consolidation process and cannot be changed via Mass Processing.</p>\r\n<p><span style=\"text-decoration: underline;\">Payment Cards</span></p>\r\n<p>Starting with MDG 9.1 it is possible to maintain payment cards for ERP Customers. Payment Cards for Customers are not supported in Consolidation and Mass Processing.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Master Data Governance, Central Governance</p>\r\n<p>MDGBP, MDG-BP, MDGC, MDG-C, MDGS, MDG-S</p>\r\n<p>SAP MDG for Business Partners</p>\r\n<p>SAP MDG for Customers</p>\r\n<p>SAP MDG for Suppliers</p>\r\n<p>SAP Master Data Governance, Consolidation &amp; Mass Processing</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>n/a</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>n/a</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-MDG-APP-BP (Business Partner (Central Parts))"}, {"Key": "Other Components", "Value": "CA-MDG-CMP-BP (Business Partner)"}, {"Key": "Other Components", "Value": "CA-MDG-APP-SUP (Supplier (Central Parts))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D020450)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D037604)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002925030/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002925030/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002925030/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002925030/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002925030/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002925030/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002925030/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002925030/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002925030/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2984675", "RefComponent": "AP-MD-BP", "RefTitle": "Business Partner external interface structures cannot be enhanced", "RefUrl": "/notes/2984675"}, {"RefNumber": "2950412", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2950412"}, {"RefNumber": "2943206", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Restriction Note", "RefUrl": "/notes/2943206"}, {"RefNumber": "2876690", "RefComponent": "LO-MD-BP", "RefTitle": "Multiple Business Partners for the same legal entity", "RefUrl": "/notes/2876690"}, {"RefNumber": "2479869", "RefComponent": "CA-MDG-APP-CLF", "RefTitle": "Usage of Lean Classification with SAP Master Data Governance", "RefUrl": "/notes/2479869"}, {"RefNumber": "2265093", "RefComponent": "LO-MD-BP", "RefTitle": "S4TWL - Business Partner Approach", "RefUrl": "/notes/2265093"}, {"RefNumber": "2221398", "RefComponent": "CA-MDG-APP-BP", "RefTitle": "MDG-BP/C/S/CA: (Un-)Supported Fields in Data Model BP", "RefUrl": "/notes/2221398"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3424825", "RefComponent": "CA-MDG-APP-BP", "RefTitle": "MDG-BP/C/S: Issue with the Exclusion of Entity Types from File Download and Upload", "RefUrl": "/notes/3424825 "}, {"RefNumber": "3397356", "RefComponent": "CA-MDG-APP-BP", "RefTitle": "MDG-BP/C/S: Upgrade and Conversion to SAP S/4HANA 2020", "RefUrl": "/notes/3397356 "}, {"RefNumber": "2943206", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2020: Restriction Note", "RefUrl": "/notes/2943206 "}, {"RefNumber": "2847807", "RefComponent": "CA-MDG-APP-BP", "RefTitle": "MDG-BP/C/S/CA: Usage of MDG Tools and Processes", "RefUrl": "/notes/2847807 "}, {"RefNumber": "2882157", "RefComponent": "CA-MDG", "RefTitle": "SAP S/4HANA Master Data Governance 2020: Release Information Note", "RefUrl": "/notes/2882157 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "MDG_APPL", "From": "805", "To": "805", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}