{"Request": {"Number": "1428709", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 248, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016959522017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=4D7B5B366A36FEB078CBDC3B9D0667B9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1428709"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.02.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SDB"}, "SAPComponentKeyText": {"_label": "Component", "value": "MaxDB"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MaxDB", "value": "BC-DB-SDB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SDB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1428709 - Creating ODBC trace as of ODBC driver 7.7 and higher"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />You require an ODBC trace to analyze the communication between SAP MaxDB and the SAP Content Server.<br />For an overview of all trace options, see question 25 in SAP Note <a target=\"_blank\" href=\"/notes/822239\">822239</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ODBC, trace, Content Server, odbc_cons</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The SAP Content Server uses ODBC driver version 7.8 or higher. <br />As of Version 6.50, the SAP Content Server always uses the ODBC driver version &gt;= 7.9. <br /><br />We recommend that you use the most current version of the ODBC driver. Refer to SAP Note <a target=\"_blank\" href=\"/notes/1571193\">1571193</a>. The ODBC driver upgrade is described in SAP Note <a target=\"_blank\" href=\"/notes/698915\">698915</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The ODBC files are managed in a user-specific manner with the command line tool <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;odbc_cons&#xFEFF;</em></span>.</p>\r\n<ol>\r\n<li>First, determine the operating system user with which the Web server (IIS Windows/Apache Tomcat&#x00A0;UNIX) is launched.<br /><br /><strong>UNIX/Linux:<br /></strong>The user name (here <em>nobody</em>) is in the output of the first column of the <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;ps&#xFEFF;</em></span> command.<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&#xFEFF;ps -ef | grep httpd</em></span><br />&#xFEFF;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>nobody&#x00A0;&#x00A0; 6940&#x00A0; 6927&#x00A0; 0 Jul09 ?&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 00:00:01 /usr/local/apache/bin/httpd -k start</em></span>&#xFEFF;<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>nobody&#x00A0;&#x00A0; 6941&#x00A0; 6927&#x00A0; 0 Jul09 ?&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 00:00:01 /usrl/local/apache/bin/httpd -k start</em>&#xFEFF;</span><br />Execute all subsequent <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;odbc_cons&#xFEFF;</em></span> commands as this user. Use the UNIX command <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;su&#xFEFF;</em></span> to switch to this user.<br /><br /><strong>Microsoft Windows<br /></strong>Normally, the IIS is executed as a service and belongs to the user <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;NT AUTHORITY\\SYSTEM&#xFEFF;</em></span>.<br />If you use an Apache Tomcat instead of IIS, use the task manager to determine the user of the &quot;httpd&quot; process.<br />Execute all subsequent <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;odbc_cons&#xFEFF;</em></span> commands with the option <em>&#xFEFF;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">-u &lt;user_name&gt;</span>&#xFEFF;</em> as an administrator.<br /><br /></li>\r\n<li>Check the directory to which the ODBC trace files are written. <br /><br />Execute the following <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;odbc_cons&#xFEFF;</em></span> command at operating system level:<br /><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;odbc_cons show all</span></em><br />The system outputs the directory that is currently being used; for example:&#x00A0;<br />Files are created at E:\\sapdb\\data\\wrk unless absolute paths are specified.<br /><br />Check whether the directory has write authorization for the user and, if necessary, choose another.<br />Check whether the memory space is sufficient in the specified directory and, if necessary, save the old trace files in a different directory. <br />If you want to define a different directory, use the following command (the placeholder %p represents the relevant PID number):<br /><br /><strong>UNIX/Linux:<br /></strong><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;odbc_cons config trace filename &#39;/&lt; path&gt;/odbctrace-%p.prt&#39;</span></em><strong><br /><br />Microsoft Windows<br /></strong>&#xFEFF;<em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;odbc_cons -u &quot;NT AUTHORITY\\SYSTEM&quot; config trace filename &#39;\\&lt;path&gt;\\odbctrace-%p.prt&#39;</span></em><br /><br />The user <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;NT AUTHORITY\\SYSTEM&#xFEFF;</em></span> generally has no write authorization in the default directory. You should therefore preferably use another directory such as <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">C:\\temp\\odbctrace%p.prt&#39;</span>.<br /><br /></li>\r\n<li>Perform the following steps to activate the ODBC trace on the SAP content server with the standard options:<br /><br /><strong>UNIX/Linux:<br /></strong><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;odbc_cons trace sql on</span></em><em><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">odbc_cons trace api on</span></em><br /><strong><br />Microsoft Windows<br /></strong><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;odbc_cons -u &quot;NT AUTHORITY\\SYSTEM&quot; trace sql on</span></em><em><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">odbc_cons -u &quot;NT AUTHORITY\\SYSTEM&quot; trace api on</span><br /><br /></em></li>\r\n<li>After you have reproduced the error situation, deactivate all trace options:<br /><strong><br />UNIX/Linux:<br /></strong><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;odbc_cons trace off</span></em><br /><strong><br />Microsoft Windows<br /></strong><em><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;odbc_cons -u &quot;NT AUTHORITY\\SYSTEM&quot; trace off</span></em><br /><br /></li>\r\n</ol>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SRV-KPR (Knowledge Provider)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D037028)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1619726", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "FAQ: SAP MaxDB Content Server", "RefUrl": "/notes/1619726"}, {"RefNumber": "1405031", "RefComponent": "BC-DB-SDB", "RefTitle": "Generate ODBC trace on Windows", "RefUrl": "/notes/1405031"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239 "}, {"RefNumber": "1405031", "RefComponent": "BC-DB-SDB", "RefTitle": "Generate ODBC trace on Windows", "RefUrl": "/notes/1405031 "}, {"RefNumber": "1619726", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "FAQ: SAP MaxDB Content Server", "RefUrl": "/notes/1619726 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}