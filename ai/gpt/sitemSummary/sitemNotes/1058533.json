{"Request": {"Number": "1058533", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 278, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016292792017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001058533?language=E&token=3C614F8D716D7A92C0C787F03F0F923E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001058533", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001058533/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1058533"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 32}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.07.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-NET-HTL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Problems with remote access from SAP to Customer system"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Network connection", "value": "XX-SER-NET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Problems with remote access from SAP to Customer system", "value": "XX-SER-NET-HTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-NET-HTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1058533 - TREX/BWA/HANA service connection to customer systems"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You require a TREX/BWA/HANA service connection to your</p>\r\n<ul>\r\n<li>TREX</li>\r\n</ul>\r\n<ul>\r\n<li>BW accelerator</li>\r\n</ul>\r\n<ul>\r\n<li>SAP HANA database</li>\r\n</ul>\r\n<p>system for support purposes.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>service connection, STFK, TREX, BI accelerator, BIA, BWA, Search and Classification (TREX), In Memory Computing Engine, IMCE, HANA database</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ul>\r\n<li>A saprouter connection to SAP (SAPservX) is already available.</li>\r\n</ul>\r\n<ul>\r\n<li>The necessary client application (for TREX/BWA: executable 'trxrss.x' on LINUX and 'trxrss.exe' on WINDOWS / for HANA database: 'hdbrss' on LINUX) is already available on your server or PC.</li>\r\n</ul>\r\n<ul>\r\n<li><strong>Using TREX releases prior to TREX 7.1/ BWA</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>TREX releases prior to TREX 7.1 and BWA releases prior to BWA 7.0 Revision 36 had been shipped without the trxrss binary: If the trxrss binary is missing on your TREX or BWA installation you can download the appropriate version from the attachment of this note.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For installations on WINDOWS running a TREX release prior to TREX 7.1 you may find that the operating system fails to start the trxrss binary. This is due to the fact that the operating system installation is missing version 8 of the Microsoft C/C++ runtime library. In this case download the trxrss_win32_vc7.car archive from the attachment of this note.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For installations on LINUX running a TREX release prior to TREX 7.1 the process might not work correctly due to differences in the configuration of the installation. To prevent any connection problems please check whether the environment variables DIR_INSTANCE and SAPSYSTEM are set. If not then set them like<br />export SAPSYTEM=XX<br />export DIR_INSTANCE=&lt;sapsid&gt;<br />in the shell environment prior to starting the trxrss binary.<br /><br />Note that XX is the instance number referred to below in the 'Setup on the SAProuter' section; &lt;sapsid&gt; is the first part of the name of the TREX/BWA administration user &lt;sapsid&gt;adm as referred to in the section 'Starting of TREX/BWA executables'.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>Using SAP HANA database</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For connecting customer systems for support purposes you can use the TREX/BWA/HANA service connection.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP HANA database is shipped with the hdbrss binary.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>To prevent from any connection problems please check whether the environment variable SAPSYSTEM is set.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>You can set it using<br />export SAPSYSTEM=XX<br />in the shell environment prior to starting the hdbrss binary.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note: XX is the instance number chosen while installing IMCE.</li>\r\n</ul>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The description of how to set up the&#160;<strong>TREX/BIA (TREX/BWA/HANA)&#160;</strong>connection is divided into three sections:</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Starting TREX/BWA/HANA executables</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Setup process on your SAProuter</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;- Setting up the service, maintaining the system data, and opening the service connection in the SAP Support Portal (SAP for Me customer portal)</p>\r\n<p>Note that SAP employees can log on only to servers that are maintained in the system data.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Starting of TREX/BWA/HANA executables</span></strong><strong>:</strong></p>\r\n<p style=\"padding-left: 30px;\"><strong><em>Instructions for TREX/BWA</em></strong>:</p>\r\n<p>For new TREX 7.1 and BIA 7.0 versions the TREX/BWA executables for the TREX/BIA service connection will be available with the TREX/BWA installation. If you have an older TREX/BWA installation you will find the executables attached to this note. Download the executable from this note and copy it into the &lt;TREX_DIR&gt; directory /usr/sap/&lt;sapsid&gt;/TRX&lt;instance_number&gt;/exe so that you can execute it from there.</p>\r\n<p>- Log on to your TREX/BWA system with the user &lt;sapsid&gt;adm.<br /> - Navigate to the &lt;TREX_DIR&gt; directory /usr/sap/&lt;sapsid&gt;/ TRX&lt;instance_number&gt; of your TREX/BWA installation<br /> - Execute the file 'trxrss.x' (UNIX) or 'trxrss.exe' (Windows) c the following commands in a command prompt</p>\r\n<p style=\"padding-left: 30px;\"><strong>(UNIX)</strong>&#160;&#160;&#160;&#160; ./TREXSettings.sh trxrss.x<br /> <strong>(WINDOWS)</strong>&#160; TREXSettings.bat trxrss.exe</p>\r\n<p>By executing the script TREXsettings.sh/.bat the directory &lt;TREX_DIR&gt;/exe is set in the environment variables as the directory for the TREX executables.</p>\r\n<p style=\"padding-left: 30px;\"><strong><em>Instructions for SAP HANA database</em></strong>:</p>\r\n<p>- Log on to your HANA database system with the user &lt;sapsid&gt;adm.<br /> - Navigate to the &lt;HDB_DIR&gt; directory /usr/sap/&lt;sapsid&gt;/HDB&lt;instance_number&gt; of your HANA database installation.<br /> - Execute the file 'hdbrss' by entering the following commands in a command prompt: ./HDBSettings.sh hdbrss</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Setup process on your SAProuter:</span></strong></p>\r\n<p>- You must determine which route permission table \"saprouttab\" is used by the SAProuter that you are using.</p>\r\n<p>- In the relevant table, create an entry of the following type:</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Note</span></strong>: <strong>TREX/BIA</strong> remote connection uses the TCP/IP port 3&lt;instance&gt;09. This port must be released explicitly. The connections that are permitted when using the respective ports are determined in a configuration file called 'saprouttab'. The instance number was created during the TREX/BWA/HANA installation process. You will find the instance number in the corresponding admin tool (Python): Landscape --&gt; Hosts --&gt; Instance.</p>\r\n<p style=\"padding-left: 30px;\">P &lt;IP address SAP-SR&gt; &lt;IP address server|host name|FQDN/FQHN&gt; 3&lt;instance&gt;09</p>\r\n<p>or, in the case of SNC:</p>\r\n<p style=\"padding-left: 30px;\">KP \"p:CN=sapserv&lt;X&gt;, OU=SAProuter, O=SAP, C=DE\" &lt;IP address server|host name|FQDN/FQHN&gt; 3&lt;instance&gt;09</p>\r\n<p>Examples:</p>\r\n<p>(e.g. if your system has the instance number 41, the port will be 34109)</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv1</strong><strong><br /> </strong>P *************** *********** 34109</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv2</strong><strong><br /> </strong>KP \"p:CN=sapserv2, OU=SAProuter, O=SAP, C=DE\" *********** 34109</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv3</strong><strong><br /> </strong>P *********** *********** 34109</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv4</strong><strong><br /> </strong>P ************ *********** 34109</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv5</strong><strong><br /> </strong>P ************ *********** 34109</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv7</strong><strong><br /> </strong>P ************* *********** 34109</p>\r\n<p style=\"padding-left: 30px;\"><strong>#for sapserv9</strong><strong><br /> </strong>KP \"p:CN=sapserv9, OU=SAProuter, O=SAP, C=DE\" *********** 34109</p>\r\n<p>-&#160;&#160;&#160;&#160;&#160;<strong>A generic entry such as P * * * is insufficient, because the wild card \"*\" that specifies the TCP port (the 3rd *), activates only ports 3200 to 3299 and does not activate any ports outside this port-range.</strong></p>\r\n<p>-&#160;&#160;&#160;&#160; Update the changed route permission table \"saprouttab\" in the SAProuter with the command \"saprouter -n\" or restart the SAProuter.</p>\r\n<p>-&#160;&#160;&#160;&#160; Check that the SAProuter can reach the target host (IP address or host name) on the relevant application port. If this is not the case, set up the network and DNS accordingly.&#160; Further details see: SAP note 48243.</p>\r\n<p>-&#160;&#160;&#160;&#160;&#160;<strong>In case your SAProuter is password-protected in the saprouttab, define the SAProuter password in the Customer Remote Logon Depot (formerly known as secure area) in accordance with SAP Note 1773689. Note: max. password length = 8 characters!</strong></p>\r\n<p>Note that only the current SAProuter software supports all services, therefore, we recommend to always use the current version.</p>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Setting up the service, maintaining the system data and opening the connection in SAP Support Portal (SAP for Me customer portal)</span></strong><strong>:</strong></p>\r\n<p>- Log on to SAP for Me, URL:&#160;<a target=\"_blank\" href=\"https://me.sap.com/systemsprovisioning/connectivity\">https://me.sap.com/systemsprovisioning/connectivity</a><br /> - If necessary, switch to the tab \"All\" in the systems area.<br /> - Click the relevant system ID to select the required system.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Creating the service !!is only required!! if it does not yet exist in the connection type list</span></strong><strong>:</strong></p>\r\n<p>- Maintain the required connection type by clicking the \"+ symbol\" (add connection type).<br /> - Select the service:&#160;<strong>TREX/BIA</strong><strong><br /> </strong>- Specify the \"Individual Port\" 3&lt;instance&gt;09 accordingly.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Note</span></strong>: The instance number was created during the TREX/BWA/HANA installation process. You will find the instance number in the corresponding admin tool (Python): Landscape --&gt; Hosts --&gt; Instance.</p>\r\n<p>- Enter at least one contact person.<br /> - Save the selection.<br /> - Navigate back to the service connections by clicking the \"&lt; symbol\" (back).</p>\r\n<p><strong><span style=\"text-decoration: underline;\">System data maintenance !!is only required!! if neither database nor application server are maintained</span></strong><strong>:</strong></p>\r\n<p>- Navigate to the system data by choosing \"Maintain System Data\".<br /> - Choose the menu option \"Server and SAProuter\".<br /> - Choose DB/Application/Other servers.<br /> - Select either database server or the \"+ symbol\" (add application / other server).<br /> - If necessary, click Edit and enter at least the required data (required entry fields are marked with a red asterisk).<br /> - Note that the entries for the additional SAProuter !!are only required!! if you have multiple SAProuters in sequence (cascaded - one after another) on your side.<br /> - Save your changes.<br /> - Navigate back to the service connections by clicking the \"&lt; symbol\" (back).</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Opening the Connection</span></strong><strong>:</strong></p>\r\n<p>- Choose the service:&#160;<strong>TREX/BIA </strong>in the list of connection types.<br /> - Choose \"Open connection\".<br /> - Enter at least one contact person.<br /> - Set the date and time when the connection is closed automatically.<br /> - Click on \"Open Connection\".</p>\r\n<p>In case there are any problems with service connections, create a case (<a target=\"_blank\" href=\"https://me.sap.com/servicessupport/productsupport\">https://me.sap.com/servicessupport/productsupport</a>) under the component XX-SER-NET.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "HAN (SAP HANA)"}, {"Key": "Other Components", "Value": "BC-TRX-BIA (TREX BI Accelerator)"}, {"Key": "Other Components", "Value": "BC-TRX (TREX)"}, {"Key": "Other Components", "Value": "HAN-DB-ENG (SAP HANA DB Engines)"}, {"Key": "Other Components", "Value": "HAN-DB (SAP HANA Database)"}, {"Key": "Responsible                                                                                         ", "Value": "D037768"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C3303606)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001058533/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001058533/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001058533/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001058533/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001058533/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001058533/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001058533/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001058533/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001058533/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "trxrss_lnx32.car", "FileSize": "266", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000345932007&iv_version=0032&iv_guid=10C3B4B0B2D6F541A975635453A2B47E"}, {"FileName": "trxrss_win64.car", "FileSize": "246", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000345932007&iv_version=0032&iv_guid=3AED04316E21CC4FAA7DD8E53919AD45"}, {"FileName": "trxrss_lnxx64.CAR", "FileSize": "481", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000345932007&iv_version=0032&iv_guid=3E86F4BEA8CD3C4EB511AA6DA3D57E5D"}, {"FileName": "trxrss_sun32.car", "FileSize": "312", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000345932007&iv_version=0032&iv_guid=4AB72A8E14858340A4CC84727511E242"}, {"FileName": "trxrss_aix32_51.car", "FileSize": "146", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000345932007&iv_version=0032&iv_guid=531FAB3EBC2D624495444253DDA7CF86"}, {"FileName": "trxrss_hp32.car", "FileSize": "329", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000345932007&iv_version=0032&iv_guid=59656239E661A347AB69E03650D8B4C0"}, {"FileName": "trxrss_win32_vc7.car", "FileSize": "202", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000345932007&iv_version=0032&iv_guid=A1EE4BEE6D6FAE4FAC93F4EF16E0DFB7"}, {"FileName": "trxrss_win32.car", "FileSize": "224", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000345932007&iv_version=0032&iv_guid=AF38BF9FC05C8746A7C3439FDAC9B180"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1855805", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1855805"}, {"RefNumber": "1855728", "RefComponent": "HAN-DB", "RefTitle": "Queries on M_CS_COLUMNS need a long time", "RefUrl": "/notes/1855728"}, {"RefNumber": "1829531", "RefComponent": "HAN-DB", "RefTitle": "Query on partitioned column table can fail before Rev. 52", "RefUrl": "/notes/1829531"}, {"RefNumber": "1828631", "RefComponent": "HAN-DB", "RefTitle": "HANA Studio shows the Manufacturer \"unknown\"", "RefUrl": "/notes/1828631"}, {"RefNumber": "1641210", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database: Checking for suspected problems", "RefUrl": "/notes/1641210"}, {"RefNumber": "1635304", "RefComponent": "HAN", "RefTitle": "Central note for HANA support connections", "RefUrl": "/notes/1635304"}, {"RefNumber": "1634848", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database Service Connections", "RefUrl": "/notes/1634848"}, {"RefNumber": "1537173", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.20: Revision 8", "RefUrl": "/notes/1537173"}, {"RefNumber": "1524186", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.20: Revision 7", "RefUrl": "/notes/1524186"}, {"RefNumber": "1485220", "RefComponent": "BC-TRX", "RefTitle": "Initial Qualified Response for a Trex message", "RefUrl": "/notes/1485220"}, {"RefNumber": "1362587", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.00: Revision 52", "RefUrl": "/notes/1362587"}, {"RefNumber": "1351722", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.00: Revision 51", "RefUrl": "/notes/1351722"}, {"RefNumber": "1318214", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA: Suspicion of bad query performance", "RefUrl": "/notes/1318214"}, {"RefNumber": "1313411", "RefComponent": "BC-TRX", "RefTitle": "BWA 7.00: Revision 50", "RefUrl": "/notes/1313411"}, {"RefNumber": "1310683", "RefComponent": "SV-SMG-SER", "RefTitle": "Prerequisites BWA Remote Configuration/Verification Check", "RefUrl": "/notes/1310683"}, {"RefNumber": "1283985", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.00: Revision 49", "RefUrl": "/notes/1283985"}, {"RefNumber": "1231097", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 48", "RefUrl": "/notes/1231097"}, {"RefNumber": "1178408", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 47", "RefUrl": "/notes/1178408"}, {"RefNumber": "1174373", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 46", "RefUrl": "/notes/1174373"}, {"RefNumber": "1162706", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 45", "RefUrl": "/notes/1162706"}, {"RefNumber": "1155479", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 44", "RefUrl": "/notes/1155479"}, {"RefNumber": "1087671", "RefComponent": "BC-TRX", "RefTitle": "TREX 7.10: Service Connection for TREX Support", "RefUrl": "/notes/1087671"}, {"RefNumber": "1067044", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1067044"}, {"RefNumber": "1003900", "RefComponent": "BC-TRX", "RefTitle": "TREX 7.1: Central note", "RefUrl": "/notes/1003900"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1832180", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "How to assist the customer with network connectivity problems between SAP and the customer system", "RefUrl": "/notes/1832180 "}, {"RefNumber": "1872501", "RefComponent": "BC-TRX-BIA", "RefTitle": "TREXIndexServer crash  - BWA Troubleshooting", "RefUrl": "/notes/1872501 "}, {"RefNumber": "2163520", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Tools and Information for SAP Support", "RefUrl": "/notes/2163520 "}, {"RefNumber": "2477204", "RefComponent": "HAN-DB", "RefTitle": "FAQ: SAP HANA Services and Ports", "RefUrl": "/notes/2477204 "}, {"RefNumber": "984434", "RefComponent": "XX-SER-FORME", "RefTitle": "How to speed up customer incident processing", "RefUrl": "/notes/984434 "}, {"RefNumber": "2105394", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA/TREX: How to handle a core file", "RefUrl": "/notes/2105394 "}, {"RefNumber": "48243", "RefComponent": "XX-SER-NET-NEW", "RefTitle": "Integrating the SAProuter software into a firewall environment", "RefUrl": "/notes/48243 "}, {"RefNumber": "1003900", "RefComponent": "BC-TRX", "RefTitle": "TREX 7.1: Central note", "RefUrl": "/notes/1003900 "}, {"RefNumber": "1828631", "RefComponent": "HAN-DB", "RefTitle": "HANA Studio shows the Manufacturer \"unknown\"", "RefUrl": "/notes/1828631 "}, {"RefNumber": "1855728", "RefComponent": "HAN-DB", "RefTitle": "Queries on M_CS_COLUMNS need a long time", "RefUrl": "/notes/1855728 "}, {"RefNumber": "1829531", "RefComponent": "HAN-DB", "RefTitle": "Query on partitioned column table can fail before Rev. 52", "RefUrl": "/notes/1829531 "}, {"RefNumber": "1635304", "RefComponent": "HAN", "RefTitle": "Central note for HANA support connections", "RefUrl": "/notes/1635304 "}, {"RefNumber": "1641210", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database: Checking for suspected problems", "RefUrl": "/notes/1641210 "}, {"RefNumber": "1310683", "RefComponent": "SV-SMG-SER", "RefTitle": "Prerequisites BWA Remote Configuration/Verification Check", "RefUrl": "/notes/1310683 "}, {"RefNumber": "1634848", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database Service Connections", "RefUrl": "/notes/1634848 "}, {"RefNumber": "1537173", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.20: Revision 8", "RefUrl": "/notes/1537173 "}, {"RefNumber": "1524186", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.20: Revision 7", "RefUrl": "/notes/1524186 "}, {"RefNumber": "1485220", "RefComponent": "BC-TRX", "RefTitle": "Initial Qualified Response for a Trex message", "RefUrl": "/notes/1485220 "}, {"RefNumber": "1318214", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA: Suspicion of bad query performance", "RefUrl": "/notes/1318214 "}, {"RefNumber": "1351722", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.00: Revision 51", "RefUrl": "/notes/1351722 "}, {"RefNumber": "1362587", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.00: Revision 52", "RefUrl": "/notes/1362587 "}, {"RefNumber": "1313411", "RefComponent": "BC-TRX", "RefTitle": "BWA 7.00: Revision 50", "RefUrl": "/notes/1313411 "}, {"RefNumber": "1283985", "RefComponent": "BC-TRX-BIA", "RefTitle": "BWA 7.00: Revision 49", "RefUrl": "/notes/1283985 "}, {"RefNumber": "1231097", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 48", "RefUrl": "/notes/1231097 "}, {"RefNumber": "1178408", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 47", "RefUrl": "/notes/1178408 "}, {"RefNumber": "1174373", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 46", "RefUrl": "/notes/1174373 "}, {"RefNumber": "1162706", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 45", "RefUrl": "/notes/1162706 "}, {"RefNumber": "1155479", "RefComponent": "BC-TRX-BIA", "RefTitle": "BIA 7.00: Revision 44", "RefUrl": "/notes/1155479 "}, {"RefNumber": "1087671", "RefComponent": "BC-TRX", "RefTitle": "TREX 7.10: Service Connection for TREX Support", "RefUrl": "/notes/1087671 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}