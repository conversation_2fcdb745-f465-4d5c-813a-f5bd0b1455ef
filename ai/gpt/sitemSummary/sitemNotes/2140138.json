{"Request": {"Number": "2140138", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 420, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018032512017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002140138?language=E&token=4B94281F669BF180EFB724F626BCB12B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002140138", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2140138"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.03.2015"}, "SAPComponentKey": {"_label": "Component", "value": "LE-TSW"}, "SAPComponentKeyText": {"_label": "Component", "value": "Trader's and Scheduler's Workbench"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics Execution", "value": "LE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Trader's and Scheduler's Workbench", "value": "LE-TSW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LE-TSW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2140138 - Decoupled TSW related End Of Purpose Check before blocking of customer and vendor data"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The application&#160;Decoupled TSW can contain data, which is related to a customer or vendor. This note describes the changes, which will prevent blocking of a customer, if this customer is still in use. In addition these changes will prevent blocking of a vendor, if this vendor is still in use.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Deletion, Blocking, Personal Data, EOP, Customer, Vendor, KUNNR , TSW for ECC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>See note 2007926</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The new functionality for&#160;<em>Decoupled TSW </em>&#160;is available as of 617 release support package SP08.</p>\r\n<p><strong><em>1.LE-TSW</em>&#160;provides&#160;<em>a&#160;End of Purpose Check&#160;</em>&#160;for the customer or vendor. This check consists of the following main features:</strong></p>\r\n<p><br />The functionality consists of the following main features:</p>\r\n<p>In customizing \"Customer Master /Vendor Master Deletion\" the following entries are added:</p>\r\n<ol><ol style=\"list-style-type: lower-alpha;\">\r\n<li>In the activity \"Define and Store Application Names for EoP Check\" the following entry was added:<br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>ID Type</td>\r\n<td>Application Name</td>\r\n<td>Application Description</td>\r\n</tr>\r\n<tr>\r\n<td>1 - Customer Master Data</td>\r\n<td>TSW_ECC</td>\r\n<td>Decoupled TSW&#160;Customer</td>\r\n</tr>\r\n<tr>\r\n<td>2 - Vendor Master Data</td>\r\n<td>TSW_ECC</td>\r\n<td>Decoupled TSW&#160;Vendor</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</li>\r\n<li>In the activity \"Define Application Classes Registered for EoP Check\" the following entry was added:<br />\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>ID Type</td>\r\n<td>Application Name</td>\r\n<td>Registered Class for EoP Checks</td>\r\n<td>General</td>\r\n<td>Comp.Code</td>\r\n</tr>\r\n<tr>\r\n<td>1 - Customer Master Data</td>\r\n<td>TSW_ECC</td>\r\n<td>CVP_TSW_ECC_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n<tr>\r\n<td>2 - Vendor Master Data</td>\r\n<td>TSW_ECC</td>\r\n<td>CVP_TSW_ECC_EOP_CHECK</td>\r\n<td>'X'</td>\r\n<td>'X'</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\nThe application&#160;TSW delivers the following class registered for the end of purpose check of the customer and the vendor:<br />&#160;CVP_TSW_ECC_EOP_CHECK</li>\r\n</ol></ol>\r\n<p><strong>2. The new Business function ILM_TSW_ECC is provided for ILM enablement of the following archiving objects-</strong></p>\r\n<ul>\r\n<li>OIG_TPUNIT</li>\r\n<li>OIG_VEHCLE</li>\r\n<li>OIJ_NOMIN</li>\r\n<li>OIJ_TICKET</li>\r\n<li>IS_OIFSPBL</li>\r\n</ul>\r\n<p><strong>3. Handling of blocked customer/vendor</strong></p>\r\n<p>No further business activity like creation or changing of nominations will be allowed on a blocked customer or vendor.Details of blocked customer or vendor can be viewed only by authorized user.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "Roopa R (I029092)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I045710)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002140138/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002140138/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002140138/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002140138/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002140138/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002140138/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002140138/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002140138/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002140138/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2007926", "RefComponent": "LO-MD-BP-DP", "RefTitle": "Simplified Blocking and Deletion of Customer / Vendor Master Data", "RefUrl": "/notes/2007926 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-OIL", "From": "617", "To": "617", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-OIL 617", "SupportPackage": "SAPK-61708INISOIL", "URL": "/supportpackage/SAPK-61708INISOIL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}