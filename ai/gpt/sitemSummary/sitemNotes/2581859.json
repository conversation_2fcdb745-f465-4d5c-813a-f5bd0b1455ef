{"Request": {"Number": "2581859", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 354, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000020587072017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=438B9A711A7B22CF9C020342DA5A7DAE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2581859"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.12.2017"}, "SAPComponentKey": {"_label": "Component", "value": "FI-RA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Revenue Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Revenue Accounting", "value": "FI-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2581859 - Final Invoice Is Not Updated When Processing Invoice For POB Without Source of Price"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>For performance obligation (POB) that does not have source of price, when final invoice&#160;is issued without&#160;any price condition type, the system does not set the final invoice in the POB although the invoice revenue accounting item is processed successfully. This is incorrect.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FARR, Revenue Accounting, Final Invoice, Source of Price</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This is coding error.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Apply this note.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I302501)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I327860)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2580961", "RefComponent": "FI-RA-MIG", "RefTitle": "RAR Data Migration and Transition - Additional Information", "RefUrl": "/notes/2580961 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "REVREC", "From": "120", "To": "120", "Subsequent": ""}, {"SoftwareComponent": "REVREC", "From": "130", "To": "130", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "REVREC 120", "SupportPackage": "SAPK-12007INREVREC", "URL": "/supportpackage/SAPK-12007INREVREC"}, {"SoftwareComponentVersion": "REVREC 130", "SupportPackage": "SAPK-13005INREVREC", "URL": "/supportpackage/SAPK-13005INREVREC"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "REVREC", "NumberOfCorrin": 2, "URL": "/corrins/**********/14019"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 24, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2324899 ", "URL": "/notes/2324899 ", "Title": "Invoice amount missing on new condition type for value relevant POB", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2328750 ", "URL": "/notes/2328750 ", "Title": "Ignore invoice if no condition type & POB is not source of price", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2329804 ", "URL": "/notes/2329804 ", "Title": "Dump ITAB_ILLEGAL_OSRT_ORDER when processing invoice", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2336637 ", "URL": "/notes/2336637 ", "Title": "First local amount of invoice should be available when its transaction amount is not 0", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2345092 ", "URL": "/notes/2345092 ", "Title": "Remove unused code for invoice performance improvement", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2358647 ", "URL": "/notes/2358647 ", "Title": "Performance improvement for mass invoice handling", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2379440 ", "URL": "/notes/2379440 ", "Title": "Request recon key for every contract in invoice process", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2397978 ", "URL": "/notes/2397978 ", "Title": "Invoice performance improvement", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2404273 ", "URL": "/notes/2404273 ", "Title": "Final invoice of value relevant POB should load price reallocation for RA 1.2", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2413124 ", "URL": "/notes/2413124 ", "Title": "One invoice RAI failed should prevent the latter invoice RAIs success in one transaction", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2417264 ", "URL": "/notes/2417264 ", "Title": "The system does not process RAIs without any condition type in RAR 1.2 and 1.3", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2425982 ", "URL": "/notes/2425982 ", "Title": "Migration: RAR will calculate exchange rate if sender component is not able to send the exchange rate", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2439066 ", "URL": "/notes/2439066 ", "Title": "Set invoice RAI to error directly when performance obligation or contract has validation result of 'E'", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2440405 ", "URL": "/notes/2440405 ", "Title": "Zero valued invoice RAI item is not processed in RAI monitor", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2446292 ", "URL": "/notes/2446292 ", "Title": "Credit Memo Invoice of Quantity Based POB with the Event Type of CI and the Fulfillment Type of Event-Based Triggers a New Fulfillment", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2449670 ", "URL": "/notes/2449670 ", "Title": "Invoice RAI Is Marked as Processed Successfully Even Though Invoice Exception Occurs", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2461301 ", "URL": "/notes/2461301 ", "Title": "Process Invoices With Different Invoice Type Together to Ensure The Sequence", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2547725 ", "URL": "/notes/2547725 ", "Title": "Transition: Allow Invoiced Amount and Revenue Different than the Contractual Price and Adjust Contractual Price accordingly", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2555985 ", "URL": "/notes/2555985 ", "Title": "Invoice Transaction Currency Is Missing For the Main Condition Type Which Has Zero Amount", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2570750 ", "URL": "/notes/2570750 ", "Title": "Report Error Message When Transaction Currency of Invoice Is Different From the Currency in Contract Header", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "130", "Number": "2552126 ", "URL": "/notes/2552126 ", "Title": "Processing Invoice Fails Due to Missing Invoice Due Date", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2403091 ", "URL": "/notes/2403091 ", "Title": "Final invoice of value relevant POB should load price reallocation (for RAR 1.3)", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2413124 ", "URL": "/notes/2413124 ", "Title": "One invoice RAI failed should prevent the latter invoice RAIs success in one transaction", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2417264 ", "URL": "/notes/2417264 ", "Title": "The system does not process RAIs without any condition type in RAR 1.2 and 1.3", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2425982 ", "URL": "/notes/2425982 ", "Title": "Migration: RAR will calculate exchange rate if sender component is not able to send the exchange rate", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2439066 ", "URL": "/notes/2439066 ", "Title": "Set invoice RAI to error directly when performance obligation or contract has validation result of 'E'", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2440405 ", "URL": "/notes/2440405 ", "Title": "Zero valued invoice RAI item is not processed in RAI monitor", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2446292 ", "URL": "/notes/2446292 ", "Title": "Credit Memo Invoice of Quantity Based POB with the Event Type of CI and the Fulfillment Type of Event-Based Triggers a New Fulfillment", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2449670 ", "URL": "/notes/2449670 ", "Title": "Invoice RAI Is Marked as Processed Successfully Even Though Invoice Exception Occurs", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2461301 ", "URL": "/notes/2461301 ", "Title": "Process Invoices With Different Invoice Type Together to Ensure The Sequence", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2506397 ", "URL": "/notes/2506397 ", "Title": "Update logic improvements in Revenue Accounting", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2519152 ", "URL": "/notes/2519152 ", "Title": "In an Initial Load, All Invoice RAIs In a Batch Processing Are Set to Error Even If Only One Invoice RAI Fails in the Local Currency Check", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2547725 ", "URL": "/notes/2547725 ", "Title": "Transition: Allow Invoiced Amount and Revenue Different than the Contractual Price and Adjust Contractual Price accordingly", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2555985 ", "URL": "/notes/2555985 ", "Title": "Invoice Transaction Currency Is Missing For the Main Condition Type Which Has Zero Amount", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2570750 ", "URL": "/notes/2570750 ", "Title": "Report Error Message When Transaction Currency of Invoice Is Different From the Currency in Contract Header", "Component": "FI-RA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}