{"Request": {"Number": "1105558", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 316, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016396822017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001105558?language=E&token=6630E86B7530CB6BF5B6F7D57ECC6021"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001105558", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001105558/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1105558"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.03.2009"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1105558 - BP_CVI: Migration report from Note 851445 terminates"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have upgraded to ERP2005, and have run the migration report from Note 851445. One or several reports terminate and trigger short dumps.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BD001, BC001, CVI_MIGRATE_CUST_LINKS, CVI_MIGRATE_VEND_LINKS, CVI_ANALYZE_CUST_TAX_NUMBERS, CVI_ANALYZE_VEND_TAX_NUMBERS, CVI_SYNC_CUST_TAX_NUMBERS, CVI_SYNC_VEND_TAX_NUMBERS, SAPLCVI_SYNC_TAX_NUMBERS</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Data inconsistency</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Inconsistencies occur in the old link tables between customer/vendor and business partner:<br /><br />The following inconsistencies may occur:<br />1) in tables BD001, BC001, entries for<br />&#x00A0;&#x00A0; - the BP does not exist (anymore).<br />&#x00A0;&#x00A0; - the customer/vendor does not exist (anymore).<br /><br />2) in tables BD001, BC001, entries for which<br />&#x00A0;&#x00A0; - two or more business partners refer to one customer or vendor.<br /><br />The section below contains four sample reports that can detect inconsistencies between the business partner and the customer in the link table BD001 and between the business partner and vendor in the link table BC001.<br /><br />Once you have identified the inconsistencies using the suggested reports, these inconsistencies must be removed. (The tables BD001 and BC001 can be edited in transaction SE16.)<br /><br />You can then run the terminated migration report again. When you restart the migration reports, use the same \"Unique Run ID\" that you used for the first start (which caused the termination).<br /><br />*---------------------------------------------------------------------*<br />* Report&#x00A0;&#x00A0;Z_CHECK_BD001_BP_EXISTS<br />*---------------------------------------------------------------------*<br /><br />REPORT&#x00A0;&#x00A0;Z_CHECK_BD001_BP_EXISTS.<br /><br />data: ls_bd001&#x00A0;&#x00A0;&#x00A0;&#x00A0; TYPE bd001,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ls_but000 type but000,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ls_kna1&#x00A0;&#x00A0; type kna1.<br /><br />select * from BD001 into ls_bd001.<br /><br />&#x00A0;&#x00A0;select single * from BUT000 into ls_but000<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where partner = ls_bd001-partner.<br />&#x00A0;&#x00A0;if sy-subrc &lt;&gt; 0.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;write: / 'BD001: ', ls_bd001, 'BP does not exist'.<br />&#x00A0;&#x00A0;endif.<br /><br />&#x00A0;&#x00A0;select single * from kna1 into ls_kna1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where kunnr = ls_bd001-kunnr.<br />&#x00A0;&#x00A0;if sy-subrc &lt;&gt; 0.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;write: / 'BD001: ', ls_bd001, 'debitor/customer does not exist'.<br />&#x00A0;&#x00A0;endif.<br /><br />endselect.<br /><br />write: / 'finished'.<br /><br />*&amp;---------------------------------------------------------------------<br />*&amp; Report&#x00A0;&#x00A0;Z_SELECT_COUNT_BD001<br />*&amp;---------------------------------------------------------------------<br /><br />REPORT z_select_count_bd001.<br /><br />data:<br />&#x00A0;&#x00A0;kunnr LIKE bd001-kunnr,<br />&#x00A0;&#x00A0;lv_count TYPE i.<br /><br />SELECT kunnr COUNT( * ) FROM bd001 INTO (kunnr, lv_count) GROUP BY kunnr.<br /><br />&#x00A0;&#x00A0;if lv_count &gt; 1.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;WRITE:/ 'kunnr: ', kunnr, ' count: ', lv_count.<br />&#x00A0;&#x00A0;endif.<br /><br />ENDSELECT.<br /><br />write: / 'finished'.<br /><br />*---------------------------------------------------------------------*<br />* Report&#x00A0;&#x00A0;Z_CHECK_BC001_BP_EXISTS<br />*---------------------------------------------------------------------*<br /><br />REPORT&#x00A0;&#x00A0;Z_CHECK_BC001_BP_EXISTS.<br /><br />data: ls_bc001&#x00A0;&#x00A0;type bc001,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ls_but000 type but000,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ls_lfa1&#x00A0;&#x00A0; type lfa1.<br /><br />select * from bc001 into ls_bc001.<br /><br />&#x00A0;&#x00A0;select single * from BUT000 into ls_but000<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where partner = ls_bc001-partner.<br />&#x00A0;&#x00A0;if sy-subrc &lt;&gt; 0.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;write: / 'BC001: ', ls_bc001, 'BP does not exist'.<br />&#x00A0;&#x00A0;endif.<br /><br />&#x00A0;&#x00A0;select single * from lfa1 into ls_lfa1<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;where lifnr = ls_bc001-lifnr.<br />&#x00A0;&#x00A0;if sy-subrc &lt;&gt; 0.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;write: / 'BC001: ', ls_bc001, 'creditor/vendor does not exist'.<br />&#x00A0;&#x00A0;endif.<br /><br />endselect.<br /><br />write: / 'finished'.<br /><br />*&amp;---------------------------------------------------------------------<br />*&amp; Report&#x00A0;&#x00A0;Z_SELECT_COUNT_BC001<br />*&amp;---------------------------------------------------------------------<br /><br />REPORT z_select_count_bc001.<br /><br />data:<br />&#x00A0;&#x00A0;lifnr LIKE bc001-lifnr,<br />&#x00A0;&#x00A0;lv_count TYPE i.<br /><br />SELECT lifnr COUNT( * ) FROM bc001 INTO (lifnr, lv_count) GROUP BY lifnr.<br /><br />&#x00A0;&#x00A0;if lv_count &gt; 1.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;WRITE:/ 'lifnr: ', lifnr, ' count: ', lv_count.<br />&#x00A0;&#x00A0;endif.<br /><br />ENDSELECT.<br /><br />write: / 'finished'.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D027661"}, {"Key": "Processor                                                                                           ", "Value": "D027661"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001105558/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001105558/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001105558/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001105558/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001105558/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001105558/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001105558/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001105558/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001105558/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "956054", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Customer/vendor integration as of ERP 6.00", "RefUrl": "/notes/956054"}, {"RefNumber": "851445", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Details about reports to be executed for ERP 2005", "RefUrl": "/notes/851445"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "956054", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Customer/vendor integration as of ERP 6.00", "RefUrl": "/notes/956054 "}, {"RefNumber": "851445", "RefComponent": "FS-BP", "RefTitle": "BP_CVI: Details about reports to be executed for ERP 2005", "RefUrl": "/notes/851445 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}