{"Request": {"Number": "2399707", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 339, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014133742017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002399707?language=E&token=C3C86BF5FF6A8EFA382B741859F26F56"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002399707", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002399707/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2399707"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 165}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2024"}, "SAPComponentKey": {"_label": "Component", "value": "CA-TRS-PRCK"}, "SAPComponentKeyText": {"_label": "Component", "value": "S/4HANA Conversion Pre-Checks"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transition to S/4HANA On-Premise", "value": "CA-TRS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-TRS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Conversion Pre-Checks", "value": "CA-TRS-PRCK", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-TRS-PRCK*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2399707 - Simplification Item Check"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>As part of your system&#8217;s SAP Readiness Check assessment, you are interested in performing a simplification item relevance check, compatibility scope relevancy check, or a simplification item consistency check. Your intent in conducting this analysis is to support the scoping and planning of either the conversion from SAP ERP to SAP S/4HANA or the upgrade from one SAP S/4HANA product version to another SAP S/4HANA product version.</p>\r\n<p>Alternatively, you are interested in manually performing a simplification item relevancy or consistency check outside SAP Readiness Check.</p>\r\n<p>To learn more about how to perform simplification item checks and the relation to SAP Readiness Check, you can explore the following links:</p>\r\n<p><a target=\"_blank\" href=\"https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./\">https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./</a></p>\r\n<p><a target=\"_blank\" href=\"https://blogs.sap.com/2020/01/02/simplification-item-catalog-simplification-item-check-and-sap-readiness-check-for-sap-s-4hana/\">https://blogs.sap.com/2020/01/02/simplification-item-catalog-simplification-item-check-and-sap-readiness-check-for-sap-s-4hana/</a></p>\r\n<p><strong>We strongly recommend analyzing a production system; otherwise, the results might be incomplete or misleading.&#160;</strong>If you choose to use another environment, for instance, a copy of a production system, implement and follow SAP Note&#160;<a target=\"_blank\" href=\"/notes/2568736\">2568736</a>&#160;(in both the productive and non-production systems) to capture and upload the necessary ST03N data.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Readiness Check, SAP S/4HANA Conversion; SAP S/4HANA Upgrade; Simplification Item, Simplification List, Compatibility Scope</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Before implementing this SAP Note (2399707), we strongly recommend implementing the latest version of SAP Note&#160;<a target=\"_blank\" href=\"/notes/1668882\">1668882</a>.</p>\r\n<p>In addition, before executing the simplification item analysis in your system, we strongly recommend implementing SAP Note&#160;<a target=\"_blank\" href=\"/notes/2502552\">2502552</a>. This note delivers check classes used to refine the list of relevant simplification items.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Contents<br /></strong>&#160; 1.&#160; Introduction<br />&#160; 2.&#160; Enabling the Simplification Item Checks<br />&#160; &#160; &#160; &#160;2.1. Note Implementation<br />&#160; &#160; &#160; &#160;2.2. Simplification Item Catalog Maintenance<br />&#160; 3.&#160; Executing the Simplification Item Checks<br />&#160; &#160; &#160; &#160;3.1. Using the Data Collection Framework for SAP Readiness Check<br />&#160; &#160; &#160; &#160;3.2. Executing the Checks Manually<br />&#160; 4.&#160; Reviewing the Results in /SDF/RC_START_CHECK<br />&#160; &#160; &#160; &#160;4.1. Interpreting the Check Results<br />&#160; &#160; &#160; &#160;4.2. Administrative Functions within the Check Results View<br />&#160; 5.&#160; Additional Information<br />&#160; 6.&#160; Frequently Asked Questions</p>\r\n<p><strong>&#160; 1. Introduction</strong></p>\r\n<p style=\"padding-left: 30px;\">While scoping and planning the conversion of your SAP ERP system to SAP S/4HANA, or the upgrade of an SAP S/4HANA system, we recommend analyzing the impact on your system based on the list of documented simplification items. This note provides the simplification item check capabilities to analyze your system. There are two types of checks integrated into this solution:</p>\r\n<ul>\r\n<li><strong>Relevance Check:</strong>&#160;Produces a customized list of relevant simplification items for your system. The relevance is determined based on rules maintained in the&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#sic\">simplification item catalog</a>.&#160;</li>\r\n</ul>\r\n<ul>\r\n<ul style=\"list-style-type: square;\">\r\n<li>If the rule is not maintained or cannot be processed, the item has the status&#160;<em>Relevance to Be Checked</em>.</li>\r\n<li>For simplification items where the rule evaluates database table content, the analysis evaluates table entries across all clients within the system.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">The results of the relevance check are presented in the&#160;<em>Simplification Items</em>&#160;tile and the&#160;<em>Compatibility Scope Analysis</em>&#160;tile within SAP Readiness Check for SAP S/4HANA and SAP Readiness Check for SAP S/4HANA upgrades.</p>\r\n<ul>\r\n<li><strong>Consistency Check:</strong>&#160;When initiated in client 000, this check analyzes the consistency of the system in preparation for the conversion or upgrade using Software Update Manager. The consistency check only evaluates those simplification items marked as relevant or potentially relevant to the system.</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">We recommend resolving all identified inconsistencies before the downtime in Software Update Manager. Otherwise, when Software Update Manager encounters unresolved consistency issues, it will need to be reset or restarted. The check also warns you about critical changes during the conversion or upgrade, for example, potential data loss. To continue, you must confirm that you have understood the warning.</p>\r\n<p style=\"padding-left: 60px;\">Individual check classes, developed by the associated application area, are leveraged during the consistency check evaluation. The implementation of SAP Note&#160;<a target=\"_blank\" href=\"/notes/2502552\">2502552</a>&#160;delivers these classes to your system.</p>\r\n<p style=\"padding-left: 60px;\">To initiate the consistency check, you can either activate the option within the SAP Readiness Check selection screen (report RC_COLLECT_ANALYSIS_DATA) or use the simplification item check report (/SDF/RC_START_CHECK).</p>\r\n<p style=\"padding-left: 60px;\">The results of the consistency check, when initiated by SAP Readiness Check (report RC_COLLECT_ANALYSIS_DATA) in client 000, can be uploaded to an existing analysis for the productive client. The results are then visible within the detailed view of the&#160;<em>Simplification Items</em>&#160;tile.</p>\r\n<p style=\"padding-left: 60px;\">Detailed results are available in the simplification item check report (/SDF/RC_START_CHECK) by selecting&#160;<em>Display Last Check Result</em>. The results are only visible in the client used to perform the analysis (for instance, client 000).</p>\r\n<p style=\"padding-left: 60px;\"><strong>Note</strong>: The check results include references to SAP Notes that describe how to resolve identified issues.</p>\r\n<p style=\"padding-left: 30px;\">To identify and resolve possible issues within time, you must&#160;<strong>correctly implement and run the simplification item checks before</strong>&#160;starting the technical conversion or upgrade. To allocate sufficient time and resources to resolve any potential issues, we recommend initially performing the analysis as part of the scoping and planning phase of the project within SAP Readiness Check. The check is performed one additional time by the Software Update Manager toolset shortly before the technical downtime of the conversion or upgrade of a system. But do not wait to implement and execute the check just before starting Software Update Manager; otherwise, there is a potential risk to the project timeline.</p>\r\n<p><strong>&#160; 2. Enabling the Simplification Item Checks</strong></p>\r\n<p style=\"padding-left: 30px;\">2.1. Note Implementation</p>\r\n<p style=\"padding-left: 60px;\">Depending on the target software level of your system conversion or release upgrade, specific&#160;<strong>minimum versions</strong>&#160;of this SAP Note (2399707) and SAP Note&#160;<a target=\"_blank\" href=\"/notes/2502552\">2502552</a>&#160;are required. Otherwise, the check may be incomplete or inaccurate.</p>\r\n<p style=\"padding-left: 60px;\">The minimum versions are:</p>\r\n<ul>\r\n<ul>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>SAP S/4HANA 2021 Initial Shipment</li>\r\n<ul style=\"list-style-type: circle;\">\r\n<li>2399707: Minimum recommended version 147 (Minimum technical version 82)</li>\r\n<li>2502552: version 90</li>\r\n</ul>\r\n<li>SAP S/4HANA 2021 Feature Package 1</li>\r\n<ul>\r\n<li><span style=\"font-size: 14px;\">2399707: Minimum recommended version 147 (Minimum technical version 82)</span></li>\r\n<li>2502552: version 90</li>\r\n</ul>\r\n<li>SAP S/4HANA 2022&#160;Initial Shipment</li>\r\n<ul>\r\n<li>2399707: Minimum recommended version 154 (Minimum technical version 82)</li>\r\n<li>2502552: version 97</li>\r\n</ul>\r\n<li>SAP S/4HANA 2022&#160;Feature Package 1</li>\r\n<ul>\r\n<li>2399707: Minimum recommended version 155 (Minimum technical version 82)</li>\r\n<li>2502552: version 97</li>\r\n</ul>\r\n<li>SAP S/4HANA 2022&#160;Feature Package 2</li>\r\n<ul>\r\n<li>2399707: Minimum recommended version 155 (Minimum technical version 82)</li>\r\n<li>2502552: version 97</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">While the minimum technical version of SAP Note&#160;2399707 enables the conversion process to proceed, the minimum version may not reveal all application-level issues. As a result, we advise using at least the recommended version referenced above.</p>\r\n<p style=\"padding-left: 60px;\">Regardless of the minimum note version, when starting your project, it is recommended to&#160;<strong>use the most recent version of SAP Notes 2399707 and 2502552</strong>. When you reach the hard-freeze phase in your project, you should also freeze the version of these notes.</p>\r\n<p style=\"padding-left: 30px;\">2.2.&#160;Simplification Item Catalog Maintenance</p>\r\n<p style=\"padding-left: 60px;\">The simplification item catalog contains a list of generally available target product versions and documented simplification items. A local replica is imported into the system to support the execution of the simplification item checks.</p>\r\n<p style=\"padding-left: 60px;\">The initial implementation of the check report (/SDF/RC_START_CHECK) includes the most recent version of the simplification item catalog. While continuing to scope and plan the project, we recommend using an up-to-date version of the simplification item catalog. Since conversion projects can take some time, you should consider and plan catalog updates during the project.</p>\r\n<p style=\"padding-left: 60px;\">While we advise you to use the most recent simplification item catalog content in early project phases, you should freeze and synchronize this content as part of the hard-freeze phase in your project.&#160;As you prepare to enter the hard-freeze phase of your project,&#160;<strong>download the local replica of the simplification item catalog&#160;</strong>from the system where you plan to perform your final test conversion. You can download the catalog using the corresponding button on the /SDF/RC_START_CHECK selection screen. We recommend downloading the content before converting the system. Similarly, you can then upload this content using report /SDF/RC_START_CHECK to synchronize the version before performing checks in any subsequent systems.</p>\r\n<p style=\"padding-left: 60px;\">2.2.1. Updating the Simplification Item Catalog</p>\r\n<p style=\"padding-left: 90px;\">The SAP hosted version of the simplification item catalog is updated periodically to accommodate new product versions, introduce new simplification items, and integrate lessons learned from project experience. By default, the report /SDF/RC_START_CHECK does not automatically update the local replica of the simplification item catalog from SAP servers. If you want to update the content from the SAP servers, you explicitly need to trigger this via the&#160;<em>Update catalog with latest version from SAP</em>&#160;button in /SDF/RC_START_CHECK.</p>\r\n<p style=\"padding-left: 90px;\">If no connection exists between the system and the SAP support backbone, you could download the content as an archive directly from the&#160;<a target=\"_blank\" href=\"https://me.sap.com/sic\">simplification item catalog</a>&#160;site. Once you download the archive to your local machine, you can manually upload it in /SDF/RC_START_CHECK. The&#160;<em>Local Version</em>&#160;will be marked with a watch icon when the local replica is over 30 days old.</p>\r\n<p style=\"padding-left: 90px;\">The available functions for managing the local replica in /SDF/RC_START_CHECK are:</p>\r\n<ul>\r\n<ul>\r\n<ul style=\"list-style-type: disc;\">\r\n<li><em>Update Catalog with latest version from SAP:&#160;</em>Used to directly download the latest catalog version from SAP to update the local replica.</li>\r\n<li><em>Upload Simplification Item Catalog from file</em>: Used to upload a version of the catalog stored on your local machine. This option supports environments without direct connectivity to the SAP support backbone. Additionally, it supports synchronizing the catalog version across systems.&#160;</li>\r\n<li><em>Download Current Simplification Item Catalog</em>: Used to download a copy of the local replica version to your local machine as a backup before updating the catalog. It also supports synchronizing the catalog version across systems.</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<p><strong>&#160; 3. Executing the Simplification Item Checks<br /></strong></p>\r\n<p style=\"padding-left: 30px;\">As stated previously, the simplification item checks (that is, the relevance check and the consistency check) are available via both the data collection framework for SAP Readiness Check (using report RC_COLLECT_ANALYSIS_DATA) and manually (using report /SDF/RC_START_CHECK).</p>\r\n<p style=\"padding-left: 30px;\">When running the simplification item checks close to the technical execution of the conversion from SAP ERP to SAP S/4HANA or the upgrade to a higher SAP S/4HANA product version, it is essential to follow the guidance below.&#160;<strong>Otherwise</strong>,&#160;the technical conversion or upgrade could end with an error, and you may have to&#160;<strong>reset and</strong>&#160;<strong>repeat the Software Update Manager procedure.</strong>&#160;If forced to reset Software Update Manager, update this SAP Note (2399707) and SAP Note&#160;<a target=\"_blank\" href=\"/notes/2502552\">2502552</a>, repeat the analysis, and resolve any new issues found before restarting the Software Update Manager procedure.</p>\r\n<p style=\"padding-left: 30px;\">3.1.&#160;Using the Data Collection Framework for SAP Readiness Check</p>\r\n<p style=\"padding-left: 60px;\">To enable the data collection framework to analyze the simplification item checks, enable the related options within the selection screen of report RC_COLLECT_ANALYSIS_DATA. The data collection framework includes the following options related to simplification items:</p>\r\n<ul>\r\n<ul style=\"list-style-type: disc;\">\r\n<li><em>Simplification Item and Compatibility Scope relevance</em>: By default, this check is active. We recommend performing this analysis on each of the system&#8217;s productive clients. Note that each client requires a separate analysis on the landing page for SAP Readiness Check.</li>\r\n<li><em>Simplification Item Effort Drivers</em>: This check is not enabled by default as it takes some additional time to collect the related information. However, we recommend enabling this analysis for clarity on the effort required to remediate simplification items that are ranked<em>&#160;potentially high</em>. Additionally, by analyzing the effort drivers, the default effort ranking for some&#160;<em>potentially high</em>&#160;simplification items may be reclassified, further supporting the project&#8217;s scoping and planning.</li>\r\n<li><em>Simplification Item Consistency</em>: This check should be performed within client 000, as this is the same client where Software Update Manager will analyze the system. Log on to client 000 and clear all other options within RC_COLLECT_ANALYSIS_DATA to enable this check. The relevance check will be automatically added to the scope when enabling the consistency check. You can then extend the analysis of the production client by updating it with the archive produced by the consistency check in client 000. Select the&#160;<em>Update Analysis</em>&#160;button on the dashboard view to add the archive.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">3.2.&#160;Executing the Checks Manually</p>\r\n<p style=\"padding-left: 60px;\">These are the steps required to execute the simplification item checks manually:</p>\r\n<ol><ol>\r\n<li>Start report&#160;/SDF/RC_START_CHECK&#160;in transaction SA38.</li>\r\n<li>In the&#160;<em>Simplification Item Check Options</em>&#160;section, choose the target SAP S/4HANA product version.</li>\r\n</ol></ol>\r\n<p style=\"padding-left: 90px;\">Note: If the target product version is not available in the list, follow the steps above to update the local replica of the simplification item catalog. Only released-to-customer product versions are visible in the list.</p>\r\n<ol><ol start=\"3\">\r\n<li>Choose the mode you want to perform the check:</li>\r\n</ol></ol><ol>\r\n<ul>\r\n<ul style=\"list-style-type: disc;\">\r\n<li><em>New relevance check in Online mode</em>: The result will be displayed immediately after the check completes.</li>\r\n<li><em>New relevance &amp; consistency check as background job</em>: We recommend this option when you expect a long runtime or you are uncertain of the runtime.</li>\r\n</ul>\r\n</ul>\r\n</ol><ol><ol start=\"4\">\r\n<li>Execute the check to receive the simplification item list.</li>\r\n<li>Review the results (see below).</li>\r\n</ol></ol>\r\n<p><strong>&#160; 4. Reviewing the Results in /SDF/RC_START_CHECK</strong></p>\r\n<p style=\"padding-left: 30px;\">When using the data collection framework for SAP Readiness Check to execute the simplification item checks, the results are collected in the generated archive and can be uploaded to the landing page for SAP Readiness Check:&#160;<a target=\"_blank\" href=\"https://me.sap.com/readinesscheck\">https://me.sap.com/readinesscheck</a>.</p>\r\n<p style=\"padding-left: 30px;\">Independently of how you initiated the simplification items evaluation, /SDF/RC_START_CHECK can be used to review the detailed results. To find the results, log on to client 000, start report /SDF/RC_START_CHECK, select the targeted SAP S/4HANA product version, then select the&#160;<em>Display Last Check Result</em>&#160;option from the&#160;<em>Simplification Item Check Options&#160;</em>section<em>,</em>&#160;and then choose&#160;<em>Execute</em>.</p>\r\n<p style=\"padding-left: 30px;\">Solve any errors identified by the consistency check, as this is&#160;<strong>mandatory</strong>&#160;for any conversion or upgrade to SAP S/4HANA 1809 or higher.</p>\r\n<p style=\"padding-left: 30px;\">4.1.&#160;Interpreting the Check Results</p>\r\n<p style=\"padding-left: 60px;\">Within the check result view, you will find the following:</p>\r\n<ul>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>Basic information about a simplification item, including application area, ID, title, application component, category, and business impact note.</li>\r\n<li>Indication of whether the simplification item is relevant to the system or not.</li>\r\n<li>Results of the most recent consistency check, with an indication of whether the system is consistent, related to the simplification item, or not. Select the simplification item and choose&#160;<em>Display Consistency Check Log</em>&#160;to find the latest result and learn how to resolve the inconsistency.</li>\r\n<li>Indication of whether an exemption is possible for those simplification items ending in error. Some simplification items only require the acknowledgment of the customer. You need only apply an exemption to the simplification item in such cases. In doing so, you acknowledge that you have read and understood the message. Once an item is exempt, it will no longer block the conversion to the corresponding target SAP S/4HANA product version.</li>\r\n<li>A summary of the relevancy assessment of the simplification item.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">4.2. Administrative Functions within the Check Results View</p>\r\n<p style=\"padding-left: 60px;\">From the check results view, various operations regarding the consistency check are possible, including:</p>\r\n<ul>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>Initiate a consistency check for all relevant items.</li>\r\n<li>Perform a detailed consistency check for a selected item.</li>\r\n<li>Display the consistency check log.</li>\r\n<li>Apply or revoke exemption for consistency check errors able to be skipped.</li>\r\n<li>Display exemption log.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>&#160; 5. Additional Information</strong></p>\r\n<p style=\"padding-left: 30px;\">5.1. Executing simplification item checks for a newly released SAP S/4HANA Product Version, Feature Package Stack, or Support Package Stack</p>\r\n<p style=\"padding-left: 60px;\">Suppose you encounter the issue where you cannot see the newly released SAP S/4HANA feature package stack or support package stack in the target list. In that case, you need to update the simplification item catalog to the latest version available from SAP. See section 2.2.1 for more information.</p>\r\n<p style=\"padding-left: 30px;\">5.2.&#160;Framework for storing and managing the simplification item check logs</p>\r\n<p style=\"padding-left: 60px;\">The simplification item checks use the application log to retain the check logs. These logs are client-specific; therefore, you must ensure you are in the correct client when analyzing the check logs.</p>\r\n<p style=\"padding-left: 60px;\">The check logs produced by Software Update Manager are only available in client 000.</p>\r\n<p style=\"padding-left: 30px;\">5.3.&#160;Adding an exemption for an inconsistent simplification item</p>\r\n<p style=\"padding-left: 60px;\">The simplification item check only supports the exemption of an item when the consistency check return code is 7.</p>\r\n<p style=\"padding-left: 60px;\">When creating the exemption, you must make sure the selected target version in /SDF/RC_START_CHECK is the same as the target product version processed by Software Update Manager.</p>\r\n<p style=\"padding-left: 30px;\">5.4.&#160;Using a&#160;central&#160;download service system to download simplification item catalog updates</p>\r\n<p style=\"padding-left: 60px;\">If you use a central download service system as a communication server and the ST-PI version in the landscape is less than or equal to ST-PI 2008_1_700 SP27, ST-PI 2008_1_710 SP27, or ST-PI 740 SP17, you need to implement this SAP Note (2399707)&#160;in the central download service system.</p>\r\n<p style=\"padding-left: 60px;\">Additionally, you need to implement SAP Note&#160;<a target=\"_blank\" href=\"/notes/2945785\">2945785</a>&#160;in the&#160;central download service system.</p>\r\n<p style=\"padding-left: 30px;\">5.5.&#160;Determining the relevant application component to use when creating an SAP incident related to a simplification item</p>\r\n<p style=\"padding-left: 60px;\">If you encounter an issue with a specific simplification item, it is best to create the customer support incident under the item&#8217;s relevant application component.</p>\r\n<p style=\"padding-left: 60px;\">The correct component can be identified by following these steps:</p>\r\n<ol><ol>\r\n<li>Open the URL&#160;<a target=\"_blank\" href=\"https://me.sap.com/sic\">https://me.sap.com/sic</a>&#160;with your S-User.</li>\r\n<li>Search the item with the Check item ID.</li>\r\n<li>Open the item.</li>\r\n<li>Go to the&#160;<em>Application Component</em>&#160;tab on the detail page.</li>\r\n</ol></ol>\r\n<p><strong>&#160; 6. Frequently Asked Questions</strong></p>\r\n<p style=\"padding-left: 30px;\">6.1.&#160;What do I need to check before implementing this SAP Note (2399707)?</p>\r\n<ul>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>We&#160;<strong>strongly recommend&#160;</strong>checking that&#160;the latest version of SAP Note&#160;<a target=\"_blank\" href=\"/notes/1668882\">1668882</a>&#160;is &#8220;completely implemented&#8221; before you implement this SAP Note.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\">If you encounter an issue while installing or updating this SAP Note (2399707), for example, syntax error after implementation, perform the following steps:</p>\r\n<ul>\r\n<ul>\r\n<ul style=\"list-style-type: circle;\">\r\n<li>Reset the implementation of this SAP Note (2399707) using transaction SNOTE.</li>\r\n<li>Implement the latest version of&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/1668882\">1668882</a>.</li>\r\n<li>Exit the transaction SNOTE.</li>\r\n<li>Open SNOTE and reimplement this SAP Note (2399707).</li>\r\n</ul>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul style=\"list-style-type: disc;\">\r\n<li>When encountering the runtime error DDIC_TYPELENG_INCONSISTENT with report /SDF/RC_START_CHECK, first check to see whether&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/1229062\">1229062</a>&#160;is valid for the system.&#160;If the SAP Note is not applicable, manually activate the following DDIC structures: SWNCAGGTASKTYPE, SWNCAGGUSERTCODE, SWNCAGGUSERWORKLOAD, and SWNCAGGTASKTIMES.</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">6.2.&#160;Why do I get syntax errors when activating the objects in this SAP Note (2399707)?</p>\r\n<p style=\"padding-left: 60px;\">Usually, this is because the code context for the correction instruction is inconsistent. You can reset the implementation of this SAP Note (2399707) and then reimplement it.</p>\r\n<p style=\"padding-left: 60px;\">If this process does not resolve the issue, create a customer support incident under the component CA-TRS-PRCK.</p>\r\n<p style=\"padding-left: 30px;\">6.3.&#160;Where can I find the SAP Note for performing the simplification item check for SAP Readiness Check for SAP BW/4HANA?</p>\r\n<p style=\"padding-left: 60px;\">The SAP Readiness Check for SAP BW/4HANA approach evolved from analyzing a list of simplification items to evaluating the objects within the SAP BW system. Now, simplification item-related SAP Notes are available in the&#160;<em>Learn More</em>&#160;text and the tables provided within the detailed views.</p>\r\n<p style=\"padding-left: 30px;\">6.4.&#160;Is it possible to skip a simplification item consistency check?</p>\r\n<p style=\"padding-left: 60px;\">Yes, it is technically possible to skip a simplification item consistency check. However, you must create a customer support incident under the component CA-TRS-TDBT requesting access to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2641675\">2641675</a>.</p>\r\n<p style=\"padding-left: 30px;\">6.5.&#160;How can I resolve the warning in /SDF/RC_START_CHECK that I cannot fetch the simplification item catalog through the SAP-SUPPORT_PORTAL HTTP connection?</p>\r\n<p style=\"padding-left: 60px;\">This issue arises when the SAP-SUPPORT_PORTAL RFC destination is not working. Reference the steps in SAP Note&#160;<a target=\"_blank\" href=\"/notes/91488\">91488</a>&#160;to resolve the communication issue.</p>\r\n<p style=\"padding-left: 60px;\">Alternatively, you can use the manual download and upload process described above to update the local simplification item catalog replica.</p>\r\n<p style=\"padding-left: 30px;\">6.6.&#160;In which client should I perform the simplification item checks?</p>\r\n<ul>\r\n<ul style=\"list-style-type: disc;\">\r\n<li><strong>Relevance Check</strong>: We recommend analyzing each of the system&#8217;s productive clients. If there is more than one productive client in the system, we recommend evaluating each client. Note that results from each client will require a separate analysis to display the results.&#160;</li>\r\n<li><strong>Consistency Check</strong>: Client 000 is the recommended client, as this is the same client the Software Update Manager will evaluate during the technical conversion. These results can enhance a system&#8217;s analysis when you upload them to the analysis results of the productive client.</li>\r\n</ul>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Version</strong></td>\r\n<td><strong>Change</strong></td>\r\n</tr>\r\n<tr>\r\n<td>165</td>\r\n<td>\r\n<p>Bugfix: The results from the first check didn't automatically sort when clicking \"Display Last Check Result\".</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>164</td>\r\n<td>\r\n<p>Includes ST-PI 740 SP25 and ST-PI 2008_1_700 SP35 into validation component.</p>\r\n<p>You don't need update to this version if your SP is smaller than above.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>162/163</td>\r\n<td>\r\n<p>Bugfix: check class is available in the system&#160;but got error 'not found'.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>161</td>\r\n<td>\r\n<p>Includes ST-PI 740 SP24 and ST-PI 2008_1_700 SP34 into validation component.</p>\r\n<p>You don't need update to this version if your SP is smaller than above.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>160</td>\r\n<td>\r\n<p>Add the stack into source release adjustment.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>158/159</td>\r\n<td>\r\n<p>Bugfix: when relevant simple check results contains MISS_ST03N or RULE_ISSUE, the relevent check result may not correct.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>157</td>\r\n<td>\r\n<p>ST-PI 740 SP21 and ST-PI 2008_1_700/2008_1_710 SP31 will include the code of this version.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>156</td>\r\n<td>\r\n<p>Enhance the SUM log if the target product version does not exist in Simplification Item check framework.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>155</td>\r\n<td>\r\n<p>Enhance note check text for SAP Note 2399707. ST-PI 740 SP20 and ST-PI 2008_1_700/2008_1_710 SP30 will include the code of this version.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>153/154</td>\r\n<td>\r\n<p>Enhance the application log text for note check of consistency check.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>152</td>\r\n<td>\r\n<p>Bugfix: if \"New relevance &amp; consistency check as background job\" selected and click \"Execute in Background\" (F9) in the menu, there's&#160; too much batch jobs \"RC_NEW_CHECK_IN_JOB\" created.</p>\r\n<p>Change the note description.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>151</td>\r\n<td>\r\n<p>Bugfix: replace the SY-DATUM with the date value when checking DB.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>150</td>\r\n<td>\r\n<p>Fix the&#160;bug which from download service server.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>148/149</td>\r\n<td>\r\n<p>Enhance the IDoc relevance check performance issue. The client number is added in the select SQL statement to avoid performance issue in some system environments.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>146/147</td>\r\n<td>\r\n<p>Fix the IDoc relevance check bug that the check result is not correct when exectued in different client.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>145</td>\r\n<td>\r\n<p>Remove the&#160;SAP S/4HANA ON-PREMISE 1511 from target version list since it's out of&#160;maintenance.</p>\r\n<p>Add the application log \"commit work\" statement for performance optimization.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>144</td>\r\n<td>\r\n<p>Fix bug: the xml format error when update catalog with latest version from SAP.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>143</td>\r\n<td>\r\n<p>Fix bug: Class /SDF/CL_RC_CHK_UTILITY, Public Section, Field \"ICON_CHECKED\" is unknown.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>142</td>\r\n<td>\r\n<p>Fix bug: avoid creating too many work processes when download service is active</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>141</td>\r\n<td>\r\n<p>Update the note description to add the&#160;minimum recommendation for SAP S/4HANA 2020 Feature Package 1.</p>\r\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>140</td>\r\n<td>\r\n<p>Bugfix: SAP S/4HANA 1709 is still supported for a system upgrade, do not show error message when upgrade to 1709.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>139</td>\r\n<td>\r\n<p>Includes ST-PI 740 SP14 and ST-PI 2008_1_700 SP24 into validation component.</p>\r\n<p>You don't need update to this version if your SP is smaller than above.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>138</td>\r\n<td>\r\n<p>Enhance error message for consistency check since that the target release SAP S/4HANA 1709 is no longer supported for a system conversion.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>137</td>\r\n<td>\r\n<p>Update the note description to add the&#160;minimum recommendation for SAP S/4HANA 1909 Feature Package 2 and SAP S/4HANA 2020 Initial Shipment.</p>\r\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>136</td>\r\n<td>\r\n<p>Fix issue: Type \"CL_ABAP_DYN_PRG\" is unknown.</p>\r\n<p>You can ignore this version if you don't have this issue.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>134/135</td>\r\n<td>\r\n<p>Support Download Service to update the Simplification Item Catalog. Please check more description of this issue in SAP Note <a target=\"_blank\" href=\"/notes/2882166\">2882166</a>.</p>\r\n<p>You can ignore this version if you are not using&#160;Download Service to&#160;connect to the&#160;SAP Support Backbone.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>133</td>\r\n<td>\r\n<p>Fix bug: do not block the process when ICM is unavailable.</p>\r\n<p>You can ignore this version if&#160;ICM service is active.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>132</td>\r\n<td>\r\n<p>Fix bug: the consistency result can not show the latest result for items with&#160;exemption applied.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>131</td>\r\n<td>\r\n<p>Improve the performance of cluster table checking if the database is Sybase.</p>\r\n<p>You can ignore this version if your&#160;database isn't Sybase.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>130</td>\r\n<td>\r\n<p>Fix bug: The program is blocked when updating BALSUB.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>129</td>\r\n<td>\r\n<p>Use SAP-SUPPORT_PORTAL HTTP connection instead of SAPOSS RFC connection. Note requirement check works and Simplification Item Catalog content can automatically update from SAP servers in this version.&#160;Please check more description of this issue in&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/2882166\">2882166</a>.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>128</td>\r\n<td>\r\n<p>Remove remote note requirement check because SAPOSS does not work anymore.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>127</td>\r\n<td>\r\n<p>Includes ST-PI 740 SP level 12 into validation component. You don't need update to this version if your ST-PI 740 SP is smaller than SP12.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>126</td>\r\n<td>\r\n<p>Update&#160;Note description and enhance&#160;ST03N data collection.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>125</td>\r\n<td>\r\n<p>Add new fields \"LoB/Technology\" and \"Business Area\" into check result; Remove usage of CL_ABAP_DYN_PRG.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>123/124</td>\r\n<td>\r\n<p>Includes ST-PI 740 SP level 11 into validation component. You don't need update to this version if your ST-PI 740 SP is smaller than SP11.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>122</td>\r\n<td>\r\n<p>Enhance error message for consistency check; Update note text for minimum version requirement; Fix bug: filter lost when resizing check result</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>120-121</td>\r\n<td>\r\n<p>Fix bug: Stop&#160;<em>SUM-Phase: PREP_EXTENSION/RUN_S4H_SIF_CHECK_INIT even if the return code is 4.</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>118/119</td>\r\n<td>\r\n<p>Add Simplification Item Consistency support for Readiness Check 2.0.</p>\r\n<p>Add SUM return code support and write the SUM log when error occurs in SUM mode.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>114/115/116/117</td>\r\n<td>\r\n<p>Change ST03N data check:&#160;Do the entry point check if the ST03N entries are available.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>113</td>\r\n<td>\r\n<p>Enhance performance issue for IDoc relevance check.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>109&#126;110</td>\r\n<td>\r\n<p>Bugfix:</p>\r\n<ul>\r\n<li>Right propagation of highest return code (8 + 7 + applied exemption is still rc=8)</li>\r\n<li>Check for the missing putstatus &#8220;I&#8221; to get the correct SUM phase</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>106&#126;107</td>\r\n<td>\r\n<p>Bugfix: the check result is wrong when check the table with DATS or TIMS data type relevant condition.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>104&#126;105</td>\r\n<td>\r\n<p>Bugfix for relevance check.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>103</td>\r\n<td>\r\n<p>Update the note title and description. <strong>SAP Note&#160;2502552 is not required for SAP Readiness Check</strong>. It is only for consistency check.</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>100</td>\r\n<td>\r\n<p>Update the note description to add the&#160;minimum recommendation for SAP S/4HANA 1809 Initial Shipment.</p>\r\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I333244)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I333244)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002399707/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002399707/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002399707/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002399707/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002399707/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002399707/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002399707/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002399707/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002399707/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2568736", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA - copy ST03N data", "RefUrl": "/notes/2568736"}, {"RefNumber": "2502552", "RefComponent": "CA-TRS-PRCK", "RefTitle": "S4TC - SAP S/4HANA Conversion & Upgrade new Simplification Item Checks", "RefUrl": "/notes/2502552"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2741967", "RefComponent": "FIN-MIG", "RefTitle": "How to plan and execute the financial data migration during conversion of SAP ERP solution to SAP S/4HANA", "RefUrl": "/notes/2741967 "}, {"RefNumber": "3340526", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2023 - application specific notes in system conversion / release upgrade follow-on phase", "RefUrl": "/notes/3340526 "}, {"RefNumber": "3340515", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2023 - application specific notes in system conversion / release upgrade preparation phase", "RefUrl": "/notes/3340515 "}, {"RefNumber": "3226469", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2022 - application specific notes in system conversion / release upgrade preparation phase", "RefUrl": "/notes/3226469 "}, {"RefNumber": "3226548", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2022  - application specific notes in system conversion / release upgrade follow-on phase", "RefUrl": "/notes/3226548 "}, {"RefNumber": "3079695", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 2021 - application specific notes in system conversion / upgrade follow-on phase", "RefUrl": "/notes/3079695 "}, {"RefNumber": "2665372", "RefComponent": "PA-ER", "RefTitle": "S4TC SAP E-Recruiting Check for S/4HANA System Conversion", "RefUrl": "/notes/2665372 "}, {"RefNumber": "2951715", "RefComponent": "EHS-SAF-GLM", "RefTitle": "S4SIC - Delete Transport Based Installation of TDAGGF", "RefUrl": "/notes/2951715 "}, {"RefNumber": "2951635", "RefComponent": "EHS-BD-TLS", "RefTitle": "S4SIC - Delete Transport Based Installation of TDAGWI", "RefUrl": "/notes/2951635 "}, {"RefNumber": "2939271", "RefComponent": "EHS-SRC", "RefTitle": "S4SIC - Add Support for De-Installation of TDAGBCA by SUM", "RefUrl": "/notes/2939271 "}, {"RefNumber": "2931193", "RefComponent": "CA-TRS-PRCK", "RefTitle": "Explanation of Simplification Item Categories", "RefUrl": "/notes/2931193 "}, {"RefNumber": "2906603", "RefComponent": "SV-SMG-DVM", "RefTitle": "Financial Data Quality: Central Preparation Note", "RefUrl": "/notes/2906603 "}, {"RefNumber": "2816352", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909 - application specific notes in system conversion / upgrade preparation phase", "RefUrl": "/notes/2816352 "}, {"RefNumber": "2816275", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1909 - application specific notes in system conversion / upgrade follow-on phase", "RefUrl": "/notes/2816275 "}, {"RefNumber": "2758146", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA & Process Discovery (evolution of SAP Business Scenario Recommendations) or SAP Innovation and Optimization Pathfinder", "RefUrl": "/notes/2758146 "}, {"RefNumber": "2669901", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1809 - application specific notes in system conversion / upgrade follow-on phase", "RefUrl": "/notes/2669901 "}, {"RefNumber": "2669982", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1809 - application specific notes in system conversion / upgrade preparation phase", "RefUrl": "/notes/2669982 "}, {"RefNumber": "2677010", "RefComponent": "IS-M", "RefTitle": "SAP Media in ERP / ECC 6.X and SAP S/4HANA", "RefUrl": "/notes/2677010 "}, {"RefNumber": "2598422", "RefComponent": "CA-TRS-PRCK", "RefTitle": "S4TC - SAP S/4HANA 1709 FPS02 Conversion & Upgrade - TCI Note #4", "RefUrl": "/notes/2598422 "}, {"RefNumber": "2568736", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA - copy ST03N data", "RefUrl": "/notes/2568736 "}, {"RefNumber": "2502552", "RefComponent": "CA-TRS-PRCK", "RefTitle": "S4TC - SAP S/4HANA Conversion & Upgrade new Simplification Item Checks", "RefUrl": "/notes/2502552 "}, {"RefNumber": "2495932", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709 - application specific notes in system conversion / upgrade follow-on phase", "RefUrl": "/notes/2495932 "}, {"RefNumber": "2495992", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA 1709 - application specific notes in system conversion / upgrade preparation phase", "RefUrl": "/notes/2495992 "}, {"RefNumber": "2477318", "RefComponent": "CA-MDG-CMP-MM", "RefTitle": "S4TC MDG_APPL Master Data Governance check for S/4 upgrade", "RefUrl": "/notes/2477318 "}, {"RefNumber": "2477311", "RefComponent": "CA-MDG-CMP-MM", "RefTitle": "S4TC MDG_MDC Master Data Consolidation check for S/4 upgrade", "RefUrl": "/notes/2477311 "}, {"RefNumber": "2314696", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "S4TC CEEISUT Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2314696 "}, {"RefNumber": "2323221", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "CEEISUT Retrofit: Business Functions", "RefUrl": "/notes/2323221 "}, {"RefNumber": "2310438", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA - Managed System (Obsolete)", "RefUrl": "/notes/2310438 "}, {"RefNumber": "2290622", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA", "RefUrl": "/notes/2290622 "}, {"RefNumber": "2455870", "RefComponent": "IS-M-MD", "RefTitle": "S4TC transfer after S/4HANA simplification item check in the Media industry solution", "RefUrl": "/notes/2455870 "}, {"RefNumber": "2445069", "RefComponent": "PPM-PRO", "RefTitle": "S4TC CPRXRPM Master Check for S/4 System Conversion Checks  (new Simplification Item Checks)", "RefUrl": "/notes/2445069 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-PI", "From": "2008_1_700", "To": "2008_1_700", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "2008_1_710", "To": "2008_1_710", "Subsequent": ""}, {"SoftwareComponent": "ST-PI", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDS", "URL": "/supportpackage/SAPKITLRDS"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPK-70036INSTPI", "URL": "/supportpackage/SAPK-70036INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDW", "URL": "/supportpackage/SAPKITLRDW"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDX", "URL": "/supportpackage/SAPKITLRDX"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPK-70032INSTPI", "URL": "/supportpackage/SAPK-70032INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPK-70031INSTPI", "URL": "/supportpackage/SAPK-70031INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDY", "URL": "/supportpackage/SAPKITLRDY"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPKITLRDZ", "URL": "/supportpackage/SAPKITLRDZ"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPK-70030INSTPI", "URL": "/supportpackage/SAPK-70030INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPK-70028INSTPI", "URL": "/supportpackage/SAPK-70028INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_700", "SupportPackage": "SAPK-70029INSTPI", "URL": "/supportpackage/SAPK-70029INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLRES", "URL": "/supportpackage/SAPKITLRES"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLREW", "URL": "/supportpackage/SAPKITLREW"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLREX", "URL": "/supportpackage/SAPKITLREX"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLREY", "URL": "/supportpackage/SAPKITLREY"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPKITLREZ", "URL": "/supportpackage/SAPKITLREZ"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPK-71028INSTPI", "URL": "/supportpackage/SAPK-71028INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPK-71029INSTPI", "URL": "/supportpackage/SAPK-71029INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPK-71030INSTPI", "URL": "/supportpackage/SAPK-71030INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPK-71031INSTPI", "URL": "/supportpackage/SAPK-71031INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPK-71032INSTPI", "URL": "/supportpackage/SAPK-71032INSTPI"}, {"SoftwareComponentVersion": "ST-PI 2008_1_710", "SupportPackage": "SAPK-71036INSTPI", "URL": "/supportpackage/SAPK-71036INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74026INSTPI", "URL": "/supportpackage/SAPK-74026INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74022INSTPI", "URL": "/supportpackage/SAPK-74022INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74021INSTPI", "URL": "/supportpackage/SAPK-74021INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74020INSTPI", "URL": "/supportpackage/SAPK-74020INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74019INSTPI", "URL": "/supportpackage/SAPK-74019INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74018INSTPI", "URL": "/supportpackage/SAPK-74018INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74017INSTPI", "URL": "/supportpackage/SAPK-74017INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74016INSTPI", "URL": "/supportpackage/SAPK-74016INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74015INSTPI", "URL": "/supportpackage/SAPK-74015INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74014INSTPI", "URL": "/supportpackage/SAPK-74014INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74013INSTPI", "URL": "/supportpackage/SAPK-74013INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74009INSTPI", "URL": "/supportpackage/SAPK-74009INSTPI"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-PI", "NumberOfCorrin": 14, "URL": "/corrins/0002399707/212"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; ST-PI&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Support Tools&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 2008_1_700&nbsp;&nbsp; SAPKITLRD1 - SAPKITLRD1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 2008_1_710&nbsp;&nbsp; SAPKITLRE1 - SAPKITLRE1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/><B>Caution</B>: You have to perform this manual activity separately in each system into which you transport the Note for  implementation.<br/></P> <UL><LI>Open transaction SE80</LI></UL> <UL><LI>Create package '/SDF/MAIN_7X' under parent package ' BSTPI_STRUCTURE '.  Select package 'BSTPI_STRUCTURE'; right click and choose 'Create' then  'Development Coordination' and then 'Package' to open the package creation popup.</LI></UL> <UL><UL><LI>Specify '/SDF/MAIN_7X' in the 'Package' field</LI></UL></UL> <UL><UL><LI>Specify 'Main Package for 7.x developments' in the 'Short Description' field</LI></UL></UL> <UL><UL><LI>Specify 'SV-SMG-SDD' in the 'Application component' field</LI></UL></UL> <UL><UL><LI>Specify 'ST-PI' in the 'Software Component' field</LI></UL></UL> <UL><UL><LI>Specify 'SAP' in the 'Transport Layer' field</LI></UL></UL> <UL><UL><LI>Specify 'Main Package' in the 'Package Type' field</LI></UL></UL> <P></P> <UL><LI>Create package '/SDF/STPI_7X' under parent package '/SDF/MAIN_7X'.  Select package '/SDF/MAIN_7X '; right click and choose 'Create' then  'Development Coordination' and then 'Package' to open the package creation popup.</LI></UL> <UL><UL><LI>Specify ' /SDF/STPI_7X' in the 'Package' field</LI></UL></UL> <UL><UL><LI>Specify '7.x developments in ST-PI' in the 'Short Description' field</LI></UL></UL> <UL><UL><LI>Specify 'SV-SMG-SDD' in the 'Application component' field</LI></UL></UL> <UL><UL><LI>Specify 'ST-PI' in the 'Software Component' field</LI></UL></UL> <UL><UL><LI>Specify 'SAP' in the 'Transport Layer' field</LI></UL></UL> <UL><UL><LI>Specify 'Not a Main Package' in the 'Package Type' field</LI></UL></UL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 14, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "2008_1_700", "Number": "2399707 ", "URL": "/notes/2399707 ", "Title": "Simplification Item Check", "Component": "CA-TRS-PRCK"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_700", "ValidTo": "740", "Number": "2399707 ", "URL": "/notes/2399707 ", "Title": "Simplification Item Check", "Component": "CA-TRS-PRCK"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}