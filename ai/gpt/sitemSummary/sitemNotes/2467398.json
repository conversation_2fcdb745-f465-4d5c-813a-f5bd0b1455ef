{"Request": {"Number": "2467398", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 320, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018786572017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002467398?language=E&token=25BA5CC6E193BB802FFD1E46C75DBB9D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002467398", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002467398/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2467398"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.06.2017"}, "SAPComponentKey": {"_label": "Component", "value": "CO-PC-ACT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Actual Costing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Cost Controlling", "value": "CO-PC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Actual Costing", "value": "CO-PC-ACT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-PC-ACT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2467398 - CKM3 on MLDOC for S/4HANA: Cost Component Mark Up and inventory valuation not relevant"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In CKM3 you can select the field for 'Relevant for Stock Valuation'. It is not clear how the system reacts on the selection, especially for SIT scenario.</p>\r\n<p>The column for price in CKM3 is shown incorrectly in certain circumstances.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SIT</p>\r\n<p>MLDOCCCS</p>\r\n<p>Delta Profit</p>\r\n<p>Cross company stock transfer</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Program error and Design</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>1 Finish the following manual steps:</p>\r\n<ul>\r\n<li>Start the transaction SE11 and create the data element&#160;ML4H_SVREL_UI</li>\r\n<ul>\r\n<li>Set the Domain&#160;XFELD</li>\r\n<li>Input the 'Field Labels' as:</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 90px;\"><span style=\"font-size: 14px;\">Short: &#160; &#160; </span><span style=\"font-size: 14px;\">Disp. All</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Medium: &#160;</span><span style=\"font-size: 14px;\">Disp. All CCS</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Long: &#160; &#160; &#160;</span><span style=\"font-size: 14px;\">Display All Cost Components</span><br style=\"font-size: 14px;\" /><span style=\"font-size: 14px;\">Heading: D</span><span style=\"font-size: 14px;\">isplay All</span></p>\r\n<ul>\r\n<ul>\r\n<li>Activate it;</li>\r\n</ul>\r\n<li>Change the Data type&#160;FCML4H_CCS_DYNP_S with&#160;the transaction SE11</li>\r\n<ul>\r\n<li>Change the component type for SVREL from ML4H_SVREL to&#160;&#160;ML4H_SVREL_UI</li>\r\n<li>Activate it.</li>\r\n</ul>\r\n<li>Change the field&#160;FCML4H_CCS_DYNP_S-SVREL on the screen 55 in program SAPLCKM8H&#160;</li>\r\n<ul>\r\n<li>Open the graphic screen by pushing the button 'Go To Layout' (see attachment screen_55)</li>\r\n<li>Change the text from 'Relevant for Stock Valuation' to 'Display All Cost Components'</li>\r\n<li>Set the flag for SET Parameter to X in the tab 'Dict'</li>\r\n<li>Set the flag for GET parameter to X&#160;in the tab 'Dict'</li>\r\n<li>Activate it;</li>\r\n</ul>\r\n</ul>\r\n<p>2 Maintain the correction in this note.</p>\r\n<p>After the above steps the selection is changed from 'Relevant for Stock Valuation' to 'Display all Cost Components'.&#160;As default, the flag for&#160;'Display all Cost Components' is initial. As default, the cost components that are inventory valuation relevant(table TCKH3-BESBW) are shown in CKM3. The cost component for Delta Profit or Mark Up&#160;from SIT scenario&#160;is shown also if the user Id has the authority for it in this case.</p>\r\n<p>If you set the flag for 'Display all Cost Components', all of the cost components are shown in CKM3 independent on the relevance for inventory valuation.</p>\r\n<p>In case there isn't any other non-inventory-relevant cost components besides the Profit or Mark Up&#160;from SIT scenario, the flag for 'Display all Cost Components'&#160;is hidden on the UI.</p>\r\n<p>The column for price is shown correctly in CKM3.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D024275)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D024835)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002467398/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002467398/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002467398/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002467398/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002467398/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002467398/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002467398/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002467398/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002467398/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Screen_55.png", "FileSize": "63", "MimeType": "image/png", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900000940032017&iv_version=0010&iv_guid=6CAE8B3E8D4B1ED7949C50ADD492C0D4"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2474031", "RefComponent": "CO-PC-ACT", "RefTitle": "CKM3 CKMCCC CKMCCD Cost Component Split value missing for \"Settlement Previous Period\"", "RefUrl": "/notes/2474031 "}, {"RefNumber": "2662799", "RefComponent": "CO-PC-ACT", "RefTitle": "Short dump ITAB_DUPLICATE_KEY in CKMVFM when navigating to CKM3", "RefUrl": "/notes/2662799 "}, {"RefNumber": "2466333", "RefComponent": "CO-PC-ACT", "RefTitle": "CKM3 on MLDOC for S/4HANA: Mark Up and Cost Component not  relevant for inventory valuation", "RefUrl": "/notes/2466333 "}, {"RefNumber": "2354768", "RefComponent": "CO-PC-ACT", "RefTitle": "S4TWL - Technical Changes in Material Ledger with Actual Costing", "RefUrl": "/notes/2354768 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10103INS4CORE", "URL": "/supportpackage/SAPK-10103INS4CORE"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0002467398/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 15, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2021025 ", "URL": "/notes/2021025 ", "Title": "CKM3 on MLDOC for S/4HANA: Prices for cumulative lines not correct", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2160604 ", "URL": "/notes/2160604 ", "Title": "CKM3 on MLDOC: C+000 'Internal error. Inform your system administrator'", "Component": "CO-PC-ACT-AVR"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2227756 ", "URL": "/notes/2227756 ", "Title": "CKM3 on MLDOC for S/4HANA: back posting to production", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2360495 ", "URL": "/notes/2360495 ", "Title": "Various errors in CKM3 with MLDOC", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2378871 ", "URL": "/notes/2378871 ", "Title": "CKM3 on MLDOC of S4HANA: Diverse improvements", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2397090 ", "URL": "/notes/2397090 ", "Title": "CKM3 on S/4 HANA: Migration data", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2399635 ", "URL": "/notes/2399635 ", "Title": "CKM3 on S/4HANA: too many cost component splits", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2402600 ", "URL": "/notes/2402600 ", "Title": "CKM3 for S/4HANA on MLDOC: data not complete", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2419044 ", "URL": "/notes/2419044 ", "Title": "CKM3 for S/4HANA on MLDOC: WIP Reduction and Rest folder not complete", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2420465 ", "URL": "/notes/2420465 ", "Title": "CKM3 for S/4HANA on MLDOC: short dump with OUTPUTFIELD_TOO_SHORT", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2421873 ", "URL": "/notes/2421873 ", "Title": "CKM3 for S/4HANA on MLDOC: show &#x2018;not allocated&#x2019;,only if settlement is finished", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2425613 ", "URL": "/notes/2425613 ", "Title": "CKM3 for S/4HANA on MLDOC: summarization for consumption to process material  and navigation to MM doc", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2432037 ", "URL": "/notes/2432037 ", "Title": "CKM3 on MLDOC for S/4HANA : CKM3OLD and Closing History", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2436088 ", "URL": "/notes/2436088 ", "Title": "CKM3 on MLDOC for S/4HANA:  cost component split incomplete", "Component": "CO-PC-ACT"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2450226 ", "URL": "/notes/2450226 ", "Title": "CKM3 on MLDOC S/4HANA: AB/KB/EB zeros and CKMCCC", "Component": "CO-PC-ACT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}