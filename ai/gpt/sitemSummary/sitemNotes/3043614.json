{"Request": {"Number": "3043614", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 296, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000618492021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003043614?language=E&token=F81C49D1B4450E1096187516F90E68CE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003043614", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003043614/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3043614"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.05.2022"}, "SAPComponentKey": {"_label": "Component", "value": "CA-LT-MC"}, "SAPComponentKeyText": {"_label": "Component", "value": "S/4HANA Migration Cockpit"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Landscape Transformation", "value": "CA-LT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Migration Cockpit", "value": "CA-LT-MC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-LT-MC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3043614 - Modifiable Systems and Transport Concept, SAP S/4HANA Migration Cockpit - Migrate Data Directly From SAP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p id=\"\">You want to learn about the transport functionality.</p>\r\n<div class=\"longtext\" style=\"font-size: 100.01%;\">\r\n<p>You experience issues with transports or system modifiability.</p>\r\n<p>You cannot create a new project - the create button is inactive / greyed out.</p>\r\n<p>Message: Cannot edit project. Dev. package &lt; name of development package &gt; is not editable. See long text.</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">Migration Cockpit, DT, Direct Transfer, transport, client transfer, client copy, modifiable, non-modifiable, client, cross-client</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p id=\"\">The transport functionality and thus the relevance of the system modifiability for the approach Direct Transfer has been introduced with SAP S/4HANA release 2020.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>1) Transport</strong></p>\r\n<p>Pls. find details about the transport topic <a target=\"_blank\" href=\"http://help.sap.com/disclaimer?site=https://www.sap.com/documents/2021/04/fa1fa2d4-dc7d-0010-87a3-c30de2ffd8ff.html\">here</a>.</p>\r\n<p><strong>2) Details on system modifiability</strong></p>\r\n<p>Projects can only be created in systems that have the following settings:</p>\r\n<ul>\r\n<li>Changes must be permitted for objects in the system at global level, cross client level, and client-specific level.</li>\r\n<li>The development package must be assigned to a modifiable software component and a modifiable namespace.</li>\r\n</ul>\r\n<p>To check whether changes are permitted for objects in the system, proceed as follows:</p>\r\n<ol start=\"1\">\r\n<li>In transaction SCC4 (Display View &#8220;Clients&#8221;: Overview), select the relevant client.</li>\r\n<li>Under <em>Changes and Transports for Client-Specific Objects</em>, ensure that one of the following options is selected</li>\r\n</ol>\r\n<ul>\r\n<li><em>Automatic Recording of Changes</em></li>\r\n<li><em>Changes without Automatic Recording</em></li>\r\n<li><em>Changes without Automatic Recording, no transports allowed</em></li>\r\n</ul>\r\n<ol start=\"3\">\r\n<li>Under <em>Cross-Client Object Changes</em>, ensure that one of the following options is selected:</li>\r\n</ol>\r\n<ul>\r\n<li><em>Changes to Repository and Cross-Client Customizing Allowed</em></li>\r\n<li><em>No changes to cross-client customizing objects</em></li>\r\n</ul>\r\n<ol start=\"4\">\r\n<li>In transaction SE06 (Post Installation Actions for Transport Organizer), choose the button <em>System Change Option</em>. Under <em>System Change Options,</em> ensure that the value of the field <em>Global Setting</em> is set to <em>Modifiable</em>.</li>\r\n</ol>\r\n<p>The development package must be assigned to a modifiable software component and a modifiable namespace. To check whether the software component and namespace are modifiable, proceed as follows:</p>\r\n<ol start=\"1\">\r\n<li>In transaction SE06 (Post Installation Actions for Transport Organizer), choose the button <em>System Change Option</em>.</li>\r\n<li>Under <em>System Change Options</em>, ensure that the following options are selected:</li>\r\n</ol>\r\n<ul>\r\n<li>The software component assigned to the development package is set to <em>Modifiable</em>.</li>\r\n<li>The namespace assigned to the development package is set to <em>Modifiable.</em></li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-LT-PE (S/4HANA Migration Cockpit – Direct Transfer)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D019700)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D019700)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003043614/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003043614/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003043614/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003043614/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003043614/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003043614/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003043614/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003043614/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003043614/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3201966", "RefComponent": "CA-LT-PE", "RefTitle": "How to delete migration project in closed system?", "RefUrl": "/notes/3201966 "}, {"RefNumber": "3200212", "RefComponent": "CA-LT-MC", "RefTitle": "How to generate the migration object after transport to quality or productive system?", "RefUrl": "/notes/3200212 "}, {"RefNumber": "3048691", "RefComponent": "CA-LT-MC", "RefTitle": "Cannot create a new migration project with Direct Transfer method", "RefUrl": "/notes/3048691 "}, {"RefNumber": "3104790", "RefComponent": "CA-LT-MC", "RefTitle": "Modifiable Systems and Transport Concept, SAP S/4HANA Migration Cockpit - Migrate Data Using Staging Tables", "RefUrl": "/notes/3104790 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}