{"Request": {"Number": "758054", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 326, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004131092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=E13E47300B39F9DC693D574125593EF7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "758054"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.09.2004"}, "SAPComponentKey": {"_label": "Component", "value": "IS-HER-CM-GB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Great Britain"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Higher Education and Research", "value": "IS-HER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-HER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Student Lifecycle Management", "value": "IS-HER-CM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-HER-CM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Great Britain", "value": "IS-HER-CM-GB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-HER-CM-GB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "758054 - UCAS import - Reprocessing logic changes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>UCAS data is overwritten with old data when the sequence file is<br />reprocessed leading to database inconsistency.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Campus Management, UCAS, MARVIN, Reprocess applicant</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The old reprocessing logic could not anticipate and handle future<br />records for the same applicant.<br />The sequence file has to be imported before the reprocessing is<br />attempted.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><UL><UL><LI>Note: The below changes are applicable for 4.64 only.</LI></UL></UL> <UL><UL><LI>Create the new DDIC objects as shown in the attachement. The DDIC objects are applicable from Support Package SAPKIPQE21. OR Import the attached NOTE758054.SAR file for the new DDIC objects.</LI></UL></UL> <UL><UL><LI>Apply the code corrections as per the note.</LI></UL></UL> <UL><UL><LI>All the text elements can be created only after the program object has been created.</LI></UL></UL> <UL><UL><LI>The GUI interface can be created only after the program object has been created.</LI></UL></UL> <p></p> <UL><UL><LI>Note: The below changes are applicable for 4.72 only.</LI></UL></UL> <UL><UL><LI>Apply the correction.</LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-PS-CA (Public Sector Contract Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "I018736"}, {"Key": "Processor                                                                                           ", "Value": "I028426"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Note_758054_attachement.zip", "FileSize": "951", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000258152004&iv_version=0004&iv_guid=C174C9D2A95B6445B50C0D9973430D2C"}, {"FileName": "NOTE758054.SAR", "FileSize": "10", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000258152004&iv_version=0004&iv_guid=F06C9D4DFD727F4B8065F6537DD18022"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "762124", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Error for mapping Route B applicants during reprocessing", "RefUrl": "/notes/762124"}, {"RefNumber": "759348", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Handling student data for reprocessing logic", "RefUrl": "/notes/759348"}, {"RefNumber": "718645", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Error while performing UCAS (*C) file import", "RefUrl": "/notes/718645"}, {"RefNumber": "710597", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Error while performing UCAS file import", "RefUrl": "/notes/710597"}, {"RefNumber": "685144", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Error during updating ST-SC relationship in infotype 1001.", "RefUrl": "/notes/685144"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "762124", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Error for mapping Route B applicants during reprocessing", "RefUrl": "/notes/762124 "}, {"RefNumber": "759348", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Handling student data for reprocessing logic", "RefUrl": "/notes/759348 "}, {"RefNumber": "718645", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Error while performing UCAS (*C) file import", "RefUrl": "/notes/718645 "}, {"RefNumber": "710597", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Error while performing UCAS file import", "RefUrl": "/notes/710597 "}, {"RefNumber": "685144", "RefComponent": "IS-HER-CM-GB", "RefTitle": "Error during updating ST-SC relationship in infotype 1001.", "RefUrl": "/notes/685144 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-PS-CA", "From": "464", "To": "464", "Subsequent": ""}, {"SoftwareComponent": "IS-PS-CA", "From": "472", "To": "472", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-PS-CA 464", "SupportPackage": "SAPKIPQE21", "URL": "/supportpackage/SAPKIPQE21"}, {"SoftwareComponentVersion": "IS-PS-CA 472", "SupportPackage": "SAPKIPQH06", "URL": "/supportpackage/SAPKIPQH06"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-PS-CA", "NumberOfCorrin": 2, "URL": "/corrins/**********/64"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-PS-CA", "ValidFrom": "464", "ValidTo": "464", "Number": "652283 ", "URL": "/notes/652283 ", "Title": "Mapping UCAS point of entry to academic session", "Component": "IS-HER-CM-GB"}, {"SoftwareComponent": "IS-PS-CA", "ValidFrom": "464", "ValidTo": "464", "Number": "718645 ", "URL": "/notes/718645 ", "Title": "Error while performing UCAS (*C) file import", "Component": "IS-HER-CM-GB"}, {"SoftwareComponent": "IS-PS-CA", "ValidFrom": "472", "ValidTo": "472", "Number": "775440 ", "URL": "/notes/775440 ", "Title": "Error in program RHIQ_UCAS_IN for Badi", "Component": "IS-HER-CM-GB"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}