{"Request": {"Number": "917447", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 461, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005303152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000917447?language=E&token=0BC9F18E288EE4ADBFF5C8557061C258"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000917447", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000917447/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "917447"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.03.2006"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Land Use Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Land Use Management", "value": "RE-FX-LM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "917447 - BAPI: Texts/memos of land registers"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>For land registers, memos with BAPIs cannot be edited.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Memo; text</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This function was not supported up to now.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>BAPIs are used for the migration from Classic RE land registers to RE-FX land registers. This function is necessary for this purpose.<br /><br />For the transfer of the correction, proceed as follows:<br /></p> <UL><LI>Create new data elements (SE11, package RE_CA_AP).</LI></UL> <UL><UL><LI><B>RECATEXTNAME</B><B> - </B>Name of the text</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Domain <B>TDOBNAME</B>, default component name <B>TEXT_NAME</B>, text description\"T<B>ext name</B>\"</p> <UL><LI>Create new structures (SE11, package RE_CA_AP).</LI></UL> <UL><UL><LI><B>BAPI_RE_TEXT</B><B> </B>- Texts/memos</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Component</TH><TH ALIGN=LEFT> Type</TH></TR> <TR><TD>OBJECT_TYPE</TD><TD> RECABUSOBJTYPE</TD></TR> <TR><TD>OBJECT_ID</TD><TD> RECABUSOBJID</TD></TR> <TR><TD>TEXT_ID</TD><TD> TDID</TD></TR> <TR><TD>TEXT_NAME</TD><TD> RECATEXTNAME</TD></TR> <TR><TD>LANGU</TD><TD> SPRAS</TD></TR> <TR><TD>LANGU_ISO</TD><TD> LAISO</TD></TR> <TR><TD>FORMAT_COL</TD><TD> TDFORMAT</TD></TR> <TR><TD>TEXT_LINE</TD><TD> TDLINE</TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI><B>BAPI_RE_TEXT_DAT</B><B> </B>- Texts/memos - Data</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Component</TH><TH ALIGN=LEFT> Type</TH></TR> <TR><TD>TEXT_ID</TD><TD> TDID</TD></TR> <TR><TD>TEXT_NAME</TD><TD> RECATEXTNAME</TD></TR> <TR><TD>LANGU</TD><TD> SPRAS</TD></TR> <TR><TD>LANGU_ISO</TD><TD> LAISO</TD></TR> <TR><TD>FORMAT_COL</TD><TD> TDFORMAT</TD></TR> <TR><TD>TEXT_LINE</TD><TD> TDLINE</TD></TR> <TR><TD></TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI><B>BAPI_RE_TEXT_DATC</B><B> </B>- Texts/memos - Data - Change fields</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Component</TH><TH ALIGN=LEFT> Type</TH></TR> <TR><TD>CHANGE_INDICATOR</TD><TD> RECACHANGEIND</TD></TR> <TR><TD>TEXT_ID</TD><TD> TDID</TD></TR> <TR><TD>TEXT_NAME</TD><TD> RECATEXTNAME</TD></TR> <TR><TD>LANGU</TD><TD> SPRAS</TD></TR> <TR><TD>LANGU_ISO</TD><TD> LAISO</TD></TR> <TR><TD>FORMAT_COL</TD><TD> TDFORMAT</TD></TR> <TR><TD>TEXT_LINE</TD><TD> TDLINE</TD></TR> <TR><TD></TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI><B>BAPI_RE_TEXT_INT</B><B> </B>- Texts/memos - Internal</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Component</TH><TH ALIGN=LEFT> Type</TH></TR> <TR><TD>OBJNR</TD><TD> RECAOBJNR</TD></TR> <TR><TD>OBJTYPE</TD><TD> RECABUSOBJTYPE</TD></TR> <TR><TD>OBJID</TD><TD> RECABUSOBJID</TD></TR> <TR><TD>TDID</TD><TD> TDID</TD></TR> <TR><TD>TDNAME</TD><TD> RECATEXTNAME</TD></TR> <TR><TD>SPRAS</TD><TD> SPRAS</TD></TR> <TR><TD>TDFORMAT</TD><TD> TDFORMAT</TD></TR> <TR><TD>TDLINE</TD><TD> TDLINE</TD></TR> <TR><TD></TD></TR> </TABLE></UL></UL> <p></p> <UL><UL><LI><B>BAPI_RE_TEXT_INTC</B><B> </B>- Texts/memos - Internal - Change fields</LI><br /> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Component</TH><TH ALIGN=LEFT> Type</TH></TR> <TR><TD>CHANGEIND</TD><TD> RECACHANGEIND</TD></TR> <TR><TD>TDID</TD><TD> TDID</TD></TR> <TR><TD>TDNAME</TD><TD> RECATEXTNAME</TD></TR> <TR><TD>SPRAS</TD><TD> SPRAS</TD></TR> <TR><TD>TDFORMAT</TD><TD> TDFORMAT</TD></TR> <TR><TD>TDLINE</TD><TD> TDLINE</TD></TR> <TR><TD></TD></TR> </TABLE></UL></UL> <p></p> <UL><LI>Create new table types (SE11, package RE_CA_AP)</LI></UL> <UL><UL><LI><B>BAPI_RE_T_TEXT_INT</B><B> </B>- Texts/memos - Internal</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Row type <B>BAPI_RE_TEXT_INT</B></p> <UL><UL><LI><B>BAPI_RE_T_TEXT_INTC</B><B> </B>- Texts/memos - Internal - Change</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Row type <B>BAPI_RE_TEXT_INTC</B></p> <UL><LI>Expand existing structures (SE11).</LI></UL> <UL><UL><LI><B>BAPI_RE_LAND_REG_SELX</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Insert the new field <B>TEXT </B>with component type <B>RECADOSELECT</B> <B> after </B><B>field</B><B> PARTNER</B><B>.</B><B></B></p> <UL><UL><LI><B>RELM_LR_MAP_ENTRYNO</B><B>, </B><B>RELM_LR_MAP_SECNO</B><B>, </B><B> RELM_LR_MAP_CHGNO</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Insert field <B>SECTYPE</B><B> </B>with component type <B>RELMSECTYPE </B><B> </B><B>as</B><B> first field of </B><B>each of </B><B>the structures</B><B>.</B><B></B></p> <UL><LI>Activate the DDIC changes.</LI></UL> <UL><LI>Create function group <B>RECA_BAPI_TEXT</B><B> </B>in package RE_CA_AP.</LI></UL> <UL><UL><LI>Create the function group without modules and activate it before the corrections are implemented.</LI></UL></UL> <UL><LI>Implement the source code from the guideline (SNOTE).</LI></UL> <p><br />The corresponding ALE interfaces must be regenerated in order to be able to use the texts also with the help of the LSMW. For this purpose, the respective Support Package must be imported, instead of doing a manual implementation.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028150)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D028150)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000917447/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917447/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917447/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917447/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917447/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917447/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917447/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917447/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000917447/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "934760", "RefComponent": "RE-FX-LM", "RefTitle": "BAPI: Texts/memos of land registers (Supplement I)", "RefUrl": "/notes/934760"}, {"RefNumber": "916739", "RefComponent": "RE-FX-LM", "RefTitle": "REFX: Land register: Some internal numbers are incorrect", "RefUrl": "/notes/916739"}, {"RefNumber": "916640", "RefComponent": "RE-FX-LM", "RefTitle": "REFX: Error messages for the land register", "RefUrl": "/notes/916640"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "865444", "RefComponent": "RE-FX-MI", "RefTitle": "Migration from Classic RE", "RefUrl": "/notes/865444 "}, {"RefNumber": "934760", "RefComponent": "RE-FX-LM", "RefTitle": "BAPI: Texts/memos of land registers (Supplement I)", "RefUrl": "/notes/934760 "}, {"RefNumber": "916640", "RefComponent": "RE-FX-LM", "RefTitle": "REFX: Error messages for the land register", "RefUrl": "/notes/916640 "}, {"RefNumber": "916739", "RefComponent": "RE-FX-LM", "RefTitle": "REFX: Land register: Some internal numbers are incorrect", "RefUrl": "/notes/916739 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD04", "URL": "/supportpackage/SAPKGPAD04"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000917447/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "915440 ", "URL": "/notes/915440 ", "Title": "BAPI: Land register (exception not caught)", "Component": "RE-FX-LM"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "916026 ", "URL": "/notes/916026 ", "Title": "REFX: Land register: Runtime error in section 1", "Component": "RE-FX-LM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}