{"Request": {"Number": "2182725", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 900, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002182725?language=E&token=242413CCDE908899618B0A1B26DE96DB"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002182725", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002182725/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2182725"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 56}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "13.08.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-TLS-TLA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Upgrade tools for ABAP"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade Tools (SUM)", "value": "BC-UPG-TLS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-TLS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade tools for ABAP", "value": "BC-UPG-TLS-TLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-TLS-TLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2182725 - S4TC Delivery of the SAP S/4HANA System Conversion Checks for SAP S/4HANA 1511 or 1610"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Checks have to be executed before the&#160;conversion to SAP S/4HANA, if all preliminary steps in the source system have been performed.</p>\r\n<p><strong><strong>Note:&#160;</strong>The check report delivered via this note was exclusively used for system conversions to SAP S/4HANA 1511 and SAP S/4HANA 1610. As system conversions to SAP S/4HANA 1511 are no longer supported since May 2018 (and respectively to SAP S/4HANA 1610 since May 2019), this report and this note are obsolete and will be removed in the near future.</strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S4TC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Conversion&#160;to SAP S/4HANA.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement this note as well as the SAP notes mentioned in the manual activities document.</p>\r\n<p>For further information please refer to the respective SAP S/4HANA&#160;conversion procedure guide.</p>\r\n<p>The report R_S4_PRE_TRANSITION_CHECKS, which is delivered with this SAP note,</p>\r\n<ul>\r\n<li>can be executed <strong>standalone</strong> and calls all available&#160;pre&#8211;conversion checks which are delivered with the SAP notes mentioned in the manual activities document of this note.</li>\r\n<li>can be executed as often as required. When called in 'Simulation Mode', nothing is persisted, otherwise the report output is saved as 'Application Log' entry. Use the respective display option on the report selection screen to search for respective application log entries and to display them.</li>\r\n<li>is called automatically in the&#160;conversion procedure by SUM (Software Update Manager) to execute the pre-conversion checks.</li>\r\n</ul>\r\n<p>When you execute the pre&#8211;conversion checks&#160;<strong>standalone </strong>using report R_S4_PRE_TRANSITION_CHECKS:</p>\r\n<ul>\r\n<li>We recommend to always choose the option 'Simulation Mode', so that all available pre&#8211;conversion checks are executed despite of erroneous or missing pre&#8211;check classes.</li>\r\n<li>We recommend to save the entries on the selection screen of report R_S4_PRE_TRANSITION_CHECKS as a report variant.</li>\r\n<li>We recommend to execute the report as batch job using the above mentioned report variant.</li>\r\n<li>You can ignore error messages in the pre&#8211;conversion check result list concerning missing pre&#8211;conversion check classes. Only when the checks are executed by SUM, information about software components that do not require a pre&#8211;conversion check class is available.</li>\r\n</ul>\r\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed for the very first time, field labels for the selection screen fields are missing, because such texts can not be delivered via correction instruction. But these texts are generated for the current logon language and persisted during this very first report execution.</p>\r\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed <strong>with the option 'Check Class Consistency Check'</strong>, the report only checks&#160;if the respective check class methods can be dynamically called, <strong>but the real pre-conversion checks are <span style=\"text-decoration: underline;\"><em>not executed</em></span></strong>.</p>\r\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed and the option <strong>'Pre-Conversion </strong><strong>Check Results' </strong>is selected:</p>\r\n<ul>\r\n<li>If the checkbox &#8216;Simulation Mode&#8217; is selected:</li>\r\n<ul>\r\n<li><strong>no</strong> application log entry is persisted, the output is shown on screen (online execution) or in spool (batch execution).</li>\r\n<li>consistency check errors <strong>are ignored</strong>, all usable checks are executed.</li>\r\n</ul>\r\n<li>If the checkbox &#8216;Simulation Mode&#8217; is not selected:</li>\r\n<ul>\r\n<li>The output is only persisted as application log.</li>\r\n<li>consistency check errors <strong>are not ignored, </strong>checks are only executed if all checks are consistent.</li>\r\n</ul>\r\n</ul>\r\n<p>If one of the dynamically called pre-check classes returns more than 10000 check result lines, a respective error message is written into the check result (and the huge amount of more than 10000 lines&#160;is ignored in the result list in order to prevent an internal memory overflow of the used application log functionality). Contact in such a case the pre-check class responsible so that the class code can be corrected. Use the application component that is mentioned in the pre-check result&#160;to create an&#160;incident.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026187)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026187)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002182725/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002182725/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002182725/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002182725/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002182725/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002182725/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002182725/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002182725/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002182725/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2818267", "RefComponent": "SV-SMG-DVM", "RefTitle": "Data Volume Management during migration to SAP S/4HANA", "RefUrl": "/notes/2818267 "}, {"RefNumber": "2515256", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Report R_S4_PRE_TRANSITION_CHECKS fails with \"Check ID is not provided\"", "RefUrl": "/notes/2515256 "}, {"RefNumber": "2512123", "RefComponent": "BC-UPG-MP", "RefTitle": "SAP S/4HANA Conversion Pre-Checks: missing license", "RefUrl": "/notes/2512123 "}, {"RefNumber": "2262098", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "\"No Check Class exists for software component  XXX\" from R_S4_PRE_TRANSITION_CHECKS report", "RefUrl": "/notes/2262098 "}, {"RefNumber": "2631831", "RefComponent": "XX-PART-IPS", "RefTitle": "S4TC IRMVUI Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2631831 "}, {"RefNumber": "2506861", "RefComponent": "XX-PART-IPS", "RefTitle": "S4TC IRMIPM Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2506861 "}, {"RefNumber": "2506757", "RefComponent": "XX-PART-DTM", "RefTitle": "S4TC IRMEPM Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2506757 "}, {"RefNumber": "2505592", "RefComponent": "XX-PART-IPS", "RefTitle": "S4TC IRMGLB Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2505592 "}, {"RefNumber": "2501201", "RefComponent": "LO-RFM-MD-ART", "RefTitle": "S4SIC SAP_APPL: Retail Generic Articles - Pre-check for Servergroup", "RefUrl": "/notes/2501201 "}, {"RefNumber": "2314696", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "S4TC CEEISUT Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2314696 "}, {"RefNumber": "2323221", "RefComponent": "XX-CSC-CZ-FICA", "RefTitle": "CEEISUT Retrofit: Business Functions", "RefUrl": "/notes/2323221 "}, {"RefNumber": "2445069", "RefComponent": "PPM-PRO", "RefTitle": "S4TC CPRXRPM Master Check for S/4 System Conversion Checks  (new Simplification Item Checks)", "RefUrl": "/notes/2445069 "}, {"RefNumber": "2431747", "RefComponent": "FI-GL-GL", "RefTitle": "General Ledger: Incompatible changes in S/4HANA compared to classic ERP releases", "RefUrl": "/notes/2431747 "}, {"RefNumber": "2357827", "RefComponent": "MM-IM-GF-CWM", "RefTitle": "SAP S/4HANA CWM 1610: Restriction Note", "RefUrl": "/notes/2357827 "}, {"RefNumber": "2341836", "RefComponent": "BNS-ARI-SE", "RefTitle": "S4TWL - Ariba Network Integration in SAP S/4HANA on-premise edition", "RefUrl": "/notes/2341836 "}, {"RefNumber": "2338097", "RefComponent": "XX-CSC-CZ-IS-U", "RefTitle": "S4TWL - Utility & Telco related Business Functions", "RefUrl": "/notes/2338097 "}, {"RefNumber": "2216943", "RefComponent": "SRM-EBP-TEC-UPG", "RefTitle": "S4TC SRM_SERVER master check for S/4 system conversion checks", "RefUrl": "/notes/2216943 "}, {"RefNumber": "2198974", "RefComponent": "FIN-FSCM-CR", "RefTitle": "S4TC FINBASIS Master Check for S/4 System Conversion Checks", "RefUrl": "/notes/2198974 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "750", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0002182725/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Activity&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BASIS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAP Basis compo...|<br/>| Release 700&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB70004&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 701&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 702&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB70201&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 731&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From SAPKB73101&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 740&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 750&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;All Support Package Levels&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Please implement the following SAP notes which contain the S/4 system conversion checks:<br/></P> <UL><UL><LI>2187732 (S4TC EA-GLTRADE Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2186288 (S4TC SRMSMC Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2185960 (S4TC SAP_APPL Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2194368 (S4TC EHSM Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2198401 (S4TC EA-APPL Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2194272 (S4TC ECC-DIMP Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2198974 (S4TC FINBASIS Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2237932 (S4TC Ariba BS Add-On Master Check for S/4 System Conv. Checks)</LI></UL></UL> <UL><UL><LI>2248512 (S4TC PICM Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2243963 (S4TC CPD Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2216943 (S4TC SRM_SERVER Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2293565 (S4TC CODERINT Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2358407 (S4TC EA-FINSERV Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2326521 (S4TC EA-RETAIL Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2314696 (S4TC CEEISUT Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2335793 (S4TC IS-CWM Master Check for SAP S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2202282 (S4TC IS-UT Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2331943 (S4TC IS-OIL Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2321885 (S4TC CPRXRPM Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2379272 (S4TC MDG_APPL Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2392233 (S4TC EA-PS Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2291034 (S4TC OGSD Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2379693 (S4TC OTAS Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2442665 (S4TC MDG_MDC Master Check for S/4 System Conversion Checks)</LI></UL></UL> <UL><UL><LI>2488679 (S4TC MRSS Master Check for S/4 System Conversion Checks)</LI></UL></UL> <P></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}