{"Request": {"Number": "1563201", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 351, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017186752017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=BB65FFF7EACE9A184B83AF778DB82437"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1563201"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.05.2011"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-HU"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hungary"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hungary", "value": "RE-FX-LC-HU", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-HU*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1563201 - RE-FX Country Version for Hungary"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Localization of Flexible Real Estate Management for Hungary.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Real Estate, Flexible Real Estate, RE-FX, Localization, Fulfillment Date, Service Charge Settlement</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have enabled the Hungarian specific Real Estate business function. More details of the localization can be found in note 1353606.<br /><br />According to requirements in Hungary, you must include all the service charge costs that are passed on to third party business partners in the Stocks row of the balance sheet. To do so, during the service charge settlement process the system must first transfer these costs to a stock account and then to a revenue account.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Apply the support package indicated.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-HU (Hungary)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I033770)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I033770)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1895254", "RefComponent": "XX-CSC-HU", "RefTitle": "RE-FX: Copy numbering on the billing document for Hungary", "RefUrl": "/notes/1895254"}, {"RefNumber": "1696391", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: To correct the incorrect Currency Translation Rule", "RefUrl": "/notes/1696391"}, {"RefNumber": "1681002", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: Invoice creation in foreign currency correctly", "RefUrl": "/notes/1681002"}, {"RefNumber": "1677003", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: Correction invoice", "RefUrl": "/notes/1677003"}, {"RefNumber": "1646423", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RERAIV: Already invoiced items are reinvoiced (RE-FX HU)", "RefUrl": "/notes/1646423"}, {"RefNumber": "1646249", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RERAPPRV: Unneeded check for transfer postings (REXCCORR017)", "RefUrl": "/notes/1646249"}, {"RefNumber": "1633499", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: Incorrect fiscal year determination during RERAIV", "RefUrl": "/notes/1633499"}, {"RefNumber": "1629538", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: Invoicing for SCS Service Charge Settl. Reversal", "RefUrl": "/notes/1629538"}, {"RefNumber": "1353606", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX Country Version for Hungary", "RefUrl": "/notes/1353606"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1895254", "RefComponent": "XX-CSC-HU", "RefTitle": "RE-FX: Copy numbering on the billing document for Hungary", "RefUrl": "/notes/1895254 "}, {"RefNumber": "1646249", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RERAPPRV: Unneeded check for transfer postings (REXCCORR017)", "RefUrl": "/notes/1646249 "}, {"RefNumber": "1696391", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: To correct the incorrect Currency Translation Rule", "RefUrl": "/notes/1696391 "}, {"RefNumber": "1681002", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: Invoice creation in foreign currency correctly", "RefUrl": "/notes/1681002 "}, {"RefNumber": "1677003", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: Correction invoice", "RefUrl": "/notes/1677003 "}, {"RefNumber": "1629538", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: Invoicing for SCS Service Charge Settl. Reversal", "RefUrl": "/notes/1629538 "}, {"RefNumber": "1646423", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RERAIV: Already invoiced items are reinvoiced (RE-FX HU)", "RefUrl": "/notes/1646423 "}, {"RefNumber": "1633499", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX HU: Incorrect fiscal year determination during RERAIV", "RefUrl": "/notes/1633499 "}, {"RefNumber": "1353606", "RefComponent": "RE-FX-LC-HU", "RefTitle": "RE-FX Country Version for Hungary", "RefUrl": "/notes/1353606 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "605", "To": "605", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 605", "SupportPackage": "SAPK-60504INEAAPPL", "URL": "/supportpackage/SAPK-60504INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1353606", "RefTitle": "RE-FX Country Version for Hungary", "RefUrl": "/notes/0001353606"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}