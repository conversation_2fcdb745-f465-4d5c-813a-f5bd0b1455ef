{"Request": {"Number": "3992", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 304, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014323022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000003992?language=E&token=AABB536D7E6D4B5477A0A9C5575DC78A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000003992", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000003992/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3992"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 25}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.04.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-ABA-LA"}, "SAPComponentKeyText": {"_label": "Component", "value": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,  <PERSON>time"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Runtime Environment - ABAP Language Issues Only", "value": "BC-ABA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-ABA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>time", "value": "BC-ABA-LA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-ABA-LA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3992 - Purpose of the table INDX"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />The table INDX contains data that is packed in binary form and usually compressed so that it cannot be displayed directly. Under certain circumstances, the table may contain a lot of records, and you want to know whether you can delete certain entries.</p>\r\n<ul>\r\n<li>For what data can the table INDX be used?</li>\r\n</ul>\r\n<ul>\r\n<li>Which applications store data in this table?</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br />The data in the table INDX and other tables similar to INDX are written and read using the ABAP commands EXPORT TO DATABASE and IMPORT FROM DATABASE. This data is usually compressed, and you can store any data (for example, large internal tables).<br /><br />If the data structure changes, or if the content of the table is corrupted in any way, you can no longer import the data in most cases.<br /><br />You can use the field RELID, which is always contained in the key, to roughly delimit the table contents of INDX tables. This two-digit ID is always specified in parentheses directly in the EXPORT command. Example: EXPORT ... TO DATABASE INDX(XY).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><br /><strong><strong>The INDX table should be used only for storing load objects,</strong></strong><br /><strong><strong>that is, for data that can be reconstructed after data loss. Original data that cannot be reconstructed should not be stored in the table INDX.</strong></strong><br /><strong><strong></strong></strong><br /><br />In general, we recommend that all applications that use INDX data create their own INDX table. This facilitates an easier identification of the application to which the data belongs. In addition, this reduces the risk of two different applications storing their data under the same RELID and data being merged or overwritten.<br /><br />The following lists some frequently used RELID values of the table INDX. In some cases, notes regarding the reorganization or display of the relevant data exist.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>RELID</th><th>Component</th><th>SAP Note</th></tr>\r\n<tr>\r\n<td>//</td>\r\n<td>PA-BC</td>\r\n<td>836478</td>\r\n</tr>\r\n<tr>\r\n<td>AL</td>\r\n<td>BC-SRV-BAL</td>\r\n</tr>\r\n<tr>\r\n<td>AP</td>\r\n<td>SV-BO</td>\r\n</tr>\r\n<tr>\r\n<td>AR</td>\r\n<td>PS-IS</td>\r\n<td>1399175</td>\r\n</tr>\r\n<tr>\r\n<td>AV</td>\r\n<td>PS-COS-PER-SE</td>\r\n</tr>\r\n<tr>\r\n<td>CY</td>\r\n<td>PP-CRP</td>\r\n</tr>\r\n<tr>\r\n<td>FF</td>\r\n<td>FIN-FSCM-CM-CM</td>\r\n<td>1754126</td>\r\n</tr>\r\n<tr>\r\n<td>FL</td>\r\n<td>BC-DWB-TOO-FUB</td>\r\n<td>1672188</td>\r\n</tr>\r\n<tr>\r\n<td>GL</td>\r\n<td>FI-SL-IS-A</td>\r\n<td>18523 (&lt;= 4.0B), 998892</td>\r\n</tr>\r\n<tr>\r\n<td>IM</td>\r\n<td>XX-CSC-RU-LO</td>\r\n<td>2463725</td>\r\n</tr>\r\n<tr>\r\n<td>IW</td>\r\n<td>KW-KM</td>\r\n<td>1292125</td>\r\n</tr>\r\n<tr>\r\n<td>IX</td>\r\n<td>LO-VC-LOI</td>\r\n<td>684744</td>\r\n</tr>\r\n<tr>\r\n<td>KC</td>\r\n<td>FS-CD, FS-ICM,</td>\r\n<td>420945, 360554</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>IS-B-DP-EDT</td>\r\n<td>1055431</td>\r\n</tr>\r\n<tr>\r\n<td>KE</td>\r\n<td>CO-PA</td>\r\n<td>1318670</td>\r\n</tr>\r\n<tr>\r\n<td>KU</td>\r\n<td>CO-PC</td>\r\n<td>1302042</td>\r\n</tr>\r\n<tr>\r\n<td>OA</td>\r\n<td>BC-SRV-ARL</td>\r\n</tr>\r\n<tr>\r\n<td>PE and PC</td>\r\n<td>EC-PCA</td>\r\n<td>1089012</td>\r\n</tr>\r\n<tr>\r\n<td>PR</td>\r\n<td>BC-SEC-AUT-PFC</td>\r\n<td>1291662</td>\r\n</tr>\r\n<tr>\r\n<td>RH</td>\r\n<td>BC-BMT-OM</td>\r\n</tr>\r\n<tr>\r\n<td>RT</td>\r\n<td>BC-DWB-SEM</td>\r\n<td>1294414</td>\r\n</tr>\r\n<tr>\r\n<td>SD</td>\r\n<td>SD-BF-AC</td>\r\n</tr>\r\n<tr>\r\n<td>SH</td>\r\n<td>BC-DOC-HLP</td>\r\n<td>989070</td>\r\n</tr>\r\n<tr>\r\n<td>SM</td>\r\n<td>CA-GTF-SCM</td>\r\n<td>977726</td>\r\n</tr>\r\n<tr>\r\n<td>V1</td>\r\n<td>PLM-CFO</td>\r\n</tr>\r\n<tr>\r\n<td>VM</td>\r\n<td>BC-DWB-SEM</td>\r\n<td>1294414</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In addition to these RELID values or components, additional values or components are known that are not relevant in practical terms because only a few entries are stored:<br /><br />$$ (BC-DWB-TOO-MEN)<br />AP (PY-NZ)<br />MS (BC-I18-UNI)<br /><br />Generally, questions regarding the reorganization of certain entries (RELID) can only be answered by the applications component that stored the data.</p>\r\n<p>&#x00A0;</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026122)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023969)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000003992/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000003992/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000003992/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000003992/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000003992/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000003992/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000003992/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000003992/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000003992/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "989070", "RefComponent": "BC-DOC-HLP", "RefTitle": "Cleanup for table INDX(SH)", "RefUrl": "/notes/989070"}, {"RefNumber": "977726", "RefComponent": "CA-GTF-SCM", "RefTitle": "INDX cluster table contains many entries in area SM", "RefUrl": "/notes/977726"}, {"RefNumber": "836478", "RefComponent": "PA-BC", "RefTitle": "HR authorizations: Displaying the data in the INDX", "RefUrl": "/notes/836478"}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478"}, {"RefNumber": "420945", "RefComponent": "FS-CD", "RefTitle": "Table INDX reaches maximum allowed size", "RefUrl": "/notes/420945"}, {"RefNumber": "2463725", "RefComponent": "XX-CSC-RU-LO", "RefTitle": "J_3RMOBVED: Cleanup table INDX(IM)", "RefUrl": "/notes/2463725"}, {"RefNumber": "18523", "RefComponent": "FI-SL-IS-A", "RefTitle": "Using INDX in the Report Writer", "RefUrl": "/notes/18523"}, {"RefNumber": "1804650", "RefComponent": "MM-PUR-GF-ES", "RefTitle": "Cluster table INDX(HK) with unexpected huge growth", "RefUrl": "/notes/1804650"}, {"RefNumber": "1754126", "RefComponent": "FIN-FSCM-CM-CM", "RefTitle": "Cleanup of table INDX(FF)", "RefUrl": "/notes/1754126"}, {"RefNumber": "1583140", "RefComponent": "XX-CSC-IN-MM", "RefTitle": "Deletion of INDX entries with RELID = 'ST' for India: CIN", "RefUrl": "/notes/1583140"}, {"RefNumber": "1399175", "RefComponent": "PS-IS", "RefTitle": "Cleanup table INDX(AR)", "RefUrl": "/notes/1399175"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}, {"RefNumber": "1294414", "RefComponent": "BC-DWB-SEM", "RefTitle": "Cleaning up table INDX (VM/RT)", "RefUrl": "/notes/1294414"}, {"RefNumber": "1089012", "RefComponent": "EC-PCA", "RefTitle": "Cleanup table INDX(PE)/(PC)", "RefUrl": "/notes/1089012"}, {"RefNumber": "1055431", "RefComponent": "IS-B-DP-EDT", "RefTitle": "Deletion of INDX entries", "RefUrl": "/notes/1055431"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "977726", "RefComponent": "CA-GTF-SCM", "RefTitle": "INDX cluster table contains many entries in area SM", "RefUrl": "/notes/977726 "}, {"RefNumber": "706478", "RefComponent": "HAN-DB", "RefTitle": "Preventing Basis tables from increasing considerably", "RefUrl": "/notes/706478 "}, {"RefNumber": "1804650", "RefComponent": "MM-PUR-GF-ES", "RefTitle": "Cluster table INDX(HK) with unexpected huge growth", "RefUrl": "/notes/1804650 "}, {"RefNumber": "1583140", "RefComponent": "XX-CSC-IN-MM", "RefTitle": "Deletion of INDX entries with RELID = 'ST' for India: CIN", "RefUrl": "/notes/1583140 "}, {"RefNumber": "1754126", "RefComponent": "FIN-FSCM-CM-CM", "RefTitle": "Cleanup of table INDX(FF)", "RefUrl": "/notes/1754126 "}, {"RefNumber": "1055431", "RefComponent": "IS-B-DP-EDT", "RefTitle": "Deletion of INDX entries", "RefUrl": "/notes/1055431 "}, {"RefNumber": "1089012", "RefComponent": "EC-PCA", "RefTitle": "Cleanup table INDX(PE)/(PC)", "RefUrl": "/notes/1089012 "}, {"RefNumber": "1399175", "RefComponent": "PS-IS", "RefTitle": "Cleanup table INDX(AR)", "RefUrl": "/notes/1399175 "}, {"RefNumber": "1294414", "RefComponent": "BC-DWB-SEM", "RefTitle": "Cleaning up table INDX (VM/RT)", "RefUrl": "/notes/1294414 "}, {"RefNumber": "836478", "RefComponent": "PA-BC", "RefTitle": "HR authorizations: Displaying the data in the INDX", "RefUrl": "/notes/836478 "}, {"RefNumber": "989070", "RefComponent": "BC-DOC-HLP", "RefTitle": "Cleanup for table INDX(SH)", "RefUrl": "/notes/989070 "}, {"RefNumber": "420945", "RefComponent": "FS-CD", "RefTitle": "Table INDX reaches maximum allowed size", "RefUrl": "/notes/420945 "}, {"RefNumber": "18523", "RefComponent": "FI-SL-IS-A", "RefTitle": "Using INDX in the Report Writer", "RefUrl": "/notes/18523 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}