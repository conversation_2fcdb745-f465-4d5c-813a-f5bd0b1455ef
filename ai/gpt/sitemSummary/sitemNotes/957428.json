{"Request": {"Number": "957428", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 383, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016118612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000957428?language=E&token=0F40D990EE8CBC204CD32D79DCC57106"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000957428", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "957428"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.10.2006"}, "SAPComponentKey": {"_label": "Component", "value": "SV-PERF-PI"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Process Integration (PI) related performance issues"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Performance Problems", "value": "SV-PERF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-PERF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Process Integration (PI) related performance issues", "value": "SV-PERF-PI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-PERF-PI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "957428 - Troubleshooting XI Service Downloads"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>XI data is missing from service reports.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>XI service session download DSA ST13 SDCC ST-A/PI<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Symptoms and solutions are explained here. For an explanation of how to check download contents go to point 4.</p> <OL>1. Latency times are zero - aggregation of performance headers is not set up on the customers system. Refer to note 820622.</OL> <OL>2. Statistics are missing. For example the check 'Weekly Load Profile' table 'Number of Messages Per Weekday' shows nothing in column 'No. of Messages'. Similarly the workload distribution checks are empty.</OL> <OL><OL>a) Check that the procedure after ST-A/PI add-on implementation was executed in RTCCTOOL. This is to uncomment code delivered conditionally, e.g. depending on SP level. Refer to note 69455 point 2d).</OL></OL> <OL><OL>b) Look for download tables 00013 &amp; 00014 as described in point 4 below. If the tables are missing they may have exceeded the allowed download size and were deleted automatically. In this case statistics can be retrieved from the report ZSXMB_REMOTE_SERVICE - refer to note 746088.</OL></OL> <OL><OL>c) A data collector which fails (dumps) 3 times is automaticlly deactivated. Check if an XI collector has failed and reactivate it:</OL></OL> <UL><UL><LI>Open the download contents as described in point 4a below. Expand 'DATA_PER_R3SYSTEM -&gt; XAD -&gt; DATACOLL_MSG_LOG' and select SUBROUTINE_STATS. The following collectors should be listed and flagged 'completed':<br />DCA_XI_DOWNLOAD_TABLES<br />DCA_XI_PERFORMANCE/DCA_XI_PERFORMANCE_30<br />DCA_XI_TOP20_MESSAGES<br />DCA_XI_FAILED_MESSAGES</LI></UL></UL> <UL><UL><LI>Reactivate any collector that did not execute. Run transaction /n/ssf/pb and enter project ID 'DCA'</LI></UL></UL> <UL><UL><LI>Select the collector with the 'arrow' button and check the traffic light status</LI></UL></UL> <UL><UL><LI>If the status is red look for menu option 'Lock/Unlock -&gt; Unlock subroutine' and use it to unlock the collector. (The option is enabled only for collectors relevant to products in use).</LI></UL></UL> <UL><UL><LI>Run the download again. If a short dump reoccurs open an it/ibc message on component SUP-RSRV-DEV.</LI></UL></UL> <p></p> <OL>3. No subchecks are displayed for check 'XI Performance of &lt;SID&gt;'. Possible solutions are:</OL> <OL><OL>a) Tick the field 'analyze' in table XI Runtime Objects beside the integration server and save.</OL></OL> <OL><OL>b) Check that the integration server name in column 'Business System' is correct. Get the value from the customer system - run transaction SE37 and execute function SXMS_PF_GET_OWN_IS_NAME in test mode. No input is needed. Take the value returned in IS_NAME, copy it to the check table and save.</OL></OL> <OL>4. How to Check Download Contents</OL> <OL><OL>a) In transaction DSA use the 'truck' icon to display the download. Expand the main node, select 'Alternate Viewers' &amp; 'ST-A/PI DATA VIEWER'. Expand 'DATA_PER_R3SYSTEM -&gt; DCA -&gt; XI Tables'. Select a table number to see the contents.</OL></OL> <OL><OL>b) If option 'Alternate Viewers' is not available: Copy the session number removing the client (the first 3 digits). Run transaction ST13 and execute tool SDCCVIEWER. Paste the session number into field 'Session number'. Press 'Select sessions'. Choose your session using the magnifying glass icon. Expand 'DATA_PER_R3SYSTEM -&gt; DCA -&gt; XI Tables'.</OL></OL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D049866)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D049866)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000957428/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000957428/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000957428/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000957428/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000957428/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000957428/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000957428/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000957428/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000957428/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "820622", "RefComponent": "SV-PERF-PI", "RefTitle": "Standard jobs for XI performance monitoring", "RefUrl": "/notes/820622"}, {"RefNumber": "746088", "RefComponent": "SV-PERF-PI", "RefTitle": "Statistics on PI/XI messages for Remote Services", "RefUrl": "/notes/746088"}, {"RefNumber": "1168369", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-A/PI 01K: Missing PI 7.10 performance data", "RefUrl": "/notes/1168369"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "820622", "RefComponent": "SV-PERF-PI", "RefTitle": "Standard jobs for XI performance monitoring", "RefUrl": "/notes/820622 "}, {"RefNumber": "746088", "RefComponent": "SV-PERF-PI", "RefTitle": "Statistics on PI/XI messages for Remote Services", "RefUrl": "/notes/746088 "}, {"RefNumber": "1168369", "RefComponent": "SV-SMG-SER", "RefTitle": "ST-A/PI 01K: Missing PI 7.10 performance data", "RefUrl": "/notes/1168369 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-SER", "From": "310", "To": "620_2006_1", "Subsequent": ""}, {"SoftwareComponent": "ST-SER", "From": "700_2005_2", "To": "700_2006_1", "Subsequent": ""}, {"SoftwareComponent": "ST-SER", "From": "700_2008_1", "To": "700_2008_1", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}