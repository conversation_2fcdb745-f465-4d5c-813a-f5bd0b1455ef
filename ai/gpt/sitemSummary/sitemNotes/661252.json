{"Request": {"Number": "661252", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 411, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000661252?language=E&token=33B3C42324D7D42A27534B5647491B0A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000661252", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000661252/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "661252"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 26}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "20.03.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "661252 - DB2-z/OS: Additions upgrade to Basis 6.40"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Additions to the upgrade procedure:</p> <UL><LI>to Basis Release 6.40</LI></UL> <UL><LI>on DB2 UDB for z/OS (DB2-z/OS)</LI></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DB2 UDB on OS/390 OS390 DB2-z/OS<br />Update, upgrade, release upgrade, release, maintenance level<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />----------------------------------------------------------------------<br />This note only contains information on the upgrade<br />to Basis Release 6.40<br />----------------------------------------------------------------------<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />-----------------------------------------------------------------------</p> <b>CAUTION: This note is updated regularly. Make sure you read it again immediately before the upgrade.<br />-----------------------------------------------------------------------</b><br /> <p></p> <OL>1. General information</OL> <UL><LI>Supported operating systems</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The upgrade can be performed on AIX, zLinux, z/OS or Windows. Solaris is not supported.</p> <UL><LI>Java</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;JDK version 1.4 is the minimum requirement on all OS platforms.</p> <UL><LI>High-Availability systsms</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The latest versions of the upgrade tool (R3up640/2 patch #29 and later) support the upgrade of high-availability systems (HA systems). However, there are the following requirements:</p> <UL><UL><LI>The application servers are running on UNIX.</LI></UL></UL> <UL><UL><LI>You have to use the upgrade strategy \"Downtime-minimized\" and choose phase MODPROF_TRANS for disabling the archive mode (in the case of DB2 this choice only determines when upgrade downtime starts).</LI></UL></UL> <OL>2. Changes to the documentation</OL> <UL><LI>UNIX Upgrade Guide, chapter 'Introduction', section 1.4. 'New Features', subsection 'New Features in the Upgrade Based on SAP Web Application Server 6.20'.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The statement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;\"The complete upgrade, including PREPARE, has to be performed on z/OS USS or Windows. AIX, zLinux, and Solaris are not supported\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;is not correct. <B>AIX and zLinux are fully supported</B>. Only Solaris cannot be used as an upgrade platform.</p> <OL>3. Actions before or at the beginning of PREPARE</OL> <UL><LI>Consistency of fields with DDIC type RAW, LRAW or VARC</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is possible that some tables are defined incorrectly in the database, i.e. that DB2 data type VARCHAR instead of VARCHAR FOR BIT DATA is used for table fields with DDIC type RAW, LRAW or VARC. This inconsistency must be removed before starting PREPARE. Therefore, check and adjust (if necessary) the SAP system as described in SAP Note 848384.</p> <UL><LI>Setup of DB2-z/OS</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ensure that the ZPARM parameter MAXDBAT is set to a minimum value of 200. This setting is only required during the upgrade and should be reset after the upgrade to the value recommended in the SAP DBA Guide (Release 6.40).</p> <UL><LI>DB2 Connect (only non-z/OS)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You must install DB2 CONNECT Version 8, FixPak 6 or higher:</p> <UL><UL><LI>For OEM customers the software is shipped with the SAP CD \"IBM DB2 Connect V8 FP6 for DB2 for z/OS\".</LI></UL></UL> <UL><UL><LI>All other customers have to order DB2 CONNECT directly from IBM.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The DB2 SYSADM group &lt;db2adm&gt; mentioned in the upgrade guide is the group to which the DB2 instance owner belongs to.</p> <UL><LI>DDIC corrections</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Apply the latest DDIC corrections as described in SAP notes 162250, 162818, 407663 and 184399.</p> <UL><LI>Implementing upgrade corrections</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We highly recommend to apply the upgrade repairs described in SAP note 663240.</p> <UL><LI>Technical settings of tables TESCL and TESPO</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the tables TESCL and TESPO exist in the source system, check whether the technical settings of these tables are active (with transaction SE13). If this is not case activate the settings.</p> <UL><LI>Profile Changes for Upgrade on z/OS</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You may need to change your instance profile. Refer to note 326949 for details.</p> <UL><LI>Exchanging upgrade tools</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To avoid problems during the upgrade, the following tool(s) and/or libraries have to be exchanged in directory &lt;DIR_PUT&gt;/exe (&lt;DIR_PUT&gt;/bin for R3up on UNIX):<br /><br /> tool&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; patch&#x00A0;&#x00A0;version&#x00A0;&#x00A0;&#x00A0;&#x00A0;note/remark<br /> ----&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -----&#x00A0;&#x00A0;-------&#x00A0;&#x00A0;&#x00A0;&#x00A0;-----------<br /> R3up 640/2&#x00A0;&#x00A0; 29&#x00A0;&#x00A0;&#x00A0;&#x00A0; 21.093&#x00A0;&#x00A0;&#x00A0;&#x00A0; 663258<br /> dbdb2slib&#x00A0;&#x00A0;&#x00A0;&#x00A0;94&#x00A0;&#x00A0;&#x00A0;&#x00A0;-&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;878632,882751<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP note 181187 describes how to perform the exchange.</p> <OL>4. Information on the individual upgrade phases</OL> <UL><LI>Phase INITPUT_PRE</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The upgrade tool now explicitly asks whether all indexes should be converted to NOT PADDED during downtime which leads to smaller indexes and a better performance (by enabling index-only access to the data). Note that the conversion may last up to several hours (depending on the size and number of affected indexes). If downtime matters it is also possible to postpone the conversion and run run transaction DB2_IXFIX after the upgrade is finished (for details refer to SAP note 793204).</p> <UL><LI>Phase HIAV_CHK</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The upgrade tool check whether it is dealing with a high-availibility HA system. If yes, a message is written to the screen.</p> <UL><LI>Phase DBPREP_CHK</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If pending indexes are reported please REBUILD these indexes (don't ignore the message).</p> <UL><LI>Phase CHECKGROUP_END11 (only with data sharing or failover setup)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the source system is setup with data sharing or failover you have to manually modify the shadow default profile, because in this case the profile parameter SAPDBHOST is specified in the instance profile and not in the DEFAULT.PFL as usual. Therefore, stop in phase CHECKGROUP_END11 and add the entry SAPDBHOST=&lt;database host&gt; to the shadow default profile &lt;DIR_PUT&gt;/&lt;SAPSID&gt;/SYS/profile/DEFAULT.PFL.</p> <UL><LI>Phase INITSUBST</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In case of the upgrade of an HA system, choose upgrade strategy \"Downtime-minimized\" and MODPROF_TRANS as phase for disabling the archive mode.</p> <UL><LI>Phase DIFFEXPDDIV</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the error messages<br /><br />&#x00A0;&#x00A0;2EETW190 \"TABT\" \"TESCL\" has no active version.<br />&#x00A0;&#x00A0;2EETW190 \"TABT\" \"TESPO\" has no active version.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;are listed in log file DIFFEXPD. ELG, proceed as follows:</p> <UL><UL><LI>Start the SAP system (if it is not running).</LI></UL></UL> <UL><UL><LI>Logon to the SAP system (as user DDIC).</LI></UL></UL> <UL><UL><LI>Call transaction SE37 for function module DD_TABT_ACTM.</LI></UL></UL> <UL><UL><LI>Specify tabname=TESCL, otherwise keep the default settings and execute the function module. Repeat the step for table TESPO.</LI></UL></UL> <UL><UL><LI>Re-start R3up with option 'init'.</LI></UL></UL> <UL><LI>Phase START_SHDI_FIRST</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the source system is setup with data sharing or failover and phase START_SHDI_FIRST fails, ensure that the parameter SAPDBHOST is set in the shadow default profile &lt;DIR_PUT&gt;/&lt;SAPSID&gt;/SYS/profile/DEFAULT.PFL.</p> <UL><LI>Phase ACT_640</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The error message<br /><br />&#x00A0;&#x00A0;1EEDO519X\"Data Element\" \"DB2MAPTS\" could not be activated<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in file ACTUPG.ELG can be ignored. If it appears continue the phase with 'ignore' (no password required).</p> <UL><LI>Phase REQ_ASCS_STOP (only HA systems)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Stop the ASCS instance. If it runs remotely, you may now update the host (where the ASCS instance is running) with the latest SAP kernel!</p> <UL><LI>Phases PARMVNT_*</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If SQLCODE -601 occurs check whether a DROP TABLE ... +LOCATION has been executed before for the affected table (see log files PD*.&lt;SID&gt;). If yes, drop the table manually and repeat the phase. Note that this error can be avoided if you apply DBSL patch level 94 or higher!</p> <UL><LI>Phase XPRAS_UPG</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;On z/OS the following messages may appear in XPRASUPG.ELG<br /><br />&#x00A0;&#x00A0;1AETR012XProgram terminated (job: \"RDDEXECL\", ...<br />&#x00A0;&#x00A0;...<br />&#x00A0;&#x00A0;1AEPU320 See job log\"RDDEXECL\" ...<br />&#x00A0;&#x00A0;1 ETP111 exit code&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; : \"12\"<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To resolve the problem:</p> <UL><UL><LI>Ensure that all profile parameters are set as described in SAP note 326949.</LI></UL></UL> <UL><UL><LI>Cleanup the shared memory used by the shadow instance with the command:<br /><br />&#x00A0;&#x00A0;cleanipc &lt;SYSNR&gt; remove</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;where &lt;SYSNR&gt; is the system number of the shadow instance.</p> <UL><UL><LI>Stop and re-start the SAP system.</LI></UL></UL> <UL><UL><LI>If the problem persists, decrease the profile parameter em/address_space_MB to a value that is smaller than 512 (e.g. 350 or 250) and re-start the system.</LI></UL></UL> <UL><LI>Phase REQ_ASCS_START (only HA systems)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Restart the (remote) ASCS instance.</p> <UL><LI>Phase CHK_POSTUP</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following error reported in LONGPOST.LOG<br /><br />&#x00A0;&#x00A0;3PETG447 Table and runtime object \"&lt;TABLE&gt;\" exist<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;without DDIC reference<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;can be ignored, if &lt;TABLE&gt; is DB2T2MCONV or DB2DB13. Please, drop the database table manually to remove the inconsistency.</p> <OL>5. Actions after upgrading</OL> <UL><LI>Reset ZPARM parameter MAXDBAT</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For details refer to the SAP DBA Guide (Release 6.40).</p> <UL><LI>Inconistencies in transaction DB02</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Index SYSIXDEP~0 and table DSN_STATEMNT_TABLE are listed as unknown in transaction DB02 --&gt; Check --&gt; Database &lt;-&gt; Dictionary. This message can be ignored and disappears after applying Basis Support Package SAPKB64005 (see note 686905).<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-DB2 (DB2 for z/OS)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022631)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022280)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000661252/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000661252/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000661252/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000661252/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000661252/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000661252/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000661252/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000661252/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000661252/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "977788", "RefComponent": "BC-DB-DB2-UPG", "RefTitle": "DB2-z/OS: SQLCODE -407 during upgrade phases SHADOW_IMPORT_*", "RefUrl": "/notes/977788"}, {"RefNumber": "932894", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Performance of upgrade phase RUN_RSCPFROM", "RefUrl": "/notes/932894"}, {"RefNumber": "926100", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/926100"}, {"RefNumber": "882751", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Avoid SQLCODE -601 in R3up phase PARMVNT", "RefUrl": "/notes/882751"}, {"RefNumber": "848384", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Inconsistent fields with DDIC type RAW/LRAW/VARC", "RefUrl": "/notes/848384"}, {"RefNumber": "843356", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Upgrade phase JOB_RSSWINCV fails", "RefUrl": "/notes/843356"}, {"RefNumber": "788218", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/788218"}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047"}, {"RefNumber": "726217", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Upgrade to SRM 4.0 - free space", "RefUrl": "/notes/726217"}, {"RefNumber": "726216", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Upgrade to SCM 4.1 - free space", "RefUrl": "/notes/726216"}, {"RefNumber": "726215", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Upgrade to ECC 5.0 - free space", "RefUrl": "/notes/726215"}, {"RefNumber": "705578", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade on SAP SRM Server 5.0", "RefUrl": "/notes/705578"}, {"RefNumber": "689574", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/689574"}, {"RefNumber": "686905", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: DDIC corrections (6.20 and higher)", "RefUrl": "/notes/686905"}, {"RefNumber": "684406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684406"}, {"RefNumber": "663258", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for R3up version 640", "RefUrl": "/notes/663258"}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240"}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640"}, {"RefNumber": "661253", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/661253"}, {"RefNumber": "407663", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: DDIC corrections (6.10, 6.20)", "RefUrl": "/notes/407663"}, {"RefNumber": "184399", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: DDIC corrections (Releases 4.6A,4.6B,4.6C,4.6D)", "RefUrl": "/notes/184399"}, {"RefNumber": "162818", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/162818"}, {"RefNumber": "162250", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/162250"}, {"RefNumber": "1092921", "RefComponent": "BC-DB-DB2-UPG", "RefTitle": "DB2-z/OS: SQLCODE -904/-913 RC=00C90096 (upgrade/EhPI/SUM)", "RefUrl": "/notes/1092921"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "184399", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: DDIC corrections (Releases 4.6A,4.6B,4.6C,4.6D)", "RefUrl": "/notes/184399 "}, {"RefNumber": "686905", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: DDIC corrections (6.20 and higher)", "RefUrl": "/notes/686905 "}, {"RefNumber": "1092921", "RefComponent": "BC-DB-DB2-UPG", "RefTitle": "DB2-z/OS: SQLCODE -904/-913 RC=00C90096 (upgrade/EhPI/SUM)", "RefUrl": "/notes/1092921 "}, {"RefNumber": "407663", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: DDIC corrections (6.10, 6.20)", "RefUrl": "/notes/407663 "}, {"RefNumber": "661640", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40", "RefUrl": "/notes/661640 "}, {"RefNumber": "848384", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Inconsistent fields with DDIC type RAW/LRAW/VARC", "RefUrl": "/notes/848384 "}, {"RefNumber": "775047", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP Web AS 6.40 SR1", "RefUrl": "/notes/775047 "}, {"RefNumber": "663258", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for R3up version 640", "RefUrl": "/notes/663258 "}, {"RefNumber": "705578", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additions to upgrade on SAP SRM Server 5.0", "RefUrl": "/notes/705578 "}, {"RefNumber": "977788", "RefComponent": "BC-DB-DB2-UPG", "RefTitle": "DB2-z/OS: SQLCODE -407 during upgrade phases SHADOW_IMPORT_*", "RefUrl": "/notes/977788 "}, {"RefNumber": "932894", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Performance of upgrade phase RUN_RSCPFROM", "RefUrl": "/notes/932894 "}, {"RefNumber": "663240", "RefComponent": "BC-UPG-RDM", "RefTitle": "Repairs for upgrade to Basis 640", "RefUrl": "/notes/663240 "}, {"RefNumber": "882751", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Avoid SQLCODE -601 in R3up phase PARMVNT", "RefUrl": "/notes/882751 "}, {"RefNumber": "843356", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2-z/OS: Upgrade phase JOB_RSSWINCV fails", "RefUrl": "/notes/843356 "}, {"RefNumber": "726215", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Upgrade to ECC 5.0 - free space", "RefUrl": "/notes/726215 "}, {"RefNumber": "726216", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Upgrade to SCM 4.1 - free space", "RefUrl": "/notes/726216 "}, {"RefNumber": "726217", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Upgrade to SRM 4.0 - free space", "RefUrl": "/notes/726217 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}