{"Request": {"Number": "2215852", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1605, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018163452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002215852?language=E&token=5A804F7C9120DD324D2ABB91104C7A15"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002215852", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002215852/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2215852"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.03.2017"}, "SAPComponentKey": {"_label": "Component", "value": "CA-FLE-MAT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Field Lenght Extension for Material"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross Application Field Lenght Extension", "value": "CA-FLE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Field Lenght Extension for Material", "value": "CA-FLE-MAT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE-MAT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2215852 - Material Number Field Length Extension: Code Adaptions"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The material&#160;number and related data types&#160;are extended.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In SAP S/4HANA, the material field length&#160;has been&#160;extended from 18 to 40 characters. This change was first implemented in SAP S/4HANA, on-premise edition 1511 and affects this release and higher releases.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Introduction</span></p>\r\n<p>In SAP S/4HANA, the maximum field length of the material number&#160;has been&#160;extended from 18 to 40 characters.&#160;Further fields that are not pure material numbers&#160;but may contain a material number occasionally have&#160;also been extended.</p>\r\n<p>The extensions were mainly realized via domain extensions, which guarantee that most of the affected development artefacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases.&#160;This also applies to customer code that references&#160;the extended domains. For more background information about the field length extensions in general, see SAP Note <a target=\"_blank\" href=\"/notes/2215424\">2215424</a>. Nevertheless, there are some artefacts that may require manual adjustments, for example, UI-related artefacts like ABAP Dynpros and WebDynpros. For more information about required UI adjustments, see SAP&#160;Note <a target=\"_blank\" href=\"/notes/2214790\">2214790</a>.</p>\r\n<p>The field length extensions also apply to parameters of all interfaces that are&#160;designed to be&#160;called only within one system,&#160;that is,&#160;local function modules, class methods, BAdIs etc. In the types and structures used as parameters in these local calls, the material number has simply been extended to 40 characters. Other parameters based on further extended fields&#160;have been extended accordingly. This was&#160;also done for not-released remote-enabled function modules&#160;because the main use case for such function modules is an internal decoupling within one system (e.g. parallel processing or asynchronous background processing).</p>\r\n<p>For released interfaces that are usually called by remote systems, a different way which&#160;keeps these interfaces compatible to the corresponding SAP ERP interfaces, has been chosen. For more information about compatibility of external interfaces, see SAP&#160;Note <a target=\"_blank\" href=\"/notes/2215424\">2215424</a>.</p>\r\n<p>Due to the remote function call (RFC) protocol, the field length of an already published interface parameter cannot be changed. This would be an incompatible change. Instead of this, the length has to be kept stable and an additional parameter with an appropriate length has to be introduced. Therefore, some attention&#160;must be paid to code where BAPIs or released RFC modules are called. This is explained in the next chapter.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Calling&#160;Extended BAPIs and Released RFC Modules Locally in SAP S/4HANA</span></p>\r\n<p>Although the main purpose of a BAPI or a released RFC module is to serve remote scenarios, they can also be called locally.</p>\r\n<p>Generally, local calls can be identified by syntactic constructs like the following (only showing the most important examples):</p>\r\n<ul>\r\n<li>CALL FUNCTION &#8216;&lt;function name&gt;&#8217;&#160;&#160; (not specifying the DESTINATION addition)</li>\r\n<li>CALL FUNCTION &#8230; DESTINATION &#8216;NONE&#8217;</li>\r\n<li>CALL FUNCTION &#8230; DESTINATION &#8216;&lt;destination_name&gt;&#8217; (when being positively sure &lt;destination name&gt; will always refer to the local system)</li>\r\n</ul>\r\n<p>In case of doubt about the destination in the last case listed above, the guidelines for remote calls of adjusted functions from S/4HANA are a safe fallback. For details, please refer to <a target=\"_self\" href=\"#RemoteCalls\">the next section</a>. The introduction of the necessary mapping calls will not have any negative functional impact in local scenarios but might degrade performance a bit.</p>\r\n<p>When consuming BAPIs and released RFC modules locally, be aware of the following: Structures used in the interfaces of BAPIs and released RFC modules have been extended in a compatible manner if they were affected by the field length extensions. This means that these structures now contain two fields for the same semantic field: a short version of the field with the original field length (for example, a material number field of length 18) and a new long version of the field (for example, a material number field of length 40). The long versions of the fields generally have a &#8220;_LONG&#8221; suffix. Notice that the<strong> short version of the field is only relevant in remote communication</strong> scenarios where the target system may be a system that is not enabled for the extended version of the field. For more information about compatibility of external interfaces, see SAP Note <a target=\"_blank\" href=\"/notes/2215424\">2215424</a>.</p>\r\n<p>Internally, the SAP S/4HANA system is capable of working&#160;with the extended version of the field. This also means that you have to fill the long version of the field when exporting data in a local BAPI or released RFC module call, and that you have to interpret the extended version of the field when importing data from a local BAPI call or released RFC module call (a local BAPI or released RFC module call is either a call without destination or a call where the destination is an application server of the same system, see above).</p>\r\n<p>Example: To select a list of materials locally in the SAP S/4HANA system, you are calling BAPI_MATERIAL_GETLIST. The \"Tables\" parameter MATNRSELECTION is used for this purose. It is important that in&#160;system-local function calls, you are using the extended field versions (MATNR_LOW_LONG and MATNR_HIGH_LONG) of the structure when supplying the selection criteria. The resulting list of materials is contained in the \"Tables\" parameter MATNRLIST. When you further process the results list based on the material number, it is important that you evaluate the long version of the material number field (MATNR_LONG).</p>\r\n<p>If you use&#160;the short version of the field in the&#160;standard SAP S/4HANA&#160;program logic (that is,&#160;not in the context of a remote function call),&#160;this is a strong indication for an error. Or the program only works correctly as long as the extended material number functionality is not yet activated since, for example, a long material number cannot be stored in the short version of the field without loss of data. For more information about the activation of the extended material number functionality and its consequences, see SAP&#160;Note <a target=\"_blank\" href=\"/notes/2215424\">2215424</a>. Watch out for places where data is moved from and to the short version of an extended field, where an internal table is sorted by the short version of the field, where logical expressions are using the short version of the field. A detailed example with source code is shown <a target=\"_self\" href=\"#ExampleLocalCall\">in the appendix</a>.</p>\r\n<p>So the overall conclusion is: <br />In SAP S/4HANA, always use the long version of an extended field for your program logic. Then your program is also prepared for the case that the system is operated with extended material number functionality activated. The short versions of extended fields only have to be&#160;filled or evaluated in the context of cross-system calls.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Remotely calling Function Modules in other Systems from Your SAP S/4HANA Code</span></p>\r\n<p><a target=\"_blank\" name=\"RemoteCalls\"></a>&#65279;When a function module that has one of the extended fields in its interface is called in another system, code adaptations may also&#160;be required. Generally, the unchanged target systems of the remote function call expect to receive the shorter versions of the fields. The field length extension raises two compatibility issues.</p>\r\n<ul>\r\n<li>There is a semantic incompatibility since the target system may not be able to work with a longer field (this is currently the case for all systems besides SAP S/4HANA). If you run integration scenarios with systems that are not enabled for the long versions of the extended fields, you cannot activate the extended material number functionality. For more information about the activation of the extended material number functionality, see SAP Note <a target=\"_blank\" href=\"/notes/2215424\">2215424</a>.</li>\r\n<li>But even if the extended material number functionality is not activated, a technical compatibility issue in the RFC communication between SAP S/4HANA and the target system has to be avoided. For example, the MATNR domain on the SAP S/4HANA side now has a length of 40 characters whereas, on the&#160;target side, the domain still a length of 18 characters. Since the RFC protocol assumes binary compatibility on both the sender and receiver side for each parameter in the function module interface, the sent data gets corrupted on receiver side if the field lengths do not match. Due to this fact, it is important that the data is sent in a compatible format. Additionally, one has to consider the fact that the data retrieved from the remote system is in the old format.</li>\r\n</ul>\r\n<p>When&#160;adjusting the remote function calls, two cases can be distinguished:</p>\r\n<ul>\r\n<li>The remotely called function is also known in the SAP S/4HANA code line. These function modules are used in SAP S/4HANA &#8211; SAP S/4HANA integration scenarios or in SAP S/4HANA &#8211; SAP ERP integration scenarios.</li>\r\n<li>The remotely called function is not part of the SAP S/4HANA code line and therefore belongs to other types of integration scenarios, for example SAP S/4HANA - SAP CRM.</li>\r\n</ul>\r\n<p>Let us have a closer look at these two cases and&#160;their consequences.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Remotely Calling a Function Module Known in the S/4HANA Code Line</span></p>\r\n<p>If the called function is a BAPI or a released RFC module, the structures used in the interface have been extended in a compatible manner as already described in the previous chapter. This means the structures contain, for each extended field, two versions of the field with different lengths. For purely local calls, the &#8220;old&#8221; short version of the field is irrelevant, as described in the previous chapter. But for a remote call, the &#8220;old&#8221; short version of the field is very relevant, since the target system may only be able to understand the short version of a&#160;field.</p>\r\n<p>As previously mentioned, internally, SAP S/4HANA&#160;only works with the long version of the field. This now has&#160;the following consequences for remote function calls: If the extended material number functionality is not activated and data is sent (exported) to the target system, the short versions of the fields have to be sent too. If the extended material number functionality is activated, the short versions of the fields can only&#160;be sent, if the material number fits into the field (to avoid&#160;truncations). When retrieving (importing) data from the target system, the relevant data may only be found in the short version of the field, since the target system may be a system that is not able to handle long field versions. Therefore, the data has to be moved to the long version of field for further SAP S/4HANA-internal processing of the retrieved data.</p>\r\n<p>To support the consistent adjustment of remote function calls, SAP provides several &#8220;mapper&#8221; classes that&#160;provide methods for data exchange between the short and long versions of the extended fields in remote interfaces. Each mapper class handles one semantic type, for example, there is a mapper class for characteristic values.</p>\r\n<p>Another example is the&#160;class CL_MATNR_CHK_MAPPER,&#160;which is provided for the material number itself. The class contains method BAPI_TABLES_CONV_TAB that allows the processing of internal tables&#160;that have to be sent out to, or have been retrieved from, a remote system. With the interface parameter IV_INT_TO_EXTERNAL = &#8216;X&#8217;, you can indicate that the internal table&#160;is to&#160;be converted for sending, meaning that the short versions of the fields&#160;are to&#160;be populated (if the extended material number functionality is not activated). With the interface parameter IV_INT_TO_EXTERNAL = &#8216; &#8217;, you can indicate that the remotely retrieved internal table shall be converted for further internal processing, meaning that the long versions of the fields shall be populated from the short version of the field (if the extended material number functionality is not activated). The methods of the mapper classes already consider the activation of the extended material number functionality. A detailed example with source code is shown <a target=\"_self\" href=\"#ExampleRemoteCallOutbound\">in the appendix</a>.</p>\r\n<p>Your coding has to be adjusted in such&#160;a way that the methods of the mapper class&#160;are called for the data that is sent just before the remote call, and the methods of the mapper class&#160;are called for the data&#160;that is retrieved just after the remote call.</p>\r\n<p>More details about the individual mapper classes and their individual methods can be found in later chapters.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number please check note <a target=\"_blank\" href=\"/notes/2381633\">2381633</a> and notes referenced therein for further mapping requirements if&#160;external material numbers are&#160;contained in the interface of the&#160;called function module.</p>\r\n<p>If the remotely called function module is not provided by SAP but by you, you&#160;can extend the function module in a compatible way (as described in the chapter &#8220;Providing a Remote-Enabled Function Module with an Extended Field in the Interface&#8221;), and afterwards adjust the remote calls as described in this chapter.</p>\r\n<p>If the remotely called function module is not part of the SAP S/4HANA code line at all, see the next chapter.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Remotely <span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Calling</span>&#160;a Function Module Unknown in the S/4HANA Code Line</span></p>\r\n<p>If you are calling a remote function module that has no representation in the SAP S/4HANA code line, the structure types you are using to serve the signature of the remote call most probably do not have a short and a long version of the extended field. Most probably, they only have one version of the field which is long (due to the fact that an SAP data type was used, which was extended via changing the length of the used domain).</p>\r\n<p>A possible strategy for adjusting the call could be the following: Rename the long field in the structure type by adding the suffix &#8220;_SHORT&#8221; and replace the type by the short data type SAP provides, for example MATNR18 for the short version of the material number. With this strategy, the structure is now binary compatible to the structure the target system expects. Then add a new parameter with the original name at the end of the structure having the long version of the type, for example MATNR for the long version of the material number. With this approach, your own &#8220;SAP S/4HANA-internal&#8221; code still works with the original field name. When using the structure type for sending (exporting) data in the remote call, you call the corresponding mapper class method for populating the short version of the structure just before the remote call is executed. When using the structure type for receiving (importing) data from the remote call, you call the corresponding mapper class method for populating the long version of the structure just after the remote call is executed.</p>\r\n<p>Another strategy for adjusting the call is to only exchange the data type of the original field with the short version of the type and add a new version of field with the suffix &#8220;_LONG&#8221; at the end of the structure that has the long version of the type. The difference to the previous approach is that you also have to adjust your own SAP S/4HANA-internal code so that the new field name is always&#160;used instead of the original field, since the field with the original name is now too short for internal processing. With this approach, you also use the mapper class methods for data exchange between the short and long version of the field.</p>\r\n<p>If you have activated the DIMP (Discrete Industries &amp; Mill Products) long material number or manufacturer parts number please check note <a target=\"_blank\" href=\"/notes/2381633\">2381633</a> and notes referenced therein for further mapping requirements if&#160;external material numbers are&#160;contained in the interface of the&#160;called function module.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Providing a Remote-Enabled Function Module with an Extended Field in the Interface</span></span></p>\r\n<p>If you are providing a remote-enabled function module in your coding that already contains one of the extended fields in the function module interface, and if you have created own structure types for the interface, these structure types are changed if they contain one of the extended standard data types. Again, one has to distinguish several cases: If the remote-enabled function module is only used system-internally (for background processing) or used for SAP S/4HANA &#8211; SAP S/4HANA integration scenarios, there is no need for further adjustments. But if the remote-enabled function module is potentially called by a system that is not enabled for the extended fields, there is now a compatibility issue.</p>\r\n<p>If the calling system is definitely a system that is not enabled for the extended field, you could solve the compatibility issue in several ways:</p>\r\n<ul>\r\n<li>If the code in the calling system is under your control, you could adjust the remote function call by using a long version of the data type for the RFC execution in the calling system.</li>\r\n<li>Alternatively, you could reset the type of the field to its short version in the SAP S/4HANA system. For example, the structure contains a field for the material number&#160;with data type MATNR. You could change the data type to MATNR18.</li>\r\n<li>The third alternative is to extend your data structure in a compatible manner, that is, in the same way SAP has extended the BAPI structures: Assign the short version of the data type to the original field and add a new field having the long version of the data type at the end of the structure. The advantage of this approach is that the interface is compatible for calling systems&#160;that are not enabled for the long version of the field, and the interface is compatible for calling systems that are enabled for the long version of the field. This approach can even handle a mixed system landscape where both systems that are, and&#160;systems that are not enabled for the extended field are calling your remote-enabled function module. With this approach, you can also use the mapper class methods for converting data that is imported into your remote-enabled function module (just at the beginning of the function module coding) and for converting data that you export from your remote-enabled function module (at the very end of the function module coding).</li>\r\n</ul>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Calling Own Remote Function Modules in OpenFI/BTE Events</span></span></p>\r\n<p>In general, the interface parameters of OpenFI/BTE events have not been changed in a compatible manner. The fields have merely been extended, just&#160;as they have for any other local function module. The assumption is that most functionality implemented in such an event is local functionality.</p>\r\n<p>If&#160;you have registered a function module to be called remotely by such an event, it may therefore be the case&#160;that the&#160;interface used in the remote call is not compatible&#160;because the interface is taken from the interface of the event. If it is not possible to adapt the remote function module in the remote system,&#160;it may be necessary to wrap the remote call with a local function module that accepts the extended parameters and maps to locally defined parameters understood by the remote function module.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">BAPI Extensions</span></span></p>\r\n<p>In several BAPIs there are EXTENSIONIN/EXTENSIONOUT parameters&#160;that can be used to send or receive additional data in the BAPIs.</p>\r\n<p>Special care&#160;must be taken if the local structures used to move data from and to the abstract extension fields contain a field that is extended in length or includes concatenated data containing&#160;one of the extended fields. If a remote caller uses copies of these structures, he will usually not be aware of the new length, and therefore the exchanged data will not fit anymore.</p>\r\n<p>To avoid this, you have to do one of the following:</p>\r\n<ul>\r\n<li>Adapt the structure that is used in the extension parameters&#160;so&#160;that it still uses the old length for any of the extended fields. This is only possible as long as the new possible field length is not used. If you use this approach, you must&#160;ensure that in&#160;the implementation that handles the extension structures, the&#160;correct mapping between the external structures - with the shortened field - and the internal structure - with the extended field - is implemented. For this mapping, you will usually need two different structures. You can use the mapper classes described below to implement a proper mapping.</li>\r\n<li>Adapt the structure used by the remote system so that it fits to the changed structure in the SAP S/4HANA system.</li>\r\n</ul>\r\n<p>For some BAPI extensions, the structure to describe the key is delivered by SAP. If such a delivered key contains an extended field, SAP will deliver the old existing structure with the old length and will in addition provide a new structure (usually called *2) that is capable of handling the new field. This ensures that consumers using the old key structure do not have to change. If the possibility of handling longer field content is needed, a switch to the new structure must be carried out.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Mapper Classes</span></span></p>\r\n<p>Several mapper classes are provided to support the required code adaptions that are induced by the field length extensions in the context of remote communication interfaces.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Class CL_MATNR_CHK_MAPPER</span></span></p>\r\n<p>This class supports the conversion of data containing the material number field with several public methods. The mapper methods always work on \"tuples\" of fields that belong together&#160;because they transfer the same material. Such a tuple is usually MATERIAL and MATERIAL_LONG (there may be additional fields representing the same material like MATERIAL_EXTERNAL, MATERIAL_VERSION, and MATERIAL_GUID, which are relevant if you have migrated from an SAP ERP system with active DIMP LAMA Switch). Note that a structure can have more than one of such \"tuples\".&#160; In case of single parameters (that is,&#160;not structures), these \"tuples\" may be a combination of single parameters.</p>\r\n<p>Here we only describe which methods are available and&#160;should be called. A detailed description of every single method can be found as a coding comment within every method.</p>\r\n<p><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri','sans-serif'; line-height: 115%; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-hansi-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-bidi-theme-font: minor-bidi; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Method CONVERT_ON_OUTPUT</span></span></span></p>\r\n<p>This method handles individual material number fields. This method can be called immediately before a remote call is executed to convert the &#8220;internal representation&#8221; to the &#8220;external representation&#8221;, which may be needed for calling a remote interface. Another use case of the method is inside a remote-enabled function module where you have to send out the external representation. In this context, the method can be called at the very end of the function module.</p>\r\n<p>In the importing parameter IV_MATNR40, the method always expects the new 40 character representation. The exporting parameters are the old 18 character representation EV_MATNR18 and, additionally, the new 40 character representation EV_MATNR40. The parameter EV_MATNR18 corresponds to the original old material number field in the RFC interface (for example field MATNR) and the parameter EV_MATNR40 corresponds to the new long material number field (for example field MATNR_LONG). The rest of the exporting parameters are related to the old &#8220;DIMP LAMA&#8221; functionality, which are relevant if you have migrated from an SAP ERP system with active DIMP LAMA Switch.</p>\r\n<p><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri','sans-serif'; line-height: 115%; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-hansi-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-bidi-theme-font: minor-bidi; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Method CONVERT_ON_INPUT</span></span></span></p>\r\n<p>This method is complementary to method CONVERT_ON_OUTPUT. It can be called immediately after a remote call is executed to convert the &#8220;external representation&#8221; retrieved to the &#8220;internal representation&#8221;. Another use case of this method is inside a remote-enabled function module where you also have to convert the external representation retrieved from the external system into the internal representation. In this context, the method can be called at the very beginning of the function module.</p>\r\n<p>The export parameter is EV_MATNR40, that is, the new 40 character representation. The import parameter belongs to the old 18 character representation (IV_MATNR18), the fields corresponding to the old &#8220;DIMP LAMA&#8221; representation and the new 40 character representation (IV_MATNR40). The method handles the situation where the remote system sends the old representation or already the new representation.</p>\r\n<p>The method can also translate 'X' flags that are used to indicate which fields are changed. The output is&#160;simply accumulated from the input: If at least one 'X' is set, the 'X' will also be set in the output. Note that the method takes the 'X' into account when determining which of the fields of the input shall be used to populate the output - contents of fields with 'X' set are preferred.</p>\r\n<p><span lang=\"EN-US\" style=\"font-size: 11pt; font-family: 'Calibri','sans-serif'; line-height: 115%; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-hansi-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-bidi-theme-font: minor-bidi; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Methods BAPI_TABLES_CONV_TAB (..._X_IDX, ..._XNUM, ..._ X_NAMES, ..._X_NAM_MAT)</span></span></span></p>\r\n<p>This group of methods handles the conversion of internal tables containing material number fields in its structure type (for example MATERIAL and MATERIAL_LONG in structures which have been extended in a compatible way). The changing parameter CT_MATNR is for the internal table that is&#160;to be converted.</p>\r\n<p>With the importing parameter IV_INT_TO_EXTERNAL, the direction of the conversion is defined. If the parameter is equal to &#8216;X&#8217;, the short version of the field is populated from the long version field, which is required when data is sent to a remote system. This means that you typically call this method with IV_INT_TO_EXTERNAL = &#8216;X&#8217; just&#160;before an execution of a remote call. If you provide a remote-enabled function module that is called by a remote system, then you call this method with IV_INT_TO_EXTERNAL = &#8216;X&#8217; at the very end of the function module, for the conversion of the data to be sent out.</p>\r\n<p>If the parameter is equal to &#8216; &#8217;, the long version of the field is populated from the short version field, which is required when data is retrieved from a remote system. That means that you typically call this method with IV_INT_TO_EXTERNAL = &#8216; &#8217; just after a remote call&#160;has been&#160;executed. If you provide a remote-enabled function module&#160;that is called by a remote system, then you call this method with IV_INT_TO_EXTERNAL = &#8216; &#8217; at the very beginning of the function module, to convert the data sent by the remote system.</p>\r\n<p>To be able to perform the conversion, the method needs to know the name of material number fields in the structure type and how they are related to each other which field names belong to the same tuple. You have to provide this information via the importing parameter IT_FNAMES. Each record in the table describes a tuple of field names. In the structure type of IT_FNAMES, field &#8220;LONG&#8221; is reserved for the field name of the long version of the material number, whereas field &#8220;INT&#8221; is reserved for the field name of the short version of the material number. The fields &#8220;EXT&#8221;, &#8220;VERS&#8221;, and &#8220;GUID&#8221; are reserved for the field names of the&#160;DIMP LAMA functionality.</p>\r\n<p>Some BAPIs contain \"X-flag tables\" that correspond to data tables. In these tables, usually one record exists per data record and describes which fields&#160;are to be&#160;be updated. To match the X-flag record from the one table to the data record in the structure used in the x-table, a key is defined. The values in these key fields are used to find the fitting record in the data table. Unfortunately there is no real \"convention\" on how these key fields are named, but the conversion methods usually have to do this mapping as well. So we have provided four different methods with different possibilities to provide information for the mapping. In these methods, the corresponding X-flag table parameter is CT_MATNRX. Additionally, you have to fill the *_x fieldnames in IT_FNAMES as well when you provide such a table. The mapping methods only interpret the x-table when data is imported, i.e. when IV_INT_TO_EXTERNAL = &#8216; &#8217;.</p>\r\n<p>A short description of the mapping methods follows:</p>\r\n<p><strong>Method BAPI_TABLE_CONV_TAB</strong></p>\r\n<p>This method can be used if NO X-flag table is available in the structure type.</p>\r\n<p><strong>Method BAPI_TABLES_CONV_TAB_X_IDX</strong></p>\r\n<p>The interface of this method additionally contains table IT_MATNR_DATA_TO_X_LINE_ASS. The structure used here contains the index of a record in the data table and the index of the corresponding record in the X-flag table. The method will just do index reads on the X-flag table to get the matching X-flag record.</p>\r\n<p><strong>Method BAPI_TABLES_CONV_TAB_X_NUM</strong></p>\r\n<p>The interface of this method additionally contains parameter IV_MATNR_DATA_TO_X_NUM_KEY_FLD. The assumption here is that the data table and the X-flag table have the same structure with the same sequence of fields, and that a certain number of fields AT THE BEGINNING of the structure form the key. You&#160;need&#160;to provide the number of fields. The method will then determine the names of these fields by itself. Note: The fields have to be at the beginning and they have to be in the same sequence!</p>\r\n<p>Additionally, the method contains parameter IV_WEAK_UNIQUE_CHECK. If this parameter is set to&#160;'&#160;' (the default), the method will check that only ONE record in the X-flag table fits to a given data record. If that is not the case, an error will be raised. If the flag is set to &#8216;X&#8217;, just the first fitting record in the X-flag table will be taken.</p>\r\n<p><strong>Method BAPI_TABLES_CONV_TAB_X_NAMES</strong></p>\r\n<p>The interface of this method additionally contains parameter IT_MATNR_DATA_TO_X_KEY_FNAMES. This is the most generic method. A record of this table describes which field of the key of the X-structure fits to which key field of the data structure. All other parameter works as described in the previous method.</p>\r\n<p><strong>Method BAPI_TABLES_CONV_TAB_X_NAM_MAT</strong></p>\r\n<p>This is a special method that is to be used in case the key that is used to match the data record and the X-flag record is a material by itself,&#160;that is,&#160;needs to be mapped. In this special case, the method has to map the MATNR to the LONG field before the matching is done. So the fields that need to be used to fill the LONG field in compatibility mode need to be specified as well.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Method BAPI_TABLES_CONV_RNG</span></p>\r\n<p>This method can be used for&#160;RANGE parameters immediately at the beginning of a remote-enabled function module to check the content and make sure that a correct range on MATNR_LONG is set. The assumption is that the range contains several LOW and HIGH fields but only one SIGN/OPTION combination.</p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Appendix</span></p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Example of local call to released BAPI function module</span></p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><a target=\"_blank\" name=\"ExampleLocalCall\"></a>&#65279;</span>Let&#8217;s assume a fictional example in which the previous material number of the first material with a material number starting with &#8220;MAT_A&#8221; shall be retrieved. For this purpose the two BAPIs BAPI_MATERIAL_GETLIST and BAPI_MATERIAL_GETDETAIL were used.</p>\r\n<p>The source code <strong>before the adaptations</strong> could look as follows:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;&#160;DATA:&#160;ls_matnr_selection&#160;TYPE&#160;bapimatram,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; lt_matnr_selection&#160;TYPE&#160;STANDARD&#160;TABLE&#160;OF&#160;bapimatram,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160; &#160;&#160;&#160;&#160;&#160;&#160;&#160;lt_material_list&#160;&#160;&#160;&#160;&#160; TYPE&#160;STANDARD&#160;TABLE&#160;OF&#160;bapimatlst,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;ls_material_list&#160;&#160;&#160;&#160;&#160; TYPE&#160;bapimatlst.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-option&#160;&#160;&#160;&#160;=&#160;'CP'.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-sign&#160;&#160;&#160;&#160;&#160;&#160;=&#160;'I'.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-matnr_low&#160;=&#160;'MAT_A*'.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;INSERT&#160;ls_matnr_selection&#160;INTO&#160;TABLE&#160;lt_matnr_selection.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;CALL&#160;FUNCTION&#160;'BAPI_MATERIAL_GETLIST'</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;TABLES</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;matnrselection&#160;=&#160;lt_matnr_selection</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;matnrlist&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; =&#160;lt_material_list.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; SORT lt_material_list BY material.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;READ&#160;TABLE&#160;lt_material_list&#160;INTO&#160;ls_material_list&#160;INDEX&#160;1.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;DATA:&#160;ls_material_general_data&#160;TYPE&#160;bapimatdoa,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;lv_old_mat_no&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;TYPE&#160;bismt.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;CALL&#160;FUNCTION&#160;'BAPI_MATERIAL_GET_DETAIL'</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;EXPORTING</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;material&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;=&#160;ls_material_list-material</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;IMPORTING</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;material_general_data&#160;=&#160;ls_material_general_data.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;lv_old_mat_no&#160;=&#160;ls_material_general_data-old_mat_no.</span></p>\r\n<p><br />To select a list of materials locally in the SAP S/4HANA system, you are calling BAPI_MATERIAL_GETLIST. The \"Tables\" parameter MATNRSELECTION is used for this purpose. It is important that in system-local function calls, you are using the extended field versions (MATNR_LOW_LONG and MATNR_HIGH_LONG) of the structure when supplying the selection criteria. The resulting list of materials is contained in the \"Tables\" parameter MATNRLIST. When you further process the results list based on the material number, it is important that you evaluate the long version of the material number field (MATNR_LONG). <br />To get the details of the found materials BAPI_MATERIAL_GET_DETAIL is called. Here the previous scalar parameter MATERIAL is replaced with a new parameter MATERIAL_LONG which is important to be used in the local calls. Also the result of the call in parameter MATERIAL_GENERAL_DATA now contains a new, long field for the previous material number (OLD_MAT_NO_LONG) which needs to be used.</p>\r\n<p>So <strong>the adapted code</strong> looks as follows:</p>\r\n<p>&#160;&#160;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">DATA:&#160;ls_matnr_selection&#160;TYPE&#160;bapimatram,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;lt_matnr_selection&#160;TYPE&#160;STANDARD&#160;TABLE&#160;OF&#160;bapimatram,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;lt_material_list&#160;&#160;&#160;TYPE&#160;STANDARD&#160;TABLE&#160;OF&#160;bapimatlst,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ls_material_list&#160;&#160;&#160;TYPE&#160;bapimatlst.</span></p>\r\n<p><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-option&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;=&#160;'CP'.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-sign&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;=&#160;'I'.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-<strong>matnr_low_long</strong>&#160;=&#160;'MAT_A*'.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;INSERT&#160;ls_matnr_selection&#160;INTO&#160;TABLE&#160;lt_matnr_selection.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;CALL&#160;FUNCTION&#160;'BAPI_MATERIAL_GETLIST'</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;TABLES</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;matnrselection&#160;=&#160;lt_matnr_selection</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;matnrlist&#160;&#160;&#160;&#160;&#160;&#160;=&#160;lt_material_list.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160; SORT lt_material_list BY <strong>material_long</strong>.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;READ&#160;TABLE&#160;lt_material_list&#160;INTO&#160;ls_material_list&#160;INDEX&#160;1.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;DATA:&#160;ls_material_general_data&#160;TYPE&#160;bapimatdoa,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;lv_old_mat_no&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;TYPE&#160;bismt.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;CALL&#160;FUNCTION&#160;'BAPI_MATERIAL_GET_DETAIL'</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;EXPORTING</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;<strong>material_long</strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;=&#160;ls_material_list-<strong>material_long</strong></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;IMPORTING</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;material_general_data&#160;=&#160;ls_material_general_data.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;lv_old_mat_no&#160;=&#160;ls_material_general_data-<strong>old_mat_no_long</strong>.&#65279;</span></p>\r\n<p><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\"><span style=\"color: #365f91; font-family: Cambria; font-size: x-large;\">Example of remote call to released BAPI function module known in S/4HANA</span></span></p>\r\n<p><a target=\"_blank\" name=\"ExampleRemoteCallOutbound\"></a>&#65279;Let&#8217;s assume the same fictional example as described in the section for local calls. in which the previous material number of the first material with a material number starting with &#8220;MAT_A&#8221; shall be retrieved. For this purpose the two BAPIs BAPI_MATERIAL_GETLIST and BAPI_MATERIAL_GETDETAIL were used. However, the data shall be retrieved from a remote system via a RFC destination which is contained in lv_destination.</p>\r\n<p><strong>This example assumes, the DIMP LAMA functionality was not activated in the local as well as the remote system</strong>. The adaptation shown below will not be sufficient for DIMP LAMA enabled systems. Please refer to SAP note <a target=\"_blank\" href=\"/notes/2381633\">2381633 </a>and notes referenced therein for more details.</p>\r\n<p>The <strong>source code before the adaptations</strong> could look as follows:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;&#160;&#160;DATA:&#160;lv_destination&#160;&#160;&#160;&#160;&#160;TYPE&#160;rfc_dest,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ls_matnr_selection&#160;TYPE&#160;bapimatram,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;lt_matnr_selection&#160;TYPE&#160;STANDARD&#160;TABLE&#160;OF&#160;bapimatram,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;lt_material_list&#160;&#160;&#160;TYPE&#160;STANDARD&#160;TABLE&#160;OF&#160;bapimatlst,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ls_material_list&#160;&#160;&#160;TYPE&#160;bapimatlst.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-option&#160;&#160;&#160;&#160;=&#160;'CP'.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-sign&#160;&#160;&#160;&#160;&#160;&#160;=&#160;'I'.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-matnr_low&#160;=&#160;'MAT_A*'.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;INSERT&#160;ls_matnr_selection&#160;INTO&#160;TABLE&#160;lt_matnr_selection.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;CALL&#160;FUNCTION&#160;'BAPI_MATERIAL_GETLIST'</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;DESTINATION&#160;lv_destination</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;TABLES</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;matnrselection&#160;=&#160;lt_matnr_selection</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;matnrlist&#160;&#160;&#160;&#160;&#160;&#160;=&#160;lt_material_list.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;SORT&#160;lt_material_list&#160;BY&#160;material.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;READ&#160;TABLE&#160;lt_material_list&#160;INTO&#160;ls_material_list&#160;INDEX&#160;1.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;DATA:&#160;ls_material_general_data&#160;TYPE&#160;bapimatdoa,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;lv_old_mat_no&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;TYPE&#160;bismt.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;CALL&#160;FUNCTION&#160;'BAPI_MATERIAL_GET_DETAIL'</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;DESTINATION&#160;lv_destination</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;EXPORTING</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;material&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;=&#160;ls_material_list-material</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;IMPORTING</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;material_general_data&#160;=&#160;ls_material_general_data.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;lv_old_mat_no&#160;=&#160;ls_material_general_data-old_mat_no.&#65279;</span></p>\r\n<p>To select a list of materials from the remote system, you are calling BAPI_MATERIAL_GETLIST. The \"Tables\" parameter MATNRSELECTION is used for this purpose. It is important that like in in system-local function calls, you are filling the extended field versions (MATNR_LOW_LONG and MATNR_HIGH_LONG) of the structure when supplying the selection criteria. However, in addition it is necessary to call the mapping functionality to also fill the original, short fields for compatibility reasons. The resulting list of materials is contained in the \"Tables\" parameter MATNRLIST. As it is not ensured the remote system supports the extended field length for the material number and fills the new, long fields, the result needs to be mapped to ensure the long fields are always filled. For this purpose, the mapper class is called as well. When you further process the results list based on the material number, it is important that you evaluate the long version of the material number field (MATNR_LONG) like in the case for local calls. <br />To get the details of the found materials BAPI_MATERIAL_GET_DETAIL is called. Here the previous scalar parameter MATERIAL is replaced with a new parameter MATERIAL_LONG which is important to be used in the local calls. To ensure proper functionality, the mapper class needs to be called again and both parameters need to be provided when performing the call. <br />Also, in the result of the call in parameter MATERIAL_GENERAL_DATA the new long field might not have been filled by the remote system for the reasons described before, so the code needs to evaluate it correctly by falling back to the original short field in case the new long field is not filled. Another option would be to call the mapper class again.</p>\r\n<p>So <strong>the adapted code</strong> looks as follows (changed parts are highlighted in <strong>bold</strong>):</p>\r\n<p><strong>Note please: The selection range handling below is shown in an oversimplified way to illustrate the pattern of adaptation and the call of the mapper class. This code is not intended to illustrate productive usage</strong>.</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#65279;&#160;&#160;DATA:&#160;lv_destination&#160;&#160;&#160;&#160;&#160;TYPE&#160;rfc_dest,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ls_matnr_selection&#160;TYPE&#160;bapimatram,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;lt_matnr_selection&#160;TYPE&#160;STANDARD&#160;TABLE&#160;OF&#160;bapimatram,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;lt_material_list&#160;&#160;&#160;TYPE&#160;STANDARD&#160;TABLE&#160;OF&#160;bapimatlst,</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ls_material_list&#160;&#160;&#160;TYPE&#160;bapimatlst.</span></p>\r\n<p><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-option&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;=&#160;'CP'.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-sign&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;=&#160;'I'.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ls_matnr_selection-<strong>matnr_low_long</strong>&#160;=&#160;'MAT_A*'.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;INSERT&#160;ls_matnr_selection&#160;INTO&#160;TABLE&#160;lt_matnr_selection.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;<strong>cl_matnr_chk_mapper</strong><strong>=&gt;</strong><strong>bapi_tables_conv_tab</strong><strong>(</strong><strong><br />&#160;&#160;&#160;&#160;</strong><strong>EXPORTING</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;iv_int_to_external&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>abap_true&#160;</strong><strong>\"convert&#160;internal&#160;to&#160;external</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;it_fnames&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>VALUE&#160;</strong><strong>#</strong><strong>(&#160;(&#160;</strong><strong>int &#160;</strong><strong>=&#160;</strong><strong>'MATNR_LOW'<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong><strong>long&#160;</strong><strong>=&#160;</strong><strong>'MATNR_LOW_LONG'&#160;</strong><strong>)</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>(&#160;</strong><strong>int&#160; </strong><strong>=&#160;</strong><strong>'MATNR_HIGH'&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong><strong>long&#160;</strong><strong>=&#160;</strong><strong>'MATNR_HIGH_LONG'&#160;</strong><strong>)&#160;)</strong><strong><br />&#160;&#160;&#160;&#160;</strong><strong>CHANGING</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;ct_matnr&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>lt_matnr_selection<br />&#160;&#160;&#160;&#160;</strong><strong>EXCEPTIONS</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;excp_matnr_ne&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>1</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;excp_matnr_invalid_input&#160;</strong><strong>=&#160;</strong><strong>2</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>OTHERS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>3&#160;</strong><strong>).</strong><strong><br /></strong>&#160;&#160;IF&#160;sy-subrc&#160;&lt;&gt;&#160;0.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;RAISE&#160;EXCEPTION&#160;remote_call_error.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ENDIF.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;CALL&#160;FUNCTION&#160;'BAPI_MATERIAL_GETLIST'</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;DESTINATION&#160;lv_destination</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;TABLES</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;matnrselection&#160;=&#160;lt_matnr_selection</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;matnrlist&#160;&#160;&#160;&#160;&#160;&#160;=&#160;lt_material_list.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;<strong>cl_matnr_chk_mapper</strong><strong>=&gt;</strong><strong>bapi_tables_conv_tab</strong><strong>(</strong><strong><br />&#160;&#160;&#160;&#160;</strong><strong>EXPORTING</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;iv_int_to_external&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>abap_false&#160;</strong><strong>\"convert&#160;external&#160;to&#160;internal</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;it_fnames&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>VALUE&#160;</strong><strong>#</strong><strong>(&#160;(&#160;</strong><strong>int&#160; </strong><strong>=&#160;</strong><strong>'MATERIAL'&#160;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; </strong><strong>long&#160;</strong><strong>=&#160;</strong><strong>'MATERIAL_LONG'&#160;</strong><strong>)&#160;)</strong><strong><br />&#160;&#160;&#160;&#160;</strong><strong>CHANGING</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;ct_matnr&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>lt_material_list<br />&#160;&#160;&#160;&#160;</strong><strong>EXCEPTIONS</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;excp_matnr_ne&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>1</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;excp_matnr_invalid_input&#160;</strong><strong>=&#160;</strong><strong>2</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>OTHERS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>3&#160;</strong><strong>).</strong><strong><br /></strong>&#160;&#160;IF&#160;sy-subrc&#160;&lt;&gt;&#160;0.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;RAISE&#160;EXCEPTION&#160;remote_call_error.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ENDIF.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;SORT&#160;lt_material_list&#160;BY&#160;<strong>material_long</strong>.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;READ&#160;TABLE&#160;lt_material_list&#160;INTO&#160;ls_material_list&#160;INDEX&#160;1.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><strong>&#160;&#160;cl_matnr_chk_mapper</strong><strong>=&gt;</strong><strong>convert_on_output</strong><strong>(</strong><strong><br />&#160;&#160;&#160;&#160;</strong><strong>EXPORTING</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;iv_matnr40&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>ls_material_list</strong><strong>-</strong><strong>material_long<br />&#160;&#160;&#160;&#160;</strong><strong>IMPORTING</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;ev_matnr18&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>ls_material_list</strong><strong>-</strong><strong>material<br />&#160;&#160;&#160;&#160;</strong><strong>EXCEPTIONS</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;excp_matnr_invalid_input&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>1</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;excp_matnr_not_found&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>2</strong><strong><br />&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>OTHERS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>3&#160;&#160;</strong><strong>).</strong><strong><br /></strong>&#160;&#160;IF&#160;sy-subrc&#160;&lt;&gt;&#160;0.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;\"RAISE&#160;EXCEPTION&#160;remote_call_error.</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;ENDIF.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;CALL&#160;FUNCTION&#160;'BAPI_MATERIAL_GET_DETAIL'</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;DESTINATION&#160;lv_destination</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;EXPORTING</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;material&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;=&#160;ls_material_list-material</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;<strong>material_long&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><strong>=&#160;</strong><strong>ls_material_list</strong><strong>-</strong><strong>material_long<br /></strong>&#160;&#160;&#160;&#160;IMPORTING</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;&#160;&#160;material_general_data&#160;=&#160;ls_material_general_data.</span><br /><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;<strong>IF&#160;</strong><strong>ls_material_general_data</strong><strong>-</strong><strong>old_mat_no_long&#160;</strong><strong>IS&#160;NOT&#160;INITIAL</strong><strong>.</strong><strong><br />&#160;&#160;&#160;&#160;lv_old_mat_no&#160;</strong><strong>=&#160;</strong><strong>ls_material_general_data</strong><strong>-</strong><strong>old_mat_no_long</strong><strong>.</strong><strong><br />&#160;&#160;</strong><strong>ELSE</strong><strong>.</strong><strong><br /></strong>*&#160;&#160;&#160;Fallback&#160;for&#160;remote&#160;systems&#160;not&#160;supporting&#160;long&#160;material&#160;numbers&#160;&#160;&#160;&#160;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#160;&#160;&#160;&#160;lv_old_mat_no&#160;=&#160;ls_material_general_data-old_mat_no.&#160;</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><strong>&#160;&#160;</strong><strong>ENDIF</strong><strong>.&#65279;</strong></span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "LO-MD-M<PERSON> (Material Master)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D048317)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026009)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002215852/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002215852/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215852/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215852/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215852/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215852/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215852/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215852/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002215852/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2381633", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Material Field Length Extension: additional information for  DIMP systems with active long material number or MPN", "RefUrl": "/notes/2381633"}, {"RefNumber": "2221293", "RefComponent": "CA-CL", "RefTitle": "Classification Field Length Extension: Coding Adoptions", "RefUrl": "/notes/2221293"}, {"RefNumber": "2216654", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - BOR Objects", "RefUrl": "/notes/2216654"}, {"RefNumber": "2215424", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - General Information", "RefUrl": "/notes/2215424"}, {"RefNumber": "2214790", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - UIs", "RefUrl": "/notes/2214790"}, {"RefNumber": "2190420", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA: Recommendations for adaption of customer specific code", "RefUrl": "/notes/2190420"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2443908", "RefComponent": "CA-FLE-MAT", "RefTitle": "SAP S/4HANA: Material Number Field Length Extension in BAPI / BOR method parameters: Restriction Note", "RefUrl": "/notes/2443908 "}, {"RefNumber": "2438006", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: Code Adaptions for compatibly enhanced local function modules", "RefUrl": "/notes/2438006 "}, {"RefNumber": "2438110", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: Code Adaptions for usages of RFC enabled function modules", "RefUrl": "/notes/2438110 "}, {"RefNumber": "2438131", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: Code Adaptions for usages of released RFCs and BAPIs", "RefUrl": "/notes/2438131 "}, {"RefNumber": "2381586", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Material Field Length Extension: communicating external material numbers in systems converted from DIMP LAMA", "RefUrl": "/notes/2381586 "}, {"RefNumber": "2381633", "RefComponent": "CA-FLE-MAT-CNV", "RefTitle": "Material Field Length Extension: additional information for  DIMP systems with active long material number or MPN", "RefUrl": "/notes/2381633 "}, {"RefNumber": "2381584", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension: ALE interface generation and calling of generated ALE outbound modules for changed BOR objects", "RefUrl": "/notes/2381584 "}, {"RefNumber": "2353005", "RefComponent": "SCM-BAS-INT", "RefTitle": "CIF adjustments for material field length extensions (MFLE)", "RefUrl": "/notes/2353005 "}, {"RefNumber": "2330311", "RefComponent": "SCM-BAS-INT", "RefTitle": "CIF Adjustments for MFLE", "RefUrl": "/notes/2330311 "}, {"RefNumber": "2267140", "RefComponent": "CA-FLE-MAT", "RefTitle": "S4TWL - Material Number Field Length Extension", "RefUrl": "/notes/2267140 "}, {"RefNumber": "2218350", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - IDoc Interface/ALE", "RefUrl": "/notes/2218350 "}, {"RefNumber": "2221293", "RefComponent": "CA-CL", "RefTitle": "Classification Field Length Extension: Coding Adoptions", "RefUrl": "/notes/2221293 "}, {"RefNumber": "2216654", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - BOR Objects", "RefUrl": "/notes/2216654 "}, {"RefNumber": "2214790", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - UIs", "RefUrl": "/notes/2214790 "}, {"RefNumber": "2215424", "RefComponent": "CA-FLE-MAT", "RefTitle": "Material Number Field Length Extension - General Information", "RefUrl": "/notes/2215424 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "QRT_STAG", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "SAP_ABA", "From": "75A", "To": "75A", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}