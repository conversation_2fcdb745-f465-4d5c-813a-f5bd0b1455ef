import json
import os

import requests

from get_summary import get_API_token
from summarize_note_level3 import remove_img


def get_completion(msg):
    auth_token = get_API_token()
    svc_url = "https://azure-openai-serv-i057149.cfapps.sap.hana.ondemand.com"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json"
    }
    request_body = {
        "deployment_id": "gpt-4-turbo",
        "temperature": 1,

        "messages": [
            {"role": "system",
             "content": "Assume you are an SAP expert. You are familiar SAP Notes, which describe a software issue "
                        "and its solution, including the symptoms, the cause of the error, and the SAP release and "
                        "support package level in which the error occurs. An SAP Note may also include workarounds "
                        "and links to support packages that solve the problem, the note may reference to other notes "
                        "and help documents. I will provide you with a note, references if exist. Your task is "
                        "to first understand this note, then give a summarize about this note only based on the "
                        "information I give."},
            {"role": "user", "content": msg}
        ]
    }
    response = requests.post(f"{svc_url}/api/v1/completions", headers=headers, json=request_body)
    print(response.status_code)
    if response.status_code != 200:
        return "ERROR"
    return response.json()['choices'][0]['message']['content']

def get_ref_completion(msg):
    auth_token = get_API_token()
    svc_url = "https://azure-openai-serv-i057149.cfapps.sap.hana.ondemand.com"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json"
    }
    request_body = {
        "deployment_id": "gpt-4-turbo",
        "temperature": 1,

        "messages": [
            {"role": "system",
             "content": "Assume you are an SAP expert. You are familiar SAP Notes, which describe a software issue "
                        "and its solution, including the symptoms, the cause of the error, and the SAP release and "
                        "support package level in which the error occurs. An SAP Note may also include workarounds "
                        "and links to support packages that solve the problem, the note may reference to other notes "
                        "and help documents. Sometimes the note may ref to a large number of references. "
                        "I will separate them into groups and provide you a group of references. Your task is "
                        "to first understand this references, then give a summarize about group of references only "
                        "based on the information I give."},
            {"role": "user", "content": msg}
        ]
    }
    response = requests.post(f"{svc_url}/api/v1/completions", headers=headers, json=request_body)
    print(response.status_code)
    if response.status_code != 200:
        return "ERROR"
    return response.json()['choices'][0]['message']['content']

def load_json(note):
    with open(f'sitemNotes/{note}.json', 'r') as f:
        data = json.load(f)
    return data

def getNoteText(note):
    try:
        with open(f'noteSummary/level2/{note}.txt', 'r') as f:
            ref_note_summary = f.read()
        ref_note_content = load_json(note)

        return f"""

            {ref_note_content['Response']['SAPNote']['Title']['value']}
            {ref_note_summary}
            This is then end of SAP Note {ref_note_content['Response']['SAPNote']['Title']['value']}.
        """
    except:
        return f"Note {note} not found."


def getRefDocText(ref_doc):
    filename = ref_doc['RefTitle'].replace('/', '_').replace('\\', '_').replace(':', ' ')
    with open(f'refDocsSummary/{filename}.txt', 'r') as f:
        doc_content = f.read()
    return f"""

            {ref_doc['RefTitle']}[{ref_doc['RefUrl']}]
            {doc_content}
            This is then end of document {ref_doc['RefTitle']}[{ref_doc['RefUrl']}].

        """


def split_list(my_list, parts):
    length = len(my_list)
    return [ my_list[i*length // parts: (i+1)*length // parts] for i in range(parts) ]


def get_note_summary_w_ref(note, third_ref):
    ref_result = []
    references = '        No Reference.'
    if note in third_ref:
        for ref_note in third_ref[note]:
            if ref_note['RefNumber'] != "":
                ref_result.append(getNoteText(ref_note['RefNumber']))
            else:
                ref_result.append(getRefDocText(ref_note))
        references = '\n        '.join(ref_result)

        if len(references) > 300000:
            size = len(references) // 300000 + 1
            parts = split_list(ref_result, size)
            ref_result2 = []
            for part in parts:
                ref_result2.append(get_ref_completion('\n        '.join(part)))
            references = '\n        '.join(ref_result2)
            references = (f"        The references list is quite long, so pre-summary the references by {size} "
                          f"groups.\n        {references}")

    try:
        with open(f'sitemNotes/{note}.json', 'r') as f:
            note_content = json.load(f)
            longText = remove_img(note_content['Response']['SAPNote']['LongText']['value'])
            summary = get_completion(f"""
{note_content['Response']['SAPNote']['Title']['value']}

{longText}

References: 
    This Note refers to:
    {references}

This is then end of SAP Note {note_content['Response']['SAPNote']['Title']['value']}.
""")
        return summary
    except:
        return f"Note {note} not found."


if __name__ == '__main__':
    with open('ref_map/second_ref.json', 'r') as f:
        second_ref = json.load(f)
    notes = set(json.load(open('ref_map/first_ref_note.json', 'r')))
    for note in notes:
        if os.path.isfile(f'noteSummary/level1/{note}.txt'):
            print("already exist")
        else:
            notes_summary = get_note_summary_w_ref(note, second_ref)
            with open(f'noteSummary/level1/{note}.txt', 'w') as txt_file:
                txt_file.write(str(notes_summary))
