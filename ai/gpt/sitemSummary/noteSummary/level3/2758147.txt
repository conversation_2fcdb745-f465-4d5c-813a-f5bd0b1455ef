SAP Note 2758147 addresses an issue with the Operational Data Provisioning (ODP) system when used in combination with an unserialized V3 update in the logistics cockpit. The problem arises because ODP relies on EOIO (Exactly Once in Order) delta logic to ensure data integrity, which cannot be assured with unserialized V3 updates during high volume data processing. As a result, there is a risk of missing data in ODP queues which could affect applications that depend on these data feeds, like SAP BW.

To resolve this issue, the note suggests changing the update mode from 'Unserialized V3 Update' to 'Queued Delta'. However, before making this change, users must ensure that all existing delta records have been processed by the last V3 collective run, and no new delta records are generated between the completion of this run and the customization change. 

If there is data missing due to previous runs, the note advises recompiling the statistics data as per the relevant application documentation and performing a new initial load into the target system to restore the missing information.

Additionally, the note references SAP Note 500426 for more information on the various update modes and limitations associated with the V3 update method.