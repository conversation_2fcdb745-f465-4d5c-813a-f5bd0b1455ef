SAP Note 1820384 addresses an issue where users experience a locking problem when browsing for Analysis Office workbooks or presentations via the Roles view in the Open Dialog. This problem persists until the user closes their Analysis Office application, either Excel or PowerPoint.

Key points from the SAP Note are as follows:

- **Symptom**: Analysis Office workbooks or presentations are locked for the end user when accessed through the Roles view of the Open Dialog.
  
- **Other Terms**: The issue relates to Analysis Office, TLOGO, Report Locking, and is associated with MS Office documents.
  
- **Reason**: The cause of this issue is identified as a program error.
  
- **Solution**:
  The solution involves importing the appropriate Support Package for the specific SAP NetWeaver BW (Business Warehouse) version the customer is using. Details of the versions and corresponding Support Packages are:
  - SAP NetWeaver BW 7.01: Import Support Package 14 (SAPKW70114).
  - SAP NetWeaver BW 7.02: Import Support Package 14 (SAPKW70214).
  - SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010).
  - SAP NetWeaver BW 7.31: Import Support Package 8 (SAPKW73108).
  - SAP NetWeaver BW 7.40: Import Support Package 3 (SAPKW74003).

Each Support Package will become available once the associated SAP Note is released for customers. These related Notes contain more detailed information on each Support Package:

- SAP Note 1794836 for BW 7.01
- SAP Note 1800952 for BW 7.02
- SAP Note 1810084 for BW 7.30
- SAP Note 1813987 for BW 7.31
- SAP Note 1818593 for BW 7.40

In case of urgency, the provided correction instructions can be applied. Users are also advised to check SAP Note 875986 for transaction SNOTE before applying the corrections.

The Note suggests that these corrections might be available before the full Support Package release, labeled as a "preliminary version."