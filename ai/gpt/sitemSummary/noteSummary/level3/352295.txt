SAP Note 352295 discusses the available options for implementing single sign-on (SSO) functionality on Microsoft Windows platforms when accessing SAP R/3 systems. The note explains that to achieve this, one can leverage the Microsoft Windows Kerberos or NTLM authentication mechanisms.

The solution provided by SAP to integrate SSO is via the BC-SNC software interface, which is compliant with the GSS-API v2 standard, outlined in the RFCs 2743 and 2744 by the Internet Engineering Task Force (IETF). The SAP Single Sign-On product uses this interface and supports various SSO scenarios, including those that reuse Microsoft Windows authentication credentials. Further information on this product is available on the SAP community network site.

Additionally, SAP has a certification program (CSP) for third-party SSO solutions to ensure interoperability. The note mentions that past versions included the GSSKRB5.dll download but states that further development has been discontinued. While SAP continues to support its own wrapper DLLs, it does not offer support for the actual underlying Microsoft Kerberos and NTLM technologies, which are proprietary to Microsoft (further discussed in SAP Note 150380). However, the wrapper DLL files (gsskrb5.dll, gx64krb5.dll, gssntlm.dll, gx64ntlm.dll) are still available for download as attachments in SAP Note 2115486 (win32sso.zip and win64sso.zip).