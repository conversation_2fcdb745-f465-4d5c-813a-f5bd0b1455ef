SAP Note 1847292 addresses an issue where users are unable to scroll through the hierarchy list when using a hierarchy InfoPackage with a new DataSource in a flat file source system. This is identified as a program error.

Solutions are provided for multiple versions of SAP NetWeaver BW:

- For SAP NetWeaver BW 7.00: Import Support Package 31 (SAPKW70031).
- For SAP NetWeaver BW 7.01: Import Support Package 14 (SAPKW70114).
- For SAP NetWeaver BW 7.02: Import Support Package 14 (SAPKW70214).
- For SAP NetWeaver BW 7.11: Import Support Package 12 (SAPKW71112).
- For SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010).
- For SAP NetWeaver BW 7.31: Import Support Package 09 (SAPKW73109).
- For SAP NetWeaver BW 7.40: Import Support Package 3 (SAPKW74003).

The note also provides references to other notes which contain more details about the respective Support Packages and their release status for customers. Additionally, it mentions that users can implement correction instructions as an advance correction if the matter is urgent and directs them to refer to Note 1668882 for information on using transaction SNOTE.

Overall, the note recommends importing the appropriate Support Package for the user's SAP NetWeaver BW version to resolve the scrolling issue in the hierarchy list when using a hierarchy InfoPackage with a flat file source system and a new DataSource.