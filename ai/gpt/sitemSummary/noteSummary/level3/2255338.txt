SAP Note 2255338 provides release information for the SAP Fiori App 'Monitor Capacity Utilization', which is a component of the SCM PPDS 1.0 on ERP 6.0 EHP8. It is designed for Production Planning and Detailed Scheduling (PP/DS).

Key points from the note:

- The App involves both frontend and backend components:
   - Frontend: Software component UIPPDS01 (Release 100), Package UI_PPDS_CAPACITY.
   - Backend: Software component SCMPPDS (Release 200), Package /SAPAPO/PPDS_ODATA.

- The SAP HANA database is a mandatory prerequisite for using this app.

- The first shipment will include the software components starting with SP00 for UIPPDS01 (UI) and SP01 for SCMPPDS.

- Limitations: 
   - Small display mobile devices like smartphones, iPad Mini, Samsung Galaxy Pad 3 (7"), and Nexus 7 are not supported.
   - Hybrid Win8 touch devices are also not supported.
   - Navigating to back-end transactions on tablet devices is not supported.
   - Specific prerequisites are needed for navigating to the back-end transaction /SAPAPO/CPDS0, including SAP Business Client 6.0 and Software component SAP_UI (Release 750 SP01).

- Required PFCG Roles: SAP_PPDS_BCR_PRODPLANNER needs to be assigned for proper access.

- For a complete setup, additional SAP Notes must be referred to:
   - Frontend server (UI): SAP Note 2258460, which addresses translation issues and technical PFCG role requirements.
   - Backend server: SAP Notes 2264617 and 2270805, which extend OData service response and cover backend PFCG role requirements not delivered with SP01, respectively.

- The second shipment will include updates with SP01 for UIPPDS01 (UI) and SP02 for SCMPPDS, maintaining the same devices and navigation limitations as stated.

In summary, this SAP Note is crucial for users who plan to implement or upgrade the 'Monitor Capacity Utilization' Fiori App for PP/DS on EHP8 for SAP ERP 6.0 powered by SAP HANA, as it outlines the key components, prerequisites, device limitations, and necessary user roles required for successful deployment and operation of the App.