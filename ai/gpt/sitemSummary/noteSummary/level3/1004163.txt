SAP Note 1004163 addresses an issue experienced with specific transactions in IS-U (Utilities Industry Solution) after installing patches for the add-on CEEISUT 4.72 with AOPs (Add-On Patches) lower than AOP 11. The affected transactions include EA19 (Create Bill, Individual), EA62 (Change Budget Billing Plan), and EA11 (Create Partial Bills, Mass Creation), which could lead to an ABAP dump with a runtime error UNICODE_TYPES_NOT_CONVERTIBLE due to a mismatch in table structures.

The exact problem occurs when trying to insert data from L_DFKKOP_ITAB, which has the structure of the DFKKOP table, into the DFKKMOP table. Two fields present in DFKKOP are missing in DFKKMOP, causing the types of these tables to not match. These fields are /SAPCE/IURU_BTYP and /SAPCE/VAT_DATE.

To resolve the issue, the note advises customers to install AOP 11 for CEEISUT 4.72 as a solution since it contains the necessary APPEND structures that should be present in the DFKKMOP database table. If it is not possible to install AOP 11, the note provides manual instructions to add the missing APPEND structures to the DFKKMOP table, detailing the specific fields and structure includes that need to be added for Russian and Polish-specific enhancements.