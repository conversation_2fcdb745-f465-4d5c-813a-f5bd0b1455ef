The SAP Note 604962 addresses a problem encountered in the German healthcare sector while using the report RNAP21F01 for data determination according to §21 of the KhEntgG (German hospital remuneration law). Users may experience many error messages with this report due to the inability to account for individual case constellations.

The note provides a solution that allows users to recognize and correct these individual constellations during the creation of individual files such as FAB, FALL, ICD, and OPS. The solution consists of several steps:

1. Download and unpack the relevant file for the respective version of IS-H (International Hospital):
   - For IS-H Version 4.63/18, use the file P21_463.ZIP.
   - For IS-H Version 4.63B/8, use the file P21_436B.ZIP.
   - It is important to note that these files are not available through OSS and must be downloaded from the SAP Service Marketplace. The note references SAP Note 13719 for importing attachments.

2. Import the unpacked requests into your SAP system. After doing so, you can use the Business Add-In (BAdI) ISH_P21_TRANSFER with its methods to determine the §21 data on an individual case basis. The methods mentioned are GET_P21_FALL, GET_P21_FAB, GET_P21_ICD, and GET_P21_OPS. Further documentation on the Business Add-In should be consulted for more details.

3. Implement the correction instructions provided in the SAP Note, which will enable the calls of the individual methods of the Business Add-In in the report RNAP21F01.

The note also defines the term "DRG BADI ish_p21_transfer," presumably as the identifier for the Business Add-In to be used for transferring DRG-related data.

Finally, a disclaimer at the beginning of the document indicates that this is a machine-translated version of the original note, which was written in German, and users are encouraged to reference the original document for complete accuracy.