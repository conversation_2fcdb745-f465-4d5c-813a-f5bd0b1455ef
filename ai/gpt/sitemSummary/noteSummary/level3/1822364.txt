SAP Note 1822364 addresses an issue where the value of a variable is lost in scenarios where the 'melting' field is set to true, particularly when the variable screen is called again.

Symptoms: The value of a variable is not retained under specific conditions when melting is true.

Other Terms: The issue involves the BADI F4 and the class CL_RSR_MULTI_VARIABLE.

Reason: The problem is identified as being caused by a program error.

Solution: The note provides different solutions depending on the version of SAP NetWeaver BW:

1. For SAP NetWeaver BW 7.30, users are instructed to import Support Package 10 (SAPKW73010). More details on this package can be found in SAP Note 1810084 titled "SAPBWNews NW7.30 BW ABAP SP10".

2. For SAP NetWeaver BW 7.31, the recommended action is to import Support Package 8 (SAPKW73108), with more information provided in SAP Note 1813987, "Preliminary Version SAPBWNews NW BW 7.31/7.03 ABAP SP8".

3. For SAP NetWeaver BW 7.40, users should import Support Package 3 (SAPKW74003), with further details available in SAP Note 1818593, "Preliminary Version SAPBWNews NW BW 7.4 ABAP SP03".

In urgent situations, the note suggests that users can apply correction instructions, but advises to check SAP Note 875986 for transaction SNOTE beforehand. Additionally, the note indicates that this SAP Note might be available before the support package's release, but may still be labeled as a "preliminary version".

In summary, SAP Note 1822364 provides solutions for an issue in SAP NetWeaver BW versions 7.30, 7.31, and 7.40 where variable values are lost when melting is true and the variable screen is accessed repeatedly. The note includes references to specific support packages and other related SAP Notes for further information and interim corrections.