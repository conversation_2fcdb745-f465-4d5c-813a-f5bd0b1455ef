The SAP Note 821032 provides important information about the new features and corrections implemented in the SAP upgrade utility SAPup, specifically for release 7.00. Below is a summary of the note:

**Symptoms:**
- The note addresses the updates in functionality and bug fixes in SAPup version 7.00.

**Other Terms:**
- The context is related to system upgrades and mentions terms like upgrade, system switch upgrade, and shadow upgrade.

**Reason and Prerequisites:**
- The note is relevant for users planning to upgrade to a system with SAP Basis Release 700.

**Solution:**
- The note recommends replacing the SAPup tool after unpacking the first archives and before executing the upgrade preparation (PREPARE) to avoid errors.
- Users should follow these steps:
  - Terminate SAPup before the "Parameter input" module.
  - Rename SAPup to SAPup_org in the directory <DIR_PUT>/bin or for iSeries in the SAPup library.
  - Download the latest SAPup version from the SAP Service Marketplace and unpack or import it into the relevant directory.
  - Restart the PREPARE process or SAPup.

The note also includes important warnings:
- Users should replace SAPup before the actual upgrade commences.
- Only replace SAPup during the upgrade if advised by the SAP support team.

To check the current SAPup version, users can run the appropriate command based on their operating system (UNIX, NT, or iSeries).

**Which SAPup version is needed:**
- For service release 3 of certain products (e.g., CRM 50 SR3, ERP 05 SR3, etc.), users need version "7.00/3".
- For all other products, version "7.00/2" is needed.
- The note explains how to find out which SAPup version is included in which archive and how to interpret the naming convention.

**Repairs and SAPup versions:**
- The note lists specific patch levels for DB2-z/OS related changes, including new options, support features, and fixes related to DB2 databases on z/OS systems.
- It refers to other SAP notes for further information on certain topics (e.g., SAP note 815202 for details on universal tablespaces).

This SAP Note essentially serves as an upgrade guide for users who need to update their SAPup tool to the latest version, ensuring a smooth upgrade process to SAP Basis Release 700. It includes the steps required to be taken before and during the upgrade and provides instructions for determining the currently used SAPup version as well as the SAPup version required for specific products or releases.