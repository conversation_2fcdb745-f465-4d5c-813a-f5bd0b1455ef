SAP Note 2382176 describes restrictions in the Application Link Enabling (ALE) framework functionality related to the Extended Material Numbers within SAP S/4HANA, on-premise edition 1511 or higher.

Key points from the note include:

- In SAP S/4HANA, material numbers can now be up to 40 characters long, accommodated by adding a long field in ALE IDoc segments.
- ALE framework cannot utilize the long material number in ALE filtering and ALE receiver determination. If the extended material number functionality is not used, these functionalities will continue to work as before.
- ALE auditing may not function correctly in determining the sending business object in both sender and receiver systems if the key of the business object has been changed due to the field length extension.
- The note also mentions that ALE auditing will particularly affect systems converted from a DIMP system with activated long material number or manufacturer parts number functionality. Class-based receiver determination won't work in these cases, even if extended material number functionality is not activated.
- SAP is currently working on a solution to resolve these restrictions.
- Workarounds are suggested, such as adding new ALE objects locally for the extended fields, to address issues with ALE filtering and receiver determination.
- It is recommended not to use ALE auditing functionality in SAP S/4HANA until a solution is available.

The note does not specify when the solution will be made available or provide a release date for an update to resolve these issues. Users are advised to follow the provided workarounds and await further instructions from SAP.