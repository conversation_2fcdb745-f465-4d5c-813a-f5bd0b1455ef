SAP Note 137248 describes an issue with cross-application planning, where the system displays errors related to units when trying to plan a cross-application information structure.

Symptoms:
Users encounter error messages related to units when planning cross-application information structures.

Other Terms:
The situation is commonly associated with SOP (Sales and Operations Planning) and is identified by the error message MA824.

Reason:
The issue arises because units from one application may not be available when another application is updating data into the information structure, which can cause units to be "initial" or undefined. Planning operations require consistent units and cannot proceed with different or undefined units.

Solution:
The note suggests updating the key figures from different applications into the same base unit to avoid initial units, when possible. If the necessary unit conversion is not available, it must be added later by making entries in control tables TMC6U and TMC23. For the production area, the necessary entries are available on the SAP servers and can be imported for different releases (3.x and 4.x). The file names and locations for these entries are provided for both releases. After importing, you would select the base unit of measure for quantity key figures within the production area from the cross-application information structure.

The note provides practical steps to resolve the issue by aligning units across applications and making necessary entries to facilitate the unit conversions. It directs users to specific server locations where the required entries can be found and downloaded.