SAP Note 2267140 addresses the extension of the material number field length from 18 to 40 characters in SAP S/4HANA, starting from the on-premise edition 1511. This is a significant transition for businesses converting their systems to SAP S/4HANA.

Summary of Key Points:

1. **Field Length Extension**: SAP S/4HANA now supports material numbers with up to 40 characters. Related SAP system components have been adapted to accommodate this change.

2. **Technical Adjustments**: The note emphasizes the necessary technical changes due to the field length extension, such as adaptations in custom code, data elements, structures, table types, databases, interfaces, and user interfaces. It also addresses the need for businesses to analyze their own coding for compatibility with the new material number length.

3. **System Internal Coding**: All domain definitions relevant to material numbers have been extended to 40 characters, and wherever a material number is used in system coding, it’s ensured that it can handle the new length without data loss.

4. **Database Storage**: The MATNR field on the database has been extended to 40 characters. For purely numeric material numbers, no data conversion is needed when converting to SAP S/4HANA.

5. **External Interfaces**: External interfaces such as BAPIs, RFCs, and IDocs have been adapted to provide technical-version compatibility by keeping existing fields with their original length and adding new fields for the 40-character material numbers.

6. **Switch for Extended Functionality**: To use the 40-character material number, businesses need to explicitly activate the functionality. By default, the extended material number functionality is switched off, ensuring compatibility within multi-system landscapes.

7. **DIMP LAMA-Functionality**: The DIMP Add-On, which previously provided a solution for long material numbers, is not part of SAP S/4HANA. A separate migration is required for those using DIMP LAMA to the native 40-character material number in S/4HANA.

8. **ALE Change Pointers**: All ALE change pointers must be processed before moving to S/4HANA as changes recorded before conversion will no longer be available compatibly post-conversion.

The note also lists several references to other SAP Notes and documentation that provide more details on specific aspects such as conversion pre-checks, custom code adaptation, and restrictions related to the field length extension. These references should be consulted to ensure a smooth transition and mitigate any issues that may arise due to the extended material number functionality.