SAP Note 1873798 provides instructions on preparing the (A)SCS instances for an upgrade when using Microsoft Failover Clustering. It introduces a Maintenance Flag feature with Kernel 720 PL 102 and saprc.dll 720 PL 311 to prevent unwanted failovers during the upgrade. SAP highly recommends using Kernel 720 PL 102 or higher for the upgrade process.

For users running older kernel versions (700, 701, 710, 711), which are out of maintenance as of August 31st, 2012, SAP refers to note 1629598 for more details, as those versions require different precautions.

The note outlines the procedure to prepare SCS (for Java systems) and ASCS (for ABAP systems) instances before the upgrade, which includes moving cluster groups, taking cluster resources offline, adjusting parameters, and changing the startup type of the (A)SCS service to "automatic" before starting it.

Post-upgrade instructions are also provided to revert the changes, essentially restoring the instance values, bringing services back online, and setting the (A)SCS service startup type back to "manual".

Relevant terms mentioned in the note are Windows Server Failover Clustering (WSFC), Upgrade, Software Update Manager (SUM), High Availability (HA), and MSCS.