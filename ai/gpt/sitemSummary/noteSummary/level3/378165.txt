SAP Note 378165 addresses an issue where, in the standard SAP system, it is not possible to select or display vehicle data for equipment using list processing. This functionality does not come out-of-the-box in standard SAP.

The note pertains to transactions IE36 and IE37 among others, and identifies the root cause as an absence of these functions in the standard SAP system. For the solution to be implemented, customers must have certain prerequisites fulfilled:

1. Your system should be on R/3 Support Package 25 for Release 4.6B or Support Package 14 for Release 4.6C.
2. OSS Notes 366733 and 372736 must be implemented.
3. You should have run the conversion for vehicle data to standard units using report RIXPRA31.

The solution provided is an advance development that requires importing a SAPSERV transport into your system, with the transport files available at a specified ftp URL. Instructions for implementing the required text symbols for report RIFLET20 are also provided.

The note specifies that because these are advanced developments, they do not include customizing transactions and documentation by default, and the created objects are not included in standard R/3 Support Package corrections. Hence, any future changes or fixes have to be manually implemented.

Additionally, maintenance of the activity category for the new transactions is required in table T370A for transactions IE36 and IE37. For Release 4.6C, it is mentioned that the field "maximum speed" along with the corresponding units have been added to table FLEET, and manual source code changes are necessary in program RIFLET20 as per the correction instructions in the SAP Note.

These developments allow users to perform list processing for vehicles where they can select and display vehicle data based on various criteria such as vehicle identification, technical data, and data relevant for shipment, in addition to general equipment data. Settings are also available to configure which fields are to be displayed in the output list.

The note concludes by stating that the vehicle list inherits the same functionalities as found in the equipment list, taking into account units of measure during selection and display.