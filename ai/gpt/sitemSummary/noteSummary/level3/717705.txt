SAP Note 717705 addresses issues that arise in the calculation of Average Weekly Earnings (AWE) for Statutory Sick Pay (SSP) when including gross payments from the current payroll period. According to legislation, employers should consider gross payments from regular payment dates going back eight weeks before the Period of Incapacity for Work (PIW) started, sometimes including the current period's pay. However, including the current period can lead to overstated values because many payments haven't been adjusted for part-period factors due to sickness.

The note outlines several specific issues:

1. Gross payments might be overstated if they are not part-period factored, which can lead to an inaccurate AWE calculation. Two potential solutions are suggested: either repositioning the processing of GBSXP to after the part period factoring or excluding the current period from AWE calculations for SSP.

2. The inclusion of SSP as part of the AWE for the current period can lead to a circular problem where the payroll needs to know how much SSP to pay before it can pay it, which is not feasible. The suggested workaround is to exclude the current period from AWE calculations.

3. Calculating AWE based on payments from multiple employment contracts can be inconsistent if other contracts have not yet been payrolled. The solution proposed is also to exclude the current period from AWE calculations for SSP.

The "Other Terms" section merely lists related terms and identifiers associated with the AWE calculations process (GBSXP, AVERA, T51AV_A).

The "Reason and Prerequisites" section acknowledges the conflicts between legislative requirements and practical payroll processing capabilities.

The proposed "Solution" to these issues is to change payroll customizing settings to not include the current period in AWE calculations for SSP. Specifically, it instructs to "untick" the checkbox for the "Current Period" against the Average Rule "SAWE" in Table T51AV_A. This change will cause the payroll system to disregard the current period in the relevant AWE calculation, aligning the system more closely with the method used by the older AWE solution that also excluded the current period.