SAP Note 1827473 addresses an issue where process chains are not being monitored by the Computing Center Management System (CCMS) despite not being excluded from monitoring. The problem arises because, due to an installation mistake in some releases, the CCMS agent for process chains (RSPC_CCMS_AGENT) is not correctly set up as a background process and is not enabled as a data collection method.

To rectify this issue, the note provides a list of solutions involving the import of specific Support Packages for different SAP NetWeaver BW (Business Warehouse) versions:

- SAP NetWeaver BW 7.00: Import Support Package 31 (SAPKW70031)
- SAP NetWeaver BW 7.01 (SAP NW BW 7.0 Enhancement Package 1): Import Support Package 14 (SAPKW70114)
- SAP NetWeaver BW 7.02 (SAP NW BW 7.0 Enhancement Package 2): Import Support Package 14 (SAPKW70214)
- SAP NetWeaver BW 7.11: Import Support Package 12 (SAPKW71112)
- SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010)
- SAP NetWeaver BW 7.31 (SAP NW BW 7.0 Enhancement Package 3): Import Support Package 08 (SAPKW73108)
- SAP NetWeaver BW 7.40: Import Support Package 03 (SAPKW74003)

Associated with each recommended Support Package, there is a reference to another SAP Note that describes the relevant Support Package in more detail.

For urgent cases, the note advises implementing the correction instructions as an advance correction. Before doing so, users must read SAP Note 875986, which provides guidance on using transaction SNOTE.

Lastly, the note mentions that the SAP Notes related to the Support Packages might be available in a "Preliminary version" before the actual release of the Support Packages. Users should look for the words "Preliminary version" in the short text of the SAP Note to confirm this.