The SAP Note 3342152 addresses an issue encountered while using the SAP S/4HANA Migration Cockpit during the data loading process for inspection plans or routings with characteristics data using the migration objects "Inspection Plan" and "Routing," respectively. The problem arises when using XML templates and manifests as either an error message "Cannot update internal table; contact SAP" (DMC_RT_MSG223) or a message stating "Value &1 can't have more than &2 decimal places" (CNV_DMC_SIN235).

The root cause of the issue lies in two program issues related to the conversion rule: firstly, the input parameter that specifies the number of decimal places allowed is not constrained to a range between 0 to 10, and secondly, there is an incorrect comparison of values with regard to the number of decimal places.

The resolution specified in the note involves implementing Transport-based Correction Instructions (TCIs) related to the problem. There are different TCIs to be applied depending on the release and service pack level of SAP S/4HANA:

- For release 2020, between SP00 and SP06, TCI Note 3342308 should be implemented.
- For release 2021, between SP00 and SP04, TCI Note 3342313 should be implemented.
- For release 2022, between SP00 and SP02, TCI Note 3342337 should be implemented.

The note also cautions that the TCIs will only correct SAP delivered content and updates generated migration objects automatically. Any modifications or copies of the migration object made by the user will not get corrected automatically.

Finally, the note references KBA 2543372 for guidance on how to implement a Transport-based Correction Instruction.