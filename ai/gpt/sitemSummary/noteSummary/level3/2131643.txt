SAP Note 2131643 addresses the transition from SAP Simple Finance add-on 1.0 to SAP Simple Finance, on-premise edition 1503 or 1605. It states that certain Analytics content, such as WebDynpro applications from the add-on, will not work with the new editions and instead are replaced by SAP Fiori applications. 

A table is provided in the note detailing which WebDynpro applications are to be replaced by corresponding SAP Fiori apps. Also, SAP HANA Live Views from the package sap.hba.sfin700 are listed, with indications of which are now replaced by Fiori apps or require customer-built queries. 

The note also warns against using SAP HANA Live Views from the sap.hba.ecc package, as they have not been adjusted to the simplified data model of the SAP Simple Finance add-on. There's a list of views that are either not syntactically correct or have undergone semantic changes, with recommendations for replacements.

To address the solution, the note suggests using the named SAP Fiori apps as replacements. Users who have not yet adopted SAP Fiori should follow the SAP Fiori installation guide. For those who were using the old reports in SAP NetWeaver Business Client (NWBC), they should now access the relevant SAP Fiori apps via the Fiori Launchpad. The note finishes by informing users that selection and display variants from the old system are no longer valid and advises the re-creation of these for the SAP Fiori Apps. It also recommends removing obsolete WebDynpro applications from any custom roles or report launchpads.