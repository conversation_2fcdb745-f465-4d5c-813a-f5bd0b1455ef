SAP Note 510680 addresses an issue encountered when performing selective deletion with time restrictions on a non-cumulative cube in the Business Information Warehouse (BW). 

Symptom: 
Selective deletion based on time restrictions can lead to unexpected results in non-cumulative cubes.

Other Terms:
The note is related to terms such as selective deletion, non-cumulatives, and DELETE_FACTS.

Solution:
The note differentiates between two scenarios regarding the deletion of data:

1. Data not yet compressed - If the data to be deleted hasn't been compressed, it hasn't been factored into the marker value. Deleting this data will alter the 'last value' or the value for 'infinity,' thus impacting the non-cumulative value. An example is given where a marker with a value of 100 exists, and a non-compressed record of 10 is deleted, resulting in the non-cumulative value adjusting from 110 back to 100 for the entire period.

This deletion method should be used when data that was loaded incorrectly into the cube needs to be fully deleted and reloaded. However, one must consider whether it might be more appropriate to delete the complete request to remove incorrect data.

2. Data already compressed - If the data to be deleted has already been compressed, it has been included in the marker value. Deleting this data will not change the last value or the value for 'infinity' because the marker of a compressed cube represents this last value. An example provided illustrates a marker with a value of 110; deleting a compressed record valued at 10 leaves the current value (the marker) unchanged.

This deletion method is recommended when the aim is to remove 'old data' from the cube (e.g., for space-saving reasons) without altering the current data state.

In conclusion, proper attention must be given to whether data has been compressed when selectively deleting records from a non-cumulative cube to ensure expected outcomes. The actions taken will differ based on whether the data removal pertains to recent, uncompressed data (affecting the current non-cumulative values) or to older, compressed data (not affecting the current values but modifying historical data).