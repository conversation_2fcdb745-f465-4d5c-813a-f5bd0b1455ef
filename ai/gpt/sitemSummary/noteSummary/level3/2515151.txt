SAP Note 2515151 addresses the issue where users are unable to find specific migration objects or fields when using the SAP S/4HANA Migration Cockpit for migrating business content. This situation can occur in different environments, including SAP S/4HANA Cloud using the "Migrate Your Data – Migration Cockpit" app, or SAP S/4HANA using transaction "LTMC" or the aforementioned app.

The cause of the issue is that the migration content is initially built for SAP S/4HANA Cloud releases and aligns with SAP Best Practices for the cloud version. If a migration object or field is not within the SAP Best Practices content or is not covered by any API used for data migration, it will not be created or available in the migration cockpit. Furthermore, there may be other reasons determined by SAP that prevent the delivery of certain migration objects to the on-premise version of SAP S/4HANA.

The resolution for SAP S/4HANA Cloud users is to request new feature developments if required functionality is missing, as detailed in KBA 2676589. For SAP S/4HANA users, starting with the 1610 FPS2 release, they can create their own migration objects using the SAP S/4HANA Migration Object Modeler (LTMOM). Users should refer to SAP Note 2481235 for restrictions regarding on-premise migrations. When creating custom objects using non-released function modules, users should thoroughly test and assume full responsibility and risk for their usage.

Users can find lists of available data migration objects for both SAP S/4HANA Cloud and SAP S/4HANA on the SAP Help Portal, with specific URLs provided in the note. Finally, SAP Note 7 and SAP Note 2527926 are referenced for related guidance on customer modifications and support for non-released function modules, respectively.

Keywords provided for this note are LTMC, LTMOM, migration cockpit, and Migration Object Modeler.