SAP Note 2553733 addresses an issue in SAP BW/4HANA 1.0 where there is incorrect processing of transformations during the checking of historical versions in edit mode within the transformation GUI (RSTRANGUI). It affects the comparison of the active and modified versions of a transformation.

The underlying cause is identified as a program error.

To rectify this issue, the following solution is proposed:

1. Users are instructed to implement Support Package 8 for SAP BW/4HANA 1.0 (identified as SAPK-10008INDW4CORE). The details of this Support Package will be described in a dedicated SAP Note 2561976, titled "SAPBWNews SAP BW/4HANA 1.0 SP08," which should be referred to once it is released to customers.

2. In cases where users require an immediate fix, they can use the correction instructions provided. However, before applying the correction instructions, it's important to check SAP Notes 1668882 and 2248091 concerning transaction SNOTE for any additional information or prerequisites.

The SAP Note also provides a feature in the report 'RSDG_TRFN_ACTIVATE' to compare the modified and active versions of the transformation.

The Support Package is anticipated to be available in accordance with the release of the detailed SAP Note 2561976. If SAP Note 2553733 is available before the Support Package, it should be noted that it may still be marked as a "preliminary version".