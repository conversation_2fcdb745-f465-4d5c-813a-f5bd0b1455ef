SAP Note 2895523 addresses an issue encountered during data migration to SAP S/4HANA using the SAP S/4HANA migration cockpit for the "Material BoM" (Bill of Materials) migration object. The problem arises when trying to migrate a super Bill of Materials with global dependency assignments or a material BoM with subitems. In the case of global dependencies, they may be incorrectly assigned to a BoM item, and for material BoMs with subitems, the subitems may not be assigned to a BoM item during the migration process.

The root cause of the issue is identified as the incorrect assignment of the value to the technical field STPO-IDENTIFIER at the BoM item level.

To correct this issue, SAP provides a solution that involves fixing the event rule EOR_LTEXT_ITEM. Furthermore, SAP suggests implementing Transport-Based Correction Instructions (TCI) with different notes depending on the SAP S/4HANA release and support package level. For those on release 1809 from SP00 to SP03, they should implement TCI Note 2891675, and for those on release 1909 from SP00 to SP01, TCI Note 2898294 should be implemented.

Users affected by this issue are advised to follow the instructions provided in the relevant TCI notes to resolve the misassignment problems during the migration of material BoMs.