SAP Note 946085 addresses an issue with compression in XML for Analysis (XMLA) requests over HTTP. The symptom highlighted in this note is that compression in XMLA is only supported with HTTP version 1.1 and not with the older HTTP 1.0 protocol.

The underlying issue is identified as a program error. Since SAP Basis Release 6.20 does not provide an option to specify the use of HTTP 1.0 or 1.1, the solution provided in this note is to deactivate compression entirely for certain SAP BW (Business Warehouse) releases which are BW 3.0B, BW 3.1C (also referred to as BW 3.10 Content), BW 3.5, and BW 7.0.

To implement the solution, the note instructs users to import specific Support Packages for their respective BW system versions. The relevant Support Packages are:

- For BW 3.0B: Support Package 32 (BW3.0B Patch 32 or SAPKW30B32), detailed in Note 914949.
- For BW 3.1C: Support Package 26 (BW3.10 Patch 26 or SAPKW31026), detailed in Note 935962.
- For BW 3.5: Support Package 18 (BW3.50 Patch 18 or SAPKW35018), detailed in Note 928661.
- For BW 7.0: Support Package 09 (BW7.0 Patch 09 or SAPKW70009), detailed in Note 914303.

The note also mentions that in urgent cases, users can apply the correction instructions provided in advance of the Support Package release. It acknowledges that the notes referenced for further details on the Support Packages might be available before the release of the Support Packages themselves, and if so, their short text will include the words "Preliminary version."

In summary, SAP Note 946085 informs users of a program error that prevents XMLA compression under HTTP 1.0 and provides a list of Support Packages for different BW versions that, when applied, will deactivate compression to resolve this issue.