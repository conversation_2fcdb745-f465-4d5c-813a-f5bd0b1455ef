SAP Note 381593 addresses the issue that batch input processing is not possible for certain transactions in the logistics invoice verification component in SAP. This includes adding, parking, and verifying incoming invoices in the background for transactions MIRO, MIR7, and MIRA, respectively.

The core reason for this limitation is that newer "Enjoy" transactions in SAP are based on control technology rather than screen technology, which is what batch input relies on. The two technologies are incompatible, making batch input unsuitable for these transactions.

The note outlines different solutions based on the SAP releases:

For Releases 4.6A to 4.6B:
- Users are advised to use specific BAPIs (Business Application Programming Interfaces) instead of batch input. The suggested BAPIs include methods such as CreateFromData, CancelSingle, ReleaseSingle, GetList, and GetDetail for handling incoming invoices.
- The conventional transactions (MR01, MR1H, MR41, MR1B) and MR1M (logistics invoice verification) support batch input processing in these releases.

For Release 4.6C and later:
- Users should continue using the recommended BAPIs, with additional methods like ParkFromData and SaveFromData for parking invoices and marking them for background verification, respectively.
- Conventional transactions (MR01, MRHG, MRHR, MR41 up to MR44, MR1G, MR1B) are no longer maintained from Release 4.6C onwards.
- Transaction MR1M is to be deleted as MIRO will fully replace it functionally.

The note also directs to specific function modules associated with each BAPI method for implementation purpose, thereby providing technical details for developers and consultants to perform the necessary actions within the SAP system.