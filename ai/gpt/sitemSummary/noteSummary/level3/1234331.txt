SAP Note 1234331 addresses issues with periodic replacement reports in the IS-U (Industry-Specific Solutions for Utilities) component, particularly for Turkish localization. The issues are specifically related to two transaction codes EG88 and EG90, which are part of a business process for creating work orders or notifications for device replacement.

**Symptom:** 
Users are unable to select records using regional selection parameters in report EG89 because the corresponding fields (county, township, municipality/village, and district) are empty for records created by EG88, which results in nothing appearing in the list. Additionally, the regional selection in the EG90 report does not include new Turkish address fields, which are necessary for its correct functionality.

**Other Terms:** 
The note refers to the management of addresses in the IS-U Turkey context.

**Reason and Prerequisites:** 
The absence of the new Turkish address field selections in the standard code causes the issue, and since EG88, EG89, and EG90 transactions form one business process, all should be consistent.

**Solution:** 
The note provides transport files (AZ6K000544, AZ6K000577, AZ6K000585, AZ6K000608, and AZ6K000620) that users must download and install in the specified order to localize the functionality of transactions EG88 and EG90. These transport files contain modified program code, function modules, screens, search helps, and other necessary changes. The installment will prepare the system for the Automatic Official Publication (AOP) 11. 

Additionally, some manual modifications are required to be carried out by the users. These manual steps involve adding elements and modifying the flow logic in the screen number 100 of program REGWDR01. The elements to be added include text and i/o fields (input/output) for the Turkish address structure (fields: REG90-/SAPCE/IUTR_CONC, REG90-/SAPCE/IUTR_TWSC, REG90-/SAPCE/IUTR_MUVC, and REG90-/SAPCE/IUTR_DRID).

Finally, the note instructs users to install correction instructions to complete the required modifications fully. Users are also advised to check for related notes that may contain additional corrections related to this functionality.