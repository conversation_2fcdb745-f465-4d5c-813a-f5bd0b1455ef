SAP Note 828160 provides information and guidance for migrating from the Classic Real Estate Management (RE) solution to the enhanced Real Estate Flexible (RE-FX) within the SAP ERP system, starting with SAP ERP 6.0 (EA-FIN 600).

The note emphasizes that this is not just a simple technical conversion but rather a project that requires careful planning and execution. Key points to consider in the planning process include:

- Understanding the new structures and concepts in RE-FX.
- Training the project team and users.
- Testing RE-FX functions.
- Comparing Classic RE processes with those in RE-FX.
- Converting correspondence letters as SAP script forms are not supported by RE-FX.
- Adapting custom developments to work in the new system.
- Noting that not all data and functionalities are covered by the standard migration tools, with some requiring additional steps as detailed in other SAP Notes referenced within this document.

Before starting the migration, the note advises to:

- Update the system with the latest Support Package (at least ECC 6.00 Support Package 10) and relevant SAP Notes for component RE-FX-MI.
- Follow the migration steps provided in transaction REMICL or REMICLBATCH for background processing while carrying out manual actions online where required.
- Read through the attached PDF document and print it for reference.
- Pay close attention to each step and check the logs after executing them to troubleshoot any issues.
- Contact SAP Support if errors cannot be resolved using available documentation.

The note also lists previous experiences and recommendations from past migration projects, including:

- Using user exits in migration (SAP Note 1079141).
- Handling business partners and addresses effectively (SAP Notes 1065388, 1032896, and 1090827).
- Dealing with accrual/deferral (SAP Note 980104).
- Managing service charge settlement and potential inconsistencies in occupancy history (SAP Notes 1111447 and 997076).
- Improving performance through database indexes and settlement rules (SAP Note 946523).

Lastly, the note states that Classic RE is not supported under SAP S/4HANA and customers using Classic RE must migrate to RE-FX before upgrading to "SAP S/4HANA, on-premise edition" as Classic RE data will not be accessible post-upgrade.