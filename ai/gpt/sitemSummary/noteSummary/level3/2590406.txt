SAP Note 2590406 addresses an issue where the Revenue Schedule User Interface (UI) does not simulate revenue correctly based on the 'Planned Invoice Amount'. The problem manifests in two specific scenarios involving migrated contracts with performance obligations (POBs) that have event-based fulfilment types, where the event is a customer invoice, and value relevance is set to true.

In the first scenario, the inconsistency arises when there is a recognized revenue in future periods that differ from the billing plan invoice amount. The Revenue Schedule UI incorrectly simulates revenue based on billing plan invoices, even when parts of these invoices are dated after the migration period and before the production period, as listed in the FARR_D_DEFITEM table.

In the second scenario, the issue occurs when a POB within a migrated contract has a billing plan invoice dated within the migration period but no corresponding migration revenue. The Revenue Schedule UI incorrectly simulates migration period revenue based on these billing plan invoices, which should not occur since migration revenue should not be simulated with billing plan invoices.

The note presents a simple solution: Apply the note to rectify the program error. Other terms related to this issue include FARR (Financial Accounting and Reporting), Revenue Accounting, and Revenue Schedule.