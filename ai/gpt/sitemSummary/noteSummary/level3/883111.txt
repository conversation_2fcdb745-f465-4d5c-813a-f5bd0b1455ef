SAP Note 883111 discusses the deactivation of an outdated EarlyWatchAlert system denoted by the transaction code SCUI. This legacy version of EarlyWatchAlert was delivered to SAP customers from 1993 to 2000 and replaced subsequently with newer transactions such as SDCC and SDCCN (from 2005 onwards). However, it has been observed that certain SAP systems, particularly those running versions 3.1*, 4.0*, 4.5*, or 4.6*, may still have the old EarlyWatchAlert active, attempting to connect to SAP every minute, which is fruitless because the supporting mechanisms at SAP's end have been deactivated.

To remedy this issue, the note provides a detailed solution for deactivating the old EarlyWatchAlert in systems where it's still enabled:

1. Log into the affected SAP system.
2. Execute Transaction SCUI.
3. If a "Transactional RFC" button exists, click on it to see a list of users, find the user under which the old EWA is operating, and delete all the related entries.
4. If the "Transactional RFC" button does not exist, use Transaction SM58 with specific selection criteria to delete entries related to the old EarlyWatchAlert.
5. Additionally, in Transaction SCUI, navigate to "GOTO" and "QOUT scheduler" to remove specific entries related to the old EWA.
6. Verify and delete any background jobs related to the old EarlyWatchAlert by using Transaction SM37.

The note emphasizes that following the described deactivation steps will not impact the newer EarlyWatchAlert mechanisms running under Transaction SDCC or SDCCN. It also stresses that this deactivation should prevent unnecessary connection attempts and potential associated costs.