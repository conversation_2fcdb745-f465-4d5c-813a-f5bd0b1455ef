SAP Note 1847663 describes enhancements to the authorization trace functionality provided by an SAP kernel update in SAP Note 1854561. It introduces a new capability to set filters for authorization trace recording, meaning that only those authorization checks that meet the specified filter conditions will be logged. This functionality is accessible via transaction STUSOBTRACE included within the correction that this note refers to.

Summary of SAP Note 1847663:

- The note addresses a new feature that allows users to activate an authorization trace with filters, an improvement on the previous functionality where all authorization checks would be recorded without a filtering option.
- The feature is managed through transaction STUSOBTRACE, which lets you set specific filter criteria.
- Filters can include criteria such as application type, user, and authorization objects.
- You must set at least one filter for the trace to record any activity.
- Activation of the authorization trace with filters involves setting the 'auth/authorization_trace' profile parameter in transaction RZ11.
- The note also references other terms related to the topic, such as USOB_AUTHVALTRC and RSU22_USOB_AUTHVALTRC_DISPLAY.
- Further corrections related to this feature are provided solely through Support Packages.

This note does not mention what SAP releases it applies to, and it also does not detail workaround methods or elaborate on other notes (apart from SAP Note 1854561 which provides the kernel update). It suggests to check the Support Packages for additional corrections.