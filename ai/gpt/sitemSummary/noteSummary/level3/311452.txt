SAP Note 311452 addresses an issue where executing maintenance transactions for equipment or functional locations in a background job might cause the system to terminate unexpectedly with a runtime error OBJECTS_OBJREF_NOT_ASSIGNED.

The Note specifically pertains to those who use transactions SHDB and SM35, as well as IBIP, IE01, IE02, IE08, IE25, IE31, IL01, IL02, IL08 for maintenance of equipment and functional locations in SAP. The error occurs during batch input or background processing.

The cause of the problem is identified as the presence of view profiles in Customizing that include a text control for the master records. This particular setup triggers the runtime error.

The solution offered in the Note is to import an advance correction via a Support Package or to implement the correction manually. If manual implementation is chosen, there are two correction instructions related to the same Include file. It's important to first implement the general maintenance task list 172197 and subsequently, the general maintenance task list 237341, in that order, to correct the issue.