SAP Note 2543469 concerns companies planning to use SAP extractors within "SAP S/4HANA on-premise edition" and specifically addresses those SAP extractors that were provided in the SAP ERP 6.0 environment for use in the banking sector. The note specifies which SAP extractors are available, not available, or available with restrictions in the context of "SAP for Banking" when used with "SAP S/4HANA on-premise edition".

Summary of the SAP Note 2543469:

- The entire SEM Banking application, including its components (Transaction Datapool, Profit Analyzer, Risk Analyzer, and Strategy Analyzer) is not available in "SAP S/4HANA on-premise edition". Consequently, SAP extractors for SEM Banking are also not available in S/4HANA.
  
- Specific SAP extractors not released for "SAP S/4HANA on-premise edition" include:
  - 0BA_PARTNR
  - 0BA_RBPROD
  - 0BA_RBPROD_TEXT
  - 0BA_RKALRG
  - 0BA_RKALRG_TEXT
  - 0CML_ENCUMBRANCE
  
- Certain SAP extractors are only available with restrictions due to a change in support for differentiation category-dependent data in "SAP S/4HANA on-premise edition". The affected extractors include:
  - 0FS_BP_RATING
  - 0FS_BP_RATING_TEXT
  - 0FS_BP_RATPROC_TEXT
  - 0FS_BP_TENDENCY_TEXT 

The note also references SAP Note 2270318, which provides more details on the unavailability of the SEM Banking application in S/4HANA, and SAP Note 2448350, which explains the changes pertaining to category-dependent data in S/4HANA.