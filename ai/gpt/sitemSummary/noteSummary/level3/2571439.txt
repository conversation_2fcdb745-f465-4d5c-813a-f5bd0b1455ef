SAP Note 2571439 addresses an issue related to the handling of fulfillment events for non-distinct performance obligations (POBs) when using the `FARR_BADI_COMPOUND_FULFILLMENT` BAdI in SAP Revenue Accounting.

Summary of the Issue:
When creating fulfillment entries for non-distinct POBs using the `FARR_BADI_COMPOUND_FULFILLMENT`, there may be a requirement to skip result validations that are normally performed to prevent incorrect data. This requirement arises particularly when there are both time-based and event-based non-distinct POBs within a compound structure, due to technical limitations.

Reason:
The standard result validation process includes checking if the percentage of fulfillment for each POB is the same throughout the compound structure. This validation relies on the values of `qty_nominator` and `qty_denominator` fields. However, for event-based non-distinct POBs, these fields always have a value of zero, making the validation check unsuitable when both event-based and time-based non-distinct POBs exist in the same compound structure.

Solution:
To address this issue, SAP instructs users to apply this note, which implies that implementing the note will adjust the system in a way that allows for the result validation to be bypassed under the specified circumstances.