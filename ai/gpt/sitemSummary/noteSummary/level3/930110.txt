The SAP Note 930110 addresses an issue related to the validation check of cost centers in the context of internal rental contract processing within SAP. When a cost center is used in the posting parameters, the system incorrectly conducts validation using the current date instead of the cost center's actual validity date. This results in errors, such as the message KS210, indicating the cost center is not valid in a specified controlling area for a given date range.

The cause of this issue is identified as a program error, and the note provides a solution that is available in a Support Package. However, for users who need an immediate fix before the Support Package is applied, the note outlines steps for manual correction of the source code. 

To correct this error manually, users are advised to make changes in various classes and interfaces using Transaction SE80. These changes are as follows:

1. Creation of a new parameter 'IF_KEYDATE_CHECK' in the 'CL_REEX_CO_SERVICES' class method 'GET_COSTOBJECT' and 'GET_COST_CENTER'.
2. Insertion of a new optional importing parameter 'IF_KEYDATE_CHECK' in the 'GET_COSTOBJECT' method of the 'IF_RERA_ACC_SYSTEM' interface.
3. Creation of a new private instance method '_GET_CHECK_INTERVAL' in the 'CL_RETM_PAYMENT_MNGR' class to define the time interval for the validity check, including parameters for the valid-from and valid-to dates.

After these changes to the classes and interface are saved and activated, users should implement the attached source code corrections provided with the note. 

The note clarifies that by applying these corrections, not all time-dependent checks on cost centers in the Real Estate (RE) module will be resolved, but it does address the specific issue at hand regarding the validation check performed during contract processing.