The SAP Note 2811788 is a collective Knowledge Base Article (KBA) for addressing various issues related to the migration of Materials (SIF_MATERIAL) or Products (SIF_PRODUCT) along with their extend objects when using the SAP S/4HANA Migration Cockpit.

**Symptom:**
This note lists several issues (and corresponding SAP Notes or KBAs) that may occur during the migration of Material or Product data in SAP S/4HANA and SAP S/4HANA Cloud.

**Environment:**
- SAP S/4HANA Cloud (Product migration only) using the "Migrate Your Data – Migration Cockpit" app with data migration via staging tables.
- SAP S/4HANA (both Product and Material migration) through Transaction "LTMC" with data transfer using files or staging tables and the "Migrate Your Data – Migration Cockpit" app utilizing staging tables.

**Resolution:**
The note provides a table with a list of common issues encountered during migration, with the following details for each entry:
1. A description of the issue.
2. A reference to an individual SAP Note or KBA that addresses the specific issue.
3. The classification (e.g., FIX, INFO) indicating whether the reference contains information or a solution to fix the issue.
4. Indication of which SAP S/4HANA Cloud and SAP S/4HANA releases the issue is relevant for.

Examples of issues covered include problems with Unit of Measurement (UOM) handling, missing views or additional views after migration, missing error messages in the return structure, errors during the Generate Runtime Object stage, and limitations with the BAPI_MATERIAL_SAVEREPLICA API.

**Keywords:**
The note is tagged with keywords such as SIF_MATERIAL, LTMC, Migration Cockpit, SIF_PRODUCT, and related extend objects, maintenance status, material view, and QM view, which help users to find this information when they search for these terms.

In summary, SAP Note 2811788 acts as an aggregated resource to assist users in troubleshooting and resolving a variety of common issues related to material and product data migration using the SAP S/4HANA Migration Cockpit.