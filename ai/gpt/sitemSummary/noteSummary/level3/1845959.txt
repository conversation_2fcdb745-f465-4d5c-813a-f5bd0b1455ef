SAP Note 1845959 addresses a performance issue experienced when displaying Analysis Authorization objects through the transaction RSECADMIN in various versions of SAP NetWeaver Business Warehouse (BW).

**Symptom:**
Users are experiencing significant delays when displaying Analysis Authorization objects using RSECADMIN.

**Other Terms:**
The note references two terms related to the issue - 'g_t_iobj' and 'buffer'.

**Reason and Prerequisites:**
The root cause of this issue is identified as a program error. Specifically, the system objects are being fetched repeatedly for each dimension within the Authorization Object, leading to performance degradation.

**Solution:**
The note provides version-specific solutions recommending the import of certain Support Packages for different SAP NetWeaver BW versions:

- For SAP NetWeaver BW 7.00, import Support Package 31 (SAPKW70031).
- For SAP NetWeaver BW 7.01, import Support Package 14 (SAPKW70114).
- For SAP NetWeaver BW 7.02, import Support Package 14 (SAPKW70214).
- For SAP NetWeaver BW 7.30, import Support Package 10 (SAPKW73010).
- For SAP NetWeaver BW 7.31, import Support Package 8 (SAPKW73108).
- For SAP NetWeaver BW 7.40, import Support Package 3 (SAPKW74003).

Each of these Support Packages will be available upon the release of their respective SAP Notes, which detail the specific Support Package further:

- SAP Note 1782745 for BW 7.00 SP31,
- SAP Note 1794836 for BW 7.01 SP14,
- SAP Note 1800952 for BW 7.02 SP14,
- SAP Note 1810084 for BW 7.30 SP10,
- SAP Note 1813987 for BW 7.31 SP8,
- SAP Note 1818593 for BW 7.40 SP3.

For urgent cases, correction instructions are available, and users are advised to check SAP Note 1668882 for the correct usage of transaction SNOTE. This SAP Note may be available before the Support Package release and may contain the term "preliminary version" in its short text.

In summary, SAP Note 1845959 identifies and provides solutions for a performance issue concerning the display of Analysis Authorization objects in SAP NetWeaver BW across various versions. It recommends importing certain Support Packages to resolve the program error that causes repeated fetching of system objects for each dimension within the Authorization Objects.