SAP Note 311979 addresses an issue encountered when running a consistency check on info structure S094 using transaction MC23 in SAP. The error message "The summation indicator cannot be set for number of receipt" is generated by the system.

The cause of this issue is identified as missing entries in the reference structure MCBCOKZ. The note provides a two-step solution to resolve the problem:

1. Manual update: The first step is to manually add specific components to the reference structure MCBCOKZ before the include MCWZAEHL, which encompasses various fields such as AZZUG, MNG01, MNG02, MNG05, and MNG06, along with their respective component types, data types, reference tables, and reference fields.

2. Import transport: The second step involves importing a transport with the required table entries from the SAP support servers. The location of this transport is provided in the note, and there are different transport requests for the SAP release 3.x and releases greater than or equal to 4.x.

Furthermore, the note directs users to SAP Note 13719 for additional information regarding the import process of transport with table entries. The components to be added as listed in the note are necessary for the proper functioning of the info structure S094 and to avoid the error during the consistency check.