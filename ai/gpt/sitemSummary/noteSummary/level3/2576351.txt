The SAP Note 2576351 addresses an issue where users encounter an error message while running the report "Calculate Contract Liabilities and Assets" (transaction FARR_LIABILITY_CALC) for contracts that were created during the initial load or transition phase.

Symptoms:
When users execute the report for calculating contract liabilities and assets, the system displays the following error messages:
1. FARR_ACCR_MAIN 242: Calculate liability asset for contract &1 failed.
2. FARR_CONTRACT_MAIN 373: Error retrieving fixed exchange rate with contract &1.

Other Terms:
The issue involves terms such as FARRIC_OL, FARR_S_CONTRACT_EX_RATES, EXCHANGE_RATE, and associated exchange rate fields.

Reason:
The cause of this error is identified as a coding error within the system.

Solution:
Users are advised to implement the correction instructions that are attached to the SAP Note to rectify the issue. These instructions are provided to fix the coding error that causes the system to fail in properly calculating contract liabilities and assets for migrated contracts. After implementing the corrections, the error messages should no longer appear, and the report should run successfully.