SAP Note 2652226 details the changes to the SAP Fiori content for Finance in SAP S/4HANA 1809. The note covers several alterations, highlighting changes across business roles, business groups, catalogs, applications, and intents.

**Business Roles**:
- New country-specific roles were introduced for General Ledger Accountants in Finland, Poland, and Austria.
- Changes to existing business roles include added business groups and catalogs for Italy and France, as well as new apps for the SAP_BR_EXTERNAL_AUDITOR role.

**Business Groups**:
- New reporting groups have been added for Austria, Finland, Italy, and Poland, and an auditing group for France.
- A Reporting Switzerland group has been deleted.

**Catalogs**:
- Several new catalogs related to General Ledger reporting and auditing, among others, have been added for various countries.
- Duplicates of some target mappings and tiles for France and Belgium in the SAP S/4HANA 1709 release were identified and one instance of each was deleted to resolve the duplications.

**Applications**:
- New applications have been introduced, which include apps for SAF-T data extraction for Luxembourg, posting of opening balances for France, and various reporting and reconciliation tools.
- Some apps such as Display G/L Account Line Items have been renamed to provide a clearer understanding of their function.
- Others have been moved to new business catalogs and groups to better categorize them according to their usage.
- Several apps and transactions have been marked as deleted or obsolete because they have been replaced by newer versions or different apps that provide enhanced functionality.

**Intents**:
- The 'Create Correspondence' intent was changed from 'Correspondence~create' to 'Correspondence~createSingle', and the old intent 'Correspondence~create' is now redirected to a new Fiori app.

This note is essential for users to understand the latest capabilities and organizational changes to the Finance content in the SAP Fiori Launchpad for SAP S/4HANA 1809. Users must review these changes to ensure they are working with the most current and efficient versions of applications and have the appropriate authorizations aligned with the updated content structure.