SAP Note 554742 explains how to modify the recording of BW (Business Warehouse) statistics without requiring a transport request even when InfoProviders have a development class. This is particularly relevant for cases such as the GoingLive check, where settings for all InfoProviders must be temporarily adjusted without the changes being transported.

The symptom describes that under normal conditions, any change to the recording of BW statistics for InfoProviders with a development class prompts a transport request. 

The reason for this is that the BW statistics settings are bundled with the InfoProviders during transport. This might not be desirable in scenarios where the change should not be permanent or transported to other systems.

The solution involves implementing a correction that enables changing these settings without creating a transport request. Although the correction code itself does not include an additional button, it requires manual creation in the system. This button should be created in the status STAT0010 of the interface SAPLRSDDK4 with the function code SAVE_NO_CTS.

To apply this solution and get the new functionality, the relevant Support Package (SAPKW30B06) for version 3.0B must be imported into the BW system. Details about this Support Package can be found in SAP Note 0493977 titled "SAPBWNews BW 3.0B Support Package 06".

Lastly, SAP Note 110934 is cited for additional information concerning BW Support Packages. Note that SAP Note 0493977 might exist in a "preliminary version" before the actual release of the Support Package, offering advanced information to customers.