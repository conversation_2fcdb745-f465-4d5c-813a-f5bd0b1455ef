SAP Note 2554160 addresses a performance issue that occurs when a large volume of asset data is being loaded into SAP S/4HANA on-premise using the SAP S/4HANA Migration Cockpit with the "Fixed asset (incl. balances)" migration object. The symptom described is a significant decrease in performance during the simulation run, particularly when the API generates numerous error messages.

The cause for this performance degradation has been identified as the buffers of the BAPI_FIXEDASSET_OVRTAKE_CREATE not being cleared until a BAPI_TRANSACTION_ROLLBACK is called. Without the rollback, the buffer size continues to increase, leading to poorer performance.

The solution provided in the note involves manual adjustments to introduce a transaction rollback when errors are returned during the simulation step. The note includes release-dependent documents that describe how to implement the rollback for different SAP S/4HANA versions (e.g., 1610 before SPS4 and 1709 before FPS1).

It is important to note that the required changes should be made to the Z-object within the user's created project and not to the pre-delivered migration object. The migration object itself will be corrected in the relevant Future Package (FPS) or Support Package (SPS) release.