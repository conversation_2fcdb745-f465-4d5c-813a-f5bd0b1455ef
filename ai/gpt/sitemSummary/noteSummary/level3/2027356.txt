This SAP Note (2027356) addresses an error encountered when opening or running queries based on the Virtual Provider /IMO/SD_V01, which utilizes the SAP HANA calculation view CV_BACKORDER. The issue specifically involves error messages triggered by a naming convention change for description fields and affects systems with SAP NetWeaver 7.40 BI Content 7.57 SP 00 and SP 01. The problem has been resolved starting with SAP NetWeaver 7.40 BI Content 7.57 SP 02.

The note provides a workaround for affected systems: Users should open the calculation view CV_BACKORDER in the SAP HANA studio, navigate to the Content package -> sap -> bicont -> sd -> dlv, and then click on the projection named PRO_SD_D53.

There are two alternatives for addressing the issue within the projection PRO_SD_D53:
1. In the Output window, users can right-click on the field 0DOC_NUMBER.description and select the "Remove" option.
2. In the details window, users can left-click on the orange dot corresponding to the field 0DOC_NUMBER.description and confirm the field removal when prompted.

After performing either of the two alternatives, users need to reactivate the calculation view CV_BACKORDER to apply the changes and resolve the error.