SAP Note 3233118 addresses an issue in SAP S/4HANA 2020 and 2021 versions where customers are not correctly classified when using the "Object classification - General template" (SIF_CLF_OBJ) during data migration, specifically if the Object Key is a character (char) type. The note acknowledges that a char customer key is not being handled correctly during the migration phase, and as a result, the customer does not get classified.

For users facing this issue in SAP S/4HANA 2020 from Support Package (SP) 00 to SP04, the solution provided is to implement the Transport-Based Correction Instructions (TCI) found in SAP Note 3234074. For SAP S/4HANA 2021 from SP00 to SP02, the recommended solution is to implement TCI from SAP Note 3234047.

The note is relevant to the SAP S/4HANA Migration Cockpit when transferring data using staging tables. Users who experience the aforementioned issue during the migration process should follow the instructions provided in the corresponding TCI notes for their respective SAP S/4HANA release and support package level.