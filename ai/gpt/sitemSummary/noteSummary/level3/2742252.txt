SAP Note 2742252 addresses an issue specific to the SAP S/4HANA Migration Cockpit when users are trying to migrate G/L Account Master data. The problem is that the template for the migration object "G/L account" is missing the "Reconciliation Account (Indicator)" field (MITKZ).

This issue pertains to the migration into SAP S/4HANA on-premise editions and arises because this particular field is not available for users to fill in, and consequently, no value for this field is passed to the API when creating a G/L account.

To resolve this problem, users who are operating on the SAP S/4HANA 1809 version should implement TCI (Transport-Based Correction Instructions) SAP Note 2755460. This will address the missing field issue and allow the reconciliation indicator to be properly included in the migration of G/L accounts.