SAP Note 2525658 addresses a specific issue that can occur during the transition or migration of legacy data. The note pertains to the Performance Obligation (POB) attributes in Revenue Accounting, where there is a risk of attribute conflicts when attribute values are derived partly from BRF+ and partly from legacy data. An example given for such a conflict is a time-based POB with an event type specified.

The core problem lies in the merge of POB attributes from different data sources during the transition, which can result in invalid attribute combinations and prevent successful processing of POBs.

The recommended solution to avoid these conflicts is to apply the SAP Note, which advises to derive POB attributes solely from BRF+ during the transition, omitting any consideration of legacy attribute values. This approach is intended to ensure a consistent source for attribute values and eliminate the risk of conflicts.