SAP Note 3364196 addresses an issue encountered when using the SAP S/4HANA Migration Cockpit for loading general ledger (G/L) account master data into SAP S/4HANA versions 1909, 2020, 2021, or 2022. The problem arises when attempting to migrate G/L account data with an "Inflation code" field that requires mapping an inflation code longer than two characters.

The "Inflation code" field is designed to support up to 8 characters, but due to a predefined export parameter, the mapping rule in data migration only allows for 2 characters. This causes longer inflation codes to be truncated, resulting in incorrect data migration.

To resolve this issue, the note provides specific Transport-based Correction Instructions (TCIs) for each SAP S/4HANA release and service pack combination:

- For SAP S/4HANA 1909 (from SP00 to SP08), implement TCI Note 3364987.
- For SAP S/4HANA 2020 (from SP00 to SP06), implement TCI Note 3364988.
- For SAP S/4HANA 2021 (from SP00 to SP04), implement TCI Note 3364959.
- For SAP S/4HANA 2022 (from SP00 to SP02), implement TCI Note 3365022.

The note also indicates that for the SAP S/4HANA 2022 release, starting from Service Pack 03 and above, the issue will not occur anymore, suggesting that the problem has been addressed in those later service packs.