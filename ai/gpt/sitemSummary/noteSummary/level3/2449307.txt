SAP Note 2449307 addresses an issue regarding the RA (Revenue Accounting) consistency check, specifically relating to the error message E16. 

**Summary of the SAP Note 2449307:**

**Symptom:**
- The E16 error check, "transferred invoice not equal with posted invoice in local currency," may incorrectly display errors. 

**Other Terms:**
- The note includes references to various terms and objects such as function module (FARR_CONS_CHECK_CONTRACT) and include (LFARR_CONS_CHECKF01).

**Reason & Prerequisites:**
- This issue arose because the E16 check was modified by SAP Note 2436118 but the old version of the E16 check was not deactivated. The old E16 check did not perform an inconsistency check but rather safeguarded revenue accounting posting runs by checking for missing profitability segment numbers.
- A new E16 check was introduced to identify disparities between transferred invoices and posted invoices in local currencies on a POB (Point of Business) level, similar to the E13 check but for local currencies.

**Solution:**
- The solution provided in this note is to switch off the old E16 error check to eliminate the incorrect error display, and an update was made in the include LFARR_CONS_CHECKF01 to reflect this change. 

Users experiencing this error should apply the changes described in the note to address the inconsistencies and prevent the incorrect E16 error messages from appearing.