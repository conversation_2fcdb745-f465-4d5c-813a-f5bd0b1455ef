SAP Note 360249 addresses a situation where executing a non-cumulative query results in no data or unintelligible data being shown for non-cumulative key figures.

The note identifies several potential reasons for this issue:

1. The validity table of the non-cumulative InfoCube may contain time intervals not updated correctly due to a too specific definition, including too many characteristics. It is advised to check and possibly reduce the number of characteristics included in the validity table.

2. There may have been an extensive period without data loads for the relevant combination of validity characteristics. The solution could involve either loading data for the current date or setting up a user-defined validity interval in Transaction RSDV.

3. A user-defined restriction, especially for a time characteristic, might conflict with the query's time selection conditions. This requires checking the validity intervals defined in Transaction RSDV.

4. Inconsistent time characteristics in the InfoCube could also cause issues; all time characteristics should be derived from the most detailed one using the provided conversion routines during data updates.

As for the technical processes:

- Up to patch level 1, changes to validity characteristics necessitate a complete re-load of the InfoCube after deleting its contents.
- Starting from patch level 10, the program RSDG_CUBE_VALT_MODIFY allows changes to the validity table without the need to reload the InfoCube, but queries will need to be regenerated afterward.

For resolving these issues, the note suggests adhering to the guidance provided in the documentation for non-cumulatives under the 'Administrator Workbench' in 'Business Information Warehouse.'