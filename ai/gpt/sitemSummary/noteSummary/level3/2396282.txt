SAP Note 2396282 addresses issues during installation, system copy, database refresh, and system rename of SAP NetWeaver 7.2x and 7.3x AS Java or AS ABAP + AS Java when using Oracle 12c or higher as the database. The error `'Exception of type com.sap.sql.log.OpenSQLException caught: JDBC driver not supported for ORACLE database'` occurs in the absence of a workaround because the Java DVD that contains open*sql.jar does not support Oracle 12c or higher.

The note provides a workaround that involves downloading a specific ojdbc6.jar file, which supports Oracle 12c, from the SAP support portal and using it to replace the original ojdbc6.jar at specific points during the installation or system copy procedures for both Unix and Windows systems. It also includes detailed steps for both central and distributed systems regarding the replacement process as well as additional steps to take after installation.

Post-installation, the note emphasizes the importance of restoring the original ojdbc6.jar file by deleting the downloaded one and renaming the original file back to its initial name to ensure the system functions correctly. Additionally, it advises to install any required support package patches for Oracle 12 as described in SAP Note 1777021.

System rename and DB refresh procedures are also described, providing instructions similar to the installation steps on both UNIX and Windows platforms, including the requirement to revert to the original ojdbc6.jar file once the processes are complete.

Overall, the note provides essential instructions and files necessary to ensure compatibility and proper function of the SAP system when operated with Oracle 12c or higher.