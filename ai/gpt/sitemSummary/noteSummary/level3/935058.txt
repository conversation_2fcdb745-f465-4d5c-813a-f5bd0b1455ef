SAP Note 935058 addresses an issue related to cross-plant valuation, which occurs when the valuation area (indicated by BWKRS_CUS in the TCURM table) is not set to '1.' Users experience errors during the update of accounting documents where the 'PLANTS' field is incorrectly updated with the same value for all plants despite the correct allocation of valuation to individual plants. This problem also manifests during the update simulation using Transaction MCVY.

The note identifies the issue as a program error and provides a solution through the implementation of attached source code corrections. It is implied that the note contains specific instructions or code snippets, which are not provided in the summary, but users encountering the described problem are instructed to apply these corrections to resolve the issue. The note also mentions several terms related to the issue, including various SAP table and transaction codes that might be relevant to understanding the problem and its context within the SAP system.