SAP Note 3283383 addresses performance issues such as long run times, timeouts, or memory overflows that users might experience when using the Bonus Buy functionality within Promotions in SAP S/4HANA Retail for merchandise management or SAP S/4HANA for fashion and vertical business.

The note identifies three key factors that can affect performance:

1. The number of Bonus Buys assigned within a Promotion.
2. The number of stores involved.
3. The number of articles included in an Article Grouping for Bonus Buys.

The best practice recommendation provided by the note is to limit the number of Bonus Buys to approximately 20 within Promotion maintenance (transactions WAK2, WAK3), especially when the number of relevant stores is about 100. Exceeding this number, or having a larger number of stores, can cause performance degradation.

Additionally, having a very high number of articles in an Article Grouping for Bonus Buys, relevant in transactions VBG1 and VBG2, can also decrease performance.

The solution advised is to keep the number of Bonus Buys and articles in the Article Grouping at a reasonable level. The note suggests considering alternative methods for modeling the desired type of rebate, such as setting up Discounts at a higher level instead of using a Bonus Buy for individual articles. This is recommended to improve the system performance during Promotion maintenance.