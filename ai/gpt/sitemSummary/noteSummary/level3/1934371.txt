SAP Note 1934371 addresses an issue that occurs during the profit center or segment reorganization (reorg) process when comparing a simulated Accounts Receivable (AR)/Accounts Payable (AP) document that includes reorganization characteristics against the original document.

Symptom:
The symptom described is that the system previously performed this comparison at the line item level only. When the original document contains more than one customer or vendor line item, the system fails to recognize rounding differences that are written off. As a result, it issues the error FAGL_REORGANIZATION 566 during the REASSIGN process.

Other Terms:
This note mentions terms related to the issue such as profit center reorganization, segment reorganization, Reorg, the error code FAGL_REORGANIZATION 566, and the class method CL_FAGL_R_SPLIT_REORG: COMPARE_REORG_SPL_WITH_ORI, which is likely involved in the comparison process.

Reason and Prerequisites:
The note refers back to the symptom for the reason and does not list any specific prerequisites.

Solution:
The solution provided in this note advises the implementation of certain corrections to resolve the issue. It also instructs users to inform Development Support about this problem by creating a message under the component FI-GL-REO-GL, referencing this SAP Note. Before beginning the REASSIGN process for critical documents, it recommends saving the tables FAGL_SPLINFO and FAGL_SPLINVO_VAL. Additionally, SAP Note 1930908 is mentioned as it provides a correction report that should be used in this context.

In summary, SAP Note 1934371 explains a specific error that arises during profit center or segment reorg when comparing certain AR/AP documents, along with steps to correct this issue and a reference for further assistance (SAP Note 1930908). The note emphasizes the importance of saving certain tables before attempting the REASSIGN process and contacting Development Support with the details of the correction.