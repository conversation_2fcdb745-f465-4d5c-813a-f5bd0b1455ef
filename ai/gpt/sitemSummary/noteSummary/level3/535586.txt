The SAP Note 535586 addresses an issue that occurs during the conversion process from traditional (TR) Business Partners (BP) to SAP Business Partners. Specifically, the problem arises with the conversion of relationship types 0020 (Marriage) and 0040 (Shared Apartment), where Partner2 in these relationships is supposed to be converted into a group. However, for customer-specific relationship types, despite having the 'Group' indicator set in the conversion view, the conversion of the partner into a group does not happen.

Keywords and concepts in this note include RFTBUP01 and RFTBUP02 - which are possibly program names or transaction codes involved in the conversion process, and V_TPZ7U_U, which could be a view or table related to the conversion settings.

The note clearly identifies that the issue stems from a program error. To resolve this, SAP provides correction instructions attached to the note, which users should implement to correct the error and ensure that the conversion process works as intended, including the grouping functionality for custom relationship types.