SAP Note 713674 addresses a problem in which users of SAP systems on Basis Release 4.x without the ST-PI (Service Tools for Applications Plug-In) can experience short dumps (CONNE_ILLEGAL_TRANSPORT_VERS) during data collection for a service session, particularly related to the function module BDL_FUPDEF_INTERFACE_GET.

The issue arises because the SAPNet R/3 Frontend Systems was upgraded to Basis Release 620 (Unicode), and the ABAP commands IMPORT FROM DATABASE and EXPORT TO DATABASE used for reading cluster table content are not backward compatible with systems that do not have ST-PI.

The solution provided by the SAP Note includes two main approaches:

1. **Implement ST-PI**: 
   - Implement the current version of ST-PI, as per SAP Note 91488, which provides a more detailed preparation guide for service sessions.
   - Repair the Service Definitions by replacing them completely, following the instructions in SAP Note 727998.

2. **Use SDCC version 2.3 if ST-PI cannot be implemented**:
   - Check for and install SDCC (Service Data Control Center) version 2.3 if needed, as outlined in SAP Note 560630.
   - Stop Service Definition Refreshes and repair the current Service Definitions as described in Appendix A and Appendix B of the note, which includes details on how to prevent future refreshes and how to fix the affected tables.

The appendices also provide detailed technical steps to address the issue, such as modifying table BDLCUST entries to stop automatic refreshes, deleting affected BDL (Business Data Layer) tables using a report ZZBDL_DELETE_ALL_TABLES, and actions to take after obtaining the necessary transport file SDCC_4x.CAR.

The note warns that during certain activities there can be increased system load and recommends against using SDCC, as this could cause a further short dump (LOAD_PROGRAM_LOST) and potential data loss.