SAP Note 2883454 addresses an error that occurs during the migration of FI - G/L account balance and open/line item using the Migration Cockpit. The error message reads "FF707 Tax entered incorrect (code AA, amount 999), correct XX 888", where AA, 999, XX, and 888 represent example values.

This issue happens in both SAP S/4HANA Cloud and SAP S/4HANA on-premise environments, specifically during the simulation phase after uploading the XML file in the Migration Cockpit.

For SAP S/4HANA Cloud, the cause is related to the Check_ID box being flagged in the SSCUI ID = 101016 "Define Tax Codes for Sales and Purchases" within "Configure your Solution." In the case of SAP S/4HANA on-premise version, the field CheckID (T007a-pruef) has been set in the corresponding tax code's properties within transaction FTXP.

The "check ID" feature works as an indicator to issue an error message if the manually entered tax amount within a document is incorrect, based on the base amount and tax code percentage rate. An error is triggered if there is more than a one currency unit deviation per line item.

The resolution for this issue involves unflagging the Check_ID box, which can be done via SSCUI ID = 101016 "Define Tax Codes for Sales and Purchases." Users require the Business Role ID “SAP_BR_BPC_EXPERT” to make this configuration change.

Furthermore, the note specifies that for the Migration object FI - G/L account balance and open/line item, automatic tax calculation is not supported by the API BAPI_ACC_DOCUMENT_POST used in this context; the tax amounts must be provided in the input parameters.

The note also references another SAP KBA (2444216) for more details related to BAPI_ACC_DOCUMENT_POST and the FF707 error. The keywords for this note are: BAPI_ACC_DOCUMENT_POST, SIF_GL_BALANCE, and G/L account balance and open/line item.