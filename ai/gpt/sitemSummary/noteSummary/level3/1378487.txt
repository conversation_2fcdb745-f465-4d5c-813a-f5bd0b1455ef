SAP Note 1378487 addresses an issue where the stock values displayed in the Retail InfoCubes (0RT_C36, 0RT_C37, and 0RT_C39) do not match the actual stock values in the Online Transactional Processing (OLTP) system. This discrepancy is caused by the erroneous updating of revaluation records for purchase orders with account assignment into these InfoCubes.

The root of the problem lies in the extraction process where revaluation activities related to account assignments are included in the DataSource 2LIS_03_UM and then transferred to BW. The specific records that are causing this discrepancy are marked with the stock characteristic (BSTAUS) set to 'V' and the stock type (BSTTYP) set to 'V'. Since these revaluations are posted directly to consumption, they should not be included in the update of non-cumulative key figures in the mentioned InfoCubes. However, they are not currently being filtered out, resulting in the incorrect stock values.

The resolution for this issue requires manual changes to be made in the system to correct the stock values displayed in the affected InfoCubes. However, the specific manual changes needed are not detailed in the summary provided. Users experiencing this issue are likely instructed to refer to the full SAP Note for detailed instructions on how to address the error and restore the accuracy of their stock data in the effected Retail InfoCubes.