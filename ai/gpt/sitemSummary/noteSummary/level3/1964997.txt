SAP Note 1964997 describes enhancements to the way RFC (Remote Function Call) statistics subrecords are stored, improving how SAP systems monitor and log RFC calls for performance analysis.

Previously, the system would log a maximum number of RFC calls based on the 'stat/rfcrec' profile parameter, only saving the most costly calls to prevent overloading the system with too much data. Calls were not sorted by function module names.

With the enhancements, the system introduces a new profile parameter 'stat/rfc/distinct_depth'. This parameter allows function modules with the same name to be grouped, and the most costly calls will be saved within each group. This can potentially increase the total number of records saved. 

The 'stat/rfcrec' parameter is now replaced by 'stat/rfc/distinct_depth', which defines the maximum number of entries to be saved within each group. If 'stat/rfc/distinct_depth' is set to 0, it means all RFC calls will be stored.

To activate the new enhanced procedure, the 'stat/rfc/distinct' profile parameter needs to be set to true. It can be changed dynamically using the TH_CHANGE_PARAMETER function module.

Furthermore, SAP Note 1964997 mentions that transaction STAD is compatible with the new enhancement and introduces a new dynamic profile parameter 'stat/recex/memory_check' for internal memory checks. The note lists specific patch levels necessary to implement these enhancements and correct certain issues, including the resolution of an ABAP server crash when changing statistical profile parameters using transaction ST03. The enhancement is only available in SAP Kernel 721 starting from certain patch levels.

In summary, the note informs of:

- Enhanced rules for storing RFC call statistics based on function module names.
- Activation and control of the new feature through new dynamic profile parameters.
- Compatibility with transaction STAD for performance analysis.
- Necessary kernel patches to implement the enhancement and fix related issues.