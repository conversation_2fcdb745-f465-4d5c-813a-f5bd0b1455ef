The SAP Note 3083860 addresses a specific issue that users encounter when using SAP S/4HANA Migration Cockpit for migrating G/L account data with migration object "FI - G/L account" in SAP S/4HANA 2020. The reported problems are:

1. Absence of fields subtype of a G/L account (GLACCOUNT_SUBTYPE) and bank reconciliation account (MAIN_SAKNR) in the migration template for G/L accounts with type 'C' (Cash Account).
2. <PERSON><PERSON>iculty in confirming the mapping for fields house bank (HBKID) and account id (HKTID) when migrating a G/L account with a reference to a house bank that does not exist in the system yet.

The note clarifies that the missing fields are not available in the migration template and that the migration cockpit attempts to verify the existence of the house bank upon mapping confirmation, which causes issues when the house bank does not exist.

The solution provided by this SAP Note is a Transport-based Correction Instruction (TCI) that adds the missing fields for cash accounts and removes the existence check for house bank-related fields. This update will only affect the SAP delivered content's related objects and will be applied automatically to the generated migration objects. However, if the user has modified or copied the migration object, the correction will not be applied to these changed objects.

Users impacted by this issue should have SAP S/4HANA 2020 (SP00 - SP02) and be using the SAP S/4HANA Migration Cockpit with unmodified pre-delivered data migration content.

The note also references KBA 2543372 for guidance on how to implement the Transport-based Correction Instruction.