The SAP Note 2565349 describes an issue encountered during the execution of the report FARR_TRANS_REV_URDR in SAP's Revenue Accounting system. When using the target liability method of Contract Liability/Contract Asset, invoice entries that fall after the due date should not be cumulated together.

The symptom described illustrates an example where three invoices related to one performance obligation (POB) are processed with the mentioned transaction, and a wrong cumulative invoice amount is calculated in table FARR_D_INV_FX_ED. The note explains that after performing the transaction with a specified takeover date, the third invoice, which is dated after the takeover date, should not be included in the cumulative amount. However, the system incorrectly includes this invoice, leading to a total amount of 300 instead of the correct amount of 200.

The issue is due to a program error, and the solution provided is to apply the note in order to correct the behavior. Applying the note should adjust the system to exclude invoice entries after the due date from being cumulated within the Revenue Accounting system when using the specified transaction and liability method. No specific details on how to apply the note or what code corrections are involved were given in the summary. Users affected by this issue should apply the instructions contained within the note to resolve the program error.