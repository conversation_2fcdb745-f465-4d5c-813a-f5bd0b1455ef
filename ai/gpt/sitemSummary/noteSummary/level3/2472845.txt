SAP Note 2472845 outlines the functional restrictions encountered in the Master Data Governance (MDG) for Business Partner / Customer / Supplier within SAP MDG version 9.1 on SAP S/4HANA 1709. Key points include:

- **Relationships**: MDG does not support the differentiation type for business partner relationships, affecting maintenance of certain relationship categories (e.g., "FI0200", "FI0210", "UKM001").

- **Replication scenarios with SOAP**: When using SOAP for data replication, the data model in MDG must align with the source system's data model to avoid data loss.

- **Central Governance**: Several limitations exist in central governance, such as support for regional structure data, creating Cleansing Cases when a FI-CA function is active, using SAP Credit Management with multiple ERP Customers assigned to a Business Partner, and enhancements of the BP data model where SAP Note 2984675 should be reviewed for guidance.

- **UI Limitations**: The consistent display of field statuses and properties can be problematic. However, once valid values are entered with a UI roundtrip, correct properties should appear.

- **File Download and Upload**: Restrictions apply to certain entity types (including several related to Contract Accounts), and customizations to these restrictions may cause severe issues.

- **FIORI Approval App**: New fields added to the BP data model are not considered by the app for Customer and Supplier.

- **IDoc Based Replication**: There's a limitation for replicating deleted data segments; manual deletion might be necessary in the receiving system, or alternatively, using Business Partner Web Services for full deletion support is advised.

- **Lean Classification**: With MDG 9.1, Lean Classification is supported for ERP Customers and Suppliers, but not all features are available, and further details are in SAP Note 2479869.

- **Longtexts**: MDG supports plain text maintenance for longtexts but not formatted text. 

- **Partner Functions**: Assigning business partner addresses to non-standard or additional ERP Customer / Supplier assignments is possible with MDG 9.1, but certain limitations are highlighted.

- **Relationships and Customization**: Relationships must be processed before creating the relationship, and parallel processing is not possible. Also, restrictions apply to the standard indicator maintenance based on relationship category customization.

- **Consolidation & Mass Processing**: Certain functionalities are not supported or are subject to restrictions for performance reasons, impacting fields like BP Relationships, payment cards, and multiple assignments to one business partner.

The solution section of the Note states that previous release restrictions are resolved by the noted versions, including the ability to assign individual addresses to the "non-standard or additional assignments" in Central Governance. 

The note does not provide prerequisites or mention specific corrections, indicating that the restrictions are inherent to the software's design in the specified versions. Users are likely expected to be aware of these limitations and plan their MDG processes accordingly.