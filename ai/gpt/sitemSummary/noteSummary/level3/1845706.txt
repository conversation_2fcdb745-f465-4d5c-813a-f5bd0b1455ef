SAP Note 1845706 addresses the performance issue related to the long runtimes experienced during the execution of MDX statements, Analysis Process Designer (APD) runs, or when running a query in transaction RSCRM_BAPI. This performance drag is specifically attributed to methods GET_DATA/GET_DATA_CRM within the class CL_RSR_MDX_AXIS.

Symptom:
Users are noticing long runtimes while performing operations that involve the aforementioned methods. This performance issue escalates with the increasing number of rows and columns in the result set, along with a very high number of "select single" statements during the execution of an APD or a query via transaction RSCRM_BAPI.

Reason:
The note identifies that the root cause of the performance issue is due to missing optimizations in the software.

Solution:
Users are advised to import the appropriate Support Package into their SAP NetWeaver BW system, which differs depending on the system version. The SAP Note provides a breakdown of which Support Package to import for specific versions of SAP NetWeaver BW (7.00, 7.01, 7.02, 7.11, 7.30, 7.31, and 7.40). It also references other SAP Notes that contain more details about these Support Packages. Users are encouraged to check those notes for more information on the contents and release status of the respective Support Packages.

In urgent scenarios, correction instructions can be implemented as an advance correction. However, before doing so, users must read SAP Note 1668882 which discusses the transaction SNOTE.

This summary focuses solely on the details provided in SAP Note 1845706, and does not include external information from the referenced SAP Notes or any implicit knowledge.