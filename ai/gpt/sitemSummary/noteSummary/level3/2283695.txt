SAP Note 2283695 addresses an issue encountered during the synchronization of business partners from customer master or vendor master in the context of Customer Vendor Integration (CVI) for SAP ERP systems connected to an SAP CRM system. The problem arises when the synchronization cockpit fails to apply the existing CRM mapping tables (CRMKUNNR, CRMPARNR, and CRMLIFNR), resulting in the creation of business partners with new Global Unique Identifiers (GUIDs) instead of using the existing GUIDs from CRM. This synchronization is necessary before using CVI in a production environment or before converting to SAP S/4HANA, on-premise edition.

For a thorough understanding of the business partner data exchange setup between SAP CRM and SAP S/4HANA, the note references SAP Note 2285062.

The reason for this issue is the mass synchronization cockpit not applying the content of the CRM mapping tables during the synchronization process.

The solution provided in the note includes correction instructions to implement the PI_BP_SET_BP_GUID_FROM_CRM functionality for the enhancement spot CVI_CUSTOM_MAPPER and interface IF_EX_CVI_CUSTOM_MAPPER, ensuring the CRM mapping tables' contents are considered, and hence, correct GUIDs are used.