SAP Note 571614 addresses an issue found in BW3.0B Support Package 7, where the partition column in partitioned InfoCubes is not correctly updated, resulting in queries with date restrictions returning incorrect results. This issue particularly affects InfoCubes that are partitioned using 0CALMONTH and 0FISCPER. The note traces the problem back to an error in the RSTMPLWI template which generates update programs.

In order to resolve this, the note suggests the following solutions:

1. Execute the report CHECK_PART_BUG_571614 (renamed to RSDD_CHECK_PART_BUG_571614 in BW 3.0B Support Package 8) to identify affected InfoCubes.
2. Delete and reload the requests identified by the report in the affected InfoCubes to correct the data.
3. If the E fact table of an InfoCube is affected, use the correction program documented in SAP Note 579902 to repair the data.
4. Restart the Administrator Workbench before the next data update after performing all corrections.

The note also references to future Support Packages containing the necessary corrections for this issue:

- BW3.0B: The corrections are part of Support Package 08 (SAPKW30B08).
- BW3.1C: The corrections are part of Support Package 02 (SAPKW31C02).

Before importing these Support Packages, it is essential to follow the provided method to repair any corrupted data resulting from this issue. Additionally, SAP Note 523240 provides detailed information about Support Package 08 for BW 3.0B, and SAP Note 539817 contains information about Support Package 02 for BW 3.1C.

Lastly, the note emphasizes the importance of reading SAP Note 571364, which describes another error in the same environment that also emerged with Support Package 7.