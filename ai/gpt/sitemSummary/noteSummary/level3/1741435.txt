SAP Note 1741435 addresses an improvement request concerning the provision of external codes for Validation, Estimation, and Editing (VEE) aimed at advanced meters. The request is driven by the need for external systems—such as Meter Data Unification and Synchronization (MDUS) systems—to utilize these VEE codes to validate, estimate, and edit discrete or interval measurement data.

The Note indicates that the provision of these external VEE codes is an improvement for SAP customers. These codes can now be entered in various SAP transactions related to metering activities such as creating and changing register groups, processes of full and technical installations, billing-related installations, device modifications, and creating device information records with specific conditions.

The SAP Note also details enhancements made to several Enterprise Services to take the external VEE codes into account if they exist. These services include various UtilitiesDeviceERP and SmartMeterUtilitiesMeasurementTask operations, which handle requests such as creating, changing, and replicating smart meter registers and measurement tasks.

The benefit of this improvement is that it allows MDUS systems to use VEE codes for managing metering data more efficiently. SAP customers can obtain this improvement through the relevant Support Package and must activate the business function ISU_AMI_4A to take advantage of the new feature.