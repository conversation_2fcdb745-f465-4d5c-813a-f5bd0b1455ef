SAP Note 589024 addresses inconsistencies between SAP BW reporting and R/3 reporting when drilling down into value-based displays using storage location (0STOR_LOC) and stock type (0STOCKTYPE) characteristics. 

Symptoms:
Users receive unexpected results during BW reporting that deviate from what is seen in R/3 reporting, which becomes apparent particularly when performing a drilldown on storage location or stock characteristic.

Reason:
The discrepancies are caused because certain movement types (e.g., 311, 343, 101) in R/3 do not create accounting documents, and the corresponding value key figures aren't uploaded correctly in BW.

Solution:
The note provides a detailed procedure to correct the issue, emphasizing changes to the update process in InfoCube 0IC_C03 and redefining queries with a focus on the correct key figure calculations that include:
- Constant selection of certain characteristics
- Exclusion of storage location and stock type from value update
- Formulas to calculate total stock value based on the quantity and plant total value/quantity ratio

Additionally, the note advises on handling issues with mixed units of measurement, stock transfers, and emphasizes using the "Constant selection" function available from BW 3.0B SP12, BI Content 3.1 SP6, or BI Content 3.2 SP1 to properly configure queries.

There's also an alternative solution for BW 2.x users, involving hidden basic key figures and recalculated key figures based on restricted quantities and a price formula. The note also provides references to related SAP Notes for further guidance.

Overall, this SAP Note is aimed to fix inconsistencies in stock value reporting in BW and align the reports with R/3, ensuring accurate representation of the stock in storage locations by material number, plant, and stock type.