SAP Note 1845868 addresses an issue in SAP NetWeaver Business Warehouse (BW) regarding the process of creating or filling indexes for the BW Accelerator (BWA). The note explains that there is a deficiency in the system's ability to check if a characteristic object has been released for indexing through the entire process.

Symptom:
When users create a process chain for BWA index creation, the system initially checks if the selected characteristic is released for indexing. However, if the properties of that characteristic are later changed in a way that disallows indexing, the system does not recheck this before executing the process. This can lead to errors in the process chain or the creation of unnecessary indexes.

Other Terms:
This problem relates to BWA and the transaction RSDDB in SAP NetWeaver BW.

Reason and Prerequisites:
The cause of this issue is identified as a program error.

Solution:
The solution provided in this note is to import certain Support Packages depending on the version of SAP NetWeaver BW being used:

1. For SAP NetWeaver BW 7.30, import Support Package 10 (SAPKW73010). The details of this package are mentioned in SAP Note 1810084.

2. For SAP NetWeaver BW 7.31, import Support Package 8 (SAPKW73108). The details of this package are mentioned in SAP Note 1813987.

3. For SAP NetWeaver BW 7.40, import Support Package 03 (SAPKW74003). The details of this package are mentioned in SAP Note 1818593.

The note also advises that in urgent situations, an advance correction can be implemented via correction instructions. It refers users to SAP Note 1668882 for guidance on using transaction SNOTE.

In some instances, SAP Notes referenced above may be available before the release of the Support Package, and their text will include the phrase "Preliminary version."

In summary, this SAP Note informs about an error in the process chain for BWA indexing where the system does not re-validate the release state of an object for indexing if its properties change after the initial setup. The note provides instructions on how to rectify this issue by importing the appropriate Support Packages for different SAP NetWeaver BW versions.