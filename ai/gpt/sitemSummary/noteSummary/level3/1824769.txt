Summary of SAP Note 1824769 - SQL Monitor: Kernel downport

Symptom:
Users are experiencing issues with the transaction "SQLM" of the SQL Monitor, where it does not function properly.

Other Terms:
Not provided.

Reason and Prerequisites:
For SAP systems running Releases 700 to 731, to use transaction "SQLM" (or "/SDF/ZQLM" in some cases), specific Support Packages and kernel patch levels are required. For Release 702, ABAP Support Package SP14 is necessary, and for Release 703/731 it's SP09. If the transaction "/SDF/ZQLM" is inaccessible, the ST-PI Support Package might be missing (see SAP Note 1855676). If transaction "SQLM" or "/SDF/ZQLM" raises the error message stating "SQLM cannot be started, latest kernel patch required (See note 1824769)," it indicates that the system lacks the required kernel patch level.

Solution:
To resolve the issue, the note advises to import the necessary kernel patch to meet the system requirements and properly run transaction "SQLM" for the SQL Monitor.