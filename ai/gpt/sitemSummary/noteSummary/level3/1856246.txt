SAP Note 1856246 addresses an issue with a planning function in SAP NetWeaver Business Warehouse (BW) that is not able to find a specific node in a hierarchy when the hierarchy has time-dependent structures, and some child nodes are not valid on a certain key date. Users face an error message BRAIN 687 indicating the mentioned problem.

The underpinning reason for this error is identified as a program error.

To resolve this issue, the note suggests importing the appropriate Support Packages based on the SAP NetWeaver BW version: 
- For BW 7.30, it recommends importing Support Package 10 (SAPKW73010), further detailed in SAP Note 1810084.
- For BW 7.31, it recommends importing Support Package 09 (SAPKW73109), further detailed in SAP Note 1847231.
- For BW 7.40, it recommends importing Support Package 5 (SAPKW74005), further detailed in SAP Note 1888375.

Customers facing this issue urgently can apply the correction instructions as an advance correction before the support packages are officially released, but they must first familiarize themselves with SAP Note 1668882, which provides information about using the transaction SNOTE.

Additionally, the referenced SAP Notes may be available as a "Preliminary version" before the actual release of the Support Packages to provide advance information to customers.

This summarizes SAP Note 1856246 and outlines the solution paths to address the issue.