SAP Note 3066402 addresses an issue encountered with the SAP S/4HANA Migration Cockpit during the migration of G/L account balance data into SAP S/4HANA for versions 1809, 1909, 2020, 2021, 2022, and 2023. While using the migration object "FI - G/L account balance and open/line item," users receive an error message "Transaction type 0xx not defined" (with the message number GZ262) during simulation or import, even after providing a valid transaction type.

The cause of the error is attributed to a conversion exit alpha in the related migration field rule that erroneously adds a leading zero to the transaction type (e.g., changing a valid transaction type 10 to a non-existent value 010).

The solution involves implementing Transport-based Correction Instructions (TCI) Notes specific to the SAP S/4HANA release and service pack:

- For S/4HANA 1809 (SP00 - SP06), implement TCI Note 3066267.
- For S/4HANA 1909 (SP00 - SP04), implement TCI Note 3066224.
- For S/4HANA 2020 (SP00 - SP02), implement TCI Note 3066214.
- For S/4HANA 2021 (SP00 - SP04), implement TCI Note 3382062.
- For S/4HANA 2022 (SP00 - SP02), implement TCI Note 3382093.
- For S/4HANA 2023 (SP00), implement TCI Note 3398244.

Additionally, the note mentions that from S/4HANA 2023 SP01 and above, this issue will not occur anymore, implying that the issue has been resolved in these future service pack updates.