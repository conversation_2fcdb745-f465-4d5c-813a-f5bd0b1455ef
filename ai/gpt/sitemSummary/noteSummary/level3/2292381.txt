SAP Note 2292381 describes a change in behavior for materials in the Material Ledger with transaction-based price determination (MLAST = '2') when using SAP S/4HANA Finance on-premise edition 1503 or later. This note addresses issues whereby a user might not see Material Ledger update documents in transaction CKM3 for certain business transactions or may find that actual costing related data is missing in these documents.

The key points of this note include:

1. Simplifications were introduced for Material Ledger active materials, which resulted in actual costing relevant figures not being updated in several CKML tables.
2. Update document data are not persisted in certain ML tables; instead, compatibility views have been created to reconstruct inventory valuation relevant data based on inventory accounting postings in ACDOCA (KTOSL = 'BSX').
3. Transaction CKM3 only provides the "Price History" view, and the "Price Determination Structure" view is not available for the affected materials.
4. The compatibility views do not support certain fields for materials with transaction-based price determination, including fields from tables like MLHD, MLIT, MLPP, and MLCR.
5. These views only consider postings on inventory accounts and do not reconstruct data for transactions that do not post on inventory accounts--such as supplier invoices, production order settlements, and material debit/credit events under certain conditions (e.g. standard price 'S' or moving average 'V' with a zero-stock situation).

The solution provided by the note is informational in nature. The note advises users about the change in system behavior but does not seem to offer a workaround or a patch. Users are recommended to see also the section on Material Ledger in the release scope information available in SAP Note 2119188 for more details.