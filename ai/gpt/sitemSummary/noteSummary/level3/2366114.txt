SAP Note 2366114 addresses the issue of transferring contact person information between two systems: one with business partner site integration and one without it. The note is relevant for operations using transaction WBTIMEX (to transfer retail sites from ERP to S/4HANA or vice versa) and DRF (Data Replication Framework for transferring retail sites from S/4HANA to ERP).

The note provides a solution for a new feature that outlines the implementation of the note itself or the corresponding support package, along with a corresponding S/4 note. It cautions users that, if they implement only this note, each transfer will result in the creation of a new business partner person in S/4HANA, because the contacts are not mapped to prevent duplication.

The general solution provided in the note is to use the Key Mapping Framework to map the contact numbers from the source to the target system, ensuring that existing contacts are updated instead of duplicated. The mapping framework shall be used for maintaining consistency in contact information across pushes and pulls between ERP and S/4HANA, and it also addresses scenarios where a supplier is assigned to the business partner, updating the contact persons for both customers and suppliers.

The note mentions that the change is related to KNVK, which typically pertains to customer master contact partner functions in SAP, and that the reason for this note is to introduce a new feature to handle the contact person data effectively during distribution between ERP and S/4HANA systems.