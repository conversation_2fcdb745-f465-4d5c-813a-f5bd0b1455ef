SAP Note 946330 addresses a specific issue where a query execution involving calculation before aggregation and the NODIM function leads to a termination with an error stating that "Object CUDIMID 0 not found."

Key Points from the SAP Note:

- **Symptom**: The error occurs when running a query that performs a calculation prior to aggregation along with the use of the NODIM function. The termination message reports that "Object CUDIMID 0 not found."
  
- **Other Terms**: The note references several related terms, including C<PERSON>DIMID, CL_RSR_RRK0_CURR, CL_RSR_FORMAT, and GET_CUDIMID, which are likely related to internal classes and methods within the SAP system.

- **Cause**: The problem is identified as a program error, suggesting that there is a bug within the SAP code that needs to be addressed.

- **Solution**: The resolution for this issue is to apply Support Package 09 for SAP NetWeaver 2004s BI (also recognized as BI Patch 09 or SAPKW70009). The note also mentions that a detailed description of this Support Package can be found in a related SAP Note 914303 titled "SAPBINews BI 7.0 SP09."

The note further indicates that even though the detailed description note (914303) may be available to customers prior to the release of the Support Package, it might still be labelled as a "Preliminary version."

In summary, individuals experiencing this termination error should look to implement the appropriate Support Package once it is available to correct the program error.