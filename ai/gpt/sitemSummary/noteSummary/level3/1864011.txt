SAP Note 1864011 addresses a specific functionality related to SAP IS-U (Industry-Specific Solutions for Utilities) localized for Poland. This note discusses an enhancement in the account balance display transaction (FPL9) for reversed or reversal documents that originate from IS-U invoicing and are reversed with a reversal reason type 'PL'. Such documents will have a specific icon in the FPL9 transaction.

Key points from the note:

- **Symptom**: The note mentions that documents reversed due to a Polish-specific reversal reason ('PL') will display a special icon in the account balance view accessible via transaction code FPL9.

- **Other Terms**: Reference to the transaction FPL9, Account Balance, and IS-U reversal reason type PL.

- **Prerequisites**: To implement this functionality, users are instructed to install the add-on CEEISUT ECC 6.06 with current Advanced Order Program (AOP) updates, which include localizations for Central and Eastern European (CEE) countries for IS-U/CCS and IS-T.

- **Solution**: Users are advised to install the corresponding support package to resolve the issue. Also, the note provides guidance on implementing specific FQEVENTS in SAP. By activating BC sets /CEEISUT/ISU_PL_04 or the hierarchical BC set /CEEISUT/ISU_PL through transaction SCPR20, three events implementations can be assigned. Users are instructed to check their own implementations using transaction FQEVENTS to define free sequential numbers.

The Event implementations that will be assigned are as follows:
- Event 1203 to function module /SAPCE/FKPL_EVENT_1203
- Event 1209 to function module /SAPCE/FKPL_EVENT_1209
- Event 1211 to function module /SAPCE/FKPL_EVENT_1211

To summarize, SAP Note 1864011 provides details on the installation and configuration required to display a specific icon for Polish-specific reversal documents in the account balance (FPL9) within the IS-U component of SAP. It includes prerequisites for the solution and the steps needed to configure the relevant FQEVENTS.