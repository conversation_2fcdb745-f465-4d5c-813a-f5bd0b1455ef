SAP Note 1838992 addresses a specific issue where a deadlock occurs on the table RSDDTREXNEWSID during a rollup job, resulting in a DBIF_RSQL_SQL_ERROR error. This error is identified with the ORA-00060 code when running on an Oracle database. The problem is categorized as a program error.

To resolve this issue, the note recommends applying specific Support Packages based on the SAP BW (SAP Business Warehouse) version:

- For SAP BW 7.0: Import Support Package 35 for SAP BW 7.00 (SAPKW70035). Details about this Support Package will be provided in SAP Note 2009937, titled "SAPBWNews NW BW 7.0 ABAP SP35", which will be released to customers.

- For SAP BW 7.01 (SAP BW 7.0 EHP 1): Import Support Package 18 for SAP BW 7.01 (SAPKW70118). Details about this Support Package will be provided in SAP Note 2123573, titled "SAPBINews NW7.01 BW ABAP SP18", which will be released to customers.

- For SAP BW 7.02 (SAP BW 7.0 EHP 2): Import Support Package 18 for SAP BW 7.02 (SAPKW70218). Details about this Support Package will be provided in SAP Note 2126275, titled "Preliminary Version SAPBWNews NW BW 7.02 ABAP SP18", which will be released to customers.

For urgent situations where the official Support Packages are not yet available, the note suggests using correction instructions, but with the caution that SAP Note 1668882 should be checked for instructions on how to use transaction SNOTE effectively. It's also mentioned that the SAP Note might be available before the release of the Support Package, though it may still be labeled as a "preliminary version."

In summary, SAP Note 1838992 provides a solution to a deadlock issue on the RSDDTREXNEWSID table during rollup jobs by recommending the application of specific Support Packages or, if necessary, the use of preliminary correction instructions.