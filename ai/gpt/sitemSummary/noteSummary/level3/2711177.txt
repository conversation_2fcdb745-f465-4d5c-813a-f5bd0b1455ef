SAP Note 2711177 addresses an issue encountered by users migrating data to SAP S/4HANA using the Migration Cockpit or the Migration Object Modeler. The specific difficulty arises when copying a migration project from a quality system to a production system and preparing data for the staging tables. Because staging table names vary across development, quality, and production systems for the same migration object and structure, users previously had to manually open each migration object to find out the exact name of the staging table.

To solve this issue, a mapping table is introduced in the same schema where staging tables are generated. The mapping table stores information correlating the migration object, the source structure, and the staging table name, making it easier for users to determine the correct staging table names after a project is copied to a production system.

This note provides guidelines to implement the necessary corrections in the affected SAP S/4HANA systems, which are SAP S/4HANA 1709 FPS02, SAP S/4HANA 1709 SPS03, and SAP S/4HANA 1809 FPS00. Once implemented, the mapping table /1LT/DS_MAPPING becomes available and is updated whenever a staging migration project is opened, or whenever a staging table is synchronized.

The note also advises the implementation of SAP Note 2776252 following the application of the described corrections.