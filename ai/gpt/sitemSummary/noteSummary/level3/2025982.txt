This SAP Note, numbered 2025982, addresses an issue in the system where users are able to change the status and the owner of the objects within a reorganization plan even after the plan has been closed. This is possible regardless of the reorganization plan's status or the user's authorization levels for changing or displaying content.

The core problem is identified as a program error, though specific details about the error's nature are not given in this summary.

The provided solution to this issue is to manually implement the instructions in the SAP Note or to download and apply the corresponding support package that contains a fix for the error. This implies that there is a correction available that prevents changes to the closed reorganization plan’s objects, enforcing the proper authorization checks and respecting the closed status of the plan.