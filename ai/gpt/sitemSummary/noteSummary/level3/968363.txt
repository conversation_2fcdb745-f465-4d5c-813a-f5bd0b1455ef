SAP Note 968363 addresses an issue specific to the Invoice V4.0 in the country version for Switzerland (IS-H CH). The problem occurs when there is only one valid insurance relationship in a medical case, which is a self-payer. In such cases, the system is printing the invoice with the header and remuneration type "TP" instead of the correct "TG."

The prerequisites for correcting this issue depend on the SAP release version:

- For Release 4.72, Add-On Support Package (AOSP) 16 or at least AOSP 10 must be installed, along with several other SAP Notes (ranging from 732660 to 956975, outlining various corrections, customizing options, and updates for Invoice V4.0).

- For Release 6.0, AOSP 5 or the implementation of other specific SAP notes listed (from 861428 to 955482) is required.

The solution to fix the issue involves implementing a patch (patch 17 for Release 4.72 or Patch 6 for Release 6.0). If these patches cannot be implemented, the note provides a file named HW968363.zip, which needs to be unpacked for the corresponding IS-H version and imported into the system manually. The file cannot be downloaded using OSS but can be obtained from the SAP Service Marketplace. Specific steps need to be followed as described in the document attached to the note.

As a warning, the note mentions that the document translation to English might have inaccuracies as it is machine-translated, and users are encouraged to refer to the original document in German or provide feedback on the translation.