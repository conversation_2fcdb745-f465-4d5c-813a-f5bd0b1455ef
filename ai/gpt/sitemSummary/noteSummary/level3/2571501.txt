SAP Note 2571501 addresses an issue in SAP Revenue Accounting where certain Revenue Accounting Items (RAIs) may be in an error state after a transition to another accounting principle. These RAIs may have been processed by the FARR_RAI_PROC_NEWACP transaction, but due to a separate issue, order RAIs were marked as soft deleted, resulting in just fulfillment and invoice RAIs being transferred to the target accounting principle without the necessary valid order RAIs. This causes an inconsistency, as invoice and fulfillment items cannot be processed without valid order RAIs.

To remedy this situation, the note introduces a new report called ZFARR_NEWACP_CLEANUP, which is designed to reset the problematic RAIs to their initial state. The steps executed by this report include:

1. Retrieving RAIs with an error status that match the selection criteria.
2. Setting the INITIAL_LOAD, RAI_ERROR, and TRANS_TARGET fields of those RAIs to their original state.
3. Deleting the rows from the FARR_D_RAI2_PROC table that correspond to the selected RAIs.

The note underscores that this report should only be utilized after consulting with SAP product support and that comprehensive testing with customer data is critical before applying the correction. The note provides guidance for creating the correction report and includes a reference to the necessary transaction codes, but it also insists on caution and the necessity for alignment with SAP support to ensure proper resolution of the issue.