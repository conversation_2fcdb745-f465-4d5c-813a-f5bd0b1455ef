The SAP Note 924444 addresses an issue encountered when transferring legacy data for advance payments within contracts. Users experienced a problem where only the total condition amount for operating costs advance payment could be entered through a BAPI, but the service charge settlement required an amount for each individual rental object for distribution purposes.

The problem was identified as a program error, and the solution provided within the note is to implement a correction in the user's system. After implementing the note, the updated functionality will expect a separate entry for each rental object, as a distribution object of the condition, within the BAPI structure. Consequently, if users need to post amounts to the special general ledger, they must ensure that an amount is posted for each rental object and that the corresponding line items are correctly recorded in the BAPI structure fields.

In summary, the note outlines the steps required to correct an error concerning the distribution of condition amounts for advance payments across multiple rental objects during the legacy data transfer process. The solution provided requires a system correction to enable proper posting and distribution of amounts for service charge settlements.