SAP Note 3192927 addresses issues related to parameter assignment in the Advanced Financial Closing (AFC) functionality for certain programs. Specifically, it refers to an earlier SAP Note 3141813, which identified problems with assigning values to parameters of programs registered in AFC, and where parameter types in the Parameter Maintenance subscreen do not affect the execution of corresponding Closing Tasks when scheduled through the Process Closing Tasks app. Additionally, it was not possible to directly assign values to Job Parameters in the AFC frontend.

The core reason for this issue is due to the parameters (e.g., Cycles-parameters) being labeled as no-display parameters, which can only be populated indirectly by an external call or by a separate algorithm when the programs are run in the backend system without using AFC.

The solution presented in this note is the introduction of a new program, RKGALKSV5_S4H, which includes the cycle parameter on the selection screen and serves as a successor to the program RKGALKSV5 (Cost Center Accounting: Actual Distribution). The note also references the program R<PERSON><PERSON>LKSU5_S4H for ECC.

Upon implementation of this note, users should verify if search help KALC_CYCLEF4_S4H and data type KALC_KSCYC_SDATE have been properly imported into their system using transaction codes SE11 or SE80. If these are not found in the system, the user is instructed to check the manual instructions in the note for further guidance on how to proceed.