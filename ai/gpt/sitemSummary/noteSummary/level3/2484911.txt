SAP Note 2484911 addresses an issue where a Data Transfer Process (DTP) fails with an error message like '<PERSON>rror while accessing H0000001897_A_HHLI' when attempting to load a hierarchy. The error may also be accompanied by messages indicating issues with duplicate view names or SQL code 322 RS_EXCEPTION109.

The root cause of the issue is identified in the note as a program error. To resolve the problem, the note suggests implementing specific Support Packages for different versions of SAP BW:

- For SAP BW 7.50, implement Support Package 9 (SAPK-75009INSAPBW). Further details on this Support Package can be found in SAP Note 2467627 titled "SAPBWNews 7.50 BW ABAP SP9".
- For SAP BW 7.52, implement Support Package 1 (SAPK-75201INSAPBW). Additional information is provided in SAP Note 2487153 with the short text "SAPBWNews 7.52 BW ABAP SP1".
- For SAP BW/4HANA 1.0, implement Support Package 6 (SAPK-10006INDW4CORE). SAP Note 2491835 described as "SAPBWNews SAP BW/4HANA 1.0 SP06" will have more information on this.

The SAP Note also advises that if the issue is urgent, customers can use correction instructions. However, before applying correction instructions, users are reminded to review SAP Notes 1668882 and 2248091 for important considerations regarding transaction SNOTE.

It is noted that this SAP Note may be released before the mentioned Support Packages are available, and in such cases, the SAP Note will have the term "preliminary version" in its short text.