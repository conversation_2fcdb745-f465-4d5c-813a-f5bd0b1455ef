SAP Note 954816 addresses the changes and requirements for creating or linking Business Partners (BPs) from customers or vendors in SAP ERP Central Component (ECC) 6.0, which is referred to as "Release ERP2005". The note provides guidance for using transactions FLBPD1, FLBPD2 (for customers), and FLBPC1, FLBPC2 (for vendors), which behave differently from previous releases due to redesigned integration with the SAP Business Partner (BP) functionality.

Key points of the note:

- **Reason for Change**: In ERP2005, customer/vendor integration (CVI) with BP was reworked to support bi-directional replication which can be activated or deactivated for specific replication paths.
  
- **Customizing Requirements**: New customizing tables and views are necessary to support the redesigned transactions.
  
- **Customizing Steps**:
  1. Activate synchronization objects in Customizing (MDSV_CTRL_OPT_A).
  2. Configure number assignment between BP, customer, and vendor in tables V_TBD001, V_TBC001, CVIV_CUST_TO_BP1, and CVIV_VEND_TO_BP1.
  3. Set BP role categories and determine BP roles in tables V_TBD002, V_TBC002, CVIV_CUST_TO_BP2, and CVIV_VEND_TO_BP2.
  
- **Transaction-specific Conditions**:
  - FLBPD1 and FLBPD2 for linking customers.
  - FLBPC1 and FLBPC2 for linking vendors.
  - Certain conditions such as the existence and linking status of the customer/vendor, active synchronization, assignment of groupings, and roles need to be met.

- **Additional Information**:
  1. Transaction MDS_LOAD_COCKPIT can substitute FLBPD1 and FLBPC1 for mass processing.
  2. Once a BP has customer/vendor roles, further linkage using FLBPD2/FLBPC2 might not be possible under certain circumstances. Workarounds include switching off synchronization temporarily or manual table entries followed by synchronization.
  3. Ensure required fields are reconciled to prevent update errors; refer to Note 928616 for more details.

In summary, the note explains the changes to Business Partner creation and linking processes in ERP2005 and outlines specific Customizing settings needed for proper operation. It also identifies the conditions that must be met to use the related transactions successfully and provides additional instructions and workarounds for certain scenarios.