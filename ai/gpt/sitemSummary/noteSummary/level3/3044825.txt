SAP Note 3044825 addresses an issue that occurs when users are processing invoices for materials through transaction MIRO ("Enter Incoming Invoice") while the Material Ledger is active. Specifically, the problem arises when one user is entering an invoice with a reference to a purchase order containing a material, while at the same time another user is trying to post a goods receipt or an invoice for the same material. In this situation, the second user receives an error message M3 024 stating "Valuation data for material & is locked by user &".

The cause of the error is the system's locking of table MBEW exclusively for incoming invoices when the material ledger is active, which might not always be necessary. This lock results in the second user's inability to proceed with their transaction due to the material being tied up by the first user's action.

The solution provided by SAP in this note includes corrections that are delivered within specified support packages. Users who require an immediate fix can implement the relevant correction instructions in advance.

Once the corrections from this note are applied, the system will perform a more detailed check to determine whether or not it is actually necessary to lock the material. This will prevent the unnecessary locking of materials and allow multiple users to work with the materials simultaneously without facing the M3 024 error message.