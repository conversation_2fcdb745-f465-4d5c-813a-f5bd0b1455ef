SAP Note 1847941 addresses an issue where, after importing a data flow, connections or links from source systems to DataSources are incomplete or missing in the target system. This issue occurs due to a program error.

The note provides a solution by instructing users to import the relevant Support Package for their specific SAP NetWeaver BW system version:

- For SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010), details of which are available in SAP Note 1810084.
- For SAP NetWeaver BW 7.31 (Enhancement Package 1): Import Support Package 09 (SAPKW73109), described in SAP Note 1847231.
- For SAP NetWeaver BW 7.40: Import Support Package 3 (SAPKW74003), with more details in SAP Note 1818593.

If the issue is urgent, there is an option to implement correction instructions as an advance correction. However, prior to taking this action, users must read SAP Note 1668882, which provides information about using transaction SNOTE.

The note also mentions that the SAP Notes detailing the Support Packages could be available before the Support Package itself is released. In such cases, these notes may be labeled with the term "Preliminary version."