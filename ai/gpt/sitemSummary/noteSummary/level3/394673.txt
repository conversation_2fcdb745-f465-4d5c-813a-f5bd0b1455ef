SAP Note 394673 addresses an issue where the status of condition records is set incorrectly when creating or extending rebate arrangements with reference to an existing one. The symptom described is the incorrect setting of the status (KONP-BOSTA) to the proposal status from the rebate arrangement type (T6B1-BOSTA), which creates problems if the proposal status ' ' is not maintained.

The note identifies that this occurs due to a program error. Condition records in purchasing should only have the statuses ' ' (not settled) or 'D' (settled), and other statuses like 'A', 'B', 'C' are intended for use in sales volume-based rebates only.

Issues arising from this error can include:

1. Inability to delete rebate arrangements with subsequent settlement without errors.
2. The "Check open documents" function may incorrectly consider condition records as settled and possibly not check them.
3. If Note 135737 is activated, provisions for rebate income may not post correctly.

The business volume update is not affected by this issue.

The solution provided involves repairing or importing the appropriate R/3 Support Package. A correction report called ZCORKONP is attached to check and correct datasets if necessary. Additionally, the note instructs to create a message and message class (ZM for Release 3.1I and ZMN for Release 4.0B and later) with specific messages and text elements for proper implementation and user prompts.

In summary, SAP Note 394673 provides a solution to correct the status setting of condition records for rebate arrangements to prevent issues with settlement processes and document checking. It also includes directions for creating necessary messages and implementing a correction report to rectify affected datasets.