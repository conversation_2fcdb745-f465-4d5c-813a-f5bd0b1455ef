SAP Note 2015394 addresses the issue where an ECC (ERP Central Component) Vendor, when blocked in the ERP system, would also become blocked in the SRM (Supplier Relationship Management) system, even if that vendor is referenced in any business documents within SRM. This behavior is corrected to prevent such automatic blocking across systems.

Key points from the note:

- The problem is related to the End of Purpose (EoP) check, which is designed to handle data, including personal data, according to data protection laws. The EoP check is implemented to decide when vendor data is no longer needed for its original purpose and can be blocked or deleted.

- This note proposes changes to ensure that a vendor that is still in use within business documents in SRM does not get blocked due to an EoP check initiated from the ECC side.

- The note mentions that the new functionality is available from ECC 6.0 Ehp7 SP05 and SRM 7.03 SP05.

- As a part of the customization:
  - The CVP_EOPAPP_V view is updated with an application name ERP_SRM, labeled as "Supplier Relationship Management."
  - The CVP_EOPCLASS_V view is also updated to include the class CL_MMSRM_EOP_VEND_CHECK for EoP checks with a registration for Vendor master data in the context of SRM.

- The note instructs to maintain RFC connection information in the SAP Customizing Implementation Guide under Financial Accounting (New).

- It refers to SAP ILM (Information Lifecycle Management) that supports data storage, retention, blocking, and deletion throughout its lifecycle. SAP ILM is used for the deletion of personal data in SAP applications, as described earlier in SAP Note 1825544.

- For vendor blocking in SRM, residence rules need to be set for the ILM object CA_BUPA under the audit area BUPA_DP for applications like BBP, BUP, and ERP_SRM.

- Lastly, the note describes the stages of the EoP check, which are:
  1. Phase one: Data is actively used.
  2. Phase two: Data is available in the system but not actively used.
  3. Phase three: Data is retained for legal reasons but is blocked from being displayed or changed in the system.

The note emphasizes the importance of avoiding blank lines when creating policies for the residence rules and provides guidelines on how the blocking of data can affect the system behavior.