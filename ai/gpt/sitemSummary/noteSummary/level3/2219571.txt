SAP Note 2219571 addresses issues related to incorrect filling of certain InfoObjects in four DataStore Objects (DSOs) used in the SAP HANA optimized BI Content for SD Conditions. Specifically, the DSOs /IMO/SD_D14, /IMO/SD_D34, /IMO/D_SD14, and /IMO/D_SD34 have been impacted.

The note describes the symptoms of these issues, which mainly involve incorrect calculations and conversions affecting InfoObjects 0KNVAL_LOC (Net Value in Local Currency), 0KPRICE (Condition Pricing), and 0CML_OR_QTY (Cumulative Order Quantity) during transformations.

The reasons for these issues include:
- Using the loading date (sy-datum) for currency conversion, potentially resulting in the use of different exchange rates, which may cause confusion.
- Incorrect unit conversion in the calculation of 0KPRICE, leading to using the document currency (0DOC_CURRCY) instead of the local currency (0LOC_CURRCY).
- Design changes to avoid the use of formulas or routines in transformations to InfoSources.

For the affected BI Content versions 7.37, 7.47, and 7.57, the note lists the required support packages that fix the errors:
- SAP NetWeaver 7.40 BI Content 7.57 SP 08
- SAP NetWeaver 7.40 BI Content 7.47 SP 15
- SAP NetWeaver 7.31 BI Content 7.47 SP 15
- SAP NetWeaver 7.30 BI Content 7.37 SP 15

The solution advises users to reinstall the affected objects using the transaction RSOR after importing the necessary Support Packages, and provides a list of transformation GUIDs to be reinstalled for each BI Content version. It also provides a set of instructions on how to reinstall these transformations and notes that subsequent Data Transfer Processes (DTPs) and InfoPackages might need to be reactivated.

Additionally, in case of urgent need to implement the corrections before the Support Packages are available, the note suggests opening a customer message to the BW-BCT-SD-BW component with a reference to this SAP Note.