SAP Note 147519 provides a detailed explanation of the maintenance strategy for SAP GUI for Windows and SAP GUI for Java, including deadlines for product support and recommendations for upgrades. Important points from the note include:

- SAP clarifies the end of support dates for different SAP GUI versions, with SAP GUI for Windows 7.70 support ending on April 9, 2024, and SAP GUI for Java 7.70 support ending on March 31, 2023.
- Customers using these or older versions are advised to plan urgently for an upgrade to SAP GUI for Windows 8.00, which has full support until January 12, 2027.
- SAP GUI for Java 7.80 will have full support until March 31, 2025, and a successor to this version is in development with delivery not expected before Q4/2024.
- The note lists previous versions of SAP GUI for Windows and SAP GUI for Java that are no longer supported and emphasizes the importance of staying updated for new features and security.
- SAP bases support durations for SAP GUI on the support duration for the Microsoft Visual Studio version used to develop it.
- A succession plan for SAP GUI is assured, with support provided until a compatible successor is available.
- New platforms may only be supported with newer releases, and users are encouraged to use the latest version available.
- Compatibility with SAP products is guaranteed as long as SAP supports any application based on Dynpro technology. However, newer 64-bit versions may not be compatible with old SAP systems using SAP_BASIS versions lower than 7.00.
- "End of Support" means no more functional or security corrections for that release of SAP GUI, and "Restricted Support" refers to limited circumstances where support can still be provided.
- Corrections for SAP GUI are provided via SAP Support Portal as patches for Windows and revisions for Java, which are cumulative and should always be updated to the latest version available.

In summary, this SAP Note outlines support deadlines for various SAP GUI versions and lays out the upgrade paths and rationales for keeping SAP GUI up to date for both Windows and Java platforms. It also discusses compatibility issues and how support durations are determined by third-party support timelines, such as those from Microsoft for Visual Studio.