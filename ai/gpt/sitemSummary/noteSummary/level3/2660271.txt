SAP Note 2660271 addresses an error encountered in the SAP S/4HANA Migration Cockpit during the mapping phase of ISO codes for units of measurement (UOM). The note is relevant for users utilizing transaction LTMC, through which data migration is taking place either using files or staging tables.

The issue arises when a user attempts to add a UOM that is not recognized as a valid ISO code into the conversion target values. The symptom is that an error would occur during the conversion mapping process, presumably indicating the invalid ISO code entry.

To resolve this issue, the note suggests users should use the value help feature to choose a valid ISO code for the UOM in question. Additionally, the note advises reading KBA 2907822 for further information.

The note also includes references to related documents that may offer additional insight into similar problems:
- SAP Note 2644649 addresses an error about entries not existing in table T005S.
- SAP Note 2621716 relates to issues concerning measurements units not being assigned to ISO codes.

Keywords provided for this note include LTMC, ISOCode, and the specific error encountered, which is denoted as "SET is not a valid value for domain ISOCD_UNIT" and "MAP_ISOCD_UNT."

In summary, SAP Note 2660271 is meant to guide users facing issues with invalid ISO codes in the Migration Cockpit of SAP S/4HANA, offering a solution and additional resources to understand and correct the problem.