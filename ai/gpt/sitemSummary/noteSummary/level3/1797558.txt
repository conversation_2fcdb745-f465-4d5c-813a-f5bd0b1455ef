SAP Note 1797558 describes an issue where, after a reorganization process, the table FAGL_SPLINFO contains entries with incorrect profit centers or segments. However, within the reorganization table FAGL_R_SPL, the new profit centers and segments are correctly set.

The root cause of this problem is that there is no reconciliation between the profit center or segment information in the reorganization data (FAGL_R_SPL) and the entries in the FAGL_SPLINFO table.

To correct this issue, SAP recommends implementing program corrections or performing manual activities as outlined in the note. Once implemented, the system will perform a reconciliation after generating new profit centers or segments in the FAGL_SPLINFO table. If the entries do not match the reorganization data, the system will then generate an error message to alert the user.

This note is relevant for users experiencing inconsistencies between profit center or segment data post-reorganization within the noted SAP tables. It ensures the integrity of financial data by enforcing a check between reorganization changes and the entries in the SAP general ledger information table.