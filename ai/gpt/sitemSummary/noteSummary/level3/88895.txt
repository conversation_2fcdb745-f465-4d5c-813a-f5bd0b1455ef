SAP Note 88895 addresses missing functions in the archiving of sales orders, particularly those involving configurable materials and CO objects, which previously could not be archived. The note includes new enhanced archiving functions for the SD_VBAK object, improving performance, and includes all corrections up to Release 3.1H. The note warns that importing the transport containing the enhancements will deactivate existing archiving programs.

Key points from the note include:

- Directions to find the revised programs for SD_VBAK on SAP's service marketplace.
- A summary of changes and how to access it (using transaction SARA and following specified steps).
- Instructions to check and adjust customizations in table V_TVARA, and to update any batch processes with new programs.
- Names of the new programs that replace the existing ones, with two additional programs added: S3VBAKPT and S3VBAKAU.
- References to additional documentation on innovations and processing functionality for archiving objects SD_VBAK (order), RV_LIKP (deliveries), and SD_VBRK (billing documents).
- Requirement for a revised version of the Archiving Development Kit (ADK) and references to other notes for further instruction on set up and potential problems.

The note also includes a change history with transport numbers and reasons for changes, highlighting improvements and bug fixes applied to the archiving process over time.

Users are directed to execute the changes detailed in the note accordingly and provided with additional resources such as the Adobe Acrobat Reader for opening documentation and alternative ways to obtain this information without Internet access.

Finally, the note concludes with several transport numbers and the date of release, documenting the chronological updates and fixes made to the archiving functions.