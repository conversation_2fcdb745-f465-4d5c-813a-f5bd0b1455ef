SAP Note 1855041 provides recommendations for sizing the master-node in a SAP BW on HANA system that uses a scale-out configuration. When planning to migrate to BW-on-HANA or setting up a new system, it's crucial to perform appropriate sizing for the SAP HANA database.

For systems operating in a scale-out environment, a distinction is made between the "master" node and "worker" nodes. The master node manages the transactional load, while worker nodes are focused on data read access and data loading. This setup is facilitated by distributing tables according to their type of use, with system tables on the master node and tables containing transactional and master data across the worker nodes.

The storage required for system tables is somewhat stable but can grow with the system size, particularly for logs, statistical tables, and object metadata tables. Therefore, proper maintenance is advised to keep their size in check, with guidance available at a referenced link.

For BW systems larger than approximately 20TB of uncompressed data, it is recommended to consider a scale-out HANA appliance with nodes that have at least 1TB of RAM each to ensure stable operations. However, this guideline is general and needs to be tailored to the specific usage patterns and operational considerations of the system. Collaboration with hardware partners and consultation with SAP services or an external consulting team is recommended for an accurate assessment.