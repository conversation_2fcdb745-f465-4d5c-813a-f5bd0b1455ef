SAP Note 1859369 addresses an issue where the SQL Monitor (transaction SQLM) has been activated, but no records are displayed (the Number of Records field shows 0) even after 60 minutes or more.

The issue occurs because the background job intended to refresh the SQL Monitor records has not been scheduled. This automatic scheduling functionality is available in SAP NetWeaver 7.40 SP3, but is not present in SAP NetWeaver 7.40 SP2.

To resolve this issue, the note provides a step-by-step solution:

1. A new variant for the program RSQLM_UPDATE_DATA should be created using transaction SE38. This variant should have the 'Collect RTM Data' checkbox deselected and the 'Update SQLM Data' checkbox selected.

2. The start time of the existing background job RTM_PERIODIC_JOB should be checked in transaction SM37.

3. A new background job should be defined in transaction SM36 with the following instructions:
   - Create a step with the ABAP program RSQLM_UPDATE_DATA using the variant created in the first step.
   - Schedule this new background job to run hourly. The start time of this job should be set to 30 minutes after the start time of the background job RTM_PERIODIC_JOB.

By following these steps, users can schedule the background job required to update the SQL Monitor with the relevant records, thus resolving the problem outlined at the beginning of the SAP Note.