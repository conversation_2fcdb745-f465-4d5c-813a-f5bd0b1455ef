SAP Note 740202 addresses an issue where users are unable to create a batch input session for the multiple selection dialog box on selection screens to run it in a background (batch) job. The problem occurs because the dialog box uses an ALV grid control, which is not supported in batch mode; hence, when called in the background, processing terminates.

Other terms associated with this issue are SHDB (Transaction for Batch Input Recorder), SM35 (Batch Input Monitoring), SAPLALDB (Function group related to selection screen processing), and RS_SET_SELECT_OPTIONS_OPTIONS (Function Module to set select-options).

The root cause of this issue is the absence of a batch-enabled dialog box that could replace the current one used for selecting the selection options.

The solution provided by the note is that after implementing the correction instructions contained in the note, a batch-enabled dialog box for selecting the selection options will be available in the batch input scenario. For recordings created prior to this correction using the transaction recorder (specifically screen 651 of the ALDB function group), they will continue to work by selecting the "No BI mode" option when running the batch input session.