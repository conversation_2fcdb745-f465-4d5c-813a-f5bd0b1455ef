SAP Note 2231678 addresses multiple errors that occur when transferring legacy assets using the BAPI_FIXEDASSET_OVRTAKE_CREATE. The symptoms include:

1. Inability to transfer mid-year acquisitions and retirements simultaneously, leading to errors AAPO 101 or AAPO 102.
2. Incorrect transfer of proportional values, especially for mid-year retirements where the system wrongly calculates these values.
3. An ASSERTION_FAILED short dump in cl_faa_cfg_leadobj_erp->get_period_and_year during the transfer of mid-year post-capitalizations, which also causes transferred proportional values to be recalculated.
4. Various issues during reversal, leading to errors like APO 101, AAPO 102, AA 478, and similar.
5. Error FAA_POST 213 when posting or reversing legacy data transfers.
6. Unexplained errors when transferring values or transactions in a single accounting principle.
7. FAA_POST 014 error message if mid-year transactions are not numbered in ascending order without gaps.
8. AA 443 error during the transfer of a mid-year revaluation transaction.
9. Incorrect handling of deactivated areas with no consistency check against the entered data in CUMULATEDVALUES and POSTEDVALUES, which are ignored without messages.
10. Short dumps (UNCAUGHT_EXCEPTION) occurring when reversing integrated acquisition-type transactions for assets that do not manage all accounting principles or when reversing documents where only some of the multiple transactions create an FI document.
11. Erroneous carryover of expired useful lives when creating an asset from within a posting (ABZON, ABNAN) in the previous year.

The note identifies the root cause as a program error and provides a solution that includes implementing source code corrections and performing manual activities. Specifically, it requires the following:

- Implementation of SAP Note 2139336 as a prerequisite.
- Running the report FAA_NOTE_2231678 and activating the associated ABAP Dictionary changes.
- Ensuring the maintenance of process control to avoid additional errors (e.g., AAPO 007) when transferring retirements.

Users affected by these issues should follow the instructions in the note to correct the errors.