SAP Note 948233 addresses an issue in SAP NetWeaver 2004s BI where HTML and MHTML output formats are available options for the distribution of formatted reports in Broadcasting, despite these formats not being supported for such reports. The problem is that in HTML and MHTML formats, users cannot navigate through pages in calculated reports, which is necessary for reviewing complete report content.

The solution provided in the note involves implementing a specific Support Package:

- The note instructs to import Support Package 09 for SAP NetWeaver 2004s BI (identified by BI Patch 09 or SAPKW70009) to resolve this issue. Details about this Support Package are available in SAP Note 914303 titled "SAPBINews BI 7.0 SP09."

Additional manual steps are required in Support Packages 8 and 9:

1. Run transaction SE16 and enter "RSRD_OUTFORM_OT" as the table name, then access the table contents.
2. Specify OBJTYPE = RP on the selection screen and execute the query.
3. Look for entries where OUTFORM equals HTML, HTML_CM_CACHE, or MHT.
4. Delete the identified entries from the table and confirm the deletion.
5. Exit the transaction.

The note also indicates that SAP Note 914303 could be accessible before the actual release of the Support Package, but it might contain the words "Preliminary version" in its short text to indicate its early status.

This note's purpose is to help SAP users prevent incorrect broadcast output format selection by disabling unsupported HTML and MHTML options for formatted report distribution.