SAP Note 2066108 addresses the issue with master data destruction for customers and vendors through Enterprise Services (ES) in the SAP ECC system. It is mainly relevant for services related to end-of-purpose operations like blocking, unblocking, and checking, both for customers and suppliers.

Key points of this note are:

- It improves upon the ES delivered with ECC EhP7 Support Package 5 by fixing errors and adding missing functionalities.
- The note specifically addresses potential data loss errors when using certain services, identified by the error code SMT(048). This error indicates a data loss issue during the copying process between two fields due to insufficient field length in various structures (BPC_CUST_ERP_EOP_R_CUST, BPS_SUPL_ERP_EOP_R_SUPL, etc.).
- The enterprise services covered by this note include a series of inbound and outbound services for both CustomerERPEndOfPurpose and SupplierERPEndOfPurpose operations.
- The recommended solution is to implement Support Package 6 of EhP 7. Alternatively, if that’s not feasible, correction instructions provided in this note should be followed.
- It is important to note that this SAP Note does not encompass the complete functionality of the Customer/Vendor Master Data Destruction, and the full functionality is only available with the specific support package mentioned (ERP 617, SP 5).

Users experiencing the relevant symptoms should refer to this SAP Note for guidance on resolving the issues and ensuring the proper handling of end-of-purpose procedures for customer and vendor master data within their SAP environment.