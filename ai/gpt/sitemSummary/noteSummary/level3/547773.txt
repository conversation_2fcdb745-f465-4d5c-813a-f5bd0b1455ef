SAP Note 547773 addresses a specific issue that occurs in transaction SPAU, where the names of modified methods or methods overwritten during an upgrade (specifically in ABAP Objects) are truncated to 30 characters. This leads to issues when the user tries to access these truncated methods, including error messages indicating the component doesn't exist or implying that modifications have been adjusted.

Key Points from the Note:

- The problem affects method names that exceed 30 characters and are displayed in the format <name_of_interface>~<name_of_method>. Names longer than 30 characters are shortened.
- Truncated method names can appear under the sections "Without Modification Assistant" marked with a red stop sign or a green question mark, or mistakenly under "Deleted objects" in the SPAU transaction.
- To view the deleted objects, the user must actively select the "Deleted objects" checkbox in SPAU's second tab.
- The issue is caused by a programming error when creating entries in the modification log.
- While entries can be truncated in Release 4.6, these issues may also be present in higher release systems if upgrades were performed or modification log entries were imported from other systems.

Solutions provided in the Note:

1. The attached correction instructions will fix future entries, but it's not possible to automatically reconstruct the truncated method names because there isn't enough information available.

2. For comparing affected methods:
   a. Use the search/display functions in the Class Builder and version management to find the correct method.
   b. Manually compare the imported version with the modified version through version management.
   c. Delete the old modification logs that have the wrongly truncated method names using the "Delete Modification Log" function in transaction SE95_Util.

3. For resetting methods to the original:
   a. Use the "Delete Modification Log" function in transaction SE95_UTIL to clear the modification logs.

The Note also mentions that, by default, deleted objects are not shown in SPAU, cautioning users that these could be easily missed.