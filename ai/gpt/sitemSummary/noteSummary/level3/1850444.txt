SAP Note 1850444 addresses an issue where an exception (CX_SY_DYNAMIC_OSQL_SYNTAX) is thrown when an apostrophe (') is used in the search for the InfoObject 0CURRENCY. This problem is caused by a program error.

To resolve this issue, the note provides a detailed solution that involves importing specific Support Packages for different versions of SAP NetWeaver BW. Here is a brief summary of the solution according to the specific versions of SAP NetWeaver BW:

1. For SAP NetWeaver BW 7.00, import Support Package 31 (SAPKW70031). Further details can be found in SAP Note 1782745.
2. For SAP NetWeaver BW 7.01, import Support Package 14 (SAPKW70114). Details are described in SAP Note 1794836.
3. For SAP NetWeaver BW 7.02, import Support Package 14 (SAPKW70214), with more information in SAP Note 1800952.
4. For SAP NetWeaver BW 7.11, import Support Package 12 (SAPKW71112). This is elaborated in SAP Note 1797080.
5. For SAP NetWeaver BW 7.30, import Support Package 10 (SAPKW73010), with details in SAP Note 1810084.
6. For SAP NetWeaver BW 7.31, import Support Package 9 (SAPKW73109). More information is available in SAP Note 1847231.
7. For SAP NetWeaver BW 7.40, import Support Package 3 (SAPKW74003), with additional details in SAP Note 1818593.

Additionally, for urgent cases, correction instructions are available. Before implementing any corrections, SAP recommends checking SAP Note 1668882 for transaction SNOTE. The SAP Note might be available before the Support Package is released, and in such cases, its short text may still contain the term "preliminary version". 

In summary, this SAP Note provides information on the issue of using an apostrophe in searches for the InfoObject 0CURRENCY, its cause, and the respective solutions for various SAP NetWeaver BW versions by importing the necessary Support Packages.