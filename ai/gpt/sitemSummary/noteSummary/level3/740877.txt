SAP Note 740877 addresses the issue of cancelling obsolete invoice documents that were originally entered via the conventional invoice verification process (MR01) in SAP Releases 3.1I to 4.6B using Transaction MR08 ('Cancel Invoice Document'). The note clarifies that as of Release 4.6C, the maintenance of the conventional invoice verification was discontinued, as detailed in Note 144081. Therefore, it was expected that any old documents would be cancelled prior to upgrading to a newer release.

The note explains that since further maintenance of the conventional invoice verification is not available from Release 4.6C onwards, users are required to handle the cancellation of old invoices in a different way. Specifically, users can issue a credit memo corresponding to the invoice that needs to be cancelled using Transaction MIRO ('Enter Incoming Invoice').

Once the credit memo is posted, the original MR01 invoice and the follow-on Financial Accounting (FI) document (the credit memo) must be manually cleared against each other in the financial accounting module. This process effectively cancels out the old invoice using an alternative method suited for releases newer than 4.6B. The reason behind this workaround is that the cancellation procedure mirrors the postings of a credit memo, as indicated in Note 46564.