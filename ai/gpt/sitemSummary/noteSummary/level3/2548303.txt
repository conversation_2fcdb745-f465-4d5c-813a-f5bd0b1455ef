SAP Note 2548303 relates to an issue in S/4HANA On Premise systems where users encounter problems with maintaining address data for business partners assigned to users. This affects user management operations in transaction SU01, BAPI_USER_CHANGE, Central User Administration, and identity management systems, as well as maintenance in transaction BP.

The issue stems from a lack of functionality within the system's new business user model, where a business user is represented by both a business partner and a link to a system user. This model has three main areas for maintaining a workplace address: Human Resources Management (HR), Business Partner Management (BP), and User Management (US).

Certain restrictions exist with the maintenance view TBZ_V_EEWA_SRC:
- If CUA is active, its configuration supersedes the maintenance view TBZ_V_EEWA_SRC.
- Without an active HR management, the HR source cannot be selected for maintenance.
- If a business user only has a business partner and workplace address without an assigned user, the maintenance view TBZ_V_EEWA_SRC's configuration is disregarded.

The solution provided in the note is to import a specific support package that includes a new maintenance view TBZ_V_EEWA_SRC. This view allows for the configuration of maintenance autonomy for individual nodes or attributes. You can access this view via transaction SM30.

There are caveats to the resolution:
- The central address data of the business partner must still be maintained in HR and cannot be autonomously maintained via TBZ_V_EEWA_SRC. The report /SHCM/RH_SYNC_BUPA_FROM_EMP should handle the necessary synchronization.
- Maintenance of a business partner's organizational data is currently not possible.

In summary, the note outlines an issue with maintaining workplace addresses for business users linked to business partners in S/4HANA On Premise systems, details restrictions, and provides a solution in the form of a support package that addresses the maintenance limitations through a new maintenance view.