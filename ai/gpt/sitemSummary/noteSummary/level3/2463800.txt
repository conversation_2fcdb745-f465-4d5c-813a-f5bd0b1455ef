SAP Note 2463800 addresses the issue that various CRM tools and Business Application Programming Interfaces (BAPIs), specifically CRM BAPI and functions within the development class RSCRM, are not available in SAP BW/4HANA. This inavailability affects retraction use cases in SAP ERP, such as plan data transfer to the budget control system in PSM-FM-BCS and retraction of cost-based CO-PA data.

Additionally, capabilities pertaining to high data volume CRM customer segmentation that were previously available are no longer supported in SAP BW/4HANA. The Note references two functionalities that are affected: "Segmentation with High Data Volume from SAP NetWeaver BW" and "Distinction Between BWA and BW High-Volume Segmentation with HANA."

The Note acknowledges that there is no analysis to determine if the CRM tool, BAPI, or customer segmentation functionalities were previously used. To work around the missing CRM BAPIs, the Note suggests using external SAP HANA views of the SAP BW/4HANA InfoProviders to allow for direct SQL access to SAP BW/4HANA data. It indicates that the external SAP HANA views must be generated for all objects that require SQL access. However, the Note also states that there is currently no replacement for the other CRM-related features that have been affected by this transition.

For users requiring further instruction on generating SAP HANA Views from the BW system, a link to related information is provided. 

The Note includes other terms that may be relevant to users affected by this issue, such as $CRM, BAPI_REP..., RSCRM..., RSDRCRM, RSDRCRM_SEG, and KELR, which relate to the technical elements being discussed.

This Note is primarily for organizations that utilized CRM functionalities within their SAP BW/4HANA implementations and now require a way to manage the absence of these features following their upgrade or migration.