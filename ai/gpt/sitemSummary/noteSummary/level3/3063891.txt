SAP Note 3063891 addresses an issue with the SAP S/4HANA migration cockpit, specifically during the data migration process of G/L (General Ledger) account data. The problem occurs in SAP S/4HANA versions 1809, 1909, and 2020 for the migration objects "FI - G/L account" and "FI - G/L account - extend existing record by new org levels."

Symptoms:
Users of SAP S/4HANA 1809 and 1909 find that when they migrate G/L account data and provide a value for the field "Clearing Specific to Ledger Groups," it is not correctly set after the migration. For users of SAP S/4HANA 2020, the same issue occurs with either of the two migration objects mentioned.

Cause:
The incorrect mapping occurred because the field "Clearing Specific to Ledger Groups" was being migrated into the wrong target field, specifically the XKRES field, which stands for "Indicator: Can Line Items Be Displayed by Account?"

Solution:
To rectify this, the note provides information on Transport-based Correction Instructions (TCIs) that need to be implemented depending on the release and service pack level:

- For SAP S/4HANA 1809 (SP00 to SP06), implement TCI Note 3066267.
- For SAP S/4HANA 1909 (SP00 to SP04), implement TCI Note 3066224.
- For SAP S/4HANA 2020 (SP00 to SP02), implement TCI Note 3066214.

These corrective TCIs will resolve the issue of incorrect mapping for the field "Clearing Specific to Ledger Groups" in G/L account migration.