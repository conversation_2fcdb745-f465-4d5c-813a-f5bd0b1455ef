SAP Note 834661 addresses an issue encountered during the business partner conversion phase II, specifically when users are attempting to select an existing transport request on the selection screen of various RFTBUH programs (RFTBUH05, RFTBUH03, RFTBUH04, and RFTBUH06) in production mode. The problem is that the "Order/task" field is not available for input.

This issue arises when customer-specific DDIC (Data Dictionary) objects need to be converted and the RFTBUH02_1 program has not identified any conversion-relevant data elements to note in the BPUM_CTL table. As a result, subsequent programs (RFTBUH03 to RFTBUH06) do not make the "Order/Task" field an entry requirement, and therefore it remains non-editable.

The SAP Note proposes a solution requiring the implementation of a correction provided in the note itself or the importation of a specified Support Package using Note Assistant. The correction ensures that the "Order/Task" field becomes ready for input as required when relevant data elements are determined for the conversion process.