SAP Note 2101436 addresses the need for deletion of personal data in Spain VAT reporting in compliance with relevant regulations (referenced in SAP Note 1825544). It details the use of SAP Information Lifecycle Management (ILM) to support simplified data deletion for such purposes.

Key points from the note include:

- Simplified deletion for Spain VAT reporting becomes available starting from SAP_FIN 6.17, Support Package 07.
- Personal data should be deleted when there are no business reasons for its retention, such as the expiration of tax audit or social insurance check periods.
- Archiving objects or data destruction objects should be used to segment the data for collective deletion based on the same retention period.
- The deletion can take place directly from the archive once the retention period has expired.
- If an application has tables extending customer or vendor master data, the application's data lifecycle corresponds to that of the extended master data.
- Standard ILM objects can be used for archiving this data, ensuring that application-specific customer/vendor data is archived along with the customer/vendor master data.
- The note specifies how Spain VAT reporting data should be archived with the customer/vendor master data by implementing the relevant exits during the archival process of the master data.

Furthermore, the note provides information on the application object relevant to Spain VAT reporting and the associated deletion functionality, such as:

- Customer master Archiving object (FI_ACCRECV) with the Enhancement Spot ARC_FI_ACCRECV and BAdI Definition FI_ACCRECV_WRITE.
- Vendor master Archiving object (FI_ACCPAYB) with the Enhancement Spot ARC_FI_ACCPAYB and BAdI Definition FI_ACCPAYB_WRITE.

Users with relevant permissions and knowledge should implement the guidance provided in this note to ensure compliance with regulations on personal data deletion within the Spain VAT reporting context.