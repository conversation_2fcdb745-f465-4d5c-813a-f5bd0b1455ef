SAP Note 2103558 provides guidance for users planning to run SAP Simple Finance, on-premise edition 1503 with additional add-ons. It covers the compatibility considerations that need to be taken into account when combining SAP Simple Finance with other software components.

Key points from the note include:

- The compatible industry solutions and financial process components from enhancement package 7 for SAP ERP 6.0 that can work with SAP Simple Finance are listed in an attached PDF file.
- A minimum set of these component versions should be included in the "Applications for FIN 700" instance of EHP7.
- Add-ons compatibility:
  - Users should verify whether the add-on suppliers have released their specific add-ons for SAP Simple Finance, on-premise edition 1503 compatibility.
  - Release information for standard ERP add-ons is provided in an attached PDF.
  - Add-ons on SAP NETWEAVER 7.4 that are independent of ERP are typically compatible with SAP Simple Finance, on-premise edition 1503.
  - For custom development add-ons, SAP recommends contacting SAP Custom Development at least three months before implementation to allow time for any necessary adjustments. Users can also check the availability of specific versions on the provided SAP service link.
  - Partner add-on release information is not included in this note but can instead be found in SAP Note 2100133.
- Users are instructed to reach out to SAP Support or the specific partner for additional information if they cannot find the add-on they are looking for in the provided resources.

To summarize, SAP Note 2103558 is a directive for users on assessing and ensuring the compatibility of add-ons when using SAP Simple Finance, on-premise edition 1503. It highlights the need to check for supplier releases, provides resources to verify standard add-on compatibility, advises on custom add-on procedures, and directs users to additional notes and contacts for further support.