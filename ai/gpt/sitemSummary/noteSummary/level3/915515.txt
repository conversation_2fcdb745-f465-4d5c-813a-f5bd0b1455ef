SAP Note 915515 addresses an issue in the SAP Data Warehousing Workbench (DWWB) where users cannot start a second change run while one is already executing, nor can they restart a terminated change run within the DWWB.

Terminology used in the note includes Administrator Workbench (AWB), DWWB, attribute change run, and hierarchy and attribute change run.

The reason for these limitations is identified as a program error. There are enhancements made available in Support Package 07 for BW 7.0.

The provided solution is that, starting with Support Package 07 for BW 7.0 (SAPKW70007), users can schedule another change run with different parameters or restart a terminated change run from the Data Warehousing Workbench using Transaction RSA1 and navigating to Tools > Apply Hierarchy/Attribute Change...

If users cannot wait for the support package or prefer an alternative method, they can start a realignment run using either a process chain or the ABAP program RSDDS_AGGREGATES_MAINTAIN. It's important to note that if restarting a terminated change run via this method, it must be started with the same parameters as it had when it terminated. Users can determine these parameters by using the realignment run monitor (Transaction CHANGERUNMONI).

To implement the permanent fix, users are instructed to import Support Package 07 (BW 7.0 Patch 07) into their BW system. This package is described in more detail in SAP Note 0872279, titled "SAPBWNews BW 7.0 Support Package 07," which may be released to customers before the support package itself is available. If the note is released before the support package, its short text will contain the words "Preliminary version."