SAP Note 195189 addresses an issue where transaction SPAU prompts for a modification adjustment for object LSSQ0F03, and potentially other related objects such as those from the function groups SQLD, SSQ0, and OCSI, even if these objects have not been modified by the user. This may surface after importing Support Package 13 for SAP release 4.5B or Support Package 29 for release 4.5A, or when upgrading to 4.6B. The symptom also includes a potential short dump with runtime error CONNE_ILLEGAL_TRANSPORT_HEADER that can occur in transaction SPAM during the AUTO_MOD_SPAU step when importing support packages for Release 4.5B.

The reason provided for this issue is that modification information was inadvertently supplied with TCC service tools for preparing an EarlyWatch session or Going-Live service as mentioned in Note 91488. This caused objects to be erroneously marked as modified in the customer system. When a support package or upgrade is applied, these objects are erroneously listed as modified in transaction SPAU.

The solution suggested to resolve this issue involves deleting the mistakenly imported modification entries in the customer's system. This can be done as a preventative measure before a scheduled upgrade or after the error has occurred.

To perform this deletion, the note instructs users to:

1. Import the add-on ST-A/PI from Note 69455 into your system.
2. Run the report /SSF/RICCSMOD without input parameters. This report is designed specifically to remove the modification information related to the TCC Service tools, and should resolve the issue by cleaning up the false modification flags.

This note serves as a guide for SAP users encountering false modification prompts in SPAU to correct the system's modification adjustment procedures.