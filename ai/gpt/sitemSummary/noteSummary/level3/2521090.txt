SAP Note 2521090 addresses an issue experienced during the migration of bank data to SAP S/4HANA when an XML file containing both IBAN (International Bank Account Number) and account numbers leads to a system crash (short dump). Below is a summary of the note:

- **Symptom**: When using the Migration Cockpit to migrate bank data to S/4HANA and the XML migration template contains both the IBAN and account number, you encounter a runtime error (MESSAGE_TYPE_X) in ABAP program SAPLIBMA, which causes a short dump in the system.

- **Environment**: The issue pertains to the SAP S/4HANA environment.

- **Reproducing the Issue**: The issue can be triggered by navigating to the Migration Cockpit, uploading a filled XML migration template, and proceeding with data transfer and validation.

- **Cause**: The problem arises because the XML template for migration has both 'Account data' and 'IBAN' populated, which should not be the case. The API checks will cause a short dump if both fields are maintained or if one of the fields is improperly formatted (e.g., "Account No" has a value like "<IBAN>********").

- **Resolution**: When maintaining the banking data within the XML template, ensure that you populate either the 'Account' field or the 'IBAN' field, but not both. Only one of these is mandatory. A correct format must be followed to prevent the system from crashing.

- **Reference**: SAP Note 2470789 is mentioned for examples of data migration template XMLs which can be helpful for correctly formatting the data.

- **Keywords**: This issue deals with terms like dump, data migration, IBAN, account, bank, LTMC (Legacy Transfer Migration Cockpit), and cockpit.

This note provides guidance on the correct method to input bank account data during the migration process to avoid system crashes due to conflicting or improperly formatted data entries.