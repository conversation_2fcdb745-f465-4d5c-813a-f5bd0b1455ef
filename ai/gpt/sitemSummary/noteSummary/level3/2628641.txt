SAP Note 2628641 discusses adaptations needed for the IDoc Interface/ALE due to the extension of selected currency amount field lengths in SAP S/4HANA, as of the 1809 release. Field lengths that previously accommodated 9-22 digits now support up to 23 digits, including two decimal places.

Key points from the note are:

1. Data type changes affect currency amount fields and closely related DEC, CHAR, and NUMC data elements used to store amounts.
2. Existing ABAP code referencing these adjusted data elements should remain syntactically correct, but some manual adjustments may be necessary.
3. In the context of IDoc interfaces, segments with extended fields are handled by creating new data elements that maintain external compatibility, following a naming convention based on the original field name and length.
4. For inbound IDocs, extended values in CHAR fields (used as amounts) must be checked for overflow against the original field length, using the class CL_AFLE_MAX_MIN. This ensures that inbound data respects the established field length limits.
5. For outbound IDocs, a compatibility check class, CL_AFLE_COMPATIBILITY_CHECK, is used to prevent data overflow when transferring values from extended internal fields to an IDoc segment.

The note provides detailed guidance and examples on how to implement the necessary checks and adjust outbound IDoc function modules to account for the increased field lengths while ensuring compatibility with existing IDoc structures and preventing data overflow errors.