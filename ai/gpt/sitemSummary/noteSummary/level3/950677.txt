SAP Note 950677 addresses an issue where input help for hierarchy nodes mistakenly displays nodes that the user is not authorized to view. This problem occurs when authorizations for nodes from different hierarchies are assigned to a single characteristic.

Key points in the note:

- **Symptom**: Users are able to see unauthorized hierarchy nodes in the input help for hierarchy nodes.
- **Other Terms**: The issue relates to F4 help functionality, variables for hierarchy nodes, and analysis authorizations.
- **Cause**: The issue is caused when authorizations for nodes across various hierarchies are granted on the same characteristic.
- **Solution**: 
  - For SAP NetWeaver 2004s BI (Business Intelligence), the resolution is to import Support Package 09 (BI Patch 09 or SAPKW70009) into the BI system.
  - Detailed information about this Support Package will be provided in SAP Note 0914303, titled "SAPBINews BI 7.0 Support Package 09," once it is released to customers.
  - For urgent situations, correction instructions are provided.
  - Furthermore, the above-mentioned note might be available before the official release of the Support Package. If the text still contains "preliminary version," it indicates that the information is provided in advance.