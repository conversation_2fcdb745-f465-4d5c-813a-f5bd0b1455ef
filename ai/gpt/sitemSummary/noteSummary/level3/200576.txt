SAP Note 200576 addresses the issue that Financial Accounting (FI) Accounts Payable (AP)/Accounts Receivable (AR) line items cannot be extracted from an R/3 System in the delta update mode.

The symptom described in the note is a lack of functionality in R/3 System that prevents the delta extraction of FI-AP/AR line items. The affected DataSources are 0FI_AP_3 for accounts payable, and 0FI_AR_3 for accounts receivable.

The solution provided applies to BW Release 2.0B and higher, and requires R/3 Release 4.0B, 4.5B, 4.6B, or 4.6C at specific support package levels (38 for 4.0B, 18 for 4.5B, and 3 for 4.6B) or higher. In cases where the required support package is not applied, users must implement the corrections from this note.

The note includes manual postprocessing steps that have to be taken during the implementation of the corrections:
- Create new data elements PR00005010 and PR00005020.
- Create function modules SAMPLE_PROCESS_00005010 and SAMPLE_PROCESS_00005020 according to the correction instructions provided.
- Create function module OPEN_FI_PERFORM_00005010_P and OPEN_FI_PERFORM_00005020_P according to the correction instructions provided.

Additionally, users are required to make specific entries in the R/3 System tables FBW4X, TPS01, and TPS31. The note provides the values to be entered in those tables to add the necessary processes.

For R/3 Releases 3.x, however, the note states that there will be no support for FI line item evaluations in the delta update mode due to missing technical Basis requirements (SAP Service API) in those releases.