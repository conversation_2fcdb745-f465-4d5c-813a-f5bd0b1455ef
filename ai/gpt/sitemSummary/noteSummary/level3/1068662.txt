SAP Note 1068662 addresses an issue where personal settings in transaction ME51N, particularly default values, are lost after upgrading from Release 4.6C to 4.7 or a higher version. The reason for this problem is that starting with Release 4.70, the structure for personalization has changed, and there was no automatic conversion process in place previously. 

To resolve this problem, the note suggests implementing the attached program changes. It recommends saving entries from the table ESDUS, which contains MM - Dynamic User Settings, before performing the upgrade. Additionally, it is advised that Note 1068662 should be implemented before the first logon after the upgrade.

If personal settings have already been lost following the upgrade, the note advises that users can manually maintain and retrieve their settings. The related transactions are ME51N, ME52N, and ME53N, which are affected by this change in personalization structure.