SAP Note 2635232 addresses an issue faced during migration to S/4HANA 1709, specifically Feature Pack Stack 01 (FPS01) or FPS02. Customers using the Migration Object Modeler may encounter a problem when attempting to add a key field to the source structure. The error message "Keyfield XYZ must be at the top" is presented and prevents the action.

The note clarifies that this is a known issue with the Migration Cockpit and Migration Object Modeler tools in the mentioned versions of S/4HANA. It suggests a solution which involves implementing specific corrections on the S/4HANA system following the correction instructions provided. After applying these fixes, users will receive a warning instead of an error when a key field is not at the top of a table, stating: "We recommend placing key fields at the top of the table." This guidance is especially crucial when working with staging tables because if key fields are not at the top, it might be impossible to create a staging table for the source structure.