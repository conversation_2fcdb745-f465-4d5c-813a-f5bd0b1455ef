SAP Note 1011190 pertains to the process of splitting the Central Instance (CI) after upgrading a Windows MSCS-based SAP ABAP Server system from NetWeaver 6.40 or earlier to NetWeaver 7.0 or 7.1. This process involves converting the old CI configuration to the new Advanced Server Configuration System (ASCS) and Enqueue Replication Server configuration on Windows Server 2008 (R2) or Windows Server 2012. 

Key points from the note:

- The described procedure is outdated and may not work for older installations/configurations.
- Homogeneous system copy procedures should be used instead.
- The note applies to systems already upgraded to NetWeaver 7.0x or higher but not yet configured for high availability (HA).
- The procedure does not apply to Java Add-in configurations.
- Some terms have changed in NetWeaver 7.1, such as "Central Instance (CI)" becoming "Primary Application Server Instance (PAS)" and "Dialog Instance (DI)" becoming "Additional Application Server Instance (AAS)".
- The note provides stepwise instructions for moving SAP Cluster group resources, installing new instances, and configuring the system for HA.
- For NetWeaver 7.1, several additional steps unique to this version are outlined.
- Particular caution is advised when specifying the instance number during the installation and when changing environment variables.
- There are specific directory structure requirements, security permissions, and commands for creating shares and setting permissions.
- After configuration, the old CI or PAS services must be deleted, and profiles need to reflect the local host names.
- For 7.10 only, additional installation of the host agent on other nodes is required.

The note also points out the importance of backup for profiles and the need to refer to the installation guide for detailed steps not mentioned in the short guide within the SAP note.

Note: As the description mentions, this process is outdated and should not be used for new configurations. Users are directed to refer to the latest system copy procedures and consider this note deprecated.