SAP Note 145812 addresses a specific issue in the IS-OIL module related to inventory management and the calculation of moving average price (MAP) when negative inventory levels are present.

**Symptom:**
The problem occurs when materials are configured to allow negative stock quantities, and the price control method is moving average price. If there is a price/value discrepancy between the purchase order and the invoice verification, normally an adjustment in invoice verification would change the MAP in the material master. However, this adjustment would not be accurate if there's negative inventory.

**Other Terms:**
Keywords relevant to this note include Invoice verification, MAP, negative inventory stocks, and MMINKON.

**Reasons and Prerequisites:**
The system is designed to automatically switch the price control from moving average to standard price control when inventory levels are negative. Once the stock levels become positive again, the price control reverts back to moving average pricing. Consequently, MAP should not change while the inventory is negative.

**Solution:**
The solution requires downloading and importing Transport SAPKI3H433. The transport files and their object list can be found on SAP's servers (specified in the note). This fix should be applied only to systems with the IS-OIL module installed, as implementing the note on other systems can cause serious harm.

The note insists on checking another two notes, 47531 for ensuring this correction is relevant for the IS-OIL system and 13719 for guidance on importing corrections, as certain sequences of steps must be followed, detailed in OSS Notes 98642 and/or 98876, before implementing this note. 

This note emphasizes that the procedure is exclusively for organizations utilizing the IS-OIL module due to its specialized nature.