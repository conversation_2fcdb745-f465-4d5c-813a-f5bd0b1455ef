This SAP Note 2303003 pertains to pre-conversion checks necessary for business partner data exchange between SAP S/4HANA, on-premise edition, and SAP CRM. It emphasizes the critical step of executing these checks before Customer Vendor Integration (CVI) is activated and prior to the commencement of mass synchronization of customer or vendor master data.

The note addresses the importance of ensuring the existence of the BAdI implementation named PI_BP_SET_BP_GUID_FROM_CRM within SAP ERP by executing the pre-conversion check CHECK_BADI_IMPLEMENTED. This is linked to ensuring proper mapping and synchronization between SAP CRM business partners and SAP S/4HANA.

To protect against the loss of critical mapping information, it stresses the necessity of implementing SAP Note 2283695 first. Without following this prerequisite, there is a risk of permanently losing the mapping information required for integration with SAP CRM.

Furthermore, the note specifies another pre-conversion check called CVI_CRM_MAPPING to detect any inconsistencies between CVI and CRM mappings, either existing ones or new issues that might arise post-synchronization.

The solution offered in this note is a reference to another SAP Note, 2304337, which contains detailed information about the various checks to be performed.

In summary, this SAP note is crucial for organizations integrating SAP S/4HANA with SAP CRM to ensure that the data exchange via CVI is set up correctly, safeguarding the necessary mapping information between the systems. It advises a sequence of pre-conversion checks and conditions to prevent potential data synchronization issues.