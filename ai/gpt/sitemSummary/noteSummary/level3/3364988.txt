SAP Note 3364988 addresses issues with the data migration content for SAP S/4HANA 2020 when transferring data using staging tables in the SAP S/4HANA Migration Cockpit. The note is applicable when users are experiencing content-related errors with the Legacy Transfer Migration Cockpit (LTMC) and the Migration Cockpit in SAP S/4HANA, without any modifications to the pre-delivered data migration content.

Issues covered by this note include incorrect data types for General Ledger (G/L) account balances related to WBS (Work Breakdown Structure) elements during migration and incorrect mapping rules for Accounts Payable (AP) and Accounts Receivable (AR) open item rules, specifically regarding the CONVERSION_EXIT_ALPHA assignment for Partner Bank Type (BVTYP).

Additionally, the note corrects the mapping rule MAP_INFKY (Inflation Key), which should have a character length of 8 instead of the incorrect 2, affecting the migration objects SIF_GL_ACCOUNT_2, SIF_GL_ACCOUNT_3, SIF_GL_ACC_EXT, and the mapping rule itself.

Users with installations of SAP S/4HANA 2020 from Support Package 00 to 06 who encounter these issues can apply the Transport-based Correction Instruction (TCI) included in the note. The TCI only corrects objects in the SAP-delivered content, and as a result, any generated migration objects will be updated automatically. However, the note cautions that if users have previously modified or copied an object, these corrections will not be applied to those modified or copied objects. In such cases, users would need to refer to SAP KBA 2543372 for guidance on how to implement a Transport-based Correction Instruction. 

Linked SAP Notes for more detailed descriptions of the issues are provided within the content of Note 3364988.