SAP Note 2318787 addresses an issue where the sum of the revenue schedule value is displayed incorrectly on the checklist within the context of revenue accounting. The specific transaction or report affected are likely FARR_CONTR_CHECK and FARR_CONTR_MON, which are associated with monitoring and checking contracts in revenue accounting.

The root cause of the problem is identified as a flaw in the method `cl_farr_rev_schedule=>query_rev_schedule`, which delivers incorrect values to the `et_rev_summary` table. The note provides a solution to this issue, suggesting that users implement the instructions contained within the note to correct the revenue schedule values that appear in the checklist. Implementing this note is necessary to ensure the consistency and accuracy of revenue schedule calculations in the application.