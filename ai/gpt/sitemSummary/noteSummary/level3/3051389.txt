SAP Note 3051389 addresses an issue in the SAP S/4HANA Migration Cockpit with the migration object "Customer Extend existing record by new org level". The symptom reported is that users encounter an error message stating "You can only flag 1 payment card as a standard" even if only one payment card is flagged as standard or just one credit card is assigned per customer record. This error is due to a mistaken check and a missing refresh of a variable in the program.

The solution provided in the note is to implement a set of Transport-Based Correction Instructions (TCI) Notes that contain fixes for this problem. Specifically, the note advises implementing the following TCI Notes based on your SAP S/4HANA release and support package level:

- For release 1809 SP00 to SP06, implement TCI Note 3052793.
- For release 1909 SP00 to SP04, implement TCI Note 3052826.
- For release 2020 SP00 to SP02, implement TCI Note 3052782.

These corrections will solve the erroneous behavior during the simulation of the migration object and allow proper flagging of a single payment card as the standard without the error.