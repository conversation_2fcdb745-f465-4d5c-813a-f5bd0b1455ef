SAP Note 961523 addresses a specific error that occurs within SAP: the termination of type BRAIN X299 in program SAPLRRK0, specifically within form SET_TCUR-03-. This error happens when the user attempts to switch currency translation within a Query in SAP.

Summary of the SAP Note 961523:

**Symptom:**
Users encounter a termination error (BRAIN X299) when switching currency translation in a Query.

**Other Terms:**
Relates to Query and currency translation.

**Reason and Prerequisites:**
The issue arises due to a programming error.

**Solution:**
To rectify this error, it is mandatory to implement SAP Note 986432. Additionally, for SAP NetWeaver 2004s BI systems, it is required to import Support Package 09 (BI Patch 09 or SAPKW70009). The details of this Support Package are further described in SAP Note 914303 titled "SAPBINews BI 7.0 Support Package 09".

It is important to note that the Support Package should be imported only after its release, which is indicated by the issuance of the related SAP Note 914303 for customers.

In urgent scenarios where waiting for the Support Package release is not feasible, there is an option to apply the correction instructions provided by SAP directly.

Furthermore, the related notes may already be available even before the release of the Support Package. If this is the case, they will be marked with a "Preliminary version" note in their short text to indicate their status.

In summary, the resolution involves implementing a related note and updating the SAP system with a specific Support Package, or applying direct corrections if immediate action is necessary.