SAP Note 1825259 addresses an issue where a UNIX-based SAP system cannot be started using the 'startsap' or 'sapcontrol' commands following an upgrade from kernel 700/701 to kernel 720 or higher.

Summary of the SAP Note:

- **Symptom**: Post-kernel upgrade, the SAP system cannot be started using 'startsap' or 'sapcontrol'.
- **Other Terms**: The keywords associated with this note include 'sapstartsrv', 'akk', '720', 'sapstart', and 'UNIX'.
- **Reason**: The problem occurs because the interactive logon for the &lt;sid&gt;adm user has been disabled at the OS level. As a result, the necessary environment for SAP and database processes is missing. Additionally, the startup sequence has changed in kernel stack 720 — in the older setup, the 'startsap' script would initiate both the 'sapstartsrv' and the 'sapstart' process, whereas in the newer setup, 'startsap' starts 'sapstartsrv', which then starts 'sapstart'.
- **Solution**:
  1. Users must check the environment known by 'sapstartsrv' and compare it with the output of the "env" command when logged in as &lt;sid&gt;adm using the following command: 
     `sapcontrol -nr <Instance number> -function GetEnvironment`.
  2. Then, they should add the missing environment settings to the instance profile, for example:
     ```
     SETENV_00 = dbms_type=ORA
     SETENV_01 = dbs_ora_tnsname=<SID>
     SETENV_02 = ...
     ```
     After updating the instance profile with the necessary environment variables, restart the 'sapstartsrv'.

This note also references SAP Note 1301712 for additional details on the environment calculation by the 'sapstartsrv'. The modifications in the startup sequence with the 720 kernel require additional environmental configurations to ensure proper system startup.