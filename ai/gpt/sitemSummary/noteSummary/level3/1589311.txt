SAP Note 1589311 outlines the replacement of various outdated software logistics tools by newer tools provided by SAP. This note lists multiple tools that have been replaced, such as the Software Update Manager (SUM) replacing the SAP Enhancement Package Installer (SAPehpi) and Java Support Package Manager (JSPM), and Software Provisioning Manager (SPWM) replacing Installation Master DVDs.

Key replacements mentioned include:
- In April 2012, SAPehpi 7.00 was replaced by SUM 1.0, with certain exceptions noted.
- In May 2012, the Upgrade Master DVD was replaced by SUM for upgrades to SAP NetWeaver 7.03 or higher.
- In August 2012 and December 2012, the Installation Master DVDs for various versions of SAP NetWeaver were replaced by SWPM.SAR or 70SWPM.SAR, respectively.
- In January 2013 and March 2013, the JSPM was replaced by SUM for SAP NetWeaver 7.3 and higher, and 7.1 and higher, respectively.
- In June 2013, SOLMANup was replaced by SUM for updates and upgrades of Solution Manager systems.
- In October 2013, Dual-Stack Split and System Rename tools were integrated into SWPM.
- In September 2015 and 2016, the Maintenance Planner succeeded the Landscape Planner and Maintenance Optimizer to calculate a stack XML file for system maintenance and add-on installation.
- In October 2015, sapcontrol replaced startsap/stopsap scripts.
- In February 2016, nZDM for Process Integration was replaced by SUM's downtime optimization capabilities.
- In July 2017 and September 2017, there were further moves to consolidate tools, including the deprecation of 32-Bit Developer Workplace and the release of SUM in versions 1.0 and 2.0 for specific scenarios.
- By January 2020, the installation option for "Gerrit for SAP XS Advanced" was to be removed from SWPM SP29 and above.
- In June 2020, guidance was provided for database migrations, and by September 2020, automated SP implementation and initial configuration were consolidated into the SL Toolset and ABAP Stack.
- By October 2020, end of support was announced for SAP NetWeaver Java including 7.40.
- In February 2021, end of support was noted for SUM 1.0 Startreleases 4.6C, 6.20, and 6.40 ABAP.
- By November 2021, ZDO for SAP S/4HANA became available in SUM 2.0 starting from SAP S/4HANA 2020.
- In January 2023, SAP advised using file share instead of FTP for security reasons during System Copy with SWPM, and finally, by October 2023, SPAM updates for unsupported SAP_BASIS versions would no longer be part of SL Toolset delivery.

The note also provides a detailed roadmap of these changes and recommends using the latest Software Logistics Toolset for support. Links to other related notes, information on exceptions, and additional guidance documents are also included.