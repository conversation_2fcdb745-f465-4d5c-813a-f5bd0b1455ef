SAP Note 630254 addresses an issue in the 0IC_C03 InfoCube, where the key figures for the calculated sales order stock and project stock are not being updated properly. These key figures are 0ISSVS_VAL (valuated stock issue value) and 0RECVS_VAL (valuated stock receipt value). The cause of the problem is identified as an incomplete update of these key figures from the 2LIS_03_BX and 2LIS_03_BF InfoSources.

The solution provided in this note involves changes to the update rules for the mentioned key figures. Specific modifications to the source code are detailed, including both the old (to be replaced) and new (corrected) source code segments. These changes are included in specific Support Packages: 3.0B SP14, 3.1 Content SP9, and BI Content 3.2 Add-on SP3.

Additionally, the note references the 0INDSPECSTK InfoObject and the KZBWS field in the transfer structure from 2LIS_40_S278 DataSource, indicating that without these, the 2LIS_40_S278 cannot update valuated customer stock, sales order stock, and project stocks. This note also directs readers to SAP Note 588015 for more information on this subject and SAP Note 616119 for extracting calculated special stocks.