SAP Note 2326776 addresses pre-transition checks for Long Material Number (LAMA) and Material Versioning in preparation for a transition to SAP S/4HANA. Here's a summary of the note content:

**Symptom:**
The note provides information on the pre-transition checks required for systems that utilize LAMA and Material Versioning before transitioning to SAP S/4HANA.

**Other Terms:**
The terms LAMA, Material Versioning, ECC-DIMP, and SAP S/4HANA are relevant to this note.

**Reason and Prerequisites:**
Since SAP S/4HANA extends the material number to 40 characters natively, the solution for the long material number specific to the automotive industry (LAMA) is not available. Additionally, Material Versioning functionality is not supported in SAP S/4HANA. Therefore, checks are necessary to ensure a smooth transition.

**Solution:**
The note outlines three major checks:

1. **LAMA Usage Check:**
   The pre-check verifies if the LAMA functionality is in use by looking at the 'MGV_LAMA' business function and the 'LMATNR' field in the 'TMCNV' table. If the 'LMATNR' field is greater than 18 characters, it indicates usage of LAMA, and a warning message will be displayed.

   Related SAP Notes for business process information: 2270396, 2360860
   Related SAP Notes for custom code information: 2228241

2. **Open Planned Changes for Material Master:**
   The pre-check searches for open planned changes in the 'PCDHDR' table. If there are entries with 'OBJECTCLAS' set to 'MATERIAL_N' and 'ACT_CHNGNO' unset, an error is displayed indicating the presence of open planned changes. These entries must be resolved before transitioning to S/4HANA.

   Related business process information can be found in SAP Note 2270396.

3. **Material Versioning Functionality:**
   The check verifies the usage of Material Versioning by checking the 'MVAKT' field in the 'TMCNV' table and entries in the 'MATERIALID' table. Since material versioning and its use in IPPE (Integrated Product and Process Engineering) will not be available in S/4HANA, this note advises users to convert material master records with versions into separate entries in the material master.

   For resolving these issues, creating a customer incident on IS-A-LMN is recommended.
   
   Related SAP Note for business process information: 2270398

Users affected by these issues must refer to the provided SAP Notes for detailed resolution steps and may need to perform necessary clean-ups or configurations before migrating to S/4HANA.