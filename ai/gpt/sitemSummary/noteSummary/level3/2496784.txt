SAP Note 2496784 relates to the limitations and changes in the SAP S/4HANA 1709 release specific to the Aerospace and Defense (A&D) industry solution. Below is a summary of the restrictions and changes mentioned in the note:

1. The Product Designer Work Bench (PDN) has been removed and replaced by Product Structure Management (PSM). The A&D functions of iPPE should now be accessed via transaction code PPE, as PSM does not include these features.

2. The transaction MB11 is no longer available in S/4 HANA and has been replaced by transaction MIGO. However, MIGO does not support the movement types MT711B and MT712B for Customer Stock load. Instead, it is recommended to use movement types MT501B and MT502B. Nonetheless, MT711B and MT712B can still be used in the background of the transaction code LI21 for inventory adjustments with Inventory Documents.

3. Using Valuated Goods Produced (GPD) is restricted in SAP S/4HANA, and the Production Order Split functionality cannot accommodate GPD because it would require components and item detail costs to move between Parent and Child Orders.

4. Production Planning and Detailed Scheduling (PP/DS) does not support GPD. PP/DS is now a part of SAP S/4 HANA.

5. In regard to Aerospace & Defense Maintenance, Repair, and Overhaul (MRO) Subcontracting, the SAP S/4HANA system does not support Extended Warehouse Management (EWM) users. Specifically, A&D subcontracting orders will not create an inbound delivery for the material number on the purchase order item. Inbound Delivery is necessary for the interface between SAP S/4HANA and EWM. Therefore, it is suggested to use the core Subcontracting process with transaction code ME2O for Subcontracting Stock Monitoring for Supplier when using EWM, instead of transaction code ADSUBCON for creating subcontracting orders.

These restrictions should be taken into account when using the SAP S/4HANA 1709 release for A&D processes to avoid encountering issues due to incompatibility or missing functionalities.