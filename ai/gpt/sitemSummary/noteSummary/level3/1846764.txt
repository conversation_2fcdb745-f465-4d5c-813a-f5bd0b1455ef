SAP Note 1846764 addresses an issue where incorrect data occurs in a query when using the LEAF function in a formula within SAP NetWeaver Business Warehouse (BW). This issue is due to a program error.

The note provides the following solutions:

1. For SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010), with more details provided in SAP Note 1810084 titled "SAPBWNews NW 7.30 BW ABAP SP10."

2. For SAP NetWeaver BW 7.31 (SAP NW BW 7.3 Enhancement Package 1): Import Support Package 8 (SAPKW73108), with more details provided in SAP Note 1813987, with the short text "SAPBWNews NW BW 7.31/7.03 ABAP SP8."

3. For SAP NetWeaver BW 7.40: Import Support Package 3 (SAPKW74003), with more details provided in SAP Note 1818593 titled "SAPBWNews NW BW 7.4 ABAP SP03."

The note also advises that in urgent cases, the correction instructions can be implemented as an advance correction. However, it is important to first read SAP Note 1668882 for information on using transaction SNOTE.

It also mentions that the SAP Notes referred to for further information about the Support Packages might be available before the actual release of the packages. If so, these notes will contain the phrase "Preliminary version" in the short text to indicate they are not the final versions.