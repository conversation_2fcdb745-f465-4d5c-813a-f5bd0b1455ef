SAP Note 949670 addresses a problem where hierarchy authorizations that were created in a development or a test system and then transported to another system do not function as expected. After the transport, the hierarchy authorizations may cause the query to terminate unexpectedly or, in rare cases, access incorrect hierarchy authorization data.

The issue is identified to be a result of a program error. The note refers to terminology such as Analysis authorizations, transport, HIESID, HIEID, and hierarchy definition.

To resolve this issue, the note recommends importing Support Package 09 for SAP NetWeaver 2004s BI (specifically BI Patch 09 or SAPKW70009) into the affected BI system. Additional details about this Support Package can be found in SAP Note 0914303 titled "SAPBINews BI 7.0 Support Package 09," which describes the Support Package in more detail. It's important to note that this SAP Note 0914303 may be available even before the Support Package is officially released. If the note is still in a "Preliminary version," it will be indicated as such in the note's short text.

For those who require an immediate fix and cannot wait for the Support Package release, the note suggests using the correction instructions provided. However, details of the temporary correction instructions are not included in the provided summary and would likely be found within the full text of the note itself.