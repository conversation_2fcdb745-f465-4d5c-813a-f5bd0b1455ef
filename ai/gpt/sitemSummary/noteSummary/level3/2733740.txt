SAP Note 2733740 is a Release Information/Restrictions Note for SAP BW/4HANA 2.0. Here's the summary of the information contained in this note:

1. **Urgent Information**: Before upgrading to BW/4HANA 2.0:
   - Implement Note 2770525 to address the issue with Data Transfer Intermediate Storage (DTIS).
   - Implement and execute Note 3138908 to fix issues with ABAP CDS trigger tables in ODP.
   - To solve errors in the SUM Phase MAIN_SWITCH/PARMVNT_VIEW or MAIN_SWITCH/PARMVNT_BAS_VIEWS, implement Note 2756802.
   - During SUM Phase ACT_UPG, follow the guidance provided in Note 2895585 or implement the latest DDICFIXBUFFER from SMP (dated before March 1).
   
2. **General Information**:
   - The supported HANA releases for BW/4HANA 2.0 are detailed in Note 2347382, with a minimum requirement of HANA 2 SPS03 Revision 36.
   - Installation and upgrade processes utilize SUM 2.0 and SWPM 2.0, optimized for HANA Database.
   - BW/4HANA 2.0 includes the UIBAS001 400 component.
   - The use of BW/4HANA 2.0 as a Frontend Server for other SAP-owned FIORI Add-Ons is not supported, but it can be used as an embedded Frontend Server for custom-developed FIORI applications.
   - Implementation of a UIBAS001 version higher than 400 is not permitted.
   - The support strategy for BW/4HANA 2.0 remains the same as for BW/4HANA 1.0, recommending at least an annual update of the underlying stack.

3. **Information about migration and upgrade**:
   - In-place Conversion from SAP BW 7.50 to BW/4HANA 2.0 is supported from BW/4HANA 2.0 SP2 or higher.
   - The direct In-place Conversion from BW/4HANA 1.0 to 2.0 is recommended from BW/4HANA 1.0 SP08 or higher.
   - In-place conversion from AS ABAP Innovation Packages 7.51, 7.52, and 1809 to BW/4HANA 2.0 is not supported.

3.a) A table is provided to help determine the equivalent Support Packages needed for upgrading from SAP BW/4HANA 1.0 and In-place Conversion from SAP BW 7.50. The Maintenance Planner tool can help calculate this.

3.b) For languages ET, LV, LT, and TH, which were mistakenly delivered for BW/HANA 1.0, Note 2811845 offers a solution if these languages have been installed and you're planning to upgrade to BW/4HANA 2.0.

Overall, the SAP Note provides critical information and actions required for successfully migrating and upgrading to SAP BW/4HANA 2.0, along with necessary prerequisite steps, compatibility requirements, and recommendations for a smooth transition.