SAP Note 2718516 addresses the issue that occurs when users input numbers with more than 15 digits in Microsoft Excel while using the SAP S/4HANA Migration Cockpit with the file transfer option. Due to Excel's limitation, which is based on the IEEE 754 specification for storing floating-point numbers, any digits past the 15th place are changed to zeros. This note explains the cause of the issue, provides possible resolutions, and references a Microsoft support page for more detailed information.

Resolution options include:
1. Manually entering the records with more than 15 digits into SAP S/4HANA using the appropriate transaction or app when the number of fields affected is limited.
2. For a larger number of fields, opting for a migration project that uses the "Staging tables" transfer option is recommended.

The note is relevant for users of both SAP S/4HANA and SAP S/4HANA Cloud environments.