SAP Note 1841927 addresses an issue in SAP NetWeaver Business Warehouse (BW) where the error counter (RSERRORCOUNT-COUNTER) displays an incorrect number of error program runs during the serial processing of data packages.

The problem described is due to a programming error and affects multiple versions of SAP NetWeaver BW, including:

- SAP NetWeaver BW 7.00
- SAP NetWeaver BW 7.01
- SAP NetWeaver BW 7.02
- SAP NetWeaver BW 7.11
- SAP NetWeaver BW 7.30
- SAP NetWeaver BW 7.31
- SAP NetWeaver BW 7.40

The solution proposed for each version is to import a specific Support Package into the BW system, which contains fixes that resolve the error counting issue:

- For SAP NetWeaver BW 7.00, import Support Package 31 (SAPKW70031).
- For SAP NetWeaver BW 7.01, import Support Package 14 (SAPKW70114).
- For SAP NetWeaver BW 7.02, import Support Package 14 (SAPKW70214).
- For SAP NetWeaver BW 7.11, import Support Package 12 (SAPKW71112).
- For SAP NetWeaver BW 7.30, import Support Package 10 (SAPKW73010).
- For SAP NetWeaver BW 7.31, import Support Package 8 (SAPKW73108).
- For SAP NetWeaver BW 7.40, import Support Package 3 (SAPKW74003).

The availability of these Support Packages is linked to the release of other SAP Notes, which provide more details about the respective Support Package:

- SAP Note 1782745 for NW 7.00 SP31
- SAP Note 1794836 for NW 7.01 SP14
- SAP Note 1800952 for NW 7.02 SP14
- SAP Note 1797080 for NW 7.11 SP12
- SAP Note 1810084 for NW 7.30 SP10
- SAP Note 1813987 for NW 7.31 SP8
- SAP Note 1818593 for NW 7.40 SP03

If there is an urgent need to correct the issue before the Support Package is made available, it is possible to implement correction instructions as an advance correction. However, before doing so, users are instructed to first read SAP Note 1668882, which provides information about using transaction SNOTE.

SAP Notes mentioned as a preliminary version might already be available before the Support Package release. If this is the case, their short text will include the words "Preliminary version".