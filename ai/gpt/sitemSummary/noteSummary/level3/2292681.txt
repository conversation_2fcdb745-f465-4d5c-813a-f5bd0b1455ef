SAP Note 2292681 provides information regarding the new output management and master form templates introduced in SAP S/4HANA for versions 1511 and 1610. Key points from this note include:

1. SAP S/4HANA Output Control: A new interface between business applications and SAP NetWeaver technologies for managing outputs.

2. Master Form Templates: Shipped by SAP as default templates for business applications, these are PDF-based print forms with various layout definitions known as fragments.

3. Fragments: Each default master form template includes predefined layout fragments for different page orientations (landscape or portrait) and types (fact sheet, item list, outbound letter). Custom fragments cannot be added by customers, but the layout of existing fragments can be modified.

4. Fragment Layout: Specific layout definitions for master pages of the first page and subsequent pages, including paper size, content area, layout for the form title, logo, page numbers, sender and receiver address, and footer blocks.

5. Content: Master form templates are configurable for new output controls, with placeholders for content that is filled at runtime by a gateway service.

6. Default Shipment: SAP provides master form templates with placeholders for text and logos, available for both portrait and landscape orientations.

7. Master Form Template Implementation: These are XDP files and should be edited according to rules provided in SAP Note 2292646.

8. Translation: SAP delivers forms in all localized languages. Customizations require manual translation in Adobe LiveCycle Designer, and maintenance is done via the Fiori app "Maintain Form Templates."

Users are directed to standard documentation on help.sap.com for releases beyond 1709.

The note serves to guide users on the new master form templates and how to work with them in SAP S/4HANA, indicating how to modify layouts, deal with content insertion and manage translations for customized templates.