SAP Note 2498019 outlines the supported browsers, Java versions, and other technical requirements for running SAP Portfolio and Project Management on SAP S/4HANA 1709.

Summary:

- The note provides information that may change due to browser incompatibilities, as browsers are not SAP software.
- Supported browsers include current versions of Microsoft Internet Explorer 11 (with specific settings recommended and limited OS support after June 15, 2022), Microsoft Edge (Current Branch for Business), Mozilla Firefox (Java Applets not supported after Firefox 52), Google Chrome (Windows), and Apple Safari (latest 2 versions for MacOS). SAP Business Client 6.5 and higher is also supported.
- Browser restrictions apply due to the use of Active Controls, specifically for the Gantt Chart, Multi-Project Monitor, and Adobe Interactive Forms within the Web Dynpro UI.
- SAP Note 2850870 outlines replacement of Adobe Interactive Forms, and SAP Notes 2645827 and 2945333 discuss replacements for Java-based monitors with UI5 solutions.
- Java applets are used by SAP Portfolio and Project Management, and the recommended Java JRE version is 1.8 or higher. The Java cache should be activated for applets as explained in SAP Note 1014150.
- The URLs for accessing SAP Fiori UI and Web Dynpro UI are provided.
- Antivirus software may affect performance, with a proposed solution of disabling ScriptScan or creating a trusted scripts whitelist.
- For MS Project import/export, a local installation on the user's PC is required, with supported versions outlined in SAP Note 892638.
- Adobe Reader latest version is recommended for SAP Interactive Forms and Adobe Print Forms.
- The software supports multiple languages, with Adobe Print Forms and some reports limited to specific languages.

This note gives details on technical prerequisites ensuring the proper functioning of SAP Portfolio and Project Management in SAP S/4HANA 1709, addressing supported web browsers, Java Applet usage, limitations with certain functionalities and alternatives, and certain third-party software requirements.