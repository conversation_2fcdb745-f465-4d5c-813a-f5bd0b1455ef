SAP Note 2891757 deals with the selective deletion of non-cumulative key figures specifically referring to the special technical characteristic 0RECORD<PERSON> in SAP BW providers. This characteristic determines if a data record is a reference point, a historical movement, or a new movement.

For SAP BW NetWeaver systems, the note outlines that:

- If the 0RECORDTP is not specified during the deletion selection, both movements and reference points will be deleted.
- Users can selectively delete either movements or reference points by filtering on 0RECORDTP values 0 or 2 for movements and 1 for reference points.
- However, careful consideration is required as improper deletion can cause inconsistencies in the provider, particularly when re-loading data that should repair incorrect data. The note stresses the importance of re-loading historical movements with the correct 0RECORDTP value to avoid issues during data compression.

For SAP BW/4HANA systems:

- The characteristic 0RECORDTP is not available as a deletion criterion in versions up to BW/4HANA 2.0.
- As of BW/4HANA 2023, a new feature is introduced for deleting reference points, which allows defining time restrictions and using the transaction DELETE_FACTS.
- There are two distinct methods for deleting markers explained in attached documentation, including steps to ensure that material data deletions are performed thoroughly, leveraging the settings for 'Reference Points are not deleted' and 'Unused Reference Points are deleted'.

Additionally, the note mentions the importance of ensuring that the newly loaded records with 0RECORDTP=1 can be compressed correctly, warning against the existence of compressed movements without markers which could lead to the termination of the compression process.

Lastly, the note references related SAP Notes and SAP Support Content for further information on handling non-cumulative key figures and provides keywords such as 'ncum', 'inventory cube', 'ADSo', and 'non-cumulative' for additional context.