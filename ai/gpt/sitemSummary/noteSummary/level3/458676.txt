SAP Note 458676 addresses a situation where a user wants to extract data from an AFS (Apparel and Footwear Solution) system, version 3.0B or higher, into a BW (Business Warehouse) system, also version 3.0B or higher. This extraction aims to supply AFS-specific InfoCubes that are available from BW 3.0B onwards.

Key Points Summarized:

- **Symptom**: Users need to enhance extractors to include AFS-specific fields for use with AFS-specific InfoCubes in BW.

- **Other Terms**: Refers to the extraction of AFS data and mentions the program /AFS/RMCSBWXP and elementary fields.

- **Reason and Prerequisites**: Standard extractors do not include AFS-specific fields. Users must refer to note 502045 for prerequisites related to AFS and SAP BW usage.

- **Solution**: The steps outlined for enhancing the extractors with AFS fields are critical, and users must perform them in the given order:

  1. Transfer and generate the standard non-AFS-specific DataSources.
  2. Transfer the AFS-specific fields without generating them.
  3. Optionally enhance the DataSources with custom fields.
  4. Generate the DataSources.
  5. Activate the update.
  6. Replicate the DataSources into BW.
  7. Maintain the transfer rules in BW (only for BW releases lower than 3.2).

The note gives detailed instructions on how to perform each of these steps, including copying and activating DataSources, running the /AFS/RMCSBWXP report to enhance extractors with AFS fields, customizing the extraction with transaction LBWE, setting inversion flags for specific fields manually, activating updates for the DataSource, replicating extended DataSources into BW, and creating transfer rules manually for BW releases lower than 3.2.

Several AFS-specific extractors are mentioned (such as 2LIS_02_SCL, 2LIS_03_BF, 2LIS_11_VASCL, etc.), and the note provides specific fields which have to have the inversion flag manually set.

Lastly, the note references additional information on the transfer rules found in note 520006 and stresses the importance of not repeating certain actions (like activating the DataSources via transaction SBIW or RSA5) as it might result in lost enhancements.