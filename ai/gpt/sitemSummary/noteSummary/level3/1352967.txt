SAP Note 1352967 addresses specific functionalities related to the Swiss healthcare smart card within the Swiss version of SAP IS-H (Industry Solution - Healthcare). These functions include manual entry of card data, patient search and creation using card data, patient master data comparison, generation of insurance relationship proposals based on card data, electronic invoice creation, and card data management.

Key points from the SAP Note:

- This note is applicable only to the country version for Switzerland.
- It lists the required add-on service packs for various IS-H versions: AOSP 02 for IS-H Version 6.04, AOSP 03 for IS-H Version 6.03, AOSP 16 for IS-H Version 6.00, and AOSP 31 for IS-H Version 4.72.
- Before implementing the source code corrections provided in this note, SAP recommends performing several manual steps in sequence. These include enhancing table TNEHC_DOCTYPE for IS-H Version 6.04, unpacking and importing the attached files as per the IS-H version, performing additional manual tasks (like enhancing domain ISH_CRDTYPE, defining new nodes and branches in the IMG, and setting up number range objects and TNPOL modules), and then implementing the source code corrections.
- It also provides a Customizing and user documentation for the Swiss healthcare smart card functions.
- For IS-H Version 6.03, additional implementation of Notes 1359214 and 1359388 is required.
- The source code corrections and further instructions are provided in attached ZIP files, which are not available for download via OSS but can be accessed from the SAP Service Marketplace.

Note: The disclaimer indicates that this is a machine-translated document and may not be fully accurate. It is recommended to consult the original document for precise instructions.