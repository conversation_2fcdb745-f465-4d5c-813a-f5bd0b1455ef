SAP Note 3408795 addresses an issue where, after either a new installation of SAP BW/4HANA 2023 or an upgrade to this version and performing specific task lists (SAP_BW4_SETUP_SIMPLE for installations or SAP_BW4_AFTER_UPGRADE for upgrades), the BW/4 Cockpit is not fully configured. Users experience HTTP 403 errors when clicking on a tile in the Fiori App.

The cause of the problem is identified as a program error.

The solution involves manually activating the "BW4" HTTP service using the transaction UCON_HTTP_SERVICES. Furthermore, the note provides correction instructions that include this fix in the task used within the mentioned task lists.

For a permanent solution, users are instructed to implement Support Package 01 for SAP BW/4HANA 2023 (SAPK-40001INDW4CORE), which will address the issue once SAP Note 3372694, describing the Support Package in more detail, is released to customers.

In urgent cases where immediate resolution is required, users are advised to apply the correction instructions provided within the note. However, before doing so, they should also check SAP Notes 1668882 and 2248091 concerning the use of transaction SNOTE.

If this SAP Note is available before the release of the Support Package, it might be labeled as a "preliminary version" in its short text.