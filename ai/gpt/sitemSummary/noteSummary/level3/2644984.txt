SAP Note 2644984 addresses issues with some features within the "Formulas" planning function type on SAP HANA Database (HDB). Specifically, it announces the release of two features:

1. Access to SAP HANA views with ATRV (Attribute Value) is now supported as of SAP HDB Release ***********.
2. INT8 (8-byte integer) processing in FOX formula calculations is supported as of SAP HDB Release *********** or ***********.

To resolve these issues, customers need to import specific support packages based on their SAP BW version:

- For SAP BW 7.50, import Support Package 13 (SAPK-75013INSAPBW). Details can be found in SAP Note 2633431.
- For SAP BW 7.51, import Support Package 7 (SAPK-75107INSAPBW). Details can be found in SAP Note 2586678.
- For SAP BW 7.52, import Support Package 3 (SAPK-75203INSAPBW). Details can be found in SAP Note 2613219.
- For SAP BW/4HANA 1.0, import Support Package 13 (SAPK-10013INDW4CORE). Details can be found in SAP Note 2756841.

Additionally, users must run the program RSPLS_DELETE_TEMPLATE_SESSIONS with the 'Delete' parameter selected. In urgent situations, correction instructions can be applied in advance.

Before implementing the instructions, users should read SAP Notes 1668882 and 2248091, which provide information on using transaction SNOTE. Customers may also access the specified SAP Notes before the release of the support package, albeit these may be marked as "preliminary version" until the official release of the support package.