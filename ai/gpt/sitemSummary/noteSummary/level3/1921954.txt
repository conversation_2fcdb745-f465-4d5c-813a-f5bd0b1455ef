SAP Note 1921954 pertains to the installation of Support Package 01 for the Component Extension 4.0 for SAP Environment, Health, and Safety Management (EHSM), specifically focusing on the Product Compliance aspect. The note summarizes the necessary configuration changes and post-installation steps that users must take after the installation of EHSM400 SP01.

Summary of SAP Note 1921954:

**Symptom:**
Users who want to install Support Package 01 for EHSM400 for Product Compliance and IMDS compliance need to be aware of configuration changes and required post-installation steps.

**Customizing Changes:**
The support package introduces the following Customizing changes:
- IMDS Check
- BOMBOS Transfer
For further details, users are referred to corresponding notes.

**Post Installation Steps:**
There are certain Business Configuration (BC) sets that need to be activated after installing EHSM400 SP01:
- EHPRC_DI_BOM_BOS
- EHPRC_DI_IMDS
- EHPRC_SPECIAL
- EHPRC_DI_PRODUCT_ASSM_PROD
It's important to use transaction SCPR20 to ensure the consistency of Customizing data before activating any BC sets to avoid overwriting any existing data that needs to be retained.

**Miscellaneous:**
After the installation, the caches for personal object worklists (POWLs) must be refreshed using the transaction SA38 and running specific reports in a given order:
a) POWL_D04 - Delete Cached Selection Criteria for Admin Queries (Caution with "APPLID" = EH*)
b) POWL_D01 - Delete Queries from Database (Caution with "APPLID"= EH* and provide a list of all users)

**Other Terms:**
Reference to the support package as EHSM400SP01PRC.

**Reason and Prerequisites:**
This note is released due to configuration and/or program errors that have been identified.

**Solution:**
The corrections and Customizing changes that address the issues are included within EHSM400, Support Package 01.

Users responsible for the installation and maintenance of the EHSM 4.0 should closely follow the instructions in this note to ensure proper implementation of the support package and to avoid any potential issues resulting from the upgrade.