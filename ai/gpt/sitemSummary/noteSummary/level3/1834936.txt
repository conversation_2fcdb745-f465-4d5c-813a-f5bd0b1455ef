SAP Note 1834936 addresses an issue where a previous correction made in SAP Note 1667511 was not fully applied in SAP NetWeaver Business Warehouse (BW) releases greater than 7.30. This note provides a solution to correct this oversight.

The problem identified in the note is a program error related to InfoCube extraction via API or SQL.

To resolve this error:

For SAP NetWeaver BW 7.31 users: 
- Import Support Package 08 for SAP NetWeaver BW 7.31 (SAPKW73108) into the BW system. 
- Further details about this Support Package can be found in SAP Note 1813987 with the short text "SAPBWNews NW BW 7.31/7.03 ABAP SP8," which will be released to customers.

For SAP NetWeaver BW 7.40 users: 
- Import Support Package 03 for SAP NetWeaver BW 7.40 (SAPKW74003) into the BW system. 
- For more information, refer to SAP Note 1818593 "SAPBWNews NW BW 7.4 ABAP SP03," which will also be made available to customers.

In urgent cases where the Support Package cannot be immediately implemented, the note advises implementing the correction instructions as an advance correction. However, before doing so, users must first read SAP Note 875986 for important information about transaction SNOTE.

Additionally, it is noted that the SAP Notes mentioned (1813987 and 1818593) may be available before the Support Package is actually released. If that’s the case, the short text of the SAP Note will still contain the words "Preliminary version".