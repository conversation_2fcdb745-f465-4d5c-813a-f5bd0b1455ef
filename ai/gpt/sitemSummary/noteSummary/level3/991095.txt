SAP Note 991095 is an informational note that lists the corrections and enhancements included in Support Package 15 for SAP Business Intelligence (BI) Release 7.0, which is also part of SAP NetWeaver 7.0 Support Package Stack 13. The note outlines manual actions that may be necessary before and after importing the Support Package, general information regarding issues fixed and features added in the package, and specific errors that could occur after the import.

Key points from the note include:

- **Manual Actions**: Before importing the Support Package, it is recommended to implement SAP Note 1157796 to perform a consistency check for characteristic 0REQUEST. Additionally, Note 1106385 should be implemented to prevent source code loss during SPAU adjustment. Inactive objects in the SAP namespace should be activated and not deleted; refer to Note 1131831 for further instructions. For instructions on downloading the Support Package stacks and other related actions, see Notes 911032 and 875986. 

- Additional recommendations before implementation of the Support Package are to implement Notes 932065 and 948389.

- **Post-Implementation Steps**: After importing, transaction SNOTE should be used to reimplement notes that have become inconsistent, to ensure the system operates smoothly. Notes related to the BI Accelerator (1065360 and 1064898) and Preventing Transformation Rule Disappearance (1085318) should be implemented.

- **Import-Related Errors**: After importing, issues may arise with input-ready queries, reading virtual providers, queries with specific calculations, starting the change run, and activating a transformation with multiple groups.

- **Corrections in the Support Package**: This release fixes issues related to InfoCube compression on IBM system i, start conditions in BAPI_IPAK_START, and no InfoPackages being offered for process chain maintenance after implementing Note 1033967.

- **Enhancements**: Performance improvements when deleting DTP requests, SFK optimization for structure element selection, and automatic collection of source requests in DTPs with specific transfer options.

Users are advised to be selective with implementing further SAP Notes after importing the Support Package, focusing on "Hot News" and priority 1 notes. The search function in the SAP Notes database or SAP Service Marketplace should be utilized with the search term "SAPKW700xx" and the appropriate filters to find relevant high priority corrections. Following these guidelines and performing necessary actions will help in addressing potential issues and taking full advantage of improvements provided in the Support Package.