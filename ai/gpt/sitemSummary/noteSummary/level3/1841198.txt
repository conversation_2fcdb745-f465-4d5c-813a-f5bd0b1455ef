SAP Note 1841198 addresses an issue in SAP NetWeaver Business Warehouse (BW) where a delta Data Transfer Process (DTP) fails to repair a request that has previously ended with a red (error) status. Instead, this DTP creates a new request with the same source requests as the red predecessor request.

Symptoms:
- Delta DTP does not handle the last red predecessor request properly; rather than repairing it, a new request with identical source requests is generated.

Other Terms:
- The note mentions relevant terms including DTP, delta DTP, red predecessor request, repair, delete, and source request.

Cause:
- The issue is due to a program error, where the source of the DTP is either an InfoCube or a DataSource with delta method "InfoCube Extraction" and the update is commutative.

Solution:
- The resolution involves importing the appropriate Support Package for the SAP NetWeaver BW version your system is running on. The note lists support packages for multiple BW versions, from SAP NetWeaver BW 7.00 through SAP NetWeaver BW 7.40, and the corresponding SAP Notes that provide more details on these packages. The availability of these packages is indicated by the release of specific SAP Notes, which describe them in more detail.

- For urgent cases, the SAP Note suggests implementing correction instructions as an advance correction and emphasizes the importance of first reviewing SAP Note 1668882 for information on using transaction SNOTE.

- The above-mentioned SAP Notes might be available in advance of the Support Package release. If so, their short text will indicate "Preliminary version."

In summary, this SAP Note provides a workaround and permanent solution via the import of respective Support Packages to fix a specific delta DTP issue in SAP NetWeaver BW systems where error requests are not being properly rectified.