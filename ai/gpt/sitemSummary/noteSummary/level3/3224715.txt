SAP Note 3224715 addresses an issue encountered by SAP S/4HANA users (1909, 2020, and 2021) when migrating data related to G/L account balances and open/line items, accounts payable open items, and accounts receivable open items using the respective migration objects (SIF_GL_BALANCE, SIF_OPEN_ITEM_AP, and SIF_OPEN_ITEM_AR). The two key problems users may face are:

1. During data upload, error messages indicate that fields for transaction currency and the related amount as well as local/company code currency and the associated amount are mandatory, requiring a nonzero value.

2. When using the "Deferred Tax Items" tab, an error message indicates that the "Tax Amount" field is empty. Additionally, if users specify a zero amount for any related currency, the import incorrectly populates it with a nonzero value based on pre-configured exchange rates, contrary to user input.

The note mentions that this issue occurs while using the pre-delivered SAP S/4HANA Data migration content in both file-based and staging table-based data transfers, without any modifications made to it.

To resolve this issue, a Transport-based Correction Instruction (TCI) is attached to SAP Note 3224715. Implementing the TCI will make all currency and amount fields optional, allowing zero amounts to be specified without being automatically replaced by exchange rate values. The specific updates depend on the SAP S/4HANA version:

- For 1909: Implement TCI Note 3224799 for SP00 to SP06.
- For 2020: Implement TCI Note 3224852 for SP00 to SP04.
- For 2021: Implement TCI Note 3224829 for SP00 to SP02.

Note that if custom modifications or copies of the migration objects were done, the TCI does not apply to them, and the fix must be applied manually. For guidance on how to implement a TCI, users are referred to KBA 2543372 "How to implement a Transport-based Correction Instruction".