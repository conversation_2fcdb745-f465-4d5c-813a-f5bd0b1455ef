This SAP Note addresses an issue observed in SAP S/4HANA versions 1909, 2020, and 2021 where, during the data migration phase using the "QM - Master inspection characteristic" (SIF_MSTR_CHAR) object, the Master Inspection Characteristic (MIC) names that are included in the migration file in lowercase are incorrectly created with lowercase IDs (names) instead of being converted to uppercase, which is the standard naming convention in SAP.

The problem occurs when transferring data using the Data Migration Cockpit by staging tables. The symptom of this issue is that when you import your data, the MIC name does not get automatically converted to uppercase as expected.

The note specifies that the cause for this issue is that the conversion to uppercase for the MIC name does not take place within the migration content.

To resolve the problem, SAP provides a specific Transport-Based Correction Instruction (TCI) note for different SAP S/4HANA releases and support package levels. The solution is as follows:

- For SAP S/4HANA 1909 from Support Package (SP) 00 to SP06, implement TCI Note 3236212.
- For SAP S/4HANA 2020 from SP00 to SP04, implement TCI Note 3236158.
- For SAP S/4HANA 2021 from SP00 to SP02, implement TCI Note 3236170.

Implementing the relevant TCI note listed in the table provided in the SAP Note should fix the issue and ensure that MIC names are correctly converted to uppercase during the data migration process.