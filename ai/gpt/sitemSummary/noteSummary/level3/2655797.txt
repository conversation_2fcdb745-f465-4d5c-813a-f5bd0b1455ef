SAP Note 2655797 addresses a situation relevant for businesses looking to manage customer master data in SAP S/4HANA, especially in scenarios demanding a simple user interface for use in retail stores by store associates. It acknowledges that the existing SAP Fiori apps "Manage Customer Master Data" and "Maintain Business Partner Data" are too complex for managing consumer data in such contexts.

The note is applicable from SAP S/4HANA 1809 FPS 001 onwards and provides a solution with two major components:

1. UI adaptation at runtime: To make the "Manage Customer Master Data" app more user-friendly, the note recommends simplifying the app’s UI. This becomes necessary because, unlike in SAP ECC, S/4HANA requires capturing additional information such as roles, sales area, and company code data along with address data to create a functional customer record for sales order processing. UI adaptation can be done by key users with appropriate authorizations (SAP_UI_FLEX_KEY_USER role).

2. A best practice approach: For fast consumer master data creation, the note suggests using a simplified version of the "Manage Customer Master Data" app and a reference customer concept. Beforehand, the user creates a fully operational customer master record (REFERENCE_CUSTOMER) with all necessary data, such as roles, sales areas, company code, currency, incoterms, payment terms, etc. Thereafter, this reference customer can be copied to quickly create new consumer records by only updating the address data.

The document references additional resources to support these processes, such as enabling UI Adaptation at Runtime and adapting Fiori UIs at runtime, and provides external links for further help. The intended result is a "SIMPLIFIED_APP" that allows for easy creation and management of consumer data with the necessary operational details for sales order processing.