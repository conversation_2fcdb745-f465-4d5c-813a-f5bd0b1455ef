SAP Note 2643790 addresses an issue in the incident management application where the system improperly conducts a check for filled out mandatory fields in a task under the "Task Details" section upon selecting the task type, even without saving or starting the process.

Summary:
- The symptom is that when creating a task in an incident and selecting the task type, unnecessary checks for mandatory fields are triggered.
- This note aims to prevent these premature mandatory fields checks in the "Tasks" tab of the incident management application.
- The underlying reason for this issue is a program error.
- To apply the fix, users should refer to the correction instructions provided with this SAP Note or update their systems with the specified Support Packages.

This SAP Note is intended for users experiencing this specific issue and provides guidance on how to rectify it either through implementing Support Packages or following manual correction instructions.