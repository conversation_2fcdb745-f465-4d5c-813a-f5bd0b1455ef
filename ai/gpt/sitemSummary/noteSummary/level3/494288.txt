SAP Note 494288 addresses an issue where certain fields are missing from the 2LIS_03_UM extractor, which affects the correct transfer and update of invoice data in SAP BW when a material revaluation occurs due to a price difference between the purchase order and the invoice. The data records transferred may cause incorrect updates to InfoCubes, such as 0CP_IC_C1.

Key points from the note include:

- **Symptom**: Incorrect InfoCube updates when posting invoices that lead to material revaluation where the material is managed quantitatively but not by value (as indicated by the designators T134M-MENGU = X and T134M-WERTU = ' ').
- **Other Terms**: Refers to fields BSTTYP (stock category) and BSTAUS (stock characteristic).
- **Reason**: The extractor 2LIS_03_UM does not include the fields BSTTYP and BSTAUS, and the 0STOCKCAT InfoObject is not queried in the update rules.
- **Solution**:
    - Manually add BSTTYP and BSTAUS to the 2LIS_03_UM extractor using transaction LBWE. The note gives instructions to copy these fields into the structure during maintenance. 
    - Make corresponding changes in BW, including adding BSTAUS and BSTTYP to the 2LIS_03_UM InfoSource and modifying the update rules. Reference to Note 495743 is provided for guidance on this step.
    - Change cannot be delivered through a Support Package but as of PlugIn 2002.1, the standard delivery includes the changes.
    - After making these changes, a delta initialization of the DataSource 2LIS_03_UM is necessary to ensure data consistency in BW.

**Caution**: The note underscores the critical need for a delta initialization post inclusion of the fields to ensure the consistency and commercial utility of the data in SAP BW.

**Additional References**: The note suggests referring to Notes 328181 and 396647 (Question 8) for more information. 

This note gives detailed technical steps how to resolve the data inconsistency issue for SAP BW when using the 2LIS_03_UM extractor to ensure accurate financial and material data representation after invoice postings.