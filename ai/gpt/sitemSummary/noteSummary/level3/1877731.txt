SAP Note 1877731 - Installed Software Information (ISI)

Summary:

This SAP Note addresses the enhancement of the process used to gather and report information on installed product versions and software on SAP technical systems. Such information is critical for lifecycle management activities like maintenance, updates, and upgrades. 

The note outlines that Installed Software Information (ISI) is now improved through the software provisioning manager 1.0 SP2, reducing manual efforts and minimizing errors. ISI is crucial when initiating maintenance transactions, as it helps to calculate the valid stack.xml file necessary for system updates or maintenance through the Maintenance Optimizer.

Key Points:

- Issues with ISI have been known to occur post-installation or during special processes such as using Dual-Stack Split or tools like JSPM, SPAM, or SAINT.
- The Solution Manager plays a vital role in reporting and verifying installed software information via the SLD-LMDB sync or LANDSCAPE FETCH job.
- Software logistics tools and data suppliers are responsible for updating the installed software information.
- New enhancements include the ability of Software Provisioning Manager 1.0 SP2 to write detailed product version information, as well as the Software Update Manager 1.0 SP8's feature to correct ISI; relevant corrections are available in SAP Note 1816146.
- There is a specific procedure to follow if uninstalling an add-on to ensure the correct representation of the product instance in LMDB.
- The note provides instructions on how to find information about installed product versions on the system and in the Solution Manager LMDB.
- An appendix lists product versions (e.g., SAP NetWeaver 7.00-7.01, certain 7.1x based versions, and others) that are exceptions and don't have ISI provided by ppms_data.xml / pv_descriptor.xml files.
- For the product versions not covered, the installed software information needs to be completed through maintenance transactions or using a stack configuration file for accuracy.

In brief, the note covers the importance of accurate Installed Software Information, the tools and methods to obtain it, improvements made to the process, and cases where manual intervention might still be necessary.