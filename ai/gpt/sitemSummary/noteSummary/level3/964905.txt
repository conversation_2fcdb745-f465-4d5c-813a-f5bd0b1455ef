SAP Note 964905 addresses the changes in the concepts and generation of analysis authorizations following an upgrade from SAP BW 3.x to SAP NetWeaver 2004s BI (Version BI 7.0 or higher).

Summary of the SAP Note:

**Symptom:**
- Introduction of new concepts for creating analysis authorizations after upgrading to a higher SAP BW version.

**Other Terms:**
- RSECADMIN (transaction for managing analysis authorizations)
- 0TCA_DS01 (DataStore object used for authorization generation)

**Reason and Prerequisites:**
- The note is relevant for systems that have been upgraded from SAP BW 3.x to SAP NetWeaver 2004s BI or above.

**Solution:**
The note outlines a change in the authorization concept, particularly focusing on the creation of authorizations for reporting. In previous versions (BW 3.x), authorizations were created in transaction RSSM, which included a generation process for DataStore objects with authorization logic. The new approach in NetWeaver 2004s makes the system more restrictive and requires relevance checks for authorizations connected to InfoProviders. 

Key points from the note:
- The new concept checks for InfoObjects 0TCAIPROV, 0TCAACTVT, and 0TCAVALID, which were not always required before.
- Adjustments might be needed when filling DataStore objects—particularly if these InfoObjects are relevant for generating authorizations.
- In many cases, the workaround is to extend existing roles with general authorizations for these three InfoObjects, eliminating the need for individual authorizations.
- The InfoObject or 0TCTAUTH column in the DataStore objects is more significant in the new version, which may need to be populated with a unique name or number.
- The note includes a set of recommended solutions and considerations for scenarios that users might encounter when dealing with the new analysis authorization concept.

The note implies that transitioning to the new authorization concept can be facilitated by using the authorization generation features, rather than performing a full migration of old authorizations. It provides guidance on how to proceed with authorizations generated from DataStore objects, how to handle characteristically relevant but unchecked InfoObjects, and dealing with authorizations containing different characteristics for different InfoProviders. The solutions include both non-change scenarios and necessary adjustment scenarios for authorization objects. 

The note also implies that additional details and instructions will be provided in the recommended actions or as updates when relevant Support Packages are released.