SAP Note 2213891 addresses an issue where the Infoobject 0HIER_VERS is not active after installing SAP NetWeaver 7.40 Support Package 08. The symptoms of this problem include the activation of technical content through transaction RSTCO_ADMIN either running indefinitely or being terminated with a MESSAGE_TYPE_X error in the program "SAPLRSDG_IOBJ_DB_READ" or the function "REGULAR_IOBJ_GET_FOR_META".

The error is caused during the activation of cube 0PPM_VC1, which attempts to check the status of Infoobject 0HIER_VERS but encounters a program error due to a bug.

The solution provided in the SAP Note suggests applying the following Support Packages to resolve the issue:

1. For SAP BW 7.40, import Support Package 14 for SAP BW 7.40 (SAPKW74014), described in more detail in SAP Note 2207824 "Preliminary Version SAPBWNews NW BW 7.4 ABAP SP14".
   
2. For SAP BW 7.50, import Support Package 1 for SAP BW 7.50 (SAPK-75001INSAPBW), with further details available in SAP Note 2192427 "Preliminary Version SAPBWNews NW BW 7.50 ABAP SP1".

Additionally, correction instructions are also provided as an alternative means to resolve the issue. Before using the correction instructions, the note advises checking SAP Note 1668882 concerning transaction SNOTE, as this note might contain relevant information even before the Support Package is released. However, the term "preliminary version" might still be included in the short text at that stage.