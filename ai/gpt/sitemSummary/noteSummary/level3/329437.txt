SAP Note 329437 addresses several functions that are not available or behave differently in the newer transactions for editing purchase orders (ME21N, ME22N, and ME23N) compared to the older versions or other related transactions. The note lists the following points:

1. Changes to master data while Transaction ME21N or ME22N is running are not recognized by the already open transaction. This issue is discussed further in Note 307054.
2. Some administrator functions for managing the table control in the item overview are not fully supported. For more details, refer to Note 120901.
3. The "open target quantity" display that was available in the old ME21 transaction when creating a purchase order with reference to a contract is no longer present in Transaction ME21N.
4. The document overview in ME21N does not support the display of item category and account assignment type, although these are available in transactions for purchase requisitions (ME51N, ME52N, ME53N).
5. In ME21N, it is impossible to mark service items as "free of charge."
6. The functionality to copy only header data from an existing document to a new one is not available in the new transaction ME21N.
7. The feature "Display Linked Documents" or viewing "linked objects" for purchase orders (object type BUS2012) is not available. As a temporary solution, the "Display originals" option can be used to access the optical archive. Note 304537 provides updated information on this topic.
8. Parked invoices are not shown in the new Transaction ME2xN, which is by design; however, the system does differentiate between parked and posted invoices at the item level in the purchase order history.
9. Unlike Transaction ME21, the new Transactions ME2xN do not have a popup for extending the existing purchase order if a new order is created with the same date for the same vendor. This functionality can be configured in the document overview by setting appropriate display parameters.

The reason for these differences is due to the design of the transactions. The note suggests users refer to the related notes for solutions or further information.