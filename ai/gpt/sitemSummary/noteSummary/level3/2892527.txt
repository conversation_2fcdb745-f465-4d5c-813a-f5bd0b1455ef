The SAP Note 2892527 addresses an issue in the Product Compliance functionality where Supplier MDS (Material Data Sheet) records incorrectly link to the compliance data objects of purchased components instead of the correct supplier components after data is downloaded from the International Material Data System (IMDS).

Key information from the note includes:

- **Symptom**: The system erroneously assigns the compliance data object of the purchased component to the supplier MDS records instead of the correct supplier component's compliance data object.

- **Other Terms**: Reference to various technical terms and codes related to the issue, such as EHPRC_CPI04, EHPRC_CPI03, EHPRC_CPO20, and message EHPRC_CPM_BOMBOS 048, which could help users identify the related components and error messages.

- **Reason**: The issue is caused by a programming error within the system.

- **Prerequisites**: Users should refer to the correction instructions linked within or accompanying the note for necessary prerequisites.

- **Solution**: To resolve the issue, users should either implement the corrections provided in the SAP Note or import corresponding Support Packages for the SAP EHS Management component extension or Feature Package Stacks for SAP S/4HANA.

The note also includes instructions for reviewing and adjusting custom implementations due to changes made by the correction, specifically changes to the default exit implementation EHPRC_CP_IM52S_OBJECT_MATCH.

The SAP Note outlines options for correcting the assignment of the supplier MDS to the compliance data object and specification:
1. Manually re-assign the correct specification in IMDS Supplier Center if it was previously assigned manually.
2. Manually re-assign the correct specification if the import MDS was already performed in the IMDS Supplier Center.
3. For other cases, the import will handle the re-assignment to the correct specification the next time it is run.