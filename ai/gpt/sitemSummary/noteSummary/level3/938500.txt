SAP Note 938500 addresses an issue where an error message "TK 502 Request & is not a Workbench or Customizing request" is displayed during Phase II of the business partner conversion process. This error occurs when executing one of the programs RFTBUH05, RFTBUH03, RFTBUH06, or RFTBUH04, and it prevents the process from continuing.

The reason behind the error is that customer-specific DDIC objects are recorded in a workbench request by these programs. The function module RS_CORR_INSERT is responsible for this and provides a request number through the return parameter "KORRNUM" to the next program using the table "BPUM_CTL". However, "KORRNUM" contains the request task number rather than the Workbench request number, leading to the error when the follow-up program is called.

The solution provided involves implementing source code corrections as per the instructions in the note or manually making the corrections using the Note Assistant. The specific steps for the manual workaround include deleting the request number from the "BPUM_CTL" table by using Transaction SE16, navigating to the "BPUM_CTL" table contents, executing the selection screen, choosing the entry with "ASSI_TYP = TR0000", changing it to delete the "TRKORR" field, and saving the changes.

This SAP Note is important for those experiencing issues with business partner conversion due to incorrect request number references impeding program execution. Implementing the solution should resolve the error and allow the conversion process to proceed as expected.