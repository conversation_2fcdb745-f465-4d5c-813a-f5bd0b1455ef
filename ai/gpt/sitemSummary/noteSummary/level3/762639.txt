SAP Note 762639 addresses a problem in the IS-H*MED (Industry Solution for Healthcare - Medical) module, specifically within the planning board functionality. The issue occurs when planning multiple services for a single appointment and moving one of those services to a different appointment in the planning board, which leads to an incorrect data transfer and an incorrect movement reference.

Other Terms: Visit

The note describes that this issue originates from an incorrect code that requires an advanced development solution.

The solution provided in the note involves creating new class methods with specific attributes and parameters in certain classes (CL_ISH_FCT_PLANNING and CL_ISHMED_UTL_BASE). It details the exact steps for creating these methods, including their visibility (protected or public), typing type, including whether the parameters should be pass by value, and the default values for each parameter. 

Additionally, the note instructs the user to unpack a file (HW762639_472.zip) that contains orders, which are to be imported into the user's system as part of the solution implementation. It is important to note that the attachments associated with this SAP Note are available through the SAP Service Marketplace only, as referenced by other SAP Notes (480180 and 13719).

Finally, once the class methods are defined and the source code corrections implemented, users are expected to store and activate the class to complete the process and solve the issue.