SAP Note 1581320 addresses an error with the designation CONVT_NO_NUMBER that occurs when using the Data Transfer Process (DTP) from the data source 0RSL_DS51 to the data target 0RMA_DS01. This error arises during runtime and is specifically linked to the Retail-Method-of-Accounting (RMA) and Stock-Ledger functionalities.

The underlying cause of the error is the presence of non-standard, non-numerical values in the field INVART (which represents the physical inventory type) delivered by the data source 2LIS_03_BF. These custom INVART values may have been introduced through specific Business Add-In (BAdI) implementations.

The solution to this issue has been included in the forthcoming support packages for BI_CONT versions 7.04, 7.05, and 7.35, ensuring that such custom values in the INVART field will be processed correctly by the upload mechanisms into the RMA raw data layer. Users experiencing this error should plan to update their systems with the appropriate support package to resolve the problem.