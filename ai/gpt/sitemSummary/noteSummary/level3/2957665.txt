SAP Note 2957665 addresses the introduction of a fallback mechanism for the SAP GUI HTML Control in SAP GUI for Windows version 7.70, which allows users to switch between using the new default Edge (Chromium-based) and the legacy Internet Explorer for displaying HTML content within SAP systems.

The note explains that with the new SAP GUI for Windows 7.70, users can now use the modern WebView2 browser control (Edge based on Chromium) instead of the traditional Internet Explorer (IE) control to render HTML content. However, recognizing that some HTML content might not yet be compatible with modern browsers like Edge, the note provides a solution to configure exceptions, allowing Internet Explorer to be used for specific SAP systems, transactions, or screens that require it.

Key points and steps included in the note are:

1. The ability to define a set of fallback rules in an XML file format, where one can specify which systems, transactions, or screens should use IE or Edge as browser control.
2. How to set up the fallback rules, including a sample XML structure for the rules file.
3. Deployment of the fallback rules by placing the file on an accessible file share or HTTP(S) server and referencing it via a registry value on client PCs.
4. A recommendation to use SAP GUI for Windows 7.70 patch level 7 or higher with the mentioned ABAP extension to fully utilize this feature.

Furthermore, the note discusses:
- Usage of patterns like "SY*" is not supported for defining rules.
- How to identify the system ID, transaction code, and screen details necessary for creating accurate rules, recommending the use of SAP GUI trace functionality to get this information.
- A warning that the fallback configuration file is specific to users' individual requirements and that it cannot be provided by SAP.

Lastly, changes in the detection of transactions and screens after implementing certain patch levels and ABAP corrections are mentioned, and users are prompted to adjust the fallback rules if necessary after updates, using the diagnostic tracing feature to validate the correct rule parameters.

Overall, this note is designed to guide SAP GUI users through configuring and maintaining the capability to use IE as a fallback option for browsing HTML content, while transitioning to a modern browsing environment with Edge.