SAP Note 2662861 addresses an issue in the revenue accounting area where the transaction price of a performance obligation (POB) is not updated correctly when multiple invoice revenue accounting items (RAIs) associated with that POB are processed. The issue occurs when each individual RAI has an invoiced amount less than the transaction price of the POB, but when taken together, the total of the invoiced amounts exceeds the transaction price. The expected behavior is that the system should update the transaction price with the total invoiced amount when it is greater. This is recognized as a source code bug.

Customers facing this issue are expected to have a POB where the `INVOICE_EFFECT_TYPE` is not set to 'F' (which likely refers to a specific condition or parameter setting within the system). The provided solution is the implementation of SAP Note 2662861 to correct the behaviour. Furthermore, this note is a follow-on to a previously released SAP Note 2579928 which addressed a related issue where the transaction price was not updated when the invoiced amount was higher than the transaction price but did not account for scenarios involving multiple RAIs.

In summary, this note is intended for users experiencing incorrect transaction price updates in the context of revenue accounting when dealing with POBs that have multiple associated RAIs with a total invoiced amount exceeding the POB's initial transaction price. The fix is done through the implementation of this note, which corrects the underlying source code error.