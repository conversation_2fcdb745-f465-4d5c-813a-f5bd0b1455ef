SAP Note 2078642 addresses an issue where the report FAGL_ASSET_MASTERDATA_UPD, which is used for the initial filling of profit center and segment fields in Asset Accounting, results in locking errors (indicated by error code AA003). These errors occur specifically when there are multiple subnumbers for an asset and they are not being processed correctly in parallel.

The reason for this issue is that the different subnumbers located in separate packages are not unlocked simultaneously, causing the locking errors when the report is run.

The solution provided in this SAP Note involves implementing correction instructions that are attached to the note or importing the appropriate Support Package to resolve the issue. After the corrections are applied, the report FAGL_ASSET_MASTERDATA_UPD can be executed again without encountering the AA003 locking errors.