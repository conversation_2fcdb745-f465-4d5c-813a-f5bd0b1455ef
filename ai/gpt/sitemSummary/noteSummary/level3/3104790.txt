SAP Note 3104790 addresses issues related to the SAP S/4HANA Migration Cockpit when using the "Migrate Data Using Staging Tables" approach. The note outlines problems such as the inability to create new projects (with the "create" button being inactive or greyed out), errors when trying to switch to edit mode in the migration object modeler (with messages like "View mode not supported" or "This project cannot be edited in the system"), and general issues regarding modifiable systems.

Key information presented in the note includes:

- **Environment**: The note is relevant for the SAP S/4HANA Migration Cockpit using Staging Tables.

- **Reasons**: Starting with SAP S/4HANA 2020, a distinction between modifiable and non-modifiable systems is necessary for using staging tables.

- **Solution**
  - In **Modifiable Systems**, projects can be created, configured, and executed within the system.
  - In **Non-Modifiable Systems**, projects can only be executed, and cannot be created or configured. Projects need to be created and configured in a modifiable system (like a development environment) and then transferred to non-modifiable systems.
    - For release SAP S/4HANA 2020, this transfer is done via export/import functionality in LTMOM (not applicable to the older LTMC transaction).
    - As of SAP S/4HANA 2021, the transfer can be done using the transport functionality in LTMOM, but projects from SAP S/4HANA 2020 can still be imported in the 2021 FPS0 and FPS01 releases, although it is not recommended to import projects to a system with a different release.

- **Additional Information about Modifiable Systems**: The note provides instructions on how to verify if a system is modifiable, including checking settings in transactions SCC4 and SE06.

- **Additional Information about the Transport Functionality**: The note references a slide deck for more details about transport functionality.

To summarize, SAP Note 3104790 provides guidance on creating and configuring data migration projects in modifiable and non-modifiable systems within the Migration Cockpit for S/4HANA, highlighting necessary system settings and the correct procedures for transferring projects between systems with different releases.