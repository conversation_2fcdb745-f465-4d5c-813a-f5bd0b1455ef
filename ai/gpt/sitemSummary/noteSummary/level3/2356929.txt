SAP Note 2356929 addresses an issue related to the legacy data transfer process for fixed assets in an SAP system. The note provides a summary of two symptoms, the underlying cause, and the recommended solution. Here is a summary of the note:

**Symptoms:**
1. After creating a legacy asset and posting to it without entering the depreciation already posted in the source system, the SAP system erroneously creates planned values for periods that are on or before the "Period in which the depreciation was posted" as defined in Customizing.
2. During the reversal of the legacy data transfer at the end of the fiscal year, the system generates an error message AA 026, which states, "Asset has no values in fiscal year &."

**Relevant Terms:** 
The note mentions several SAP transactions and tools that are relevant to the issue including AB08, AS91, ABLDT, ABZON, AW01N, Asset Explorer, as well as reporting and the asset history sheet.

**Reason:** 
The cause of the problem is identified as a program error within the SAP system.

**Solution:** 
The note recommends the implementation of an attached program correction to resolve the issue. Additionally, for those fixed assets experiencing the AA026 error during reversal, the SAP note provides a correction report named RACORR_INS_YDDA_LDT_EFY, which should be used as instructed in the attachment.

Users experiencing the documented symptoms should apply the provided program corrections and run the correction report to correct the asset values and avoid the AA026 error upon reversal at the fiscal year-end.