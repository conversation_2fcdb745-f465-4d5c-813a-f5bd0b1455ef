SAP Note 2825852 addresses various restrictions specific to the Aerospace and Defense (A&D) industry solution within the SAP S/4HANA 1909 release. Below is a summary of the key points from the note:

1. Product Designer Work Bench (PDN) is not available in SAP S/4HANA 1909, and it is replaced by Product Structure Management (PSM). Users should use the transaction code PPE for A&D functions of iPPE since PSM does not have these features.

2. The transaction code MB11 is replaced by MIGO in SAP S/4HANA. Movement types MT711B and MT712B for Customer Stock load are no longer supported in MIGO. Instead, movement types MT501B and MT502B should be used. However, MT711B and MT712B can still be used in the background of transaction code LI21 for inventory adjustments with Inventory Documents.

3. Group Production Planning (GPD) has several restrictions:
   - Valuated GPD cannot be used in S/4HANA.
   - GPD does not support delivery costs, only requirements from network activities, production orders, sales orders, and planned independent requirements.
   - GPD does not support by-products, negative stock quantities, or the Model-Unit type of Parameter Effectivity (PE). Instead, it is suggested to use an operative Work Breakdown Structure (WBS) for each model unit number.
   - Serial numbers in stock and goods movements are not considered in GPD.
   - GPD does not recognize MRP Areas and plans assuming the plant is one MRP Area.
   - Transfer Borrow Loan Payback (TBLP) process and Production Order Split functionality do not support GPD.
   - Production Planning and Detailed Scheduling (PP/DS) does not support GPD as it has been moved to SAP S/4HANA.

4. In the context of Maintenance, Repair, and Overhaul (MRO) Subcontracting in the A&D sector, SAP S/4HANA does not support Extended Warehouse Management (EWM) users. MRO subcontracting orders do not create an inbound delivery needed as an interface between SAP S/4HANA and EWM. It is recommended to use the core Subcontracting process with transaction code ME2O for EWM users and avoid using the code ADSUBCON for creating subcontracting orders.

Note that the SAP Note is subject to change and should be referred to for the most up-to-date information regarding restrictions.