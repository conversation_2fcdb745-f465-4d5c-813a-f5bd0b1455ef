SAP Note 2755476 addresses the error ME816, which occurs during the migration of "Purchasing Info Records with conditions" to SAP S/4HANA Cloud Editions 1811 or 1902. The error message displayed is "System error (error in method PROCESS_CONDITION)".

To reproduce the error, one must go through the data migration steps in the app 'Manage your solution', specifically targeting the 'Purchasing Info Record with conditions' entry, and upon simulation of creating the info record, the error will occur.

The root cause of the problem is the absence of Pricing table generation. This issue has been resolved starting from release CE1905.

The solution provided requires a user with purchasing authority (e.g., with the role SAP_BR_PURCHASER or access to the business catalog SAP_MM_BC_PC_PROCESS_MC) to log in and simulate the creation of a "Purchasing Info Record" with conditions in the relevant app. Going through the conditions screen prompts the system to generate the missing pricing table in the backend. It should be noted that this is a one-time action required per system ID and release, and it might need to be carried out in the production system for CE1811 and CE1902.

After these steps are completed, the data migration should be able to proceed without encountering the PROCESS_CONDITION error message. If any additional pricing-related errors arise, the user is advised to check the XML file content against the required fields in the template.

Keywords associated with this note include <PERSON><PERSON>RI, COND_MNT_RECORD_PUT, method PROCESS_CONDITION, ME816, and migrate your data.