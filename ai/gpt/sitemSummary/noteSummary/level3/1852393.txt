SAP Note 1852393 addresses a specific issue where a query in SAP NetWeaver Business Warehouse (BW) does not display texts for the query objects. This problem specifically affects queries that are implemented in the Business Add-In (BAdI) RSR_TRANS_QUERY_BADI when the query definition is transferred using an XML string.

The underlying cause of this problem is identified as a program error.

To resolve this issue, the note outlines the following solutions based on different SAP NetWeaver BW versions:

1. For SAP NetWeaver BW 7.30: Import Support Package 10 (specified as SAPKW73010). Details on this Support Package can be found in SAP Note 1810084, entitled "SAPBWNews NW 7.30 BW ABAP SP10."

2. For SAP NetWeaver BW 7.31, also known as BW 7.3 Enhancement Package 1: Import Support Package 09 (specified as SAPKW73109). Additional information on this Support Package is available in SAP Note 1847231, entitled "SAPBWNews NW BW 7.31/7.03 ABAP SP9."

3. For SAP NetWeaver BW 7.40: Import Support Package 03 (specified as SAPKW74003). Further details on this Support Package can be read in SAP Note 1818593, entitled "SAPBWNews NW BW 7.4 ABAP SP03."

For urgent cases, the SAP Note suggests that the correction instructions can be implemented as an advance correction. It also advises users to refer to SAP Note 1668882 for information on transaction SNOTE, which is used for applying SAP Notes.

The note indicates that in some instances, the mentioned notes may be available in a preliminary version before the Support Package is released. If so, the short text of the note would include the words "Preliminary version."