SAP Note 2026423 addresses a specific issue that occurs when loading data from DataStore Object (DSO) /IMO/FIAR_030 to /IMO/FIAR_D30 using a Data Transfer Process (DTP) within an SAP HANA-optimized BI environment. The issue involves an error in the End Routine of a transformation, which happens if the source DSO /IMO/FIAR_030 contains no customer line items with the Item Status "cleared" (denoted as 0FI_DOCSTAT = 'C').

Key points of this note include:
- The error is due to a program error in the End Routine of the transformation TRCS /IMO/FIAR_IS30 to ODSO /IMO/FIAR_D30.
- This error is remedied in the following SAP NetWeaver BI Content Support Packages:
  - 7.40 BI Content 7.57 SP 03
  - 7.40 BI Content 7.47 SP 10
  - 7.31 BI Content 7.47 SP 10
  - 7.30 BI Content 7.37 SP 10

If users need a fix before they can implement the above support packages, the note provides detailed instructions on how to manually update the code in the End Routine. The note includes both the old coding (to be replaced) and the new coding that should be used to correct the error. The correction involves checking the return code after selecting distinct entries from the source DSO and handling the situation when no entries with the cleared status are found.

To summarize, SAP Note 2026423 outlines the symptoms, reason, and solution for an error occurring in the SAP HANA-optimized BI Content during data transfer from one DSO to another when a certain condition is not met in the source data. The note provides instructions for a manual fix and references to support packages that will resolve the issue.