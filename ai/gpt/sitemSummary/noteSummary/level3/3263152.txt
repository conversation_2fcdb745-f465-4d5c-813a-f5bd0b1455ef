SAP Note 3263152 addresses an issue that occurs after migrating open sales orders using the "SD - Sales order (only open SO)" object in the SAP S/4HANA Migration Cockpit. The issue described is that the decimal values in the "Amount" field on the "Item Conditions" tab have their decimal places shifted. For instance, a value like 455.64 might incorrectly appear as 4556.4 after migration.

The cause of the problem is identified as the absence of a value in the "Condition unit (currency or percentage)" field in the "Item Conditions" sheet during the migration process.

To resolve the issue, it is essential to enter the correct currency value in the "Condition unit (currency or percentage)" field. This field will determine the interpretation of the amount as either a currency or a percentage. For percentage conditions, the field should be left empty or filled with a "%". For any other condition, the appropriate currency should be entered.

Additional instructions and information regarding the migration object "SD - Sales order (only open SO)" can be found in the SAP Help documentation linked in the note.

Keywords that pertain to this note include: decimal, shift, currency, percentage, sales order, LTMC (Legacy Transfer Migration Cockpit), migration cockpit, object, SD (Sales and Distribution), amount, VA03 (the transaction code for Display Sales Order), and switch.