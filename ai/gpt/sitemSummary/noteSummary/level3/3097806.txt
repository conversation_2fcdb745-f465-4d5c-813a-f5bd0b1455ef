SAP Note 3097806 addresses an issue encountered within the Product Compliance functionality when importing Material Data Sheets (MDS) from the International Material Data System (IMDS). Specifically, the problem arises when users initially import MDS from IMDS without assigning a supplier component, and subsequently create a supplier component in their system (e.g., via BOM Transfer). Upon re-importing at the MDS record in IMDS Supplier Center, the system errantly generates an additional supplier component instead of updating the existing one.

The root cause of this duplication issue is identified as a program error. To rectify this error, users are advised to implement the SAP Note or import the relevant support packages for either the SAP EHS Management component extension or the Feature Package Stacks for SAP S/4HANA.

The proposed solution enhances the import functionality to attempt to identify the compliance data object of the component using various identifiers including the IMDS Node ID, material, supplier, and manufacturer part number (MPN). If the component cannot be uniquely identified, the import process will stop, prompting users to manually assign the correct supplier component before restarting the import. The solution also includes messages to inform users when no compliance data object or when multiple objects are found.

A notable inclusion in the process is that BOM Transfer will now prioritize any component recognized by the IMDS import, and any superfluous components will be eliminated from the product structure to ensure they do not affect the compliance analysis.

Users who have customized the exit function EHPRC_CP_IM52S_OBJECT_MATCH are advised to check and adjust it according to the correction instructions provided in this SAP Note to ensure proper function post-implementation.

Overall, the note provides a resolution for the duplication issue, ensuring that only one correct supplier component remains within the product structure after an IMDS import.