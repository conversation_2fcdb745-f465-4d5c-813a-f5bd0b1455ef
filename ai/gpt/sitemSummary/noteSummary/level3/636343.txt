SAP Note 636343 addresses an issue occurring in the transaction codes ME51N, ME52N, and ME53N, which are used for creating, changing, and displaying purchase requisitions respectively. The specific problem is that when attempting to sort the delivery date column within these transactions, the sort result is incorrect and does not display the delivery dates in the proper ascending or descending order.

The error is attributed to a program fault and occurs when the 'Item overview as grid control' option is not used. This indicates that the issue is related to the particular method in which the item overview is displayed and sorted within the transactions.

The solution provided involves applying a correction as per given instructions. It is important to note that the correction only applies to the 'table control' and is not applicable to the 'grid control' due to technical limitations. To avoid the issue and implement the solution, users should navigate to the personal settings area of the affected transaction (ME51N, ME52N, or ME53N) and deselect the 'Item Overview as grid control' option, allowing the system to revert to the table control method where the sort function will work correctly.