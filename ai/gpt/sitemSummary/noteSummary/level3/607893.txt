SAP Note 607893 addresses the handling of user exits within transaction SPAU which is used for adjusting custom enhancements after an SAP system upgrade. 

The note begins by explaining the issue where user exits appear in transaction SPAU for adjustment, which is the case despite being an enhancement method. It clarifies that user exits, different from those created with SMOD and CMOD transactions, are considered obsolete. In the past, user exits were delivered once and not overwritten during an upgrade. 

However, with the change in the upgrade procedure starting from Basis Release 6.x (including R/3 Enterprise 4.70), all SAP source code is replaced during an upgrade, including user exits modified by customers. Consequently, transaction SPAU will list all user exits that have been changed by the customer.

To maintain customer enhancements, one must adjust the user exits by selecting the appropriate traffic light signal in SPAU. If an object is indicated by a red traffic light, it is advisable to convert the modification using the Modification Assistant. The note outlines steps for this process:

1. Note down the user exit name.
2. Select "Reset to original" in SPAU.
3. Open the object in an editor using transaction SE38 or SE80.
4. Re-implement the enhancements with the help of the Modification Assistant by copying and pasting the old source code from version management.

The note assures that although SPAU will continue to display these objects in future upgrades, they can easily be adjusted by selecting the green traffic light.

To summarize, SAP Note 607893 provides guidance on how to handle obsolete user exits that are affected by SAP system upgrades, ensuring that customer-specific enhancements are retained efficiently with the help of the Modification Assistant and traffic light system in transaction SPAU.