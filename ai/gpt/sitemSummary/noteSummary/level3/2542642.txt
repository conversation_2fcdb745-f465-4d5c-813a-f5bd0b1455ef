SAP Note 2542642 addresses an issue with the program "Calculate Liabilities and Assets" under the fixed exchange rate method where the calculated local amount (in local currency) for liabilities and assets is inaccurate, despite the transactional currency amount being correct. This inaccuracy leads to an imbalance in the receivable adjustment account.

The note specifies that under the fixed exchange rate method, the program is supposed to follow a series of steps to calculate the local amount correctly, which involves:

1. Calculating total transactional liabilities and assets for a single contract.
2. Retrieving the calculated transactional figures from the Posting Table.
3. Computing the difference between the figures in step 1 and step 2.
4. Converting the difference into local amount using the fixed exchange rate specified on the contract header.
5. Rounding the results from steps 3 and 4.
6. Updating the Posting Table with the rounded results.

The cause of the issue is identified as a program error with no prerequisites mentioned. The resolution suggested in the note is simply to apply the note itself to correct the issue, presumably by providing either a patch, instructions, or both to fix the program error that resulted in the inaccurate calculations.