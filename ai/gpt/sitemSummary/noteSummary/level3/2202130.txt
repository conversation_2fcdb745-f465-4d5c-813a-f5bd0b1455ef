SAP Note 2202130 addresses a specific issue concerning the data extractor 2LIS_03_BF for Inventory Management in SAP systems. The core problem is a discrepancy between the quantity field (MENGE) in the extractor when run in transaction RSA3 and the corresponding MENGE in Table MSEG.

**Summary of the SAP Note:**

- **Symptom:** During the execution of RSA3 for 2LIS_03_BF, the MENGE field does not match the MENGE in Table MSEG.
  
- **Environment:** The issue is relevant to various SAP environments, including SAP R/3, SAP R/3 Enterprise, SAP ERP, SAP ECC, SAP enhancement packages for SAP ERP (including HANA version), and SAP S/4HANA.

- **Reproducing the Issue:** The mismatch occurs during the setup of the table using transaction OLI1BW, where the MENGE quantities are not being populated for records that have a blank MENGU in MSEG.

- **Cause:** The issue is due to the system design. When the specified forms and functions are executed during the setup process, they check if the MENGU field is blank. If it is and if certain conditions are met (specifically BSTAUS and BSTTYP not equalling 'V'), the MENGE field is intentionally set to zero. This behavior is meant to prevent certain stock movements (which do not count as usages) from inadvertently changing stock figures in the BW system.

- **Resolution:** The note explains that movements with BSTAUS and BSTTYP equal to 'V' are considered usages only and should not affect stock. When MENGU in MSEG is not filled, the movement is not intended to alter stock figures. Thus, the MENGE field is cleared by the code when BSTAUS and BSTTYP do not equal 'V' to avoid influencing stock figures in BW.

- **Additional Information:** If there is no material number in the material document (MSEG-MATNR is blank), then MENGE is determined by MSEG-ERFMG (Quantity in the unit of entry).

**Application of This SAP Note:**
Organizations using SAP for Inventory Management should take note of this explanation when they encounter zero quantities in the 2LIS_03_BF extractor, which are different from the quantities shown in the MSEG table. The note clarifies that this behavior is as designed and not an error. Understanding this note would ensure that users do not misinterpret these zero values as a data extraction error, when in fact, it is a deliberate mechanism to ensure accurate stock figures.