SAP Note 2373204 addresses various issues encountered by users who have implemented the SAP BW/4HANA Starter Add-On. The problems users might face include:

- Rejection of transport deletion when the TLOGO object is unsupported.
- Inability to add a TLOGO object to the whitelist when subtypes of TLOGO are not allowed.
- Failure in creating a virtual provider with a function module.

The note identifies the cause of these issues as programming errors.

To resolve these issues, the note suggests the following solutions based on the SAP BW version:

- For SAP BW 7.50, users should implement Support Package 6 (SAPK-75006INSAPBW). Further details will be available in SAP Note 2346340, titled "SAPBWNews 7.50 BW ABAP SP6", upon its release for customers.
- For SAP BW 7.51, users need to implement Support Package 1 (SAPK-75101INSAPBW). SAP Note 2345927, with the short text "SAPBWNews 7.51 BW ABAP SP1", will provide more information once released to customers.

For urgent cases, the note advises users to follow the correction instructions provided. Before applying these corrections, users should consult SAP Notes 1668882 and 2248091 related to transaction SNOTE. It is also noted that this particular SAP Note may be available before the release of the respective Support Package, in which case the text may still indicate it as a "preliminary version".