SAP Note 1852068 addresses an issue about a program error with VirtualProviders in a HANA model when they are used within a MultiProvider in SAP NetWeaver Business Warehouse (BW). The error results in the inability to read data for input parameters, leading to filters being evaluated incorrectly.

The note gives solution instructions for different SAP NetWeaver BW versions:

- For SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010). Further details can be found in SAP Note 1810084, titled "SAPBWNews NW 7.30 BW ABAP SP10".
- For SAP NetWeaver BW 7.31: Import Support Package 9 (SAPKW73109). Additional information is provided in SAP Note 1847231, "SAPBWNews NW BW 7.31/7.03 ABAP SP9".
- For SAP NetWeaver BW 7.40: Import Support Package 3 (SAPKW74003). This is further explained in SAP Note 1818593, "SAPBWNews NW BW 7.4 ABAP SP03".

The note also suggests that in urgent situations, correction instructions are available as an advance correction. It is recommended to read SAP Note 1668882, which contains information about using transaction SNOTE, before implementing any advance corrections.

Lastly, it mentions that preliminary versions of the SAP Notes may be available before the official release of the Support Package, with the short text of the SAP Note containing the words "Preliminary version".