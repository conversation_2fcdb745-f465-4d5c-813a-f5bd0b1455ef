SAP Note 2441826 addresses the approach for migrating Web Service source systems when transitioning from SAP BW to either SAP BW/4HANA or SAP Datasphere (formerly SAP Data Warehouse Cloud, including the SAP BW bridge).

Key points of the note include:

- In SAP BW/4HANA, source systems of type "Web Service" are not available. The "Write Interface" for DataStore Objects (ADSOs) is introduced as a new capability to replace the push capability of PSA tables of DataSources of Web Service Source Systems. Data can be pushed into the inbound queue tables of Staging and Standard ADSOs.
- To integrate with SAP BW/4HANA 2.0 using this new capability, integration platforms like SAP Cloud Platform Integration or SAP NetWeaver Process Integration (version 7.50 SP15 or later) can use an HTTP (REST) interface for data.
- No full automated conversion path exists from DataSources of Web Service Source Systems in SAP BW to ADSOs with Write Interface in SAP BW/4HANA 2.0 or SAP Datasphere, SAP BW bridge. Manual and semi-automated approaches are recommended for migration:
  - Manually, create a new ADSO in SAP BW using the existing Web Service DataSource as a template; integrate it into the data flow, replicating any necessary transformation logic.
  - Use the task list SAP_BW4_TRANSFER_INPLACE or SAP_BW4_TRANSFER_REMOTE_PREPARE/SHELL to create an ADSO for the Web Service DataSource.
  - After conversion, the new ADSO needs to have the "Write Interface enabled" property active. Template URIs for data push will be available in the properties section of the ADSO editor in the BW Modeling Tools.
- For SAP BW/4HANA 1.0, alternative methods such as the SAP HANA Smart Data Integration (SDI) OData or SOAP Adapters are recommended to integrate with web services.
- For SAP Datasphere, SAP BW bridge, similar steps apply regarding the setup, use of task lists, and enabling of the Write Interface for DataStore Objects.
- The concept of writing into an open Request, maintained by the BW System automatically, no longer exists. Instead, there are two supported loading scenarios:
  - "One-step" where each data call results in a RSPM Request, internally managed by the BW system. This scenario is limited in throughput and parallel loading capacity.
  - "Write into Request" where the external system explicitly administers the RSPM Request and sends data packages into it. The external system is responsible for opening and closing the RSPM Request. This enables parallel loading of data packages.

The note provides links to relevant documentation and emphasizes the need for users to understand the implications of migrating Web Service source systems and to consider the approaches suggested for maintaining data integration capabilities in newer SAP environments.