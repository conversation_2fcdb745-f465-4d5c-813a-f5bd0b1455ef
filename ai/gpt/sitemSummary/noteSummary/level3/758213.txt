The SAP Note 758213 addresses an issue specific to the healthcare industry solutions IS-H (Industry Solution Healthcare) and IS-H*MED, where certain critical information is either incorrectly printed or not printed at all in the "Detailed print" function of a clinical order. The symptoms noted are:

1. When a short anamnesis is entered with a long text, along with a question with long text or a free-text diagnosis with long text, and an encoded diagnosis under the "General Medical Data" tab, the resulting printout of the clinical order details is incomplete:

   - The short anamnesis entries are incorrectly printed as the answer to the "Question."
   - For diagnosis inputted as free text with an accompanying long text, both the long text and short text are printed when only the long text should appear.
   - The encoded diagnosis does not get printed at all.

This issue is identified as a program error. 

Other terms related to this note include ISH_CLINICAL_ORDER_DETAILS and Smartforms.

The solution provided in the note offers a corrected version of the standard form used for the detailed printout of a Clinical Order. This update is applicable as of Release 4.72/AOP002. Additionally, the note also includes enhancements to print output for provisional insurance relationships.

For implementation, the note instructs users to download a ZIP file (HW758213_472.zip) containing the correction, which is not available through OSS but can be accessed via the SAP Service Marketplace. After downloading, users should unpack the file and implement the contents into their SAP system. The note also points to SAP Note 480180 and SAP Note 13719 for guidance on implementing attachments.

In summary, SAP Note 758213 provides a correction to a print output error for clinical orders in SAP's Industry Solution Healthcare, specifying the problem, its cause, and detailed instructions on how to implement the fix.