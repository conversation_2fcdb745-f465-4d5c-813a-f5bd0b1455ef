SAP Note 760646 addresses an issue with the design of a tachometer chart in SEM (Strategic Enterprise Management) versions 3.1A/B, 3.2, and 3.5. The note provides a solution that involves using a new tool provided with SAPGUI Release 4.6D Compilation 3 for the automatic application of Support Packages.

Key Points from the Note:

1. **Symptom**: Users experience a termination error when attempting to design a tachometer chart in the specified SEM versions.

2. **Other Terms**: The note mentions terms like SAPGUI, frontend, installation, SEMOCX, and Balanced Scorecard that are relevant to the issue and its resolution.

3. **Reason and Prerequisites**: The issue can occur if the system has at least SAPGUI 4.6D Compilation 3. It is downward-compatible for a range of SEM versions, starting from SEM 2.0B up to SEM 3.5.

4. **Solution**:
   - The SAPGUI installation now includes a tool that automates the import of Support Packages and updates the installation database on the server.
   - For certain SEM versions (like SEM 3.1A from SP4 and setup46D from SP5), manual unpacking of Support Packages is no longer an option (refer to Note 361222 for more details).
   - The installation process involves preparing an installation server, downloading the necessary files, and using the SAPSetup tool and SapAdmin program to import and install the Support Package.
   - Clients can recognize when a new Support Package is available and update automatically.
   - For stand-alone computers without an installation server, a setup-update is available for installing new Support Packages.
   - The note emphasizes the necessity of the SAP Chart OCX component for displaying graphic elements in Balanced Scorecard and Management Cockpit, and recommends installing the most current version.

The note provides a detailed step-by-step procedure for both server-based and stand-alone installations, ensuring that the SEM components are updated with the requisite front-end Support Package for proper functionality.