SAP Note 323030 addresses the absence of official archiving for Funds Management in versions 4.5A to 4.6B of the SAP system. It informs that the first official release of Funds Management archiving is available in release 4.6C and in the IS-PS add-on release 461. The note lists the following archiving objects introduced in these versions:

1. FM_DOC_FI: For archiving FI line items in Funds Management.
2. FM_DOC_OI: For archiving commitments in Funds Management.
3. FM_DOC_CO: For archiving CO line items in Funds Management.
4. FM_BUDHIE: For archiving Budget Hierarchy Documents in Funds Management.

Additionally, transport files K102889.P4D and R102889.P4D include other archiving objects:

1. FM_ACTSUM: For archiving Commitments/Actuals Totals Records in FM.
2. FM_BUDSUM: For archiving Budget Totals Records in Funds Management.

These transport files also include predefined "Archive Info Structures" for the Archiving Information System (SAP AS), which is accessible via transaction code SARI. The note emphasizes that the archiving is provided in both German and English and originated from the IS-PS release 461A which had a distinct archiving object named FM_BUDENT for Budget Entry Documents not used in standard releases and should be ignored.

For productive archiving, the note recommends using transaction code SARA or other specific transaction codes related to the various archiving objects:

- FMAR_FI
- FMAR_OI
- FMAR_CO
- FMAR_BH
- FMAR_AT
- FMAR_BT

To implement complete FM archiving in standard releases 4.5A-4.6C, users are directed to apply the transport files from SAPSERV3 located in the folder general/R3server/abap/note.0323030. Furthermore, users should refer to SAP Note 0013719 titled "Preliminary transports to customers" for additional details. For release 4.5B, FM archiving is delivered via Hotpackage SAPKH45B29.

The note also provides a list of other terms or transaction/report codes related to FM archiving, presumably for the user's reference. These are likely report names or transaction codes that users can execute in relation to FM archiving.