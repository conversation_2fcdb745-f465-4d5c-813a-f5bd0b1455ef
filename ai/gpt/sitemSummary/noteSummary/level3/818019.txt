SAP Note 818019 addresses a specific update for the country version Austria (AT) in the context of the scoring process in the healthcare solution IS-H (Industry-Specific Component for Healthcare).

**Symptom:**
Previously in Austria's IS-H implementation, the master file EW.DAT, which lists error and warning codes for scoring purposes, only contained codes that were marked as "acceptable." This meant any error messages that arose in the scoring process were automatically accepted.

**Other Terms:**
- Scoring
- EW.DAT
- Acceptable error/warning codes

**Reason and Prerequisites:**
A legal change necessitated updates to the system's handling of acceptable error and warning codes.

**Solution:**
The master file EW.DAT now specifies for each error/warning code if it is "acceptable." During the scoring process, the system will only accept codes that are identified as such in record type 7. Codes without the "acceptable" flag will no longer be allowed through.
Users can use the report RNWAT_SCO_MSG_LOAD to import the updated file.

It is important to note that the changes detailed in this SAP Note should not be implemented via the Note Assistant, as not all modifications have been included in the corrective measure or correction instructions attached to the note. Instead, the updates are available only through an attached file named HW818019_472.ZIP. This file pertains to IS-H Version 4.72 AOP 01 - 07 and should be unpacked and imported according to the instructions in the Note.

Additionally, the attached files are not downloadable via OSS (Online Service System), but only through the SAP Service Marketplace. The note also references SAP Notes 480180 and 13719 for guidelines on importing attachments.

If users are unable to timely import patch 08 for Release 4.72 or need the changes immediately, they must follow the specified sequence of steps to implement the change manually:

1. Unpack the file HW818019_472.ZIP specific to their IS-H Version.
2. Import the unpacked requests into the system.

The note concludes by explaining that these steps are necessary due to the inability of the Note Assistant to implement all the changes required by the legal adjustment in Austria.