SAP Note 1972227 addresses a specific error that occurs in an SAP BW (Business Warehouse) system, which is a COMPUTE_BCD_OVERFLOW dump, resulting in an arithmetic overflow exception (CX_SY_ARITHMETIC_OVERFLOW). This issue happens when the specified report (/SSA/CAT) is run during SAP Going Live Support or SAP CQC (Continuous Quality Checks) Going Live Support. The cause of the problem is identified as a computation where the result is too large to fit into the target field, and the system doesn't have proper exception handling to catch the overflow.

To resolve this problem, the SAP Note advises users to implement the corrections attached to the note. It is necessary first to apply ST-A/PI 01Q, ST-A/PI 01Q SP01, or ST-A/PI 01Q SP02. Additionally, when available, ST-A/PI 01R could also be implemented. Furthermore, the note also recommends ensuring that another related SAP Note, 1886979, which deals with overflow issues when reading BW process chain data, is implemented to avoid similar problems.