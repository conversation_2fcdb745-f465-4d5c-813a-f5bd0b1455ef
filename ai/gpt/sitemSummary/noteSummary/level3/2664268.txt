SAP Note 2664268 introduces a new communication feature for the Service Data Control Center (SDCCN) to interact with the SAP Support Backbone using new destinations. This update becomes necessary due to changes in the SAP Support Backbone infrastructure, and it affects systems using the transaction SDCCN to communicate directly with SAP, rather than through a Solution Manager or FRUN system.

Key points from the SAP Note:

- The feature is available with Support Packages ST-PI 740 SP09 or ST-PI 2008_1_7xx SP19.
- SAP recommends upgrading to ST-PI 740 SP10 or ST-PI 2008_1_7xx SP20 to avoid manual configuration and popup messages when entering SDCCN for the first time.
- On systems with ST-PI 2008_1_7xx SP19 and kernel version equal to or higher than 742, an upgrade to ST-PI 740 or an update to SP20 is recommended. Otherwise, a specific SAP Note (2735923) can be applied.
- It is advised not to create destinations SAP-SUPPORT_PORTAL or SAP-SUPPORT_PARCELBOX for systems with ST-PI 2008_1_7xx, as these require at least an SAP kernel 742.
- The note provides solutions for systems that need to update to the new communication channel to continue using SDCCN effectively, including creating new HTTP destinations (types G and H) and ensuring that the RFC SAPOSS is operational.
- Steps are given on how to manage the new destinations within the SDCCN RFC destinations table, including the migration process from the old SDCC_OSS RFC and how to handle tasks that were previously directed to SDCC_OSS or SAPOSS.
- A workaround is also provided to temporarily switch off the use of the new channel by setting a custom field SDCCN_NEW_CONN to 'OFF' in case the new destinations are not yet ready.

The note also addresses some common issues, such as migration problems related to authorization or settings of the S-user used in the HTTP destinations. If problems arise, SAP suggests creating a support ticket in the component SV-SMG-SDD.

For more details on the update of the SAP Support Backbone and the impact on SAP Solution Manager and Focused Run, links to the respective landing pages are provided in the note.