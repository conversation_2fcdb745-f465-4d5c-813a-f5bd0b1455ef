SAP Note 30318 addresses an issue where changing the currency relationship in table TCURR leads to incorrect valuation in Sales and Distribution (SD) documents. This also affects the Sales Information System (SIS) key figures and open credit values.

The note provides a caution that currency relations in table TCURR should not be altered, even when new entries are made for currency conversions because this can result in erroneous document valuations.

To resolve this issue, incorrect values can be corrected using specific correction programs or reorganization programs, but only after ensuring that table TCURR has been corrected to perform all currency translations from one currency to another using a single currency relation.

The solution outlined involves the following:

1. Correction program for SD documents (SDKURS01):
   - This program should be ran with caution, and the considerations in Note 143244 must be taken into account before running it.
   - It updates document segments by reading all sales and billing documents, determining the correct exchange rates, and performing price determination to correct the value fields in the documents.
   - It is important to note that manually set exchange rates will be lost when the program is run.
   - Exchange rate determination is either made with the creation date of the document or with the pricing date.
   - A selection of parameters for running this program are provided, including the range of dates from the first to the last error, and whether to include sales documents, billing documents, and logs. Additionally, test runs can be performed where recalculated exchange rates are not saved back to the database.

2. Reorganization of credit values (RVKRED77 for version 3.0 or RVKR2277 for version 2.2).
   
3. Reorganization of the Sales Information System.

Lastly, the note suggests these programs should ideally be executed in the background due to long runtimes, and it is wise to first test them in a test system.