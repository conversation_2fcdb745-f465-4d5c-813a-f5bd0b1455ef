SAP Note 69800 addresses the error message FF758 "No tax was determined," which can occur during the credit-side settlement accounting of an arrangement in the subsequent settlement process within purchasing.

Symptom:
Users face an error FF758 stating "No tax was determined" while carrying out the credit-side settlement accounting.

Other Terms:
This issue impacts subsequent settlement transactions in SAP, particularly for purchasing. The relevant transactions mentioned are MEB4, MEB2, MEU2, and the report RWMBON01.

Reason and Prerequisites:
The error appears when the General Ledger (G/L) accounts involved in the subsequent settlement process are not set up correctly. Specifically, the concerned G/L accounts must be marked as taxable and configured to allow posting for the tax code specific to the vendor's business volumes. The error might also be caused by incorrect settings in the "Tax category" field for a G/L account responsible for the subsequent settlement, where the field is either not maintained or is not set to allow posting with the required tax code.

Solution:
The solution involves several configuration steps:

1. First, the user needs to locate the relevant accounts for subsequent settlement through transaction OMR0 with appropriate account assignments for BO2 (Subsequent Settlement of Revenues) or BO1 (Subsequent Settlement of Provisions), the latter being applicable if provisions for accrued income are used.

2. Secondly, in Financial Accounting, the user should maintain the correct settings for the G/L account using transaction FS02. This includes entering "-" in the tax category field to indicate that only transactions subject to input tax are allowed and that postings can be made with any tax code that is recognized as input tax. Additionally, the user must tick the "Posting without taxes allowed" indicator in the second screen of the transaction.

By taking these measures, organizations can correct the error and carry out the settlement process successfully.