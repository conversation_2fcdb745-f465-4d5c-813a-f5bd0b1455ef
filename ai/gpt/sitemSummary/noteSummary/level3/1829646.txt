SAP Note 1829646 addresses a termination error that occurs in SAP NetWeaver Business Warehouse (BW) when running a query. The error message displayed is "System error in program CL_RSR and form GET_COB_PRO-05-". The issue affects queries defined on an InfoProvider that is based on a "Search and Analytics Model" (Operational Data Provisioning or ODP). The problem specifically arises when one of the characteristics has a compound attribute associated with an attribute ODP and a text ODP and when the key fields involved have different field names.

The underlying issue is identified as a program error and occurs only under the mentioned conditions.

To resolve this error, the following solutions are provided:

1. For SAP NetWeaver BW 7.30: Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010) into the BW system. Further details about this Support Package are described in SAP Note 1810084, which is titled "SAPBWNews NW 7.30 BW ABAP SP10" and should be read once released to customers.

2. For SAP NetWeaver BW 7.31 (also known as SAP NW BW 7.0 Enhancement Package 3): Import Support Package 08 for SAP NetWeaver BW 7.31 (SAPKW73108). More information on this Support Package is provided in SAP Note 1813987, "SAPBWNews NW BW 7.31/7.03 ABAP SP8".

3. For SAP NetWeaver BW 7.40: Import Support Package 03 for SAP NetWeaver BW 7.40 (SAPKW74003). For more detailed information, customers should refer to SAP Note 1818593, "SAPBWNews NW BW 7.4 ABAP SP03".

In cases where a solution is urgently required and the Support Package is not yet released, the note advises the implementation of correction instructions as an advance correction. Before proceeding with this approach, users must read SAP Note 875986 for important information about using transaction SNOTE, which is used for implementing SAP Notes.

The note also suggests that before the release of the Support Package, the mentioned SAP Notes might be available in a preliminary version, indicated by the words "Preliminary version" in the short text of the SAP Note.