SAP Note 16513 addresses the issue of a full file system in an AS ABAP environment, which can happen if unnecessary files accumulate or the file system is too small. The note provides guidance on which files can be safely deleted to free up space. The following points summarize the recommended actions:

1. Core files located in the work directory (specifically on Unix systems) can be deleted anytime as they are remnants of previous program terminations.
   
2. Spool and log files such as client-specific spool requests, job logs, and batch input logs should be regularly deleted. Reports RSPO0041, RSBTCDEL, and RSBDCREO can assist with selective deletion, and some related notes offer further guidance.

3. Old archiving files can be removed from the global directory if they're no longer needed for production and have been backed up.

4. ABAP trace files named AT<instance_number>nnnn in the data directory can be deleted using transaction SE30, keeping the system parameter abap/atrasizequota in mind.

5. While the system is not running, page files (PAGFILnn) and roll files (ROLLFLnn) in the data directory can be deleted, but only if they've grown unnecessary large due to special activities.

6. Output requests that are stored as SP* files in the data directory are usually auto-deleted post-output; Note 11070 includes details.

7. SORT and EXTRACT command temporary files (SAP recommends checking that these files are old before deletion) can also be removed, and the associated profile parameters can be adjusted to direct these files to other storage locations.

8. As an emergency measure in case the R/3 System cannot start, job logs can be deleted using OS utilities, and RSBTCDEL run in FORCED mode to clear them.

9. Miscellaneous files such as overly large "stat" files and very large trace files due to an increased trace level parameter should be examined and managed appropriately.

The note also references other SAP Notes to consider for more detailed information on handling specific files and adjusting system parameters to avoid future full file system scenarios. The appropriate system parameters, directories, and filenames are specified for both Unix and Windows NT environments.