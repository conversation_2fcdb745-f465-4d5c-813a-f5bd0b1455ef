SAP Note 1680404 addresses an issue encountered when using the report FAGL_ASSET_MASTERDATA_UPD, which is designed to populate asset master records with segment reporting information. Users experience a problem where fixed assets related to an investment measure cannot be modified as they are incorrectly displayed.

Key information from this SAP Note includes:

- Symptom: Users running the report FAGL_ASSET_MASTERDATA_UPD find they cannot edit fixed assets under construction associated with an investment measure, as these assets are shown erroneously.

- Other Terms: The issue involves fields ANLA-XINVM (indicator for assets under construction belonging to investment measure), PRCTR (profit center), and SEGMENT (segment for segmental reporting).

- Reason and Prerequisites: The cause of the issue is identified as a program error that specifically affects assets under construction associated with investment measures.

- Solution: The SAP Note prescribes that users implement the corrections detailed in the attached correction instructions to resolve the issue.

In summary, this SAP Note is advising on a fix for an identified program error that affects the editing of fixed assets under construction linked to investment measures within the asset master data, and it provides instructions for implementing the required corrections.