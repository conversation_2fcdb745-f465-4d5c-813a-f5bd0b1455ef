SAP Note 2369934 addresses the limitations in SAP S/4HANA regarding the Consumer and Mortgage Loans (CML) functions related to collaterals and collateral objects, which are not available in the SAP S/4HANA system (including SAP Simple Finance editions). 

Key points of the note:
- CML-specific functions for managing collaterals and collateral objects used in earlier versions (such as SAP ECC) are not supported in SAP S/4HANA.
- Transactions for object processing (FNO1, FNO2, FNO3), collateral value calculation (FN61, FN62, FN63), and collateral operations (FNO5, FNO6, FNO7) should not be used in SAP S/4HANA.
- Any customizing transactions and settings related to CML-specific functions are also not to be used in the SAP S/4HANA context.
- The recommended solution is to transition to the FS-CMS (Collateral Management System) application functions before migrating to SAP S/4HANA.
- The transition to FS-CMS should include the implementation (configuration and customizing) of FS-CMS and the data migration from FS-CML to FS-CMS.

The note is important for customers in the process of system conversion to SAP S/4HANA, who were previously using CML functions related to collaterals. They need to plan the transition to FS-CMS in advance of their system conversion to SAP S/4HANA to ensure continued management of collaterals.