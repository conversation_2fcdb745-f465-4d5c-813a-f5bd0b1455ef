SAP Note 2009237 addresses an issue where two InfoObjects, 0DEB_CRED and 0DEL_H_CNT, are incorrectly populated during the data transformation from Infosource /IMO/SD_IS20 into DSO /IMO/SD_D20 when DOC_CATEG is not equal to 'T'. Specifically, 0DEB_CRED is incorrectly filled with 'C' instead of 'D' and 0DEL_H_CNT is incorrectly filled with -1 when it should be 1.

The note identifies that the cause of this error is a coding mistake in the start routine of the transformation, where the DATA declaration of gv_credit should have been gv_credit(1) instead of gv_credit(2) with TYPE C and VALUE 'T'.

To rectify this issue, the note provides the associated support packages that contain the fix for this error:
- SAP NetWeaver 7.40 BI Content 7.57 SP 02
- SAP NetWeaver 7.40 BI Content 7.47 SP 09
- SAP NetWeaver 7.31 BI Content 7.47 SP 09
- SAP NetWeaver 7.30 BI Content 7.37 SP 09

For those requiring an immediate solution before the release of the mentioned support packages, the note advises manually updating the coding in the transformation routine for 0DEB_CRED and 0DEL_H_CNT as follows:

Change:
```
DATA: gv_credit(2) TYPE C VALUE 'T'.
```
to:
```
DATA: gv_credit(1) TYPE C VALUE 'T'.
```
After making this change, it instructs to activate the transformation and reload the data. The note clarifies that it is not directly implementable, meaning the changes have to be manually applied if not applying the support package updates.