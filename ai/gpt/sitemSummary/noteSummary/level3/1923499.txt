SAP Note 1923499 addresses an issue where recorded static or semi-dynamic Technical Bills of Material (TBOMs) have very small content. This problem occurs when creating TBOMs in transaction SOLAR01/02 or by using the mass TBOM generation report 'AGS_BPCA_TBOM_STATIC_GEN', despite setting a high branching level.

The underlying cause of this issue is that the STATIC TBOM recording relies on the where-used functionality's environment data, which might not be current.

To resolve this issue, it is recommended that the where-used index in the managed system be updated as described in SAP Note 28022.

Furthermore, additional information about the where-used index, including details on managing the size of table WBCROSSGT and improving runtime for the where-used list, can be found in SAP Notes 2039618 and 1917231, respectively. These references are included in the note to assist with troubleshooting and optimizing the performance of the where-used functionality.