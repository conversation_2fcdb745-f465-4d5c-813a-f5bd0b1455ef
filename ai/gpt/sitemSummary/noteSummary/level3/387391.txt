SAP Note 387391 deals with enhancing the standard checks provided by the Logistics Information System (LIS) within SAP. These additional checks help to ensure that the creation of information structures and update rules within LIS leads to the desired results. Despite the availability of standard checks, they were found to be insufficient, prompting the need for further checks.

Key points from the SAP Note:

- Additional checks for LIS are provided which can detect potential problems early in the design phase of information structures and update rules.
- The additional checks can present a clear log of the checks performed.
- These improvements are included in the Support Package SAPKH46C21.
- To implement these additional checks before importing the support package, the note outlines a few steps:
  1. Import the transport located in the directory /general/R3server/abap/note.0387391 on the SAPSERVx computers, considering Note 13719.
  2. Perform the corrections as per correction instructions 0120031469 0000234004. Corrections from instructions 0120061532 0000283486 are already included in the SAPSERVx transport, so they are not required separately.
  3. Create certain text symbols in specified programs with defined lengths.
- The SAPSERVx transport includes new objects such as programs, domains, data elements, table types, tables, required table entries, and messages with long texts in message class M! in both German and English.
- Users are also instructed to continue implementing changes from Note 417941, with the exception of a specified correction that is already included in the transport.

In summary, SAP Note 387391 provides enhanced checks for the Logistics Information System, which are available in a specific Support Package. Users who wish to implement these enhancements prior to the release of the package must follow specific instructions involving the import of transport files, application of correction instructions, and creation of text symbols. These enhancements aim to improve early detection of design issues in information structures and update rules, thereby improving LIS effectiveness.