SAP Note 1765330 addresses the issue of closing reorganization plans within an SAP system when not all steps in the plan have been processed successfully. The user might want to close these plans in cases like setting up a follow-up plan for the same organizational unit in a test system, but an older plan is still marked as open due to minor issues.

The term relevant to this note is "CLOSE."

The note provides an additional function to facilitate the closing of such incomplete plans. The proposed solution is to add the special parameter '&special_closing=X' to the URL of the plan overview in the browser when viewing the Web Dynpro application FAGL_R_PLAN_OVERVIEW. This addition will prompt the system to display an "Close Plan with Open Objects" button, which allows the user to close the plans with open tasks.

For those who can't see the URL, the note gives instruction on how to navigate to the application through the ERP system using transaction SE80 and find the Web Dynpro application within a specific package (FAGL_REORGA<PERSON>ZATION for Ehp5 or FAGL_REORGANIZATION_FW for EhP6 or later). Users can start the application with a "Test" after finding it in the Web Dynpro applications folder.

The note also mentions that in the 'SAP NetWeaver Business Client' environment, adding the parameter directly to the URL is not possible. Users should start the call through transaction SE80 in such cases.

Additionally, it highlights a potential issue with Web browsers where the URL might end with the character '#'. In this scenario, the '&special_closing=X' parameter needs to be inserted before the '#' to ensure proper functionality.