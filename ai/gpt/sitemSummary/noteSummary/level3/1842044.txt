SAP Note 1842044 introduces a new feature for Oracle databases that allows data loading into an InfoCube's F fact table without the need to drop bitmap indexes, thus avoiding potential deadlocks. For the feature to work correctly, additional code corrections and related SAP Notes must be implemented, as documented in SAP Note 3068177 titled "ORA: Best practice 'dataload without dropping bitmap indexes'". It's recommended to subscribe to updates for SAP Note 3068177 to stay informed of any changes.

This feature involves parameters such as "rsadmin disable local indexes", "ORA_IC_LOAD_NO_DROP", and "UNUSABLE index partitions". While the specific steps and prerequisites for enabling this feature can be found in SAP Note 3068177, it is crucial to apply the recommended related SAP Notes and Correction Instructions to ensure proper functionality.

Regarding the versions affected, SAP NetWeaver BW 7.30, 7.31, and 7.40 users should import the relevant Support Packages (SAPKW73011 for NW 7.30, SAPKW73111 for NW 7.31, and SAPKW74006 for NW 7.40) into their BW system. These Support Packages are detailed in subsequent SAP Notes (1878293 for NW 7.30 SP11, 1914639 for NW 7.31 SP11, and 1920525 for NW 7.40 SP06) and will be available after these Notes are released to customers.

In urgent situations where Support Packages are not yet available, users may apply correction instructions directly. Users are also advised to refer to SAP Note 1668882, which covers the transaction SNOTE, to ensure compatibility.

In summary, this SAP Note emphasizes the importance of implementing the new feature according to the best practices detailed in SAP Note 3068177, including the subscription to notifications for any updates, and applying necessary Support Packages or Correction Instructions to avoid issues with bitmap index maintenance during concurrent data loads into an InfoCube.