SAP Note 3059582 addresses an issue encountered in SAP S/4HANA versions 1809, 1909, and 2020 when customers use the SAP S/4HANA Migration Cockpit to migrate Accounts Payable (A/P) open items or Accounts Receivable (A/R) open items. The problem is that the cash discount values entered in the fields "Amount Eligible for Cash Disc. (Doc.Cur)" and/or "Cash discount amount in curr. of DocTyp" are not being considered during the clearing transactions subsequent to data migration.

This issue occurs because the migration process does not submit the DISC_BASE and DISC_AMT for currency type '10'.

To resolve this problem, users are instructed to implement the corresponding Transport-Based Correction Instructions (TCI) SAP Notes depending on their SAP S/4HANA release and its service pack level:

- For SAP S/4HANA 1809 (Service Pack levels 00 to 06), implement SAP Note 3066267.
- For SAP S/4HANA 1909 (Service Pack levels 00 to 04), implement SAP Note 3066224.
- For SAP S/4HANA 2020 (Service Pack levels 00 to 02), implement SAP Note 3066214.

The table in the solution provides a clear overview of which TCI SAP Note should be implemented for each version and service pack level to fix the issue.