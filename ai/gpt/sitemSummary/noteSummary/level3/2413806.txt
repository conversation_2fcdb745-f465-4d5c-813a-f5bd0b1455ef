SAP Note 2413806 addresses the support issues for Java browser-plugins used within cFolders, particularly for features like mass upload/download of files, document check-out/check-in, and the ECL Viewer for engineering documents. As Java plugins are being deprecated starting with JDK9 (expected in March 2017), most browsers, except Internet Explorer, will no longer support these plugins. 

Customers using cFolders features reliant on Java Applets will only be able to do so on Internet Explorer, as it is the only browser expected to continue supporting Java browser plugins in 2017. SAP has no plans to replace these Java Applet-based features with HTML-based features and suggests users migrate to other document collaboration solutions, like SAP S/4HANA for intelligent product design, as cFolders and its integration with SAP PLM/SAP Portfolio and Project Management are not supported in SAP S/4HANA. Additional information can be found on SAP's product development collaboration webpage.