SAP Note 2715504 provides guidance on configuring an SAP ABAP system to automatically send data for the Early Watch Alert (EWA) report to an Focused Run for SAP Solution Manager (FRUN) system. The key points of this note are as follows:

1. **Symptom**: The goal is to set up an SAP ABAP system to regularly provide and send EWA data to FRUN.

2. **Environment**: The note specifies that a Solution Documentation Assistant (SDA) version greater than 1.28 must be installed on the host where the ABAP system is running. It also mentions that the necessary user roles should be generated beforehand by using the 'GenerateMonitoringUserRoles' configuration task.

3. **Resolution**: The note gives a step-by-step procedure on how to configure the EWA job through the preparation tool, including:
   - Discovering all instances of the ABAP system and selecting a dialog instance for JCo calls.
   - Updating or creating a new RFC destination for FRUN.
   - Creating or verifying an EWA user with the assigned role 'SAP_SDCCN_ALL' (if not already existing). 

   It is essential to ensure that if an existing user is used, they must have the 'SAP_SDCCN_ALL' role; otherwise, post-processing of the configuration will fail. The configuration task will not add missing roles to an existing user for legal reasons.

4. **Task Configuration Parameters**: The note lists the parameters required to set up the EWA job, including the ABAP system’s client, login user, and SID, the FRUN system's host name, port, and SID, as well as various user-related settings like namespace, roles, and passwords. It also gives default values for some of these parameters and specifies which ones are mandatory.

5. **Example**: An example of how to fill out the parameters for the task configuration is provided at the end of the note, with placeholder values indicating how to fill in the actual values for specific settings like the ABAP client number, user credentials, system IDs, and network details.

By following this SAP Note, users will be able to configure their SAP systems to automatically schedule and send EWA reports to their respective FRUN systems, helping maintain the performance and stability of their SAP landscapes through proactive monitoring.