SAP Note 1617641 addresses the issue wherein the Logon Control of SAP GUI does not support certain Single Sign-On (SSO) scenarios with Secure Network Communication (SNC). Specifically:

1. The inability to use SNC logon with username and password (without SSO) when the SAP Logon connection entry has SNC enabled.
2. The need to always perform an SNC logon without SSO via such a defined connection entry.

The solution provided involves a kernel extension (as detailed in SAP Note 1561161) and an extension of SAP GUI functionalities, whereby:

1. A new context menu entry named "SNC Logon without Single Sign On" has been added for connection entries. This allows users to logon with a different username and password, bypassing SSO.
2. A new checkbox setting "SNC Logon with User/Password (no Single Sign-On)" has been incorporated into the "Secure Network Settings" on the property page of the connection entry. When enabled, SNC logon will proceed using the username and password without leveraging SSO.

Furthermore, when using the "SNC Logon with User/Password (no Single Sign-On)" setting, users must enter their credentials to log on to the R/3 system.

The note also points out that the issue will be resolved in a future SAP GUI for Windows patch, and that patches for the SAP GUI are cumulative. Users should install the patch to obtain the correction. If patch details are not yet provided within the note, users are instructed to check the note at a later time for updates.

Other relevant information includes:

- Users can find expected delivery dates for patches by checking SAP Note 1053737 or subscribing to a notification thread on the SAP Developer Network.
- Patches can be downloaded from the SAP Service Marketplace, with details available in SAP Notes 563161 and 330793.
- Instructions for installing SAP GUI patches are documented in the SAP GUI Installation Guide and in SAP Note 535308.

Overall, this note outlines an enhancement to SAP GUI, enabling users to bypass SSO when making an SNC logon, along with the prerequisite kernel extensions and information on obtaining the necessary patches for the SAP GUI.