SAP Note 2700165 addresses the issue where a vendor cannot be created as a 'Person' - instead, they are always created as an 'Organization' when using the Migration Cockpit (LTMC) in an on-premise S/4HANA environment. This limitation applies to release 1709 and earlier, where the migration objects are designed based on the best practices for S/4 HANA Cloud releases. Therefore, originally, only the migration of organizations is supported, and the TITLE field in the Business Partner data only accepts 'blank' or '0003', which corresponds to an organization.

The note suggests that if users want to create a supplier as a person, they can do so manually or create a custom-specific migration object. For further assistance, users are directed to contact SAP's global customer engagement team via the provided email.

The resolution also mentions that in the 1809 release and later, the system allows for the creation of both person and organization categories.

Finally, the note references to two related KBAs (Known Bug Articles) - KBA 2000099, which discusses an error related to form of address designation for organizations, and KBA 2462700, which addresses issues with changing BP (Business Partner) types post-creation.

Key search terms included in the note are related to the inability to create vendors as persons within the Migration Object Modeler and the necessary checks for business partner creation.