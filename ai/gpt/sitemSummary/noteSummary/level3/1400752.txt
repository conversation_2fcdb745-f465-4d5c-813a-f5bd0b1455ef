SAP Note 1400752 addresses a problem that occurs after upgrading, specifically with the activation of extract structures for DataSources 2LIS_03_BF and 2LIS_02_SCL within logistic applications. The issues involve the duplication of certain fields in the extract structures that can lead to syntax errors and terminations during data extraction to BW. Additional problems include the visibility of fields in transaction LBWE when transferring fields from communication structures to extract structures.

The cause of the issue is identified as the direct addition of append structures to the extract structures MC03BF0 and MC02M_0SCL in earlier versions of ECC (6.00 or 6.02). Duplicate fields within these append structures and communication structures (MCMSEG for 2LIS_03_BF and MCEK<PERSON> for 2LIS_02_SCL) trigger conflicts during upgrades to newer Enhancement Packages. Another problem is that transaction LBWE does not support or display some fields contained in the append structures, even though they are populated during data extraction to BW.

The solution provided in the note involves removing the problematic fields from the customer append during the upgrade so that the extract structure can be activated successfully. It is also noted that direct enhancements of extract structures for logistic application DataSources will no longer occur using append structures. Instead, new fields will be delivered through Enhancement Packages using switch BC sets. When the relevant business function is activated, these switch BC sets are activated, and a report is triggered that adds the new fields to the extract structures while taking care to compare with any fields already added via customer appends.