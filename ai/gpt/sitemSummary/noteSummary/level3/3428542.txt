SAP Note 3428542 addresses an issue in the SAP S/4HANA Migration Cockpit where the "EHS - Compliance requirement" migration object is lacking a field for the "Paragraph Type" in the "Requirement Structure" section. Users migrating EHS Compliance Requirement data would encounter this missing field.

The solution provided by this note is the addition of the "Paragraph Type" field to the "Requirement Structure" within the "EHS - Compliance Requirement" object. To resolve this issue, the note gives instructions to implement two other SAP Notes:

1. SAP Note 3427573 should be implemented for the migration API, which pertains to the release 2023 and addresses the missing "Paragraph Type" field in the EHS Compliance Requirement migration API.

2. SAP Note 3428490, which is a TCI-based SAP Note for the migration object for releases 2023, SP00 - SP01. This note will update the delivered migration object content automatically. However, it will not address any modifications or copies made by users to their migration objects. Users with such modifications are referred to KBA 2543372 for instructions on implementing a Transport-based Correction Instruction (TCI).

The technical details further clarify that the source structure S_STRUCTURE will have a new field, PARAGRAPH_TYPE, added to it. Additionally, a new mapping rule (MAP_REQ_PAR_TY) and conversion rule (CVT_REQ_PAR_TY) will be created for use in the EHS - Compliance Requirement migration object. These changes will enable users to appropriately map and convert the "Paragraph Type" during data migration processes.