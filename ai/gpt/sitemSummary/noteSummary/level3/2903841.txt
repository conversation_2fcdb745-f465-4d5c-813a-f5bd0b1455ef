SAP Note 2903841 addresses an issue within the SAP S/4HANA Data Migration Cockpit on the on-premise product S4CORE. When using a migration object that requires updating with additional content from SAP, a popup window may arise if the object contains custom modifications. These custom modifications can come from self-implemented enhancements or pre-corrections provided by SAP Support.

The cause of the issue is that SAP periodically updates migration objects to align with the current state of the SAP S/4HANA system. If a migration object is slated for an update and it has custom modifications, the system will prompt users to review these changes before updating. Upon update, any custom modifications will be overwritten.

The resolution involves several steps for users who cannot start a new migration project (due to ongoing go-live activities, for example). Users must open the respective migration object in display mode using the Migration Object Modeler and check if updates are available and if the object has been modified. To proceed with an update and revert the object to the standard content provided by SAP, users must perform a reset. It's crucial to delete all files attached to the object before proceeding because attached files will prevent deletion and therefore updating of the object.

After completing these steps, users must open the object again in the SAP S/4HANA Migration Cockpit, which will display a popup prompting for the update. Users can then update the object and continue using it post-update.

The note also recommends users to copy the migration object to be modified instead of directly modifying it to avoid update prompts in the future, as copied objects become decoupled from the original and won't receive updates. Any modifications to copied objects become the responsibility of the user.

Additional information and restrictions regarding the extensibility of pre-delivered migration objects can be found in SAP Note 2481235. The keywords for this issue include Update object, Object modeler, LTMOM, modification, and ignore content update.