SAP Note 1716626 provides guidance on how to install the ABAP add-on EHSM 300 for SAP ERP. EHSM stands for Environment, Health, and Safety Management. The note includes instructions and prerequisites needed for the installation process, as well as solutions for known issues that may arise during the installation.

Summary:

- **Symptom**: The note addresses the desire to install EHSM 300 on SAP ERP.
  
- **Other Terms**: References various terms related to the installation such as SAINT, MOPZ, and SUM.
  
- **Reason and Prerequisites**:
  - Installation can be done if ERP 6.0 EHP5/EHP6/"EHP6 ON HANA" is already present, using SAINT.
  - If an upgrade to one of the mentioned ERP versions is needed, the EHSM 300 installation should be included in the upgrade path using SUM/MOPZ.
  
- **Solution**:
  - Always use the latest version of the note.
  - The note comprises several sections including prerequisites, preparation steps, execution steps for installation, post-installation steps, language support, password for installation package, known issues, and instructions for specific installation scenarios.
  
- **Prerequisites**:
  - EHSM 300 cannot be uninstalled once added.
  - Specific releases and support packages for SAP components are required such as EA-APPL, SAP_APPL, and SAP_BASIS.
  - The latest R3trans and tp should be imported as well as the latest kernel.
  - Obtain several SAP Notes before beginning installation.
  
- **Preparing for Installation**:
  - EHSM 300 needs to be available, either by requesting a CD or downloading from SAP Service Marketplace.
  - Instructions for unpacking are provided for different operating systems.
  
- **Executing the Installation**:
  - Log on with a user that has SAP_ALL authorization in client 000, avoiding SAP* or DDIC users.
  - Use transaction SAINT for the installation.
  
- **Post-Installation**:
  - No additional imports or generation errors indicated.
  - Delivery customizing is imported into client 000 and may need to be adjusted for other clients.
  - Activate necessary WebDynpros services and NWBC runtime node.
  
- **Language Support**:
  - EHSM 300 supports multiple languages, and language-dependent parts of the add-on should be imported accordingly.
  
- **Password**:
  - A specific password for the package, mentioned in the note, is required during installation via SAINT.
  
- **Known Issues**:
  - Shared memory should be around 250 MB.
  - Specific notes are provided to resolve known issues such as navigation errors in SAP Product Stewardship Network.
  
- **Specific Installation Scenarios**:
  - Solutions to generation errors while installing EHSM 300 on different versions of SAP ERP are provided, along with relevant note references for resolution.

Overall, the note serves as a comprehensive guide for installing EHSM 300, detailing every step of the process, from preparation to post-installation, and how to handle certain problems that may occur.