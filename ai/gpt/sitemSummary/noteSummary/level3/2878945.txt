SAP Note 2878945 addresses the need to enhance data load throughput during migration to SAP S/4HANA using the Migration Cockpit for handling XML files. When migrating, loading a single large XML file can be time-consuming, as the system does not process files in parallel by default.

**Key points from the note:**

- **Applicable Systems:** SAP S/4HANA on-premise (using LTMC and Fiori app "Migrate Your Data") and SAP S/4HANA Cloud.
- **Problem:** Long load times when using large XML files, as parallel processing isn't enabled by default.
- **Solution:** Split data into multiple XML files and configure the system to process them in parallel. The steps required to do this are different depending on the specific version of SAP S/4HANA being utilized:
  - **For on-premise versions up to 2020:** Use transaction LTMC or the Fiori app to upload and activate multiple files, adjusting "Max. Data Transfer Jobs" to a number equal to or less than the number of files.
  - **For the 2020 Fiori app:** Edit the "Jobs Used for Migration Objects" section and set the "Maximum Number Used" to 1 or more.
  - **For the S/4HANA Cloud and versions from 2021 onward:** Change the "Number of Jobs" in the "Job Management" view for the specific migration object.
- **Caution:** Some migration objects cannot be parallelized. A list of these objects is provided in KBA 3294684.
- **Example:** If you split a large file into three smaller ones and set "Max. Data Transfer Jobs" to '3', the system will attempt to process all three in parallel.
- **Restrictions:** Features such as the parallel processing of the "Cost Center" migration object are not supported due to constraints like hierarchy group creation. Additionally, splitting data should adhere to XML size limits detailed in KBA 2719524.
- **Recommendations:** Avoid parallel processing of multiple migration objects to prevent system resource overconsumption. A proper sequence based on object dependencies should be followed, and it's advised to complete the migration of one object before starting another.
- **Tools:** A sample file splitter tool is available on GitHub which can aid in splitting large XML files.

The SAP Note also references additional notes and documentation for further information and advises users on keywords related to this note such as "Parallelization, migration cockpit, LTMC, XML, load, jobs, parallel".