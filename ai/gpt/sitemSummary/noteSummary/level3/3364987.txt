SAP Note 3364987 is a central correction note for content issues specifically for SAP S/4HANA 1909. The note addresses problems experienced with the data migration content when transferring data using staging tables in the SAP S/4HANA Migration Cockpit.

The note is applicable under the following conditions:
- The SAP S/4HANA version installed is 1909, ranging from Support Package 00 to 08.
- The SAP S/4HANA migration cockpit is being utilized.
- The pre-delivered SAP S/4HANA Data migration content is used without modifications.

The key issues addressed by this note include:
- An incorrect data type assigned to the WBS element in the FI - G/L account balance by CO-PA, which is covered by SAP Note 3358011.
- An incompatibility in the length of the inflation key (MAP_INFKY) in the mapping rule, where export parameter length should be 8 characters but was only 2. This issue is described in SAP Note 3364196.

To fix these issues, a Transport-based Correction Instruction (TCI) is included in the note, and its application will automatically update the related objects of the delivered content. However, this does not apply to migration objects that have been modified or copied by the user. For those, the corrections will have to be applied manually.

Users who have made such modifications to their objects and require guidance on implementing a TCI should refer to KBA 2543372, "How to implement a Transport-based Correction Instruction."

In summary, SAP Note 3364987 provides a solution to specific data migration content issues for users of SAP S/4HANA 1909, with detailed references to the affiliated SAP Notes for each addressed issue. Users are advised to apply the TCI according to the note to correct these issues unless they have modified the content, in which case they should follow the guidance provided in the referenced KBA.