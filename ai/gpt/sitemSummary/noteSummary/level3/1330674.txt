SAP Note 1330674 outlines the requirements and steps necessary for delivering the SAP Custom Code Maintainability Check (CCMC), a service that is available as part of SAP Enterprise Support. This check is particularly useful for implementation projects with a significant amount of custom code, or for those who want to analyze the customizability of their system.

The note specifies that it applies only if your SAP Solution Manager is on release 7.0 or on release 7.10 with support package stack 7 or lower. For systems on release 7.10 with support package stack 8 or higher, SAP Note 1861798 should be referred to instead.

To utilize the CCMC, the following steps should be taken:

1. Review the attached information sheet for details about the CCMC.
2. Fill out the attached questionnaire with the required data.
3. Create a support message with the component SV-BO-REQ, requesting the CCMC, and attach the completed questionnaire or ask a support advisor to create a service request.
4. Ensure that the technical requirements outlined in the note are met, which includes having certain Solution Tool Plugins and required SAP Notes implemented on the managed systems (DEV, QAS, PRD). These can be checked by running the report RTCCTOOL.

The specific technical requirements include:

- Having the Solution Tool Plugins ST-PI 2008_1 with at least support package 2, and ST-A/PI 01M installed.
- Implementing a series of SAP Notes for the ST-PI (>1337204, 1335023, and others) and ST-A/PI components (>1466759, 1511492, etc.), as well as the SAP_BASIS component for systems on 4.6C (SAP Code Inspector according to SAP Note 543359).
- The necessary authorizations on the managed systems, such as roles SAP_S_SWCM and SAP_CDMC_MASTER.
- A basic TMS Configuration setup is also required.
- On the SAP Solution Manager, software component ST-SER 2010.1 and certain roles are necessary.
- Service Connections and users for the development, quality assurance, and production systems, as well as for the SAP Solution Manager, must be in place.