SAP Note 702326 provides guidance for customizing correspondence within the SAP Learning Solution (LSO) module after an upgrade to ECC 5.00, where both Training and Event Management (TEM) and LSO are delivered. The note is intended for first-time LSO users as well as those transitioning from TEM or an earlier LSO release.

Key points of the note include:

1. **Customizing Switch**: Users must decide whether to use LSO or TEM and set the HRLSO switch accordingly.

2. **Automatic Customizing Adjustments**: When the HRLSO switch is set, the system automatically updates certain Customizing settings:
   - Changes function module calls for correspondence from TEM modules to LSO modules.
   - Extends the interface for function modules used to determine recipient groups in LSO.
   - Updates text events for LSO participants.
   - Adjusts data collection routines to return correct information for LSO.

3. **Manual Adjustments Required**: Users need to make manual adjustments to:
   - Forms, to ensure they are usable for all delivery methods.
   - Manually adding specific LSO text variables.
   - Adjusting any custom text variables and data collection routines.
   - Modifying function modules for recipient determination in line with LSO requirements.
   - Ensuring proper calls to function modules or subroutines that have been replaced or modified for LSO.

4. **New Text Variables**: The note provides a description of new text variables introduced in LSO correspondence, including how these variables are defined and where data is collected from within the system:
   - Variables like BUSTRANTYPE, KTFORMID, KTFORMTXT and many others are defined with detailed instructions on data collection for each.

The note ensures users have adequate information to update their system for a smooth transition and use of the LSO correspondence functionality post-upgrade. It also helps in checking whether Customizing settings are correct with respect to LSO as compared to TEM.