SAP Note 2975855 provides an overview of the CoPilot skills available in SAP S/4HANA 2020 for various lines of business, along with the associated roles required for each skill. This note outlines the available CoPilot functionalities within areas such as Procurement, Professional Services, Finance, and Sales. It also indicates that each CoPilot skill is linked to a specific role and provides hyperlinks to detailed help documents for each skill.

Additionally, the note highlights a restriction regarding the usage of the update functionality within CoPilot skills. It mentions that users cannot pass an utterance with the entity's key in an update intent; they can only use synonyms of the key or perform an update by referencing a previous result. An example is provided to clarify which methods of updating are functional and which are not. 

For example, while the direct command "Update Sales Order 123" will not work, alternatives like using the synonym "Update Order 123" or contextual references—such as "Update this one" after a query showing a specific sales order—will work.

To employ these CoPilot skills, the technical CoPilot user must be assigned the appropriate authorizations. The SAP Note instructs administrators to assign the listed roles to the technical CoPilot user and provides a link to documentation on how to configure data provisioning for this purpose.