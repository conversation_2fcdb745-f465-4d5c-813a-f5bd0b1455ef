SAP Note 2194279 describes an issue where incorrect use of master data read classes for technical characteristics or time characteristics can cause various problems in an SAP system. These issues are further detailed in SAP Notes 939484 and 1387166, and in Knowledge Base Articles (KBAs) 2074801 and 2158503. Previously, checks for using the correct master data read class needed to be done manually in the InfoObject maintenance transaction.

The note addresses that especially after system upgrades, issues may arise if the master data read class provided by SAP content is not used. It's also noted that if additional attributes need to be utilized for certain characteristics, a custom master data read class might have to be implemented (as per KBA 2158503).

The solution provided with this SAP Note is the introduction of report RSD_TIME_TCT_MREADCLASS_CHECK, which becomes available after importing the specified Support Package or correction instructions attached to this SAP Note. The report can be executed in transaction SA38/SE38 and helps users to verify that the master data read classes used for the characteristics match the ones from the technical content or meet the users' expectations.

The SAP Note specifies which Support Packages need to be imported for different SAP BW versions to resolve the issue:

- For SAP BW 7.30, import Support Package 15 (SAPKW73105).
- For SAP BW 7.31, import Support Package 17 (SAPKW73117).
- For SAP BW 7.40, import Support Package 13 (SAPKW74013).
- For SAP BW 7.50, import Support Package 1 (SAPK-75001INSAPBW).

The availability of these Support Packages is contingent upon the release of other related SAP Notes that describe them in more detail, such as SAP Notes 2199121, 2139356, 2176288, and 2192427.

In cases that require urgent attention, correction instructions can be implemented as an advance correction. Before doing so, it is advised to read SAP Note 1668882 for information on how to use transaction SNOTE.

Lastly, the SAP Note may be available before the Support Package is released, and if that's the case, the SAP Note would be marked as a "preliminary version."