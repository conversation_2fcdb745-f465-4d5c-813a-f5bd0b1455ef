SAP Note 1237517 addresses an issue with function module SUSR_INTERFACE_AUTH, which is used for processing authorizations. The symptom described is that when there's an incompatibility between the process to be handled and the authorization object's fields, the function module terminates further processing and issues error messages. Additionally, the error logs generated by SUSR_INTERFACE_AUTH may interfere with the application logs of the calling transaction.

The note also references other terms that may appear in error messages or logs: 308(s#), 089(5@), and 090(5@), along with the term "check_auth_data."

This note serves as a functional enhancement and should be used alongside Note 841612. The resolution provided involves using the Note Assistant to implement the corrections or importing the relevant Support Package to address the issue.

The solution introduces a new optional import parameter for the function module named SHOW_LOG. The default behavior (if SHOW_LOG is set to 'X' or not used) does not change the current system response. However, if the parameter is set to SPACE, the error log will be suppressed within the function module. Instead of logging the error, the function module conveys error messages about inconsistent authorization data back to the calling program using the OBJECT_DOESNT_EXIST exception. This allows for better handling of error messages and reduces conflicts with the application logs of the calling program.