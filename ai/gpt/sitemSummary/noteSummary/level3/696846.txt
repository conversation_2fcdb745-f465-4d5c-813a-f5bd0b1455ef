SAP Note 696846 addresses an issue where changing time-dependent data in the equipment, which is synchronized to an asset, does not result in the creation of a new time segment for the asset's time-dependent data. This kind of data includes business areas, cost centers, or plant data that should be time-segmented in the asset. The note clarifies that this functionality is currently not available.

Key Points from SAP Note 696846:

- **Symptom**: Changes in equipment data that should lead to updating time-dependent data in the asset do not create a new time segment.

- **Reason**: The function to create new time segments for time-dependent data in the asset upon equipment data changes does not exist.

- **Solution**: Implement a new function by applying the Support Package or transport file provided with the note. For SAP release 4.6C, the transport request and ZIP archive files (with KO and data files) are provided.

- **Implementation Details**: The solution involves using the method PM_DETERMINE_ADATU from the new BAdI (Business Add-In) AAPM to determine the validity start date for the new time segment in the asset.

- **Responsibilities and Restrictions**:
  - The method PM_DETERMINE_ADATU should be deactivated if not required by the customer.
  - Asset data is only synchronized if the asset already exists; the method doesn't trigger during the asset creation process.
  - Time-dependent fields must be impacted during synchronization.
  
- **Risks**: An improper selection of the validity start date for the new time segment can lead to irreversible asynchronization between equipment and asset data. This risk must be managed by the user through the PM_DETERMINE_ADATU method.

- **Documentation**: Further details on BAdI documentation, including prerequisites and restrictions.

- **Related Notes**: Users are directed to refer to Notes 480180 and 13719 for additional instructions and details on the implementation procedure.

In summary, this note provides the solution for creating time segments in an asset when time-dependent data from the equipment is modified. The implementation involves using a newly defined BAdI and comes with certain prerequisites and responsibilities for the user. Users also have to pay attention to potential risks of asynchronization introduced by incorrect validity start dates.