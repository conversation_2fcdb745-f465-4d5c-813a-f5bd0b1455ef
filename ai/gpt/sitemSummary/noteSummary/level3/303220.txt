SAP Note 303220 addresses an issue specific to systems with the IS-OIL solution installed. The problem pertains to the incorrect defaulting of Tank IDs during the modification of orders.

Summary of the SAP Note 303220:

**Symptom:**
Users of the IS-OIL solution are experiencing issues with Tank ID defaulting incorrectly when they change orders.

**Warning:**
This note is strictly for systems with the IS-OIL component. Implementing this note on systems without IS-OIL could cause severe damage.

**Other Terms:**
Relevant keywords include TPI, order creation, Tank defaulting, and tank assignment.

**Reason and Prerequisites:**
The note is issued to resolve the specific problem of Tank ID defaulting inaccurately on order changes.

**Solution:**
The resolution requires downloading and importing specific transport files for different SAP release versions.

For Release 4.0B:
- Download and import Transport SOEK005698 from the specified server locations on SAPSERVx.
- Check the object list in the provided server path.
- This transport should only be applied to systems with the IS-OIL module. SAP Notes 47531 and 13719 should be referred for prerequisites and how to import corrections.

For Release 3.1H:
- Download and import Transport SODK005884 from the specified server locations on SAPSERVx.
- Check the object list in the provided server path.
- This transport should also only be applied to systems with the IS-OIL module. Refer to SAP Notes 47531 and 13719 for prerequisites and importing instructions.

Additionally, Note 303220 advises reviewing SAP Service System Notes 145850 and 145854 for Release 4.0B—or 98642 and 98876 for Release 3.1H—to ensure proper sequence of installation.

In summary, the note provides a correction for the Tank ID defaulting issue and points to further instructions and prerequisites needed before the transport files can be applied to the appropriate IS-OIL systems.