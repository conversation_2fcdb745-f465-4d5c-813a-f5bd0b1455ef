SAP Note 2413261 addresses a performance issue with the Large Object (LOB) garbage collection feature in row stores for SAP HANA databases. This issue manifests through continuously growing database persistence and data backup sizes, as well as an ever-increasing number of entries in specific system tables called SYS.RS_LOB_GARBAGE_<volume_id>_. Additionally, the Multi-Version Concurrency Control (MVCC) version count may also keep growing, as the long transaction of rowstore LOB garbage collection hinders the global MVCC version garbage collection.

The affected database versions include SAP HANA 1.0 Revisions 122.00 to 122.10, excluding 122.05 and 122.06, as well as SAP HANA 2.0 database Revisions before 012.00. The reason behind this issue is a change made to the logic of row store LOB garbage collection in SAP HANA 1.0 SPS12 that aimed to avoid MVCC version problems. This logic introduced new tables, which can, due to a performance issue, accumulate a large number of entries and prevent the garbage collection from keeping pace with the creation of new LOB garbage.

The note provides a solution that includes upgrading to newer Revisions of SAP HANA that are not affected by this issue (versions after 122.11 for HANA 1.0 and 012.01 for HANA 2.0). It also outlines a workaround that involves disabling the problematic garbage collection mechanism, identifying and canceling the long-running LOB garbage collection transaction, and converting row tables with hybrid LOBs to column tables to prevent future accumulation of rowstore LOB garbage.

Lastly, the note advises that the growth of persistence and data backup is generally not an issue if enough free space is available on the data volume, and emphasizes that the provided workaround must be reverted after upgrading to a revision that includes the fix for this issue.