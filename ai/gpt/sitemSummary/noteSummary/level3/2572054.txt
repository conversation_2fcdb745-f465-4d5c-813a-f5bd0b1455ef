SAP Note 2572054 addresses an issue in SAP Revenue Accounting where the system incorrectly creates or changes deferral items with deleted conditions. These conditions have a zero amount and total revenue, which results from erroneously copying data from old deferral items.

The symptom is that when certain steps are taken, including creating a contract with an event-based performance obligation (POB), suspending and unsuspending this POB, and changing its allocated price, the system generates a new deferral item with an incorrect allocation difference. This issue is indicated by the inflight check message C0501 on the POB details UI and may also be spotted through data validation error E04 using report FARR_CONS_MON or SQL E4.

The root cause of the problem is a programming error where the system incorrectly copies data from old deferral items in the previous period for conditions with a zero amount and zero revenue that have been deleted.

The solution provided by this note is to apply the note itself, which suggests that it contains the necessary corrective measures, such as a code correction, to resolve the issue. Users who have encountered this problem are advised to implement the note to prevent the creation or modification of deferral items incorrectly.