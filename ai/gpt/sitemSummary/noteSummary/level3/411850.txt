SAP Note 411850 addresses an issue encountered by users who have installed Support Package 1 of IS-PS (Industry Solution for Public Sector) 4.62. After the installation, the table KOMKAIS is reduced to include only the basic delivery standard field KDUMMY_IS (CHAR 1). As a result, errors are generated indicating that the data objects "T<PERSON><PERSON><PERSON>" and "<PERSON><PERSON><PERSON>" do not contain components named "ISHS<PERSON><PERSON><PERSON><PERSON>" and "ISH<PERSON><PERSON><PERSON>", respectively.

The problem arises when both the IS-H (Industry Solution for Healthcare) and IS-PS modules are installed, and Support Package 1 for IS-PS release 4.62 has been applied, but Support Package 4 for IS-H 4.62B has not yet been imported.

The solution provided is to apply SAPKIPH914. For those unable to import Support Package 4 for IS-H 4.62B, an alternative solution is available on the SAP service marketplace (sapservx, where x could be 3, 4, etc.) in the directory specified as general/R3server/abap/note.0411850.

In summary, this note gives a resolution to a specific error involving missing components in data objects after applying a support package, and provides options for users to remedy the situation either by applying a subsequent support package or by following the workaround provided in the SAP service marketplace.