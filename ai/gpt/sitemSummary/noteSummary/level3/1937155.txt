SAP Note 1937155 addresses the issue where an RSAU499 error occurs during data loading into an InfoCube using an InfoPackage in SAP Business Warehouse (SAP BW) or SAP NetWeaver environments. This error is caused by the use of outdated versions of special and technical characteristics, such as 0CALQUARTER.

To resolve this issue, the note recommends installing the most recent content version of the special and technical characteristics. By doing so, it should eliminate the error and allow successful data loading into the InfoCube.

Keywords associated with this note suggest it pertains to various SAP BW tasks like UPDATE_INFOCUBE and information related to real-time data warehouse platforms, decision-ready business intelligence, and real-time access to information.