SAP Note 1849497 outlines an optimization for DataStore objects (DSOs) when used with the SAP HANA database. Previously, to leverage enhanced activation performance with SAP HANA, users had to model HANA-optimized DSOs or convert standard DSOs using transaction RSMIGRHANADB, which modified the table layout and used a calculation view for the change log. 

With the corrections and support packages detailed in this note, such conversions are no longer necessary. All standard DSOs can now utilize an SAP HANA-optimized activation and rollback process with comparable performance and without needing to alter the table layout. The change log data is stored in a transparent table, keeping memory consumption at the level of HANA-optimized DSOs.

The note also mentions that creating SAP HANA-optimized DSOs is not possible once this note is applied, and recommends converting existing HANA-optimized DSOs back into classic DSOs for standardization purposes (referencing SAP Note 1849498 for more details).

For the solution to take effect, users on SAP NetWeaver BW 7.30 should import Support Package 10 (after the release of SAP Note 1810084), users on SAP NetWeaver BW 7.31 should import Support Package 9 (after the release of SAP Note 1847231), and users on SAP NetWeaver BW 7.40 should import Support Package 4 (after the release of SAP Note 1853730). 

In urgent situations, correction instructions are available as an advance correction after referring to SAP Note 1668882, which provides information on how to use transaction SNOTE.

The prerequisites for implementing the solutions in this note include using SAP HANA SP5 with a revision level of 57 or higher.