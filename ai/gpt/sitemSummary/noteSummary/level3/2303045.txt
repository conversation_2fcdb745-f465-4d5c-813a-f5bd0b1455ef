SAP Note 2303045 provides guidance on how to install new systems and perform system copy or Unicode conversion for systems using DB2 version 12 for z/OS. Here's a summary:

**Symptom**:
This note applies when installing a new system on DB2 12 for z/OS or when performing system copy/Unicode conversion with DB2 12 for z/OS as the target database.

**Other Terms**: 
Installation system copy, DB2 zOS 12.

**Reason and Prerequisites**: 
- Your SAP NetWeaver Release should be released for DB2 11 (see SAP Note 2302997).
- All prerequisites from SAP Note 2303027 should be fulfilled, especially the hardware and OS requirements for DB2 12 for z/OS.

**Solution**:
For new installations:
1. Use Software Provisioning Manager (SWPM), kernel, and DB2 Driver media from "Media List" section.
2. After installation, increase the number of work processes and update the kernel following SAP Note 2303027.
3. Set up the transport management system.
4. Update the system to the support package level required for DB2 12. There are specific steps for different SAP Basis versions, which involve importing transport SAPKDB2V10 and subsequent steps related to support packages and corrections.

For System Copy/Unicode Conversion:
1. Prepare the source system by updating it with the required support packages and corrections as per SAP Note 1480594.
2. Use SWPM for the system copy/unicode conversion, and update the kernel according to the SAP Note 2303027 after the copy.
3. Perform additional steps which include creating db2applcompat.ini and performing a rebind for systems using SWPM for 7.0X.

**Media List**:
1. SWPM should be part of SL Toolset SP18 or higher, downloadable from SAP's website.
2. Necessary media (Export, Kernel, J2EE content, CLI driver, etc.) can be found in the SAP Software Download Center with specific instructions for finding DB2 Connect 11.1 and using 721_EXT kernel based on the version of SAP NetWeaver.

Make sure to follow all outlined steps, use the specified media versions, and apply all necessary support packages and corrections to ensure successful installation or system copy/unicode conversion with DB2 12 for z/OS.