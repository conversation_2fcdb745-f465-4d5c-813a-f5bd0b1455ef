SAP Note 2246699 details the prerequisites, installation processes, and compatibility concerns for the SAP BW/4HANA Starter Add-On, which is a transitional tool to convert an existing SAP BW system to SAP BW/4HANA.

Key Points from the SAP Note:

1. Pre-Requisites: To install the SAP BW/4HANA Starter Add-on, the system must be running SAP BW 7.5 SP 5 or higher on an SAP HANA database, and only certain SAP and 3rd party add-ons are supported.

2. Documentation: Detailed instructions for installation are included in the "Conversion Guide for SAP BW/4HANA," accessible via the provided link.

3. Installation: 
   - The Starter Add-on is installed using the Maintenance Planner and SAINT transaction.
   - Post-installation, the system operates in Compatibility Mode, allowing the modification of compatible objects and running existing processes, but restricting changes to non-compatible objects.
   - To convert non-compatible objects, the SAP BW/4HANA Transfer Tool is used, guided by instructions in SAP Note 2383530.

4. Checking Compatibility: 
   - The SAP BW/4HANA Transfer Cockpit can be employed to assess object compatibility with the Starter Add-on.

5. Operating Modes: 
   - Systems with exclusively compatible objects can switch to "B4H Mode" for full SAP BW/4HANA operations.
   - If important objects are unusable or require alteration, options include converting objects to be compatible, reverting to Compatibility Mode, or de-installing the Starter Add-on.

6. De-Installation: 
   - De-installation of the Add-on returns the system to Compatibility Mode without needing to reverse-convert objects, provided the attribute-change package (ACP) is installed.

7. Supported Add-Ons:
   - A list of compatible SAP and 3rd party add-ons is available in SAP Note 2189708.
   - Compatibility details for SAP BPC 10.1 in different operating modes are specified, including the requirement of SAP Note 2373204 for the embedded model in B4H Mode.

In short, this SAP Note serves as a guideline for customers planning to transition from SAP BW to SAP BW/4HANA by utilizing the Starter Add-on, providing essential information on the installation and compatibility checks necessary for a successful conversion.