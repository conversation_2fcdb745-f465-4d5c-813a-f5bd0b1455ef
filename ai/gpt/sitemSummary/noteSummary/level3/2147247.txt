SAP Note 2147247 is an extensive FAQ on SAP HANA Statistics Server. It covers a broad range of topics related to the understanding and troubleshooting of statistics server-related issues.

Symptom:
Users interested in details about SAP HANA's statistics server.

Environment:
SAP HANA

Resolution:
The note includes information on:

1. The purpose and implementation details of SAP HANA statistics server.
2. How to find information and documentation on the statistics server.
3. Identifying and troubleshooting common problems related to the statistics server, including specific SAP HANA alerts and MiniChecks.
4. Implementations differences between standalone (SSS) and embedded statistics server (ESS), and recommended migration processes.
5. SQL statements and procedures for retrieving statistics server details and performing specific configurations.
6. Benefits of embedding the statistics server within the index server.
7. Detailed step-by-step guidance on diverse topics ranging from configuring the statistics server, minimizing memory requirements, optimizing runtime and CPU consumption, to analyzing and resolving SAP HANA alerts.
8. Special considerations for system databases and Multi-Tenant Database Containers (MDC).
9. Information on the retention time for historical data, and how to address unique constraint violation errors.
10. Recommendations on event acknowledgments and adjustments to alert thresholds.
11. Guidance on restarting and initializing statistics server components.
12. Considerations on using Native Storage Extension (NSE) with statistics server tables and requirements for SQLScript versions.
13. Collection of statistics server data for remote system replication sites starting with SAP HANA 2.0 SPS 06.

The note is comprehensive, specific to implementing and maintaining an efficient SAP HANA statistics server setup, and includes references to various supporting notes and documents for more detailed resolutions and workarounds.