SAP Note 961778 addresses a software issue where the processing of a broadcast setting in SAP NetWeaver 2004s BI terminates unexpectedly with an ABAP dump error 'OBJECTS_OBJREF_NOT_ASSIGNED', which is associated with the exception 'CX_SY_REF_IS_INITIAL' in the context of the 'FINALIZE_FRAMEWORK_FACTORY' function.

The reason for this error is that the system cannot process an online execution of a broadcast setting for the current user. This situation occurs, for example, when there's a user-specific preliminary calculation and the current user is not included in the list of permissible users for this action.

To resolve this issue, SAP recommends importing Support Package 08 for SAP NetWeaver 2004s BI (BI Patch 08 or SAPKW70008) into the BI system. This Support Package will address the problem and is further detailed in SAP Note 872280, which will be titled "SAPBINews BI 7.0 SP08" upon its release to customers.

It is noted that the information about the Support Package may be available before its actual release, and such preliminary information will be indicated by including the words "Preliminary version" in the short text of the note. Users should look for SAP Note 872280 for detailed information and confirmation that the Support Package is ready for implementation.