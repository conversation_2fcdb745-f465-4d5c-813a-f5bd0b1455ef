SAP Note 2665266 addresses an issue encountered when using the Migration Cockpit to upload open purchase orders (POs) where the net price is automatically set to 0 if the invoice receipt indicator is not set. Despite providing a net price on the "Item Data" sheet, the created POs lack proper pricing conditions due to the missing invoice receipt indicator. The resolution is to ensure that this indicator field is set, which should then incorporate the provided net price into the created POs.

The symptom indicates a problem with migrating open PO data where pricing conditions are absent in S/4HANA Cloud and S/4HANA environments when migrating data using files or staging tables through the LTMC transaction or the "Migrate Your Data – Migration Cockpit" app.

The SAP Note includes a resolution that the observed behavior of a net price being automatically set to 0 is actually the expected result because the "Indicator: Invoice receipt" field was not set. To remedy this, users must set this field, which should then reflect the provided net price correctly. An accompanying image within the note likely provides a visual guide or example for reference.

Additionally, the See Also section suggests related topics or terms such as Net price, LTMC, POs, open POs, and others that could be helpful for users experiencing this issue or requiring further information on related subjects.