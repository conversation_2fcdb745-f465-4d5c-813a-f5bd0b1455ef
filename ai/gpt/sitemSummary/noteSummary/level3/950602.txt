SAP Note 950602 addresses performance issues when executing a query that contains many characteristics and attributes, as well as a large number of data cells using the Java Web runtime. The note identifies that the high runtime is predominantly due to the timing of the BICS_PROV_GET_INITIAL_STATE function module as indicated by Java statistics (referenced in SAP Note 948158).

The main reasons for the prolonged runtime include the time-consuming process of transmitting the initial query status via Remote Function Call (RFC).

To mitigate this performance problem, SAP advises the following solutions:

1. Reduce the number of characteristics navigable in the query to lighten the load and improve performance.
2. Implement specific Support Packages for SAP NetWeaver 2004s BI. Customers are advised to import Support Package 09 (BI Patch 09 or SAPKW70009) into their BI systems for improvements. This package should be considered once SAP Note 914303, which provides more details on the package, is released to customers.
3. For immediate concerns, correction instructions can be applied.
4. The note indicates that the corrections provided in Support Package Stack 7 have limited effects and may not be adequate in many cases. In contrast, corrections in Support Package Stack 8 are expected to significantly improve performance. Further improvements are anticipated in Support Package Stack 9.

Additionally, if Support Package Stack 9 does not resolve the issue satisfactorily, SAP requests customers to provide Java statistics as mentioned in Note 948158, and an OLAP trace as outlined in Note 899572 for further analysis.

It is also mentioned that the referenced notes may be available in a preliminary version prior to the release of the Support Package. Users should look for the phrase "Preliminary version" in the short text of the note to recognize this status.