SAP Note 333128 addresses a specific issue that arises for customers who are using the 'Cable Solution' Add-On in their sales document processing. The problem occurs when there is a change in the partner function (e.g., sold-to party, ship-to party, payer, bill-to party) within a sales document. Normally, the system prompts the user to confirm if the default values should be redetermined and updated in the existing schedule lines, but this prompt causes issues when orders are being processed through batch input or during the import of IDocs, leading to a termination.

The note provides a solution to prevent this prompt from appearing in batch input scenarios, ensuring that the default values are automatically redetermined and written to the schedule lines without user interaction. Additionally, a user exit (SAP enhancement J_2CSD09 and function module EXIT_/CAB/MV45A_003) is mentioned, which allows for customization of this behavior according to the user's needs.

The cause of this issue is identified as a program error, and the solution involves importing a program correction. The note instructs customers to import the transport request CS8K001145 following the guidance provided in SAP Note 13719.

Keywords and transactions mentioned in this note include VA01, VA02 (sales document creation and change transactions), default values, partner functions, IDocs, and batch input. The resolution provided in this note is intended to streamline the sales order processing for customers using the Cable Solution Add-On by eliminating disruptions due to unnecessary prompts during batch processing.