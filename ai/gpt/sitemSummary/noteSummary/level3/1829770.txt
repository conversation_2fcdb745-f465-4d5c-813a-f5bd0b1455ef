SAP Note 1829770 addresses an issue with the authorization check in SAP NetWeaver Business Warehouse (BW) when performing data archiving or restoration operations to near-line storage (NLS) using an existing data archiving process (DAP). Specifically, the system incorrectly demands the authorization object BI_ALL, which is normally associated with BW reporting activities, rather than the appropriate authorization checks for data retrieval tasks within the Administrator Workbench (AWB).

The root cause of the problem is identified as a program error, where the system calls the RSEC_AUTHORITY_CHECK_IPROV module, which is meant for BW reporting authorization checks, instead of the correct module RSSM_AUTHORITY_CHECK, which is intended for AWB activities.

The solution provided in the note involves a correction that, once implemented, will ensure that authorization checks for archiving activities are performed against the correct authorization objects: S_RS_CUBE for InfoCubes and S_RS_ODSO for DataStore Objects (DSOs).

Users are advised to import the relevant Support Package for their specific version of SAP NetWeaver BW to fix the problem. The different versions and corresponding Support Packages are:

- For SAP NetWeaver BW 7.00, import Support Package 31 (SAPKW70031).
- For SAP NetWeaver BW 7.01 (Enhancement Package 1), import Support Package 14 (SAPKW70114).
- For SAP NetWeaver BW 7.02 (Enhancement Package 2), import Support Package 14 (SAPKW70214).
- For SAP NetWeaver BW 7.30, import Support Package 10 (SAPKW73010).
- For SAP NetWeaver BW 7.31 (Enhancement Package 3), import Support Package 08 (SAPKW73108).
- For SAP NetWeaver BW 7.40, import Support Package 03 (SAPKW74003).

The note also provides references to other SAP Notes with detailed information about these Support Packages and mentions that in urgent situations, correction instructions can be implemented in advance. Furthermore, it advises users to read SAP Note 875986 for guidance on using transaction SNOTE, which is the standard tool for implementing correction instructions.

Finally, the note mentions that the related SAP Notes providing further details on the Support Packages may already be available before the actual release of the packages, and in such cases, the short text of the SAP Note will contain the phrase "Preliminary version."