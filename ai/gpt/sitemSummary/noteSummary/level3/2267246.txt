SAP Note 2267246 addresses the simplification of MRP (Materials Requirements Planning) fields in both Material Master and Article Master as part of the system conversion to SAP S/4HANA. The note outlines changes in various tabs and fields in transactions MM01/02/03 and MM41/42/43, and how these changes impact the configuration of MRP related data.

The simplifications include:
- In the MRP1 tab, the "Unit of Measure Group" (MARC-MEGRU) field is mentioned.
- In the MRP2 tab, "Quota arr. usage." (MARC-USEQU) field is no longer required since MRP always considers quota arrangements.
- Various fields in the MRP4 tab for BOM explosion/dependent requirements, Repetitive manufacturing/assembly strategy, and Storage Location, such as "Selection Method" (MARC-ALTSL), "Action control" (MARC-MDACH), "fair share rule" (MARC-DPLFS), "push distribution" (MARC-DPLPU), and "Deployment horizon" (MARC-DPLHO) along with Storage Location specific fields like "SLoc MRP indicator" (MARD-DISKZ), "spec.proc.type SLoc" (MARD-LSOBS), "Reorder Point" (MARD-LMINB), and "Replenishment qty." (MARD-LBSTF).
- The field "Action control" (MARC-MDACH) is available from SAP S/4HANA 2020, but requires no action and has no impact on existing data.

For the Article Master in transactions MM41/42/43, similar simplifications exist as those described for the Material Master.

The scheduling margin key (MARC-FHORI) is also discussed. In SAP S/4HANA, it is mandatory if defined in transaction code OMSR but is not mandatory by default. This contrasts with SAP ECC, where the scheduling margin key was mandatory based on MRP type, independent of customizing in OMSR. This reflects SAP’s effort to simplify material master maintenance in S/4HANA.

Retail customers need not adapt the usage of fields MARC-MEGRU and MARC-MDACH as the field is available from S/4HANA 2020 without requiring any action or changes to existing data.

For more information and to understand the implications of these simplifications, SAP Note 2224371 is referenced as providing general information about this Simplification Item. Furthermore, the note assures that the backend database fields related to "omitted functionality" still exist in the system.

In summary, the note details the simplifications and changes made to certain MRP fields during the transition to SAP S/4HANA, outlining which fields are affected, the actions required (if any), and the underlying changes to system behavior, specifically emphasizing SAP's intent to streamline master data management within the new system environment.