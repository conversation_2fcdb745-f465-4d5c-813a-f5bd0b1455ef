SAP Note 712875 addresses two main issues encountered during the batch input process for creating facilities (transaction TM_61). Here's a summary of the note:

**Symptom:**
1. Executing batch input in the background for facility creation causes the system to generate a dump.
2. An error message stating "Field VTS_ASGN_LIMIT_PROFILE-LIMIT_POS_AM . does not exist in screen SAPLFTR_FC 1100" appears when running a batch input session.

**Other Terms:**
- Batch, background, control

**Reason and Prerequisites:**
- The issue is related to SAP Note 311440, which discusses how accessing front end controls in batch input processing leads to a dump. However, for facility creation, this issue can be partially avoided. The transaction TM_61 doesn't support batch processing fully, and the field name for the drawing amount is too long, causing problems.

**Solution:**
- Apply the corrections provided in the note or import the corresponding support package to address the first problem.
- Also, implement SAP Note 668107 in the system.
- To resolve the second problem, locate the field VTS_ASGN_LIMIT_PROFILE-LIMIT_POS_AM in your recording and replace it with VTS_ASGN_LIMIT_PROFILE-LIMIT_POS_AMOUNTX wherever it appears, and then save the changes.

In summary, the note provides solutions to avoid dumps during batch input for facility creation and corrects an error related to a field name on screen SAPLFTR_FC 1100 by suggesting changes to the field name in the recording of the batch session.