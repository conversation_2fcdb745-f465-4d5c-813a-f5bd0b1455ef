SAP Note 1827823 addresses an issue specifically related to SAP HANA systems where the properties of a DataStore Object (DSO) within a HybridProvider experience two symptoms:

1. If a HybridProvider is created based on an existing DSO, the property "Optimized for SAP HANA" is incorrectly activated. This should not be the case.
2. The "Optimized for SAP HANA" property of a DSO cannot be modified using the standard maintenance transaction.

The underlying cause for these issues is identified as a program error. This note applies only if the SAP system's database platform is SAP HANA.

To resolve this problem, the note provides solutions for different versions of SAP NetWeaver Business Warehouse (BW):

1. For SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010), which is further described in SAP Note 1810084.
2. For SAP NetWeaver BW 7.31: Import Support Package 08 (SAPKW73108), with more details available in SAP Note 1813987.
3. For SAP NetWeaver BW 7.40: Import Support Package 03 (SAPKW74003), as elaborated in SAP Note 1818593.

In urgent situations, there is a provision to implement the correction instructions as an advance correction.

Additionally, the note advises users to read SAP Note 875986, which contains information about using transaction SNOTE for implementing corrections.

The note also mentions that before the support packages are officially released, the associated SAP Notes might be available in a "Preliminary version." Hence, users should look out for this phrasing in the short text of those notes to understand that the official release is still pending.