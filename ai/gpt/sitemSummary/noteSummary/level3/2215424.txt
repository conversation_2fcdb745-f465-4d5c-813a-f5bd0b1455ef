SAP Note 2215424 provides information on extending the material number field length from 18 to 40 characters in SAP S/4HANA, starting with the on-premise edition 1511. This extension impacts various aspects of an SAP system, including database fields, internal coding, external interfaces, and the compatibility with processes that rely on material numbers.

The note explains that corresponding SAP development entities, such as domains, data elements, and interfaces, have been adapted to accommodate the longer material numbers. Special attention must be given to ensure that data is not truncated and lost due to the previous 18-character limitation.

Customer and partner code that uses material numbers will also need to be analyzed and possibly adapted to support the increased length. Automated logic is in place to execute necessary conversions during system conversions to SAP S/4HANA.

The note further discusses how to handle interfaces commonly called remotely (such as BAPIs, IDocs, and RFCs), emphasizing the need for maintaining compatibility with systems that may not immediately handle the new 40-character length. For integration techniques that require a fixed-length data structure, additional fields have been introduced at the end of the data structures to support the transmission of longer material numbers.

There are different behaviors of the system depending on whether the extended material number functionality is activated or not. Without activation, the system restricts usage to the original 18-character limit, ensuring backward compatibility with external communication methods. With activation, there is no guarantee that the data can be transmitted using the old shorter fields, and communication partners must adjust accordingly.

The note highlights that internally, SAP system coding should only use the newly added extended fields, whereas when calling released external APIs, compatibility is not necessary within the same system.

Finally, the note provides references to additional notes for further details on data migration and the specific changes applied to accommodate the technical length extension of the material number and other extended fields.