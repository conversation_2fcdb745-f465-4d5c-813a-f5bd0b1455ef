SAP Note 3067314 provides guidance on how to handle multiple addresses in SD documents using the SAP Business Partner functionality in SAP S/4HANA 2021 or higher. The SAP Business Partner is the main master data object for both customers and suppliers, and this note specifically relates to applications that are adopting aspects of the SAP Business Partner data model from the classical ERP customer and supplier master data models.

Key points from the note include:

- The note acknowledges the shift from the classical ERP customer and supplier master data models to the SAP Business Partner data model that occurs with the SAP S/4HANA 2021 on-premise release.
- It announces the availability of new business functions for multiple address handling for both customer master data and business partners in sales and distribution within the SAP S/4HANA 2021 release.
- The note links to Product Assistance for the functionality on the SAP Help Portal for more guidance.
- It acknowledges the presence of restrictions on the functionality and refers to SAP Note 3081750 for details on these restrictions.
- It provides reference to a collective note (SAP Note 3086514) which includes information on errors and their fixes related to multiple address handling.
- The note includes attachments with additional guides: a Configuration Guide, a Master Data Guide, and a Master Data Migration Guide, which deliver further information on customization settings, enhanced master data capabilities, and data migration steps related to multiple address handling, respectively.
- It also mentions that there will be a Custom Code Adaptation Guide, which is not yet available but will address the impacts on custom code and provide guidelines for its adaptation.

In summary, this central note serves as a hub for resources and information about adopting the new functionality of multiple address handling in SAP S/4HANA 2021 using the SAP Business Partner framework and contains various references to additional detailed guides for implementation.