SAP Note 1839523 is a subsequent correction related to SAP Note 1811734. It addresses an issue where filtering the postable part of a postable node in a query using a temporal hierarchy join leads to the filter being ignored, or only the node itself being filtered. Additionally, the issue might cause a short dump with the error ASSIGN_LENGTH_NEGATIVE in the SAPLRRSI program.

Key Points:
- This note is designated for SAP NetWeaver Business Warehouse (BW) versions 7.30, 7.31, and 7.40.
- The root cause is identified as a program error.
- The solution to this problem is to import specific Support Packages depending on the BW version:
  - For BW 7.30: Import Support Package 10 (SAPKW73010) that is detailed in SAP Note 1810084.
  - For BW 7.31: Import Support Package 08 (SAPKW73108) that is detailed in SAP Note 1813987.
  - For BW 7.40: Import Support Package 03 (SAPKW74003) that is detailed in SAP Note 1818593.
- The mentioned Support Packages should become available upon the release of the respective SAPBWNews Notes.
- If the issue is urgent, the note advises implementing the correction instructions as an advance correction.

Before proceeding with the implementation, SAP Note 875986 should be read for information on using transaction SNOTE. Additionally, preliminary versions of the mentioned SAP Notes might be available before the official release of the Support Package, indicated by the words "Preliminary version" in the short text of the Note.

In conclusion, this note provides guidance on how to resolve a specific filtering error within SAP BW queries using temporal hierarchy join and directs the user to applicable support packages and correction instructions to address the problem.