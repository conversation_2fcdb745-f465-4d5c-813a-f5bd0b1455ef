SAP Note 2726010 provides information on how the SAP S/4HANA Migration Cockpit supports custom fields that are added through the key-user extensibility framework, also known as in-App extensibility.

Key points from the note include:

1. The note states that it is outdated, and users should refer to the latest information within the migration object documentation and the Available Migration Objects list in the SAP Help Portal. The links provided direct users to the relevant pages for both SAP S/4HANA Cloud and SAP S/4HANA on-premise.

2. Custom fields can be included in migration objects only if the API used for data migration is associated with a business context in the extensibility framework.

3. The note specifies that if a migration object is listed in the accompanying table (omitted here) and the business context related to custom fields is supported, then the SAP S/4HANA migration cockpit will automatically incorporate custom fields into the migration template.

4. However, the Migration Cockpit does not offer value mapping for custom fields. Users must ensure that the values entered into the migration template or staging tables match the properties of the custom fields and are semantically correct.

5. Any issues with custom fields must be addressed to the component responsible for the custom field within the migration API.

6. The note lists migration objects (such as Commercial Project, Customer, Supplier, etc.) that are supported through the extensibility framework, along with their respective business context, responsible components, and the SAP S/4HANA Cloud and SAP S/4HANA release versions in which they are available.

7. It also includes a disclaimer that the information regarding future SAP software releases is subject to change, and those releases are to be considered with this in mind.

In summary, SAP Note 2726010 details the support and considerations for using custom fields within the SAP S/4HANA Migration Cockpit and includes references to the most current documentation. Users should verify that migration objects and custom fields conform to the requirements and refer to the SAP Help Portal for the most recent information.