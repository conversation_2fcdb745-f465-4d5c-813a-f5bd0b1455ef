SAP Note 1000077 is an FAQ that addresses common issues encountered while implementing the report from SAP Note 32236. Below is a summary of the solutions provided in Note 1000077:

1. Ignore error messages about lines being too long (truncated at 72 characters) or syntax errors in the programs during the import process.

2. The transport is successful if the programs and the respective database tables (MMINKON, MMINKON_UP) exist in the system.

3. If you encounter import errors like "Probably the data file was destroyed during file transfer!" or "ENTRY_SIZE is too short", update to the current transport version of R/3 trans or kernel, and refer to Note 454321 for Release 4.5B.

4. If syntax errors concerning the "MMINKON_UP-OWNER" field occur in MB* reports, re-import the transport using unconditional modes U19. This issue may arise due to an old inconsistency tool version imported into the system. See Note 13719 for more information.

5. For release levels lower than 4.6C that encounter syntax errors, see Note 989970.

6. Do not execute the reports from Note 32236 directly since results can be misleading and need interpretation. Instead, use SAP standard transaction MB5K to check materials, and for error 045, refer to Note 1026379.

7. For analysis and correction of data inconsistencies, support teams require a user master record with sufficient authorization, including debugging authorization ('S_DEVELOP', 'DEBUG', and activity '03'), Debug&Replace authorization, and authorization for all transactions in package MB and SM*/SE* system transactions.

8. Note 32236 cannot be implemented using transaction SNOTE because it does not contain correction instructions. Transport files must be downloaded from the note attachment and transported into the system manually, as explained in the long text of Note 32236.

Other Terms mentioned are related to various transactions, reports, and tables linked to the issues discussed in this SAP Note.

This SAP Note does not include a Reason and Prerequisites section or a Solution section, as it specifically addresses FAQs related to implementing a specific report from Note 32236.