SAP Note 398396 addresses an issue in SAP BW (Business Warehouse) where user-defined validity intervals entered through Transaction RSDV are not displayed when the transaction is revisited, nor are these intervals considered when executing a query.

The cause of the issue is a program error in the function group RSDV that affects the system's ability to read records during read operations, despite the fact that these records are successfully written to the validity table upon saving.

The solution to this problem is to import the appropriate Support Package depending on the SAP BW version being used. For BW2.0B, users need to import Support Package 15 (SAPKW20B15), which will be available once Note 0374844 titled "SAPBWNews BW 2.0B Support Package 15" is released for customers. Until the release of Note 0374844, its short text will indicate it's a "preliminary version."

For BW2.1C, users should import Support Package 07 (SAPKW21C07), availability of which will be indicated by the release of Note 384999 titled "SAPBWNews BW 2.1C Support Package 07."

Additionally, for more information on BW Support Packages, users should refer to Note 110934.