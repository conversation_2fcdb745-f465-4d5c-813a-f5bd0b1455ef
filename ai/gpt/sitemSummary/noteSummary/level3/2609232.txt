The SAP Note 2609232 addresses a software issue where catchup revenue is incorrectly calculated for Performance Obligations (POBs) using manual spreading during the transition phase of revenue accounting. This problem arises under specific conditions:

1. The source POB has manual spreading with zero values for periods after the takeover date, and its revenue is fully recognized before the takeover date.

2. During the migration phase, a target POB is created with identical end date and manual spreading as the source POB using the FARR_RAI_PROC_NEWACP transaction.

3. In the transition phase, any change to the allocated amount of the target POB results in a spreading conflict due to its manual spreading settings.

4. When the allocated amount is reverted to the original value, resolving the spreading conflict, catchup revenue calculation issues occur on the reconciliation key of type T (transition). This issue is observed as a discrepancy in revenue between the period 2017012 and the migration revenue.

The note clarifies that the transaction FARR_TRANS_CATCHUP, which normally would not calculate catchup revenue for a POB with manual spreading, is not related to this issue. Instead, there are two reasons for the problem:

1. A dummy fulfillment is generated incorrectly for POBs with manual spreading.

2. The revenue recalculation method FINALIZE_REV_FOR_CHG_DEFITEM( ) is executed incorrectly for POBs with manual spreading when the migration quantity and revenue are mismatched.

The solution proposed in this SAP Note is to apply the Note itself to rectify the problem.