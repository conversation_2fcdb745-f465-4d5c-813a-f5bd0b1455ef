SAP Note 956305 addresses an issue in BI 7.0 where the filter values in the BW 3.x front end are always sorted according to the key. This behavior is identified as a program error.

To resolve the issue, the note recommends importing Support Package 09 for SAP NetWeaver 2004s BI (also referred to as BI Patch09 or SAPKW70009) into the BI system. The details of this Support Package are further described in SAP Note 0914303 titled "SAPBINews BI 7.0 SP09".

For those who require an immediate fix and cannot wait for the Support Package release, the note suggests using the provided correction instructions. It is also mentioned that the related notes may offer preliminary information before the official release of the Support Package, indicated by the term "preliminary version" in the short text.