SAP Note 1850370 addresses a specific problem encountered in SAP NetWeaver Business Warehouse (BW) where a time-out error occurs during a repair attempt using the RSRV (BW System Data Consistency Check) tool.

**Symptom:**
When performing the RSRV check 'Compare Size of P-, Q- with X- and Y- Tables respectively' on master data, if errors are returned and a repair is attempted, the system may dump with a TIME_OUT error.

**Other Terms:**
List of key terms related to this note:
- RSRV: A tool used for consistency checks in BW.
- Aggregate: A summarized dataset in BW that improves query performance.
- TIME_OUT: An error that occurs when a process takes too long to execute.
- TSV_TNEW_BLOCKS_NO_ROLL_MEMORY, AGGRCAT_READ_DB: Technical terms related to the error scenario.

**Reason and Prerequisites:**
The issue arises when there is an existing aggregate that contains navigational attributes of the InfoObject that failed the RSRV check. If this aggregate also has dependent child aggregates, it can lead to an endless loop resulting in a time-out.

**Solution:**
The problem is identified as a program error. The note suggests importing the respective Support Package to correct the error based on the SAP NetWeaver BW version:

- For BW 7.30: Import Support Package 10 (SAPKW73010).
- For BW 7.31: Import Support Package 9 (SAPKW73109).
- For BW 7.40: Import Support Package 3 (SAPKW74003).

The availability of these Support Packages is contingent on the release of other related SAP Notes (1810084 for 7.30, 1847231 for 7.31, 1818593 for 7.40), which provide more details on the Support Packages.

In urgent situations, correction instructions are available, and users are advised to check SAP Note 1668882 related to transaction SNOTE for additional information.

Implementing the solution involves applying the Support Package or using the correction instructions provided. Users should note that the Support Package mentioned might be in a "preliminary version" before its official release.