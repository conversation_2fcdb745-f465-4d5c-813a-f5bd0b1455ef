SAP Note 2422224 addresses the integration of long material numbers from SAP S/4HANA into SAP BW or SAP BW/4HANA systems and provides guidance depending on whether the extended material number functionality is used in SAP S/4HANA. Here's a summary:

Symptom:
The note provides guidelines for scenarios where long material numbers from SAP S/4HANA need to be extracted into a SAP BW or SAP BW/4HANA system.

Scenarios:
1. Not using extended material number functionality (material numbers are <= CHAR 18). It's suggested to consider the steps for scenario 2 even if extended functionality is not currently in use but may switch on later.
2. Using extended material number functionality (material numbers can be longer than CHAR 18 and <= CHAR 40).

Reason and Prerequisites:
In SAP S/4HANA, the material number domain (MATNR) is extended to CHAR 40. SAP BW extractors have been updated accordingly. Specific prerequisites are outlined in the solution section.

Solution:
- Scenario 1: Material numbers <= CHAR 18. No extra actions required for new systems in SAP BW versions 7.30/7.31/7.40/7.50/7.51 and BI Content 7.37/7.47/7.57. For upgraded systems, DataSources and transformations may need reactivation but no data conversion is necessary. For SAP BW/4HANA 1.0, InfoObjects with conversion exit MATN1 are delivered as CHAR 40 but can be adjusted to CHAR 18 if desired.

- Scenario 2: Material numbers <= CHAR 40. For SAP BW 7.30/7.31, only InfoObjects without MATN1/MATNB conversion can hold long material numbers. Later BW versions (7.40/7.50/7.51) and BI Content versions (7.47/7.57) support extraction of material numbers longer than 18 characters using conversion exit MATNB, with certain prerequisites and reactivation of affected objects. No data conversion is necessary.

- For SAP BW/4HANA, installation of SAP BW/4HANA 1.0 SP02, along with related SAP Notes, is required. Affected objects may need reactivation. InfoObjects with MATN1 need to be updated to a data type of CHAR length 40.

Additional Instructions:
New DataSources are available for text extraction when using long material numbers with specific InfoObjects: 0MAT_PLANT_LM_TEXT, 0MAT_SALES_LM_TEXT, 0MAT_ST_LOC_LM_TEXT. The previous DataSources should be used only for material numbers <= CHAR 18.

This SAP Note communicates the necessary steps for ensuring material number data is accurately extracted and integrated between SAP S/4HANA and SAP BW or SAP BW/4HANA systems, with particular attention paid to the changes in handling material numbers of varying lengths.