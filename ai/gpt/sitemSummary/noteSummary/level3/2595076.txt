SAP Note 2595076 addresses an error that occurs when processing incident-related offline forms within the SAP Interactive Forms by Adobe component of the Incident Management module. The error results in a short dump when a user tries to open a work item in their inbox application, such as a worklist or an incident.

The root cause of this issue is a mismatch between the date format settings of the user who processes the work item and the settings of the user who created the form (often a workflow batch user). Since these forms typically have hidden timestamps, the discrepancy in date format settings causes inbound processing to fail.

To resolve this problem, the SAP Note provides correction instructions that eliminate the hidden timestamps from the form and from the form interface when the user who created the form has different date format settings from the specified country during form creation. If the formats match, the timestamps remain but are hidden.

Additionally, a new check is introduced during inbound processing that generates an error message when there's a mismatch in date format settings between the form and the form processor. This message provides further details about the issue and possible solutions.

However, the correction will not fix forms that have already been sent or received but not processed. For open workflow tasks, users are advised to temporarily adjust their date format settings in their user profile using transaction code SU01.

The SAP Note concludes by providing a list of Support Packages containing the necessary corrections and offers the option to manually implement the correction instructions if preferred.