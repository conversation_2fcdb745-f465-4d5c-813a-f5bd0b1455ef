SAP Note 2470352 addresses the migration of 3.x data flow objects, such as Update Rules, InfoSources, Transfer Rules, DataSources, Transfer and Communication Structures in the context of transitioning to SAP BW/4HANA or SAP Datasphere, SAP BW bridge. During the migration, certain objects must be replaced, while others need to be adapted.

Key points from the note:

- Objects that need replacing:
  - Update Rules
  - 3.x InfoSources
  - Transfer Rules
  - 3.x DataSources
  - Transfer and Communication Structures

- Objects that need adaptation:
  - InfoPackages
  - Process Chains
  - Process Chain Variants
  - VirtualProviders based on 3.x InfoSource

The note also states that the 3.x data flow migration tool (transaction RSMIGRATE) is not available in SAP BW/4HANA. For migration, users are advised to run the pre-check program RS_B4HANA_RC to determine the availability and convertibility of objects.

For versions below SAP BW 7.3, migration is manual, and hierarchy DataSources with corresponding transfer rules cannot be migrated automatically. It is emphasized that the migration from 3.x to 7.x data flows must be performed prior to using the SAP BW Transfer Cockpit for conversion to SAP BW/4HANA or SAP BW bridge compatible objects.

Related documentation links are provided, detailing the migration process for different versions of SAP BW (7.00 to 7.50), including a best practice guide for data flow migration.

Abbreviations such as UPDR, DUPD, ISCS, DSCS, ISTD, DSTD, ISTS, SHTR, ISMP, SHMP, ISFS, SHFS, ISIP, SHIP, RSPC, DSPC, RSPV, CUBE, DCUB are referenced, possibly as the technical names for the various objects and processes involved in the migration.