SAP Note 3262018 addresses an issue where long texts exceeding 132 characters were being truncated without any error messages when imported into maintenance orders. This problem occurred because the field designated to receive the text only allowed for 132 characters, but there was no corresponding validation to check the length of the text in the sender field.

To resolve this, a validation rule named CHECK_MOVE_RULE has been introduced. This rule will ensure that any attempt to migrate text lines exceeding 132 characters will trigger an error message, alerting the user to the issue.

The solution for this problem has been provided in the form of an implementation of a Transport-based Correction Instruction (TCI) Note, specifically Note 3276342. For customers affected by this issue, they will need to apply this TCI Note if they are using the 2022 release of the software at Service Pack level SP01.