SAP Note 1858595 addresses a conflict that arises when SPNego (Simple and Protected GSS-API Negotiation Mechanism) is enabled for Kerberos-based Single Sign-On (SSO) on a NetWeaver Gateway system, which simultaneously serves OData services accessed by HTTP clients such as a Mobile Cloud Platform or Mobile App.

**Symptom:**
HTTP clients attempting to call Gateway OData services receive a 401 Unauthorized response code on their first request if SPNego is enabled on the server.

**Other Terms:**
The issue is related to SPNego configuration and OData services.

**Reason and Prerequisites:**
The prerequisite condition for the issue is that the server is already configured for SPNego-based authentication. The reason behind the 401 error is that the HTTP request header contains attributes signaling the need for a Kerberos ticket, and if the client cannot perform SPNego authentication, it fails with an unauthorized error because it does not respond as expected for SPNego-enabled clients.

**Solution:**
There are several ways to address this issue:

- **Workaround:** Disable SPNego on the OData ICF (Internet Communication Framework) services within the SICF configuration. This approach is simple and resolves the immediate problem.

- **Solution 1:** Modify the HTTP client so that it handles the 401 server responses correctly.

- **Solution 2:** Application developers can bypass SPNego authentication for OData service calls by adding a parameter "spnego=disabled" to the service URLs or by including a field "spnego" with the value "disabled" in GET requests.

In cases where customers still require the use of SPNego with their OData services, they should implement additional configurations as per Solution 1 or Solution 2. If that's not possible, they should consider entirely disabling SPNego for OData ICF services or solely rely on Solution 2.