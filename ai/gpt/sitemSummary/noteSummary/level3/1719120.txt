SAP Note 1719120 addresses an issue in General Ledger Accounting (new) as of Enhancement Package 3 (EHP 3) where the elimination profit center is incorrectly filled in some lines of an FI document, contrary to the expectations set forth in SAP Note 826357.

Key aspects of the note include:

Symptom:
In the new General Ledger Accounting, the elimination profit center is filled in some lines of FI documents, which is not in line with the documentation provided in SAP Note 826357.

Other Terms:
The note lists keywords related to the issue, including "COPCA_PARTNER_GSBER_PRCTR," "EPRCTR," "FI-GL (new)," and "OCCL," which may be used for searching the issue or related solutions in SAP systems.

Reason and Prerequisites:
This issue occurs due to the following conditions:
- General Ledger Accounting (new) is activated.
- The business function FIN_GL_CI_1 is active.
- The user is employing the read function of transaction OCCL to set the partner profit center.
- The user intends to set the elimination profit center based on their own criteria using the Business Add-In (BAdI) FAGL_DERIVE_EPRCTR.

Solution:
The note proposes the following resolutions:
- Implement the advance corrections provided with this note.
- Additionally, implement the corrections from the subsequent SAP Notes 1826940 and 2733583.

After the solutions are applied, the elimination profit center should no longer be set incorrectly during the determination of the partner profit center using transaction OCCL when the specified conditions are met (General Ledger Accounting (new) active with business function FIN_GL_CI_1).

As SAP Notes are frequently used to detail specific issues and solutions within the SAP environment, users encountering this problem should follow the instructions and corrections provided in this SAP Note to resolve the unwanted behavior in their systems.