SAP Note 1825611 addresses an issue where error messages are produced when executing input help or reading master data in SAP NetWeaver Business Warehouse (BW). The error messages indicate that view attributes 0TXTSH_KEY, 0TXTMD_KEY, or 0TXTLG_KEY are not found for cube index with a suffix "_F4".

The cause is identified as a program error, which specifically arises when an InfoObject uses 0TXTSH, 0TXTMD, or 0TXTLG as an attribute.

To resolve the issue, the note suggests the following solutions based on the version of SAP NetWeaver BW:

1. For BW 7.30: Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010), which will be detailed in SAP Note 1810084 titled "SAPBWNews NW 7.30 BW ABAP SP10".

2. For BW 7.31: Import Support Package 08 for SAP NetWeaver BW 7.31 (SAPKW73108), described in SAP Note 1813987 "SAPBWNews NW BW 7.31/7.03 ABAP SP8".

3. For BW 7.40: Import Support Package 03 for SAP NetWeaver BW 7.40 (SAPKW74003), with further details provided in SAP Note 1818593 "SAPBWNews NW BW 7.4 ABAP SP03".

In urgent situations, the correction instructions can be applied as an advance correction prior to the release of the support packages.

Additionally, SAP Note 875986 should be read first to understand how to use transaction SNOTE. It's important to note that the SAP Notes mentioned above may be available even before the official release of the respective Support Package, and in such cases, they may be labeled as a "Preliminary version".

After implementing the advance correction, the system administrators must also execute the report RSDDB_LOGINDEX_CREATE for the relevant characteristic.