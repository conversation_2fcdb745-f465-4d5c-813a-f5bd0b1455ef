SAP Note 2181745 addresses a syntax error that occurs when migrating Transfer Rules that include a 'Start routine' to a Transformation in various SAP BW versions. The issue is reported as a program error, and it is specifically identified by error message "Type 'TAB_TRANSTRU' is unknown." The symptomatic error is labeled with the term "RSTRAN527."

To resolve the issue, the note instructs users to import specific Support Packages for each affected SAP BW version. These versions and the corresponding Support Packages are as follows:

- For SAP BW 7.0, import Support Package 35 (SAPKW70035).
- For SAP BW 7.01 (SAP BW 7.0 EHP 1), import Support Package 18 (SAPKW70118).
- For SAP BW 7.02 (SAP BW 7.0 EHP 2), import Support Package 18 (SAPKW70218).
- For SAP BW 7.30, import Support Package 14 (SAPKW73014).
- For SAP BW 7.31 (SAP BW 7.3 EHP 1), import Support Package 17 (SAPKW73117).
- For SAP BW 7.40, import Support Package 13 (SAPKW74013).

Each Support Package is further referenced by other SAP Notes, which are to be released for customers providing more detail on the respective Support Package.

The note advises that in urgent situations, correction instructions can be used as a workaround. However, it is recommended to first check SAP Note 1668882 regarding the use of transaction SNOTE, which may contain preliminary information on the issue before the Support Package is officially released.