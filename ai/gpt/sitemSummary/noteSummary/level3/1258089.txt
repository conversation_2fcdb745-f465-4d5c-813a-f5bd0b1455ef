SAP Note 1258089 - Design rule: Adding records to the start routine

Summary:

This SAP Note provides guidelines for error handling when adding new records to the 'source_package' table during the start routine of a transformation process within an SAP system.

Symptom:
The note addresses a specific scenario where new records are being added to the 'source_package' table in the transformation start routine, which requires adherence to specific design rules for proper error handling and generation of error requests.

Other Terms:
None provided in the note.

Reason and Prerequisites:
Error handling must be active in the Data Transfer Process (DTP) for the note's guidance to be applicable.

Solution:
The note outlines that the table 'source_package' has a structure indicated by the field symbol <SOURCE_FIELDS>, which includes technical keys (REQUEST, DATAPAKID, RECORD). When adding new records, the following rules should be followed for the 'RECORD' field:

1. If new records are uniquely created from a single record in 'source_package', the 'RECORD' field should be filled with the value from the source record to ensure proper error tracing.

2. If a new record aggregates information from multiple source records, the 'RECORD' field should be set to initial (0) as the runtime environment does not support referencing several source records to a single added record. In case of an error, the added record will cause the request status to be marked as 'incorrect'.

3. For a new record that does not reference any source information, the 'RECORD' field should also be set to initial (0). Any error in this record will result in the request status being set to 'incorrect'.

The note suggests consulting the online documentation for the Data Transfer Process (DTP) to learn more about its error handling functionalities.
