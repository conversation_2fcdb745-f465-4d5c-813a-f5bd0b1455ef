This SAP Note 2418242 addresses a specific issue within the SAP S/4HANA Sales and Distribution (SD) component. The problem pertains to the handling of document relationships (successor, predecessor) in the VBFA database table, especially when linked SD documents have been archived. The symptom of the issue is that certain Sales Order Management dialogue fields remain open for data input when they should not be due to the status of subsequent documents.

The key points from this note are:

- The issue has been previously communicated in a Hot News Note 2369405.
- It provides a correction to the problem and details the removal of the archiving lock that was introduced with SAP Note 2369405, once the solutions from this note are applied.

The prerequisites for the relevance of this note vary based on the system version and support package level:

- SAP S/4HANA 1511 SP03 or below: always relevant
- SAP S/4HANA 1511 SP04 or higher: relevant if started at a lower SP or as a new installation on SP04
- SAP S/4HANA 1610 SP01 or below: always relevant
- SAP S/4HANA 1610 SP02 or higher: relevant with similar conditions as above

Upgrading to release 1709 requires the SD_VBFA_RECONSTRUCTION report to have been executed, which will be verified via the Software Update Manager.

The solution involves two main parts:

1. Correction of code
2. Reconstruction of the correct table content for the SD document flow using the report SD_VBFA_RECONSTRUCTION.

The implementation process is sequential:

- Ensure correction from Note 2452775 is installed.
- Execute report NOTE_2418242 (from Note 2452775).
- Implement all correction instructions from Note 2418242 and additionally from SAP Note 2460076.
- Plan and execute the report SD_VBFA_ARCHIVE_ANALYZE as a batch job to verify prerequisites.
- If the analysis shows issues (red lines), contact SAP Support; if not, proceed.
- Execute the report SD_VBFA_RECONSTRUCTION with default values, preferably as a background job.
- Successful execution of SD_VBFA_RECONSTRUCTION resolves the issues and removes the archiving lock.

To summarize, this note provides the necessary steps and prerequisites to correct an SD document flow issue in SAP S/4HANA, related to archived documents and improper field access. It mandates running specific reports and potentially contacting SAP support for further assistance. After completing these steps, the constraints caused by previous notes are lifted.