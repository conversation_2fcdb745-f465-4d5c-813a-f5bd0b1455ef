SAP Note 2326302 addresses several corrections and functional enhancements related to Real Estate Management (RE-FX) with a focus on leasing processes. Here is a summary of the key points from the note:

1. The valuation simulation within the transactions RECN, RE80, and RECECN is now permitted even if there are unsaved changes or the rule status is set to "Incomplete."
2. A new parameter, CF_PROCESS_EXECUTION_ALLOWED, has been added to allow users to prevent a valuation from being executed under certain conditions, like for inactive contracts.
3. The contract object mentioned in SAP Note 2326200 is now supported in the context of the valuation process.
4. A compound interest calculation will be performed at specific interest calculation times when no repayment occurs, but this can be disabled with the new parameter CF_COMPOUNDED_INTEREST.
5. The system introduces a new "Reduction" valuation behavior.
6. Improvements have been made regarding the due date for payments.
7. Two new movement type relationships, CEA and CEB, have been added for handling special movements related to equity capital adjustment on the date of the first posting.

Before implementing SAP Note 2326302, it is necessary to ensure that SAP Notes 2326200 and 2326260 are already implemented in the system. It should be noted that changes in documentation, such as the F1 help, are only provided through Support Packages.

The SAP Note was issued due to legal requirements and advises implementing the attached correction instructions or importing the specified Support Package to resolve the issues. The note also includes other terms such as IFRS leasing, lease-in, leasing contract, valuation, right-of-use asset (RoU), new leasing standard, US GAAP, and HGB, which are related to the enhancements and corrections made.