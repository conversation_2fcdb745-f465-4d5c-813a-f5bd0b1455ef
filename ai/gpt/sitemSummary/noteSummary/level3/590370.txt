The SAP Note 590370 addresses issues related to performance problems in an SAP BW system, particularly those that arise due to having too many uncompressed requests in the F fact table of an InfoCube. These performance problems can manifest as long execution times for statistics updates or index rebuilds, intermittent ORA 14400 errors during data loads, and difficulties with aggregate builds.

Key points from this SAP Note include:

1. **Symptoms**: The note outlines that infocubes may exhibit non-specific symptoms such as long execution times for updates, ORA 14400 errors during loading, and issues with aggregate buildup.

2. **Reason and Prerequisites**: The primary cause of these issues is identified as having too many uncompressed requests in the F facttable, which corresponds to a high number of partitions and secondary index partitions in the database. This large number of database objects can lead to performance issues during statistics updates and index management.

3. **Partitioning Guidelines**: It is mentioned that a typical infocube's F facttable should not exceed 20-30 partitions. The note explains that excessive partitions in the F facttable can impair query performance because every partition must be accessed for each query. The E facttable can have more partitions due to time restrictions that limit the number of partition accesses.

4. **Solutions**: The main solution proposed is to frequently compress the infocube to reduce the number of uncompressed requests and thereby the number of partitions. The note advises on how to set up regular compression through various tools provided in SAP BW, such as the admin workbench of infocube management, automatic compression settings, and custom event or process chains.

5. **Analysis Tools**: The note suggests using specific reports and database views like SAP_DROP_EMPTY_FPARTITIONS, RSORAVDV, and DBA_PART_TABLES to analyze the number of partitions and the extent of the data they contain.

6. **Future Tools**: Mention is made of upcoming features in the next release of SAP NetWeaver BW (NetWeaver 2004s), including a repartitioning tool that will enable dynamic changes to a cube’s partitioning range and facilitate partitioning of previously unpartitioned cubes without reloading the data.

In summary, SAP Note 590370 provides guidance on how to deal with performance issues attributed to too many partitions in the F facttable of infocubes. It recommends regular compression of the infocube, provides tools to analyze current partitions, and previews future tooling that will simplify partition management.