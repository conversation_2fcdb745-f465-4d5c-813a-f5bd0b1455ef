SAP Note 2186803 discusses the treatment of NewGL tables and customer enhancements during an upgrade to SAP Simple Finance, on-premise edition. The note outlines the process and tools for handling custom enhancements in the NewGL tables as part of the upgrade.

Here is a summary of the key points:

1. During the upgrade, any customer enhancements made to the Standard SAP NewGL tables are considered and integrated into the compatibility views using a generation tool accessible via transaction FINS_MIG_REGENERATE.

2. Compatibility views for customer-specific NewGL tables are also generated by this tool, following defined naming patterns:
   - Line item tables receive names like ZFGLV_GLSI_C<number>.
   - Totals tables are named ZFGLV_GLTT_C<number>.

3. The numbering for these generated views and their associated tables can be located in the table FGLT_GLTAB.

4. Redirection is set up in the Data Dictionary to automatically divert selection from the NewGL tables to the relevant compatibility views, thus enabling data access from the new sources.

5. The original data in the NewGL tables can still be viewed using CDS-Views with the naming convention V_<tablename>_ORI.

6. After the upgrade to SAP Simple Finance is complete, it is no longer possible to extend the original NewGL tables.

The note provides important information for users who are upgrading to SAP Simple Finance and have previously made custom enhancements to the NewGL tables, ensuring continuity and accessibility of their data post-upgrade.