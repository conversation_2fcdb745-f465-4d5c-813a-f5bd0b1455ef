SAP Note 1854470 addresses an issue with poor performance in CompositeProvider queries within SAP NetWeaver Business Warehouse (BW) when there is a specific configuration involving compounding. The performance issue arises when a compounding parent is used in a join and the compounding child is only supplied with one part of the join.

The underlying cause of the problem is identified as a program error, where a false compounding inconsistency is detected, leading to the deactivation of hierarchy processing. Consequently, the field element selection (FEMS) and hierarchy processing may be executed on the application server instead of in the database, which negatively impacts performance.

To resolve this issue, the note provides a solution that requires the affected CompositeProvider to be reactivated after implementing the changes described in the note. The resolution involves importing the specified Support Packages, which contain the necessary corrections, into the system for different SAP NetWeaver BW versions:

- SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010), as detailed in SAP Note 1810084.
- SAP NetWeaver BW 7.31 (SAP NW BW 7.3 Enhancement Package 1): Import Support Package 09 (SAPKW73109), as detailed in SAP Note 1847231.
- SAP NetWeaver BW 7.40: Import Support Package 03 (SAPKW74003), as detailed in SAP Note 1818593.

For urgent cases where immediate resolution is required, the note allows for the implementation of the correction instructions as an advance correction. However, before doing so, users must familiarize themselves with SAP Note 1668882, which provides information on using transaction SNOTE for handling SAP Notes.

Furthermore, the note explains that preliminary versions of the mentioned SAP Notes might be available before the official release of the Support Package. These can be identified by the phrase "Preliminary version" in the short text of the note.

Overall, SAP Note 1854470 helps users to address and rectify the compounding-related performance issue in CompositeProvider queries by guiding them through the necessary steps to apply the appropriate Support Packages or advance corrections.