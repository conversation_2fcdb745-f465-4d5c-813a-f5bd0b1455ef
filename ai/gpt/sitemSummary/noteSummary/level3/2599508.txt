SAP Note 2599508 addresses a problem that occurs during the migration of a revenue accounting contract. The specific issue is that the allocation difference is not being copied correctly into the target contract when migrating from one accounting principle (A) to another (B).

The symptom is observed when executing the following steps:
1. Two operational documents are created: the first has two items with reference ID 'A', the second has one item with the same reference ID.
2. A revenue accounting (RA) contract is created from these documents. This contract includes three performance obligations (POBs), each of which has an allocation difference in accounting principle A.
3. The transaction 'FARR_RAI_PROC_NEWACP' is used to migrate the contract to accounting principle B.

The reason for the issue is due to how the migration transaction handles the selection and creation of the target RA contract. Initially, items from operational document 1 are selected and their conditions from accounting principle A are copied. When the item from the second document (the 3rd POB) is selected for combination into the RA contract, its condition from accounting principle A is also copied. However, due to the system status being 'migration', no price allocation is performed, resulting in the omission of input allocation difference for the 3rd POB.

The solution provided in this note is simply to apply the note, suggesting that the note contains either a correction or further instructions for mitigating this issue. Applying the note should resolve the allocation difference problem during the migration process.