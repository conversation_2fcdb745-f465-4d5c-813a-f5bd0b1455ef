SAP Note 1843687 addresses the issue where determining a where-used list for certain reusable query components (Calculated Key Figures, Restricted Key Figures, Structures, and Filters) takes an unusually long time. This issue also affects the after-import activation of a transport request that includes these components.

The root cause of this problem is identified as a program error. Specifically, the information about INFOCUBE assignment for a reusable query component is not being passed correctly to the function, resulting in the unnecessary selection of a large volume of data from the database table RSZELTXREF.

The solution provided involves importing specific support packages for different versions of SAP NetWeaver BW. A detailed list of the support packages corresponding to the various SAP NetWeaver BW versions (7.00, 7.01, 7.02, 7.11, 7.30, 7.31, and 7.40) is given, along with the note that these packages will be available after the release of respective SAP Notes describing them in more detail.

Furthermore, for urgent cases, the SAP Note suggests using correction instructions before the release of the Support Package. It advises checking SAP Note 1668882 for the transaction SNOTE before applying the correction instructions. The note may have a "preliminary version" short text if it's available before the Support Package release.