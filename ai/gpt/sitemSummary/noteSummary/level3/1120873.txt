SAP Note 1120873 addresses an issue with the stamp tax calculation in the Italian real estate flexible module (RE-FX) where all conditions of a contract are being considered for the calculation, even though not all tax groups are relevant to stamp tax.

Key points from this note:

**Symptom:**
- Stamp tax is incorrectly being calculated by considering all tax conditions, not just those relevant to stamp tax.

**Other Terms:**
- The issue concerns specifically the stamp tax ("imposta di bollo") in Italy within the SAP Real Estate Management (RE-FX) module.

**Reason and Prerequisites:**
- The system that's encountering issues must have the RE-FX module activated.
- All allowed tax groups must have been defined in the implementation guide (IMG) activity: "Create Tax Groups."

**Solution:**
- A new interface is provided so that users can choose which tax groups are relevant for the stamp tax.
- This solution was released in service pack 12 (SP12) for ERP 6.00.

**Installation Process:**
- **Manual Installation Actions:**
  - Create a new data element (RERAISBOLLO) with a type of RECABOOL and associated text descriptions for field labels of different lengths.
  - A new table (TIVRATAXBOLLO) with specific fields for client (MANDT), tax group (TAXGROUP) and a flag for stamp tax relevance (ISBOLLO) needs to be created.
  - A new view (V_TIVRATAXBOLLO) that includes the fields from the newly created table should be generated.

**Customizing:**
- Users will need to add an entry into the V_TIVRATAXBOLLO view which indicates the tax groups that are relevant for stamp tax.

This note gives instructions on how to address the stamp tax calculation issue and provides details on creating required elements within the SAP system to rectify the problem. Users are to follow these steps and refer to the given correction instruction, which is likely attached elsewhere, to resolve the issue.