SAP Note 1105139 addresses the scenario in which a user wants to prefill the OLAP cache with a query containing variables to optimize performance by avoiding direct reads from the InfoProvider. In BW 3.x systems, users could use a "contains pattern" placeholder (e.g., AB*) to prefill the cache with all values matching the pattern. However, in BI 7.0, this functionality is limited, and SAP explains how to achieve similar results.

Key Points from the Note:

1. Variables in queries can be either global (fixed) filters or dynamic filters. Global filters are used as keys to access cache entries and must match exactly. Dynamic filters can change during query navigation and allow partial results to be derived from cache entries.

2. To use variables as dynamic filters in BI 7.0, they must be defined in the "Default Values" area of the Query Designer. This allows them to be optional and selectable during the first query execution.

3. Steps to precalculate the cache with a large data set:
   - Define variables as dynamic and make input optional.
   - Place the characteristic for the variable in the query's start view for drilldown.
   - Precalculate the query, leaving the variable input field empty to capture all values for the characteristic.
   - After precalculation, entering a value for the variable will filter the results from the cache.
   - Use intervals instead of "contains pattern" to specify data selection areas for precalculation.

4. Users need to define a selection area with intervals (e.g., ["AB" to "ABZZ"]) because using the "contains pattern" is no longer supported in Release BI 7.0 for prefilling the cache.

5. The note also suggests checking SAP Note 1006905 for more information about precalculation.

In summary, the note provides a solution to work around the limitations for using "contains pattern" placeholders to fill the OLAP cache in BI 7.0 by setting variables as dynamic filters and using intervals for precalculation.