SAP Note 138704 details the policy on the provision of patches for SAP kernel releases. It states that rather than patching older R/3 kernel releases, SAP provides a downward-compatible kernel from a newer release. A kernel is considered downward-compatible if it can be used to correct errors in an R/3 release without requiring an upgrade of the R/3 system itself.

Once a downward-compatible kernel is available, SAP stops providing patches for the older kernel releases that can be replaced. The note includes a list with information about various kernel releases, their last patch levels, and the status of whether patches are still being provided or when maintenance ended.

The SAP Note lists the following kernel versions along with the last patch level and their maintenance status:

- Kernel 6.x series (e.g., 6.10, 6.20, 6.40), with specific dates mentioned for when no more patches were provided or maintenance ended.
- Kernel 4.6 series (e.g., 4.6A, 4.6B, 4.6C, 4.6D, and extensions), including final patch levels and the end of their maintenance periods.
- Kernel 4.5 series (4.5A, 4.5B, and extensions) and 4.0 series (4.0A, 4.0B, and extensions) with similar information as above.
- Kernel 3.x series (e.g., 3.0C, 3.0D, 3.0E, 3.0F, 3.1G, 3.1H, and 3.1I series including COM and EXT extensions), with details on the last patches and end of maintenance dates.

The note also references several other SAP Notes which provide additional information on the installation and compatibility of various kernel versions, such as:

- Note 664679 for installing the 6.40 kernel in SAP WEB AS 6.10/6.20.
- Note 318846 for installing the 4.6D kernel in 4.6A/B/C systems.
- Note 149682 for installing the 4.5B kernel with 4.5A-DB.
- Note 102461 and Note 154912 for information related to the installation and year 2000 information for 4.0B kernels.
- Note 327059 for the new 40B_COM kernel and supported platform combinations.
- Note 102445 and Note 319978 for installation and new kernel information regarding 3.1I kernels.

This SAP Note is essentially an informational notice informing customers of the end of patch support for older kernel versions and directing them to use newer downward-compatible kernels for error corrections.