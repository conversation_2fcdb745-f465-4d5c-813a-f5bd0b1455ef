The SAP Note 2432089 addresses a performance issue with the new read API, which involves unnecessary accesses to the older function modules K_COSPA_READ_MULTI and K_COSSA_READ_MULTI in some scenarios. The solution provided in this note is an enhancement to improve the performance of the new read API. Additionally, the note advises users to implement SAP Note 2451785 to resolve a problem related to the read mode "callback". Therefore, the users are prompted to apply the changes in SAP Note 2432089 and to also apply the corrections from SAP Note 2451785 to achieve the desired performance improvements and correct functionality.