SAP Note 2590180 addresses an issue where error messages are not being displayed for manually added Performance Obligations (POBs) when reprocessing Revenue Accounting Items (RAIs) for a new accounting principle using the transaction FARR_RAI_PROC_NEWACP. This issue occurs in the context of a source contract transition that includes a manually created POB.

The symptom of the problem is that the counterpart errors of the manually added POB in the target contract are not logged in either the Revenue Accounting (RA) engine application log or the RAI monitor log.

The note indicates that this is due to a coding error where the logic for handling errors for manually added POBs was missing.

To resolve this issue, SAP provides correction instructions. Once implemented, the error messages for manually added POBs will be recorded for all relevant RAIs, similar to how error messages are recorded at the contract level. Users affected by this issue should apply the provided corrections to ensure that these error messages are displayed correctly.