SAP Note 1900896 addresses issues related to the use of SAP web applications with Microsoft Internet Explorer (IE), specifically focusing on how different versions of IE render content and the impact on SAP applications.

Symptom:
The note discusses SAP web applications running in Internet Explorer 9 (IE9) and higher document modes.

Other Terms:
The note covers various IE document modes, standards, quirks, and SAP technologies like SAPUI5, NWBC, Portal, etc.

Background:
From IE9 onwards, IE introduced a new layout engine mode, separating IE8-compatible modes from IE9 and higher compatible modes (IE8-engine and IE9-engine). This change affected the way IFRAMES handled content and, consequently, impacted SAP web applications.

Restrictions:
IE9 and above cannot mix HTML codes compatible with IE9-engine modes and those for IE8-engine modes within the same web document. SAP-supported browsers do not have this restriction.

Conclusion:
Specific SAP solutions, like Web Dynpro, run in quirks mode due to historical reasons, while others like SAPUI5 need IE11 standards mode for HTML5 features. Some technologies can switch between modes, but there's a need to set up and configure systems to ensure the correct content and shell setup.

Solution:
SAP applications and clients require specific configurations to support different IE document modes:
- SAPUI5 and new WDA applications require Standards Mode.
- Older SAP applications can run in either Quirks or Edge Mode for NW7.40 and higher releases.
  
SAP UI clients have varied support across IE modes:
- NWBC Desktop supports all modes with no additional configuration.
- NWBC for HTML needs configuration to run in both modes.
- Portal Frame Page supports Quirks mode only unless run as "headerless" for Standards mode.
- Unified Framework Page and Fiori Launchpad support Standards mode only and cannot integrate Quirks content.

A supporting table outlines which SAP UI Clients are compatible with the different IE modes and SAP UI technologies.

For up-to-date information on browser support in SAP UI technologies, users are directed to check the Product Availability Matrix (PAM) for the relevant NetWeaver Release.