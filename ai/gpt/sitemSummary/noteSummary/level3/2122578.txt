SAP Note 2122578 introduces a new Security Audit Log event to track unencrypted SAPGUI and RFC (Remote Function Call) communication. The note explains that previously, while it was possible to block unencrypted communications using methods from SAP Note 1690662, there was no way to audit if such unblocked or blocked communication attempts were occurring.

The note outlines that in order to use this new feature, customers must apply the correction from SAP Note 2104732, which enables the new Security Audit Log event "BUJ." Once the correction is applied, the event can be activated using transaction code SM19.

The new functionality enables the Security Audit Log to record events when unencrypted SAPGUI or RFC communications are detected, indicating whether each communication was "BLOCKED" or "TOLERATED." Furthermore, a profile parameter (`snc/log_unencrypted_rfc`) is available to configure if the event should be triggered only for blocked unencrypted RFC communications or for tolerated ones as well.

Four possible values for `snc/log_unencrypted_rfc` are provided, with each subsequent value expanding the scope of the Audit Log recording, from only logging blocked unencrypted communications to logging various levels of tolerated communications, including external non-ABAP RFC clients, other ABAP system/client/users, and even internal RFC communication within the same system, client, and user.

The motivation behind this feature is to encourage and enforce the use of encrypted communication. The Note guides customers through a process of first enabling Security Audit Logging without setting the profile parameters (`snc/only_encrypted_gui`, `snc/only_encrypted_rfc`, `snc/log_unencrypted_rfc`) to any value other than the default (which is 0), then progressively stepping up encryption requirements until all communications are secured as per the organization's security policies.

In summary, SAP Note 2122578 provides a mechanism for SAP system administrators to audit and enforce encryption standards for SAPGUI and RFC communications by using the Security Audit Log to monitor and potentially block unencrypted communications.