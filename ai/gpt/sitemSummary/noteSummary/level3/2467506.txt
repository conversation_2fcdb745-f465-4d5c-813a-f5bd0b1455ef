SAP Note 2467506 discusses the implications for businesses using the Order Allocation Run (ARun) feature in SAP Fashion Management during a system conversion to SAP S/4HANA. The note details the differences between the ARun feature in SAP Fashion Management and the redesigned Supply Assignment functionality in SAP S/4HANA.

Key points from the note:

- The Order Allocation Run is part of the Supply Assignment in SAP S/4HANA and has been integrated with the Back Order Processing (BOP) into one common solution based on Advanced ATP.
- New features include FIORI-based executions, result monitoring, an explanation tool, and a workbench for managing exceptions.
- Some features previously available in SAP Fashion Management's ARun are not available in SAP S/4HANA, including certain customizing settings, workbench reports, release strategies, consistency check reports, and support for special order types like Make-to-Order.
- The data model in SAP S/4HANA has been simplified, meaning certain tables used by Fashion Management's ARun (FSH_BDBS and FSH_PREVIEW) are not used and their data will not be migrated to S/4HANA. This requires setting up and executing ARun again after the system conversion.
- Specific transactions related to ARun from SAP Fashion Management are no longer available in SAP S/4HANA.
- To use Supply Assignment, the business function SUPPLY_ASSIGNMENT_01 must be activated and for retail fashion management functionalities, SUPPLY_ASSIGNMENT_RETAIL_01 should also be activated.
- Businesses need to adjust their processes accordingly to align with the new S/4HANA system's changes.
- Relevance to a business can be determined if they use ARun functionality, which can be checked by looking at entries in tables FSH_BDBS or FSH_PREVIEW.

The note essentially provides a transition guide and outlines the necessary actions for users moving from SAP Fashion Management to SAP S/4HANA, highlighting changes in functionality and recommending the activation of certain business functions in the new system.