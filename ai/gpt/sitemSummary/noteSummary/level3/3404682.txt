Summary:

SAP Note 3404682 provides guidance on creating a maintenance view via the migration object PRODUCT within SAP S/4HANA environments, specifically for SAP S/4HANA Cloud and SAP S/4HANA 2023 and higher.

The issue arises when migrating products using the "Migrate your Data" app and the migration object "Product," where the expected maintenance view may not be created. The cause is an incorrectly filled XML template.

The resolution involves ensuring organizational-dependent data and views (such as purchasing, accounting, sales, etc.) are correctly set up in the product master. For automatic determination of the maintenance status during migration, at least one relevant field must be provided. Examples provided include filling out fields under 'Purchasing Header Data' on the 'Basic Data' sheet or under the 'Purchasing' group on the 'Plant Data' sheet to activate the purchasing view.

Additionally, if a special view needs to be active regardless of the provided fields, this can be achieved using specific status fields available on the 'Basic Data,' 'Plant Data,' and 'Valuation Data' sheets. Examples are provided to illustrate how these fields can be flagged to ensure the desired maintenance status is set.

Keywords associated with this note are related to the PRODUCT migration object, maintenance status, and views within SAP S/4HANA.

(Note: The images referenced in the summary are not visible in this text format).