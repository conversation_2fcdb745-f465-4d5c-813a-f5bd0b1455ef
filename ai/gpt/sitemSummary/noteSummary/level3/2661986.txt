SAP Note 2661986 addresses issues related to currency keys and ISO codes encountered during the Migration Cockpit process in SAP S/4HANA. The errors described include problems such as:

- No currency key found for ISO code in a particular field.
- The ISO code is not assigned to a currency key for a valid time period.
- The currency key does not have an ISO code assigned.
- There are multiple SAP currency codes for an ISO code intended for ALE (Application Link Enabling) usage.
- There is no unique SAP currency code for an ISO code for ALE.
- An ISO code conversion error.

The causes of these issues could be that a currency needs an assigned ISO code marked as PR<PERSON>AR<PERSON>, or a target value mapped in the Migration Cockpit does not match an ISO code or is not correctly assigned in the currency code configuration.

The resolution advises checking whether the ISO code is assigned to a currency and that exactly one entry is flagged as PRIMARY for clarity. When using the mapping task for currency key mapping, it's recommended to select the ISO code via value help to ensure validity.

For errors B1 88x, users should refer to the long text of the error message for explanations and solutions. For the AFLE 008 error, SAP Note 2993687 should also be checked as it addresses issues where error messages do not contain adequate information to resolve the problem.

Keywords include various error codes associated with currency key and ISO code issues, LTMC (Legacy Transfer Migration Cockpit), and the importance of the ISO code in the field CURRENCY_ISO being the primary.