SAP Note 1848513 addresses an issue concerning the BI Consumer Services (BICS) when used with BI Java or similar products. The problem arises with a Temporal Hierarchy Join (THJ) InfoObject: when a user invokes an F4 help dialogue, the read mode is always incorrectly set to mode M, which can result in performance issues. This problem is specific to SAP NetWeaver BW version 7.30 and does not occur in the older ABAP runtime environments.

The note identifies this behavior as a program error and provides a solution in the form of support packages for various versions of SAP NetWeaver BW:

1. For SAP NetWeaver BW 7.30, customers should import Support Package 10 (SAPKW73010), as elaborated in SAP Note 1810084.
2. For SAP NetWeaver BW 7.31 (also known as SAP NW BW 7.3 Enhancement Package 1), the required support package is SP 8 (SAPKW73111), which is detailed in SAP Note 1914639.
3. For SAP NetWeaver BW 7.40, Support Package 3 (SAPKW74003) is recommended, and more information can be found in SAP Note 1818593.

In urgent situations, correction instructions are available for immediate implementation. However, users are advised to first check SAP Note 1668882 regarding the transaction SNOTE before using these correction instructions.

It is also important to note that these SAP Notes might be available before the official release of the Support Packages and might still be labeled as a "preliminary version" at that time.