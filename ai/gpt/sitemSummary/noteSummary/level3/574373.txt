SAP Note 574373 addresses an issue in SEM (Strategic Enterprise Management) versions 3.1A and 3.1B where the system fails to correctly store the general strategy. This note outlines that the problem affects users with SAP GUI versions at least as old as 4.6D compilation 3 and is compatible with SEM versions as far back as 2.0B. To remedy the problem, the note describes an updated procedure for installing front end packages using a new tool provided with SAP GUI Release 4.6D compilation 3, which automates package application and updates the installation database on the installation server.

The solution steps include:
1. Setting up an installation server by running setup.exe and choosing "Administrative set-up."
2. Downloading the package archive (SEM 3.1b SP4) from the SAP service portal and copying the files to the server's installation directory.
3. Using the SapSetup Configuration Program ("SapAdmin" from the "\netinst" directory) to import the package.
4. Indicating the package file to be installed.
5. Installing the package, ensuring that client installations are performed using Netsetup without manual registration of OCX files to guarantee proper uninstallation.

The note also provides instructions for installing the package on a stand-alone computer without an installation server by using the Setup update "localpat46D_Y.exe". It highlights the necessity of installing SAP Chart OCX for graphic components in Balanced Scorecard and Management Cockpit and directs users to retrieve and install the latest package (CSN Note 318196).

Lastly, the note emphasizes that manual unpacking of packages is not viable from the specified package levels onward and points to SAP Note 361222 for additional details. For installing the latest front end package related to graphical components, users are directed to the SAP service portal.