This SAP Note 2558835 addresses an issue concerning the incorrect conversion of the Stand Alone Selling Price (SSP) determined by Business Rules Framework Plus (BRF+) rules. Specifically, when the SSP is derived for a Performance Obligation Bundle (POB) in a currency that does not use decimal places, such as the Japanese Yen (JPY) or the South Korean Won (KRW), the SSP value is recorded as 100 times smaller than it should be. Additionally, if the SSP range amount is also determined using BRF+ rules, then the same currency issue causes the SSP range amount to be 100 times larger than intended.

The note identifies the cause of this problem as a coding error, which was introduced by a previous note, number 2448683. This error resulted in the SSP amount being converted to the internal representation twice, hence the incorrect calculations.

To resolve this issue, the SAP Note provides correction instructions which need to be implemented by the users affected by this error. Implementing these instructions should correct the SSP calculations to properly handle currencies without decimal places.