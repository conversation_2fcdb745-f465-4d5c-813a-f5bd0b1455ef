SAP Note 2700103 deals with an issue encountered during the migration of a significant number of documents into SAP Revenue Accounting and Reporting (RAR) using the SD integration component's operational load tool. These documents are related to legacy data creation, which is essential for revenue accounting.

**Symptom:**
Customers experience a situation where the job runs without errors being reported in the log, yet when they check the revenue accounting item monitor, they find that the legacy data is missing. An error message (FARR_RAI 651) stating "No or incomplete legacy data available for item &1, acct. principle &2" is displayed when processing the generated Revenue Accounting Items (RAIs).

**Other Terms:**
Key terms associated with this note include SD Revenue Accounting, Migration, operational load, missing legacy data, and errors such as communication_failure, system_failure, and RFC (Remote Function Call) error.

**Reason:**
The cause of the missing data is identified as an issue with the synchronous RFC used by the operational load tool to generate legacy data. If the RAR system’s RFC interface cannot handle the incoming call, or if the system is unavailable when the call is made, the data transmission fails and information is lost.

**Solution:**
With the implementation of the changes from this SAP Note, the operational load tool has been updated to log any failures in the RFC calls properly. Furthermore, instead of sending all legacy main items in a single accumulated RFC call at the end of the process, each main item is now sent in a separate sRFC (synchronous RFC) call. This change minimizes the number of documents potentially affected by any temporary unavailability of the target system during the operational load process.

In summary, the note provides a solution to ensure that legacy data creation during document migration in SAP RAR is accurately logged and less vulnerable to RFC-related issues that could occur if the RAR system is temporarily inaccessible.