SAP Note 2569950 covers FAQs related to the migration and operational load within the SD Integration Component for SAP Revenue Accounting and Reporting (SAP RAR). The note addresses common questions and concerns that may arise during the migration of Sales & Distribution (SD) processes to SAP RAR.

Key points summarized from the SAP Note:

1. General Information: Documentation is available for customers about migration and operational load, and additional information can be found in other referenced SAP Notes and the Migration Guide for SD Revenue Recognition Customers.

2. Tools Provided: SAP supports the migration of SD documents with tools such as the Operational Load report (transaction FARRIC_OL), Expert Mode (transaction FARRIC_OL_EXPERT), Cleanup (transaction FARRIC_IL_CLEANUP), and the Correction report (transaction FARRIC_RR_CORR).

3. Automated Migration: The operational load program allows for the automated migration of related documents at the sales document level without the need for manual migration.

4. Selective Migration: While migration typically occurs at the sales document level, customers can use BADI FARRIC_OL_VALIDATION to implement logic that excludes specific documents or items from migration.

5. Exclusion from Migration: Documents may be skipped due to various validation checks. Details on excluded items are provided, indicating that exclusion results in them not being updated with the revenue accounting relevance flag.

6. Migration of Changed Item Categories: Documents previously migrated can be reprocessed if an existing item category is later marked for revenue accounting relevance.

7. Standard SD Process Migration Limitations: Not all standard SD processes are supported for migration to SAP RAR. Various subquestions clarify specific scenarios and document types, such as proof-of-delivery relevant processes and cross-company sales processes.

8. SD Revenue Recognition Migration: The note provides detailed information on how time-based, service-based, and event-based SD Revenue Recognition processes can be migrated to SAP RAR.

9. Revenue Correction Report: Explains the purpose of the revenue correction report (transaction FARRIC_RR_CORR), which is used for migrated SD Revenue Recognition processes that require correction due to posts made after a company code´s or migration package´s transfer date.

10. Restarting Migration After Errors: Details are given on how to undo and restart the operational load if errors occur.

11. Cancelling Billing Document Migration: Billing document cancellations are generally not migrated if the original and cancellation documents were created before the transfer date.

12. Incomplete Document Migration: Incomplete sales documents are skipped during migration until their missing data is completed.

13. Use of Migration Packages: Migration packages help perform granular migration in steps and can have different configurations and statuses compared to the company code level.

14. Initial Load Indicator: Explanation on why operational load generates RAIs with or without the initial load indicator based on document dates in relation to the transfer date.

15. Migration of Costs: With the update in SAP Note 2754952, it is now possible to send legacy conditions for costs realized before the migration.

The note provides a comprehensive FAQ to assist users in understanding and navigating the complexities of the migration process to SAP RAR from SD processes.