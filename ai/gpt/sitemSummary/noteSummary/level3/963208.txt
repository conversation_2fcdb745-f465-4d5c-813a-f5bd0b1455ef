SAP Note 963208 addresses an issue where a short dump occurs in SAP NetWeaver 2004s BI during the creation of a transformation, particularly when multiple source fields designated as units are assigned to a single rule.

Summary of the SAP Note 963208:

- **Symptom**: Users encounter a short dump when they try to create a transformation that involves assigning several unit-typed source fields to a rule.
- **Cause**: The cause of this problem is identified as a program error within the software.
- **Solution**: SAP provides a resolution to this issue by recommending the import of Support Package 09 for SAP NetWeaver 2004s BI. This support package is detailed in SAP Note 0914303, titled "SAPBINews BI 7.0 SP09". This note provides more comprehensive information about the support package, including its availability to customers.

The note also offers an immediate workaround through correction instructions, which can be used in urgent situations before the formal release of the Support Package. The note warns that prior to the release, the short text of the Support Package may indicate a "preliminary version".