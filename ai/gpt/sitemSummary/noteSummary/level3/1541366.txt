SAP Note 1541366 addresses an issue regarding the delta update process for DataSources 2LIS_11_V_ITM or 2LIS_11_V_SCL, which are used to extract delivery-related data from sales documents into the delta queue for further processing in BI systems.

Symptom:
When creating or deleting (partial) deliveries, additive records are written into the delta queue using the ADD delta method instead of the ABR method. This can cause problems or extra work during querying as ADD data cannot be indexed properly in an ABR context.

Other Terms:
The terms ODP, delta, RSA2, and RSA5 are referenced, indicating that the problem involves the Operational Data Provisioning (ODP) framework and the delta extraction process using SAP transactions RSA2 (DataSources) and RSA5 (Install Business Content DataSources).

Reason and Prerequisites:
The core issue is that the ADD delta method is incorrectly used in scenarios where the ABR method should be implemented, potentially causing inefficiencies or errors in the data update process.

Solution:
The solution involves importing the relevant Support Package for the user's SAP release version and activating a complete ABR update for the affected DataSource or extractor. This activation is accomplished by using transaction SM30 to modify view V_ANLY_SD_SETUP and setting the necessary indicators as per the provided documentation.

In summary, SAP Note 1541366 describes a problem with delta updates not being processed correctly for certain delivery-related DataSources and provides instructions on how to resolve the issue by activating the correct delta update method (ABR) through the importation of a Support Package and adjustments in the system configuration.