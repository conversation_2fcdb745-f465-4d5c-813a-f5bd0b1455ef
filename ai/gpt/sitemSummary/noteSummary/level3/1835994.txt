SAP Note 1835994 addresses an issue in MultiProviders where queries with a filter condition that includes a wildcard character at the beginning (e.g., CP '*ABC') are displaying incorrect results. Specifically, the problem manifests as the system incorrectly showing an initial line for PartProviders that do not have the relevant characteristic.

The issue is identified as a program error and occurs in the following SAP NetWeaver BW (Business Warehouse) versions:

- SAP NetWeaver BW 7.30
- SAP NetWeaver BW 7.31 (which is also known as SAP NW BW 7.0 Enhancement Package 3)
- SAP NetWeaver BW 7.40

The solution provided in the SAP Note consists of importing specific Support Packages for each affected BW version:

- For NetWeaver BW 7.30, one should import Support Package 10 (SAPKW73010). Additional details are available in SAP Note 1810084 titled "SAPBWNews NW 7.30 BW ABAP SP10."
- For NetWeaver BW 7.31, one should import Support Package 8 (SAPKW73108). Further details can be found in SAP Note 1813987.
- For NetWeaver BW 7.40, one should import Support Package 03 (SAPKW74003). For more information, refer to SAP Note 1818593.

If the issue is urgent, the note provides the option to implement the correction instructions as an advance correction. However, before doing so, users must read SAP Note 875986, which offers guidance on using transaction SNOTE, an SAP transaction that is used for applying SAP Notes.

Additionally, the notes referenced for more details may already be available before the actual release of the Support Package and may be labeled with "Preliminary version" in the short text of the SAP Note.