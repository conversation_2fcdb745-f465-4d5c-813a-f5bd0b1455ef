SAP Note 938706 addresses an issue where default user settings, including default values in transaction ME21N (Create Purchase Order), disappear after upgrading from release 4.6B to release 4.7 or a higher version. The note identifies this as a program error.

To resolve this issue, the note advises users to implement advance corrections. It also instructs users to save entries from the ESDUS table (MM - Dynamic User Settings) before performing the upgrade to prevent loss of settings. Furthermore, this SAP Note should be implemented before the first logon after the upgrade to ensure that the default values are retained. Other transactions mentioned that might be affected include ME22N (Change Purchase Order) and ME23N (Display Purchase Order), which are related to personalization settings.