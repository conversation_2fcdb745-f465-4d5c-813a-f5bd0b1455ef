SAP Note 487043 pertains to an issue where calculated sales order stocks and project stocks are not included when loading initial non-cumulative values into SAP's Business Information Warehouse (BW) using the RMCBINIT_BW program or transaction MCNB. The cause of the issue is that, up to now, the QBEW and EBEW tables have not been considered.

The solution provided in the note requires manual enhancements to certain function module interfaces, the addition of data elements and structures in the Data Dictionary, and changes to the function modules' properties and interfaces. Examples include creating data elements MCBW_VBELN, MCBW_POSNR, and MCBW_PSPNR, enhancing the MCBIWSTOCK structure, and modifying the properties of the LOG_CONTENT_STOCK_MAP and LOG_CONTENT_STOCK_SELECT function modules.

Other specific tasks include adding fields to structures (MBBIWXEBEW, MBBIWXQBEW, MBBIWXMSSA, MBBIWXMSSQ) and defining the export and import parameters of function modules manually.

These changes are necessary but not automatically performed by the Note Assistant and must be done manually by implementing the source code corrections as specified.

Note: To accurately follow the instructions given in the SAP note, users should have access to the SAP system and the ability to modify Data Dictionary objects and function module interfaces.