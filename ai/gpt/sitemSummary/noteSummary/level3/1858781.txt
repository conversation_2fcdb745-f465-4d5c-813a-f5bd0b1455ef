SAP Note 1858781 addresses an issue with performance during the reassignment of sales document items, specifically for the object type SO/SOI. The note indicates that users experience long runtimes when performing this operation due to a significant amount of data being stored in the table FAGL_R_SDLOG_001.

The reason for the performance issue is that the system is reading and logging more follow-on documents in the table FAGL_R_SDLOG_001 than necessary during the profit center reorganization process. This excessive data volume and logging especially affect sales documents with numerous follow-on documents, like scheduling agreements.

To resolve the issue, the SAP Note recommends implementing correction instructions or importing the corresponding Support Package. Additionally, if the function module RV_ORDER_FLOW_INFORMATION lacks the parameter NO_ACC_DOC, users should implement SAP Note 1827079. This measure aims to optimize the reassignment process and improve the performance by reducing unnecessary data volume and runtime.