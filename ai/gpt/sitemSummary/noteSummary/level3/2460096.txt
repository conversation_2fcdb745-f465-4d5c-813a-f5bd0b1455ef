SAP Note 2460096 is concerned with enhancing data consistency checks in Revenue Accounting and Reporting (RAR) to prevent data inconsistencies that may arise when contracts are generated or updated from operational documents, including scenarios where customer implementations change data.

The note recognizes the possibility of errors from customer implementations and inconsistencies from operational documents sent via SAP or non-SAP systems. Such inconsistencies might impact revenue figures and are costly to correct once saved to the database. Therefore, the note emphasizes the importance of performing inflight checks to ensure data integrity before committing to the database.

Here are the key points of this note:

1. If inconsistencies are detected, the affected contract processing fails:
   - In the Revenue Accounting Interface (RAI), processing stops, RAI items are marked with an error, and revenue contracts are not created or updated.
   - For manual editing of revenue contracts, users receive an error message and cannot save the changes.

2. The note introduces several specific checks to prevent inconsistencies:
   - E01: Ensures the total amount of allocation effects for performance obligations is zero.
   - E02: Confirms the allocated amount of a performance obligation matches the total of each period from its revenue schedule.
   - E03: Checks that the effective quantity of an event-based performance obligation matches the total of all fulfillments once it's fully fulfilled.
   - E06, E07, E08, E09, E13, E17: Various checks related to the consistency of posted revenues, invoice corrections, special indicators correctness, allocation effects, invoice amounts in different tables, and currency signs.

3. Note 2456711 already implemented some checks (E01, E02, E03, and E09), and this note (2460096) adds further checks.

The note concludes by advising users to apply it to ensure these inflight checks are in place for preventing data inconsistencies in RAR. It does not detail the technical steps of implementation, suggesting that applying the note itself is the solution.