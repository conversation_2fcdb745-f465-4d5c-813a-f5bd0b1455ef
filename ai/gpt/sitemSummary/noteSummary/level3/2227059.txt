SAP Note 2227059 addresses a simplification change in SAP S/4HANA related to the Materials Requirements Planning (MRP) within the Production Planning (PP) module. Specifically, it details a change in the database table used for MRP planning file entries and long-term planning. 

The note explains that a new database table, PPH_DBVM, has been introduced to replace the previous tables MDVM, MDVL, DBVM, and DBVL. The reason for this change is a revision and simplification of the source code for MRP planning file entries in SAP S/4HANA. With this new implementation, MRP areas are activated by default.

As a solution, SAP advises that any updates or inserts that previously targeted the old tables must now be done to the new table PPH_DBVM. For read operations, compatibility views have been created to ensure that data stored in the legacy tables can still be accessed, albeit from the new PPH_DBVM table.

Additionally, SAP suggests using the existing function modules from the function group DISP (such as "DISPSATZ_ERSTELLEN" or "MD_MDVM_TABLE") and provides a transport bill of material SI_PP12_PLANNING_FIL to assist in identifying relevant locations within the customer's source code that might require adjustments due to this change.