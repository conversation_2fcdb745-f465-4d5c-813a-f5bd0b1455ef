SAP Note 3011797 addresses an issue regarding data migration using the SAP S/4HANA Migration Cockpit for fixed assets, including balances and transactions, specifically within SAP S/4HANA 1809. Users encountered a problem where, after successful migration, a trailing comma was incorrectly added to the source value in the value mapping of the Main Asset Number.

The reason for this issue lies in the migration template for the release OP 1809, where the asset subnumber is disabled. Consequently, the identifier for the migrated asset does not recognize the separator comma + space, leading to the unwanted trailing comma in the mapping. Users affected must have SAP S/4HANA 1809 (from FPS0 to FPS5) installed and must be using the pre-delivered SAP S/4HANA Data migration content without modifications.

The solution provided by this SAP Note is a Transport-based Correction Instruction (TCI) that, after being implemented, will fix the issue. It will ensure that the source value of the Main Asset Number is written into the mapping without the trailing comma. However, it must be noted that if users have made any modifications or copied the migration object, this TCI will not correct those altered objects. Additionally, the correction only applies to the SAP-delivered content-related objects, and as a result, the generated migration objects will be updated automatically.

The note also references KBA 2543372 for instructions on how to implement a Transport-based Correction Instruction. Users should be aware that the maximum field length supported by Microsoft Excel is 15 digits, including decimals.

In summary, SAP Note 3011797 provides a fix for a specific data migration error that occurs in SAP S/4HANA 1809 related to a comma being incorrectly added in the value mapping of migrated fixed asset data. The fix is applicable to standard SAP content and will not correct user-modified objects.