SAP Note 1425765 outlines the process of generating configuration files for securing external programs that start or register with an SAP system. It specifically deals with two files, sec_info and reg_info, which are vital for maintaining gateway security by controlling access to the SAP system.

Main points of this note:

- Transaction SMGW offers a feature to create reginfo and secinfo files. These files define which external programs are allowed to start or register with the SAP system.
- From Kernel Release 46D onwards, it's possible to set the gw/reg_info parameter.
- The note emphasizes enhancing system security by controlling the registration and starting of external programs with proper configuration.
- Prerequisites for using this function include:
  - gw/reg_no_conn_info parameter must be set to at least 1, as described in Note 1444282.
  - Necessary Enhancement Packages and specific kernel patch levels (referenced on the "Support Packages" and "SP Patch Level" tabs) must be imported.
  - If the system does not meet the required package and kernel levels, secinfo and reginfo must be manually created using guidelines from Note 1408081.

The note highlights options for file placement and maintenance:
- A central file for the entire system simplifies maintenance but might become large.
- Individual files for each application server offer better clarity.
 
Instructions for generating rules for various security levels are provided, including setting up system rules and evaluating transaction SM59 for external programs.

For production environments without existing secinfo and reginfo:
- Progress from an open to a secure configuration, activating default values followed by manual adjustment based on administrator knowledge.

For environments with existing files:
- Revise existing secinfo and reginfo files, with suggestions on updating and tightening security settings.

User guidance is offered for creating the files through transaction SMGW:
- Select the necessary entries to include in the files.
- Choose between "Save Selected Entries in File" or "Create ACL File" to check and confirm selected entries.
- Rules become immediately valid when saved, replacing or appending to existing files based on choice.

Finally, the note explains that ACL file contents shown in the transaction SMGW are color-coded to indicate the correctness of the lines (OK, warning, or error) as further outlined in Note 1503858.