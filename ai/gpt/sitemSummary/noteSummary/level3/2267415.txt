SAP Note 2267415 details the transition from the Subsequent Settlement application (MM-PUR-VM-SET) to the new Contract Settlement (LO-GT-CHB) application, as part of a system conversion to SAP S/4HANA.

Key points of the SAP Note are:

1. Subsequent Settlement: The existing Subsequent Settlement application is being replaced by Contract Settlement in SAP S/4HANA due to its limitations and the introduction of a superior solution.

2. Benefits of Contract Settlement:
   - Centralized and standardized administration of supplier and customer conditions.
   - Increased transparency of document relevance and relationships.
   - Detailed settlement overviews.
   - A flexible settlement calendar.
   - Ability to support various data sources for modelling flexible rebate scenarios.
   - Full integration with both Order-to-Cash and Procure-to-Pay processes.
   - High-performance design optimized for SAP HANA.

3. Restrictions in SAP S/4HANA: The ability to create new rebate arrangements using transaction MEB1, or extend existing ones using transactions MEBV, MEB7, or MEBH has been restricted.

4. Business Process Changes: Existing rebate agreements can only be processed until their validity end date in SAP S/4HANA. Final settlements are required for closing them, and new agreements must be created as condition contracts.

5. Technical Changes: The structure of table S111 has been altered to accommodate the material field length extension in SAP S/4HANA, affecting business volume data rebuilding.

6. Actions Required:
   - No direct actions are necessary for continuing subsequent settlement processes apart from closing agreements after their validity period.
   - If recompilation of the business volume data is needed, rebuild table S111 using report RMEBEIN3 as outlined in SAP Note 73214.

7. Transactions Not Available in SAP S/4HANA: Transactions MEB1, MEB7, MEBV, and MEBH are not available in the SAP S/4HANA on-premise edition 1511.

Related SAP Notes mentioned for further reference are 2194923, 2215220 related to conversion pre-checks, and 73214 related to the rebuilding of business volume data.