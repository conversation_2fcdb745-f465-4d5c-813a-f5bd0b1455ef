SAP Note 787302 addresses a situation where the end of maintenance for an SAP kernel appears to occur before the end of maintenance for the corresponding SAP application. Users might perceive this as an inconsistency or oversight, but the note clarifies that this is not a contradiction but rather a part of SAP's maintenance strategy.

The note explains that SAP guarantees to provide suitable successor versions of the kernel ("successor versions" like 4.6D_EXT in place of 4.6D) in time to ensure continuous support as long as any of the SAP applications relying on those kernels are still within their maintenance period. These new kernel versions are created based on more recent operating system and database (OS/DB) combinations since the end of kernel maintenance is typically linked to the end of maintenance for the OS/DB combinations they were originally produced with.

Additionally, it highlights that kernels are developed to be downward compatible within a release track. It means that a newer kernel version like 4.6D_EXT might be used not only for an application with a Basis release of 4.6D but potentially for other systems based on earlier versions such as 4.6C, 4.6B, or 4.6A. This compatibility is subject to confirmation by specific SAP Notes that collect the corresponding downward compatible kernel (DCK) release notes.

It is important to note that taking advantage of a more recent kernel as part of SAP's extended maintenance may necessitate an upgrade to the user's operating system and/or database software to ensure compatibility with the new kernel version.