SAP Note 98586 pertains to the analysis and resolution of potential issues in the area of subsequent settlement purchasing. Here is a summary of the content provided in the note:

**Symptom:**
The note provides additional information and tools for analyzing and solving issues related to the updating of business volume data and reviewing settled incomes in the context of subsequent settlement (volume-based rebate) in purchasing.

**Other Terms:**
- Subsequent settlement (volume-based rebate) in purchasing.

**Reason and Prerequisites:**
Program errors may necessitate analysis to check for issues with business volume updates or to assess settled incomes. The note introduces reports not included in the R/3 delivery but essential for problem analysis for Releases 3.0 to 3.1I.

**Reports Provided:**
1. **ZANASTAT:** Compares business volume data from detailed statements with statistical data to identify variances (for Releases 3.0 to 3.1I).
2. **ZINCOME:** Compares the scale and condition basis with actual incomes for already settled condition records (for Releases 3.0 to 3.1I).
3. **ZDELSTAT:** Deletes the business volume data for an arrangement (Releases 3.0 to 3.1I).
4. **ZCHKEKBO:** Checks the consistency between purchase order history (invoices) and updated invoices (specifically for Releases 3.0F, 3.1H, and 3.1I).

Note that these reports are periodically enhanced and updated, and not all reports may be available for every release.

**Solution:**
- **Report ZANASTAT** is used to analyze variances in business volume data update. It has options to exclude already settled arrangements and include deleted condition records at the user's discretion. A detailed log is available in "expert mode."
- **Setting Up Statistical Data:** For Release 4.0B and higher, data deletion as part of setting up statistical data is automated, but for Release 3.0, report ZDELSTAT is to be used.
- **Report ZINCOME** helps in identifying variances between scale/condition basis and income. If variances are found, the condition record can be reset for further settlement as an interim settlement.
- **Report ZCHKEKBO** is utilized for a consistency check between purchase order history and executed updates, which aids in identifying missing business volume data updates.

The note advises on specific prerequisites such as having all settlement documents released for accounting before conditions can be checked. It also suggests related notes for additional guidance (e.g., Note 73214 for the setup of statistical data and Note 117988 for related updates).

Overall, SAP Note 98586 provides critical tools and guidelines to troubleshoot updating issues and discrepancies in business volume data within the subsequent settlement process in purchasing, catering primarily to R/3 Release 3.0 to 3.1I.