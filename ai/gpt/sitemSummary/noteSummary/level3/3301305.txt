SAP Note 3301305 provides a central correction for issues specifically relating to the SAP S/4HANA Data Migration content delivered for SAP S/4HANA 2020. The symptom addressed by this note is a content-related error observed when users utilize the data migration cockpit in SAP S/4HANA 2020, employing the "Transferring Data Using Staging Tables" method.

The note is relevant for the "LTMC - Transferring Data Using Staging Tables" and "Migrate Your Data Migration Cockpit - Transferring Data Using Staging Tables" processes.

The targeted issue involves the technical name SIF_FIXED_ASSET, the migration object for fixed assets, including balances and transactions. The correction detailed in this note sets specific local fields to obsolete. For a more detailed description, users are directed to referenced SAP Note 3299933.

The note clarifies that this correction is applicable for installations of SAP S/4HANA 2020 from Support Package 00 (SP00) to Support Package 05 (SP05), and it applies when using the pre-delivered SAP S/4HANA Data migration content without any modifications.

The solution provided is a Transport-based Correction Instruction (TCI) that automatically updates the related objects of the SAP-delivered content and fixes the issues described. Users with customized or copied objects will not have these corrections applied automatically to their modified objects. They should refer to SAP KBA 2543372 - "How to implement a Transport-based Correction Instruction" for details on how to apply the TCI to their modified migration content.