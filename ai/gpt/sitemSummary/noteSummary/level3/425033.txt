SAP Note 425033 addresses an issue with extending rebate arrangements in the SAP system when they involve currency conversion, specifically the euro (EUR) currency. Here's a summary of the note:

**Symptom:**
Users experience difficulties in extending rebate arrangements when individual condition records already contain the EUR currency. The system is failing to check for multiple currencies in an arrangement based on the correct date, which should ideally be as of 01.01.1800 according to Note 91481.

**Other Terms:**
The note references rebate processing in purchasing, mentioning transactions MEB7, MER7, and reports RWMBON05, RWMBON35.

**Reason and Prerequisites:**
Rebate arrangements must not be created in old currencies (e.g., DEM) for the year 2002 and onwards. For extension purposes, if a currency is part of the EMU, it is identified by fixed exchange rates.

**Problem:**
Issues occur when extending rebate arrangements that include a second currency. For example, when the second currency is not EUR (like USD), users can manually exclude or force an automatic extension of these arrangements. However, problems arise when the second currency is already EUR, leading to incorrect determination of EMU membership and resulting in the rejection of the extension with error message MN 638.

**Solution:**
The solution involves importing a correction instruction or support package to enhance the functionality of the function module MM_ARRANGEMENT_CHECK_WAERS to handle the new arrangement currencies properly. With the correction implemented, users will only need to set the parameter "Carry out conversion also if cond. scale/cond. currency euro currency differs from arr. Currency" for a successful extension. Additionally, users are advised to read Note 400432 to ensure that rebate arrangements with invalid currencies cannot be saved or extended post-correction.