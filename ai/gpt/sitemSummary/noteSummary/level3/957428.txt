SAP Note 957428 provides troubleshooting steps for missing XI (Exchange Infrastructure) data in service reports.

Symptom:
The service reports are missing XI data.

Other Terms:
The note mentions terms related to XI service session downloads like DSA, ST13, SDCC, and ST-A/PI.

Reason and Prerequisites:
This section seems to be blank in the given content.

Solution:
The solutions are divided into points addressing different symptoms:

1. If latency times are showing zero, it suggests performance headers are not aggregated on the customer's system. Reference to SAP Note 820622 is provided for a solution.

2. If statistics are missing (e.g., 'Weekly Load Profile' or workload distribution checks), the following steps should be taken:
   a. Ensure that procedures post ST-A/PI add-on implementation have been executed by uncommenting code via RTCCTOOL (Refer to SAP Note 69455, point 2d).
   b. Check for the existence of download tables 00013 & 00014. If missing, they might have been auto-deleted due to the large download size; in this case, retrieve statistics from the report ZSXMB_REMOTE_SERVICE (Refer to SAP Note 746088).
   c. If an XI data collector fails three times, it gets deactivated. Ensure XI collectors are active and have completed their tasks. If a collector is inactive, it should be reactivated through transaction /n/ssf/pb and, if necessary, unlocked from the status menu. Rerun the download and report any recurring issues to component SUP-RSRV-DEV.

3. If the 'XI Performance of <SID>' check displays no subchecks:
   a. Activate the 'analyze' field in the XI Runtime Objects table for the integration server.
   b. Confirm the integration server name in the 'Business System' column matches the customer system's value, which can be found using transaction SE37 and function SXMS_PF_GET_OWN_IS_NAME.

4. Instructions on how to check download contents:
   a. Use transaction DSA with the 'truck' icon, navigate to 'Alternate Viewers' > 'ST-A/PI DATA VIEWER', and browse the XI Tables under 'DATA_PER_R3SYSTEM -> DCA'.
   b. If 'Alternate Viewers' is not available, remove the client ID from the session number, use tool SDCCVIEWER in transaction ST13, and select the appropriate session to analyze the XI Tables.

In essence, this SAP Note provides a comprehensive guide for diagnosing and resolving issues with missing XI data in service reports, including relevant steps to take, checks to perform, and references to additional SAP Notes for further guidance.