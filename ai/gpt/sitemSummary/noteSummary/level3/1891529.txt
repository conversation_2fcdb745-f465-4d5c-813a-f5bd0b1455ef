SAP Note 1891529 pertains to the reconversion of SAP HANA-optimized DataStore objects (DSOs) that are part of a semantically partitioned object (SPO) in SAP systems using SAP HANA as the database platform. Previously, the reconversion for these types of objects was not supported.

Key Points of the SAP Note:

- The SAP Note discusses the use of program RSDRI_RECONVERT_DATASTORE, which now supports the reconversion of semantically partitioned, SAP HANA-optimized DSOs.

- The reconversion process for an SPO is similar to that outlined in SAP Note 1685280 for the conversion process.

- The note provides specific instructions and requirements for different versions of SAP NetWeaver BW (Business Warehouse):

  - For SAP NetWeaver BW 7.30, customers need to import Support Package 11 (SAPKW73011) into their BW system. More details about this Support Package are available in SAP Note 1878293.
  
  - For SAP NetWeaver BW 7.31 (enhancement package 1), customers need to import Support Package 10 (SAPKW73110) as detailed in SAP Note 1882717.
  
  - For SAP NetWeaver BW 7.40, Support Package 5 (SAPKW74005) should be imported, as explained in SAP Note 1888375.

- In urgent situations, the correction instructions may be implemented as an advance correction. For information on how to apply these instructions, the note advises readers to consult SAP Note 1668882, which discusses using transaction SNOTE.

- Lastly, it mentions that before a Support Package is officially released, preliminary versions of the related SAP Notes might be accessible, identifiable by the phrase "Preliminary version" in the short text.

In summary, SAP Note 1891529 addresses the previously unsupported reconversion of semantically partitioned, SAP HANA-optimized DSOs, outlining the solution and prerequisites for application in different SAP NetWeaver BW versions, and points to related SAP Notes for further details on Support Packages and correction instructions.