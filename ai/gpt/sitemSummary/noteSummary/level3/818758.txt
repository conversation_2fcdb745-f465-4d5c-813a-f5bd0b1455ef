SAP Note 818758 addresses an issue where a customer exit (a specific point in an SAP system where a customer can insert custom code) is not functioning correctly in conjunction with LAMA, which is related to Material Number handling.

**Symptom:**
The primary symptom described in the note is that the customer exit, specifically Customer Include LXCKAF00, does not work as intended when dealing with LAMA.

**Other Terms:**
Key terms related to this note include Long Material Number (LMN), Manufacturer Part Number (MPN), and the Customer Include LXCKAF00, as well as MGV, which presumably stands for Materialnumber (Matnr) Greater than 18 Characters / Long Material Number Validation (MGV_LAMA).

**Reason and Prerequisites:**
The issue arises when using the Industry Solution DIMP (Discrete Industries and Mill Products) in combination with either the Long Material Number functionality (MGV_LAMA) or the Manufacturer Part Number (IS_AD_MPN).

**Solution:**
The note advises users to implement correction instructions to resolve the issue. It also mentions that the correction belongs to the ECC-DIMP-Package named MGV_MATNR_LAMA, indicating that the solution involves a support package or enhancements specifically for the mentioned package.

To resolve this issue, users would need to access the correction instructions provided by SAP and apply the necessary changes referred to in this note, potentially with the help of their SAP support team.