SAP Note 2901278 addresses issues arising from the transition of SAP GUI for Windows from using Internet Explorer Control to the Microsoft Edge based on Chromium (WebView2 control) for rendering HTML pages within the SAP GUI HTML Control. 

Summary of the SAP Note:

- Prior to SAP GUI for Windows 7.70, legacy HTML pages (Quirks Mode) and modern HTML pages (Standards Mode) were supported using Internet Explorer Control.
- From SAP GUI 7.70 onwards, an alternative Edge-based browser control (WebView2) is introduced and becomes the default in SAP GUI 8.00.
- WebView2 does not support legacy HTML rendering, and it has some functional limitations compared to the Internet Explorer Control.
- Known limitations using Edge with Chromium in SAP GUI HTML Control are listed, including unsupported MHT files, Adobe XML Form Module Library-generated PDFs, .bin files, and more.
- The note provides workarounds for dealing with legacy HTML issues, such as using a "Fallback Configuration file" to specify when to use IE Control as a fallback, as detailed in SAP Note 2957665.
- Some limitations are permanent while others may be resolved through updates from Microsoft or SAP. Issues like file drag-and-drop operations, SSO with HTTPS URLs, and extension support are examples.
- Certain issues have been solved, like StatusBar refresh, certificate selection dialog suppression, and opening the Edge browser in a new window with SAP GUI for Windows 8.00, among others.
- Further information on special handling required for SAP GUI HTML control using Edge based on Chromium is provided, including digital signature visualization, home page configuration, and performance comparisons to Internet Explorer Control.

The note also provides links to other relevant SAP Notes and Microsoft documentation for more detailed guidance on addressing specific issues and limitations.