SAP Note 921820 provides an overview of the authorization concept in SAP BW 3.x systems, focusing specifically on reporting authorizations. Here's a summarized version of the note's content:

1. **Symptom**: The note aims to clarify the main concepts and rules for authorization checks in BW systems to assist with problem analysis and new authorization creation.

2. **Other Terms**: FAQ is mentioned as a related term.

3. **Reason and Prerequisites**: The note differentiates between two types of BW authorizations: reporting authorizations (checked by the OLAP processor) and RS authorizations. It focuses exclusively on reporting authorizations.

4. **Solution**:
   - **(I)**: For an InfoObject to require authorization checks, it must be set as authorization-relevant in transaction RSD1 and must exist in an activated authorization object for the InfoProvider.
   - **(II)a-d**: Authorization checks are performed for each relevant and activated authorization object separately. Positive results on all authorization objects are required for query data to be displayed. Each field in an authorization object must be positively checked.
   - **(II)e**: Hierarchy node authorizations are also considered, with hierarchy authorizations being stronger than flat list authorizations.
   - **(III)**: Hierarchy authorizations via field 0TCTAUTHH are always checked if the InfoObject is authorization-relevant and used in hierarchies.
   - **(IV)**: Authorizations do not automatically act as query filters; authorization variables can be used to fill in authorized values for the user dynamically.
   - **(V)**: For display attributes, users must have full authorization to access them.
   - **(VI)**: Compatibility mode is addressed in Note 728077.
   - **(VII)**: The colon ":" as an authorized value is explained in Note 727354.
   - **(VIII)**: Multi-dimensional authorizations allow for combinations of characteristic values and are detailed in Note 668520.
   - **(IX)**: Exclude selections in queries: Users require star authorization ("All values") to be allowed for exclude selections.
   - **(X)**: The authorization log in transaction RSSM is essential for analyzing authorization problems. Further details are provided in Note 790323.
   - **(XI)**: Authorization profiles can include variables filled via customer exits.
   - **(XII)**: Information about generating authorizations is available in Note 824500.
   - **(XIII)**: For MultiProviders, the relevant authorization objects must be activated.
   - **(XIV)**: New InfoProviders automatically activate all potentially relevant authorization objects.
   - **(XV)**: Information regarding compound characteristics’ authorizations is provided in Note 967403.

In conclusion, SAP Note 921820 is an essential resource for understanding the authorization checks within SAP BW systems, offering guidance on setting up and troubleshooting reporting authorizations. It includes references to other notes for in-depth information on specific topics.