SAP Note 1934038 addresses the housekeeping of the UJ0_STAT_DTL table in the context of SAP Business Planning and Consolidation (BPC) for NetWeaver versions 7.5, 10.0, and 10.1.

The cause of the potential issue is the growth of the UJ0_STAT_HDR and UJ0_STAT_DTL tables due to the activation of the BPC_STATISTICS parameter, which gathers query performance statistics. If this parameter is not switched off after performing performance investigations, these tables can become very large and potentially impact system performance.

To resolve this, the note offers two recommendations:
1. Always turn off (deactivate) the BPC_STATISTICS parameter as soon as the necessary BPC performance statistics trace is complete.
2. Run the program UJ0_STATISTICS_DELETE through transaction code SE38 to delete old and unnecessary performance statistics. This can be done immediately or scheduled as a background job. For more information on scheduling, the note refers to SAP Note 1648137.

The keyword associated with this note is UJSTAT, which likely refers to the nomenclature used by SAP BPC for table names and related parameters for performance statistics.