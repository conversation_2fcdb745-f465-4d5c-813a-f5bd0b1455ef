SAP Note 3039679 addresses an issue related to the integration of Microsoft Office in the "Manage Risk Assessments" app within the Environment, Health, and Safety (EHS) module in SAP S/4HANA, specifically during a system conversion or upgrade to SAP S/4HANA 2021.

Summary of the SAP Note:

- **Symptom**: During an upgrade or conversion to SAP S/4HANA 2021, users working with risk assessments in EHS are affected.

- **Other Terms**: The terms related to this note include Environment, Health and Safety, Health and Safety Management, and Operational Risk Assessment.

- **Reason**: The note is issued due to the "renovation" of a feature.

- **Solution**: With the deprecation of in-browser Microsoft Office support, the integration for editing Excel documents within the "Manage Risk Assessments" app has been refactored to use a download and upload process. The steps for the updated process include:
  1. Downloading the Excel document, which includes all relevant measurement and sampling data.
  2. Editing the document in Microsoft Excel locally on the user's machine.
  3. Uploading the edited document back to the SAP system, where the exposure assessment result is extracted and updated in the system.

  There is no change to the business process, only to the method of handling the document.

- **Required and Recommended Actions**:
  1. Users should verify if they use the "Quantitative Exposure Assessment" step by checking the customizing activity for the assignment of the analysis method (EXP_ASSMNT_QUANT).
  2. If the step is in use, users should inform business users about the new handling of the document editing process.

- **How to Determine Relevancy**: The note is relevant for businesses that use the EXP_ASSMNT_QUANT analysis method. The relevance can be determined by checking if table EHHSSC_ANMASSIGN has entries with the field TAB_ID set to "EXP_ASSMNT_QUANT".

The note indicates that the change is intuitive enough that extensive user training should not be necessary. It advises informing users of the new approach for handling Microsoft Excel documents within the EHS risk assessment process.