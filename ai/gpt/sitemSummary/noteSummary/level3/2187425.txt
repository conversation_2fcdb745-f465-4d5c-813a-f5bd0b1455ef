SAP Note 2187425 provides information about the new method of shipping ABAP corrections through SAP Notes using Transport based Correction Instructions (TCI). This method is now a part of the standard maintenance and upgrade procedures, which means users may encounter it in software logistic tools like SPAM, SAINT, SUM, and Note Assistant.

Key points from this note include:

- TCIs are a new channel for delivering ABAP corrections.
- You might need to handle TCIs when using software logistic tools: SPAM, SAINT, SUM, and Note Assistant.
- The prerequisites for handling TCIs are:
  - A minimum SPAM version of 70; the latest version is recommended.
  - For different SAP BASIS releases, specific bootstrapping SAP Notes must be applied: 
     - SAP BASIS 700 requires SAP Note 2446868.
     - SAP BASIS 701 and 702 require SAP Note 2444141.
     - SAP BASIS 731 and onwards require SAP Note 1995550.
- TCIs are not supported in SAP_BASIS releases 710, 711, and 730. For systems upgrading to these releases, it's recommended to choose a target release or support package where the functionality delivered as TCI in lower releases is available.

For detailed information and instructions regarding SAP Note Transport based Correction Instructions, one should refer to the attached PDF document provided with the SAP Note.