SAP Note 2755642 addresses an issue where applications are unable to influence the global value for the number of table entries in the buffer when using the function module MATERIAL_READ. The note introduces the function module MATERIAL_READ_SET_BUFFER_SIZE, which allows an application to set a maximum number of entries in the buffer tables. However, it is important to note that the number of entries can only be increased once in order to prevent premature buffer resets that could affect other applications.

The note is relevant to function modules related to material reading, such as MARA_SINGLE_READ and MARC_SINGLE_READ, and it is associated with various terms including MATERIAL_READ, MAXTZ, MTCOM, MTCOM-MAXTZ, KZRFB, MIRO, and NF.

The reason behind this note is to improve performance, and the prerequisites are not specified. The provided solution to this issue is to implement the correction instructions attached to the note or to import the specified support package, which will include the fix.