SAP Note 486096 is a follow-up to manual corrections that were applied as per SAP Note 485060. This note is specifically relevant to those who manually implemented note 485060, which provided a program correction impacting the mapping tables for LDAP directory synchronization.

Symptom:
The note addresses the necessity of additional actions after the manual implementation of a previous correction (Note 485060).

Other Terms:
The note pertains to LDAP (Lightweight Directory Access Protocol), directory synchronization, and specifically the DBOBJ and LDAPPROP mapping elements within SAP.

Reason and Prerequisites:
After manually implementing the corrections from SAP Note 485060, there's a need for new entries in the mapping tables, which were not included in the manual implementation. The note indicates that the package from 485060 contains the necessary changes, but if those were applied manually, then special transport of table contents is necessary.

Solution:
The solution provided in this note involves downloading special transport files from SAPSERV. The file path on SAPSERV is provided (/general/R3server/ABAP/score.0486096), and SAP Note 13719 is referenced for guidance on importing this special transport into the user’s SAP system. It is mentioned that manual correction of the table contents is not possible; the special transport method must be used to correctly update the system.

In summary, SAP Note 486096 outlines what steps should be taken to consolidate the database objects (DBOBJ) and LDAP properties (LDAPPROPx) after a manual correction from a prior note (SAP Note 485060). It provides a link to special transport files necessary for the update and references another note for the import instructions. It stresses that manual changes to the table contents are not viable, thus emphasizing the importance of following the transport procedure.