SAP Note 570673 addresses an issue specific to the Real Estate (RE) extension in the SAP system. The key points of this note are as follows:

**Symptom**:
The note provides detailed information about the Business Transaction Events (BTE) application indicator 'RE' for the SAP Real Estate extension.

**Other Terms**:
The contexts mentioned are related to financial processes in Real Estate like figures, clearing, dunning, and service charge settlement (SCS).

**Reason and Prerequisites**:
The application indicator 'RE' was introduced in support package 4 of the SAP R/3 Enterprise extension 1.10, but it was delivered inactive. This inactivity can lead to errors in financial functions like clearing and dunning when these functions are executed in the Real Estate extension. For example, during clearing, the system may give an error message indicating "Item already cleared" which impacts the service charge settlement process.

**Solution**:
To resolve these issues, the 'RE' application indicator must be set to 'Active' ('X'). This solution is included in Support Package 5 of the SAP R/3 Enterprise extension 1.10 and 2.0. For systems with Support Package 4, the note provides a manual procedure to activate the 'RE' indicator: 
1. Run transaction FIBF.
2. Go to 'Settings -> Identification -> SAP applications'.
3. Set the 'Active' flag for the 'RE' application to 'X'.

By following the solution, users should be able to overcome the errors encountered in the financial functions of the SAP Real Estate extension related to BTE application indicator 'RE'.