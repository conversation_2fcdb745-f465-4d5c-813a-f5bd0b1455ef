SAP Note 1122142 pertains to the configuration of Adobe Document Services which are utilized by SAP Interactive Forms by Adobe. The main focus of this note is to guide users on how to ensure printed output from these forms can utilize a specific printer input tray or make use of printer resident fonts.

Key points from this note include:

- Adobe Document Services can render various output formats by leveraging a special print driver along with an XDC (XML configuration) file.
- To direct output to a specific printer tray or to utilize a printer's built-in fonts, the relevant XDC file on the SAP server must be customized. This customization can be executed using Adobe's XDC Editor.
- Modifications are necessary when there's a requirement to use a printer-specific trait (like an input tray or resident font) which isn't configured by default.
- Detailed instructions for making these adjustments have been provided as PDF attachments to this note; one for input tray configuration and another for configuring printer resident fonts.
- For situations where the desired font is only available on the printer and not on the server (using Unix systems as an example), the encoding in the XDC file should be altered. Specifically, if trying to use a font like WingDings that is not present on Unix, the encoding should be changed from "fontSpecific" to "ISO-8859-1".

This SAP Note serves as a technical guide for administrators or users who need to adjust printing settings for forms so they can properly utilize capabilities that are specific to certain printers. This is particularly useful for ensuring that outputs from SAP Interactive Forms by Adobe are formatted and printed as required by the business processes.