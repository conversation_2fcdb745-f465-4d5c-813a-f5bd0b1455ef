SAP Note 2603056 addresses an issue with report FARR_TRANS_REV_URDR where it incorrectly updates entries in the FARR_D_INV_FX_ED table that have an empty period field. This update should not occur because it results in inconsistent data.

The demonstrated scenario shows a performance obligation (POB) with ID 1001 having two entries, one with an empty period and a zero amount (BETRW), and another with a period specified and a zero amount as well. However, when an invoice for 100 EUR is processed for POB 1001, the report erroneously updates the entry with the empty period, changing the amount to 100 EUR, which should not happen.

The issue is classified as a program error. The solution for resolving this problem is to apply the given SAP Note to correct the report behavior and prevent this incorrect update from happening.