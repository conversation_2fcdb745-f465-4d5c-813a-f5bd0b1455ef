SAP Note 2639505 outlines the restrictions associated with Low Level Variant Configuration when using the SAP HANA database. The note is specifically relevant for users who utilize MRP live and transaction CS12H, as these areas use a new mass-processing implementation optimized for HANA's high performance.

However, this new implementation does not include all the features from the classic Variant Configuration. As such, the note provides references to additional notes that detail these restrictions based on the release version of the SAP system:

- For SAP_APPL 617, it refers to Note 2639936.
- For SAP_APPL 618 and subsequent releases, S4CORE 100, and SAPSCORE 112 (and their subsequent releases), it refers to Note 2639994.

The referred notes (2639994 and 2639936) contain comprehensive lists of limitations that users might encounter while using Low Level Variant Configuration on HANA. These include unsupported features such as multiple dependent procedures, certain kinds of value assignments, direct linking between variant tables and database tables, particular variant functions and syntax, as well as specific conditions regarding class nodes and decimal number comparisons.

By informing users about these restrictions, SAP Note 2639505 serves as a guide to avoid unsupported scenarios and potential issues when working with Variant Configuration in an environment powered by the HANA database. This guidance ensures that users are aware of the current limitations and supports proper functionality and system behavior.