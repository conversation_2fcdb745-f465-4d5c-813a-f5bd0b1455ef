SAP Note 2711123 addresses an issue encountered during the migration of tasks within an SAP system. Specifically, this issue occurs when the `R_EHFND_WFF_RESTART_PROCESSES` report is run and there is a missing execution node for an action definition. In such cases, the system tries to compensate for the missing node by creating a dummy node, which results in a short dump—a critical system failure that terminates the current transaction.

The note explains that the root cause of this problem is a program error and it recommends applying the corrections provided by the note before beginning post-conversion activities to prevent migration attempts of missing execution nodes.

To solve the issue, the SAP Note suggests that users apply the correction instructions included within the note or update their system with the Support Packages that include the necessary corrections. The specific Support Packages can be found in a separate section presumably included in the full document of the note.

There are no external references stated in this note, meaning that the information and solution are self-contained within SAP Note 2711123. Users affected by this issue are directed to follow the correction instructions or apply the mentioned Support Packages to resolve it.