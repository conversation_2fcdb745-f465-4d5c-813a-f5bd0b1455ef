SAP Note 2403248 pertains to the availability of functionalities for adding new depreciation areas or accounting principles in the new Asset Accounting (FI-AA new) module within SAP.

Here is a summary:

**Symptom:**
Users wish to create a new depreciation area in Asset Accounting (new) after the initial setup has been implemented.

**Other Terms:**
The note mentions terms like automatic opening of new depreciation areas, subsequent creation, and various programs and BAdIs related to this process, as well as scenarios for migration and the application in SAP S/4HANA environments.

**Reason and Prerequisites:**
The note assumes that the user is already using Asset Accounting (new) productively.

**Solution:**
The note provides details on various programs and functions for implementing new depreciation areas or accounting principles:

1. **Program RAFABNEW:**
   - Usage: For subsequent implementation of a new depreciation area.
   - Availability: The program is not available as of SAP Simple Finance 1503 and SAP S/4HANA, on-premise Edition 1511 due to a change in data structure with journal entry introduction.

2. **Program RAFAB_COPY_AREA:**
   - Usage: For adding a new depreciation area to an existing valuation, e.g., adding insurance values within an existing "Local GAAP" accounting principle.
   - Availability: As of SAP S/4HANA 1809.

3. **Subsequent Implementation of a Further Accounting Principle function:**
   - Usage: For adding a new accounting principle or valuation such as implementing IFRS values alongside Local GAAP.
   - Availability: As of SAP S/4HANA Finance 1605 SP05 and SAP S/4HANA 1610.

The note includes a table summarizing the availability of these programs and functions across different SAP releases:
- Program RAFABNEW is only available for SAP ERP 6.0 EHP 7 or higher with SAP Simple Finance add-on 1.0.
- Program RAFAB_COPY_AREA becomes available starting with SAP S/4HANA 1809.
- Subsequent Implementation of a Further Accounting Principle function is available starting with SAP S/4HANA Finance 1605 SP05 and SAP S/4HANA 1610, and also in later versions.

**Additional Information:**
The note clarifies that while SAP ERP supports subsequent implementation of a new ledger and a switch from the accounts approach to the ledger approach in classic Asset Accounting through migration scenarios 7 and 8, these scenarios are not supported in Asset Accounting (new).

**References:**
There are no references provided within this SAP Note. 

That concludes the briefing on SAP Note 2403248 - FI-AA (new): Availability of RAFABNEW.