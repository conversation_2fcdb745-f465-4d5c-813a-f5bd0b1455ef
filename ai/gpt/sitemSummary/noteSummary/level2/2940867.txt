SAP Note 2940867 is a Central Correction Note for issues related to the SAP S/4HANA Data Migration content specific to the SAP S/4HANA 1909 release, specifically for service packs SP00 to SP02. This note targets the SAP S/4HANA Migration Cockpit, which is involved in transferring data using files or using staging tables.

The symptom described in this note is a problem with the migration content where certain fields, such as Account Currency and Field Status Group, are incorrectly set as mandatory during the data migration process.

The note is applicable if the following conditions are met:

- The user has installed SAP S/4HANA 1909 (SP00 - SP02).
- The SAP S/4HANA migration cockpit is being used.
- The pre-delivered SAP S/4HANA Data migration content is being used without any modifications.

The solution provided in the note includes a Transport-based Correction Instruction (TCI) that corrects the issues related to the SIF_GL_ACCOUNT_2 migration object, specifically making the fields Account Currency and Field Status Group optional, which were mistakenly set as mandatory. This correction is automatically applied to the generated migration objects but will not apply to any modified or copied objects a user might have.

The note emphasizes that any modifications or copies of the migration object will not receive this correction, and the user may need to refer to another KBA, SAP Note 2543372, for guidance on how to implement Transport-based Correction Instructions. Additionally, it provides information on how to download and upload the TCI.

Reference is made to SAP Note 2939981, which details the issues with the message FH219 for G/L Account migration, where the cost element category is not used for posting-relevant business transactions, and provides a resolution to this specific issue.

In summary, SAP Note 2940867 offers a correction to an issue faced during data migration in SAP S/4HANA 1909 and guides users on how to apply this correction using TCI procedures.