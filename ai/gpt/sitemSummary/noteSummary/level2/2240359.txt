SAP Note 2240359 addresses issues related to "Always-Off" Business Functions in the context of a system conversion to SAP S/4HANA. The note clarifies the behavior of business functions during the conversion process based on their status of either always_on, customer_switchable, or always_off. Key points are:

- If a business function that is active in the source system is marked as always_off in SAP S/4HANA, the system conversion cannot be performed.
- If a business function is inactive in the source system but always_on in the target S/4HANA system, it will be activated automatically during the conversion.
- Business functions that have been deleted in SAP S/4HANA do not block the conversion.

The Maintenance Planner tool is used to identify business functions that are active and marked as always_off or deleted in SAP S/4HANA, as these will impede the conversion.

The solution provided by the note includes:

- A list of attachments that detail the business functions classified as always_off for various SAP S/4HANA releases and feature pack stacks.
- An explanation that business functions activated in a productive system are generally irreversible and deactivation could cause serious issues or data loss. This also applies to business functions marked as reversible, which should only be switched off in non-productive systems.
- Highlights of exceptions where selected always_off business functions can be turned off in a productive environment through a special procedure detailed in other SAP Notes (2227901, 2427342, 2413469, 2427371, 2431838, 2432160, 2468447, 2510861, 2487913, and 2956616).
- Instructions for dealing with situations where an always_off business function that is not covered by the exceptions is active in a productive system. In such cases, customers should open a support message to possibly get an individual workaround.
- Information on resolving issues where deactivation of a business function has not been correctly reflected in the SAP Maintenance Planner or SAP Readiness Check, potentially requiring a re-sync of system information or reference to SAP Note 2323131.

This note is important to organizations planning to convert their systems to SAP S/4HANA, as it ensures that they consider the status of active business functions in their current SAP environment and take appropriate steps to comply with conversion prerequisites.