SAP Note 2086815 addresses an issue where the indicator that flags a constructor of an exception class as generated or not generated is not considered during the transport process. Because of this, the information about whether constructors were manually edited or automatically generated gets lost in the target system after transport. Additionally, this property could not be properly transmitted through the Note Assistant or Correction Workbench prior to implementing this note.

The issue occurs when an exception class is edited using the ABAP Development Tools for SAP NetWeaver (ADT) and the generated constructor is manually modified.

To resolve this issue, the SAP Note introduces a new pragma: ##ADT_SUPPRESS_GENERATION. This pragma is to be automatically added to the METHOD statement of a constructor in an exception class to prevent it from being overwritten when the class is edited in the backend with the form-based Class Builder. The inclusion of this pragma ensures that the source code reflects the correct state of the constructor when exported, and also remedies the issues with Note Assistant and Correction Workbench regarding this property.

The solution to the issue is to import the support package provided by SAP or to implement the instructions contained in this SAP Note. The note does not reference any other notes.