SAP Note 2011558 addresses an issue in the Production Planning - Shop Floor Control application of SAP. The problem is related to blocking the data of customers or vendors that might still be associated with pending goods movements or externally processed operations within production orders.

Key elements of the note are:

- The note indicates that the new functionality will be incorporated starting from SAP_APPL 617 with support package SP05.
- Customizing enhancements have been made allowing application names and classes to be registered for 'End of Purpose' (EoP) checks to prevent blocking as long as the customer/vendor is required for the production order.
- Two new application entries for 'Customer Master Data' and 'Vendor Master Data' have been added to "Define and Store Application Names for EoP Check".
- A new class called `CL_WUC_PP_SFC_EOP_CHECK` ("Where-Used-Check Production Order") has been registered for EoP checks within production orders for both customer and vendor data.
- To improve performance, new database indexes have been predefined for tables AFFW and AFVC. These indexes can be activated in the ABAP Data Dictionary to optimize database accesses and are relevant for fields linked to customers (KUNNR) and vendors (LIFNR).
- The note does not mention any references to other SAP Notes.

You should refer to SAP Note 2007926 for additional context related to the reasons and prerequisites for this update.

In summary, SAP Note 2011558 offers a solution to avoid premature blocking of customer or vendor data that are still in use in production orders. It introduces new entries in the customizing activity, provides a new class for 'Where-Used-Check Production Order,' and suggests the implementation of specific database indexes to enhance performance.