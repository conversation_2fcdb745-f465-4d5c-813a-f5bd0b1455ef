SAP Note 2484134 provides important release information and restrictions for customers planning to use "SAP for Banking" solutions in conjunction with SAP S/4HANA 1709 (on-premise edition). Below is a summary of the key points in the note:

1. SAP Note 2211665's statements regarding release and restrictions also apply to SAP S/4HANA 1709.
   
2. FS-CML (Consumer and Mortgage Loans):
   - There is no integration with the "Central Payment for SAP Central Finance" application; its functions are not available for FS-CML.
   - CML-specific functions concerning collaterals and collateral objects are not available. Customers are advised to use the FS-CMS (Collateral Management) application instead.
   
3. FS-CYT (Capital Yield Tax Management):
   - This is a separate component, not part of SAP S/4HANA 1709's delivery.
   - As of Support Package 12 for "SAP Capital Yield Tax Management for Banking 8.0", it can be used as an add-on to SAP S/4HANA 1709.

4. FS-PE (Payment Engine):
   - Released for integration with SAP S/4HANA 1709 as of Release 8 and Support Package 7.

5. FS-TXS (SAP Funding Management):
   - It is not released for use in SAP S/4HANA 1709.
   - A separate installation in combination with a remote communication to FS-CML is permitted as of SAP Funding Management 3.0 Support Package 06.

The note references additional SAP Notes for more information on specific topics:
   - SAP Note 2491467 details broader restrictions associated with SAP S/4HANA 1709.
   - SAP Note 2369934 discusses the lack of CML-specific functions in SAP S/4HANA related to collaterals and suggests using FS-CMS instead.
   - SAP Note 2346233 outlines the implementation steps for activating the Central Payment functionality in SAP Central Finance, which is not available for FS-CML.
   - SAP Note 2211665 provides release information for the same "SAP for Banking" solutions in the context of SAP S/4HANA 1511.

Overall, this SAP Note is critical for organizations in the banking sector that use or plan to upgrade to SAP S/4HANA 1709, as it outlines the applications and solutions from "SAP for Banking" that can be used, along with their respective restrictions and the necessary considerations for integration.