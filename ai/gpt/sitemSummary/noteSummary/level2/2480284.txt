SAP Note 2480284 deals with the issue of loading hierarchy DataSources via Operational Data Provisioning (ODP) in SAP systems, particularly when dealing with certain source system constraints. Here is a summary of the key points:

**Symptoms:**
- Users cannot load hierarchy DataSources via ODP in SAP NetWeaver 7.0x to 7.3x source systems. This issue impacts two contexts:
  - ODP-BW: Hierarchies from InfoObjects in SAP BW source systems for versions between SAP BW 7.0x and SAP BW 7.3x.
  - ODP-SAPI: Hierarchy DataSources/Extractors in any SAP Source System that uses the ODP Data Replication API 1.0 or the ODP Replication API 2.0 in conjunction with PI_BASIS 7.30.

**Other Terms:**
- RSDS
- DataSource *_HIER not released
- program RODPS_OS_EXPOSE

**Reason and Prerequisites:**
- There is no automated analysis for this issue.

**Solution:**
- Implement specific SAP Notes to enable the extraction of hierarchy DataSources via ODP:
  
  For ODP-BW:
  - For SAP BW v7.0x but lower than 7.3x: implement SAP Note 2418250 on both the source and target systems.
  - For SAP BW v7.3x: implement SAP Note 2469120 on the source system.
  
  For ODP-SAPI:
  - For source systems using ODP API 1.0: implement SAP Note 2418250 on both the source and target systems and consider the pre-step of SAP Note 2768527 in PI_BASIS 7.0-7.11 source systems.
  - For source systems with ODP API 2.0 and PI_BASIS 7.30: implement SAP Note 2469120.

- As an alternative to the preceding recommendation, use the SAP BW Source System type for loading InfoObject hierarchies or the SAP Source System type for loading Hierarchy DataSources in target systems of specific releases (7.3x, 7.4x, or 7.5x). However, in SAP BW/4HANA, ODP is the only supported source system type.

- For BW/4HANA remote conversion scenarios, there are certain limitations regarding the data flow of hierarchy DataSources that need to be resolved before conversion.

**General Information:**
- SAP Note 2481315 and an FAQ provide additional details about the availability and functionality of ODP-based extractions from SAP systems to SAP BW or SAP BW/4HANA.

**References:**
- The note refers to additional SAP Notes related to ODP data extraction, hierarchy visibility within ODP sources, use of ODP APIs, and simplifications when transitioning to SAP BW/4HANA. These references provide further information on support packages needed for specific system versions, limitations of ODP replication frameworks, and recommended practices for ensuring a system is updated correctly to use ODP interfaces.

The note clarifies that there are prerequisites for successfully implementing these solutions, including support packages and other SAP Notes that address related errors and provide additional guidance for particular scenarios.