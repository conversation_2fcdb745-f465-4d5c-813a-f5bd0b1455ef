SAP Note 2793305 addresses an issue encountered during the material migration process in the SAP S/4HANA Migration Cockpit. Specifically, users receive an error message during the 'Convert Values' step, indicating that a provided value for the 'Planning Cycle' field is invalid for the domain LFRHY.

The problem is rooted in the incorrect conversion rule being assigned to the receiving API field PLNG_CYCLE. The conversion rule ought to have been CVT_MRPPP, but it was mistakenly set as CVT_LFRHY.

The solution provided in the note is to change the conversion rule to the correct one, CVT_MRPPP for the field PLNG_CYCLE. Affected releases and their corresponding service packs are listed, with the resolution being either to see a document titled "Change_CVT_LFRHY" for release 1709 or to implement a TCI (Transport-based Correction Instruction) Note for releases 1809 SP00 through SP02.

In addition, the note references another central correction note, 2793418, which addresses various content issues related to the SAP S/4HANA 1809 release and provides a list of other affected migration objects and their relevant SAP notes for detailed information. Another reference, Note 2755460, is a similar central correction note for the 1809 release providing fixes for different migration and translation objects through a TCI.

In summary, SAP Note 2793305 provides a corrective action for the planning cycle value check error during material migration in the SAP S/4HANA Migration Cockpit by changing the conversion rule for the PLNG_CYCLE field and provides references for potential issues with other migration objects.