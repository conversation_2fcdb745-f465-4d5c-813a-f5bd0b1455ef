SAP Note 2186803 pertains to the handling of New General Ledger (NewGL) tables and customer enhancements during an upgrade to SAP Simple Finance, on-premise edition.

Symptom: This note addresses the scenario where a customer has implemented enhancements in the NewGL (General Ledger) within their current SAP system, and they are planning an upgrade to SAP Simple Finance.

Solution: During the upgrade to SAP Simple Finance, the compatibility views for the Standard SAP NewGL tables that have customer enhancements are considered. A tool (transaction FINS_MIG_REGENERATE) is used to include these enhancements into the corresponding compatibility views.

Compatibility views for customer-specific NewGL tables are generated to mirror the structure of the existing tables. The naming conventions for these compatibility views are:
- ZFGLV_GLSI_C<number> for line item tables
- ZFGLV_GLTT_C<number> for totals tables

The table FGLT_GLTAB contains the numbering and assignment information for these tables. A redirection is automatically established in the Data Dictionary from the NewGL tables to the corresponding compatibility views to facilitate data selection from the new data source. Old data from the NewGL tables can still be accessed using CDS-Views with the naming pattern V_<tablename>_ORI.

It is important to note that after completing the SAP Simple Finance upgrade, it is no longer possible to extend the existing NewGL tables.

References: There are no references provided within this SAP Note.