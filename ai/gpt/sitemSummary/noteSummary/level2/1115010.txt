SAP Note 1115010 addresses an issue regarding the migration of update rules to transformations within SAP NetWeaver BI 7.0, specifically when using Support Package 17 (SP17) or after implementing Note 1043343. Here's a summary of the note:

**Symptom:**
When trying to migrate an update rule to a transformation, the resulting transformation may end up having no rules. Additionally, incorrect error messages might appear during the check process. This issue particularly affects update rules that write to a DataStore object.

**Other Terms:**
None mentioned directly in this note.

**Reason and Prerequisites:**
The problem is due to a program error and occurs if Support Package 14 or higher is imported, or if Note 1043343 has been implemented.

**Solution:**
The note recommends importing Support Package 17 for SAP NetWeaver 7.0 BI with the patch SAPKW70017. Details of the Support Package can be found in Note 1106569 titled "SAPBINews BI7.0 Support Package 17". If urgent, the correction instructions can be applied as an advance correction.

Before implementing the above solution, users must delete the faulty transformation and recreate it from the update rule after applying the correction instructions. Additionally, it's essential to first read Note 875986, which contains information about using transaction SNOTE.

References contained in this note:
- Note 1106569 provides details about what's included in Support Package 17 for SAP NetWeaver BI 7.0 and offers guidance on implementing the update.
- Note 1043343 is related to updates on SAP NetWeaver BI prior to SP17.
- Note 875986 offers important information about the transaction SNOTE, which is used for implementing and managing SAP Notes.

Customers are advised to follow the instructions and guidelines provided in these related notes as part of resolving the migration issue.