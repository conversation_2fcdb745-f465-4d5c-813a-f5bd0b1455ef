SAP Note 1797558 addresses an issue where, after a reorganization, the table FAGL_SPLINFO may contain incorrect profit center or segment entries. This is in contrast to the reorganization table FAGL_R_SPL, where the correct new profit center and segment are set. The problem arises because the system does not reconcile the profit center or segment between the reorganization data and FAGL_SPLINFO.

The solution offered involves implementing program corrections or executing manual activities. A reconciliation will be executed after the generation of the new profit center/segment entries in FAGL_SPLINFO. If the reconciliation finds any discrepancies, an error message will be triggered by the system.

This SAP Note should be used in conjunction with the following related notes that may be applicable to similar contexts or problems:
- SAP Note 1852410, which discusses errors during the reassignment of receivables related to sales and distribution documents, specifically the error messages FAGL_REORGANIZATION545 and FAGL_REORGANIZATION566.
- SAP Note 1810392 and SAP Note 1808980, both addressing error message FAGL_REORGANIZATION 566 during reassignments of receivables and payables related to profit center or segment reorganization.
- SAP Note 1627018, a composite SAP Note that doesn't directly provide solutions but guides users to related SAP Notes concerning issues of segment reorganization.
- SAP Note 1471153, another composite SAP Note focusing on the reorganization of profit centers and Funds Management in connection with the new General Ledger functionality. 

Users experiencing the symptoms described in SAP Note 1797558 should refer to the correction instructions provided to align their FAGL_SPLINFO table with reorganization data and to check the related SAP Notes for a comprehensive understanding and resolution of similar issues.