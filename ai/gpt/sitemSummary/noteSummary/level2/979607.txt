SAP Note 979607 addresses a limitation in SAP NetWeaver 2004s BI, where you couldn't previously access runtime information of a request within start, end, expert, and rule routines. This note introduces a new attribute 'P_R_REQUEST' that allows you to use methods of the interface 'if_rsbk_request_admintab_view' at runtime to determine attributes such as the source system.

The problem originated from a program error, and to resolve it, SAP recommends importing Support Package 10 for SAP NetWeaver 2004s BI – specifically, patch SAPKW70010. Detailed information about the Support Package is provided in Note 914304 "SAPBINews BI 7.0 Support Package 10".

For those who need an urgent fix, the note suggests implementing correction instructions, but cautions that certain other Notes (932065, 935140, 948389, 964580, and 969846) must be implemented first to avoid problems and syntax errors that may occur when deimplementing some notes.

Users are advised to check if advance corrections are available and to keep in mind that such corrections may initially be labeled as "Preliminary version" until the Support Packages are released. For guidelines on implementing advance corrections, users should also see Note 875986.

Additionally, SAP Note 979607 references SAP Note 1052648, which discusses the migration of transfer rules and update rules for BW7.x. This additional note outlines known issues with the migration tool such as limitations with routines, start routines, return tables, and other areas. It provides guidance on manual adjustments and workarounds that may be necessary post-migration, and it underscores the importance of implementing other related notes to address migration problems.