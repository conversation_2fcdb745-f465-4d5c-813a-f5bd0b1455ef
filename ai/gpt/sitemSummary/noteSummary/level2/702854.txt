SAP Note 702854 elaborates on the procedures to activate or deactivate classic Profit Center Accounting (PCA) and provides solutions for problems that may occur during the process. Below is a summary of the key points covered in the note:

**Activation of classic Profit Center Accounting (PCA):**

1. Activation must be done for each controlling area using specific transactions like OKKS or customizing paths.
2. Standard profit center hierarchy should be created, potentially copied from the Cost Center Accounting hierarchy, and maintained with transactions like 2KEU or KCH1.
3. A dummy profit center is required to ensure postings occur without an initial profit center; created using transaction KE59 and must be valid for all company codes.
4. Profit center master data, including groups, should be created and maintained, and as of Release 4.6C, there is a new concept allowing you to create and activate them at any time.
5. Account assignment objects must be assigned to profit centers with enough lead time (recommended three months before production) to ensure unwanted postings are addressed.
6. Controlling area settings (with transaction 0KE5), control parameters for actual data (with transaction 1KEF), and activation of profit center components (with transaction OKKP) must be maintained.
7. Settings need to be transported to other systems with tools provided in PCA Customizing.
8. General information mentions that PCA should be active long before reporting data for feeder applications.

**Known problems and solutions:**
- Issues like error message KE396 after transport or message KM026 related to dummy profit center can occur, which may require deleting the dummy or modifications as per SAP Notes 131557 and 115069.

**Deactivation of classic Profit Center Accounting (PCA):**

1. Deactivation of online transfer of actual data to classic PCA can be managed with transaction 1KEF by removing indicators and dealing with error messages KM 177 and KM 586 as per SAP Notes 1503968 and 1639693.
2. The year blocking indicator should be removed in 1KEF to prevent updates for the year without triggering error KM 585.
3. For entry related to the fiscal year in 1KEF, ensure accuracy to transfer totals data periodically. KM400 and KM410 messages should be addressed by creating transport requests.
4. Planned real-time update to PCA in transaction OKEQ should be deactivated and entries properly managed.
5. The 'Active Indicator' should be removed in transaction 0KE5 when fully deactivating PCA.
6. Profit center master data shouldn't be deleted initially but can be locked to prevent issues with MM and CO postings.
7. Deletion of PCA transaction data and master data should be done carefully to prevent inconsistencies.
8. Removal of the real dummy profit center should be processed with the attached report Z30PCA23 if classic PCA is no longer in use.

Lastly, the note references additional related SAP Notes that address specific issues, offering further instructions and program corrections: 

- SAP Note 515525 deals with locking of the dummy profit center.
- SAP Note 388178 addresses inconsistencies in company code assignments.
- SAP Note 1639693 and SAP Note 1503968 outline the deactivation process and error message KM 177 handling.
- SAP Note 131557 provides a resolution for profit center validation errors.
- SAP Note 115069 discusses errors KM026 or KM700 related to numeric profit centers.

Users should carefully follow the guidance in the note to successfully activate or deactivate classic PCA as well as address any encountered issues.