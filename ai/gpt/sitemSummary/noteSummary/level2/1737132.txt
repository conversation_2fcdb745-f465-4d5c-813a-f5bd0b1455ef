SAP Note 1737132 addresses an issue where the job status is displayed incorrectly as "Completed" in the "JobStatus" column when an account assignment change is executed and the "Update" option is selected. This incorrect status occurs despite the account assignment change not being fully completed in transaction SM37.

Summary of SAP Note 1737132:

- **Symptom**: The system displays the job status as "Completed" when users execute an account assignment change and update it, even though the process is not actually completed as observed in transaction SM37.

- **Other Terms**: The note makes a reference to “reorganization plan” but does not provide further context in the provided information.

- **Reason and Prerequisites**: The cause of the error is identified as a program error.

- **Solution**: It recommends implementing corrections but does not specify the details of the correction within the provided content.

References within this note point to two other SAP Notes which are composite notes encompassing issues regarding segment reorganization (SAP Note 1627018) and profit center and Funds Management reorganization (SAP Note 1471153). These referenced notes do not provide direct solutions but rather guide users to a list of relevant SAP Notes that address a variety of issues related to their respective reorganization topics. They also contain important information, such as the necessity to check for additional licensing fees and other related notes for detailed solutions.

In conclusion, SAP Note 1737132 detects a discrepancy in job status reporting, specifically for account assignment changes, and requires corrections due to a program error. Users facing this issue should look to implement the proposed corrections detailed in the full note.