SAP Note 2512771 addresses an issue that occurs before the migration of financial documents to Trade Finance is completed. The problem arises when a new SD (Sales and Distribution) document is created, which uses a financial document as a payment guarantee, or when the assigned financial document in a sales order is changed during this migration phase. When this happens, data in the trade finance transaction assigned to these documents is updated, leading to potential data inconsistency for the transaction before and after the migration is finished.

The note applies to various Trade Finance operations, such as Letters of credit (L/C), risk checks, and risk check decisions (RCD). The underlying cause of the issue is identified as a program error.

The solution provided in the note is to implement the corrections attached to the note or to import the relevant Support Package to resolve the issue. Additionally, there is an important instruction for users of SAP S/4HANA 1709 (initial shipment) to implement this note before starting the migration of financial documents to Trade Finance, to prevent such inconsistencies.

No references are provided with this note. It is self-contained and does not refer out to other notes or documents.