SAP Note 1626583 addresses an issue users may encounter during the reassignment of receivables and payables in New General Ledger Accounting (NewGL). Specifically, the error FAGL_REORGANIZATION 533 is reported, indicating that document splitting information necessary for the reassignment process is not available for the given receivable or payable.

Key points of SAP Note 1626583:

- **Symptom**: When reassigning receivables and payables, users experience the error FAGL_REORGANIZATION 533 with the message "Document splitting information not found for receivable/payable &1." This is also accompanied by duplicate entries in the table FAGL_R_SPL, where several entries are flagged with XSPLINFO = X.

- **Other Terms**: The note involves terms related to profit center and segment reorganization, such as Profit center reorganization (PRCTR), Segment reorganization (SEG), and New General Ledger Accounting (NewGL).

- **Reason**: The underlying cause for this issue is identified as a program error.

- **Solution**: Users are advised to implement the attached correction instructions to resolve this error.

Furthermore, this note references two other composite SAP Notes, which offer more extensive information on related topics:

1. SAP Note 1627018: This composite note provides a centralized reference for segment reorganization issues and advises users to consult related notes for solutions. It's important for users to check with SAP regarding potential additional licensing fees if they've activated the business function FIN_GL_REORG_SEG for segment reorganization.

2. SAP Note 1471153: This composite note is a comprehensive guide for profit center and Funds Management reorganization within the new General Ledger framework. It offers direction on locating related notes (using specific prefixes like PRCTR, FM, and SEG), checks for additional license fees with SAP Account Executives, specifies prerequisites such as activation of business functions FIN_GL_REORG_1 or PSM_FM_REASSIGN, and provides instructions for seeking further support for implementation issues.