SAP Note 1854990 addresses an issue where, after executing a profit center reorganization for purchase orders with account assignment, postings are incorrectly made to the old profit center instead of the new one.

**Symptom:**
Users experience that even after the reorganization of profit centers, purchase orders that have only one account assignment (a single entry in the EKKN table) are still posting to the old profit center.

**Other Terms:**
The issue is referenced by the terms CL_FAGL_R_OBJ_TYPE_001_POA and fagl_splinfo, which seem related to the code involved in the erroneous postings to profit centers.

**Reason and Prerequisites:**
The problem occurs when there is only one account assignment for a purchase order in the EKKN table.

**Solution:**
The note suggests implementing program corrections to fix the issue. However, no specific details about the corrections are given in the text provided.

Additionally, the note refers to SAP Note 1471153, which is a composite note concerning profit center and Funds Management (FM) reorganization. Note 1471153 serves as a hub for related issues and includes information on identifying related notes (with prefixes like PRCTR for profit center reorganization), possible additional licensing fees, and prerequisites for using reorganization features. It also instructs users on how to seek further help and check related notes for detailed problem resolutions. SAP Note 1668882 is recommended for correct implementation of Basis-related notes.

In short, SAP Note 1854990 deals with a specific issue related to profit center reorganizations and advises the implementation of program corrections without detailing those corrections in the provided summary. It also points to a more comprehensive composite note for users dealing with reorganization in profit centers and FM.