SAP Note 2348023 provides detailed guidance on the system conversion of the Manufacturer Part Number (MPN) functionality from SAP ECC-DIMP to SAP S/4HANA. It notes that while the MPN functionality was not available in the initial SAP S/4HANA 1511 release, starting from the 1610 release, there has been a technical redesign for handling MPNs that addresses previous shortcomings and provides a redesigned solution.

Key points addressed in the note include:

1. Changes in technical handling and configuration of MPN within SAP S/4HANA 1610. The note highlights the following specifics:
   - Restrictions on certain special characters and leading spaces in material numbers.
   - The MPN material number will be stored directly as a key (MATNR) in the MARA table, without the need for a conversion exit.
   - Rules around concatenating the manufacturing part number and the external manufacturer, including the use of delimiters and behavior depending on whether the 'Concatenated Material No.' option is selected for the material type.
   - Once MPN numbers are created, they cannot be altered in SAP S/4HANA.
   - Saved MPN data in transaction MIGO will not be transferred during migration to SAP S/4HANA.
   - Differences in handling non-MPN materials that contain MFRPN data from ECC-DIMP to S/4HANA.

2. The necessity of utilizing new APIs when interacting with the SAP S/4HANA system for material creation from non-S/4HANA systems.

3. Certain deprecated objects and functionality post-migration, such as specific transactions that will not work with MPN materials in SAP S/4HANA, changes in web services behavior, and deprecated objects no longer relevant in S/4HANA.

The note also includes before-migration recommendations, advising users to refer to several other SAP Notes for pre-checks and pre-transition actions to ensure a smooth migration of the MPN functionality to SAP S/4HANA. Among the key SAP Notes referenced for additional information on pre-checks and related actions are 2334012, 2334008, and 2270836 which deal with migration pre-checks, pre-transition checks, and MPN technical changes, respectively. It is important for users to attend to these pre-requisites to avoid issues during the system conversion process.