SAP Note 1672817 provides information on SAP's support policy for Microsoft Internet Explorer (IE), Microsoft Edge running in Internet Explorer 11 (IE11) mode, and Microsoft Legacy Edge based on the EdgeHTML rendering engine. It also provides guidance about the newer Microsoft Edge on Chromium (see SAP Note 2884143 for details).

Key aspects of SAP Note 1672817:

- **Symptom**: Users want to run SAP Web applications on various Microsoft browsers, including the older Internet Explorer, Edge in IE11 mode, and Legacy Edge based on EdgeHTML.

- **Other Terms**: The note lists several key terms related to user interface technologies, including SAP NetWeaver, portal applications, and various browser technologies by Microsoft.

- **Reason and Prerequisites**: The note emphasizes that SAP assumes that the latest patches for NetWeaver SPS and browser updates have been applied. It functions as the central note for SAP's support of Internet Explorer and Edge Legacy.

- SAP Release Policy for Internet Explorer:
  - Microsoft Internet Explorer 11 is regarded as a legacy browser and is not recommended by SAP. Microsoft stopped supporting IE11 for certain OS versions on June 15, 2022, and consequently, SAP ended support for those versions.
  - SAP previously declared the end of support for IE11 for many of its applications, clearly indicating which product releases would not be compatible with IE11 or IE11 mode of MS Edge. SAP recommends using Microsoft WebView2 control (Edge with Chromium engine) for integration in SAP GUI for Windows (SAP Note 2913405).
  - The note states not to use Modern UI or Enterprise Mode (EMIE) of IE11 and highlights that Quirks Mode is not supported for all SAP UI technologies.

- For Microsoft Edge IE11 mode, SAP does not support IE11 mode for SAP UI technologies, but mentions a few scenarios where it might still be used, such as administrative tasks or integrations that require specific ActiveX controls.

- Regarding Microsoft Edge Legacy (EdgeHTML), the note clarifies that this version of Edge is no longer supported by Microsoft as of March 9, 2021, and refers to SAP Note 2884143 for information about the newer MS Edge on Chromium.

The note as a whole seeks to inform users on the support policy for outdated Microsoft browsers and to guide them towards newer, more secure, and feature-rich browsing options for the optimal use of SAP Web applications. Users are also given directions to related documentation and SAP Notes where they can find information about specific issues, browsers, and solutions, as well as to refer to the Product Availability Matrix (PAM) for the latest browser support statuses.

References in the note highlight relevant maintenance information for Microsoft Office product compatibility (SAP Note 892638), specifications for using Adobe products with SAP (SAP Note 834573), guidance on the Active Component Framework (SAP Note 766191), details on SAP Integrated ITS (SAP Note 709038), a comprehensive overview of supported platforms for SAP GUI (SAP Note 66971), strategies for using SAP Support Packages (SAP Note 432027), and other significant topics that can be affected by the choice of browser as explained within this and other SAP Notes.