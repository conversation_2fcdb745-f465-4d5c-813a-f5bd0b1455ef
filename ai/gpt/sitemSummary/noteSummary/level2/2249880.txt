SAP Note 2249880 addresses an issue in SAP S/4HANA on-premise where the system generates a SYSTEM_ABAP_ACCESS_DENIED dump. This dump occurs when an ABAP development object, marked as a blocklist item, is executed. Blocklist items include Transactions, Function Modules, Reports, Class Methods, or Form Routines that are restricted due to incompatible data models, licensing restrictions, or pending removal from the S/4HANA code base.

**Symptom:**
Executing a development object that is on the blocklist causes a dump.

**Reason and Prerequisites:**
The object is blocklisted for one of three reasons: it is not adjusted for S/4HANA, it is not licensed for use, or it is to be removed in the future. This note is applicable starting from SAP S/4HANA on-premise Edition 1511 SP01.

**Solution:**
The solution involves the following steps:

1. Confirm the use of the ABAP development object with SAP development support via a support ticket.
2. Once approved, use the report 'ABLM_MODIFY_ITEMS' with program 'SE38' to modify the blocklist entries. This requires the system user to have the S_ADMI_FCD authorization.
3. Execute the report in 'Testmode' to confirm the target blocklist entry status.
4. Change the status to "Allow Entry for Internal Usage" and execute the report to apply the modification.
5. Retest the application that caused the initial dump.

The process can be reversed to restore the original blocklist status by using the same report and following the steps for restoration.

References:
- SAP Note 2933993 informs users about updating 'ABLM_MODIFY_ITEMS' to reset blocklist modifications.
- SAP Note 2753393 details the removal of an erroneously blocklisted item, specifically a function module used in licensing measurements.
- SAP Note 2476734 deals with the runtime error caused by attempting to execute blocklisted items and references this note for resolving such issues.
- SAP Note 2408693 covers how to override blocklist settings for Remote Enabled Function Modules, advising caution and suggesting consultation with SAP development support.
- SAP Note 2234168 explains how to start or restart the Blocklist Monitor if it's not operating correctly, potentially allowing blocklisted transactions and reports to be executed.

This note provides guidance on preventing the unauthorized execution of development objects that could lead to undesired behavior or licensing issues in S/4HANA on-premise. It also provides steps on how to temporarily allow the use of such objects in a controlled manner and how to restore the original blocklist settings.