SAP Note 2668594 addresses an issue with the "Deficit Cash Pool" app in SAP S/4HANA 1809 Feature Pack Stack 00 (FPS00). The issue is that the app does not display cash pools that were created using the "Manage Cash Pools" Fiori application.

The root cause of the problem is identified as a limitation within the version of SAP S/4HANA (1809 FPS00) where the "Deficit Cash Pool" app does not yet support the new cash pool design. However, it is noted that if customers have upgraded from an earlier SAP S/4HANA version (1709 or 1610), the "Deficit Cash Pool" app will still show pools that were configured using the "Manage Bank Accounts - Bank Hierarchy View" app.

The provided solutions to address this issue are:

1. Apply SAP Note 2707215 to the system.
2. Upgrade the system to SAP S/4HANA 1809 Feature Pack Stack 01 (FPS01).

There are no references to other notes or documents in this SAP Note.