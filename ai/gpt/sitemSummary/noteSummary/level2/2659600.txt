SAP Note 2659600 addresses an issue encountered during the migration of sales orders using the Migration Cockpit (LTMC) in SAP S/4HANA on-premise versions 1610 and 1709. Users report an error message stating "Please enter sold-to party or ship-to party" when attempting to migrate sales orders. Additionally, an error occurs when the 'Sales Document Item' field in the 'Partner' sheet is populated with '0', indicating that key information is missing.

To resolve this issue, SAP provides a step-by-step guide in the note. The instructions include:

1. Running transaction LTMOM to search for and open the sales order object in the project.
2. Double-clicking on "Source Structures".
3. Ensuring that Technical Names are turned on.
4. Right-clicking on the structure S_VBPA and selecting "Display View".
5. Switching to Edit mode and changing the field POSNR under the column 'On-premise - Enterprise Mgmt' from 'Required' to 'Visible'.
6. Saving the changes and clicking on the "Generate Runtime Object" button to apply the changes.

After these adjustments, the 'Sales Document Item' field in the 'Partner' sheet can be accurately filled in within the migration template, using an example value like '000000', to prevent the errors from occurring.

The note is specifically targeting users who are experiencing the described issue and does not include references to other SAP Notes or documents. It provides a direct solution to address the migration errors related to sales order processing in the specified SAP S/4HANA releases.