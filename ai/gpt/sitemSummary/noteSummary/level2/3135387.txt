SAP Note 3135387 is a central correction note addressing several issues with Data Migration content for SAP S/4HANA 2021. This note is applicable for users who have installed SAP S/4HANA 2021 up to support package levels SP00-SP01 and are using the SAP S/4HANA Migration Cockpit for data migration through files or staging tables without modifications to the pre-delivered content. The issues corrected by this note include:

1. Missing rule for setting the "Do Not Release Immediately" indicator in the maintenance plan migration object (SIF_MAINT_PLAN_2), as described in SAP Note 3135137.

2. Missing mapping task for the "Control Key for Work Center" in the master recipe migration object (SIF_PP_MSTRRCP), as specified in SAP Note 3138072.

3. Omission of the field for the accounting indicator during the migration of the service order object (SIF_SRVC_ORDER) which is detailed in SAP Note 3136516.

The solution provided is a Transport-based Correction Instruction (TCI) that automatically updates the generated migration objects if they have not been modified or copied by the user. This note also references KBA 2543372, which guides users on how to implement TCIs. Users experiencing the stated issues should implement this correction note to resolve content-related errors in their data migration process. It is important to note that if the migration objects have been modified or copied, the TCI will not apply corrections to those objects. Users should also ensure that they follow the proper TCI implementation steps as outlined in the references.