SAP Note 1810419 addresses the issue that despite MIGO being the standard transaction for displaying documents in MM inventory management, some reports are still incorrectly referencing the older transaction MB03. This is identified as a program error that needs correcting. The reason to move away from MB03 is that it does not support newer developments, like documentary batches, which are supported by MIGO.

The note provides a solution stating that from SAP Release 6.0 onwards, the issue has been corrected in specific Support Packages. It also mentions the possibility of implementing advance correction instructions to resolve the problem.

Furthermore, the note references SAP Note 1804812 which discusses the limited maintenance and eventual decommissioning of MB transactions (such as MB03) that have been replaced by the transaction MIGO as of Release 4.6. It details how MB transactions will only receive critical corrections, new functionalities will be developed only for MIGO, and how SAP plans to completely decommission MB transactions in future products like S/4HANA. Users are encouraged to switch from MB transactions to MIGO, and the BAPI "BAPI_GOODSMVT_CREATE" is suggested for background processing needs that previously depended on batch inputs related to MB transactions.