SAP Note 1780066 addresses an issue encountered during the reorganization of profit centers. Specifically, users who open the object list for purchase orders—either with or without account assignment—observe that despite selecting additional display fields in Customizing, these fields appear without content. This problem occurs due to a program error.

The note references two classes within the SAP system: CL_FAGL_R_OBJ_TYPE_001_POA and CL_FAGL_R_OBJ_TYPE_001_PO. These classes are associated with the object handling for purchase orders in the context of profit center reorganization.

To resolve this issue, the note simply advises the user to implement the correction instructions provided. This could potentially involve applying a support package or patch that contains the necessary code corrections.

Additionally, SAP Note 1780066 references SAP Note 1471153, which is a composite note concerning profit center and Funds Management (FM) reorganization within the new General Ledger functionality. Note 1471153 serves as an umbrella document referencing various other notes on related issues, offering guidelines and important information for those implementing or experiencing problems related to these reorganization features. Moreover, it provides information on prerequisites, like activating specific business functions, and instructions for seeking further support.

In conclusion, SAP Note 1780066 is a straightforward directive for users facing issues with displaying additional fields in the profit center reorganization plan's purchase order object list. The solution involves applying provided correction instructions to fix the underlying program error.