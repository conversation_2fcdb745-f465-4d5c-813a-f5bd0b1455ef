SAP Note 2313354 addresses a specific scenario in which an organization deletes the SAP Product and REACH Compliance 2.0 add-on from its system but wishes to continue using other processes within the SAP Environmental Health and Safety (EH&S) component. The note outlines the steps required to restore the check functions for legal entities associated with SAP EH&S characteristics to their original state after the deletion of the add-on.

Key information from the note includes:

- **Symptom**: The need to restore check functions for SAP EH&S characteristics after the removal of the SAP Product and REACH Compliance 2.0.

- **Other Terms**: References to S/4HANA, S4CONV, ATPRF, CABN, Rechtliche Einheit (which is "Legal Entity" in German).

- **Reason and Prerequisites**: SAP Product and REACH Compliance 2.0 modifies check functions during its installation to integrate EH&S processes with this add-on, which need to be restored after its deletion.

- **Solution**: The solution includes implementing correction instructions or importing a specific Support Package. There is a program provided (/TDAG/CPX_S4_LEG_ENT_RESET) to be executed in test mode first, which gives an overview of changes and identifies invalid legal entities so they can be corrected before running the program in full mode. This program resets the check function in processed characteristics to the delivery value (C14F_LEGENTITY_CHECK), and users should not run the program if they intend to use different check functions. 

- **References**: SAP Note 2313354 references SAP Note 2275942, which discusses contained corrections in Support Package 08 for SAP Product and REACH Compliance 2.0.

In summary, SAP Note 2313354 provides guidance on how to reverse changes made by the SAP Product and REACH Compliance 2.0 add-on to the check functions of SAP EH&S legal entity characteristics upon deletion of the add-on. It includes instructions for running a program to identify and correct invalid data before restoring the original settings.