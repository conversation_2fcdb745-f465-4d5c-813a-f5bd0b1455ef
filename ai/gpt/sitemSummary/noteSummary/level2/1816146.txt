SAP Note 1816146 discusses the issue where incorrect information about Product versions and product instances is shown in the System Landscape Directory (SLD), Landscape Management Database (LMDB), and Maintenance Planner. This can result in systems being incorrectly flagged as "erroneous" in the Maintenance Planner, displaying a status ERROR.

The note provides a solution by indicating that the incorrect installed software information must be corrected at the source on the technical system itself. It details two methods to correct this information:

**A. Maintenance Planner - Correction of Installed Software Information (CISI) with SUM**
- Users should follow a specific procedure described in the Maintenance Planner User Guide to verify an erroneous system and correct the software information using the Software Update Manager (SUM).

**B. Manual Cleanup**
- If CISI with SUM is not viable, the note recommends a manual cleanup and details the steps to do this:
    - Targeted cleanup of incorrect product information, possibly referring to SAP Note 1595918 for guidance.
    - If targeted cleanup is not possible, SAP Support should be contacted with a ticket under component BC-UPG-MP for assistance.
    - Send corrected data to the SLD using specific transactions and verify it has been processed.
    - Wait for the automatic propagation of corrected data from SLD to LMDB in SAP Solution Manager 7.2.
    - Verify that the error messages no longer appear in product system checks.

The note also provides references to other related SAP Notes detailing automatic and manual cleanup and information on how these tasks contribute to the correction in the SLD/LMDB. These references include notes on software component checks during the CISI process, handling of SAP NetWeaver systems with specific software components, identifying and addressing automation challenges in landscape management, improvements in reporting installed software information, and dealing with obsolete product definitions remaining in SLD/LMDB.