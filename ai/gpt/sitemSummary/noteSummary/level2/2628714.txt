SAP Note 2628714 - Amount Field Length Extension: User Interface Adaptations

Summary:

This SAP note explains the user interface adaptations required due to the extension of selected currency amount field lengths in SAP S/4HANA On-Premise 1809 or higher. The primary cause for these adaptations is the increase of field lengths from 9-22 digits (including 2 decimals) to 23 digits in data types such as DEC, CHAR, and NUMC. The note outlines that these changes could affect ABAP GUI Dynpro, Web Dynpro, Fiori applications, JavaScript code, and Adobe Forms, and provides detailed instructions for necessary adjustments in these different technologies.

Key points include:

1. The adjustments mainly concern user interfaces and programs that display the extended amount fields. Developers might need to modify the properties of UI elements to ensure that the full length of the amount fields is displayed or to prevent the entry of large amounts where not needed.

2. For ABAP GUI Dynpro, modifications might involve changes to the screen painter properties and potential fallback strategies for displaying extended amounts in smaller visible lengths. The note also provides guidance on the use of conversion exits. 

3. Web Dynpro adjustments can involve setting the "length" and "width" properties for affected fields in both static and dynamic models.

4. Fiori Application and JavaScript Code adaptations require handling data without rounding issues because JavaScript has limitations with large decimal numbers. The note suggests using UI5 controls, which support larger amounts, and provides coding examples for handling amounts correctly.

5. Adobe Forms users should not be affected as long as they don't need to activate the extension in customizing or if they handle amounts as decimal numbers. However, specific guidance for supporting extended amount lengths in Adobe Forms is not provided in this note.

Additionally, the note recommends comprehensive testing for all affected user interfaces after upgrading to S/4HANA On-Premise 1809 or higher to ensure correct functionality. It emphasizes the necessity of manually adjusting certain development artifacts and provides references to other SAP Notes for more information on the extension process, necessary code adaptations, and general information regarding the extension of amount field lengths.