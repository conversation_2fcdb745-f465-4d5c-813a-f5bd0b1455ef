SAP Note 2270550 addresses the transition process for customers using SAP Real Estate Management as they convert their systems to SAP S/4HANA, on-premise edition, specifically the migration from the old Real Estate Classic (RE Classic) version to the new Real Estate Flexible (RE-FX) version.

Key points from the SAP Note 2270550:

- SAP S/4HANA on-premise edition 1511, SAP S/4HANA Finance edition 1503, and subsequent versions do not support Real Estate Classic (RE Classic).
- Customers using RE Classic are required to migrate their data to Real Estate Flexible (RE-FX) before converting to SAP S/4HANA, as RE-FX is the supported real estate component in these versions of SAP S/4HANA.
- The migration from RE Classic to RE-FX is not a simple technical conversion; it must be planned and executed as a project, taking into account the complexity of the existing data, customer developments, and the extent to which RE-FX functions are used.
- As part of the planning process, customers should refer to SAP Note 828160 for more information on the migration process, and SAP Note 517673 for details on functions and restrictions of the RE-FX component.
- Key aspects to consider during migration include preparation, testing, adapting custom developments, and understanding the broader functional scope of RE-FX.

Additionally, SAP Note 2270550 refers to related notes:

- Note 828160 provides guidance on the migration process from RE Classic to RE-FX, and includes information about the necessities of the audit, technical requirements, key considerations, testing, and recommendations based on previous projects.
- Note 517673 describes the functionalities and limitations of the RE-FX component across various SAP releases.
- Note 1944871 indicates that RE Classic is no longer available in SAP Simple Finance, On-Premise Edition and has been replaced by the guidelines in note 2270550.

In summary, businesses previously using RE Classic must migrate to RE-FX before moving to S/4HANA. The note serves as a crucial reminder that detailed planning and execution of the migration project are mandatory steps to ensure that real estate data and functionality are preserved and enhanced within the new SAP S/4HANA environment.