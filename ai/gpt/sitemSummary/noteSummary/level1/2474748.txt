SAP Note 2474748 describes the process and considerations for converting the season data associated with articles from the SAP Fashion Management or S/4HANA 1610 (S4CORE 101) system to the simplified season data model in SAP S/4HANA 1709 (S4CORE 102).

The reason for the conversion is that the new S/4HANA environment has a simplified data model for seasons, which improves the readability and efficiency of determining seasons for articles across different applications. The prerequisite for this conversion is that the existing season data, stored in the FSH_SEASONS_MAT table, needs to be transformed to fit the new model.

The solutions proposed in the note involve significant changes to the way season data is handled:

1. The new data model in SAP S/4HANA 1709 will store both the relevant seasons from the generic level and exception seasons at the specific level (variant article/specific segment), whereas SAP Fashion Management only maintained exceptions at the specific level.

2. The representation of the generic segment has changed, with the '*' (star symbol) now indicating the generic segment in S/4HANA, as opposed to the previous blank/space (' ') used in SAP Fashion Management. Seasons with spaces will be converted to stars for segments relevant articles.

3. Season usage is now dynamically calculated in S/4HANA based on the presence of seasons in an article. Previously, seasons were assigned statically in SAP Fashion Management, and this could not be changed after the article's creation. After the conversion, season usages that are set without actual seasons will be cleared, and season usage marked 'S' and 'C' will be converted to 'T'.

4. Season maintenance is not allowed for scope 2 segmented articles. Any existing seasons associated with such articles will be deleted after the conversion.

The note does not reference any other documents or notes. Users performing a system conversion to S/4HANA 1709 will need to understand these changes and apply the conversion to their season data accordingly to ensure proper functionality in the new environment.