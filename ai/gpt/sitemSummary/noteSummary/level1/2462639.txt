SAP Note 2462639 addresses the potential impacts and required adaptations for customer-specific ABAP development when converting from SAP BW to SAP BW/4HANA. The conversion may necessitate a custom code migration due to changes to the system software affecting custom code.

The note provides a list of unavailable Dictionary (DDIC) and ABAP objects in BW/4HANA and specifies that tools such as the SAP BW/4HANA Transfer Cockpit's Code Scan Tool can help check for custom code objects and identify incompatibilities. However, SAP Readiness Check and the Pre-check Tool do not analyze custom code.

Key impacts and required actions outlined in this note include:

- Database Change Impact: ABAP coding revised for SAP HANA when moving from other database platforms.
- Simplification of Application Functionality: Many standard programs and dictionary objects used in custom code in SAP BW are not available in SAP BW/4HANA. Replacement with documented standard interfaces is recommended.
- Change of Application Technology: Customer enhancements or "exits" are not available. Enhancement spots are to be used instead. Conversion of some known exits to enhancement spots is detailed.
- Simplification of Object Types: Custom code adjustments may be needed for object types that are not available in BW/4HANA, such as InfoCubes and classic DataStore objects.
- Simplification of Content Objects: Adjustment of custom code for content objects that are not available in BW/4HANA.
- Change of Personalization Objects: Custom code adjustments for data persistency changes, as DataStore objects are replaced with transparent tables.
- Simplification of Authorization Objects: Authorization objects like S_RS_ICUBE not available and require code adjustments.
- Changes of Application Interfaces: Interfaces have changed in BW/4HANA, requiring adjustments in custom code for compatibility.
- Enhanced Data Types for Characteristics and Constants: When converting from a BW release below 7.4, changes in data types and lengths may necessitate code changes.
- Front-end Technology Change: BEx Web Templates or Workbooks will not work with BW/4HANA.
- Impact of Software Component Upgrades: Transition to BW/4HANA may involve upgrading software components that can impact custom code.

Recommended measures to manage these changes include resetting objects to SAP standard where possible, adapting modifications via SPDD, SPAU, and SPAU_ENH, and potentially contacting SAP or partners involved in Custom Development Projects for assistance. Additionally, there's guidance on using tools like the ABAP Test Cockpit, SQL Monitor, and Runtime Check Monitor to ensure the smooth continuation of business processes post-migration.

The note references other related SAP Notes providing detailed information on corresponding topics, such as Simplification List for SAP BW/4HANA (2421930), strategic enterprise management APIs (2526508), standard authorizations (2468657), among others, to assist customers in understanding and managing the transition to SAP BW/4HANA more efficiently.