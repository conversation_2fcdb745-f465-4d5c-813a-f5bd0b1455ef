SAP Note 2217112 addresses the issue of the Product Assembly functionality being disabled or removed in SAP S/4HANA, as product assemblies are no longer supported in this environment. To mitigate this, the note provides detailed steps to enable Product Assembly functionality if needed.

To enable the Product Assembly functionality, a user must:

1. Activate a business function with the switch /PLMI/PPE_SFWS_S4_01 by creating it in transaction SFW2 with certain properties (such as 'Reversible' and 'G Enterprise Business Function') and then activating it in transaction SFW5.
2. Adjust Customizing settings based on Enhancement Package 7 Customizing.

Additionally, for the Product Assembly functionality to be visible in PFCG roles:

1. Product assembly menu items in the work overview of PFCG roles should have their 'Visibility' parameter set to 'Visible'. This affects roles such as:
   - SAP_PLMWUI_DISCRETE_MENU
   - SAP_PLMWUI_DISCRETE_MENU2
   - SAP_PLMWUI_DISCRETE_MENU3

2. For the Product Assembly Work Center, the 'Visibility' parameter should also be set to 'Visible', and the 'Default Page' should be set to true.

If users wish to switch back the 'Phantom Assembly' function in the Product Structure Management application, they should make certain components visible again, such as various labels and input fields within the /PLMU/WDC_PPE_CMP_VARIANT component by changing their 'visible' properties to 'visible'.

For further context, the note references SAP Note 2190420, which gives recommendations for adapting customer-specific ABAP code for compatibility with SAP S/4HANA.

In summary, SAP Note 2217112 provides guidance for organizations that require the Product Assembly functionality in SAP S/4HANA, outlining the steps to reactivate and display the relevant fields and navigation pertaining to product assemblies.