SAP Note 2788517 addresses the concerns of converting custom code in the context of a migration from a CRM 7.0 installation to S/4HANA OP 1909 or a later release. Specifically, it provides guidance on how to adapt the SQL statements used in custom code that interact with business transaction database tables. The changes necessitated by a new persistency layer in S/4HANA are detailed, and the note includes a cookbook as an attachment, which offers an overview of these changes, alongside examples and guidelines for converting SQL statements to align with the new S/4HANA database table structure. This note does not mention any references to other documents or notes and is focused on aiding developers in updating custom code during an S/4HANA migration.