SAP Note 954816 addresses the use of specific transactions in SAP ERP 2005 (ERP2005) related to Business Partner (BP) and Customer-Vendor Integration (CVI). These transactions are FLBPD1, FLBPD2 for customers, and FLBPC1, FLBPC2 for vendors. Users may find that these transactions behave differently from previous releases.

Reason:
The CVI has undergone a redesign in ERP2005, featuring a new bi-directional replication capability for business partners, customers, and vendors that can be individually activated or deactivated.

Solution:
To properly utilize the mentioned transactions, users must perform specific Customizing (configuration) for CVI-related tables. This involves setting up:

- Activation of synchronization objects (MDSV_CTRL_OPT_A) to manage replication pathways.
- Determination of number assignments for various directions (BP to customer/vendor, customer to BP, vendor to BP), using dedicated views such as V_TBD001, V_TBC001, CVIV_CUST_TO_BP1, and CVIV_VEND_TO_BP1.
- Assignment of BP role categories and determining BP roles for customer/vendor directions through views V_TBD002, V_TBC002, CVIV_CUST_TO_BP2, and CVIV_VEND_TO_BP2.

The note specifies certain conditions that must be met for each transaction to be successful, such as existing customer/vendor not yet linked to a Business Partner, synchronization settings being active, and proper assignments and roles defined.

Additional points to consider:

- MDS_LOAD_COCKPIT can be used as an alternative for bulk creating BPs from customers or vendors.
- If a BP has customer and vendor roles without links to corresponding customers and vendors, it can cause issues with transactions FLBPD2 and FLBPC2. The note provides workarounds for this situation, such as toggling synchronization or creating manual assignments in specific tables.
- It is important that entry fields required across objects are reconciled to prevent update errors, as detailed in SAP Note 928616.

References:
The note directly refers to SAP Note 956054, which elaborates on the CVI redesign in ERP 6.00, including the needed customizing settings, migration reports, and key points for setting up the CVI environment during an upgrade. SAP Note 1306639 is also mentioned, addressing conditional synchronization when linking BPs from customers or vendors, offering a workaround solution until an enhancement becomes standard.

In summary, SAP Note 954816 guides users through the changes in transactions for creating or linking business partners from customers or vendors within the redesigned CVI in ERP2005. It outlines the necessary Customizing requirements, the operation of the transactions, and the conditions for their use, while also suggesting potential workarounds for specific scenarios.