SAP Note 2551913 describes an update relevant to system conversions to SAP S/4HANA FPS1, specifically regarding the simplification of Commodity Curves in the Commodity Management area. The note helps users understand the changes in managing Commodity Curve master data after the conversion. Key points from the note include:

1. In SAP S/4HANA on-premise edition 1709 FPS1, the data model for Commodity Management has been simplified, and as part of this change, the Commodity ID has been discontinued.

2. With these simplifications, only the '2 Based on DCS' category for Commodity Curves is supported. Commodity Curves are now identified and accessed using DCS ID, MIC (which is optional), and Commodity Curve Type.

3. It is no longer necessary to activate specific Business Functions previously required for the usage of Commodity Curves within the Commodity Pricing Engine (namely, Business Functions for Commodity Management for Treasury - FIN_TRM_COMM_RM).

4. The note references another SAP Note, 2553281, which provides details about the process of deprecating the Commodity ID and includes a "cookbook"- a detailed guide for converting existing data to comply with the new simplified data model.

In summary, SAP Note 2551913 informs users about the simplification of Commodity Curves in SAP S/4HANA 1709 FPS1 and guides them on the changes they need to make during the system conversion, pointing to additional resources for the deprecation of the Commodity ID.