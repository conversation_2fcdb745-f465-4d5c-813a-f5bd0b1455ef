SAP Note 2484134 provides release information and relevant restrictions for customers planning to use "SAP for Banking" solutions in conjunction with "SAP S/4HANA 1709" (on-premise edition). This note acts as guidance for businesses to understand the compatibility and functional limitations when using Banking solutions with this particular release of SAP S/4HANA.

Below is a summary of the key details from the note:

- Statements from previous SAP Note 2211665 also apply to "SAP S/4HANA 1709", detailing general compatibility for "SAP for Banking" solutions.
- Specific solutions such as FS-CML Consumer and Mortgage Loans do not integrate with "Central Payment for SAP Central Finance", and for CML-specific functions regarding collateral management, SAP recommends using the FS-CMS Collateral Management application. References are made to SAP Notes 2346233 and 2369934 for further details.
- FS-CYT Capital Yield Tax Management is available as a separate add-on starting from Support Package 12 for "SAP Capital Yield Tax Management for Banking 8.0", implying that it is not delivered with the core of "SAP S/4HANA 1709".
- The "SAP Payment Engine" (FS-PE) is compatible with "SAP S/4HANA 1709" starting with Release 8 and Support Package 7.
- CML-specific collateral functions are no longer available, with a transition to FS-CMS being advisable.
- SAP Funding Management (FS-TXS) is not released for use in "SAP S/4HANA 1709". However, a standalone installation with remote communication to FS-CML is allowed starting from SAP Funding Management 3.0 Support Package 06.

The note includes references to other SAP Notes, specifically 2491467, 2369934, 2346233, and 2211665, providing additional context and detailed restrictions for different components and functional areas within the SAP S/4HANA 1709 environment. It is critical for organizations to refer to these notes to ensure compliance with supported functionalities and necessary workarounds for the limitations present in their banking applications when operating with "SAP S/4HANA 1709".