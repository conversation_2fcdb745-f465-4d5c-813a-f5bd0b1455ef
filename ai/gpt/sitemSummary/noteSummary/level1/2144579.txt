SAP Note 2144579 provides release information and highlights restrictions when using SAP for Banking with SAP Simple Finance, on-premise edition 1503 and 1605. Here's a summary of the key points:

1. Calculations for Month-to-Date, Quarter-to-Date, and Year-to-Date averages are not covered in SAP Simple Finance, on-premise edition. A resolution is available with certain support packages, but ADB monthly submitter and value correction scenarios are not supported, and only New G/L is supported. SAP note 2231634 is related to this issue.

2. Multi-Currency Accounting for Banks is not released for SAP Simple Finance, on-premise edition. A resolution is available in specified support packages, but only New G/L is supported, and SAP note 2236517 should be considered for further details.

3. SAP Leasing for Banking (including Lease Accounting Engine) and SAP SEM Banking components (Datapool, Profit, Risk, Strategy Analyzer) are not released for use with SAP Simple Finance, on-premise editions.

4. For FS-CML Consumer and Mortgage Loans, CML-specific functions regarding collaterals and collateral objects are no longer available due to changes in "Real Estate Classic" associated with the Simplification List for SAP S/4HANA. FS-CMS Collateral Management should be used instead. SAP note 2369934 expands on the implications of this transition.

5. The financial accounting component FI no longer supports the calculation of interest on arrears at the customer level; instead, FS-CML's transaction FIOA should be used for this purpose.

6. The note also does not address the remediation of the Field Length issue.

7. Additional information is provided about the validity of the note for various components such as FS-TXS (FUNDMGMT), FS-CYT (CYT), and FS-AM, FS-BA (FSAPPL), which require separate installations independent of ERP installation.

Overall, SAP Note 2144579 serves to inform on the limitations and considerations that need to be made when running SAP for Banking with SAP Simple Finance, particularly regarding average daily balance calculations, multi-currency accounting, and leasing components specific to banking, and suggesting alternative solutions where applicable.