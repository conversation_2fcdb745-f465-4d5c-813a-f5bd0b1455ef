SAP Note 2918965 addresses an issue for SAP S/4HANA on-premise edition users who are performing a system conversion and find that they can no longer import commodity daily prices via transaction TBD4, which is a functionality that has been deprecated since SAP S/4HANA 1709.

Key points from the note:

- In the previous SAP Business Suite (ERP), market data not related to derivative contracts specification (DCS) could be uploaded using transaction TBD4 with the "Commodities (Daily Basis)" option. This option is no longer available in SAP S/4HANA.
- Non Ferrous Metal (NFM) functionality previously relied on these tables to consume market rates.
- SAP proposes a modification for customers who wish to continue using NFM in S/4HANA up until the end-of-use rights date of December 31, 2025. SAP does not intend to reinstate this feature as a standard solution.
- The note outlines specific modifications that need to be made to reactivate the market data upload capability in transaction TBD4:
  1. Uncomment certain lines of code in program RFTBDF07 to reintroduce the checkbox for "Commodities (Daily Basis)".
  2. Maintain a class description for class ‘05’ as "Commodities (Daily Basis)" using transaction SM30.
  3. Manually create four custom transactions (Z-tcodes) for specific views related to defining quotation source, types, data, and commodities.
- SAP will not reactivate these views as SPRO customizing nodes, but users can maintain and view data manually using the Z-tcodes, or create an area menu using transaction SE43.

The disclaimer emphasizes that as this functionality is deprecated, customers are responsible for any modifications and testing. Current Commodity Management functionalities do not use these market data tables.

This note provides a temporary workaround for those relying on the deprecated feature while transitioning to or operating in S/4HANA and underscores that customers should plan for a future without this functionality post-2025.