SAP Note 2548303 addresses the inability to maintain address data for users with a business partner assignment in S/4HANA On Premise systems. This affects various areas including user management (SU01, BAPI_USER_CHANGE), Central User Administration, or identity management systems and transaction BP. The issue stems from missing functionality in the context of a new business user model in S/4HANA, where business users are linked to business partners and system users and are involved in various operational roles.

The maintenance view TBZ_V_EEWA_SRC is introduced to manage workplace address changes, but it has limitations. Changes are restricted based on the source of maintenance (HR, BP, or US), whether Central User Administration is active, and if the business user has linked user data.

The solution provided is to import a support package that provides the new maintenance view TBZ_V_EEWA_SRC, which allows for choosing the maintenance autonomy of individual attributes. The maintenance should be conducted via transaction SM30. However, the central address data maintenance autonomy is still under HR's domain and needs to be updated there with synchronization handled by the report /SHCM/RH_SYNC_BUPA_FROM_EMP. As of the note, organizational data maintenance for a business partner or employee is not possible.

There are no references provided in this note.