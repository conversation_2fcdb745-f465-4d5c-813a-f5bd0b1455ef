SAP Note 2229126 addresses a simplification that impacts the logistics and materials requirements planning (MRP) process in SAP S/4HANA, specifically indicating changes to how total dependent requirements are handled.

The note highlights that with the upgrade to S/4HANA, the aggregated dependent requirement tables RQHD and RQIT, as well as the read view MDRI, are no longer supported. This change is due to the capabilities of the SAP HANA database, which can efficiently read individual records directly from the RESB table, without the need for aggregating data.

As a result of this change, customers who migrate to S/4 HANA must deactivate the aggregation function and migrate the relevant requirements back to the RESB table. Following the deactivation, it will no longer be possible to read requirements from the RQHD and RQIT tables or the MDRI view.

This transition means that any custom reports that customers have developed, which are based on aggregated requirement tables or the MDRI view, must be rewritten to use the RESB table. Essentially, customers who upgrade to S/4HANA will need to adapt their custom code to align with the new data handling processes.

No references to other SAP Notes or documents are provided within this note. This change is specific to an SAP S/4HANA environment and reflects the ongoing efforts by SAP to simplify and improve performance by leveraging the capabilities of the HANA database.