{"guid": "00109B131AF41ED998A26D2C9E8120DA", "sitemId": "SI4: FIN_SLL_ISR_POI_ADDR", "sitemTitle": "S4TWL - Company code address data in provider of information", "note": 2782281, "noteTitle": "2782281 - S/4HANA Intrastat: Reuse address data from company code in Provider of Information", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use Intrastat reporting in S/4HANA and are upgrading to on-premise release 1909 or higher.<br/>From 1909 release you can not maintain address data of POI(Provider of Information) directly in 'Maintain Provider of Information' app or transaction /ECRS/POI_EDIT.</p>\n<p>This address data already exists in the system defined as the address data of the company code POI is created for. <br/>Therefore, the address data of the company code should be reused for the POI and not entered in the app 'Maintain Provider of Information' anymore.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Provider of Information (POI), Address Data, /ECRS/POI_EDIT, Company Code, S/4HANA OP 1909.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This is to remove redundant address maintenance in app 'Maintain Provider of Information'.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In the app 'Maintain Provider of Information' the address data is not entered anymore but displayed from the address data of the company code.</p>\n<p><strong>IMPORTANT</strong>: Please take backup of address defined for POI in transaction /ECSR/POI_EDIT before upgrade to 1909 as this data will be lost after upgrade. This is to ensure that incase you have correct address data defined directly in POI and not in company code, then after upgrade you dont loose the address information and use it to specify the correct address data in company code.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Business Operations", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Take backup of address defined for POI in transaction /ECSR/POI_EDIT before upgrade."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Inform users about change in maintaining address data."}]}