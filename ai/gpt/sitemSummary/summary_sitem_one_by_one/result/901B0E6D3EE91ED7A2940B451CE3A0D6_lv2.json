{"guid": "901B0E6D3EE91ED7A2940B451CE3A0D6", "sitemId": "SI56: Logistics_General", "sitemTitle": "S4TWL - Segment Field Length Extension", "note": 2522971, "noteTitle": "2522971 - S4TWL - Segment Field Length Extension", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a sytem conversion from SAP ERP 6.0 EHP7/EHP8 to SAP S/4HANA 1709 and you are using segmentation functionality. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>The data model of segments has changed in SAP S/4HANA 1709. The field length has been increased from 16 to 40 characters.</p>\n<p><strong>Business Process related information</strong></p>\n<p>This data model change does not have any business impact.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>During the conversion, adjustments are made automatically.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if segmentation functionality is used.<br/>This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with MARA-SGT_REL non initial.</p>", "noteVersion": 1, "refer_note": [{"note": "2471098", "noteTitle": "2471098 - Conversion of Default Segment Values Data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1709 (S4CORE 102), and you are using segmentation and default segment values in SAP Fashion Management.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Fashion Management, SAP ERP, Segmentation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP Fashion Management, system was equipped with two ways to maintain default segments that can be consumed by different documents. Few applications use the data in the table SGT_CATDYN for the determination of default segment value. Few applications use the data in the tables SGT_SEGVAL and SGT_SEGVAL_STG. Both of them have different data models and data maintenance tools.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In S/4HANA, single data model combines the functionality and advantages of both the data model and single data maintenance tool to handle the data maintenance. The data present in the above mentioned tables will be converted and stored in the tables SGT_DYNCAT and SGT_DYNVAL under a single data model. Applications will start determining the default segment by using the new data model.</p>\n<p>During the conversion, the following report is executed automatically R_SGT_DEFAULT_MIG.<br/>In case of problems during the conversion, you can execute these reports manually.</p>", "noteVersion": 3}], "activities": [{"Activity": "Software Upgrade / Maintenance", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Field length of segments has been increased from 16 to 40 characters.This data model change does not have any business impact. During conversion adjustments are made automatically."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Adjust Custom Code accoring to SAP Note 2416766"}]}