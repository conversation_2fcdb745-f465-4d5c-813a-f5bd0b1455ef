{"guid": "0050569455E21ED5B3E17678391EE09E", "sitemId": "SI_2_Logistics_QM", "sitemTitle": "S4TWL - ITS services in QM", "note": 2270126, "noteTitle": "2270126 - S4TWL - ITS services in QM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IAC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Renovation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>SAP Internet Transaction Server (ITS) is considered to be an old technique. Therefore the ITS services for the Internet Application Components (IAC) in QM are not available within SAP S/4HANA, on-premise edition 1511.</p>\n<p><strong>Business Process related information</strong></p>\n<p>As alternatives, you could use the corresponding QM transactions in SAP GUI for HTML or you could use the WebDynpro applications mentioned below, which are part of the PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5).</p>\n<p>a) Results Recoding: WebDynpro application QI_RECORD_RESULTS_ETI_APPL</p>\n<p>b) Quality Notifications: WebDynpro application QIMT_NOTIFICATION_APP</p>\n<p>c) Certificates: No WebDynpro application available. However, existing QM transactions are still available, such as QC20, QC21</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"TableGrid\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"116\">\n<p><strong>Transaction Code</strong></p>\n</td>\n<td width=\"163\">\n<p><strong>Corresponding ITS</strong></p>\n<p><strong>Service</strong></p>\n</td>\n<td valign=\"top\" width=\"109\">\n<p><strong>Description</strong></p>\n</td>\n<td valign=\"top\" width=\"169\">\n<p><strong>Package</strong></p>\n</td>\n<td width=\"103\">\n<p><strong>Application</strong></p>\n<p><strong>Component</strong></p>\n</td>\n<td valign=\"top\" width=\"106\">\n<p><strong>Area</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"116\">\n<p>QC40</p>\n</td>\n<td valign=\"top\" width=\"163\">\n<p>QC40</p>\n</td>\n<td width=\"109\">\n<p>Internet Certificate for Delivery</p>\n</td>\n<td valign=\"top\" width=\"169\">\n<p>QC</p>\n</td>\n<td valign=\"top\" width=\"103\">\n<p>QM-CA-MD</p>\n</td>\n<td width=\"106\">\n<p>Basic Data for</p>\n<p>Certificates</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"116\">\n<p>QC40A</p>\n</td>\n<td valign=\"top\" width=\"163\">\n<p>QC40A</p>\n</td>\n<td width=\"109\">\n<p>Internet Certificate for Delivery</p>\n</td>\n<td valign=\"top\" width=\"169\">\n<p>QC</p>\n</td>\n<td valign=\"top\" width=\"103\">\n<p>QM-CA-MD</p>\n</td>\n<td width=\"106\">\n<p>Basic Data for</p>\n<p>Certificates</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"116\">\n<p>QC42</p>\n</td>\n<td valign=\"top\" width=\"163\">\n<p>QC42</p>\n</td>\n<td width=\"109\">\n<p>Batch certificate on</p>\n<p>WWW</p>\n</td>\n<td valign=\"top\" width=\"169\">\n<p>QC</p>\n</td>\n<td valign=\"top\" width=\"103\">\n<p>QM-CA-MD</p>\n</td>\n<td width=\"106\">\n<p>Basic Data for</p>\n<p>Certificates</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"116\">\n<p>QEW01</p>\n</td>\n<td valign=\"top\" width=\"163\">\n<p>QEW01</p>\n</td>\n<td width=\"109\">\n<p>Results Recording on Web</p>\n</td>\n<td valign=\"top\" width=\"169\">\n<p>QEWW</p>\n</td>\n<td valign=\"top\" width=\"103\">\n<p>QM-IM</p>\n</td>\n<td valign=\"top\" width=\"106\">\n<p>Quality Inspection</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"116\">\n<p>QEW01V</p>\n</td>\n<td valign=\"top\" width=\"163\">\n<p>QEW01V</p>\n</td>\n<td width=\"109\">\n<p>Variant Maint.:</p>\n<p>Recording on Web</p>\n</td>\n<td valign=\"top\" width=\"169\">\n<p>QEWW</p>\n</td>\n<td valign=\"top\" width=\"103\">\n<p>QM-IM</p>\n</td>\n<td valign=\"top\" width=\"106\">\n<p>Quality Inspection</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"116\">\n<p>QMW1</p>\n</td>\n<td valign=\"top\" width=\"163\">\n<p>QMW1</p>\n</td>\n<td width=\"109\">\n<p>Create quality notification (WWW)</p>\n</td>\n<td valign=\"top\" width=\"169\">\n<p>QQM</p>\n</td>\n<td valign=\"top\" width=\"103\">\n<p>QM-QN</p>\n</td>\n<td valign=\"top\" width=\"106\">\n<p>Quality Notifications</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"116\">\n<p>QISR</p>\n</td>\n<td valign=\"top\" width=\"163\">\n<p>QISR</p>\n</td>\n<td width=\"109\">\n<p>Internal Service</p>\n<p>Request</p>\n</td>\n<td valign=\"top\" width=\"169\">\n<p>QNWEB</p>\n</td>\n<td valign=\"top\" width=\"103\">\n<p>QM</p>\n</td>\n<td valign=\"top\" width=\"106\">\n<p>Quality Notifications</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"116\">\n<p>QISRW</p>\n</td>\n<td valign=\"top\" width=\"163\">\n<p>QISRW</p>\n</td>\n<td width=\"109\">\n<p>Internal Service</p>\n<p>Request on the</p>\n<p>Web</p>\n</td>\n<td valign=\"top\" width=\"169\">\n<p>QNWEB</p>\n</td>\n<td valign=\"top\" width=\"103\">\n<p>QM</p>\n</td>\n<td valign=\"top\" width=\"106\">\n<p>Quality Notifications</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Knowledge transfer to key and end users</p>\n<p>Check if the services are used via transaction SICF. Hierarchy Type = \"Service\", Service Name e.g. \"QC40\" and execute. Click on node e.g. \"QC40\" and check via context menu (mouse-click right) if service is active. Do the same for all services in the table.</p>", "noteVersion": 2, "refer_note": [{"note": "2207278", "noteTitle": "2207278 - ITS services for quality certificate, results recording, and quality notification removed", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The ITS services QC40, QC40A, QC42, QEW01, QISR, QISRW, QMW1, PLM_MOBQDEF1, PLM_MOBQNOTIF199, and TS_TS20000780H are no longer available.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IAC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>An obsolete UI technology is concerned.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Use the relevant Fiori applications, if available. Otherwise, you can also use the relevant transactions in the HTML GUI or use the Web Dynpro application QI_RECORD_RESULTS_ETI_APPL for the results recording.</p>", "noteVersion": 3}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "In case WebDynpro applications will be used as replacement, then PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5) need to be configured, and respective authorizations"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Technical System Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "ITS services in QM - Internet Application Components (IAC) in QM are not available within SAP S/4HANA, As alternatives, you could use the corresponding QM transactions in SAP GUI for HTML or you could use the WebDynpro applications mentioned below, which are part of the PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5)."}]}