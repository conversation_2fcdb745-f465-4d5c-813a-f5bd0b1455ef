{"guid": "00109B1315FA1ED8ADD4EC58EB9A60D3", "sitemId": "SI027: CRM", "sitemTitle": "Marketing functionality not supported in S/4HANA for customer management", "note": 2692798, "noteTitle": "2692798 - Marketing functionality not supported in S/4HANA Service", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are considering or planning a migration of a CRM 7.0 or CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. The following information on changed or deprecated functionality is relevant in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRMTWL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Marketing functionality is not available in S/4HANA (with the exception of leads which are sometimes assigned to the Marketing area). Thus, you cannot create campaigns, target groups, or trade promotions. For Marketing functionality in the S/4HANA realm, SAP Marketing Cloud shall be used.<br/>As a consequence, all references that point to Marketing functionality, such as the campaign field in transactions, and all related functions, such as campaign determination, have been removed.<br/>Lead templates and mass lead generation are not supported.</p>", "noteVersion": 3, "refer_note": []}