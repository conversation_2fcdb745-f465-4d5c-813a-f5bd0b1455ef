{"guid": "00109B1315FA1ED9868012A6881FC0D7", "sitemId": "SI2: Logistics_GT", "sitemTitle": "S4TWL - Invoice Forecasting Worklist not available in SAP S/4HANA", "note": 2739164, "noteTitle": "2739164 - S4TWL - Invoice Forecasting Worklist not available in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA and using IFW in your original system, you will find switch ISR_GLT_IFW as always deactivated in SAP S/4HANA systems.Thus, you cannot use invoice forecasting in SAP S/4HANA systems.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IFW, SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Functionality not available.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Switch ISR_GLT_IFW is deactivated in SAP S/4HANA systems and thus invoice forecasting cannot be used in SAP S4/HANA systems.</p>\n<p><strong><strong><strong><strong>Business Process related information</strong></strong></strong></strong></p>\n<p>Implemented business processes need to be adjusted since the functionality is no more available. IFW data will no more remain relevant in SAP S/4HANA systems.</p>\n<p><strong><strong><strong><strong>Required and Recommended Action</strong></strong></strong></strong></p>\n<p>IFW related data will no more remain relevant.</p>\n<p><strong><strong><strong><strong><strong>How to Determine Relevancy</strong></strong></strong></strong></strong></p>\n<p>This simplification item is relevant if Invoice forcasting worklist functionality is in use which means table IFW_WORKLIST has data.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adjust business processes using Invoice Forecasting Worklist."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Train users in changed business processes."}]}