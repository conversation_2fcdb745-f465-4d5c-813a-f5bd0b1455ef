{"guid": "6CAE8B28C6E31ED6AB83009DB6BD80CB", "sitemId": "SI2_ECO_Subcontracting", "sitemTitle": "S4TWL Business Package for Subcontracting (EC&O)", "note": 2389447, "noteTitle": "2389447 - S4TWL Business Package for Subcontracting (EC&O)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Enterprise Portal, Business Package for Subcontracting (EC&amp;O), EC_PORTAL, S/4HANA, Simplification List, DIMP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Reason: Conversion from SAP ERP for Discrete Industries &amp; Mill Products (ECC-DIMP) to SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The Business Package for Subcontracting (E&amp;C) was available for the general contractors to monitor current , upcoming and overall activities. It provided subcontractors access to the required Information. Using the business package (EC_PORTAL), general contractors and subcontractors had the  access to role-specific iViews that display the relevant data from multiple SAP <span>ERP</span> sources, including purchase orders, sales orders, and network activities.</p>\n<p>Subcontracting portal is not available with SAP S/4HANA. There are successors with SAP Ariba and Ariba Networks .</p>\n<p><strong>Business Process Related Information</strong></p>\n<p>Implementing the successor solution for subcontract portal will result in the IT system landscape and integration as well as in business applications used for the subcontract portal. Not all scenarios covered by subcontract portal are supported by the successor products.</p>\n<p><strong>Required and Recommended Action(S)</strong></p>\n<p>Package EC_PORTAL is no longer available in S/4HANA. You must remove the usage of SAP objects from your custom code. as indicated by custom code check</p>\n<p> </p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Landscape Redesign", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Subcontracting portal is not available with SAP S/4HANA. There are successors with SAP Ariba and Ariba Networks. Implementing the successor solution for subcontract portal will result in the IT system landscape and integration as well as in business applications used for the subcontract portal. Not all scenarios covered by subcontract portal are supported by the successor products."}, {"Activity": "Implementation project required", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Subcontracting portal is not available with SAP S/4HANA. There are successors with SAP Ariba and Ariba Networks. Implementing the successor solution for subcontract portal will result in the IT system landscape and integration as well as in business applications used for the subcontract portal. Not all scenarios covered by subcontract portal are supported by the successor products."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Package EC_PORTAL is no longer available in S/4HANA. You must remove the usage of SAP objects from your custom code. as indicated by custom code check"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": ""}]}