{"guid": "6CAE8B3EA08B1ED791BB5257952E40C6", "sitemId": "SI55: Logistics_General", "sitemTitle": "S4TWL - Season Conversion (1610 to 1709)", "note": 2481891, "noteTitle": "2481891 - S4TWL - Season Conversion (SAP S/4HANA 1610 to 1709)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system upgrade from SAP S/4HANA 1610 to SAP S/4HANA 1709, and you are using season functionality. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>The data model of season has changed from SAP S/4HANA 1610 to SAP S/4HANA 1709.</p>\n<p><strong>Business Process related information</strong></p>\n<p>This data model change does not have any business impact.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>During the conversion, the following report is executed automatically R_FSH_S4_SEASONS_MAT_MIG_XPRA.<br/>In case of problems during the conversion, you can execute this report manually.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if season functionality is used.<br/>This can be checked via transaction SE16N. Enter table FSH_SEASONS_MAT and check whether there are entries.</p>", "noteVersion": 2, "refer_note": [{"note": "2474748", "noteTitle": "2474748 - Conversion of Articles' Season Data to S/4 Data Model", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1709 (S4CORE 102), and you are using articles with seasons attached to them in SAP Fashion Management or S/4HANA 1610 (S4CORE 101).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Fashion Management, S/4HANA 1610, S4CORE 101, Seasons, FSH_SEASONS_MAT, R_FSH_S4_SEASONS_MAT_MIG_XPRA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The season data model is simplified in the S/4 environment which requires the existing articles' season data present in the table FSH_SEASONS_MAT to be converted accordingly. It simplifies the readability of seasons for a given article and reduces the additional time taken by the earlier data model to determine the seasons across all the consuming applications. This can only be possible if the season data is converted to a form that is optimized for this purpose.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business process related information</strong></p>\n<p>In SAP Fashion Management, an article can be made relevant to seasonality by attaching the relevant seasons to them. Such assignments were stored within the table FSH_SEASONS_MAT. However, the data store follows an unique and complex way to handle referencing of seasons between generic articles and variants.</p>\n<p>In SAP S/4HANA 1709, the season data model is simplified and adjusted to simplify the readability of the seasons for a given article, to reduce the additional time taken by the earlier data model to determine the seasons across all the consuming applications, to correct the functional gaps with respect to segment based seasons and so on.</p>\n<p><strong>Major changes </strong></p>\n<ul>\n<li>\n<div>New data model stores the exception seasons at specific level (variant article/ specific segment) along with the relevant seasons from generic level (generic article / generic segment) in SAP S/4HANA whereas only the exceptions were maintained at specific level in SAP Fashion Management.</div>\n</li>\n<li>'*' (star symbol) is considered to be the generic segment in SAP S/4HANA whereas ' ' (blank/space) was considered as generic segment in SAP Fashion Management. Post conversion, all the seasons exist with ' ' segment will be converted to '*' segment if the article is segment relevant.</li>\n<li>Season usage is calculated dynamically based on the existence of seasons in the article in SAP S/4HANA while season usage was assigned statically in create mode and cannot be changed once the article is created in SAP Fashion Management. Post conversion, season usage will be cleared for the articles with only seasons usage set and not having seasons and season usage 'S' and 'C' will be converted to 'T'.</li>\n<li>Season maintenance is not allowed for scope 2 segmented article. Post conversion, seasons exist with such articles will be deleted.</li>\n</ul>", "noteVersion": 1}], "activities": [{"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "In case of problems automatic execution during the conversion, execute report R_FSH_S4_SEASONS_MAT_MIG_XPRA manually."}]}