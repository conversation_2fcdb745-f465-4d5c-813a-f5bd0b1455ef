{"guid": "0050569455E21ED5B3E17678391DA09E", "sitemId": "SI13: Logistics_PSS", "sitemTitle": "S4TWL - Simplification in Product Compliance for Discrete Industry", "note": 2267461, "noteTitle": "2267461 - S4TWL - Simplification in Product Compliance for Discrete Industry", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With component extension for SAP EHS Management, customers can use the <em>Product Compliance for Discrete Industry</em> (EHS-MGM-PRC) solution for managing their product safety and stewardship business processes. In SAP S/4HANA on-premise edition 1511 and later, some functions are not available.</p>\n<ul>\n<li>BI extraction and queries for product compliance reporting including the roles SAP_EHSM_PRC_BI_EXTR and SAP_EHSM_PRC_BW_ANALYTICS</li>\n<li>Compliance Assessment Workflows</li>\n<li>Compliance Requirement Check Conflict Workflows</li>\n<li>Automated updated of contact person information upon import from Supplier declarations from SAP Product Stewardship Network</li>\n</ul>\n<p> </p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA on-premise edition the compliance reporting in BI is not available. For parts of the BI reporting, the compliance explorer and the exemption explorer is a functional equivalent.</p>\n<p>If you are using <em>SAP Product Stewardship Network</em> to collaborate with your suppliers, the contact person information are not updated automatically any more in SAP S/4HANA on-premise edition.</p>\n<p>The following compliance requirement check conflict workflow templates are not available in <em>SAP S/4HANA,</em> on-premise edition. They have been replaced with a new application that allows exploring the compliance requirement check conflicts based on native master data using CDS views.</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"TableGrid\">\n<tbody>\n<tr>\n<td width=\"418\">\n<p><strong>Workflow Template / Process Control Object</strong></p>\n</td>\n<td width=\"418\">\n<p><strong>Class Name / Event</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600054 - EHPRC_CHK_BA</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_COMP_REQ_BASE</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600049 - EHPRC_CHK_CH</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_COMP_REQ_CHROHS</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600056 - EHPRC_CHK_IM</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_CMP_REQ_IMDS</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600069 - EHPRC_CHK_JG</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_CMP_REQ_JIG</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600053 - EHPRC_CHK_SV</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_COMP_REQ_SVHC</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600068 - EHPRC_CHK_RO</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_COMP_REQ_EUROHS</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p>The following compliance assessment workflow templates are not available in <em>SAP S/4HANA,</em> on-premise edition. The assessment processes for components and products have been replaced with a new application that provides an overview of running assessments for products and components based on native master data using CDS views.</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"TableGrid\">\n<tbody>\n<tr>\n<td width=\"278\">\n<p><strong>Workflow Template / Process Control</strong></p>\n<p><strong>Object</strong></p>\n</td>\n<td valign=\"top\" width=\"278\">\n<p><strong>Class Name / Event</strong></p>\n</td>\n<td valign=\"top\" width=\"278\">\n<p><strong>Type of Assessment Process</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"278\">\n<p>00600070 - EHPRC_BMASMT</p>\n</td>\n<td width=\"278\">\n<p>CL_EHPRC_PCO_BASMAT_ASSMNT</p>\n</td>\n<td width=\"278\">\n<p>Basic Material Assessment</p>\n</td>\n</tr>\n<tr>\n<td width=\"278\">\n<p>00600071 - EHPRC_COASMT</p>\n</td>\n<td width=\"278\">\n<p>CL_EHPRC_PCO_COMP_ASSMNT</p>\n</td>\n<td width=\"278\">\n<p>Component Assessment</p>\n</td>\n</tr>\n<tr>\n<td width=\"278\">\n<p>00600062 - EHPRC_PRASMT</p>\n</td>\n<td width=\"278\">\n<p>CL_EHPRC_PCO_PROD_ASSMNT</p>\n</td>\n<td width=\"278\">\n<p>Product Assessment</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Details about the necessary activities vebore and after converting, you find in the attachment. Ensure that you always have the latest version of the document.</p>", "noteVersion": 5, "refer_note": [{"note": "2230140", "noteTitle": "2230140 - SAP S/4HANA Simplification Item: Manage Product Compliance for Discrete Industry", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using <em>Product Compliance Management</em> (EHS-MGM-PRC) functions with the <em>Component Extension of SAP EHS Management</em> and you are planning to convert to SAP S/4HANA. The functions are included in SAP S/4HANA but some of their features are no longer supported and you want to know whether your own coding is affected and has to be adopted.</p>\n<p>The following features are affected:</p>\n<ul>\n<li>BI extraction logic for product compliance reporting</li>\n<li>Compliance assessment workflows, such as</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Workflow Template / Process Control Object</strong></p>\n</td>\n<td>\n<p><strong>Class Name / Event</strong></p>\n</td>\n<td>\n<p><strong>Type of Assessment Process</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>00600070 - EHPRC_BMASMT</p>\n</td>\n<td>\n<p>CL_EHPRC_PCO_BASMAT_ASSMNT</p>\n</td>\n<td>\n<p>Basic Material Assessment</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>00600071 - EHPRC_COASMT</p>\n</td>\n<td>\n<p>CL_EHPRC_PCO_COMP_ASSMNT</p>\n</td>\n<td>\n<p>Component Assessment</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>00600062 - EHPRC_PRASMT</p>\n</td>\n<td>\n<p>CL_EHPRC_PCO_PROD_ASSMNT</p>\n</td>\n<td>\n<p>Product Assessment</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li> Compliance requirement check conflict workflows, such as</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Workflow Template / Process Control Object</strong></p>\n</td>\n<td>\n<p><strong>Class Name / Event</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>00600054 - EHPRC_CHK_BA</p>\n</td>\n<td>\n<p>CL_EHPRC_PCO_COMP_REQ_BASE</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>00600049 - EHPRC_CHK_CH</p>\n</td>\n<td>\n<p>CL_EHPRC_PCO_COMP_REQ_CHROHS</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>00600056 - EHPRC_CHK_IM</p>\n</td>\n<td>\n<p>CL_EHPRC_PCO_CMP_REQ_IMDS</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>00600069 - EHPRC_CHK_JG</p>\n</td>\n<td>\n<p>CL_EHPRC_PCO_CMP_REQ_JIG</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>00600053 - EHPRC_CHK_SV</p>\n</td>\n<td>\n<p>CL_EHPRC_PCO_COMP_REQ_SVHC</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>00600068 - EHPRC_CHK_RO</p>\n</td>\n<td>\n<p>CL_EHPRC_PCO_COMP_REQ_EUROHS</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In <em>SAP S/4HANA,</em> on-premise edition, the BI extraction and data sources for the managing product compliance process are no longer supported. For compliance status reporting, the following alternative is available:</p>\n<ul>\n<li>Explore compliance status: Analytical application to get an overview of the compliance status for your products and components</li>\n</ul>\n<p>In <em>SAP S/4HANA,</em> on-premise edition, the corresponding workflows for the managing product compliance processes are no longer supported. The following alternatives are available:</p>\n<ul>\n<li>Compliance assessments in process: Analytical application to get an overview of running assessments for your products and components</li>\n<li>Explore compliance check conflicts: Analytical application to get an overview of existing issues for your products and components that have been identified by the compliance checks</li>\n</ul>\n<p>See also SAP note <a href=\"/notes/2267461\" target=\"_blank\">2267461</a><span class=\"urTxtStd\"> for more details.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"longtext\">\n<p>SAP note <a href=\"/notes/2190420\" target=\"_blank\">2190420</a> provides recommendations how to check custom code and note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a> provides information about how you can use the custom code check tool to analyze if you are using any ABAP development objects in your custom code that are no longer supported.</p>\n<p>If you are using any development objects that are no longer supported, adapt your coding to ensure that these objects are no longer included in your custom code. If you need some of the development objects that are no longer supported, you can, for example, copy these objects into your customer namespace and adapt your custom code to use the copied versions.</p>\n</div>", "noteVersion": 7, "refer_note": [{"note": "2267461", "noteTitle": "2267461 - S4TWL - Simplification in Product Compliance for Discrete Industry", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With component extension for SAP EHS Management, customers can use the <em>Product Compliance for Discrete Industry</em> (EHS-MGM-PRC) solution for managing their product safety and stewardship business processes. In SAP S/4HANA on-premise edition 1511 and later, some functions are not available.</p>\n<ul>\n<li>BI extraction and queries for product compliance reporting including the roles SAP_EHSM_PRC_BI_EXTR and SAP_EHSM_PRC_BW_ANALYTICS</li>\n<li>Compliance Assessment Workflows</li>\n<li>Compliance Requirement Check Conflict Workflows</li>\n<li>Automated updated of contact person information upon import from Supplier declarations from SAP Product Stewardship Network</li>\n</ul>\n<p> </p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA on-premise edition the compliance reporting in BI is not available. For parts of the BI reporting, the compliance explorer and the exemption explorer is a functional equivalent.</p>\n<p>If you are using <em>SAP Product Stewardship Network</em> to collaborate with your suppliers, the contact person information are not updated automatically any more in SAP S/4HANA on-premise edition.</p>\n<p>The following compliance requirement check conflict workflow templates are not available in <em>SAP S/4HANA,</em> on-premise edition. They have been replaced with a new application that allows exploring the compliance requirement check conflicts based on native master data using CDS views.</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"TableGrid\">\n<tbody>\n<tr>\n<td width=\"418\">\n<p><strong>Workflow Template / Process Control Object</strong></p>\n</td>\n<td width=\"418\">\n<p><strong>Class Name / Event</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600054 - EHPRC_CHK_BA</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_COMP_REQ_BASE</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600049 - EHPRC_CHK_CH</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_COMP_REQ_CHROHS</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600056 - EHPRC_CHK_IM</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_CMP_REQ_IMDS</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600069 - EHPRC_CHK_JG</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_CMP_REQ_JIG</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600053 - EHPRC_CHK_SV</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_COMP_REQ_SVHC</p>\n</td>\n</tr>\n<tr>\n<td width=\"418\">\n<p>00600068 - EHPRC_CHK_RO</p>\n</td>\n<td width=\"418\">\n<p>CL_EHPRC_PCO_COMP_REQ_EUROHS</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p>The following compliance assessment workflow templates are not available in <em>SAP S/4HANA,</em> on-premise edition. The assessment processes for components and products have been replaced with a new application that provides an overview of running assessments for products and components based on native master data using CDS views.</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"TableGrid\">\n<tbody>\n<tr>\n<td width=\"278\">\n<p><strong>Workflow Template / Process Control</strong></p>\n<p><strong>Object</strong></p>\n</td>\n<td valign=\"top\" width=\"278\">\n<p><strong>Class Name / Event</strong></p>\n</td>\n<td valign=\"top\" width=\"278\">\n<p><strong>Type of Assessment Process</strong></p>\n</td>\n</tr>\n<tr>\n<td width=\"278\">\n<p>00600070 - EHPRC_BMASMT</p>\n</td>\n<td width=\"278\">\n<p>CL_EHPRC_PCO_BASMAT_ASSMNT</p>\n</td>\n<td width=\"278\">\n<p>Basic Material Assessment</p>\n</td>\n</tr>\n<tr>\n<td width=\"278\">\n<p>00600071 - EHPRC_COASMT</p>\n</td>\n<td width=\"278\">\n<p>CL_EHPRC_PCO_COMP_ASSMNT</p>\n</td>\n<td width=\"278\">\n<p>Component Assessment</p>\n</td>\n</tr>\n<tr>\n<td width=\"278\">\n<p>00600062 - EHPRC_PRASMT</p>\n</td>\n<td width=\"278\">\n<p>CL_EHPRC_PCO_PROD_ASSMNT</p>\n</td>\n<td width=\"278\">\n<p>Product Assessment</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Details about the necessary activities vebore and after converting, you find in the attachment. Ensure that you always have the latest version of the document.</p>", "noteVersion": 5}, {"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}]}, {"note": "2265093", "noteTitle": "2265093 - S4TWL - Business Partner Approach", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, any of the listed on-premise editions -&gt; 1511, 1610, 1709,1809, 1909, 2020, 2021, 2022. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>In SAP S/4HANA, Business Partner is the leading object and single entry point to maintain Business Partner, Customer and Supplier (formerly known as Vendor) master data. This is to ensure ease of maintenance of the above master data and to achieve harmonization between them. Compared to classical ERP transactions, maintenance of Customer and Supplier master data via Business Partner has multiple advantages. Some of them are as follows:</p>\n<ul>\n<li>Business Partner allows maintenance of multiple addresses with corresponding address usages.</li>\n<li>In classical transactions, one customer can only be associated to one account group. But in Business Partner, multiple roles can be associated to the same Business Partner.</li>\n<li>Maximal data sharing and reuse of data which lead to an easier data consolidation.</li>\n<li>General Data available for all different Business Partner roles, specific data is stored for each role.</li>\n<li>Maintenance of multiple relationships to the same Business Partner.</li>\n<li>Maintenance of Time Dependency at different sub-entities roles, address, relationship, bank data etc.</li>\n</ul>\n<p><strong>Description</strong></p>\n<p>There are redundant object models in the traditional ERP system. Here the vendor master and customer master is used. The (mandatory) target approach in S/4HANA is the Business Partner approach.</p>\n<p>Business Partner is now capable of centrally managing master data for business partners, customers, and vendors. With current development, BP is the single point of entry to create, edit, and display master data for business partners, customers, and vendors.</p>\n<p>Additional Remarks:</p>\n<ul>\n<li>It is planned to check the introduction of the Customer/Vendor Integration in the pre-checks and the technical Conversion procedure of SAP S/4HANA on-premise edition 1511, 1610, 1709, 1809, 1909 and 2020. A system where the customer/vendor integration is not in place will be declined for the transition.</li>\n<li>The Business Partner Approach is not mandatory for the SAP Simple Finance, on-premise edition 1503 and 1605.</li>\n</ul>\n<p><strong>Business Process related information</strong></p>\n<p>Only SAP Business Suite customer with C/V integration in place can move to SAP S/4HANA, on-premise(Conversion approach). It is recommended but not mandatory that BuPa ID and Customer-ID / Vendor ID are the same.</p>\n<p>The user interface for SAP S/4HANA is transaction BP. There is no specific user interface for customer/vendor like known from SAP Business Suite (the specific transactions like XD01, XD02, XD03 or VD01, VD02, VD03/XK01, XK02, XK03 or MK01, MK02, MK03 etc. are not available in SAP S/4HANA on-premise)</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td rowspan=\"2\" valign=\"top\" width=\"182\">\n<p>Transactions not available in SAP S/4HANA on-premise edition</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>Transactions that get redirected to transaction BP:</p>\n<p>FD01,FD02,FD03, FK01,FK02,FK03,MAP1,MAP2,MAP3, MK01, MK02, MK03, V-03,V-04,V-05,V-06,V-07,V-08,V-09, V-11, VAP1, VAP2, VAP3, VD01, VD02,VD03, XD01, XD02, XD03, XK01, XK06, XK07, XK02, XK03</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"414\">\n<p>Transactions that are obsolete:</p>\n<p>FD06, FK06, MK06, MK12, MK18, MK19, VD06, XD06, V+21, V+22, V+23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Points to Note in SAP S/4HANA:</p>\n<p>In addition, note the following points while using the following functionalities in SAP S/4HANA:</p>\n<ul>\n<li><strong>Consumer</strong> - While creating a consumer, ensure that you use the BP Grouping that is associated to the customer account group (Consumer) in TBD001 (Assignment of Account Group BP Grouping). Note that customer itself is a contact person, hence no further relationships can be maintained.</li>\n<li><strong>One-time Customer/Supplier</strong> - While creating a one-time customer or supplier, ensure that you use the BP Grouping that is associated to the customer/supplier account group (One time customer/supplier) in TBD001 and TBC001 (Assignment of Account Group BP Grouping).</li>\n<li>The idocs <strong>DEBMS/CREMAS</strong> and cremas are not recommended for data integration between S/4HANA systems. In S/4HANA, BP is the leading object. Therefore, only customer or supplier integration should be avoided. In S/4HANA, Business Partner is the leading object. Therefore, customer or supplier only integration should be avoided. You can use Business Partner web services (SOAP) via API Business Hub for SAP S/4HANA integration. It is recommended to use IDocs only if the source system has Customer or Supplier (no Business Partner) master data.</li>\n<li><strong>Role Validity</strong> - In SAP S/4HANA, you can maintain the validity of roles. With this feature, the user can close the (prospect) role and move to another after the mentioned validity period. For example, a business partner with prospect role can set the role change to sold-to-party by specifying the validity and by closing the previous validity. </li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Before you begin the BP conversion from an SAP ERP system to an SAP S/4 HANA system, you have to answer the questions</p>\n<ul>\n<li>Whether the Business Partner ID and Customer-ID /Vendor ID should be the same in the S/4 HANA System?</li>\n</ul>\n<p>The keys for a smooth synchronization of the ERP customer/vendor into the S/4 system with the business partner as the leading object are beside Business Partner Know-How also consistent customer/vendor data and valid and consistent custom/vendor and Business Partner customizing entries. For this reason, the customer/vendor data has to be cleaned up before it can be converted into the S/4 Business Partner.</p>\n<p>Prepare: Pre-Checks and clean-up customer/vendor data in the ERP System</p>\n<ul>\n<li>Implement SAP S/4HANA Conversion Pre-Checks according to the SAP S/4HANA Conversion guide chapter Pre-Checks.</li>\n<li>Activate Business Function CA_BP_SOA.<br/>In case that the Business Function CA_BP_SOA not yet in the system exist, you have to create a new Business Function in the customer namespace with the switches VENDOR_SFWS_SC1 and VENDOR_SFWS_SC2. The new customer specific Business Function must be of type Enterprise Business Function (G) - see also Mandatory checks for customer, vendor and contact.</li>\n<li>Check CVI customizing and trigger necessary changes  e.g. missing BP Role Category, Define Number Assignments according to the S/4 Conversion guide chapter Introduce Business Partner Approach (Customer Vendor Integration).</li>\n<li>Check and maintain BP customizing e.g. missing tax types.</li>\n<li>Check master data consistency using CVI_MIGRATION_PRECHK and maintain consistency.</li>\n<li>Check and clean-up customer/vendor data e.g. missing @-sign in the e-mail address.</li>\n</ul>\n<p>Synchronization</p>\n<ul>\n<li>Synchronization (Data load) is done via <em>Synchronization Cockpit</em> according to the attached BP Conversion Document.pdf &gt; Chapter 5. <em>Convert Customer/Supplier Data into Business Partner</em>.</li>\n<li>In case of an error during the synchronization process due to data/customizing mismatch you can find the errors using Logs button. You can also view this via MDS_LOAD_COCKPIT&gt; Monitor tab &gt; Call PPO button in Synchronization Cockpit.</li>\n</ul>\n<p>Conversion Process</p>\n<ul>\n<li>Conversion Process must be triggered according to the BP Conversion Document.pdf attached to this SAP Note.<strong><br/></strong></li>\n</ul>\n<p>Business Partner Post Processing</p>\n<ul>\n<li>The customer/vendor transformation is bidirectional. You can both process customer/vendor master records from business partner maintenance as well as populate data from customer/vendor processing to the business partner. After the successful S/4 conversion process you have to activate the post processing for direction Business Partner a Customer /Vendor.</li>\n</ul>\n<p><strong>Related SAP Notes &amp; Additional Information</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"158\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"446\">\n<p>SAP Notes: 2211312, 2210486, 2216176</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"158\">\n<p>Other SAP Notes</p>\n</td>\n<td valign=\"top\" width=\"446\">\n<p>SAP Note: 1623677, 954816</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"158\">\n<p>SAP Help</p>\n</td>\n<td valign=\"top\" width=\"446\">\n<p>Refer to the attachments (in SAP Note 2265093):</p>\n<ul>\n<li>BP_Conversion Document.pdf: <em>Description of Busines Partner Approach and conversion activities</em></li>\n<li>01_TOP-Item_MD_Business-Partner-Approach_Version1.0.pdf: <em>Presentation on Business Partner Approach and Customer Vendor Integration during Conversion</em></li>\n</ul>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Custom Code Adaption</strong></p>\n<p>If you are writing a direct write statement or a custom code on the tables mentioned in piece list SI_MD_BP, Please follow the below mentioned steps instead.</p>\n<p>Reason and Prerequisites:</p>\n<ul>\n<li>You want to create BP/Customer/Supplier with direct write statements or a particular piece of custom code.</li>\n<li>You need an API to create Business Partner with Customer and Supplier roles.</li>\n</ul>\n<p>The following are possible use-cases:</p>\n<ol start=\"1\">\n<li>Requirement to create business partners in the same system: You can use API CL_MD_BP_MAINTAIN. Pass the data in CVIS_EI_EXTERN structure format and pass it to the method VALIDATE_SINGLE. This method validates all the data that is passed; after that, you can use the method MAINTAIN to create business partners.</li>\n<li>Integration of data with various landscapes: If you want to integrate BP/Customer/Supplier master data across different systems, you can use following interfaces:<br/> <br/> a. IDOCs <br/> <br/> There are two types of IDocs available that can be used:</li>\n<ol start=\"1\">\n<li>DEBMAS: If you want to integrate customer (without BP) data between two systems, this IDOC can be used. </li>\n<li>CREMAS: If you want to integrate supplier (without BP) data between two systems, this IDOC can be used.</li>\n</ol></ol>\n<p>        b. SOA Services</p>\n<ol start=\"1\"><ol>\n<li>If the system has data of Business Partners, use SOA services for integration. Business Partner SOAP services enable you to replicate data between two systems. There are both inbound and outbound services available. Refer to SAP Note 2472030 for more information.</li>\n</ol></ol>\n<p>However, the IDocs DEBMAS and CREMAS are not recommended for data integration between S/4HANA systems. In S/4HANA, Business Partner is the leading object. Therefore, you can use Business Partner web services (SOAP) for SAP S/4HANA integration.</p>\n<p><strong>Industry Specific Actions</strong></p>\n<p>IS-OIL Specific Actions:</p>\n<ul>\n<li> In S4HANA all the IS-OIL specific fields (including customer BDRP data fields) have been adapted as per the new Framework of BP.</li>\n<li> The IS-OIL Fields related to Vendor and Customer now have been moved to BP under the roles FLCU01 (Customer), FLVN01 (Vendor).</li>\n</ul>\n<p>Retail Sites Specific Action:</p>\n<ul>\n<li>Customers and vendors assigned to Retail sites are not handled by CVI synchronization (MDS_LOAD_COCKPIT) on SAP ERP as described in this note, but by a specific migration process during SAP S/4HANA conversion. See SAP Note <a href=\"/notes/2310884\" target=\"_blank\">2310884</a> for more information.</li>\n</ul>", "noteVersion": 43, "refer_note": [{"note": "2344034", "noteTitle": "2344034 - SAP S/4HANA Automation for Master Data Migration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This report checks the necessary CVI and Business Partner Customizing for each client and proposes a solution for missing or wrong Customizing entries.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CVI_UPGRADE_CHECK_RESOLVE, AUTOMATION , UPGRADATION , CVIC_CUST_TO_BP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Automation of the existing precheck.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Perform the following steps:</p>\n<ol>\n<li>Implement the Note 2780288 first to set Migration scenario ON from CVI_UPGRADE_CHECK_RESOLVE</li>\n<li>Perform the manual pre implementation steps.</li>\n<li>Implement this note.</li>\n<li>After implementation, perform the manual post-implementation steps. <br/><br/></li>\n</ol>", "noteVersion": 80}, {"note": "2210486", "noteTitle": "2210486 - Obsolete : S/4HANA Business Partner Conversion Reports", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>** The report \"CVI_UPGRADE_CHECK_RESOLVE\" has been deprecated. Please use the new report \"CVI_CUSTOMIZING_RESOLVE\". For more information, see SAP Note 2891455.**</p>\n<p>This note contains information regarding the checks for necessary Customizing and synchronization for Customer/Supplier Integration (CVI), which have to be executed during the conversion from ERP to S/4HANA. During synchronization, the customer/supplier data get synchronized to Business Partner.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Conversion report, PRECHECK_UPGRADATION_REPORT, Precheck</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You need to ensure the necessary CVI mappings are maintained during conversion.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The CVI_UPGRADE_CHECK_RESOLVE performs the necessary checks per client, to find out missing customizing entries, and solution to create/correct these entries for each client. For information about this report, see SAP Note <a href=\"/notes/2344034\" target=\"_blank\">2344034 - S/4HANA Automation for Master Data Migration</a>.</p>\n<p>For completeness after the execution of CVI_UPGRADE_CHECK_RESOLVE Report and the CVI synchronization is completed (Customer and supplier data synchronized with Business partner)</p>\n<p>1. Deactivate the Badi's CVI_MIGRATION_SUPPRESS_CHK - Suppress Mandatory Checks, CVI_CONSUMER_RELATION - Restrict contact person creation for consumer which is available in the enhancement spot CVI_CUSTOM_MAPPER.</p>\n<p> 2. Click on COMPLETE flag in the report CVI_UPGRADE_CHECK_RESOLVE report (CVI Synchrionization)</p>\n<p> </p>\n<p>In addition, run the PRECHECK_UPGRADATION_REPORT (client independent) which determines the mapping entries that are missing between CVI data and BP data, and missing Customizing entries. Refer to SAP Note <a href=\"/notes/2216176\" target=\"_blank\">2216176 - Precheck report from business partner</a></p>\n<p>For more information on conversion activities for Business Partner, refer to <strong><em>BP_Conversion Document.pdf</em> </strong>which is attached to the SAP Note <a href=\"/notes/2265093\" target=\"_blank\">2265093 - Business Partner Approach</a>.</p>", "noteVersion": 9}, {"note": "3011764", "noteTitle": "3011764 - SEPA Mandate for Vendors", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>A consistency check to ensure debitors (customers) in table <em>TRFKREDEB_SYNC</em> are linked to same business partner as that of the creditors (vendors), prior to S/4HANA conversion.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SEPA Mandate, S/4HANA conversion, migration, BP/CVI, TRFKREDEB_SYNC, RFKREDEB_SYNC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>With SAP S/4HANA, customer and supplier (vendor) master data can be created and maintained using only the business partner transactions. A business partner can be created in both the customer and supplier (vendor) roles.</p>\n<p>The vendors and their mapped customers in table <em>TRFKREDEB_SYNC </em>must be synchronized to same business partner.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>A new simplification check is introduced in the class <em>“CLS4SIC_LO_MD_BP\" </em> to ensure debitors (customers) in table <em>TRFKREDEB_SYNC</em> are linked to the same business partner as that of creditors (vendors), prior to S/4HANA conversion.The simplification check will ensure that for all the entries in <em>TRFKREDEB_SYNC</em> table the vendors and mapped customers are linked to the same business partner. If not, it will throw error messages.</p>\n<p><strong>Additional Information</strong></p>\n<p>Before the upgrade, run <strong><em>RFKREDEB_SYNC</em></strong> report or execute <strong><em>MDS_LOAD_COCKPIT</em></strong>  to ensure that vendor and mapped customer are synchronized to same business partner.</p>\n<p>In case the error persists, perform the following steps:</p>\n<ol>\n<li>Create additional customer role for the business partner of that vendor.</li>\n<li>Archive the business partner of customer.</li>\n</ol>", "noteVersion": 2}, {"note": "2216176", "noteTitle": "2216176 - Obsolete :Precheck report from business partner", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>** The report \"PRECHECK_UPGRADATION_REPORT\" is obsolete and integrated into the \"CVI_COCKPIT\" transaction code. For more information, see SAP Note 2832085.**</p>\n<p>This note delivers the pre-conversion checks for Business Partner which have to be executed before the conversion to S/4.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Conversion report, CL_S4_Checks_BP, PRECHECK_UPGRADATION_REPORT, Precheck</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Pre-conversion check is done to ensure all the necessary <span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">CVI Link and </span>CVI mappings are done for both customer and vendor.</p>\n<p>As a preliminary step cvi_link check is carried out in the direction customer/vendor to BP. This check determines customer/vendor to BP link is established.</p>\n<p>Post-conversion checks are done to check whether all CVI mappings are done for Business partner which belong to customer or vendor role.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To check after the conversion process, please download this note. Once you have downloaded the note, execute the same in transaction SE38 after providing the report name <strong>PRECHECK_UPGRADATION_REPORT. </strong> This report includes both mandatory and optional checks, and if you do not perform the mandatory checks at least, ideally, the conversion should not be continued. If status turns red after the execution, we can click on each error status and get the information on what needs to be maintained.</p>", "noteVersion": 34}, {"note": "1623677", "noteTitle": "1623677 - BP_CVI: Check report for checking CVI Customizing", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the new Customer Vendor Integration (CVI), which is delivered with enhanced functions as of Release ERP 6.00, to synchronize the business partner object with the customer object and/or vendor object. You want to check the existing CVI Customizing that is required for this.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CVI, customer vendor integration, customer/vendor, customer-vendor, business partner, BP, FI customer, FI vendor, synchronization, synch, Customizing, Implementation Guide, check, check report, CVI_FS_CHECK_CUSTOMIZING</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Extensive Customizing is required for the synchronization between the business partner object and the customer object and/or vendor object, as described in SAP Note 956054 \"BP_CVI: Customer/vendor integration as of ERP 6.00\". Errors or terminations that occur during synchronization are often due to incorrect Customizing.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><b>General</b><br/> <p>The report CVI_FS_CHECK_CUSTOMIZING provided with this SAP Note can be used to display and check the Customizing for the customer vendor integration (CVI) that has been made for the business partner. The results of the checks return information about possible causes of error in the Customizing settings.<br/></p> <b>Installation</b><br/> <p>Install the report by importing the relevant Support Package of the underlying release.<br/></p> <b>Report call</b><br/> <p>Start the report using transaction CVI_FS_CHECK_CUST.<br/>Choose \"Display Help\" to call the detailed help for the relevant setting overview selected. You can choose \"Customizing\" to navigate back to Customizing and to check or adjust the settings for CVI.<br/><br/>The display of the current Customizing settings and the relevant check results are divided into three categories:</p> <ol>1. General settings</ol> <p>              These include the general settings for the synchronization directions, the connection between CVI and postprocessing using Post Processing Office (PPO), and the function modules required for inbound/outbound processing. <ol>2. Customer settings</ol> <p>              These include role assignments and number range assignments for the synchronization directions \"business partner -&gt; customer\", and \"customer -&gt; business partner\". <ol>3. Vendor settings</ol> <p>              These include role assignments and number range assignments for the synchronization directions \"business partner -&gt; vendor\" and \"vendor -&gt; business partner\".</p></p></p></div>", "noteVersion": 2}, {"note": "954816", "noteTitle": "954816 - BP_CVI: Transactions for creating/linking BPs", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In Release ERP2005, you use the following transactions to create business partners from customers or vendors or link them with existing business partners:<br/><br/>FLBPD1 - Create BP from customer<br/>FLBPD2 - Link BP to customer<br/>FLBPC1  Create BP from vendor<br/>FLBPC2 - Link BP to vendor<br/><br/>In the process, you find these perform differently to the previous releases.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Customer, vendor, FLBPD1, FLBPC1, FLBPD2, FLBPC2, MDSV_CTRL_OPT_A, CVIV_CUST_TO_BP1, CVIV_CUST_TO_BP2, CVIV_VEND_TO_BP1, CVIV_VEND_TO_BP2, TBD001, TBD002, TBC001, TBC002</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In Release ERP2005, the integration of the SAP Business Partner with the customer and the vendor (CVI) was redesigned. Starting with this release, the replication works bi-directionally and can be switched on and off for individual replication paths.<br/>Through the support of the replication of the customer/vendor to the business partner and the related technical constraints, it was necessary to create new Customizing tables and views and to adjust the function of the dialogs for the transactions accordingly.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>To be able to use Transactions FLBPD1, FLBPD2, FLBPC1 and FLBPC2, Customizing is required for the CVI relevant tables. Call all maintenance views using the IMG menu path \"Cross-Application Components\" -&gt; \"Master Data Synchronization\" and the entries below. Specifically, these are:</p> <ul><li>Activate synchronization objects (MDSV_CTRL_OPT_A)</li></ul> <p>           Here, you can activate the replications for business partner to customer, business partner to vendor, customer to business partner, and vendor to business partner.</p> <ul><li>Determine number assignment for direction BP to customer (V_TBD001)</li></ul> <p>           Account groups of the customer are assigned to the business partner groupings relevant for the replication. Using the related number ranges, the determination is made as to whether the customer numbers are assigned internally or externally or whether there is number equivalence.</p> <ul><li>Determine number assignment for direction BP to vendor (V_TBC001)</li></ul> <p>           Account groups of the vendor are assigned to the business partner groupings relevant for the replication. Using the related number ranges, the determination is made as to whether the customer numbers are assigned internally or externally or whether there is number equivalence.</p> <ul><li>Determine number assignment for direction customer to BP (CVIV_CUST_TO_BP1)</li></ul> <p>           Business partner groupings are assigned to the customers account groups relevant for the replication. Using the related number ranges, the determination is made as to whether the business partner numbers are assigned internally or externally. For external number assignment, number equivalence is absolutely necessary.</p> <ul><li>Determine number assignment for direction vendor to BP (CVIV_VEND_TO_BP1 )</li></ul> <p>           Business partner groupings are assigned to the vendors account groups relevant for the replication. Using the related number ranges, the determination is made as to whether the business partner numbers are assigned internally or externally. For external number assignment, number equivalence is absolutely necessary.</p> <ul><li>Set BP role category for direction BP to customer (V_TBD002)</li></ul> <p>           Here, the definition is made for which role categories of the SAP BP customers are created. Optional customer roles cannot be taken into account for creating BPs from customers.</p> <ul><li>Set BP role category for direction BP to vendor (V_TBC002)</li></ul> <p>           Here, the definition is made for which role categories of the SAP BP vendors are created. Optional vendor roles cannot be taken into account for creating BPs from vendors.<br/>           Determine BP role for direction customer to BP (CVIV_CUST_TO_BP2)<br/>           Defines the roles in which business partners are created from customers of a certain account group.</p> <ul><li>Determine BP role for direction vendor to BP (CVIV_VEND_TO_BP2)</li></ul> <p>           Defines the roles in which business partners are created from customers of a certain account group.<br/>Through Transactions FLBPD1 and FLBPC1, the same processes are triggered as run for the creation of a customer or vendor. For Transactions FLBPD2 and FLBPC2, the same processes are triggered as run for creating a business partner who is a vendor or customer within Transaction BP.<br/>For this reason, Customizing entries must be maintained in all tables and the synchronizations must be active for the correct execution of the transactions. If the synchronization for the direction customer/vendor -&gt; BP is not active, the customer or vendor is classified as irrelevant for the replication and it is not possible to create a business partner or to establish a link from that.<br/><br/>Following conditions must be fulfilled:<br/></p> <b>FLBPD1</b><br/> <ul><li>Customer already exists and was not linked to BP yet.</li></ul> <ul><li>Customer is not consumer or retail site.</li></ul> <ul><li>Synchronization customer -&gt; BP is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>A GP grouping was assigned to the current customer accounts group (CVIV_CUST_TO_BP1).</li></ul> <ul><li>At least one BP role was assigned to the current customer accounts group (CVIV_CUST_TO_BP2).</li></ul> <b>FLBPC1</b><br/> <ul><li>Vendor already exists and was not linked to BP yet.</li></ul> <ul><li>Synchronization vendor -&gt; BP is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>A GP grouping was assigned to the current vendor account group (CVIV_VEND_TO_BP1).</li></ul> <ul><li>At least one BP role was assigned to the current vendor account group (CVIV_VEND_TO_BP2).</li></ul> <b>FLBPD2</b><br/> <ul><li>Business partner already exists and was not linked to a customer yet.</li></ul> <ul><li>Customer already exists and was not linked to a business partner yet.</li></ul> <ul><li>Synchronization BP -&gt; customer is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>Synchronization customer -&gt; BP is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>A customer accounts group was assigned to the current GP grouping (V_TBD001).</li></ul> <ul><li>A GP grouping was assigned to the current customer accounts group (CVIV_CUST_TO_BP1).</li></ul> <ul><li>The selected BP role is a customer role (V_TBD002).</li></ul> <ul><li>The business partner grouping used and the customer accounts group used do not have to be assigned to each other.</li></ul> <b>FLBPC2</b><br/> <ul><li>Business partner already exists and was not linked to a vendor yet.</li></ul> <ul><li>Vendor already exists and was not linked to a business partner yet.</li></ul> <ul><li>Synchronization BP -&gt; vendor is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>Synchronization vendor -&gt; BP is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>A vendor account group was assigned to the current GP grouping (V_TBC001 ).</li></ul> <ul><li>A GP grouping was assigned to the current vendor account group (CVIV_VEND_TO_BP1).</li></ul> <ul><li>The selected BP role is a vendor role (V_TBC002).</li></ul> <ul><li>The business partner grouping used and the vendor account group used do not have to be assigned to each other.</li></ul> <p></p> <b>Additional notes:</b><br/> <ol>1. Substitute transaction</ol> <p>Instead of Transactions FLBPD1 and FLBPC1, you can use the synchronization cockpit (Transaction MDS_LOAD_COCKPIT). This can be used for large-scale creation of business partners from customers and vendors.</p> <ol>2. A BP exists with customer and vendor role.</ol> <p>If a business partner already has customer or vendor roles without being linked to customers and vendors, a link is no longer possible using Transactions FLBPD2/FLBPC2. If internal number assignment was set for the objects involved, this results in a new entry of a vendor that is linked with the business partner, in addition to the link with the customer for FLBPD2. If the external number assignment is set, this results in a termination since either the vendor number does not exist or the vendor already exists (number equivalence).<br/><br/>This problem can only occur if:</p> <ul><li>The roles are created when synchronization is switched off.</li></ul> <ul><li>Role Customizing was subsequently changed (V_TBD002, V_TBC002).</li></ul> <ul><li>Roles/BPs are created using paths other than those provided with the standard system (own programs, direct inserts into table BUT100).</li></ul> <p><br/>In this case, the following workarounds may help:</p> <ul><li>Switch off of the synchronization for the other object (MDSV_CTRL_OPT_A). After the successful program run, it is switched on again and the link with the other object is repeated.</li></ul> <ul><li>Manually create the assignments in tables CVI_CUST_LINK and CVI_VEND_LINK and subsequently synchronize the business partners involved using the synchronization cockpit.</li></ul> <ol>3. Required entry field comparison</ol> <p>So that no errors occur for the update of the data, the required entry fields of the objects involved must first be reconciled with each other and the data filled. Refer to Note 928616 for more information on this topic.</p></div>", "noteVersion": 2}, {"note": "2211312", "noteTitle": "2211312 - S4TC SAP_APPL – Pre-Conversion check for Business Partner", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note delivers the pre-conversion checks for Business Partner which have to be executed before the conversion to S/4 HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Conversion report, CL_S4_Checks_BP.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Pre-conversion check is done to ensure that all the necessary CVI mappings are done for both customer and vendor.</p>\n<p>Post-conversion checks are done to check whether all CVI mappings are done for business partner which belong to customer or vendor role.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Refer to SAPNote (<strong>2210486)</strong> for all the checks which were executed. This also provides the Customizing (IMG) path where the missing entries need to be maintained.</p>\n<p>To check after the conversion process, refer SAP note 2832085 for CVI_COCKPIT transaction code which has PRECHECK_UPGRADATION_REPORT integrated into it.</p>", "noteVersion": 29}, {"note": "2780288", "noteTitle": "2780288 - S/4 HANA Migration: The BP checks can be suppressed in the direction Customer/Vendor->BP during synchronization from MDS_LOAD_COCKPIT", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During synchronization from MDS_LOAD_COCKPIT, Business Partner would not get created if Customer/Vendor has errors. In a Migration scenario, these checks can be suppressed through suppression customizing.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SUPPRESSION CHECKS, MDS_LOAD_COCKPIT, SYNCHRONIZATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Suppression of business checks during synchronization</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Perform the following steps:</p>\n<ol>\n<li>Implement the pre-requisite notes 2345087, 2784166, 2323598, 2749015 and 2807124 prior to the current note.</li>\n<li>Perform the manual pre implementation steps.</li>\n<li>Implement the automatic correction instructions.</li>\n<li>After implementation, perform the manual post-implementation steps. </li>\n</ol>\n<p>During synchronization from MDS_LOAD_COCKPIT, there is default BAdI implementation (CVI_CUSTOM_MAPPER) called up in the direction Customer/Supplier to Business partner, to bypass the following checks:</p>\n<ul>\n<li>Postal Code check &amp; Address Regional check</li>\n<li>Geo Code checks</li>\n<li>Mandatory Field checks</li>\n<li>Tax Number checks</li>\n<li>Bank checks</li>\n<li>Tax Jusrisdiction checks</li>\n<li>Email checks</li>\n</ul>\n<p><strong>Note: </strong>Postal code check &amp; Address Regional checks are always suppressed by default in normal synchronization process(non-migration scenario). These checks can be enabled/disabled <strong>ONLY</strong> in a Migration scenario</p>\n<p><strong>Note</strong><span>: Only the Mandatory check suppression is logged and can be viewed from CVI Synchronization.</span></p>\n<p><span><strong>Note</strong>: By default the checks are enabled and unless the checks are disabled explicitly in the customizing delivered for controlling the suppression checks, the checks would be executed always.</span></p>\n<p>For BP Synchronization:</p>\n<ol>\n<li>Go to se38 and execute the report <em>CVI_CUSTOMIZING_RESOLVE</em>. <br/>For<em> CVI_CUSTOMIZING_RESOLVE </em>documentation, kindly look into<em> SAP Note 2265093 &gt; BP_Conversion Document.PDF (attachment) &gt; Appendix &gt; <em>CVI_CUSTOMIZING_RESOLVE </em>.</em></li>\n<li>Choose the radio button CVI Synchronization.</li>\n<li>This will enable Migration scenario and the checks would be perfomred based on the customing maintained for suppression checks</li>\n</ol>\n<p><strong>Note</strong>: You have to de-activate the BAdI implementation using Note 2651202 to disable the suppression checks after succesful conversion to S/4.</p>", "noteVersion": 11}]}, {"note": "2241080", "noteTitle": "2241080 - SAP S/4HANA: Content for checking customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to check your customer specific ABAP code via ABAP Test Cockpit (ATC) for compatibility with SAP S/4HANA as described in SAP note <a href=\"/notes/0002190420\" target=\"_blank\">2190420</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Custom Code Check, ABAP Test Cockpit, ATC, Simplification Database, System Conversion, Release Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In order to provide accurate results, ATC requires up to date content describing changes done to SAP objects in SAP S/4HANA (= Simplification Database).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP provides the most recent content for the Simplification Database of SAP S/4HANA as a ZIP file in the Software Center, which you can download and import into your system.</p>\n<p><strong>Downloading the Simplification Database Content</strong></p>\n<ul>\n<li>Go to <a href=\"https://me.sap.com/softwarecenter\" target=\"_blank\">https://me.sap.com/softwarecenter</a></li>\n<li>Search for Component \"CCMSIDB\"</li>\n<li>Download the displayed \"Simplification Database Content\" (most recent version: patch level 18 - creation date: 2024-02-28)</li>\n<ul>\n<li>When using content version 2021-09-28 or newer, please ensure that you have also implemented SAP note 3039646. With this content version the usage detection accuracy for DDIC objects has been improved. But without the corresponding check logic from SAP note 3039646, this content version will lead to an increased number of false positives.</li>\n</ul>\n</ul>\n<p><strong>Importing the Simplification Database Content</strong></p>\n<p>For details how to import the Simplification Database content into your ATC system please refer to the ABAP Platform help:</p>\n<p>   &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/4355509bf8a75f6be10000000a1553f6.html\" target=\"_blank\">Application Development on AS ABAP</a><br/>      &gt; Customer-Specific ABAP Development<br/>        &gt; Custom Code Migration<br/>         &gt; SAP S/4HANA Conversion<br/>           &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/41316510041a4b53974ee74ab6d52512.html\" target=\"_blank\">Simplification Database</a></p>\n<p>When updating the Simplification Database content to the most recent version, please also ensure, that you update the check logic to the most recent version,  by implementing all relevant, new notes from SAP note 2436688. Otherwise you might get wrong check results (e.g. false positives).</p>\n<p>Please be aware, that upon importing the Simplification Database content into your code analysis system you might sometimes get a message \"Details on download of simplification notes: note &lt;note number&gt; incomplete.\" This only means the corresponding SAP note, describing a specific custom code impact is temporarily not accessible as it's being updated by the responsible development area. Or that the connection from your system to SAP service marketplace for note downloads has not been setup. This has no impact on the successful import of the Simplification Database content into your code analysis system. And this will not negatively impact the result of the custom code analysis.</p>\n<p>Though this note download mechanism can cause long import times for the Simplification Database content or even timeouts in systems that are not properly configured for downloading SAP notes. In this case you can implement SAP note 3221402. This adds an option to the Simplification Database upload dialog, to skip the note downloads. The only drawback is, that then the note titles shown next to the ATC results might not be up to date in all cases. But the correct notes are shown anyway.<a href=\"/notes/3221402\" target=\"_blank\" title=\"3221402  - Skip download of SAP Notes during the import of simplification database\"><br/></a></p>\n<p><strong>Additional Information</strong></p>\n<ul>\n<li>The main focus of the Simplification Database Content is to identify the custom code impact of Simplification Items (<a href=\"https://launchpad.support.sap.com/#/sic/overview\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/overview</a>) and other major functional or technical changes in SAP S/4HANA.</li>\n<li>Please be aware that code checks based on the SIDB content do not replace the need to properly test relevant custom code during maintenance events, in order to see if there is any impact from syntactical or semantical changes done on SAP side.</li>\n<li>For prioritization of issues found by the SAP S/4HANA ATC checks, please take the category of the findings into consideration. Especially the findings under the \"Non-strategic...\" categories indicate usage of objects which will become obsolete only at a later point in time. The ATC errors of this category only serve as a reminder, that such objects will become obsolete in future releases. For example compatibility pack functionality (see SAP note 2269324 for details). So these findings do not necessarily have to be fixed already in earlier SAP S/4HANA releases. Please refer to the individual notes shown along the \"Non-strategic...\" findings for details.</li>\n<li>The Simplification Database content covers all available SAP S/4HANA releases. This e.g. allows you, as preparation for your SAP S/4HANA conversion project, to scan your custom code against different SAP S/4HANA target releases with the same Simplification Database content. Which is helpful if the exact target version of your SAP S/4HANA conversion project is not yet decided on.</li>\n<li>It's recommended to use this content for ATC checks in preparation of a SAP S/4HANA system conversion or release upgrade. But it should also be used to continuously check the SAP S/4HANA compliance of customer specific coding in development projects on SAP S/4HANA. </li>\n<li>Only customers with a valid SAP S/4HANA license are able to download this SAP S/4HANA content.</li>\n</ul>\n<p>Changelog:</p>\n<ul>\n<li>Patchlevel 16:</li>\n<ul>\n<li>Automatic calculation of deletions. As of this patch level the Simplification Database also contains all main objects (R3TR) that have been deleted between two consecutive SAP S/4HANA releases, even if the deletion is not related to any Simplification Item or major functional or technical change (e.g. deletions of unused / orphaned objects).</li>\n</ul>\n<li>Patchlevel 17:</li>\n<ul>\n<li>Custom code content for various Simplification Items (especially related to compatibility scope) has been added or updated. Focus of the updates is SAP S/4HANA 2023 initial shipment.</li>\n<li>Automatic calculation of deletions now also considers objects that have been deleted between SAP ERP EHP8 and SAP S/4HANA, that are not covered by other specific Simplification Items.</li>\n</ul>\n<li>Patchlevel 18:</li>\n<ul>\n<li>Custom code content for various Simplification Items has been added or updated. Focus of the updates is SAP S/4HANA 2023 FPS1.</li>\n<li>Improvements to the automatic calculation of deletions. This will reduce fealse positives related to SAP note 2296016 (e.g. if an object did exist in SAP ERP, was deleted in early SAP S/4HANA versions, but reintroduced again in higher SAP S/4HANA versions, this is no longer considered as a deletion) as well as reduce the overlap of findings for SAP note 2296016 with findings for other Simplification Items. Also FI/CO tables (see SAP note 3414643) that have technically been deleted, but are covered by compatibility views are no longer considered as a deletion.</li>\n</ul>\n</ul>\n<p>In case of</p>\n<ul>\n<li>issues with importing the Simplification Database content into your system please open an incident on BC-DWB-CEX.</li>\n<li>general questions related to the Simplification Database Content please open an incident on CA-TRS-TDBC.</li>\n<li>questions on individual custom code SAP notes please open an incident on the application component of the respective SAP note.</li>\n</ul>", "noteVersion": 37}, {"note": "2194782", "noteTitle": "2194782 - Pre-Transition Checks for software component EHSM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>Pre-Transition Checks for software component EHSM </span></p>\n<p><strong><span>1. Checks for Incident Management</span></strong></p>\n<ul>\n<li><span>Check ID: EHSM_INC_MATERIAL</span></li>\n</ul>\n<p><span>Released materials with material number from the Material Management (MM) component found: Material numbers will be removed, material descriptions are kept. Adding material numbers is not supported in S/4HANA.</span></p>\n<ul>\n<li><span>Check ID: EHSM_INC_QM_NOTIFICATION_1</span></li>\n</ul>\n<p><span> Open quality notifications found: Quality notifications must be completed before upgrade.</span></p>\n<ul>\n<li><span>Check ID: EHSM_INC_QM_NOTIFICATION_2</span></li>\n</ul>\n<p><span>  Open recurring quality notifications found: End dates of recurrences must be set to past date before upgrade.</span></p>\n<ul>\n<li><span>Check ID: EHSM_INC_QM_NOTIFICATION_3</span></li>\n</ul>\n<p><span>  Completed quality notifications found: Existing ones will only be displayed without quality notification specific attributes. Adding quality notifications is not supported in S/4HANA.</span></p>\n<ul>\n<li><span>Check ID: EHSM_INC_CS_NOTIFICATION_1</span></li>\n</ul>\n<p><span>  Open service notifications found: Service notifications must be completed before upgrade. </span></p>\n<ul>\n<li><span>Check ID: EHSM_INC_CS_NOTIFICATION_2</span></li>\n</ul>\n<p><span>   Open recurring service notifications found: End dates of recurrences must be set to past date before upgrade.</span></p>\n<ul>\n<li><span>Check ID: EHSM_INC_CS_NOTIFICATION_3</span></li>\n</ul>\n<p><span>   Completed service notifications found: Existing ones will only be displayed without service notification specific attributes. Adding service notifications is not supported in S/4HANA.</span></p>\n<ul>\n<li><span>Check ID: EHSM_INC_SERVICE_ORDERS</span></li>\n</ul>\n<p><span>   Service orders for financial tracking found: Existing ones will only be displayed without service order specific attributes. Adding service orders is not supported in S/4HANA.</span></p>\n<ul>\n<li><span>Check ID: EHSM_INC_GRC_INTEGRATION</span></li>\n</ul>\n<p><span>   RFC connection for GRC integration found: GRC integration is not supported in S/4HANA.</span></p>\n<ul>\n<li><span>Check ID: EHSM_INC_INTEGRATION_DESTINATION</span></li>\n</ul>\n<p><span>   Integration destination found for components that only run locally: For the following components, RFC connections will not be supported because the components can only be accessed locally</span><span>: </span><span><br/></span></p>\n<ul>\n<ul>\n<li><span>Business Partner</span></li>\n<li><span><span>Human Resource Management</span></span></li>\n<li><span><span><span>Plant Maintenance</span></span></span></li>\n<li><span><span><span><span><span><span>Accounting</span></span></span></span></span></span></li>\n</ul>\n</ul>\n<p><span><span><span><span><span><span>Check you integration settings for these components after conversion.</span></span></span></span></span></span></p>\n<p><strong><span>2. Checks for Risk Assessment</span></strong></p>\n<ul>\n<li><span>Check ID: EHSM_RAS_AMOUNTS</span></li>\n</ul>\n<p><span>Amounts found: Amounts are no longer supported in S/4HANA. Functionality is replaced by workplace sampling.</span></p>\n<ul>\n<li><span>Check ID: EHSM_RAS_DATA_SERIES</span></li>\n</ul>\n<p><span><span>Data Series found: </span>Data Series are no longer supported in S/4HANA. Functionality is replaced by workplace sampling.</span></p>\n<ul>\n<li><span>Check ID: EHSM_RAS_QM_NOTIFICATION_1</span></li>\n</ul>\n<p><span> Open quality notifications found: Quality notifications must be completed before upgrade.</span></p>\n<ul>\n<li><span>Check ID: EHSM_RAS_QM_NOTIFICATION_2</span></li>\n</ul>\n<p><span>  Open recurring quality notifications found: End dates of recurrences must be set to past date before upgrade.</span></p>\n<ul>\n<li><span>Check ID: EHSM_RAS_QM_NOTIFICATION_3</span></li>\n</ul>\n<p><span>  Completed quality notifications found: Existing ones will only be displayed without quality notification specific attributes. Adding quality notifications is not supported in S/4HANA.</span></p>\n<ul>\n<li><span>Check ID: EHSM_RAS_CS_NOTIFICATION_1</span></li>\n</ul>\n<p><span>  Open service notifications found: Service notifications must be completed before upgrade. </span></p>\n<ul>\n<li><span>Check ID: EHSM_RAS_CS_NOTIFICATION_2</span></li>\n</ul>\n<p><span>   Open recurring service notifications found: End dates of recurrences must be set to past date before upgrade.</span></p>\n<ul>\n<li><span>Check ID: EHSM_RAS_CS_NOTIFICATION_3</span></li>\n</ul>\n<p><span>   Completed service notifications found: Existing ones will only be displayed without service notification specific attributes. Adding service notifications is not supported in S/4HANA.</span></p>\n<ul>\n<li><span>Check ID: EHSM_RAS_INTEGRATION_DESTINATION</span></li>\n</ul>\n<p><span>   Integration destination found for components that only run locally: For the following components, RFC connections will not be supported because the components can only be accessed locally<span>:</span> <span><span><br/></span></span></span></p>\n<ul>\n<ul>\n<li><span>Business Partner</span></li>\n<li><span><span>Human Resource Management</span></span></li>\n<li><span><span><span>Plant Maintenance</span></span></span></li>\n<li><span><span><span><span>Inventory Management</span></span></span></span></li>\n<li><span><span><span><span><span><span><span><span>SAP EHS Management as part of SAP ERP</span></span></span></span></span></span></span></span></li>\n</ul>\n</ul>\n<p><span><span><span><span><span><span><span><span><span><span><span><span><span><span>Check you integration settings for these components after conversion.</span></span></span></span></span></span></span></span></span></span></span></span></span></span><span> </span></p>\n<p><strong>3. Checks for Product Compliance Management</strong></p>\n<ul>\n<li>Check ID: EHSM_PRC_AWF</li>\n</ul>\n<p>Assessment workflows found. The compliance assessment workflow is no longer supported in S/4HANA (see SAP note 2230140). Complete all open compliance assessment workflow tasks.</p>\n<ul>\n<li>Check ID: EHSM_PRC_BW</li>\n</ul>\n<p>Active datasources found for business object 'Compliance Data'. The Business Warehouse is no longer supported in S/4HANA (see SAP note 2230140).</p>\n<ul>\n<li>Check ID: EHSM_PRC_CWF</li>\n</ul>\n<p>Check workflows found. The check workflow is no longer supported in S/4HANA (see SAP note 2230140). Complete all open check workflow tasks.</p>\n<ul>\n<li>Check ID: EHSM_PRC_PSN</li>\n</ul>\n<p>Check for PSN Synchronization of contact data: The synchronization of contact data is no longer supported in S/4HANA.</p>\n<ul>\n<li>Check ID: EHSM_PRC_BTE</li>\n</ul>\n<p>Entry for Business Transaction Event CS000010 found. After migration to S/4 HANA verify setting for Business Transaction Event CS000010 (see IMG activity Prerequisites for Bill of Material Transfer).</p>\n<p><strong><span>4. Checks for Environmental Management</span></strong></p>\n<ul>\n<li><span>Check ID:  EHSM_EMIS</span></li>\n</ul>\n<p>Environmental Management  can no longer be used in S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement note 2194368 \"S4TC EHSM - Checks for software component EHSM\". The checks for software component EHSM will be executed during the transition process.</p>", "noteVersion": 9}, {"note": "2194368", "noteTitle": "2194368 - S4TC EHSM Master Check for S/4HANA System Conversion Checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>Pre-Transition Checks for software component EHSM have to be executed before the upgrade to S/4HANA.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC<br/>S/4HANA transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note. The checks for software component EHSM will be executed during the transition process.</p>", "noteVersion": 10}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Data cleanup / archiving", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}]}