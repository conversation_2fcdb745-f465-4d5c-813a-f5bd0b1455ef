{"guid": "0050569455E21ED5B3E17678391E009E", "sitemId": "SI3: Logistics_PM", "sitemTitle": "S4TWL - Scheduling of Maintenance Plan", "note": 2270078, "noteTitle": "2270078 - S4TWL - Scheduling of Maintenance Plan", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Transaction IP30 is doing scheduling for Maintenance Plans. Within this scheduling outdated technology (Batch Input)  is used.</p>\n<p>Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is not available yet.<br/>We plan to discontinue this in one of the next Releases.</p>\n<p>The new transaction for doing mass scheduling is IP30H which is optimized for HANA and is offering parallel processing at a much hiher speed.</p>\n<p><strong>Business Process related information</strong></p>\n<p>No influence on business processes expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Review your background Jobs which you most probably have scheduled periodically for transaction IP30 (Reports RISTRA20) and create new background jobs for IP30H (Report RISTRA20H).</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Review your background Jobs which you most probably have scheduled periodically for transaction IP30 (Reports RISTRA20) and create new background jobs for IP30H (Report RISTRA20H)."}]}