{"guid": "0050569455E21ED5B3E176783911E09E", "sitemId": "SI4: Logistics_PLM", "sitemTitle": "S4TWL - Access Control Management (ACM)", "note": 2267842, "noteTitle": "2267842 - S4TWL - Access Control Management (ACM)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PLM, ACM, Simplification, Access Control Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Access Control Management not supported in S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The PLM specific Access Control Management is not available in the SAP S/4HANA shipment. Access Control related UI fields are not available from the PLM objects' Web screens. Authorization restriction is not in place so these fields would not be considered and system won't restrict authorizations.</p>\n<p>Also please check the related SAP Note: 2212593</p>\n<p><strong>Business Process related information</strong></p>\n<p>The unavailability of ACM in SAP S/4HANA means that instance specific control for data access is not in place. Normal authorization object can be used via standard transactions, like PFCG.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Check if Access Control Management is in use see below \"How to Determine Relevancy\" chapter. If you are using Access Control Management to control access to system data, you should consider or remodel it with ERP authorization, usually managed with PFCG roles.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note 2212593</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if:</p>\n<ul>\n<li>ACM objects are linked to Business Objects, thus entries exist in DB Table <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone31\">/PLMB/AUTH_OBSID</span> (check with SE16)</li>\n<li>ACM is used if entries exist in DB Table HRP7600 where Otype=CC, as these entries contain ACM type objects (check with SE16)</li>\n<li>This note is relevant only up to S/4HANA 1709 FPS0</li>\n</ul>", "noteVersion": 7, "refer_note": [{"note": "2212593", "noteTitle": "2212593 - Access Control Management Hiding", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>ACM related fields, navigations and controlling mechanism is disabled/removed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4Core, ACM, Access Control, Hiding, Simplification</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In S4H ACM is not supported.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The ACM control, related fields (UI changes):</p>\n<ul>\n<ul>\n<li>Deactivated the switch /PLMI/ACM_SFWS_S4_01</li>\n<ul>\n<li>Create (in SFW2) and activate (in SFW5) local and reversible Business Function with assigning the /PLMI/ACM_SFWS_S4_01 switch</li>\n</ul>\n<li>Set the proper customizing based on EhP7 customizing.</li>\n<ul>\n<li>necessary customizings</li>\n<ul>\n<li>view maintenance in transaction SM30</li>\n<ul>\n<li><span 'times=\"\" 12.0pt;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" en-gb;=\"\" lang=\"EN-GB\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-font-size:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">/PLMB/V_GOS_OTYP</span></li>\n</ul>\n</ul>\n</ul>\n<ul>\n<ul>\n<ul>\n<ul>\n<li><span 'times=\"\" 12.0pt;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" en-gb;=\"\" lang=\"EN-GB\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-font-size:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">removed the PLM_ACC object type</span></li>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"73\">\n<p>Object Type</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>Description of Obj.Type</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>Class for Conv.to Ext. Format</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>Class for Conv.to Int. Format</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>Control Conversion Rule</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>DDIC Structure Type of Object</p>\n</td>\n<td valign=\"top\" width=\"47\">\n<p>DDIC Table Type of Object</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>Service Relevance</p>\n</td>\n<td valign=\"top\" width=\"73\">\n<p>Icon</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"73\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>Access Control Context</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>/PLMB/CL_AUTH_ACC_BO</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>/PLMB/CL_AUTH_ACC_BO</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>Conversion Not in Back-End System</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>/PLMB/S_AUTH_ACC_HDR_EXT_ID</p>\n</td>\n<td valign=\"top\" width=\"47\"></td>\n<td valign=\"top\" width=\"66\">\n<p>Relevant for Last Accessed and Recently Changed</p>\n</td>\n<td valign=\"top\" width=\"73\">\n<p>~Icon/AccessControlledArea</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<ul>\n<ul>\n<ul>\n<ul>\n<li><span 'times=\"\" 12.0pt;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" en-gb;=\"\" lang=\"EN-GB\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-font-size:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 12.0pt;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" en-gb;=\"\" lang=\"EN-GB\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-font-size:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">/PLMB/V_FRW_PRM</span></span> </li>\n</ul>\n</ul>\n</ul>\n<ul>\n<ul>\n<ul>\n<ul>\n<li><span 'times=\"\" 12.0pt;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" en-gb;=\"\" lang=\"EN-GB\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-font-size:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 12.0pt;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" en-gb;=\"\" lang=\"EN-GB\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-font-size:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">removed the ACCID_EXT parameter</span></span></li>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n<p>Parameter Name:           ACCID_EXT</p>\n<p>Parameter Description:  Access Control Context ID (external)</p>\n<p>Data element:                /PLMB/AUTH_ACC_ID_EXTERNAL</p>\n<ul>\n<ul>\n<ul>\n<ul>\n<ul>\n<li>/PLMU/V_NAVTG</li>\n<ul>\n<li>removed the ACC transfer list navigatation target</li>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n<p>Navigation Target:                     OIF_ACC_TRANSFER_LIST</p>\n<p>Navigation Target Description:   ACC transfer list</p>\n<ul>\n<ul>\n<ul>\n<ul>\n<ul>\n<li>/PLMB/V_MAPDTL</li>\n<ul>\n<li>removed data element mapping</li>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"246\">\n<p>Data element</p>\n</td>\n<td valign=\"top\" width=\"55\">\n<p>Group ID</p>\n</td>\n<td valign=\"top\" width=\"99\">\n<p>Parameter Name</p>\n</td>\n<td valign=\"top\" width=\"83\">\n<p>Object Type</p>\n</td>\n<td valign=\"top\" width=\"81\">\n<p>Object Type Reference</p>\n</td>\n<td valign=\"top\" width=\"56\">\n<p>Field Type</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"246\">\n<p>/PLMB/AUTH_ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"55\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"99\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"83\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"81\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"56\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"246\">\n<p>/PLMB/AUTH_INHER_ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"55\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"99\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"83\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"81\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"56\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"246\">\n<p>/PLMB/AUTH_PARENT_ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"55\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"99\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"83\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"81\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"56\">\n<p> </p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<ul>\n<ul>\n<ul>\n<ul>\n<li>/PLMB/V_MAPFLD</li>\n<ul>\n<li>removed field mapping</li>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>Table Name</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>Field Name</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>Group ID</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>Parameter Name</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>Object Type</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>Object Type Reference Field</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>Field Type</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_ACC_SEA_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>EXTERNAL_KEY</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_ACC_SEA_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>PARENT_ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_ACC_SEA_RESULT_ESH</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_ACC_SEA_RESULT_ESH</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>PARENT_ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_ASSIG_OBJ_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>CONCATENATED_KEY</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>KEY</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>OBJECT_TYPE</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_ASSIG_OBJ_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>OBJECT_ID</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>KEY</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>OBJECT_TYPE</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>SAP_INTKEY</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_ASSIG_OBJ_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>OBJECT_TYPE</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>FRW_OTYPE</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_HDR_EXT_ID</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_HDR_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_HDR_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>PARENT_ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_HIERARCHY_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>CHILD_ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_INIT_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_INIT_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>PARENT_ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_OBJ_OL_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>CONCATENATED_KEY</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>KEY</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>OBJECT_TYPE</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_OBJ_OL_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>LENT_TO_ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_OBJ_OL_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>OBJECT_ID</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>KEY</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>OBJECT_TYPE</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>SAP_INTKEY</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_OBJ_OL_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>OBJECT_TYPE</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>FRW_OTYPE</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_OWNACC_DESCR</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_PREVIEW_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>PARENT_ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_ROLE_ID</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>ROLE_ID</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_ACC_ROLE_ID</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ROLE_ID</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>ROLE_ID</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ROLE_ID</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACCROL</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_AUTH_TFR_LIST_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_DIR_SEA_RESULT_ESH</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_ECN_SEA_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_ECR_SEA_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_EDESK_DOCUMENT_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_EDESK_DOCUMENT_TREE_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_EXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_GSS_B2B_SEA_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>OWNING_CONTEXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_GSS_I2B_SEA_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>OWNING_CONTEXT</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_GSS_R2X_SU_SP</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXT_BOM</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_MAT_SEA_RESULT_ESH</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_MBOM_SEA_ESH_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p>MBOM</p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_PVS_SEA_ASM_GRP_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_PVS_SEA_ASM_HDR_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_PVS_SEA_NODE_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_PVS_SEA_VARIANT_RESULT</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"227\">\n<p>/PLMB/S_RCP_SEA_RESULT_ESH</p>\n</td>\n<td valign=\"top\" width=\"142\">\n<p>ACC_ID_EXTERNAL</p>\n</td>\n<td valign=\"top\" width=\"66\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p>ACCID_EXT</p>\n</td>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"85\">\n<p> </p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<ul>\n<ul>\n<ul>\n<ul>\n<li>/PLMB/V_GOS_SGRP</li>\n<ul>\n<li>deassigned services to service groups</li>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>Service Group</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>Service Name</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ALL</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ALL</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ALL_CR</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ALL_CR</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_DIR</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_DIR</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ECN</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ECN</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ER</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_ER</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_GSS</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_GSS</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_LBL</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_LBL</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_PPEND</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_PPEND</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_PPEVAR</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_PPEVAR</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_RCP</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_RCP</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_RCPMFE</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_RCPMFE</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_RCP_BB</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_RCP_BB</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_RSPSUB</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_RSPSUB</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_SPC</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_OWNCTX</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>PLM_SPC</p>\n</td>\n<td valign=\"top\" width=\"132\">\n<p>FRW_TRNSFL</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<ul>\n<ul>\n<ul>\n<ul>\n<li>/PLMU/V_FRW_CTXM</li>\n<ul>\n<li>removed context menu for ACC</li>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n<p>Object Type:     PLM_ACC</p>\n<p>ABB ID:</p>\n<p>Role:                PLM</p>\n<p>Instance:           CM_ACC</p>\n<p>Service Group:  PLM_ACC</p>\n<ul>\n<ul>\n<ul>\n<ul>\n<ul>\n<li>/PLMU/V_FRW_NAV</li>\n<ul>\n<li>removed navigation targets</li>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"76\">\n<p>Object Type</p>\n</td>\n<td valign=\"top\" width=\"105\">\n<p>ABB ID</p>\n</td>\n<td valign=\"top\" width=\"183\">\n<p>Navigation Target</p>\n</td>\n<td valign=\"top\" width=\"41\">\n<p>Role</p>\n</td>\n<td valign=\"top\" width=\"77\">\n<p>Instance</p>\n</td>\n<td valign=\"top\" width=\"159\">\n<p>LaunchPad Application Alias</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"76\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"105\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"183\">\n<p>OIF_ACC_TRANSFER_LIST</p>\n</td>\n<td valign=\"top\" width=\"41\">\n<p>PLM</p>\n</td>\n<td valign=\"top\" width=\"77\">\n<p>GENERAL</p>\n</td>\n<td valign=\"top\" width=\"159\">\n<p>LPD_ACC_QAF</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"105\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"183\">\n<p>OBJECT_CHANGE</p>\n</td>\n<td valign=\"top\" width=\"41\">\n<p>PLM</p>\n</td>\n<td valign=\"top\" width=\"77\">\n<p>GENERAL</p>\n</td>\n<td valign=\"top\" width=\"159\">\n<p>LPD_ACC_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"105\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"183\">\n<p>OBJECT_CREATE</p>\n</td>\n<td valign=\"top\" width=\"41\">\n<p>PLM</p>\n</td>\n<td valign=\"top\" width=\"77\">\n<p>GENERAL</p>\n</td>\n<td valign=\"top\" width=\"159\">\n<p>LPD_ACC_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"105\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"183\">\n<p>OBJECT_DEFAULT</p>\n</td>\n<td valign=\"top\" width=\"41\">\n<p>PLM</p>\n</td>\n<td valign=\"top\" width=\"77\">\n<p>GENERAL</p>\n</td>\n<td valign=\"top\" width=\"159\">\n<p>LPD_NAV</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"105\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"183\">\n<p>OBJECT_DISPLAY</p>\n</td>\n<td valign=\"top\" width=\"41\">\n<p>PLM</p>\n</td>\n<td valign=\"top\" width=\"77\">\n<p>GENERAL</p>\n</td>\n<td valign=\"top\" width=\"159\">\n<p>LPD_ACC_DISPLAY</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"105\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"183\">\n<p>OBJECT_DEFAULT</p>\n</td>\n<td valign=\"top\" width=\"41\">\n<p>PLM</p>\n</td>\n<td valign=\"top\" width=\"77\">\n<p>GENERAL</p>\n</td>\n<td valign=\"top\" width=\"159\">\n<p>LPD_ACC_DISPLAY</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"105\">\n<p>PLM_WCC</p>\n</td>\n<td valign=\"top\" width=\"183\">\n<p>OBJECT_DEFAULT</p>\n</td>\n<td valign=\"top\" width=\"41\">\n<p>PLM</p>\n</td>\n<td valign=\"top\" width=\"77\">\n<p>GENERAL</p>\n</td>\n<td valign=\"top\" width=\"159\">\n<p>LPD_NAV</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"76\">\n<p>PLM_ACC</p>\n</td>\n<td valign=\"top\" width=\"105\">\n<p>PLM_WCC_CC</p>\n</td>\n<td valign=\"top\" width=\"183\">\n<p>OBJECT_DEFAULT</p>\n</td>\n<td valign=\"top\" width=\"41\">\n<p>PLM</p>\n</td>\n<td valign=\"top\" width=\"77\">\n<p>GENERAL</p>\n</td>\n<td valign=\"top\" width=\"159\">\n<p>LPD_NAV</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>How to show ACM related content (Object Access, Access Control, User Group menu, My Active Context POWL) in PFCG roles.</li>\n<ul>\n<li>Show Object Access menu items in the Work Overview of the PFCG roles</li>\n<ul>\n<li>The 'Visibility' parameter was set to 'Invisible' for all items in the Role Menu/Recipe Development/Work Overview/Object Access folder for the following roles</li>\n<ul>\n<li>SAP_PLMWUI_PROCESS_MENU</li>\n<li>SAP_PLMWUI_PROCESS_MENU2</li>\n<li>SAP_PLMWUI_PROCESS_MENU3</li>\n<li>SAP_PLMWUI_PROCESS_MENU4</li>\n<li>SAP_PLMWUI_DISCRETE_MENU</li>\n<li>SAP_PLMWUI_DISCRETE_MENU2</li>\n<li>SAP_PLMWUI_DISCRETE_MENU3</li>\n</ul>\n</ul>\n<li>Show Object Access Work Center in the PFCG roles</li>\n<ul>\n<li>The 'Visibility' parameter was set to 'Invisible' and the 'Default Page' setting was removed for the Object Access Work Center (in the Role Menu/Recipe Development/Object Access folder) for the following roles</li>\n<ul>\n<li>SAP_PLMWUI_PROCESS_MENU</li>\n<li>SAP_PLMWUI_PROCESS_MENU2</li>\n<li>SAP_PLMWUI_PROCESS_MENU3</li>\n<li>SAP_PLMWUI_PROCESS_MENU4</li>\n<li>SAP_PLMWUI_DISCRETE_MENU</li>\n<li>SAP_PLMWUI_DISCRETE_MENU2</li>\n<li>SAP_PLMWUI_DISCRETE_MENU3</li>\n</ul>\n</ul>\n<li>Show the My Active Context POWL on the Work Overview in the PFCG roles (except SAP_PLMWUI_PROCESS_MENU and SAP_PLMWUI_DISCRETE_MENU)</li>\n<ul>\n<li>Changed the configuration on the Work Overview</li>\n<li>Removed the PLM POWL CHIP (X-SAP-WDY-CHIP:/PLMU/WDC_WCC_POWL_CHIP)</li>\n<li>Unset the Application ID to PLM_POWL_WCC_CC_MYCTX</li>\n</ul>\n</ul>\n<li>Switched-off Owning Context/Access Control Context in the applications</li>\n<ul>\n<li>Set the generic component to Invisible</li>\n<ul>\n<li>Open the WDC: /PLMU/WDC_AUTH_OWNACC_DO, View: V_OWNACC and set the TPC_OWNACC element: 'visible' property to 'invisible'</li>\n</ul>\n<li>The application which uses the generic component made invisible</li>\n<ul>\n<li>/PLMU/WDC_DIR / V_DIR_HEADER / TPC_OWNING_CONTEXT - set the 'visible' property to 'invisible'</li>\n<li>/PLMU/WDC_ECN / V_ECN_DESCRIPTION / VCE_OWN_ACC - set the 'visible' property to 'invisible'</li>\n<li>/PLMU/WDC_ECR_HEADER / V_HEADER / TPC_OWNING_CTX - set the 'visible' property to 'invisible'</li>\n<li>/PLMU/WDC_GSS_PLAN_B2B / V_DET_ADMIN_DATA / TPC_ACC - set the 'visible' property to 'invisible'</li>\n<li>/PLMU/WDC_GSS_PLAN_I2B / V_DET_ADMIN_DATA /</li>\n<ul>\n<li>TPC_CTX_GUTTER - set the 'visible' property to 'invisible'</li>\n<li>TPC_CTX - set the 'visible' property to 'invisible'</li>\n<ul>\n<li>TXV_ACC - set the 'visible' property to 'invisible'</li>\n<li>LBL_OWNING_CTX - set the 'visible' property to 'invisible'</li>\n<li>TPC_CTX_INIT- set the 'visible' property to 'invisible'</li>\n<ul>\n<li>LTA_OWNING_CTX - set the 'visible' property to 'invisible'</li>\n</ul>\n</ul>\n</ul>\n<li>/PLMU/WDC_LBL_BASIC_DATA / V_MAIN / TPC_AUTH - set the 'visible' property to TABLE_CONTROLLER/SHOW_AUTH (Bind Directly...)</li>\n<li>/PLMU/WDC_MAT / V_MAT_OWN_CTX / TPC_OWNING_CTX - set the 'visible' property to 'invisible'</li>\n<li>/PLMU/WDC_MBOM / V_HEADER_DETAIL</li>\n<ul>\n<li>VCE_OWN_CONTEXT -  the 'visible' property was set to 'invisible'</li>\n<li>TPC_OWNACC_GUTTER - the 'visible' property was set to 'invisible'</li>\n</ul>\n<li>/PLMU/WDC_RCP / V_BASIC_DATA</li>\n<ul>\n<li>VCE_OWN_ACC - the 'visible' property was set to 'invisible'</li>\n<li>HZG_3 - the 'visible' property was set to 'invisible'</li>\n</ul>\n<li>/PLMU/WDC_RSP_ACC / MAIN / VC_ACC - the 'visible' property was set to 'invisible'</li>\n<li>/PLMU/WDC_SPC_BAS_ACC / V_ACC / VCE_OWN_ACC - set the 'visible' property to VISIBILITY/INVISIBLE (Bind Directly...)</li>\n</ul>\n<li>The application which does not use the generic component was also made  Invisible</li>\n<ul>\n<li>Product Structure</li>\n<ul>\n<li>set the /PLMU/WDC_PPE_CMP_OIF_CFG component configuration / Main Screens / button choice: Assign / Access Control Context to 'Invisible'</li>\n<li>set the /PLMU/WDC_PPE_CMP_OVP_CFG component configuration / Main Page / button choice: Assign / Access Control Context to 'Invisible'</li>\n<li>/PLMU/WDC_PPE_CMP WD / View: V_INIT_CMP / CONT_CHNG_MGMT</li>\n<ul>\n<li>TEXT_CHNG_MGMT_AUTH - set the 'visible' property to PPE_INIT/OWNING_CONTEXT</li>\n<li>TEXT_CHNG_MGMT - set the 'visible' property to PPE_INIT/OWNING_CONTEXT</li>\n<li>LABEL_ACC - set the 'visible' property to PPE_INIT/OWNING_CONTEXT</li>\n<li>INPUTFIELD_ACC - set the 'visible' property to PPE_INIT/OWNING_CONTEXT</li>\n</ul>\n<li>/PLMU/WDC_PPE_CMP WD / View: V_HEADER_REGION_OVP / CONT_TEXT/CONT_OTHERS</li>\n<ul>\n<li>ROOTCT_OWNING_CONTEXT_LBL - the 'visible' property was set to 'invisible'</li>\n<li>ROOTCT_OWNING_CONTEXT - the 'visible' property was set to 'invisible'</li>\n</ul>\n</ul>\n<li>Product Assembly</li>\n<ul>\n<li>set the /PLMU/WDC_PPE_CMP_ASM_OIF_CFG component configuration / Main Screens / button choice: Assign / Access Control Context to 'Invisible</li>\n</ul>\n<li>PPE PVS</li>\n<ul>\n<li>/PLMU/WDC_PPE_PVS_RMPINIT / View: V_ADDITIONAL_DATA / TPC_CHANGE_MGMT</li>\n<ul>\n<li>LBL_ACC - the 'visible' property was set to 'invisible'</li>\n<li>INP_ACC - the 'visible' property was set to 'invisible'</li>\n</ul>\n</ul>\n<li>Planning Scope</li>\n<ul>\n<li>open the /PLMU/WDC_RTG_RCA_HDRASG_CFG component configuration and add below the AENNR_TEXT the following:</li>\n<ul>\n<li>ACC_ID_EXT_MBOM / Input Field / Access Control Context</li>\n<li>ACC_ID_EXT_MBOM_TEXT / Link To Action / Context Description</li>\n</ul>\n<li>open the /PLMU/WDC_RTG_RCA_HADTL_G_F component configuration and add below the change number the following:</li>\n<ul>\n<li>ACC_ID_EXT_MBOM / Input Field / Access Control Context</li>\n<li>ACC_ID_EXT_MBOM_TEXT / Link To Action / Context Description</li>\n</ul>\n</ul>\n<li>Status and Action Managemen </li>\n</ul>\n<ul>\n<ul>\n<li>/PLMU/WDC_SAM / V_OBJ_STATUS / TPC_STATUS_FORM</li>\n<ul>\n<li>LBL_TARGET_CONTEXT- set the 'visible' property to NEXT_STATUS/ACC_ID_EXT (Bind to Property 'Invisible')</li>\n<li>TPC_TARGET_CONTEXT - the 'visible' property was set to 'invisible'</li>\n<li>LBL_TARGET_DATA - set the 'Layout Data' property to “MatrixData” from “MatrixHeadData”</li>\n</ul>\n</ul>\n<li>Document Browser - Create Folder popup</li>\n<ul>\n<li>/PLMU/WDC_DIR_BRW / V_CRT_FOLDER_OR_DOC / TPC_CREATE_FOLDER</li>\n<ul>\n<li>LBL_OWNING_CONTEXT - the 'visible' property was set to 'invisible'</li>\n<li>INP_OWNING_CONTEXT - the 'visible' property was set to 'invisible'</li>\n</ul>\n</ul>\n<li>Work Environment</li>\n<ul>\n<li>/PLMU/WDC_WEN / View: V_WEN_MAIN / TC_COLL_CONTEXT</li>\n<ul>\n<li>LBL_COLL_CONTEXT - the 'visible' property was set to 'invisible'</li>\n<li>INP_COLL_CONTEXT - the 'visible' property was set to 'invisible'</li>\n</ul>\n<li>/PLMU/WDC_WEN / View: V_WEN_POPUP / TS_POPUP/TAB_SPECIFIC/TC_OUTER/TC_WEN_POPUP/TC_DATA</li>\n<ul>\n<li>COLL_CONTEXT_LBL - the 'visible' property was set to 'invisible'</li>\n<li>COLL_CONTEXT -the 'visible' property was set to 'invisible'</li>\n</ul>\n</ul>\n</ul>\n</ul>\n<li>How hided the Owning Context/Access Control Context in the preview applications</li>\n<ul>\n<li>Set the generic preview component to Invisible</li>\n<ul>\n<li>Open the WDC: /PLMU/WDC_AUTH_OAC_PRW_DO, View: V_OWNACC and set the TPC_OWNACC element: 'visible' property to 'Invisible'</li>\n</ul>\n<li>The application which uses the generic preview component has to made Invisible</li>\n<ul>\n<li>/PLMU/WDC_DIR_DO_PRV / V_DIR_PREVIEW_HEADER / TPC_DOCUMENT_OUT/VCE_OWN_CONTEXT - set the 'visible' property to AUTHORIZATION/INVISIBLE (Bind Directly...)</li>\n<li>/PLMU/WDC_DIR_DO_PRV_VERT / V_DIR_PREVIEW_HEADER / TPC_DOCUMENT_DATA/VCE_OWN_CONTEXT - set the 'visible' property to AUTHORIZATION/INVISIBLE (Bind Directly...)</li>\n<li>/PLMU/WDC_ECN_PREVIEW / V_PREVIEW_HEADER</li>\n<ul>\n<li>TRANS_PREV_COV/VCU_OWNACC - the 'visible' property was set to 'invisible'</li>\n<li>TRANS_PREV_COV/HGUTTER_2 - the 'visible' property was set to 'invisible'</li>\n</ul>\n<li>/PLMU/WDC_ECR_PREVIEW / V_ECR_PREVIEW / TPC_OWN_CTX - the 'visible' property was set to 'invisible'</li>\n<li>/PLMU/WDC_LBL_PREVIEW / V_PREVIEW</li>\n<ul>\n<li>VCU_OWNACC_PREVIEW - set the 'visible' property to VISIBILITY/INVISIBLE_ACM (Bind Directly...)</li>\n<li>HG_2 - the 'visible' property was set to 'invisible'</li>\n</ul>\n<li>/PLMU/WDC_MAT_PREVIEW / V_MAT_PREVIEW / VCU_OWN_CTX_PREVIEW - the 'visible' property was set to 'invisible'</li>\n<li>/PLMU/WDC_MBOM_PREVIEW / V_PREVIEW_HEADER</li>\n<ul>\n<li>VCU_OWNACC_PREVIEW - the 'visible' property was set to 'invisible'</li>\n<li>HZG_OWNACC_PREVIEW -the 'visible' property was set to 'invisible'</li>\n</ul>\n<li>/PLMU/WDC_PPE_ADMIN / V_PPE_ADMIN / TPC_PPE_ADMIN_COV/VCU_OWN_ACC - the 'visible' property was set to 'invisible'</li>\n<li>/PLMU/WDC_RCP_PREVIEW / V_PREVIEW</li>\n<ul>\n<li>VCU_OWNACC_PREVIEW - the 'visible' property was set to 'invisible'</li>\n<li>HG_3 - the 'visible' property was set to 'invisible'</li>\n</ul>\n<li>/PLMU/WDC_RSP_PREVIEW / V_PREVIEW</li>\n<ul>\n<li>VCU_OWNACC_PREVIEW - set the 'visible' property to VISIBILITY2/INVISIBLE2 (Bind Directly...)</li>\n<li>HG_3 - the 'visible' property was set to 'invisible'</li>\n</ul>\n<li>component configuration EAMS_WDC_DIR_PRVW_CFG</li>\n<ul>\n<li>add on 'General Data' a 'Melting Group' below the 'Valid From'</li>\n<ul>\n<li>Removed melt. group element 'ACC_ID_EXT' as 'Input Field' with label 'Access Control Context' with label visibility 'Is not visible'</li>\n<li>Removed melt. group element 'DESCR' as 'Text View' with label 'Description of Context' with label visibility 'Is Visible'</li>\n</ul>\n</ul>\n<li>component configuration EAMS_WDC_MAT_PRVW_CFG</li>\n<ul>\n<li>Removed on 'Basic Data' a 'Melting Group' below the 'Industry Standard Description'</li>\n<ul>\n<li>Removed melt. group element 'ACC_ID_EXT' as 'Input Field' with label 'Access Control Context' with label visibility 'Is not Visible'</li>\n<li>add melt. group element 'DESCR' as 'Text View' with label 'Description of Context' with label visibility 'Is Visible'</li>\n</ul>\n</ul>\n</ul>\n</ul>\n<li>How removed the Authorizations tab to the UI:</li>\n<ul>\n<li>component configuration /PLMU/WDC_DIR_OIF_CFG / Main View: AUTHORIZATIONS - checked the 'Hidden Element'</li>\n<li>component configuration /PLMU/WDC_ECN_OIF_CFG / Main View: M9 - checked the 'Hidden Element'</li>\n<li>component configuration /PLMU/WDC_ECR_APP_OIF_CFG / Main View: MAINVIEW6 - checked the 'Hidden Element'</li>\n<li>component configuration /PLMU/WDC_LBL_OIF_CFG / Main View: MV_AUTH_SW3 - checked the 'Hidden Element'</li>\n<li>component configuration /PLMU/WDC_MAT_OIF_CFG / Main View: MAIN7 - checked the 'Hidden Element'</li>\n<li>component configuration /PLMU/WDC_MBOM_OIF_CFG / Main View: AUTH - checked the 'Hidden Element'</li>\n<li>component configuration /PLMU/WDC_PPE_CMP_ASM_OIF_CFG / Main View: Assembly Group / Subview: Authorizations - checked the 'Hidden Element'</li>\n<li>component configuration /PLMU/WDC_RCP_OIF_CFG / Main View: MV_AUTH - checked the 'Hidden Element'</li>\n<li>component configuration /PLMU/WDC_RCP_BB_OIF_CFG / Main View: MV_AUTH - checked the 'Hidden Element'</li>\n<li>component configuration /PLMU/WDC_SPC_OIF_CFG / Main View: GENERAL DATA / Subview: AUTH - checked the 'Hidden Element'</li>\n</ul>\n</ul>", "noteVersion": 6, "refer_note": [{"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Check if access control management is in use. If you are using Access Control Management to control access to system data, you should consider or remodel it with ERP authorization, usually managed with PFCG roles."}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "Use new process instead of ACM"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Custom Code related information SAP Note 2212593"}]}