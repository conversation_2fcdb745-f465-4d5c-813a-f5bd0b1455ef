{"guid": "901B0E6D3EE91ED6939B2538DEE0A0CF", "sitemId": "SI21 Logistics_PS", "sitemTitle": "S4TWL - Handling Large Projects Business Functions", "note": 2352369, "noteTitle": "2352369 - S4TWL - Handling Large Projects Business Functions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Project System business functions for Handling Large Projects and related transactions are not the target architecture anymore in SAP S/4HANA.</p>\n<p>With SAP S/4 HANA 1511 - FPS1 hence the following business functions are switchable (to enable technical conversion) but obsolete:</p>\n<ul>\n<li>OPS_PS_HLP_1</li>\n<li>OPS_PS_HLP_2</li>\n</ul>\n<p>The following transactions and related IMG activities are categorized not to be the target architecture and cannot be used any longer with SAP S/4HANA:</p>\n<ul>\n<li>PSHLP10 Project Worklist</li>\n<li>PSHLP20 Project Editor</li>\n<li>PSHLP30 Draft Workbench</li>\n<li>PSHLP90 Administrator Workbench</li>\n</ul>\n<p><strong><em>Basic Information related to Business Functions</em></strong></p>\n<p><em>If a business function was switched “ON” in the Business Suite start release system, but defined as “always_off” in the SAP S/4HANA, on-premise edition target release, then a system conversion is not possible with this release. See SAP Note 2240359 - SAP S/4HANA, on-premise edition 1511: Always-Off Business Functions. If a business function is defined as \"customer_switchable\" in the target release (SAP S/4HANA, on-premise edition 1511 – FPS1), then the business function status remains unchanged from the status on start release. Business Functions defined as “obsolete” cannot be switched “ON”</em></p>\n<p><strong>Business Process related information</strong></p>\n<p>Customer having these business functions switched “ON” in Business Suite start release can execute the system conversion. Nevertheless the functionality related to the business functions OPS_PS_HLP_1, OPS_PS_HLP_2 are not available within SAP S/4 HANA 1511. Customers having none of these business functions switched “ON” in Business Suite start release cannot activate these business functions after the system conversion to SAP S/4 HANA 1511.</p>\n<p>Alternative standard functions should be considered. Instead of Handling Large Projects transactions e.g. the Project Builder transaction should be used.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>None</p>\n<p> </p>\n<p> </p>", "noteVersion": 1, "refer_note": [{"note": "2343381", "noteTitle": "2343381 - Deprecation of Handling Large Projects(HLP) in Project Systems(PS)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Handling Large Projects(HLP) functionality is deprecated in Project Systems(PS) in S4.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\"> S4_TRANSFORMATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Remove the usages of objects, detected by the custom code check, from your customer coding. These objects are not existing any more.</p>", "noteVersion": 1}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Project System business functions for Handling Large Projects and related transactions are not available in SAP S/4HANA, evaluate alternative options"}, {"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Alternative standard functions should be considered. Instead of Handling Large Projects transactions e.g. the Project Builder transaction should be used"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Remove the usages of unavailable objects from your Custom Code as per SAP Note 2343381"}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}