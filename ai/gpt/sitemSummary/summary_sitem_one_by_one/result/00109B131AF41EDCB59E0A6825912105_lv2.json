{"guid": "00109B131AF41EDCB59E0A6825912105", "sitemId": "SI38: Logistics_PP", "sitemTitle": "S4TWL - Time Streams Usage in Kanban", "note": 3196066, "noteTitle": "3196066 - SAP S/4HANA Simplification Item: Usage of Time Streams in Kanban", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In S/4HANA, persisted time streams are not longer used for Kanban time calculations.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, TTSTR, Time Stream, Kanban, Kanban Summarized JIT Call, Time Definitions, Kanban Calculation, OM19, OJI3</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The usage of persisted time streams for time calculations based on factory calendars and shift definitions has been removed in Kanban.</p>\n<ul>\n<li>For Kanban Calculation, dynamic time streams are used which are generated on the fly based on the customizing settings.</li>\n<li>For the determination of delivery dates and for Kanban Summarized JIT Calls, a new calculation algorithm was introduced.</li>\n</ul>\n<p>In the customizing transactions OM19 (\"Define Kanban Calculation Profiles\") and OJI3 (\"Define Time Definition\"), the time stream entries in table TTSTR will still be saved and transported.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>If the SAP standard functionality for Kanban time calculation is used, there is no adoption needed.</p>\n<p>Customer specific modifications in the function group includes L0PK1F01, L0PK1F03, or LPABC1F01 that change the way how time streams are created will have no effect anymore on the Kanban time calculations.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Customer specific extensions / modifications of the time streams handling or usage in the Kanban process (PP-KAB) might need to be reimplemented. See SAP note 3196066."}]}