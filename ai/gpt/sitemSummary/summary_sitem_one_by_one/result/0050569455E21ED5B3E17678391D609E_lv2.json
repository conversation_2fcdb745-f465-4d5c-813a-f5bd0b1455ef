{"guid": "0050569455E21ED5B3E17678391D609E", "sitemId": "SI11: Logistics_PSS", "sitemTitle": "S4TWL - PS&S SARA Reports", "note": 2267438, "noteTitle": "2267438 - S4TWL - PS&S SARA Reports", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The creation of SARA reports is not available within SAP S/4HANA. A successor functionality is on the roadmap for the on-premise release S/4HANA 1909.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Transaction CBIHSR01 is not available in SAP S/4HANA.</p>", "noteVersion": 3, "refer_note": [{"note": "2214914", "noteTitle": "2214914 - SAP S/4 HANA Simplification Item: SARA Report Creation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use transaction CBIHSR01 to create SARA reports. You are planning to convert to SAP S/4 HANA and need to check before doing so if adaptions to your own coding are required in order to make it compatible with SAP S/4 HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4 simplification</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The creation of SARA reports by using transaction CBIHSR01 is no longer supported and is not available with S/4 HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Note 2241080 describes the procedure and tools that you can use in order to check if you are using any of the removed ABAP development objects in your custom coding.</p>\n<p>If you are using any of the removed development objects, adapt your coding and make sure that the deleted development objects are no longer used. If you need some of the removed development objects, you can, for example, copy these development objects into your customer namespace and adapt your custom coding to use the copied versions.</p>", "noteVersion": 3, "refer_note": [{"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}, {"note": "2241080", "noteTitle": "2241080 - SAP S/4HANA: Content for checking customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to check your customer specific ABAP code via ABAP Test Cockpit (ATC) for compatibility with SAP S/4HANA as described in SAP note <a href=\"/notes/0002190420\" target=\"_blank\">2190420</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Custom Code Check, ABAP Test Cockpit, ATC, Simplification Database, System Conversion, Release Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In order to provide accurate results, ATC requires up to date content describing changes done to SAP objects in SAP S/4HANA (= Simplification Database).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP provides the most recent content for the Simplification Database of SAP S/4HANA as a ZIP file in the Software Center, which you can download and import into your system.</p>\n<p><strong>Downloading the Simplification Database Content</strong></p>\n<ul>\n<li>Go to <a href=\"https://me.sap.com/softwarecenter\" target=\"_blank\">https://me.sap.com/softwarecenter</a></li>\n<li>Search for Component \"CCMSIDB\"</li>\n<li>Download the displayed \"Simplification Database Content\" (most recent version: patch level 18 - creation date: 2024-02-28)</li>\n<ul>\n<li>When using content version 2021-09-28 or newer, please ensure that you have also implemented SAP note 3039646. With this content version the usage detection accuracy for DDIC objects has been improved. But without the corresponding check logic from SAP note 3039646, this content version will lead to an increased number of false positives.</li>\n</ul>\n</ul>\n<p><strong>Importing the Simplification Database Content</strong></p>\n<p>For details how to import the Simplification Database content into your ATC system please refer to the ABAP Platform help:</p>\n<p>   &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/4355509bf8a75f6be10000000a1553f6.html\" target=\"_blank\">Application Development on AS ABAP</a><br/>      &gt; Customer-Specific ABAP Development<br/>        &gt; Custom Code Migration<br/>         &gt; SAP S/4HANA Conversion<br/>           &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/41316510041a4b53974ee74ab6d52512.html\" target=\"_blank\">Simplification Database</a></p>\n<p>When updating the Simplification Database content to the most recent version, please also ensure, that you update the check logic to the most recent version,  by implementing all relevant, new notes from SAP note 2436688. Otherwise you might get wrong check results (e.g. false positives).</p>\n<p>Please be aware, that upon importing the Simplification Database content into your code analysis system you might sometimes get a message \"Details on download of simplification notes: note &lt;note number&gt; incomplete.\" This only means the corresponding SAP note, describing a specific custom code impact is temporarily not accessible as it's being updated by the responsible development area. Or that the connection from your system to SAP service marketplace for note downloads has not been setup. This has no impact on the successful import of the Simplification Database content into your code analysis system. And this will not negatively impact the result of the custom code analysis.</p>\n<p>Though this note download mechanism can cause long import times for the Simplification Database content or even timeouts in systems that are not properly configured for downloading SAP notes. In this case you can implement SAP note 3221402. This adds an option to the Simplification Database upload dialog, to skip the note downloads. The only drawback is, that then the note titles shown next to the ATC results might not be up to date in all cases. But the correct notes are shown anyway.<a href=\"/notes/3221402\" target=\"_blank\" title=\"3221402  - Skip download of SAP Notes during the import of simplification database\"><br/></a></p>\n<p><strong>Additional Information</strong></p>\n<ul>\n<li>The main focus of the Simplification Database Content is to identify the custom code impact of Simplification Items (<a href=\"https://launchpad.support.sap.com/#/sic/overview\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/overview</a>) and other major functional or technical changes in SAP S/4HANA.</li>\n<li>Please be aware that code checks based on the SIDB content do not replace the need to properly test relevant custom code during maintenance events, in order to see if there is any impact from syntactical or semantical changes done on SAP side.</li>\n<li>For prioritization of issues found by the SAP S/4HANA ATC checks, please take the category of the findings into consideration. Especially the findings under the \"Non-strategic...\" categories indicate usage of objects which will become obsolete only at a later point in time. The ATC errors of this category only serve as a reminder, that such objects will become obsolete in future releases. For example compatibility pack functionality (see SAP note 2269324 for details). So these findings do not necessarily have to be fixed already in earlier SAP S/4HANA releases. Please refer to the individual notes shown along the \"Non-strategic...\" findings for details.</li>\n<li>The Simplification Database content covers all available SAP S/4HANA releases. This e.g. allows you, as preparation for your SAP S/4HANA conversion project, to scan your custom code against different SAP S/4HANA target releases with the same Simplification Database content. Which is helpful if the exact target version of your SAP S/4HANA conversion project is not yet decided on.</li>\n<li>It's recommended to use this content for ATC checks in preparation of a SAP S/4HANA system conversion or release upgrade. But it should also be used to continuously check the SAP S/4HANA compliance of customer specific coding in development projects on SAP S/4HANA. </li>\n<li>Only customers with a valid SAP S/4HANA license are able to download this SAP S/4HANA content.</li>\n</ul>\n<p>Changelog:</p>\n<ul>\n<li>Patchlevel 16:</li>\n<ul>\n<li>Automatic calculation of deletions. As of this patch level the Simplification Database also contains all main objects (R3TR) that have been deleted between two consecutive SAP S/4HANA releases, even if the deletion is not related to any Simplification Item or major functional or technical change (e.g. deletions of unused / orphaned objects).</li>\n</ul>\n<li>Patchlevel 17:</li>\n<ul>\n<li>Custom code content for various Simplification Items (especially related to compatibility scope) has been added or updated. Focus of the updates is SAP S/4HANA 2023 initial shipment.</li>\n<li>Automatic calculation of deletions now also considers objects that have been deleted between SAP ERP EHP8 and SAP S/4HANA, that are not covered by other specific Simplification Items.</li>\n</ul>\n<li>Patchlevel 18:</li>\n<ul>\n<li>Custom code content for various Simplification Items has been added or updated. Focus of the updates is SAP S/4HANA 2023 FPS1.</li>\n<li>Improvements to the automatic calculation of deletions. This will reduce fealse positives related to SAP note 2296016 (e.g. if an object did exist in SAP ERP, was deleted in early SAP S/4HANA versions, but reintroduced again in higher SAP S/4HANA versions, this is no longer considered as a deletion) as well as reduce the overlap of findings for SAP note 2296016 with findings for other Simplification Items. Also FI/CO tables (see SAP note 3414643) that have technically been deleted, but are covered by compatibility views are no longer considered as a deletion.</li>\n</ul>\n</ul>\n<p>In case of</p>\n<ul>\n<li>issues with importing the Simplification Database content into your system please open an incident on BC-DWB-CEX.</li>\n<li>general questions related to the Simplification Database Content please open an incident on CA-TRS-TDBC.</li>\n<li>questions on individual custom code SAP notes please open an incident on the application component of the respective SAP note.</li>\n</ul>", "noteVersion": 37}]}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}