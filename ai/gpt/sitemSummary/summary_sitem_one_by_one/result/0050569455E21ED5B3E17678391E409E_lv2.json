{"guid": "0050569455E21ED5B3E17678391E409E", "sitemId": "SI5: Logistics_PM", "sitemTitle": "S4TWL - Batch Input for Enterprise Asset Management (EAM)", "note": 2270107, "noteTitle": "2270107 - S4TWL - Batch Input for Enterprise Asset Management (EAM)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Transaction IBIP is using Batch Input as an technology to create transactional data for all EAM Objects (Equipment, Functional Location, Notification, Order, Maintenance Plan, Task List...). This is an outdated technology.</p>\n<p>Within EAM we plan to discontinue to support this technology in a future release.</p>\n<p>Batch Input programs may also no longer work as transactions may have changed functionality or may be completely removed within some areas</p>\n<p>Currently there is no functional equivalent for enterprise asset management (EAM) within SAP S/4HANA, on-premise edition 1511 (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is not fully available yet.).</p>\n<p>Plant Maintenance offers a set of API´s which are supporting the creation / change of Plant Maintenance data like:</p>\n<ul>\n<li>Equipment</li>\n<ul>\n<li>BAPI_EQUI_CREATE</li>\n<li>BAPI_EQUI_CHANGE</li>\n<li>BAPI_EQUI_GET_DETAIL</li>\n</ul>\n<li>Functional Location</li>\n<ul>\n<li>BAPI_FUNCLOC_CREATE</li>\n<li>BAPI_FUNCLOC_CHANGE</li>\n<li>BAPI_FUNCLOC_GET_DETAIL</li>\n</ul>\n<li>Notification</li>\n<ul>\n<li>BAPI_ALM_NOTIF_CREATE</li>\n<li>BAPI_ALM_NOTIF_DATA_ADD</li>\n<li>BAPI_ALM_NOTIF_DATA_MODIFY</li>\n<li>BAPI_ALM_NOTIF_GET_DETAIL</li>\n</ul>\n<li>Maintenance Order</li>\n<ul>\n<li>BAPI_ALM_ORDER_MAINTAIN </li>\n</ul>\n</ul>\n<p>Please also review the following blogs in SCN</p>\n<p>https://blogs.sap.com/2015/11/24/eam-bapi-s-support-now-custom-fields-and-user-status/</p>\n<p>https://blogs.sap.com/2015/12/14/eam-order-notification-api-s-support-now-custom-fields-and-user-status/</p>\n<p><strong>Business Process related information</strong></p>\n<p>No influence on business processes expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Please check if you use Batch Input for creating / changing Plant Maintenance data. If Yes do not invest further in this kind of technology. Recommendation within EAM is to use the API´s wherever possible.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "If you use Batch Input for creating / changing Plant Maintenance data, recommendation within EAM is to use the API´s wherever possible."}, {"Activity": "Business Decision", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "Please check if you use Batch Input for creating / changing Plant Maintenance data. If Yes do not invest further in this kind of technology. Recommendation within EAM is to use the API´s wherever possible"}]}