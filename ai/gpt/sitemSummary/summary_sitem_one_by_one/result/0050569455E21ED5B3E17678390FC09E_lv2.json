{"guid": "0050569455E21ED5B3E17678390FC09E", "sitemId": "SI1: Logistics_AB", "sitemTitle": "S4TWL - Agency Business", "note": 2267743, "noteTitle": "2267743 - S4TWL - Agency Business ", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<ul>\n<li>Simplification of DB Key Fields and tables structures (WBRL,WBRRE, WBRF, WBRFN and WBRP)</li>\n<li>Replacement of concatenated fields by corresponding new fields in mentioned DB tables (Details mentioned notes below)</li>\n<li>Replaced fields are moved to the dynamical part of the communication structures to avoid runtime errors and to ensure the same system behaviour as before</li>\n<li>All redundant transaction and reports or business processes where replaces by one unified set of transaction, reports and business processes</li>\n<li>Different Usage of Copy Requirements in Transfer Manager </li>\n</ul>\n<p><strong>Business Process related information</strong></p>\n<p>The application simplification offers now for each business process step one single transaction instead of many. Redundant functionalities were combined into one transaction or business process. This will help to streamline each single business step and to reduce unnecessary complexity by eliminating of similar business processes steps.</p>\n<p>The Remuneration Request List is not available within SAP S/4HANA. The functional equivalent in SAP S/4HANA is the Extended Remuneration List offers the same business capability as well. This means that the customizing of the Extended Remuneration List has to be set up to ensure that the same documents are created. The replacement of the Remuneration List by the extended one can be done before or after the system conversion to SAP S/4HANA, on-premise edition 1511. Details about the system configuration kind be found in the note: 2197892</p>\n<p>The Standard Settlement Methods to generate Settlement Documents is not available win SAP S/4HANA. The functional equivalent in SAP S/4HANA is the New Settlement Method. The System will adjust all documents which were created with reference to standard settlement method and the customizing data automatically. So you have to process the documents with respect to the business process steps of the new settlement method.  Details about the system configuration kind be found in the note: 2197898</p>\n<p><strong>Transaction Codes and Reports not available in SAP S/4HANA<br/></strong>Here you will find a list of all transactions or reports which are not available within SAP S/4HANA and the corresponding successor transactions or reports (see also note 2204135).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p><strong>Obsolete Report</strong></p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p><strong>Obsolete Report Title</strong></p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p><strong>Obsolete Tcode</strong></p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p><strong>Successor Report </strong></p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p><strong>Successor Report Title</strong></p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p><strong>Successor Tcode</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1001</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Billing docs (header data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFB</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1002</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Billing documents (with item data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFC</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1003</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Settlement Document Lists (Header Information)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFD</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1004</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Settlement Document Lists (With Item Data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFE</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1005</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Settlement Requests (Header Information)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFG</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1006</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Settlement Requests (With Item Data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFH</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1007</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Settlement Request Lists (Header Data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFO</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1008</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Settlement Request List (Item Data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFP</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1009</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Customer Settlement Lists (Header Data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFQ</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1010</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Customer Settlement Lists (With Item Data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1011</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Relevant Payment Documents for Settlement Doc List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLRA</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1012</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Relevant Billing Documents for Creation of Settlem. Doc Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLRB</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1013</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Relevant Customer Settlement Lists for Settl Doc List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WBLRB</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1014</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Customer Settlement (Header Information)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000193</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1015</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Customer Settlement (With Item Data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000194</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1016</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Expenses Settlement (Header Information)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000195</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1017</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Expenses Settlement (With Item Data)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000196</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1018</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Customer Settlements for Settlement Document List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000198</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1019</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant Expenses Settlements for Settlement Document List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000334</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1050</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>List Output Single Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLI1</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1052</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>List Output List Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLI3</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1CHD</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Change Document for Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_CHANGE_DOCUMENTS</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Display Change Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_CHANGE_DOCUMENTS</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1KO1</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Conditions for Settlement Requests (Vendor)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1KO2</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Conditions for Settlement Request Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1KO3</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Conditions for Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1KO4</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Conditions for Settlement Requests (Customer)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF1KO5</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Conditions for Customer Settlement List Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2001</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Release of Settlement Documents to Accounting</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFF</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2056</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Release of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_RELEASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2002</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Cancellation of Settlement Requests</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFI</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2055</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Cancellation of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CANCEL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2003</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Cancellation of Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFJ</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2055</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Cancellation of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CANCEL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2004</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass release of billing documents to accounting</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFK</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2056</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Release of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_RELEASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2005</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Release of Remuneration Lists to Accounting</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFL</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2056</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Release of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_RELEASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2006</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Settlement Document Lists from Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFM</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2007</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Remuneration Lists from Payment Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFN</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2008</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Release of Settlement Request Lists to Accounting</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WRL4</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2056</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Release of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_RELEASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2009</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass release of posting lists to Accounting</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WBL4</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2056</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Release of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_RELEASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2010</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create posting lists from preceding documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WBLR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2052</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Generate Customer Settlement Lists from Settlement Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WBLRN</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2011</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Settlement Document Lists from Customer Settlement Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFV</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2012</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Complete Settlement Request Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFW</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2057</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Completion of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CLOSE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2013</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Release of Customer Remuneration Lists to Financial Accounting</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFLK</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2056</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Release of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_RELEASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2014</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Customer Settlements for Settlement Requests</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000192</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Generate Customer Settlements from Settlement Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF1K</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2016</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Remuneration Lists from Customer Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000197</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2018</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Release of Customer Settlements for Financial Accounting</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000212</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2056</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Release of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_RELEASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2019</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Cancellation of Settlement Request Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000307</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2055</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Cancellation of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CANCEL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2020</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Cancellation of Customer Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000308</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2055</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Cancellation of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CANCEL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2021</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Cancellation of Remuneration Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000306</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2055</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Cancellation of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CANCEL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2022</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Cancellation of Customer Settlement Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000309</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2055</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Cancellation of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CANCEL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2023</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Price Determination for Settlement Request Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000324</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2054</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Pricing for Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_PRICING</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2024</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Price Determination for Payment Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000325</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2054</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Pricing for Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_PRICING</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2025</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Remuneration Lists from Expense Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000333</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2026</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Release of Expense Settlements for Financial Accounting</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000335</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2056</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Release of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_RELEASE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2027</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Cancellation of Expense Settlement</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AL0_96000336</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2055</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Cancellation of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CANCEL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2028</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Complete Settlement Requests</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AC0_52000143</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2057</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Completion of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CLOSE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2029</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Complete Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AC0_52000169</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2057</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Completion of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CLOSE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2030</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Settlement Request Lists from Preceding Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WRLV</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2053</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Generate Vendor Settlement Lists from Settlement Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WRLVN</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2031</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Complete Expenses Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AC0_52000170</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2057</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Completion of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CLOSE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2032</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Reopen Settlement Requests</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AC0_52000172</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2058</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Reopening of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_REOPEN</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2033</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Reopen Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AC0_52000174</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2058</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Reopening of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_REOPEN</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2034</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Reopen Expenses Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AC0_52000175</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2058</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Mass Reopening of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_REOPEN</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2059</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Posting Lists from Settlement Requests</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WBLRO</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2052</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Generate Customer Settlement Lists from Settlement Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WBLRN</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2059D</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Dispatcher: Create Posting Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WBLRO_DISP</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2052D</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Dispatcher: Create Customer Settlement Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WBLRN_DISP</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2060</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Customer Settlements from Settlement Requests</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLF1KO</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Generate Customer Settlements from Settlement Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF1K</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF2061</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Settlement Lists from Settlement Requests</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WRLVO</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2053</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Generate Vendor Settlement Lists from Settlement Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WRLVN</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF4001</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Delete Archived Vendor Billing Documents from the Database</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACLFR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WLF_DEL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Vendor Billing Docs and Vendor Settlements: Delete Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF4002</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Delete Archived Customer Settlement Lists from Database</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACBLR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WBU_DEL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Customer Settlement Lists: Delete Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF4003</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Delete Archived Settlement Document Lists from Database</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACLRR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WRECH_DEL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Settlement Document Lists: Delete Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF4004</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Delete Archived Settlement Documents from Database</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACZRR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WZR_DEL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Settlement Documents: Delete Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF4005</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Delete Archived Vendor Settlement Lists from Database</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACRLR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WREG_DEL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Vendor Settlement Lists: Delete Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF4006</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Delete Archived Customer Settlements from Database</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACCIR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WCI_DEL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Customer Settlements: Delete Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF4007</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Delete Archived Expense Settlements from Database</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACSIR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WSI_DEL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Expense Settlements: Delete Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF7001</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Document Flow of Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WFL1</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF7002</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Document Flow for Customer Settlement Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WFL2</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF7003</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Document Flow for Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WFL3</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF7004</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Document Flow for Settlement Document</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WFL4</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF7005</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Document Flow for Vendor Settlement Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WFL5</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9002</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Extended Remuneration Lists From Payment Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000170</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9003</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Extended Remuneration Lists From Posting Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000171</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9004</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Settlement Documents for Application Status</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WAPZR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9005</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Vendor Settlement Lists for Application Status</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WAPRL</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9006</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Remuneration Lists from Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000172</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9007</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Remuneration Lists From Customer Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000173</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9008</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Remuneration Lists From Expense Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000174</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9009</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant Payment Documents For Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000175</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9010</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant Customer Settlements For Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000176</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9011</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant Posting Lists For Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000177</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9012</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant Expense Settlements for Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000178</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9013</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant Vendor Billing Documents, Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000179</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9014</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Simulate Generation of Remuneration Lists From Payment Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000371</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9015</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Simulate Generation of Remuneration Lists From Posting Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000372</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9016</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Simulate Generation of Remuneration Lists From Customer Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000373</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9017</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Simulation of Remuneration Lists From Expense Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000375</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9018</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Simulate Generation of Remuneration List From Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_ALN_01000376</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9019</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Extended Remuneration Lists from Remuneration Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000032</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9020</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant Remuneration Lists for Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000033</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9021</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Simulation of Remuneration Lists from Remuneration Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000034</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9022</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Extended Remuneration Lists from Preceding Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000060</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9023</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant Preceding Documents for Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000065</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9024</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Create Simulation of Remuneration Lists from Preceding Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000066</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9028</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Extended Remuneration Lists from FI Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_E37_17000019</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9029</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Simulation Remuneration Lists from FI Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_E37_17000020</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9030</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Remuneration Lists from Vendor Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_PRN_53000718</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9031</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Simulation of Remuneration Lists from Vendor Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_PRN_53000719</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9032</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant Vendor Settlements for Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_PRN_53000720</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9033</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Extended Remuneration Lists from SD Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_E4R_35000021</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9034</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Generate Simulation of Remuneration Lists from SD Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_E4R_35000022</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9042</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Simulate Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_SIM</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9037</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant SD Billing Documents for Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_E4R_35000025</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9038</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Relevant FI Documents for Extended Remuneration List Creation</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_E4R_35000024</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF_SETTLEMENT_LIST_RL</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Worklist based Creation of Settlement Document List (Dialog)</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9080</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Posting List Creation Dispatcher</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> S_AEN_10000037</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2052D</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Dispatcher: Create Customer Settlement Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WBLRN_DISP</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9081</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Customer Settlement Creation Dispatcher</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000038</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2051D</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Dispatcher: Create Customer Settlements</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF1K_DISP</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9082</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Mass Release Dispatcher</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000075</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2056D</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Dispatcher: Mass Release of Settlement Management Doc.</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_RELEASE_DISP</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9083</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Settlement Request List Creation Dispatcher</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000074</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2053D</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Dispatcher: Create Vendor Settlement Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WRLVN_DISP</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9084</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Extended Remuneration List Creation Dispatcher</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_AEN_10000083</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF9039D</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Dispatcher: Create Settlement Document Lists</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_RRLE_CREATE_DISP</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9085</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Dispatcher Message Output</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>S_E37_17000021</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14D</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Dispatcher: Message Output for Settlement Mgmt Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14D</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF9086</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Dispatcher Mass Reversal</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF2055D</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Dispatcher: Mass Cancellation of Settlement Management Docs</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WAB_CANCEL_DISP</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFFLOW</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Display Document Flow Information for Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFU</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLF1051</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>List Output Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI2</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFIDOC</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Extended IDoc Reporting</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFEIR</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFIDOC_NEW</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>IDoc Processing</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLF_IDOC</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFM30</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Automatic Document Adjustment of Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFM30</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFM99</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Automatic Document Adjustment of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFM99</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFM31</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Automatic Document Adjustment of Expenses Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFM31</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFM99</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Automatic Document Adjustment of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFM99</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFM32</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Automatic Document Adjustment of Vendor Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFM32</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFM99</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Automatic Document Adjustment of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFM99</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFM60</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Automatic Document Adjustment of Customer Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFM60</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFM99</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Automatic Document Adjustment of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFM99</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFM40</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Automatic Document Adjustment of Payment Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFM40</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFM99</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Automatic Document Adjustment of Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLFM99</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR01</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messages from Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN1</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR02</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messages from Customer Settlement Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN2</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR03</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messages from Remuneration Lists for Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN3</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR04</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messages from settlement requests</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN4</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR05</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messages from Settlement Request Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN5</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR06</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messages from Remuneration Lists for Settlement Requests</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN6</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR10</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messags from Customer Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN10</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR11</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messages from Expenses Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN11</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR12</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messages from Remuneration Lists for Customer Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN12</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLFWR13</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Messages from Remuneration Lists for Expenses Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLN13</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFWR14</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Issue Messages from Settlement Management Documents</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLN14</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLWRCINV</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Archive Customer Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACCIA</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WCI_WRI</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Customer Settlements: Write Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLWRILS</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Archive Settlement Document List</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACLA</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WRECH_WRI</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Settlement Document Lists: Write Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLWRINV</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Archive Vendor Billing Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACLFA</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WLF_WRI</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Vendor Billing Docs and Vendor Settlements: Write Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLWRPLS</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Archive Customer Settlement Lists</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACBA</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WBU_WRI</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Customer Settlement Lists: Write Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLWRSINV</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Activate Expense Settlements</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACSIA</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WSI_WRI</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Expense Settlements: Write Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLWRSLS</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Archive Settlement Request List</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACRLA</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WREG_WRI</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Vendor Settlement Lists: Write Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLWRSMR</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Archive Settlement Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WACZRA</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>WZR_WRI</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Archiving Settlement Documents: Write Program</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF3001</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Read Vendor Billing Documents from the Archive (Display Items)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLFT</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFLIST</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Document Monitor</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI5</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF3002</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Read Customer Settlement Lists from the Archive (Display Items)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLA8</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFLIST</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Document Monitor</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI5</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF3003</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Read Settlement Document Lists from the Archive (Display Items)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLA5</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFLIST</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Document Monitor</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI5</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF3004</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Read Settlement Documents from the Archive (Display Items)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLA6</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFLIST</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Document Monitor</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI5</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF3005</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Read Vendor Settlement Lists from the Archive (With Items)</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLA7</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFLIST</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Document Monitor</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI5</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF3006</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Read Customer Settlements (with Item Data) from the Archive</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLACII</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFLIST</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Document Monitor</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI5</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p>RWLF3007</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Read Expense Settlements (with Item Data) from the Archive</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p>WLASII</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p>RWLFLIST</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p>Document Monitor</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p>WLI5</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p> RWLF9060</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p> List Output of Inbound IDoc for Settlement Request List</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> WRLI</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> RWLFIDOC_NEW</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p> IDoc Monitor</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> WLF_IDOC</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"64\">\n<p> RWLF9061</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p> List Output of Inbound IDocs for Payment Documents</p>\n</td>\n<td valign=\"top\" width=\"78\">\n<p> WZRI</p>\n</td>\n<td valign=\"top\" width=\"94\">\n<p> RWLFIDOC_NEW</p>\n</td>\n<td valign=\"top\" width=\"165\">\n<p> Idoc Monitor</p>\n</td>\n<td valign=\"top\" width=\"97\">\n<p> WLF_IDOC</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Obsolete Tcode</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Successor Tcode</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>WLR1</p>\n</td>\n<td valign=\"top\">\n<p>WLFSLRL</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>WBL1</p>\n</td>\n<td valign=\"top\">\n<p>WBLRN</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>OAGZ</p>\n</td>\n<td valign=\"top\">\n<p>n/a</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong> </strong></p>\n<p><strong>Copy Requirements in Transfer Manager</strong></p>\n<p>In S4 the behavior of the following copy requirements in transfer manager differs from ECC, if source and target field are different. The reason is an inconsistency in the German and English translations</p>\n<ul>\n<li>4 Transfer Fixed Value When Target Field Is Empty</li>\n<li>5 Transfer Fixed Value When Target Field Is Not Empty</li>\n</ul>\n<p><span>The code in ECC follows the German version, which differs from the English version (It has Source Field instead of Target Field).  </span>In S4 the German text is the same as the English text and the code matches this.</p>\n<p>You can keep the current behavior by changing to these new values</p>\n<ul>\n<li>7  Transfer Fixed Value When Source Field Is Empty</li>\n<li>8  Transfer Fixed Value When Source Field Is Not Empty</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Pre-Check is performed for the component LO-AB. Custom code adoption on the new DB-Design can be checked in the start release to identify the code places which have to adjust to the new DB-Structures. Batch or online processes which needed the obsolete transactions or reports have to be adjusted so that the new transactions and reports are used instead the obsolete once. Mostly all replaced reports and transaction where part of the solution portfolio of SAP_APPL and EA-RETAIL of the Business Suite To simplify the Conversion to S/4 it is recommended to adjust the business process in the start release by a step by step replacement of the obsolete transactions by the once which will be part of S/4. You have to activate the software component EA-RETAIL to use the successor reports or transactions. You can also adopt the business processes in S/4 without to change the processes in the current release.</p>\n<p>Please adjsut copy requirements in case you have used the copy requirement 4 or 5 and if you use them for different sourc and target field. Please adjust the settings after upgrade accordingly.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"187\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"417\">\n<p>SAP Notes: 2197892, 2197898, 2204135, 2203518.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 2, "refer_note": [{"note": "2204135", "noteTitle": "2204135 - S4TC SAP_APPL - Obsolete Reports, PFCG Roles and Transactions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>List of all Reports, PFCG Roles and Transactions of application component LO-AB which are obsolete in S/4. Successors of the obsolete reports and transaction can be found in the attachement.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Check the objects in the attachement if your business processes are affected by one of the obsolete reports or transactions.</p>\n<p>Batch or online processes which requires the obsolete transactions or reports have to be adjusted so that the successor transactions or reports are used instead of the obsolete ones. Mostly all replaced reports and transaction where part of the solution portfolio of SAP_APPL and EA-RETAIL of the Business Suite. To simplify the transition to S/4 it is recommended to adjust the business process in the start release by a step by step replacement of the obsolete transactions/reports by the once which will be part in S/4. You have to activate the software component EA-RETAIL to use the successor reports or transactions in the Business Suite. You can also adopt the business processes in S/4 without to change the processes in your start release.</p>\n<p>In general you will find now for the a Business Process within Settlement Management a unified Report were you can specify for which Business Object you whant to run the report.</p>\n<p>The piecelist <em><strong>SI_LO_AB_PROG_TRAN</strong></em> contains a list of the depricated objects.</p>\n<p>The PFCG Roles SAP_EP_IS_R_WZR0 and SAP_EP_LO_WZR0 are obsolete. Please use SAP_EP_LO_WZR0N instead.</p>\n<p>There is no worklist for remuneration lists anymore. Therefore the following searchhelps are deprecated:</p>\n<ul>\n<li>WRLB</li>\n<li>WRLBA</li>\n<li>WRLBB</li>\n<li>WRLBC</li>\n</ul>\n<p> </p>", "noteVersion": 6}, {"note": "2299208", "noteTitle": "2299208 - S4TC SAP_APPL Credit Limit Check in Agency Business", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The FI-AR-CR Credit Management is not available with SAP S/4HANA, on-premise edition 1511. The functional equivalent in SAP S/4HANA is SAP Credit Management (FIN-FSCM-CR). If you use the FI-AR-CR Credit Management in Agency Business you have to migrate to SAP Credit Management. Additional Information can be found in the Simplification List of SAP Credit Management.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have only to migrate to SAP Credit Managment if you have activated in the payment type (Settlement process type) the FI-AR-CR Credit Management check.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You can use or migrate to the SAP Credit Management in the SAP ERP Release before you migrate to SAP S/4 HANA.</p>\n<p>How to use or to activate the SAP Credit Management in Agency Business / Settlement Managemnt is described in the SAP Solution Manager, in the release notes or in the applicaiton help.</p>\n<p> </p>", "noteVersion": 1, "refer_note": [{"note": "2267341", "noteTitle": "2267341 - S4TWL - Credit Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>This note is obsolete. Please refer to note 2270544.</strong></p>", "noteVersion": 1}]}, {"note": "2203518", "noteTitle": "2203518 - S4TC SAP_APPL -  DB Field Changes within Agency Business", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Your are using Agency Business and you want to upgrade to S/4. If you have own custom code you might have to adjust your code to the new DB design to avoid syntax or processing problems.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In S/4 the data model has changed so that DB Tables have new or changed KEY Fields or different fields as in ERP Release. In additon some DB Fields are moved to the dynamical area of the corresponding communication structures so that you do not find these kind of information on the DB anymore.</p>\n<p>The follwing list will give you an overview of the changed DB tables and which kind of changes were done and how to overcome issues if you have used these objetcs in your custom code.</p>\n<p> </p>\n<p><strong>Table WBRF:</strong></p>\n<p>The primary key is replaced as follows by a GUID; all fields of the former primary key, except for 'MANDT', are in the data part.</p>\n<p>Old primary key:</p>\n<ul>\n<ul>\n<li>MANDT</li>\n<li>WBELNV</li>\n<li>POSNV</li>\n<li>WBELNN</li>\n<li>POSNN</li>\n<li>LFTYPN</li>\n</ul>\n</ul>\n<p>New Primary key:</p>\n<ul>\n<ul>\n<li>MANDT</li>\n<li>RUUID</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>Table WBRFN:</strong></p>\n<p>The primary key is replaced as follows by a GUID; all fields of the former primary key, except for 'MANDT' and 'WBELNV', are in the data part.</p>\n<p>Old primary key:</p>\n<ul>\n<ul>\n<li>MANDT</li>\n<li>WBELNV</li>\n<li>POSNV</li>\n<li>WBELNN</li>\n<li>POSNN</li>\n<li>LFTYPN</li>\n</ul>\n</ul>\n<p>New Primary key:</p>\n<ul>\n<ul>\n<li>MANDT</li>\n<li>RUUID</li>\n</ul>\n</ul>\n<p>Field WBELNV: this field is split into and replaced by the following 3 fields</p>\n<ul>\n<ul>\n<li>REF_DOC_NR</li>\n<li>REF_DOC_YEAR</li>\n<li>REF_LOG_SYS</li>\n</ul>\n</ul>\n<p>The replaced field WBELNV is contained in the communication structure of table WBRFN.</p>\n<p> </p>\n<p><strong>Table WBRP:</strong></p>\n<p>Field REF_NUMBER: this field is split into and replaced by the following 3 fields:</p>\n<ul>\n<ul>\n<li>REF_DOC_NR_1</li>\n<li>REF_DOC_YEAR_1</li>\n<li>REF_LOG_SYS_1</li>\n</ul>\n</ul>\n<p>Field REF_NUMBER_2: this field is split into and replaced by the following 3 fields:</p>\n<ul>\n<ul>\n<li>REF_DOC_NR_2</li>\n<li>REF_DOC_YEAR_2</li>\n<li>REF_LOG_SYS_2</li>\n</ul>\n</ul>\n<p>The replaced fields REF_NUMBER, and REF_NUMBER_2 are contained in the communication structure of table WBRP.</p>\n<p> </p>\n<p><strong>Table WBRRE:</strong></p>\n<p>The primary key is replaced as follows by a GUID; all fields of the former primary key, except for 'MANDT' and 'OBJECT_ID', are in the data part.</p>\n<p>Old primary key:</p>\n<ul>\n<ul>\n<li>MANDT</li>\n<li>WDTYP_LI</li>\n<li>OBJECT_ID</li>\n<li>RRLPA</li>\n<li>RRLCG</li>\n</ul>\n</ul>\n<p>New Primary key:</p>\n<ul>\n<ul>\n<li>MANDT</li>\n<li>RUUID</li>\n</ul>\n</ul>\n<p>Field OBJECT_ID: this field is split into and replaced by the following 4 fields:</p>\n<ul>\n<ul>\n<li>EXT_DOC_NR</li>\n<li>EXT_DOC_YEAR</li>\n<li>EXT_DOC_BUKRS</li>\n<li>EXT_DOC_ITEM</li>\n</ul>\n</ul>\n<p>The replaced field OBJECT_ID is contained in the communication structure of table WBRRE.</p>\n<p><strong>Table WBRL:</strong></p>\n<p>Field OBJECT_ID: this field is split into and replaced by the following 4 fields:</p>\n<ul>\n<ul>\n<li>EXT_DOC_NR</li>\n<li>EXT_DOC_YEAR</li>\n<li>EXT_DOC_BUKRS</li>\n<li>EXT_DOC_ITEM</li>\n</ul>\n</ul>\n<p>The replaced field OBJECT_ID is contained in the communication structure of table WBRL.</p>\n<p><strong>View V_TMFK:</strong></p>\n<p>Fields KSCHL and KSCHLP cannot be maintained by Standard Customizing anymore because these fields were not used in SAP Standard Programs.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Table WBRF:</strong></p>\n<ul>\n<li>Use the standard function modules to read data from database respectively to write data into database. Otherwise use the class CL_WLF_TABLES_MIGRATION_UTIL which offers 2 methods to maintain the effected fields in the database structure respectively in the communication structure.</li>\n<ul>\n<li>Recommendation: do not use the database structure but the communication structure having type KOMLFF.</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>Table WBRFN:</strong></p>\n<ul>\n<li>Use the standard function modules to read data from database respectively to write data into database. Otherwise use the class CL_WB2_TABLES_MIGRATION_UTIL which offers 2 methods to maintain the effected fields in the database structure respectively in the communication structure.</li>\n<ul>\n<li>Recommendation: do not use the database structure but the communication structure having type KOMWLFFN.</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>Table WBRP:</strong></p>\n<ul>\n<li>Use the standard function modules to read data from database respectively to write data into database. Otherwise use the class CL_WB2_TABLES_MIGRATION_UTIL which offers 2 methods to maintain the effected fields in the database structure respectively in the communication structure.</li>\n<ul>\n<li>Recommendation: do not use the database structure but the communication structure having type KOMWLFP.</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>Table WBRRE:</strong></p>\n<ul>\n<li>Use the standard function modules to read data from database respectively to write data into database. Otherwise use the class CL_WB2_TABLES_MIGRATION_UTIL which offers 2 methods to maintain the effected fields in the database structure respectively in the communication structure.</li>\n<ul>\n<li>Recommendation: do not use the database structure but the communication structure having type KOMWBRRE.</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong>Table WBRL:</strong></p>\n<ul>\n<li>Use the standard function modules to read data from database respectively to write data into database. Otherwise use the class CL_WB2_TABLES_MIGRATION_UTIL which offers 2 methods to maintain the effected fields in the database structure respectively in the communication structure.</li>\n<ul>\n<li>Recommendation: do not use the database structure but the communication structure having type KOMLFL.</li>\n</ul>\n</ul>\n<p>The piecelist <em><strong>SI_LO_AB_DATA_MODEL</strong></em> contains a list of objects which were changed:</p>\n<ul>\n<li>Tables: the listed tables are the changed tables mentioned above</li>\n<li>Function Modules: at least one parameter of the listed function modules was changed; additionally, some BAPI-interfaces are listed which are obsolete because of changed business processes (details are in note 2197892)</li>\n<li>Interfaces: at least one object of the listed interfaces was changed</li>\n<li>Classes: the parameters of at least one public method of the listed classes were changed</li>\n<li>Lock Objects: the change of the primary key of a table involves a change of the lock object of this table and this change leads to a change of the interface of the generated lock-function modules. In detail, the following lock function modules are changed:</li>\n<ul>\n<li>ENQUEUE_EMWBRREE</li>\n<li>DEQUEUE_EMWBRREE</li>\n</ul>\n</ul>\n<p><strong>View V_TMFK:</strong></p>\n<p>if you have the need to maintain the fields KSCHL and KSCHLP please create you own maintaince view.</p>\n<p><strong>In General: </strong></p>\n<p>If you have created a database index on a table which contains at least one of the deleted fields of the table and which is valid for HANA database or all databases, then you have to delete this index before starting the conversion.</p>", "noteVersion": 5}, {"note": "2197898", "noteTitle": "2197898 - S4TC SAP_APPL - Checks for Settlement Method", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During transition to S/4 pre checks issues a warning message for the following check ID:  SAP_LO_AB_SETT_METH_CHECKS</p>\n<p>You are using the standard Settlement Method within Agency Business Solution. The standard Settlement Method functionaltiy is not supported in S/4 anymore.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The Standard Settlement Methods to generate Settlement Documents will no longer be supported because the New Settlement Method will offer the same business capability. The System will adjust all documents which were created with reference to standard settlement method and the customizing data automatically. So you have to process the documents with respect to the business process steps of the new settlement method.</p>\n<p>In Customizing of the Settlement Process Type, table TMZR, the settlement procedure is changed from “Blank = Direct Settlement” to “1 = Settlement using Follow-On Document”.</p>\n<p>In the header table WBRK the following fields are changed:</p>\n<p>-the settlement procedure is changed in the same way as in the settlement process type.</p>\n<p>-the fields SETTL_DOCTYPE_V (Settlement Document Type – Vendor) and SETTL_DOCTYPE_C (Settlement Document Type – Customer) are filled with the follow-on document types chosen in the settlement process type</p>\n<p>-the settlement status for vendor and customer (fields SETTL_STATUS_V and SETTL_STATUS_C) are filled depending on the posting status and posting party.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Guideline how to process the migrated documents and how to create the documents with the S/4 capabilities.</p>\n<p>After the Upgrade to S/4 all relevant documents and the customzing settings are adjusted automatically so that the system is prepared to handle the creation of Customer Settlement, Customer Settlement List and Vendor Settlement List Documents in a unified way.</p>\n<ol>\n<li>New Reports and Dispatcher for Document Creation:</li>\n<ul>\n<li>Please use for the creation of the Customer Settlement Documents the Report RWLF2051 or the corresponding Dispatcher RWLF2051D.</li>\n<li>Please use for the creation of the Customer Settlement List Documents the Report RWLF2052 or the corresponding Dispatcher RWLF2052D</li>\n<li>Please use for the creation of the Vendor Settlement List Documents the Report RWLF2053 or the corresponding Dispatcher RWLF2053D </li>\n</ul>\n<li>Create for the Reports and or Displatcher above your variants as you have done in ERP system. Please consider that the new programs offers more felxibility as before to set up Variants to bundle and to handle documents for follow on processing.</li>\n<li>Change, display and single document canncellation transactions are not affected by the usage of this unified settlement method.</li>\n<li>Please consider the note 2204135 which describes the obsolete transactions and reports in S/4. You will find in the note for mostly every obsolete transaction or report a successor transaction or successor report so that you can perform your business steps as before.</li>\n<li>Please consider, that the following field on supplier settlement list document are not filled automaticall any more. These fields can be filled via transfer manager if the supplier settlement list document header will be filled from the settlement document.</li>\n<ol>\n<li>VKORG</li>\n<li>VTWEG</li>\n<li>SPART</li>\n</ol>\n<li>\n<p>Class based accounting is switched on by default except for expense settlements and supplier billing documents where the WLFB is still active.</p>\n</li>\n</ol>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>", "noteVersion": 5}, {"note": "2197892", "noteTitle": "2197892 - <PERSON>4TC SAP_APPL - Checks for Remuneration List", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During transition to S/4 pre checks issues a warning message for the following check ID:  SAP_LO_AB_SL_CHECKS</p>\n<p>You are using the Remuneration List functionallity of the Agency Business Solution. The standard Remunseration List functionaltiy is not supported in S/4 anymore.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The Remuneration Request List will no longer be supported anymore because the Extended Remuneration List offers the same business capability as well. This means that the customizing of the Extended Remuneration List has to be set up to ensure that the same documents are created. The replacement of the Remuneration List by the extended one can be done before or after the migration. Please consider that you have to activate EA-RETAIL Software Component to run the Extended Remuneration List functionality in ERP.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Guideline how to migrate the Remuneration List process to the Extended Remuneration List process. Please consider that the Business Object in S/4 is named Settlement Document List instead Extended Remuneration List in ERP.</p>\n<p><strong><em>Documentation Migration Remuneration Lists to Settlement Document Lists</em></strong></p>\n<p>In S/4HANA the classic remuneration lists process is no longer supported. Classic means, a document will be settled in one remuneration list (new: Settlement Document List) as follow-on document, if the customizing settings are suitable .</p>\n<p>The new process is based on  a 2-step approach. When creating the document one or more entries in the index table WBRR will be generated. The Settlement Document List(s) are generated based on these index entries. This method has the following advantages: <br/><br/></p>\n<ul>\n<li>Definition of rules for the creation of Settlement Document Lists (Customizing Settlement Document List Type)</li>\n<li>Multiple Settlement Document Lists with different context can be created for one document (1:n).</li>\n</ul>\n<p>Open documents from the classical procedure, for which no remuneration list was still created, must be migrated into new Settlement Document List technology. That means, first you must generate subsequent index entries in the table WBRR for all relevant documents with the program RWLF9026. Then, Settlement Document Lists can be generated from these entries according to the new procedure.</p>\n<p><em><strong>How to:</strong></em></p>\n<p>First, you must determine how many different billing types are used for remuneration lists. Normally, this information is stored in the field WBRK-LFART_RL of the relevant original document (RWLF1051). For any billing type of a remuneration list, a Settlement Document Lists Type must be defined in Customizing.</p>\n<p><strong>The following customizing settings are necessary:</strong></p>\n<ol>\n<li>Creation Settlement Document List Type (one for each Settlement Document Type)<br/>(Path: Settlement Management -&gt; Settlement Document Lists -&gt; Settlement Document List Type)</li>\n<li>Create Settlement Document List Group (one for each partner type)<br/>(Path: Settlement Management -&gt; Settlement Document Lists -&gt; Settlement Document List Groups -&gt; Define Settlement Document List Groups)</li>\n<ol>\n<li>Assign all Settlement Document List Types to the Settlement Document List Group</li>\n<li>Assign all relevant Settlement Document Types to Settlement Document List Group<br/>(Path: Settlement Management -&gt; Settlement Document Lists -&gt; Settlement Document List Groups -&gt; Assign Settlement Doc. Types to Settlement Doc. List Group)</li>\n<ol>\n<li>Settlement Document Types (of Source Document) </li>\n<li>Partner Type (Supplier / Customer )</li>\n<li>Settlement Document List Group </li>\n</ol></ol></ol>\n<p><strong>Example:</strong></p>\n<p>Documents with 3 different Settlement Document Types (Billing Types) are relevant for creation of 2 Settlement Document Lists (Remuneration Lists) (one for Customer side FC01, one for Supplier side FV01)<br/><br/></p>\n<ul>\n<li>B001 (Customer: Invoice)                    FC01 Customer List</li>\n<li>B005 (Customer: Credit Memo)            FC01 Customer List</li>\n<li>L001 (Supplier: Settlement Doc.)         FV01 Supplier List</li>\n</ul>\n<p><strong>The customizing settings looks like this:</strong></p>\n<ol>\n<li>Creation Settlement Document List Type</li>\n</ol>\n<ul>\n<li>CT01 Customer List</li>\n<ul>\n<li>Control Data</li>\n<ul>\n<li>Settl. Doc. Type for Settlement Doc List   FC01</li>\n<li>Settl. Doc Type for Reversal SttDoc List   FS01</li>\n<li>Category of Settlement Doc. List Type     Standard</li>\n<li>Creation of Settlement Document List      Always</li>\n<li>Data Determination for Cancellation        Transfer Data without changes</li>\n<li>Sett Doc List Relevant for Cancellationsv  If preceding document completed</li>\n<li>End Relevance for Stt. Doc List Creation   Standard</li>\n<li>Relevant Docs for Settlement Doc Lists     Completed Documents</li>\n</ul>\n<li>Rules</li>\n<ul>\n<li>Settlement Document List Recipiant</li>\n<ul>\n<li>Method of Determination of Recipiant        Partner Function</li>\n</ul>\n<li>Settlement Document List Data</li>\n<ul>\n<li>Default for Settlement Doc List Date         06 Posting Date</li>\n</ul>\n<li>Settlement Document List Conditions</li>\n<ul>\n<li>Determination of Stt Doc List Conditions    1  all List Conditions</li>\n</ul>\n<li>Currency</li>\n<li>Term of Payment</li>\n<ul>\n<li>Determination Method of Payment                     Standard</li>\n</ul>\n<li>Pricing</li>\n<ul>\n<li>Price Determination                                          Standard (Pricing Type E)</li>\n</ul>\n<li>Cleatring Worbench</li>\n</ul>\n</ul>\n</ul>\n<ol start=\"2\">\n<li>VT01 Supplier List</li>\n<ul>\n<li>Control Data</li>\n<ul>\n<li>Settl. Doc. Type for Settlement Doc List          FV01</li>\n<li>Settl. Doc Type for Reversal SttDoc List          FS02</li>\n<li>Category of Settlement Doc. List Type            Standard</li>\n<li>Creation of Settlement Document List             Always</li>\n<li>Data Determination for Cancellation               Transfer Data without changes</li>\n<li>Sett Doc List Relevant for Cancellations          If preceding document completed</li>\n<li>End Relevance for Stt. Doc List Creation         Standard</li>\n<li>Relevant Docs for Settlement Doc Lists          Completed Document</li>\n</ul>\n</ul>\n</ol>\n<ul>\n<ul>\n<li>Rules</li>\n<ul>\n<li>Settlement Document List Recipiant</li>\n<ul>\n<li>Method of Determination of Recipiant                    Partner Function</li>\n</ul>\n<li>Settlement Document List Data</li>\n<ul>\n<li>Default for Settlement Doc List Date                     06 Posting Date</li>\n</ul>\n<li>Settlement Document List Conditions</li>\n<ul>\n<li>Determination of Stt Doc List Conditions                1  all List Conditions</li>\n</ul>\n<li>Currency</li>\n<li>Term of Payment</li>\n<ul>\n<li>Determination Method Terms of Payment               Standard</li>\n</ul>\n<li>Pricing</li>\n<ul>\n<li>Price Determination                                              Standard (Pricing Type E)</li>\n</ul>\n<li>Clearing Workbench<br/><br/></li>\n</ul>\n</ul>\n</ul>\n<ol start=\"3\">\n<li>Create Settlement Document List Group</li>\n</ol>\n<ul>\n<li>CG01 Customer Settlement</li>\n<ul>\n<li>CT01 (Settlement Document List Type: 1.1)</li>\n</ul>\n<li>VG01 Supplier List</li>\n<ul>\n<li>VT01 (Settlement Document List Type: 1.2)</li>\n</ul>\n</ul>\n<ol start=\"4\">\n<li>Assign all relevant Settlement Document Types to Settlement Document List Group</li>\n</ol>\n<ul>\n<li>B001 (Customer: Invoice)                    1 (Customer)     CG01</li>\n<li>B005 (Customer: Credit Memo)            1 (Customer)     CG01</li>\n<li>L001 (Supplier: Settlement Doc.)            (Supplier)       VG01    <br/><br/></li>\n</ul>\n<p><strong>Processing:</strong><br/><br/></p>\n<ol>\n<li>Report RWLF9026 (TA SA38)<br/>This report creates entries in the index table WBRR of relevant documents. Relevant are:</li>\n<ul>\n<li>all documents with Settlement Document Type B001, B005, L001 (these document types are relevant because of customizing settings)</li>\n<li>not settled (WBRK-WBELN_RL = SPACE)<br/><br/></li>\n</ul>\n<li>Report RWLF9039 (TA WLF_RRLE_CREATE)<br/>This report creates Settlement Document Lists from index table WBRR. The program must be started separately for each Settlement Document List Type (e.g. C001 and V001).</li>\n</ol>\n<p> </p>\n<p> </p>", "noteVersion": 6}], "activities": [{"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "The Remuneration Request List is not available within SAP S/4HANA. The functional equivalent in SAP S/4HANA is the Extended Remuneration List offers the same business capability as well. This means that the customizing of the Extended Remuneration List has to be set up to ensure that the same documents are created. Details about the system configuration kind be found in the note: 2197892"}, {"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "The Standard Settlement Methods to generate Settlement Documents is not available win SAP S/4HANA. The functional equivalent in SAP S/4HANA is the New Settlement Method. The System will adjust all documents which were created with reference to standard settlement method and the customizing data automatically. So you have to process the documents with respect to the business process steps of the new settlement method. Details about the system configuration kind be found in the note: 2197898"}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "The Standard Settlement Methods to generate Settlement Documents is not available win SAP S/4HANA. The functional equivalent in SAP S/4HANA is the New Settlement Method. The System will adjust all documents which were created with reference to standard settlement method and the customizing data automatically. So you have to process the documents with respect to the business process steps of the new settlement method. Details about the system configuration kind be found in the note: 2197898"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Custom code adoption on the new DB-Design can be checked in the start release to identify the code places which have to adjust to the new DB-Structures."}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Batch or online processes which needed the obsolete transactions or reports have to be adjusted so that the new transactions and reports are used instead the obsolete once."}, {"Activity": "Process Design / Blueprint", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Mostly all replaced reports and transaction where part of the solution portfolio of SAP_APPL and EA-RETAIL of the Business Suite To simplify the Conversion to S/4 it is recommended to adjust the business process in the start release by a step by step replacement of the obsolete transactions by the once which will be part of S/4. You have to activate the software component EA-RETAIL to use the successor reports or transactions."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "User need to be trained for all the changes "}]}