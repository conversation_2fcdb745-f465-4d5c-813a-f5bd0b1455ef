{"guid": "0090FA844D321EDBADE536BFE12860C8", "sitemId": "SI67: Logistics_General", "sitemTitle": "S4TWL - BAdI for Article Reference Handling", "note": 3043856, "noteTitle": "3043856 - S4TWL - BAdI for Article Reference Handling", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion or an upgrade to SAP S/4HANA 2021, and you are using some Retail related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">BADI_ARTICLE_REF_RT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p><q class=\"SAPXDPScreenElement\" title=\"Object name\">With release SAP S/4HANA 2021 the technical type of BADI_ARTICLE_REF_RT  has changed from a \"classic BAdI\" to kernel BAdI.</q></p>\n<p><strong>Business Process related information</strong></p>\n<p>This change of the BAdI type does not have any business impact.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Which steps need to be described <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/46a2cfc13d25463b8b9a3d2a3c3ba0d9/27703b42ea85b26be10000000a155106.html?version=202210.000\" target=\"_blank\">here</a>.<br/>In case this link does not work, go to the home page <a href=\"http://help.sap.com\" target=\"_blank\">http://help.sap.com</a> and enter search term \"Migrating BAdIs\".<strong><br/></strong></p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if BADI_ARTICLE_REF_RT is used.<br/>This is the case if there is a relevant entry in view V_EXT_ACT. You can test this via transaction SE16 for <br/>EXIT_NAME = BADI_ARTICLE_REF_RT  AND IMP_NAME starts with  Z* <br/>or <br/>EXIT_NAME = BADI_ARTICLE_REF_RT and IMP_NAME starts with  Y*.</p>", "noteVersion": 2, "refer_note": [], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}