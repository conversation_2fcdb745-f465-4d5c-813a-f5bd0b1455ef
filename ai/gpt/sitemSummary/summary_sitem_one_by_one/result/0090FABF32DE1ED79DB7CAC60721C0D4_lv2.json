{"guid": "0090FABF32DE1ED79DB7CAC60721C0D4", "sitemId": "SI26: MasterData_BP", "sitemTitle": "S4TWL - API RFC_CVI_EI_INBOUND_MAIN is obsolete", "note": 2506041, "noteTitle": "2506041 - S4TWL - API RFC_CVI_EI_INBOUND_MAIN is not supported from the release S/4 HANA OP 1709 FPS2 and in Cloud Edition 1805 onwards", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Customer is migrating to SAP S/4HANA on-premise edition, and they are using function module RFC_CVI_EI_INBOUND_MAIN to migrate business partners.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Business Partner, RFC CVI_EI_INBOUND_MAIN, Customer Master, Supplier Master</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>RFC_CVI_EI_INBOUND_MAIN in SAP S/4HANA, was initially used to migrate business partners.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>RFC_CVI_EI_INBOUND_MAIN is used to update/create BP in S/4 HANA. However, it is not the recommended approach as it doesn't cover all the features. RFC_CVI_EI_INBOUND_MAIN is not an officially released SAP Function module and was created for internal use only. Hence, it has been decided to stop supporting this function module from the OP release 1709 FPS2  and in Cloud Edition 1805 onwards.</p>\n<p>Customers should refer to the SAP Note <a href=\"/notes/2417298\" target=\"_blank\">2417298</a> for maintenance of Business Partners.</p>\n<p><strong>Business Process related information</strong></p>\n<p>No impact on business processes is expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Check if you use <em>RFC CVI_EI_INBOUND_MAIN </em>for creating/changing customer master or supplier master data. If yes, please switch to the recommended alternatives in the SAP Note <a href=\"/notes/2417298\" target=\"_blank\">2417298</a>.</p>", "noteVersion": 5, "refer_note": [], "activities": [{"Activity": "Data migration", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "This function module was designed for internal use only. Starting release 1709 FPS2  and in Cloud Edition 1805 onwards please switch to the recommended alternatives in the SAP Note 2417298."}]}