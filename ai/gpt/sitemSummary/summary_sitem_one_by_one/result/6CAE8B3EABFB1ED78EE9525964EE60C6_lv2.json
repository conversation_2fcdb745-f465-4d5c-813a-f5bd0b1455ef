{"guid": "6CAE8B3EABFB1ED78EE9525964EE60C6", "sitemId": "SI02: CPM_C4C_INTERFACE", "sitemTitle": "S4TWL - Interface of SAP CPM with SAP Cloud for Customer", "note": 2318733, "noteTitle": "2318733 - S4TWL – Interface of SAP Commercial Project Management with SAP Cloud for Customer", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP Commercial Project Management for SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>C4C, SAP Commercial Project Management, SAP Cloud for Customer, CA-CPD, S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>There is no interface of <em>SAP Commercial Project Management</em> with <em>SAP Cloud for Customer</em> in <em>SAP Commercial Project Management for SAP S/4HANA</em>.</p>\n<p><strong>Required and Recommended Actions</strong></p>\n<p>Inform key users and end users.</p>", "noteVersion": 2, "refer_note": [], "activities": [{"Activity": "Interface Adaption", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "There is no interface of SAP Commercial Project Management with SAP Cloud for Customer in SAP Commercial Project Management for SAP S/4HANA. Please, inform key users and end users. See also SAP Note 2318733."}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Interface Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Remove interface of SAP Commercial Project Management with SAP Cloud for Customer in SAP Commercial Project Management for SAP S/4HANA"}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "Inform users about changed interfaces."}]}