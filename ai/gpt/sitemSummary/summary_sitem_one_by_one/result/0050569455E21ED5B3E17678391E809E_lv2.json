{"guid": "0050569455E21ED5B3E17678391E809E", "sitemId": "SI7: Logistics_PM", "sitemTitle": "S4TWL - WebDynpro Application for Maintenance Planner (<= EhP4)", "note": 2270110, "noteTitle": "2270110 - S4TWL - WebDynpro Application for Maintenance Planner (<= EhP4)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition.  You are using PM Functionality Maintenance Planer, Technician or Supervisor delivered (&lt;= EhP4). The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">Maintenance Planer, Technician, Supervisor, S4HANA, Business Package for Maintenance Worker 1.61,</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Old WD Application covering Maintenance Tenchnician, Planner, and Supervisor delivered in Business Suite in Release &lt;= EhP4  is not the target architecture (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.).</p>\n<p>Maintenance Technician role is obsolete for S4 HANA release.</p>\n<p>Use the new Maintenance Planner and Maintenance Worker Roles available in S/4 HANA (Business Package for Maintenance Worker 1.61).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No influence on business processes expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Activate the below Business Functions to get the full functionality of Maintenance Planner and Maintenance Worker Role.</p>\n<ul>\n<li>LOG_EAM_SIMPLICITY_2 </li>\n<li>LOG_EAM_SIMPLICITY_3</li>\n<li>LOG_EAM_SIMPLICITY_4 </li>\n<li>LOG_EAM_SIMPLICITY_5 </li>\n<li>LOG_EAM_SIMPLICITY_6</li>\n</ul>\n<div></div>", "noteVersion": 4, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Optional", "Additional_Information": "WebDynpro application for Maintenance Worker, Planner, and Supervisor delivered in Business Suite in SAP ERP 6.0 EhP <= 4 is not considered target architecture. Maintenance Planner and Maintenance Worker Role is the equivalent"}, {"Activity": "Process Design / Blueprint", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": "You can use the new Maintenance Planner and Maintenance Worker Role which is available in SAP S/4 HANA"}, {"Activity": "Customizing / Configuration", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": "Activate the Business Function to get full functionality of Maintenance Planner and Maintenance Worker Role as per Note 2270110"}]}