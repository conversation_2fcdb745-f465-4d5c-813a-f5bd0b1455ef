{"guid": "0050569455E21ED5B3E176783917209E", "sitemId": "SI19: Logistics_PP", "sitemTitle": "S4TWL - ANSI/ISA S95 Interface", "note": 2268117, "noteTitle": "2268117 - S4TWL - ANSI/ISA S95 Interface", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>﻿Description</strong></p>\n<p>Usage of the ANSI /ISA S95 interface is not possible in S/4HANA. Use MES Integration or the POI Interface instead.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Usage of the ANSI/ISA S95 interface is not possible anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Use the POI interface or the MES interface to connect SAP S/4HANA to an MES system or a planning system. For details, see the MES integration documentation (<a href=\"http://help.sap.com/erp2005_ehp_07/helpdata/en/f1/c7efaf63e44a35b0f1e67556d047b6/frameset.htm\" target=\"_blank\">link</a>) and the POI documentation (<a href=\"http://help.sap.com/erp2005_ehp_07/helpdata/en/7e/1ebf53d25ab64ce10000000a174cb4/content.htm?frameset=/en/38/20bf53d25ab64ce10000000a174cb4/frameset.htm&amp;current_toc=/en/38/20bf53d25ab64ce10000000a174cb4/plain.htm&amp;node_id=6&amp;show_children=false\" target=\"_blank\">link</a>).</p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "Interface Adaption", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "The ANSI/ISA-S95 Interface is not avalable with S/4HANA. Please, use the POI Interface and the MES-Integration. Please, see SAP Note 2268117 for detailed information."}, {"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}]}