{"guid": "00109B1318B61ED98FE4EB3F046500F6", "sitemId": "SI15: CT_SUPPORT_DESK", "sitemTitle": "S4TWL - SAP Support Desk ($$-Messages)", "note": 2852082, "noteTitle": "2852082 - S4TWL – existence check for basis notification and $$ notification", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use $$ notifications and basic notifications that were created in table DNOD_NOTIF. The function module DNO_OW_EXTERN_SEND_NOTIF_2_BC, which was used to create basic notifications, is no longer supported in SAP S/4HANA.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$$, $$ notifications, support notifications, enter support notifications, DNOD_NOTIF, function module DNO_OW_EXTERN_SEND_NOTIF_2_BC, DNO_OW_EXTERN_SEND_NOTIF_2_BC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Renewal</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business value</strong></p>\n<p>Instead of the $$ notifications and basis notifications, SAP Support is available via SAP Support Portal at https://support.sap.com.</p>\n<p><strong>Required and recommended action(s)</strong></p>\n<p>Check whether an entry has been defined in the table DNOD_NOTIF.</p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Business Operations", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Use SAP Support Portal for raising incidents to SAP"}]}