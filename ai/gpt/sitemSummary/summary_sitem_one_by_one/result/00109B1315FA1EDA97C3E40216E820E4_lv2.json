{"guid": "00109B1315FA1EDA97C3E40216E820E4", "sitemId": "SI01: CS", "sitemTitle": "S4TWL - Customer Service", "note": 2962632, "noteTitle": "2962632 - S4TWL - Customer Service", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div>You are currently using Customer Service (CS) as compatibility scope in SAP S/4HANA on-premise edition. More information on compatibility scope in S/4HANA can be found in the following note 2269324. The following SAP S/4HANA Transition Worklist item is applicable in this case. </div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<div>Customer Service (CS) provides Service Management functionality for planning, executing and billing service orders. CS is used for field service, in-house repair as well as service projects. It works in conjunction with Plant Maintenance (PM) functionality and provides integration to Resource Related Billing as well as Service Contracts.</div>\n<div>Customer Service (CS) will be replaced by S/4HANA Service. S/4HANA  Service will be the new state-of-the-art Service Management backend as part of SAP S/4HANA.</div>\n<div> </div>\n<div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<div>Customer Service (CS) in S/4HANA is intended as a bridge or interim solution, which allows you a stepwise system conversion from SAP ERP to SAP S/4HANA on-premise edition and SAP S/4HANA Service.</div>\n<div></div>\n<p><strong>Custom Code related information</strong></p>\n<div>\n<p>S/4HANA Service is an all-new and own Service Management solution that comes with a new solution scope, processes, objects, and capabilities. At the same time, it also reuses selected functionalities of ERP PM in order to simplify a transformation from ERP CS.</p>\n<p>As a rough guideline, master data focused objects (e.g. equipment, functional location, maintenance plans) are mainly reused from ERP PM. Service transactional data (e.g. service order and service contract) for the commercial and operational service execution in S/4HANA Service are based on new objects.</p>\n<p>For customers with a high need of CS service order capabilities for service planning and execution (e.g. task lists), S/4HANA Service provides the capability “Service with Advanced Execution” since the release SAP S/4HANA 2023. This process uses maintenance orders for service planning and execution and the Dynamic Item Processor (DIP) for resource-related quotation and billing. Please consider the notes linked below to understand limitations of S/4HANA Service in the different SAP S/4HANA releases.</p>\n</div>\n</div>", "noteVersion": 3, "refer_note": [{"note": "3330419", "noteTitle": "3330419 - This SAP Note informs you about release information and restrictions related to SAP S/4HANA Service", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note informs you about restrictions related to SAP S/4HANA Service</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release restrictions, SAP S/4HANA 2023 FPS0, SAP S/4HANA 2023 FPS1, Service</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note provides information about restrictions that exist for the SAP S/4HANA 2023 release.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Restrictions for SAP S/4HANA 2023 FPS0</strong></p>\n<p><strong>S/4HANA Accounting Integration</strong></p>\n<ul>\n<li>Item-based accounting can be activated in customizing <em>Service -&gt; Transactions -&gt; Basic Settings -&gt; Settings for Service Transactions -&gt; Integration -&gt; Enable Item-Based Accounting for Service Management</em></li>\n<li>Controlling with account assignment manager (internal order, WBS element) cannot be used in parallel with Item-based Accounting. When Item-based Accounting is activated, controlling with account assignment manager (internal order, WBS element) can no longer be used.</li>\n<li>Reversing the activation of Item-based Accounting may incur financial inconsistencies and is not recommended.</li>\n<li>Results Analysis is not supported.</li>\n<li>Integration with Central Finance is not supported.</li>\n<li>Costing-based CO-PA is not supported.</li>\n<li>Predictive Accounting Integration for Service Orders and Service Contracts is not supported.</li>\n<li>Revenue Recognition for Inhouse Repair based on outbound delivery is not supported.</li>\n<li>Settlement of service oders and contracts is available, but automatic settlement rule generation is not supported. Hierarchical settlement of an execution order item is not supported.</li>\n</ul>\n<p><strong>S/4HANA Service Order Management</strong></p>\n<ul>\n<li>If you have activated Contract Accounts Receivable and Payable (FI-CA), the integration of Credit Management with service transactions is not supported.</li>\n<li>Billing Plan on the service order/repair order header is not supported. Thus, IMG activity <em>Assign Billing Plan Type to Business Transaction Type</em> is not supported for business transaction types that are assigned to business object type BUS2000116.</li>\n<li>Field Service Management (FSM) integration and the advanced execution order process can be used together in the service order. But in certain cases, the execution status on header level does not reflect the sum of all execution statuses on the item level correctly. These cases occur if the execution status for service items relevant for FSM integration has not yet been set, so is still in execution status 'Not Available'.</li>\n<li>Down-payments are not supported in Service Order.</li>\n<li>When Item-based Accounting is activated:</li>\n<ul>\n<li>The Advance Shipment process does not support the direct procurement integration (Purchase Requistion/Purchase Order) via service order or sales order. Advance Shipment from stock is supported.</li>\n<li>Time recording in Service Request is not supported.</li>\n<li>Service confirmations can only be created as a follow-up from service orders and not from the Create Service Confirmation app (Fiori app ID TBT117MCR).</li>\n<li>When a Sales Order is triggered from a Service Order Sales Item, there are no costs and revenue visible on the service order item. The sales order delivery and billing is account assigned to a market segment, in which service order is not included.</li>\n</ul>\n<li>Intercompany service order process:</li>\n<ul>\n<li>Event-Based Revenue Recognition (EBRR) is not supported for the intercompany item (in the customer-facing service order).</li>\n<li>Attachments are only copied to the intercompay service order if the service order was already saved after the attachment has been uploaded and before  the intercompany item is released.</li>\n<li>The process with an intercompany service order and a fixed price service item is only supported when Sales Billing is used.</li>\n<li>The intercompany service order process is not supported with Contract Accounts Receivable and Payable (FI-CA) and Convergent Invoicing (CI) to FI-CA.</li>\n</ul>\n</ul>\n<p><strong>Service with Advanced Execution</strong></p>\n<p>While the restrictions from the other sections of this Service note are also applicable here, the following are specific to Service with Advanced Execution.</p>\n<ul>\n<li>Service with Advanced Execution is only supported with Item-based Accounting.</li>\n<li>In Resource-related quote (quote items with a connected maintenance order), contract determination / assignment for the subitems is not supported.</li>\n<li>Intercompany checks and other validations are not supported in maintenance plan.</li>\n<li>ACDOCP is not supported as a source in the DIP profile for usage 11 – Quotation creation and Sales Pricing in service order quotation. To enable usage of “1008 – Planned Costs from ACDOCP” as source, see SAP Note <a href=\"https://me.sap.com/notes/3390894/E\" target=\"_blank\">3390894 - Sub Items in Service Quotation incorrectly updated - SAP for Me</a>.<strong><br/></strong></li>\n<li>Credit Check:</li>\n<ul>\n<li>Credit check is not supported for Execution Order items in service order quotation.</li>\n<li>Credit check is supported for Billing on Completion, but not for Summarized and Itemized billing.</li>\n</ul>\n<li>Deletion of the debit memo and credit memo lines does not impact in the transfer to billing status field value in the billing assignment block of the item. It is recommended to reject such billing request lines and not delete them.</li>\n<li>The billed value in the item level billing assignment block of execution order item will only be displayed when billing relevance is Billing on Completion.</li>\n<li>No APIs are available for Service with Advanced Execution. Mass processing or external integration is currently not possible.</li>\n<li>When using Enterprise Organizational Model, no automatic determination of Planning Plant and Work Center is supported. </li>\n<li>No integration or data exchange between the service request and the plant maintenance notification. </li>\n<li>No integration or data exchange between the service order and the billable maintenance order in warranty determination.</li>\n<li>Archiving of service with advanced execution orders is not supported with existing archiving object of service orders. Archiving of billable orders is also not supported. Any attempt to archive service with advanced execution orders or billable orders could cause inconsistencies in the system.</li>\n<li>Resource-Related Billing of Service with Advanced Execution processing:</li>\n<ul>\n<li>The ability to process multiple service order items and mass processing of service orders for billing via Debit Memo Request (DMR) is not supported.</li>\n<li>Direct cost posting to execution order item is not supported.</li>\n<li>Contract Accounts Receivable and Payable (FI-CA) and SEPA are not supported for resource-related billing.</li>\n<li>The following field values are not transferred from the service order to the debit / credit memo request or to the debit / credit memo.</li>\n<ul>\n<li>Contract account</li>\n<li>SEPA mandate</li>\n<li>Terms of payment</li>\n<li>Payment card</li>\n<li>Alternate tax classification</li>\n</ul>\n</ul>\n</ul>\n<p><strong>Service with Advanced Execution - Service Contract Management</strong></p>\n<ul>\n<li>Value and quantity-based service contract items are not supported.</li>\n<li>No delivery Customizing for service contract integration with service execution order item. One can refer to scope item 426. Item categories SCN1, SCN2, SCN3 integration with service execution order item. This is delivered with transaction type SC1 as inactive.</li>\n<li>There is no migration for service contracts from Sales and Distribution to S/4HANA service contract with item-based accounting.</li>\n</ul>\n<p><strong>Service with Advanced Execution - <strong>Maintenance Management</strong></strong></p>\n<ul>\n<li>Billable maintenance order can't be created through an external API. </li>\n<li>Plant maintenance operation level controlling is not supported. </li>\n<li>The usage of EAM Fiori Apps for the connected billable maintenance order from service with advanced execution is not supported. The creation and change of billable maintenance orders along with the synchronization of the service relevant attributes - Billable indicator, Sales Area, and Service Product are supported only via SAPGUI. For more information, see note <a href=\"https://me.sap.com/notes/3363107\" target=\"_blank\">﻿3363107</a>.</li>\n<li>Sub-orders are not supported in Service with Advanced Execution.</li>\n<li>None of the mass change transactions are currently supported for billable orders like IW37 or IW37n.</li>\n<li>Only SAP GUI apps in the area of plant maintenance / maintenance management are supported.</li>\n<li>The use of an equipment with a WBS element reference in a maintenance order will lead to inconsistencies and hence is not recommended</li>\n<li>The advance shipment document generated from the billable maintenance order currently displays the maintenance order number in the document flow. The service order is not displayed advance shipment document, but can be viewed in the document flow connected to the maintenance order. </li>\n<li>Assignment of contract to the advance shipment order generated out of billable maintenance orders is not supported.</li>\n<li>Warranty processing of billable maintenance order and warranty claims integration is not supported.</li>\n<li>Billable maintenance orders are not supported in SAP S/4HANA Asset Management for resource scheduling. For more information, see note <a href=\"https://me.sap.com/notes/3398291/E\" target=\"_blank\">3398291 - Filter out Billable Orders - SAP for Me</a>.</li>\n<li>New fields for service processing (e.g., service product, service order etc.) are not supported in report RIAUFK20 via SE38 or using transaction variants. These fields are only supported if you run IW38 or IW39 directly.</li>\n<li>The result list of BAPI list function module (e.g., BAPI_ALM_ORDERHEAD_GET_LIST) does not supporting the service related fields for billable maintenance orders. The result list has no information specific for billable maintenance orders.</li>\n<li>In case of list transactions, it is not possible to search with service fields (EQ initial) to filter out only non-billable maintenance orders.</li>\n<li>Status changes in the billable maintenance order are supported only in the transaction IW32. It is not supported in other channels such as IW41, IW42, BAPIs, list transactions, mass change and so on.</li>\n<li>Configurable service products can't be entered in transaction IW32 when creating a billable maintenance order.</li>\n</ul>\n<p><strong>Service with Advanced Execution - <strong>Finance</strong></strong></p>\n<ul>\n<li>Intercompany plant maintenance master data set up is supported when sales area company code and planning plant company code are the same. If there are multiple maintenance orders corresponding to a single execution order item, all maintenance orders have to be in the same company code.</li>\n<li>Planned revenue calculation is not supported in resource-related billing.</li>\n<li>Execution order items with Summarized billing are not supported in EBRR.</li>\n<li>Completed contract and percentage of completion method (POC) are not recommended for resource-related billing.</li>\n<li>In resource-related billing, dynamic line items that are not relevant for billing should be rejected to avoid EBRR balance during period end activities.</li>\n<li>The EBRR balance sheet values will be cleared (including reserves for missing cost) when billable maintenance order status is set to Technically Completed (TECO) and billing is completed along with its release to accounting for the service order item. Therefore, for Fixed Price EBRR all actual cost should be posted to billable maintenance order before setting the order status to TECO.</li>\n<li>Settlement of billed costs in billable maintenance order should be avoided.</li>\n<li>Profit Center reorganization is not supported for Service Orders with Advanced Execution.</li>\n</ul>\n<p><strong>S/4HANA Service Contract Management</strong></p>\n<ul>\n<li><span>When Item-based Accounting is activated, then quantity and value-based service contracts are not supported. </span></li>\n<li><span>When Contract Accounts Receivable and Payable (FI-CA) is active, the integration of Credit Management with service transactions is not supported.</span></li>\n<li><span>Contracts with item-based accounting are not integrated with Revenue Accounting and Reporting (RAR). Event-Based Revenue Recognition should be used instead (EBRR, see </span><a class=\"fui-Link ___1idfs5o f3rmtva f1ewtqcl fyind8e f1k6fduh f1w7gpdv fk6fouc fjoy568 figsok6 f1hu3pq6 f11qmguv f19f4twv f1tyq0we f1g0x7ka fhxju0i f1qch9an f1cnd47f fqv5qza f1vmzxwi f1o700av f13mvf36 f1cmlufx f9n3di6 f1ids18y f1tx3yz7 f1deo86v f1eh06m1 f1iescvh ftqa4ok f2hkw1w fhgqx19 f1olyrje f1p93eir f1h8hb77 f1x7u7e9 f10aw75t fsle3fq f17ae5zn\" href=\"https://me.sap.com/notes/3357625\" rel=\"noreferrer noopener\" target=\"_blank\" title=\"https://me.sap.com/notes/3357625\">https://me.sap.com/notes/3357625</a><span> ).</span></li>\n<li>There is no migration for S/4HANA service contracts based on internal order to S/4HANA service contract with item-based accounting.</li>\n</ul>\n<p><strong>Cross application functions</strong></p>\n<ul>\n<li>Concurrent employment is not supported in S/4HANA Service process (which includes Field Service process, In-House Repair process, Recurring Service). Service Process is working with Business Partner with Employee Role.</li>\n</ul>\n<p><strong>Restrictions for SAP S/4HANA 2023 FPS1</strong></p>\n<p><strong>S/4HANA Accounting Integration</strong></p>\n<ul>\n<li>Item-based accounting can be activated in customizing <em>Service -&gt; Transactions -&gt; Basic Settings -&gt; Settings for Service Transactions -&gt; Integration -&gt; Enable Item-Based Accounting for Service Management</em></li>\n<li>Controlling with account assignment manager (internal order, WBS element) cannot be used in parallel with Item-based Accounting. When Item-based Accounting is activated, controlling with account assignment manager (internal order, WBS element) can no longer be used.</li>\n<li>Reversing the activation of Item-based Accounting may incur financial inconsistencies and is not recommended.</li>\n<li>Results Analysis is not supported.</li>\n<li>Integration with Central Finance is not supported.</li>\n<li>Costing-based CO-PA is not supported.</li>\n<li>Predictive Accounting Integration for Service Orders and Service Contracts is not supported.</li>\n<li>Revenue Recognition for Inhouse Repair based on outbound delivery is not supported.</li>\n<li>Settlement of service oders and contracts is available, but automatic settlement rule generation is not supported. Hierarchical settlement of an execution order item is not supported.</li>\n</ul>\n<p><strong>S/4HANA Service Order Management</strong></p>\n<ul>\n<li>If you have activated Contract Accounts Receivable and Payable (FI-CA), the integration of Credit Management with service transactions is not supported.</li>\n<li>You can't use the Billing Plan on the service order/repair order header. Thus, IMG activity <em>Assign Billing Plan Type to Business Transaction Type</em> is not supported for business transaction types that are assigned to business object type BUS2000116.</li>\n<li>Field Service Management (FSM) integration and the advanced execution order process can be used together in the service order. But in certain cases, the execution status on header level does not reflect the sum of all execution statuses on the item level correctly. These cases occur if the execution status for service items relevant for FSM integration has not yet been set, so is still in execution status 'Not Available'.</li>\n<li>Down-payments are not supported in Service Order.</li>\n<li>When item-based accounting is activated:</li>\n<ul>\n<li>Time recording in Service Request is not supported.</li>\n<li>Service confirmations can only be created as a follow-up from service orders and not from the Create Service Confirmation app (Fiori app ID TBT117MCR) since this app is not supported with item-based accounting.</li>\n<li>When a Sales Order is triggered from a Service Order Sales Item, there are no costs and revenue visible on the service order item. The sales order delivery and billing is account assigned to a market segment, in which service order is not included.</li>\n</ul>\n<li>Intercompany service order process:</li>\n<ul>\n<li>Event-Based Revenue Recognition (EBRR) is not supported for the intercompany item (in the customer-facing service order).</li>\n<li>Attachments are only copied to the intercompay service order if the service order was already saved after the attachment has been uploaded and before  the intercompany item is released.</li>\n<li>The process with an intercompany service order and a fixed price service item is only supported when Sales Billing is used.</li>\n<li>The intercompany service order process is not supported with Contract Accounts Receivable and Payable (FI-CA) and Convergent Invoicing (CI) to FI-CA.</li>\n</ul>\n<li>Configurable service parts in Service Management are supported since SAP S/4HANA 2023 FPS1, but configurable service parts are not supported with Advance Shipment.</li>\n</ul>\n<p><strong>Service with Advanced Execution</strong></p>\n<p>While the restrictions from the other sections of this Service note are also applicable here, the following are specific to Service with Advanced Execution.</p>\n<ul>\n<li>Service with Advanced Execution is only supported with Item-based Accounting.</li>\n<li>For quotations with a connected maintenance order, contract determination / assignment for the subitems is not supported.</li>\n<li>Intercompany checks and other validations are not supported in maintenance plan.</li>\n<li>The ability to configure credit check relevance for execution order item at the item category level configuration node is currently not provided. However, it can be found in the SAP Reference IMG under: <em>Service -&gt; Transactions -&gt; Basic Settings -&gt; Credit Management -&gt; Activate Credit Check on Items</em>.</li>\n<li>Credit check is supported for Billing on Completion and Summarized but not for Itemized billing. Deletion of debit memo request (DMR), deletion of DMR lines or rejection of DMR lines are not considered in the credit exposure update.</li>\n<li>Deletion of the debit memo and credit memo lines will not have an impact in the transfer to billing status field value in the billing assignment block of the item. It is recommended to reject such billing request lines and not delete them.</li>\n<li>The billed value in the item level billing assignment block of execution order item will only be displayed when billing relevance is Billing on Completion.</li>\n<li>No APIs are available for Service with Advanced Execution. Mass processing or external integration is currently not possible.</li>\n<li>When using Enterprise Organizational Model, no automatic determination of Planning Plant and Work Center is supported. </li>\n<li>No integration or data exchange between the service request and the plant maintenance notification. </li>\n<li>No integration or data exchange between the service order and the billable maintenance order in warranty determination.</li>\n<li>Archiving of service with advanced execution orders is not supported with existing archiving object of service orders. Archiving of billable orders is also not supported. Any attempt to archive service with advanced execution orders or billable orders could cause inconsistencies in the system.</li>\n<li>Price item is not supported as a subitem of the execution order item in the service order quotation.</li>\n<li>Resource-Related Billing of Service with Advanced Execution processing:</li>\n<ul>\n<li>The ability to process multiple service order items and mass processing of service orders for billing via Debit Memo Request (DMR) is not supported.</li>\n<li>The payment card field value is not transferred from the service order to the debit / credit memo request or to the debit / credit memo.</li>\n</ul>\n<li>Business completion of execution order item in service order with billing relevance Itemized Billing is not supported.</li>\n</ul>\n<p><strong>Service with Advanced Execution - Service Contract Management</strong></p>\n<ul>\n<li>Value and quantity-based service contract items are not supported.</li>\n<li>No delivery Customizing for service contract integration with service execution order item. One can refer to scope item 426. Item categories SCN1, SCN2, SCN3 integration with service execution order item. This is delivered with transaction type SC1 as inactive.</li>\n<li>There is no migration for service contracts from Sales and Distribution to S/4HANA service contract with item-based accounting.</li>\n</ul>\n<p><strong>Service with Advanced Execution - Maintenance Management</strong></p>\n<ul>\n<li>Billable maintenance order can't be created through an external API. </li>\n<li>Plant maintenance operation level controlling is not supported. </li>\n<li>The usage of EAM Fiori Apps for the connected billable maintenance order from service with advanced execution is not supported. The creation and change of billable maintenance orders along with the synchronization of the service relevant attributes - Billable indicator, Sales Area, and Service Product are supported only via SAPGUI. For more information, see note <a href=\"https://me.sap.com/notes/3363107\" target=\"_blank\">﻿3363107</a>.</li>\n<li>None of the mass change transactions are currently supported for billable orders like IW37 or IW37n.</li>\n<li>Only SAP GUI apps in the area of plant maintenance / maintenance management are supported.</li>\n<li>The use of an equipment with a WBS element reference in a maintenance order will lead to inconsistencies and hence is not recommended</li>\n<li>The advance shipment document generated from the billable maintenance order currently displays the maintenance order number in the document flow. The service order is not displayed advance shipment document, but can be viewed in the document flow connected to the maintenance order. </li>\n<li>Assignment of contract to the advance shipment order generated out of billable maintenance orders is not supported.</li>\n<li>Warranty processing of billable maintenance order and warranty claims integration is not supported.</li>\n<li>Billable maintenance orders are not supported in SAP S/4HANA Asset Management for resource scheduling. For more information, see note <a href=\"https://me.sap.com/notes/3398291/E\" target=\"_blank\">3398291 - Filter out Billable Orders - SAP for Me</a>.</li>\n<li>New fields for service processing (e.g., service product, service order etc.) are not supported in report RIAUFK20 via SE38 or using transaction variants. These fields are only supported if you run IW38 or IW39 directly.</li>\n<li>The result list of BAPI list function module (e.g., BAPI_ALM_ORDERHEAD_GET_LIST) does not supporting the service related fields for billable maintenance orders. The result list has no information specific for billable maintenance orders.</li>\n<li>In case of list transactions, it is not possible to search with service fields (EQ initial) to filter out only non-billable maintenance orders.</li>\n<li>Status changes in the billable maintenance order are supported only in the transaction IW32. It is not supported in other channels such as IW41, IW42, BAPIs, list transactions, mass change and so on.</li>\n<li>Configurable service products can't be entered in transaction IW32 when creating a billable maintenance order.</li>\n<li>In Distributed Execution, the setting of Completed status on the execution order item or the service order may not work correctly when the corresponding billable maintenance orders includes a suborder hierarchy.</li>\n<li>In Distributed Execution, it is currently possible to change the profit center and accounting indicator fields in the corresponding billable maintenance orders. However, it is recommended to keep these values as it is maintained in the execution order item.</li>\n<li>Billable maintenance orders are not automatically checked for business completion when service order is set to business complete. Billable maintenance orders have to be manually checked to ensure business completion.</li>\n</ul>\n<p><strong>Service with Advanced Execution - Finance</strong></p>\n<ul>\n<li>Intercompany plant maintenance master data set up is supported when sales area company code and planning plant company code are the same. If there are multiple maintenance orders corresponding to a single execution order item, all maintenance orders have to be in the same company code.</li>\n<li>Planned revenue calculation is not supported in resource-related billing.</li>\n<li>Execution order items with Summarized billing are not supported in EBRR.</li>\n<li>Completed contract and percentage of completion method (POC) are not recommended for resource-related billing.</li>\n<li>In resource-related billing, dynamic line items that are not relevant for billing should be rejected to avoid EBRR balance during period end activities.</li>\n<li>The EBRR balance sheet values will be cleared (including reserves for missing cost) when billable maintenance order status is set to Technically Completed (TECO) and billing is completed along with its release to accounting for the service order item. Therefore, for Fixed Price EBRR all actual cost should be posted to billable maintenance order before setting the order status to TECO.</li>\n<li>Settlement of billed costs in billable maintenance order should be avoided.</li>\n<li>Profit Center reorganization is not supported for Service Orders with Advanced Execution.</li>\n<li>EBRR is not applicable for execution order items with billing relevance Not Relevant for Billing.</li>\n</ul>\n<p><strong>Service with Advanced Execution – Restrictions Resolved in FPS01</strong></p>\n<ul>\n<li>ACDOCP is supported as a source in the DIP profile for usage 11 – Quotation creation and Sales Pricing in service order quotation. </li>\n<li>Credit check is supported in service order quotation.</li>\n<li>Credit check is supported for Summarized Billing.</li>\n<li>Sub-orders are supported in Service with Advanced Execution.</li>\n<li>Direct cost posting to execution order item is supported.</li>\n<li>Contract Accounts Receivable and Payable (FI-CA) and SEPA are supported for resource-related billing.</li>\n<li>The following field values are transferred from the service order to the debit / credit memo request or to the debit / credit memo.</li>\n<ul>\n<li>Contract account</li>\n<li>SEPA mandate</li>\n<li>Terms of payment</li>\n<li>Alternate tax classification</li>\n</ul>\n</ul>\n<p><strong>S/4HANA Service Contract Management</strong></p>\n<ul>\n<li>When Item-based Accounting is activated, then quantity and value-based service contracts are not supported. </li>\n<li>When Contract Accounts Receivable and Payable (FI-CA) is active, the integration of Credit Management with service transactions is not supported.</li>\n<li>Contracts with item-based accounting are not integrated with Revenue Accounting and Reporting (RAR). Event-Based Revenue Recognition should be used instead (EBRR, see <a class=\"fui-Link ___1idfs5o f3rmtva f1ewtqcl fyind8e f1k6fduh f1w7gpdv fk6fouc fjoy568 figsok6 f1hu3pq6 f11qmguv f19f4twv f1tyq0we f1g0x7ka fhxju0i f1qch9an f1cnd47f fqv5qza f1vmzxwi f1o700av f13mvf36 f1cmlufx f9n3di6 f1ids18y f1tx3yz7 f1deo86v f1eh06m1 f1iescvh ftqa4ok f2hkw1w fhgqx19 f1olyrje f1p93eir f1h8hb77 f1x7u7e9 f10aw75t fsle3fq f17ae5zn\" href=\"https://me.sap.com/notes/3357625\" rel=\"noreferrer noopener\" target=\"_blank\" title=\"https://me.sap.com/notes/3357625\">https://me.sap.com/notes/3357625</a> ).</li>\n<li>There is no migration for S/4HANA service contracts based on internal order to S/4HANA service contract with item-based accounting.</li>\n</ul>\n<p><strong>Cross application functions</strong></p>\n<ul>\n<li>Concurrent employment is not supported in S/4HANA Service process (which includes Field Service process, In-House Repair process, Recurring Service). Service Process is working with Business Partner with Employee Role.</li>\n</ul>\n<p><strong>Important Information for Upgrade of Maintenance Service 2022 to Service with Advanced Execution 2023</strong></p>\n<p>It is not recommended to implement Maintenance Service from the SAP S/4HANA 2022 release for productive use. We strongly recommend implementing this capability directly from SAP S/4HANA 2023 or later releases. There have been significant changes in the Maintenance Service area since the 2022 release. This makes an upgrade of a productive system from 2022 to 2023 or later more difficult.</p>\n<p>The improvements introduced in 2023 make it far more advanced and streamlined than the 2022 version.</p>\n<ul>\n<li>The Customizing activity Map Order Types has been changed. While the old view still remains in the 2022 setup, it is not recommended to use this anymore. This has been replaced with new views which is more intuitive. The activity can be found in the SAP Reference IMG under: <em>Service <em>-&gt;</em> Transactions <em>-&gt;</em> Settings for Service Transactions <em>-&gt;</em> Integration <em>-&gt;</em> Plant Maintenance Integration <em>-&gt;</em> Map Order Types</em></li>\n<li>For the scenario where the maintenance order is created from the service quotation, the status previously used in the billable maintenance order have been replaced.</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"335\">\n<blockquote>\n<p><strong>Current Status</strong></p>\n</blockquote>\n</td>\n<td valign=\"top\" width=\"280\">\n<blockquote>\n<p><strong>New Status</strong></p>\n</blockquote>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"335\">\n<blockquote>\n<p>QPIP - Quotation Planning In Process</p>\n</blockquote>\n</td>\n<td valign=\"top\" width=\"280\">\n<blockquote>\n<p>ORAR - Order Approval Required</p>\n</blockquote>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"335\">\n<blockquote>\n<p>QPCD - Quotation Planning Completed</p>\n</blockquote>\n</td>\n<td valign=\"top\" width=\"280\">\n<blockquote>\n<p>ORAI - Order Approval in Process</p>\n</blockquote>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"335\">\n<blockquote>\n<p>QPAC - Quotation Planning Accepted</p>\n</blockquote>\n</td>\n<td valign=\"top\" width=\"280\">\n<blockquote>\n<p>ORAP - Order Approved</p>\n</blockquote>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"335\">\n<blockquote>\n<p> </p>\n</blockquote>\n</td>\n<td valign=\"top\" width=\"280\">\n<blockquote>\n<p>ORRJ - Order Rejected</p>\n</blockquote>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>To create a service order from the maintenance order, the Billable indicator should be checked in transaction IW32 Change Order.</li>\n<li>The Simulate Subitems button is not available in the maintenance service quotation. </li>\n<li>You can combine maintenance service items along with other service item types in the same service transaction.</li>\n<li>The selection option of multiple items of a service order through transaction DP92 (Create Resource-Related Billing Request - Service Order) for billing via Debit Memo Request (DMR) will be changed to an individual line selection in the 2023 OP release.</li>\n<li>The following Web UI components for maintenance service order and maintenance service quotation will be made obsolete in all future releases and not supported anymore.</li>\n<ul>\n<li>BT116MM_MCSO</li>\n<li>BT116MM_MCSQ</li>\n<li>BT116MH_MCSO</li>\n<li>BT116MH_MCSQ</li>\n</ul>\n<li>The term Maintenance Service has been renamed to Service with Advanced Execution and maintenance service item has been renamed to execution order item. Also, maintenance service order and maintenance service quotation have been replaced with service order and service order quotation.</li>\n<li>Service with Advanced Execution can be processed with the standard transaction types SRVO (service order) and SRVQ (service order quotation). The transaction types MSO1 and MSQ1 are not offered as part of the delivery customizing anymore, but they are still available as part of SAP Best Practices.</li>\n<li>The following business catalogs and group have been removed:</li>\n<ul>\n<li>Catalogs - SAP_S4CRM_BC_MCSO_PROC and SAP_S4CRM_BC_MCSO_DISP</li>\n<li>Group - SAP_S4CRM_BCG_MCSO_PROC</li>\n</ul>\n<li>The following business catalogs can be used:</li>\n<ul>\n<li>SAP_SERV_BC_EMP_ORDERS_T - Service – Service Orders</li>\n<li>SAP_SERV_BC_SRVCPROF_OPS_T - Service Management – Operations</li>\n<li>SAP_S4CRM_BC_MAINT_ORD - Plant Maintenance Order – Process</li>\n<li>SAP_S4CRM_BC_MSP_PROC - Recurring Service with Advanced Execution</li>\n</ul>\n<li>The following business groups can be used:</li>\n<ul>\n<li>SAP_SERV_BCG_SRVCPROFNL_OPS_T - Operations</li>\n<li>SAP_S4CRM_BCG_MSP_PROC - Recurring Service with Advanced Execution</li>\n</ul>\n</ul>\n<blockquote></blockquote>", "noteVersion": 34}, {"note": "3209741", "noteTitle": "3209741 - SAP S/4HANA 2022: Release information and restrictions for Maintenance Service in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">This SAP note provides information about the restrictions that exist for implementing Maintenance Service Process in SAP S/4HANA, on-premise edition 2022.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The <strong>Enable Item-Based Accounting for Service Management</strong> switch must be enabled by the customer. Enabling this switch makes the service item the controlling object on the Universal Journal.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following list of restrictions could be extended over time.</p>\n<p>SAP S/4HANA Service Management - Maintenance Service</p>\n<ul>\n<li>Maintenance Service can be used only when the Enable Item-Based Accounting for Service Management switch is set to True. It is not supported if the account assignment manager is in use.</li>\n<li>Existing S/4HANA Service, using the account assignment manager, and Maintenance Service, using maintenance service item as controlling object, cannot work together.</li>\n<li>For existing S/4HANA Service customers upgrading to SAP S/4HANA 2022, the Item-Based Accounting switch is delivered in a deactivated state. </li>\n<li>Once the Item-Based Accounting switch is activated by the customer, reversing this activation will incur financial inconsistencies and is not recommended.</li>\n<li>The current scope of Maintenance Service is focused on:</li>\n<ul>\n<li>Preventive Maintenance (scope item 6F1)</li>\n<li>Corrective Maintenance (scope item 6AU)</li>\n</ul>\n<li>Automatic inclusion of price conditions using modular pricing procedures is not supported.</li>\n<li>No APIs are available for Maintenance Service. Mass processing or external integration is currently not possible.</li>\n<li>Organizational Model in SAP S/4HANA Service is a pre-requisite to run Maintenance Service. The Enterprise Organizational Model is not supported.</li>\n<li>No integration or data exchange between the service request and the plant maintenance notification. </li>\n<li>No integration or data exchange between the maintenance service order and the work order in warranty determination. </li>\n<li>Intercompany billing is not supported. </li>\n<li>Summarized billing is not supported via Event-Based Revenue Recognition (EBRR). Itemized billing will be supported via EBRR. </li>\n<li>Advance shipment capability is not available. </li>\n<li>Configurable service is not supported. </li>\n<li>Down payment scenario is not supported. </li>\n<li>Credit check is not supported. </li>\n<li>Archiving of maintenance service orders is not supported with existing archiving object of service orders. Archiving of billable orders is also not supported. Any attempt to archive maintenance service orders or billable orders could cause inconsistencies in the system.</li>\n</ul>\n<p>SAP S/4HANA Service Management – Service Order Management</p>\n<ul>\n<li>Service Order Processing with the item-based accounting is not supported.</li>\n</ul>\n<p>SAP S/4HANA Service Management – In-House Repair</p>\n<ul>\n<li>In-house repair processing with the item-based accounting is not supported.</li>\n</ul>\n<p>SAP S/4HANA Service Management – Recurring Service</p>\n<ul>\n<li>Mass scheduling of maintenance service plan is not supported.</li>\n</ul>\n<p>SAP S/4HANA Service Contract Management</p>\n<ul>\n<li>Value and quantity-based service contract items are not supported.</li>\n<li>Maintenance Service customers will not be able to run Revenue Accounting and Reporting (RAR) on service contracts/service processes. Customers will instead use Event-Based Revenue Recognition (EBRR).</li>\n<li>Only time-based contracts will be supported by EBRR.</li>\n<li>Only item categories SCN1, SCN2, and SCN3 will be supported in EBRR. These item categories will be shipped with scope item 426 as part of transaction type SC1 with status inactive. After activation of SC1 they can be used out-of-the-box.  </li>\n<li>There is no migration for service contracts from Sales and Distribution and service contracts based on internal order (IO) or work breakdown structure (WBS).</li>\n</ul>\n<p>Maintenance Management</p>\n<ul>\n<li>Billable maintenance order can't be created through an external API. </li>\n<li>Plant maintenance operation level controlling is not supported. </li>\n<li>The usage of EAM Fiori Apps for the connected billable maintenance order from maintenance service is not supported. The creation and change of billable maintenance order along with the synchronization of the service relevant attributes - Billable indicator, Sales Area, and Service Product are supported only via SAPGUI.</li>\n<li>Sub-orders are not supported in Maintenance Service.</li>\n<li>None of the mass change transactions are currently supported for billable orders like IW37 or IW37n.</li>\n</ul>\n<p>Finance</p>\n<ul>\n<li>Time and Material based revenue recognition is supported as revenue recognition method.</li>\n<li>Time based revenue recognition is also supported.</li>\n<li>Results Analysis is not supported.</li>\n<li>Assignment of work breakdown structure (WBS) element is not supported. </li>\n<li>Assignment of a service contract with internal order is not supported.</li>\n<li>Contract Accounts Receivable (FI-CA) is not supported for receivables management.</li>\n<li>Intercompany Plant Maintenance Master data setup is not supported in the process.</li>\n<li>Planned cost and revenue calculation is not supported.</li>\n<li>There is no integration with Central Finance.</li>\n<li>Costing based CO-PA is not supported.</li>\n<li>Settlement of maintenance order should be avoided for billable orders.</li>\n<li>Profit Center reorganization is not supported for maintenance service orders.</li>\n<li>Assignment of a service contract with internal order or work breakdown structure (WBS) element as account assignment object, is not supported.</li>\n</ul>\n<p>UI limitations</p>\n<p>Support for maintenance service process is restricted to the following apps:</p>\n<ul>\n<li>Business Group Maintenance Service</li>\n<ul>\n<li>Create Service Order</li>\n<li>Search Service Orders</li>\n<li>Create Service Order Quotation</li>\n<li>Search Service Order Quotations</li>\n<li>Release for Billing</li>\n<li>Create Resource-Related Billing Request - Maintenance Service</li>\n</ul>\n<li>Business Group Service Contracts</li>\n<ul>\n<li>Create Service Contract</li>\n<li>Search Service Contracts</li>\n<li>Manage Service Contracts</li>\n</ul>\n<li>Business Group Maintenance Service Planning</li>\n<ul>\n<li>Add Single Plan</li>\n<li>Add Strategy-Controlled Plan</li>\n<li>Change Maintenance Plan</li>\n<li>Display Maintenance Plan (IP03)</li>\n<li>Create Maintenance Item</li>\n<li>Change Maintenance Item (IP05)</li>\n<li>Display Maintenance Item (IP06)</li>\n<li>Schedule Maintenance Plan</li>\n</ul>\n</ul>\n<p>The following apps are not supported. Please note that this is not a complete list.</p>\n<ul>\n<li>Business Group Service Monitoring and Analytics</li>\n<ul>\n<li>Service Management Overview</li>\n<li>Service Order Issues</li>\n</ul>\n<li>Business Group Recurring Service Planning</li>\n<ul>\n<li>Create Maintenance Plan</li>\n<li>Find Maintenance Plans - Service</li>\n<li>Find Maintenance Items - Service</li>\n</ul>\n<li>Business Group Recurring Service Scheduling</li>\n<ul>\n<li>Schedule Maintenance Plan - Service</li>\n<li>Mass Schedule Maintenance Plans - Service</li>\n<li>Monitor Mass Scheduling Logs - Service</li>\n</ul>\n<li>Business Group Maintenance Plan</li>\n<ul>\n<li>Find Maintenance Plans</li>\n</ul>\n<li>Business Group Maintenance Item</li>\n<ul>\n<li>Find Maintenance Items</li>\n</ul>\n<li>Business Group Maintenance Orders Management</li>\n<ul>\n<li>Business Group Maintenance Planning Management</li>\n<li>Business Group Maintenance Orders Display</li>\n</ul>\n</ul>\n<p>Billable orders are only supported in SAP GUI. For more information, see note <a href=\"https://me.sap.com/notes/3363107\" target=\"_blank\">﻿3363107</a>.</p>\n<p>The 2022 release of Maintenance Service has not been enabled for productive use. Upon request, a pilot note can be provided which enables you to use the 2022 version in a productive environment.</p>\n<p>Resource-Related Billing of Maintenance Service processing</p>\n<ul>\n<li>The selection option of multiple items of a service order through transaction DP92 for billing via Debit Memo Request (DMR) will be changed to an individual line selection in the 2023 OP release.</li>\n<li>The ability to process multiple service order items and mass processing of service orders for billing via Debit Memo Request (DMR) is a road map item for supporting the varied customer requirements.</li>\n</ul>\n<p><strong>Important Information For Upgrade from 2022 to 2023</strong></p>\n<p>It is not recommended to implement Maintenance Service from the SAP S/4HANA 2022 release for productive use. We strongly recommend implementing this capability directly from SAP S/4HANA 2023 or later releases.</p>\n<p>There have been significant changes in the Maintenance Service area since the 2022 release. This makes an upgrade of a productive system from 2022 to 2023 or later more difficult.</p>\n<p>The improvements introduced in 2023 make it far more advanced and streamlined than the 2022 version. For example:</p>\n<ul>\n<li>The Customizing activity Map Order Types has been changed. While the old view still remains in the 2022 setup, it is not recommended to use this anymore. This has been replaced with new views which is more intuitive. The activity can be found in the SAP Reference IMG under: Service &gt; Transactions &gt; Settings for Service Transactions &gt; Integration &gt; Plant Maintenance Integration &gt; Map Order Types</li>\n</ul>\n<ul>\n<li>For the scenario where the maintenance order is created from the service quotation, the status previously used in the billable maintenance order have been replaced.</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\" summary=\"\" title=\"\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"249\">\n<p><strong>Current Status</strong></p>\n</td>\n<td valign=\"top\" width=\"203\">\n<p><strong>New Status</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"249\">\n<p>QPIP - Quotation Planning In Process</p>\n</td>\n<td valign=\"top\" width=\"210\">\n<p>ORAR - Order Approval Required</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"251\">\n<p>QPCD - Quotation Planning Completed</p>\n</td>\n<td valign=\"top\" width=\"208\">\n<p>ORAI - Order Approval in Process</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"249\">\n<p>QPAC - Quotation Planning Accepted</p>\n</td>\n<td valign=\"top\" width=\"203\">\n<p>ORAP - Order Approved</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"249\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"203\">\n<p>ORRJ - Order Rejected</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>To create a service order from the maintenance order, the Billable indicator should be checked in transaction IW32 Change Order.</li>\n<li>You can combine maintenance service items along with other service item types in the same service transaction.</li>\n<li>The following Web UI components for maintenance service order and maintenance service quotation will be made obsolete in all future releases and not supported anymore.</li>\n<ul>\n<li><span>BT116MM_MCSO</span></li>\n<li><span>BT116MM_MCSQ</span></li>\n<li><span>BT116MH_MCSO</span></li>\n<li><span>BT116MH_MCSQ</span></li>\n</ul>\n<li>The term Maintenance Service has been renamed to Service with Advanced Execution and maintenance service item has been renamed to execution order item.</li>\n</ul>", "noteVersion": 8}], "activities": [{"Activity": "Implementation project required", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "If decided, implement alternative solution."}, {"Activity": "Custom Code Adaption", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "Remove and clean up your Customer Service specific custom developments if it was decided to replace Customer Service."}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Decide on future Customer Service strategy."}]}