{"guid": "00109B1315FA1ED8ADE3FC1B0A2E00D3", "sitemId": "SI014: CRM", "sitemTitle": "Changes in payment card handling", "note": 2693417, "noteTitle": "2693417 - Changes in payment card handling", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are considering or planning a migration of a SAP CRM 7.0 or SAP CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. In this case, the following information about changed or deprecated functionality is relevant.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRMTWL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Payment cards are no longer entered directly into a business transaction. Maintenance thereof has been redirected towards payment service providers (PSPs). The S/4HANA service transaction only holds a token referring to the payment card; no card data is persisted in S/4HANA.<br/>Manual payment card authorization and payment type \"Cash on delivery\" are not supported.</p>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span>In order to check whether payment cards are used in transactions, check whether the database table COMD_PAYPLAN contains entries.</span></p>", "noteVersion": 3, "refer_note": []}