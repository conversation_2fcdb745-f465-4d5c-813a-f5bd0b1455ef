{"guid": "901B0E6D3EE91ED6918A5D81D28740CF", "sitemId": "SI5_FIN_GL", "sitemTitle": "S4TWL - Closing Cockpit with S/4 HANA OP", "note": 2332547, "noteTitle": "2332547 - S4TWL - Closing Cockpit with S/4HANA OP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SYSTEM_ABAP_ACCESS_DENIED</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>Task</strong>: <br/>Check if any of the Business Functions FIN_ACC_LOCAL_CLOSE, FIN_ACC_LOCAL_CLOSE_2, FIN_ACC_LOCAL_CLOSE_3 are active in SAP ERP. Use transaction SFW5 to list active Enterprise Business Functions.<br/>Check if transaction CLOCO is used in SAP ERP or SAP S/4HANA.</p>\n<p><strong>Rating</strong>:</p>\n<p>If at least one transaction codes has been used -&gt; YELLOW</p>\n<p>If none of the transaction codes has been used -&gt; GREEN</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong>:</p>\n<p>The functionality of the standard Closing cockpit (without license) is available, but defined as \"<span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">not the target architecture\" in </span>SAP S/4HANA 1511, 1610 or subsequent releases. The Business Functions FIN_ACC_LOCAL_CLOSE, FIN_ACC_LOCAL_CLOSE_2, FIN_ACC_LOCAL_CLOSE_3 (requiring a license for SAP Financial Closing cockpit) are flagged as obsolete in S/4HANA OP.</p>\n<p><strong>Business Process related information</strong></p>\n<p>If any of the Business Functions listed above are active, instead of using the classical Closing cockpit,</p>\n<ul>\n<li>under SAP S/4HANA 1610 OP consider to use the Add-On Financials Closing Cockpit 2.0, </li>\n<li>under SAP S/4HANA OP 2019, use the <a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/8b1d76cd5e7644caa0553fcf338f3982/3fe49e8767d648a6b1b5edeec8849b8d.html\" target=\"_blank\">SAP S/4HANA Financial Closing cockpit</a>,</li>\n<li>with SAP S/4HANA OP 2019 ff. consider to use <a href=\"https://help.sap.com/docs/advanced-financial-closing?locale=en-US\" target=\"_blank\">SAP Advanced Financial Closing</a>.</li>\n</ul>", "noteVersion": 14, "refer_note": [], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": " if you want to use \"SAP S/4HANA Financial Closing cockpit\" then execute configuration guide steps"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Data correction", "Phase": "Before conversion project", "Condition": "Conditional", "Additional_Information": "If you want to use your customer-specific reports in the Closing Cockpit functionality, and if you want to use SAP Financial Closing cockpit for SAP S/4HANA, please include two function modules according to this SAP Note."}, {"Activity": "Technical System Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If you decide for implementing closing cockpit 2.0 then please implement the Add-on mentioned in the SAP Note"}, {"Activity": "Fiori Implementation", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": "Implement Fiori Apps for Financial Closing Cockpit"}, {"Activity": "Data migration", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "If you switch to Financial Closing Cockpit, migrate your task list templates."}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "Inform users about new UIs and features of SAP S/4HANA Financial Closing Cockpit.."}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Evaluate SAP S/4HANA Financial Closing Cockpit and decided if to replace the classical Closing Cockpit with it."}]}