{"guid": "00109B1315FA1EDABF8056D998BAA0EF", "sitemId": "SI7_FIN_AA", "sitemTitle": "S4TWL - Group Assets", "note": 3014869, "noteTitle": "3014869 - S4TWL - Group Assets", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">You are doing a system conversion from SAP ERP to SAP S/4HANA or an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this note. The following SAP S/4HANA Simplification Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Compatibility Scope, Compatibility Package, System Conversion, Upgrade, Joint Venture Accounting, ID 428</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p><strong> <br/> </strong>The functionality group assets in Asset Accounting is part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, group assets can be found under the ID 428.</p>\n<p>SAP does not plan to provide an alternative for group assets in Asset Accounting. If you wish to group your assets also in future, we recommend building your own solution. The aggregation for reporting according to different aggregation attributes is supported by following CDS Views</p>\n<ul>\n<li><a href=\"https://api.sap.com/cdsviews/PCE_I_ASSETBALANCECUBE\" target=\"_blank\">I_ASSETBALANCECUBE</a> </li>\n<li><a href=\"https://api.sap.com/cdsviews/PCE_I_ASSETHISTORYSHEETCUBE\" target=\"_blank\">I_ASSETHISTORYSHEETCUBE</a> </li>\n</ul>\n<p>All information is recorded in ACDOCA. Extensions can be used for CDS reporting on grouped assets.</p>\n<p>Alternatively, you can introduce respective user fields in asset master or make use of the existing feature “Asset Super Number” to group asset if it is not yet used for any other purpose.</p>\n<p>Note that postings to aggregated assets are not possible with SAP S/4HANA.</p>\n<p><strong>Business Process related information</strong></p>\n<p>A description of the functionality in compatibility scope you find in the SAP S/4HANA Feature Scope Description in section <a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/029944e4a3b74446a9099e8971c752b9/09337156ec2ec459e10000000a441470.html?lastversion\" target=\"_blank\">FI-AA: Group Assets</a>.</p>\n<p>Adjust roles, user menus and authorizations to remove group asset transactions, since transactions AS21, AS22, AS23, AS24, AS25 and AS26 are no longer supported.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Until the expiry date of the compatibility pack license, you can still use group asset functionality. However, we recommend defining your future strategy early in advance of this date because group asset functionality must not be used after this point in time.</p>", "noteVersion": 4, "refer_note": [{"note": "2269324", "noteTitle": "2269324 - Compatibility Scope Matrix for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Through the Compatibility Packages (CP) listed in the attachment \"Compatibility Scope Matrix\", SAP provides a limited use right to SAP S/4HANA on-premise customers to run certain classic SAP ERP solutions on their SAP S/4HANA installation. Condition is that these customers have licensed the applicable solutions as set forth in their License Agreements. Compatibility Pack use rights may apply to selected private cloud deployments as well, without the prerequisite of an on-premise classic license. Please refer to the respective Service Description Guide for details.</p>\n<p>This use right expires on Dec 31, 2025, and is available to installed-base as well as net-new customers.</p>\n<p>The <a href=\"https://news.sap.com/2020/02/sap-s4hana-maintenance-2040-clarity-choice-sap-business-suite-7/\" target=\"_blank\">announcement</a> about the extension of maintenance for Business Suite solutions has no influence on the end of compatibility pack use rights - they will be terminated after 2025.<sup>(1)</sup></p>\n<p>Besides reading the attached documents, SAP recommends the latest SAP Community webinar on the topic: <a href=\"https://www.youtube.com/live/muPrV5J7ffM?feature=shared\" target=\"_blank\">https://www.youtube.com/live/muPrV5J7ffM</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA Compatibility Scope Matrix, Way Forward</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In the “Attachments” section, the “Overview Presentation” and “Detail FAQ” documents explain the “why”, “what” and “how” of CPs. For functional details, please also refer to the Feature Scope Description (FSD) of SAP S/4HANA, <a href=\"https://help.sap.com/doc/e2048712f0ab45e791e6d15ba5e20c68/latest/\" target=\"_blank\">latest version</a> (or through http://help.sap.com/s4hana), chapter 5.</p>\n<p>The “Way Forward” presentation and overview list, also under “Attachments”, provide information about solution alternatives in the perpetual scope of SAP S/4HANA. They describe the status of each Compatibility Pack and provide links to more detailed information about its strategy. This information is provided via SAP standard documentation, such as the Roadmaps and the Innovation Discovery. The “Item ID” helps to cross-reference between the original, descriptive CP matrix and the future-oriented “Way Forward” spreadsheet.</p>\n<p>This blog <a href=\"https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/\" target=\"_blank\">https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/</a> provides more details about the procedure for Compatibility Packs after their use right expiry in 2025/2030.</p>\n<p>SAP is planning regular updates of the attachments. All forward-looking information is non-binding and with preview character. ​The released SAP Roadmaps are the proper source of information.</p>\n<p>For questions and feedback about this note and its content, please create a customer message on component XX-SER-REL with the prefix ‘CompScope’​.</p>\n<p>(1) In the exceptional cases of CS, LE-TRA and PP-PI, the usage right to their respective compatibility pack items (cf. matrix) terminates at the end of 2030.</p>", "noteVersion": 84}]}