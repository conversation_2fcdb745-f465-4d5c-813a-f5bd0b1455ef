{"guid": "6EAE8B28C1011ED69BBDC6ECA53E40C1", "sitemId": "SI11: Utilities_MCF", "sitemTitle": "S4TWL - Multichannel Foundation for Utilities", "note": 2329190, "noteTitle": "2329190 - S4TWL - SAP Multichannel Foundation for Utilities", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>You currently use or are planning to use product <a href=\"http://help.sap.com/umc\" target=\"_blank\">SAP Multichannel Foundation for Utilities and Public Sector </a>for your B2C consumer self-services and y</span><span>ou are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP MCF, Multichannel for Utilities, SSU, SAP Self-Service for Utilities</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>SAP Multichannel Foundation for Utilities and Public Sector (SAP MCF) offers utilities companies a powerful and cost-effective way to extend their customer interactions to digital channels such as Mobile, Web, and Social. SAP MCF exposes business processes out of SAP IS-U and SAP CRM for Utilities by way of OData services enabling the utilities company to offer self-service apps and website for their consumers.</p>\n<p>MCF is delivered as 3 add-ons: UMCERP01 on SAP ERP, UMCCRM01 on SAP CRM and UMCUI501 on SAP Gateway. The SAP ERP and SAP CRM add-ons contain OData services for self-service business processes whereas the SAP Gateway add-on UMCUI501 contains two template SAPUI5 apps/UIs that are integrated with the OData services. These  SAPUI5 template apps serve as examples demonstrating the consumption of SAP MCF's OData services and how they may be integrated into a self-service website or mobile app.</p>\n<p class=\"sapxdpparagraph\">With SAP S/4HANA 1610, the SAP ERP add-on UMCERP01 has been retrofitted into the IS-UT layer. The SAP CRM add-on UMCCRM01 remains unaffected and you can continue to use it after conversion of your SAP ERP system to SAP S/4HANA 1610.  If you are currently using one of the two SAPUI5 template apps delivered in add-on UMCUI501 or are using your own UI/mobile app, you will need to adapt the code as per the instructions in this Note (See \"Required and Recommended Action(s)' section below) and the corresponding custom code SAP note: 2352104. Optionally, you can also use our new SAPUI5 responsive UI template delivered in a new UI SAP Gateway add-on called UIS4MCF. This new UI template is pre-integrated into SAP Multichannel Foundation for Utilities and Public Sector on S/4HANA 1610.</p>\n<p class=\"sapxdpparagraph\">These instructions also apply if you use the mobile apps SAP Self-Service for Utilities for Android or SAP Self-Service for Utilities for iOS. The code of the mobile apps will need to be adapted as well.</p>\n<p class=\"sapxdpparagraph\"><strong><strong>Business Process related information</strong></strong></p>\n<p class=\"sapxdpparagraph\"><span>No influence on business processes expected.</span></p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Follow the instructions in SAP note: 2352104 and the attached Cookbook.</p>", "noteVersion": 3, "refer_note": [{"note": "2432508", "noteTitle": "2432508 - SAP S/4 HANA: Changes in SAP Multichannel Foundation for Utilities and Public Sector (Public Sector)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using an SAP ERP system and intend to perform the upgrade-like conversion to SAP S/4HANA.</p>\n<p>The custom code check shows those customer objects that are affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note contains detailed descriptions on how to adapt customer objects to the changes in SAP MCF.s</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The SAP MCF add-on (UMCERP01) is retrofitted into SAP S/4HANA 1610. In some cases, the change requires the adaptation of the customer code and dictionary, or at least checking whether code or the dictionary need to be adapted. This cookbook aims to help users to understand the changes and their consequences.</p>\n<p> </p>\n<p>Changes in SAP MCF</p>\n<p> </p>\n<p>To facilitate reuse by other industries, for example, Public Sector, non IS-U specific logic of SAP MCF is split into the FI-CA layer. The corresponding OData classes, APIs, structures, data elements, etc. have been moved to the FI-CA layer. The PS specific part remains in IS-PS-CA layer.</p>\n<p>Package: FKK_UMC and sub package: FKK_UMC_BEA, FKK_UMC_CUST, FKK_UMC_ODC are created in FI-CA layer following the same name convention.</p>\n<p> </p>\n<p>The UI template is available in SAP S/4HANA on-premise edition 1610 as add-on: UIS4MCF. The UI template has been changed to MCF_FMCA.</p>\n<p> </p>\n<p>Read Access Logging (RAL) is used to monitor and log read access to sensitive data. In MCF RAL configuration, BankAccount and PaymentCard are involved for read access logging. They require batch processing for the OData request.</p>", "noteVersion": 1}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "SAP Multichannel Foundation for Utilities and Public Sector (SAP MCF) functionality has been changed in SAP S/4HANA 1610"}, {"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "SAP ERP 6.0 add-on UMCERP01 has been retrofitted into the IS-UT layer, but the SAP CRM add-on UMCCRM01 remains unaffected and you can continue to use it after conversion"}, {"Activity": "Interface Adaption", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "If you are using one of the two SAPUI5 template apps as add-on UMCUI501 -> you will need to adapt the code as per Note 2352104"}, {"Activity": "Interface Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "You can also use the new SAPUI5 responsive UI template delivered in a new UI SAP Gateway add-on called UIS4MCF"}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "If you are using your own UI/mobile app -> you will need to adapt the code as per the Cookbook in SAP Note 2352104"}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}