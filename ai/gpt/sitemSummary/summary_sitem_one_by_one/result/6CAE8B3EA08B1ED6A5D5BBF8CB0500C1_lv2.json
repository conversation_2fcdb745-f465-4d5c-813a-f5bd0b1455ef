{"guid": "6CAE8B3EA08B1ED6A5D5BBF8CB0500C1", "sitemId": "SI34: Logistics_General", "sitemTitle": "S4TWL - SAP Retail Store", "note": 2370133, "noteTitle": "2370133 - S4TWL - SAP Retail Store", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>SAP Retail Store and SAP In-Store Merchandise and Inventory Management Mobile (Java) enhanced and optimized tools available for executing store-oriented processes by providing store associates with specially developed store management functions. It offered a web-based user interface that was tailored specifically to the requirements of stores.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, SAP Retail Store and SAP In-Store MIM Mobile (Java) are not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Simplified user interfaces to support store associates/managers are available as Fiori apps or as part of In-Store MIM (Web Dynpro ABAP).</p>\n<p>Because of the Business Partner harmonization the WebDynpro WDA_WSSD_CUST cannot be used in releases prior to SAP S/4HANA 2022, only displaying an existing customer is possible. The respective BAPIs BAPI_CUSTOMER_CREATEFROMDATA1 and BAPI_CUSTOMER_CHANGEFROMDATA1 are not available as well. The WebDynpro app has been adjusted for SAP S/4HANA 2022 and can be used gain.</p>\n<p>As an alternative, customer master data can be maintained through the Fiori app Manage Customer Master Data (F0850A). If you need to adjust the app to your specific needs, consulting note 2655797 describes how this can be achieved.</p>\n<p>It is planned to deprecate the WebDynpro app for sales order maintenance (LO_OIF_MAIN_APP, LO_OIF_SDOC_APPL) with SAP S/4HANA 2023. You need to switch to suitable Fiori apps for sales order maintenance by then.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if SAP Retail Store or SAP In-Store MIM Mobile (Java) is used. <br/>This is the case if transactions</p>\n<ul>\n<li>RFAF</li>\n<li>RME2N</li>\n<li>SRS_GM_SH</li>\n<li>SW_WW10</li>\n<li>WCSO</li>\n<li>WCSO_ENTRY</li>\n<li>WCSOC</li>\n<li>WCSOD</li>\n<li>WCSOS</li>\n<li>WCU2</li>\n<li>WOSCR_CBL</li>\n<li>WOSCR_EXI</li>\n<li>WOSCR_SLOGIN</li>\n<li>WOSZ_MAT_SHOW</li>\n<li>WOSZ_PRINTER</li>\n<li>WSAK</li>\n<li>WSAM_ENTRY</li>\n<li>WSAM_SHMAE</li>\n<li>WSAM_SHMAE</li>\n<li>WSAO_CYCLE</li>\n<li>WSAO_LKPLC</li>\n<li>WSAU</li>\n<li>WSGM</li>\n<li>WSHT</li>\n<li>WSII_ENTRY</li>\n<li>WSPO_ENTRY</li>\n<li>WSSW</li>\n<li>WSTA</li>\n<li>WSTA_R_DLV_DISP</li>\n<li>WSTA_R_PDC_DATA</li>\n<li>WSTA_R_PDC_STRUCT</li>\n<li>WSTE_R_PDC_CUSTOMIZ</li>\n<li>WSTE_R_PDC_DOCUMENT</li>\n<li>WSTE_R_PDC_GR</li>\n<li>WSTE</li>\n<li>WSTE_NEW</li>\n<li>WSTED</li>\n<li>WSTI</li>\n<li>WSTI_DOC</li>\n<li>WSTI_MON</li>\n<li>WSTI_R_PDC_PI</li>\n<li>WSTL</li>\n<li>WSTP</li>\n<li>WSTSA</li>\n<li>WSTU</li>\n<li>WSTV</li>\n<li>WSTV_R_PDC_CUST</li>\n<li>WSTV_R_PDC_DATA</li>\n<li>WSTV_R_PDC_MATDATA</li>\n<li>WSVD_ENTRY</li>\n<li>WSVD_SAPGUI</li>\n<li>WSVD_VNDR_WLIST</li>\n<li>WTAD_SRS</li>\n<li>WTAD_STWB</li>\n<li>WebDynpro WDA_WSSD_CUST       /sap/bc/webdynpro/sap/WDA_WSSD_CUST</li>\n<li>WebDynrpro LO_OIF_MAIN_APP      /sap/bc/webdynpro/sap/LO_OIF_MAIN_APP</li>\n<li>WebDynrpro LO_OIF_SDOC_APPL    /sap/bc/webdynpro/sap/LO_OIF_SDOC_APPL</li>\n</ul>\n<p>are used. This is not a complete list.</p>", "noteVersion": 13, "refer_note": [{"note": "2655797", "noteTitle": "2655797 - Manage Customer Master Data App Variant for Consumer Data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You run a sales order scenario that requires a lean user interface to manage customer master records for consumers, for example for customer orders that are created in retail stores by store associates.</p>\n<p>To manage customer data in SAP S/4HANA, you need to use one of the following SAP Fiori apps: \"Manage Customer Master Data\" or \"Maintain Business Partner Data\". These apps offer the full set of customer or business partner master data and are therefore awkward to use for consumer data management in in-store scenarios.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP note is supported as of SAP S/4HANA 1809 FPS 001.</p>\n<p>The solution described in this SAP note has two parts:</p>\n<ol>\n<li>UI adaptation at runtime to simplify the user interface. <br/>The simpler UI requires more fields than just the consumer's address data because SAP S/4HANA does not support the reference business partner concept for consumers that is available in SAP ECC. Consequently, storing only address data in the business partner is not sufficient for creating a fully operational customer record for sales order processing. In addition, roles, sales area and company code data need to be captured.<br/><br/></li>\n<li>A best practice approach for creating fully operational customer master records by entering just address data in the simplified UI.</li>\n</ol>\n<p>UI adaptation at runtime is applied to the SAP Fiori app \"Manage Customer Master Data\", Fiori ID F0850A. This app can be found within group \"Customer Master\" on the SAP Fiori launch-pad. To see this group, assign business role SAP_BR_BUPA_MASTER_SPECIALIST to your user.</p>\n<p>Only key users can use UI adaptation at runtime. To enable a key user to change the user interface (UI) of the \"Manage Customer Master Data\" app, assign the key user to the authorization role SAP_UI_FLEX_KEY_USER. For further information about UI adaption at runtime, please refer to the following product assistance:</p>\n<ul>\n<li>Enabling UI Adaptation at Runtime:<br/><a href=\"https://help.sap.com/doc/61634ead9e5144b89e7eca2b1d4b8bce/1809.000/en-US/UITECH_OP1809.pdf\" rel=\"nofollow\" target=\"_blank\">https://help.sap.com/doc/61634ead9e5144b89e7eca2b1d4b8bce/1809.000/en-US/UITECH_OP1809.pdf</a></li>\n<li>Adapting SAP Fiori UIs at Runtime: <br/><a href=\"https://help.sap.com/viewer/a7b390faab1140c087b8926571e942b7/1809.000/en-US/877a1b392b7f466e8be3b338ea14a099.html\" target=\"_blank\">https://help.sap.com/viewer/a7b390faab1140c087b8926571e942b7/1809.000/en-US/877a1b392b7f466e8be3b338ea14a099.html</a></li>\n</ul>\n<p>For more information about the \"Manage Customer Master Data\" app, see <a class=\"external-link\" href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/index.html?appId=F0850A\" rel=\"nofollow\" target=\"_blank\">https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/index.html?appId=F0850A</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li>Change the user interface (UI) of the \"Manage Customer Master Data\" app using UI adaptation at runtime (RTA).<br/>Please refer to the attached document.<br/>In the following, the result of the UI adaptation is called \"SIMPLIFIED APP\".<br/><br/></li>\n<li>Follow this best practice approach to avoid entry of non-address data.<br/>The approach makes use of the copy function, which comes with the \"Manage Customer Master Data\" app and is also available in SIMPLIFIED_APP.<br/>Create a fully operational customer master record in the \"Manage Customer Master Data\" app. This record will be used later to create copies and is referred to as \"REFERENCE_CUSTOMER\" in the following. <br/>The data that is required to make REFERENCE_CUSTOMER fully operational depends on the configuration of your system and usually includes the following data besides mandatory address fields:</li>\n<ul>\n<li>Roles: FLCU00 (Business Partner FI Customer) and FLCU01 (Business Partner Customer)</li>\n<li>Sales Areas in which your consumers will place orders along with the following fields:</li>\n<ul>\n<li>Currency </li>\n<li>Incoterms</li>\n<li>Payment Terms</li>\n<li>Delivery Plant</li>\n<li>Shipping Conditions</li>\n<li>Tax classification (for relevant countries and tax categories)</li>\n</ul>\n<li>Company Codes with </li>\n<ul>\n<li>Reconciliation Account</li>\n</ul>\n</ul>\n</ol>\n<p><span>The end user (for example, the store associate) who wants to create a new customer record in an easy way starts SIMPLIFIED_APP, searches for and selects REFERENCE_CUSTOMER in the list and clicks \"Copy\". The user has to take care that the relevant sales areas are included in the copy preselection. The detail screen for data entry then opens and displays the roles and sales area data. So, the end user only has to maintain the address data.</span></p>\n<p><span> </span></p>", "noteVersion": 4}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If customer specific coding re-used development objects in ERP which are no longer available in SAP S4/HANA, the coding needs to be adjusted accordingly."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Training in new UX for store associates/Managers"}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Business needs to decide to support store associates/managers by implementing the available Fiori apps or as part of In-Store MIM (Web Dynpro ABAP)."}, {"Activity": "Fiori Implementation", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "This activity is relevant if business decides to implement FIORI APPs for Retail store Associates/Managers in the store"}]}