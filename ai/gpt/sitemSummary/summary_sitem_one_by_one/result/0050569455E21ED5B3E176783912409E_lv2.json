{"guid": "0050569455E21ED5B3E176783912409E", "sitemId": "SI7: Logistics_PLM", "sitemTitle": "S4TWL - Recipe Management", "note": 2267863, "noteTitle": "2267863 - S4TWL - Recipe Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PLM, Recipe Management, Simplification</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>PLM Recipe Management related transactions are disabled</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>SAP PLM Recipe Management (RM) is not available in SAP S/4HANA. This also affects PLM RM sub-functions such as Labelling, Trial Management and Handover to Manufacturing. The appropriate business requirements are covered within successor functionality SAP S/4HANA PLM Recipe Development (excluding Trial Management.)</p>\n<p>The takeover of recipes from SAP Business Suite into SAP S/4HANA is supported, so the recipes are available and can be displayed after the system conversion to SAP S/4HANA. To use and further develop them, they need to be migrated via the migration function integrated into recipes in SAP S/4HANA PLM Recipe Development.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Functions related to PLM Recipe Development (RM), such as the Recipe Workbench, Recipe, Labelling and Trial Management, are not available within SAP S/4HANA. The functional equivalent is SAP PLM Recipe Development (RD).</p>\n<p>The customer needs to implement development processes based on SAP PLM Recipe Development which will change the user interaction with the software and might include minor process changes. It also allows users to leverage new capabilities of PLM Recipe Development to increase the software based support for the product developer.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>RMWB                  Start Workbench<br/>BD_GEN_GRCP      General Recipe Distribution<br/>BD_GEN_SRCP      Replicate Site Recipe<br/>FRML02                Edit Recipe Formula<br/>FRML03                Display Recipe Formula<br/>FRML04                Formula Information System<br/>MRTRS_START      Start MR Transformation <br/>MRTRSC01            RFC Destination of GR<br/>MRTRSC02            Master Recipe Generation<br/>RMSL02                Change Label Set<br/>RMSL03                Display Label Set<br/>RMSTAT                Mass Status Change</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Customizing Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>ORCP*, RCP*, RMS*, RMX*</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Projects need to migrate their processes from PLM Recipe Management functional capabilities to PLM Recipe Development functional capabilities.</p>\n<p>Data Migration from RM recipes to RD recipes is an integrated part of the tool and can be executed after the system conversion SAP S/4HANA. For the detailed steps, follow the guides for the migration from PLM RM to PLM RD.</p>\n<p>The specification object is not impacted by the switch over to PLM RD.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<ul>\n<li>Does the customer use TRIAL MANAGEMENT</li>\n<li>Check which recipes, formulas and label objects need to be migrated</li>\n<li>Migration transactions - executable before or after system conversion  \r\n<ul>\n<li>/PLMI/RCP_MIGRATION           Recipe Migration</li>\n<li>/PLMI/RMSL_MIGRATION         Migration of Label Sets</li>\n</ul>\n</li>\n</ul>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>How to Determine Relevancy</strong></p>\n<ul>\n<li>The PLM Recipe Management is used if entries exist in DB Table RMSAT_HDR or RMXTT_TRIAL_HD (check with SE16)</li>\n<li>Use ST03N (on the production system, if possible) to check transaction profile of the complete last month, whether the following transactions have been used: <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone80\">RMWB, <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone81\">FRML02, </span><span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone81\">FRML03, <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone81\">FRML04, <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone86\">RMSL02, <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text56-__clone86\">RMSL03</span></span></span></span></span></li>\n</ul>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "Implementation project required", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Projects need to migrate their processes from PLM Recipe Management functional capabilities to PLM Recipe Development functional capabilities."}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Data Migration from RM recipes to RD recipes is an integrated part of the tool and can be executed after the system conversion SAP S/4HANA. For the detailed steps, follow the guides for the migration from PLM RM to PLM RD."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}