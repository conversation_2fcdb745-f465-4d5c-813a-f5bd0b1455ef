{"guid": "0050569455E21ED5B3E176783910609E", "sitemId": "SI2: Logistics_EHS - Occupational Health", "sitemTitle": "S4TWL - Occupational Health", "note": 2267782, "noteTitle": "2267782 - S4TWL - Occupational Health", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Occupational Health, EHS-HEA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With <em>SAP EHS Management</em> as part of <em>SAP ERP</em>, customers can use the <em>Occupational Health</em> (EHS-HEA) function to support general employee occupational healthcare in their company.</p>\n<p>In <em>SAP S/4HANA</em> this solution is no longer available.</p>\n<p>For <em>SAP S/4HANA</em> 1709 and higher the add-on solution <em>SAP EHS Management for SAP S/4HANA, occupational health</em> is available to re-establish this <em>Occupational Health</em> function.</p>\n<p><strong>Business Process related information</strong></p>\n<p><em>Occupational Health</em> (EHS-HEA) is not available in <em>SAP S/4HANA</em>.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"191\">\n<p>Transactions not available in SAP S/4HANA on-premise</p>\n</td>\n<td valign=\"top\" width=\"404\">\n<p>EHSAMBTAET,EHSASSIGN,EHSASSLTA,EHSASSPERS,</p>\n<p>EHSBC02,EHSBP11,EHSBP12,EHSBP13,EHSBP31,EHSBP32,EHSBP33,EHSBP41,EHSBP42,EHSBP43,EHSBP51,EHSBP52,EHSBP53,</p>\n<p>EHSCALPROX,EHSCBER,EHSCDCT, EHSCMAS,EHSDATIMP,EHSDIAGSL,EHSEVAL00,EHSEXIST0,</p>\n<p>EHSH_C_NR_EXA_C,EHSH_C_NR_NEWPE,EHSH_C_NR_RECN,EHSH_C_NR_VAC_I,EHSH_D_CALPHYS,EHSH_D_PCP,</p>\n<p>EHSMQUEST,EHSMQUEST01,EHSPERSHC1,EHSPP01,EHSPRANZ,EHSPRLOE,EHSQCATOH,EHSQEVAL01,EHSQUESTOH,</p>\n<p> EHSSCAN,EHSSERV,EHSSERV01,EHSSERV11,EHSSERV30,EHSSER50,EHSSTRU00,EHSSUGGP,EHSTERM01,</p>\n<p>EHSVA02,EHSVU01,EHSVU01_SELSCREEN,EHSVU11,EHSVU21,EHSVU31</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>For <em>S/4HANA</em> 1709, the Repeatable Customer Solution \"SAP EHS Management for SAP S/4HANA, occupational health\" is available as a replacement. This add-on is technically and from a functional point of view equivalent to the deprecated <em>Occupational Health</em> (EHS-HEA) function.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<div class=\"WordSection1\">\n<ul>\n<li>If you want to remove the <em>Occupational Health </em>(EHS-HEA) function from your system, ...</li>\n<ul>\n<li>... this is done automatically by the <em>S/4HANA</em> system conversion (for target release <em>S/4HANA</em> 1610 and below)</li>\n<li>... just confirm the pre-check error during the conversion check (for target release <em>S/4HANA </em>1709 and higher)</li>\n</ul>\n<li>If you want to continue using your existing <em>Occupational Health</em> functionality in <em>S/4HANA</em>, make sure <strong>not to convert to <em>SAP S/4HANA</em> 1511 or 1610</strong>.</li>\n<ul>\n<li>These conversions would delete all EHS-HEA objects including all existing EHS-HEA data.</li>\n<li>Instead, include the previously mentioned add-on using the <em>SAP Maintenance Planner</em> into your conversion to <em>SAP S/4HANA</em> 1709 or higher.</li>\n<ul>\n<li>If you don't do so and install the add-on after the conversion, your existing data for <em>Occupational Health</em> will get lost!</li>\n</ul>\n</ul>\n</ul>\n<p>If you proceed as described, all your <em>Occupational Health</em> data, transactions and processes will stay untouched. For further details see the referenced notes.</p>\n</div>", "noteVersion": 6, "refer_note": [{"note": "2703822", "noteTitle": "2703822 - Release strategy and Maintenance Information for the ABAP add-on EHSOHS4", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note contains information about planning the installation, upgrades and support packages of the ABAP add-on EHSOHS4</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release strategy, EHSOHS4 100, S/4HANA EHS OCCUP HEALTH 1812, Maintenance Information</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ol>\n<li>General information</li>\n<li>Overview</li>\n<ul>\n<li>SAP Product Availability Matrix (PAM)</li>\n<li>Download</li>\n<li>Modifications</li>\n</ul>\n<li>EHSOHS4 100</li>\n<ol>\n<li>Installation</li>\n<li>Conversion</li>\n<li>Support Package</li>\n</ol></ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p> </p>\n<ol>\n<li>\n<div>General information</div>\n</li>\n</ol>\n<ul>\n<li>\n<div>See the general information from SAP Note <a href=\"/notes/70228\" target=\"_blank\">70228</a>.</div>\n</li>\n<li>\n<div>In general it is not possible to uninstall ABAP add-ons. Exceptions see Note <a href=\"/notes/2011192\" target=\"_blank\">2011192</a>.</div>\n</li>\n<li>\n<div>Upgrades with ABAP add-ons</div>\n</li>\n<ul>\n<li>In systems in which an ABAP add-on is installed, you can upgrade only to SAP releases that are supported for this add-on.</li>\n<li>Note that there is a delay between the delivery of the SAP standard releases and the release of the corresponding add-on releases.</li>\n<li>If an add-on upgrade is connected with a change of the SAP release, it is integrated into the SAP upgrade (repository or system switch). To carry out this upgrade, you require an additional add-on exchange package in addition to the SAP standard upgrade packages. If the SAP standard upgrade is carried out without this additional exchange package, the add-on will no longer work after the upgrade. The entire SAP system is inconsistent.<br/>For more information about this problem, see SAP Note <a href=\"/notes/33040\" target=\"_blank\">33040</a>.</li>\n<li>If S/4 conversion is planned on a system with add-ons, then the add-on upgrade is integrated in the conversion process. To carry out this conversion, you require the target release add-on relevant packages in addition to the SAP standard upgrade packages.If you are scheduling an upgrade or the import of an enhancement package, note that you should not import the latest Support Package Stack in the source release directly before the upgrade to ensure that the required, equivalent Support Package is already available in the target release.<br/>For more information about this, see SAP Note <a href=\"/notes/832594\" target=\"_blank\">832594</a> and the following information on SAP Support Portal.<br/><a href=\"http://support.sap.com/sp-stacks\" target=\"_blank\">http://support.sap.com/sp-stacks</a> -&gt; SP Stack Information -&gt; SP Stack Strategy</li>\n</ul>\n</ul>\n<ol start=\"2\">\n<li>Overview</li>\n<ul>\n<li>SAP Product Availability Matrix<br/>The following information about the add-on in the SAP Product Availability Matrix is available on SAP Support Portal at <a href=\"https://service.sap.com/sap/support/pam\" target=\"_blank\">https://support.sap.com/pam</a><br/>- Availability <br/>- End of maintenance <br/>- Release for products with enhancement packages <br/>- Language support</li>\n<li>Download<br/>The software is available on SAP Support Portal at <a href=\"https://launchpad.support.sap.com/#/softwarecenter\" target=\"_blank\">https://launchpad.support.sap.com/#/softwarecenter</a><br/>-&gt; Search for Software Downloads<br/>-&gt; S/4HANA EHS OCCUP HEALTH 1812</li>\n<li>The add-on does not contain any modifications.<br/>You can also perform the installation if you have already imported more Support Packages into your system than are specified in the section \"Required Support Packages\".</li>\n</ul>\n</ol>\n<p> </p>\n<ol start=\"3\">\n<li>EHSOHS4 100</li>\n<ol>\n<li>Installation<br/>Name of the Installation Package (AOI): SAPK-100AGINEHSOHS4</li>\n</ol></ol>\n<p>You can find the Installation Package on the SAP Support Portal also via the material number 51053524. Download the medium and find the file in the subdirectory DATA_UNITS.<br/><br/>Required components / Support Packages:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td width=\"115\">\n<p>Component</p>\n</td>\n<td width=\"90\">\n<p>Release</p>\n</td>\n<td width=\"256\">\n<p>Support Packages</p>\n</td>\n</tr>\n<tr>\n<td width=\"115\">\n<p>SAP_BASIS</p>\n</td>\n<td width=\"90\">\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>752</td>\n</tr>\n<tr>\n<td>753</td>\n</tr>\n<tr>\n<td>754</td>\n</tr>\n<tr>\n<td>755</td>\n</tr>\n<tr>\n<td>756</td>\n</tr>\n<tr>\n<td>757</td>\n</tr>\n<tr>\n<td>758</td>\n</tr>\n</tbody>\n</table></div>\n</td>\n<td width=\"256\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td width=\"115\">\n<p>S4CORE</p>\n</td>\n<td width=\"90\">\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>102</td>\n</tr>\n<tr>\n<td>103</td>\n</tr>\n<tr>\n<td>104</td>\n</tr>\n<tr>\n<td>105</td>\n</tr>\n<tr>\n<td>106</td>\n</tr>\n<tr>\n<td>107</td>\n</tr>\n<tr>\n<td>108</td>\n</tr>\n</tbody>\n</table></div>\n</td>\n<td width=\"256\">\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>SAPK-10201INS4CORE</p>\n</td>\n</tr>\n<tr>\n<td> -</td>\n</tr>\n<tr>\n<td> -</td>\n</tr>\n<tr>\n<td> - </td>\n</tr>\n<tr>\n<td> -</td>\n</tr>\n<tr>\n<td> -</td>\n</tr>\n<tr>\n<td> -</td>\n</tr>\n</tbody>\n</table></div>\n</td>\n</tr>\n</tbody>\n</table></div>\n<ol><ol start=\"2\">\n<li>Conversion from ERP system<br/><br/><strong>Attention: if you plan a conversion from ERP system and you had used the occupational health functionality it's important to include the Add-on with maintenance planner into the conversion. Otherwise (installing Add-On later) your existing data for occupational health will get lost during conversion.</strong><br/><br/></li>\n<li>Support Packages</li>\n</ol></ol>\n<p>You can download the available Support Packages in the Softwarecenter</p>\n<p><a href=\"https://launchpad.support.sap.com/#/softwarecenter\" target=\"_blank\">https://launchpad.support.sap.com/#/softwarecenter</a></p>\n<p>Please select Support Packages &amp; Patches and find your product using the provided categorizations.</p>\n<p>An overview of the available Support Packages and their Release dates for most products can be found on this page:</p>\n<p><a href=\"https://support.sap.com/en/release-upgrade-maintenance/maintenance-information/schedules-for-maintenance-deliveries.html\" target=\"_blank\"><span>https://support.sap.com/en/release-upgrade-maintenance/maintenance-information/schedules-for-maintenance-deliveries.html</span></a></p>", "noteVersion": 9}, {"note": "2217205", "noteTitle": "2217205 - SAP S/4 HANA Simplification Item: Occupational Health (EHS-HEA)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span arial',sans-serif;\"=\"\">With <em><span arial',sans-serif;\"=\"\">SAP EHS Management</span></em> as part of <em><span arial',sans-serif;\"=\"\">SAP ERP</span></em>, you can use the <em><span arial',sans-serif;\"=\"\">Occupational Health</span></em> (EHS-HEA) function. This function is no longer supported in <em><span arial',sans-serif;\"=\"\">SAP S/4HANA</span></em>.  <br/>For this reason, you need to check if you use development objects belonging to this function in your custom code prior to converting to <em><span arial',sans-serif;\"=\"\">SAP S/4HANA</span></em>. If so, you need to adapt your code.</span></p>\n<p><span arial',sans-serif;\"=\"\"><span>Note</span>: Starting with <em>S/4HANA</em> on-premise edition 1709 there is a functional equivalent available for <em>Occupational Safety</em> (EHS-HEA). For more details on this see the refecenced <a href=\"/notes/2267782\" target=\"_blank\">note 2267782</a>.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><em>SAP S/4HANA</em> simplification,  Occupational Health, EHS-HEA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">SAP Note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a> provides information about how you can use the custom code check tool to analyze if you are using any ABAP development objects in your custom code that are no longer supported.</span></p>\n<p><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">If you are using any development objects that are no longer supported, adapt your coding to ensure that these objects are no longer included in your custom code. <br/>If you need some of the development objects that are no longer supported, you can, for example, copy these objects into your customer namespace and adapt your custom code to use the copied versions.</span></p>\n<p><strong>Note:</strong> Due to technical reasons, the development objects of the <em>Occupational Health</em> (EHS-HEA) function are combined with those of <em>Industrial Hygiene and Safety</em> (EHS-IHS) function. Therefore, the check will also find custom code that uses development objects of the EHS-IHS function as well. For more information about <em>Industrial Hygiene and Safety</em>, see SAP Note <a href=\"/notes/2217206\" target=\"_blank\">2217206</a>.</p>", "noteVersion": 4, "refer_note": [{"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}, {"note": "2241080", "noteTitle": "2241080 - SAP S/4HANA: Content for checking customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to check your customer specific ABAP code via ABAP Test Cockpit (ATC) for compatibility with SAP S/4HANA as described in SAP note <a href=\"/notes/0002190420\" target=\"_blank\">2190420</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Custom Code Check, ABAP Test Cockpit, ATC, Simplification Database, System Conversion, Release Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In order to provide accurate results, ATC requires up to date content describing changes done to SAP objects in SAP S/4HANA (= Simplification Database).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP provides the most recent content for the Simplification Database of SAP S/4HANA as a ZIP file in the Software Center, which you can download and import into your system.</p>\n<p><strong>Downloading the Simplification Database Content</strong></p>\n<ul>\n<li>Go to <a href=\"https://me.sap.com/softwarecenter\" target=\"_blank\">https://me.sap.com/softwarecenter</a></li>\n<li>Search for Component \"CCMSIDB\"</li>\n<li>Download the displayed \"Simplification Database Content\" (most recent version: patch level 18 - creation date: 2024-02-28)</li>\n<ul>\n<li>When using content version 2021-09-28 or newer, please ensure that you have also implemented SAP note 3039646. With this content version the usage detection accuracy for DDIC objects has been improved. But without the corresponding check logic from SAP note 3039646, this content version will lead to an increased number of false positives.</li>\n</ul>\n</ul>\n<p><strong>Importing the Simplification Database Content</strong></p>\n<p>For details how to import the Simplification Database content into your ATC system please refer to the ABAP Platform help:</p>\n<p>   &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/4355509bf8a75f6be10000000a1553f6.html\" target=\"_blank\">Application Development on AS ABAP</a><br/>      &gt; Customer-Specific ABAP Development<br/>        &gt; Custom Code Migration<br/>         &gt; SAP S/4HANA Conversion<br/>           &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/41316510041a4b53974ee74ab6d52512.html\" target=\"_blank\">Simplification Database</a></p>\n<p>When updating the Simplification Database content to the most recent version, please also ensure, that you update the check logic to the most recent version,  by implementing all relevant, new notes from SAP note 2436688. Otherwise you might get wrong check results (e.g. false positives).</p>\n<p>Please be aware, that upon importing the Simplification Database content into your code analysis system you might sometimes get a message \"Details on download of simplification notes: note &lt;note number&gt; incomplete.\" This only means the corresponding SAP note, describing a specific custom code impact is temporarily not accessible as it's being updated by the responsible development area. Or that the connection from your system to SAP service marketplace for note downloads has not been setup. This has no impact on the successful import of the Simplification Database content into your code analysis system. And this will not negatively impact the result of the custom code analysis.</p>\n<p>Though this note download mechanism can cause long import times for the Simplification Database content or even timeouts in systems that are not properly configured for downloading SAP notes. In this case you can implement SAP note 3221402. This adds an option to the Simplification Database upload dialog, to skip the note downloads. The only drawback is, that then the note titles shown next to the ATC results might not be up to date in all cases. But the correct notes are shown anyway.<a href=\"/notes/3221402\" target=\"_blank\" title=\"3221402  - Skip download of SAP Notes during the import of simplification database\"><br/></a></p>\n<p><strong>Additional Information</strong></p>\n<ul>\n<li>The main focus of the Simplification Database Content is to identify the custom code impact of Simplification Items (<a href=\"https://launchpad.support.sap.com/#/sic/overview\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/overview</a>) and other major functional or technical changes in SAP S/4HANA.</li>\n<li>Please be aware that code checks based on the SIDB content do not replace the need to properly test relevant custom code during maintenance events, in order to see if there is any impact from syntactical or semantical changes done on SAP side.</li>\n<li>For prioritization of issues found by the SAP S/4HANA ATC checks, please take the category of the findings into consideration. Especially the findings under the \"Non-strategic...\" categories indicate usage of objects which will become obsolete only at a later point in time. The ATC errors of this category only serve as a reminder, that such objects will become obsolete in future releases. For example compatibility pack functionality (see SAP note 2269324 for details). So these findings do not necessarily have to be fixed already in earlier SAP S/4HANA releases. Please refer to the individual notes shown along the \"Non-strategic...\" findings for details.</li>\n<li>The Simplification Database content covers all available SAP S/4HANA releases. This e.g. allows you, as preparation for your SAP S/4HANA conversion project, to scan your custom code against different SAP S/4HANA target releases with the same Simplification Database content. Which is helpful if the exact target version of your SAP S/4HANA conversion project is not yet decided on.</li>\n<li>It's recommended to use this content for ATC checks in preparation of a SAP S/4HANA system conversion or release upgrade. But it should also be used to continuously check the SAP S/4HANA compliance of customer specific coding in development projects on SAP S/4HANA. </li>\n<li>Only customers with a valid SAP S/4HANA license are able to download this SAP S/4HANA content.</li>\n</ul>\n<p>Changelog:</p>\n<ul>\n<li>Patchlevel 16:</li>\n<ul>\n<li>Automatic calculation of deletions. As of this patch level the Simplification Database also contains all main objects (R3TR) that have been deleted between two consecutive SAP S/4HANA releases, even if the deletion is not related to any Simplification Item or major functional or technical change (e.g. deletions of unused / orphaned objects).</li>\n</ul>\n<li>Patchlevel 17:</li>\n<ul>\n<li>Custom code content for various Simplification Items (especially related to compatibility scope) has been added or updated. Focus of the updates is SAP S/4HANA 2023 initial shipment.</li>\n<li>Automatic calculation of deletions now also considers objects that have been deleted between SAP ERP EHP8 and SAP S/4HANA, that are not covered by other specific Simplification Items.</li>\n</ul>\n<li>Patchlevel 18:</li>\n<ul>\n<li>Custom code content for various Simplification Items has been added or updated. Focus of the updates is SAP S/4HANA 2023 FPS1.</li>\n<li>Improvements to the automatic calculation of deletions. This will reduce fealse positives related to SAP note 2296016 (e.g. if an object did exist in SAP ERP, was deleted in early SAP S/4HANA versions, but reintroduced again in higher SAP S/4HANA versions, this is no longer considered as a deletion) as well as reduce the overlap of findings for SAP note 2296016 with findings for other Simplification Items. Also FI/CO tables (see SAP note 3414643) that have technically been deleted, but are covered by compatibility views are no longer considered as a deletion.</li>\n</ul>\n</ul>\n<p>In case of</p>\n<ul>\n<li>issues with importing the Simplification Database content into your system please open an incident on BC-DWB-CEX.</li>\n<li>general questions related to the Simplification Database Content please open an incident on CA-TRS-TDBC.</li>\n<li>questions on individual custom code SAP notes please open an incident on the application component of the respective SAP note.</li>\n</ul>", "noteVersion": 37}, {"note": "2267782", "noteTitle": "2267782 - S4TWL - Occupational Health", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Occupational Health, EHS-HEA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With <em>SAP EHS Management</em> as part of <em>SAP ERP</em>, customers can use the <em>Occupational Health</em> (EHS-HEA) function to support general employee occupational healthcare in their company.</p>\n<p>In <em>SAP S/4HANA</em> this solution is no longer available.</p>\n<p>For <em>SAP S/4HANA</em> 1709 and higher the add-on solution <em>SAP EHS Management for SAP S/4HANA, occupational health</em> is available to re-establish this <em>Occupational Health</em> function.</p>\n<p><strong>Business Process related information</strong></p>\n<p><em>Occupational Health</em> (EHS-HEA) is not available in <em>SAP S/4HANA</em>.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"191\">\n<p>Transactions not available in SAP S/4HANA on-premise</p>\n</td>\n<td valign=\"top\" width=\"404\">\n<p>EHSAMBTAET,EHSASSIGN,EHSASSLTA,EHSASSPERS,</p>\n<p>EHSBC02,EHSBP11,EHSBP12,EHSBP13,EHSBP31,EHSBP32,EHSBP33,EHSBP41,EHSBP42,EHSBP43,EHSBP51,EHSBP52,EHSBP53,</p>\n<p>EHSCALPROX,EHSCBER,EHSCDCT, EHSCMAS,EHSDATIMP,EHSDIAGSL,EHSEVAL00,EHSEXIST0,</p>\n<p>EHSH_C_NR_EXA_C,EHSH_C_NR_NEWPE,EHSH_C_NR_RECN,EHSH_C_NR_VAC_I,EHSH_D_CALPHYS,EHSH_D_PCP,</p>\n<p>EHSMQUEST,EHSMQUEST01,EHSPERSHC1,EHSPP01,EHSPRANZ,EHSPRLOE,EHSQCATOH,EHSQEVAL01,EHSQUESTOH,</p>\n<p> EHSSCAN,EHSSERV,EHSSERV01,EHSSERV11,EHSSERV30,EHSSER50,EHSSTRU00,EHSSUGGP,EHSTERM01,</p>\n<p>EHSVA02,EHSVU01,EHSVU01_SELSCREEN,EHSVU11,EHSVU21,EHSVU31</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>For <em>S/4HANA</em> 1709, the Repeatable Customer Solution \"SAP EHS Management for SAP S/4HANA, occupational health\" is available as a replacement. This add-on is technically and from a functional point of view equivalent to the deprecated <em>Occupational Health</em> (EHS-HEA) function.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<div class=\"WordSection1\">\n<ul>\n<li>If you want to remove the <em>Occupational Health </em>(EHS-HEA) function from your system, ...</li>\n<ul>\n<li>... this is done automatically by the <em>S/4HANA</em> system conversion (for target release <em>S/4HANA</em> 1610 and below)</li>\n<li>... just confirm the pre-check error during the conversion check (for target release <em>S/4HANA </em>1709 and higher)</li>\n</ul>\n<li>If you want to continue using your existing <em>Occupational Health</em> functionality in <em>S/4HANA</em>, make sure <strong>not to convert to <em>SAP S/4HANA</em> 1511 or 1610</strong>.</li>\n<ul>\n<li>These conversions would delete all EHS-HEA objects including all existing EHS-HEA data.</li>\n<li>Instead, include the previously mentioned add-on using the <em>SAP Maintenance Planner</em> into your conversion to <em>SAP S/4HANA</em> 1709 or higher.</li>\n<ul>\n<li>If you don't do so and install the add-on after the conversion, your existing data for <em>Occupational Health</em> will get lost!</li>\n</ul>\n</ul>\n</ul>\n<p>If you proceed as described, all your <em>Occupational Health</em> data, transactions and processes will stay untouched. For further details see the referenced notes.</p>\n</div>", "noteVersion": 6}, {"note": "2217206", "noteTitle": "2217206 - SAP S/4 HANA Simplification Item: Industrial Hygiene and Safety (EHS-IHS) and SAP Environmental Compliance (EC) interfaces", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">With <em><span arial',sans-serif;\"=\"\">SAP EHS Management</span></em> as part of <em><span arial',sans-serif;\"=\"\">SAP ERP</span></em>, you can use the <em><span arial',sans-serif;\"=\"\">Industrial Hygiene and Safety</span></em> (EHS-IHS) function. This function and the corresponding interfaces for <em><span arial',sans-serif;\"=\"\">SAP Environmental Compliance</span></em> (EC) are no longer supported in <em>SAP S/4HANA</em>.<br/>For this reason, you need to check if you use development objects belonging to this function in your custom code prior to converting to <em><span arial',sans-serif;\"=\"\">SAP S/4HANA</span></em>. If so, you need to adapt your code.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span ar-sa;\"=\"\" black;=\"\" calibri','sans-serif';=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><span ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">SAP S/4HANA simplification,  Industrial Hygiene and Safety, EHS-IHS, SAP Environmental Compliance Interfaces</span></span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span arial',sans-serif;\"=\"\">SAP Note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a> provides information about how you can use the custom code check tool to analyze if you are using any ABAP development objects in your custom code that are no longer supported.</span></p>\n<p><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">If you are using any development objects that are no longer supported, adapt your coding to ensure that these objects are no longer included in your custom code. <br/>If you need some of the development objects that are no longer supported, you can, for example, copy these objects into your customer namespace and adapt your custom code to use the copied versions.</span></p>\n<p><strong>Note:</strong> Due to technical reasons, the development objects of the <em>Industrial Hygiene and Safety</em> (EHS-IHS) function are combined with those of the <em>Occupational Health</em> (EHS-HEA) function. Therefore, the check will also find custom code that uses development objects of the EHS-HEA function as well. For more information about <em>Occupational Health</em>, see SAP Note <a href=\"/notes/2217205\" target=\"_blank\">2217205</a>.</p>", "noteVersion": 3}]}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Decide if you want to continue using your existing Occupational Health functionality or to remove it. If you want to continue using Occupational Health functionality minimum target release must be SAP S/4HANA 1709."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}