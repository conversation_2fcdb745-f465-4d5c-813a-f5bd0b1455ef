{"guid": "901B0E6D3EE91ED7BEBABD4BF3BD20D8", "sitemId": "SI7_FIN_GL", "sitemTitle": "S4TWL - Manual Accruals (Accrual Engine)", "note": 2582883, "noteTitle": "2582883 - S4TWL - Manual Accruals (Accrual Engine)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ACE, ACAC.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note is only relevant if you are using the application <em>Manual Accruals</em>: The Manual Accruals application is based in the reuse tool Accrual Engine.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Business Value</strong></strong></p>\n<p>With release S/4HANA 1809, a new Accrual Engine has been developed. The new Accrual Engine is called <em>S/4HANA Accrual Engine</em>.</p>\n<p>The S/4HANA Accrual Engine is seamlessly integrated with the General Ledger. The following features are available in the S/4HANA Accrual Engine:</p>\n<ul>\n<li>The Accrual Engine supports all currencies of the General Ledger.</li>\n<li>Postings can be performed on ledger level instead of only accounting principle level.</li>\n<li>Complex postings are supported, for example the posting of exchange rate differences: An accrual posting can now contain more than two line items.</li>\n<li>The number of database tables has been reduced. For example the original document of the accrual postings is now contained in the Universal Journal Entry, table ACDOCA.</li>\n</ul>\n<p><strong><strong><span><strong>Description</strong> </span></strong></strong></p>\n<p>The old Accrual Engine still exists and is still being used by applications like</p>\n<ul>\n<li>Accruals For Rights Management</li>\n<li>Revenue Recognition For License Sales</li>\n<li>Accruals For License Acqusitions</li>\n<li>Accruals For Real Estates</li>\n</ul>\n<p>But the application Manual Accruals is the first application that has been adopted to the new S/4HANA Accrual Engine:<br/>After upgrading to release S/4HANA 1809 the application Manual Accruals is only available based on the S/4HANA Accrual Engine.</p>\n<p>As a consequence, after upgrading to release S/4HANA 1809 you need to migrate the content of the old database tables of the old Accrual Engine into the new tables of the S/4HANA Accrual Engine. This affects customizing tables as well as transactional tables of the Accrual Engine.</p>\n<p><strong><strong><span><span><span><strong>Required Actions</strong></span></span></span></strong></strong></p>\n<p>The migration of the Accrual Engine data can be performed after you migrated the General Ledger data to S/4HANA. <br/>Mandatory application configuration steps related in the IMG (transaction SPRO):</p>\n<ol>\n<li>Applying migration relevant SAP Notes mentioned in step 3 of SAP Note 2795791.</li>\n</ol><ol start=\"2\">\n<li>As a first step you migrate the customizing of the Accrual Engine. <br/>You will find the corresponding IMG activity in the following IMG folder:<br/>Conversion of Accounting to SAP S/4HANA -&gt; Preparations and Migration of Customizing -&gt; Preparations and Migration of Customizing for Accrual Engine.<br/>In this folder you also need to create a mass data project. This project is needed for the migration of the transactional data of the Accrual Engine: The transactional data consist of Accrual Engine postings (table ACEPSOIT) and accrual object data (tables ACEOBJ, ACEDS_... and ACEPS_...).<br/>Both, the migrated customizing and the mass data project creation are recorded in a customizing transport. <br/><br/></li>\n<li>Afterwards you perform the migration of the transactional data of the Accrual Engine by executing the mass data project:<br/>You will find the corresponding IMG activity in the following IMG folder: <br/>Conversion of Accounting to SAP S/4HANA -&gt; Activities after Migration -&gt; Migration of Accrual Engine Data.<br/><br/></li>\n<li>After the customizing transport was imported in the productive system, you need to execute the mass data project in the productive system also. <br/>Note that you do not need to perform the migration of the Accrual Engine customizing in the productive system: The migrated customizing was imported by the customizing transport. Only the migration of the transactional data needs to be performed in each system and client separately.</li>\n</ol>\n<p>The migration of the Accrual Engine does not require a system down time: You can perform this migration even if the system is already being used productively: The only restriction is that activities that affect the Accrual Engine data, like accrual postings, creation or change of accrual objects is not possible as long as you did not finish the Accrual Engine migration.</p>\n<p><strong><strong><strong>How to Determine Relevancy</strong></strong></strong></p>\n<p>You can easily check wether you are using the application Manual Accruals or not: If the database table ACEOBJ contains entries that fullfill the condition COMP = 'ACAC', then you need to perform the above mentioned migration steps. Exceptional case: If the entries in this table are obsolete from a business point of view, then you do not need to perform the migration of the Accrual Engine.</p>\n<p><strong>Further Information</strong></p>\n<p>The migration process, new features and incompatible changes of Manual Accruals in S/4HANA 1809 are described in the attached presentations.</p>\n<p>These presentations are also available as pdf document. You can access it using the following URL:</p>\n<p><a href=\"https://www.sap.com/documents/2019/07/d010dfa6-577d-0010-87a3-c30de2ffd8ff.html\" target=\"_blank\">https://www.sap.com/documents/2019/07/d010dfa6-577d-0010-87a3-c30de2ffd8ff.html</a></p>", "noteVersion": 6, "refer_note": [{"note": "2814246", "noteTitle": "2814246 - Manual Accruals: SAP Notes need to be applied for S/4HANA On Premise 1809 SP0 to SP2", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>After the standard delivery of Manual Accruals in S/4HANA On Premise 1809, many corrections and new functionalities have been continuously delivered. To avoid possible program error and have these new functionalities, applying below SAP Notes is highly recommended. These Notes are collected up to July 11th, 2019, and applicable to S/4HANA On Premise 1809 SP0 to SP2.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Accrual Engine, Manual Accruals, Corrections, Release, Archiving, ACAC, ACE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Some program error may happen when using the manual accruals.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>1. Below notes contain corrections for porgam error and small updates fo new functions. Applying below notes are highly recommended.</p>\n<p>2696406        Reversal posting with incorrect amount</p>\n<p>2698232        Manual Accruals: Invaid Subroutines in POAC Tree UI</p>\n<p>2706556        Review&amp;Approval Accruals:Adjustment tolerance check</p>\n<p>2708191        Manual Accurals: Disorder Message Displays for a Selected Entry</p>\n<p>2708772        Accrual Engine SP1: attachment message is not correct in worklist</p>\n<p>2708850        Manual Accruals: Fix the bug in the buffer class.</p>\n<p>2709719        BugFix: Opening posting happened when item has activity 'Delete'</p>\n<p>2709721        BugFix: Wrong error message(ACAC 013) when create accrual object which external number is assigned</p>\n<p>2709756        BugFix: Attachment inconsistency between tree UI and review/approval worklist</p>\n<p>2710579        Accrual Engine SP01: Admin information can't be reset if accrual object is adjusted before</p>\n<p>2710992        Manual Accruals: Wrong Rounding for Amounts</p>\n<p>2711569        BugFix: proprosal changed by/at/on empty for actual accrual item</p>\n<p>2715707        Accrual Engine: The message long text of message ACE_S4 015 contains a wrong link</p>\n<p>2718213        Dump in ACE when leading ledger is not found</p>\n<p>2721338        BugFix: DB save error in parallel uploading by ACACTREE02</p>\n<p>2722000        Accrual Engine: Accruals can be re-proposed even it was posted</p>\n<p>2726322        Accrual Engine: Schedule manager selection is not correct</p>\n<p>2727387        Manual Accrual: Add Test Run Mode for Manual Accruals Upload</p>\n<p>2727505        S/4HANA Accrual Engine: Incorrect reversal postings</p>\n<p>2729292        Total accrual amounts are displayed with incorrect decimal places</p>\n<p>2736262        Accrual Engine: Make Threshold Variant for Review Optional</p>\n<p>2738270        Manual Accrual: Enable Exporting Error Message and Showing Message Together</p>\n<p>2738342        Accrual Engine: Accrual Tree UI Change Document Accrual Object and Accrual Sub-object ( Manual Accrual and Purchase Order Accrual )</p>\n<p>2743950        Manual Accruals: Failed to Uplode Accrual Objects that Contains 0.07 in Spreadsheet</p>\n<p>2744437        Accrual Engine: Error message F5A 460 \"Invalid document reference...\"</p>\n<p>2748438        BugFix: Save successfully with error messages</p>\n<p>2750354        Account assignment missing for BS line item</p>\n<p>2750360        Authority check error during posting run</p>\n<p>2750503        Shift Operator for derived item types</p>\n<p>2754251        ACE calculation of balances based on line items not BCF</p>\n<p>2755562        S/4HANA Accrual Engine: Successor for Program ACAC_DATA_TRANSFER_EXAMPLE</p>\n<p>2756806        Accrual object missing in coding block</p>\n<p>2757334        ACE: Posted totals tab has incorrect currency reference</p>\n<p>2757361        Accrual object missing in coding block: DDIC changes</p>\n<p>2761802        Accrual Engine: Attachment in Review Application</p>\n<p>2761813        BugFix: Remove reset message method in DELETE DEACTIVATE SUSPEND</p>\n<p>2762698        ACE: Account determination based on representative ledger</p>\n<p>2763520        Accrual Engine: File upload field length</p>\n<p>2766065        Missing fields in ACEPOSTINGRUN after upgrade to S4HANA 1809</p>\n<p>2769936        ACE: BAdI to change journal entry before posting</p>\n<p>2769960        BugFix: Wrong message when edit archived accrual object</p>\n<p>2773076        BugFix: Dump on Tree UI when double click on item ALV column header</p>\n<p>2773803        Accrual Engine Customizing: Add Derived Check to Check All IMG Node</p>\n<p>2774006        BAdI BADI_ACE_DOCUMENT_ACCDET_CUST is not called</p>\n<p>2774007        Shift operator for derived item types: wrong result for leap years</p>\n<p>2774486        BufFix: Adjustment Amount check BAdI</p>\n<p>2777191        Reduce number of customizing table entries for account determination</p>\n<p>2780168        ACE Posting Run: no Result from view FACRARUN</p>\n<p>2780238        Manual Accrual: BAdI for modifing the uploaded content when create or update new accrual objects</p>\n<p>2781012        Accrual Engine: Negative posting doesn't work</p>\n<p>2781283        ACAC_D_TRANS_EXAMPLE_S4: Accrual object created several times</p>\n<p>2783614        Manual Accruals: Key Date is Not Considered for Opening Postings</p>\n<p>2783833        Accruals engine: Shift Operator S not calculating values</p>\n<p>2784221        Accrual Engine: Change History Fix for Accrual Object Batch Update</p>\n<p>2785976        BADI_ACE_MDO_CHECK_CUST missing header and assignment information</p>\n<p>2786034        Dump in Class CL_ACE_MDO_DBBUF_ACCTASSGMTS</p>\n<p>2786613        Dump in upload accruals: Uncaught Exception in CL_ACE_MDO_DBBUF_SUBOBJ</p>\n<p>2786978        Review Accrual: Total Posted Accrual Amount till current Period</p>\n<p>2787291        Accrual amounts in additional G/L currencies are not calculated by the Accrual Engine</p>\n<p>2788357        Accrual can be re-proposed even posting has happened</p>\n<p>2788787        Accrual Engine: missing activity information in BADI_ACE_MDO_CHECK_CUST</p>\n<p>2788788        Accrual Engine: missing posting date in BAdI BADI_ACE_DOCUMENT_ACCDET_CUST</p>\n<p>2788789        Accrual Engine: Duplicate entries after implementing BADI_ACE_DOCUMENT_SCHEMA_CUST</p>\n<p>2789164        Accrual Engine: Company Code currency is not filled</p>\n<p>2789226        Accrual Customizing Checks for Zero Amount Configuration</p>\n<p>2790663        ACE: Posted accrual amounts are missing when displaying the accrual object</p>\n<p>2790910        Accrual Engine: document number skipped during excel upload</p>\n<p>2793328        Accrual Engine. Individual documents</p>\n<p>2793963        Accrual Engine: Missing document type during posting</p>\n<p>2794455        Reverse Accruals Postings when Deleting an Accrual Subobject</p>\n<p>2794458        Review Periodic Accruals for Cost Object: Selection Screen Enhancement</p>\n<p>2795569        Simulate Future Accruals Postings</p>\n<p>2796767        Accruals Engine: Dump when posting</p>\n<p>2797827        Accrual Engine Tree UI: Item ALV Variant</p>\n<p>2798511        Accrual Engine: Document type determination error</p>\n<p>2798941        Attachment list button in GOS icon</p>\n<p>2799435        Accrual Engine: Duplicated Structure Include</p>\n<p>2799474        Check Fiscal Year in Change Mode Only</p>\n<p>2799541        Accrual engine: dump when posting</p>\n<p>2799665        Accrual Engine: Phasing Balance operator</p>\n<p>2800078        Accrual Engine: Postings after premature finish date</p>\n<p>2800148        New field ACRVALDAT in tables BSEG and ACDOCA</p>\n<p>2800758        ACAC_D_TRANS_EXAMPLE_S4: Dump in ABAP method CREATE_ITEMS_PER_LEDGER</p>\n<p>2802072        Disable transaction FB08 for Accrual Engine postings</p>\n<p>2802464        Some ABAP methods are missing</p>\n<p>2802466        Preparations for Accrual Engine Utilization Process: Some program corrections - incl. new field Period of Performance</p>\n<p>2803804        Account Assignment Enhancement</p>\n<p>2. Below notes contain some big new functionalities, including utilization posing for accruals, release posting for accruals, legacy data transfer for manual accruals and accruals archiving. These notes are not mandatory. If below notes are applied, retesting is required before productive.</p>\n<p>2800604        Downport 1909 to 1809 - Objects which cannot be implemented with the SAP Note Assistant</p>\n<p>2800607        Downport 1909 to 1809 - Objects implemented by SAP Note Assistant</p>\n<p>2810595        Downport 1909 to 1809: some additional bug fixes</p>\n<p>2810798        Accrual Engine: Manual input of document type</p>\n<p>2813621        ACE: Finance Closing Cockpit for Job Service</p>\n<p> </p>", "noteVersion": 5}, {"note": "2582884", "noteTitle": "2582884 - Manual Accruals (Accrual Engine): Incompatible Changes in Release S/4HANA 1809", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using a SAP ERP system and intend to perform the upgrade-like conversion to S/4 HANA.</p>\n<p>The custom code check shows customer objects that are using objects of the Accrual Engine: <br/><br/><strong>These check results are only relevant for you if you are using the application <span>Manual Accruals</span>! </strong></p>\n<p>The application Manual Accruals is based on the Accrual Engine.<br/>In release S/4HANA 1809 a new Accrual Engine was developed. It is called S/4HANA Accrual Engine.</p>\n<p>From release 1809 onwards, the application Manual Accruals will be based on the new S/4HANA Accrual Engine.<br/>Note: The old Accrual Engine is still being used by some other applications like Real Estate Management. So the old Accrual Engine is not obsolete. But the application Manual Accruals is available only for the new S/4HANA Accrual Engine from release 1809 onwards.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SI_ACCRUAL_ENGINE_RW.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The S/4HANA Accrual Engine</p>\n<ul>\n<li>uses new database tables and new source code compared to the old Accrual Engine and it</li>\n<li>contains some incompatible changes.</li>\n</ul>\n<p>If you have developed your own programs or other repository objects for Manual Accruals that are using the old Accrual Engine objects, adoption effort will likely be required to replace the usage of those objects.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Read and write access to the replaced tables must be adopted to the new tables. The affected tables are contained in the piece list SI_ACCRUAL_ENGINE_RW.<br/>Access to these tables only needs to be replaced if database records are affected that belong to <em>Manual Accruals</em>. Other applications like <em>Leasing</em> or <em>Real Estate Management</em> are not affected: For such applications the read and write access is still allowed: For example access to table TACE_COMBINATION only needs to be replaced if TACE_COMBINATION-COMP = 'ACAC'.</p>\n<p>The list of affected tables and a detailed description of the change is given in the power point presentation that is attached to note <a href=\"/notes/2582883\" target=\"_blank\">2582883</a>.</p>", "noteVersion": 2, "refer_note": [{"note": "2582883", "noteTitle": "2582883 - S4TWL - Manual Accruals (Accrual Engine)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ACE, ACAC.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note is only relevant if you are using the application <em>Manual Accruals</em>: The Manual Accruals application is based in the reuse tool Accrual Engine.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Business Value</strong></strong></p>\n<p>With release S/4HANA 1809, a new Accrual Engine has been developed. The new Accrual Engine is called <em>S/4HANA Accrual Engine</em>.</p>\n<p>The S/4HANA Accrual Engine is seamlessly integrated with the General Ledger. The following features are available in the S/4HANA Accrual Engine:</p>\n<ul>\n<li>The Accrual Engine supports all currencies of the General Ledger.</li>\n<li>Postings can be performed on ledger level instead of only accounting principle level.</li>\n<li>Complex postings are supported, for example the posting of exchange rate differences: An accrual posting can now contain more than two line items.</li>\n<li>The number of database tables has been reduced. For example the original document of the accrual postings is now contained in the Universal Journal Entry, table ACDOCA.</li>\n</ul>\n<p><strong><strong><span><strong>Description</strong> </span></strong></strong></p>\n<p>The old Accrual Engine still exists and is still being used by applications like</p>\n<ul>\n<li>Accruals For Rights Management</li>\n<li>Revenue Recognition For License Sales</li>\n<li>Accruals For License Acqusitions</li>\n<li>Accruals For Real Estates</li>\n</ul>\n<p>But the application Manual Accruals is the first application that has been adopted to the new S/4HANA Accrual Engine:<br/>After upgrading to release S/4HANA 1809 the application Manual Accruals is only available based on the S/4HANA Accrual Engine.</p>\n<p>As a consequence, after upgrading to release S/4HANA 1809 you need to migrate the content of the old database tables of the old Accrual Engine into the new tables of the S/4HANA Accrual Engine. This affects customizing tables as well as transactional tables of the Accrual Engine.</p>\n<p><strong><strong><span><span><span><strong>Required Actions</strong></span></span></span></strong></strong></p>\n<p>The migration of the Accrual Engine data can be performed after you migrated the General Ledger data to S/4HANA. <br/>Mandatory application configuration steps related in the IMG (transaction SPRO):</p>\n<ol>\n<li>Applying migration relevant SAP Notes mentioned in step 3 of SAP Note 2795791.</li>\n</ol><ol start=\"2\">\n<li>As a first step you migrate the customizing of the Accrual Engine. <br/>You will find the corresponding IMG activity in the following IMG folder:<br/>Conversion of Accounting to SAP S/4HANA -&gt; Preparations and Migration of Customizing -&gt; Preparations and Migration of Customizing for Accrual Engine.<br/>In this folder you also need to create a mass data project. This project is needed for the migration of the transactional data of the Accrual Engine: The transactional data consist of Accrual Engine postings (table ACEPSOIT) and accrual object data (tables ACEOBJ, ACEDS_... and ACEPS_...).<br/>Both, the migrated customizing and the mass data project creation are recorded in a customizing transport. <br/><br/></li>\n<li>Afterwards you perform the migration of the transactional data of the Accrual Engine by executing the mass data project:<br/>You will find the corresponding IMG activity in the following IMG folder: <br/>Conversion of Accounting to SAP S/4HANA -&gt; Activities after Migration -&gt; Migration of Accrual Engine Data.<br/><br/></li>\n<li>After the customizing transport was imported in the productive system, you need to execute the mass data project in the productive system also. <br/>Note that you do not need to perform the migration of the Accrual Engine customizing in the productive system: The migrated customizing was imported by the customizing transport. Only the migration of the transactional data needs to be performed in each system and client separately.</li>\n</ol>\n<p>The migration of the Accrual Engine does not require a system down time: You can perform this migration even if the system is already being used productively: The only restriction is that activities that affect the Accrual Engine data, like accrual postings, creation or change of accrual objects is not possible as long as you did not finish the Accrual Engine migration.</p>\n<p><strong><strong><strong>How to Determine Relevancy</strong></strong></strong></p>\n<p>You can easily check wether you are using the application Manual Accruals or not: If the database table ACEOBJ contains entries that fullfill the condition COMP = 'ACAC', then you need to perform the above mentioned migration steps. Exceptional case: If the entries in this table are obsolete from a business point of view, then you do not need to perform the migration of the Accrual Engine.</p>\n<p><strong>Further Information</strong></p>\n<p>The migration process, new features and incompatible changes of Manual Accruals in S/4HANA 1809 are described in the attached presentations.</p>\n<p>These presentations are also available as pdf document. You can access it using the following URL:</p>\n<p><a href=\"https://www.sap.com/documents/2019/07/d010dfa6-577d-0010-87a3-c30de2ffd8ff.html\" target=\"_blank\">https://www.sap.com/documents/2019/07/d010dfa6-577d-0010-87a3-c30de2ffd8ff.html</a></p>", "noteVersion": 6}]}, {"note": "2795791", "noteTitle": "2795791 - SAP Notes to be applied before migrating Manual Accruals to S/4HANA Accrual Engine in S/4 HANA On Premise 1809", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When migrating of Manual Accruals to S/4HANA Accrual Engine, some errors happened.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Accrual Engine, Migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Some notes related to accrual engine haven't been applied. This note is for S/4 HANA On Premise 1809 SP0 to SP2.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please follow the steps below to redo your migration.</p>\n<p>1. If you have migrated transactional data, please reset all your migrated transactional data. Using T-Code ACEMIGIMG. Open customizing activity Run Project for Migrating Transactional Data of Accrual Engine. If error message says migration project can't be reset due to status, you can change status by using customizing activity Set Status of Mass Data Project to Completed.</p>\n<p>2. If you have migrated customizing data for accrual engine, please reset all your migrated customizing data.  Using T-Code ACEMIGIMG. Open customizing activity Migrate Customizing for Accrual Engine. Select reset to delete all migrated customizing.</p>\n<p>3. Then you need to check if you have applied below notes to your system. Please apply below notes to your system.</p>\n<p>2694471 Migration Pre-check: Check status 'To Be Archived'</p>\n<p>2711023 Customizing Migration Simplify for Accrual Engine</p>\n<p>2730864 Accrual Migration: AWTYP is empty but FI documents exist</p>\n<p>2731382 Accrual Engine Migration: Error in Accrual Item Migration When Target Ledger Group is Unassgined</p>\n<p>2733604 Fix Customizing Migration of Accrual Item Type Posting Table for Accrual Engine</p>\n<p>2741658 ACE Migration: multiple currency enhancement</p>\n<p>2743713 Transaction ACE_MIGRATE_CUST: Message ACE_MIG 005 \"There are no appl. components to be migrated\"</p>\n<p>2743975 ACE Migration: After check for posting migration</p>\n<p>2745182 Accrual Engine Migration: Fix Note 2711023 Activation Error</p>\n<p>2755911 Accrual Engine migration: Fill additional fields in table ACDOCA: TSL and RTCUR</p>\n<p>2774945 Remove Accrual Engine Migration Finished Flag After Remove All S/4 Customizing</p>\n<p>2778646 ACE Migration: Accrual Item Type Calculation</p>\n<p>2779112 Add Precheck for TACE_COMBINATION</p>\n<p>2781349 Accruals Migration Failure When the Accounting Principle is Empty</p>\n<p>2781954 ACE Posting Migration: No empty account principle configured</p>\n<p>2785101 Accrual Engine Migration: Combination of Item Type and Ledger Group of Migrated Accrual Objects doesn't Exist</p>\n<p>2785488 ACE Posting Migration: Ledger Assigning</p>\n<p>2786518 ACE Posting Migration: Check after Migration for ACEPSOIT</p>\n<p>2788992 Accrual Migration: Only reset migrated accrual objects</p>\n<p>2792290 Accrual Engine: Dump when reversing migrated line items</p>\n<p>2796715 Accrual Engine Migration: Empty Item Type</p>\n<p>2754251 ACE calculation of balances based on line items not BCF</p>\n<p>2806000 Accrual Engine Customizing Migration Empty Ledger Group Issue</p>\n<p>2796117 Accrual Migration: additional currency types</p>\n<p>2796124 Accrual Engine Migration: afte check issue for table ACEDSOH</p>\n<p>2817707 ACE Migration: sub ojbect migration has duplicate key dump</p>\n<p>2820683 ACE Migration: Item Type is not enter</p>\n<p>4. Redo customizing migration for accrual engine. Using T-Code ACEMIGIMG. Open customizing activity Migrate Customizing for Accrual Engine. If some error occurred fix the errors first.</p>\n<p>5. Redo transactional data migration for accrual engine. Using T-Code ACEMIGIMG. Open customizing activity Run Project for Migrating Transactional Data of Accrual Engine.</p>\n<p>6. Please make sure you have implemented migration successfully on Quality system first. The Productive system don't have reset function for transactional data. You can't redo any steps on Productive system.</p>", "noteVersion": 10}], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Perform IMG activities for preparing migration the customizing of the Accrual Engine."}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Perform the migration of the transaction data of the Accrual Engine."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adjust custom code to changed data model."}]}