{"guid": "0050569455E21ED5B3E176783924A09E", "sitemId": "SI2: FIN_MISC_ML", "sitemTitle": "S4TWL - Technical Changes in Material Ledger", "note": 2332591, "noteTitle": "2332591 - S4TWL - Technical Changes in Material Ledger", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ML, Document, CKMB, CKM3, Period Totals, LBKUM, SALK3</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This simplification makes it obligatory to use the Material Ledger (ML) which is now part of the standard and automatically active in all SAP S/4HANA systems.</p>\n<p>Actual Costing (including Actual Cost Component Split) is still optional.</p>\n<p>When an existing SAP system is converted to SAP S/4HANA, the Material Ledger will be activated during the migration process (if not already active). If any additional plants are added at a later point in time, the material ledger has to be activated for those plants (valuation areas) manually via transaction OMX1.</p>\n<p>As a consequence of the close integration of Material Ledger into sFIN processes, further simplification, refactoring, and process redesign has been implemented. This has incompatible effects especially on database table design and therefore direct SQL accesses to the affected tables.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The content of most of the former Material Ledger database tables is now stored in table ACDOCA which allows simpler and faster (HANA optimized) access to the data. The attributes of the ML data model that are relevant for the inventory subledger functionality are now part of table ACDOCA. The former tables are obsolete. Therefore the following tables must not be accessed anymore via SQL statements. In most cases a corresponding access function or class has been provided that must be used instead.</p>\n<ul>\n<li>Period Totals and Prices\r\n<ul>\n<li>CKMLPP “Material Ledger Period Totals Records Quantity”</li>\n<li>CKMLCR “Material Ledger: Period Totals Records Values”</li>\n<li>Access function: CKMS_PERIOD_READ_WITH_ITAB</li>\n</ul>\n</li>\n<li>Material Ledger Document and associated views\r\n<ul>\n<li>MLHD</li>\n<li>MLIT</li>\n<li>MLPP</li>\n<li>MLPPF</li>\n<li>MLCR</li>\n<li>MLCRF</li>\n<li>MLCRP</li>\n<li>MLMST</li>\n<li>Access functions: CKML_F_DOCUMENT_READ_MLHD, CKML_F_DOCUMENT_READ_MLXX</li>\n<li>Further tables (with no access function): MLKEPH, MLPRKEKO, MLPRKEPH</li>\n<li>Obsolete database views: MLXXV, MLREPORT, MLREADST</li>\n</ul>\n</li>\n<li>Index for Accounting Documents for Material\r\n<ul>\n<li>CKMI1</li>\n<li>Obsolete, no further access possible</li>\n</ul>\n</li>\n</ul>\n<p>In addition, some further simplifications have to be taken into account:</p>\n<ul>\n<li>Separate currency customizing of Material Ledger (transactions OMX2 / OMX3) is now mandatory. The Material Ledger acts on the currencies defined for the leading ledger in Financials.<br/>There is no default Material Ledger Type “0000” anymore.<br/>Customizing in Financial applications allows you to assign more than three currency and valuation types as being relevant in your company code. As the Material Ledger still supports only three currency and valuation types, it is no longer allowed to use an ML Type that references currency settings defined in FI or CO (flags “Currency Types from FI” and “Currency Types from CO”). Instead you have to explicitly define the currency and valuation types that are relevant for the Material Ledger.<br/>Steps to be executed:<br/>1. Use transaction OMX2 to define the currency and valuation types that are relevant for the Material Ledger.<br/>2. Then use transaction OMX3 to assign this ML Type to your valuation area.<br/><br/>See also note <a href=\"/notes/2291076\" target=\"_blank\">https://i7p.wdf.sap.corp/sap/support/notes/2291076</a></li>\n<li>Material Price Analysis (transaction CKM3 / CKM3N)<br/>The transaction CKM3/CKM3N was refactored and now provides a simplified and improved view of materials in plants with active Actual Costing. It replaces the former CKM3 view Price Determination Structure.<br/>The former CKM3 Price History view is still available via transaction CKM3PH for all materials (independent of price determination control and active Actual Costing). <br/>All other views formerly offered by CKM3/CKM3N are no longer available.</li>\n<li>For details about new Actual Costing simplification, please check the corresponding upgrade information SAP Note.</li>\n</ul>", "noteVersion": 3, "refer_note": [{"note": "2291076", "noteTitle": "2291076 - Message FML_CUST-010: ML type referencing FI or CO is not allowed", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In one of the following situations you get the error message FML_CUST-010 \"ML type &amp;1 referencing FI or CO is not allowed\":</p>\n<ul>\n<li>\n<div>you try to set Material Ledger productive (transaction CKMSTART)</div>\n</li>\n<li>you assign a ML Type to a valuation area (transaction OMX3)</li>\n</ul>\n<p>The ML Type you use is defined with activated flag \"Currency Types from FI\" and / or \"Currency Types from CO\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CKMSTART, OMX1, OMX2, OMX3</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Customizing in Financial application allows you to assign more than three currency and valuation types as being relevant in your company code. As Material Ledger still supports only up to three currency and valuation types, it is not allowed anymore to use a ML Type that references to currency settings defined in FI or CO. Instead you have to define explicitly the currency and valuation types that are relevant for Material Ledger.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Define the currency and valuation types that are relevant for Material Ledger using transaction OMX2.</p>\n<p>Afterwards assign this ML Type to your valuation area using transaction OMX3.</p>", "noteVersion": 2}, {"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19, "refer_note": [{"note": "2241080", "noteTitle": "2241080 - SAP S/4HANA: Content for checking customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to check your customer specific ABAP code via ABAP Test Cockpit (ATC) for compatibility with SAP S/4HANA as described in SAP note <a href=\"/notes/0002190420\" target=\"_blank\">2190420</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Custom Code Check, ABAP Test Cockpit, ATC, Simplification Database, System Conversion, Release Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In order to provide accurate results, ATC requires up to date content describing changes done to SAP objects in SAP S/4HANA (= Simplification Database).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP provides the most recent content for the Simplification Database of SAP S/4HANA as a ZIP file in the Software Center, which you can download and import into your system.</p>\n<p><strong>Downloading the Simplification Database Content</strong></p>\n<ul>\n<li>Go to <a href=\"https://me.sap.com/softwarecenter\" target=\"_blank\">https://me.sap.com/softwarecenter</a></li>\n<li>Search for Component \"CCMSIDB\"</li>\n<li>Download the displayed \"Simplification Database Content\" (most recent version: patch level 18 - creation date: 2024-02-28)</li>\n<ul>\n<li>When using content version 2021-09-28 or newer, please ensure that you have also implemented SAP note 3039646. With this content version the usage detection accuracy for DDIC objects has been improved. But without the corresponding check logic from SAP note 3039646, this content version will lead to an increased number of false positives.</li>\n</ul>\n</ul>\n<p><strong>Importing the Simplification Database Content</strong></p>\n<p>For details how to import the Simplification Database content into your ATC system please refer to the ABAP Platform help:</p>\n<p>   &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/4355509bf8a75f6be10000000a1553f6.html\" target=\"_blank\">Application Development on AS ABAP</a><br/>      &gt; Customer-Specific ABAP Development<br/>        &gt; Custom Code Migration<br/>         &gt; SAP S/4HANA Conversion<br/>           &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/41316510041a4b53974ee74ab6d52512.html\" target=\"_blank\">Simplification Database</a></p>\n<p>When updating the Simplification Database content to the most recent version, please also ensure, that you update the check logic to the most recent version,  by implementing all relevant, new notes from SAP note 2436688. Otherwise you might get wrong check results (e.g. false positives).</p>\n<p>Please be aware, that upon importing the Simplification Database content into your code analysis system you might sometimes get a message \"Details on download of simplification notes: note &lt;note number&gt; incomplete.\" This only means the corresponding SAP note, describing a specific custom code impact is temporarily not accessible as it's being updated by the responsible development area. Or that the connection from your system to SAP service marketplace for note downloads has not been setup. This has no impact on the successful import of the Simplification Database content into your code analysis system. And this will not negatively impact the result of the custom code analysis.</p>\n<p>Though this note download mechanism can cause long import times for the Simplification Database content or even timeouts in systems that are not properly configured for downloading SAP notes. In this case you can implement SAP note 3221402. This adds an option to the Simplification Database upload dialog, to skip the note downloads. The only drawback is, that then the note titles shown next to the ATC results might not be up to date in all cases. But the correct notes are shown anyway.<a href=\"/notes/3221402\" target=\"_blank\" title=\"3221402  - Skip download of SAP Notes during the import of simplification database\"><br/></a></p>\n<p><strong>Additional Information</strong></p>\n<ul>\n<li>The main focus of the Simplification Database Content is to identify the custom code impact of Simplification Items (<a href=\"https://launchpad.support.sap.com/#/sic/overview\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/overview</a>) and other major functional or technical changes in SAP S/4HANA.</li>\n<li>Please be aware that code checks based on the SIDB content do not replace the need to properly test relevant custom code during maintenance events, in order to see if there is any impact from syntactical or semantical changes done on SAP side.</li>\n<li>For prioritization of issues found by the SAP S/4HANA ATC checks, please take the category of the findings into consideration. Especially the findings under the \"Non-strategic...\" categories indicate usage of objects which will become obsolete only at a later point in time. The ATC errors of this category only serve as a reminder, that such objects will become obsolete in future releases. For example compatibility pack functionality (see SAP note 2269324 for details). So these findings do not necessarily have to be fixed already in earlier SAP S/4HANA releases. Please refer to the individual notes shown along the \"Non-strategic...\" findings for details.</li>\n<li>The Simplification Database content covers all available SAP S/4HANA releases. This e.g. allows you, as preparation for your SAP S/4HANA conversion project, to scan your custom code against different SAP S/4HANA target releases with the same Simplification Database content. Which is helpful if the exact target version of your SAP S/4HANA conversion project is not yet decided on.</li>\n<li>It's recommended to use this content for ATC checks in preparation of a SAP S/4HANA system conversion or release upgrade. But it should also be used to continuously check the SAP S/4HANA compliance of customer specific coding in development projects on SAP S/4HANA. </li>\n<li>Only customers with a valid SAP S/4HANA license are able to download this SAP S/4HANA content.</li>\n</ul>\n<p>Changelog:</p>\n<ul>\n<li>Patchlevel 16:</li>\n<ul>\n<li>Automatic calculation of deletions. As of this patch level the Simplification Database also contains all main objects (R3TR) that have been deleted between two consecutive SAP S/4HANA releases, even if the deletion is not related to any Simplification Item or major functional or technical change (e.g. deletions of unused / orphaned objects).</li>\n</ul>\n<li>Patchlevel 17:</li>\n<ul>\n<li>Custom code content for various Simplification Items (especially related to compatibility scope) has been added or updated. Focus of the updates is SAP S/4HANA 2023 initial shipment.</li>\n<li>Automatic calculation of deletions now also considers objects that have been deleted between SAP ERP EHP8 and SAP S/4HANA, that are not covered by other specific Simplification Items.</li>\n</ul>\n<li>Patchlevel 18:</li>\n<ul>\n<li>Custom code content for various Simplification Items has been added or updated. Focus of the updates is SAP S/4HANA 2023 FPS1.</li>\n<li>Improvements to the automatic calculation of deletions. This will reduce fealse positives related to SAP note 2296016 (e.g. if an object did exist in SAP ERP, was deleted in early SAP S/4HANA versions, but reintroduced again in higher SAP S/4HANA versions, this is no longer considered as a deletion) as well as reduce the overlap of findings for SAP note 2296016 with findings for other Simplification Items. Also FI/CO tables (see SAP note 3414643) that have technically been deleted, but are covered by compatibility views are no longer considered as a deletion.</li>\n</ul>\n</ul>\n<p>In case of</p>\n<ul>\n<li>issues with importing the Simplification Database content into your system please open an incident on BC-DWB-CEX.</li>\n<li>general questions related to the Simplification Database Content please open an incident on CA-TRS-TDBC.</li>\n<li>questions on individual custom code SAP notes please open an incident on the application component of the respective SAP note.</li>\n</ul>", "noteVersion": 37}, {"note": "2436688", "noteTitle": "2436688 - Recommended SAP Notes for Using S/4HANA Custom Code Checks in ATC or Custom Code Migration App", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using ABAP Test Cockpit (ATC) or the Custom Code Migration Fiori app to perform the S/4HANA custom code checks.</p>\n<p>This SAP Note summarizes recommended SAP Notes for these scenarios.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, custom code analysis, custom code adaptation, system conversion, Notes</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Note Assistant has been updated to its latest version. Therefore, ensure that you have applied the latest version of the following SAP Notes in your system:</p>\n<ul>\n<li>1668882 - Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753)</li>\n<li>2730170 - SCWB/SNOTE Activation processes Methods in DDIC Phase</li>\n<li>2844646 - Ignore the local class definition, implementation, definition deferred, definition load, definition local friends changes</li>\n<li>2910608 - Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition</li>\n<li>2971435 - SNOTE - Delta calculation Issue when ‘mod unit’ positions are changed</li>\n<li>2970782 - Nested interfaces are not deleted by SAP Note Assistant</li>\n<li>3051466 - Note Assistant Made Easy : Revamped Note Assistant</li>\n<li>3200109 - SNOTE - Note Analyzer</li>\n<li>3218983 - Order of nested interfaces is not considered in correction instructions of SAP Notes <strong>   </strong></li>\n<li>3225158 - Nested interfaces are not created by SAP Note Assistant</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Important!</strong> Check whether your system fulfills the prerequisites mentioned above before you process the following steps.</p>\n<p>Update your local/central check system using Note Analyzer by processing the following steps:</p>\n<div title=\"Page 85\"><ol>\n<li><span>Launch transaction SNOTE.</span></li>\n<li><span>In the menu bar, select \"Goto &gt; other tools &gt; Launch Note Analyzer\".</span></li>\n<li><span>Upload the file \"SAPNote_2436688_Central_System.xml\" available as attachment to this note. You can process the notes using the Note Analyzer now.</span></li>\n</ol></div>\n<p>In case of a remote analysis or when using the Fiori app <em>Custom Code Migration</em>, it is also recommended to apply SAP Notes in checked systems (i.e., systems which will be analyzed by Custom Code Migration app or ATC). Update your <strong>checked systems</strong> using Note Analyzer by processing the following steps</p>\n<div title=\"Page 85\"><ol>\n<li><span>Launch transaction SNOTE.</span></li>\n<li><span>In the menu bar, select \"Goto &gt; Other tools &gt; Launch Note Analyzer\".</span></li>\n<li><span>Upload the file \"SAPNote_2436688_Checked_System.xml\" available as attachment to this note. You can process the notes using the Note Analyzer now.</span></li>\n</ol></div>\n<p><strong><em>Note</em></strong></p>\n<p><em>In case you were not able to use SAP Note Analyzer to apply the SAP Notes (as mentioned above), you would need to apply the SAP Notes manually. </em></p>\n<p><em>In the checked system, install also the following SAP Note:</em></p>\n<ol>\n<ul>\n<li><em>2599695 - Custom Code Migration Fiori App: Remote Stubs for the Checked System</em></li>\n<li><em>2888880 - Remote analysis: Bulk determination of method names</em></li>\n<li><em>2889487 - API: Corrections for remote API</em></li>\n<li><em>2742368 - REPOSITORY_ENVIRONMENT_ALL - environment analysis - appends error</em></li>\n</ul>\n</ol>\n<p><em><em>In your local/central check system, apply all SAP Notes mentioned below and all SAP Notes mentioned in SAP Note 2364916 (recommended SAP Notes for using ATC to perform a remote analysis).</em></em></p>\n<p><strong>In all Releases</strong></p>\n<p>Following SAP Notes are updated regularly. Please check whether you have applied the latest version of these SAP Notes:</p>\n<ul>\n<li>2364938 - Downport of the infrastructure of the SAP S/4HANA readiness checks</li>\n<li>2527903 - Remote analysis (for central check system)</li>\n</ul>\n<p><strong>SAP BASIS 7.58 (SP1 or lower)</strong></p>\n<ul>\n<li>3344072 - Custom Code Migration App - Correction bundle for S/4HANA 2023</li>\n</ul>\n<p><strong>SAP BASIS 7.57 (SP3 or lower)</strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n<li>3231871 - Custom Code Migration App - Correction bundle for S/4HANA 2022</li>\n</ul>\n<p><strong>SAP BASIS 7.57 (SP1 or lower)</strong></p>\n<ul>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n</ul>\n<p><strong>SAP BASIS 7.57 (SP0)</strong></p>\n<ul>\n<li>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</li>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n</ul>\n<p><strong>SAP BASIS 7.56 (SP5 or lower)</strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n<li>3107617 - Custom Code Migration App - Correction bundle for S/4HANA 2021</li>\n</ul>\n<p><strong>SAP BASIS 7.56 (SP3 or lower)</strong></p>\n<ul>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.56 (SP2 or lower)</strong></strong></p>\n<ul></ul>\n<ul>\n<li><span>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</span></li>\n<li><span>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition </span></li>\n</ul>\n<p><strong>SAP BASIS 7.56 (SP0)</strong></p>\n<ul>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3109122 - Execution of SAP S/4HANA Custom Code Checks Fails in ATC Developer Scenario</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.55 (SP7 or lower)</strong></strong></strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n<li>2964574 - Custom Code Migration App - Correction bundle for S/4HANA 2020</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.55 (SP5 or lower)</strong></strong></strong><strong><strong><strong>     </strong></strong></strong></p>\n<ul>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.55 (SP4 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</li>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.55 (SP3 or lower)</strong></strong></p>\n<ul>\n<li>3032974 - ATC check 'S/4HANA: Field Length Extension\" reports findings with message type \"Related Type\"</li>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3090106 - Check variant for SAP S/4HANA 2021 custom code checks</li>\n<li>3109122 - Execution of SAP S/4HANA Custom Code Checks Fails in ATC Developer Scenario</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.55 (SP2 or lower)</strong></strong></p>\n<ul>\n<li>3039646 - The ATC Check 'S/4HANA: Search for Usages of Simplified Objects' reports false-positive findings if only DDIC fields are simplified</li>\n<li>3045580 - Web Dynpro support in ATC check 'S/4HANA: Search for Usages of Simplified Objects'</li>\n<li>3057501 - Updated ATC check variants for SAP S/4HANA custom code checks<br/><em>Note: This SAP Note includes the new ATC Check \"S/4HANA: Idoc Check\"</em></li>\n<li>3066710 - Add sub object information to export for SAP Readiness Check 2.0</li>\n<li>3068784 - Exception CX_SY_RANGE_OUT_OF_BOUNDS in class CL_QF_DB_OPS_REPL_TABL</li>\n<li>3073012 - Exception CX_SY_ITAB_LINE_NOT_FOND in class CL_QF_DB_OPS_API_BSEG</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong>SAP BASIS 7.55 (SP1 or lower)</strong></p>\n<ul>\n<li>3027090 - ATC check \"S/4HANA: Field Length Extension\" leads to flickering results</li>\n<li>3026984 - S/4HANA: Search for simplified objects in literals</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.55 (SP0)</strong></strong></strong></strong></p>\n<ul>\n<li>2898180 - ATC check 'S/4HANA: Field Length Extension' reports false-positives for calls with generic parameters to SAP objects</li>\n<li>2942419 - Include ATC check 'S/4HANA: Readiness Check for SAP Queries' to the SAP S/4HANA custom code checks</li>\n<li>2939956 - Additional checks in ATC check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</li>\n<li>2946418 - Correction of runtime error with BSEG Quickfixes</li>\n<li>2971294 - S/4HANA custom code checks: Wrong behavior for multi-purpose fields in check S/4HANA field length extension</li>\n<li>2981370 - More categories considered by ATC check S/4HANA: Search for base tables of ABAP Dictionary views</li>\n<li>2991074 - Quick Fix: SELECTs from KONV with ORDER BY PRIMARY KEY</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.54 (SP9 or lower)</strong></strong></strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.54 (SP8 or lower)</strong></strong></p>\n<ul>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n<li>2809550 - Custom Code Migration App - Correction bundle for S/4HANA 1909</li>\n</ul>\n<p><strong>SAP BASIS 7.54 (SP7 or lower)</strong></p>\n<ul>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.54 (SP6 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.54 (SP5 or lower)</strong></strong></strong></p>\n<ul>\n<li>3032974 - ATC check 'S/4HANA: Field Length Extension\" reports findings with message type \"Related Type\"</li>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3090106 - Check variant for SAP S/4HANA 2021 custom code checks</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.54 (SP4 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>3027090 - ATC check \"S/4HANA: Field Length Extension\" leads to flickering results</li>\n<li>3026984 - S/4HANA: Search for simplified objects in literals</li>\n<li>3039646 - The ATC Check 'S/4HANA: Search for Usages of Simplified Objects' reports false-positive findings if only DDIC fields are simplified</li>\n<li>3045580 - Web Dynpro support in ATC check 'S/4HANA: Search for Usages of Simplified Objects'</li>\n<li>3057501 - Updated ATC check variants for SAP S/4HANA custom code checks<br/><em>Note: This SAP Note includes the new ATC Check \"S/4HANA: Idoc Check\"</em></li>\n<li>3066710 - Add sub object information to export for SAP Readiness Check 2.0</li>\n<li>3068784 - Exception CX_SY_RANGE_OUT_OF_BOUNDS in class CL_QF_DB_OPS_REPL_TABL</li>\n<li>3073012 - Exception CX_SY_ITAB_LINE_NOT_FOND in class CL_QF_DB_OPS_API_BSEG</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.54 (SP3 or lower)</strong></strong></p>\n<ul>\n<li>2925563 - Check variants for S/4HANA custom code checks without field length extensions</li>\n<li>2939956 - Additional checks in ATC check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</li>\n<li>2959341 - Check variant for SAP S/4HANA 2020 custom code checks</li>\n<li>2971294 - S/4HANA custom code checks: Wrong behavior for multi-purpose fields in check S/4HANA field length extension</li>\n<li>2981370 - More categories considered by ATC check S/4HANA: Search for base tables of ABAP Dictionary views</li>\n<li>2991074 - Quick Fix: SELECTs from KONV with ORDER BY PRIMARY KEY</li>\n</ul>\n<p><strong>SAP BASIS 7.54 (SP2 or lower)</strong></p>\n<ul>\n<li>2857006 - SAP S/4HANA: Readiness check for SAP queries</li>\n<li>2879257 - Reducing findings and quick fix for VBRK and VBRP in 'S/4HANA: Search for database operations'</li>\n<li>2896275 - Incorrect finding in test cl_ci_test_for_all_entr_hana</li>\n<li>2898180 - ATC check 'S/4HANA: Field Length Extension' reports false-positives for calls with generic parameters to SAP objects</li>\n<li>2905941 - ATC Check 'S/4HANA: Field Length Extension': Dump CX_SY_EXPORT_BUFFER_NO_MEMORY</li>\n<li>2900798 - Remove modification of shared symbol table</li>\n<li>2901862 - Correction cl_ci_test_no_order_by AMB_SINGLE</li>\n<li>2902053 - Downport of quickfix comments/defect correction for quickfixes</li>\n<li>2907328 - ATC in ADT: Take over results from ATC result browser into the ATC Problems View</li>\n<li>2911030 - Quick fix for syntax errors related to constants declared in include RVVBTYP</li>\n<li>2918372 - S/4HANA Field length extension: ATC ignores pseudo comments</li>\n<li>2918443 - ADT: Changes in function modules cannot be saved</li>\n<li>2919760 - Message code _SYMBNF_ during no-order check</li>\n<li>2923410 - Syntax warnings after applying quick fixes due to the usage of field MANDT in JOIN conditions</li>\n<li>2925515 - SQL analysis in ATC: Join aliases</li>\n<li>2927221 - Improvement of SQL-Quickfixes with alias or host variables</li>\n<li>2942419 - Include ATC check 'S/4HANA: Readiness Check for SAP Queries' to the SAP S/4HANA custom code checks</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.54 (SP1 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>2882569 - Coding created by quick fix for BSEG potentially leads to a runtime exception</li>\n<li>2874255 - False-Positive findings in ATC Check 'S/4HANA: Search for usages of simplified objects'</li>\n<li>2880652 - Check 'S/4HANA: Search for S/4-related syntax errors' shows wrong SAP Notes for findings</li>\n<li>2882279 - Transaction SYCM: Error when uploading the simplification database</li>\n<li>2885753 - S/4HANA: Field Length Extension - Information for findings is missing</li>\n<li>2873049 - ASSERTION_FAILED during check of global class</li>\n<li>2876160 - ATC check CL_CI_TEST_NO_ORDER_BY runs out of memory</li>\n<li>2894687 - Non-deterministic ATC findings with cross-program analysis</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.54 (SP0)</strong></strong></p>\n<ul>\n<li>2824538 - S/4HANA: Field length extension check: Referenced object type and application component are empty</li>\n<li>2806677 - ATC run shows finding 'Quick Fix Failure...'</li>\n<li>2823371 - Missing findings in ATC test cl_ci_test_no_order_by for SELECT statements with COUNT</li>\n<li>2824797 - \"on change\" is not recognized by the no-order check</li>\n<li>2833432 - Reduction of memory consumption in check 'S/4HANA: Field length extensions'</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP11 or lower)</strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n<li>3480456 - ATC result extractor SAP Readiness Check writes correct scope and usage</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.53 (SP10 or lower)</strong></strong></p>\n<ul>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP9 or lower)</strong></p>\n<ul>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.53 (SP8 or lower)</strong></strong></strong></p>\n<ul>\n<li>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.53 (SP7 or lower)</strong></strong></p>\n<ul>\n<li>3032974 - ATC check 'S/4HANA: Field Length Extension\" reports findings with message type \"Related Type\"</li>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3090106 - Check variant for SAP S/4HANA 2021 custom code checks</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.53 (SP6 or lower)</strong></strong></strong></p>\n<ul>\n<li>3027090 - ATC check \"S/4HANA: Field Length Extension\" leads to flickering results</li>\n<li>3026984 - S/4HANA: Search for simplified objects in literals</li>\n<li>3039646 - The ATC Check 'S/4HANA: Search for Usages of Simplified Objects' reports false-positive findings if only DDIC fields are simplified</li>\n<li>3045580 - Web Dynpro support in ATC check 'S/4HANA: Search for Usages of Simplified Objects'</li>\n<li>3057501 - Updated ATC check variants for SAP S/4HANA custom code checks</li>\n<li>3066710 - Add sub object information to export for SAP Readiness Check 2.0</li>\n<li>3068784 - Exception CX_SY_RANGE_OUT_OF_BOUNDS in class CL_QF_DB_OPS_REPL_TABL</li>\n<li>3073012 - Exception CX_SY_ITAB_LINE_NOT_FOND in class CL_QF_DB_OPS_API_BSEG</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP5 or lower)</strong></p>\n<ul>\n<li><em>2939956 - Additional checks in ATC check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</em></li>\n<li>2959341 - Check variant for SAP S/4HANA 2020 custom code checks</li>\n<li>2971294 - S/4HANA custom code checks: Wrong behavior for multi-purpose fields in check S/4HANA field length extension</li>\n<li>2981370 - More categories considered by ATC check S/4HANA: Search for base tables of ABAP Dictionary views</li>\n<li>2991074 - Quick Fix: SELECTs from KONV with ORDER BY PRIMARY KEY</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.53 (SP4 or lower)</strong></strong></p>\n<ul>\n<li>2857006 - SAP S/4HANA: Readiness check for SAP queries</li>\n<li>2879257 - Reducing findings and quick fix for VBRK and VBRP in 'S/4HANA: Search for database operations'</li>\n<li>2894687 - Non-deterministic ATC findings with cross-program analysis</li>\n<li>2896275 - Incorrect finding in test cl_ci_test_for_all_entr_hana</li>\n<li>2898180 - ATC check 'S/4HANA: Field Length Extension' reports false-positives for calls with generic parameters to SAP objects</li>\n<li>2900798 - Remove modification of shared symbol table</li>\n<li>2901862 - Correction cl_ci_test_no_order_by AMB_SINGLE</li>\n<li>2894687 - Non-deterministic ATC findings with cross-program analysis</li>\n<li>2918372 - S/4HANA Field length extension: ATC ignores pseudo comments</li>\n<li>2918443 - ADT: Changes in function modules cannot be saved</li>\n<li>2919760 - Message code _SYMBNF_ during no-order check</li>\n<li>2923410 - Syntax warnings after applying quick fixes due to the usage of field MANDT in JOIN conditions</li>\n<li>2925515 - SQL analysis in ATC: Join aliases</li>\n<li>2925563 - Check variants for S/4HANA custom code checks without field length extensions</li>\n<li>2927221 - Improvement of SQL-Quickfixes with alias or host variables</li>\n<li>2942419 - Include ATC check 'S/4HANA: Readiness Check for SAP Queries' to the SAP S/4HANA custom code checks</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP3 or lower)</strong></p>\n<ul>\n<li>2882569 - Coding created by quick fix for BSEG potentially leads to a runtime exception</li>\n<li>2874255 - False-Positive findings in ATC Check 'S/4HANA: Search for usages of simplified objects'</li>\n<li>2728715 - Custom Code Migration App - Correction bundle for S/4HANA 1809</li>\n<li>2880652 - Check 'S/4HANA: Search for S/4-related syntax errors' shows wrong SAP Notes for findings</li>\n<li>2882279 - Transaction SYCM: Error when uploading the simplification database</li>\n<li>2885753 - S/4HANA: Field Length Extension - Information for findings is missing</li>\n<li>2873049 - ASSERTION_FAILED during check of global class</li>\n<li>2876160 - ATC check CL_CI_TEST_NO_ORDER_BY runs out of memory</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP2 or lower)</strong></p>\n<ul>\n<li>2824538 - S/4HANA: Field length extension check: Referenced object type and application component are empty</li>\n<li>2812556 - Check variant for SAP S/4HANA 1909 custom code checks</li>\n<li>2806677 - ATC run shows finding 'Quick Fix Failure...'</li>\n<li>2725414 - NO_ORDER_BY ATC check does not process target lists in SELECT statements</li>\n<li>2738251 - Quick Fixes for the S/4HANA Custom Code Checks<br/><em><strong>Attention</strong> Consider the manual pre- and post-implementation steps of all dependent notes.<br/><em><strong>Attention</strong> Do not de-implement SAP Note 2738251.</em><br/></em></li>\n<li>2755679 - S/4HANA Custom Code Check 'S/4HANA: Search for usages of simplified objects' does not consider CDS</li>\n<li>2763301 - ATC S4HANA Readiness Checks: CX_SY_MOVE_CAST_ERROR in CL_S4H_QUICKFIX_PROVIDER</li>\n<li>2771060 - ATC S/4HANA ABAP Dictionary Checks - Redirection to dedicated items in Simplification Database patch level 8 and higher</li>\n<li>2768987 - Support field lists for database tables in S/4HANA simplification DB</li>\n<li>2791259 - Enablement of Quickfixes with modern SQL syntax for S/4HANA</li>\n<li>2823371 - Missing findings in ATC test cl_ci_test_no_order_by for SELECT statements with COUNT</li>\n<li>2824797 - \"on change\" is not recognized by the no-order check</li>\n<li>2833432 - Reduction of memory consumption in check 'S/4HANA: Field length extensions'</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP1 or lower)</strong></p>\n<ul>\n<li>2712310 - Quick fix for ambiguous SELECT SINGLE</li>\n</ul>\n<p><strong>SAP BASIS 7.52 (SP13 or lower)</strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n</ul>\n<p><strong>SAP BASIS 7.52 (SP12 or lower)</strong></p>\n<ul>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n</ul>\n<p><strong>SAP BASIS 7.52 (SP11 or lower)</strong></p>\n<ul>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong></strong> 7.52 (SP9 or lower)</strong></p>\n<ul>\n<li>3032974 - ATC check 'S/4HANA: Field Length Extension\" reports findings with message type \"Related Type\"</li>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3090106 - Check variant for SAP S/4HANA 2021 custom code checks</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong></strong></strong> 7.52 (SP8 or lower)</strong></p>\n<ul>\n<li>3027090 - ATC check \"S/4HANA: Field Length Extension\" leads to flickering results</li>\n<li>3026984 - S/4HANA: Search for simplified objects in literals</li>\n<li>3039646 - The ATC Check 'S/4HANA: Search for Usages of Simplified Objects' reports false-positive findings if only DDIC fields are simplified</li>\n<li>3045580 - Web Dynpro support in ATC check 'S/4HANA: Search for Usages of Simplified Objects'</li>\n<li>3057501 - Updated ATC check variants for SAP S/4HANA custom code checks</li>\n<li>3066710 - Add sub object information to export for SAP Readiness Check 2.0</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP7 or lower)</strong></p>\n<ul>\n<li>2939956 - Additional checks in ATC check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</li>\n<li>2959341 - Check variant for SAP S/4HANA 2020 custom code checks</li>\n<li>2971294 - S/4HANA custom code checks: Wrong behavior for multi-purpose fields in check S/4HANA field length extension</li>\n<li>2981370 - More categories considered by ATC check S/4HANA: Search for base tables of ABAP Dictionary views</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.52 (SP6 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>2857006 - SAP S/4HANA: Readiness check for SAP queries</li>\n<li>2894687 - Non-deterministic ATC findings with cross-program analysis</li>\n<li>2896275 - Incorrect finding in test cl_ci_test_for_all_entr_hana</li>\n<li>2900798 - Remove modification of shared symbol table</li>\n<li>2918372 - S/4HANA Field length extension: ATC ignores pseudo comments</li>\n<li>2919760 - Message code _SYMBNF_ during no-order check</li>\n<li>2925515 - SQL analysis in ATC: Join aliases</li>\n<li>2925563 - Check variants for S/4HANA custom code checks without field length extensions</li>\n<li>2942419 - Include ATC check 'S/4HANA: Readiness Check for SAP Queries' to the SAP S/4HANA custom code checks</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP5 or lower)</strong></strong></p>\n<ul>\n<li>2874255 - False-Positive findings in ATC Check 'S/4HANA: Search for usages of simplified objects'</li>\n<li>2879257 - Reducing findings and quick fix for VBRK and VBRP in 'S/4HANA: Search for database operations'</li>\n<li>2880652 - Check 'S/4HANA: Search for S/4-related syntax errors' shows wrong SAP Notes for findings</li>\n<li>2882279 - Transaction SYCM: Error when uploading the simplification database</li>\n<li>2873049 - ASSERTION_FAILED during check of global class</li>\n<li>2876160 - ATC check CL_CI_TEST_NO_ORDER_BY runs out of memory</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP4 or lower)</strong></p>\n<ul>\n<li>2725414 - NO_ORDER_BY ATC check does not process target lists in SELECT statements</li>\n<li>2755679 - S/4HANA Custom Code Check 'S/4HANA: Search for usages of simplified objects' does not consider CDS</li>\n<li>2756171 - New check category for the S/4HANA search DB Operations Check for reading DB Operations</li>\n<li>2771060 - ATC S/4HANA ABAP Dictionary Checks - Redirection to dedicated items in Simplification Database patch level 8 and higher</li>\n<li>2812556 - Check variant for SAP S/4HANA 1909 custom code checks</li>\n<li>2768987 - Support field lists for database tables in S/4HANA simplification DB</li>\n<li>2823371 - Missing findings in ATC test cl_ci_test_no_order_by for SELECT statements with COUNT</li>\n<li>2824797 - \"on change\" is not recognized by the no-order check</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP3 or lower)</strong></p>\n<ul>\n<li>2659194 - Check variant for SAP S/4HANA 1809 custom code checks</li>\n<li>2737924 - Error displaying Simplification Item Category</li>\n<li>2749689 - Endless Loop in Check \"S/4HANA: Search for S/4 related syntax errors\"</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP2 or lower)</strong></strong></p>\n<ul>\n<li>2516160 - CI check \"Objects used in programs (remote)\" does not return all usages or reports \"Symbol ... not found\"</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP1 or lower)</strong></p>\n<ul>\n<li>2502402 - Display application component and referenced object in S/4HANA ATC readiness checks</li>\n<li>2578127 - Pseudo comments for S/4HANA custom code checks</li>\n<li>2577440 - Improvements in S/4HANA custom code check \"S/4HANA: Search for S/4 related syntax errors\"</li>\n<li>2569135 - S/4HANA custom code check \"S/4HANA: Search for S/4 related syntax errors\" reports syntax warnings</li>\n<li>2573527 - S/4HANA custom code check for buffered DB table access</li>\n<li>2522926 - S/4HANA: Search for ABAP Dictionary enhancements check: Suppress message on table with regenerated compatibility view</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP0)</strong></p>\n<ul>\n<li>2503262 - Filter function in cl_ci_test_search_db_ops returns too many findings</li>\n<li>2505885 - Priorities of ATC check for S/4HANA use of simplified objects</li>\n<li>2531490 - Code Inspector variants S4HANA_READINESS: Setting in ABAP Dictionary checks for processing of modified objects</li>\n<li>2487726 - S/4HANA Readiness Checks: Check performance improvement</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP9 or lower)</strong></p>\n<ul>\n<li>2882279 - Transaction SYCM: Error when uploading the simplification database</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP8 or lower)</strong></p>\n<ul>\n<li>2725414 - NO_ORDER_BY ATC check does not process target lists in SELECT statements</li>\n<li>2768987 - Support field lists for database tables in S/4HANA simplification DB</li>\n</ul>\n<p><strong><strong><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP7 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>2749689 - Endless Loop in Check \"S/4HANA: Search for S/4 related syntax errors\"</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP6 or lower)</strong></strong></p>\n<ul>\n<li>2516160 - CI check \"Objects used in programs (remote)\" does not return all usages or reports \"Symbol ... not found\"</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP5 or lower)</strong></p>\n<ul>\n<li>2569135 - S/4HANA custom code check \"S/4HANA: Search for S/4 related syntax errors\" reports syntax warnings</li>\n<li>2573527 - S/4HANA custom code check for buffered DB table access</li>\n<li>2522926- S/4HANA: Search for ABAP Dictionary enhancements check: Suppress message on table with regenerated compatibility view</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP3 or lower)</strong></strong></p>\n<ul>\n<li>2476990 - Misleading messages during the execution of a remote ATC check run</li>\n<li>2478717 - S/4HANA Readiness Check: SELECT FOR UPDATE not found in search for critical DB statements</li>\n<li>2485726 - Release Information for S/4HANA Readiness Checks <br/><em><strong>Attention</strong> Consider the manual pre- and post-implementation steps of this note.</em></li>\n<li>2494509 - Adjustments to message titles in checks with regard to S4HANA_READINESS</li>\n<li>2494150 - Adjustments to message titles in Code Inspector field length extensions check</li>\n<li>2503262 - Filter function in cl_ci_test_search_db_ops returns too many findings</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP2 or lower)</strong></strong></p>\n<ul>\n<li>2444208 - Merge of Code Inspector check variants for SAP S/4HANA readiness and SAP HANA analysis<br/><strong><em>Attention </em></strong><em>Consider the manual post steps of this note.</em></li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP1 or lower)</strong></p>\n<ul>\n<li>2401298 - S/4HANA readiness Code Inspector check of ABAP Dictionary views: Supporting all classic view types</li>\n<li>2403110 – Improvements for Code Inspector Check \"Search for S/4HANA related syntax errors\"</li>\n</ul>\n<p><strong><span>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP0)</span></strong></p>\n<ul>\n<li>2362362 - Runtime error in Code Inspector check for search for usages of simplified objects</li>\n<li>2362553 - Additional and revised S/4HANA readiness Code Inspector checks<br/><strong><em>Attention </em></strong><em>Consider the manual post steps of this note.</em></li>\n</ul>\n<p>It is also recommended to apply the SAP Notes mentioned in SAP Note 2364916 in your system.</p>", "noteVersion": 140}]}, {"note": "2270546", "noteTitle": "2270546 - S4TWL - Technical Changes in Material Ledger", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Usage of Material Ledger for parallel currencies and parallel valuation purpose:</p>\n<ul>\n<li>Contents of tables MLIT, MLPP, MLPPF, MLCR, MLCRF, MLCD, CKMI1, BSIM are now stored in ACDOCA. MLHD data is stored in BKPF  </li>\n<li>Compatibility views V_&lt;TABLENAME&gt; (for example, V_MLIT) are provided to reproduce the old structures </li>\n<li>Access to old data in tables still possible via the views V_&lt;TABLENAME&gt;_ORI (for example, V_MLIT_ORI) </li>\n<li><PERSON><PERSON><PERSON>, MLIT, MLPP, MLCR still keep prima nota information, in case of manual price changes or material debit/credit.</li>\n<li>Separate currency customizing of Material Ledger is become obsolete, instead Material Ledger is acting on the currencies defined for the leading ledger in Financials.</li>\n</ul>\n<ul>\n<li>For materials that are not relevant for actual costing (xBEW-MLAST = '2') no ML update documents (MLHD-VGART = 'UP') are persisted anymore in the ML document tables MLHD, MLIT, MLPP, MLCR and MLCD. Instead those update document data are being reconstructed by read access compatibility views based on ACDOCA. Hence attributes from ML data model, relevant for a pure inventory sub-ledger functionality, are part of table ACDOCA.</li>\n<li>No “Price Determination Structure“ view for non-actual costing materials<br/> With the merge of the Material Ledger update document into the Universal  Journal (ACDOCA) actual costing relevant key figures will not be updated anymore for non-actual costing materials ( „Price Determination: Transaction-Based“).  Therefore transaction CKM3N „Material Price Analysis“ will provide only the view „Price History“ for non-actual costing materials, means the view „Price Determination Structure“ is not available any longer.</li>\n</ul>\n<p> Usage of Material Ledger for Actual Costing purpose:</p>\n<ul>\n<li>MLHD, MLIT, MLPP, MLPPF, MLCR, MLCRF, MLCD are used as before Simple Finance 2.0</li>\n</ul>\n<p>The following Customizing Transaction are obsolete win Sap S/4HANA:</p>\n<ul>\n<li>OMX2: Assign Currency Types to Material Ledger Type</li>\n<li>OMX3: Assign Material Ledger Types to Valuation Area</li>\n</ul>", "noteVersion": 1}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "CKM3/CKM3N was refactored and now provides a simplified and improved view of materials in plants with active Actual Costing. It replaces the former CKM3 view Price Determination Structure. The former CKM3 Price History view is still available via transaction CKM3PH for all materials (independent of price determination control and active Actual Costing)"}]}