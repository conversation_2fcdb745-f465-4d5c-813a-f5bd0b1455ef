{"guid": "0050569455E21ED5B3E176783910809E", "sitemId": "SI3: Logistics_EHS - Waste Management", "sitemTitle": "S4TWL - Waste Management", "note": 2267783, "noteTitle": "2267783 - S4TWL - Waste Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With <em>SAP EHS Management</em> as part of <em>SAP ERP</em>, customers can use the <em>Waste Management</em> (EHS-WA) solution for handling waste disposal processes within their company. In <em>SAP S/4HANA</em>, this solution is not available.</p>\n<p><span>Note</span>: A new <em>Waste Management</em> (EHS-SUS-WA) solution is available for <em>SAP S/4HANA for EHS environment management</em> as of <em>SAP S/4HANA</em><em> 202</em><em>3 </em>and <em>SAP S/4HANA Cloud Private Edition 202</em><em>3</em>.</p>\n<p><strong>Business Process related information</strong></p>\n<p><em>Waste Management</em> (EHS-WA) is not available in <em>SAP S/4HANA</em> and the related business processes based on the following list of transactions are no longer supported.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"191\">\n<p>Transactions not available in <em>SAP S/4HANA</em> on-premise edition</p>\n</td>\n<td valign=\"top\" width=\"404\">\n<p>WAA01,WAA02,WAA03,WAA04,WAA10,WAA11,WAA12,WAA13,WAA19,WAA20,WAA21,WAA22,WAA23,WAA24,WAA25,WAA26, WAAP, WACB01,WACM01,WACM02,WACM03,WACM04,WACM10,WACM30,WACO02,WACO02OLD,WACO03OLD,WACO04OLD,<br/>WACS1,WACS2,WACS3,WACS4,WACS6,WADC,WADI,WAE01,WAE02,WAE03,WAE10,WAEA,WAGE, WAM01, WAM02,WAM03,WAM04,WAM05,WAMC,WAMI,WAMR,WANP,WAREP001,<br/>WASM100, WASM101,WASM102,WASM109,WASS100,WASS101,WASS102,WASS103,WASS104,WASS105,WASS106,WASS107,WASS108,WASS110,<br/>WASS111,WASS112,WASS113,WASS114,WASS115,WASS116,WASS117,WASS118,WASS119,WASS120,WASS121,WASS122,WASS123,WASS124,<br/>WATR,WATREE</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Conversion to the new <em>Waste Management</em> solution (EHS-SUS-WA).</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2217202</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 6, "refer_note": [{"note": "2232552", "noteTitle": "2232552 - SAP S/4HANA, SAP Waste and Recycling: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are going to use SAP S/4HANA, on-premise 1511 and including 1610 FPS01. This note informs you about restrictions in this release related to SAP Waste and Recycling features. SAP Waste and Recycling and all enhancements (for example, BW) are released as of SAP S/4HANA, on-premise 1610 FPS02 or higher. Please refer to activation note <a href=\"/notes/2437332\" target=\"_blank\">2437332</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Waste and Recycling, Waste Management, IS-U-WA, CRM-WA, BW-BCT-WA, W&amp;R, IS-WASTE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Waste and Recycling and all extensions for example inside CRM and BW are not released for SAP S/4HANA, on-premise 1511 to 1610 FPS01. Business Functions ISU_WASTE_1, ISU_WASTE_RCI, ISU_WASTE_C&amp;I, ISU_WASTE_2, ISU_WASTE_3, ISU_WASTE_4 and similar cannot be activated.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP Waste and Recycling is not released for SAP S/4HANA, on-premise 1511 and including 1610 FPS01. Customers should</p>\n<ul>\n<li>either run SAP Waste and Recycling on a SAP Business Suite on HANA release (if you want to use HANA as a database for the SAP Business Suite)</li>\n<li>or run SAP S/4HANA without SAP Waste and Recycling as separate installation.</li>\n</ul>\n<p>Furthermore the following data sources are not release and must not be used if you run SAP S/HANA 1511 and including 1610 FPS01.</p>\n<ul>\n<li>0UC_CM_NCL_WDO</li>\n<li>0UC_CM_CL_WDO</li>\n<li>0UC_EWAELOCSD</li>\n<li>0UC_EWAOBJ</li>\n<li>0UC_EWAOBJH</li>\n<li>0UC_EWAOBJH_DATES</li>\n<li>0UC_EWAOBJH_DIFFCULT</li>\n<li>0UC_EWAOBJH_UPOS</li>\n<li>0UC_EWAPROPSD</li>\n<li>0UC_PCKGDSPOS</li>\n<li>0UC_PCKGDSREG</li>\n<li>0UC_SAMPLE</li>\n<li>0UC_WA_WDOHEAD</li>\n<li>0UC_WDOITEM</li>\n<li>0UC_WDOWDAT</li>\n<li>0UC_WDO_RES</li>\n<li>0UC_WDPROC</li>\n<li>0UC_WEIGHPROC</li>\n<li>0UCCLEANOBJ_ATTR</li>\n<li>0UCCLEANOBJ_TEXT</li>\n<li>0UC_CLEAN_ATTR</li>\n<li>0UC_CLEAN_TEXT</li>\n<li>0UC_EHSWABPID_ATTR</li>\n<li>0UC_PROP_ATTR</li>\n<li>0UC_PROP_TEXT</li>\n<li>0UC_SERVTYP_ATTR</li>\n<li>0UC_SERVTYP_TEXT</li>\n<li>0UC_VEHICLE_ATTR</li>\n<li>0UC_VEHICLE_TEXT</li>\n<li>0UC_WDIFFNT_ATTR</li>\n<li>0UC_WDIFFNT_TEXT</li>\n<li>0UC_WDPLANT_ATTR</li>\n<li>0UC_WDPLANT_TEXT</li>\n<li>0UC_WODREL_TEXT</li>\n<li>0UC_WPRODAR_ATTR</li>\n<li>0UC_WPRODAR_TEXT</li>\n<li>0UC_WROUTE_ATTR</li>\n<li>0UC_WROUTE_TEXT</li>\n<li>0UC_WRTHMD_TEXT</li>\n<li>0UC_WRTHMM_TEXT</li>\n<li>0UC_WRTHMW_TEXT</li>\n<li>0UC_WSRVDCH_TEXT</li>\n<li>0UC_WSRVGRP_TEXT</li>\n<li>0UC_WSTCST_ATTR</li>\n<li>0UC_WSTCST_TEXT</li>\n<li>0UC_WSTSRV_TEXT</li>\n<li>0UC_WWINTLVL_TEXT</li>\n</ul>\n<p> </p>", "noteVersion": 12, "refer_note": [{"note": "2119188", "noteTitle": "2119188 - Release Scope Information: SAP Simple Finance, on-premise edition 1503", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SAP Simple Finance, on-premise edition comes with simplified architecture, optimized financial accounting processes and supports the operational finance experts driving efficiency and compliance. It is a system of records based on line items as a single source of truth for operational reporting and planning.</p>\n<p>The SAP Simple Finance, on-premise edition is a new product, powered by the in-memory capabilities of SAP HANA, that allows you to use in-memory technology to further optimize Financials core business processes. SAP HANA comprises the required hardware as well as the SAP HANA appliance software (SAP HANA database and data replication tools, for example). Please note that the measured performance depends on configuration, data volumes, and data constellation.</p>\n<p>The <strong>SAP Simple Finance, on-premise edition 1503</strong> for SAP Business Suite powered by SAP HANA is the successor of SAP Simple Finance add-on 1.0 (corresponding SAP Note: <a href=\"/notes/1968568\" target=\"_blank\">1968568</a>).</p>\n<p>The SAP Simple Finance, on-premise edition is technically an exchange add-on, which is deployed to SAP enhancement package 7 for SAP ERP 6.0 on a HANA installation. It is an alternative to the classical ERP Financials core applications of SAP enhancement package 7 for SAP ERP 6.0. You cannot use both in parallel within the same SAP ERP installation.</p>\n<p>For details on industry solutions, add-ons, or functions already supported by the SAP Simple Finance, on-premise edition see below and check back regularly for updates.</p>\n<p>The SAP Simple Finance, on-premise edition is a net new product and not the legal successor for Financials in SAP ERP. Therefore it is not delivered under any maintenance agreement for SAP ERP.</p>\n<p>In case you are using Financials applications with SAP enhancement package 7 for SAP ERP (or previous SAP ERP product versions) or SAP Simple Finance add-on 1.0, a migration project to the SAP Simple Finance, on-premise edition 1503 is required. <br/>For more information, see <em>SAP Help portal at <a href=\"http://help.sap.com/sfin200\" target=\"_blank\">http://help.sap.com/sfin200</a> -&gt; Application Help: SAP ERP Central Component -&gt; Accounting -&gt; SAP Simple Finance, on-premise edition -&gt; <strong>Migration to SAP Accounting powered by SAP HANA</strong> </em>and the <em><strong>Administrator's Guide for SAP Simple Finance</strong>, on-premise edition 1503 at <a href=\"http://help.sap.com/sfin200\" target=\"_blank\">http://help.sap.com/sfin200</a>.</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SFINANCIALS, SFIN, supported industries, supported enterprise extensions, supported business processes, supported add-ons, supported partner add-ons, S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note provides an overview on the industries, enterprise extensions, add-ons and financials business processes supported with the SAP Simple Finance, on-premise edition 1503.</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Release Information - SAP Simple Finance, on-premise edition 1503 support package stack</strong></p>\n<p>Starting from support package 05, SAP Simple Finance, on-premise edition 1503 is in maintenance only.</p>\n<p>Technical Release Information please refer to note <a href=\"/notes/2117481\" target=\"_blank\">2117481</a>.</p>\n<p><strong>----------------------------------------------------------------------------------------------------------------------------------------------------------------------------</strong></p>\n<p><strong>Release Information - SAP Simple Finance, on-premise edition 1503 support package stack 04 (02/2016):</strong></p>\n<p>Please be noticed that in Central Finance, there is a change with regards to Initial Load (further details, please refer to note <a href=\"/notes/2240675\" target=\"_blank\">2240675</a>)</p>\n<p>----------------------------------------------------------------------------------------------------------------------------------------------------------------------------</p>\n<p><strong>Release Information - SAP Simple Finance, on-premise edition 1503 support package stack 03 (11/2015):</strong></p>\n<p><strong>New features and <strong>enhancements: </strong></strong></p>\n<ul>\n<li>Continuing innovations for Central Finance: Replication of clearings</li>\n<li>Limitation resolving: Enable Joint Ventures Accounting (JVA) for Oil&amp;Gas Industry (see note <a href=\"/notes/2137314\" target=\"_blank\">2137314</a>)</li>\n<li>Limitation resolving: Enable Aging-Object for Journal Entry</li>\n</ul>\n<p>----------------------------------------------------------------------------------------------------------------------------------------------------------------------------</p>\n<p><strong>Financials processes released for usage with SAP Simple Finance, on-premise edition 1503:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"542\"/></colgroup>\n<tbody>\n<tr>\n<td height=\"20\" width=\"542\"><strong>Business Process</strong></td>\n<td height=\"20\" width=\"542\"><strong> Hints</strong></td>\n</tr>\n<tr>\n<td height=\"20\" width=\"542\">Accounts Payable</td>\n<td height=\"20\" width=\"542\"></td>\n</tr>\n<tr>\n<td height=\"20\">Accounts Receivable</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Audit Support</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Cash Management (SAP Cash Management powered by SAP HANA)</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Central Payments with In-House Cash</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Credit Evaluation and Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Electronic Tax Declaration</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">FI-AR: Collections Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">FI-AR: Credit Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">FI-AR: Dispute Resolution</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Fixed Asset Accounting</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Internal Payments with In-House Cash</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Liquidity Management (SAP Cash Management powered by SAP HANA)</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Local Close</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">New General Ledger and Profit Center Accounting</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Operational Treasury Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Overhead Cost Management and ABC/M</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Parallel Accounting</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Processing Outbound Payments Using Bank Communication Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Product Cost Management for Make2Order with Sales Order Controlling</td>\n<td height=\"20\">covers Material Ledger</td>\n</tr>\n<tr>\n<td height=\"20\">Product Cost Management for Make2Order without Sales Order Controlling</td>\n<td height=\"20\">covers Material Ledger</td>\n</tr>\n<tr>\n<td height=\"20\">Product Cost Management for Make2Stock with Order Manufacturing</td>\n<td height=\"20\">covers Material Ledger</td>\n</tr>\n<tr>\n<td height=\"20\">Product Cost Management for Make2Stock with Repetitive Manufacturing</td>\n<td height=\"20\">covers Material Ledger</td>\n</tr>\n<tr>\n<td height=\"20\">Profitability Management</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Tax Accounting</td>\n<td height=\"20\"></td>\n</tr>\n<tr>\n<td height=\"20\">Treasury Risk Management</td>\n<td height=\"20\"></td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>With SAP Simple Finance, on-premise edition 1503 a transition to the following applications is automatically part of the SAP Simple Finance migration:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"206\"/> <col width=\"800\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl66\" height=\"40\" width=\"206\">New General Ledger</td>\n<td class=\"xl65\" width=\"800\">Classic General Ledger is mostly automatically transformed into a basic implementation of New General Ledger (G/L in SAP Accounting powered by SAP HANA)</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\" width=\"206\">New Asset Accounting</td>\n<td class=\"xl65\" width=\"800\">Classic Asset Accounting is mostly automatically transformed into the New Asset Accounting</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"40\" width=\"206\">SAP Cash Management powered by SAP HANA</td>\n<td class=\"xl65\" width=\"800\">Classic Cash Management is replaced by the new SAP Cash Management powered by SAP HANA. <br/>SAP Cash Management powered by SAP HANA requires a separate license. Refer to SAP Note <a href=\"/notes/2044295\" target=\"_blank\">2044295 </a>for more information.</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Country versions released for SAP Simple Finance, on-premise edition 1503:</strong></p>\n<p>SAP Note <a href=\"/notes/2153644\" target=\"_blank\">2153644</a> provides an overview of the country release information and limitations concerning the productive usage of localization functions.</p>\n<p><strong>The SAP Simple Finance, on-premise edition 1503 does not support the following functionalities from SAP enhancement package 7 for SAP ERP 6.0:</strong></p>\n<ul>\n<li>Classic Real Estate Management (RE); only RE-FX is supported (see SAP Note 1944871)</li>\n<li>Activity Based Costing (CO-OM-ABC) using delta versions</li>\n<li>General cost object and cost object hierarchies </li>\n<li>in New Asset Accounting: integration to LAE (Lease Accounting Engine) and JVA (business function JVA_GL_INTEGRATION, solved in <a href=\"/notes/2137314\" target=\"_blank\">2137314</a>); for more information, see SAP Help portal at <a href=\"http://help.sap.com/sfin200\" target=\"_blank\">http://help.sap.com/sfin200</a> -&gt; Application Help: SAP ERP Central Component -&gt; Accounting -&gt; SAP Simple Finance, on-premise edition -&gt; Migration to SAP Accounting powered by SAP HANA -&gt; Migration to New Asset Accounting</li>\n<li>General ledger: balance sheet planning, reports for comparing planning data and actual data and average balance ledger are not supported</li>\n<li>New General Ledger: subsequent implementation of online document split and ledgers, the transformation of parallel accounts into ledger approach (scenarios 6, 7 and 8) and the change in leading valuation (business function FIN_GL_CHNG_LEAD_VAL) are not supported.</li>\n<li>Profit Center- and Segment Reorganization: The functionality of Profit Center- and Segment Reorganization is not supported in the context of the Universal Journal. As per the financials processes listed above as released for usage with SAP Simple Finance, Profit Center and Segment Reorganization” are not part of the released scope. These reorganization functions are offered within the product portfolio of SAP Landscape Transformation (SAP LT) – see SAP notes <a href=\"/notes/1534197\" target=\"_blank\" title=\"2119188  - Release Scope Information: SAP Simple Finance, on-premise edition 1503\">1534197</a> and <a href=\"/notes/1686874\" target=\"_blank\" title=\"2119188  - Release Scope Information: SAP Simple Finance, on-premise edition 1503\">1686874</a> for further information. <strong> </strong></li>\n</ul>\n<p><strong>The SAP Simple Finance, on-premise edition 1503 differs from SAP enhancement package 7 for SAP ERP 6.0:</strong></p>\n<ul>\n<li>CO-OM planning, P&amp;L planning and profit center planning are now covered by Integrated Business Planning (SAP Note <a href=\"/notes/2081400\" target=\"_blank\">2081400</a>).</li>\n<ul>\n<li>CO-OM planning: in case you do not want to use „Integrated Business Planning for Finance” but classic CO-OM planning functions, you may apply the modifications as indicated in SAP Note <a href=\"/notes/1946054\" target=\"_blank\">1946054</a>. With these modifications the deactivation of the old CO-OM planning transactions can be removed.</li>\n</ul>\n<li>Refer to SAP Note <a href=\"/notes/1946054\" target=\"_blank\">1946054</a> for a detailed comparison of transaction codes / reports in SAP enhancement package 7 for SAP ERP 6.0 and SAP Simple Finance.</li>\n<li>\n<p>Material Ledger: <br/>- Actual costing relevant key figures are not updated for non-actual costing materials („Price Determination: Transaction-Based\"). Therefore transaction CKM3N „Material Price Analysis\" provides only the view „Price History\" for non-actual costing materials. The view „Price Determination Structure\" is not available.<br/>- Material Ledger currency settings for new installations / newly added company codes: currency settings may not differ from the one centrally defined in Accounting and Controlling.<br/><em>- </em>Material Ledger currency settings for migration scenario: currency settings have to be a subset of currencies defined in Accounting and Controlling.</p>\n</li>\n</ul>\n<p><strong>SAP ERP Enterprise Extensions are not completely released with the SAP Simple Finance, on-premise edition 1503:</strong></p>\n<p>You can install SAP Simple Finance, on-premise edition 1503 and use it with the following SAP ERP 6.0 Enterprise Extensions:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"68\"/> <col width=\"637\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"68\"><strong>Extension</strong></td>\n<td class=\"xl65\" width=\"637\"><strong>Title</strong></td>\n</tr>\n<tr>\n<td height=\"20\">EA-CP</td>\n<td>Consumer Products</td>\n</tr>\n<tr>\n<td height=\"20\">EA-DFP</td>\n<td>DefenseForces&amp;PublicSecurity</td>\n</tr>\n<tr>\n<td height=\"20\">EA-FIN</td>\n<td>Financials Extension (required)</td>\n</tr>\n<tr>\n<td height=\"20\">EA-FRC</td>\n<td>FERC: Regulatory Reporting</td>\n</tr>\n<tr>\n<td height=\"20\">EA-FS</td>\n<td>Financial Services, for processes Operational Treasury Management and Treasury Risk Management</td>\n</tr>\n<tr>\n<td height=\"20\">EA-GLT</td>\n<td>Global Trade Management</td>\n</tr>\n<tr>\n<td height=\"20\">EA-HR</td>\n<td>Human Capital Management</td>\n</tr>\n<tr>\n<td height=\"20\">EA-ICM</td>\n<td>Incentive and Sales Force Mgmt</td>\n</tr>\n<tr>\n<td height=\"20\">EA-ISE</td>\n<td>Industry-Spec. Sales Enhancement</td>\n</tr>\n<tr>\n<td height=\"20\">EA-PLM</td>\n<td>PLM Extension</td>\n</tr>\n<tr>\n<td height=\"20\">EA-PS</td>\n<td>Public Services (details refer to SAP Note <a href=\"/notes/2148944\" target=\"_blank\">2148944</a>)</td>\n</tr>\n<tr>\n<td height=\"20\">EA-RET</td>\n<td>Retail</td>\n</tr>\n<tr>\n<td height=\"20\">EA-SCM</td>\n<td>SCM Extension</td>\n</tr>\n<tr>\n<td height=\"20\">EA-TRV</td>\n<td>Travel Management Extension</td>\n</tr>\n</tbody>\n</table></div>\n<p>You can check this in transaction SFW5 -&gt; node ENTERPRISE_EXTENSIONS -&gt; only the above listed can be combined together with SAP Simple Finance, on-premise edition.</p>\n<p><strong>SAP ERP Industry Solutions released with the SAP Simple Finance, on-premise edition 1503:</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"2\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"254\"/> <col width=\"267\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"254\"><strong>Industry Solution</strong></td>\n<td class=\"xl65\" width=\"267\"><strong>Related release information</strong><br/> [SAP Note]</td>\n</tr>\n<tr>\n<td height=\"20\">Aerospace &amp; Defense</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Automotive</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Banking</td>\n<td><a href=\"/notes/2144579\" target=\"_blank\">2144579</a></td>\n</tr>\n<tr>\n<td height=\"20\">Chemicals</td>\n<td><a href=\"/notes/2137314\" target=\"_blank\">2137314</a></td>\n</tr>\n<tr>\n<td height=\"20\">Consumer Products</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Defense and Security</td>\n<td><a href=\"/notes/2148944\" target=\"_blank\">2148944</a></td>\n</tr>\n<tr>\n<td height=\"20\">Engineering Construction &amp; Operations</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Healthcare</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Higher Education &amp; Research</td>\n<td><a href=\"/notes/2148944\" target=\"_blank\">2148944</a></td>\n</tr>\n<tr>\n<td height=\"20\">High Tech</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Industrial Machinery &amp; Components</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Insurance</td>\n<td><a href=\"/notes/2150839\" target=\"_blank\">2150839</a></td>\n</tr>\n<tr>\n<td height=\"20\">Life Sciences</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Media</td>\n<td><a href=\"/notes/2157325\" target=\"_blank\">2157325</a></td>\n</tr>\n<tr>\n<td height=\"20\">Mill Products</td>\n<td><a href=\"/notes/2151074\" target=\"_blank\">2151074</a></td>\n</tr>\n<tr>\n<td height=\"20\">Mining</td>\n<td>\n<p><a href=\"/notes/2137314\" target=\"_blank\">2137314</a></p>\n</td>\n</tr>\n<tr>\n<td height=\"20\">Oil &amp; Gas</td>\n<td>\n<p><a href=\"/notes/2137314\" target=\"_blank\">2137314</a></p>\n</td>\n</tr>\n<tr>\n<td height=\"20\">Public Sector</td>\n<td><a href=\"/notes/2148944\" target=\"_blank\">2148944</a> </td>\n</tr>\n<tr>\n<td height=\"20\">Professional Services</td>\n<td>Released </td>\n</tr>\n<tr>\n<td height=\"20\">Retail (including Fashion Management)</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Sports &amp; Entertainment</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Telecommunications</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Travel &amp; Transportation</td>\n<td>Released</td>\n</tr>\n<tr>\n<td height=\"20\">Utilities</td>\n<td><a href=\"/notes/2148799\" target=\"_blank\">2148799</a></td>\n</tr>\n<tr>\n<td height=\"20\">Wholesale Distribution</td>\n<td>Released<a href=\"/notes/2148799\" target=\"_blank\"><br/></a></td>\n</tr>\n</tbody>\n</table></div>\n<p>To cover the released industry solutions and financials processes the SAP Simple Finance on-premise edition can be combined with a subset of SAP enhancement package 7 for SAP ERP 6.0 software components.<br/>Refer to SAP Note <a href=\"/notes/2103558\" target=\"_blank\">2103558</a> for a list of these software components.</p>\n<p><strong>SAP and partner add-ons to ERP 6.0 are not all released with the SAP Simple Finance, on-premise edition 1503:</strong></p>\n<ul>\n<li><em>SAP add-ons to ERP 6.0</em> released for usage with the SAP Simple Finance, on-premise edition 1503: see SAP Note <a href=\"/notes/2103558\" target=\"_blank\">2103558</a></li>\n<li><em>Partner add-ons to ERP 6.0</em> released for usage with the SAP Simple Finance, on-premise edition 1503: see SAP Note <a href=\"/notes/2100133\" target=\"_blank\">2100133</a></li>\n</ul>\n<p>The SAP Simple Finance on-premise edition 1503 might not be installable if any add-on, that is not released, is already included in your current SAP ERP installation.</p>\n<p><strong>Current Release Restrictions for SAP Simple Finance on-premise edition 1503:</strong></p>\n<p>At the time of the release of SAP Simple Finance on-premise edition 1503 limitations existed concerning the productive usage of certain functions. Refer to SAP Note <a href=\"/notes/2127080\" target=\"_blank\">2127080</a> for more information.</p>\n<p><strong>-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------</strong></p>\n<p>For similar information on the previous support package stacks, refer to SAP Note <a href=\"/notes/2247851\" target=\"_blank\">2247851</a>.</p>", "noteVersion": 50}, {"note": "2148799", "noteTitle": "2148799 - Release Information for components of SAP for Utilities together with SAP Simple Finance, on-premise edition 1503 or SAP S/4HANA Finance 1605", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You would like to use the Industry Solution for Utilities (IS-U) together with SAP Simple Finance, on-premise edition 1503 or SAP S/4HANA Finance 1605</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Conditions and restrictions relevant for SAP for Utilities in combination with SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</strong></p>\n<p>This note is relevant for SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605 combined with SAP ERP 6.0 EhP7 and EhP8</p>\n<ul>\n<li>Please check note 2140797 for conditions and restrictions for Contract Accounts Receivable and Payable (FI-CA). There is one restriction for Funds Management in FI-CA to be mentioned here: It is not possible to do a post activation of Funds Management in an already productive system so that transaction FPFMDY cannot be used.</li>\n<li>FERC is released for SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</li>\n</ul>\n<ul>\n<li>SAP Asset Lifecycle Accounting 1.0 (software component FAAM 604) can be used in combination with SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</li>\n</ul>\n<ul>\n<li>SAP Asset Retirement Obligation Management 1.0 cannot be used in combination with the Financials add-ons.</li>\n<li>Before installation of SAP Financial add-ons, please check your system installation as described in note 2176077.</li>\n</ul>\n<p><strong>Release</strong></p>\n<p>Following components and applications can be used in combination with SAP Simple Finance, on-premise edition 1503 or SAP S/4HANA Finance 1605:</p>\n<ul>\n<li>SAP Waste &amp; Recycling Apps by Prologa 7.0</li>\n<li>SAP Utilities - Customer Care &amp; Service (SAP IS-U/CCS) as of SAP ERP 6.0 EHP7 SP08.</li>\n<li>Localization of IS-U for countries in Central and Eastern Europe (SAP IS-UT CEE 6.06 and earlier releases)</li>\n<li>Localization of IS-U for India (SAP IS-U LOCINCS 6.0 and earlier releases)</li>\n</ul>\n<p><span>For the following products, detailed release information is provided to give full clarity:</span></p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p><strong>PPMS name</strong></p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p><strong>Product name</strong></p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p><strong>Software Component</strong></p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p><strong>Released for</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>SAP IDEX DE 1.0 ON ERP6.0 EHP7</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Intercompany Data Exchange for German Electric Utilities 1.0 on SAP ERP 6.0 EHP 7</p>\n<p>(Intercompany Data Exchange for German Gas Utilities (IDEX-GG))</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDEXDE 617<br/> (IDXGC 200)<br/> (IDXGL 200)</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>SAP IDEX DE 1.0 ON ERP6.0 EHP8</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Intercompany Data Exchange for German Electric Utilities 1.0 on SAP ERP 6.0 EHP 8</p>\n<p>(Intercompany Data Exchange for German Gas Utilities (IDEX-GG))</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDEXDE 618<br/> IDXGC 200)<br/> (IDXGL 200)</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>QUANTITIES BILL. GER ENRG 1.0<br/> <em>(add-on of IDEX DE 1.0)</em></p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>overtake and undertake quantities billing for German energy utilities 1.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>MEMI 100</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>SAP IDEX GER ELEC FEED 1.0<br/> <em>(add-on of IDEX DE 1.0)</em></p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Intercompany Data Exchange for German Electricity Feed 1.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDXGF 100</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>MARKET PROCESS MGMT UTIL 1.0</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>market process management for utilities 1.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDEXGC 200</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>MOS BILLING 1.0</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>meter operation service billing for German electricity and gas utilities 1.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDEXGC 200</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>SAP GERMAN METERING 1.0</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Intercompany Data Exchange for German Metering 1.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDEXGM 604<br/> (IDXGC 100)</p>\n<p>(IDXGC 200)</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>SAP GERMAN METERING 2.0</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Intercompany Data Exchange for German Metering 2.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDXGC 200<br/> (IDXGL 200)</p>\n<p>(IDXGM 200)</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>SAP INTELL METER GER ENRGY 1.0</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Intelligent Metering for German Energy Utilities 1.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDXGC 100</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>SAP INTELL METER GER ENRGY 2.0</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Intelligent Metering for German Energy Utilities 2.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDEIM 200<br/> (IDXGC 200)<br/> (IDXGL 200)</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>ENERGY CALC GAS CONSUMP 1.0</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>German gas solution 1.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>CONDET 600</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>MARKET PROCESS MGMT UTIL 1.0</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>market process management for utilities 1.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>IDXGC 200</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"226\">\n<p>SAP EEG BILL GER ELEC FEED 1.0</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP EEG Billing for German Electricity Feed 1.0</p>\n</td>\n<td valign=\"top\" width=\"104\">\n<p>ENBIL 606</p>\n</td>\n<td valign=\"top\" width=\"293\">\n<p>SAP Simple Finance, on-premise edition 1503 and SAP S/4HANA Finance 1605</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><span> </span></p>", "noteVersion": 5}, {"note": "2437332", "noteTitle": "2437332 - SAP S/4HANA, SAP Waste and Recycling: Activation SAP Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use SAP S/4HANA, On-Premise Edition 1610 FPS02 or higher. This SAP Note provides information about the restrictions of this release with regard to the SAP Waste and Recycling functions.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Waste and Recycling, waste management, IS-U-WA, CRM-WA, BW-BCT-WA, W&amp;R, IS-WASTE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Waste and Recycling and all enhancements (for example, BW including all DataSources) are released as of SAP S/4HANA, On-Premise 1610 FPS02 or higher. To be able to use SAP Waste and Recycling, the business functions ISU_WASTE_1, ISU_WASTE_RCI_2, ISU_WASTE_C&amp;I_2, ISU_WASTE_2, ISU_WASTE_3, ISU_WASTE_4 must be activated. All code branches that would pass through the \"Off\" switch settings are no longer available. In this context, also refer to the additional documentation for the relevant business functions.</p>\n<p dir=\"ltr\">If you want to switch from an earlier release to SAP S/4HANA and have not yet activated all of the aforementioned business functions, a possible conversion strategy could be to perform the technical upgrade to SAP S/4HANA on-premise first, then to activate the aforementioned business functions, and then to carry out the functional test.</p>\n<p dir=\"ltr\">Please note for the conversion to S/4HANA on-premise that the integration in EH&amp;S: Waste Management is no longer supported.</p>\n<p dir=\"ltr\">The existing integration of SAP CRM for SAP Waste and Recycling and its associated interfaces are delivered neither in S/4HANA on-premise nor in the S/4HANA Service Interaction Center (formerly known as SAP S/4HANA Customer Management). A comparable integration between SAP S/4HANA Service and SAP S/4HANA (SAP Waste and Recycling) is provided and licensed via the solution extension <em>SAP S/4HANA for Waste and Recycling, Environmental Services Add-On by PROLOGA</em>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP Waste and Recycling is released for SAP S/4HANA, On-Premise-Edition 1610 FPS02 and higher.</p>\n<p>The following transactions are no longer supported for SAP S/4HANA On-Premise-Edition 1610 FPS02 and are on the black list:</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"176\">\n<p>EWAEL01</p>\n</td>\n<td valign=\"top\" width=\"290\">\n<p>Delivery Locks</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"176\">\n<p>EWAEL02</p>\n</td>\n<td valign=\"top\" width=\"290\">\n<p>Visual Control</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"176\">\n<p>EWAEL03</p>\n</td>\n<td valign=\"top\" width=\"290\">\n<p>Reserve Sample</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"176\">\n<p>EWAGG</p>\n</td>\n<td valign=\"top\" width=\"290\">\n<p>Guarantor Contract</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"176\">\n<p>EWAGGABR</p>\n</td>\n<td valign=\"top\" width=\"290\">\n<p>Allocate Bill.Data to Guarantor Cat.</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"176\">\n<p>EWAORDALL</p>\n</td>\n<td valign=\"top\" width=\"290\">\n<p>Create Waste Disposal Order</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"176\">\n<p>EWASPGCONF</p>\n</td>\n<td valign=\"top\" width=\"290\">\n<p>Configuration: Service Product Generator</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"176\">\n<p>EWAWA02</p>\n</td>\n<td valign=\"top\" width=\"290\">\n<p>Weighing Transaction</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"176\">\n<p>EWAWA04</p>\n</td>\n<td valign=\"top\" width=\"290\">\n<p>Offline Weighing</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>See the corresponding simplification element and the list of simplifications in SAP S/4HANA.</p>", "noteVersion": 11}, {"note": "2227748", "noteTitle": "2227748 - SAP S/4 HANA, on-premise edition 1511, Utilities: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using SAP S/4HANA, on-premise edition 1511 support package 0, for Utilities and this note informs you about any restrictions in this release.</p>\n<p>The Utilities Business Scenario \"Registration for Grid Usage\" which includes the processes for start of supply, end of supply, and change of supplier (all from the supplier's perspective) is not released for SAP S/4HANA, on-premise edition 1511 support package 0.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For using Utilities Business Scenario \"Registration for Grid Usage\", please perform an upgrade to SAP S/4HANA, on-premise edition 1511 support package 1 or higher. Transaction CIC0 is not available on SAP S/4HANA. Therefore please consider the integration with SAP CRM for Utilities for supporting the processes of the business scenario.</p>", "noteVersion": 2}, {"note": "2050021", "noteTitle": "2050021 - Release Information for components of SAP for Utilities together with Financials add-on for SAP Business Suite powered by SAP HANA 1.0 SP01", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You would like to use the Industry Solution for Utilities (IS-U) with the Financials add-on for SAP Business Suite powered by SAP HANA1.0 SP01 in the Utilities industry.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Change History</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Date</strong></td>\n<td><strong>Description</strong></td>\n</tr>\n<tr>\n<td>29.10.2014</td>\n<td>SAP IS-U LOCINCS 6.0 released for SAP sFinancials 1.0</td>\n</tr>\n<tr>\n<td>27.10.2014</td>\n<td>CEEISUT 606 released for SAP sFinancials 1.0</td>\n</tr>\n<tr>\n<td>04.08.2014</td>\n<td>Note created</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Limited Release</strong></p>\n<ul>\n<li>As of ERP6 EHP7 SP05, Contract Accounts Receivable and Payable (FI-CA) can be used in combination with the Financials add-on for SAP Business Suite powered by SAP HANA1.0 SP01, if the following components are not used:</li>\n<ul>\n<li>Cash Management</li>\n<li>Regulatory Reporting (FERC)</li>\n</ul>\n<li>SAP Asset Lifecycle Accounting 1.0 cannot be used in combination with the Financials add-on for SAP Business Suite powered by SAP HANA1.0 SP01.</li>\n<li>SAP Asset Retirement Obligation Management 1.0 cannot be used in combination with the Financials add-on for SAP Business Suite powered by SAP HANA1.0 SP01.</li>\n<li>Funds Management in FI-CA generally runs together with sFIN 1.0 in one system. There is only one restriction: it is not possible to do a post activation of Funds Management in an already productive system. This means transaction FPFMDY cannot be used for sFIN installations.</li>\n</ul>\n<p><strong>Release</strong></p>\n<p>Following components and applications can be used in combination with the Financials add-on for SAP Business Suite powered by SAP HANA1.0 SP01, if the limitations listed above don't apply:</p>\n<ul>\n<ul>\n<li>SAP Waste&amp;Recycling Apps by Prologa 5.0.</li>\n<li>SAP Utilities - Customer Care &amp; Service (SAP IS-U/CCS) as of ERP6 EHP7 SP05.</li>\n<li>Localization of IS-U for countries in Central and Eastern Europe (SAP IS-UT CEE 6.06 and earlier releases)</li>\n<li>Localization of IS-U for India (SAP IS-U LOCINCS 6.0 and earlier releases)</li>\n</ul>\n</ul>", "noteVersion": 4}, {"note": "2101291", "noteTitle": "2101291 - Composite SAP note for SAP Waste and Recycling", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This is a composite SAP note for SAP Waste and Recycling. It contains references to important information concerning the Industry Business Solution.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Waste and Recycling,</p>\n<p>ELOC, ERONEW, EROUTE, EWABULKY, EWACLEAN, EWACONTS, EWAEL01, EAEL02, EWAEL03, EWAEL04, EWAEL05, EWAEL06, EWAEL07, EWAORDALL, EWACREA, EWAORDDEL, EWAORDER, EWAORDERDOWN, EWAPLAN, EWAPRICE, EWAROB, EWAWA01, EWA_MULTI, EWAWA02, EWAWA03, EWAWDOC, EWAORDRESL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You want to introduce or use SAP Waste and Recycling, or carry out an upgrade, and you look for important information that you have to take into account.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This SAP Note serves as central hub for important information concerning SAP Waste and Recycling. It contains a collection of references to important subject areas or SAP Notes that you should take into account during use, introduction, or in the context of an upgrade project.</p>", "noteVersion": 1}, {"note": "2214213", "noteTitle": "2214213 - SAP S/4HANA, on-premise edition 1511: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using SAP S/4HANA on-premise edition 1511. This SAP Note, which is subject to change, informs you about all restrictions in this release.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release restrictions, SAP S/4HANA, on-premise,  1511</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note provides information about the restrictions that currently exist for SAP S/4HANA, on-premise edition 1511.</p>\n<p><strong><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Note:</span></strong><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> This SAP Note is subject to change.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li>SAP S/4HANA, on-premise edition 1511 supports the following industries: \"Consumer Products\", \"Wholesale\", \"Life Sciences\", \"Aerospace &amp; Defense (A&amp;D)\", \"HighTech\", \"Industrial Machinery &amp; Components (IM&amp;C)\", \"Automotive\", \"Chemicals\", \"Mining\", \"Mill Products\", \"Utilities\", \"Banking\", \"Insurance\", \"Public Sector\", \"Engineering, Construction &amp; Operations (EC&amp;O)\", \"Professional Services\", \"Telecommunication\", \"Sports &amp; Entertainment\", \"Transportation &amp; Logistics\", and \"Contract Accounts Receivable and Payable (FI-CA)\". The following two industries are supported as of FPS 01: \"Higher Education and Research\" and \"Defense and Security\". Please also check the linked SAP notes for restrictions (see 11. below).</li>\n<li>In SAP S/4HANA, the maximum length of the material number has been extended to 40 characters. <em>Long material number-</em>related restrictions are described in SAP Note <a href=\"/notes/2233100\" target=\"_blank\">http://service.sap.com/sap/support/notes/2233100</a>.</li>\n<li>Account Numbers: In case of usage of the Best Practices with demo data, Account Numbers need to consist of 10 digits. They need to consist of numeric digits only. No alphanumeric characters are allowed at the moment. Examples of valid account numbers: \"*********; **********\".</li>\n<li>Conversion:</li>\n<ul>\n<li>Customers who are using the <em>Long Material Number (LAMA) in DIMP </em>cannot migrate from ERP to SAP S/4HANA with the 1511 shipment.</li>\n<li>Following restriction is obsolete with FPS02: The tools that support an automated migration from FI-AR Credit Management to FIN-FSCM Credit Management cannot be used in SAP S/4HANA, on-premise edition 1511 based on FPS00 or FPS01. In SAP S/4HANA, this can also affect the functionality of FIN-FSCM Credit Management itself after the conversion from SAP ERP. In this case, the standard Sales &amp; Distribution (SD) functionality (sales order, outbound delivery) also doesn't work if a credit check is executed. SAP ERP customers that are already using FIN-FSCM Credit Management are not affected by this.</li>\n<li>Conversions from start release SAP enhancement package 8 for SAP ERP 6.0 are not supported.</li>\n<li>Following restriction is obsolete with FPS02: Conversions from start systems containing SRM_SERVER (SRM One Client scenario) are not supported.</li>\n<li>Conversion from source releases below SAP ERP 6.0 EHP 5 (SAP_APPL 605) is supported for systems containing vendors with assigned contact but you need to ensure to update to the following minimum SP levels upfront. In addition, you need to implement corrective note <a href=\"/notes/2383051\" target=\"_blank\">2383051</a> and further corrections into the system. For this purpose please get in contact with SAP via customer incident on component AP-MD-BF-SYN. The minimum SP levels are: <br/> SAP ERP 6.0 SP20<br/> EHP2 FOR SAP ERP 6.0 SP10<br/> EHP3 FOR SAP ERP 6.0 SP09<br/> EHP4 FOR SAP ERP 6.0 SP11.</li>\n</ul>\n<li>SAP HANA:</li>\n<ul>\n<li>SAP HANA 2.0 SPS01 Revision 10:</li>\n<ul>\n<li>Migration from HANA 1.0 to 2.0 on Power platform not supported</li>\n</ul>\n<li>SAP HANA 2.0 SPS01 Revision 11:</li>\n<ul>\n<li>New installations and conversions using DMO <a href=\"/notes/2478183\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2478183</a></li>\n</ul>\n</ul>\n<li>The \"Customer Project Based Service\" scenario delivered as part of SAP S/4HANA Cloud is not released with SAP S/4HANA, on-premise edition 1511.</li>\n<li>The \"Maintenance Repair and Overhaul\" scenario is not released with SAP S/4HANA, on-premise edition 1511. </li>\n<li>The integrated solution of Service Parts Management using SAP CRM Order Management with an active Service Parts Management configuration (Direct Delivery Scenario) in combination with SAP S/4HANA is not released with SAP S/4HANA, on-premise edition 1511.</li>\n<li>Please use SAP DMS (Document Management Service) via SAP GUI. Starting with FPS02, SAP DMS can also be used via Web UI.</li>\n<li>The following Business Functions are not released with SAP S/4HANA, on-premise edition 1511. It is not permitted to switch them on, even if this is technically possible (see attachment). Additional information regarding Always-Off and Always-On Business Functions can be found in SAP Notes 2240359 and 2240360. If a business function was switched on in the start release system, but defined as always_off in SAP S/4HANA, then a system conversion is not yet possible with this release at that current point in time. If a business function was switched off in the start release system, but defined as always_on in the target release, then the business function will be automatically activated during the conversion.</li>\n<li>The release information for Add-Ons can be found in SAP Note <a href=\"/notes/2214409\" target=\"_blank\">2214409</a>.</li>\n<li>There are limitations for packaging material with more than 35 characters in combination with Returnable Packaging Logistics (application component MM-IM-RL or IS-A-RL). These are:</li>\n<ul>\n<li>EDI processing of returnable packaging account statements (message type ACCSTA). The IDoc type ACCSTA01 allows supplier and customer material numbers for packaging materials of up to 35 characters to be transmitted.</li>\n<li>EDI processing of returnable packaging account statement requests (message type ACCSTAREQ). The IDoc type ACCSTA01 allows supplier and customer material numbers for packaging materials of up to 35 characters to be transmitted.  </li>\n</ul>\n<li>Business partner:</li>\n<ul>\n<li>The Application Equipment &amp; Tools Management is not released, which means that the maintenance of recipient master data is not possible with this release.</li>\n<li>85 customer and supplier fields in transaction BP are delivered (see attached list \"S4OP1511_BP_supported_fields\"). Further fields are delivered with SAP S/4HANA, on-premise edition 1511 FPS 01 (see attached list \"S4OP1511_BP_supported_fields_FPS01\"). Other customer and supplier fields cannot be maintained.</li>\n</ul>\n<li>Further restrictions for SAP S/4HANA, on-premise edition 1511 are described in the following SAP Notes:</li>\n<ul>\n<li>Aerospace &amp; Defense (A&amp;D): <a href=\"/notes/2234353\" target=\"_blank\">http://service.sap.com/sap/support/notes/2234353</a></li>\n<li>Automotive: <a href=\"/notes/2228672\" target=\"_blank\">http://service.sap.com/sap/support/notes/2228672</a></li>\n<li>Mill Products: <a href=\"/notes/2229398\" target=\"_blank\">http://service.sap.com/sap/support/notes/2229398</a></li>\n<li>Utilities: <a href=\"/notes/2227748\" target=\"_blank\">http://service.sap.com/sap/support/notes/2227748</a></li>\n<li>Banking: <a href=\"/notes/2211665\" target=\"_blank\">http://service.sap.com/sap/support/notes/2211665</a></li>\n<li>Public Sector: <a href=\"/notes/2228940\" target=\"_blank\">http://service.sap.com/sap/support/notes/2228940</a></li>\n<li>Finance: <a href=\"/notes/2233501\" target=\"_blank\">http://service.sap.com/sap/support/notes/2233501</a></li>\n<li>LOB Manufacturing: <a href=\"/notes/2237351\" target=\"_blank\">http://service.sap.com/sap/support/notes/2237351</a><span class=\"urTxtStd urVt1\"> </span></li>\n<li>LOB Research and Development(R&amp;D) and Enterprise Asset Management (EAM): <a href=\"/notes/2237624\" target=\"_blank\">http://service.sap.com/sap/support/notes/2237624</a> </li>\n<li>LOB CEC: <a href=\"/notes/2231667\" target=\"_blank\">http://service.sap.com/sap/support/notes/2231667</a></li>\n<li>Globalization: <a href=\"/notes/2228890\" target=\"_blank\">http://service.sap.com/sap/support/notes/2228890</a></li>\n<li>Configuration: <a href=\"/notes/2234180\" target=\"_blank\">http://service.sap.com/sap/support/notes/2234180</a></li>\n<li>Environment, Health and Safety (EHS): <a href=\"/notes/2233367\" target=\"_blank\">http://service.sap.com/sap/support/notes/2233367</a></li>\n<li>Global Trade Service (GTS): <span class=\"urTxtStd urVt1\"><a href=\"/notes/2238122\" target=\"_blank\">http://service.sap.com/sap/support/notes/2238122</a></span></li>\n<li>Waste and Recycling: <span class=\"urTxtStd urVt1\"><a href=\"/notes/2232552\" target=\"_blank\">http://service.sap.com/sap/support/notes/2232552</a></span></li>\n<li>Integration: <span class=\"urTxtStd urVt1\"><a href=\"/notes/2241931\" target=\"_blank\">http://service.sap.com/sap/support/notes/2241931</a></span></li>\n<li>Defense and Security: <span class=\"urTxtStd urVt1\"><a href=\"/notes/2254377\" target=\"_blank\">http://service.sap.com/sap/support/notes/2254377</a></span></li>\n<li>Consumer Products: <span class=\"urTxtStd urVt1\"><a href=\"/notes/2274495\" target=\"_blank\">http://service.sap.com/sap/support/notes/2274495</a></span></li>\n<li>Sales and Distribution: <span class=\"urTxtStd urVt1\"><a href=\"/notes/2333901\" target=\"_blank\">http://service.sap.com/sap/support/notes/2333901</a></span></li>\n<li>Please check the linked SAP notes for further restrictions.  </li>\n</ul>\n</ol>", "noteVersion": 43}]}, {"note": "2217202", "noteTitle": "2217202 - SAP S/4 HANA Simplification Item: Waste Management (EHS-WA)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">With <em><span arial',sans-serif;\"=\"\">SAP EHS Management</span></em> as part of <em><span arial',sans-serif;\"=\"\">SAP ERP</span></em>, you can use the <em><span arial',sans-serif;\"=\"\">Waste Management</span></em> (EHS-WA) function. This function is no longer supported in <em><span arial',sans-serif;\"=\"\">SAP S/4HANA</span></em>.  <br/>For this reason, you need to check if you use development objects belonging to this function in your custom code prior to converting to <em><span arial',sans-serif;\"=\"\">SAP S/4HANA</span></em>. If so, you need to adapt your code.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA simplification,  Waste Management, EHS-WA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">SAP Note</span> <a href=\"/notes/2241080\" target=\"_blank\">2241080</a> <span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">provides information about how you can use the custom code check tool to analyze if you are using any ABAP development objects in your custom code that are no longer supported.</span></p>\n<p><span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">If you are using any development objects that are no longer supported, adapt your coding to ensure that these objects are no longer included in your custom code. <br/>If you need some of the development objects that are no longer supported, you can, for example, copy these objects into your customer namespace and adapt your custom code to use the copied versions.</span></p>", "noteVersion": 4, "refer_note": [{"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}, {"note": "2241080", "noteTitle": "2241080 - SAP S/4HANA: Content for checking customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to check your customer specific ABAP code via ABAP Test Cockpit (ATC) for compatibility with SAP S/4HANA as described in SAP note <a href=\"/notes/0002190420\" target=\"_blank\">2190420</a>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Custom Code Check, ABAP Test Cockpit, ATC, Simplification Database, System Conversion, Release Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In order to provide accurate results, ATC requires up to date content describing changes done to SAP objects in SAP S/4HANA (= Simplification Database).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP provides the most recent content for the Simplification Database of SAP S/4HANA as a ZIP file in the Software Center, which you can download and import into your system.</p>\n<p><strong>Downloading the Simplification Database Content</strong></p>\n<ul>\n<li>Go to <a href=\"https://me.sap.com/softwarecenter\" target=\"_blank\">https://me.sap.com/softwarecenter</a></li>\n<li>Search for Component \"CCMSIDB\"</li>\n<li>Download the displayed \"Simplification Database Content\" (most recent version: patch level 18 - creation date: 2024-02-28)</li>\n<ul>\n<li>When using content version 2021-09-28 or newer, please ensure that you have also implemented SAP note 3039646. With this content version the usage detection accuracy for DDIC objects has been improved. But without the corresponding check logic from SAP note 3039646, this content version will lead to an increased number of false positives.</li>\n</ul>\n</ul>\n<p><strong>Importing the Simplification Database Content</strong></p>\n<p>For details how to import the Simplification Database content into your ATC system please refer to the ABAP Platform help:</p>\n<p>   &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/4355509bf8a75f6be10000000a1553f6.html\" target=\"_blank\">Application Development on AS ABAP</a><br/>      &gt; Customer-Specific ABAP Development<br/>        &gt; Custom Code Migration<br/>         &gt; SAP S/4HANA Conversion<br/>           &gt; <a href=\"https://help.sap.com/docs/ABAP_PLATFORM_NEW/7bfe8cdcfbb040dcb6702dada8c3e2f0/41316510041a4b53974ee74ab6d52512.html\" target=\"_blank\">Simplification Database</a></p>\n<p>When updating the Simplification Database content to the most recent version, please also ensure, that you update the check logic to the most recent version,  by implementing all relevant, new notes from SAP note 2436688. Otherwise you might get wrong check results (e.g. false positives).</p>\n<p>Please be aware, that upon importing the Simplification Database content into your code analysis system you might sometimes get a message \"Details on download of simplification notes: note &lt;note number&gt; incomplete.\" This only means the corresponding SAP note, describing a specific custom code impact is temporarily not accessible as it's being updated by the responsible development area. Or that the connection from your system to SAP service marketplace for note downloads has not been setup. This has no impact on the successful import of the Simplification Database content into your code analysis system. And this will not negatively impact the result of the custom code analysis.</p>\n<p>Though this note download mechanism can cause long import times for the Simplification Database content or even timeouts in systems that are not properly configured for downloading SAP notes. In this case you can implement SAP note 3221402. This adds an option to the Simplification Database upload dialog, to skip the note downloads. The only drawback is, that then the note titles shown next to the ATC results might not be up to date in all cases. But the correct notes are shown anyway.<a href=\"/notes/3221402\" target=\"_blank\" title=\"3221402  - Skip download of SAP Notes during the import of simplification database\"><br/></a></p>\n<p><strong>Additional Information</strong></p>\n<ul>\n<li>The main focus of the Simplification Database Content is to identify the custom code impact of Simplification Items (<a href=\"https://launchpad.support.sap.com/#/sic/overview\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/overview</a>) and other major functional or technical changes in SAP S/4HANA.</li>\n<li>Please be aware that code checks based on the SIDB content do not replace the need to properly test relevant custom code during maintenance events, in order to see if there is any impact from syntactical or semantical changes done on SAP side.</li>\n<li>For prioritization of issues found by the SAP S/4HANA ATC checks, please take the category of the findings into consideration. Especially the findings under the \"Non-strategic...\" categories indicate usage of objects which will become obsolete only at a later point in time. The ATC errors of this category only serve as a reminder, that such objects will become obsolete in future releases. For example compatibility pack functionality (see SAP note 2269324 for details). So these findings do not necessarily have to be fixed already in earlier SAP S/4HANA releases. Please refer to the individual notes shown along the \"Non-strategic...\" findings for details.</li>\n<li>The Simplification Database content covers all available SAP S/4HANA releases. This e.g. allows you, as preparation for your SAP S/4HANA conversion project, to scan your custom code against different SAP S/4HANA target releases with the same Simplification Database content. Which is helpful if the exact target version of your SAP S/4HANA conversion project is not yet decided on.</li>\n<li>It's recommended to use this content for ATC checks in preparation of a SAP S/4HANA system conversion or release upgrade. But it should also be used to continuously check the SAP S/4HANA compliance of customer specific coding in development projects on SAP S/4HANA. </li>\n<li>Only customers with a valid SAP S/4HANA license are able to download this SAP S/4HANA content.</li>\n</ul>\n<p>Changelog:</p>\n<ul>\n<li>Patchlevel 16:</li>\n<ul>\n<li>Automatic calculation of deletions. As of this patch level the Simplification Database also contains all main objects (R3TR) that have been deleted between two consecutive SAP S/4HANA releases, even if the deletion is not related to any Simplification Item or major functional or technical change (e.g. deletions of unused / orphaned objects).</li>\n</ul>\n<li>Patchlevel 17:</li>\n<ul>\n<li>Custom code content for various Simplification Items (especially related to compatibility scope) has been added or updated. Focus of the updates is SAP S/4HANA 2023 initial shipment.</li>\n<li>Automatic calculation of deletions now also considers objects that have been deleted between SAP ERP EHP8 and SAP S/4HANA, that are not covered by other specific Simplification Items.</li>\n</ul>\n<li>Patchlevel 18:</li>\n<ul>\n<li>Custom code content for various Simplification Items has been added or updated. Focus of the updates is SAP S/4HANA 2023 FPS1.</li>\n<li>Improvements to the automatic calculation of deletions. This will reduce fealse positives related to SAP note 2296016 (e.g. if an object did exist in SAP ERP, was deleted in early SAP S/4HANA versions, but reintroduced again in higher SAP S/4HANA versions, this is no longer considered as a deletion) as well as reduce the overlap of findings for SAP note 2296016 with findings for other Simplification Items. Also FI/CO tables (see SAP note 3414643) that have technically been deleted, but are covered by compatibility views are no longer considered as a deletion.</li>\n</ul>\n</ul>\n<p>In case of</p>\n<ul>\n<li>issues with importing the Simplification Database content into your system please open an incident on BC-DWB-CEX.</li>\n<li>general questions related to the Simplification Database Content please open an incident on CA-TRS-TDBC.</li>\n<li>questions on individual custom code SAP notes please open an incident on the application component of the respective SAP note.</li>\n</ul>", "noteVersion": 37}]}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Decide on your future SAP EHS Waste Management strategy and impact on conversion decision"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adjust or remove your custom code referring to unavailable SAP EHS Waste Management functionality"}, {"Activity": "Interface Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adjust or remove interfaces referring to unavailable SAP EHS Waste Management functionality"}]}