{"guid": "00109B1318B61ED9B6DA33FBCC8720FA", "sitemId": "SI5: Logistics_WM", "sitemTitle": "S4TWL - Wave Management", "note": 2889652, "noteTitle": "2889652 - S4TWL - Wave Management / Collective Processing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The functionality of Wave Management / collective Processing in Warehouse Management (LE-WM) is not the target architecture anymore within SAP S/4HANA, on-premise edition (Functionality available in SAP S/4HANA on-premise edition 1511 delivery but not considered as future technology. Functional equivalent is available.). The (already available) alternative functionality is Wave Management in Extended Warehouse Management (SAP EWM).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No immediate influence on business processes expected related to Wave Management / collective Processing. The functionality Wave Management / collective Processing of Warehouse Management (LE-WM) is still available within the SAP S/4HANA architecture stack as part of the compatibility packages. All related functionalities can be used in an unchanged manner for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As part of your SAP S/4HANA system conversion or upgrade project you need to decide, if you want to continue using Wave Management / collective Processing in Warehouse Management (LE-WM) for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information), or if you want to migrate to Extended Warehouse Management (SAP EWM).</p>\n<p>If your decision is to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project.<br/>Please check as well the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement.</p>\n<p>Once you migrate to Extended Warehouse Management (SAP EWM), but latest with expiry of the compatibility scope license (see SAP note 2269324 for more information), you need to remove all usages of Wave Management development objects delivered by SAP from your custom code, as these are no longer supported after expiry of the compatibility scope license. Such usages are shown by the SAP S/4HANA specific custom code checks in ABAP Test Cockpit (see SAP note 2241080).<em></em></p>", "noteVersion": 3, "refer_note": [{"note": "2882809", "noteTitle": "2882809 - Scope Compliance Check for Stock Room Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are running warehouses with LE-WM and plan a system conversion to SAP S/4HANA. As described in SAP Note 2577428, LE-WM is in a compatibility mode and mustn’t be used beyond 2025 in SAP S/4HANA. From SAP S/4HANA 1909, Stock Room Management is available for use in place of LE-WM. Some features that are available in LE-WM are not part of Stock Room Management. You therefore need to know if the functionality you are currently using in your system is covered by Stock Room Management or whether you are using functionality beyond its scope.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>STOCKROOM_COMPLIANCE_CHECK, LE-WM, Stock Room Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the note and execute the Compliance Check Report in every productive client in your system to check your warehouses. If your system is compliant with the scope of Stock Room Management, you can use your current setup beyond 2025 with Stock Room Management.</p>\n<p>Steps to execute the report:</p>\n<p class=\"StandardParagraph\">Start the ABAP Editor transaction (SE38) and enter STOCKROOM_COMPLIANCE_CHECK in the Program field. Choose Execute.</p>\n<p class=\"StandardParagraph\">Select the warehouse or warehouses you want to check. To check all the warehouse in your system, leave the Warehouse Number field blank.</p>\n<p class=\"StandardParagraph\">Enter the date range you want to check.</p>\n<p class=\"StandardParagraph\">Choose Execute.</p>", "noteVersion": 3}, {"note": "2269324", "noteTitle": "2269324 - Compatibility Scope Matrix for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Through the Compatibility Packages (CP) listed in the attachment \"Compatibility Scope Matrix\", SAP provides a limited use right to SAP S/4HANA on-premise customers to run certain classic SAP ERP solutions on their SAP S/4HANA installation. Condition is that these customers have licensed the applicable solutions as set forth in their License Agreements. Compatibility Pack use rights may apply to selected private cloud deployments as well, without the prerequisite of an on-premise classic license. Please refer to the respective Service Description Guide for details.</p>\n<p>This use right expires on Dec 31, 2025, and is available to installed-base as well as net-new customers.</p>\n<p>The <a href=\"https://news.sap.com/2020/02/sap-s4hana-maintenance-2040-clarity-choice-sap-business-suite-7/\" target=\"_blank\">announcement</a> about the extension of maintenance for Business Suite solutions has no influence on the end of compatibility pack use rights - they will be terminated after 2025.<sup>(1)</sup></p>\n<p>Besides reading the attached documents, SAP recommends the latest SAP Community webinar on the topic: <a href=\"https://www.youtube.com/live/muPrV5J7ffM?feature=shared\" target=\"_blank\">https://www.youtube.com/live/muPrV5J7ffM</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA Compatibility Scope Matrix, Way Forward</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In the “Attachments” section, the “Overview Presentation” and “Detail FAQ” documents explain the “why”, “what” and “how” of CPs. For functional details, please also refer to the Feature Scope Description (FSD) of SAP S/4HANA, <a href=\"https://help.sap.com/doc/e2048712f0ab45e791e6d15ba5e20c68/latest/\" target=\"_blank\">latest version</a> (or through http://help.sap.com/s4hana), chapter 5.</p>\n<p>The “Way Forward” presentation and overview list, also under “Attachments”, provide information about solution alternatives in the perpetual scope of SAP S/4HANA. They describe the status of each Compatibility Pack and provide links to more detailed information about its strategy. This information is provided via SAP standard documentation, such as the Roadmaps and the Innovation Discovery. The “Item ID” helps to cross-reference between the original, descriptive CP matrix and the future-oriented “Way Forward” spreadsheet.</p>\n<p>This blog <a href=\"https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/\" target=\"_blank\">https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/</a> provides more details about the procedure for Compatibility Packs after their use right expiry in 2025/2030.</p>\n<p>SAP is planning regular updates of the attachments. All forward-looking information is non-binding and with preview character. ​The released SAP Roadmaps are the proper source of information.</p>\n<p>For questions and feedback about this note and its content, please create a customer message on component XX-SER-REL with the prefix ‘CompScope’​.</p>\n<p>(1) In the exceptional cases of CS, LE-TRA and PP-PI, the usage right to their respective compatibility pack items (cf. matrix) terminates at the end of 2030.</p>", "noteVersion": 84}], "activities": [{"Activity": "Implementation project required", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "If decided, implement EWM."}, {"Activity": "Business Decision", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Decide, if EWM shall be implemented as replacement."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "Remove and clean up your WM specific custom developments if you migrate to EWM."}]}