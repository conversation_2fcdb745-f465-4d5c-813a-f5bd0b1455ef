{"guid": "6CAE8B3E8C3B1ED792D9EB9E8D06A0C6", "sitemId": "BW502: Business Planning & Consolidation", "sitemTitle": "BW4SL - Planning", "note": 2443189, "noteTitle": "2443189 - BW4SL - Business Planning & Consolidation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Objects related to Business Planning &amp; Consolidation functionality are not available with core SAP BW/4HANA. This includes:</p>\n<ul>\n<li>SAP BW Integrated Planning (BW-IP)</li>\n<li>Planning Application Kit (PAK)</li>\n<li>SAP Business Planning &amp; Consolidation (embedded)</li>\n<li>SAP Business Planning &amp; Consolidation (standard)</li>\n</ul>\n<p>To use planning functionality with SAP BW/4HANA, you will need to install the SAP BPC add-on, version for SAP BW/4HNANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>AABC, AADT, AAPP, AAPS, ABPC, ABPF, ABRU, ACGA, ACGP, ACGS, ACLB, ACTR, ADAF, ADEE, ADEI, ADEL, ADIM, ADMC, ADMD, ADMF, ADMG, ADML, ADMP, ADMS, ADTG, AFLC, AFLD, AFLE, AFLG, AJUT, AKPI, AMBR, AMPF, APPS, ARTP, ASPD, ASPF, ASPR, ATEM, ATPF, AWSS, BAOE, ALVL, BBPF, BDAP, ENVM, MODL, PLCR, PLDS, PLSE, PLSQ, PLST, TEAM, WKSP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are supported with or can be converted to SAP BW/4HANA.</p>\n<p>See node: Required actions for switch to B4H mode --&gt; BW Planning --&gt; BPC Embedded / BPC Standard</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Objects related to Business Planning can be used with SAP BPC 11.0, version for SAP BW/4HNANA (or higher). Install the SAP BPC add-on (technical name BPC4HANA) on the target SAP BW/4HANA system.</p>\n<p><strong>Related Information</strong></p>\n<p><a href=\"https://help.sap.com/viewer/p/SAP_BPC_VERSION_BW4HANA\" target=\"_blank\">SAP BPC, version for SAP BW/4HANA documentation</a></p>", "noteVersion": 2, "refer_note": [{"note": "2343286", "noteTitle": "2343286 - Planning on BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use planning in BW4HANA. You need the Business Object Planning and Consolidation (BPC) license and product for this.</p>\n<p>You might have used planning in BW with integrated planning (BW-IP), HANA optimized wiht planning application kit (PAK) or with BPC before (see notes 2061095, 1637199, 1919631)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BPC, Business Planning and Consolidation, integrated planning, BW-IP, PAK, Planning Application Kit, BW-BPS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have installed BW4HANA. Now you want to also leverage planning similar to Bw-IP, PAK or BPC on Netweaver.</p>\n<p> You need to install also the BPC4HANA Addon. All kind of planning on BW4 requires the 'SAP BusinessObjects Planning and Consolidation, version for BW/4HANA' license.</p>\n<p>Planning solutions based on BW-BPS are not supported in BW/4HANA and have to be migrated manually to a BPC embedded model.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Following switches exist to de-/activate planning in BPC/4HANA which requires the above license:</p>\n<ul>\n<li>Main switch to generally activate deep HANA integration: table view RSPLS_HDB_ACT in SM30. If not yet existing, create a new entry for \"Deep HANA Integration Active\" (HANA_ACT) and mark the checkbox 'Function Active' in order to activate the ABAP Planning Applications KIT.</li>\n</ul>\n<p>HANA optimized processing can in also the controlled by:</p>\n<ul>\n<li>In rare cases some scenarios might cause problems when executed in HANA. Thus the table view RSPLS_HDB_ACT_IP in SM30 can be used to deactivate the ABAP Planning Applications KIT for individual InfoProviders. Choose the real-time InfoCube, direct update DSO or aDSO you do not want to use for HANA planning. This feature is available in order to guarantee a smooth transition from existing BW-IP scenarios and to avoid additional roundtrips due to the restrictions mentioned below. <br/><br/>Every data store object must be HANA optimized to run in HANA. In case of   CompositeProvider all planning enabled part providers must be HANA enabled (not de -activated in the table mentioned above) and also HANA optimized. One disabled or not HANA optimized InfoProvider disables the entire  CompositeProvider for HANA processing. By default all InfoProviders not mentioned in the table view RSPLS_HDB_ACT_IP are HANA enabled. It is only necessary to add an InfoProvider when it should be HANA disabled. This can be done by adding the InfoProvider to the table and de-selecting the ‘Functn. Active’ checkbox.</li>\n</ul>\n<ul>\n<li>There is a global “emergency exit switch” that can be set using report SAP_RSADMIN_MAINTAIN with parameter object RSPLS_HDB_PROCESSING_OFF. This switch should only be used in rare special circumstances.<br/>Possible values are:</li>\n</ul>\n<ul>\n<ul>\n<li>'F': Force Off         No HANA optimized processing, cannot be overruled any user</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>'X': Off           No HANA optimized processing, but can be overruled for single users for special purposes</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Not set: switch inactive (default and usual value) <br/>HANA processing will run on the intended planning function etc., but can be switched off for single users (traces, comparison etc.)</li>\n</ul>\n</ul>\n<ul>\n<li>For project studies/PoC or special tasks like special process chains one can set a user specific PAK activation switch: Set/Get-Parameter RSPLS_HDB_SUPPORT (set in transaction SU01)<br/>Possible Values are:</li>\n</ul>\n<ul>\n<ul>\n<li>'HDB_ON': HANA optimized processing when possible also if global switch set   'X' (OFF) .</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>'HDB_OFF': HANA optimized processing disabled independently from the global setting.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Not set: inactive, general system settings are used (including the global switch)</li>\n</ul>\n</ul>\n<ul>\n<li>Trace information like whether a planning function is executed HANA optimized or the information about changed and created records are only displayed and written to the log when the user parameter RS_DEBUGLEVEL is set to 2 (or higher).</li>\n<li>If you use transaction RSPLAN and run a step in a planning sequence by pressing 'Execute Step with Trace' (as opposed to 'Execute Step') the function currently still runs in the IP (ABAP) runtime since we need to retrieve information for a detailed display. See knowledge base article 1911914 and 2)</li>\n</ul>\n<p><br/> <br/>Below we provide a list of features which are supported to run fully in HANA and also a list of features which are still executed in ABAP application server. Those will cause an additional data round trip and might harm the performance.</p>\n<p>Also a list of functions is added which are not supported on HANA. We have been working on enabling further functions in HANA (and continue to do so) so this list is subject to change. Some of the topics refer to a comment at the end of the note indicating the potential to eliminate the restrictions over time. In order to check whether a scenario in principle can run HANA optimized we provide the report RSPLS_PLANNING_ON_HDB_ANALYSIS.  .</p>\n<p>1. List of features which run fully in HANA</p>\n<ul>\n<li>Following Planning Functions are only executed in HANA:</li>\n<ul>\n<li>Planning Function Type: 0RSPL_COPY                    Copy<br/> </li>\n<li>Planning Function Type: 0RSPL_REPOST                Repost<br/> </li>\n<li>Planning Function Type: 0RSPL_REVALUATION       Revaluation<br/> </li>\n<li>Planning Function Type: 0RSPL_DELETE                Delete<br/> </li>\n<li>Planning Function Type: 0RSPL_DELETE_CR          Deletion of Invalid Combinations <br/> </li>\n<li>Planning Function Type: 0RSPL_SET_VALUES        Set Key Figure Values<br/> </li>\n<li>Planning Function Type: 0RSPL_DISTR_REFDATA  Distribution by Reference Data<br/> </li>\n<li>Planning Function Type: 0RSPL_CREATE_CR         Generate Combinations<br/> </li>\n<li>Planning Function Type: 0RSPL_REPOST_CR         Repost on Basis of Characteristic Relationship <br/> </li>\n<li>Planning Function Type: 0RSPL_DISTR_KEY         Distribution with Keys                                   <br/> </li>\n<li>Planning Function Type: 0RSPL_FORMULA            Formula<br/>                                                                       (see restrictions below)</li>\n<li>Planning Function Type: 0RSPL_UNIT_CONV         Unit Conversion</li>\n<li>Planning Function Type: 0RSPL_CURR_CONV        Currency Translation </li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li>delta mode flag switched on in planning functions is supported. However the determination of the filter is done in ABAP and can cause more time then executing all records in HANA. It should therefore be only used for functional reasons and not due to better performance as in the classic IP case</li>\n<li>Disaggregation in the Query is executed in HANA if</li>\n<ul>\n<li>the query is modeled directly on the aggregation level</li>\n<li>for all restricting characteristics all contributing key figures are only restricted to one single value <br/> </li>\n</ul>\n<li>Following characteristic relationships are supported to be fully checked in HANA</li>\n<ul>\n<li>Characteristic relationship of type attribute</li>\n<li>Characteristic relationship of type DSO</li>\n<li>Characteristic relationships of type hierarchy </li>\n<li>Characteristic relationship of type transient. This is only available through the ABAP BICS interface<br/> </li>\n</ul>\n<li>Following data slices are supported to be fully checked in HANA</li>\n<ul>\n<li>Data slices of type selection  <br/> </li>\n</ul>\n<li>Logging BADI  <br/> </li>\n<li>Key-figures in the InfoCube or aDSO with aggregate ‘SUM’. For direct update DSO we also allow ‘NO2’. The usage of MIN/MAX in read only key figure.</li>\n<li> In this case where the model allows the algorithm to fully execute in HANA also the data of the planning buffer only resists in HANA (see above). Then also  the save of plan buffers is fully executed within HANA only.</li>\n</ul>\n<p>2. List of features still executed in ABAP application server with data round trip and not fully HANA optimized</p>\n<ul>\n<li>Planning:</li>\n<ul>\n<li>Own planning function type: any planning function exit as own type as long as no interface IF_RSPLFA_SRVTYPE_TREX_EXEC or IF_RSPLFA_SRVTYPE_TREX_EXEC_R is implemented leveraging SQL Script for HANA processing. See Standard BW4 Documentation or note 1870369.</li>\n<li>Planning function type: 0RSPL_FORECASTING   Forecast<br/>As workaround one can use a SQL Script based implementation (see footnote 1) which includes some PAL functions<br/>(See <a href=\"http://help.sap.com/hana/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf\" target=\"_blank\">http://help.sap.com/HANA/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf</a> and <a href=\"http://www.saphana.com/community/hana-academy/#predictive-analytics-library\" target=\"_blank\">http://www.sapHANA.com/community/HANA-academy/#predictive-analytics-library</a>)<br/>  </li>\n</ul>\n<li>Planning function type 'FOX' does not support the following commands and is in those cases executed in the application server</li>\n<ul>\n<li>CALL FUNCTION (1)</li>\n<li>ABAP BREAK-POINT command is ignored</li>\n<li>FOX key words are not supported for other usage like variable names. E.g. DATA I TYPE I is not allowed but DATA J TYPE I is allowed.</li>\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on standard time characteristics like 0CALYEAR or characteristic referencing those</li>\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on characteristics which don't have a master data table, which is a BW object like HANA View based info objects.</li>\n<li>Using a hierarchy node variable in VARC or similar statements</li>\n<li>Using InfoObjects outside the aggregation level (e.g. in DATA statements)</li>\n<li>The FOX features ‘modularization’ or ‘forms’  are currently not yet supported in PAK and lead to the processing in ABAP application server. </li>\n<li>The new FOX feature ‘directly reading from a non-input enabled DSO ‘  is  supported to run HANA optimized. Only in case the DSO contains non-key fields the the exectuion however is still on the ABAP stack. </li>\n<li>The feature of transient attributes is not be supported to run in HANA for time dependent attributes in FOX (e.g. ATRV). Then the FOX is processed on ABAP side.</li>\n</ul>\n</ul>\n<p>       In general a HANA check of FOX exit in the Fox Editor</p>\n<ul>\n<li>If conditions exist on a planning function and</li>\n<ul>\n<li>More on has more than 1 condition on this planning function or</li>\n<li>The global filter contains selection on attributes, but the condition contains selection on the basic characteristics or</li>\n<li>The filter contains selections on hierarchy nodes and the condition has selections on the characteristic the hierarchy is defined on or</li>\n<li>The selection contains selections which are not single values restriction and those selections are not one by the same in the global filter.<br/> </li>\n</ul>\n<li>Disaggregations in the Query are executed not in the database if</li>\n<ul>\n<li>Planning model uses MultiProvider on top of an aggregation level</li>\n<li>A formula is used as reference key figure for disaggregation</li>\n<li>The key figure is restricted to multiple values for a given characteristic except several single values. E.g. intervals, or hierarchy nodes in the restriction lead to execution in the application server.</li>\n</ul>\n</ul>\n<p>          Here the additional impact of a round trip is not as considerable as for planning functions. Only the generated data has to be pushed to the database.</p>\n<p> </p>\n<p>3. List of features which cannot be executed with the ABAP applications planning KIT in HANA and leads to switch to BW-IP (for the entire model, not a single step)</p>\n<ul>\n<li>TIMS &amp; DATS key-figures in the InfoCube. We recommend to use data type DEC and instead of TIMS or DATS</li>\n</ul>\n<ul>\n<li>Master data access of type Own Implementation or Remote (Direct Access). We allow (as exemption) characteristics referencing to standard BW time characteristics, source system (0SOURSYSTEM) or InfoProvider (0INFOPROV) when using their standard implementations.</li>\n</ul>\n<ul>\n<li>Virtual master data, including hierarchies and technical master data which are not persisted in tables. This also affects compounded characteristics leveraging those master data. Examples are InfoObjects that exist in BW but without stored data in the InfoObject tables. Exceptions are time and system characteristics which also work in the HANA optimized case.</li>\n</ul>\n<ul>\n<li>Virtual and referencing key-figures</li>\n</ul>\n<ul>\n<li>Transient characteristics</li>\n</ul>\n<ul>\n<li>Certain constraints by characteristic relationship cannot be checked in HANA yet. This includes</li>\n</ul>\n<ul>\n<ul>\n<li>characteristic relationships of type EXIT as long as the interface IF_RSPLS_CR_EXIT_HDB is not implemented. One can either leveraging SQL Script for HANA processing or use ABAP as fall back (see note 1956085 - BW-IP (PAK): ABAP as fallback for exit characteristic relations and data slices on SAP HANA). See footnote (1)</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li>Certain constraints by data slices cannot be checked in HANA yet. This includes</li>\n</ul>\n<ul>\n<ul>\n<li>data slices of type EXIT as long as the interface IF_RSPLS_DS_EXIT_HDB is not implemented leveraging SQL Script for HANA processing. See footnote (1)</li>\n</ul>\n</ul>\n<ul>\n<li>CP-Problem as explained in the long text of message BRAIN 313 (see note 2095418) </li>\n<li>HANA views with placeholder in virtual providers or as part providers in a CompositeProvider</li>\n</ul>\n<p> </p>\n<p>Footnote</p>\n<ul>\n<ul>\n<li>(1) By conception, we will not be able to support the full flexibility of an ABAP EXIT within HANA, in particular, when other ABAP sources are leveraged in the implementation. However, there are EXIT implementations using SQL script that can be executed in HANA.    </li>\n</ul>\n</ul>", "noteVersion": 2, "refer_note": [{"note": "1637199", "noteTitle": "1637199 - Using the planning applications KIT (PAK)", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Starting with SAP BW 7.30 SP5 we have introduced the Planning Applications KIT <strong>(PAK)</strong> which allows execution of BW-IP standard functions in HANA. The following needs to be considered when using the ABAP Planning Applications KIT:</p>\n<ul>\n<li>The use of the Planning Applications KIT requires the following license: 'SAP BusinessObjects Planning and Consolidation, version for SAP NetWeaver'. If you do not have acquired this license, please contact your account executive for further information.</li>\n</ul>\n<ul>\n<li>Some standard BW-IP functions are not accelerated with the ABAP Planning Applications KIT or lead to errors when executed in HANA.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Hana optimized Planning, planning applications KIT, disable, deactivate, unable, PAK, HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have acquired the license mentioned above and want to leverage BW-IP standard functions in HANA. Certain features available today in BW-IP (and PAK) are only supported in ABAP and might lead to additional data exchanges between the ABAP layer and the HANA database which can have a bad impact on the system performance. Also some ABAP specific features are not supported in the HANA optimized execution (see below for a list of all these features). Some remodeling might be required to leverage full HANA optimization when starting from an existing scenario.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Following switches exist to de-/activate ABAP Planning Applications KIT:</p>\n<ul>\n<li>Main switch to generally activate the Planning Applications KIT (license required): table view RSPLS_HDB_ACT in SM30. If not yet existing, create a new entry for \"Deep HANA Integration Active\" (HANA_ACT) and mark the checkbox 'Function Active' in order to activate the ABAP Planning Applications KIT.</li>\n</ul>\n<ul>\n<li>In rare cases some scenarios might cause problems when executed in HANA. Thus the table view RSPLS_HDB_ACT_IP in SM30 can be used to deactivate the ABAP Planning Applications KIT for individual InfoProviders. Choose the real-time InfoCube, direct update DSO or aDSO you do not want to use for HANA planning. This feature is available in order to guarantee a smooth transition from existing BW-IP scenarios and to avoid additional roundtrips due to the restrictions mentioned below. <br/><br/>Every real-time InfoCube must be HANA optimized to run in HANA. In case of a MultiProvider or CompositeProvider all planning enabled part providers must be HANA enabled (not de -activated in the table mentioned above) and also HANA optimized. One disabled or not HANA optimized InfoProvider disables the entire Multi- or CompositeProvider for HANA processing. By default all InfoProviders not mentioned in the table view RSPLS_HDB_ACT_IP are HANA enabled. It is only necessary to add an InfoProvider when it should be HANA disabled. This can be done by adding the InfoProvider to the table and de-selecting the ‘Functn. Active’ checkbox.</li>\n</ul>\n<ul>\n<li>There is a global “emergency exit switch” that can be set using report SAP_RSADMIN_MAINTAIN with parameter object RSPLS_HDB_PROCESSING_OFF. This switch should only be used in rare special circumstances.<br/>Possible values are:</li>\n</ul>\n<ul>\n<ul>\n<li>'F': Force Off         No HANA optimized processing, cannot be overruled any user</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>'X': Off           No HANA optimized processing, but can be overruled for single users for special purposes</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Not set: switch inactive (default and usual value) <br/>HANA processing will run on the intended planning function etc., but can be switched off for single users (traces, comparison etc.)</li>\n</ul>\n</ul>\n<ul>\n<li>For project studies/PoC or special tasks like special process chains one can set a user specific PAK activation switch: Set/Get-Parameter RSPLS_HDB_SUPPORT (set in transaction SU01)<br/>Possible Values are:</li>\n</ul>\n<ul>\n<ul>\n<li>'HDB_ON': HANA optimized processing when possible also if global switch set   'X' (OFF).</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>'HDB_OFF': HANA optimized processing disabled independently from the global setting.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Not set: inactive, general system settings are used (including the global switch)</li>\n</ul>\n</ul>\n<ul>\n<li>Trace information like whether a planning function is executed HANA optimized or the information about changed and created records are only displayed and written to the log when the user parameter RS_DEBUGLEVEL is set to 2 (or higher).</li>\n<li>If you use transaction RSPLAN and run a step in a planning sequence by pressing 'Execute Step with Trace' (as opposed to 'Execute Step') the function currently still runs in the IP (ABAP) runtime since we need to retrieve information for a detailed display. See knowledge base article 1911914 and 2)</li>\n</ul>\n<p><br/> <br/>Below we provide a list of features which are supported to run fully in HANA and also a list of features which are still executed in ABAP application server. Those will cause an additional data round trip and might harm the performance.</p>\n<p>Also a list of functions is added which are not supported on HANA. We have been working on enabling further functions in HANA (and continue to do so) so this list is subject to change. Some of the topics refer to a comment at the end of the note indicating the potential to eliminate the restrictions over time. In order to check whether a scenario in principle can run HANA optimized we provide the report RSPLS_PLANNING_ON_HDB_ANALYSIS (note 1824516 or 7.30 SP 10/ 7.31 SP8 and note 2336691 for better FOX support). In 7.4 SP5 and follow up releases they are supported as well if not mention explicit.</p>\n<p>1. List of features which run fully in HANA</p>\n<ul>\n<li>Following Planning Functions are executed in HANA:\r\n<ul>\n<li>Planning Function Type: 0RSPL_COPY                        Copy<br/>  </li>\n<li>Planning Function Type: 0RSPL_COPY_NO_ZEROS      Copy ( without records where all keyfigures are zero ) <br/><br/></li>\n<li>Planning Function Type: 0RSPL_CREATE_CR               Generate Combinations<br/>  </li>\n<li>Planning Function Type: 0RSPL_CURR_CONV              Currency Translation<br/>                                   requires BW 7.4 SP8 or note 2076956 for BW 7.3/7.31 and Hana Revision &gt;= *********</li>\n<li>Planning Function Type: 0RSPL_DELETE                     Delete<br/><br/></li>\n<li>Planning Function Type: 0RSPL_DELETE_DSO             Delete DSO data physically<br/>                                   requires note  1855154 or 7.30 SP10/7.31 SP9/7.40 SP5 <br/>  </li>\n<li>Planning Function Type: 0RSPL_DELETE_CR                Deletion of Invalid Combinations<br/>                                   requires note  1778939 or 7.30 SP9/7.31 SP7<br/>  </li>\n<li>Planning Function Type: 0RSPL_DELETE_CR_DSO        Physical Deletion of Invalid Combinations in DSOs<br/>                                   requires note  1855154 or 7.30 SP10/7.31 SP9/7.40 SP5<br/><br/></li>\n<li>Planning Function Type: 0RSPL_DISTR_KEY                 Distribution with Keys<br/>                                   requires BW 7.4 SP8 and HANA revision &gt;= 1.00.73<br/>                                   or only note 1821899 or 7.30 SP10/7.31 SP9/7.4 SP5 for distribution type 'Distribute Not-Assigned (#)'. <br/><br/></li>\n<li>Planning Function Type: 0RSPL_DISTR_REFDATA          Distribution by Reference Data<br/>  </li>\n<li>Planning Function Type: 0RSPL_FORMULA                    Formula<br/>                                   (see restrictions below)<br/><br/></li>\n<li>Planning Function Type: 0RSPL_REPOST                       Repost<br/>   </li>\n<li>Planning Function Type: 0RSPL_REPOST_DSO               Repost Data and Delete DSO Data physically<br/>                                   requires note  1912307 or 7.30 SP11/7.31 SP10/7.40 SP5 <br/>  </li>\n<li>Planning Function Type: 0RSPL_REPOST_CR                  Repost on Basis of Characteristic Relationship<br/>                                   requires note 1855154 or 7.30 SP10/7.31 SP9<br/>  </li>\n<li>Planning Function Type: 0RSPL_REPOST_CR_DSO          Repost DSO Data on Basis of Characteristic Relationships<br/>                                   requires note  1855154 or 7.30 SP10/7.31 SP9/7.40 SP5 </li>\n<li>Planning Function Type: 0RSPL_REVALUATION               Revaluation<br/><br/></li>\n<li>Planning Function Type: 0RSPL_SET_VALUES                 Set Key Figure Values<br/><br/></li>\n<li>Planning Function Type: 0RSPL_UNIT_CONV                  Unit Conversion<br/>                                   requires BW 7.4 SP8, or note 2076956 for BW 7.3/7.31 and Hana Revision &gt;= *********</li>\n</ul>\n</li>\n</ul>\n<p> </p>\n<ul>\n<li>delta mode flag switched on in planning functions is supported. However, the determination of the filter is done in ABAP and can cause more time then executing all records in HANA. It should therefore be only used for functional reasons and not due to better performance as in the classic IP case</li>\n<li>Disaggregation in the Query is executed in HANA if</li>\n<ul>\n<li>the query is modeled directly on the aggregation level</li>\n<li>for all restricting characteristics all contributing key figures are only restricted to one single value <br/> </li>\n</ul>\n<li>Following characteristic relationships are supported to be fully checked in HANA</li>\n<ul>\n<li>Characteristic relationship of type attribute</li>\n<li>Characteristic relationship of type DSO</li>\n<li>Characteristic relationships of type hierarchy is supported with 7.30 SP12 or 7.31 SP12 or note 1984344 </li>\n<li>Characteristic relationship of type transient. This is only available through the ABAP BICS interface<br/> </li>\n</ul>\n<li>Following data slices are supported to be fully checked in HANA</li>\n<ul>\n<li>Data slices of type selection with 7.30 SP9 or 7.31 SP7 or note 1803016<br/> </li>\n</ul>\n<li>Logging BADI requires 7.30 SP9 or 7.31 SP7 or note 1802658<br/> </li>\n<li>Key-figures in the InfoCube or aDSO with aggregate ‘SUM’. For direct update DSO we also allow ‘NO2’. The usage of MIN/MAX in read only key figure like in classic BW-IP requires BW 7.4 SP8.</li>\n<li>The new DSO of type planning (see note 1735590) is released for HANA optimized planning with 7.30 SP9 or 7.31 SP7 or note 1799168. Character like key figures which were introduced with BW 7.4 SP8 are available in PAK with note 2196138<br/> <br/>In this case where the model allows the algorithm to fully execute in HANA also the data of the planning buffer only resists in HANA (see above). Then also the save of plan buffers is fully executed within HANA only.</li>\n</ul>\n<p>2. List of features still executed in ABAP application server with data round trip</p>\n<ul>\n<li>Planning:</li>\n<ul>\n<li>Own planning function type: any planning function exit as own type as long as no interface IF_RSPLFA_SRVTYPE_TREX_EXEC or IF_RSPLFA_SRVTYPE_TREX_EXEC_R is implemented leveraging SQL Script for HANA processing. See Standard BW Documentation or note 1870369.</li>\n<li>Planning function type: 0RSPL_FORECASTING   Forecast<br/>As workaround one can use a SQL Script based implementation (see footnote 1) which includes some PAL functions<br/>(See <a href=\"http://help.sap.com/hana/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf\" target=\"_blank\">http://help.sap.com/HANA/SAP_HANA_Predictive_Analysis_Library_PAL_en.pdf</a> and <a href=\"http://www.saphana.com/community/hana-academy/#predictive-analytics-library\" target=\"_blank\">http://www.sapHANA.com/community/HANA-academy/#predictive-analytics-library</a>)<br/>  </li>\n</ul>\n<li>Planning function type 'FOX' does not support the following commands and is in those cases executed in the application server</li>\n<ul>\n<li>CALL FUNCTION (1)</li>\n<li>ABAP BREAK-POINT command is ignored</li>\n<li>FOX key words are not supported for other usage like variable names. E.g. DATA I TYPE I is not allowed but DATA J TYPE I is allowed.</li>\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on standard time characteristics like 0CALYEAR or characteristic referencing those</li>\n<li>attribute lookup (ATRV/ATRVT) or validity check (MDEXIST) on characteristics which don't have a master data table, which is a BW object. But HANA View based info objects are supported ATRV in FOX on HANA view solved with note 2644984 and Revision 2.00.030.01</li>\n<li>Using a hierarchy node variable in VARC or similar statements</li>\n<li>Using compounding in variables before revision 122.05. It work from revision 122.05 onwards when applying note 2386434.</li>\n<li>Using InfoObjects outside the aggregation level (e.g. in DATA statements)</li>\n<li>The underlying aggregation level contains a float key figure you need at least note 2644984 and for HANA 1.0 revision 1.00.122.17 or for HANA 2.0 2.00.030.01. See also notes 2449817 and 460652 and the general advice to avoid float in BW. </li>\n<li>New FOX features introduced with BW 7.4 SP8 like reading from external aggregation levels which is supported for PAK with revision 102.4 incl. note 2234156 and 2241643.  Internal tables are also supported in PAK with HANA revision 97.02 and note 2145611</li>\n<li>The new FOX features ‘modularization’ or ‘forms’ introduced with BW 7.5 SP0 are currently not yet supported in PAK and lead to the processing in ABAP application server.  </li>\n<li>The new FOX feature ‘directly reading from a non-input enabled DSO ‘ (introduced in BW 7.5 SP0) is  supported in PAK with SAP BW 7.52. In case the DSO contains non-key fields the the execution however is still on the ABAP stack.</li>\n<li>The new feature of transient attributes coming with BW 7.4 SP4 will not be supported to run in HANA for time dependent attributes in FOX (e.g. ATRV). Then the FOX is processed on ABAP side.</li>\n<li>Key fields in internal tables of type KEYFIGURE_NAME.</li>\n<li>Access to external InfoProviders with variables of type KEYFIGURE_NAME.</li>\n</ul>\n</ul>\n<p>     Note 2336691 includes the HANA check of FOX in Fox Editor</p>\n<ul>\n<li>If conditions exist on a planning function and</li>\n<ul>\n<li>More on has more than 1 condition on this planning function or</li>\n<li>The global filter contains selection on attributes, but the condition contains selection on the basic characteristics or</li>\n<li>The filter contains selections on hierarchy nodes and the condition has selections on the characteristic the hierarchy is defined on or</li>\n<li>The selection contains selections which are not single values restriction and those selections are not one by the same in the global filter.<br/> </li>\n</ul>\n<li>Dis-aggregations in the Query are executed not in the database if</li>\n<ul>\n<li>Planning model uses MultiProvider on top of an aggregation level</li>\n<li>A formula is used as reference key figure for disaggregation</li>\n<li>The key figure is restricted to multiple values for a given characteristic except several single values. E.g. intervals, or hierarchy nodes in the restriction lead to execution in the application server.</li>\n<li>You use disaggregation with reference to a further structure element that contains a constant selection for a characteristic.</li>\n</ul>\n</ul>\n<p>          Here the additional impact of a round trip is not as considerable as for planning functions. Only the generated data has to be pushed to the database.</p>\n<p> </p>\n<p>3. List of features which cannot be executed with the ABAP applications planning KIT in HANA and leads to switch to BW-IP (for the entire model, not a single step)</p>\n<ul>\n<li>TIMS &amp; DATS key-figures in the InfoCube. We recommend to use data type DEC and instead of TIMS or DATS</li>\n</ul>\n<ul>\n<li>Master data access of type Own Implementation or Remote (Direct Access). With note 1929130 we allow (as exemption) characteristics referencing to standard BW time characteristics, source system (0SOURSYSTEM) or InfoProvider (0INFOPROV) when using their standard implementations.</li>\n</ul>\n<ul>\n<li>Virtual master data, including hierarchies and technical master data which are not persisted in tables. This also affects compounded characteristics leveraging those master data. Examples are InfoObjects that exist in BW but without stored data in the InfoObject tables. Exceptions are time and system characteristics which also work in the HANA optimized case.</li>\n</ul>\n<ul>\n<li>Virtual and referencing key-figures</li>\n</ul>\n<ul>\n<li>Transient characteristics</li>\n</ul>\n<ul>\n<li>Certain constraints by characteristic relationship cannot be checked in HANA yet. This includes</li>\n</ul>\n<ul>\n<ul>\n<li>characteristic relationships of type EXIT as long as the interface IF_RSPLS_CR_EXIT_HDB is not implemented. One can either leveraging SQL Script for HANA processing (see notes 1877182, 1874412 and 1929130(1)) or use ABAP as fall back (see note 1956085 - BW-IP (PAK): ABAP as fallback for exit characteristic relations and data slices on SAP HANA)</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li>Certain constraints by data slices cannot be checked in HANA yet. This includes</li>\n</ul>\n<ul>\n<ul>\n<li>data slices of type EXIT as long as the interface IF_RSPLS_DS_EXIT_HDB is not implemented leveraging SQL Script for HANA processing. See notes 1877182, 1874412 and 1929130. See footnote (1)</li>\n</ul>\n</ul>\n<ul>\n<li>CP-Problem as explained in the long text of message BRAIN 313 (see note 2095418) is not working before BW 7.4 SP11 or note 2111189.</li>\n<li>HANA views with placeholder in virtual providers or as part providers in a CompositeProvider</li>\n<li>numeric ADSO fields, e.g. integers, without aggregation, except those of type NUMC. It is recommended to use NUMC instead.</li>\n</ul>\n<p> </p>\n<p>Potential to overcome current limitation<br/>(this list reflects development plans and is no commitment for dates and or functionality)</p>\n<ul>\n<ul>\n<li>(1) By conception, we will not be able to support the full flexibility of an ABAP EXIT within HANA, in particular, when other ABAP sources are leveraged in the implementation. However, there are EXIT implementations using SQL script that can be executed in HANA.  Prerequisites are HANA 1.0 SP6 Revision 67 and NW 7.30 SP10/7.31 SP9. For BW one can alternatively apply notes 1861395, 1870342, 1877182, 1870369, 1929130 and 1956085.</li>\n</ul>\n</ul></div>", "noteVersion": 70}, {"note": "2061095", "noteTitle": "2061095 - SAP Business Planning & Consolidation 10.1 NW SP05 Central Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>Central Note for SAP Business Planning and Consolidation 10.1 Support Package 05, version for SAP NetWeaver</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<div class=\"longtext\">\n<p>Release notes, installation, central note</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Business Planning and Consolidation 10.1 Support Package 05 matches SAP_UI5 SP11 (version 1.26.1). Please use the correct SAP UI5 version.</p>\n<p>For 10.1 Embedded only, SAP Business Planning and Consolidation 10.1 on NetWeaver 740 (component SAP_BW740) SP09 is required. HANA DB <span>Revision 83 (HANA SP08) or higher is required. The recommended HANA DB revision is 85 or higher. </span>Refer to note 1600929 for more information on the dependency between ABAP DB and SAP HANA Revision.</p>\n<p>For 10.1 Standard on NetWeaver, SAP Business Planning and Consolidation 10.1 on NetWeaver 740 (component SAP_BW740) SP09 and (component CPMBPC810) SP04 are required.</p>\n<p>For 10.1 Standard on HANA, SAP Business Planning and Consolidation 10.1 on NetWeaver 740 (component SAP_BW740) SP09, (component CPMBPC810) SP04 and (component HANABPC810) SP04 are required. HANA DB <span>Revision 83 (HANA SP08) or higher is required. The recommended HANA DB revision is 85 or higher.</span></p>\n<p>SAP Business Planning and Consolidation 10.1 SP05 is compatible with EPM add-in SP21; we recommend you use this version of EPM add-in.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For 10.1 Embedded only, please upgrade to SAP_BW 740 SP10.</p>\n<p>For 10.1 Standard on NetWeaver, please upgrade to SAP_BW 740 SP10 and CPMBPC 810 SP05.</p>\n<p>For 10.1 Standard on HANA, please upgrade to SAP_BW 740 SP10, CPMBPC 810 SP05, and HANABPC 810 SP05.</p>\n<p><strong>SAP Planning and Consolidation 10.1 SP05, version for SAP NetWeaver contains the following:</strong></p>\n<ol>\n<li>Bug fixes</li>\n<li>New enhancements in both embedded and standard models:</li>\n<ul>\n<li>BPF integration with work status</li>\n<ul>\n<li>When closing a BPF activity, you can automatically set the work status at the same time.</li>\n<li>Within a BPF template, you can assign a desired target work status for each BPF activity action (complete, submit, approve, reject, or reopen).</li>\n</ul>\n</ul>\n<li>New enhancements in the embedded model:</li>\n<ul>\n<li>The hierarchy of dimensions used in an environment can be directly maintained in the HTML5 web client. The following features are available:</li>\n<ul>\n<li>External Characteristic node, text node, and interval node</li>\n<li>Version dependent hierarchy</li>\n<li>Time dependent hierarchy (supporting both 'Entire hierarchy time dependent' and 'Time dependent hierarchy structure')</li>\n<li>Reverse sign editing</li>\n</ul>\n<li>New BAdI BADI_RSBPCB_SETUSER (Enhancement Spot RSBPCB_SETUSER) is delivered for:</li>\n<ul>\n<li>Providing owner and manager for work status</li>\n<li>Providing performer and reviewer for BPF activity</li>\n</ul>\n<li>BPF navigation (also known as BPF web-based application)</li>\n</ul>\n<li>New enhancements in the standard model:</li>\n<ul>\n<li>Consolidation Monitor:</li>\n<ul>\n<li>Capability to execute calculations using logic scripts after execution of currency translation and consolidation (elimination).</li>\n<li>Support of simulation scenarios.</li>\n</ul>\n<li>Controls:</li>\n<ul>\n<li>Extending Controls to cover both Local, Reporting, and Group data (post eliminations)</li>\n</ul>\n</ul>\n<li>Removal of limitations: </li>\n<ul>\n<li>From both the embedded and standard models: BPF system report supports open BPF review comments</li>\n<li>From the embedded model: Aggregation authorization for data security</li>\n</ul>\n</ol>", "noteVersion": 7}, {"note": "1919631", "noteTitle": "1919631 - Activating the BPC embedded", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With the installation of SAP BW 7.40 SP05, the BPC ‘embedded’ can be used without further software components. It integrates</p>\n<ol>1. Business Planning and Consolidation (BPC)</ol><ol>2. Planning Application Kit (PAK).</ol>\n<p>However the BPC ‘embedded’ belongs to the BPC NW product which requires a dedicated license. This is why it needs to be activated specifically. The following prerequisites need to be considered in order to use the BPC ‘embedded’:</p>\n<ul>\n<li>A license for 'SAP BusinessObjects Planning and Consolidation, version for SAP NetWeaver' is required</li>\n</ul>\n<ul>\n<li>Planning Application Kit should be activated (See Note 1637199)</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Hana optimized planning</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To activate the BPC-PAK embedded perform the following steps:</p>\n<ul>\n<li>Start transaction SM30 and enter table view RSPLS_HDB_ACT.</li>\n</ul>\n<ul>\n<li>Add and activate the parameter BPC_ACT 'BPC Embedded Model Active'.</li>\n</ul></div>", "noteVersion": 2}]}, {"note": "2510414", "noteTitle": "2510414 - Conversion to SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA Central Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>For customers who are using SAP Business Planning and Consolidation 10.1, on SAP NetWeaver 740/750 (BPC 10.1 on NW 740/750) or SAP Business Planning and Consolidation 10.0, on SAP NetWeaver 730/740 (BPC 10.0 on NW 730/740) standard configuration, please use UJBR to backup the old BPC environment and restore to SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA (BPC 11.0) standard environment.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BPC, BPC 10.0, BPC 10.1, BPC4HANA, BPC 11.0, Standard, Remote Conversion, UJBR, Backup &amp; Restore, Migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Two conversion modes are provided to customers from  to BPC 11.0:</p>\n<ol>\n<li>In-place conversion</li>\n<li>Remote conversion</li>\n</ol>\n<p>This central note describes the remote conversion mode only.</p>\n<p>BPC 10.1 on NW 740/750 and BPC 10.0 on NW 730/740 standard environment is now the only source system that could be supported to be converted to BPC 11.0 via remote conversion.</p>\n<p>Before you start the remote conversion, please upgrade the target system to latest support package. The lowest support package on your target BPC 11.0 system is SP01. In BPC 11.0, the concept of HANA optimization parameter is changed, thus the following notes also need to be applied beforehand.</p>\n<ul>\n<li>2505668 - New concept of HANA optimization parameter</li>\n<li>2501622 - Cannot connect to the specific model (configured ENABLE_HANA_MDX) via ODBO provider or AO plug-in</li>\n</ul>\n<p>Remote conversion for BPC 10.1 and BPC 10.0 Standard Environment use backup &amp; restore (UJBR) + Migration solution. In BPC 11.0 SP01, the migration program \"UJT_MIGRATE_101_TO_BPC4\" has already intergated into UJBR function, which means in BPC 11.0 SP01 the data migration will be executed automatically when you execute UJBR restore function.</p>\n<p>This SAP Note provides some necessary information concerning the mentioned for remote conversion.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><em><br/>BPC 10.x Standard Environment on NW version convert to SAP BPC 11.0, version SAP BW/4HANA</em></strong></p>\n<p><strong><em>E2E Scenario:</em></strong></p>\n<p>Your source environment in BPC 10.1 on NW 740/750 or BPC 10.0 on NW 730/740:</p>\n<ol>\n<li>Go to T-code UJBR backup your environment</li>\n<li>In target BW/4 system go to T-code UJBR restore your backup environment.</li>\n</ol>\n<p><strong>Notice:</strong></p>\n<p>Sicnce Global parameter will not be backup and restore to target system, so we recommend you to config Global parameter in T-code SPRO in target system before you start to restore your BPC standard environment.</p>", "noteVersion": 2, "refer_note": [{"note": "2463467", "noteTitle": "2463467 - SAP Business Planning and Consolidation 11.0 SP01, version for SAP BW/4HANA Central Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Central Note for Support Package 01 of SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BPC/4HANA, BW/4HANA, BPC, BPC/4, installation, central note, SP01, FP01</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Business Planning and Consolidation 11.0 SP01, version for SAP BW/4HANA matches SAP UI5 751_SP01 (version 1.44.1). Be sure to use the correct SAP UI5 version.</p>\n<p>HANA DB <span>Revision: 122.12 or higher(HANA 1.0 SPS12) is required; Or Revision 12 or higher(HANA 2.0 SPS 1 or higher).</span></p>\n<p>Refer to SAP note 1600929 for more information on the dependency between ABAP DB and SAP HANA Revision.</p>\n<p>SAP Business Planning and Consolidation 11.0 SP01, version for SAP BW/4HANA matches SAP BW/4HANA SP05. Be sure to upgrade to the required BW version.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>*IMPORTANT <strong>INSTALLATION INSTRUCTIONS</strong>*</strong></strong></p>\n<ul>\n<li><strong>BEFORE YOU ACTIVATE ENVIRONMENTSHELL</strong></li>\n</ul>\n<p>It is of vital importance that before activating EnvironmentShell you apply SAP note 2484911, which contains important fixes. This SAP note prevents erroneous hierarchy information from being generated when activating EnvironmentShell. This erroneous information could block the creation of dimensions afterwards.</p>\n<ul>\n<li><strong>APPLY IMPORTANT FIXES</strong></li>\n<ul>\n<li>SAP note 2558026. <em><strong>This is a mandatory note for BPC embedded configuration. Be sure to apply this note, otherwise BW queries will be deleted when deleting a BPC embedded model.</strong></em></li>\n</ul>\n</ul>\n<p>SAP Business Planning and Consolidation 11.0 SP01, version for SAP BW/4HANA contains:</p>\n<ul>\n<li>Bug fixes</li>\n<li>New enhancements in both Embedded and Standard configurations:</li>\n<ul>\n<li>Supports showing and hiding favorites, timeline, and charts on the homepage. For details, refer to SAP note 2479316.</li>\n<li>Supports pictures in user profiles. For details, refer to SAP note 2497983.</li>\n<li>Supports a customized logo and product name. For details, refer to SAP note 2489289.</li>\n<li>Supports cross-column searching in a user list and team list. For details, refer to SAP note 2498012.</li>\n<li>Supports cross-column searching in the My Activities list. For details, refer to SAP notes 2474815 and 2484179.</li>\n<li>Supports favorites management. For details, refer to SAP note 2474727.</li>\n</ul>\n<li>New enhancements in the Standard configuration:</li>\n<ul>\n<li>Supports chart creation based on a selected data range in worksheet grid reports. For details, refer to SAP note 2493913.</li>\n<li>Supports cross-column searching in a master data list view. For details, refer to SAP note 2498012.</li>\n<li>Supports remote conversion of BPC standard configurations from SAP BPC 10.1 &amp; 10.0 on SAP NetWeaver to SAP BPC 11.0, version for SAP BW/4HANA. For details, refer to the Conversion Guide on the <a href=\"https://help.sap.com/viewer/product/SAP_BPC_VERSION_BW4HANA/11.0.1.0/en-US\" target=\"_blank\">SAP Help Portal</a> and SAP note 2510414.</li>\n<li>Supports cross-column searching in the library. For details, refer to SAP note 2487004.</li>\n<li>Supports SAML for EPM Plug-in reports. For details, refer to SAP notes 2439031 and 2467294.</li>\n</ul>\n<li>Resolved product limitation:</li>\n<ul>\n<li>Re-editing of action group, worksheet, and content is not supported in BPF workspace design time. For details, refer to SAP note 2497910.</li>\n</ul>\n</ul>", "noteVersion": 10}]}, {"note": "2450774", "noteTitle": "2450774 - SAP Business Planning and Consolidation 11.0 SP00, version for SAP BW/4HANA Central Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Central Note for SAP Business Planning and Consolidation 11.0 SP00, version for SAP BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BPC/4HANA, BW/4HANA, BPC, BPC/4, installation, central note</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>Before You Install</strong><br/>Read this SAP Note in its entirety. You should also read the <a href=\"https://help.sap.com/viewer/c2c861bca6d3467ca49e758bcc6abdfb/11.0.0/en-US\" target=\"_blank\">Master Guide</a> for SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA, which provides important installation instructions.</p>\n<p>SAP Business Planning and Consolidation 11.0, version for BW/4HANA must run on top of SAP BW/4HANA. Find additional information about SAP BW/4HANA in the <a href=\"https://support.sap.com/content/dam/library/ssp/infopages/pam-essentials/TIP/BW4HANAPAM.pdf\" target=\"_blank\">Product Availability Matrix</a> and on the <a href=\"http://help.sap.com/bw4hana10\" target=\"_blank\">BW/4HANA Help Portal page</a>. Also refer to the following SAP Notes:</p>\n<ul>\n<li><a href=\"/notes/ 2347382\" target=\"_blank\">SAP Note 2347382</a> - BW/4HANA – Information (Installation, HANA, …)</li>\n<li><a href=\"/notes/ 2347384\" target=\"_blank\">SAP Note 2347384</a> - Important SAP Notes for SAP BW/4HANA</li>\n</ul>\n<p>SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA (Component BPC4HANA) SP00 is required. This release matches SAP_UI 751 SP01 (UI5 version 1.44.1). Be sure to use the correct SAP UI5 version. For more detailed information, refer to the <a href=\"https://support.sap.com/content/dam/library/ssp/infopages/pam-essentials/BPC11_4_HANA.pdf\" target=\"_blank\">Product Availability Matrix</a> for SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA.</p>\n<p><strong>Software components</strong></p>\n<ul>\n<li>BPC4HANA 100 SP00</li>\n</ul>\n<p><strong>Analysis Office</strong></p>\n<p>SAP BusinessObjects Analysis, edition for Microsoft Office (AO) 2.4 SP03 is required to establish the SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA connection.</p>\n<p>SAP BusinessObjects Analysis, edition for Microsoft Office (AO) 2.4 consists of three components: Analysis Plug-in, EPM Plug-in, and BPC plug-in. If using a standard configuration of Business Planning and Consolidation, use the EPM Plug-in of AO. If using an embedded configuration, use the Analysis Plug-in of AO. Please note that the EPM Add-in for Microsoft Office is not supported in SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>*IMPORTANT <strong>INSTALLATION INSTRUCTIONS</strong>*</strong><br/>To install the required components, see the <a href=\"https://help.sap.com/viewer/c2c861bca6d3467ca49e758bcc6abdfb/********/en-US\" target=\"_blank\">Master Guide</a> for SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA.</p>\n<ul>\n<li>\n<div><strong>BEFORE YOU ACTIVATE ENVIRONMENTSHELL<br/></strong>It is of vital importance that you apply SAP Note 2479275 and SAP Note 2484911 before activating EnvironmentShell which contains important fixes.<br/>SAP Note 2484911 prevents garbage hierarchy information from being generated when activating EnvrionmentShell. These garbage information could block dimension creation afterwards.<br/>SAP Note 2479275 fixes an error while activating EnvironmentShell.</div>\n</li>\n<li>      <strong>APPLY IMPORTANT FIXES</strong><br/>\n<ul>\n<li>SAP note 2558026. <em><strong>This is a mandatory note for BPC embedded configuration. Be sure to apply this note, otherwise BW queries will be deleted when deleting a BPC embedded model.</strong></em></li>\n</ul>\n</li>\n</ul>\n<p><strong>Conversion</strong></p>\n<p>Please refer to Conversion Guide and below Note</p>\n<ul>\n<li><a href=\"/notes/2645732\" target=\"_blank\">SAP Note 2645732</a> - Conversion from SAP BPC NW to BPC 11.0 for BW/4HANA</li>\n</ul>\n<p><br/><strong>Documentation</strong><br/>A full set of documentation for Business Planning and Consolidation 11.0, version for SAP BW/4HANA - including a Security Guide, an Administrator's Guide, Application Help for end users and administrators, and more - is available to you on its <a href=\"https://help.sap.com/viewer/p/SAP_BPC_VERSION_BW4HANA\" target=\"_blank\">SAP Help Portal page</a>.</p>\n<p><strong>Feature Highlights</strong></p>\n<ul>\n<li>Planning and consolidation capabilities in both the standard configuration and embedded configuration</li>\n<li>Newly designed Fiori-like web interface with a customizable home page</li>\n<li>Deeper integration with SAP Analytics Cloud offers a Hybrid deployment combining the best of Cloud and On-Premise to EPM end users.</li>\n<li>Simplified web reporting and planning with new worksheet functionality</li>\n<li>An enhanced user experience in business process flows</li>\n<li>Enhanced embedded consolidation functionalities</li>\n<li>Private Cloud deployment with AWS or Azure</li>\n</ul>\n<p><strong>Limitations in Business Planning and Consolidation 11.0, version for SAP BW/4HANA</strong><br/>See <a href=\"/notes/2472541\" target=\"_blank\">SAP Note 2472541</a>.</p>", "noteVersion": 10, "refer_note": [{"note": "2484911", "noteTitle": "2484911 - <PERSON><PERSON> failing with error while accessing hierarchy.", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you try to load a hierarchy then you may get an error  like 'Error while accessing H0000001897_A_HHLI'.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Error while accessing hierarchy, cannot use duplicate view name, SQL code 322 RS_EXCEPTION109</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program Error</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<ul> </ul>\n<li><strong><span>SAP BW 7.50</span></strong><br/>Implement Support Package 9 for SAP BW 7.50 (SAPK-75009INSAPBW) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2467627 </strong>with the short text \"SAPBWNews 7.50 BW ABAP SP9\", which describes this Support Package in more detail, is released for customers<br/><br/></li>\n<li><span><strong>SAP BW 7.52</strong></span><br/>Implement Support Package 1 for SAP BW 7.52 (SAPK-75201INSAPBW) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2487153 </strong>with the short text \"SAPBWNews 7.52 BW ABAP SP1\", which describes this Support Package in more detail, is released for customers. <br/><br/></li>\n<li><span><strong>SAP BW/4HANA 1.0</strong></span><br/>Implement Support Package 6 for SAP BW/4HANA 1.0 (SAPK-10006INDW4CORE) into your SAP BW/4HANA system. The Support Package will be available as soon as <strong>SAP Note 2491835</strong><strong> </strong>with the short text \"SAPBWNews SAP BW/4HANA 1.0 SP06\", which describes this Support Package in more detail, is released for customers. </li>\n</ul>\n<p>In urgent cases you can use the correction instructions.<br/><br/>Before you use the correction instructions, make sure that you check <strong>SAP Note 1668882 and SAP Note 2248091</strong> for transaction SNOTE.<br/><br/>This SAP Note might already be available before the Support Package is released. In this case, however, the short text still contains the term \"preliminary version\".</p>", "noteVersion": 6}, {"note": "2479275", "noteTitle": "2479275 - Error in activating environmentshell", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Error \"Please run BPC_MIGRATE_TO_HANA to update hana tables of environments\" occurs when activating environmentshell</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Environmentshell, BPC_MIGRATE_TO_HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>program error</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please apply this SAP note, or upgrade component BPC4HANA100 to Support Package 01.</p>", "noteVersion": 1}, {"note": "2347382", "noteTitle": "2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to run or you are already running an SAP BW/4HANA system.<br/>You want to convert your SAP BW system to SAP BW/4HANA.<br/>You want to upgrade your system from SAP BW/4HANA 1.0 to SAP BW/4HANA 2.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revision, BW4, BW/4HANA, BW, NetWeaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note contains information about the following topics:</strong></p>\n<ol>\n<li>General Information about SAP BW/4HANA and the conversion to SAP BW/4HANA</li>\n<li>Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</li>\n<li>Information relevant for SAP BW/4HANA 2023</li>\n<li>Information relevant for SAP BW/4HANA 2021</li>\n<li>Information relevant for SAP BW/4HANA 2.0</li>\n<li>Information relevant for SAP BW/4HANA 1.0</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>1. General information about SAP BW/4HANA and the conversion to SAP BW/4HANA</span></strong></p>\n<p><span>High Level information about BW/4HANA: <a href=\"http://www.sap.com/bw4hana\" target=\"_blank\">http://www.sap.com/bw4hana</a></span></p>\n<p><span>See SAP Note <a href=\"/notes/2347384 \" target=\"_blank\">2347384</a> - SAP BW/4HANA 1.0 Important Notes before you start</span></p>\n<p><span>When planning your system landscape(s) for the next release of SAP BW/4HANA (not release 1.00), you may want to take the available applicaton server platforms into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> </span></p>\n<p><span>SAP changed the <a href=\"https://support.sap.com/en/my-support/knowledge-base/security-notes-news.html\" target=\"_blank\">SAP security strategy</a> to cover security corrections for Support Packages delivered during the past 24 months (formerly 18 months). BW/4HANA will not extend this period. We provide security corrections for Support Packages delivered in the last 18 months as before.</span></p>\n<p><span>See note </span><a href=\"/notes/2733740\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2733740</a> for Information/Restriction about Relase BW/4HANA 2.0</p>\n<p><strong>2. Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Productively used SAP BW/4HANA release</strong></td>\n<td><strong>Recommended minimum SAP HANA SP/revision</strong></td>\n</tr>\n<tr>\n<td>1.0</td>\n<td>Revision 122.12 or higher (HANA 1.0 SPS 12)<br/>Revision 12 or higher (HANA 2.0 SPS 1 or higher)</td>\n</tr>\n<tr>\n<td>2.0</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2021</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2023</td>\n<td>Revision 71 or higher (HANA 2.0 SPS 7 or higher)</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that SAP strongly recommends to apply the <span>latest available</span> SAP HANA revision.</p>\n<p>For details about SAP HANA Maintenance Strategy see SAP Note <em><a href=\"/notes/2021789\" target=\"_blank\">2021789</a> - SAP HANA Revision and Maintenance Strategy</em>.</p>\n<p>Please keep the update paths for Maintenance Revisions in mind. Details can be found in SAP Note <em><a href=\"/notes/1948334\" target=\"_blank\">1948334</a> - SAP HANA Database Update Paths for Maintenance Revisions</em>.</p>\n<p>Notes with regards to SAP HANA Revisions:</p>\n<ul>\n<li>HANA SPS12: SAP Note <a href=\"/notes/2298750\" target=\"_blank\">2298750</a> - SAP HANA Platform SPS 12 Release Note</li>\n</ul>\n<p><span>SAP BW/4HANA and SAP HANA 2.0: </span></p>\n<p>SAP BW/4HANA 1.00 (including SAP_BASIS 7.50) has been released for HANA 2.0. Details can be found in note 2420699.<br/>SAP BW/4HANA 2.00 (including SAP_BASIS 7.53) and newer run on HANA 2.0 only</p>\n<p><strong> </strong></p>\n<p><strong>3. </strong><strong>Information relevant for SAP BW/4HANA 2023 (planned mid of Q4/2023)</strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3365187\" target=\"_blank\">3365187 - Information/Restriction Note for Release SAP BW/4HANA 2023</a>.</p>\n<p><strong><strong>4. Information relevant for SAP BW/4HANA 2021</strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3100794\" target=\"_blank\">3100794 - Release Information/Restrictions Note for SAP BW/4HANA 2021</a>.</p>\n<p><strong><strong><strong>5. Information relevant for SAP BW/4HANA 2.0</strong></strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"https://me.sap.com/notes/2733740\" target=\"_blank\">2733740 - Release Information/Restrictions Note for SAP BW/4HANA 2.0</a></p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>See note 2733740 - Release Information/Restriction Note for SAP BW/4HANA 2.0 for restrictions in BW/4HANA 2.0</li>\n<li>Urgent! Implement note <a href=\"/notes/2770525\" target=\"_blank\" title=\"2770525  - Data Transfer Intermediate Storage (DTIS): Missing package assignment\">2770525 - Data Transfer Intermediate Storage (DTIS): Missing package assignment</a> <span><strong>before</strong></span> upgrading to BW/4HANA 2.0</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Search/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Introduction Video: <a href=\"https://youtu.be/bxH0EzAhSwQ\" target=\"_blank\">https://youtu.be/bxH0EzAhSwQ</a></li>\n</ul>\n<li><span>Upgrading BW/4HANA 1.0 to 2.0</span></li>\n<ul>\n<li>We now introduced tasklist SAP_BW4_BEFORE_UPGRADE and SAP_BW4_AFTER_UPGRADE  to support you upgrading your system. Please see note 2846537 - Taskliste for upgrade of BW/4HANA</li>\n<li>Afte the upgrade to BW/4HANA 2.0 the flag TADIR Popup f. Obj. in View RSADMINC gets initialized. Please see note 2884312 for details.</li>\n</ul>\n<li><span>ABAP Software Stack</span></li>\n<ul>\n<li>The initial Software stack for BW/4HANA 2.0 looks like this:</li>\n<ul>\n<li>SAP_BASIS 7.53 SP01</li>\n<li>SAP_ABA 7.5D SP01</li>\n<li>SAP_UI 7.53 SP02</li>\n<li>SAP_GWFND 7.53 SP01</li>\n<li>ST-PI 7.40 SP09</li>\n<li>DW4CORE 2.00 SP00</li>\n<li>UIBAS100 4.00 SP01</li>\n</ul>\n<li>This should dramatically reduces the effort for installation</li>\n<li>During the upgrade from BW/4HANA 1.0 the missing software components are installed automatically</li>\n<li></li>\n</ul>\n<li><span>BW/4HANA 2.0 SP01</span></li>\n<ul>\n<li>Please install following note on top of SP01 directly after update:</li>\n<ul>\n<li>2779827  Error starting task list SAP_BW4_LAUNCHPAD_SETUP from other task lists</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP02 </span></li>\n<ul>\n<li>SAP_UI 7.54 SP1 as alternative UI version is released. Please check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07</li>\n<li>please also take into consideration the Support Package Upgrade Strategy when running on higher UI Version described in note <strong>2618449</strong></li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP04</span></li>\n<ul>\n<li>SAP_UI 7.54 &gt;= SP2 as alternative UI version is released. lease check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07. Also take into consideration the Support Paackage Strategy when running on higher UI version described in note <strong>2618449</strong></li>\n<li>NSE for DTO --&gt; available since HANA 2.0 SP04 + limitation see HANA Note <a href=\"/notes/2771956\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2771956</a></li>\n<li>Open Hub with SDA --&gt; available since HANA 2.0 SP04 Revision 46</li>\n<li>XDA --&gt; available since HANA 2.0 Sp04 Revision 40 </li>\n<li>Geo Support --&gt; available since HANA 2.0 SP04 Revision 45</li>\n<li>Treespec Partitioning in ADSO --&gt; available since Hana 2.0 SP04 </li>\n</ul>\n</ul>\n<p><strong>6. Information relevant for SAP BW/4HANA 1.0</strong></p>\n<p>Installation/Setup</p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>Please see SAP Note<strong> </strong><a href=\"/notes/2354516\" target=\"_blank\">2354516</a> to enable client copy after installation.</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Serach/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Equivalent Support Packages<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>SAP BW 7.50 (SAP_BW)</td>\n<td>BW/4HANA 1.00(DW4CORE)</td>\n<td>BPC 11 (BPC4HANA)</td>\n<td>SAP UI 7.52 </td>\n</tr>\n<tr>\n<td>               4</td>\n<td>        Initial Installation</td>\n<td>           N.A.</td>\n<td> </td>\n</tr>\n<tr>\n<td>               5</td>\n<td>                   1</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               6</td>\n<td>                   &gt;= 2</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               7</td>\n<td>                   &gt;= 4</td>\n<td>  Initial Installation</td>\n<td> </td>\n</tr>\n<tr>\n<td>               8</td>\n<td>                   &gt;= 5</td>\n<td>             &gt;= 1</td>\n<td> </td>\n</tr>\n<tr>\n<td>               9</td>\n<td>                   &gt;= 6</td>\n<td>             &gt;= 2             </td>\n<td> </td>\n</tr>\n<tr>\n<td>             10</td>\n<td>                   &gt;= 7</td>\n<td>             &gt;= 3</td>\n<td> </td>\n</tr>\n<tr>\n<td>             11*</td>\n<td>                   &gt;=8</td>\n<td>             &gt;=4</td>\n<td>      &gt;=2</td>\n</tr>\n<tr>\n<td>             12*</td>\n<td>                   &gt;=9</td>\n<td>             &gt;=5</td>\n<td>      &gt;=3</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n</ul>\n<p><span>*) Starting with BW/4HANA 1.00 SP08 SAP_UI 7.52 is required</span></p>\n<ul>\n<li><span>BW/4HANA SP08</span></li>\n<ul>\n<li><strong>Requirements:</strong></li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP10</li>\n<li>SAP_UI <strong>7.52 SP01 -- update required</strong></li>\n</ul>\n<li><strong>For new Installs:</strong></li>\n<ul>\n<li>For your convience we created a Support Release for BW/4HANA 1.00 including the following components:</li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP 10</li>\n<li>SAP_UI 7.52 SP02</li>\n<li>DW4CORE SP08</li>\n<li>BW4CONTB SP03</li>\n</ul>\n<li>this installation is based on Software Provisioning Manager 2.0 which requires HANA 2 SP2 Revision 23 see note 2610954.</li>\n<li>Release of the installation is planned for april (CW 17)</li>\n</ul>\n<li><strong>Enhancment in Tasklist</strong></li>\n<ul>\n<li>there are some enhancements made in the tasklists with regards to the new Web UI's: Please refert to note 2351381 for details.</li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP03</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n</ul>\n<li><span>BW/4HANA SP02</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n<li>Please implement the following notes on top of SP02 to avoid issues during setup/tasklisk execution</li>\n<ul>\n<li>2410466                BW Workspace customizing: RSDDTREXADMIN_TO_RSWSPCUST dumps </li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP01</span></li>\n<ul>\n<li>Please implement the following notes on top of SP01 to avoid issues during setup/later SP update</li>\n<ul>\n<li>2385382               Add SAPFSPOR to avoid strange DUMPS during SP update</li>\n<li>2385265               Check if essential objects are active after installation</li>\n<li>2381312               Task list SAP_BW4_SETUP_SIMPLE: Error in \"Check/Generate Packages for BW ODATA Services\" step</li>\n</ul>\n<li>General Information about tasklists and their usage in BW/4HANA: 2351381 SAP BW/4HANA task lists</li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA SP00</span></li>\n<ul>\n<li>If job BI_WRITE_PROT_TO_APPLLOG is not started by the tasklist please see SAP Note <a href=\"/notes/2356498\" target=\"_blank\">2356498</a>.</li>\n<li>In SP0 the tasklist shows an error which can be ignored. See SAP Note <a href=\"/notes/2351344\" target=\"_blank\">2351344</a> for details.</li>\n</ul>\n</ul>", "noteVersion": 36}, {"note": "2472541", "noteTitle": "2472541 - SAP Business Planning and Consolidation 11.0, 11.1 and 2021,  version for SAP BW/4HANA Limitations", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note is for known limitations for SAP Business Planning and Consolidation 11.0, 11.1 and 2021, version for SAP BW/4HANA (SAP BPC 11.0, 11.1 and 2021)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP BPC, BPC BW/4HANA, limitations, restrictions, BPC 11.0, BPC 11.1, BPC 2021</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>To access the list of supported languages/platforms (Product Availability Matrix):</p>\n<p><a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3DSAP%2520BPC%252011.0%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73555000100900001652%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix Details for Product Version SAP BPC 11.0, FOR SAP BW/4HANA</a></p>\n<p><a href=\"https://apps.support.sap.com/sap/support/pam?hash=pvnr%3D73554900100900003601\" target=\"_blank\">Product Availability Matrix Details for Product Version SAP BPC 11.1, FOR SAP BW/4HANA</a></p>\n<p><a href=\"https://apps.support.sap.com/sap/support/pam?hash=s%3Dbpc%25202021%26o%3Dmost_viewed%257Cdesc%26st%3Dl%26rpp%3D20%26page%3D1%26pvnr%3D73555000100900005481%26pt%3Dg%257Cd\" target=\"_blank\">Product Availability Matrix Details for Product Version SAP BPC 2021, FOR SAP BW/4HANA</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Below is a list of limitations of SAP Business Planning and Consolidation 11.0, 11.1 and 2021, version for SAP BW/4HANA. They will either be resolved in a future Support Package or remain in the product as known limitations.</p>\n<p><strong>Open Limitation(s)</strong></p>\n<p><strong><strong>EPM-BPC-BW4</strong></strong></p>\n<p>Creating a dimension in a model by referencing to another InfoObject is not supported in BPC 11.0, 11.1 and 2021. These dimensions won't get the correct memeber and hierarchy from the referenced InfoObject.</p>\n<p><strong>BI-RA-AO-XLA</strong></p>\n<p>The limitation ”BPC Plug-in cannot connect to BPC2021 server” has been removed in Analysis Office 2.8 SP13.</p>\n<p>In case your AO support package version is lower than SP13, you may upgrade to Analysis Office 2.8 SP13+ or open BPF from BPC web client as a workaround to resolve the issue.</p>\n<p> </p>", "noteVersion": 14}, {"note": "2645732", "noteTitle": "2645732 - Conversion from SAP BPC for NetWeaver to SAP BPC for BW/4HANA Central Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the different paths to go from SAP BPC for NetWeaver system to SAP BPC 11.0, BPC 11.1 and BPC 2021, version for BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BPC 11.0, BPC 11.1, BPC 2021, Conversion, Migration, In-place, Remote, BW/4HANA, BW, SAP Business Planning and Consolidation version for NetWeaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Convert to SAP BPC, version for BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>1 Overview</strong></span></p>\n<p><strong>1.1 </strong><strong>What’s SAP BPC, version for BW/4HANA </strong></p>\n<p>SAP BPC, version for BW/4HANA is an add-on of the next generation data warehouse platform BW/4HANA. It’s evolved from BPC 10.1, version for SAP NetWeaver and provides a brand new SAP Belize user interface that has a clean and consistent layout with delightful visual details and typography.</p>\n<p>SAP BPC 11.0, version for BW/4HANA supports BW/4HANA 1.0, and the release SAP BPC 11.1, version for BW/4HANA supports BW/4HANA 2.0, the new release SAP BPC 2021, version for BW/4HANA supports BW/4HANA 2021.</p>\n<p>BPC 2021, BPC 11.1 has exactly the same BPC features as BPC 11.0, which means it’s purely a technical/platform upgrade. If you upgrade a system from BPC 11.0 to BPC 11.1 or BPC 2021 no migration effort is expected from BPC side. You either don't need to perform any post-installation steps such as activating BW technical content and ICF service as you did in a new BPC system installation.</p>\n<p>There are a few functions of BPC 10.1 NW which are not available for BPC 11.0, BPC 11.1 and BPC 2021, for more detail, please refer to blog <a href=\"https://blogs.sap.com/2017/11/28/whats-not-possible-with-sap-bpc-11.0/\" target=\"_blank\">https://blogs.sap.com/2017/11/28/whats-not-possible-with-sap-bpc-11.0/</a>.  Nevertheless, we do support customers of BPC 10.1 to convert their existing BPC environment to BPC 11.0, BPC 11.1 and BPC 2021.</p>\n<p><strong>1.2 Related Information</strong></p>\n<p>Please find more related information as listed below:</p>\n<ul>\n<li>BPC 2021 Online Documentation<br/><a href=\"https://help.sap.com/viewer/product/SAP_BPC_VERSION_BW4HANA/2021.00/en-US\" target=\"_blank\">https://help.sap.com/viewer/product/SAP_BPC_VERSION_BW4HANA/2021.00/en-US</a></li>\n<li>BPC 2021 Product Availability Matrix<br/><a href=\"https://userapps.support.sap.com/sap(bD1lbiZjPTAwMQ==)/support/pam/pam.html#pvnr=73554900100900003601&amp;pt=g%7Cd&amp;ts=0\" target=\"_blank\">https://userapps.support.sap.com/sap(bD1lbiZjPTAwMQ==)/support/pam/pam.html#pvnr=73554900100900003601&amp;pt=g%7Cd&amp;ts=0</a></li>\n<li>BPC 11.1 Online Documentation<br/><a href=\"https://help.sap.com/viewer/product/SAP_BPC_VERSION_BW4HANA/11.0.8/en-US\" target=\"_blank\">https://help.sap.com/viewer/product/SAP_BPC_VERSION_BW4HANA/11.1.0/en-US</a></li>\n<li>BPC 11.1 Product Availability Matrix<br/><a href=\"https://apps.support.sap.com/sap(bD1lbiZjPTAwMQ==)/support/pam/pam.html#pvnr=73554900100900003601&amp;ts=0&amp;pt=g%7Cd\" target=\"_blank\">https://apps.support.sap.com/sap(bD1lbiZjPTAwMQ==)/support/pam/pam.html#pvnr=73554900100900003601&amp;ts=0&amp;pt=g%7Cd</a></li>\n<li>SAP BW/4HANA vs. SAP BW<br/><a href=\"https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html</a></li>\n<li>BPC 11.0 Product Availability Matrix<br/><a href=\"https://support.sap.com/content/dam/launchpad/en_us/pam/pam-essentials/BPC11_4_HANA.pdf\" target=\"_blank\">https://support.sap.com/content/dam/launchpad/en_us/pam/pam-essentials/BPC11_4_HANA.pdf</a></li>\n<li>BPC 11.0 Online Documentation<br/><a href=\"https://help.sap.com/viewer/product/SAP_BPC_VERSION_BW4HANA/11.0.8/en-US\" target=\"_blank\">https://help.sap.com/viewer/product/SAP_BPC_VERSION_BW4HANA/11.0.8/en-US</a></li>\n</ul>\n<p><span>For information about the functionalities and objects available in SAP BW but were replaced, converged or deleted in SAP BW/4HANA, refer to the latest version of <span><em>Simplification List for SAP BW/4HANA</em></span> available at <a href=\"https://help.sap.com/viewer/p/SAP_BPC_VERSION_BW4HANA\" target=\"_blank\">https://help.sap.com/viewer/p/SAP_BW4HANA</a></span></p>\n<p>For a holistic view of the BW/4HANA conversion topic, see BW/4HANA Conversion Central note: <a href=\"/notes/2383530\" target=\"_blank\">2383530</a><a href=\"/notes/2383530\" target=\"_blank\"><br/></a></p>\n<p>and <em><span>Conversion Guide for SAP BW/4HANA</span> </em>available<em> </em>at<em> <a href=\"https://help.sap.com/viewer/p/SAP_BPC_VERSION_BW4HANA\" target=\"_blank\">https://help.sap.com/viewer/p/SAP_BW4HANA</a> </em><a href=\"https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf\" target=\"_blank\"><br/></a></p>\n<p><strong>2 Paths to SAP B</strong><strong>PC</strong><strong>/4HANA</strong></p>\n<p><em><strong>2.1 New implementation or fresh start</strong></em></p>\n<p>New implementations are the best choice for customers moving from a legacy system to a new system or building a system from scratch with new data model only.</p>\n<p><strong>Steps</strong>:</p>\n<ol>\n<li>Install SAP BW/4HANA (with BPC/4HANA add-on) and run post-installations</li>\n<li>Selective transport of BW objects (optional)</li>\n<li>Implement HANA-optimized data models and flows</li>\n<li>Data load and quality checks.</li>\n</ol>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), system connection (SAP Source), SAP BW/4HANA ETL, file upload and/or SAP HANA EIM (legacy sources)</p>\n<p>See SAP First Guidance: Complete functional scope (CFS) for SAP BW/4HANA: <a href=\"https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html</a></p>\n<p><strong><em>2.2 System conversion</em></strong></p>\n<p>Both remote and in-place conversion are supported for BPC standard and embedded models.</p>\n<p>Remote conversion from a BPC standard model to BPC 11.0 , BPC 11.1 or BPC 2021, version for BW/4HANA is different from the others in that it doesn’t follow BW/4HANA conversion process, rather it uses transaction UJBR to convert the system.</p>\n<p>The other types of conversions, namely in-place conversion of a BPC standard model and remote &amp; in-place conversion of a BPC embedded model, follow BW/4HANA conversion process and you can use BW/4HANA Transfer Toolbox to do the conversion.</p>\n<p><strong><em>2.2.1 </em></strong><strong><em>Preparation</em></strong></p>\n<p>Before converting to BPC 11.0, BPC 11.1 or BPC 2021 for BW/4HANA, you need to check whether all the components, add-ons and business function are compatible with BW/4HANA. For detailed information, refer to the BW conversion central note <a href=\"/notes/2383530\" target=\"_blank\">2383530</a>.</p>\n<p><strong>Steps</strong>:</p>\n<ol>\n<li>BPC release/support package upgrade (optional)</li>\n<li>Check system requirement</li>\n<li>Run maintenance planner tool</li>\n<li>Run pre-checks</li>\n<li>Custom code migration</li>\n<li>Make object-specific preparations</li>\n<li>Check and install necessary notes</li>\n</ol>\n<p><strong>What to use</strong>: Maintenance Planner, SAINT/SPAM, SAP BW Checklist Tool (optional, see note <em>1729988</em>), SAP Note Analyzer</p>\n<p><strong><em>2.2.2 In-Place conversion</em><em> (standard &amp; embedded)</em></strong></p>\n<p><em>Prerequisite</em></p>\n<ul>\n<li>Your original BPC system is running on the SAP HANA platform. If not, perform a database migration to SAP HANA.</li>\n<li>The minimum BPC support package is BPC NW 7.50 SP10. Otherwise, make sure you install the note <a href=\"/notes/2453637\" target=\"_blank\">2453637</a>.<em> </em></li>\n</ul>\n<p><em><span>BW/4HANA Starter Add-on</span></em></p>\n<p>BW/4HANA Starter Add-on must be installed for in-place conversion, but not required for remote conversion.</p>\n<p>BPC <em>standard model</em> is supported with SAP BW/4HANA Starter Add-On only in the <em>Compatibility Mode</em> from SAP BW 7.50 SP07 onwards.</p>\n<p>BPC <em>embedded model</em> is supported with SAP BW/4HANA Starter Add-on after implementing SAP Note <a href=\"/notes/2373204\" target=\"_blank\">2373204</a>. This allows a customer using the embedded model to make their overall system ready for SAP BW/4HANA.</p>\n<p>Later all classic objects have to be transferred into their HANA optimized replacements using the Transfer Toolbox. As all BPC specific logic of system conversion has been embedded into BW/4HANA, simply follow the BW/4HANA conversion steps described in note <a href=\"/notes/2383530\" target=\"_blank\">2383530</a> and BW/4HANA Conversion Guide.</p>\n<p><strong>Steps</strong>:</p>\n<ol>\n<li>If you haven't, then migrate and/or upgrade to SAP BW 7.50 powered by SAP HANA</li>\n<li>Install SAP BW/4HANA Starter Add-On</li>\n<li>Install Transfer Toolbox</li>\n<li>BPC downtime start for BPC standard model </li>\n<li>Select and Transfer data models. For BPC Standard model, it's important to select the BPC objects (i.e. BPC Environment name) rather than the BW objects generated by BPC (i.e. /CPMB/* objects) during 'Collect Scope for Transfer' process.</li>\n<li>Adjust custom coding</li>\n<li>Perform system conversion include BPC and BW/4HANA</li>\n<li>BPC downtime end for BPC standard model </li>\n</ol>\n<p><strong>What to use</strong>: SAP BW/4HANA, SAP BW/4HANA Starter Add-On, Transfer Toolbox, SPAM/SAINT, Software Update Manager (SUM)</p>\n<p><em><strong>2.2.3 Remote conversion (Embedded only)</strong></em></p>\n<p>There are some limitations for BPC embedded Environment remote conversion. For detailed information and how to enable remote conversion for BPC embedded Environment, refer to the note <a href=\"/notes/2602319\" target=\"_blank\">2602319</a>.</p>\n<p>For underlying SAP BW systems on releases from 7.40 to 7.50 running on any database, a remote conversion can be performed.</p>\n<p><strong>Steps</strong>:</p>\n<ol>\n<li>Install SAP BW/4HANA (select BPC/4HANA add-on)</li>\n<li>BPC/4HANA post-installation</li>\n<li>Install DMIS Add-On</li>\n<li>Transfer data models</li>\n<li>Transport custom developments (might need adjustment to work with SAP BW/4HANA)</li>\n</ol>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), SAP BW/4HANA, Transfer Toolbox, DMIS Add-On</p>\n<p><em><strong>2.2.4 Remote Conversion via UJBR (Standard only)</strong></em></p>\n<p>Follow the steps in note <a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6B65793D30303230373531323538303030303234383736373230313726\" target=\"_blank\">2510414</a> to convert your system from BPC 10.0 or 10.1 version for SAP NetWeaver (including any support packages) on any database to the version BPC 11.0, BPC 11.1 or BPC 2021 for BW/4HANA.</p>\n<p>The recommended support package for BPC 11.0 system is SP02 or higher. For information about the server and client requirements, see the <a href=\"https://help.sap.com/viewer/DRAFT/ce7d2dbd8c4e42dd854e540504fdd8c6/11.0.4/en-US/d50415d034ce402e96c3021e64e28f76.html\" target=\"_blank\" title=\"Review the following information before beginning your installation:\">Master Guide</a> available on the SAP Help Portal.</p>\n<p><strong>3. Landscape transformation</strong></p>\n<p>Landscape transformation is for customers who want to consolidate and optimize their complex SAP BW landscape (multiple production systems) into a single SAP BW/4HANA system or who want to carve-out selected data models or flows into a global SAP BW/4HANA system.</p>\n<p><strong>Steps</strong>:</p>\n<ol>\n<li>Install SAP BW/4HANA</li>\n<li>Implement consolidated and HANA-optimized data models and flows</li>\n<li>Data load and quality checks</li>\n</ol>\n<p><strong>What to use</strong>: Selective data transfer, SAP Landscape Transformation (SLT), SAP Data Services, SAP BW/4HANA ETL or SAP HANA EIM</p>\n<p><strong>4. Other Important Information</strong></p>\n<ul>\n<li>In BPC 11.0, BPC 11.1 or BPC 2021, if you cannot create business rules for embedded model consolidation tasks, this may due to the 2-bits string that is automatically generated in the embedded environment prefix begins with 1 or 2 digits. To resolve this issue, refer to the note <a href=\"/notes/2634332\" target=\"_blank\">2634332</a>.</li>\n<li>SAP Note <a href=\"/notes/2824804\" target=\"_blank\">2824804</a>: Minimum SPAM version 71 is required when upgrade from SAP BW/4HANA 1.0 to SAP BW/4HANA 2.0 with BPC</li>\n<li>SAP Note <a href=\"/notes/2823306\" target=\"_blank\">2823306</a>: Mandatory steps to avoid BPC deletion: In-Place Conversion from SAP NetWeaver 7.5 to SAP BW/4HANA 2.0 with BPC </li>\n</ul>", "noteVersion": 24}, {"note": "2347384", "noteTitle": "2347384 - BW/4HANA 1.00 Important Notes", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use SAP BW/4HANA 1.0.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Support Package, SP, SAP Business Information Warehouse, SAP BW/4HANA, BW/4</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP recommends that you implement the relevant SAP Notes in addition to your SAP BW/4HANA Support Package. For more information, please read the attached file.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The attached XLS file contains a list of the recommended SAP Notes.</p>\n<p>Any mandatory actions, which must be performed before the relevant SAP Note is implemented, are highlighted in the table in red.</p>\n<p>In most cases, you can reduce the steps required for the manual actions by implementing a second SAP Note with an attached report. When you run this report in transaction SE38/SA38, the report implements the required changes (for example, in ABAP Dictionary). You can find the number of the additional SAP Note in the following column: \"Note # with UDO report\". If this step is required, then you must perform this step either in advance (in the case of changes to the ABAP Dictionary) or as a manual step, and you must always adhere to the instructions described in the relevant SAP Note.</p>", "noteVersion": 2}]}, {"note": "2421930", "noteTitle": "2421930 - Simplification List for SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Simplification List for SAP BW/4HANA is published as PDF document on the SAP Help Portal (<a href=\"http://help.sap.com/bw4hana20\" target=\"_blank\">http://help.sap.com/bw4hana20</a>). This SAP Note provides the Simplification List in spreadsheet format.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SIMPL, BW4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Planning to transition from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable our customers to better plan and estimate their path to SAP BW/4HANA, we have created the “Simplification List for SAP BW/4HANA”. In this list we describe in detail, on a functional level, what happens to individual object types and solution capabilities when transitioning to SAP BW/4HANA. Compared to the SAP Business Warehouse products, we have in some cases merged certain functionality with other elements or reflected it within a new solution or architecture.</p>\n<p><strong>A new, web-based UI for searching and displaying Simplification Items</strong></p>\n<p>In parallel to the PDF document and the spreadsheet available via this SAP Note, the <em>SAP Simplification Item Catalog</em> offers direct search and browse of SAP BW/4HANA Simplification Items in their current state via the SAP ONE Support Launchpad at <a href=\"https://launchpad.support.sap.com/#/sic/\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/</a>.</p>", "noteVersion": 6, "refer_note": [{"note": "2383530", "noteTitle": "2383530 - Conversion from SAP BW to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the different paths to get from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP BW/4HANA, SAP BW, Migration, Conversion, Transfer, In-Place, Remote, Shell, Lifecycle Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>0 Disclaimer</strong></span></p>\n<p>Converting a SAP BW system to SAP BW/4HANA is not a simple task. There is no \"wizard\" that magically converts everything. Instead, SAP provides a well-defined process to guide you through the renovation of your data warehouse. We have developed tools to automate this renovation where applicable and feasible, but they are not built or meant to fix badly designed models or clean-up neglected systems. In any conversion there is a need for manual interaction and re-design. The amount of such manual tasks varies from customer to customer and depends on the configuration and state of the SAP BW system. For example, a conversion of newer systems that have been configured using SAP HANA-optimized objects and LSA++ from the beginning will be relatively easy. In contrast to that, a conversion of older systems that are running on non-SAP HANA databases, using out-of-date interfaces, legacy data models, and multiple redundant layers can become quite challenging.</p>\n<p>It is therefore essential that you understand the differences between SAP BW and SAP BW/4HANA and how to handle them, thoroughly analyze your existing system, estimate the complexity and duration of required tasks, properly plan all conversion activities, learn and practice to use the conversion tools, test the conversion processes ideally with a \"copy of production\", and actively manage changes to your business. It speaks for itself that good project management and skilled personnel will be required to execute a timely and efficient conversion of a complete SAP BW system. If done right, your conversion to SAP BW/4HANA will be very successful.</p>\n<p>SAP offers several way to help. To get to started run a <a href=\"https://help.sap.com/viewer/p/SAP_READINESS_CHECK\" target=\"_blank\">SAP Readiness Check</a> or book a <a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">SAP Value Assurance service </a><a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">for SAP BW/4HANA</a>.</p>\n<p>We also highly recommend the Class Room Training <strong>BW4HC</strong>. (<a href=\"https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/\" target=\"_blank\">https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/</a>?).</p>\n<p> </p>\n<p><span><strong>1 Overview</strong></span></p>\n<p><strong>1.1 What is SAP BW/4HANA</strong></p>\n<p>SAP BW/4HANA is a new, next generation data warehouse product from SAP that, like SAP S/4HANA, is optimized for the SAP HANA platform, including inheriting the high performance, simplicity, and agility of SAP HANA. SAP BW/4HANA delivers real time, enterprise wide analytics that minimize the movement of data and can connect all the data in an organization into a single, logical view, including new data types and sources.<strong><br/></strong></p>\n<p><strong>1.2 Related Information</strong></p>\n<p>Please find more information here:</p>\n<ul>\n<li>The Future of Data Warehousing - SAP BW/4HANA  <br/><a href=\"http://sap.com/bw4hana\" target=\"_blank\">http://sap.com/bw4hana</a></li>\n<li>SAP BW/4HANA Community (includes FAQ)<br/><a href=\"https://community.sap.com/topics/bw4-hana\" target=\"_blank\">https://community.sap.com/topics/bw4-hana</a><br/><a href=\"https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html</a></li>\n<li>Online Documentation<br/><a href=\"http://help.sap.com/bw4hana10\" target=\"_blank\">http://help.sap.com/bw4hana10</a></li>\n<li>Product Roadmap<br/><a href=\"https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021\" target=\"_blank\">https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021</a></li>\n<li>SAP BW/4HANA vs. SAP BW<br/><a href=\"https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html</a><br/><br/></li>\n</ul>\n<p>For information related to functionality available in SAP BW but replaced or deleted in SAP BW/4HANA please refer to the Simplification List: <br/><a href=\"https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf\" target=\"_blank\">https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 1.0: <br/><a href=\"https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2.0:</p>\n<p><a href=\"https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2021 and higher:</p>\n<p><a href=\"https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf</a></p>\n<p> </p>\n<p><span><strong>2 How to Get to BW/4HANA</strong></span></p>\n<p><strong>2.1 Paths to SAP BW/4 HANA</strong></p>\n<p><strong><em>2.1.1 New implementation or fresh start</em></strong></p>\n<p>New implementations are the best choice for customers converting from a legacy system or building a system from scratch with new data model only.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, selective transport of BW objects (optional), implement HANA-optimized data models and flows, followed by data load and quality checks.</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), system connection (SAP Source), SAP BW/4HANA ETL, file upload and/or SAP HANA EIM (legacy sources)</p>\n<p>See SAP First Guidance: Complete functional scope (CFS) for SAP BW/4HANA: <a href=\"https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html<br/></a></p>\n<p><em><strong>2.1.2 System conversion</strong></em></p>\n<p>A system conversion addresses customers who want to change their current SAP BW system into a SAP BW/4HANA system. Using the Transfer Cockpit provided by SAP, the logical systemname of the system can be kept (in-place conversion) or a new  logical systemname can be used (remote conversion resp. shell conversion).</p>\n<p>A conversion project typically follows this sequence:</p>\n<ul>\n<li><strong>Discover / Prepare Phase</strong>: check system for BW/4HANA compliance (gather information about objects and code that needs to be transferred or changed), estimate effort for the conversion project</li>\n<li><strong>Explore / Realization Phase</strong>: Transfer legacy objects into HANA-optimized counterparts, system conversion, post conversion tasks</li>\n</ul>\n<p><em>*******. In-Place conversion</em></p>\n<p>Systems running on SAP BW 7.50 powered by SAP HANA can be converted in-place keeping their logical systemname. In the realization phase of the conversion project, classic objects have to be transferred into their HANA optimized replacements using the Transfer Cockpit. This transfer can be performed scenario-by-scenario. When all classic objects have been replaced, the system conversion to SAP BW/4HANA can be triggered.</p>\n<p>SAP highly recommends to use the latest support package for SAP BW/4HANA when performing the conversion. See note https://launchpad.support.sap.com/#/notes/2347382 regarding the equivalent support packages. Please keep the RTC Dates of the different Support Packages in mind when you plan your conversion project.</p>\n<p><strong>Steps</strong>: Migrate and/or upgrade to SAP BW 7.50 powered by SAP HANA (if necessary), install SAP BW/4HANA Starter Add-On (see “Installation” below), install Transfer Cockpit, transfer data models, adjust custom coding, perform system conversion</p>\n<p>To include the Data Comparison Functionality into InPlace-TaskList, please refer to the SAP Note <a href=\"/notes/2607742\" target=\"_blank\">2607742</a>.</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-IPL</p>\n<p><strong>What to use</strong>: SAP BW/4HANA Starter Add-On, SAP BW/4HANA Transfer Cockpit, SPAM/SAINT<br/><em></em></p>\n<p><em></em><em>*******. Remote conversion</em></p>\n<p>For SAP BW systems on releases from 7.30 to 7.50 running on Any-DB, a remote conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models into the new installation and to perform a remote data transfer.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, install DMIS Add-On, transfer data models, transport custom developments (might need adjustment to work with SAP BW/4HANA)</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-RMT</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit, DMIS Add-On</p>\n<p><em>*******. Shell conversion</em></p>\n<p>For SAP BW systems on releases from 7.00 to 7.50 running on Any-DB, a Shell conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models without data into the new installation.</p>\n<p>Our recommendation for shell conversion is to use a sandbox as the sender system. The latest SPs should also be installed on this system. Depending on the version of the current system, this saves a lot of time and effort. If you still decide to import the hints, you should look at the wiki <a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\" target=\"_blank\" title=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718</a> beforehand and follow the information from the wiki when importing.</p>\n<p><em><strong>Steps</strong>: </em>Install SAP BW/4HANA, transfer data models without data using SAP BW/4HANA Transfer Cockpit, and transport custom developments (might need adjustments to work with SAP BW/4HANA).</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-SHL</p>\n<p><strong>What to use: </strong>Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit</p>\n<p><strong><em>2.1.3. Landscape transformation</em></strong></p>\n<p>Landscape transformation is for customers who want to consolidate and optimize their complex SAP BW landscape (multiple production systems) into a single SAP BW/4HANA system or who want to carve-out selected data models or flows into a global SAP BW/4HANA system.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, implement consolidated and HANA-optimized data models and flows, followed by data load and quality checks</p>\n<p><strong>What to use</strong>: Selective data transfer, SAP Landscape Transformation (SLT), SAP Data Services, SAP BW/4HANA ETL or SAP HANA EIM<br/><em></em></p>\n<p>The following sections are focusing on the system conversion.</p>\n<p> </p>\n<p><strong>2.2 Availability</strong></p>\n<p><strong><em>2.2.1 Overview</em></strong></p>\n<p>The tools required to perform an In-Place, Remote or Shell conversion are general available now.</p>\n<p><strong><em>2.2.2 Releases</em></strong></p>\n<p>SAP highly recommends to use the latest support package for a release to start a conversion project with. It will massively reduce the effort for notes implementation and we cannot guarantee that all required notes can be imported from other areas.<em> </em>Nevertheless, the Transfer Cockpit was made available via SAP Notes for the following support packages.</p>\n<p><em>******* Pre-Checks (General Available)</em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 25 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 07 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td>SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>******* In-Place conversion</em></p>\n<p>SAP BW 7.50<strong> minimal </strong>requirement SP 05 ,<strong> recommended </strong>SP 12 and higher.</p>\n<p>An in-place conversion of SAP BW <strong>7.51</strong> or higher to SAP BW/4HANA 1.0 is <strong>not</strong> possible (since SAP BW/4HANA 1.0 is based on SAP Basis <strong>7.50</strong>).</p>\n<p>Starting from Release SAP BW <strong>7.50 SP16</strong> the conversion is only to SAP BW/4HANA <strong>2.0 SP&gt;=04</strong> possible!</p>\n<p>SAP highly recommends to update the systems before starting the conversion process. Support Package Implementation is possible until B4H Mode.</p>\n<p>Other components relevant for SAP BW Systems used for InPlace Conversion</p>\n<ul>\n<li>SAP HANA 1.0 SPS 12 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong>)</li>\n<li>SAP HANA 2.0 SPS 36 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>)</li>\n<li>SAP Business Content 7.57 SP11 and higher</li>\n<li>SPAM version 72 and higher</li>\n<li>SAP UI 7.52 or lower (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong> . SAP BW/4HANA 1.0 does <strong>not</strong> support SAP_UI 7.53. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n<li>SAP UI 7.53 (Possible only by Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n</ul>\n<p> </p>\n<p><em>*******. Remote conversion </em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong>Exceptional usage with increased manual implementation effort</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* Shell conversion</em></em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release </strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong> <strong>Exceptional usage with increased manual implementation effort</strong></strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 28 and higher</td>\n<td>SP 25</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 10 and higher</td>\n<td>SP 07</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* SAP BW/4HANA required release and support package stack</em></em></p>\n<p><em><em></em></em>SAP BW/4HANA General Information see SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a></p>\n<p>SAP BW/4HANA <strong>1.0</strong></p>\n<p>For In-Place / Shell:<strong> minimal</strong> requirement SP 08, <strong>recommended</strong> - latest released SP!</p>\n<p>For Remote: <strong>minimal</strong> requirement SP 09, <strong>recommended</strong> - latest released SP!</p>\n<p>SAP BW/4HANA <strong>2.0</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 02 (released)</p>\n<p>For more information regading SAP BW/4HANA 2.0 Release Restictions see SAP Note <a href=\"/notes/2733740\" target=\"_blank\">2733740</a>.</p>\n<p>SAP BW/4HANA <strong>2021</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2021 Release Restictions see SAP Note <a href=\"/notes/3100794\" target=\"_blank\">3100794</a>.<a href=\"/notes/2733740\" target=\"_blank\"><br/></a></p>\n<p>SAP BW/4HANA <strong>2023</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2023 Release Restictions see SAP Note <a href=\"/notes/3365187\" target=\"_blank\">3365187</a>.</p>\n<p>For Conversion from SAP_BW 7.50 for HANA to SAP BW/4HANA 2023 (SAP_BASIS 7.58) please check the downloaded Upgrade Export according to SAP Note <a href=\"https://me.sap.com/notes/3454844\" target=\"_blank\">3454844</a>.</p>\n<p><span><em>2.2.2.6 Add-On's</em></span></p>\n<p><span>Please see SAP Note <a href=\"/notes/2189708\" target=\"_blank\">2189708</a> regarding usage of Add-Ons in </span><span>SAP BW/4HANA.</span></p>\n<p> </p>\n<p><em></em><em><strong>2.2.3 Installation</strong></em></p>\n<p><em>2.2.3.1 SAP BW/4HANA Starter Add-On</em></p>\n<p>For the In-Place Conversion, the installation of the SAP BW/4HANA Starter Add-on is required. Please follow the instruction of the Conversion Guide.</p>\n<p>For Shell or Remote Conversion, the SAP BW/4HANA Starter Add-On is not required.</p>\n<p><em>2.2.3.2 SAP BW/4HANA Transfer Cockpit</em></p>\n<p>To install the Transfer Cockpit for a transfer of scenarios (in-place, remote, and shell conversion), a set of SAP Notes needs to be applied to the corresponding systems. Please use the SAP BW Note Analyzer to analyze the system and to install the prerequisites. Create the SAP BW Note Analyzer program in each system for which notes have to be applied. Depending on your use case, the following systems needs to be processed:</p>\n<ul>\n<li><strong>SAP BW</strong> (in-place conversion or sending system for a remote/shell conversion)</li>\n<li><strong>Source systems</strong> connected to SAP BW (in-place and remote/shell conversion)</li>\n<li><strong>SAP BW/4HANA</strong> (post conversoin for in-place; receiving system for a remote/shell conversion)</li>\n</ul>\n<p>Run the SAP BW Note Analyzer in each system with the corresponding XML file.</p>\n<p><em>2.2.3.3 SAP BW Note Analyzer</em></p>\n<p>For a detailed documentation of the SAP BW Note Analyzer, please see <a href=\"https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf\" target=\"_blank\">https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf</a></p>\n<p>Attached to that note you’ll find a text file “Z_SAP_BW_NOTE_ANALYZER.txt”. Save the file on your computer and open it in an editor.</p>\n<p>In the corresponding system, use transaction SE38 to create a report called “Z_SAP_BW_NOTE_ANALYZER”. Copy and paste the content of the above-mentioned text file into the report and activate it.</p>\n<p>Attached to that note, you’ll find several XML files. Save those files that are related to your use-case:</p>\n<p>Please find more detailed informations under:</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874</a>.</p>\n<p>You want to run the pre-checks in the discovery phase (RS_B4HANA_CONVERSION_CONTROL in SAP BW 7.0x and RS_B4HANA_RC from SAP BW 7.30 to 7.50):</p>\n<ul>\n<li>For SAP BW 7.0x, use <em>SAP_BW4HANA_<strong>Pre_Checks</strong>_[last_update].xml</em></li>\n<li>For SAP BW from 7.30 to 7.50, use XML<em> SAP_BW4HANA_<strong>Readiness_Check</strong>_<em>[last_update]</em>.xml</em> from note 2575059. </li>\n</ul>\n<p>You want to transfer scenarios and perform a system conversion in the realization phase:</p>\n<ul>\n<li>For SAP BW 7.50 (all cases),</li>\n<ul>\n<li>if you start now with the installation of the tool, use <em>SAP_BW4HANA_Conversion_<strong>SAP_BW_750</strong>_[last_update].xml </em></li>\n<li>if you have already installed most of the notes before and only want the latest updates, continue to use <em>SAP_BW4HANA_<strong>In-place_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>For all systems connected to SAP BW as a data load source system, use <em><strong>Source_System</strong>_for_SAP_BW4HANA_[last_update].xml</em></li>\n<li>Remote Transfer</li>\n<ul>\n<li>For SAP BW from 7.30 to 7.40 acting as sending system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>Shell Transfer</li>\n<ul>\n<li>For SAP BW from 7.00 to 7.40 acting as sending system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n</ul>\n<p>You have successfully converted the system (In-Place) to SAP BW/4HANA (see section 2.3.2.3 Post-Conversion phase)</p>\n<ul>\n<li>Use <em>SAP_BW4HANA_<strong>In-place_Conversion_(After_System_Conversion)</strong>_[last_update].xml</em></li>\n</ul>\n<p>Execute the SAP BW Note Analyzer in each relevant system. Click on the icon “Load XML file” and chose the system specific XML file.</p>\n<p>Select radio button “Check implementation state against information in XML file” and check checkbox “Download needed SAP Notes”. Select radio button “In background”.</p>\n<p>Run the report. A background process will be started which downloads the required notes.</p>\n<p>When the process finished successfully, execute the report again with a de-selected checkbox “Download needed SAP Notes”.</p>\n<p>A list of notes with its implementation status will be shown. You can now install the missing notes by clicking on the corresponding icon in the list.</p>\n<p><em>2.2.3.4 Transport-Enabled Correction Instructions (TCIs)</em></p>\n<p>When installing the SAP BW/4HANA Transfer Cockpit using the SAP BW Note Analyzer, you will be prompted for SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>, <a href=\"/notes/2371120\" target=\"_blank\">2371120</a>, and <a href=\"/notes/2735300\" target=\"_blank\">2735300</a> to install a TCI.</p>\n<p>For more information about TCI, see SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>.<a href=\"/notes/2358953\" target=\"_blank\"><br/></a></p>\n<p><em>******* DMIS Add-On</em></p>\n<p>For a remote conversion, the DMIS Add-On needs to be installed in SAP BW (sending system) and SAP BW/4HANA (receiving system). Follow the instructions in SAP Note <a href=\"/notes/2513088\" target=\"_blank\">2513088</a>.</p>\n<p> </p>\n<p><strong>2.3 Conversion Process</strong></p>\n<p>For a detailed description of the conversion process, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide</a>. In additional we attached a excel file, \"<em><strong>SAP BW4HANA Conversion - Steps to consider.xlsx</strong></em>\", as a kind of check list/project plan.</p>\n<p>List of required authorization objects for conversion, see attachment \"<em><strong>Authorization_Objects_List_Conversion_2383530.xls</strong></em>\".</p>\n<p>Frequencly ask questions are collected in note <a href=\"/notes/2930058\" target=\"_blank\">2930058</a>.</p>\n<p>Each object can only be converted once. However, it can be collected again if it is used in another scenario. For example, an IOBJ that has been converted can be used in different objects such as ADSO, DSO, CUBE. Due to consistency, these IOBJs are also collected several times, but the metadata can only be transferred once.</p>\n<p><strong>2.4 New Features</strong></p>\n<p>Under link, you can find new developed features.</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910</a>.</p>\n<p>The wiki is updated regularly.</p>", "noteVersion": 420}]}]}