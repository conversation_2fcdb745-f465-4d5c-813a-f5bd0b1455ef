{"guid": "0050569455E21ED5B3E176783913C09E", "sitemId": "SI19: Logistics - PLM", "sitemTitle": "S4TWL - Engineering Change Management ECR/ECO", "note": 2267918, "noteTitle": "2267918 - S4TWL - Engineering Change Management ECR/ECO", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Change Number, Change Master, Change Management, Engineering Change Request, Engineergin Change Order, ECR, ECO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The Engineering Change Request / Engineering Change Order functionality is no more available with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA the current the Engineering Change Request / Engineering Change Order functionality within SAP Product Lifecycle Management (SAP PLM) is not available anymore. It is recommended for new customers to use Change Record instead for tracking, approving and facilitating (change management) purposes. Alternatively Engineering Record (only available on Web UI as part of the <a href=\"/notes/2269324\" target=\"_blank\">compatibility package</a>) could be assessed.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Managing (tracking, approving and facilitating) changes will still be possible. The Change Record / Engineering Record shall be used for this.</p>\n<p>The change number will serve to identify the explicit changes and the Change Record / Engineering Record is to be used to manage the whole process. The approval process, the objects to be changed will all be part of the Engineering Record, while the change number will solely be used to facilitate the changes after approval has been given.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>CC31 - Create Change Request<br/>CC32 - Change Change Request<br/>CC33 - Display Change Request<br/>CC62 - Conversion of change packages<br/>OS72 - ECH: Workflow for ECR/ECO<br/>OS73 - ECH: Workflow for Object Mgmt Record</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Existing and running Change Requests and Change Orders should be finished before system upgrade is facilitated as working with these type of object will not be possible in S/4HANA. Additional settings will be required in the system for the Customer to use the Change Record / Engineering Record. Set up Change Record / Engineering Record (Types, status network, BRF+) and Process Route (Activities, services, process route templates, BRF+) for tracking, approving and facilitating business object related changes.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<ul>\n<li>The Engineering Change Request / Engineering Change Order functionality is used if entries exist in DB Table AENR where the CCART field is fileld (check with SE16)</li>\n<li>Use ST03N (on the production system, if possible) to check transaction profile of the complete last month, whether the following transactions have been used: CC31, CC32, CC33, CC62, OS72, OS73</li>\n</ul>\n<p>﻿﻿</p>", "noteVersion": 5, "refer_note": [{"note": "2268043", "noteTitle": "2268043 - S4TWL - Engineering Record", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Engineering Record, PLM Web UI, Change Record, compatibility pack</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Upgrade information</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The PLM Web UI <em><strong>Engineering Record</strong> (PLM-WUI-OBJ-ECR)</em> is part of the <a href=\"/notes/2269324\" target=\"_blank\">compatibility package</a>, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, PLM Web UI Engineering Record can be found under the ID 441.</p>\n<p>Its alternative solution, <em><strong>Change Record</strong> (PLM-CR)</em> is available with more modern user interface and additional features, and it is continuously being improved. However, customers can continue to use Engineering Record within SAP S/4HANA as long as the compatibility pack is supported.</p>\n<p><strong>Business Process Related Information</strong></p>\n<p>The following capabilities are the key available capabilities in the alternative solution, <em><strong>Change Record</strong></em>:</p>\n<ul>\n<li>SAP <strong>Fiori UX</strong> to deliver personalized, responsive, and simple user experience including:</li>\n<ul>\n<li>Status-based field attributes</li>\n<li>Visual change process to track milestones</li>\n<li>Quick overview on assigned change objects</li>\n<li>Thumbnails of change objects (on-premise only)</li>\n</ul>\n<li><strong>Workflow</strong> driven change management process using process routes:</li>\n<ul>\n<li>Rule-based automatic or ad-hoc template loading</li>\n<li>Support of parallel, sequential</li>\n<li>Support of background tasks (on-premise only)</li>\n<li>Graphical process route status overview</li>\n<li>Integration to <em>S/4HANA Teams and Responsibility Management</em></li>\n<li>Automatic agent determination</li>\n<li>Unified <em>My Inbox</em> for task owners to monitor and handle their tasks</li>\n<li>Email notifications on task assignments (on-premise only)</li>\n<li>Sub-workflows for change objects</li>\n</ul>\n<li>Ad-hoc stakeholder communication with SAP CoPilot or built-in commenting functions</li>\n<li>Support of <strong>Electronic Signatures</strong> including signature strategies (on-premise only)</li>\n<li>Easy attachments including support for <em>Document Info Records</em></li>\n<li>\n<div><strong>Change impact analysis</strong> to determine the related impact in the downstream processes</div>\n</li>\n<li>Automatic Change Number generation</li>\n<li>Change Record hierarchies</li>\n<li><em>Engineering Cockpit</em>, giving actionable insights on business process with visualized data</li>\n<li>Extensibility on customer attributes via</li>\n<ul>\n<li>SAP <em>S/4HANA Custom Fields and Logic</em> app</li>\n<li>Integration to Classification System</li>\n</ul>\n<li>Search capabilities using SAP Fiori Search</li>\n<li><strong>Newly Added</strong> <strong>Features</strong> with <strong>On-Premise 2022</strong> release:</li>\n<ul>\n<li>Enabling Digital Signature at Change Item level (on-premise only)</li>\n<li>Ability to assign purpose to a changed record type via configuration</li>\n<li>Integration of BOM objects to change records include:-</li>\n<ul>\n<li>Equipment BOM</li>\n<li>Standard BOM</li>\n<li>Functional location BOM</li>\n<li>Variant BOM</li>\n<li>Order BOM</li>\n<li>WBS BOM</li>\n</ul>\n<li>Integration of task list object types to change records include:\r\n<ul>\n<li>Equipment (type E)</li>\n<li>Functional location (type T)</li>\n<li>General maintenance task list (type A)</li>\n<li>Rough-cut planning profile task list (type 3)</li>\n<li>Reference operation set task list (type S)</li>\n<li>Rate routing task list (type R)</li>\n<li>Reference rate routing task list (type M)</li>\n</ul>\n</li>\n</ul>\n</ul>\n<p>More information about <em><strong>Change Record</strong></em> can be found in the following applications:</p>\n<ul>\n<ul>\n<li><strong>[</strong><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/6f020371ca9f4bbb8ec6e59e21da2e98/0b5e51261df7411fbbca11d2464ef1c9.html?locale=en-US\" target=\"_blank\">Manage Change Records</a><strong>]</strong></li>\n<li><strong>[</strong><a href=\"https://help.sap.com/docs/SAP_S4HANA_ON-PREMISE/6f020371ca9f4bbb8ec6e59e21da2e98/85343eac429a470cb439218bb2401e91.html?locale=en-US\" target=\"_blank\">Apps in Change Management</a><strong>]</strong></li>\n</ul>\n</ul>\n<p><span>Comparing to the capabilities of Engineering Record in ECC, the following key feature is currently missing in SAP S/4HANA Change Record:</span></p>\n<ul>\n<li>Allowing customer-defined change objects</li>\n</ul>\n<p><strong> </strong></p>", "noteVersion": 10}], "activities": [{"Activity": "Business Decision", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "With SAP S/4HANA the current the Engineering Change Request / Engineering Change Order functionality within SAP Product Lifecycle Management (SAP PLM) is not available anymore. It is recommended for new customers to use Change Record instead for tracking, approving and facilitating (change management) purposes. Alternatively Engineering Record (only available on Web UI as part of the compatibility package) could be assessed."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Any customer code related to ECR and ECO, has to be adjusted accordingly"}, {"Activity": "Data cleanup / archiving", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Existing and running Change Requests and Change Orders should be finished before system upgrade is facilitated as working with these type of object will not be possible in SAP S/4HANA."}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "Depending on the business decision, user need to be trained for using Change Record or PLM WebUI Engineering Record."}, {"Activity": "Implementation project required", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "Depending on the business decision, Change Record or PLM WebUI Engineering Record has to be configured in SAP S/4HANA system."}]}