{"guid": "0050569455E21ED5B3E17678392A009E", "sitemId": "SI7_IS_DIMP_HT", "sitemTitle": "S4TWL - Condition Technique in DRM: Rule Configuration", "note": 2270855, "noteTitle": "2270855 - S4TWL - Condition Technique in DRM: Rule Configuration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Distributor Reseller Management (DRM) was part of the Discrete Industries and Mill Products (DIMP) industry solution.</p>\n<p>DRM supports business processes for the distribution and resale of High-Tech products, from the perspective of both a High-Tech Manufacturer and a Distributor.</p>\n<p>It provided capabilities such as ship-and-debit agreements, sell-in and sell-through distribution, drop-shipment to distributor customer, ship-and-debit claims processing, price change and price protection.</p>\n<p>“Condition Technique in DRM: Rule Configuration” provided rules configuration enhancements to the core DRM solution, specifically to treat claims differently depending on parameters such as tracking partner, sales area, material, etc.</p>\n<p>Within SAP Business Suite, DRM had been superseded by the SAP CRM Channel Management for High Tech solution.</p>\n<p>DRM and thus “Condition Technique in DRM: Rule Configuration” have not been brought into SAP S/4HANA.</p>\n<p>SAP Channel Management for High Tech is also not available in SAP S/4HANA.</p>\n<p>There is no specific, targeted successor solution in SAP S/4HANA for these capabilities.</p>\n<p> </p>", "noteVersion": 1, "refer_note": [{"note": "2349033", "noteTitle": "2349033 - S4TC Transition Check for ECC-DIMP System Conversion to SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>When you perform system conversion to SAP S/4HANA, we have provided pre-checks to help you analyze the transition.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Pre-check, System Conversion, ECC-DIMP, Flight Scheduling, HI-TECH , AMOSS, XLO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the note.</p>\n<p>The pre-check class checks whether the respective tables exist. Entries in the tables are checked. If the entries are present, the class displays an error indicating that you need to delete these entries from the tables. Execute the pre-check class again and check if the run is successful.</p>\n<p>Deprecated topics for which pre-check has been created:</p>\n<ul>\n<li>Flight Scheduling</li>\n<li>HI-TECH </li>\n<li>AMOSS</li>\n<li>XLO</li>\n</ul>", "noteVersion": 18}, {"note": "2383997", "noteTitle": "2383997 - SAP S/4HANA Simplification : Distributor Reseller Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using a SAP ERP system and intend to perform the system conversion to SAP S/4HANA.</p>\n<p>The custom code check shows customer objects affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<p>The custom code check refers to SAP Note 2383997, which covers the deprecation of Distributor Reseller Management solution in the High-tech industry area.</p>\n<p>The piece lists for this functionality are SI_DIMP_HT_DRM, SI_DIMP_HT_DRM_EXITS, SI_DIMP_HT_COND_TQ.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4_TRANSFORMATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note contains detailed descriptions on how to adopt customer objects to the functionality changes in Distributor Reseller Management solution related to High-tech industry.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Eliminate the indicated usages from your customer objects.</p>\n<p>The pre-check SAP Note 2349033 provides you the relevant information related to the cleanup of the data before migration to SAP S/4HANA.</p>", "noteVersion": 3, "refer_note": [{"note": "2270856", "noteTitle": "2270856 - S4TWL - Manufacturer/Supplier Processes", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Distributor Reseller Management (DRM) was part of the Discrete Industries and Mill Products (DIMP) industry solution.</p>\n<p>DRM supports business processes for the distribution and resale of High Tech products, from the perspective of both a High Tech Manufacturer and a Distributor.</p>\n<p>It provided capabilities such as ship-and-debit agreements, sell-in and sell-through distribution, drop-shipment to distributor customer, ship-and-debit claims processing, price change and price protection.</p>\n<p>“Manufacturer/Supplier Processes” provided the core capabilities for High Tech Manufacturers.</p>\n<p>Within SAP Business Suite, DRM had been superseded by the SAP CRM Channel Management for High Tech solution.</p>\n<p>DRM and thus “Manufacturer/Supplier Processes” have not been brought into SAP S/4HANA, nor has SAP Channel Management for High Tech been brought into SAP S/4HANA.</p>\n<p>Thus, there is no specific, targeted successor solution in SAP S/4HANA for these capabilities.</p>\n<p><strong>Business Process related information</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"1\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Transactions not available in SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>DRMPPMS_C - Price Protection (Create Due List)</p>\n<p>DRMPPMS_P - Price Protection (Change/Process Due List)</p>\n<p>DRMPPMS_D - Price Protection (Display Due List)</p>\n<p>DRMPPMS – Price Protection (Background Processing)</p>\n<p>DRMCLMS – Ship-and-Debit</p>\n<p>DRMBUMS – Bill-up</p>\n<p>DRMIRMS – Inventory Reconciliation</p>\n<p>DRMLMMS – Manual Lot Maintenance</p>\n<p>DRMR3MS – SAP Transactions</p>\n<p>DRMSTGMS – DRM Staging Area</p>\n<p>DRMXMRPT – Transmission Report</p>\n<p>DRMCDMS – Discrepancy in Ship-and-Debit Claims</p>\n<p>DRMMSBUSMET – Business Metrics</p>\n<p>DRMPCMS – Price Protection Correction Report</p>\n<p>DRMPEMS – Partner Validation Report</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 1}]}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Distributor Reseller Management (DRM) and its successor in SAP Business Suite - SAP Channel Management for High Tech are unavailable in SAP S/4HANA, consider alternative solutions"}, {"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Redesign of the process since the functionality is unavailable"}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": "DRM transactions are not available, process change and user training required"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Adopt Custom Code objects to the functionality changes in Distributor Reseller Management solution"}, {"Activity": "Data cleanup / archiving", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "Clean up your data as per Note 2349033 before migration"}]}