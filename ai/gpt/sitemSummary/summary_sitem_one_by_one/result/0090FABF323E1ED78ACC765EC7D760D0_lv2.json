{"guid": "0090FABF323E1ED78ACC765EC7D760D0", "sitemId": "SI17: CT_PROCESS_OBSERVER", "sitemTitle": "S4TWL - Changes in Process Observer", "note": 2412899, "noteTitle": "2412899 - Changes in Process Observer when moving from SAP Business Suite to S/4HANA OP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>A few changes occurred in the implementation of Process Observer in S/4HANA, which may affect you when moving from SAP Business Suite to S/4HANA OP version.</p>\n<p>1) Business Function FND_EPT_PROC_ORCH_1 for Process Observer is always on</p>\n<p>2) Improved IMG structure in S/4HANA</p>\n<p>3) Name change for background job for event processing</p>\n<p>4) Authorization check for maintaining process definitions enabled</p>\n<p>5) Predecessor determination by document relationship browser (DRB) is off by default</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Process Performance Monitoring in S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>1) Business Function FND_EPT_PROC_ORCH_1:</p>\n<p>In order to use Process Observer in S/4HANA, there is no need to activate FND_EPT_PROC_ORCH_1 any more, as it is set to 'always on' in the s/4HANA systems. The remaining steps to activate process observer is the:</p>\n<p>- activation setting: via IMG or transaction POC_ACTIVE</p>\n<p>- scheduling of background job for event processing: via IMG or transaction POC_JOB_SCHEDULER</p>\n<p>2) Improved IMG structure for Process Observer:</p>\n<p>Please see note 2461687 to find information about the changes to the IMG structure of Process Observer, and to relocate the activities.</p>\n<p>3) Background job for event processing:</p>\n<p>The name of the running background job for event processing was changed from 'POC_JOB_&lt;client&gt;' in SAP Business Suite to 'SAP_POC_PROCESS_EVENT_QUEUES'. This may be relevant, for example when managing the job via SM37.</p>\n<p>4) Authorization check for maintaining process definitions:</p>\n<p>The authorization check for maintaining process definitions via IMG or transaction POC_MODEL was not activated by default SAP Business Suite. In S/4HANA the authorization object POC_DEFN is checked when trying to create, change or delete process definitions. Make sure the corresponding user roles in S/4HANA contain this authorization object with the appropriate settings.</p>\n<p>5) Predecessor determination via DRB:</p>\n<p>In your SAP Business Suite implementation you may be relying on predecessor determination for events using DRB, if the following appies:</p>\n<p>- you are using BOR events for monitoring</p>\n<p>- different business object types are part of one process definition</p>\n<p>- you did not implement or configure a specific predecessor determination for your objects (like referencing an attribute in BOR event, or BAdI implementation)</p>\n<p>In order to activate the predecessor determination via DRB again in S/4HANA do the following: enter the relevant BOR events in the follwing IMG activity:</p>\n<p>&lt;...&gt; Process Observer - Application Instrumentation - BOR Event Instrumentation - Enable Data Relationship Browser (DRB) Usage</p>", "noteVersion": 2, "refer_note": [{"note": "2461687", "noteTitle": "2461687 - Change of IMG-Structure for Process Observer when moving from SAP Business Suite to S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Process Observer contains an improved IMG structure in S/4HANA compared to SAP Business Suite. Nodes in the IMG and activity names may be changed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">In both products, the IMG for process observer can be directly reached via transaction POC_CUSTOMIZING or via the SAP Reference Implementation Guide.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The IMG new structure contains the following structure:</p>\n<p>- Activation: All activities to activate Process Observer in the client</p>\n<p>- Facade Layer Content: Definition of tasks for usage in process definitions</p>\n<p>- Application Instrumentation: Mapping of application events to task layer + event enrichment</p>\n<p>- Federation: system setup for cross-system and local federation</p>\n<p>- Process Definition: Define process, KPIs, Thresholds for monitoring</p>\n<p>- Process Logging: Activate/Deactivate logging or error tracing</p>\n<p>- BI Content activation: Activate BI content objects</p>\n<p>Check the mapping of Business Suite IMG activities vs. S/4HANA IMG activities in the attached documents.</p>", "noteVersion": 1}], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "perform customizing and authorization adjustments"}, {"Activity": "Business Operations", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "rename background job names for event processing"}]}