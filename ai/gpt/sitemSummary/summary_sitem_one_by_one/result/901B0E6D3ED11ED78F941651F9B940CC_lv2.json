{"guid": "901B0E6D3ED11ED78F941651F9B940CC", "sitemId": "SI03: OTAS_SALES_ACTITIVITIES", "sitemTitle": "S4TWL - OTAS - Sales Activities", "note": 2490447, "noteTitle": "2490447 - S4TWL - OTAS - Sales Activities", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert a system with Add-on OTAS to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Simplification Item, System Conversion to SAP S/4HANA, OTAS Application Sales Action, Transactions /OTAS/SA_ACTION, /OTAS/SA_EVALUATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are using the OTAS application Sales Activities (or Sales Action) in combination with OGSD.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description:</strong></p>\n<p>You can use this OTAS application Sales Activities (or Sales Action) to map the commission transaction.</p>\n<p><strong>Business related information:</strong></p>\n<p>The action partner (commission recipient) provides services to a group of ship-to parties (delivery locations) in a specific region. The company issues a quotation to its action partners. You need to create a quotation, the action document, concerning the delivery of various products with various delivery dates and prices. The action partner forwards this quotation, with any necessary changes, to the ship-to party. When a quotation results in an order, a commission settlement will be made for the action partner based on the orders that are delivered to its ship-to parties.</p>\n<p>OTAS application Sales Activities is discontinued in SAP S/4HANA, so all its program and DDIC objects as well as all data are lost after a migration to SAP S/4HANA.</p>\n<p>For further information about \"Sales Activities\" (= \"Sales Action\") see:</p>\n<p><a href=\"https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/6809d552b2340126e10000000a4450e5.html\" target=\"_blank\">https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/6809d552b2340126e10000000a4450e5.html</a></p>\n<p><strong>Required and recommended action(s):</strong></p>\n<ol>\n<li>OTAS application \"Sales Activities\" is discontinued in SAP S/4HANA, so all its program and DDIC objects as well as all data are lost after a migration to SAP S/4HANA.</li>\n<li>If you want to continue using functionalities provided by OTAS application \"Sales Activities\" also in SAP S/4HANA, you need to consider switching to other SAP modules / applications.</li>\n<li>In case of a switching to another module, tool or functionality you need to organize knowledge transfer to all users working with OTAS-Application \"Sales Activities\" as their pattern of work will change considerably after such a switch. </li>\n</ol>\n<p>Users will have to use new or different applications for creating, changing, displaying, processing, planning and monitoring your commission transactions.<br/><br/></p>", "noteVersion": 2, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "decide about replacement of functionality unavailable in SAP S/4HANA"}, {"Activity": "Implementation project required", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "implement customer-specific replacement solution"}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "remove obsolete custom code"}]}