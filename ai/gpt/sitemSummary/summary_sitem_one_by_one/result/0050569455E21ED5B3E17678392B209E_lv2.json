{"guid": "0050569455E21ED5B3E17678392B209E", "sitemId": "SI2: Public Sector_PSM-FM - USFED_FACTS", "sitemTitle": "S4TWL - Functionality for FACTS I and FACTS II (Federal Agencies Centralized Tri", "note": 2270415, "noteTitle": "2270415 - S4TWL - Functionality for FACTS I and FACTS II (Federal Agencies Centralized Trial Balance System)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>As mandated by US Treasury, the US Federal specific Trial Balance System has been changed from FACTS I (Federal Agencies Centralized</p>\n<p>Trial Balance System I) and FACTS II (Federal Agencies Centralized Trial Balance System II) to GTAS (Government-wide Treasury Account Symbol Adjusted Trial Balance System) starting with fiscal year 2014. SAP's US Federal Ledger 95 (FMUSGA and FMUSFGT) is now the single reporting ledger for financial reports such as GTAS bulk files and Trial Balances. It replaces the US Federal Ledgers 96 and 97 (FMUSFGFACTS1A, FMUSFGFACTS1T as well as FMUSFGFACTS2A and FMUSFGFACTS2T).</p>\n<p><strong>Business Process related information</strong></p>\n<p>All known customer implementations have already switched to GTAS, aligning with the legal regulations for FACTS/GTAS. This entry just appears for the record.</p>\n<p>The business process remains as is.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p><strong> Transaction not available in SAP S/4HANA</strong></p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>RFACTS1_BL</p>\n<p>RFACTS1_FILE_SEND</p>\n<p>RFACTS2_BL</p>\n<p>RFACTS2_UPLMAF</p>\n<p>RFACTS2_MAF</p>\n<p>RFACTS2_EXTRACT</p>\n<p>RFACTS2_FOOTNOTE</p>\n<p>RFACTS2_EDITS</p>\n<p>RFACTS2_FILE_SEND</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong><strong>How to determine relevancy</strong></strong></p>\n<p>Go to transaction SE16N and type “FMUSFGFACTS1A” into the table field. Enter the current fiscal year (RYEAR) and prior year for selection. Click on the execute button (or F8).<br/>If no entries were found in the selection, type in table name “FMUSFGFACTS2A” instead, enter the current fiscal year (RYEAR) and prior year, and execute.</p>\n<p>If entries are found in either of the selections, look at note 2225817. If no entries are found in either of the selections, nothing else has to be done.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>None</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"160\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"444\">\n<p>All related objects do still exist, thus no technical problems (like syntax errors) will occur all existing data resulting from the use of ADB in the Business Suite will remain untouched/unchanged</p>\n<p>Custom code for reporting purposes does not need to be changed to continue to display existing FACTS results and rules. Code that is used to maintain the FACTS settings or to execute FACTS functionality can no longer be executed.</p>\n<p>This is documented in SAP note 2225817.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 5, "refer_note": [{"note": "2225817", "noteTitle": "2225817 - SAP S/4 HANA Simplification Item: FACTS reporting for US Federal Government", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have implemented own coding using the FACTS objects.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4 simplification, US FG FACTS reporting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>As mandated by US Treasury, the US Federal specific Trial Balance System has been changed from FACTS I (Federal Agencies Centralized Trial Balance System I) and FACTS II (Federal Agencies Centralized Trial Balance System II) to GTAS (Governmentwide Treasury Account Symbol Adjusted Trial Balance System) starting with fiscal year 2014. SAP's US Federal Ledger 95 (FMUSGA and FMUSFGT) is now the single reporting ledger for financial reports such as GTAS bulk files and Trial Balances. It replaces the US Federal Ledgers 96 and 97 (FMUSFGFACTS1A, FMUSFGFACTS1T as well as FMUSFGFACTS2A and FMUSFGFACTS2T).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>FACTS functionality has been deactivated and is not supported with S/4 HANA. Execution of transactions belonging to FACTS functionality is blocked and not possible.</p>\n<p>All related objects do still exist, thus</p>\n<ul>\n<li>no technical problems (like syntax errors) will occur</li>\n<li>all existing data resulting from the use of FACTS in the Business Suite will remain untouched/unchanged</li>\n</ul>\n<p>You can use the transport piecelist SI_PSM_FACTS to check which of your custom code relates to which FACTS object.</p>\n<p>Custom code for reporting purposes does not need to be changed to continue to display existing FACTS data.</p>\n<p>Code that is used to maintain the FACTS settings or to execute FACTS functionality can no longer be executed. You need to adapt such coding to the new GTAS functionality, using GTAS configuration, GTAS transactions based on the database tables used for US Federal Ledger 95, FMUSGA and FMUSFGT.</p>\n<p>The disablement does not require any data migration activities.</p>", "noteVersion": 2, "refer_note": [{"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}]}], "activities": [{"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}