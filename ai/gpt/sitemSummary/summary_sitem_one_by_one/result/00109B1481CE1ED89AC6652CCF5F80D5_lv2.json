{"guid": "00109B1481CE1ED89AC6652CCF5F80D5", "sitemId": "SI50a: Logistics_General", "sitemTitle": "S4TWL - Segmentation (from 1809 onwards)", "note": 2652842, "noteTitle": "2652842 - S4TWL - Segmentation (from 1809)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1809 or higher, and you are using segmentation related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segmentation, Fashion, Retail.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>In SAP S/4HANA segmentation is available, but has some diiferences compared to SAP ERP which need to be considered.</p>\n<p><strong><strong><strong><strong>Business Process related information</strong></strong></strong></strong></p>\n<p>The differences in segmentaion between SAP ERP and SAP S/4HANA are the following:</p>\n<ul>\n<li>In SAP S/4HANA segmentation strategies 1:n and n:m are available for MRP and supply asignment.</li>\n<li>In SAP S/4HANA segmentation strategies 1:n and n:m are not available for advanced ATP. Advanced ATP supports Pool Segment only.</li>\n<li>Segmentation is considered only in the advanced ATP (aATP). The classic ATP is not supported anymore. </li>\n<li>Stock protection in SAP ERP is implemented via segmentation. In SAP S/4HANA stock protection can be achieved via product allocation (PAL).</li>\n<li>In SAP S/4HANA, the default segmentation can be maitained via a specific transaction (SGT_DEFAULT).</li>\n<li>In SAP S/4HANA transactions to configure segmentation structures and strategies (SGTS, SGTC) are now available in the application (vs. customiting in SAP ERP).</li>\n<li>In SAP S/4HANA MRP related article/material master data on segment level are not available anymore.</li>\n<li>In SAP S/4HANA segmentation maintenance in the article/material master has been simplified compared to SAP ERP (e.g. automatic fixing of the segmentation strategy during article and listing maintenance). </li>\n<li>In SAP S/4HANA the transaction LS24 doesn’t support  the “Refresh” button anymore</li>\n<li>PRC planning level (SGT_PRPL) and Consecutive Number for Segmentation of Equal Status (SGT_CNES) fields are not supported anymore</li>\n<li>Safety stock is supported only at Default stock segment level (vs stock segment level in SAP ERP)</li>\n<li>Segmentation is considered only in MRP live (MD01N). The classic MRP is not supported. </li>\n<li>In SAP S/4HANA segmentation is not supported in the following applications:</li>\n<ul>\n<li>Quality Management (QM)</li>\n<li>Production Planning - Detailed Scheduling (PP-DS)</li>\n<li>Process Order</li>\n<li>Project stock</li>\n<li>Demand-Driven MRP (DDMRP)</li>\n<li>Predictive MRP (pMRP)</li>\n<li>Segmentation structure and strategy are not considered in EWM</li>\n<li>Spare Parts Planning (SPP) </li>\n</ul>\n<li>In SAP S/4HANA, the following fields are not available:</li>\n<ul>\n<li>Discrete Batch Number (SGT_CHINT)</li>\n<li>Stock Protection Indicator (SGT_STK_PRT)  </li>\n<li>Consumption Priority (SGT_PRCM)  </li>\n<li>ATP/MRP Status for Material and Segment (SGT_MRP_ATP_STATUS)  </li>\n<li>Date from which the plant-specific material status is valid (SGT_MMSTD)</li>\n</ul>\n<li>In SAP S/4HANA, the following transactions are not available:</li>\n<ul>\n<li>SGT_CONVERT_MATERIAL</li>\n<li>SGT_MAINTAIN_VALT</li>\n<li>SGT_MASS_EXTD_MAT</li>\n</ul>\n</ul>\n<p><strong><strong><strong><strong>Required and Recommended Action</strong></strong></strong></strong></p>\n<p>Part of the conversion from SAP ERP to SAP S/4HANA, article need to be resasigned from \"old\" to \"new\" segmentation, please see referenced note.</p>\n<p>Implemented business processes need to be adjusted according to the changes listed above.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if segmentation is used. <br/>This can be checked via transaction SE16N. Enter table <span class=\"sapMText sapMTextMaxWidth sapUiSelectable sapUiTinyMarginTopBottom tdbCheckListFont\" id=\"__text36-__list6-0\">SGT_COVS_T </span>and check whether there are entries.<br/>There is also a pre-check class <span class=\"sapMText sapMTextMaxWidth sapUiSelectable sapUiTinyMarginTopBottom tdbCheckListFont\" id=\"__text39-__list7-0\">CLS4SIC_SGT_MASTER_DATA, please see attached note.</span></p>", "noteVersion": 16, "refer_note": [], "activities": [{"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "The report R_SGT_DEFAULT_MIG is executed automatically during conversion.In case of problems during the conversion, you can execute these reports manually."}, {"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Business processes will have to be adapted as per the changes listed in SAP Note 2652842"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Users must ne trained in the new process designed"}]}