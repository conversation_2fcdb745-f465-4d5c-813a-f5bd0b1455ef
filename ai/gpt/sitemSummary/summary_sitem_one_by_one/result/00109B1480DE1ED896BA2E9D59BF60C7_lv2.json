{"guid": "00109B1480DE1ED896BA2E9D59BF60C7", "sitemId": "SI8_FIN_GL", "sitemTitle": "S4TWL - Amount Field Length Extension", "note": 2628654, "noteTitle": "2628654 - S4TWL: Amount Field Length Extension", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types have been extended.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, 23 digits, Currency, Decoupling</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Table of Contents: </strong></p>\n<ul>\n<li><strong><a href=\"#Intro\" target=\"_self\">Introduction</a></strong></li>\n<li><strong><a href=\"#Scope\" target=\"_self\">Motivation and Scope</a></strong></li>\n<li><strong><a href=\"#Consistant\" target=\"_self\">Consistent Usage in System-Internal Code</a></strong></li>\n<ul>\n<li><strong><a href=\"#Decoupling\" target=\"_self\">Decoupling</a></strong></li>\n<li><strong><a href=\"#Adaptation\" target=\"_self\">Code Adaptation</a></strong></li>\n<li><strong><a href=\"#Database\" target=\"_self\">Storage of an Amount on the Database</a></strong></li>\n<li><strong><a href=\"#Interface\" target=\"_self\">Internal Interfaces</a></strong></li>\n<li><strong><a href=\"#Write\" target=\"_self\">Write To List</a></strong></li>\n<li><strong><a href=\"#Type\" target=\"_self\">Strict Type Checks</a></strong></li>\n<li><strong><a href=\"#External\" target=\"_self\">External Interfaces</a></strong></li>\n</ul>\n<li><strong><a href=\"#Settings\" target=\"_self\">System Settings to Activate the Provided Amount Field Length Extension in Customizing﻿</a></strong></li>\n<li><strong><a href=\"#functions\" target=\"_self\">Functions that may use the Provided Amount Field Length Extension </a></strong></li>\n</ul>\n<p><strong>Introduction <a name=\"Intro\" target=\"_blank\"></a>﻿</strong></p>\n<p>SAP S/4HANA can support extended amount fields. We have adapted the appropriate related SAP development entities (domains, data elements, structures, table types, and transparent tables, external and internal interfaces, user interfaces, and so on) accordingly. There are some scenarios where it will be required for you to adapt your code.  In a multi-system landscape, you must decide if and when to activate the provided amount field length extension in customizing, as this will impact how the system communicates compatibly within a system landscape.  To ensure consistent behavior, the provided amount field length extension must be explicitly activated in customizing.  Note that the extended field length in the data dictionary is not influenced by this activation.  Refer to the below in section <a href=\"#Settings\" target=\"_self\">System Settings to Activate the Provided Amount Field Length Extension in Customizing </a>for details.</p>\n<p>This note focuses on what is affected by the amount field length extension and what adjustments are necessary to ensure that the system remains technically and functionally stable.  The different aspects of the amount field length extension in SAP S/4HANA are described in the following sections. <em> </em>Consider the restrictions outlined here and in the notes listed under References.</p>\n<p><strong>Motivation and Scope<a name=\"Scope\" target=\"_blank\"></a>﻿</strong></p>\n<p>The amount field length extension was developed to meet the requirements of banks and financial institutions to post financial documents representing balance sheet information with amounts that exceed what the previous definition in SAP ERP ECC60 and previous S/4HANA releases support. Therefore, we extended the amount fields within the general ledger and Controlling application areas. As part of these changes, we changed data elements of other application components with shared dependencies.</p>\n<p>The fields that were subject to extension were primarily data elements of type CURR with a defined length between 9 and 22, including 2 decimal places.  Additionally, data elements of type DEC, CHAR, and NUMC that were used to store amounts were also extended.</p>\n<p>While this scope was limited, there was a broad usage of the extended data elements across other applications that will not use extended amounts, for example Logistics.  We decoupled the data elements that would be extended by creating copies of the original with the original domain and technical attributes, and replaced them in all data dictionary usages outside of the areas that were planned for extension.   We extended the original data elements by exchanging the existing shorter domains with new longer domains. Conversion routines were created and assigned to the longer domains to limit the maximum value permitted as screen input where the provided amount field length extension is not activated in customizing.</p>\n<p>To maintain the stability of the applications, certain adjustments were required for data dictionary objects, ABAP source code, dependent user interfaces, as well as in both internal and external interfaces.</p>\n<p><strong>See the attached document for a list of application components with extended amounts.</strong></p>\n<p>  <strong>Assumptions</strong></p>\n<ul>\n<li>Data elements of type CURR with more than 2 decimal places are generally not related to currency amounts.</li>\n<li>Any currency amount with a field length of less than 9 digits including 2 decimals was not intended for long amounts in the first place.</li>\n</ul>\n<p>At a high level, there are the below impacts to your objects due to the extended amount field lengths:</p>\n<ol>\n<li>There are code changes that may be necessary to adapt, irrespective of whether you will support the provided amount field length extension or not. This is necessary to avoid runtime errors and syntax errors once you upgrade to S/4HANA 1809 or higher. Refer to notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>, <a href=\"/notes/2610650\" target=\"_blank\">2610650</a> for more information.</li>\n<li>If you choose not to support the provided amount field length extension in your code, although you interact with modules that do support and/or use extended amounts, adjustments may be necessary to ensure that your code is not impacted. Refer to notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>, <a href=\"/notes/2628699\" target=\"_blank\">2628699</a>, <a href=\"/notes/2628704\" target=\"_blank\">2628704</a>, <a href=\"/notes/2628706\" target=\"_blank\">2628706</a>, <a href=\"/notes/2628724\" target=\"_blank\">2628724  </a>for more information.</li>\n<li>If you choose to support the provided amount field length extension, enablement is necessary and adjustments should be made. Note, however, that your enablement is directly dependent on the areas where the provided amount field length extension is supported in SAP S/4HANA 1809 or later.  Refer to notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>, <a href=\"/notes/2610650\" target=\"_blank\">2610650</a>, <a href=\"/notes/2628617\" target=\"_blank\">2628617</a>, <a href=\"/notes/2628641\" target=\"_blank\">2628641</a>, <a href=\"/notes/2628699\" target=\"_blank\">2628699</a>, <a href=\"/notes/2628704\" target=\"_blank\">2628704</a>,<a href=\"/notes/2628706\" target=\"_blank\">2628706 </a><a href=\"/notes/2628714\" target=\"_blank\">2628714</a>, and <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>,</li>\n</ol>\n<p>In the following section, we will provide an introduction to what has been done to enable the extended amounts in S/4HANA, and explain how it affects your code. For more information, see attached notes under section \"References\".</p>\n<p><strong>Consistent Usage in System-Internal Code<a name=\"Consistant\" target=\"_blank\"></a>﻿</strong></p>\n<p><strong>Decoupling<a name=\"Decoupling\" target=\"_blank\"></a>﻿</strong></p>\n<p>The consistent behavior of the SAP system is secured through the use of data dictionary objects, data elements and domains.  The relevant internal fields that are typed to these data elements derive their technical details (for example, data type, length, decimals, etc.) from the domain. Cross-usages of the data elements to be extended existed between the application areas that will use the provided amount field length extension, and the application areas that will not use extended amounts.   To maintain data integrity and compatibility between these areas while preventing any impact on user interfaces, database entries, external interfaces, etc., the cross-usages had to be identified and decoupled.  We created new data elements and introduced them in the data dictionary objects in the areas without the provided amount field length extension.</p>\n<p>For more information, see related SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>.</p>\n<p><strong>Code Adaptation<a name=\"Adaptation\" target=\"_blank\"></a>﻿</strong></p>\n<p>To facilitate the correct handling of the extended amount fields within the ABAP coding, the specific circumstances and requirements of each possible scenario were considered.  Overflow errors could occur if an extended amount is moved into a shorter amount. Syntax errors and report generation errors can be identified though S/4HANA Readiness code scans.</p>\n<p>The decoupling activities together with the amount field length extension created type conflicts due to a mismatch between data elements assigned to each other in one statement. All amount fields referring to an extended data element had to be checked to ensure that these fields were long enough to hold the extended amounts. If the dependent fields were also extended, all usages of these fields had to be analyzed as well. Extending one field typically triggered follow up changes. Overall, the complete data flow had to be analyzed to identify all places where an extended amount is moved into a shorter amount field. Such analysis must also be done in your custom coding.</p>\n<p>The described changes have also been applied to local interface signatures, including function modules, class methods, form routines, BAdIs and so on.  The types and structure parameters of these local calls were also extended wherever an amount field exists.  External interfaces were also adapted. However, a different approach was taken for these remote calls.</p>\n<p>For more information, see related SAP Note <a href=\"/notes/2610650\" target=\"_blank\">2610650</a>.</p>\n<p><strong>Storage of an Amount on the Database<a name=\"Database\" target=\"_blank\"></a>﻿</strong></p>\n<p>By extending the data elements of an amount field, the field length on the database is also extended. Although the maximum length is larger, the manner in which the content is stored has not changed.  There is no difference in how an amount is stored on the database in SAP S/4HANA 1809 compared to previous versions of S/4HANA as well as in the SAP Business Suite.  No data conversions are needed unless an amount is part of concatenated content. Concatenation in the code uses the complete technical length of the field, which is now longer, and is also reflected in the database content.</p>\n<p><strong>Internal Interfaces<a name=\"Interface\" target=\"_blank\"></a>﻿ </strong></p>\n<p>Interface related conflicts will occur if the lengths of actual and formal parameters are no longer the same.  These type conflicts were found in the internal interface calls between different applications as well as in the interface calls within the same application. A variance in the formal and actual parameters will result in a syntax error or a short dump at run time.  Resolving local interface conflicts depends mainly on the direction of the call (are the caller and receiver using extended amounts?) and data flow (importing, exporting, using, changing, or returning parameter) within each call had to be analyzed.  The parameter could either be a structure/table or it could be a single field.  For more information, see related SAP Note <a href=\"/notes/2628699\" target=\"_blank\">2628699</a>.</p>\n<p><strong>Write Statements for List Output<a name=\"Write\" target=\"_blank\"></a>﻿</strong></p>\n<p>When amount fields that are extended are used in WRITE statements for list processing, they may cause the list to go out of alignment and overlapping values independent of whether the provided amount field length extension is active or not.   Data can be truncated or completely lost if no adjustment is made.  Other write statements might need adjustments in case of multi-row output, printing column headings, or splitting the preparation of one row into multiple WRITE statements.  For more information, see related SAP Note <a href=\"/notes/2610650\" target=\"_blank\">2610650</a>.</p>\n<p><strong>Strict Type Checks<a name=\"Type\" target=\"_blank\"></a>﻿ </strong></p>\n<p>There are statements within the ABAP code that require strict type checks where data elements assigned to each other must have the same data type and length.  A syntax error will be issued when the fields within the statement are incompatible.  Conflicts will occur if an extended amount is used in this type of statement within an application area that is not using the provided amount field length extension. There is no implicit type conversion done in this case. Adjustments are necessary to avoid syntax errors. This can be solved with the help of an auxiliary variable or by changing the type to the copied data element.  For more information, see related SAP Note <a href=\"/notes/2610650\" target=\"_blank\">2610650</a>.</p>\n<p><strong>External Interfaces<a name=\"External\" target=\"_blank\"></a>﻿ </strong></p>\n<p>A typical ERP system is connected to miscellaneous internal and external (SAP or non-SAP) systems. Accordingly, an S/4HANA system with the provided amount field length extension activated in customizing must consider that not all related systems will be able to handle extended amounts.  Furthermore, it cannot be assumed that all ERP systems in your landscape will be converted or upgraded to SAP S/4HANA 1809 at the same point in time. That means that the external interfaces that are used for integration must be able to function as in the previous versions of the interface.</p>\n<p>This is especially relevant for the commonly used integration techniques BAPI, RFC, and IDoc as these techniques rely on a fixed length and sequence of fields in the transmitted data. Simply extending the amount fields in these interfaces would technically break how they interact with the inbound and outbound calling programs.</p>\n<p>As mentioned above, we used a different approach for internal and released external APIs.  We have decided to provide a technical-version compatibility for released external interfaces in the way that is commonly used and proposed for BAPI interfaces: The already existing field remains with its original length, and an additional extended field is added at the end of the structure parameter (or as an additional single parameter) allowing for transmision of the extended lengths.  It must be ensured that no extended field can be maintained in the system with a content length greater than the original length of the field.</p>\n<p>It must be ensured that any released external API containing an extended amount field as a single parameter or as part of a structure parameter, can be called internally or locally within one system. Hence, when the provided amount field length extension is activated in customizing, all internal calls of external interfaces must only use the extended fields.</p>\n<p>The changes described were done for BAPIs, IDocs, and released remote-enabled function modules, and where necessary, unreleased remote enabled function modules used to communicate with other SAP Business Suite products like other SAP ERP installations or SAP CRM.</p>\n<p>A complete list of relevant function modules, IDocs, and structures that have been extended in this way can be found in the piece lists in the simplification database in SAP Notes <a href=\"/notes/2628699\" target=\"_blank\">2628699</a>, <a href=\"/notes/2628706\" target=\"_blank\">2628706 </a>and <a href=\"/notes/2628704\" target=\"_blank\">2628704</a>.</p>\n<p>Refer to notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>, <a href=\"/notes/2628724\" target=\"_blank\">2628724 </a>for more information.</p>\n<p><strong>System Settings to Activate the Provided Amount Field Length Extension in Customizing<a name=\"Settings\" target=\"_blank\"></a>﻿﻿</strong></p>\n<p>When converting from the SAP Business Suite System, or upgrading from an older version of SAP S/4HANA, to SAP S/4HANA 1809 or later, the amount field length extension is not activated in customizing by default.</p>\n<p><em><span><strong>Note:</strong> <strong>Once the provided amount field length extension is activated in customizing, it cannot be deactivated.</strong></span></em></p>\n<p>The activation is realized as a client-dependent customizing table that can be accessed in Customizing via the following path:</p>\n<p>Cross-Application Components -&gt; General Application Functions -&gt; Field Length Extension -&gt; Activate Extended Fields</p>\n<p>Set the checkbox for Extended Amount Length and SAVE.</p>\n<p>Alternatively, transaction FLETS.  Authorization group FLE [authorization object S_TABU_DIS] is required for table maintenance.</p>\n<p> </p>\n<p><strong><a name=\"functions\" target=\"_blank\"></a>﻿Below is a list of functions that can be used with the</strong> <strong>amount field length extension:</strong></p>\n<ul>\n<li>Manage Profit Center Groups</li>\n<li>Manage Profit Centers</li>\n<li>Edit Master Data – Profit Centers – Collective</li>\n</ul>\n<p>This process step is to make changes to large quantities of profit center master data without having to change the master data for each single profit center.</p>\n<ul>\n<li>Edit Company Code Assignment Profit Centers</li>\n</ul>\n<p>In this activity, you make changes to the company code assignments of a larger number of profit centers without having to change the master data for each single profit center.</p>\n<ul>\n<li>Display Parked Journal Entries</li>\n</ul>\n<p>You can use document parking to enter and store (park) incomplete documents in the SAP System without carrying out extensive entry checks. Parked documents can be completed, checked, and then posted later or deleted – if necessary by a different accounting clerk. With this app you can display parked documents.</p>\n<ul>\n<li>Parked Journal Entry Changes</li>\n</ul>\n<p>You can use document parking to enter and store (park) incomplete documents in the SAP System without carrying out extensive entry checks. Parked documents can be completed, checked, and then posted later or deleted – if necessary by a different accounting clerk. With this app you can track all changes made in parked journal entries.</p>\n<ul>\n<li>Post General Journal Entries</li>\n</ul>\n<p>You receive G/L account documents that must be posted manually in the SAP system.  When posting to the balance sheet accounts only, you only need to make an entry in the Segment field. If the field is left blank segment 1000_C will be defaulted and will need to be reconciled.</p>\n<ul>\n<li>Upload General Journal Entries</li>\n</ul>\n<p>You receive G/L account documents that must be posted manually in the SAP system.  When posting to the balance sheet accounts only, you only need to make an entry in the Segment field. If the field is left blank segment 1000_C will be defaulted and will need to be reconciled.  The app allows for multiple G/L account documents to be uploaded using a single upload file.</p>\n<ul>\n<li>Verify General Journal Entries</li>\n</ul>\n<p>You use Verify General Journal Entries for Requester to enter a journal entry which needs to be checked before final posting.  You can check the status of the documents submitted for verification in various tabs: All, Submitted, Rejected and Others. For those rejected, you can edit and submit again for approval to Processor.  Documents with status Submitted have not been posted yet. Once approved by the processor, the document is posted automatically and it can be seen in the SAP Fiori App Manage Journal Entries.</p>\n<ul>\n<li>Approve Verify General Journal Entries (for Processor – Inbox)</li>\n</ul>\n<p>You receive G/L account documents that need to be checked before final posting. After checking the documents, you can either approve or reject with a comment/ reason. Once a document has been selected and approved, it disappears from the list. If a document is approved, posting of FI document is automatically triggered.</p>\n<ul>\n<li>Reject Verify General Journal Entries (for Processor – Inbox)</li>\n</ul>\n<p>You receive G/L account documents that need to be checked before final posting. After checking the documents, you can either approve or reject with a comment/ reason. Once a document has been selected and rejected, it disappears from the list and goes back to the requester.</p>\n<ul>\n<li>Edit a Rejected Verify General Journal Entry (Requester)</li>\n</ul>\n<p>The G/L account documents need to be checked before final posting. The document you submitted is rejected and for it to be approved and posted you need to make the changes requested by the approver.</p>\n<ul>\n<li>Manage Journal Entries</li>\n<li>Display G/L Account Balances</li>\n<li>Display G/L Account Line Items – Reporting View</li>\n<li>Display G/L Account Line Items – Posting View</li>\n<li>Manage Recurring Journal Entries</li>\n</ul>\n<p>This functionality supports postings that can periodically be created on a regular basis. Recurring entries are similar to standing orders with banks for debiting rent, contribution payments, or loan repayments directly. The postings are done by the recurring entry program based on the recurring entry documents.  In G/L accounting, recurring entries can be used for example, for periodic posting of deferrals and accruals, if there are fixed amounts to be posted.  The following data of a recurring entry document remains unchanged:</p>\n<ul>\n<ul>\n<ul>\n<li>Posting Key</li>\n<li>Account</li>\n<li>Amount</li>\n</ul>\n</ul>\n</ul>\n<p>You only need to enter the data that changes occasionally in a recurring entry document. This document does not result in transaction figures being updated. The recurring entry program uses this document to create a journal entry.</p>\n<ul>\n<li>Display changes of Recurring Entry</li>\n<li>Clear G/L Accounts – Manual Clearing</li>\n<li>Clear G/L Accounts</li>\n</ul>\n<p> For detailed information, refer to the <strong>J58 Best Practices</strong> page at<span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">  </span><span ar-sa;\"=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"DE\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" segoe=\"\" ui',sans-serif;=\"\"><a href=\"https://rapid.sap.com/bp/#/scopeitems/J58\" target=\"_blank\">https://rapid.sap.com/bp/#/scopeitems/J58</a>.</span></p>\n<div>\n<div>\n<div>\n<p> </p>\n</div>\n</div>\n</div>", "noteVersion": 15, "refer_note": [{"note": "2628699", "noteTitle": "2628699 - Amount Field Length Extension: Code Adaptations for Compatibly Enhanced Local Function Modules", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types have been extended. See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>for motivation and scope.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<p>Code scans might have reported statements that require adaptations.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, AFLE, 23 digits, Currency, Local Calls, Remote Calls, Code Adaptation, BAPI, RFC, IDoc, ALE, BDBG</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Introduction</strong></p>\n<p>Amount field extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases. This also applies to your code that references the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040</a> and SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>. Nevertheless, there are some artifacts that may require manual adjustments.</p>\n<p>Due to the remote function call (RFC) protocol, the field length of an already published interface parameter cannot be changed. This would be an incompatible change for both RFCs and BAPIs. Instead of this, the length of existing fields has to be kept stable and an additional field with an appropriate length has to be introduced. This approach was taken for adjusting structured and table typed parameters of BAPIs or/and released RFC modules. In some cases, the same structures or table types are also used in other, not RFC enabled function modules. These modules were compatibly adjusted (see SAP Note <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>) , although it would not strictly be required. The same applies for ALE outbound function modules generated by transaction BDBG for remote access to BAPIs, which involve compatibly adjusted IDoc segment types. (See SAP Note <a href=\"/notes/2628617\" target=\"_blank\">2628617</a>)<a href=\"file:///Z:/20_Central%20Team%20NSQ/Simplification/2438006%20Amount%20Field%20Length%20Extension%20Code%20Adaptions%20for%20compatibly%20enhanced%20local%20function%20modules.docx#_msocom_1\" target=\"_blank\"><br/></a></p>\n<p>Calls to such (compatibly adjusted) function modules are identified by custom code scans which provide a list of code locations which might require adaptations due to changes in the context of amount field length extension. This note describes how usages of affected function modules need to be analyzed and adjusted. Affected function modules/interfaces/BAPIs and RFCs in this regard are those having parameters (scalar as well as structured or table typed) which contain a field which is typed with one of the affected data elements (see attachment to SAP Notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>). The analysis and adaptation needs to focus on these parameters.</p>\n<p><em>Your code scans will provide a worklist of call locations. Due to the nature of the custom code scans these cannot inspect the type of the call, analyze if relevant parameters are used or verify the correct adaptation of the calls. This is the responsibility of the developer analyzing and adjusting your code.</em></p>\n<p><strong>Adaptation approach</strong></p>\n<ol>\n<li>First check for the found function module in the attached file, which parameter or parameters we adjusted compatibly. We introduced long fields in case of structured or table typed parameters or additional parameters in case of scalar ones. In case the respective call involves one or multiple such parameters it needs to be adapted.<a href=\"file:///Z:/20_Central%20Team%20NSQ/Simplification/2438006%20Amount%20Field%20Length%20Extension%20Code%20Adaptions%20for%20compatibly%20enhanced%20local%20function%20modules.docx#_msocom_4\" target=\"_blank\"><br/></a></li>\n<li>Next check the column \"Note\" if it contains a special remark.<a href=\"file:///Z:/20_Central%20Team%20NSQ/Simplification/2438006%20Amount%20Field%20Length%20Extension%20Code%20Adaptions%20for%20compatibly%20enhanced%20local%20function%20modules.docx#_msocom_5\" target=\"_blank\"><br/></a></li>\n</ol>\n<p>In case there is no special remark for the function module the necessary adjustments are described in SAP Note <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>,  section \"Calling Extended BAPIs and Released RFC Modules Locally in SAP S/4HANA\". Please be aware that the documentation refers to remote enabled function modules but due to the re-use of structures described in the introduction the proposed solution for local calls to these adjusted RFC modules also applies for the local function modules targeted by this SAP note.</p>\n<p>In case the function module is classified as \"ALE Outbound Module\" in the remark, the necessary adjustments are described in SAP Note <a href=\"/notes/2628617\" target=\"_blank\">2628617</a>, section \"Solution Step 4\". Some of the SAP delivered ALE outbound modules already incorporate the necessary mapping code, however it is strongly recommended to carefully check the code, and in case of doubt add the mapping in the calling code and thoroughly test if both short and long fields are filled in the generated IDocs.</p>\n<div></div>", "noteVersion": 7, "refer_note": [{"note": "2628654", "noteTitle": "2628654 - S4TWL: Amount Field Length Extension", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types have been extended.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, 23 digits, Currency, Decoupling</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Table of Contents: </strong></p>\n<ul>\n<li><strong><a href=\"#Intro\" target=\"_self\">Introduction</a></strong></li>\n<li><strong><a href=\"#Scope\" target=\"_self\">Motivation and Scope</a></strong></li>\n<li><strong><a href=\"#Consistant\" target=\"_self\">Consistent Usage in System-Internal Code</a></strong></li>\n<ul>\n<li><strong><a href=\"#Decoupling\" target=\"_self\">Decoupling</a></strong></li>\n<li><strong><a href=\"#Adaptation\" target=\"_self\">Code Adaptation</a></strong></li>\n<li><strong><a href=\"#Database\" target=\"_self\">Storage of an Amount on the Database</a></strong></li>\n<li><strong><a href=\"#Interface\" target=\"_self\">Internal Interfaces</a></strong></li>\n<li><strong><a href=\"#Write\" target=\"_self\">Write To List</a></strong></li>\n<li><strong><a href=\"#Type\" target=\"_self\">Strict Type Checks</a></strong></li>\n<li><strong><a href=\"#External\" target=\"_self\">External Interfaces</a></strong></li>\n</ul>\n<li><strong><a href=\"#Settings\" target=\"_self\">System Settings to Activate the Provided Amount Field Length Extension in Customizing﻿</a></strong></li>\n<li><strong><a href=\"#functions\" target=\"_self\">Functions that may use the Provided Amount Field Length Extension </a></strong></li>\n</ul>\n<p><strong>Introduction <a name=\"Intro\" target=\"_blank\"></a>﻿</strong></p>\n<p>SAP S/4HANA can support extended amount fields. We have adapted the appropriate related SAP development entities (domains, data elements, structures, table types, and transparent tables, external and internal interfaces, user interfaces, and so on) accordingly. There are some scenarios where it will be required for you to adapt your code.  In a multi-system landscape, you must decide if and when to activate the provided amount field length extension in customizing, as this will impact how the system communicates compatibly within a system landscape.  To ensure consistent behavior, the provided amount field length extension must be explicitly activated in customizing.  Note that the extended field length in the data dictionary is not influenced by this activation.  Refer to the below in section <a href=\"#Settings\" target=\"_self\">System Settings to Activate the Provided Amount Field Length Extension in Customizing </a>for details.</p>\n<p>This note focuses on what is affected by the amount field length extension and what adjustments are necessary to ensure that the system remains technically and functionally stable.  The different aspects of the amount field length extension in SAP S/4HANA are described in the following sections. <em> </em>Consider the restrictions outlined here and in the notes listed under References.</p>\n<p><strong>Motivation and Scope<a name=\"Scope\" target=\"_blank\"></a>﻿</strong></p>\n<p>The amount field length extension was developed to meet the requirements of banks and financial institutions to post financial documents representing balance sheet information with amounts that exceed what the previous definition in SAP ERP ECC60 and previous S/4HANA releases support. Therefore, we extended the amount fields within the general ledger and Controlling application areas. As part of these changes, we changed data elements of other application components with shared dependencies.</p>\n<p>The fields that were subject to extension were primarily data elements of type CURR with a defined length between 9 and 22, including 2 decimal places.  Additionally, data elements of type DEC, CHAR, and NUMC that were used to store amounts were also extended.</p>\n<p>While this scope was limited, there was a broad usage of the extended data elements across other applications that will not use extended amounts, for example Logistics.  We decoupled the data elements that would be extended by creating copies of the original with the original domain and technical attributes, and replaced them in all data dictionary usages outside of the areas that were planned for extension.   We extended the original data elements by exchanging the existing shorter domains with new longer domains. Conversion routines were created and assigned to the longer domains to limit the maximum value permitted as screen input where the provided amount field length extension is not activated in customizing.</p>\n<p>To maintain the stability of the applications, certain adjustments were required for data dictionary objects, ABAP source code, dependent user interfaces, as well as in both internal and external interfaces.</p>\n<p><strong>See the attached document for a list of application components with extended amounts.</strong></p>\n<p>  <strong>Assumptions</strong></p>\n<ul>\n<li>Data elements of type CURR with more than 2 decimal places are generally not related to currency amounts.</li>\n<li>Any currency amount with a field length of less than 9 digits including 2 decimals was not intended for long amounts in the first place.</li>\n</ul>\n<p>At a high level, there are the below impacts to your objects due to the extended amount field lengths:</p>\n<ol>\n<li>There are code changes that may be necessary to adapt, irrespective of whether you will support the provided amount field length extension or not. This is necessary to avoid runtime errors and syntax errors once you upgrade to S/4HANA 1809 or higher. Refer to notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>, <a href=\"/notes/2610650\" target=\"_blank\">2610650</a> for more information.</li>\n<li>If you choose not to support the provided amount field length extension in your code, although you interact with modules that do support and/or use extended amounts, adjustments may be necessary to ensure that your code is not impacted. Refer to notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>, <a href=\"/notes/2628699\" target=\"_blank\">2628699</a>, <a href=\"/notes/2628704\" target=\"_blank\">2628704</a>, <a href=\"/notes/2628706\" target=\"_blank\">2628706</a>, <a href=\"/notes/2628724\" target=\"_blank\">2628724  </a>for more information.</li>\n<li>If you choose to support the provided amount field length extension, enablement is necessary and adjustments should be made. Note, however, that your enablement is directly dependent on the areas where the provided amount field length extension is supported in SAP S/4HANA 1809 or later.  Refer to notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>, <a href=\"/notes/2610650\" target=\"_blank\">2610650</a>, <a href=\"/notes/2628617\" target=\"_blank\">2628617</a>, <a href=\"/notes/2628641\" target=\"_blank\">2628641</a>, <a href=\"/notes/2628699\" target=\"_blank\">2628699</a>, <a href=\"/notes/2628704\" target=\"_blank\">2628704</a>,<a href=\"/notes/2628706\" target=\"_blank\">2628706 </a><a href=\"/notes/2628714\" target=\"_blank\">2628714</a>, and <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>,</li>\n</ol>\n<p>In the following section, we will provide an introduction to what has been done to enable the extended amounts in S/4HANA, and explain how it affects your code. For more information, see attached notes under section \"References\".</p>\n<p><strong>Consistent Usage in System-Internal Code<a name=\"Consistant\" target=\"_blank\"></a>﻿</strong></p>\n<p><strong>Decoupling<a name=\"Decoupling\" target=\"_blank\"></a>﻿</strong></p>\n<p>The consistent behavior of the SAP system is secured through the use of data dictionary objects, data elements and domains.  The relevant internal fields that are typed to these data elements derive their technical details (for example, data type, length, decimals, etc.) from the domain. Cross-usages of the data elements to be extended existed between the application areas that will use the provided amount field length extension, and the application areas that will not use extended amounts.   To maintain data integrity and compatibility between these areas while preventing any impact on user interfaces, database entries, external interfaces, etc., the cross-usages had to be identified and decoupled.  We created new data elements and introduced them in the data dictionary objects in the areas without the provided amount field length extension.</p>\n<p>For more information, see related SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>.</p>\n<p><strong>Code Adaptation<a name=\"Adaptation\" target=\"_blank\"></a>﻿</strong></p>\n<p>To facilitate the correct handling of the extended amount fields within the ABAP coding, the specific circumstances and requirements of each possible scenario were considered.  Overflow errors could occur if an extended amount is moved into a shorter amount. Syntax errors and report generation errors can be identified though S/4HANA Readiness code scans.</p>\n<p>The decoupling activities together with the amount field length extension created type conflicts due to a mismatch between data elements assigned to each other in one statement. All amount fields referring to an extended data element had to be checked to ensure that these fields were long enough to hold the extended amounts. If the dependent fields were also extended, all usages of these fields had to be analyzed as well. Extending one field typically triggered follow up changes. Overall, the complete data flow had to be analyzed to identify all places where an extended amount is moved into a shorter amount field. Such analysis must also be done in your custom coding.</p>\n<p>The described changes have also been applied to local interface signatures, including function modules, class methods, form routines, BAdIs and so on.  The types and structure parameters of these local calls were also extended wherever an amount field exists.  External interfaces were also adapted. However, a different approach was taken for these remote calls.</p>\n<p>For more information, see related SAP Note <a href=\"/notes/2610650\" target=\"_blank\">2610650</a>.</p>\n<p><strong>Storage of an Amount on the Database<a name=\"Database\" target=\"_blank\"></a>﻿</strong></p>\n<p>By extending the data elements of an amount field, the field length on the database is also extended. Although the maximum length is larger, the manner in which the content is stored has not changed.  There is no difference in how an amount is stored on the database in SAP S/4HANA 1809 compared to previous versions of S/4HANA as well as in the SAP Business Suite.  No data conversions are needed unless an amount is part of concatenated content. Concatenation in the code uses the complete technical length of the field, which is now longer, and is also reflected in the database content.</p>\n<p><strong>Internal Interfaces<a name=\"Interface\" target=\"_blank\"></a>﻿ </strong></p>\n<p>Interface related conflicts will occur if the lengths of actual and formal parameters are no longer the same.  These type conflicts were found in the internal interface calls between different applications as well as in the interface calls within the same application. A variance in the formal and actual parameters will result in a syntax error or a short dump at run time.  Resolving local interface conflicts depends mainly on the direction of the call (are the caller and receiver using extended amounts?) and data flow (importing, exporting, using, changing, or returning parameter) within each call had to be analyzed.  The parameter could either be a structure/table or it could be a single field.  For more information, see related SAP Note <a href=\"/notes/2628699\" target=\"_blank\">2628699</a>.</p>\n<p><strong>Write Statements for List Output<a name=\"Write\" target=\"_blank\"></a>﻿</strong></p>\n<p>When amount fields that are extended are used in WRITE statements for list processing, they may cause the list to go out of alignment and overlapping values independent of whether the provided amount field length extension is active or not.   Data can be truncated or completely lost if no adjustment is made.  Other write statements might need adjustments in case of multi-row output, printing column headings, or splitting the preparation of one row into multiple WRITE statements.  For more information, see related SAP Note <a href=\"/notes/2610650\" target=\"_blank\">2610650</a>.</p>\n<p><strong>Strict Type Checks<a name=\"Type\" target=\"_blank\"></a>﻿ </strong></p>\n<p>There are statements within the ABAP code that require strict type checks where data elements assigned to each other must have the same data type and length.  A syntax error will be issued when the fields within the statement are incompatible.  Conflicts will occur if an extended amount is used in this type of statement within an application area that is not using the provided amount field length extension. There is no implicit type conversion done in this case. Adjustments are necessary to avoid syntax errors. This can be solved with the help of an auxiliary variable or by changing the type to the copied data element.  For more information, see related SAP Note <a href=\"/notes/2610650\" target=\"_blank\">2610650</a>.</p>\n<p><strong>External Interfaces<a name=\"External\" target=\"_blank\"></a>﻿ </strong></p>\n<p>A typical ERP system is connected to miscellaneous internal and external (SAP or non-SAP) systems. Accordingly, an S/4HANA system with the provided amount field length extension activated in customizing must consider that not all related systems will be able to handle extended amounts.  Furthermore, it cannot be assumed that all ERP systems in your landscape will be converted or upgraded to SAP S/4HANA 1809 at the same point in time. That means that the external interfaces that are used for integration must be able to function as in the previous versions of the interface.</p>\n<p>This is especially relevant for the commonly used integration techniques BAPI, RFC, and IDoc as these techniques rely on a fixed length and sequence of fields in the transmitted data. Simply extending the amount fields in these interfaces would technically break how they interact with the inbound and outbound calling programs.</p>\n<p>As mentioned above, we used a different approach for internal and released external APIs.  We have decided to provide a technical-version compatibility for released external interfaces in the way that is commonly used and proposed for BAPI interfaces: The already existing field remains with its original length, and an additional extended field is added at the end of the structure parameter (or as an additional single parameter) allowing for transmision of the extended lengths.  It must be ensured that no extended field can be maintained in the system with a content length greater than the original length of the field.</p>\n<p>It must be ensured that any released external API containing an extended amount field as a single parameter or as part of a structure parameter, can be called internally or locally within one system. Hence, when the provided amount field length extension is activated in customizing, all internal calls of external interfaces must only use the extended fields.</p>\n<p>The changes described were done for BAPIs, IDocs, and released remote-enabled function modules, and where necessary, unreleased remote enabled function modules used to communicate with other SAP Business Suite products like other SAP ERP installations or SAP CRM.</p>\n<p>A complete list of relevant function modules, IDocs, and structures that have been extended in this way can be found in the piece lists in the simplification database in SAP Notes <a href=\"/notes/2628699\" target=\"_blank\">2628699</a>, <a href=\"/notes/2628706\" target=\"_blank\">2628706 </a>and <a href=\"/notes/2628704\" target=\"_blank\">2628704</a>.</p>\n<p>Refer to notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>, <a href=\"/notes/2628724\" target=\"_blank\">2628724 </a>for more information.</p>\n<p><strong>System Settings to Activate the Provided Amount Field Length Extension in Customizing<a name=\"Settings\" target=\"_blank\"></a>﻿﻿</strong></p>\n<p>When converting from the SAP Business Suite System, or upgrading from an older version of SAP S/4HANA, to SAP S/4HANA 1809 or later, the amount field length extension is not activated in customizing by default.</p>\n<p><em><span><strong>Note:</strong> <strong>Once the provided amount field length extension is activated in customizing, it cannot be deactivated.</strong></span></em></p>\n<p>The activation is realized as a client-dependent customizing table that can be accessed in Customizing via the following path:</p>\n<p>Cross-Application Components -&gt; General Application Functions -&gt; Field Length Extension -&gt; Activate Extended Fields</p>\n<p>Set the checkbox for Extended Amount Length and SAVE.</p>\n<p>Alternatively, transaction FLETS.  Authorization group FLE [authorization object S_TABU_DIS] is required for table maintenance.</p>\n<p> </p>\n<p><strong><a name=\"functions\" target=\"_blank\"></a>﻿Below is a list of functions that can be used with the</strong> <strong>amount field length extension:</strong></p>\n<ul>\n<li>Manage Profit Center Groups</li>\n<li>Manage Profit Centers</li>\n<li>Edit Master Data – Profit Centers – Collective</li>\n</ul>\n<p>This process step is to make changes to large quantities of profit center master data without having to change the master data for each single profit center.</p>\n<ul>\n<li>Edit Company Code Assignment Profit Centers</li>\n</ul>\n<p>In this activity, you make changes to the company code assignments of a larger number of profit centers without having to change the master data for each single profit center.</p>\n<ul>\n<li>Display Parked Journal Entries</li>\n</ul>\n<p>You can use document parking to enter and store (park) incomplete documents in the SAP System without carrying out extensive entry checks. Parked documents can be completed, checked, and then posted later or deleted – if necessary by a different accounting clerk. With this app you can display parked documents.</p>\n<ul>\n<li>Parked Journal Entry Changes</li>\n</ul>\n<p>You can use document parking to enter and store (park) incomplete documents in the SAP System without carrying out extensive entry checks. Parked documents can be completed, checked, and then posted later or deleted – if necessary by a different accounting clerk. With this app you can track all changes made in parked journal entries.</p>\n<ul>\n<li>Post General Journal Entries</li>\n</ul>\n<p>You receive G/L account documents that must be posted manually in the SAP system.  When posting to the balance sheet accounts only, you only need to make an entry in the Segment field. If the field is left blank segment 1000_C will be defaulted and will need to be reconciled.</p>\n<ul>\n<li>Upload General Journal Entries</li>\n</ul>\n<p>You receive G/L account documents that must be posted manually in the SAP system.  When posting to the balance sheet accounts only, you only need to make an entry in the Segment field. If the field is left blank segment 1000_C will be defaulted and will need to be reconciled.  The app allows for multiple G/L account documents to be uploaded using a single upload file.</p>\n<ul>\n<li>Verify General Journal Entries</li>\n</ul>\n<p>You use Verify General Journal Entries for Requester to enter a journal entry which needs to be checked before final posting.  You can check the status of the documents submitted for verification in various tabs: All, Submitted, Rejected and Others. For those rejected, you can edit and submit again for approval to Processor.  Documents with status Submitted have not been posted yet. Once approved by the processor, the document is posted automatically and it can be seen in the SAP Fiori App Manage Journal Entries.</p>\n<ul>\n<li>Approve Verify General Journal Entries (for Processor – Inbox)</li>\n</ul>\n<p>You receive G/L account documents that need to be checked before final posting. After checking the documents, you can either approve or reject with a comment/ reason. Once a document has been selected and approved, it disappears from the list. If a document is approved, posting of FI document is automatically triggered.</p>\n<ul>\n<li>Reject Verify General Journal Entries (for Processor – Inbox)</li>\n</ul>\n<p>You receive G/L account documents that need to be checked before final posting. After checking the documents, you can either approve or reject with a comment/ reason. Once a document has been selected and rejected, it disappears from the list and goes back to the requester.</p>\n<ul>\n<li>Edit a Rejected Verify General Journal Entry (Requester)</li>\n</ul>\n<p>The G/L account documents need to be checked before final posting. The document you submitted is rejected and for it to be approved and posted you need to make the changes requested by the approver.</p>\n<ul>\n<li>Manage Journal Entries</li>\n<li>Display G/L Account Balances</li>\n<li>Display G/L Account Line Items – Reporting View</li>\n<li>Display G/L Account Line Items – Posting View</li>\n<li>Manage Recurring Journal Entries</li>\n</ul>\n<p>This functionality supports postings that can periodically be created on a regular basis. Recurring entries are similar to standing orders with banks for debiting rent, contribution payments, or loan repayments directly. The postings are done by the recurring entry program based on the recurring entry documents.  In G/L accounting, recurring entries can be used for example, for periodic posting of deferrals and accruals, if there are fixed amounts to be posted.  The following data of a recurring entry document remains unchanged:</p>\n<ul>\n<ul>\n<ul>\n<li>Posting Key</li>\n<li>Account</li>\n<li>Amount</li>\n</ul>\n</ul>\n</ul>\n<p>You only need to enter the data that changes occasionally in a recurring entry document. This document does not result in transaction figures being updated. The recurring entry program uses this document to create a journal entry.</p>\n<ul>\n<li>Display changes of Recurring Entry</li>\n<li>Clear G/L Accounts – Manual Clearing</li>\n<li>Clear G/L Accounts</li>\n</ul>\n<p> For detailed information, refer to the <strong>J58 Best Practices</strong> page at<span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">  </span><span ar-sa;\"=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"DE\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" segoe=\"\" ui',sans-serif;=\"\"><a href=\"https://rapid.sap.com/bp/#/scopeitems/J58\" target=\"_blank\">https://rapid.sap.com/bp/#/scopeitems/J58</a>.</span></p>\n<div>\n<div>\n<div>\n<p> </p>\n</div>\n</div>\n</div>", "noteVersion": 15}]}, {"note": "2628724", "noteTitle": "2628724 - Amount Field Length Extension: Code Adaptations/Usages of BAPIs and RFCs", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types have been extended. See the SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>for motivation and scope.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<p>Code scans might have reported statements that require adaptations.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, AFLE, 23 digits, Currency, BAPI, RFC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<div></div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Table of Contents</strong></p>\n<p><strong><a href=\"#Intro\" target=\"_self\">Introduction</a></strong></p>\n<p><strong><strong><a href=\"#Approach\" target=\"_self\">BAPI/RFC Adaption approach</a></strong></strong></p>\n<p><strong><strong><strong><a href=\"#MapperClass\" target=\"_self\">Mapper Class</a></strong></strong></strong></p>\n<p><strong><strong><strong><strong><a href=\"#CallingExtBapi\" target=\"_self\">Calling Extended BAPIs and Released RFC Modules Locally in SAP S/4HANA</a></strong></strong></strong></strong></p>\n<p><strong><strong><strong><strong><strong><a href=\"#RemotelyCallsOtherSystems\" target=\"_self\">Remotely calling Function Modules in other Systems from Your SAP S/4HANA Code</a></strong></strong></strong></strong></strong></p>\n<p><strong><strong><strong><strong><strong><strong><a href=\"#RemoteCallS4\" target=\"_self\">Remotely Calling a Function Module Known in the S/4HANA Code Line</a></strong></strong></strong></strong></strong></strong></p>\n<p><strong><strong><strong><strong><strong><strong><a href=\"#RemoteCallnonS4\" target=\"_self\">Remotely Calling a Function Module Unknown in the S/4HANA Code Line</a></strong></strong></strong></strong></strong></strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><a href=\"#QueryingTargetSystem\" target=\"_self\">Querying a Target System</a></strong></strong></strong></strong></strong></strong></strong></p>\n<p><strong><strong><strong><strong><strong><strong><strong><a href=\"#ProvidingRFCWithExtendedField\" target=\"_self\">Providing a Remote-Enabled Function Module with an Extended Field in the Interface</a></strong></strong></strong></strong></strong></strong></strong></p>\n<p><strong><strong><a href=\"#CallRemoteInOPENFI\" target=\"_self\">Calling Own Remote Function Modules in OpenFI/BTE Events</a></strong></strong></p>\n<p><strong><a name=\"Intro\" target=\"_blank\"></a>﻿Introduction</strong></p>\n<p>Amount field extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases. This also applies to your code that references the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040 </a>and SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>. Nevertheless, there are some artifacts that may require manual adjustments.</p>\n<p>Due to the remote function call (RFC) protocol, the change in the amount field length of an already published interface parameter is an incompatible change. To keep the length stable, we kept the existing parameters at their present length and introduced an additional parameter with extended length . The same applies for structured parameters where the existing fields were kept at their present length and new fields of extended  length were added at the end of the structures to ensure downward compatibility. The naming convention for these new fields and parameters is to suffix the existing field- / parameter-name with _LONG. For example for field AMOUNT an additional corresponding field named AMOUNT_LONG will be added. Therefore, we recommend paying some attention to code where BAPIs or released RFC modules are called. For more information refer to the section 'External Interfaces' of the SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>.</p>\n<p>Calls to such (compatibly adjusted) released RFCs and BAPIs are identified by custom code scans which provide a list of code locations which might require adaptations due to changes in the context of amount field length extension. This note describes how usages of affected RFCs and BAPIs need to be analyzed and adjusted. Affected function modules/interfaces/BAPIs and RFCs in this regard are those having parameters (scalar as well as structured or table typed) which contain a field which is typed with one of the affected data elements (see attachment to SAP Notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>). The analysis and adaptation needs to focus on these parameters.</p>\n<p><em>Your code scans will provide a worklist of call locations. Due to the nature of the custom code scans these cannot inspect the type of the call, analyze if relevant parameters are used or verify the correct adaptation of the calls. This is the responsibility of the developer analyzing and adjusting your code.</em></p>\n<p><span><strong><a name=\"Approach\" target=\"_blank\"></a>﻿BAPI/RFC Adaptation approach</strong></span></p>\n<p><strong>BAPI Interfaces</strong></p>\n<p>Generally, parameters and fields for currency amounts used in BAPI interfaces use the domain BAPICURR (Dec 23,4) or domain BAPICUREXT (Dec 28,9). A new domain BAPICUREXT31 (Dec 31,8) and data element BAPICUREXT31 are created to handle long amounts. The new domain BAPICUREXT31 (Dec 31, 8) is used for long amount fields. It has 23 digits before the decimal, so in case of currencies with no decimals (Example: Japanese Yen), the sender can enter amounts up to 23 digits. Note, that BAPICUREXT has 9 decimals, therefore, we only support 8 decimals for long domains.</p>\n<p>For BAPI interfaces, amount representation using the data elements BAPICURR and BAPICUREXT31 is called external representation. For internal representation SAP uses a two-decimal representation of currency amounts. For more information see <a href=\"https://help.sap.com/saphelp_nwpi71/helpdata/en/a5/3ec9ea4ac011d1894e0000e829fbbd/frameset.htm\" target=\"_blank\">SAP Help</a>.</p>\n<p>The position of the decimal point in currency amount fields must be converted correctly. You can use two function modules for this conversion. The function module BAPI_CURRENCY_CONV_TO_EXTERNAL converts currency amounts from SAP internal data formats into external data formats. The function module BAPI_CURRENCY_CONV_TO_INTERNAL converts currency amounts from SAP external data formats into internal data formats. After the extension of amount fields, function modules BAPI_CURRENCY_CONV_TO_INT_31 and BAPI_CURRENCY_CONV_TO_EXT_31 have to be used for fields typed with domain BAPICUREXT31.</p>\n<p>Example:</p>\n<p>DATA: l_amounts TYPE bapi2021_amounts.</p>\n<p> CALL FUNCTION 'BAPI_CURRENCY_CONV_TO_INT_31'          \" AFLE<br/>    EXPORTING<br/>      currency             = l_amounts-paym_curr<br/>      amount_external      = l_amounts-paym_amount_long<br/>      max_number_of_digits = '23'<br/>    IMPORTING<br/>      amount_internal      = p_payrq-pamtf<br/>      return               = l_return.</p>\n<p>There is a limited number of BAPIs that directly use the internal representation. These usages will not be covered in this section but rather the guidance in the section for released RFC modules applies to these cases as well.</p>\n<p>As described earlier to achieve compatibility we kept existing fields and parameters and introduced new fields and parameters of extended length. To ensure consistent processing and to reduce the complexity of handling two fields in numerous places a mapping feature is offered. This is used by the SAP implementations to ensure that the new long field will always be filled and the BAPI implementation can rely on this field only, instead of the previous one. In addition the mapper class ensures, that in system in which the provided amount field length extension is not activated no larger values are accepted by the system than before the extension of the amount fields.</p>\n<p><strong><a name=\"MapperClass\" target=\"_blank\"></a>﻿Mapper Class</strong></p>\n<p>We provide a mapper class 'CL_AFLE_CHK_MAPPER' to support the required code adaptations for BAPIs and RFCs.</p>\n<p><strong>Class</strong> <strong>CL_AFLE_CHK_MAPPER</strong></p>\n<p>This class supports the conversion of data containing the extended amount fields with several public methods. Here we only describe which methods are available and should be called for BAPIs and RFCs . A detailed description can be found in the class documentation.</p>\n<p><strong>BAPI Methods</strong></p>\n<p>You can use the methods BAPI_STRUCT_INBOUND and BAPI_STRUCT_OUTBOUND to adapt the BAPI code if the BAPI interface has structure parameters.</p>\n<p>The inbound method shall be called at the beginning of the function module its purpose, is to populate _LONG values from interface parameters because the internal code will always use the _LONG values.</p>\n<p>The outbound method is called before the function module returns. Its purpose is to populate both fields, _LONG and short (if the amount fits into the short field).</p>\n<p>Both methods utilize an importing parameter IT_FNAMES of type  tt_amt_bapi_fnames to supply information about the inbound interface parameter structure. IT_FNAMES is a table of structure ts_amt_bapi_fnames which contains fields for the short fieldname, the corresponding long fieldname, currency code to be used to convert to internal format and compatible length. The compatible length should be determined based on the mapping of the external representation to the internal field. For example, if DMBTR is the internal target field for the external BAPICURR_D field, then DMBTR’s original length of 13 shall be provided as the compatible length.  <span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" black;=\"\" color:=\"\" de;=\"\" en-us;=\"\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\" verdana',sans-serif;=\"\">The compatible length is required to validate that the input provided would be acceptable for an un-extended system for systems where the provided amount field length extension is not activated. </span>Each record in IT_FNAMES describes a \"pair\" of short and _LONG fields to be mapped.</p>\n<p>BEGIN OF ts_amt_bapi_fnames,</p>\n<p>        short       TYPE fieldname,</p>\n<p>        short_x     TYPE fieldname,</p>\n<p>        long        TYPE fieldname,</p>\n<p>        long_x      TYPE fieldname,</p>\n<p>        comp_length TYPE i,</p>\n<p>        curr        TYPE fieldname, \"reference to currency field</p>\n<p>        curr_code   TYPE waers,  \"explicitly provided currency code</p>\n<p>        curr_iso    TYPE fieldname,</p>\n<p> END OF ts_amt_bapi_fnames .</p>\n<p>The field 'curr' is used to specify the reference of currency field in the same structure (Example: curr = 'PAYM_CURR')</p>\n<p>The field curr_iso is used to specify iso currency code in the same structure (example: curr_iso = 'PAYM_ISO')</p>\n<p>The field curr_code is used to specify an explicit currency code. In some cases, currency code is specified explicitly. (example: curr_code = 'USD', curr_code = t001-waers)</p>\n<p>The order or precedence is: first curr is checked, if not filled, then curr_iso is checked, if not filled then curr_code is checked. If none of them is passed then an error message is raised in case extended amount lengths are not enabled, as checking of the permissible value ranges will not be possible.</p>\n<p>Example of how to build LT_FNAMES parameters:</p>\n<p>BAPI: BAPI_PAYMENTREQUEST_CREATE</p>\n<p>Importing Parameter: AMOUNTS  type BAPI2021_AMOUNTS.</p>\n<p>DATA: lt_fnames TYPE cl_afle_chk_mapper=&gt;ft_amt_bapi_fname.</p>\n<p>lt_fnames = VALUE #(</p>\n<p>                ( short = 'PAYM_AMOUNT' long = 'PAYM_AMOUNT_LONG' comp_length = 13 curr = 'PAYM_CURR' )</p>\n<p>                ( short = 'LC_AMOUNT' long = 'LC_AMOUNT_LONG' comp_length = 13 curr = 'LOC_CURRCY')</p>\n<p>                ( short = 'LC2_AMOUNT' long = 'LC2_AMOUNT_LONG' comp_length = 13 curr = 'LC2_CURR')</p>\n<p>                ( short = 'LC3_AMOUNT' long = 'LC3_AMOUNT_LONG' comp_length = 13 curr = 'LC3_CURR')</p>\n<p>                ( short = 'AMT_DOCCUR' long = 'AMT_DOCCURLONG' comp_length = 13 curr = 'DOC_CURR')</p>\n<p>                ( short = 'LOC_DEDUCT_AMOUNT' long = 'LOC_DEDUCT_AMOUNT_LONG' comp_length = 13 curr = 'LOC_CURRCY')</p>\n<p>                ( short = 'PAY_WITHHTAX_AMOUNT' long = 'PAY_WITHHTAX_AMOUNT_LONG' comp_length = 13</p>\n<p>                  curr = 'PAYM_CURR')</p>\n<p>                ( short = 'LOC_WITHHTAX_AMOUNT' long = 'LOC_WITHHTAX_AMOUNT_LONG' comp_length = 13</p>\n<p>                  curr = 'LOC_CURRCY')</p>\n<p>    ).</p>\n<div>\n<div>\n<div>\n<p>Example of how to find the compatible length (the length of a field <span>before</span> the amount field length extension):</p>\n<p>In include LFPRQF04, where the inbound mapping is performed, the external value paym_amount_long is converted into internal value p_payrq-pamtf.</p>\n<p> CALL FUNCTION 'BAPI_CURRENCY_CONV_TO_INT_31'          \" AFLE<br/>    EXPORTING<br/>      currency             = l_amounts-paym_curr<br/>      amount_external      = l_amounts-paym_amount_long<br/>      max_number_of_digits = '23'<br/>    IMPORTING<br/>      amount_internal      = p_payrq-pamtf<br/>      return               = l_return.</p>\n<p>The length of p_payrq-pamtf before the amount field length extension is the compatible length.</p>\n<p>Another way to find the compatible length is the check the domain:</p>\n<p>Data element- PRQ_AMTFC has a domain 'AFLE13D2O16N_TO_23D2O30N'. In this case 13 (after amount field length extension) is the compatible mode length.</p>\n<p>cl_afle_chk_mapper=&gt;bapi_struct_inbound( EXPORTING  it_fnames    = lt_fnames<br/>                                           IMPORTING  return       = return<br/>                                           CHANGING   cs_amt_ext   = amounts ).</p>\n<p>For table parameters, the methods BAPI_STRUCT_INBOUND and BAPI_STRUCT_OUTBOUND can be called in a loop.</p>\n<p>Example:</p>\n<p> LOOP AT doc_lines ASSIGNING FIELD-SYMBOL(&lt;line&gt;).<br/>    cl_afle_chk_mapper=&gt;bapi_struct_outbound(<br/>                          EXPORTING it_fnames  = lt_fnames<br/>                          IMPORTING return     = DATA(ls_return)<br/>                          CHANGING  cs_amt_ext = &lt;line&gt; ).</p>\n<p>    IF ls_return IS NOT INITIAL.<br/>      IF ls_return-type = 'E'.<br/>        MESSAGE ID  ls_return-id<br/>        TYPE        ls_return-type<br/>        NUMBER      ls_return-number<br/>        WITH        ls_return-message_v1<br/>                    ls_return-message_v2<br/>                    ls_return-message_v3<br/>                    ls_return-message_v4<br/>        RAISING error_creating_idocs.<br/>      ENDIF.<br/>    ENDIF.<br/>  ENDLOOP.</p>\n<p>The method BAPI_SINGLE_OUTBOUND can be called at the end of the BAPI, if there are any exporting/changing/returning scalar parameters. This method, takes the external long amount and fills both the external short value and the external long value of the respective scalar BAPI fields. If the external long value doesn't fit into the short value, then the short value is reset to zero.</p>\n<p>Example:</p>\n<p>BAPI_GL_ACC_GETPERIODBALANCES</p>\n<p>*BALANCE_CARRIED_FORWARD = LM_BALANCE_CARRIED_FORWARD.</p>\n<p><br/>  cl_afle_chk_mapper=&gt;BAPI_SINGLE_OUTBOUND(<br/>    EXPORTING<br/>      IV_EXT_LONG_AMT  = LM_BALANCE_CARRIED_FORWARD<br/>    IMPORTING<br/>      EV_EXT_SHORT_AMT = BALANCE_CARRIED_FORWARD<br/>      EV_EXT_LONG_AMT  = BALANCE_CARRIED_FORWARD_LONG<br/>  ).</p>\n<p><span>All access to the amount fields has to go to the new (*_LONG) field. Only the new fields should be used in the BAPIs.</span></p>\n<p><strong>RFC Methods:</strong></p>\n<p>The methods CONVERT_STRUCT_INBOUND and CONVERT_STRUCT_OUTBOUND can be used to adapt the RFCs code and BAPIs that contain internal currency representation in their signatures. The inbound method is called at the beginning of the function module its purpose, is to populate _LONG values from interface parameters because the internal code will always use the _LONG values. The outbound method is called before the function module returns. Its purpose, is to populate both fields, _LONG and short (if the amount fits into the short field).</p>\n<p>Both methods utilize an importing parameter IT_FNAMES of type tt_amt_fnames, which contains those fields relevant for RFC:</p>\n<p>BEGIN OF ts_amt_fnames,<br/>        short       TYPE fieldname,<br/>        long        TYPE fieldname,<br/>        comp_length TYPE i,<br/>END OF ts_amt_fnames .</p>\n<p>Method CONVERT_STRUCT_INBOUND will be called at the beginning of the RFC to check the content and to make sure that _LONG is filled.</p>\n<p>Example:</p>\n<p>  DATA: lt_fnames TYPE cl_afle_chk_mapper=&gt;tt_amt_fname.</p>\n<p>  lt_fnames = VALUE #( ( short = 'AMT_DIFF' long = 'AMT_DIFF_LONG' comp_length = 13 ) ).</p>\n<p><br/>    CALL METHOD cl_afle_chk_mapper=&gt;convert_struct_inbound(<br/>      EXPORTING<br/>        it_fnames  = lt_fnames<br/>      CHANGING<br/>        cs_amt = is_bam_amd<br/>      EXCEPTIONS<br/>        others = 1 ).<br/>    IF sy-subrc nE 0.<br/>      MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno with sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4 into return-message.<br/>      return-id = sy-msgid.<br/>      return-type = sy-msgty.<br/>      return-number = sy-msgno.<br/>      return-message_v1 = sy-msgv1.<br/>      return-message_v2 = sy-msgv2.<br/>      return-message_v3 = sy-msgv3.<br/>      return-message_v4 = sy-msgv4.<br/>      RETURN.<br/>    ENDIF.</p>\n<p>Method CONVERT_STRUCT_OUTBOUND is called just before any exit point in a function, if there are any exporting/changing/returning structure or table parameters. This method takes the external long amount and fills both the external short value and the external long value of the respective RFC fields. If the external long value does not fit in the short value, then the short value is reset to zero.</p>\n<p>Example:</p>\n<p>FM: BSPL_DATA_GET_XBRL_RFC</p>\n<p> data(lt_fnames) = VALUE cl_afle_chk_mapper=&gt;tt_amt_fname( ( short = 'BALANCE' long = 'BALANCE_LONG' comp_length = 17 ) ).<br/> <br/>     cl_afle_chk_mapper=&gt;convert_struct_outbound(<br/>                                  EXPORTING  it_fnames = lt_fnames<br/>                                  CHANGING   cs_amt    = ls_data_rfc<br/>                                  EXCEPTIONS excp_inv_input = 1<br/>                                             OTHERS         = 2 ).</p>\n</div>\n</div>\n</div>\n<p><strong>BAPI Extensions</strong></p>\n<p>In several BAPIs there are EXTENSIONIN/EXTENSIONOUT parameters that can be used to send or receive additional data in the BAPIs.<br/>Take special care if the local structures used to move data from and to the abstract extension fields contain a field that is extended in length or includes concatenated data containing one of the extended fields. A remote caller using copies of these structures will usually not be aware of the new length, and therefore, the exchanged data will not fit anymore. <strong>﻿</strong></p>\n<p>To avoid this, you should do one of the following:</p>\n<ul>\n<li>Adapt the structure that is used in the extension parameters so that it still uses the original length for any of the extended fields. This is only possible as long as the <span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" black;=\"\" color:=\"\" de;=\"\" en-us;=\"\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\" verdana',sans-serif;=\"\">amount length extension is not activated in customizing</span>. If you use this approach, you shall ensure that in the implementation that handles the extension structures, the correct mapping between the external structure - with the shortened field - and the internal structure - with the extended field - is implemented. For this mapping, you will usually need two different structures. You can use the mapper classes described below to implement a proper mapping.</li>\n<li>Adapt the structure used by the remote system so that it fits to the changed structure in the SAP S/4HANA system.</li>\n</ul>\n<p> </p>\n<p><span><strong><a name=\"CallingExtBapi\" target=\"_blank\"></a>﻿Calling Extended BAPIs and Released RFC Modules Locally in SAP S/4HANA</strong></span></p>\n<p>Although the main purpose of a BAPI or a released RFC module is to serve remote scenarios, they can also be called locally.<br/>Generally, local calls can be identified by syntactic constructs like the following (only showing the most important examples):</p>\n<ul>\n<li>CALL FUNCTION ‘&lt;function name&gt;’  (not specifying the DESTINATION addition)</li>\n<li>CALL FUNCTION … DESTINATION ‘NONE’</li>\n<li>CALL FUNCTION … DESTINATION ‘&lt;destination_name&gt;’ (when being positively sure &lt;destination name&gt; will always refer to a local system)</li>\n</ul>\n<p>In case of doubt about the destination in the last case listed above, the guidelines for remote calls of adjusted functions from S/4HANA are a safe fallback. For details, please refer to the next section. The introduction of the necessary mapping calls will not have any negative functional impact in local scenarios but might degrade performance to some extent.</p>\n<p>When consuming BAPIs and released RFC modules locally, be aware of the following: Structures used in the interfaces of BAPIs and released RFC modules have been extended in a compatible manner if they were affected by the amount field length extensions. This means that these structures now contain two amount fields for the same semantic field: a short version of the field with the original amount field length (for example, a amount field of length 15,2) and a new long version of the field (for example, a amount field of length 23,2). The long versions of the amount fields generally have a “_LONG” suffix. Notice that the short version of the field is relevant in remote communication scenarios where the target system may be a system that is not enabled for the extended version of the field. For more information about compatibility of external interfaces, see SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>.</p>\n<p>Internally, the SAP S/4HANA system works with the extended version of the field. This also means that it is necessary to fill the long version of the field when exporting data in a local BAPI or released RFC module call, and that it is necessary to interpret the extended version of the field when importing data from a local BAPI call or released RFC module call (a local BAPI or released RFC module call is either a call without destination or a call where the destination is an application server of the same system, see above).</p>\n<p>If you use the short version of the field in the standard SAP S/4HANA program logic (that is, not in the context of a remote function call), this is a strong indication for an error. The program only works correctly as long as the provided amount field length extension is not yet activated in customizing since, for example, a long amount cannot be stored in the short version of a field without causing an error. For more information about the activation of the amount field lenght extension in customizing and its consequences, see SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>. Watch out for places where data is moved from and to the short version of an extended field and where an internal table is sorted by the short version of the field, where logical expressions are using the short version of the field. A detailed example with source code is shown in the appendix.</p>\n<p>The overall conclusion is the following:<br/>In SAP S/4HANA, always use the long version of an extended field for your program logic. Then your program is also prepared for the case that the system is operating with the provided field length extension activated in customizing. The short versions of extended fields only have to be filled or evaluated in the context of cross-system calls.</p>\n<p><span><strong><a name=\"RemotelyCallsOtherSystems\" target=\"_blank\"></a>﻿Remotely calling Function Modules in other Systems from Your SAP S/4HANA Code</strong></span></p>\n<p>When a function module that has one of the extended fields in its interface is called in another system, code adaptations may also be required. Generally, the unchanged target systems of the remote function call expect to receive the shorter versions of the fields. The field length extension raises two compatibility issues:</p>\n<ul>\n<li>There is a semantic incompatibility since the target system may not be able to work with a longer field (this is currently the case for all systems besides SAP S/4HANA). If you run integration scenarios with systems that are not enabled for the long versions of the extended fields, you cannot activate the amount field lenght extension in customizing. For more information about the activation of the provided amount field length extension, see SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a></li>\n<li>But even if the provided amount field length extension is not activated in customizing, a technical compatibility issue in the RFC communication between SAP S/4HANA and the target system shall be avoided. For example, the DMBTR domain on the SAP S/4HANA side now has a length of 23,2 whereas, on the target side, the domain is still 13,2. Since the RFC protocol assumes binary compatibility on both the sender and receiver side for each parameter in the function module interface, the sent data gets corrupted on receiver side if the field lengths do not match. Due to this fact, it is important that the data is sent in a compatible format. Additionally, one shall consider the fact that the data retrieved from the remote system is in the original format.</li>\n</ul>\n<p>When adjusting the remote function calls, two cases can be distinguished:</p>\n<ul>\n<li>The remotely called function is also known in the SAP S/4HANA code line. These function modules are used in SAP S/4HANA – SAP S/4HANA integration scenarios or in SAP S/4HANA – SAP ERP integration scenarios.</li>\n<li>The remotely called function is not part of the SAP S/4HANA code line and therefore belongs to other types of integration scenarios, for example SAP S/4HANA - SAP CRM.</li>\n</ul>\n<p>Let us have a closer look at these two cases and their consequences.</p>\n<p><strong><a name=\"RemoteCallS4\" target=\"_blank\"></a>﻿Remotely Calling a Function Module Known in the S/4HANA Code Line</strong></p>\n<p>If the called function is a BAPI or a released RFC module, the structures used in the interface have been extended in a compatible manner as already described in the previous section. This means the structures contains, for each extended field, two versions of the field with different lengths. For purely local calls, the short version of the field is irrelevant, as described in the previous section. But for a remote call, the short version of the field is relevant, since the target system may only be able to process the short version of a field.</p>\n<p>As previously mentioned, internally, SAP S/4HANA only works with the long version of the field. This now has the following consequences for remote function calls: If the amount field length extension is not activated in customizing and data is sent (exported) to the target system, the short versions of the fields shall be sent too. If the amount field length extension is activated, the short versions of the fields can only be sent, if the amount fits into the field. When retrieving (importing) data from the target system, the relevant data may only be found in the short version of the field, since the target system may be a system that is not able to handle long field versions. Therefore, the data shall be moved to the long version of field for further SAP S/4HANA-internal processing of the retrieved data.</p>\n<p>To support the consistent adjustment of remote function calls, you can use the following  “mapper” class that provides methods for data exchange between the short and long versions of the extended fields in remote interfaces.</p>\n<p>The mapper class is called CL_AFLE_CHK_MAPPER. The class contains BAPI_STRUCT_OUTBOUND/CONVERT_STRUCT_OUTBOUND methods that allow the processing of internal tables that have to be sent out to, and BAPI_STRUCT_INBOUND/CONVERT_STRUCT_INBOUND methods which determine long amounts being retrieved from a remote system. The methods of the mapper class already consider the activation of the amount field length extension in customizing.</p>\n<p>Your code shall be adjusted in such a way that the methods of the mapper class are called for the data that is sent just before the remote call, and the methods of the mapper class are called for the data that is retrieved just after the remote call.</p>\n<p>If the remotely called function module is not provided by SAP but by you, you can extend the function module in a compatible way (as described in the section “Providing a Remote-Enabled Function Module with an Extended Field in the Interface”), and afterwards adjust the remote calls as described in this section.<br/>If the remotely called function module is not part of the SAP S/4HANA code line at all, see the next section.</p>\n<p><strong><a name=\"RemoteCallnonS4\" target=\"_blank\"></a>﻿Remotely Calling a Function Module Unknown in the S/4HANA Code Line</strong></p>\n<p>If you are calling a remote function module that has no representation in the SAP S/4HANA code line, the structure types you are using to serve the signature of the remote call most probably do not have a short and a long version of the extended field. Most probably, they only have one version of the field which is long (due to the fact that an SAP data type was used, which was extended via changing the length of the used data type).</p>\n<p>A possible strategy for adjusting the call could be the following: Rename the long field in the structure type by adding the suffix “_SHORT” and replace the type by the short data type SAP provides, for example DMBTR_13_2 for the short version of the amount value. With this strategy, the structure is now binary compatible to the structure the target system expects. Then add a new parameter with the original name at the end of the structure having the long version of the type, for example DMBTR for the long version of the amount field. With this approach, your own “SAP S/4HANA-internal” code still works with the original field name. When using the structure type for sending (exporting) data in the remote call, you call the corresponding mapper class method for populating the short version of the structure just before the remote call is executed. When using the structure type for receiving (importing) data from the remote call, you call the corresponding mapper class method for populating the long version of the structure just after the remote call is executed.</p>\n<p>Another strategy for adjusting the call is to only exchange the data type of the original field with the short version of the type and add a new version of field with the suffix “_LONG” at the end of the structure that has the long version of the type. The difference to the previous approach is that you also shall adjust your own SAP S/4HANA-internal code so that the new field name is always used instead of the original field, since the field with the original name is now too short for internal processing. With this approach, you also use the mapper class methods for data exchange between the short and long version of the field.</p>\n<p><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" black;=\"\" color:=\"\" de;=\"\" en-us;=\"\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\" verdana',sans-serif;=\"\">A third strategy is to leave the structures unadjusted and change the serialization in the RFC destination to the new transfer protocol “New Fast serialization” and set the right compatibility mode for existing scenarios. For details please refer to the <a href=\"https://help.sap.com/viewer/753088fc00704d0a80e7fbd6803c8adb/1709%20000/en-US/489bde7a0c1c73e7e10000000a42189b.html\" target=\"_blank\">documentation</a>.</span></p>\n<p><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" black;=\"\" color:=\"\" de;=\"\" en-us;=\"\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\" verdana',sans-serif;=\"\"><strong>﻿<a name=\"QueryingTargetSystem\" target=\"_blank\"></a>﻿<span>Querying a Target System</span></strong></span></p>\n<p>Under some circumstances it may be necessary to determine whether a remote system is capable of using extended currency amounts. SAP S/4HANA and SAP ERP systems enable this possibility via an RFC named GET_FLE_TOPIC_MODE. You can find out if a function module exists in the target system by calling function module FUNCTION_EXISTS remotely. As a reference have a look into report 'RWWATRAT'. The function module returns one of 3 values:</p>\n<div>\n<ul>\n<li>0 – Not Extended; indicates the system is not enabled to work with extended currency amounts.  Ie. ERP EhP8.</li>\n<li>1 – Compatibility Mode; indicates the system is capable of using extended values but the functionality is not enabled in customizing.</li>\n<li>2 – Target Mode; indicates the system capable of using extended values and has been activated in customizing to do so.</li>\n</ul>\n</div>\n<p>e.g.</p>\n<p>CALL FUNCTION 'GET_FLE_TOPIC_MODE‘ DESTINATION ‘remotesys’<br/>       EXPORTING<br/>         iv_extension_topic = 'AFLE'<br/>       IMPORTING<br/>         ev_mode            = gv_afle_mode.</p>\n<p><span><strong><a name=\"ProvidingRFCWithExtendedField\" target=\"_blank\"></a>﻿Providing a Remote-Enabled Function Module with an Extended Field in the Interface</strong></span></p>\n<p>If you are providing a remote-enabled function module in your code that already contains one of the extended fields in the function module interface, and if you have created your own structure types for the interface, these structure types are changed if they contain one of the extended standard data types. Again, one shall distinguish several cases: If the remote-enabled function module is only used system-internally (for background processing) or used for SAP S/4HANA – SAP S/4HANA integration scenarios, there is no need for further adjustments. But if the remote-enabled function module is potentially called by a system that is not enabled for the extended fields, there is now a compatibility issue.</p>\n<p>If the calling system is a system that is not enabled for extended fields, you could solve the compatibility issue in several ways:</p>\n<ul>\n<li>If the code in the calling system is under your control, you could adjust the remote function call by using a long version of the data type for the RFC execution in the calling system.</li>\n<li>Alternatively, you could reset the type of the field to its short version in the SAP S/4HANA system. For example, the structure contains a field for amount with data type DMBTR. You could change the data type to DMBTR_13_2.  The naming convention for these data elements is originalname_xx_2.</li>\n<li>The third alternative is to extend your data structure in a compatible manner, that is, in the same way SAP has extended the BAPI structures: Assign the short version of the data type to the original field and add a new field having the long version of the data type at the end of the structure. The advantage of this approach is that the interface is compatible for calling systems that are not enabled for the long version of the field, and the interface is compatible for calling systems that are enabled for the long version of the field. This approach can even handle a mixed system landscape where both, systems that are and systems that are not enabled for extended fields are calling your remote-enabled function module. With this approach, you can also use the mapper class methods for converting data that is imported into your remote-enabled function module (just at the beginning of the function module code) and for converting data that you export from your remote-enabled function module (at the very end of the function module code).</li>\n<li>Another alternative is to clarify the possibility to use the new length different tolerant RFC serialization. Refer to <a href=\"https://help.sap.com/viewer/753088fc00704d0a80e7fbd6803c8adb/1709%20000/en-US/489bde7a0c1c73e7e10000000a42189b.html\" target=\"_blank\">SAP Help</a> which will provide a possibility of having different lengths defined on RFC client and server side, as long as the values to be transferred are fitting into the shorter of the two lengths. This provides the additional benefit of potential higher performance and the detection of overflows in the mapping of the data between client and server structures.</li>\n</ul>\n<p><span><strong><a name=\"CallRemoteInOPENFI\" target=\"_blank\"></a>﻿Calling Own Remote Function Modules in OpenFI/BTE Events</strong></span></p>\n<p>In general, the interface parameters of OpenFI/BTE events have not been changed in a compatible manner. The fields have merely been extended, just as they have for any other local function module. The assumption is that most functionality implemented in such an event is local functionality.</p>\n<p>If you have registered a function module to be called remotely by such an event, it may therefore be the case that the interface used in the remote call is not compatible because the interface is taken from the interface of the event. If it is not possible to adapt the remote function module in the remote system, it may be necessary to wrap the remote call with a local function module that accepts the extended parameters and maps to locally defined parameters understood by the remote function module.</p>\n<p> </p>", "noteVersion": 9}, {"note": "2628704", "noteTitle": "2628704 - Amount Field Length Extension: Code Adaptations for Usages of Adjusted Interfaces", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types have been extended. See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>for motivation and scope.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<p>Code scans might have reported statements that require adaptations.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, AFLE, 23 digits, Currency, BAPI, RFC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Introduction</strong></p>\n<p>Amount field extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases. This also applies to your code that references the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040</a> and SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>. Nevertheless, there are some artifacts that may require manual adjustments.</p>\n<p>Due to the remote function call (RFC) protocol, the change in the field length of an already published interface parameter is an incompatible change. To keep the length stable, we have introduced an additional parameter with an appropriate length. Therefore, we recommend paying some attention to coding where BAPIs or released RFC modules are called. See SAP Note <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>.</p>\n<p>Calls to such (compatibly adjusted) interfaces are identified by custom code scans which provide a list of code locations which might require adaptations due to changes in the context of amount field length extension. This note describes how usages of affected interfaces need to be analyzed and adjusted. Affected function modules/interfaces/BAPIs and RFCs in this regard are those having parameters (scalar as well as structured or table typed) which contain a field which is typed with one of the affected data elements (see attachment to SAP Notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>). The analysis and adaptation needs to focus on these parameters.</p>\n<p><em>Your code scans will provide a worklist of call locations. Due to the nature of the custom code scans these cannot inspect the type of the call, analyze if relevant parameters are used or verify the correct adaptation of the calls. This is the responsibility of the developer analyzing and adjusting your code.</em></p>\n<p><strong>Adaptation approach</strong></p>\n<p>1. Check for the found interfaces in the attached file, which parameter or parameters we adjusted compatibly. We introduced long fields in case of structured or table typed parameters or additional parameters in case of scalar ones.  In case the respective call involves one or multiple such parameters it needs to be adapted.</p>\n<p>2. The necessary adaptation steps depend on the type of call. Here it is important to distinguish local calls (without destination specified or with a destination that addresses a local system, as 'NONE' for example) and remote calls (with a destination specified which does not address the/a local system):</p>\n<ul>\n<li>For local calls, the necessary adjustments are described in SAP Note <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>,  section \"Calling Extended BAPIs and Released RFC Modules Locally in SAP S/4HANA\".</li>\n<li>For remote calls, the necessary adjustments are described in SAP Note <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>,  section \"Remotely Calling a Function Module Known in the S/4HANA Code Line\".</li>\n</ul>\n<div></div>", "noteVersion": 8}, {"note": "2628641", "noteTitle": "2628641 - Amount Field Length Extension: IDoc Interface/ALE Adaptations", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types have been extended. See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>for motivation and scope.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, Classification, AFLE, IDoc</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Table of Contents</strong></p>\n<ul>\n<li><strong><a href=\"#Intro\" target=\"_self\">Introduction</a></strong></li>\n<li><strong><strong><a href=\"#IDOCINTERFACE\" target=\"_self\">IDoc Interface/ALE</a></strong></strong></li>\n<li><strong><strong><a href=\"#InboundIdocs\" target=\"_self\">Error Handling for Inbound IDocs</a></strong></strong></li>\n<li><strong><strong><strong><a href=\"#CompatibilityClass\" target=\"_self\">Sending IDocs from SAP S/4HANA</a></strong></strong></strong></li>\n</ul>\n<p><strong><strong><strong><a name=\"Intro\" target=\"_blank\"></a>﻿Introduction</strong></strong></strong></p>\n<p>Amount field extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases. This also applies to your code that references the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040</a> and SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>. Nevertheless, there are some artifacts which may require manual adjustments.</p>\n<p><strong><a name=\"IDOCINTERFACE\" target=\"_blank\"></a>﻿IDoc Interface/ALE</strong></p>\n<p>The <a href=\"https://help.sap.com/saphelp_nw70/helpdata/en/72/c18ee5546a11d182cc0000e829fbfe/frameset.htm\" target=\"_blank\">IDoc</a> interface exchanges business data between SAP and/or an external system. The IDoc interface consists of a data structure definition, along with processing logic for this data structure. The IDoc is the exchange format that is common to all the communicating systems. For data exchange, an application module creates a master IDoc. This formatted IDoc is then passed to the communication layer and from there sent to the system that was called (server) via a transactional remote function call (RFC) or via file interfaces (for example, EDI).</p>\n<p>An IDoc type is defined through its permitted segments. Segments can be dependent on each other (parent and child segments). A segment definition is the release-specific name of a segment.</p>\n<p>It is not permitted to change the field length of an already released segment definition. Changing the length would be an incompatible change. Where data elements that were extended as part of amount field length extension were used, extended data elements have been introduced that keep the signature of the external IDoc interfaces stable. The naming convention for these data elements is <em>originalname</em>_XX_2 (where XX stands for the original length).  See SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040 </a>section - \"Decoupling of Extended Data Elements in Released Interfaces\"<strong>.</strong></p>\n<p>You might get errors when trying to edit the segment definition. In case the segment definition references one of the data elements that have been extended in length. If errors are relating to length mismatches between the fields' external length and the application data element it is required to repair the existing segment definition. This is done by replacing the used data element with one which is kept at the previous length. For this replacement, use the report RSEREPA5 to replace the field's data element with a data element of the previous length.  See SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040 </a>section - \"Decoupling of Extended Data Elements in Released Interfaces\"<strong>.</strong></p>\n<p>This change only applies to IDoc segments that have not been generated by tools like BDBG or BDFG. The length of amount fields in IDoc segments will not be changed. Inbound function modules should not exhibit any errors since the short values (input) can be accommodated by the amount field length extension within the S4/Hana codebase.</p>\n<p>If for BDBG generated IDocs where the interface is generated from a BAPI interface, long fields have been introduced, all necessary mapping for BDBG is also generated.</p>\n<p><a name=\"InboundIdocs\" target=\"_blank\"></a>﻿<strong>Error Handling for Inbound IDocs</strong></p>\n<p>To prevent extended values from entering via Inbound IDocs segments we have replaced extended elements with data elements of the previous length. However, sometimes type CHAR data elements in EDI segments have been used as amount fields. These fields can hold extended amounts.</p>\n<p>This error check detects when an extended value is present in a char field used for a currency amount in an EDI segment. The check should be performed after the value has been converted to SAP internal currency format (if not submitted in internal format).  An error should be thrown when the value of the inbound field is greater than the maximum value that would have been possible to store if the field in the internal ABAP structure would not have been extended.</p>\n<p>The utility class CL_AFLE_MAX_MIN contains a method that will return the maximum value that may be assigned to the original short amount value of the ABAP internal structure.  A method exits in CL_AFLE_MAX_MIN for all possible lengths of the original ABAP internal structure field.  You can find the original length of the field by examining its domain.  See SAP Note <a href=\"/notes/2628724\" target=\"_blank\">2628724 </a>section <strong>Mapper Class.</strong></p>\n<p>Example:</p>\n<p>    PERFORM edi_convert_amount_p       \"Sum 5<br/>             USING e1idpu5-moabetr idoc_febpi-waers<br/>                   idoc_febpi-oth01.<br/>     IF abs( idoc_febpi-oth01 ) &gt; cl_afle_max_min=&gt;get_max_val_13_2( ).<br/>       MESSAGE e117(fv) WITH e1idpu5-moabetr.<br/>     ENDIF.</p>\n<p>In the example above if field idoc_febpi-oth01 exceeds the maximum value that could have been stored in a CURR 13,2 field an error is generated.</p>\n<p><strong><a name=\"CompatibilityClass\" target=\"_blank\"></a>﻿Sending IDocs from SAP S/4HANA</strong></p>\n<p>In SAP IDocs, which are not generated, there exists the possibility that in the outbound function module an extended amount field, which is used internally, is moved into an unchanged original field in the IDoc segment causing an overflow error. SAP Note <a href=\"/notes/2628617\" target=\"_blank\">2628617</a>.</p>\n<p>For this reason, a check has been added to outbound function modules that populates IDoc segments. The check ensures that no transport of values too large for unchanged IDoc structures is attempted in systems where amount field length extension has been activated in customizing.</p>\n<p><strong>Compatibility Check Class</strong></p>\n<p>To adapt outbound IDoc function modules, use the method CHECK_OVERFLOW of class CL_AFLE_COMPATIBILITY_CHECK. The system performs this check before an amount is moved from an internal structure to the IDoc segment. The move is typically done with a MOVE_CORRESPONDING statement.</p>\n<p>The structure  tt_amt_fname of CL_AFLE_COMPATIBILITY_CHECK is passed into the check method. It contains a list of fields to be checked each for the field’s associated maximum compatible length (the previous length of the internal field).</p>\n<p> types:<br/>    BEGIN OF ts_amt_fnames,<br/>        int_field   TYPE fieldname,     \"field to check<br/>        comp_length TYPE i,             \"original length<br/>      END OF ts_amt_fnames .</p>\n<p><br/>    types:<br/>    tt_amt_fname TYPE STANDARD TABLE OF ts_amt_fnames WITH EMPTY KEY .</p>\n<p>A typical implementation of this check is if the following fields of PYMT_DATA are used internally in a component targeted for extension:</p>\n<p>      Format: <em>field</em> : <em>data element  domain;</em></p>\n<p>structure pymt_data {<br/> mandt : mandt;</p>\n<p>...</p>\n<p>pamtl : prqamtlc17 AFLE_C17_TO_C30; AFLE_C17_TO_C30 is the domain<br/> pamtf : prqamtfc17 AFLE_C17_TO_C30;<br/> ...</p>\n<p>rskon : rskon17  AFLE_C17_TO_C30;<br/> rwskt : rwskt17  AFLE_C17_TO_C30 ;</p>\n<p>}</p>\n<p>PYMT_DATA is used to populate an IDOC structure the corresponding field of which remain at the original length.</p>\n<p>We populate tt_amt_fname with a row for each field to be checked and call method chk_struct_comp_len_in_target of class cl_afle_compatibility_check.</p>\n<p>Example:</p>\n<p>DATA: lt_fnames TYPE cl_afle_compatibility_check=&gt;tt_amt_fname.</p>\n<div>\n<div>\n<p> </p>\n<p>     lt_fnames = VALUE #(  ( int_field = 'PAMTL' comp_length = 17 )</p>\n<p>                          ( int_field = 'PAMTF' comp_length = 17 )</p>\n<p>                          ( int_field = 'RSKON' comp_length = 17 )</p>\n<p>                          ( int_field = 'RWSKT' comp_length = 17 )</p>\n<p>                    ).</p>\n<p>    CALL METHOD cl_afle_compatibility_check=&gt;chk_struct_comp_len_in_target</p>\n<p>      EXPORTING</p>\n<p>        it_fnames   = lt_fnames</p>\n<p>        is_amt      = PYMT_DATA</p>\n<p>      EXCEPTIONS</p>\n<p>        ex_overflow = 1</p>\n<p>        OTHERS      = 2.</p>\n<p>    IF sy-subrc &lt;&gt; 0.</p>\n<p>      MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno.</p>\n<p>    ENDIF.</p>\n<p>  MOVE-CORRESPONDING PYMT_DATA TO E1FIPRH.</p>\n<p>In this example an error is thrown, if a value is detected that would cause a loss of data as a truncated and invalid value is sent in the IDoc.</p>\n<div></div>\n</div>\n<div><span>    </span></div>\n</div>", "noteVersion": 12}, {"note": "2628706", "noteTitle": "2628706 - Amount Field Length Extension: Code Adaptations for Usages of RFC Enabled Function Modules", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types have been extended. See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>for motivation and scope.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<p>Code scans might have reported statements that require adaptations.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, AFLE, IDoc, ALE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Introduction</strong></p>\n<p>Amount field extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases. This also applies to your code that references the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040</a> and SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>. Nevertheless, there are some artifacts which may require manual adjustments. <a href=\"file:///Z:/20_Central%20Team%20NSQ/Simplification/2218350%20-%20Amount%20Field%20Length%20Extension%20-%20IDoc%20InterfaceALE.docx#_msocom_1\" target=\"_blank\"><br/></a></p>\n<p>Due to the remote function call (RFC) protocol, the change in the field length of an already published interface parameter is an incompatible change. RFC enabled function modules, which were not released by us were affected by the exchange of the domains so they technically became incompatible for the RFC protocol. However, most usages of these modules were system internal for parallelization purposes or to allow for asynchronous processing. In these scenarios the incompatible change was without impact as the involved type definitions used for the parameters are adjusted on the calling as well as on the called side.</p>\n<p>Calls to such (incompatibly changed) RFC enabled function modules are identified by custom code scans which provide a list of code locations which might require adaptations due to changes in the context of amount field length extension. This note describes how usages of affected RFC enabled function modules need to be analyzed and adjusted. Affected function modules/interfaces/BAPIs and RFCs in this regard are those having parameters (scalar as well as structured or table typed) which contain a field which is typed with one of the affected data elements (see attachment to SAP Notes <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>). The analysis and adaptation needs to focus on these parameters.</p>\n<p><em>Your code scans will provide a worklist of call locations. Due to the nature of the custom code scans these cannot inspect the type of the call, analyze if relevant parameters are used or verify the correct adaptation of the calls. This is the responsibility of the developer analyzing and adjusting your code.</em></p>\n<div></div>\n<p><strong>Adaptation approach</strong></p>\n<p>The necessary adaptation steps depend on the type of call. Here it is important to distinguish local calls (i.e. without destination specified or with a destination that addresses the local system, as 'NONE' for example) and remote calls (i.e. with a destination specified which does not address the local system):</p>\n<ul>\n<li>For local calls, no changes to the custom code are necessary, assuming the call uses the proper datatypes typed with the correct domains for the parameters.</li>\n<li>Remote calls will be prevented by the ABAP system due to possible incompatibilities mentioned above which could go undetected on the called system and create corrupted data. In this case we recommend checking, if there is a corresponding released function module which you can use. The approach for calling such released modules is described in SAP Note <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>.  In case there is no corresponding released function module available it is now recommended to perform remote calls from/to S4/HANA systems using <a href=\"https://help.sap.com/viewer/753088fc00704d0a80e7fbd6803c8adb/1709.001/en-US/489bde7a0c1c73e7e10000000a42189b.html\" target=\"_blank\">New Fast Serialization</a>, it provides improved compatibility checking and may allow blacklisted RFCs to be called successfully.  Finally, get in touch with us if no solution can be found.</li>\n</ul>\n<div></div>\n<div>\n<div></div>\n<div></div>\n</div>\n<p> </p>", "noteVersion": 14}, {"note": "2610650", "noteTitle": "2610650 - Amount Field Length Extension: Code Adaptations", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types have been extended. See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>for motivation and scope.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, AFLE, 23 digits, Currency</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Introduction</strong></p>\n<p>Amount field extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases. This also applies to your code that references the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040 </a>and SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>. Nevertheless, there are some artifacts that may require manual adjustments.</p>\n<p>The amount field length extension in specific parts of SAP S/4HANA 1809 as well as the changes required to prevent field length extensions in parts that were not subject to extension have effects that require adaptations. These adaptations generally come in three categories:</p>\n<ul>\n<li><span 'times=\"\" 107%;=\"\" arial',sans-serif;=\"\" bold;\"=\"\" en-us;=\"\" he;=\"\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-font-weight:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\" zh-cn;=\"\">Changes that must be done regardless of the intention to support amount field length extension in the affected code or not, for example syntax errors or runtime errors would occur due to type mismatches, data being <span>corrupted </span>due to mismatches in memory layout an so on. Note that the details of the change still might depend on the intention to support the provided amount field length extension or not.</span></li>\n<li><span 'times=\"\" 107%;=\"\" arial',sans-serif;=\"\" bold;\"=\"\" en-us;=\"\" he;=\"\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-font-weight:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\" zh-cn;=\"\">Changes that should be done to ensure the field length extension does not have impact on areas that should not support the provided amount field length extension, for example ensuring the layout of ABAP lists is unaffected etc.</span></li>\n<li><span 'times=\"\" 107%;=\"\" arial',sans-serif;=\"\" bold;\"=\"\" en-us;=\"\" he;=\"\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-font-weight:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\" zh-cn;=\"\">Changes that should be done to ensure extended amount values can be processed in areas that shall support amount field length extension without problems, for example preventing overflow related dumps, ensuing UI layouts are consistent, preventing incorrect data caused by rounding issues and so on.</span></li>\n</ul>\n<p>In the following different constructs and use-cases are described that require adaptations.</p>\n<p>Note that adjustments of some constructs mentioned below may lead to a need for subsequent adjustments. Therefore, it might be necessary to re-run the checks iteratively to ensure completeness of adjustments.</p>\n<p><span><strong>Type conflicts in calls to modularization units</strong></span></p>\n<p>Due to the provided amount field length extension there could be type mismatches in calls to modularization units (CALL METHOD, PERFORM, CALL FUNCTION) where the lengths of actual and formal parameters differ. This will cause run time errors or syntax errors respectively, independent from the actual data used in the call. So, these usages need to be adapted in any case. This applies to scalar as well as structured and table parameters. Generally the adaptations for such type conflicts can be different, depending on the specific requirements (supporting amount field length extension or not). If amount field length extension shall not be supported it might be advisable to change the typing of actual or formal parameter (depending on which of the two uses the extended length SAP data element) to a different data element with the required length (for example using the data elements following the _CS naming convention). See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>and SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>.</p>\n<p>Example:</p>\n<p><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">PERFORM </span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">convert_amount_to_external<br/>   </span><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">USING </span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">p_credits_int</span><span background:=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" gray;=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">-</span><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">currency</span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\"><br/>         p_credits_int</span><span background:=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" gray;=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">-</span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">total_amount<br/>     </span><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">CHANGING</span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\"><br/>        c_credits</span><span background:=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" gray;=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">-</span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">total_amount</span><span background:=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" purple;=\"\" white;=\"\">.</span> <span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">p_credits_int</span><span background:=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" gray;=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">-</span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">total_amount is of type WRBTR_CS.</span></p>\n<p><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">FORM </span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">convert_amount_to_external  </span><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">USING    </span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">p_currency   </span><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">TYPE </span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">waers<br/>                                         p_amount_int </span><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">TYPE </span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">wrbtr_cs<br/>                                 </span><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">CHANGING</span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\"><br/>                                         p_amount_ext </span><span background:=\"\" blue;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">TYPE </span><span background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" white;=\"\">bapicurr_d</span><span background:=\"\" calibri;=\"\" color:=\"\" courier=\"\" font-weight:=\"\" minor-latin;\"=\"\" mso-fareast-font-family:=\"\" mso-fareast-theme-font:=\"\" new';=\"\" normal;=\"\" purple;=\"\" white;=\"\">.</span></p>\n<p>In this case the type of 'p_amount_int' can be changed from 'wrbtr' to 'wrbtr_cs', so that the types match.</p>\n<p>For adapting the types, especially when calling SAP function modules or methods, the introduction of helper variables (scalar or structured) might be advisable in case the typing of the underlying structure, data-element etc. can or shall not be changed. In some cases also the <a href=\"https://help.sap.com/http.svc/rc/abapdocu_750_index_htm/7.50/en-US/abenconv_constructor_inference.htm\" target=\"_blank\">CONV#() </a>operator can be used to adapt the actual to the formal parameter, where syntactically supported. Please note, that both the introduction of helper variables as well as usage of the CONV #( ) operator will only be possible if there is no requirement to support the provided amount field length extension, as otherwise overflow errors will occur in these places at runtime when processing longer amounts.</p>\n<p><strong>Type conflicts in OPEN SQL statements</strong></p>\n<p>In OPEN SQL statements type conflicts between the database table and the work area / table parameter can occur. <br/>For SELECT statements the same changes as described for the MOVE statement applies. So, there might be overflow errors when selecting longer amount values from the database than the value range in the target variable can support. <br/>For INSERT/UPDATE/MODIFY statements the type conflicts between the table and the parameter will manifest as syntax errors, that is the type mismatch needs to be solved, independent of the intention to support the amount field length extension or not. In this regard, these statements need to be treated similar to type mismatches in calls to modularization units.</p>\n<p><strong>Type conflicts in iterations / internal table accesses</strong></p>\n<p>In statements like ADD … THEN … UNTIL, DO … VARYING as well as internal table accesses like LOOP … INTO / ASSIGNING or READ … INTO/ASSIGNING type conflicts between the involved fields that are iterated over in case of ADD .. THEN … UNTIL and DO … VARYING might occur. Respectively in case of LOOP and READ statements, the table row type and the work areas type mismatches between the table’s row type and the work area will lead to syntax errors, similar to type mismatches in the calls to modularization units. The code adaptations are necessary regardless of the intention to support the amount field length extension or not.</p>\n<p><strong>Assignments / MOVE statement</strong></p>\n<p>Assignments within numeric types lead to overflows in case an extended amount is moved to a too small target variable, regardless if the involved variables are scalar or structured. In case extended amount lengths shall be supported the conflicts need to be solved. <br/>Assignments with character like target types can lead to truncations and need careful checking to ensure no problems occur due to the amount field length extension.</p>\n<p><span><strong>MOVE-CORRESPONDING Statements</strong></span></p>\n<p>In case of MOVE-CORRESPONDING statements, data is copied from source structure to a target structure using name. There can be cases when the source structure contains an amount field that was extended and the corresponding field in the target structure is not extended. In such cases overflows might occur at runtime in case the provided amount field length extension is used.</p>\n<p>In this case the typing of the involved structures shall be checked and adjusted if required, similarly to the handling for assignments / MOVE statements.</p>\n<p>In some cases, the involved structures are used as formal or actual parameter for call to external interfaces that were kept stable by adding corresponding fields with extended length. In these cases the CORRESPONING #(…) operator can be used, with the … EXCEPT… MAPPING … addition to ensure the correct, extended length field is supplied. Another option is to store the too large values in a temporary variable and clear the respective field in the source structure and moving the value stored in the temporary variable into the correct target field.</p>\n<p><span><strong>WRITE statements for LIST OUTPUT</strong></span></p>\n<p>A WRITE statement formats the content of the data object and writes it to the current page of the current list in the list buffer. This is either a screen list in the list buffer or a spool list. The layout is on the one hand influenced by the concrete write statement, on the other hand the output is also influenced by the output length that is defined on the domain of the respective data elements. The output lengths were extended along with the actual lengths of the data elements; hence list output is affected by the extended amount field length. When amount fields that are extended are used in WRITE statements for list processing, they may cause the list to go out of alignment and overlapping values independent of whether the provided amount field length extension has been activated in customizing or not.</p>\n<p>Example:<br/>DATA : lv_amt TYPE DMBTR, ….<br/>WRITE :      40 lv_amt,<br/>                  57 lv_curr.</p>\n<p>Before the change data element DMBTR had an output length of 16 characters, so the offset 57 was exactly one character after the last digit of the contents of lv_amount. After the amount field length extension, the list will not be displayed correctly, since the output length of DMBTR was changed to 30 characters due to the extension.</p>\n<p>In case the amount field length extension shall not be supported in the respective report, one option is to explicitly specify the output length in the WRITE statement using the (&lt;length&gt;) syntax. Another option is to use the CONV operator and convert the value to a data element which was not extended in length (for example one of the ones following the _CS naming convention). See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a> and SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>.</p>\n<p>Example:<br/>Therefore, the statement needs to be adjusted as below to include the original output length (16 in the example below) in case the provided amount field length extension is not needed.</p>\n<p>WRITE :      40(16) lv_amt,<br/>                  57 lv_curr.</p>\n<p>In case the amount field length extension is needed, the statement needs to be adjusted differently to shift the subsequent fields so that the current target length (30 in the example below) of the extended amount field is considered.</p>\n<p>WRITE :      40 lv_amt,<br/>                  71 lv_curr.</p>\n<p>Please note that also other write statements might need adjustments in case of multi-row output, printing column headings, or splitting the preparation of one row into multiple WRITE statements.</p>\n<p><strong><span>Write to</span></strong></p>\n<p>The statement <strong>WRITE TO</strong> is primarily designed for formatting data for output purposes other than direct writing to ABAP lists.</p>\n<p>Example:</p>\n<p> Data lv_dmbtr(17) type C.</p>\n<p> write i_dmbtr currency i_document_currency to lv_dmbtr</p>\n<p>In the above case, lv_dmbtr is not big enough to accommodate i_dmbtr which has the extended amount length.</p>\n<p>This can be changed as shown below:</p>\n<p>Data lv_dmbtr(25) type C</p>\n<p><strong><span>Floating Point Arithmetic Calculations</span></strong></p>\n<p>In case of arithmetic calculations involving long amount fields rounding errors might occur, if one of the involved operands is of type F (and also DECFLOAT16) or if the exponentiation operator ** is used. Details on the calculation type can be found in this <a href=\"https://help.sap.com/doc/abapdocu_751_index_htm/7.51/en-US/abennumber_types.htm#@@ITOC@@ABENNUMBER_TYPES_3\" target=\"_blank\">link</a>. Calculations in floating point type are limited to  about 15 digits precision for the mantissa, in type DECFLOAT16 to 16 digits. For results as well as intermediate results of the calculation which exceed that number of digits rounding will take place, there won’t be any overflows.</p>\n<p>This means that code that shall support the provided amount field length extension needs to be checked carefully for floating point arithmetic being used and shall be adapted accordingly.</p>\n<p>Example:</p>\n<p>DATA lv_dmbtr   TYPE dmbtr VALUE '123456789012345678901.23'.</p>\n<p>DATA lv_factor   TYPE f     VALUE '1.0'.</p>\n<p>DATA lv_result   TYPE dmbtr.</p>\n<p>DATA lv_result_2 TYPE dmbtr.</p>\n<p>lv_result  = lv_dmbtr * lv_factor.</p>\n<p>lv_result2 = lv_dmbtr  * 10**(2-2).</p>\n<p>The value of lv_result as well as lv_result2 after the calculation is \"123456789012345680000.00\" rather than the to be expected \"123456789012345678901.23\".</p>\n<p>In this case, the calculation type can be changed to DECFLOAT34.</p>\n<p>DATA lv_factor   TYPE DECFLOAT34     VALUE '1.0'.</p>\n<p>In case of the rounding due to exponentiation operator, DECFLOAT34 could be used to avoid the rounding issue as shown below.</p>\n<p>lv_result2 = lv_dmbtr  * CONV DECLOAFT34( 10**(2-2) ).</p>\n<p><strong><span>Arithmetic Error handling</span></strong></p>\n<p>For code that shall not support the amount field length extension as described in the previous sections overflow errors are of concern in case too long values are encountered. Counter intuitively, the absence of overflow exceptions after the amount field length extension. Arithmetic overflows that are catchable at runtime are caught using the exception group ARITHMETIC_ERRORS. However, with amount fields extended, the exception may no longer occur as the involved variables might be longer than before. Therefore, the system behavior will be different in comparison to the one for non-extended amount fields and the error handling needs to be adjusted in some cases to also check the previous maximum value.</p>\n<p>For example:</p>\n<p>DATA lv_amt TYPE P.</p>\n<p>DATA lv_debit_amt type DMBTR.</p>\n<p>CATCH SYSTEM-EXCEPTIONS arithmetic_errors = 4.</p>\n<p>lv_debit_amount = lv_amt.</p>\n<p>ENDCATCH.</p>\n<p>IF sy-subrc &lt;&gt; 0.</p>\n<p>…</p>\n<p>ENDIF.</p>\n<p>The above example works when DMBTR was not extended. However, after DMBTR is extended to 23 digits including 2 decimals, SY-SUBRC check will never be true.</p>\n<p>The following changes were necessary to solve this:</p>\n<p>IF sy-subrc &lt;&gt; 0 OR</p>\n<p>         ( abs( lv_debit_amount ) &gt; cl_afle_max_min=&gt;get_max_val_13_2( ) ).</p>\n<p>…</p>\n<p>ENDIF.</p>\n<p> </p>\n<p><strong><span>Constants /Bound Checks</span></strong>:</p>\n<p>Hardcoded maximum and minimum amount fields are part of the application logic, and are used throughout the system (example '999999999999999', ‘-999999999999999‘).  The primary purpose for these maximum and minimum “constants” is to prevent field overflow errors when a calculation assigned to an amount field exceeds the permitted field length.</p>\n<p>After the amount field length extension, the existing hardcoded values will be too short for the permissible extended maximum amount.</p>\n<p>If you want to support amount field length extension, then perform the changes as described below.</p>\n<p>In case you activate the provided amount field length extension, boundary values for extended amounts can be derived using methods of the class CL_AFLE_MAX_MIN which will return the right value based on customizing. If the program is non-fixed point the methods with suffix ‘_NO_FP’ have to be used. Refer to the <a href=\"https://help.sap.com/saphelp_47x200/helpdata/en/fc/eb333d358411d1829f0000e829fbfe/frameset.htm\" target=\"_blank\">SAP Help</a> for more information</p>\n<p>To derive the constant value after amount field length extension, methods of the class CL_AFLE_MAX_MIN  can be used. If the program is non fixed point the methods with suffix ‘_NO_FP’ have to be used. Refer to the <a href=\"https://help.sap.com/saphelp_47x200/helpdata/en/fc/eb333d358411d1829f0000e829fbfe/frameset.htm\" target=\"_blank\">SAP Help</a> for more information.</p>\n<p>If the program is fixed point methods GET_MAX_VAL_XX_2 have to be used.</p>\n<p>Example:</p>\n<p> DATA:<br/>    <br/>    MAX_BTR_B TYPE DMBTR VALUE '99999999999.99'.</p>\n<p>After the provided amount field length extension was activated in customizing, the above statement will be as shown below:</p>\n<p>Data: MAX_BTR_B TYPE DMBTR.</p>\n<p> MAX_BTR_B  = CL_AFLE_MAX_MIN=&gt;GET_MAX_VAL_13_2( ).</p>\n<p>If you do not want to support the provided amount field length extension, no changes need to be performed, but we recommend testing to ensure everything works correctly.</p>\n<p>If the code shall unconditionally be changed to longer amount values, adjusting the ‘constant’ to the extended maximum value is sufficient as well.</p>\n<p><strong><span>Data Clusters</span></strong></p>\n<p>A data cluster is a group of data objects grouped together for the purpose of storage in a memory medium that can only be edited using ABAP statements.</p>\n<ul>\n<li>The data objects are written to the memory medium by the statement EXPORT.</li>\n<li>The data objects are extracted again by the statement IMPORT.</li>\n</ul>\n<p>Due to the extended amount lengths, following places may need to be adapted:</p>\n<p><strong>IMPORT FROM DATABASE</strong></p>\n<p>An EXPORT TO DATABASE statement saves a data cluster defined in the parameter list with the specified ID in the database table specified in the statement. The data is persisted and can be read using a corresponding <a href=\"https://help.sap.com/doc/abapdocu_751_index_htm/7.51/en-US/abapimport_medium.htm\" target=\"_blank\">IMPORT</a> statement. In case the data cluster involves a data element that was extended and if the EXPORT happens before upgrade and the IMPORT happens after the upgrade, it will cause a dump when the IMPORT statement is reached even if the imported value is of the original length. To avoid runtime errors, <a href=\"https://help.sap.com/doc/abapdocu_751_index_htm/7.51/en-US/abapimport_conversion.htm\" target=\"_blank\">“ACCEPTING PADDING”</a> addition to the IMPORT FROM DATABASE would be necessary.</p>\n<p><strong>IMPORT FROM MEMORY</strong></p>\n<p>An EXPORT to memory statement saves a data cluster defined in the parameter list with the specified ID in the ABAP memory. This technique is usually used to transfer data between programs within the same session. The data can be read using a corresponding IMPORT statement. In case the data cluster involved a data element which was extended, there is a possibility of a runtime error. This happens if the data element in the EXPORT statement does not lengthwise fit to the data element in the IMPORT statement due to extended amount lengths. In such cases, adjustment is necessary to ensure that the data element in the EXPORT and IMPORT statements match lengthwise.</p>\n<p><strong>ALV Extracts</strong></p>\n<p>ALV Extracts are written using function module REUSE_ALV_EXTRACT_SAVE which internally exports the data to database and read with function module REUSE_ALV_EXTRACT_LOAD, which internally imports the data from the database. If the ALV extract SAVE (by using REUSE_ALV_EXTRACT_SAVE), which involves extended amount fields, happens before upgrade with short amounts and the load happens after upgrade, then the load function module will raise an error: \"Obsolete extract structure. Extract can no longer be read.\", even though only short amounts are read back from data cluster. To avoid this, the optional importing parameter I_ACCEPT_PADDING should be set to X when calling REUSE_ALV_EXTRACT_LOAD.</p>\n<p><span><strong>ABAP Core Data Services (CDS Views)</strong></span><br/>DDLs (Data Definition Languages) created using CDS Views may need to be adjusted due to amount field length extension. In cases where a cast expression is used to convert the value of the operand to an abap.curr or abap.dec type with specified length, the length may need to be adjusted due to amount field length extension. Unions may also need to be checked, because the field length of the union result is determined by the result of the first select clause of the union statement.</p>\n<p><br/><span><strong>ABAP Managed Database Procedures</strong></span><br/>There is no specific guidance for ABAP Managed Database Procedures, but they may need to be checked for any impact due to amount field length extension. Similar to the CDS views, casts and unions may need to be checked.</p>\n<p><br/><span><strong>Generated Code</strong></span><br/>Impact of extended the provided amount field length extension functionality on the generated code should also be considered. If you generate code in the development system, it must be considered during upgrade because it might require re-generation because of amount field length extension.</p>", "noteVersion": 12}, {"note": "2659194", "noteTitle": "2659194 - Check variant for SAP S/4HANA 1809 custom code checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to perform the SAP S/4HANA custom code checks for release SAP S/4HANA 1809, but the check variant S4HANA_READINESS_1809 is not available in your system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4HANA_READINESS_1809</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Install the mentioned support package or apply the attached correction instruction and consider the manual post-implementation steps.</p>\n<p>Afterwards, the check variant S4HANA_READINESS_1809 is available in your system.</p>", "noteVersion": 3}, {"note": "2628714", "noteTitle": "2628714 - Amount Field Length Extension: User Interface Adaptations", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types are extended. See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>for motivation and scope.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, AFLE, 23 digits, Currency,  User Interface, GUI, Dynpro, Web Dynpro, List Printing, Print Forms, OData, FIORI, JavaScript.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Introduction</strong></p>\n<p>Amount field extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases. This also applies to your code that references the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040 </a>and SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>. Nevertheless, there are some artifacts that may require manual adjustments.</p>\n<p>All user interfaces in which at least one of the extended amount fields is part of the underlying data model are affected. In these UIs, the properties of the UI elements or programs might need to be adjusted in such a way that the complete length of the amount field is displayed in case it is intended to support extended amount field length in the respective screen. In other cases where extended amount field length shall not be supported, it might be necessary to make adjustments to ensure that no large amounts are entered.</p>\n<p>To support the adoption, the following sections provide instructions for different UI technologies.</p>\n<p><strong><a href=\"#ABAPGUI\" target=\"_self\">ABAP GUI Dynpro</a></strong></p>\n<p><strong><a href=\"#WebDynpro\" target=\"_self\">Web Dynpro</a></strong></p>\n<p><strong><a href=\"#FIORIANDJAVASCRIPT\" target=\"_self\">Fiori Applications and JavaScript Code</a><br/></strong></p>\n<p><strong><strong><a href=\"#ADOBEFORMS\" target=\"_self\">Adobe Forms</a></strong></strong></p>\n<p>We recommend comprehensive testing for all affected user interfaces to ensure that they work correctly after upgrade to S/4HANA 1809 or higher.</p>\n<p><strong><a name=\"ABAPGUI\" target=\"_blank\"></a>ABAP GUI Dynpro</strong></p>\n<p>If extended amount field length shall not be supported, amounts and the corresponding screen fields are affected because of the amount field length extension. In this case, consider replacing the data element with an equivalent data element that was not extended, for example, one of the data elements to support the original length. If this change is not possible and the provided amount field length extension will not be activated in customizing, the conversion exits attached to domains will only allow entries of the previous amount field length.</p>\n<p>If the extended amount field is part of GUI screen and you decided to support amount field length extension, adjustments in the screenpainter are required. You need to extend the visible field length by adjusting the “Vis. Length” property of the field to be equal to “Def. length” whenever desired, to support displaying long amounts. Determine this on a case by case basis based on screen layout. Please refer to figure 1 in attachment.</p>\n<p>If the screen field visible length cannot be adjusted, we provide a fallback strategy that enables displaying of an extended amount field in a smaller visible length. Ellipsis will be displayed in this scenario and the entire amount is displayed on mouse hover. Please refer to figure 2 in attachment. Take care that the DDIC Modify flag in the screen attributes is not set to 'X' (modified compared to the Dictionary) for the provided amount field length extension to work correctly.</p>\n<p>For the screens where a field is intended to support amount field length extension and it is not activated in customizing, conversion exits attached to the domains automatically restrict the entry of larger amounts. If the screen field is not referring to the dictionary, the respective conversion routine must be specified explicitly in the screen painter. Conversion routine naming convention uses Data Type, Sign Information, No. of characters &amp; Decimals as shown in the examples below.</p>\n<p>AC132: Used for signed currency domains with 13 characters and 2 decimals</p>\n<p>AU132: Used for unsigned currency domains with 13 characters and 2 decimals</p>\n<p>AD132: Used for signed decimal domains with 13 characters and 2 decimals</p>\n<p>AE132: Used for unsigned decimal domains with 13 characters and 2 decimals</p>\n<p>For more information and for a list of domains and the corresponding conversion exits, see SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040</a>.</p>\n<p>For cases where a maintenance view exists on the tables using amount field length extension, the maintenance view should be regenerated to support this.</p>\n<p><strong><a name=\"WebDynpro\" target=\"_blank\"></a>﻿Web Dynpro</strong></p>\n<p>Please make sure that the \"length\" and \"width\" properties of affected fields in static models are set correctly. If the Web Dynpro models are created dynamically, check the corresponding implementation.</p>\n<p><strong><a name=\"FIORIANDJAVASCRIPT\" target=\"_blank\"></a>﻿Fiori Applications and JavaScript Code</strong></p>\n<p>Adjustments required for Fiori applications might consist of adjustments to the backing OData service(s) as well as JavaScript code or view definitions in the Fiori application itself.</p>\n<p><strong>OData</strong></p>\n<p>OData services are usually used in Fiori application as backend dataprovider. The need for adjustments of the OData services depends on the implementation style of the service and on the choice, if the provided amount field length extension shall be supported or not. Note that the decision must be made based on the implementation of the affected entity type instead of on the ODATA service definition as there might be different implementation styles mixed within one OData service.</p>\n<p>If you decide not to support the provided amount field length extension for your system, you need to make the following adjustments: For your OData services containing CDS views, that use the “@OData.publish:true” annotation, adjust the dictionary structure used in the CDS view definition to use equivalent data elements that are not extended. For example, adjust the dictionary structure for the corresponding _CS data element by casting the field in the CDS view to the respective data element and by activating the view. If you decide to support the provided amount field length extension no changes are necessary.</p>\n<p>For your OData services created via transaction SEGW using Data Source Reference to CDS views, make the necessary changes in the CDS views. If you decide not to support the provided amount field length extension, the respective field in the CDS view definition must be adjusted to use equivalent data elements that are not extended, for example, the corresponding _CS data element by casting the field in the CDS view to the respective data element and by activating the view. If you decide to support the provided amount field length extension and you would like to support both short and long amounts, to improve the usability and perform input validations at client side, implement/adjust the model provider extension class MPC_EXT as shown <a href=\"#MPC_EXT_CHANGES\" target=\"_self\">below</a>.</p>\n<p>For your OData services modeled in SEGW, if you decide not to support the provided amount field length extension, the dictionary structure referenced in the respective entity type or complex type must be adjusted to use equivalent data elements that are not extended, for example, the corresponding _CS data element. If you decide to support extended amounts, the steps below must be performed:</p>\n<ol>\n<li>In the property attributes of the affected type’s property “Precision” and “Scale” shall be adjusted to “24” and “3” respectively for the amount property of Gateway Builder. Refer to figure 3 in the attachment.</li>\n<li>Implement/adjust model provider extension class MPC_EXT changes as described <a href=\"#MPC_EXT_CHANGES\" target=\"_self\">below </a>for better usability.</li>\n</ol>\n<p>Note: In the front-end development system, you might need to clean the services cache to reflect the latest changes when testing the service.</p>\n<p>For your OData services where metadata was defined by implementing the model provider class, if you decide not to support the provided amount field length extension, metadata definition code usually does not need to be adjusted, however it might be advisable to adjust the affected DDIC structures used for implementing the service, for example by using equivalent data elements that are not extended, for example the corresponding _CS data element. If you decide to support the provided amount field length extension, execute the following steps described below:</p>\n<ol>\n<li>Adjust the implementation in the model provider class code by setting the “Maxlength” and “precision” of the respective properties to “24” and “3” respectively.</li>\n<li>Implement/adjust model provider extension class MPC_EXT changes as described <a href=\"#MPC_EXT_CHANGES\" target=\"_self\">below </a>for better usability.</li>\n</ol>\n<p>Note: In the front-end development system, you might need to clean the services cache to reflect the latest changes when testing the service.</p>\n<p><strong><a name=\"MPC_EXT_CHANGES\" target=\"_blank\"></a>﻿Dynamic adjustments of service metadata based on activation of extended amount length in the backend SAP system</strong></p>\n<p>In case DDIC changes described above are not possible or the provided amount field length extension shall be supported with the possibility to dynamically react to activated / deactivated amount field length extension, a possibility to dynamically adjust the OData metadata is provided by class CL_AFLE_MPC_EXT_UTILITY_CLASS. The class needs to be called in the model provider extension class (_MPC_EXT)</p>\n<p>Enhance the DEFINE method of the model provider extension class MPC_EXT to call ADJUST_MODEL of utility class CL_AFLE_MPC_EXT_UTILITY_CLASS at the very end of the method.</p>\n<p>Example:</p>\n<p>METHOD define.</p>\n<p>super-&gt;define( ).</p>\n<p>cl_afle_mpc_ext_utility_class=&gt;adjust_model( io_model = model ).</p>\n<p>ENDMETHOD.</p>\n<p>The GET_LAST_MODIFIED method has to be adjusted to call ADJUST_LAST_MODIFED at the end of the method to ensure metadata caches are properly invalidated.</p>\n<p>Example:</p>\n<p>METHOD GET_LAST_MODIFIED.</p>\n<p>rv_last_modified = super-&gt;get_last_modified( ).</p>\n<p>cl_afle_mpc_ext_utility_class=&gt;adjust_last_modified( CHANGING cv_last_modified = rv_last_modified ).</p>\n<p>ENDMETHOD.</p>\n<p><strong>Fiori and JavaScript coding</strong>:</p>\n<p>The following adjustments are usually only relevant in case the provided amount field length extension shall be supported by the respective Fiori application.</p>\n<p>It is a known limitation that, JavaScript has no native datatype for decimal numbers. There is only the number/float type which internally uses double precision binary floating point format (IEEE 754, binary64). This data type has 53 bits for the mantissa, which means between 15 and 16 digits in decimal representation. Therefore, amounts exceeding 15 digits returned from OData model will result in rounding as shown below. The OData model itself represents decimal amounts with datatype “Edm.Decimal” for which the representation JavaScript side is a string.</p>\n<p>The OData amount “123456789012345678901.23”, is represented like123456789012345680000.00</p>\n<p>If you decided to support the provided amount field length extension for Fiori screens, to avoid the rounding issue different aspects need to be considered</p>\n<p>Generally, the SAP UI5 controls are supporting larger amounts without the rounding issues described above. There is once exception in control sap.ui.unified.Currency that needs to be considered. The same applies in case the applications implemented their own formatting for amounts that internally convert to datatype number. Fiori control “sap.ui.unified.Currency” is used to handle extended amount values in Fiori XML views. The control’s property value is typed as float, that is, rounding issues will occur. Instead in SAP UI5 1.54 an alternative property stringValue was introduced, which can be used instead and supports larger amounts.</p>\n<p>Example coding with type -\"sap.ui.model.type.String\" as of SAP UI5 1.52</p>\n<p>&lt;unified:Currency</p>\n<p>value=\"{path: 'AmountInDisplayCurrency',</p>\n<p>type: 'sap.ui.model.type.String'}\"</p>\n<p>currency=\"{path: 'DisplayCurrency'}\"/&gt;</p>\n<p>With the new property “stringValue” introduced to this control in SAP UI5 1.54, adapt the prior example coding as below.</p>\n<p>&lt;unified:Currency</p>\n<p>stringValue=\"{path: 'AmountInDisplayCurrency'}</p>\n<p>currency=\"{path: 'DisplayCurrency'}\"/&gt;</p>\n<p>Adjust custom formatter functions that internally use datatype number to ensure that no rounding issues occur. There is no guideline on how to identify the relevant formatters. Check functions like parseFloat() and Number() but be sure to check others as well. Check whether you can use SAP UI5 standard functions like sap.ui.core.format.NumberFormat.getCurrencyInstance and sap.ui.model.type.Currency for the adjustments. There is no guideline on how to identify the relevant formatters. Check functions like parseFloat() and Number() but be sure to check others as well. Check whether you can use SAP UI5 standard functionalities like sap.ui.core.format.NumberFormat.getCurrencyInstance and sap.ui.model.type.Currency for the adjustments.</p>\n<p>In case of a calculation on the client side (inside the Web browser) with amounts exceeding 15 digits, the calculation result may not be accurate. A third-party JavaScript library to handle larger amount might be considered as a solution. Alternatively, the calculation logic could be done in the backend and then only the calculation result is transferred and used on the client side.</p>\n<p><strong><a name=\"ADOBEFORMS\" target=\"_blank\"></a>﻿Adobe Forms</strong></p>\n<p>Adobe form output is unaffected in case the provided amount field length extension shall not be activated in customizing, at least in case the amounts are passed as decimal numbers in the form interface. In case character datatypes are used, adjustments of the code (see SAP Note <a href=\"/notes/2610650\" target=\"_blank\">2610650</a>) might be required.</p>\n<p>Regarding supporting extended amount length in Adobe form, we currently cannot give detailed guidance.</p>", "noteVersion": 10}, {"note": "2628040", "noteTitle": "2628040 - Amount Field Length Extension: General Information", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types are extended. See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>for motivation and scope.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, AFLE, BAPI, IDoc, RFC, 23 digits, Currency, Decoupling, Conversion Exit</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<div></div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Table of Contents</strong></p>\n<ul>\n<li><strong><strong><a href=\"#Introduction\" target=\"_self\">Introduction</a></strong></strong></li>\n<li><strong><strong><strong><a href=\"#NewDomains\" target=\"_self\">Exchanged Domains</a></strong></strong></strong></li>\n<li><strong><strong><strong><strong><strong><strong><strong><a href=\"#DictionaryReplacement\" target=\"_self\">Dictionary Replacement</a></strong></strong></strong></strong></strong></strong></strong></li>\n<li><strong><strong><strong><strong><strong><strong><strong><strong><a href=\"#DecouplingNotTargeted\" target=\"_self\">Decoupling in areas Not Targeted for Extension</a></strong></strong></strong></strong></strong></strong></strong></strong></li>\n<li><strong><strong><strong><strong><strong><strong><strong><strong><strong><a href=\"#FloatDomains\" target=\"_self\">Domains and Data Elements for FLOAT data type</a></strong></strong></strong></strong></strong></strong></strong></strong></strong></li>\n<li><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><a href=\"#DatabaseRepresentation\" target=\"_self\">Database Representation of Currency Values</a></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></li>\n<li><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><a href=\"#ReleaseExtInt\" target=\"_self\">Released External Interfaces</a></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></li>\n<li><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><a href=\"#DecouplingExtInt\" target=\"_self\">Decoupling of Extended Data Elements in Released Interfaces</a></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></li>\n<li><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><a href=\"#SystemBehavior\" target=\"_self\">System Behavior That Depends on the Activation of Amount Field Length Extension</a><br/></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></li>\n<li><strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><a href=\"#InternalCallReleaseInt\" target=\"_self\">Internal Calls of Released External Interfaces</a></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></li>\n<li><strong><strong><strong><strong><strong><a href=\"#ListofIntroducedDomains\" target=\"_self\">Appendix: Domains with Associated Conversion Exits</a></strong></strong></strong></strong></strong></li>\n</ul>\n<p><strong><a name=\"Introduction\" target=\"_blank\"></a>﻿Introduction</strong></p>\n<p>In SAP S/4HANA, the amounts in general ledger are extended to a uniform length of 23 digits overall with 2 decimal places. It is important to consider, that this relates to the ABAP internal representation where currencies are stored with two decimals. This means for currencies defined with 2 decimals, 21 significant digits can be represented, for currencies without decimals, 23 respectively and so on.</p>\n<p>Additionally, the value hierarchies in universal ledger that supported 13 digits on the financial document line item level, 15 digits on the header level for totals and 17 digits for account balances were flattened to support 23 digits on all levels, as it was already introduced in the universal journal.</p>\n<p>This note gives an overview about how we implemented this extension and what the consequences of the extension are for your code. For details on different aspects, see the referenced notes.</p>\n<p>Extending the maximum amount of currency fields means that the code in the system must guarantee that all relevant pieces of code can handle the maximum potential values. It is especially important to ensure that handling of the currency amounts does not lead to overflows and no truncation occurs resulting in loss of accuracy.</p>\n<p>This note and referenced notes describe code changes and other procedures which were needed to accommodate the technical length extension. As your code may use some of the same artifacts, the described code adaptations need to be performed in your affected custom code. This is true, in some cases, irrespective of whether the provided amount field length extension is <a href=\"#SystemBehavior\" target=\"_self\">activated</a> in customizing or not.</p>\n<div>\n<p>In a SAP system, consistent behavior of a data field is usually guaranteed by the fact that all data elements used are derived from the same domain. This domain defines the technical properties of the derived fields. However, for representation of amounts there was no singular domain or unique set of domains that was used in all relevant places, rather a multitude of similar or identical domains existed for various reasons. These modifications have been performed only in areas targeted for amount field length extension.</p>\n<p>The provided amount field length extension can be activated in customizing or remain inactive, in either case, the extended data types encode the previous technical properties (the length of the domains which have been replaced) and introduce the extended lengths needed for the target areas. The use of only original, short amounts is enforced until amount field lenght extension is explicitly activated in customizing. To use extended amounts the provided amount field length extension first needs to be explicitly <a href=\"#SystemBehavior\" target=\"_self\">activated</a> in customizing.</p>\n</div>\n<p><strong><a name=\"NewDomains\" target=\"_blank\"></a>﻿Exchanged Domains</strong></p>\n<p>We have <a href=\"#ListofIntroducedDomains\" target=\"_self\">exchanged domains</a> located in package <strong>AFLE_S4</strong>. These were used to replace the domains of all currency holding data elements that were between the lengths of 9 to 22 digits within the areas targeted for extension. The domains represent the types listed above: CURR, DEC, CHAR, and FLOAT.</p>\n<p><strong><a name=\"BreakdownofDomanNameoneExample\" target=\"_blank\"></a>Convention of Domain and Technical Data Element Names</strong></p>\n<p>This section includes descriptions of introduced domains including replacement and target domains. Replacement domains are used for replacing the domains of already in use data elements to enable them to be capable of storing extended amounts when the provided amount field length extension is activated in customizing. Target domains are available for the types CURR and DEC.</p>\n<p>•    CURR data type</p>\n<p>Example:</p>\n<p>technical DTEL: AFLEX09D2O10N</p>\n<p>domain: AFLE09D2O10N_TO_23D2O30N =&gt;  length 9, decimals 2, output length 10, no sign TO length 23, decimals 2, output length 30, no sign.</p>\n<p>•     CURR data type Extended Length or Target Domain Length</p>\n<p>These technical data elements and domains are intended to be used wherever an extended length data element is required (for example, compatibly adapted internally released interfaces)</p>\n<p>Example:</p>\n<p>technical DTEL: AFLE23D2O31S</p>\n<p>domain: AFLE23D2O30N =&gt;  length 23, decimals 2, output 31, with sign, TO length 23, decimals 2, output 30, no sign</p>\n<p><span>·<span new=\"\" roman';\"=\"\" times=\"\">         </span></span><span>DEC Data Type Domains and Data Elements</span></p>\n<p>Example:</p>\n<p>technical DTEL: AFLEDX09D2O10N</p>\n<p>domain: AFLED09D2O10N_TO_23D2O30N=&gt; length 9, decimals 2, output length 10, no sign TO length 23, decimals 2, output length 30, no sign.</p>\n<p>•    CHAR data type</p>\n<p>These technical data elements were mainly intended for replacing local data definitions.</p>\n<p>Example:</p>\n<p>technical DTEL: AFLEXC10</p>\n<p>domain: AFLE_C10_TO_C25=&gt; character length 10 to character length 25.</p>\n<p>•    CHAR data type with varying target output lengths</p>\n<p>Based on various usage patterns and application requirements in WRITE statements/ report output, and so on, additional domains for CHAR data type were created with output lengths other than 25. These domains were created to replace existing domains as well as to be used in application specific data elements.</p>\n<p>Example:</p>\n<p>AFLE_C13_to_C23=&gt; character length 13 to character length 23.</p>\n<p>•    Domains and Data Elements for FLOAT data type or Target Domain Length</p>\n<p>There are local definitions and DDIC objects defined with data type 'F' or being referenced to data element 'FLOAT'.  Using such variables and DDIC objects could potentially lead to rounding issues if not adjusted for extended amounts. The following domain and data element can be used to resolve such conflicts:</p>\n<p>technical DTEL: AFLEDF34_DEC</p>\n<p>domain: AFLEDF34_DEC =&gt; DECFLOAT34 length 31</p>\n<p><strong><a name=\"DictionaryReplacement\" target=\"_blank\"></a>﻿Dictionary Replacement</strong></p>\n<p>The ABAP dictionary automatically adjusts the following objects when a data element's domain is exchanged:</p>\n<div>\n<ul>\n<li>Data elements</li>\n<li>Structures</li>\n<li>Table types</li>\n<li>Transparent tables</li>\n<li>Views</li>\n</ul>\n<p>The replaced domains were used in the <a href=\"/notes/2628654\" target=\"_blank\">areas targeted</a> for the amount field length extension as well as in areas not targeted for extension. These <a href=\"#ListofIntroducedDomains\" target=\"_self\">domains</a> encode the previous technical properties (the original length of the domains replaced) and introduce the extended lengths needed for the target areas.</p>\n<div>\n<p>As mentioned in the introduction to the note, irrespective of whether you intend to adapt your code to utilize the domains created for large amounts you should consider usages of targeted data elements that appear in your code.  In areas not targeted for extension we replaced or ‘decoupled’ (see below) the usage of target data elements with copies of the original data elements.</p>\n<p>Overall, we analyzed the entire data flow in the system to identify all the places in which an amount was moved to a field that was not long enough for a 23 digit currency value. We have handled these conflicts in one of the ways described. This type of analysis is also required for your coding using SAP S/4HANA Readiness Codes cans.</p>\n<p>We have applied the described changes to parameters of all interfaces that are usually called within the same system, such as local function modules, class methods, BAdIs, and so on. In the types and structures that are used as parameters in these local calls, we have simply extended amounts to 23 digits. The same is also true for other extended fields. We did this usually for unreleased remote-enabled function modules because the main use cases for such function modules are parallelization and asynchronous communication.  For interfaces that are designated as “released” or which are called remotely, we chose a different way. For more information, see the specific chapter below on <a href=\"#ReleaseExtInt\" target=\"_self\">Released External Interfaces</a>.</p>\n<p><strong><a name=\"DecouplingNotTargeted\" target=\"_blank\"></a>﻿Decoupling in areas Not Targeted for Extension</strong></p>\n<ul>\n<li>Any extended data element being used in a data dictionary (DDIC) object belonging to an area not targeted for extension was replaced with a data element of the previous length.  The decoupling data element was created as a copy of the original, keeping the original domain and its technical attributes.  These replacement data elements are named the same as the original, but with the addition of suffix <strong>“_CS”</strong>.  The copied data element exists in the same package as the original.  For example, structure EKBEDATA belongs to an untargeted area, but used an extended data element DMBTR to define an amount field.  Since long amounts will not be used in EKBEDATA, DMBTR was replaced with the data element, DMBTR_CS. DMBTR_CS was created as a copy of the DMBTR with the original domain, WERT7, and the field length, 13,2.</li>\n<li>Alternatively, DDIC objects containing target data elements may also appear in the user interface of areas not to be extended.  There are two options to decouple these DDIC objects:</li>\n<ul>\n<li>Create new DDIC objects (e.g. structures) that use the decoupling data elements mentioned above.</li>\n<li>Leave the existing DDIC objects that are affected by the field length extension unchanged (i.e. supporting larger amounts) but restrict the value range the user can enter to the previous one, by adding checking logic the respective reports / transactions / function modules etc.</li>\n</ul>\n</ul>\n<p><strong><a name=\"FloatDomains\" target=\"_blank\"></a>﻿Domains and Data Elements for FLOAT data type</strong></p>\n<p>Currency amounts are sometimes also processed via datatypes that cannot directly be extended in length and would result in rounding issues, like type Float and DECFLOAT16. In these situations, a type that supports sufficiently large value ranges could be introduced instead, which is DECFLOAT34. These kind of fields, which are not currency values in themselves, are only extended if amounts need to be handled in these fields.</p>\n<div>\n<p><strong><a name=\"DatabaseRepresentation\" target=\"_blank\"></a>﻿Database Representation of Currency Values</strong></p>\n<p>Extending currency values in the database means that the field length of the currency data type in the database has been extended to 23 digits. We have done this in all target area tables (and all fields within the tables) in which a currency value can be stored.</p>\n<p><strong><a name=\"ReleaseExtInt\" target=\"_blank\"></a>﻿Released External Interfaces</strong></p>\n<p>External interfaces used for integration in a multi-system landscape (with SAP systems and/or non-SAP systems) must stay compatible with the already released versions of the interfaces, because you cannot assume that all connected systems will utilize extended currency.<a href=\"file:///M:/20_Central%20Team%20NSQ/Simplification/2215424%20-%20Amount%20Field%20Length%20Extension%20Field%20Length%20Extension%20-%20General%20Information.docx#_msocom_1\" target=\"_blank\"><br/></a></p>\n<p>This is especially relevant for the commonly used integration techniques BAPI, RFC because these techniques rely on a fixed length and order of fields in the transmitted data. Simply extending the amount fields in these interfaces would therefore technically break the version compatibility. For the currency values and other extended fields as described in this note, version compatibility has been achieved in the same way that is commonly used for BAPI interfaces: The already existing field keeps its original length and an extended field has been added at the end of the structure that allows transmitting 23 digit values.</p>\n<div>\n<p><strong><a name=\"DecouplingExtInt\" target=\"_blank\"></a>﻿Decoupling of Extended Data Elements in Released Interfaces</strong></p>\n<ul>\n<li>Replacement data elements of the same name with the addition of suffix “_XX_2” (XX = original length of data element) were created for use in external interfaces, such as IDocs, RFCs, BAPIs, Enterprise Services.  These data elements were created as copies of the original data elements, keeping the original domain with its technical attributes<strong>. </strong> The _XX_2 data elements are used to keep an existing field unchanged and the interface compatible. With the addition of a long field using the extended data element added either as a scalar parameter or to the end of a given structure parameter the interface can be used to import or export the amounts in either extended or original length.</li>\n<li>The domain BAPICUREXT31 (Dec 31,8) and data element BAPICUREXT31 are created to handle the long values. The domain BAPICUREXT31 (Dec 31, 8) is introduced for long fields. It has 23 digits before the decimal, so in case of currencies with no decimals (Example: Japanese Yen), the sender can enter amounts up to 23 digits. Note that, BAPICUREXT has 9 decimals, therefore, we only support 8 decimals for the long domain.</li>\n</ul>\n<div>\n<p>We have made these changes for BAPIs, IDocs, and released remote-enabled function modules.</p>\n<p>If you have created own external interfaces you have to check if similar changes are needed to ensure it stays compatible. A detailed description of the applied changes can be found in SAP note <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>.</p>\n<p><strong><a name=\"SystemBehavior\" target=\"_blank\"></a>﻿<strong><strong><strong><strong><strong><strong><strong><strong><strong><strong><a href=\"#SystemBehavior\" target=\"_self\">System Behavior That Depends on the Activation of Amount Field Length Extension</a></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></strong></p>\n<p>If the provided amount field length extension is <strong>NOT</strong> <strong>activated in customizing</strong>, the system behaves as follows:</p>\n<ul>\n<li>After an SAP Business Suite System has been migrated to SAP S/4HANA, the B2B and A2A communication via BAPIs, IDocs, Web-Services, and released RFCs (inbound) still works without further changes.</li>\n<li>The system prevents data from being created that cannot be sent via the original interfaces, that is, the usage of the extended fields is restricted to the original length.</li>\n<li>The shorter versions of the extended fields</li>\n<ul>\n<li>are still part of interfaces</li>\n<li>are still filled when sending data</li>\n<li>are still understood when receiving data</li>\n</ul>\n</ul>\n<ul>\n<li>Communication partners can still rely on the known behavior of the interfaces.</li>\n</ul>\n<p>When the amount field length extension is <strong>activated in customizing,</strong> it is no longer guaranteed that the data is transmitted in the original fields. The system behaves as follows:</p>\n<ul>\n<li>Currency amounts and other extended fields can be used with the full length.</li>\n<li>It is no longer guaranteed that the old short field in external interfaces is filled or accepted:</li>\n<ul>\n<li>If the currency values or other extended fields are used with more than the original length, the shorter version of an extended field is no longer filled in the interface and is therefore left initial, that is it has a value of zero, whereas the corresponding extended field has a non-zero value. For non-numeric fields, the field will be empty, rather than zero accordingly.</li>\n<li>If the current value of the currency field or the current value of another extended field still fits into the short field in the interface, the short field is filled in outbound and accepted in inbound as well.<a href=\"file:///M:/20_Central%20Team%20NSQ/Simplification/2215424%20-%20Amount%20Field%20Length%20Extension%20Field%20Length%20Extension%20-%20General%20Information.docx#_msocom_4\" target=\"_blank\"><br/></a></li>\n</ul>\n<li>Communication partners have to adjust and support the extended fields and data formats. <br/>Be aware that all SAP Business Suite systems in the landscape are communication partners!</li>\n</ul>\n<p>It is important to understand that the activation of the provided amount field length extension only affects the length of the value that you can enter. Independent from the activation of the provided amount field length extension, the technical length of the currency fields (and other affected fields) is already extended to the maximum length. That means that all code needs to be able to handle a technical field of length of 23,2 even if the amount field length extension is not enabled.</p>\n<p> <a name=\"InternalCallReleaseInt\" target=\"_blank\"></a>﻿<strong>Internal Calls of Released External Interfaces</strong></p>\n<p>As described in the previous chapters, we chose different strategies for internal and for released external APIs.</p>\n<p>If a released external API is called internally, that is, locally within one system, compatibility is not required. The interfaces already understand and fill the extended fields. Therefore, all internal calls of external interfaces must only use the added extended fields.</p>\n<p>This is also true if structures that are also used in released external interfaces, and which have therefore been changed in the way described, are used internally. Additionally, only the extended fields should be used for all internal coding in this case. See SAP note <a href=\"/notes/2628724\" target=\"_blank\">2628724</a>.</p>\n<p>Appendix.</p>\n<p><strong><a name=\"ListofIntroducedDomains\" target=\"_blank\"></a></strong><strong>﻿List of Introduced Domains and Conversion Exits</strong></p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"91\"/> <col width=\"341\"/> <col width=\"76\"/> <col width=\"73\"/> <col width=\"74\"/> <col width=\"87\"/> <col width=\"124\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl66\" height=\"20\" width=\"91\">Type</td>\n<td class=\"xl66\" width=\"341\">Domain</td>\n<td class=\"xl66\" width=\"76\">Length</td>\n<td class=\"xl66\" width=\"73\">Decimals</td>\n<td class=\"xl66\" width=\"74\">Output</td>\n<td class=\"xl66\" width=\"87\">Sign</td>\n<td class=\"xl66\" width=\"124\">Conversion Exit</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C16_TO_C24</td>\n<td class=\"xl65\" width=\"76\">24</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">24</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C18_TO_C26</td>\n<td class=\"xl65\" width=\"76\">26</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">26</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C13_TO_C23</td>\n<td class=\"xl65\" width=\"76\">23</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">23</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C15_TO_C26</td>\n<td class=\"xl65\" width=\"76\">26</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">26</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C10_TO_C25</td>\n<td class=\"xl65\" width=\"76\">25</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">25</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C11_TO_C25</td>\n<td class=\"xl65\" width=\"76\">25</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">25</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C12_TO_C25</td>\n<td class=\"xl65\" width=\"76\">25</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">25</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C13_TO_C24</td>\n<td class=\"xl65\" width=\"76\">24</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">24</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C13_TO_C25</td>\n<td class=\"xl65\" width=\"76\">25</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">25</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C13_TO_C31</td>\n<td class=\"xl65\" width=\"76\">31</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">31</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C14_TO_C25</td>\n<td class=\"xl65\" width=\"76\">25</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">25</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C15_TO_C23</td>\n<td class=\"xl65\" width=\"76\">23</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">23</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C15_TO_C25</td>\n<td class=\"xl65\" width=\"76\">25</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">25</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C15_TO_C28</td>\n<td class=\"xl65\" width=\"76\">28</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">28</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C15_TO_C30</td>\n<td class=\"xl65\" width=\"76\">30</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">30</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C15_TO_C31</td>\n<td class=\"xl65\" width=\"76\">31</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">31</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C16_TO_C25</td>\n<td class=\"xl65\" width=\"76\">25</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">25</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C16_TO_C26</td>\n<td class=\"xl65\" width=\"76\">26</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">26</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C16_TO_C30</td>\n<td class=\"xl65\" width=\"76\">30</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">30</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C16_TO_C31</td>\n<td class=\"xl65\" width=\"76\">31</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">31</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"91\">CHAR</td>\n<td class=\"xl65\" width=\"341\">AFLE_C17_TO_C25</td>\n<td class=\"xl65\" width=\"76\">25</td>\n<td class=\"xl65\" width=\"73\"></td>\n<td class=\"xl65\" width=\"74\">25</td>\n<td class=\"xl65\" width=\"87\"></td>\n<td class=\"xl65\" width=\"124\"></td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C17_TO_C30</td>\n<td>30</td>\n<td> </td>\n<td>30</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C17_TO_C31</td>\n<td>31</td>\n<td> </td>\n<td>31</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C18_TO_C25</td>\n<td>25</td>\n<td> </td>\n<td>25</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C18_TO_C30</td>\n<td>30</td>\n<td> </td>\n<td>30</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C18_TO_C31</td>\n<td>31</td>\n<td> </td>\n<td>31</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C18_TO_C36</td>\n<td>36</td>\n<td> </td>\n<td>36</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C19_TO_C25</td>\n<td>25</td>\n<td> </td>\n<td>25</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C19_TO_C31</td>\n<td>31</td>\n<td> </td>\n<td>31</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C20_TO_C25</td>\n<td>25</td>\n<td> </td>\n<td>25</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C20_TO_C28</td>\n<td>28</td>\n<td> </td>\n<td>28</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C20_TO_C30</td>\n<td>30</td>\n<td> </td>\n<td>30</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C20_TO_C31</td>\n<td>31</td>\n<td> </td>\n<td>31</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C20_TO_C36</td>\n<td>36</td>\n<td> </td>\n<td>36</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C21_TO_C25</td>\n<td>25</td>\n<td> </td>\n<td>25</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C21_TO_C31</td>\n<td>31</td>\n<td> </td>\n<td>31</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C22_TO_C25</td>\n<td>25</td>\n<td> </td>\n<td>25</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C22_TO_C30</td>\n<td>30</td>\n<td> </td>\n<td>30</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C22_TO_C31</td>\n<td>31</td>\n<td> </td>\n<td>31</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C23_TO_C25</td>\n<td>25</td>\n<td> </td>\n<td>25</td>\n<td> </td>\n<td>ACH23</td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C23_TO_C30</td>\n<td>30</td>\n<td> </td>\n<td>30</td>\n<td> </td>\n<td>ACH23</td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C23_TO_C31</td>\n<td>31</td>\n<td> </td>\n<td>31</td>\n<td> </td>\n<td>ACH23</td>\n</tr>\n<tr>\n<td height=\"20\">CHAR</td>\n<td>AFLE_C24_TO_C25</td>\n<td>25</td>\n<td> </td>\n<td>25</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE09D2O12S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC092</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE09D2O13S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC092</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE10D2O13S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC102</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE10D2O14S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC102</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE11D2O14S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC112</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE11D2O15S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC112</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE11D2O16S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC112</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE12D2O16S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC122</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE12D2O17S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC122</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE13D2O14S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC132</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE13D2O16S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC132</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE13D2O17S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC132</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE13D2O18S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC132</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE14D2O19S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC142</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE15D2O17S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC152</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE15D2O20S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC152</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE15D2O21S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC152</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE16D2O22S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC162</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE17D2O17S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC172</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE17D2O22S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC172</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE17D2O23S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC172</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE18D2O18S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC182</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE18D2O25S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC182</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE18D5O24S_TO_23D5O30S</td>\n<td>23</td>\n<td>5</td>\n<td>30</td>\n<td>X</td>\n<td>AC185</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE19D2O26S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC192</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE20D2O27S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC202</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE21D2O28S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC212</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE21D2O29S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC212</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE22D2O30S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AC222</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE09D2O10N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU092</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE09D2O12N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU092</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE10D2O13N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU102</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE11D2O12N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU112</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE11D2O14N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU112</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE11D2O16N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU112</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE12D2O15N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU122</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE12D2O16N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU122</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE13D2O16N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU132</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE13D2O17N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU132</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE13D2O18N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU132</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE13D3O17N_TO_23D3O30N</td>\n<td>23</td>\n<td>3</td>\n<td>30</td>\n<td> </td>\n<td>AU133</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE14D2O18N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU142</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE15D2O15N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU152</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE15D2O16N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU152</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE15D2O20N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU152</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE15D3O19N_TO_25D3O33N</td>\n<td>25</td>\n<td>3</td>\n<td>33</td>\n<td> </td>\n<td>AU153</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE16D2O21N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU162</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE17D2O18N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU172</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE17D2O20N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU172</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE17D2O22N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU172</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE17D3O22N_TO_23D3O30N</td>\n<td>23</td>\n<td>3</td>\n<td>30</td>\n<td> </td>\n<td>AU173</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE17D5O21N_TO_23D5O29N</td>\n<td>23</td>\n<td>5</td>\n<td>29</td>\n<td> </td>\n<td>AU175</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE18D2O24N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU182</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE19D2O25N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU192</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE20D2O26N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU202</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE21D2O28N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU212</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE22D2O29N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AU222</td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE_MDG_AMT_ADAPT</td>\n<td>15</td>\n<td>2</td>\n<td>20</td>\n<td>X</td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE_MDG_AMT_LONG</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">CURR</td>\n<td>AFLE23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">D34D</td>\n<td>AFLE_FLTP_TO_DF34_DEC</td>\n<td>31</td>\n<td>0</td>\n<td>46</td>\n<td>X</td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">D34D</td>\n<td>AFLEDF34_DEC</td>\n<td>31</td>\n<td>0</td>\n<td>46</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED16D5O21S_TO_23D5O30S</td>\n<td>23</td>\n<td>5</td>\n<td>30</td>\n<td>X</td>\n<td>AD165</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED10D2O14S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD102</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED11D0O15S_TO_23D0O31S</td>\n<td>23</td>\n<td>0</td>\n<td>31</td>\n<td>X</td>\n<td>AD110</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED11D2O15S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD112</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED11D2O16S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD112</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED12D2O17S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD122</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED13D0O18S_TO_23D0O31S</td>\n<td>23</td>\n<td>0</td>\n<td>31</td>\n<td>X</td>\n<td>AD130</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED13D2O18S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD132</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED15D0O16S_TO_23D0O24S</td>\n<td>23</td>\n<td>0</td>\n<td>24</td>\n<td>X</td>\n<td>AD150</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED15D0O20S_TO_23D0O31S</td>\n<td>23</td>\n<td>0</td>\n<td>31</td>\n<td>X</td>\n<td>AD150</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED15D2O21S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD152</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED17D0O23S_TO_23D0O31S</td>\n<td>23</td>\n<td>0</td>\n<td>31</td>\n<td>X</td>\n<td>AD170</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED17D2O23S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD172</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED18D2O25S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD182</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED19D0O26S_TO_23D0O31S</td>\n<td>23</td>\n<td>0</td>\n<td>31</td>\n<td>X</td>\n<td>AD190</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED19D0O26S_TO_25D0O34S</td>\n<td>25</td>\n<td>0</td>\n<td>34</td>\n<td>X</td>\n<td>AD190</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED19D2O26S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD192</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED19D2O26S_TO_25D2O31S</td>\n<td>25</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD192</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED19D5O25S_TO_27D5O36S</td>\n<td>27</td>\n<td>5</td>\n<td>36</td>\n<td>X</td>\n<td>AD195</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED20D2O27S_TO_23D2O31S</td>\n<td>23</td>\n<td>2</td>\n<td>31</td>\n<td>X</td>\n<td>AD202</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED21D0O28S_TO_23D0O31S</td>\n<td>23</td>\n<td>0</td>\n<td>31</td>\n<td>X</td>\n<td>AD210</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED23D5O30S_TO_27D5O36S</td>\n<td>27</td>\n<td>5</td>\n<td>36</td>\n<td>X</td>\n<td>AD235</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED16D6O20N_TO_23D6O29N</td>\n<td>23</td>\n<td>6</td>\n<td>29</td>\n<td> </td>\n<td>AE166</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED09D2O10N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE092</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED09D2O12N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE092</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED10D2O12N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE102</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED10D2O13N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE102</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED11D2O14N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE112</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED12D2O16N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE122</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED13D2O17N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE132</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED14D2O18N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE142</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED15D0O19N_TO_23D0O30N</td>\n<td>23</td>\n<td>0</td>\n<td>30</td>\n<td> </td>\n<td>AE150</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED15D2O10N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE152</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED15D2O20N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE152</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED15D4O19N_TO_23D4O30N</td>\n<td>23</td>\n<td>4</td>\n<td>30</td>\n<td> </td>\n<td>AE154</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED16D2O21N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE162</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED17D2O16N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE172</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED17D2O22N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE172</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED17D4O22N_TO_23D4O30N</td>\n<td>23</td>\n<td>4</td>\n<td>30</td>\n<td> </td>\n<td>AE174</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED18D2O24N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE182</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED20D2O26N_TO_23D2O30N</td>\n<td>23</td>\n<td>2</td>\n<td>30</td>\n<td> </td>\n<td>AE202</td>\n</tr>\n<tr>\n<td height=\"20\">DEC</td>\n<td>AFLED22D6O28N_TO_27D6O34N</td>\n<td>27</td>\n<td>6</td>\n<td>34</td>\n<td> </td>\n<td>AE226</td>\n</tr>\n<tr>\n<td height=\"20\">NUMC</td>\n<td>AFLE_N09_TO_N23</td>\n<td>23</td>\n<td> </td>\n<td>23</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">NUMC</td>\n<td>AFLE_N13_TO_N23</td>\n<td>23</td>\n<td> </td>\n<td>23</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"20\">NUMC</td>\n<td>AFLE_N15_TO_N23</td>\n<td>23</td>\n<td> </td>\n<td>23</td>\n<td> </td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<div>\n<script language=\"JavaScript\" src=\"/sap/public/bc/ur/sap_secu.js\" type=\"text/javascript\"></script>\n<br/><br/>\n<script language=\"JavaScript\" src=\"/sap/public/bc/ur/sap_secu.js\" type=\"mce-mce-text/javascript\"></script>\n</div>\n</div>\n</div>\n</div>\n</div>\n</div>\n", "noteVersion": 11}, {"note": "2628617", "noteTitle": "2628617 - Amount Field Length Extension: Adaptations for ALE/BDBG Generation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Selected currency amount field lengths and related data types have been extended. See SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654 </a>for motivation and scope.</p>\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Amount Field Length Extension, AFLE, BAPI, BDBG, ALE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Introduction</strong></p>\n<p>Amount field extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases. This also applies to your code that references the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040 </a>and SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>. Nevertheless, there are some artifacts that may require manual adjustments.</p>\n<p><span>ALE Interface for BAPIs</span></p>\n<p>For more information, see <a href=\"https://help.sap.com/saphelp_nw73/helpdata/en/4a/644ebc4b752b1ae10000000a42189c/frameset.htm\" target=\"_blank\">SAP Help </a>.</p>\n<p>To allow the asynchronous calls of BAPI in ALE business processes, create a BAPI-ALE interface for the BAPI. The integration of BAPIs and ALE makes it easier for you to develop your own ALE business processes for distribution.</p>\n<p>BAPIs are methods of SAP business objects. They are defined in the Business Object Repository (BOR) and are subject to strict design guidelines. The SAP Business Objects and their BAPIs provide an open, object-oriented view of the business processes and data in an SAP System. BAPIs are implemented as RFC-enabled function modules in the SAP system.</p>\n<p>When you maintain an ALE interface for a BAPI, the following objects are generated:</p>\n<ul>\n<li>\n<p>Message type</p>\r\nIDoc type &amp; IDoc segment types: An IDoc type is generated for the BOR method specified on the initial screen of BDBG. The IDoc type would be an assembly of segment types generated as per the parameters of the BAPI. Only relevant input parameters are considered for segments types. The parameter should be a parameter for the BOR method. The segment types consist of a header segment for all single value scalar parameters and segments per reference structure.</li>\n<li>ALE outbound function module:<br/>A function module that is called on the outbound side, acting as a proxy for sending the IDoc. It uses the BAPI data to generate the IDoc and dispatches it.</li>\n<li>ALE inbound function module:<br/>A function module that calls the BAPI with the IDoc data on the inbound processing side.</li>\n</ul>\n<p><span>Amount Field Length Extension</span></p>\n<p> For more information about the amount field length extensions in general, see the SAP Note <a href=\"/notes/2628654\" target=\"_blank\">2628654</a>.</p>\n<p><strong>Solution step 1: Add an additional parameter to your BOR method for the LONG parameter</strong></p>\n<p>This step is only needed if the extended field is a scalar parameter of the BAPI and a LONG scalar parameter has been added to the BAPI function module’s signature. It is not needed if extended fields only occur in structured parameters.</p>\n<p>As explained above, for generation of segment types only those input BAPI parameters are considered which are maintained as “Import” BOR method parameter. This is particularly important for amount field length extension. As part of field length extension, we add long parameters in BAPI for all scalar parameters extended. If the existing scalar parameter was considered for IDoc header segment field the added long parameter should also be considered for IDoc segment field.</p>\n<p>For BOR object methods delivered by SAP the needed adaptions to the BOR method parameters have already been done.</p>\n<p>If the extended scalar parameter is an importing parameter of the BOR method, just maintain the LONG parameter also as BOR method parameter.</p>\n<p><strong>Solution</strong><strong> step 2: Repair IDoc segments for IDocs that you already generated before conversion to S/4HANA </strong></p>\n<p><strong>This step and the subsequent steps are relevant for scalar parameters as well as structured parameters.</strong></p>\n<p>The segment definition in table EDSAPPL contains the reference to the currency data elements (Example: DMBTR) in case the ALE interface was already generated before. In the BAPIs delivered by SAP this has been changed to currency data element *_XX_2 (Example: DMBTR_13_2, Refer to the SAP Note <a href=\"/notes/2628040\" target=\"_blank\">2628040 </a>for details on Decoupling). For custom generated IDoc segments this still needs to be done.</p>\n<p>To repair the IDoc segment follow these steps:</p>\n<ol>\n<li>Execute transaction IDoc</li>\n<li>Go to <em>Repair Segments</em></li>\n<li>Select<em> Change data element for field</em></li>\n<li>Choose Execute</li>\n<li>Select<em> Segment Type, Field Name and extended data element</em></li>\n<li>Choose Execute </li>\n</ol>\n<div>\n<div>\n<div>\n<p><strong>Solution step 3: Regenerate the ALE interfaces</strong></p>\n<p>Trigger regeneration in transaction BDBG.</p>\n<p>You usually do not need to change any values on the subsequent dialogs.</p>\n<p>Further steps will be just the same that you usually do in BDBG: inbound, outbound function module and all affected segments are regenerated. If you did not generate function modules before for the specific interface, you can skip this step by canceling the confirmation dialog for function module generation. You should finally check the generated interface if the entities are contained as before with the additional fields added.</p>\n<div>\n<p><strong>Solution step 4:  Adapt your coding that calls the generated ALE outbound function module </strong></p>\n<p>In case you call the generated ALE outbound module to send an IDoc to a BAPI in a system that has not been enabled for the S/4HANA amount field length extension (example: an ECC system), make sure that the original short fields are also filled by the generated ALE outbound function module. Theoretically it would be possible to provide both short and long field for the amounts in each call to the generated outbound module, however this is error-prone. The other option is to use the mapper class already used for the BAPI implementations and embed the necessary mapping calls in the generated module. The latter is the recommended approach and will be described in the following.</p>\n<p>Change all calls to the generated outbound modules to fill the long amount fields in case amount field length extension shall be supported.</p>\n<p>To ensure both the short and long fields are filled compatibly, you can use the method ‘bapi_struct_outbound’ of the class ‘cl_afle_chk_mapper’ for filling the short fields. For more information see the class documentation.</p>\n<div>\n<div>\n<div>\n<div>\n<div>\n<div>\n<p>Example: FM: ALE_ACC_MANUAL_ALLOC_POST</p>\n</div>\n</div>\n</div>\n</div>\n</div>\n</div>\n</div>\n</div>\n</div>\n</div>\n<div>\n<div>\n<div>* begin-of-insertion<br/>* AFLE<br/>  DATA lt_fnames TYPE cl_afle_chk_mapper=&gt;tt_amt_bapi_fname.<br/>  lt_fnames = VALUE #(<br/>    ( short = 'VALUE_TCUR' long = 'VALUE_TCUR_LONG' )<br/>    ).<br/><br/>  LOOP AT DOC_ITEMS ASSIGNING FIELD-SYMBOL(&lt;ls_doc_item&gt;).<br/><br/>    CALL METHOD cl_afle_chk_mapper=&gt;bapi_struct_outbound<br/>      EXPORTING<br/>        it_fnames  = lt_fnames<br/>      IMPORTING<br/>        return     = DATA(ls_return)<br/>      CHANGING<br/>        cs_amt_ext = &lt;ls_doc_item&gt;.<br/><br/>    IF ls_return-type = 'E'.<br/>      MESSAGE ID ls_return-id<br/>      TYPE ls_return-type<br/>      NUMBER ls_return-number<br/>      WITH ls_return-message_v1 ls_return-message_v2<br/>      ls_return-message_v3 ls_return-message_v4<br/>      RAISING ERROR_CREATING_IDOCS.<br/>    ENDIF.<br/><br/>  ENDLOOP.<br/>* end-of-insertion </div>\n</div>\n</div>\n<div>\n<div>\n<div>\n<p>You should do this in a similar way as described in SAP Note <a href=\"/notes/2628724\" target=\"_blank\">2628724 </a>for remote calls to other systems, although the call to the generated ALE function module is local. This is because the generated ALE outbound module is a proxy for calling a BAPI asynchronously in a remote system. In this way it is an asynchronous remote function call.</p>\n<p>Note that this mapping has not only to be done for added scalar parameters but also for extended fields in structure and table parameters.</p>\n<div>\n<div>\n<div>\n<p>To protect the changes from getting lost during re-generation of the ALE inbound function modules we advise to follow the approach described in SAP Note <a href=\"/notes/513785\" target=\"_blank\">513785</a>.</p>\n</div>\n</div>\n</div>\r\n </div>\n</div>\n</div>", "noteVersion": 8, "refer_note": [{"note": "513785", "noteTitle": "513785 - UTI: Extension of source code generated by BDBG/BDFG", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to manually extend the source code generated by transaction BDBG or BDFG and retain this extension in the case of a new generation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ALE, BDBG, BDFG, BAPI-ALE interface</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>None</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The extended source code is taken into account by the generation tool in the case of a new generation if the following guidelines are observed:<br/>Never manually change the generated code, so do not delete or modify the lines of generated source code.<br/>To find the place to insert the source code, first find a distinct section (one or more lines) as context in the generated source code.<br/>There must not be any comment lines in the context.<br/>At the start of this section, manually insert a comment line as an indicator:<br/>* context-of-insertion<br/><br/>At the end of this section, manually add two comment lines as indicators for the source code to be inserted:<br/>* begin-of-insertion<br/>* end-of-insertion<br/><br/>Then insert the extension code between these two comment lines. The line width can be a maximum of 72 characters.<br/><br/>If the extended code is no longer required, delete it along with the indicators.</p></div>", "noteVersion": 3}]}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Software Upgrade / Maintenance", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Implementation of SAP Notes might be required in case new functionality should not be used"}, {"Activity": "Software Upgrade / Maintenance", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Implementation of SAP Notes might be required in case new functionality should be used"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}