{"guid": "6CAE8B3E8C3B1ED792D9EB9E8D0FE0C6", "sitemId": "BW701: Reporting Authorizations", "sitemTitle": "BW4SL - Reporting Authorizations", "note": 2478384, "noteTitle": "2478384 - BW4SL & BWbridgeSL - Reporting Authorizations", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use SAP BW Reporting Authorizations (3.x format) and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$REPAUTH</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP BW Reporting Authorization (3.x format) are not available in SAP BW/4HANA and SAP Datasphere, SAP BW bridge. There is no analysis available, if the Reporting Authorizations are used or not.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>Target SAP BW/4HANA:</strong></span></p>\n<p>Reporting authorizations are obsolete since SAP BW 7.3. If you are converting from lower SAP BW releases to SAP BW/4HANA and are still using reporting authorizations, you have to switch to Analysis Authorizations.</p>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.5.7/en-US/43fc4c7387e1025de10000000a1553f7.html\" target=\"_blank\">Reporting Authorizations --&gt; Analysis Authorizations</a></li>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/4c65fcd82f841f3ce10000000a42189c.html\" target=\"_blank\">Analysis Authorizations</a></li>\n<li><a href=\"https://help.sap.com/saphelp_nw75/helpdata/en/ad/8f7842fdb70f53e10000000a155106/frameset.htm\" target=\"_blank\">Migration of Reporting Authorizations to the New Concept</a></li>\n</ul>\n<p><span><strong>Target SAP Datasphere, SAP BW bridge:</strong></span></p>\n<p>The transfer from SAP BW to SAP Datasphere starts with release 7.3. On this release reporting authorization are already obsolete. In addition SAP Datasphere, SAP BW bridge acts a an staging layer and does not support OLAP engine and functionality dependent on the OLAP engine. Therefore, there is no option to migrate SAP BW Reporting Authorization.</p>", "noteVersion": 5, "refer_note": [{"note": "2421930", "noteTitle": "2421930 - Simplification List for SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Simplification List for SAP BW/4HANA is published as PDF document on the SAP Help Portal (<a href=\"http://help.sap.com/bw4hana20\" target=\"_blank\">http://help.sap.com/bw4hana20</a>). This SAP Note provides the Simplification List in spreadsheet format.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SIMPL, BW4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Planning to transition from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable our customers to better plan and estimate their path to SAP BW/4HANA, we have created the “Simplification List for SAP BW/4HANA”. In this list we describe in detail, on a functional level, what happens to individual object types and solution capabilities when transitioning to SAP BW/4HANA. Compared to the SAP Business Warehouse products, we have in some cases merged certain functionality with other elements or reflected it within a new solution or architecture.</p>\n<p><strong>A new, web-based UI for searching and displaying Simplification Items</strong></p>\n<p>In parallel to the PDF document and the spreadsheet available via this SAP Note, the <em>SAP Simplification Item Catalog</em> offers direct search and browse of SAP BW/4HANA Simplification Items in their current state via the SAP ONE Support Launchpad at <a href=\"https://launchpad.support.sap.com/#/sic/\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/</a>.</p>", "noteVersion": 6, "refer_note": [{"note": "2347382", "noteTitle": "2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to run or you are already running an SAP BW/4HANA system.<br/>You want to convert your SAP BW system to SAP BW/4HANA.<br/>You want to upgrade your system from SAP BW/4HANA 1.0 to SAP BW/4HANA 2.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revision, BW4, BW/4HANA, BW, NetWeaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note contains information about the following topics:</strong></p>\n<ol>\n<li>General Information about SAP BW/4HANA and the conversion to SAP BW/4HANA</li>\n<li>Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</li>\n<li>Information relevant for SAP BW/4HANA 2023</li>\n<li>Information relevant for SAP BW/4HANA 2021</li>\n<li>Information relevant for SAP BW/4HANA 2.0</li>\n<li>Information relevant for SAP BW/4HANA 1.0</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>1. General information about SAP BW/4HANA and the conversion to SAP BW/4HANA</span></strong></p>\n<p><span>High Level information about BW/4HANA: <a href=\"http://www.sap.com/bw4hana\" target=\"_blank\">http://www.sap.com/bw4hana</a></span></p>\n<p><span>See SAP Note <a href=\"/notes/2347384 \" target=\"_blank\">2347384</a> - SAP BW/4HANA 1.0 Important Notes before you start</span></p>\n<p><span>When planning your system landscape(s) for the next release of SAP BW/4HANA (not release 1.00), you may want to take the available applicaton server platforms into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> </span></p>\n<p><span>SAP changed the <a href=\"https://support.sap.com/en/my-support/knowledge-base/security-notes-news.html\" target=\"_blank\">SAP security strategy</a> to cover security corrections for Support Packages delivered during the past 24 months (formerly 18 months). BW/4HANA will not extend this period. We provide security corrections for Support Packages delivered in the last 18 months as before.</span></p>\n<p><span>See note </span><a href=\"/notes/2733740\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2733740</a> for Information/Restriction about Relase BW/4HANA 2.0</p>\n<p><strong>2. Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Productively used SAP BW/4HANA release</strong></td>\n<td><strong>Recommended minimum SAP HANA SP/revision</strong></td>\n</tr>\n<tr>\n<td>1.0</td>\n<td>Revision 122.12 or higher (HANA 1.0 SPS 12)<br/>Revision 12 or higher (HANA 2.0 SPS 1 or higher)</td>\n</tr>\n<tr>\n<td>2.0</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2021</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2023</td>\n<td>Revision 71 or higher (HANA 2.0 SPS 7 or higher)</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that SAP strongly recommends to apply the <span>latest available</span> SAP HANA revision.</p>\n<p>For details about SAP HANA Maintenance Strategy see SAP Note <em><a href=\"/notes/2021789\" target=\"_blank\">2021789</a> - SAP HANA Revision and Maintenance Strategy</em>.</p>\n<p>Please keep the update paths for Maintenance Revisions in mind. Details can be found in SAP Note <em><a href=\"/notes/1948334\" target=\"_blank\">1948334</a> - SAP HANA Database Update Paths for Maintenance Revisions</em>.</p>\n<p>Notes with regards to SAP HANA Revisions:</p>\n<ul>\n<li>HANA SPS12: SAP Note <a href=\"/notes/2298750\" target=\"_blank\">2298750</a> - SAP HANA Platform SPS 12 Release Note</li>\n</ul>\n<p><span>SAP BW/4HANA and SAP HANA 2.0: </span></p>\n<p>SAP BW/4HANA 1.00 (including SAP_BASIS 7.50) has been released for HANA 2.0. Details can be found in note 2420699.<br/>SAP BW/4HANA 2.00 (including SAP_BASIS 7.53) and newer run on HANA 2.0 only</p>\n<p><strong> </strong></p>\n<p><strong>3. </strong><strong>Information relevant for SAP BW/4HANA 2023 (planned mid of Q4/2023)</strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3365187\" target=\"_blank\">3365187 - Information/Restriction Note for Release SAP BW/4HANA 2023</a>.</p>\n<p><strong><strong>4. Information relevant for SAP BW/4HANA 2021</strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3100794\" target=\"_blank\">3100794 - Release Information/Restrictions Note for SAP BW/4HANA 2021</a>.</p>\n<p><strong><strong><strong>5. Information relevant for SAP BW/4HANA 2.0</strong></strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"https://me.sap.com/notes/2733740\" target=\"_blank\">2733740 - Release Information/Restrictions Note for SAP BW/4HANA 2.0</a></p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>See note 2733740 - Release Information/Restriction Note for SAP BW/4HANA 2.0 for restrictions in BW/4HANA 2.0</li>\n<li>Urgent! Implement note <a href=\"/notes/2770525\" target=\"_blank\" title=\"2770525  - Data Transfer Intermediate Storage (DTIS): Missing package assignment\">2770525 - Data Transfer Intermediate Storage (DTIS): Missing package assignment</a> <span><strong>before</strong></span> upgrading to BW/4HANA 2.0</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Search/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Introduction Video: <a href=\"https://youtu.be/bxH0EzAhSwQ\" target=\"_blank\">https://youtu.be/bxH0EzAhSwQ</a></li>\n</ul>\n<li><span>Upgrading BW/4HANA 1.0 to 2.0</span></li>\n<ul>\n<li>We now introduced tasklist SAP_BW4_BEFORE_UPGRADE and SAP_BW4_AFTER_UPGRADE  to support you upgrading your system. Please see note 2846537 - Taskliste for upgrade of BW/4HANA</li>\n<li>Afte the upgrade to BW/4HANA 2.0 the flag TADIR Popup f. Obj. in View RSADMINC gets initialized. Please see note 2884312 for details.</li>\n</ul>\n<li><span>ABAP Software Stack</span></li>\n<ul>\n<li>The initial Software stack for BW/4HANA 2.0 looks like this:</li>\n<ul>\n<li>SAP_BASIS 7.53 SP01</li>\n<li>SAP_ABA 7.5D SP01</li>\n<li>SAP_UI 7.53 SP02</li>\n<li>SAP_GWFND 7.53 SP01</li>\n<li>ST-PI 7.40 SP09</li>\n<li>DW4CORE 2.00 SP00</li>\n<li>UIBAS100 4.00 SP01</li>\n</ul>\n<li>This should dramatically reduces the effort for installation</li>\n<li>During the upgrade from BW/4HANA 1.0 the missing software components are installed automatically</li>\n<li></li>\n</ul>\n<li><span>BW/4HANA 2.0 SP01</span></li>\n<ul>\n<li>Please install following note on top of SP01 directly after update:</li>\n<ul>\n<li>2779827  Error starting task list SAP_BW4_LAUNCHPAD_SETUP from other task lists</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP02 </span></li>\n<ul>\n<li>SAP_UI 7.54 SP1 as alternative UI version is released. Please check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07</li>\n<li>please also take into consideration the Support Package Upgrade Strategy when running on higher UI Version described in note <strong>2618449</strong></li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP04</span></li>\n<ul>\n<li>SAP_UI 7.54 &gt;= SP2 as alternative UI version is released. lease check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07. Also take into consideration the Support Paackage Strategy when running on higher UI version described in note <strong>2618449</strong></li>\n<li>NSE for DTO --&gt; available since HANA 2.0 SP04 + limitation see HANA Note <a href=\"/notes/2771956\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2771956</a></li>\n<li>Open Hub with SDA --&gt; available since HANA 2.0 SP04 Revision 46</li>\n<li>XDA --&gt; available since HANA 2.0 Sp04 Revision 40 </li>\n<li>Geo Support --&gt; available since HANA 2.0 SP04 Revision 45</li>\n<li>Treespec Partitioning in ADSO --&gt; available since Hana 2.0 SP04 </li>\n</ul>\n</ul>\n<p><strong>6. Information relevant for SAP BW/4HANA 1.0</strong></p>\n<p>Installation/Setup</p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>Please see SAP Note<strong> </strong><a href=\"/notes/2354516\" target=\"_blank\">2354516</a> to enable client copy after installation.</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Serach/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Equivalent Support Packages<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>SAP BW 7.50 (SAP_BW)</td>\n<td>BW/4HANA 1.00(DW4CORE)</td>\n<td>BPC 11 (BPC4HANA)</td>\n<td>SAP UI 7.52 </td>\n</tr>\n<tr>\n<td>               4</td>\n<td>        Initial Installation</td>\n<td>           N.A.</td>\n<td> </td>\n</tr>\n<tr>\n<td>               5</td>\n<td>                   1</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               6</td>\n<td>                   &gt;= 2</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               7</td>\n<td>                   &gt;= 4</td>\n<td>  Initial Installation</td>\n<td> </td>\n</tr>\n<tr>\n<td>               8</td>\n<td>                   &gt;= 5</td>\n<td>             &gt;= 1</td>\n<td> </td>\n</tr>\n<tr>\n<td>               9</td>\n<td>                   &gt;= 6</td>\n<td>             &gt;= 2             </td>\n<td> </td>\n</tr>\n<tr>\n<td>             10</td>\n<td>                   &gt;= 7</td>\n<td>             &gt;= 3</td>\n<td> </td>\n</tr>\n<tr>\n<td>             11*</td>\n<td>                   &gt;=8</td>\n<td>             &gt;=4</td>\n<td>      &gt;=2</td>\n</tr>\n<tr>\n<td>             12*</td>\n<td>                   &gt;=9</td>\n<td>             &gt;=5</td>\n<td>      &gt;=3</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n</ul>\n<p><span>*) Starting with BW/4HANA 1.00 SP08 SAP_UI 7.52 is required</span></p>\n<ul>\n<li><span>BW/4HANA SP08</span></li>\n<ul>\n<li><strong>Requirements:</strong></li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP10</li>\n<li>SAP_UI <strong>7.52 SP01 -- update required</strong></li>\n</ul>\n<li><strong>For new Installs:</strong></li>\n<ul>\n<li>For your convience we created a Support Release for BW/4HANA 1.00 including the following components:</li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP 10</li>\n<li>SAP_UI 7.52 SP02</li>\n<li>DW4CORE SP08</li>\n<li>BW4CONTB SP03</li>\n</ul>\n<li>this installation is based on Software Provisioning Manager 2.0 which requires HANA 2 SP2 Revision 23 see note 2610954.</li>\n<li>Release of the installation is planned for april (CW 17)</li>\n</ul>\n<li><strong>Enhancment in Tasklist</strong></li>\n<ul>\n<li>there are some enhancements made in the tasklists with regards to the new Web UI's: Please refert to note 2351381 for details.</li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP03</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n</ul>\n<li><span>BW/4HANA SP02</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n<li>Please implement the following notes on top of SP02 to avoid issues during setup/tasklisk execution</li>\n<ul>\n<li>2410466                BW Workspace customizing: RSDDTREXADMIN_TO_RSWSPCUST dumps </li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP01</span></li>\n<ul>\n<li>Please implement the following notes on top of SP01 to avoid issues during setup/later SP update</li>\n<ul>\n<li>2385382               Add SAPFSPOR to avoid strange DUMPS during SP update</li>\n<li>2385265               Check if essential objects are active after installation</li>\n<li>2381312               Task list SAP_BW4_SETUP_SIMPLE: Error in \"Check/Generate Packages for BW ODATA Services\" step</li>\n</ul>\n<li>General Information about tasklists and their usage in BW/4HANA: 2351381 SAP BW/4HANA task lists</li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA SP00</span></li>\n<ul>\n<li>If job BI_WRITE_PROT_TO_APPLLOG is not started by the tasklist please see SAP Note <a href=\"/notes/2356498\" target=\"_blank\">2356498</a>.</li>\n<li>In SP0 the tasklist shows an error which can be ignored. See SAP Note <a href=\"/notes/2351344\" target=\"_blank\">2351344</a> for details.</li>\n</ul>\n</ul>", "noteVersion": 36}, {"note": "2383530", "noteTitle": "2383530 - Conversion from SAP BW to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the different paths to get from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP BW/4HANA, SAP BW, Migration, Conversion, Transfer, In-Place, Remote, Shell, Lifecycle Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>0 Disclaimer</strong></span></p>\n<p>Converting a SAP BW system to SAP BW/4HANA is not a simple task. There is no \"wizard\" that magically converts everything. Instead, SAP provides a well-defined process to guide you through the renovation of your data warehouse. We have developed tools to automate this renovation where applicable and feasible, but they are not built or meant to fix badly designed models or clean-up neglected systems. In any conversion there is a need for manual interaction and re-design. The amount of such manual tasks varies from customer to customer and depends on the configuration and state of the SAP BW system. For example, a conversion of newer systems that have been configured using SAP HANA-optimized objects and LSA++ from the beginning will be relatively easy. In contrast to that, a conversion of older systems that are running on non-SAP HANA databases, using out-of-date interfaces, legacy data models, and multiple redundant layers can become quite challenging.</p>\n<p>It is therefore essential that you understand the differences between SAP BW and SAP BW/4HANA and how to handle them, thoroughly analyze your existing system, estimate the complexity and duration of required tasks, properly plan all conversion activities, learn and practice to use the conversion tools, test the conversion processes ideally with a \"copy of production\", and actively manage changes to your business. It speaks for itself that good project management and skilled personnel will be required to execute a timely and efficient conversion of a complete SAP BW system. If done right, your conversion to SAP BW/4HANA will be very successful.</p>\n<p>SAP offers several way to help. To get to started run a <a href=\"https://help.sap.com/viewer/p/SAP_READINESS_CHECK\" target=\"_blank\">SAP Readiness Check</a> or book a <a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">SAP Value Assurance service </a><a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">for SAP BW/4HANA</a>.</p>\n<p>We also highly recommend the Class Room Training <strong>BW4HC</strong>. (<a href=\"https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/\" target=\"_blank\">https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/</a>?).</p>\n<p> </p>\n<p><span><strong>1 Overview</strong></span></p>\n<p><strong>1.1 What is SAP BW/4HANA</strong></p>\n<p>SAP BW/4HANA is a new, next generation data warehouse product from SAP that, like SAP S/4HANA, is optimized for the SAP HANA platform, including inheriting the high performance, simplicity, and agility of SAP HANA. SAP BW/4HANA delivers real time, enterprise wide analytics that minimize the movement of data and can connect all the data in an organization into a single, logical view, including new data types and sources.<strong><br/></strong></p>\n<p><strong>1.2 Related Information</strong></p>\n<p>Please find more information here:</p>\n<ul>\n<li>The Future of Data Warehousing - SAP BW/4HANA  <br/><a href=\"http://sap.com/bw4hana\" target=\"_blank\">http://sap.com/bw4hana</a></li>\n<li>SAP BW/4HANA Community (includes FAQ)<br/><a href=\"https://community.sap.com/topics/bw4-hana\" target=\"_blank\">https://community.sap.com/topics/bw4-hana</a><br/><a href=\"https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html</a></li>\n<li>Online Documentation<br/><a href=\"http://help.sap.com/bw4hana10\" target=\"_blank\">http://help.sap.com/bw4hana10</a></li>\n<li>Product Roadmap<br/><a href=\"https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021\" target=\"_blank\">https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021</a></li>\n<li>SAP BW/4HANA vs. SAP BW<br/><a href=\"https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html</a><br/><br/></li>\n</ul>\n<p>For information related to functionality available in SAP BW but replaced or deleted in SAP BW/4HANA please refer to the Simplification List: <br/><a href=\"https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf\" target=\"_blank\">https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 1.0: <br/><a href=\"https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2.0:</p>\n<p><a href=\"https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2021 and higher:</p>\n<p><a href=\"https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf</a></p>\n<p> </p>\n<p><span><strong>2 How to Get to BW/4HANA</strong></span></p>\n<p><strong>2.1 Paths to SAP BW/4 HANA</strong></p>\n<p><strong><em>2.1.1 New implementation or fresh start</em></strong></p>\n<p>New implementations are the best choice for customers converting from a legacy system or building a system from scratch with new data model only.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, selective transport of BW objects (optional), implement HANA-optimized data models and flows, followed by data load and quality checks.</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), system connection (SAP Source), SAP BW/4HANA ETL, file upload and/or SAP HANA EIM (legacy sources)</p>\n<p>See SAP First Guidance: Complete functional scope (CFS) for SAP BW/4HANA: <a href=\"https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html<br/></a></p>\n<p><em><strong>2.1.2 System conversion</strong></em></p>\n<p>A system conversion addresses customers who want to change their current SAP BW system into a SAP BW/4HANA system. Using the Transfer Cockpit provided by SAP, the logical systemname of the system can be kept (in-place conversion) or a new  logical systemname can be used (remote conversion resp. shell conversion).</p>\n<p>A conversion project typically follows this sequence:</p>\n<ul>\n<li><strong>Discover / Prepare Phase</strong>: check system for BW/4HANA compliance (gather information about objects and code that needs to be transferred or changed), estimate effort for the conversion project</li>\n<li><strong>Explore / Realization Phase</strong>: Transfer legacy objects into HANA-optimized counterparts, system conversion, post conversion tasks</li>\n</ul>\n<p><em>*******. In-Place conversion</em></p>\n<p>Systems running on SAP BW 7.50 powered by SAP HANA can be converted in-place keeping their logical systemname. In the realization phase of the conversion project, classic objects have to be transferred into their HANA optimized replacements using the Transfer Cockpit. This transfer can be performed scenario-by-scenario. When all classic objects have been replaced, the system conversion to SAP BW/4HANA can be triggered.</p>\n<p>SAP highly recommends to use the latest support package for SAP BW/4HANA when performing the conversion. See note https://launchpad.support.sap.com/#/notes/2347382 regarding the equivalent support packages. Please keep the RTC Dates of the different Support Packages in mind when you plan your conversion project.</p>\n<p><strong>Steps</strong>: Migrate and/or upgrade to SAP BW 7.50 powered by SAP HANA (if necessary), install SAP BW/4HANA Starter Add-On (see “Installation” below), install Transfer Cockpit, transfer data models, adjust custom coding, perform system conversion</p>\n<p>To include the Data Comparison Functionality into InPlace-TaskList, please refer to the SAP Note <a href=\"/notes/2607742\" target=\"_blank\">2607742</a>.</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-IPL</p>\n<p><strong>What to use</strong>: SAP BW/4HANA Starter Add-On, SAP BW/4HANA Transfer Cockpit, SPAM/SAINT<br/><em></em></p>\n<p><em></em><em>*******. Remote conversion</em></p>\n<p>For SAP BW systems on releases from 7.30 to 7.50 running on Any-DB, a remote conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models into the new installation and to perform a remote data transfer.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, install DMIS Add-On, transfer data models, transport custom developments (might need adjustment to work with SAP BW/4HANA)</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-RMT</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit, DMIS Add-On</p>\n<p><em>*******. Shell conversion</em></p>\n<p>For SAP BW systems on releases from 7.00 to 7.50 running on Any-DB, a Shell conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models without data into the new installation.</p>\n<p>Our recommendation for shell conversion is to use a sandbox as the sender system. The latest SPs should also be installed on this system. Depending on the version of the current system, this saves a lot of time and effort. If you still decide to import the hints, you should look at the wiki <a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\" target=\"_blank\" title=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718</a> beforehand and follow the information from the wiki when importing.</p>\n<p><em><strong>Steps</strong>: </em>Install SAP BW/4HANA, transfer data models without data using SAP BW/4HANA Transfer Cockpit, and transport custom developments (might need adjustments to work with SAP BW/4HANA).</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-SHL</p>\n<p><strong>What to use: </strong>Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit</p>\n<p><strong><em>2.1.3. Landscape transformation</em></strong></p>\n<p>Landscape transformation is for customers who want to consolidate and optimize their complex SAP BW landscape (multiple production systems) into a single SAP BW/4HANA system or who want to carve-out selected data models or flows into a global SAP BW/4HANA system.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, implement consolidated and HANA-optimized data models and flows, followed by data load and quality checks</p>\n<p><strong>What to use</strong>: Selective data transfer, SAP Landscape Transformation (SLT), SAP Data Services, SAP BW/4HANA ETL or SAP HANA EIM<br/><em></em></p>\n<p>The following sections are focusing on the system conversion.</p>\n<p> </p>\n<p><strong>2.2 Availability</strong></p>\n<p><strong><em>2.2.1 Overview</em></strong></p>\n<p>The tools required to perform an In-Place, Remote or Shell conversion are general available now.</p>\n<p><strong><em>2.2.2 Releases</em></strong></p>\n<p>SAP highly recommends to use the latest support package for a release to start a conversion project with. It will massively reduce the effort for notes implementation and we cannot guarantee that all required notes can be imported from other areas.<em> </em>Nevertheless, the Transfer Cockpit was made available via SAP Notes for the following support packages.</p>\n<p><em>******* Pre-Checks (General Available)</em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 25 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 07 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td>SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>******* In-Place conversion</em></p>\n<p>SAP BW 7.50<strong> minimal </strong>requirement SP 05 ,<strong> recommended </strong>SP 12 and higher.</p>\n<p>An in-place conversion of SAP BW <strong>7.51</strong> or higher to SAP BW/4HANA 1.0 is <strong>not</strong> possible (since SAP BW/4HANA 1.0 is based on SAP Basis <strong>7.50</strong>).</p>\n<p>Starting from Release SAP BW <strong>7.50 SP16</strong> the conversion is only to SAP BW/4HANA <strong>2.0 SP&gt;=04</strong> possible!</p>\n<p>SAP highly recommends to update the systems before starting the conversion process. Support Package Implementation is possible until B4H Mode.</p>\n<p>Other components relevant for SAP BW Systems used for InPlace Conversion</p>\n<ul>\n<li>SAP HANA 1.0 SPS 12 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong>)</li>\n<li>SAP HANA 2.0 SPS 36 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>)</li>\n<li>SAP Business Content 7.57 SP11 and higher</li>\n<li>SPAM version 72 and higher</li>\n<li>SAP UI 7.52 or lower (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong> . SAP BW/4HANA 1.0 does <strong>not</strong> support SAP_UI 7.53. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n<li>SAP UI 7.53 (Possible only by Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n</ul>\n<p> </p>\n<p><em>*******. Remote conversion </em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong>Exceptional usage with increased manual implementation effort</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* Shell conversion</em></em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release </strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong> <strong>Exceptional usage with increased manual implementation effort</strong></strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 28 and higher</td>\n<td>SP 25</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 10 and higher</td>\n<td>SP 07</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* SAP BW/4HANA required release and support package stack</em></em></p>\n<p><em><em></em></em>SAP BW/4HANA General Information see SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a></p>\n<p>SAP BW/4HANA <strong>1.0</strong></p>\n<p>For In-Place / Shell:<strong> minimal</strong> requirement SP 08, <strong>recommended</strong> - latest released SP!</p>\n<p>For Remote: <strong>minimal</strong> requirement SP 09, <strong>recommended</strong> - latest released SP!</p>\n<p>SAP BW/4HANA <strong>2.0</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 02 (released)</p>\n<p>For more information regading SAP BW/4HANA 2.0 Release Restictions see SAP Note <a href=\"/notes/2733740\" target=\"_blank\">2733740</a>.</p>\n<p>SAP BW/4HANA <strong>2021</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2021 Release Restictions see SAP Note <a href=\"/notes/3100794\" target=\"_blank\">3100794</a>.<a href=\"/notes/2733740\" target=\"_blank\"><br/></a></p>\n<p>SAP BW/4HANA <strong>2023</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2023 Release Restictions see SAP Note <a href=\"/notes/3365187\" target=\"_blank\">3365187</a>.</p>\n<p>For Conversion from SAP_BW 7.50 for HANA to SAP BW/4HANA 2023 (SAP_BASIS 7.58) please check the downloaded Upgrade Export according to SAP Note <a href=\"https://me.sap.com/notes/3454844\" target=\"_blank\">3454844</a>.</p>\n<p><span><em>2.2.2.6 Add-On's</em></span></p>\n<p><span>Please see SAP Note <a href=\"/notes/2189708\" target=\"_blank\">2189708</a> regarding usage of Add-Ons in </span><span>SAP BW/4HANA.</span></p>\n<p> </p>\n<p><em></em><em><strong>2.2.3 Installation</strong></em></p>\n<p><em>2.2.3.1 SAP BW/4HANA Starter Add-On</em></p>\n<p>For the In-Place Conversion, the installation of the SAP BW/4HANA Starter Add-on is required. Please follow the instruction of the Conversion Guide.</p>\n<p>For Shell or Remote Conversion, the SAP BW/4HANA Starter Add-On is not required.</p>\n<p><em>2.2.3.2 SAP BW/4HANA Transfer Cockpit</em></p>\n<p>To install the Transfer Cockpit for a transfer of scenarios (in-place, remote, and shell conversion), a set of SAP Notes needs to be applied to the corresponding systems. Please use the SAP BW Note Analyzer to analyze the system and to install the prerequisites. Create the SAP BW Note Analyzer program in each system for which notes have to be applied. Depending on your use case, the following systems needs to be processed:</p>\n<ul>\n<li><strong>SAP BW</strong> (in-place conversion or sending system for a remote/shell conversion)</li>\n<li><strong>Source systems</strong> connected to SAP BW (in-place and remote/shell conversion)</li>\n<li><strong>SAP BW/4HANA</strong> (post conversoin for in-place; receiving system for a remote/shell conversion)</li>\n</ul>\n<p>Run the SAP BW Note Analyzer in each system with the corresponding XML file.</p>\n<p><em>2.2.3.3 SAP BW Note Analyzer</em></p>\n<p>For a detailed documentation of the SAP BW Note Analyzer, please see <a href=\"https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf\" target=\"_blank\">https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf</a></p>\n<p>Attached to that note you’ll find a text file “Z_SAP_BW_NOTE_ANALYZER.txt”. Save the file on your computer and open it in an editor.</p>\n<p>In the corresponding system, use transaction SE38 to create a report called “Z_SAP_BW_NOTE_ANALYZER”. Copy and paste the content of the above-mentioned text file into the report and activate it.</p>\n<p>Attached to that note, you’ll find several XML files. Save those files that are related to your use-case:</p>\n<p>Please find more detailed informations under:</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874</a>.</p>\n<p>You want to run the pre-checks in the discovery phase (RS_B4HANA_CONVERSION_CONTROL in SAP BW 7.0x and RS_B4HANA_RC from SAP BW 7.30 to 7.50):</p>\n<ul>\n<li>For SAP BW 7.0x, use <em>SAP_BW4HANA_<strong>Pre_Checks</strong>_[last_update].xml</em></li>\n<li>For SAP BW from 7.30 to 7.50, use XML<em> SAP_BW4HANA_<strong>Readiness_Check</strong>_<em>[last_update]</em>.xml</em> from note 2575059. </li>\n</ul>\n<p>You want to transfer scenarios and perform a system conversion in the realization phase:</p>\n<ul>\n<li>For SAP BW 7.50 (all cases),</li>\n<ul>\n<li>if you start now with the installation of the tool, use <em>SAP_BW4HANA_Conversion_<strong>SAP_BW_750</strong>_[last_update].xml </em></li>\n<li>if you have already installed most of the notes before and only want the latest updates, continue to use <em>SAP_BW4HANA_<strong>In-place_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>For all systems connected to SAP BW as a data load source system, use <em><strong>Source_System</strong>_for_SAP_BW4HANA_[last_update].xml</em></li>\n<li>Remote Transfer</li>\n<ul>\n<li>For SAP BW from 7.30 to 7.40 acting as sending system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>Shell Transfer</li>\n<ul>\n<li>For SAP BW from 7.00 to 7.40 acting as sending system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n</ul>\n<p>You have successfully converted the system (In-Place) to SAP BW/4HANA (see section 2.3.2.3 Post-Conversion phase)</p>\n<ul>\n<li>Use <em>SAP_BW4HANA_<strong>In-place_Conversion_(After_System_Conversion)</strong>_[last_update].xml</em></li>\n</ul>\n<p>Execute the SAP BW Note Analyzer in each relevant system. Click on the icon “Load XML file” and chose the system specific XML file.</p>\n<p>Select radio button “Check implementation state against information in XML file” and check checkbox “Download needed SAP Notes”. Select radio button “In background”.</p>\n<p>Run the report. A background process will be started which downloads the required notes.</p>\n<p>When the process finished successfully, execute the report again with a de-selected checkbox “Download needed SAP Notes”.</p>\n<p>A list of notes with its implementation status will be shown. You can now install the missing notes by clicking on the corresponding icon in the list.</p>\n<p><em>2.2.3.4 Transport-Enabled Correction Instructions (TCIs)</em></p>\n<p>When installing the SAP BW/4HANA Transfer Cockpit using the SAP BW Note Analyzer, you will be prompted for SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>, <a href=\"/notes/2371120\" target=\"_blank\">2371120</a>, and <a href=\"/notes/2735300\" target=\"_blank\">2735300</a> to install a TCI.</p>\n<p>For more information about TCI, see SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>.<a href=\"/notes/2358953\" target=\"_blank\"><br/></a></p>\n<p><em>******* DMIS Add-On</em></p>\n<p>For a remote conversion, the DMIS Add-On needs to be installed in SAP BW (sending system) and SAP BW/4HANA (receiving system). Follow the instructions in SAP Note <a href=\"/notes/2513088\" target=\"_blank\">2513088</a>.</p>\n<p> </p>\n<p><strong>2.3 Conversion Process</strong></p>\n<p>For a detailed description of the conversion process, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide</a>. In additional we attached a excel file, \"<em><strong>SAP BW4HANA Conversion - Steps to consider.xlsx</strong></em>\", as a kind of check list/project plan.</p>\n<p>List of required authorization objects for conversion, see attachment \"<em><strong>Authorization_Objects_List_Conversion_2383530.xls</strong></em>\".</p>\n<p>Frequencly ask questions are collected in note <a href=\"/notes/2930058\" target=\"_blank\">2930058</a>.</p>\n<p>Each object can only be converted once. However, it can be collected again if it is used in another scenario. For example, an IOBJ that has been converted can be used in different objects such as ADSO, DSO, CUBE. Due to consistency, these IOBJs are also collected several times, but the metadata can only be transferred once.</p>\n<p><strong>2.4 New Features</strong></p>\n<p>Under link, you can find new developed features.</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910</a>.</p>\n<p>The wiki is updated regularly.</p>", "noteVersion": 420}]}, {"note": "1125108", "noteTitle": "1125108 - Use of obsolete 3.x authorizations in BW 7.x", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the obsolete concept of the 3.x reporting authorizations in an SAP BW 7.x system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>It is technically possible to use the old concept of the BW 3.5 reporting authorizations in Release BW 7.0. However, you should only do this to facilitate the upgrade from 3.x to 7.0 and to correct a schedule. This enables you to carry out the technical upgrade in a first project phase. You can change the authorizations in a second project phase once any possible difficulties with the upgrade have been overcome and the BW 7.0 system is stable.<br/><br/>After you change to the new 7.0 authorization concept, the advantages of the new concept are available. The following are examples: Authorization by navigation attributes, direct maintenance of hierarchy authorizations, improved performance when generating authorizations, full compatibility with the new planning functions, monitoring function, restriction of authorizations by time<br/>For more information, see Note 923176.<br/><br/>The change should take place as soon as possible after the upgrade, at the latest when structural changes to the authorization scenario are required.<br/><strong>Important: </strong>If you use the old 3.5 authorization concept in a BW 7.0 system, SAP does not accept <strong>any responsibility</strong> if errors occur after you make a change.<br/>Such problems are solved with the provision of the new authorization concept.<br/><br/>You can find information regarding the <strong>necessity</strong> of the change to the new 7.0 authorization concept in the following places:</p>\n<ol>1. SAP Note 923176</ol><ol>2. BW 7.0 online documentation in the section \"Data Warehouse Management -&gt; Authorizations -&gt; Analysis Authorizations: support will no longer be provided for the old concept.\"</ol><ol>3. NetWeaver Release Information: \"Analysis authorizations for BW Reporting (New)\".</ol><ol>4. The dialog box that is displayed each time you call obsolete transaction RSSM.</ol>\n<p><br/>As of SAP BW 7.3, the old BW 3.5 authorization concept is technically no longer available.<br/><br/></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>As soon as you have completed the technical upgrade successfully, the authorization concept should also be updated to the status 7.0.<br/>SAP also provides a consulting package here:<br/><br/><strong>BI authorization migration service</strong><br/>Is a complete package for planning and executing the BI authorization migration and includes</p>\n<ul>\n<li>Detailed planning of the migration including system analysis, concept adjustment, and project planning</li>\n</ul>\n<ul>\n<li>Migration of the old BW reporting authorizations to the new BW 7.0 analysis authorizations</li>\n</ul>\n<ul>\n<li>Test and GoLive support</li>\n</ul>\n<p><br/>For more detailed information, contact the SAP Business Technology Factory at <span><EMAIL></span>.</p></div>", "noteVersion": 9, "refer_note": [{"note": "923176", "noteTitle": "923176 - Support situation authorization management BI70/NW2004s", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This note deals with the upgrade and support of reporting authorizations.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Upgrade and support of reporting authorizations</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note deals with the upgrade and support of reporting authorizations.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>           New features<br/><br/>As of SAP NetWeaver 2004s, a completely new concept for analysis authorizations will be used in BI, based only on authorizations as elementary objects. The previous authorization concept (called reporting authorizations) will be completely replaced. We will be moving away from the SAP authorization concept of authorization objects. The new authorizations can now include any authorization-relevant characteristics and treat single values, intervals and hierarchy authorizations the same. Navigation attributes can now also be flagged as authorization relevant in the attribute maintenance for characteristics and can be transferred into authorizations as characteristics.<br/>Furthermore, the restriction to ten characteristics and the name restriction for InfoObjects to ten characters are no longer valid.<br/><br/>The activation of authorizations by InfoProvider is no longer required. Instead, all authorization-relevant characteristics are checked. Beyond that, there are three special characteristics for the InfoProvider, the activity, and the reconceived authorization characteristic validity, which specifies the validity period of an authorization. The authorization of an activity, such as Write, is set for an InfoProvider using InfoProvider. The validity period of an authorization is set using Validity. Patterns and open time intervals provide a variety of options, such as creating authorizations that are valid periodically.<br/><br/>The characteristic for InfoProvider maps the structure of the InfoProvider store in the Data Warehousing Workbench with its master data and the hierarchy characteristic for InfoArea. In this way it is also possible to authorize entire InfoAreas.<br/><br/>The authorizations are available as virtual master data for the characteristic 0TCTAUTH and can also be grouped hierarchically, for example to create thematic or role-based arrangements.<br/><br/>The authorization checks on authorization objects for hierarchies and InfoProviders that also had to be maintained until now are no longer required for reporting analysis and have thus been removed from the analysis, as opposed to back-end management. This means that there is no longer a difference between hierarchy authorization and hierarchy node authorization in the query.<br/><br/>                    Managing analysis authorizations<br/><br/>There is a new and better integrated maintenance transaction, from which all functions for managing analysis authorizations can be reached.<br/>All activities for managing components of the analysis authorization system are maintained with authorizations for the new S_RSEC authorization object, which covers all relevant objects with namespace authorizations for specific activities.<br/><br/>The maintenance transactions have been completely redesigned, made accessible and customized to typical users. Closer integration enables quicker administration and better control of the relevant objects than was possible previously.<br/><br/>There is a separate infrastructure for maintaining the authorizations and assigning them to users. These replace the standard transactions for user maintenance from SAP NetWeaver. It is not absolutely necessary to assign authorizations to roles. This can also be achieved with a connection to the SAP role concept. With a special authorization object for role connection, the new authorizations can be assigned using role maintenance.<br/><br/>The function for generating authorizations was adapted and enhanced to include the option of loading medium and long texts as well. It is no longer necessary to select authorization objects. The authorizations are generated directly from the entries in special DataStore objects. In general, the adjustments are not very extensive.<br/><br/>           Checking and monitoring analysis authorizations<br/><br/>To improve revision capabilities, a complete change recording of authorizations and assignments to users was created. These changes can be analyzed using queries on RemoteProviders and restricted with analysis authorizations.<br/><br/>In addition, there is a new tool for troubleshooting that replaces the old authorization log. In addition, there is a new tool for troubleshooting that replaces the old authorization log. It uses the HTML format and can be saved and printed and it is stored persistently on the database.<br/><br/>The log for generating authorizations was improved with regard to the readability of messages.<br/><br/>It is possible to execute certain actions in connection with the analysis as other users (as a test). This is protected by password.<br/><br/>           Migration<br/><br/>The old SAP BW 3.x authorizations concept will continue to exist in SAP NetWeaver 2004s for compatibility reasons, but it will be removed completely in the next release. You should no longer use features to be discontinued in new implementations. SAP recommends that you change to the new concept, which is also the default setting.<br/><br/>To make the transition easier when you upgrade to NetWeaver 2004s,<br/>the old source code was retained and integrated as much as possible.<br/>This should allow you to use the old authorizations concept more or less as before. There is a switch that allows you to switch back to the old authorization concept in an emergency.<br/><br/>The new concept is not completely compatible with the old concept that was based on authorization objects.<br/>For this reason, there is a migration help that can carry out many steps semi-automatically. However, this may take some time which can be reduced using the migration option.<br/><br/>In addition, features that do not yet exist in BW3.x and that do not or only partly work in the old authorization concept cannot be supported.  An example is integrated planning, which is not designed for old authorizations and which may behave unexpectedly or inconsistently in places.<br/><br/>SAP Notes and Support Packages have been provided to correct certain security problems; however, these may require a reconfiguration.<br/>Therefore, if you upgrade from a 3.x Support Package level that does not correspond to the general support strategy (see Note 375631), we cannot guarantee that the authorization configuration still works. In this case, you may have to make certain adjustments.<br/>This also applies if you upgrade from BW2.x to BI7.x.<br/><br/>For general security reasons, we recommend that you plan the upgrade in advance (before the GoLive) and that you do so thoroughly to prevent any security problems.<br/>SAP provides a consulting package here - the BI Authorization Migration Service:<br/><br/><b>BI Authorization Migration Service</b><br/>Is a complete package for planning and executing the BI authorization migration and includes</p> <ul><li>Detailed planning of the migration including system analysis, concept adjustment, and project planning</li></ul> <ul><li>Migration of the old BW reporting authorizations to the new BW 7.0 analysis authorizations</li></ul> <ul><li>Test and GoLive support</li></ul> <p><br/>For more detailed information, contact the SAP Business Technology Factory at <u><EMAIL></u><br/><br/>           Further information<br/><br/> Release Notes (German):<br/>http://help.sap.com/saphelp_nw04s/helpdata/de/80/D71042F664E22CE10000000A1550B0/content.htm<br/><br/> Release Notes (English):<br/>http://help.sap.com/saphelp_nw04s/helpdata/en/80/D71042F664E22CE10000000A1550B0/content.htm<br/><br/> German documentation:<br/> http://help.sap.com/saphelp_nw04s/helpdata/de/66/019441b8972e7be10000000a1550b0/frameset.htm<br/><br/> English documentation<br/> http://help.sap.com/saphelp_nw04s/helpdata/en/66/019441b8972e7be10000000a1550b0/frameset.htm<br/><br/>FAQ:<br/> Consulting Note 820183 provides answers to some frequently asked questions.<br/><br/>A further forum for questions and exchange with other customers during the ramp up phase of SAP NetWeaver 2004s in SDN:<br/><br/>https://www.sdn.sap.com/irj/sdn/forum?forumID=154&amp;start=0<br/><br/>Also see the FAQs on SAP Service Marketplace alias /bifaq!<br/>http://service.sap.com/bifaq --&gt; NetWeaver 2004s<br/><br/><br/></p></div>", "noteVersion": 6}, {"note": "1412800", "noteTitle": "1412800 - MDX and BW authorizations", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You notice different behavior when a use with SAP_ALL rights or a restricted user logs on to the BW system. <br/><br/>There are authorization problems the MDX interface is used for access.  <br/><br/>In an SAP BW Support message, you are asked for an analysis of the various authorizations and the recording of traces. </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Analysis authorizations, RSECADMIN, ST01, S_RS_AUTH, S_RFC, RSSM, EYE007, EYE 007, BRAIN804, BRAIN 804</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The BW system uses basic authorizations and has a user-defined authorization concept that is implemented on a technically independent basis.  The BW system recognizes two authorization concepts:  Standard authorizations and analysis authorizations. <br/><br/>It is not clear which traces for which authorizations are relevant for which problem.  It is also not clear how the traces are used. </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><br/><strong>Most problems can be solved with a precise analysis of the correct trace.  </strong><br/><br/>The various authorizations that can have an influence are listed below and which trace helps for the analysis of authorization problems is mentioned briefly. <br/>The actual traces are addressed lastly in detail. </p>\n<p><strong><span>A. Authorization concept in SAP BW system</span></strong><br/> <strong>1) Basic authorizations</strong></p>\n<p><br/>Various authorization objects are checked when logging on to the BW system.  Messages about missing authorizations are not passed on completely and can often only be determined via the system trace in transaction ST01. <br/>Note 23342 contains examples for error messages at this point. <br/><br/>For example: The MDX BAPIs are called via Remote Function Call (RFC).  As a result, the authorization object S_RFC is checked for various function groups.  If one of the called function groups, such as RSOB (for example) was not authorized, the MDX interface of the BW system cannot be called by the external user. <br/><br/>An overview or a description of the various authorization objects can be found in transaction SU21. <br/><br/>Trace:  System trace in transaction ST01</p>\n<p><strong>2) BW standard authorizations</strong></p>\n<p><br/>The BW standard authorizations are usually configured to protect functions such as \"Change/Display\" or entire objects such as \"Queries/InfoProvider/DataSources...\". <br/><br/>The BW standard authorizations are technically implemented as basic authorizations.  For this reason, an authorization problem is also to be analyzed here only via the ST01 system trace.  You can find an overview of the BW standard authorization objects in transaction SU21 in the object class RS. <br/><br/>For example: You can use the BW-specific authorization object S_RS_COMP to determine whether a user has access to a specific query or a specific InfoProvider.  If a user only has authorization for queries that begin with ZSAP*, no queries that begin with another name ID, such as ZBO* (for example) are offered via the BAPI BAPI_MDPROVIDER_GET_CUBES. <br/><br/>Trace: System trace in transaction ST01<br/><br/>Special feature: In the ST01 authorization trace, an incorrect check occurs on the authorization object S_RS_AUTH. This indicates a problem with the analysis authorizations and you only find the exact cause via the RSECADMIN authorization log. </p>\n<p><strong>3) BW 7.x analysis authorizations</strong></p>\n<p><br/>You can configure the BW analysis authorizations to restrict access to the data in the InfoProvider.  As a result, the same query object can return different data for two users with different analysis authorizations. <br/>The analysis authorizations are managed using transaction RSECADMIN.  This transaction also provides the RSECADMIN authorization log for the analysis of problems with the analysis authorizations. <br/><br/>For example: A user accesses a query that uses a variable that is filled by authorizations.  This user sees a smaller and more restricted dataset (for example, only one country or one division) than a user that has the SAP_ALL profile. <br/><br/>Trace:  RSECADMIN - authorization log <br/>Simulation: Transaction RSUDO provides the option to simulate the execution of MDX statements and queries with a restricted user and to record this log. </p>\n<p><strong>4) Old BW 3.2 reporting authorizations (obsolete as of Release 7.0) </strong></p>\n<p><br/>Releases that are earlier than BW 7.0 contain the BW reporting authorizations to restrict access to the data.  These were technically configured as RSR authorization objects.  The maintenance usually occurred in transaction RSSM.  In transaction RSCUSTV23, you can check whether this old authorization concept is still active in your 7.X system; it is no longer supported (Note:  1125108 and 923176).<br/>An overview with tips and tricks for this RSR authorization concept can be found in SAP Note: 921820.<br/><br/>Example: A user accesses a query that uses a variable that is filled by authorizations.  This user sees a smaller and more restricted dataset (for example, only one country or one division) than a user that has the SAP_ALL profile. <br/><br/>Traces:  RSSMQ authorization log <br/><br/></p>\n<p><strong><span>B. Authorization traces in SAP BW system</span></strong><br/> <strong>1) ST01 - Trace</strong></p>\n<p># Call transaction ST01. <br/># Select the \"Authorization check\" checkbox.<br/># Use the \"General Filters\" button to restrict access to users that are connected with the BW system. <br/># Activate the trace by choosing \"Trace on\". <br/># Now execute the report in the non-BW system. <br/># After the error message is displayed, deactivate the user again in transaction ST01 by choosing \"Trace off\". <br/># Use the \"Analysis\" button to navigate to the recorded records for your user. These can also be exported to Microsoft Excel or as a text file. <br/><br/>For more information, see Note 1359226.<br/><br/>Provide the time, the restricted user, and (if necessary) the exported result of the trace in the message for BW Support. </p>\n<p><strong>2) RSECADMIN - authorization log </strong></p>\n<p><br/>Note 1234567 contains a very detailed description of the operation, the setup, and the analysis of the authorization log. <br/><br/>Provide the log number, the restricted user, and (if necessary) the exported result of the trace in the message for BW Support. <br/><br/></p>\n<p><strong>3) RSSM - authorization log </strong></p>\n<p><br/>Note 790323 contains a very detailed description of the operation, the setup, and the analysis of the reporting authorization log. <br/><br/>Provide the log number, the restricted user, and (if necessary) the exported result of the trace in the message for BW Support. </p></div>", "noteVersion": 3}, {"note": "2478384", "noteTitle": "2478384 - BW4SL & BWbridgeSL - Reporting Authorizations", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use SAP BW Reporting Authorizations (3.x format) and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$REPAUTH</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP BW Reporting Authorization (3.x format) are not available in SAP BW/4HANA and SAP Datasphere, SAP BW bridge. There is no analysis available, if the Reporting Authorizations are used or not.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>Target SAP BW/4HANA:</strong></span></p>\n<p>Reporting authorizations are obsolete since SAP BW 7.3. If you are converting from lower SAP BW releases to SAP BW/4HANA and are still using reporting authorizations, you have to switch to Analysis Authorizations.</p>\n<p><span>Related Information</span></p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.5.7/en-US/43fc4c7387e1025de10000000a1553f7.html\" target=\"_blank\">Reporting Authorizations --&gt; Analysis Authorizations</a></li>\n<li><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.4/en-US/4c65fcd82f841f3ce10000000a42189c.html\" target=\"_blank\">Analysis Authorizations</a></li>\n<li><a href=\"https://help.sap.com/saphelp_nw75/helpdata/en/ad/8f7842fdb70f53e10000000a155106/frameset.htm\" target=\"_blank\">Migration of Reporting Authorizations to the New Concept</a></li>\n</ul>\n<p><span><strong>Target SAP Datasphere, SAP BW bridge:</strong></span></p>\n<p>The transfer from SAP BW to SAP Datasphere starts with release 7.3. On this release reporting authorization are already obsolete. In addition SAP Datasphere, SAP BW bridge acts a an staging layer and does not support OLAP engine and functionality dependent on the OLAP engine. Therefore, there is no option to migrate SAP BW Reporting Authorization.</p>", "noteVersion": 5}, {"note": "1240660", "noteTitle": "1240660 - Error generating authorizations: Inconsistency", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Initial symptom: A user does not have enough reporting authorization. For example, a query returns \"No authorization\".<br/><br/>Actual error:<br/>When you generate reporting authorizations (in transaction RSSM), the system issues error 5@ 089: \"Authorization &amp;1 for object &amp;2 is inconsistent\". As a result, not all authorizations are generated.<br/>The generation terminates with this error message.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Generation<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The error occurs when you generate reporting authorizations.<br/>As a follow-on error, users have fewer authorizations than expected.<br/><br/>Important:<br/>This error may <b>only</b>occur if, in the BW system, you are using the SAP Basis<br/>from one of the following releases:<br/>SAP Basis 6.20 Support Package 63 (SAPKB62063)<br/>SAP Basis 6.40 Support Package 21 (SAPKB64021)<br/>SAP Basis 7.00 Support Package 17 (SAPKB70017)<br/>SAP Basis 7.01 Support Package 12 (SAPKB70102)<br/>SAP Basis 7.10 Support Package 10 (SAPKB71007)<br/>SAP Basis 7.11 Support Package 01 (SAPKB71101)<br/><br/><b>or:</b><br/>You have implemented Basis Note 841612.<br/><br/>The error (termination) only occurs if the generation error was already previously reported in the log (but without a termination).<br/><br/>Information regarding validity in Release 7.0:<br/>This error may also occur in Release BI 7.00, since the function to generate the old 3.5 reporting authorizations can still be used. However, support for this by SAP is very limited. For more information, see note 1125108.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>1. Implement Basis Note 1237517.<br/>2. Following this, implement the corrections contained in this note.<br/><br/><b>Important:</b><br/>Regardless of the Support Package level of the BW system, you must <b>only </b>implement these corrections if the SAP Basis conditions mentioned above are fulfilled.<br/></p></div>", "noteVersion": 4}, {"note": "61726", "noteTitle": "61726 - RSSM: No profile assignment if Central User Administration", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In a Business Warehouse (BW) system, you use transaction RSSM or the report RSSB_GENERATE_AUTHORIZATIONS to automatically create authorization profiles from ODS (Operational Data Store) objects and assign these profiles to users ( Note: These are manual profiles that are maintained using transaction SU02). User assignment fails if the BW system is part of a Central User Administration. In this case, the profile is created without user assignment.<br/>If the BW system is not integrated in a Central User Administration, this error does not occur.<br/><br/>Important:<br/>If you use the new analysis authorizations that are available as of BW Release 7.00, this note is not relevant for you.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RSSB_AUTH_GEN_FROM_INFOPROV<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Up to the correction contained in Note 841612, the profile assignment was always executed, independent of whether the BW system is part of a Central User Administration (CUA) or not. If it was, an inconsistency occurred that had to be corrected by retransferring the profile assignments in transaction SCUG.<br/>After you implement the corrections mentioned above, the system uses the regular interface BAPI_USER_PROFILES_ASSIGN for the profile assignment. However, if the BW system is part of a Central User Administration, this module does nor work and no profile assignment is created.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Use transaction SNOTE to implement the correction instructions or import the relevant Support Package.<br/></p> <b>General information</b><br/> <p>The corrections contained in this note restore the previous system response:<br/>The system executes the profile assignment only locally in the BW system, but does not automatically transfer it to the central system. You must then carry out the transfer manually using transaction SCUG.<br/><br/>To be able to implement an automatic transfer, a correction in the user management area is not sufficient. An additional extensive adjustment of the BW function would be required.<br/>We have decided not to make this extensive adjustment for the following reasons:</p> <ol>1. This would result in a dependency between corrections of different software components. After implementing the change using the Support Package for SAP_BASIS, you would have to import the relevant Support Package for SAP_BW to avoid runtime errors.<br/>We cannot inform all BW customers about the dependency in time before they import Support Packages.</ol> <ol>2. The analysis authorizations that were introduced with BW Release 7.00 are independent of the classic user management and the classic authorization management, that is, they are also independent of the CUA. We consider the use of the new concept to be binding (see Note 1125108) so that the problem described in this note will become less important.<br/></ol></div>", "noteVersion": 12}, {"note": "1157604", "noteTitle": "1157604 - BRAIN 813 -only allowed to display the hierarchy to level xx", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you execute a query, the system issues the error message:<br/>\"You are only allowed to display the hierarchy to level xx.\" (BRAIN 813) <br/><br/>However, you can display the entire hierarchy.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Customer exit, BRAIN813, BRAIN 813, user exit, variable<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note corrects the following specific error situation:<br/>A user has the following authorization:</p> <ol>1. A hierarchy authorization that grants authorization to a subarea of a hierarchy, but to a limited level only.</ol> <ol>2. However, for the hierarchy-defining characteristic, full authorization ('*') has also been assigned using a customer exit variable.</ol> <p><br/>The correct system behavior would be:  The full authorization (\"asterisk authorization\") overrides all hierarchy authorizations. The user is allowed to see everything.<br/><br/>The following error occurs: The user can see the entire hierarchy, but the system still issues message BRAIN 813. This is incorrect.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>BW 3.0B and BW 3.10 Content</li></ul> <p>           Implement the attached correction instructions. For SAP BW Releases 3.0B and 3.10, no additional Support Packages will be delivered until further notice.</p> <ul><li>BW 3.50</li></ul> <p>           Import Support Package 23 for 3.5 (BW 3.50 Patch 23 or <b>SAPKW35023</b>) into your BW system. The Support Package is available when <b>Note 1146357</b> with the short text \"SAPBWNews BW Support Package 23 NetWeaver 04 Stack 23\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>BW 7.0</li></ul> <p>           <b>Caution:</b> The old authorization concept should only be used transitionally in Release BI 7.0. You must also read Note 1125108.<br/>           Import Support Package 18 for 7.0 (BW 7.0 Patch 18 or <b> SAPKW70018</b>) into your BW system. The Support Package is available when <b>Note 1136882</b> \"SAPBWNews BW 7.0 Support Package 18\", which describes this Support Package in more detail, is released for customers.<br/><br/>In urgent cases, you can implement the correction instructions<br/>as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 2}]}, {"note": "964905", "noteTitle": "964905 - New concepts and generation of analysis authorizations", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>New concepts and generation of analysis authorizations</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RSECADMIN, 0TCA_DS01</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have upgraded from Release SAP BW 3.x to NetWeaver 2004s BI or higher (as of Version BI 7.0).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>In the old authorization concept for reporting authorizations, authorizations were created in transaction RSSM. An additional option was authorization generation, also in transaction RSSM. (Refer to the documentation.)<br/>First, you selected the predefined authorization objects for which authorizations were to be generated, and the DataStore objects (formerly ODS objects) of the corresponding authorization scenarios.<br/>During generation, the DataStore objects were first loaded with authorization values or authorized hierarchy nodes for the InfoObjects. This loading process (using the extractor from R/3 systems or from files , for example) included the actual logic of the authorization assignment , as well as the assignment to users.<br/>It was possible, by defining authorization objects, to select more InfoObjects as authorization-relevant than were checked in the subsequent queries because the only authorization objects checked were those contained in the InfoObjects from the authorization objects that were selected as relevant for the InfoProvider in question.<br/>This process had some disadvantages, which were eliminated with NetWeaver 2004s. The system is now more restrictive. The generation, which did not previously check the relevance of authorizations for InfoProviders, may need to be adjusted. This is particularly relevant for the InfoObjects 0TCAIPROV (InfoProvider), 0TCAACTVT (activity), and 0TCAVALID (validity of an authorization), which are now always checked as required characteristics.<br/>The adjustment relating to these characteristics is only required during filling of the DataStore object if the characteristics play a role in the generation of authorizations, that is, if the relevant InfoProviders, activities and validity periods  have to be specified for each authorization. This is often not the case, and standard roles or standard authorizations can control access to the InfoProviders, or at least to activities and validity. Therefore, it would be more useful in many cases to extend the roles that control the execution authorizations for queries with authorization object S_RS_COMP with general authorizations for the three characteristics specified above (using S_RS_AUTH), so that they do not have to be generated for each individual authorization. In this case, no changes to the generation procedure are required.<br/><br/>However, it is much simpler to make adjustments to the generation or, more precisely, to the filling of the DataStore object, than it is to perform a full migration of old authorizations, for example. Using the authorization generation has advantages for the transition from the old to the new analysis authorization concept.<br/><br/>In general, it is to be noted that the InfoObject or the 0TCTAUTH column in the generation DataStore objects has become more important if one authorization cannot be clearly distinguished from another, for example by a user name. In this case, it must be filled with a real name or a number. That option was also possible in BW 3.x but was often overlooked.<br/><br/>Some questions that could arise in this context are listed below:<br/></p> <ol>1. General question: If I generate authorizations from DataStore objects (formerly ODS objects), what should I do?<br/><br/>Recommended solutions:</ol> <p>                    a) Do not change the generation scenario, provided that you do not need to define permitted entries for the required characteristics specified above for each individual authorization. To avoid this, you can instead assign authorizations for these three characteristics separately and assign them to all users (or to different user groups).<br/>                    b) The additional special characteristics that must also be authorized ( 0TCAIPROV, 0TCAACTVT, and 0TCAVALID), and which were not previously included in authorization objects, are also generated. This means that additional entries (rows) must be created in the DataStore object. The same applies if key figures with 0TCAKYFNM are used for control (and these special characteristics are assigned separately (see point 3 below)). Depending on the specifics of each individual case, you may need to adjust the extractor of the authorizations or to enhance the file or make adjustments in the transfer update rules or the transformations during loading into the DataStore object.</p> <ol>2. What can I do about a characteristic M1 in an InfoProvider IP1 that is authorization-relevant but which was not previously checked in any InfoCube?</ol> <p>The recommended procedure is as follows:<br/>Delete the \"authorization-relevant\" property if it does not have to be checked at any point. There is no reason to select characteristics as authorization-relevant as a precautionary measure because you can do this at any stage if it becomes necessary.<br/><br/>Other options:</p> <ul><li>If you only want this characteristic to be checked for certain contexts, a row must be inserted for the characteristic in the DataStore object with full authorization (star, *)  during generation. With manual maintenance, insert the characteristic with full authorization in the authorizations .</li></ul> <p>or</p> <ul><li>Create a standard authorization as shown below.<br/><br/>Characteristic Value<br/>M1  *<br/><br/>Assign this authorization to all users that are directly affected, or to a standard role.</li></ul> <p>or</p> <ul><li>You could also create the following type of standard authorization:<br/><br/>Characteristic Value<br/>M1   *<br/>0TCAIPROV  IP1<br/>0TCAACTVT  03<br/>0TCAVALID  *<br/><br/>Assign this to all users, provided that you only want the effect (\"not authorization-relevant\") to apply for the special InfoProvider IP1. Otherwise, refer to point 1 above.</li></ul> <ol>3. Authorization object X contains characteristic I, which is not in authorization object Y but is now checked for InfoProvider IP2 (previously, Y but not X was assigned to IP2).</ol> <p>You should consider why different authorizations exist for the same characteristics in different InfoProviders, that is, why the InfoProvider is used as the criterion for differentiation.<br/>What distinguishes one InfoProvider from another, and what is it that is required for a differentiated authorization assignment? The answer is often key figures, which means that this case can be deleted if key figures are used for authorization assignment (with 0TCAKYFNM).<br/>Otherwise:<br/>The InfoProvider authorization for 0TCAIPROV must be assigned again explicitly, which you can do in the authorization directly in the new system.<br/><br/>In urgent cases, you can use the correction instructions.<br/>To provide information in advance, the notes mentioned above may be available before the Support Packages are released. However, the short texts still contain the words \"preliminary version\" in this case.</p></div>", "noteVersion": 3, "refer_note": [{"note": "914303", "noteTitle": "914303 - SAPBWNews NW BW 7.00 ABAP SP9", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Support Package 09 for BI Release 7.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAPBINEWS, SAPBWNEWS, Support Packages for 7.0, BW 7.0, BW 7.00, BW Patches, BI, BI 7.00, NetWeaver 7.0, NW2004s, 2004s</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This note contains the SAPBINews for Support Package 09 for SAP NetWeaver 7.0 BI ABAP. It contains a list of notes that describe BI-relevant corrections and enhancements in BI Support Package 09 (part of SAP NetWeaver 7.0 Support Package Stack 09). This note will be updated when other notes are added. <br/><br/>The information is divided into the following areas:</p>\n<ul>\n<li><strong>Manual actions that may be necessary:</strong></li>\n</ul>\n<ul>\n<ul>\n<li>Factors you must take into account when you import the Support Package</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Errors that may occur after you import the Support Package</li>\n</ul>\n</ul>\n<ul>\n<li><strong>For general information:</strong></li>\n</ul>\n<ul>\n<ul>\n<li>This Support Package corrects the following errors </li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Enhancements contained in this Support Package</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>See the release and information notes.</li>\n</ul>\n</ul>\n<p><strong>Factors you must take into account when you import the Support Package:</strong></p>\n<ul>\n<ul>\n<li><strong>Note 978148 contains a list of notes that you must refer to after you have imported Support Package 09. Implement these notes if necessary.</strong></li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Following the import of a Support Package, <strong>post-implementation steps</strong> are normally <strong>required</strong> in transaction SNOTE. SAP Notes that have already been implemented can become inconsistent and can cause <strong>functions that work incorrectly or syntax errors</strong>. Call transaction <strong>SNOTE</strong> and <strong>reimplement</strong> the notes that have become inconsistent. When you have done this, your BW system is operative and consistent again.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>You should implement Notes 932065 and 948389 before you use transaction SNOTE to implement advance corrections. Otherwise, problems may occur when you try to deimplement notes again.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>If documents have already been migrated to the portal, you may have to repeat the migration. For more information, see Note 950877.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>The BI search requires the TREX Server as of 7.0 Support Package 06 or higher. If the search engine is not fully installed, you can still use the standard search that the system carries out in the database. However, as a result, the search takes longer, it is case-sensitive and it does not support the search for attributes.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>A syntax error may occur in the 'CL_RSTRAN_FOBU_APPL' class or in the 'CL_RSTRAN*'.classes. For more information, see Note 982474.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When you call the transactions for process chain maintenance, InfoSet maintenance and data flow display, short dump MESSAGE_TYPE_X occurs. This error is corrected with SAP NetWeaver 2004s BI Front End Patch 903. For more information, see Notes 946481 and 921790.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>If you have questions about downloading Support Package stacks, see Note 911032 'FAQ - SAP Support Package Stack Download'.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When data is written or activated or when a DataStore object is activated, SQL errors and runtime errors occur. For more information, see Note 1012607.</li>\n</ul>\n</ul>\n<p><strong>Errors that may occur after you import the Support Package:</strong></p>\n<ul>\n<ul>\n<li>After you have implemented the advance corrections contained in Note 996693, a syntax error may occur in the SAPLRSDG_MDOB program. For more information, see Note 1008009.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Delta DTPs retrieve requests twice from the source. In the process chain log, the system issues message RSM1 642: Request &amp;1(&amp;2) is duplicated - no activation; target-RNR &amp;3(&amp;4).    For more information, see Note 971898.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>A transport error occurs during the DTP (Data Transfer Process). For more information, see Note 966234.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>The DTP is deleted after the activation.  For more information, see Note 966233.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>The system issues a message telling you to choose another name (reserved namespace) if you execute a query or a template delivered by SAP. For more information, see Note 965763.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When you delete a green link, the red link of the same process is also deleted. For more information, see Note 966996.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When you search using 'BI Search', the system issues an error message telling you that the TREX engine is not fully installed and to check the configuration. For more information, see Note 966132.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>After the transport and Content activation for process chains, DTPs and InfoPackages appear more than once in the chain. For more information, see Note 951390.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When you remove reusable components in the target system, this is incorrectly interpreted as a deletion. For more information, see Note 977045.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When you execute a query, it results in termination X299(BRAIN) System error: Program SAPLRRK0; FORM NACHLESEN_INIT_NODE-01-'. For more information, see Note 977912.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Source requests are extracted several times from the Persistent Staging Area (PSA) of the DataSource. See Note 980259 for information about prerequisites.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When you transport DTPs or when you create or install Content, the DTP descriptions are not adjusted in the target system. For more information, see Note 999007.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Termination BRAIN X299 occurs when you execute or generate a query. 'System error in program SAPLRRI2 and form KFB_FUELLEN_WGR-01-1'. For more information, see Note 1005775.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When you use a data transfer process to load master data, there is no option for handling duplicate records in a package. This option is available only as of Support Package 10. For more information, see Note 978960.</li>\n</ul>\n</ul>\n<p><strong>This Support Package corrects the following errors: </strong></p>\n<ul>\n<li>In the <strong>end user technology</strong> area:</li>\n</ul>\n<ul>\n<ul>\n<li>A runtime error occurs in the Administration Workbench due to multiple entries. For more information, see Note 945690.</li>\n</ul>\n</ul>\n<ul>\n<li>In the <strong>OLAP technology</strong> area:</li>\n</ul>\n<ul>\n<ul>\n<li>Incorrect time selection in non-cumulative queries. For more information, see Note 955029.</li>\n</ul>\n</ul>\n<ul>\n<li>In the <strong>Warehouse Management</strong> area:</li>\n</ul>\n<ul>\n<ul>\n<li>The ChangeLog table of the DataStore is created without partitions when you activate or when you delete the data completely. For more information, see Note 941565.</li>\n</ul>\n</ul>\n<p><br/><br/><strong>Enhancements delivered with this Support Package:</strong></p>\n<ul>\n<li>In the <strong>Warehouse Management</strong> area:</li>\n</ul>\n<ul>\n<ul>\n<li>Remodeling tool: You can transport remodeling rules.</li>\n</ul>\n</ul></div>", "noteVersion": 21}]}, {"note": "820183", "noteTitle": "820183 - New authorization concept in BI", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Authorizations for data analysis<br/><br/>There is a new authorization system for authorizing query data in<br/>transaction LISTCUBE and the RSDRI modules for reading data in the BI system.</p> <ol>1. During an upgrade, problems may occur for users with SAP_ALL authorizations.</ol> <ol>2. \"No authorization\" messages (and variants of this message) are issued for users with limited authorizations for reporting.</ol> <ol>3. Allegedly, the new authorization concept requires S_RS_AUTH with authorization 0BI_ALL \"for all\".</ol> <ol>4. There is an entry in the log that implies a check is carried out for NOT IN A, although value A is authorized (or several values, intervals or selection options).</ol> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Authorizations, reporting, analysis, query authorizations, insufficient authorization, 0BI_ALL, SU53, ST01<br/><PERSON>AIN 804, BRAIN 819, <PERSON>A<PERSON> 816, <PERSON><PERSON><PERSON> 007, <PERSON><PERSON><PERSON> 016, <PERSON><PERSON><PERSON> 019<br/>BRAIN804, BRAIN819, BRAIN816, EYE007, EYE016, EYE019, 0BI_ALL, 0TCAKYFNM, 0TCAIPROV, 0TCAACTVT, 0CTAVALID, LISTCUBE, NOT IN<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>- Upgrading to NetWeaver BI 2004s.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>See also the release notes for NetWeaver 2004s BI.<br/>(Transaction SE61: Document RELN name: BW_BEX_70_AUTHORIZE)<br/>Link:<br/>(German) http://help.sap.com/saphelp_nw04s/helpdata/de/80/d71042f664e22ce10000000a1550b0/frameset.htm<br/>(English)<br/>http://help.sap.com/saphelp_nw04s/helpdata/en/80/d71042f664e22ce10000000a1550b0/frameset.htm<br/><br/>The BI authorizations are no longer based on authorization objects; rather they are BI-specific objects.<br/>In addition, a new authorization object called S_RS_AUTH was created. Its entries are reporting authorizations for a user.<br/><br/>Users who have SAP_ALL authorization should be able to execute queries without authorization messages.  For this purpose, an authorization is automatically added for the S_RS_AUTH object into which the *-authorization is inserted. This gives an SAP_ALL user all BI authorizations for reporting, analysis, and in particular, also for the 0BI_ALL authorization, which was artificially created and which permits all data accesses.</p> <ol>1. If \"No authorization\" messages are issued for SAP_ALL-users or users that should see all data, proceed as follows:<br/>Execute the point OBI_ALL or function module RSEC_GENERATE_BI_ALL in transaction RSECADMIN under \"Extras\". The \"Regenerate SAP_ALL\" button in transaction SU21 can be used to update the SAP_ALL profile. (The sequence is irrelevant.)<br/>In certain cases you then need to delete the buffers, you should therefore execute $sync if possible, which deletes ALL buffers.<br/>Activating a characteristic has the same effect because in this case the function module is also executed automatically and the relevant buffers are updated.</ol> <ol>2. If problems occur with restricted users:<br/>Queries for which you actually want restrictions no longer run unless you adjust the authorizations.<br/>The BI authorizations for reporting and analysis include a special authorization called 0BI_ALL, which includes all authorization-relevant characteristics. This authorization is automatically updated when you activate any BI InfoObject. This authorization is also executed when you upgrade after you start transaction RSA1 using XPRA for the first time.<br/>A user who has this authorization can therefore execute all queries. If you do not want to place any restrictions on data access, you can assign the 0BI_ALL authorization to the relevant users. There are a number of options for this, based on the explanations above:</ol> <ol><ol>a) Create a role with authorization object S_RS_AUTH and enter authorization * (asterisk) or 0BI_ALL and assign the role to all relevant users.<br/>You can also adjust the standard roles and profiles in this case. The authorization object should be transported automatically in S_RS_ALL.<br/>Check this or insert the S_RSEC and S_RS_AUTH authorization objects in the respective profile.</ol></ol> <p></p> <ol><ol>b) The following option is particularly useful if you have a small number of individual users: Use transaction RSU01 to assign 0BI_ALL to the users directly.</ol></ol> <p><br/>In each case you must activate the characteristics 0TCAIPROV, 0TCAVALID and 0TCAACTVT and mark them as authorization-relevant.<br/>(However, the system always checks these characteristics).<br/>When you first call the Data Warehouse Workbench (transaction RSA1), these and other characteristics that should be automatically active are copied from the Content and activated. You must flag these characteristics as authorization-relevant manually. They are returned as not authorization-relevant, but you must mark them as authorization-relevant.</p> <ol>3. Miscellaneous:<br/></ol> <ol><ol>a) It is not correct that authorizations are only granted if the user has been assigned 0BI_ALL. People may think this is so because an irrelevant entry appears in transaction SU53 or transaction ST01 informing you of an unsuccessful check on S_RS_AUTH with the value 0BI_ALL.<br/>This entry is of no significance.<br/>Transaction SU53 (ST01) does not contain any entries that are relevant for analysis authorizations.<br/>This entry merely means that the user has restricted authorizations, which may be as intended and is completely normal. This is as it should be and therefore the analysis authorization check does not deny access.<br/><b>The reason for an authorization message usually lies in the fact that S_RS_AUTH requires the authorization 0BI_ALL.</b><br/>The entry is usually generated as a result of a technical optimization: The system checks if the user has 0BI_ALL to speed up processing. The quickest way to do this is using an ABAP command, which checks this authorization and then, unfortunately, leaves this entry in transaction SU53 (ST01).</ol></ol> <ol><ol>b) The 0TCAKYFNM characteristic must always be active. If it is not, the system issues an error message (characteristic 0TCAKYFNM does not exist in Version A. Check the authorizations).<br/>If it is authorization-relevant, it is always checked. If you do not need authorization relevance, deselect the authorization relevance checkbox in InfoObject maintenance (transaction RSD1) on the Business Explorer tab page.<br/></ol></ol> <ol><ol>c) The 0INFOPROV characteristic is part of every query on a MultiProvider. If you do not want the system to check this characteristic, you should deactivate the authorization relevancy. The characteristic must always be active because the 0TCAIPROV characteristic references it.</ol></ol> <ol><ol>d) If there is a selection for NOT IN {A,B, C} in the authorization log, or something similar is checked, this means the following:<br/>Everything is selected in the query without any restrictions. The authorization check takes each of the authorizations in turn and checks whether they are part of the selection. The remainder of the selection, that is, the part that is not yet authorized, is treated in the same way.<br/>If you select '*' for whatever reason and values A,B and C are valid, these values are removed from the selection (intersection). The remainder, that is 'everything except A,B and C', is checked. This remainder, 'everything except A,B and C', is the same as the NOT IN {A,B,C} expression in SQL language. It therefore means all authorized values (intervals and selection options).<br/>If you want to select just one authorized value for display purposes, you can usually use a variable on the characteristic which is filled from the authorizations. The query selection is then restricted to the authorized values.</ol></ol> <ol><ol>e) A semi-automatic migration tool is available. In urgent cases, you can also switch back to the old authorization concept. However, you must have a good reason for doing so. The old concept is still delivered with this release, but it will not be developed further. However, it is supplied by corrections for BW 3.0/3.1 systems and NetWeaver 2004 (BW 3.5) and it is regularly updated. For further information, see Note 923176.</ol></ol> <ol><ol>f) Special case: In the old authorization concept, you could use customer exit variables for the assignment of hierarchy authorizations in roles (and therefore in profiles). In the new concept of the BI analysis authorizations, the artificial 'Hierarchy authorization' objects are no longer available. Therefore, these can no longer be assigned by variables. Normally, however, not the whole hierarchy authorization is variable, rather only the hierarchy node. Therefore, in Release 7.0, you can use customer exit variables as before for this.</ol></ol> <p><br/>Further information is available in the SAP Help Portal in the general documentation for authorizations:<br/><br/>German documentation:<br/>http://help.sap.com/saphelp_nw04s/helpdata/de/66/019441b8972e7be10000000a1550b0/frameset.htm<br/><br/>English documentation<br/>http://help.sap.com/saphelp_nw04s/helpdata/en/66/019441b8972e7be10000000a1550b0/frameset.htm<br/><br/>For general information and information about the support situation in authorizations and during migration, This problem is caused by Note 923176.<br/><br/></p></div>", "noteVersion": 23, "refer_note": [{"note": "949447", "noteTitle": "949447 - mySAP CRM 2005 SP Stack 05", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>**********************************************************************<br/>mySAP CRM 2005: Release and Information Note for mySAP CRM 2005<br/>Support Package Stack 05<br/>**********************************************************************<br/>* This Release- and Information Note (RIN) contains information and<br/>* references to notes in the context of applying Support<br/>* Package Stack 05 for mySAP CRM 2005.<br/>* This note is subject to changes. Make sure to check for<br/>* changes on a regular basis. For your convenience, changes will<br/>* be documented in a section at the end of the note called<br/>* \"Changes made after Release of SP Stack 05\".<br/>***********************************************************************<br/>* Send comments and suggestions regarding the release and<br/>* information note to following e-mail address:<br/>* <EMAIL><br/>**********************************************************************<br/>* Make sure you install new Support Packages regularly to<br/>* prevent problems that have already been corrected from occurring<br/>* in your SAP solution.<br/>* (SAP's Release Strategy: http://service.sap.com/releasestrategy)<br/>**********************************************************************<br/>This note contains a collection of notes that you should apply before an<br/>d after importing Support Package Stack 05.<br/><br/>Download summary for mySAP CRM 2005 Support Package Stacks can be found<br/><br/>in the Service Marketplace at:<br/>www.service.sap.com/sp-stacks -&gt; SP Stack Information &amp; Download<br/>For detailed information about how to apply the SP-stack refer to the NW<br/> 2004s Support Package Stack guide:<br/>www.service.sap.com/sp-stacks -&gt; SP NetWeaver'04<br/>Important remark: first of all, you should have a very clear picture of<br/>the components you use in your system landscape. Needless to say only in<br/>structions and notes for components, which you use, are relevant for<br/>you.<br/><br/>The \"not applicable\" text is used in cases where no support package spec<br/>ific instructions or notes are required for a given technical component<br/>or scenario.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CRM, Customer Relationship Management, Account Management, Channel Manag<br/>ement, Enterprise Service, E-Selling, E-Service, Field Sales, Field<br/>Service, IC WebClient, Internet Sales, Interaction Center, Marketing<br/>Management, Middleware, People Centric CRM, Professional Services,<br/>Sales, Service Resource Planning, SAPKU50005, Support Package, Support<br/>Package Stack, Trade Promotion Management, Workforce Management<br/>Reason and Prerequisites<br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>You should apply all notes, which are listed in the related notes section of this release, and information note (depending, of course, on the components and scenarios you use). These notes are also referred to in the text below.<br/>Read completely this note BEFORE applying SP Stack 05 in your system and follow the instructions.<br/><br/>Intergration with SAP R/3<br/>For qestions regarding the integration of mySAP CRM 2005 and SAP R/3<br/>please refer to the SAP Service Marketplace at: www.service.sap.com/~form/sapnet?_SHORTKEY=00200797470000047531&amp;</p> <ol>1. What to do before applying SP Stack 05:<br/><br/>None</ol> <ol>2. Instructions for Technical Components including mandatory SAP Notes per component<br/><br/>Mandatory Notes: None<br/><br/>Download Area: www.service.sap.com/sp-stacks -&gt; SP Stack Download -&gt; SP Stack mySAP CRM 2005<br/><br/>If you have deployed your CRM 2005 Java Components on the NETWEAVER 2004s Usage types BI-Java or EP you have to download an additional stack.xml file together with the corresponding archives for the respective usage type (EP or BI-Java) from the NETWEAVER 2004s Stack-Download page. The additional stack.xml file has to be renamed and, together with the archives, to be put under directory /usr/sap/trans/EPS/in.<br/>For patching JSPM has to called twice: once for the CRM Stack and once for the NETWEAVER 2004S usage type(EP or BI-Java).<br/>The CRM stack.xml file only covers patching CRM and NETWEAVER components on NETWEAVER 2004s usage type AS-Java.</ol> <ol>3. Specific Instructions for Scenarios</ol> <ol><ol>a) For all Scenarios</ol></ol> <ul><ul><li>959198: NetWeaver 04S kernel patch level for CRM 5.0 SP 5</li></ul></ul> <ol><ol>b) CRM Documentation</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>c) CRM Enterprise Service</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>d) CRM Channel Management</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>e) CRM E-Commerce</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>f) CRM Field Applications</ol></ol> <ul><ul><li>Recommended:<br/>943435: Positionen werden nicht aus Vorgänger übernommen<br/>952032: CRM Mobile Client 5.0 SP05: Runtime framework fixes<br/>960779: Errors in \"Take over Deleted\" during Collision<br/>962254: Error during conntrans receive on mobile client.<br/></li></ul></ul> <ol><ol>g) CRM Interaction Center - WebClient</ol></ol> <ul><ul><li>Mandatory:<br/>None</li></ul></ul> <ul><ul><li>Recommended:<br/>957647: Exception in ERP Sales Order while deleting an item</li></ul></ul> <ol><ol>h) CRM Interaction Center - WinClient</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>i) CRM Enterprise Marketing</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>j) CRM Enterprise Sales</ol></ol> <ul><ul><li>Mandatory:</li></ul></ul> <ul><ul><li>Recommended:</li></ul></ul> <ol><ol>k) Business Partner Management</ol></ol> <ul><ul><li>Recommended:<br/>820183: New authorization concept in BI</li></ul></ul> <ol><ol>l) Product</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>m) People Centric CRM</ol></ol> <ul><li>Recommended Notes:</li></ul> <ul><ul><li>885050: Correction CRM BSP Framework, SP06 (01)</li></ul></ul> <ul><ul><li>887916: Correction CRM BSP Framework, SP06 (2)</li></ul></ul> <ul><ul><li>889142: Correction CRM BSP Framework, SP06 (03)</li></ul></ul> <ul><ul><li>890821: Correction CRM BSP Framework, SP06 (04)</li></ul></ul> <ul><ul><li>890843: Correction CRM BSP Framework, SP06 (05)</li></ul></ul> <ul><ul><li>895343: Correction CRM BSP Framework, SP06 (06)</li></ul></ul> <ul><ul><li>900260: CRM5.0 Tree OIC messages: 849614 and 914460 (SP06)</li></ul></ul> <ul><ul><li>901864: Correction CRM BSP Framework, SP06 (07)</li></ul></ul> <ul><ul><li>902573: Correction CRM BSP Framework, SP06 (08)</li></ul></ul> <ul><ul><li>904866: Correction CRM BSP Framework, SP06 (09)</li></ul></ul> <ul><ul><li>910442: Correction CRM BSP Framework, SP06 (10)</li></ul></ul> <ol><ol>a) Industry  Financial Services</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>b) Industry - Media</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>c) Industry - Utilities</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>d) Industry - Telco</ol></ol> <ul><ul><li>None</li></ul></ul> <ol><ol>e) Industry  Public Services</ol></ol> <ul><ul><li>None</li></ul></ul> <ol>4. Changes made after Release of SP Stack 05<br/></ol> <ol>5. Regarding known limitations please ask your local Ramp Up driver for further assistance</ol> <ul><ul><li>Asia Pacific  Gerald Khor</li></ul></ul> <ul><ul><li>EMEA News   Christophe Bertin</li></ul></ul> <ul><ul><li>EMEA Central  Alain Lutz</li></ul></ul> <ul><ul><li>Japan   Tetsuya Hayashi</li></ul></ul> <ul><ul><li>Latin America  Andrea Maspero</li></ul></ul> <ul><ul><li>North America  Ann Ruhl<br/></li></ul></ul> <ul><ul><li></li></ul></ul></div>", "noteVersion": 4}, {"note": "955990", "noteTitle": "955990 - BI in SAP NetWeaver 7.0: Incompatibilities with SAP BW 3.x", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Business Intelligence (BI) in SAP NetWeaver 7.0 2004s behaves differently from SAP BW 3.x.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Incompatible, changes</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Although SAP always endeavors to transfer customer scenarios created in earlier releases into the latest software version without changes during an upgrade, some manual effort may sometimes be necessary to ensure that an existing scenario can still be used due to incompatible changes.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>This composite SAP Note provides information about all known incompatible changes in SAP NetWeaver 7.0 (2004s).<br/>You must also read the release notes under http://help.sap.com. These notes describe new functions and possible restrictions in detail.<br/>The FAQs under 'http://sdn.sap.com -&gt; Business Intelligence' are another useful source of information.<br/></p> <ul><li>Enterprise Data Warehousing</li></ul> <ul><ul><li>Changes in the Data Warehousing Workbench (previously known as the Admin Workbench): InfoPackage groups, Persistant Staging Area (PSA) tree, reporting agent and so on are only supported in RSA1OLD --&gt; see the relevant release note.</li></ul></ul> <ul><ul><li>Compounding consistency for MultiProviders:   Note 920416</li></ul></ul> <ul><ul><li>Identifying InfoObjects in MultiProviders  Note 1105187</li></ul></ul> <ul><ul><li>Query runtime statistics: The old statistics tables (RSDDSTAT) were replaced by new tables and are no longer filled   &gt; see the relevant release note.</li></ul></ul> <ul><ul><li>Change run:  Note 915515</li></ul></ul> <ul><ul><li>Change run:  The split parallel processes of the change run are always background business transaction category (BTC) processes in SAP NetWeaver 7.0.</li></ul></ul> <ul><ul><li>MOLAP: This Is no longer supported - you can use the BI accelerator as an alternative   &gt; see the relevant note and Note 456931.</li></ul></ul> <ul><ul><li>Authorizations: see Notes  923176 and 820183.</li></ul></ul> <ul><ul><li>Authorization check for MultiProvider: Note 927872</li></ul></ul> <ul><ul><li>VirtualProvider with function module: If hierarchies are supported, master data IDs must also be supported. See the release notes: http://help.sap.com/saphelp_nw2004s/helpdata/en/43/94f069633821b5e10000000a1553f6/frameset.htm</li></ul></ul><ul><ul><li>Compound objects in MultiProviders - corrected behavior with initial values: Note 1009987</li></ul></ul> <ul><ul><li>The input help for DataStore objects can now use the setting 'Only values in InfoProvider', this can lead to performance problems: Note 984229</li></ul></ul> <ul><ul><li>Constant with compound characteristics. Note 1035916</li></ul></ul> <ul><ul><li>DB Connect: Note that there is a new Product Availability Matrix (PAM) for SAP NetWeaver 7.0 and certain platforms (for example Informix) are no longer supported. This also applies to the availability of the DB clients that are required for DB Connect. For more information, see the SAP Netweaver documentation. In certain cases, UD Connect may offer greater platform independence.</li></ul></ul> <ul><ul><li>You cannot selectively delete real-time InfoCubes in the planning mode due to a new caching architechtur in SAP NetWeaver7.0. Note 1016181</li></ul></ul> <ul><li>Enterprise Query, Reporting &amp; Analysis</li></ul> <ul><ul><li>Calculating before the aggregation with Content InfoProviders: Note 921325</li></ul></ul> <ul><ul><li>Changes in the variable screen of the BEx Analyzer:  see Notes  945653 and 924316.</li></ul></ul> <ul><ul><li>When you drilldown to 1CUDIM, the system displays original currency: see Note 962850.</li></ul></ul> <ul><ul><li>Most recent variables 0RS_RQTRA, 0RS_RQMRC and 0RS_RQALL converted to ACTUALDATA: Note 1007722</li></ul></ul> <ul><ul><li>Changes to the behaviour of formula variables with a replacement path before the aggregation. Note 1005772</li></ul></ul> <ul><ul><li>The changed behavior of variables: The affects on the targeted filling of the OLAP cache: Note 1105139.</li></ul></ul> <ul><ul><li>The new BEx Analyzer only allows resulting sets up to 750,000 data cells due to changed memory processing (for additional functions and improved performance). Note 1040454</li></ul></ul> <ul><ul><li>Analysis authorizations: You can no longer use customer exit variables to assign hierarchy authorizations in the new authorization concept. See Note 820183, section 3f.</li></ul></ul> <ul><ul><li>Table view in the query designer: Note 1002271.</li></ul></ul> <ul><ul><li>Calculating with attributes with display hierarchy: Note 856485.</li></ul></ul> <ul><li>Business Planning</li></ul> <ul><ul><li>No incompatible changes are known.</li></ul></ul></div>", "noteVersion": 18}, {"note": "1053989", "noteTitle": "1053989 - <PERSON><PERSON><PERSON> and Patterns [(*),(+)] in Analysis Authorizations", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p><br/>Incorrectly entered characters may result in unexpected behaviour or a 'No authorization' message when executing a query.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>values, interval, pattern, variable, plus, star, asterisk, range, between, RSECADMIN, RSECAUTH, RSEC132, RSEC100, RSEC101, EYE007, EYE 007, Select Range. AUTH_TO_SETOBJECT CX_RSMDS_INPUT_INVALID<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Setting up authorization(s) you have defined intervals or made use of patterns [(*),(+)] that are not permitted for analysis authorizations<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\"></th></tr> <tr><th align=\"LEFT\">General Definitions</th></tr> </table> <p><br/>The required format for normal characteristics is the following:<br/><br/> - Single value: I EQ A (characteristic value =A),<br/><br/> - Interval : I BT A B (A &lt;= characteristic value&lt;= B)<br/><br/> - Pattern : I CP A* (pattern with exactly one wildcard (*) ).<br/><br/> - Aggregation authorization :<br/> Colon character (:). Permits an aggregation on the characteristic.<br/> Written as single value I EQ :<br/></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Patterns</th></tr> </table> <p>Only patterns with * (pattern for arbitrarily many charactersy) are allowed.<br/><br/>For example : entering A*A is not possible.<br/></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Allowed Signs/Operators</th></tr> </table> <p>For characteristics other than 0TCAVALID \"Excluding\" (E) as well as the operators GT, GE, LT, LE are forbidden.<br/><br/>For the validity of authorizations, excluding sign (E) and the relational operators EQ, BT, GT, GE, LT, LE, CP are also permitted; These are converted to actual intervals with interval limits 10000101 and 99991231.<br/></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Exit Variables:</th></tr> </table> <p>Exit variables can be entered beginning with $. (If both the lower limit and the upper limit contain a variable value, these are interpreted as part of an overall variable in authorization processing. They are concatenated into the low value.) The existence and correct type of exit variables is checked. Use the variable button in RSECAUTH to find an appropriate variable.<br/>A single variable has the operator EQ (like I EQ $MYVAR) independent of what will be filled in in the corresponding customer coding.<br/><br/></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Character Sets and Case-Sensitivity</th></tr> </table> <p>Note: Values may be case-sensitive. If required, use the input help (F4) to ensure you have the correct values.<br/><br/>Example: I BT a - A is incorrect because the lower value 'a' comes alphabetically after 'A'.<br/><br/>The order and the content of an interval depends on the character set of the underlying operating system.<br/><br/>Characteristics of type CHAR with length larger than 1: You need to define separate ranges for numbers and letters.<br/><br/>A valid example is A-to-Z or 0-to-9<br/><br/>Do not use a range of type A-to-9. This may result in a 'no authorization' message or in too many displayed data.<br/></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Automatic Corrections</th></tr> </table> <p><br/> In RSECAUTH, in most cases, intervals are corrected by the system, if it can interpret what is meant.<br/><br/><strong> But this does NOT always guarantee a correct interpretation!</strong><br/><br/><br/>Examples for Automatic Correction:<br/><br/>General characteristics:<br/><br/>a) I EQ A B -&gt; I BT A B<br/><br/>b) I EQ A*  -&gt; I CP A*<br/><br/>For the validity of authorizations (characteristic 0TCAVALID):<br/><br/>c) I BT 200+1231 -&gt; I EQ 200+1231 (BT -&gt; EQ)<br/><br/>d) I CP 2001++13  20011+14 -&gt; I BT 2001++13  2001++14   (CP -&gt; BT)<br/><br/>e) I CP 200+1015 -&gt; I EQ 200+1015 (CP-&gt;EQ)<br/><br/>f) I GT 20041231 -&gt; I BT 20050101 99991231<br/><br/><br/>Values, Intervals and patterns are not corrected if it is not clear what the result should be.<br/><br/>Examples of not allowed definitions<br/><br/>a) I CP A*A is rejected.<br/><br/>b) I LE A is rejected (LE is allowed only for 0TCAVALID)<br/><br/>c) I BT A* B* or I CP A* B* is not permitted (use simple patterns instead)<br/><br/>Numeric characteristics of a certain length are cut off to this length and may result in a \"no authorization\" message. E.g.<br/><br/>d) I EQ  000* for a NUMC 3 is not allowed but not checked automatically.<br/><br/>In most cases, when a combination of characters is not permitted a warning is issued and the authorization cannot be saved. These informational messages are also issued by pressing the check button in RSECAUTH.<br/></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><tr><th align=\"LEFT\">Important Points :</th></tr> </table> <p><br/><br/>It is necessary to define the same intervals as the authorization content of the characteristics for the transactional selection or at least select a subset of the authorization.<br/><br/>For instance, the following authorized values for characteristic TEST have been set:<br/><br/>I EQ A<br/>I EQ B<br/>I EQ C<br/>I EQ D<br/><br/>And, in the example, during query runtime the following range is being checked for characteristic TEST:<br/><br/>I BT A and D<br/><br/>In the authorization log you will notice the following :<br/>The first check on B, C will be partially authorized. The second check will look for the potential existing characters within the interval: SQL: TEST &gt; 'A' AND TEST &lt; 'D' AND NOT TEST IN ('B','C'). Meaning that all other values except A,B,C and D will now be checked.<br/>-&gt; see also note 820183 below Miscellaneous.<br/>Since this is a character other values could also exist.<br/><br/>It is therefore, in this example, necessary to authorize an interval for the user, or make sure that during the query runtime only single values are entered.<br/><br/>Please make sure that for the definition of authorized values you are following the rules described in the online help :<br/><br/>http://help.sap.com/saphelp_nw2004s/helpdata/en/52/671631439b11d1896f0000e8322d00/frameset.htm.<br/><br/>Regarding the use of Patterns [(*),(+)], please refer to the below<br/>link under the chapter 'Using the characteristic 0TCAVALID ':<br/><br/>http://help.sap.com/saphelp_nw2004s/helpdata/en/b1/0c9441b8972e7be10000000a1550b0/frameset.htm<br/><br/></p></div></div></div></div></div></div></div></div>", "noteVersion": 8}, {"note": "1261193", "noteTitle": "1261193 - Composite SAP Note : HCM Authorizations Documentation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You would like to have a consolidated view of must read OSS notes when<br/>deealing with HCM Authorizations. This list is not exhaustive and contains useful notes. It is provided as a courtesy.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>HCM Authorizations Security Personnel Administration Personnel Development</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Documentation.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Read the documentation listed in this note.<br/>Attend standard SAP class HR940 - SAP HCM Authorizations for HCM for hands-on experience.<br/><br/>List of related transaction codes for HCM Authorizations (non exhaustive, for information purpose only)<br/><br/>SU01 = User Administration<br/>SU01D = Display User Administration<br/>SU3 = Maintain User profile (incl. parameters)<br/>PFCG = Profile Configurator (as of release 4.6)<br/>SU02 = Profile Creation - Manual process (prior release 4.6)<br/>SU03 = List of authorization objects<br/>SU53 = Authorization checks (displays missing authorization in the profile)<br/>HRUSER = Check user mapping between SAP userid and personnel number (through infotype 0105 - Communication - Subtype 0001)<br/>SUIM = Authorization Reporting Tree<br/>OOAC = Enable AUTSW switches in table T77S0<br/>SE93 = Maintain transaction codes<br/>SE37 = Function modules<br/>OOSP = Display Personnel Development Profiles<br/>SLG1 = Application Troubleshooting<br/>SU56 = Authorization buffer for end user<br/>SU21 = Create specific authorization object<br/><br/>List of related transaction codes for HCM when dealing with authorizations (non exhaustive, for information purpose only)<br/><br/>PPMDT = Manager Desktop<br/>SP01  = Spool Request<br/>SU3  = Maintain own user paramaters<br/>SM31  = Access Table<br/>PA30  = Maintain HR Master Data<br/>PE03  = HR Features (Decision Trees) like PINCH<br/>KS03  = Display Cost Center<br/>PPOME = Maintain Org. Structure (new interface)<br/>PPOM_OLD = Maintain Org. Structure ('old' interface)<br/>PTARQ = Test Leave Request (includes a program to identify the line manager).<br/>SWU_OBUF = Refresh Org. Management Buffer<br/>SE18  = Business Add In's (BAdI's)<br/><br/>List of tables<br/>T77S0 = HR Switches main tables - group AUTSW regarding authorizations<br/>USERS_SSM = Deactivate SAP standard menu and force user menus<br/>V_T591C = Enable long field (241) form infotype 0105 - Communication<br/>TRESC = Allowed naming space lie Z* or Y* convention for SAP<br/>tables<br/>V_T582A = Infotypes attributes<br/>T526  = Administrators<br/></p></div>", "noteVersion": 1}, {"note": "1291204", "noteTitle": "1291204 - Loading with DTP: Failed authorization check on 0BI_ALL", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Data loading with DTP fails and the log shows an authorization error:<br/>User  does not have authorization for InfoProvider &amp;1<br/>Message no. RS_EXCEPTION251</p>\n<p><br/>The basis authorization log (ST01 or SU53) shows a failed check on authorization object S_RS_AUTH for value 0BI_ALL.<br/>This is <strong>no error</strong> (see below) but is due to authorization checks in data staging.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>DTP, Status 'Processed with Errors' RSBK257  RSBK 257 \"You do not have sufficient authorization for InfoProvider &lt;IP&gt;\" EYE 001, EYE007<br/>\"You donnot have sufficient authorization\" EYE 007, EYE001, RS_EXCEPTION 251</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The user executing the Data loading does not have the authorization required for the staged data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Important:</p>\n<ol><ol>\n<li>The failed check on authorization object S_RS_AUTH for value 0BI_ALL <strong>is no error! </strong></li>\n<li>It does <strong>not </strong>mean that <strong>always </strong>the <strong>full </strong>authorization would be required.</li>\n</ol></ol><ol>3. See also note 820183.</ol><ol>4. If anyway an error in the Authorization Check is suspected, please follow note 1234567. Please analyze the RSECADMIN-Log.</ol><ol>5. An OSS message should only be opened in two situations:</ol><ol><ol>a) The given Authorizations should authorize the Selection, but fail to do so. In this case, please state clearly which Authorization (technical name) should do this. A problem can not be investigated when it is not clear where exactly it goes wrong.</ol></ol><ol><ol>b) The Selection shown in the RSECADMIN-log does not fit to the actually selected data: It is too large and thus does not get authorized. Then please state what you expect to be selected and how this can be reproduced.</ol></ol>\n<p>In both cases the RSECADMIN-Log should get attached to the message. (See below.)<br/>           <br/><br/>It is a technical artefact and just indicates that the corresponding user does not have full authorization. But this is usually desired and valid.<br/><br/>Also note:<br/>Even in case the staging user gets full data authorizations assigned, this does NOT mean that this user automatically can see all data in all queries: <strong>Query execution is additionally protected by the authorization object S_RS_COMP.</strong><br/><br/>Details:<br/>In DTP data loading with extraction from InfoProviders the BI Analysis Authorizations are checked.<br/>The extracting user must have authorizations that are sufficient for the selected data.<br/>With this concept, beginning from BI 7.0, the level of security has been improved in the area of staging. It enables also to protect/allow various separate areas of data which can be accessed. One may use InfoProviders or even data based access control on value level. The concept is based on the analysis authorizations that are also used for reporting in queries.<br/>To configure a staging authorization concept you need to configure analysis authorizations.<br/>Contact your administrator for Analysis Authorizations in case you want to use this capability or see also the docu for Analysis Authorizations:<br/>http://help.sap.com/saphelp_nw04s/helpdata/en/be/076f3b6c980c3be10000000a11402f/frameset.htm<br/><br/>Please understand that a technical explanation of the functionality of Analysis Authorizations can not be provided within an OSS message.<br/>The note 1234567 explains the details of the Protocol for BI Analysis Authorizations (RSECADMIN-Log)</p>\n<p><strong>Scenarios</strong></p>\n<p><br/>Solution 1:<br/>If only one user ID is used for Data Loading (only), and no separation of data is desired it is possible to assign the full BI Analysis Authorizations 0BI_ALL. Then, all data in all InfoProviders are accessible through staging. This is compatible to 3.x where no data access was checked and only the (still available) high level checks on authorization objects where performed. Please be aware, that also new authorization checks in staging have been introduced with 7.0.<br/>Also note:<br/>Granting 0BI_ALL does not necessarily mean full access to all queries because query execution is also protected by the Authorization Object S_RS_COMP.<br/>Also, the loading user may be defined as System/Background (not Dialog user) inorder to avoid security issues completely.<br/><br/>Solution 2:<br/>If you want to separate data areas (for instance HR data and other business data) you can use different staging users with different analysis authorizaitons basing on InfoProviders or even on data areas within a single InfoProvider.<br/>Usually, (but not necessarily,) staging users are batch users so they are not able to access backend tools as SE16 to look at data they are not authorized to. Have in mind however, that Administrator (dialog) users might need authorizations on a more certain level concerning tools like SE16, application log (no analysis auth check), RSA3 (analysis auth check included), PSA(?), ... depending on the security requirements for the staging administrators.</p>\n<p><strong>Performance</strong></p>\n<p>Authorizaton check impact on performance is comparable (if not less) to what can be expected in online execution of queries. Typically the authorization check is far less than a second (unless you use a very complicated detailed authorization concept where execution time can raise). Therefore, the staging process is typically expected to be dominated by the data transfer and transformation processes which may include RFC and data base accesses.<br/><br/></p>\n<p><br/><br/><strong>Logical Background:</strong><br/>The DTP reads data from an InfoProvider. It is a general principle in SAP BI that all such reading <strong>must</strong> be authorized by Analysis Authorizations to enable distinct security models with different users.<br/>Not checking the appropriate authorizations would be a security gap!<br/><br/><br/><br/><br/></p></div>", "noteVersion": 8}]}]}