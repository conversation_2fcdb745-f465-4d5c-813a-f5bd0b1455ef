{"guid": "6CAE8B3EA08B1ED6A5C324437E6280C1", "sitemId": "SI16: Logistics_General", "sitemTitle": "S4TWL - Retail Order Optimizing: Load Build, Investment Buy", "note": 2371602, "noteTitle": "2371602 - S4TWL - Retail Order Optimizing: Load Build, Investment Buy", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management, functionality to support order optimizing via load build, and investment buy is available, but defined as \"not strategic any more\".  See SAP note <a href=\"/notes/2269324\" target=\"_blank\" title=\"2269324  - Compatibility Scope Matrix for SAP S/4HANA, on-premise edition\">2269324 - Compatibility Scope Matrix for SAP S/4HANA, on-premise edition </a>, refer to ID 104 in the xls.</p>\n<p><strong>Business Process related information</strong></p>\n<p>This type of order optimization functionality is covered by replenishment solutions. Corresponding functionality is planned for SAP Predictive Replenishment.</p>\n<p><strong>Required and Recommended Action</strong></p>\n<p>Move to an alternative before expiration of use rights as explained in note 2269324.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if retail order optimizing is used. <br/>This is the case if any of the following transactions WLB1, WLB2, WLB4, WLB5, WLB6, WLB7, WLB13, WLBA, WLBB, WLBC, or programs RWVLB002, RWVLB003, RWVLB004, RWVLB006, RWVLB008, RWVLB00A, RWVLB00B, RWVLB00C, RWVLB013, RWVLB014 is used.<br/>This also can be checked via transaction SE16N. Enter table WVFB, WBO* and check whether there are any entries.</p>", "noteVersion": 9, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Before or after conversion project", "Condition": "Optional", "Additional_Information": "Business can choose to use SAP Forecstaing & Replenishment to support order optimizing via load build, and investment buy ."}, {"Activity": "Implementation project required", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "This activity is requried in case Business makes a decision to use SAP Forecstaing & Replenishment to replace non strategic functionality to support order optimizing via load build, and investment buy"}, {"Activity": "Custom Code Adaption", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": "Adjust customer specific coding prior to End of Maintenance of the Compatibility Pack ."}]}