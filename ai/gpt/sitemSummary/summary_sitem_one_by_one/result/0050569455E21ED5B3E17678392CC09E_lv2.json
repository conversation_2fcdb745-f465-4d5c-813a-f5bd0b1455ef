{"guid": "0050569455E21ED5B3E17678392CC09E", "sitemId": "SI3: Utilities_UIS", "sitemTitle": "S4TWL - IS-U Stock, Transaction and Master Data Statistics (UIS, BW)", "note": 2270505, "noteTitle": "2270505 - S4TWL - IS-U Stock, Transaction and Master Data Statistics (UIS, BW)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Utilities Information System, UIS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>IS-U Stock and Transaction Statistics based on Logistics Information System (LIS) is not available within SAP S/4HANA, on-premise edition 1511. Successor functionality: CDS view based virtual data model for Utilities (see documentation <a href=\"https://help.sap.com/viewer/6b356c79dea443c4bbeeaf0865e04207/1709%20000/en-US/dde1c356a3386a15e10000000a441470.html\" target=\"_blank\">https://help.sap.com/viewer/6b356c79dea443c4bbeeaf0865e04207/1709%20000/en-US/dde1c356a3386a15e10000000a441470.html</a> and <a href=\"https://go.support.sap.com/innovationdiscovery/#/innovation/6CAE8B27FCC31EE6A080E3EEE0531B00\" target=\"_blank\">https://go.support.sap.com/innovationdiscovery/#/innovation/6CAE8B27FCC31EE6A080E3EEE0531B00</a>) and SAP BW/4HANA content for Utilities (see documentation <a href=\"https://help.sap.com/viewer/06e872f914a44d77b6c692b0273ca400/1.0.1/en-US/a3e19257660b6c10e10000000a441470.html\" target=\"_blank\">https://help.sap.com/viewer/06e872f914a44d77b6c692b0273ca400/1.0.1/en-US/a3e19257660b6c10e10000000a441470.html</a>). Please check the documentation for the availability of the successors.</p>\n<p>The corresponding functionality and transactions can be found in SAP Business Suite in the SAP Easy Access menu under \"Utilities Industry\" - \"Information System\" - \"Utlities Information System\" - \"Stock Statistics\" and \"Transaction Statistics\", respectively. In SAP S/4HANA, \"Stock Statistics\" and \"Transaction Statistics\" are not available anymore. Following transactions are not available in SAP S/4HANA:</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"216\"/> <col width=\"312\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl65\" height=\"19\" width=\"216\">EI35</td>\n<td class=\"xl65\" width=\"312\">Set up stock statistics</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"19\">EP01</td>\n<td class=\"xl65\">Customizing: Transaction Statistics</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"19\">EP02</td>\n<td class=\"xl65\">Customizing: Stock Statistics</td>\n</tr>\n</tbody>\n</table></div>\n<p>Usage of UIS is indicated by entries in database tables TE789C and TE790C with ACTIVE field set to ‘X’. If no entries exist, this note is not relevant for you. The check can be done via transaction SE16.</p>\n<p>Following BW Data Sources are not available anymore:<br/>Stock and Transaction statistics (in brackets please find alternatives):<br/>0UC_ISU_12 (0UCCONTRACT_ATTR_2)<br/>0UC_ISU_13 (0UCINSTALLAH_ATTR_2)<br/>0UC_ISU_14 (0UC_ISU_32)<br/>0UC_ISU_15 (0UC_DEVINST_ATTR)<br/>0UC_ISU_16 (0UC_SALES_STATS_02 or 0UC_SALES_STATS_03)<br/>0UC_ISU_17 (0UC_SALES_STATS_02 or 0UC_SALES_STATS_03)<br/>0UC_ISU_18 (0UC_DEVICE_ATTR and 0UC_DEVICER_ATTR)<br/>0UC_ISU_19 (0UC_DEVICE_ATTR)<br/>0UC_ISU_20 (0UC_DEVICE_ATTR)<br/>0UC_ISU_22 (0BPARTNER_ATTR)<br/>0UC_ISU_23 (0UC_ACCNTBP_ATTR_2)<br/>0UC_ISU_24 (0UCCONTRACT_ATTR_2 and 0UCCONTRACTH_ATTR_2)<br/>0UC_ISU_25 (0UCINSTALLAH_ATTR_2 or 0UCINSTALLA_ATTR_2)<br/>0UC_ISU_26 (0UC_CONNOBJ_ATTR_2)<br/>0UC_ISU_27 (0UCPREMISE_ATTR_2)<br/>0UC_ISU_28 (0UC_DEVLOC_ATTR)<br/>0UC_ISU_30 (0UC_DEVICE_ATTR and 0UC_DEVICER_ATTR)<br/>0UC_ISU_31 (0UC_INSTFACTS)</p>\n<p>Master Data and attributes (in brackets please find alternatives):<br/>0UC_ACCNTBP_ATTR (0UC_ACCNTBP_ATTR_2)<br/>0UC_CONNOBJ_ATTR (0UC_CONNOBJ_ATTR_2)<br/>0UC_INDSECT_TEXT<br/>0UC_TRANSPORT_TEST<br/>0UCCONTRACT_ATTR (0UCCONTRACT_ATTR_2)<br/>0UCCONTRACTH_ATTR (0UCCONTRACTH_ATTR_2)<br/>0UCINSTALLA_ATTR (0UCINSTALLA_ATTR_2)<br/>0UCINSTALLAH_ATTR (0UCINSTALLAH_ATTR_2)<br/>0UCPREMISE_ATTR (0UCPREMISE_ATTR_2)</p>\n<p>Other (in brackets please find alternatives):</p>\n<p>0UC_ISU_CONSUMPT_02 (Sales Statistics shall be applied)<br/>0UC_ISU_CONSUMPTION (Sales Statistics shall be applied)</p>\n<p>0UC_SALES_SIMU_02 (0UC_SALES_SIMU_01, 0UC_SALES_SIMU_03)</p>\n<p>0UC_SALES_STATS_01 (0UC_SALES_STATS_02, 0UC_SALES_STATS_03)</p>\n<p>Regarding BW extractors and data sources in other line of businesses and industry areas, p<span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" de;=\"\" ja;=\"\" lang=\"DE\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">lease</span><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" de;=\"\" ja;=\"\" lang=\"DE\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"> also refer to SAP note 2500202 of simplification item S4TWL - BW Extractors in SAP S/4HANA.</span></p>", "noteVersion": 11, "refer_note": [{"note": "2500202", "noteTitle": "2500202 - S4TWL - BW Extractors in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p dir=\"ltr\">Customers considering moving to SAP S/4HANA (on premise) seek information whether BW extractors (aka data sources) known form SAP ERP still work, to be able to evaluate the potential impact on the BW environment when moving to SAP S/4HANA. To meet this requirement, SAP reviewed the status and the attached list (MS Excel document) provides the information that we have per extractor. The results are valid from SAP S/4HANA 1709 (on premise) until further notice or otherwise and in most cases apply to SAP S/4HANA 1610 and SAP S/4HANA 1511 as well (see respective flags in attached MS Excel document).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BW, Extractors, Datasources, SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p dir=\"ltr\">Parts of this information are already available in CSS and can be found through 2333141 - SAP S/4HANA 1610: Restriction Note. The status of the extractors is collected in one list (the attached MS Excel document), see the attached MS Powerpoint document for information how the list is typically used. Not all extractors that are technically available in SAP S/4HANA are covered, SAP is planning to provide clarifications for additional extractors and share new versions of the list via this note. Hence it is recommended to review this note for updates.</p>\n<p>The information in this file is dated as of 10.09.2018. Please acknowledge that S/4HANA functionality is set forth in the S/4HANA Feature Scope Description. All business functionality not set forth therein is not licensed for use by customer.</p>\n<p><em>Please note that extractors in the namespace 0BWTC*, 0TCT* and 8* are related to the \"Embedded BW\" that is offered as a technology component within the S/4HANA software stack. They are all not explicitly whitelisted in this SAP Note as they are not part of the delivered Business Content by the S/4HANA Lines of Business but can be used for extraction. </em><em> </em></p>\n<ul>\n<li><em>0BWTC* and 0TCT* are extractors providing technical statistical information from the Embedded BW such as query runtime statistics or data loading statistics. </em></li>\n<li><em>8* are <a href=\"https://help.sap.com/viewer/64e2cdef95134a2b8870ccfa29cbedc3/7.5.6/en-US/4c1a1b9054914c86e10000000a42189e.html\" target=\"_blank\">Export DataSources</a> for transferring business data from InfoProviders of the “Embedded BW” system to a target system</em></li>\n<ul>\n<li><em>For a SAP BW target systems this is achieved by the <a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.5.6/en-US/4a1411c3174f0452e10000000a421937.html\" target=\"_blank\">SAP Source System</a> type </em></li>\n<li><em>For a data transfer via the <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/2.0.1/en-US/c6afacb707764885a6fb62f511c24f34.html\" target=\"_blank\">ODP Source System</a> type (only option in SAP BW/4HANA target system) these Export DataSources are obsolete and invisible. Instead, the ODP-BW context is used. For more information on Operational Data Provisioning see the <a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">ODP FAQ document</a>.</em></li>\n</ul>\n</ul>\n<p><em> For more information and positioning on the \"Embedded BW\" see:</em> <a href=\"https://www.sap.com/documents/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.html\" target=\"_blank\"><em>https://www.sap.com/documents/2015/06/3ccee34c-577c-0010-82c7-eda71af511fa.html</em></a></p>\n<p> For more information on partner registered data sources and to find partner details from partner namespaces, please create a message on component XX-SER-DNSP.</p>\n<p><strong>This is a collective note, containing information of several industries and areas. In case issues arrise with extractors listed in the Excel, or missing extractors, please do raise an incident on the APPLICATION COMPONENT of the respective EXTRACTOR. This is the <span>only</span> way to ensure that your incident reaches the responsible desk in the shortest time. You can find this in column E, 'Appl.component' in the attached excel document. Do not use the component from this note. Thank you!</strong></p>\n<p> </p>\n<p> </p>\n<p> </p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Details can be found in the respective note per area:</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Item Area - Line of Business</strong></td>\n<td><strong>Note number for details</strong></td>\n</tr>\n<tr>\n<td>Asset Management</td>\n<td>\n<p><a href=\"/notes/2299213\" target=\"_blank\">2299213</a> - Restrictions for BW-Extractors in S/4HANA in the Enterprise Asset Management domain (EAM)</p>\n</td>\n</tr>\n<tr>\n<td>Business Process Management</td>\n<td>\n<p><a href=\"/notes/2796696\" target=\"_blank\">2796696</a> - Restrictions for BW extractors relevant for S/4HANA as part of SAP S/4HANA, on-premise edition 1709 and above</p>\n</td>\n</tr>\n<tr>\n<td>Customer Services</td>\n<td>\n<p><a href=\"/notes/2533548\" target=\"_blank\">2533548</a> - Restrictions for BW-Extractors in S/4HANA in the CS (Customer Service) area</p>\n</td>\n</tr>\n<tr>\n<td>Enterprise Portfolio and Project Management</td>\n<td>\n<p><a href=\"/notes/2496759\" target=\"_blank\">2496759</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Enterprise Portfolio and Project Management</p>\n</td>\n</tr>\n<tr>\n<td>Financials</td>\n<td>\n<p><a href=\"/notes/2270133\" target=\"_blank\">2270133</a> - Restrictions for BW extractors relevant for S/4HANA Finance as part of SAP S/4HANA, on-premise edition 1511</p>\n</td>\n</tr>\n<tr>\n<td>Flexible Real Estate</td>\n<td>\n<p><a href=\"/notes/2270550\" target=\"_blank\">2270550</a> - S4TWL - Real Estate Classic</p>\n</td>\n</tr>\n<tr>\n<td>Global Trade</td>\n<td>\n<p><a href=\"/notes/2498786\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>Globalization Services Finance</td>\n<td><a href=\"/notes/2559556\" target=\"_blank\">2559556</a> - Restrictions for BW extractors in Financial Localizations relevant for S/4HANA Finance as part of SAP S/4HANA, on-premise edition 1511</td>\n</tr>\n<tr>\n<td>Human Resources</td>\n<td><a href=\"/notes/2559556\" target=\"_blank\"> </a></td>\n</tr>\n<tr>\n<td>Incident Management and Risk Assessment </td>\n<td> <a href=\"/notes/2267784\" target=\"_blank\">2267784</a> - S4TWL - Simplification in Incident Management and Risk Assessment</td>\n</tr>\n<tr>\n<td>Master Data</td>\n<td>\n<p><a href=\"/notes/2498786\" target=\"_blank\">2498786</a> - Data Sources supported by Central Master Data in S/4HANA</p>\n<p><a href=\"/notes/2576363\" target=\"_blank\" title=\"2576363  - Data Sources supported by Central Master Data in S/4HANA\">2576363</a> - Data Sources supported by Master Data Governance in S/4HANA</p>\n</td>\n</tr>\n<tr>\n<td>Procurement</td>\n<td>\n<p dir=\"ltr\"><a href=\"/notes/2504508\" target=\"_blank\">2504508</a> - Restrictions for BW Extractors relevant for S/4 HANA Procurement as part of SAP S/4HANA, on premise edition 1709 and above</p>\n</td>\n</tr>\n<tr>\n<td>Produce</td>\n<td>\n<p dir=\"ltr\"><a href=\"/notes/2499728\" target=\"_blank\">2499728</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Detailed Scheduling <br/><a href=\"/notes/2499716\" target=\"_blank\">2499716</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Control <br/><a href=\"/notes/2499589\" target=\"_blank\">2499589</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Quality Management<br/><a href=\"/notes/2499310\" target=\"_blank\">2499310</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Inventory Management</p>\n</td>\n</tr>\n<tr>\n<td>Sales and Distribution</td>\n<td><a href=\"/notes/2498211\" target=\"_blank\">2498211</a> - Restrictions for BW extractors relevant for S/4HANA Sales as part of SAP S/4HANA, on-premise edition 1709</td>\n</tr>\n<tr>\n<td>\n<p>Supply Chain – Extended Warehouse Management</p>\n</td>\n<td>\n<p><a href=\"/notes/2552797\" target=\"_blank\">2552797</a> List of BI Data Sources used in EWM<br/><a href=\"/notes/2382662\" target=\"_blank\">2382662</a> List of BI Data Sources from SCM Basis used in EWM Context</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Supply Chain – Transportation Management</p>\n</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Master data governance for Finance</p>\n</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Item Area - Industry</strong></p>\n</td>\n<td><strong>Note number for details</strong>                                                                                                                  </td>\n</tr>\n<tr>\n<td>DIMP Automotive</td>\n<td>\n<p><a href=\"https://launchpad.support.sap.com/\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>Defense and Security</td>\n<td>\n<p><a href=\"https://launchpad.support.sap.com/\" target=\"_blank\">2544193</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Defense &amp; Security</p>\n<p><a href=\"/notes/2273294\" target=\"_blank\">2273294</a> - S4TWL - BI content, Datasources and Extractors for DFPS</p>\n</td>\n</tr>\n<tr>\n<td>Financial Services                                        </td>\n<td>\n<p><a href=\"/notes/2543469\" target=\"_blank\">2543469</a> - \"SAP for Banking\": SAP extractors in connection with \"SAP S/4HANA on-premise edition</p>\n<p><span><a href=\"https://i7p.wdf.sap.corp/sap(bD1lbiZjPTAwMQ==)/bc/bsp/sno/ui_entry/entry.htm?param=69765F6D6F64653D3030312669765F7361706E6F7465735F6E756D6265723D3235343631303226\" target=\"_blank\">2546102</a></span> - \"SAP for Insurance\": SAP extractors in connection with \"SAP S/4HANA on-premise edition“</p>\n</td>\n</tr>\n<tr>\n<td>Higher Education</td>\n<td>\n<p><a href=\"/notes/2543469\" target=\"_blank\"> </a></p>\n</td>\n</tr>\n<tr>\n<td>IS Healthcare</td>\n<td>-</td>\n</tr>\n<tr>\n<td>IS Utilities</td>\n<td>\n<p><a href=\"/notes/2270505\" target=\"_blank\">2270505</a> - S4TWL - IS-U Stock, Transaction and Master Data Statistics (UIS, BW)</p>\n</td>\n</tr>\n<tr>\n<td>Oil and Gas</td>\n<td>\n<p> </p>\n</td>\n</tr>\n<tr>\n<td>Public Sector Collection and Disbursement (PSCD)</td>\n<td>\n<p>All Data Sources working and whitelisted, no separate note exists</p>\n</td>\n</tr>\n<tr>\n<td>Public Sector Management (PSM)</td>\n<td><a href=\"/notes/2556359\" target=\"_blank\">2556359</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Public Sector Management</td>\n</tr>\n<tr>\n<td>IS Retail</td>\n<td><a href=\"/notes/2543543\" target=\"_blank\">2543543</a> - Restrictions for BW extractors relevant for S/4HANA in the area of Retail</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p> The classification scheme is:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"4\" cellspacing=\"1\" class=\"table table-bordered table-striped col-resizeable\" dir=\"ltr\">\n<tbody>\n<tr>\n<td height=\"32\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\"><strong>Current status                             </strong></p>\n</td>\n<td height=\"32\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\"><strong>Status for Publication</strong></p>\n</td>\n<td height=\"32\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\"><strong>Description                                                             </strong></p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"2\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Whitelisted</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - no restrictions</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is available with S/4 and works without any restrictions compared to ERP</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working – regeneration of extractor and check of BW content based on this DS is needed                                                                                          .</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is working, because of data model changes, it is recommended to check the upward dataflow.</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"3\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Not Whitelisted</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - restrictions</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is available with S/4 but works with noteworthy restrictions; e.g. not all fields are available</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - alternative exists</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but an alternative exists, such as a new extractor, CDS view</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - alternative planned</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but equivalent available on roadmap for future release</p>\n</td>\n</tr>\n<tr>\n<td height=\"17\" rowspan=\"2\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Deprecated</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS obsolete</p>\n</td>\n<td height=\"17\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is obsolete - legacy extractors</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not working - no alternative exists</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">DataSource is not working with S/4 but no alternative exists</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Generated Data Source</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS working - no restrictions</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">Because of the nature of the extractors, being generated in the system, we cannot whitelist those in general. Experience so far showed that they should be working without restrictions.</p>\n</td>\n</tr>\n<tr>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">Not relevant for BW extraction</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"27%\">\n<p dir=\"ltr\">DS not relevant</p>\n</td>\n<td height=\"18\" valign=\"top\" width=\"45%\">\n<p dir=\"ltr\">Datasource is available in ROOSOURCE, however, not to be used for extraction by BW.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 41, "refer_note": [{"note": "2499310", "noteTitle": "2499310 - Restrictions for BW extractors relevant for S/4HANA in the area of Inventory Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several data sources for BW extraction are no longer available with S/4 in the area of Inventory Management as part of the product version SAP S/4HANA, on-premise edition 1511 and above</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, BW, Extractors, MM-IM, Inventory Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following DataSources are not working any longer</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"193\"/> <col width=\"102\"/> <col width=\"97\"/> <col width=\"365\"/></colgroup>\n<tbody>\n<tr>\n<td height=\"17\" width=\"193\">DataSource</td>\n<td width=\"102\">Appl.component</td>\n<td width=\"97\">Restriction</td>\n<td width=\"365\">Comment</td>\n</tr>\n<tr>\n<td height=\"17\">/FMP/MP_PRICE_TYPE_ATTR</td>\n<td>MM-IO</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td height=\"17\">/FMP/MP_PRICE_TYPE_TEXT</td>\n<td>MM-IO</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\">0DT_CATEG_TEXT</td>\n<td>MM</td>\n<td class=\"xl67\">Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\">0DT_ELEMENT_ATTR</td>\n<td>MM</td>\n<td class=\"xl67\">Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\">0DT_ELEMENT_TEXT</td>\n<td>MM</td>\n<td class=\"xl67\">Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\"></td>\n<td> </td>\n<td class=\"xl67\"></td>\n<td> </td>\n</tr>\n<tr>\n<td height=\"17\">0MM_IM_BF</td>\n<td>MM</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists: DataSource 2LIS_03_BF</td>\n</tr>\n<tr>\n<td height=\"17\">0MM_IM_BX</td>\n<td>MM</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists: DataSource 2LIS_03_BX</td>\n</tr>\n<tr>\n<td height=\"17\">0MM_IM_STOCK</td>\n<td>MM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td height=\"17\">0MM_IM_UM</td>\n<td>MM</td>\n<td>Not Whitelisted</td>\n<td>DS not working - alternative exists DataSource 2LIS_03_UM</td>\n</tr>\n<tr>\n<td height=\"17\">2LIS_03_S091</td>\n<td>MM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td height=\"17\">2LIS_03_S194</td>\n<td>MM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td height=\"17\">2LIS_03_S195</td>\n<td>MM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td height=\"17\">2LIS_03_S196</td>\n<td>MM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td height=\"17\">2LIS_03_S197</td>\n<td>MM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td height=\"17\">2LIS_03_S198</td>\n<td>MM</td>\n<td>Deprecated</td>\n<td>DS not working - no alternative exists</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 2}, {"note": "2556359", "noteTitle": "2556359 - Restrictions for BW extractors relevant for S/4HANA in the area of Public Sector Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several data sources for BW extraction are no longer available with S/4 in the area of Public Sector Management as part of the product version SAP S/4HANA, on-premise edition 1610 and higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, BW, Extractors, Public Sector Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following DataSources are not supported any longer:</p>\n<div class=\"table-responsive\"><table border=\"5\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>DataSource</td>\n<td>Appl.component</td>\n<td>Restriction</td>\n<td>Comment</td>\n</tr>\n<tr>\n<td>0PSM_FM_EC_AMOUNTS</td>\n<td>PSM-EC</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n<tr>\n<td>0PSM_FM_EC_LEVELS</td>\n<td>PSM-EC</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n<tr>\n<td>0PSM_FM_EC_OPERA_AUD</td>\n<td>PSM-EC</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n<tr>\n<td>0PSM_FM_EC_OPERA_PERF</td>\n<td>PSM-EC</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n<tr>\n<td>0PSM_FM_EC_RUN</td>\n<td>PSM-EC</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n<tr>\n<td>0PSM_FM_FNS</td>\n<td>PSM-EC</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n<tr>\n<td>0PSM_FM_FNST_TXT</td>\n<td>PSM-EC</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n<tr>\n<td>0PSM_FM_OPERA_TXT</td>\n<td>PSM-EC</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n<tr>\n<td>0PU_IS_PS_1</td>\n<td>PSM-FM-IS</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n<tr>\n<td>0PU_IS_PS_11</td>\n<td>PSM-FM-IS</td>\n<td>Deprecated</td>\n<td>DS obsolete</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 1}, {"note": "2496759", "noteTitle": "2496759 - Restrictions for BW extractors relevant for S/4HANA in the area of Enterprise Portfolio and Project Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several data sources for BW extraction are no longer available with S/4HANA in the area of Enterprise Portfolio and Project Management as part of the product version SAP S/4HANA, on-premise edition 1511 and above</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, BW, Extractors, EPPM, PPM, PS, cFolders, cProjects</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following DataSources have been <strong>deprecated</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"204\"/> <col width=\"192\"/> <col width=\"92\"/> <col width=\"74\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl65\" height=\"20\" width=\"204\"><strong>DataSource</strong></td>\n<td class=\"xl65\" width=\"192\"><strong>Appl. Component</strong></td>\n<td class=\"xl65\" width=\"92\"><strong>Classification</strong></td>\n<td class=\"xl65\" width=\"74\"><strong>Comments</strong></td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0AREA_ATTR</td>\n<td class=\"xl66\">CFX DEVELOPMENT PROJECTS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0CFO_COLLAB</td>\n<td class=\"xl66\">CFX DEVELOPMENT PROJECTS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0CFO_TRANSACTION</td>\n<td class=\"xl66\">CFX DEVELOPMENT PROJECTS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0COLLABORATION_ATTR</td>\n<td class=\"xl66\">CFX DEVELOPMENT PROJECTS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0FOL_ATTR</td>\n<td class=\"xl66\">CFX DEVELOPMENT PROJECTS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0DPR_APPROVAL_OBJECT_LINK</td>\n<td class=\"xl66\">DEVELOPMENT PROJECTS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0PS_10</td>\n<td class=\"xl66\">PS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0PS_20</td>\n<td class=\"xl66\">PS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0PS_30</td>\n<td class=\"xl66\">PS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0PS_OBJNR_ATTR</td>\n<td class=\"xl66\">PS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0PS_OBJNR_HIER</td>\n<td class=\"xl66\">PS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"20\">0PS_OBJNR_TEXT</td>\n<td class=\"xl66\">PS</td>\n<td class=\"xl67\">obsolete</td>\n<td class=\"xl67\">Obsolete and no longer to be used by customer</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 3}, {"note": "3436286", "noteTitle": "3436286 - Restrictions for BW extractors relevant for Asset accounting as part of SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several DataSources for BW extraction are no longer available with S/4HANA Finance as part of the product version SAP S/4HANA, on-premise edition 1511 and above</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SFIN, extractor</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The DataSources listed below have been deactivated:</p>\n<ul>\n<li>0FI_AA_001</li>\n<li>0FI_AA_002</li>\n<li>0FI_AA_003</li>\n<li>0FI_AA_004</li>\n<li>0FI_AA_005</li>\n<li>0FI_AA_006</li>\n</ul>\n<p><span>Those DataSources were already outdated in ECC. Data sources 0FI_AA_11 and 0FI_AA_12 can be used instead.</span></p>\n<p>0FI_AA_11: DataSource 0FI_AA_11 can be used but has the limitation that group assets are no longer supported (see note <a href=\"/notes/2748735\" target=\"_blank\">2748735</a>).</p>\n<p>0FI_AA_12: Usage of DataSource 0FI_AA_12 requires implementation of SAP note <a href=\"/notes/2408988\" target=\"_blank\">2408988</a>. Refer to note <a href=\"/notes/614804\" target=\"_blank\">614804</a> concerning supported methods for simulated depreciation values.</p>", "noteVersion": 1}, {"note": "2499728", "noteTitle": "2499728 - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Detailed Scheduling", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several data sources for BW extraction are no longer available with S/4 in the area of PP/DS (Production Planning and Detailed Scheduling) as part of the product version SAP S/4HANA, on-premise edition 1511 and above</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, BW, Extractors, PPDS, Production Planning and Detailed Scheduling, PP/DS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following DataSources are not working any longer.</p>\n<p>In case it is needed to use extractors, please get in contact with a BW consultant to clarify how to access specific data from PP or S/4 HANA in general.</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"212\"/> <col width=\"105\"/> <col width=\"90\"/> <col width=\"250\"/></colgroup>\n<tbody>\n<tr>\n<td height=\"17\" width=\"212\">DataSource</td>\n<td width=\"105\">Appl.component</td>\n<td width=\"90\">Restriction</td>\n<td width=\"250\">Comment</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_BESKZ_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LANE_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LOCMAP_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LOCNO_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LOCNO_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LOCTYP_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LPROD_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_MATMAP_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_MODEL_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_MODEL_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"34\" width=\"212\">0APO_MSP_PLANNING_TYPE_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_ORDETP_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PART_PROD_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PART_PRODT_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PLNNR_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PLNNR_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PLVERS_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PLVERS_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PRDTYP_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PROD_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PROD_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_RESART_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_RESNAM_ATTR</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_RESNAM_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_RTYPE_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_SUBLOC_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_TRTYPE_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_WHTBOM_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0SCM_BOD_HIER</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">0SCM_PROD2BOD</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td height=\"17\">9ACFM_IDOC</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl65\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_MATGRPTYPE_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_REPTYP_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SCRAPIND_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SODISPO_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SOREPLEN_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SORETGRP_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SORFLAG_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SORSN_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SOTPOP_TEXT</td>\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\n<td class=\"xl66\" width=\"90\">Deprecated</td>\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 6}, {"note": "3427502", "noteTitle": "3427502 - BW Extractors for E Recruiting in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">Customers planning an SAP S/4HANA (on premise) move, seek information about the working of BW extractors (also known as datasources) related to E Recruiting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\"> BW, Extraction, PA-ER Extractors, SAP S/4HANA, e recruiting Datasources.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\"> Most of the BW extractors in SAP S/4HANA can be found through SAP Note 2500202 - S4TWL - BW Extractors.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The PA-ER extractors(also known as datasources) are also supported in SAP S/4HANA and the extraction process works in the same way as in SAP ERP systems.</p>", "noteVersion": 1}, {"note": "2333141", "noteTitle": "2333141 - SAP S/4HANA 1610: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using SAP S/4HANA 1610, respectively you are planning or running a conversion to SAP S/4HANA 1610.</p>\n<p>This SAP Note, which is subject to change, informs you about all restrictions in this release.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release restrictions, SAP S/4HANA 1610</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note provides information about the restrictions that exist for SAP S/4HANA 1610.</p>\n<p>Note: This SAP Note is subject to change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<ul>\n<li>SAP S/4HANA 1610 supports the following industries/solutions:</li>\n<ul>\n<li>Consumer Products</li>\n<li>Wholesale</li>\n<li>Life Sciences</li>\n<li>Aerospace &amp; Defense (A&amp;D)</li>\n<li>HighTech</li>\n<li>Industrial Machinery &amp; Components (IM&amp;C)</li>\n<li>Automotive</li>\n<li>Chemicals</li>\n<li>Mining</li>\n<li>Mill Products</li>\n<li>Utilities</li>\n<li>Banking</li>\n<li>Insurance</li>\n<li>Public Sector</li>\n<li>Engineering, Construction &amp; Operations (EC&amp;O)</li>\n<li>Professional Services</li>\n<li>Telecommunication</li>\n<li>Sports &amp; Entertainment</li>\n<li>Transportation &amp; Logistics</li>\n<li>Contract Accounts Receivable and Payable (FI-CA)</li>\n<li>Higher Education and Research</li>\n<li>Defense and Security</li>\n<li>Oil and Gas</li>\n<li>Retail<br/>Please also check the linked SAP Notes for restrictions.</li>\n</ul>\n<li>In SAP S/4HANA, the maximum length of the material number has been extended to 40 characters. <em>Long material number-</em>related restrictions are described in SAP Note <a href=\"/notes/2233100\" target=\"_blank\">2233100</a>.</li>\n<li>The integrated solution of Service Parts Management using SAP CRM Order Management with an active Service Parts Management configuration (Direct Delivery Scenario) in combination with S/4HANA is not released with SAP S/4HANA 1610.</li>\n<li>The attached Business Functions are not released with SAP S/4HANA 1610. It is not permitted to switch them on, even if this is technically possible (see <a href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000721462016&amp;iv_guid=00109B36BC8E1ED9B3F9C1950A6060E1&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F4933B334318AF734378A3730B4AA2C0EA8CC0FAE4C37092E702B0D37AA4A3676F6764DB24C76B60C8C32C94D2D7749CC31ADF2090BD7750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\" target=\"_blank\">attachment</a>). Additional information regarding Always-Off and Always-On Business Functions can be found in SAP Notes <a href=\"/notes/2240359\" target=\"_blank\">2240359</a> and <a href=\"/notes/2240360\" target=\"_blank\">2240360</a>. If a business function was switched on in the start release system, but defined as always_off in SAP S/4HANA, then a system conversion is not yet possible with this release at that current point in time. If a business function was switched off in the start release system, but defined as always_on in the target release, then the business function will be automatically activated during the conversion.</li>\n<li>There are limitations for packaging material with more than 35 characters in combination with Returnable Packaging Logistics (application component MM-IM-RL or IS-A-RL). These are:</li>\n<ul>\n<li>EDI processing of returnable packaging account statements (message type ACCSTA). The IDoc type ACCSTA01 allows supplier and customer material numbers for packaging materials of up to 35 characters to be transmitted.</li>\n<li>EDI processing of returnable packaging account statement requests (message type ACCSTAREQ). The IDoc type ACCSTA01 allows supplier and customer material numbers for packaging materials of up to 35 characters to be transmitted.</li>\n</ul>\n<li>The released information for Add-Ons can be found in SAP Note <a href=\"/notes/2214409\" target=\"_blank\">2214409</a>.</li>\n<li>Account Numbers: In case of usage of the Best Practices with demo data, Account Numbers need to consist of 10 digits. They need to consist of numeric digits only. No alphanumeric characters are allowed at the moment. Examples of valid account numbers: \"*********; **********\".</li>\n<li>Further area-specific restrictions for SAP S/4HANA 1610 are described - amongst others - in the linked SAP Notes, e.g.:</li>\n<ol>\n<li>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"277\"/> <col width=\"65\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl67\" height=\"20\" width=\"277\"><strong>Area</strong></td>\n<td class=\"xl67\" width=\"65\"><strong>SAP Note</strong></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Master Data Governance</td>\n<td class=\"xl66\"><a href=\"/notes/2349002\" target=\"_blank\">2349002</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Aerospace &amp; Defense (A&amp;D)</td>\n<td class=\"xl66\"><a href=\"/notes/2349454\" target=\"_blank\">2349454</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Automotive</td>\n<td class=\"xl66\"><a href=\"/notes/2347206\" target=\"_blank\">2347206</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Mill Products</td>\n<td class=\"xl66\"><a href=\"/notes/2229398\" target=\"_blank\">2229398</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Banking</td>\n<td class=\"xl66\"><a href=\"/notes/2328754\" target=\"_blank\">2328754</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Public Sector</td>\n<td class=\"xl66\"><a href=\"/notes/2348636\" target=\"_blank\">2348636</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Finance</td>\n<td class=\"xl66\"><a href=\"/notes/2344977\" target=\"_blank\">2344977</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LoB Manufacturing: QM</td>\n<td class=\"xl66\"><a href=\"/notes/2345333\" target=\"_blank\">2345333</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LoB Manufacturing: MM-IM</td>\n<td class=\"xl66\">\n<p><a href=\"/notes/2345321\" target=\"_blank\">2345321<br/></a><a href=\"/notes/2440007\" target=\"_blank\" title='2440007  - Error in Fiori App \"Post Goods Receipt for Purchase Order\" (F0843) due to bug inSAP HANA 2.0 SPS 00 Database Revision 001'>2440007</a><a href=\"/notes/2345321\" target=\"_blank\"><br/></a></p>\n</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">LoB CEC</td>\n<td class=\"xl66\"><a href=\"/notes/2231667\" target=\"_blank\">2231667</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Globalization</td>\n<td class=\"xl66\"><a href=\"/notes/2349004\" target=\"_blank\">2349004</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Best Practices Framework</td>\n<td class=\"xl66\"><a href=\"/notes/2348479\" target=\"_blank\">2348479</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Environment, Health and Safety (EHS)</td>\n<td class=\"xl66\"><a href=\"/notes/2383548\" target=\"_blank\">2383548</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Global Trade Service (GTS)</td>\n<td class=\"xl66\"><a href=\"/notes/2382239\" target=\"_blank\">2382239</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Waste and Recycling</td>\n<td class=\"xl66\"><a href=\"/notes/2232552\" target=\"_blank\">2232552</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Defense and Security</td>\n<td class=\"xl66\"><a href=\"/notes/2339505\" target=\"_blank\">2339505</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Consumer Products</td>\n<td class=\"xl66\"><a href=\"/notes/2355560\" target=\"_blank\">2355560</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Sales</td>\n<td class=\"xl66\">\n<p><a href=\"/notes/2348936\" target=\"_blank\">2348936</a><br/><a href=\"/notes/2369405\" target=\"_blank\">2369405</a></p>\n</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">SAP Hybris Billing</td>\n<td class=\"xl66\"><a href=\"/notes/2351374\" target=\"_blank\">2351374</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Oil and Gas</td>\n<td class=\"xl66\"><a href=\"/notes/2349061\" target=\"_blank\">2349061</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Project Systems</td>\n<td class=\"xl66\"><a href=\"/notes/2353392\" target=\"_blank\">2353392</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Integration</td>\n<td class=\"xl66\"><a href=\"/notes/2376061\" target=\"_blank\">2376061</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Extended Warehouse Management</td>\n<td class=\"xl66\"><a href=\"/notes/2347770\" target=\"_blank\">2347770</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">advanced Available-to-Promise (aATP)</td>\n<td class=\"xl66\"><a href=\"/notes/2343524\" target=\"_blank\">2343524</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">\n<p>Available-to-Promise (ATP)</p>\n</td>\n<td class=\"xl66\"><a href=\"/notes/2517708\" target=\"_blank\">2517708</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Production Planning and Detailed Scheduling</td>\n<td><a href=\"/notes/2382787\" target=\"_blank\">2382787</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Extended Warehouse Management (EWM)</td>\n<td class=\"xl66\"><a href=\"/notes/2347770\" target=\"_blank\">2347770</a></td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"20\">Catch Weight Management (CWM)</td>\n<td class=\"xl66\"><a href=\"/notes/2357827\" target=\"_blank\">2357827</a></td>\n</tr>\n</tbody>\n</table></div>\n</li>\n<li></li>\n<li>Please also check the linked SAP Notes for restrictions.</li>\n</ol>\n<ul>\n<ul></ul>\n</ul>\n<li>Restrictions for conversions to SAP S/4HANA 1610:</li>\n<ul>\n<ul>\n<li>Conversion from source releases below SAP ERP 6.0 EHP 5 (SAP_APPL 605) is supported for systems containing vendors with assigned contact but you need to ensure to update to the following minimum SP levels upfront. In addition, you need to implement corrective SAP Note <a href=\"/notes/2383051\" target=\"_blank\">2383051</a> and further corrections into the system. For this purpose please get in contact with SAP via customer incident on component AP-MD-BF-SYN. The minimum SP levels are: <br/> SAP ERP 6.0 SP20<br/> EHP2 FOR SAP ERP 6.0 SP10<br/> EHP3 FOR SAP ERP 6.0 SP09</li>\n<li>EHP4 FOR SAP ERP 6.0 No Minimum SP required.</li>\n<li>   This is not relevant for systems which do not have vendors with assigned contacts( You can also verify  for existence of records in table KNVK where LIFNR has non initial values (KNVK-LIFNR &lt;&gt; ‘’).</li>\n</ul>\n</ul>\n<ul>\n<li>Conversions for DIMP-LAMA: see SAP Note <a href=\"/notes/2384347\" target=\"_blank\">2384347</a></li>\n<li>Restrictions in conversion paths (the following ADD-Ons cannot be converted to SAP S/4HANA 1610 in one step but require an intermediate upgrade step):</li>\n<ul>\n<li>The Israeli Annexing Transport solution (see SAP Note <a href=\"/notes/2298073\" target=\"_blank\">2298073</a> for more details)</li>\n<li>SRM_SERVER 550/SRM_PLUS 550 (see SAP Note <a href=\"/notes/2251946\" target=\"_blank\">2251946</a> for more details)</li>\n</ul>\n<li>For restrictions concerning Cost Of Goods Manufactured (COGM) please see SAP Note <a href=\"/notes/2270414\" target=\"_blank\">2270414</a>.</li>\n<li>For restrictions concerning New Inventory Management Data Model , see Sap Note <a href=\"/notes/2493434\" target=\"_blank\">2493434</a>.</li>\n<li>Customers which are already using PRF (Procurement Reporting Framework) or are planning to use it after the conversion / upgrade to SAP S/4HANA have to convert / upgrade to SAP S/4HANA 1610 FPS02 or higher. For more information see SAP Note <a href=\"/notes/2481876\" target=\"_blank\">2481876</a>.</li>\n<li>Please also check the linked SAP Notes for restrictions.</li>\n</ul>\n<li>Platform restrictions:</li>\n<ul>\n<li>Conversions on Sybase: SAP Notes <a href=\"/notes/2383862\" target=\"_blank\">2383862</a> and <a href=\"/notes/2384739\" target=\"_blank\">2384739</a><br/>Please also check the linked SAP Notes for restrictions.</li>\n<li>Restrictions for SAP S/4HANA 1610 on SAP HANA 2.0:</li>\n<ul>\n<li>Restriction in LoB Manufacturing MM-IM, see SAP Note <a href=\"/notes/2440007\" target=\"_blank\" title='2440007  - Error in Fiori App \"Post Goods Receipt for Purchase Order\" (F0843) due to bug inSAP HANA 2.0 SPS 00 Database Revision 001'>2440007</a></li>\n</ul>\n</ul>\n<li>SAP HANA 2.0 SPS01 Revision 10: </li>\n<ul>\n<li>Usage of LiveCache not supported </li>\n<li>Migration from HANA 1.0 to 2.0 on Power platform not supported</li>\n</ul>\n<li>SAP HANA 2.0 SPS01 Revision 11: \r\n<ul>\n<li>New installations and conversions using DMO (<a href=\"/notes/2478183\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2478183</a>)</li>\n</ul>\n</li>\n</ul>\n</ul>\n<p> </p>", "noteVersion": 29}, {"note": "2499716", "noteTitle": "2499716 - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Control", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several data sources for BW extraction are no longer available with S/4 in the area of Production Planning and Control as part of the product version SAP S/4HANA, on-premise edition 1511 and above</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, BW, Extractors, Production Planning and Control</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following DataSources are not working any longer</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"212\"/> <col width=\"105\"/> <col width=\"90\"/> <col width=\"250\"/></colgroup>\n<tbody>\n<tr>\n<td height=\"17\" width=\"212\">DataSource</td>\n<td width=\"105\">Appl.component</td>\n<td width=\"90\">Restriction</td>\n<td width=\"250\">Comment</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_DOWNTIME</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">0PP_MD_APPLID_TEXT</td>\n<td class=\"xl65\">PP</td>\n<td class=\"xl66\">Deprecated</td>\n<td class=\"xl66\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_CAPACITY_OPS</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_CAPACITY_REQ</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_KPI</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_MATERIAL</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_OPR</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_OPR_MACH</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_OPR_MATL</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_OPR_PRED</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_OPR_SUCC</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_OPR_TOOL</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_OPS_MAT_AVAIL</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_ORD_CAP</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_ORDER</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_ORDER_KPI</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_PLANACTUAL_MCC</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_PLANACTUAL_PCC</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_PLANACTUAL_PCOC</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_PLANACTUAL_TCC</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_TIMEBUCKET</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_TOOLS</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">0PP_MD_URL_TEXT</td>\n<td class=\"xl65\">PP</td>\n<td class=\"xl66\">Deprecated</td>\n<td class=\"xl66\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">0PP_MD_VAR_ATTR</td>\n<td class=\"xl65\">PP</td>\n<td class=\"xl66\">Deprecated</td>\n<td class=\"xl66\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl67\" height=\"17\" width=\"212\">0PP_MD_WORKCENTER</td>\n<td class=\"xl67\" width=\"105\">PP</td>\n<td class=\"xl67\" width=\"90\">Deprecated</td>\n<td class=\"xl67\" width=\"250\">DS not working - no alternative exists</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 4}, {"note": "2232584", "noteTitle": "2232584 - Release of SAP extractors for ODP replication (ODP SAPI)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use ODP data replication to replicate data from an SAP system using one of the provided ODP consumer contexts (SAP BW, SAP DataServices using ODP-API, or OData (as of SAP BW 7.50 )) to a target system (such as SAP BW/4HANA).<br/>This SAP Note explains which DataSources SAP has released for ODP replication.</p>\n<p>This SAP Note encompasses the SAP extractors already released in the individual SAP Notes 1558737, 1706582, and 1806637, which are now obsolete.</p>\n<p>If your source system is an SAP S/4HANA system, refer also to SAP Note <a href=\"/notes/2500202\" target=\"_blank\">2500202</a>, which explains further restrictions for S-API extractors especially for SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Extraction, transformation and loading, ETL, ETL interface, operational delta queue, operational data provider, DataSource, ODP, ODP data replication, application programming interface, API, BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>To enable use of ODP data replication you must first implement the Support Packages or SAP Notes described in the following SAP Notes in your system:</p>\n<p>- SAP Note <a href=\"/notes/1521883\" target=\"_blank\">1521883</a> (ODP Replication API 1.0 ) for SAP_BASIS &lt; 730</p>\n<p>- SAP Note <a href=\"/notes/1931427\" target=\"_blank\">1931427</a> (ODP Replication API 2.0 ) for SAP Basis &gt;= 730</p>\n<p></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You can release the extractors as follows:</p>\n<p><strong>- SAP extractors:</strong></p>\n<p>Most of the delivered SAP extractors are already released for ODP replication. You can release the relevant DataSources in your SAP source system by implementing the current version of this SAP Note in your source system and then executing the program <span &#23435;&#20307;;=\"\" ar-sa;\"=\"\" courier=\"\" en-us;=\"\" minor-fareast;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new';=\"\">BS_ANLY_DS_RELEASE_ODP</span> in the relevant source system. The program writes corresponding entries to the ODP control table ROOSATTR, thus exposing them to the operational data provisioning framework.</p>\n<p>Please note also that in addition to the DataSources that are stated explicitly in the program, a number of additional DataSources are released for ODP replication by means of the entry 0$GENERIC_EXTRACTION. These are DataSources whose extraction method (table ROOSOURCE, field EXMETHOD) is either 'D' (domain-based extraction) or 'V' (table-based or view-based extraction). For these DataSources, there is therefore no need to write separate entries to ROOSATTR.</p>\n<p>For an Excel list of ALL SAP extractors that are currently released for ODP, see the attachment to this SAP Note.</p>\n<p>Pay attention to valid restrictions for the ODP release as described in KBA <a href=\"/notes/2407906\" target=\"_blank\">2407906</a>, SAP Note <a href=\"/notes/2481315\" target=\"_blank\">2481315</a>, and SAP Note <a href=\"/notes/1932459\" target=\"_blank\">1932459</a>.</p>\n<p>If you use the Logistics Cockpit for ODP extraction with the option 'Unserialized V3 Update', see SAP Note <a href=\"/notes/2758147\" target=\"_blank\">2758147</a>.</p>\n<p>For other SAP extractors that are not currently released for ODP replication but that you require, please open an incident in the application component BW-BCT-GEN.</p>\n<p> </p>\n<p><strong><strong>- Extractors/DataSources generated by SAP applications in the SAP namespace (not Z*) </strong></strong></p>\n<p>The following generated extractors/DataSources are also released by ODP replication using the program BS_ANLY_DS_RELEASE_ODP (see above):</p>\n<p>- Generated CO-PA DataSources whose technical name starts with <em>1_CO_PA_</em>, <em>0_CO_PA_HIER_</em>, or <em>0G_C</em></p>\n<p>- Generated classification DataSources (CA-CL) whose technical name starts with <em>1CL</em></p>\n<p>- Generated special ledger DataSources (FI-SL) whose technical name starts with <em>3FI_</em></p>\n<p>- Generated special ledger hierarchy DataSources (FI-SL) whose technical name starts with 4R_</p>\n<p>- Generated Bank Analyzer DataSources (FS-BA) whose technical name starts with <em>1_BA_BA_CV</em></p>\n<p>- Generated Public Sector Collection and Disbursement (PSCD) DataSources whose technical name starts with <em>3PSCD_TRM_FORM_DATA</em></p>\n<p>For DataSources above with $COMPLETE in ROOSATTR, see SAP Note <a href=\"/notes/1585204\" target=\"_blank\">1585204</a>.</p>\n<p>- Generated APO DataSources whose technical name starts with <em>9A</em> can be released selectively using the program /SAPAPO/PAREA_EXTR_EXPOSE after the implementation of SAP Note <a href=\"/notes/2085981\" target=\"_blank\">2085981</a>.</p>\n<p>The extractors/DataSources 0STATUSxxxx_TEXT generated in SAP Note <a href=\"/notes/300300\" target=\"_blank\">300300</a> can be released for ODP replication as described in SAP Note <a href=\"/notes/2825113\" target=\"_blank\">2825113</a>.<br/>Replace the DataSource name 0STATUSSYS3_TEXT from SAP Note 2825113 with the name you generated.</p>\n<p><strong>- Partner content extractors</strong></p>\n<p> - VISTEX DataSources: Please follow the instructions in SAP Note <a href=\"/notes/2522360\" target=\"_blank\">2522360</a> to release DataSources delivered by the SAP partner VISTEX for ODP replication.</p>\n<p> </p>\n<p><strong>- Customer-specific extractors: </strong></p>\n<p>Following the implementation of SAP Note <a href=\"/notes/2350464\" target=\"_blank\">2350464</a> (\"Creation of Generic DataSource in RSO2 - automatic release for ODP\"), customer-defined extractors are automatically released for ODP. Alternatively, you can use the program RODPS_OS_EXPOSE to directly release DataSources in the customer namespace (starting with letters) in the source system. For more information, see SAP Note <a href=\"/notes/1585204\" target=\"_blank\">1585204</a>.</p>\n<p> </p>\n<p>Information about the operational data provisioning (ODP) framework is available in the SAP Community Network:</p>\n<p><a href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\" target=\"_blank\">https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/</a></p>", "noteVersion": 35}, {"note": "2499589", "noteTitle": "2499589 - Restrictions for BW extractors relevant for S/4HANA in the area of Quality Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Several data sources for BW extraction are no longer available with S/4 in the area of Quality Management as part of the product version SAP S/4HANA, on-premise edition 1511 and above</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, BW, Extractors, QM, QAM, QIM, IAM-GEN, Quality Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>See the reasons given below</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The following DataSources are not working any longer</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"236\"/> <col width=\"135\"/> <col width=\"132\"/> <col width=\"227\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl67\" height=\"67\" width=\"236\">DataSource</td>\n<td class=\"xl67\" width=\"135\">Appl.component</td>\n<td class=\"xl67\" width=\"132\">Restriction</td>\n<td class=\"xl67\" width=\"227\">Comment</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/ACTIVITY_CATEGORY</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/ACTIVITY_LCYCLE_STAT_CD</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/ACTIVITY_ORIGIN_CD</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/ACTIVITY_TEMPLATE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/ACTIVITY_TYPE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/ACT_RESPONSE_VALUE_BOOL</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl68\" height=\"17\">/IAM/APPLICATION</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/APPROVAL_STATUS_CD</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/AUTHORIZATION_GROUP</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/CODE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/CODE_GROUP</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/CRITICALITY</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/DEFERRAL_STATUS_CD</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/DIGIT_SIGN_CD</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/DOC_TYPE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/DURATION</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/EFFECTIVENESS_CD</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/ISSUE_CATEGORY</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/ISSUE_LCYCLE_STAT_CD</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/ISSUE_TYPE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/LOGICAL_SYSTEM</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/MIMECODE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/OBJ_SUBTYPE_CODE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/OBJ_TYPE_CODE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/PARTY_ROLE_CODE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/PRIORITY</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/PROCESSING_CD</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/QUANTITY_ROLE_CODE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/RESPONSIBLE_IND</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/TIME_POINT_ROLE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/TIME_ZONE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/UOM</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/USER_STATUS_01</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/USER_STATUS_02</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/USER_STATUS_03</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/USER_STATUS_04</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/USER_STATUS_05</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/USER_STATUS_PROFILE</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">/IAM/VALIDATION_CD</td>\n<td class=\"xl65\">IAM-GEN</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">0QAM_ACTIVITIES</td>\n<td class=\"xl65\">QAM</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">0QAM_ISSUES</td>\n<td class=\"xl65\">QAM</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">0QN_NOTIF_HEADER</td>\n<td class=\"xl65\">QM</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">0QN_NOTIF_ITEM</td>\n<td class=\"xl65\">QM</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n<tr>\n<td class=\"xl65\" height=\"17\">0QN_NOTIF_TASK</td>\n<td class=\"xl65\">QM</td>\n<td class=\"xl70\">Deprecated</td>\n<td class=\"xl71\">DS not working - no alternative exists</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 2}]}], "activities": [{"Activity": "Interface Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Please also refer to SAP note 2500202 of simplification item S4TWL - BW Extractors in SAP S/4HANA."}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "IS-U Stock and Transaction Statistics based on Logistics Information System (LIS) is not available within SAP S/4HANA. Successor functionality: CDS view based virtual data model for Utilities"}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "This activity depends on the decision whether CDS view based queries and info cubes will be used on SAP S/4HANA"}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "This activity depends on the decision whether CDS view based queries and info cubes will be used on SAP S/4HANA"}]}