{"guid": "901B0E6D3F311ED69D807BF7EA7180CB", "sitemId": "SI23: PPM_INTGR_XPD_01", "sitemTitle": "S4TWL - xPD Integration in SAP Portfolio and Project Management for SAP S/4HANA", "note": 2361181, "noteTitle": "2361181 - S4TWL - xPD Integration in SAP Portfolio and Project Management for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong><strong>Description</strong></strong></strong></p>\n<p>In SAP Portfolio and Project Management for SAP S/4HANA, the SAP xPD integration is no longer available.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>It is no longer possible to exchange data between SAP Portfolio and Project Management for SAP S/4HANA and SAP xPD.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>SAP recommends to evaluate SAP Innovation Management as a substitute.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "In SAP Portfolio and Project Management for SAP S/4HANA, the SAP xPD integration is no longer available. Hence, it is no longer possible to exchange data between SAP Portfolio and Project Management for SAP S/4HANA and SAP xPD. SAP recommends to evaluate SAP Innovation Management as a substitute. Please, see SAP Note 2361181 for more information."}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}]}