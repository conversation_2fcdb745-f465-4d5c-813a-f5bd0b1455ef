{"guid": "00109B13199E1ED9A1C22CB14A2B00E9", "sitemId": "SI05: Insurance_FS-RI", "sitemTitle": "S4TWL - FS-RI - Deletion of LSMW Integration", "note": 2887844, "noteTitle": "2887844 - S4TWL - Deletion of LSMW integration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are performing a system conversion to SAP S/4HANA. In this case, the following entry in the worklist for the transition to SAP S/4HANA applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>LSMW</p>\n<p>IDocs</p>\n<p>/MSG/VRP_BA_IMPORT_DTA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note is relevant to you if your source release for the system conversion is an ERP release.</p>\n<p>It is not relevant if your source release is SAP S/4HANA Finance or SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The \"Import of a Data Transmission\" background job is no longer available In SAP Reinsurance Management for SAP S/4HANA and in SAP S/4HANA Insurance for reinsurance management. When you navigate to a data transmission, the \"LSMW\" and \"IDocs\" tab pages and the import button that are displayed when the data transmission is being processed are also no longer available.</p>\n<p><strong>Information related to the business process</strong></p>\n<p>The impact on business processes depends on the use of the obsolete objects.</p>\n<p><strong>Required and recommended action(s)</strong></p>\n<p>Check whether you used the obsolete objects. If this is the case, you must implement your own solution for importing a data transmission, if you do not do so already.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "New developments", "Phase": "Any time", "Condition": "Optional", "Additional_Information": "Implement your own solution for importing a data transmission."}]}