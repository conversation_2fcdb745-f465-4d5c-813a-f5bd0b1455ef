{"guid": "00109B131AF41EDDBA9F2B7D9C4FC117", "sitemId": "SI15: FIN_CO", "sitemTitle": "S4TWL - Profitability Segment Number Change of Type", "note": 3320010, "noteTitle": "3320010 - S4TWL - Profitability Segment Number Change of Type", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use CO-PA and are upgrading to SAP S/4HANA release 2023 or a later release from SAP S/4HANA release 2022 or earlier.</p>\n<p>During this upgrade, the data type of the profitability segment number has been changed from data type NUMC length 10 to data type CHAR length 10.</p>\n<p>This enables the use of alphanumeric profitability numbers after the decimal profitability segments have run out.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Domain RKEOBJNR. Data element RKEOBJNR. Global field ProfitabilitySegment. Profitability Analysis. Margin Analysis. Costing-Based profitability analysis.</p>\n<p>Decommissioning of CDS field ProfitabilitySegment. Replace usages with ProfitabilitySegment_2</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use CO-PA and are upgrading to SAP S/4HANA release 2023 or a later release from SAP S/4HANA release 2022 or earlier. The domain change is relevant for Margin Analysis and Costing-Based Profitability Analysis.</p>\n<p><strong>Task</strong>: Check that Profitability Analysis is activated in your system.</p>\n<p>Procedure: Go to the implementation guide using transaction SPRO --&gt; Controlling --&gt; Profitability Analysis --&gt; Flows of Actual Values --&gt; Activate Profitability Analysis. If there is a check mark in the columns “costing-based” or “account-based” this SAP Note is relevant for you.</p>\n<p><strong>Changes to Customer Coding Required</strong></p>\n<p>The domain change has several technical consequences which are explained in detail in SAP Note 3320427. This note contains a detailed list of the different technical consequences of the type change, and how you must adapt your coding. The two following aspects illustrate the necessary code changes:</p>\n<p><strong>Checks that a Profitability Segment is Assigned - IS INITIAL Checks</strong></p>\n<p>The ABAP statements \"Is initial\" and \"is not initial\" can no longer be used to check if fields of type RKEOBJNR are empty.</p>\n<p>The previous initial value of profitability segment number was '**********, while the new initial value will be 'space'. Since the existing database entries containing the old initial value are not updated, either the old or the new initial value can occur and must be checked. In order to encapsulate the complexity of the initial (or not initial) check we recommend to use the method cl_fco_copa_paobjnr=&gt;is_initial( ) to check if a field is empty.</p>\n<p><strong>Alphanumeric Values</strong></p>\n<p>In case you expect, in the next five years, to use more than 50% of the available profitability segment numbers, we suggest to prepare your code for alphanumeric numbers that you may require in the future. Please see the SAP Note 3321347 for more details.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<h3 data-toc-skip=\"\">Business-Process Related Information</h3>\n<p>The change of the data type of profitability segment number has no business-related impact.</p>\n<h3 data-toc-skip=\"\"><strong>Required and Recommended Actions</strong></h3>\n<p>To avoid any errors in the system behaviour due to the change of the data type in the profitability segment field, you need to adapt your customer coding for this change. The adaption of the initial value check needs to be carried out, regardless of whether you will be using the additional alphanumeric profitability segments or not.</p>\n<p><strong>Decommissioning of Field ProfitabilitySegment in C1 Released CDS Views</strong></p>\n<p>In order to identify coding that needs to be adapted, the Code Inspector checks are executed as part of the related simplification item. If you are using the decommissioned CDS field ProfitabilitySegment in customer-defined CDS Views, you must switch to the replacement field ProfitabilitySegment_2.</p>\n<p>This SAP Note only covers the actions that all customers must take. <em>For customers that require more profitability segment numbers see SAP Note &lt;needs to be created&gt;.</em></p>\n<p><span>What to Do Before the Upgrade</span></p>\n<p>Please ensure that you update the check logic to the most recent version by implementing all relevant SAP Notes from SAP Note 2436688,  otherwise you might get wrong or missing check results.</p>\n<p>Whether you are using the initial check or select statement, please investigate if you have set PAOBJNR to 'empty' in your customer coding. The change of the underlying technical domain in the ABAP dictionary could lead to changed behavior when moving profitability segment numbers into different types, or when performing calculations.</p>\n<p>As described before, the initial value of the field PAOBJNR will be 'space' rather than '**********' for new profitability segments. However, old values on the database will not be updated during the upgrade process. This means that the code checking whether the field PAOBJNR is empty or not, needs to check for both values:</p>\n<p><strong>ABAP Code Examples</strong></p>\n<p>ABAP code such as</p>\n<p>    <span class=\"SNO_DNT\" translate=\"no\">data: lv_paobjnr type rkeobjnr.<br/></span><span><br/>       ...   </span></p>\n<p><span>   <em>\" check if prof. segment isn initial</em></span><em><br/></em><span>    IF </span><span>lv_paobjnr IS INITIAL.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">        ...</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    ENDIF.</span></p>\n<p>has to be changed to</p>\n<p>    <span class=\"SNO_DNT\" translate=\"no\">data: lv_paobjnr type rkeobjnr.</span><br/><span class=\"SNO_DNT\" translate=\"no\">     ...   </span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">     <em>\" check if prof. segment is initial using predefined constant values <br/>     \" for either the new or the old initial value<br/></em></span><span><br/>    IF cl_fco_copa_paobjnr=&gt;is_initial( lv_paobjnr) = abap_true</span><span>.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">        ...</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    ENDIF.</span></p>\n<p> Likewise the IS NOT INITIAL check has to be changed as follows:</p>\n<p>  <span class=\"SNO_DNT\" translate=\"no\">data: lv_paobjnr type rkeobjnr.</span><br/><span class=\"SNO_DNT\" translate=\"no\">       ...   </span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    <em>\" check if prof. segment is not initial<br/></em>    IF lv_paobjnr IS NOT INITIAL.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">        ...</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    ENDIF.﻿</span></p>\n<p>﻿has to be changed to﻿</p>\n<p>  <span class=\"SNO_DNT\" translate=\"no\">data: lv_paobjnr type rkeobjnr.</span><br/><span class=\"SNO_DNT\" translate=\"no\">       ...   </span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    <em>\" check if prof. segment is not initial using predefined constant values <br/>     \" for either the new and the old initial value</em></span><br/><span class=\"SNO_DNT\" translate=\"no\">    IF cl_fco_copa_paobjnr=&gt;is_initial( lv_paobjnr) = abap_false.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">        ...</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    ENDIF.﻿</span></p>", "noteVersion": 4, "refer_note": [{"note": "3320427", "noteTitle": "3320427 - Profitability Segment Number Change of Type - More details on the coding adjustments", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use CO-PA and are upgrading to SAP S/4HANA release 2023, or a later release from SAP S/4HANA release 2022 or earlier.</p>\n<p>The data type of field Profitability Segment Number has been changed from NUMC length 10 to type CHAR length 10.</p>\n<p>The initial value of the Profitability Segment Number field changes from **********00 to \"space\" with the upgrade to SAP S/4HANA release 2023.</p>\n<p>You need to adapt your customer coding for this change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Domain RKEOBJNR. Data element RKEOBJNR. Global field ProfitabilitySegment. Profitability Analysis. Margin Analysis. Costing-Based profitability analysis.</p>\n<p>Decommissioning of CDS field ProfitabilitySegment. Replace usages with ProfitabilitySegment_2</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use CO-PA and are upgrading to SAP S/4HANA release 2023, or a later release from SAP S/4HANA release 2022 or earlier. The domain change is relevant for Margin Analysis and Costing-Based profitability analysis.</p>\n<p>The data type of the Profitability Segment Number is defined by the domain RKEOBJNR. The former definition of the Profitability Segment Number field (PAOBJNR) did not support as many profitability segments as large customers require.</p>\n<p>While fields of data type CHAR behave differently than fields of NUMC, you need to adjust your coding to ensure that the correct behavior is retained.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<h3 data-toc-skip=\"\">You must follow the instructions given in the attached document.</h3>\n<h3 data-toc-skip=\"\"></h3>\n<p>The following list shows the relevant message types from the Code Inspector results.</p>\n<p><span>Relevant</span><strong> Message Types from category Errors</strong></p>\n<p>AR_OPER, AR_TYPE, ARC_OPER, MVC_TYPE, Code TYPE, TYPE_CMP</p>\n<h3 data-toc-skip=\"\">Relevant<strong> Message Types from category Information</strong></h3>\n<p>MOVE_CUTO</p>", "noteVersion": 3, "refer_note": [{"note": "3320010", "noteTitle": "3320010 - S4TWL - Profitability Segment Number Change of Type", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use CO-PA and are upgrading to SAP S/4HANA release 2023 or a later release from SAP S/4HANA release 2022 or earlier.</p>\n<p>During this upgrade, the data type of the profitability segment number has been changed from data type NUMC length 10 to data type CHAR length 10.</p>\n<p>This enables the use of alphanumeric profitability numbers after the decimal profitability segments have run out.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Domain RKEOBJNR. Data element RKEOBJNR. Global field ProfitabilitySegment. Profitability Analysis. Margin Analysis. Costing-Based profitability analysis.</p>\n<p>Decommissioning of CDS field ProfitabilitySegment. Replace usages with ProfitabilitySegment_2</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use CO-PA and are upgrading to SAP S/4HANA release 2023 or a later release from SAP S/4HANA release 2022 or earlier. The domain change is relevant for Margin Analysis and Costing-Based Profitability Analysis.</p>\n<p><strong>Task</strong>: Check that Profitability Analysis is activated in your system.</p>\n<p>Procedure: Go to the implementation guide using transaction SPRO --&gt; Controlling --&gt; Profitability Analysis --&gt; Flows of Actual Values --&gt; Activate Profitability Analysis. If there is a check mark in the columns “costing-based” or “account-based” this SAP Note is relevant for you.</p>\n<p><strong>Changes to Customer Coding Required</strong></p>\n<p>The domain change has several technical consequences which are explained in detail in SAP Note 3320427. This note contains a detailed list of the different technical consequences of the type change, and how you must adapt your coding. The two following aspects illustrate the necessary code changes:</p>\n<p><strong>Checks that a Profitability Segment is Assigned - IS INITIAL Checks</strong></p>\n<p>The ABAP statements \"Is initial\" and \"is not initial\" can no longer be used to check if fields of type RKEOBJNR are empty.</p>\n<p>The previous initial value of profitability segment number was '**********, while the new initial value will be 'space'. Since the existing database entries containing the old initial value are not updated, either the old or the new initial value can occur and must be checked. In order to encapsulate the complexity of the initial (or not initial) check we recommend to use the method cl_fco_copa_paobjnr=&gt;is_initial( ) to check if a field is empty.</p>\n<p><strong>Alphanumeric Values</strong></p>\n<p>In case you expect, in the next five years, to use more than 50% of the available profitability segment numbers, we suggest to prepare your code for alphanumeric numbers that you may require in the future. Please see the SAP Note 3321347 for more details.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<h3 data-toc-skip=\"\">Business-Process Related Information</h3>\n<p>The change of the data type of profitability segment number has no business-related impact.</p>\n<h3 data-toc-skip=\"\"><strong>Required and Recommended Actions</strong></h3>\n<p>To avoid any errors in the system behaviour due to the change of the data type in the profitability segment field, you need to adapt your customer coding for this change. The adaption of the initial value check needs to be carried out, regardless of whether you will be using the additional alphanumeric profitability segments or not.</p>\n<p><strong>Decommissioning of Field ProfitabilitySegment in C1 Released CDS Views</strong></p>\n<p>In order to identify coding that needs to be adapted, the Code Inspector checks are executed as part of the related simplification item. If you are using the decommissioned CDS field ProfitabilitySegment in customer-defined CDS Views, you must switch to the replacement field ProfitabilitySegment_2.</p>\n<p>This SAP Note only covers the actions that all customers must take. <em>For customers that require more profitability segment numbers see SAP Note &lt;needs to be created&gt;.</em></p>\n<p><span>What to Do Before the Upgrade</span></p>\n<p>Please ensure that you update the check logic to the most recent version by implementing all relevant SAP Notes from SAP Note 2436688,  otherwise you might get wrong or missing check results.</p>\n<p>Whether you are using the initial check or select statement, please investigate if you have set PAOBJNR to 'empty' in your customer coding. The change of the underlying technical domain in the ABAP dictionary could lead to changed behavior when moving profitability segment numbers into different types, or when performing calculations.</p>\n<p>As described before, the initial value of the field PAOBJNR will be 'space' rather than '**********' for new profitability segments. However, old values on the database will not be updated during the upgrade process. This means that the code checking whether the field PAOBJNR is empty or not, needs to check for both values:</p>\n<p><strong>ABAP Code Examples</strong></p>\n<p>ABAP code such as</p>\n<p>    <span class=\"SNO_DNT\" translate=\"no\">data: lv_paobjnr type rkeobjnr.<br/></span><span><br/>       ...   </span></p>\n<p><span>   <em>\" check if prof. segment isn initial</em></span><em><br/></em><span>    IF </span><span>lv_paobjnr IS INITIAL.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">        ...</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    ENDIF.</span></p>\n<p>has to be changed to</p>\n<p>    <span class=\"SNO_DNT\" translate=\"no\">data: lv_paobjnr type rkeobjnr.</span><br/><span class=\"SNO_DNT\" translate=\"no\">     ...   </span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">     <em>\" check if prof. segment is initial using predefined constant values <br/>     \" for either the new or the old initial value<br/></em></span><span><br/>    IF cl_fco_copa_paobjnr=&gt;is_initial( lv_paobjnr) = abap_true</span><span>.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">        ...</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    ENDIF.</span></p>\n<p> Likewise the IS NOT INITIAL check has to be changed as follows:</p>\n<p>  <span class=\"SNO_DNT\" translate=\"no\">data: lv_paobjnr type rkeobjnr.</span><br/><span class=\"SNO_DNT\" translate=\"no\">       ...   </span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    <em>\" check if prof. segment is not initial<br/></em>    IF lv_paobjnr IS NOT INITIAL.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">        ...</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    ENDIF.﻿</span></p>\n<p>﻿has to be changed to﻿</p>\n<p>  <span class=\"SNO_DNT\" translate=\"no\">data: lv_paobjnr type rkeobjnr.</span><br/><span class=\"SNO_DNT\" translate=\"no\">       ...   </span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    <em>\" check if prof. segment is not initial using predefined constant values <br/>     \" for either the new and the old initial value</em></span><br/><span class=\"SNO_DNT\" translate=\"no\">    IF cl_fco_copa_paobjnr=&gt;is_initial( lv_paobjnr) = abap_false.</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">        ...</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">    ENDIF.﻿</span></p>", "noteVersion": 4}]}, {"note": "2436688", "noteTitle": "2436688 - Recommended SAP Notes for Using S/4HANA Custom Code Checks in ATC or Custom Code Migration App", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using ABAP Test Cockpit (ATC) or the Custom Code Migration Fiori app to perform the S/4HANA custom code checks.</p>\n<p>This SAP Note summarizes recommended SAP Notes for these scenarios.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, custom code analysis, custom code adaptation, system conversion, Notes</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Note Assistant has been updated to its latest version. Therefore, ensure that you have applied the latest version of the following SAP Notes in your system:</p>\n<ul>\n<li>1668882 - Note Assistant: Important notes for SAP_BASIS 730,731,740,750,751,752,753)</li>\n<li>2730170 - SCWB/SNOTE Activation processes Methods in DDIC Phase</li>\n<li>2844646 - Ignore the local class definition, implementation, definition deferred, definition load, definition local friends changes</li>\n<li>2910608 - Issue with applying changes to 'TYPES', 'DATA' or 'CONSTANTS' within ABAP Class definition</li>\n<li>2971435 - SNOTE - Delta calculation Issue when ‘mod unit’ positions are changed</li>\n<li>2970782 - Nested interfaces are not deleted by SAP Note Assistant</li>\n<li>3051466 - Note Assistant Made Easy : Revamped Note Assistant</li>\n<li>3200109 - SNOTE - Note Analyzer</li>\n<li>3218983 - Order of nested interfaces is not considered in correction instructions of SAP Notes <strong>   </strong></li>\n<li>3225158 - Nested interfaces are not created by SAP Note Assistant</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Important!</strong> Check whether your system fulfills the prerequisites mentioned above before you process the following steps.</p>\n<p>Update your local/central check system using Note Analyzer by processing the following steps:</p>\n<div title=\"Page 85\"><ol>\n<li><span>Launch transaction SNOTE.</span></li>\n<li><span>In the menu bar, select \"Goto &gt; other tools &gt; Launch Note Analyzer\".</span></li>\n<li><span>Upload the file \"SAPNote_2436688_Central_System.xml\" available as attachment to this note. You can process the notes using the Note Analyzer now.</span></li>\n</ol></div>\n<p>In case of a remote analysis or when using the Fiori app <em>Custom Code Migration</em>, it is also recommended to apply SAP Notes in checked systems (i.e., systems which will be analyzed by Custom Code Migration app or ATC). Update your <strong>checked systems</strong> using Note Analyzer by processing the following steps</p>\n<div title=\"Page 85\"><ol>\n<li><span>Launch transaction SNOTE.</span></li>\n<li><span>In the menu bar, select \"Goto &gt; Other tools &gt; Launch Note Analyzer\".</span></li>\n<li><span>Upload the file \"SAPNote_2436688_Checked_System.xml\" available as attachment to this note. You can process the notes using the Note Analyzer now.</span></li>\n</ol></div>\n<p><strong><em>Note</em></strong></p>\n<p><em>In case you were not able to use SAP Note Analyzer to apply the SAP Notes (as mentioned above), you would need to apply the SAP Notes manually. </em></p>\n<p><em>In the checked system, install also the following SAP Note:</em></p>\n<ol>\n<ul>\n<li><em>2599695 - Custom Code Migration Fiori App: Remote Stubs for the Checked System</em></li>\n<li><em>2888880 - Remote analysis: Bulk determination of method names</em></li>\n<li><em>2889487 - API: Corrections for remote API</em></li>\n<li><em>2742368 - REPOSITORY_ENVIRONMENT_ALL - environment analysis - appends error</em></li>\n</ul>\n</ol>\n<p><em><em>In your local/central check system, apply all SAP Notes mentioned below and all SAP Notes mentioned in SAP Note 2364916 (recommended SAP Notes for using ATC to perform a remote analysis).</em></em></p>\n<p><strong>In all Releases</strong></p>\n<p>Following SAP Notes are updated regularly. Please check whether you have applied the latest version of these SAP Notes:</p>\n<ul>\n<li>2364938 - Downport of the infrastructure of the SAP S/4HANA readiness checks</li>\n<li>2527903 - Remote analysis (for central check system)</li>\n</ul>\n<p><strong>SAP BASIS 7.58 (SP1 or lower)</strong></p>\n<ul>\n<li>3344072 - Custom Code Migration App - Correction bundle for S/4HANA 2023</li>\n</ul>\n<p><strong>SAP BASIS 7.57 (SP3 or lower)</strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n<li>3231871 - Custom Code Migration App - Correction bundle for S/4HANA 2022</li>\n</ul>\n<p><strong>SAP BASIS 7.57 (SP1 or lower)</strong></p>\n<ul>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n</ul>\n<p><strong>SAP BASIS 7.57 (SP0)</strong></p>\n<ul>\n<li>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</li>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n</ul>\n<p><strong>SAP BASIS 7.56 (SP5 or lower)</strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n<li>3107617 - Custom Code Migration App - Correction bundle for S/4HANA 2021</li>\n</ul>\n<p><strong>SAP BASIS 7.56 (SP3 or lower)</strong></p>\n<ul>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.56 (SP2 or lower)</strong></strong></p>\n<ul></ul>\n<ul>\n<li><span>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</span></li>\n<li><span>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition </span></li>\n</ul>\n<p><strong>SAP BASIS 7.56 (SP0)</strong></p>\n<ul>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3109122 - Execution of SAP S/4HANA Custom Code Checks Fails in ATC Developer Scenario</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.55 (SP7 or lower)</strong></strong></strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n<li>2964574 - Custom Code Migration App - Correction bundle for S/4HANA 2020</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.55 (SP5 or lower)</strong></strong></strong><strong><strong><strong>     </strong></strong></strong></p>\n<ul>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.55 (SP4 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</li>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.55 (SP3 or lower)</strong></strong></p>\n<ul>\n<li>3032974 - ATC check 'S/4HANA: Field Length Extension\" reports findings with message type \"Related Type\"</li>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3090106 - Check variant for SAP S/4HANA 2021 custom code checks</li>\n<li>3109122 - Execution of SAP S/4HANA Custom Code Checks Fails in ATC Developer Scenario</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.55 (SP2 or lower)</strong></strong></p>\n<ul>\n<li>3039646 - The ATC Check 'S/4HANA: Search for Usages of Simplified Objects' reports false-positive findings if only DDIC fields are simplified</li>\n<li>3045580 - Web Dynpro support in ATC check 'S/4HANA: Search for Usages of Simplified Objects'</li>\n<li>3057501 - Updated ATC check variants for SAP S/4HANA custom code checks<br/><em>Note: This SAP Note includes the new ATC Check \"S/4HANA: Idoc Check\"</em></li>\n<li>3066710 - Add sub object information to export for SAP Readiness Check 2.0</li>\n<li>3068784 - Exception CX_SY_RANGE_OUT_OF_BOUNDS in class CL_QF_DB_OPS_REPL_TABL</li>\n<li>3073012 - Exception CX_SY_ITAB_LINE_NOT_FOND in class CL_QF_DB_OPS_API_BSEG</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong>SAP BASIS 7.55 (SP1 or lower)</strong></p>\n<ul>\n<li>3027090 - ATC check \"S/4HANA: Field Length Extension\" leads to flickering results</li>\n<li>3026984 - S/4HANA: Search for simplified objects in literals</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.55 (SP0)</strong></strong></strong></strong></p>\n<ul>\n<li>2898180 - ATC check 'S/4HANA: Field Length Extension' reports false-positives for calls with generic parameters to SAP objects</li>\n<li>2942419 - Include ATC check 'S/4HANA: Readiness Check for SAP Queries' to the SAP S/4HANA custom code checks</li>\n<li>2939956 - Additional checks in ATC check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</li>\n<li>2946418 - Correction of runtime error with BSEG Quickfixes</li>\n<li>2971294 - S/4HANA custom code checks: Wrong behavior for multi-purpose fields in check S/4HANA field length extension</li>\n<li>2981370 - More categories considered by ATC check S/4HANA: Search for base tables of ABAP Dictionary views</li>\n<li>2991074 - Quick Fix: SELECTs from KONV with ORDER BY PRIMARY KEY</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.54 (SP9 or lower)</strong></strong></strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.54 (SP8 or lower)</strong></strong></p>\n<ul>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n<li>2809550 - Custom Code Migration App - Correction bundle for S/4HANA 1909</li>\n</ul>\n<p><strong>SAP BASIS 7.54 (SP7 or lower)</strong></p>\n<ul>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.54 (SP6 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.54 (SP5 or lower)</strong></strong></strong></p>\n<ul>\n<li>3032974 - ATC check 'S/4HANA: Field Length Extension\" reports findings with message type \"Related Type\"</li>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3090106 - Check variant for SAP S/4HANA 2021 custom code checks</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.54 (SP4 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>3027090 - ATC check \"S/4HANA: Field Length Extension\" leads to flickering results</li>\n<li>3026984 - S/4HANA: Search for simplified objects in literals</li>\n<li>3039646 - The ATC Check 'S/4HANA: Search for Usages of Simplified Objects' reports false-positive findings if only DDIC fields are simplified</li>\n<li>3045580 - Web Dynpro support in ATC check 'S/4HANA: Search for Usages of Simplified Objects'</li>\n<li>3057501 - Updated ATC check variants for SAP S/4HANA custom code checks<br/><em>Note: This SAP Note includes the new ATC Check \"S/4HANA: Idoc Check\"</em></li>\n<li>3066710 - Add sub object information to export for SAP Readiness Check 2.0</li>\n<li>3068784 - Exception CX_SY_RANGE_OUT_OF_BOUNDS in class CL_QF_DB_OPS_REPL_TABL</li>\n<li>3073012 - Exception CX_SY_ITAB_LINE_NOT_FOND in class CL_QF_DB_OPS_API_BSEG</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.54 (SP3 or lower)</strong></strong></p>\n<ul>\n<li>2925563 - Check variants for S/4HANA custom code checks without field length extensions</li>\n<li>2939956 - Additional checks in ATC check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</li>\n<li>2959341 - Check variant for SAP S/4HANA 2020 custom code checks</li>\n<li>2971294 - S/4HANA custom code checks: Wrong behavior for multi-purpose fields in check S/4HANA field length extension</li>\n<li>2981370 - More categories considered by ATC check S/4HANA: Search for base tables of ABAP Dictionary views</li>\n<li>2991074 - Quick Fix: SELECTs from KONV with ORDER BY PRIMARY KEY</li>\n</ul>\n<p><strong>SAP BASIS 7.54 (SP2 or lower)</strong></p>\n<ul>\n<li>2857006 - SAP S/4HANA: Readiness check for SAP queries</li>\n<li>2879257 - Reducing findings and quick fix for VBRK and VBRP in 'S/4HANA: Search for database operations'</li>\n<li>2896275 - Incorrect finding in test cl_ci_test_for_all_entr_hana</li>\n<li>2898180 - ATC check 'S/4HANA: Field Length Extension' reports false-positives for calls with generic parameters to SAP objects</li>\n<li>2905941 - ATC Check 'S/4HANA: Field Length Extension': Dump CX_SY_EXPORT_BUFFER_NO_MEMORY</li>\n<li>2900798 - Remove modification of shared symbol table</li>\n<li>2901862 - Correction cl_ci_test_no_order_by AMB_SINGLE</li>\n<li>2902053 - Downport of quickfix comments/defect correction for quickfixes</li>\n<li>2907328 - ATC in ADT: Take over results from ATC result browser into the ATC Problems View</li>\n<li>2911030 - Quick fix for syntax errors related to constants declared in include RVVBTYP</li>\n<li>2918372 - S/4HANA Field length extension: ATC ignores pseudo comments</li>\n<li>2918443 - ADT: Changes in function modules cannot be saved</li>\n<li>2919760 - Message code _SYMBNF_ during no-order check</li>\n<li>2923410 - Syntax warnings after applying quick fixes due to the usage of field MANDT in JOIN conditions</li>\n<li>2925515 - SQL analysis in ATC: Join aliases</li>\n<li>2927221 - Improvement of SQL-Quickfixes with alias or host variables</li>\n<li>2942419 - Include ATC check 'S/4HANA: Readiness Check for SAP Queries' to the SAP S/4HANA custom code checks</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.54 (SP1 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>2882569 - Coding created by quick fix for BSEG potentially leads to a runtime exception</li>\n<li>2874255 - False-Positive findings in ATC Check 'S/4HANA: Search for usages of simplified objects'</li>\n<li>2880652 - Check 'S/4HANA: Search for S/4-related syntax errors' shows wrong SAP Notes for findings</li>\n<li>2882279 - Transaction SYCM: Error when uploading the simplification database</li>\n<li>2885753 - S/4HANA: Field Length Extension - Information for findings is missing</li>\n<li>2873049 - ASSERTION_FAILED during check of global class</li>\n<li>2876160 - ATC check CL_CI_TEST_NO_ORDER_BY runs out of memory</li>\n<li>2894687 - Non-deterministic ATC findings with cross-program analysis</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.54 (SP0)</strong></strong></p>\n<ul>\n<li>2824538 - S/4HANA: Field length extension check: Referenced object type and application component are empty</li>\n<li>2806677 - ATC run shows finding 'Quick Fix Failure...'</li>\n<li>2823371 - Missing findings in ATC test cl_ci_test_no_order_by for SELECT statements with COUNT</li>\n<li>2824797 - \"on change\" is not recognized by the no-order check</li>\n<li>2833432 - Reduction of memory consumption in check 'S/4HANA: Field length extensions'</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP11 or lower)</strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n<li>3480456 - ATC result extractor SAP Readiness Check writes correct scope and usage</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.53 (SP10 or lower)</strong></strong></p>\n<ul>\n<li>3293605 - No BSEG Quickfixes for selects with group by or sql functions</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP9 or lower)</strong></p>\n<ul>\n<li>3235362 - Quick fix for SELECT FROM BSEG with JOIN condition or UP TO clause</li>\n<li>3243267 - Fix BSEG Quickfix endlessloop</li>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.53 (SP8 or lower)</strong></strong></strong></p>\n<ul>\n<li>3227781 - Incorrect quick fix for SELECT FROM BSEG APPENDING TABLE with additions FOR ALL ENTRIES</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.53 (SP7 or lower)</strong></strong></p>\n<ul>\n<li>3032974 - ATC check 'S/4HANA: Field Length Extension\" reports findings with message type \"Related Type\"</li>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3090106 - Check variant for SAP S/4HANA 2021 custom code checks</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong><strong><strong>SAP BASIS 7.53 (SP6 or lower)</strong></strong></strong></p>\n<ul>\n<li>3027090 - ATC check \"S/4HANA: Field Length Extension\" leads to flickering results</li>\n<li>3026984 - S/4HANA: Search for simplified objects in literals</li>\n<li>3039646 - The ATC Check 'S/4HANA: Search for Usages of Simplified Objects' reports false-positive findings if only DDIC fields are simplified</li>\n<li>3045580 - Web Dynpro support in ATC check 'S/4HANA: Search for Usages of Simplified Objects'</li>\n<li>3057501 - Updated ATC check variants for SAP S/4HANA custom code checks</li>\n<li>3066710 - Add sub object information to export for SAP Readiness Check 2.0</li>\n<li>3068784 - Exception CX_SY_RANGE_OUT_OF_BOUNDS in class CL_QF_DB_OPS_REPL_TABL</li>\n<li>3073012 - Exception CX_SY_ITAB_LINE_NOT_FOND in class CL_QF_DB_OPS_API_BSEG</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP5 or lower)</strong></p>\n<ul>\n<li><em>2939956 - Additional checks in ATC check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</em></li>\n<li>2959341 - Check variant for SAP S/4HANA 2020 custom code checks</li>\n<li>2971294 - S/4HANA custom code checks: Wrong behavior for multi-purpose fields in check S/4HANA field length extension</li>\n<li>2981370 - More categories considered by ATC check S/4HANA: Search for base tables of ABAP Dictionary views</li>\n<li>2991074 - Quick Fix: SELECTs from KONV with ORDER BY PRIMARY KEY</li>\n</ul>\n<p><strong><strong>SAP BASIS 7.53 (SP4 or lower)</strong></strong></p>\n<ul>\n<li>2857006 - SAP S/4HANA: Readiness check for SAP queries</li>\n<li>2879257 - Reducing findings and quick fix for VBRK and VBRP in 'S/4HANA: Search for database operations'</li>\n<li>2894687 - Non-deterministic ATC findings with cross-program analysis</li>\n<li>2896275 - Incorrect finding in test cl_ci_test_for_all_entr_hana</li>\n<li>2898180 - ATC check 'S/4HANA: Field Length Extension' reports false-positives for calls with generic parameters to SAP objects</li>\n<li>2900798 - Remove modification of shared symbol table</li>\n<li>2901862 - Correction cl_ci_test_no_order_by AMB_SINGLE</li>\n<li>2894687 - Non-deterministic ATC findings with cross-program analysis</li>\n<li>2918372 - S/4HANA Field length extension: ATC ignores pseudo comments</li>\n<li>2918443 - ADT: Changes in function modules cannot be saved</li>\n<li>2919760 - Message code _SYMBNF_ during no-order check</li>\n<li>2923410 - Syntax warnings after applying quick fixes due to the usage of field MANDT in JOIN conditions</li>\n<li>2925515 - SQL analysis in ATC: Join aliases</li>\n<li>2925563 - Check variants for S/4HANA custom code checks without field length extensions</li>\n<li>2927221 - Improvement of SQL-Quickfixes with alias or host variables</li>\n<li>2942419 - Include ATC check 'S/4HANA: Readiness Check for SAP Queries' to the SAP S/4HANA custom code checks</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP3 or lower)</strong></p>\n<ul>\n<li>2882569 - Coding created by quick fix for BSEG potentially leads to a runtime exception</li>\n<li>2874255 - False-Positive findings in ATC Check 'S/4HANA: Search for usages of simplified objects'</li>\n<li>2728715 - Custom Code Migration App - Correction bundle for S/4HANA 1809</li>\n<li>2880652 - Check 'S/4HANA: Search for S/4-related syntax errors' shows wrong SAP Notes for findings</li>\n<li>2882279 - Transaction SYCM: Error when uploading the simplification database</li>\n<li>2885753 - S/4HANA: Field Length Extension - Information for findings is missing</li>\n<li>2873049 - ASSERTION_FAILED during check of global class</li>\n<li>2876160 - ATC check CL_CI_TEST_NO_ORDER_BY runs out of memory</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP2 or lower)</strong></p>\n<ul>\n<li>2824538 - S/4HANA: Field length extension check: Referenced object type and application component are empty</li>\n<li>2812556 - Check variant for SAP S/4HANA 1909 custom code checks</li>\n<li>2806677 - ATC run shows finding 'Quick Fix Failure...'</li>\n<li>2725414 - NO_ORDER_BY ATC check does not process target lists in SELECT statements</li>\n<li>2738251 - Quick Fixes for the S/4HANA Custom Code Checks<br/><em><strong>Attention</strong> Consider the manual pre- and post-implementation steps of all dependent notes.<br/><em><strong>Attention</strong> Do not de-implement SAP Note 2738251.</em><br/></em></li>\n<li>2755679 - S/4HANA Custom Code Check 'S/4HANA: Search for usages of simplified objects' does not consider CDS</li>\n<li>2763301 - ATC S4HANA Readiness Checks: CX_SY_MOVE_CAST_ERROR in CL_S4H_QUICKFIX_PROVIDER</li>\n<li>2771060 - ATC S/4HANA ABAP Dictionary Checks - Redirection to dedicated items in Simplification Database patch level 8 and higher</li>\n<li>2768987 - Support field lists for database tables in S/4HANA simplification DB</li>\n<li>2791259 - Enablement of Quickfixes with modern SQL syntax for S/4HANA</li>\n<li>2823371 - Missing findings in ATC test cl_ci_test_no_order_by for SELECT statements with COUNT</li>\n<li>2824797 - \"on change\" is not recognized by the no-order check</li>\n<li>2833432 - Reduction of memory consumption in check 'S/4HANA: Field length extensions'</li>\n</ul>\n<p><strong>SAP BASIS 7.53 (SP1 or lower)</strong></p>\n<ul>\n<li>2712310 - Quick fix for ambiguous SELECT SINGLE</li>\n</ul>\n<p><strong>SAP BASIS 7.52 (SP13 or lower)</strong></p>\n<ul>\n<li>3365357 - Check Variant for SAP S/4HANA 2023 Custom Code Checks</li>\n</ul>\n<p><strong>SAP BASIS 7.52 (SP12 or lower)</strong></p>\n<ul>\n<li>3293011 - Extend ATC Result Extractor for S/4HANA</li>\n</ul>\n<p><strong>SAP BASIS 7.52 (SP11 or lower)</strong></p>\n<ul>\n<li>3245680 - S4HANA_READINESS_SAPQUERY: Verbesserter Remote-Check</li>\n<li>3231748 - Check variant for SAP S/4HANA 2022 custom code checks</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong></strong> 7.52 (SP9 or lower)</strong></p>\n<ul>\n<li>3032974 - ATC check 'S/4HANA: Field Length Extension\" reports findings with message type \"Related Type\"</li>\n<li>3103812 - ATC Check result export writes empty findings xml file</li>\n<li>3090106 - Check variant for SAP S/4HANA 2021 custom code checks</li>\n<li>3086267 - ATC check 'S/4HANA: Usages of Simplified Objects' does not report usages of simplified append fields</li>\n</ul>\n<p><strong><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong></strong></strong> 7.52 (SP8 or lower)</strong></p>\n<ul>\n<li>3027090 - ATC check \"S/4HANA: Field Length Extension\" leads to flickering results</li>\n<li>3026984 - S/4HANA: Search for simplified objects in literals</li>\n<li>3039646 - The ATC Check 'S/4HANA: Search for Usages of Simplified Objects' reports false-positive findings if only DDIC fields are simplified</li>\n<li>3045580 - Web Dynpro support in ATC check 'S/4HANA: Search for Usages of Simplified Objects'</li>\n<li>3057501 - Updated ATC check variants for SAP S/4HANA custom code checks</li>\n<li>3066710 - Add sub object information to export for SAP Readiness Check 2.0</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP7 or lower)</strong></p>\n<ul>\n<li>2939956 - Additional checks in ATC check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</li>\n<li>2959341 - Check variant for SAP S/4HANA 2020 custom code checks</li>\n<li>2971294 - S/4HANA custom code checks: Wrong behavior for multi-purpose fields in check S/4HANA field length extension</li>\n<li>2981370 - More categories considered by ATC check S/4HANA: Search for base tables of ABAP Dictionary views</li>\n</ul>\n<p><strong><strong><strong><strong>SAP BASIS 7.52 (SP6 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>2857006 - SAP S/4HANA: Readiness check for SAP queries</li>\n<li>2894687 - Non-deterministic ATC findings with cross-program analysis</li>\n<li>2896275 - Incorrect finding in test cl_ci_test_for_all_entr_hana</li>\n<li>2900798 - Remove modification of shared symbol table</li>\n<li>2918372 - S/4HANA Field length extension: ATC ignores pseudo comments</li>\n<li>2919760 - Message code _SYMBNF_ during no-order check</li>\n<li>2925515 - SQL analysis in ATC: Join aliases</li>\n<li>2925563 - Check variants for S/4HANA custom code checks without field length extensions</li>\n<li>2942419 - Include ATC check 'S/4HANA: Readiness Check for SAP Queries' to the SAP S/4HANA custom code checks</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP5 or lower)</strong></strong></p>\n<ul>\n<li>2874255 - False-Positive findings in ATC Check 'S/4HANA: Search for usages of simplified objects'</li>\n<li>2879257 - Reducing findings and quick fix for VBRK and VBRP in 'S/4HANA: Search for database operations'</li>\n<li>2880652 - Check 'S/4HANA: Search for S/4-related syntax errors' shows wrong SAP Notes for findings</li>\n<li>2882279 - Transaction SYCM: Error when uploading the simplification database</li>\n<li>2873049 - ASSERTION_FAILED during check of global class</li>\n<li>2876160 - ATC check CL_CI_TEST_NO_ORDER_BY runs out of memory</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP4 or lower)</strong></p>\n<ul>\n<li>2725414 - NO_ORDER_BY ATC check does not process target lists in SELECT statements</li>\n<li>2755679 - S/4HANA Custom Code Check 'S/4HANA: Search for usages of simplified objects' does not consider CDS</li>\n<li>2756171 - New check category for the S/4HANA search DB Operations Check for reading DB Operations</li>\n<li>2771060 - ATC S/4HANA ABAP Dictionary Checks - Redirection to dedicated items in Simplification Database patch level 8 and higher</li>\n<li>2812556 - Check variant for SAP S/4HANA 1909 custom code checks</li>\n<li>2768987 - Support field lists for database tables in S/4HANA simplification DB</li>\n<li>2823371 - Missing findings in ATC test cl_ci_test_no_order_by for SELECT statements with COUNT</li>\n<li>2824797 - \"on change\" is not recognized by the no-order check</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP3 or lower)</strong></p>\n<ul>\n<li>2659194 - Check variant for SAP S/4HANA 1809 custom code checks</li>\n<li>2737924 - Error displaying Simplification Item Category</li>\n<li>2749689 - Endless Loop in Check \"S/4HANA: Search for S/4 related syntax errors\"</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP2 or lower)</strong></strong></p>\n<ul>\n<li>2516160 - CI check \"Objects used in programs (remote)\" does not return all usages or reports \"Symbol ... not found\"</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP1 or lower)</strong></p>\n<ul>\n<li>2502402 - Display application component and referenced object in S/4HANA ATC readiness checks</li>\n<li>2578127 - Pseudo comments for S/4HANA custom code checks</li>\n<li>2577440 - Improvements in S/4HANA custom code check \"S/4HANA: Search for S/4 related syntax errors\"</li>\n<li>2569135 - S/4HANA custom code check \"S/4HANA: Search for S/4 related syntax errors\" reports syntax warnings</li>\n<li>2573527 - S/4HANA custom code check for buffered DB table access</li>\n<li>2522926 - S/4HANA: Search for ABAP Dictionary enhancements check: Suppress message on table with regenerated compatibility view</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.52 (SP0)</strong></p>\n<ul>\n<li>2503262 - Filter function in cl_ci_test_search_db_ops returns too many findings</li>\n<li>2505885 - Priorities of ATC check for S/4HANA use of simplified objects</li>\n<li>2531490 - Code Inspector variants S4HANA_READINESS: Setting in ABAP Dictionary checks for processing of modified objects</li>\n<li>2487726 - S/4HANA Readiness Checks: Check performance improvement</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP9 or lower)</strong></p>\n<ul>\n<li>2882279 - Transaction SYCM: Error when uploading the simplification database</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP8 or lower)</strong></p>\n<ul>\n<li>2725414 - NO_ORDER_BY ATC check does not process target lists in SELECT statements</li>\n<li>2768987 - Support field lists for database tables in S/4HANA simplification DB</li>\n</ul>\n<p><strong><strong><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP7 or lower)</strong></strong></strong></strong></p>\n<ul>\n<li>2749689 - Endless Loop in Check \"S/4HANA: Search for S/4 related syntax errors\"</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP6 or lower)</strong></strong></p>\n<ul>\n<li>2516160 - CI check \"Objects used in programs (remote)\" does not return all usages or reports \"Symbol ... not found\"</li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP5 or lower)</strong></p>\n<ul>\n<li>2569135 - S/4HANA custom code check \"S/4HANA: Search for S/4 related syntax errors\" reports syntax warnings</li>\n<li>2573527 - S/4HANA custom code check for buffered DB table access</li>\n<li>2522926- S/4HANA: Search for ABAP Dictionary enhancements check: Suppress message on table with regenerated compatibility view</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP3 or lower)</strong></strong></p>\n<ul>\n<li>2476990 - Misleading messages during the execution of a remote ATC check run</li>\n<li>2478717 - S/4HANA Readiness Check: SELECT FOR UPDATE not found in search for critical DB statements</li>\n<li>2485726 - Release Information for S/4HANA Readiness Checks <br/><em><strong>Attention</strong> Consider the manual pre- and post-implementation steps of this note.</em></li>\n<li>2494509 - Adjustments to message titles in checks with regard to S4HANA_READINESS</li>\n<li>2494150 - Adjustments to message titles in Code Inspector field length extensions check</li>\n<li>2503262 - Filter function in cl_ci_test_search_db_ops returns too many findings</li>\n</ul>\n<p><strong><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP2 or lower)</strong></strong></p>\n<ul>\n<li>2444208 - Merge of Code Inspector check variants for SAP S/4HANA readiness and SAP HANA analysis<br/><strong><em>Attention </em></strong><em>Consider the manual post steps of this note.</em></li>\n</ul>\n<p><strong>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP1 or lower)</strong></p>\n<ul>\n<li>2401298 - S/4HANA readiness Code Inspector check of ABAP Dictionary views: Supporting all classic view types</li>\n<li>2403110 – Improvements for Code Inspector Check \"Search for S/4HANA related syntax errors\"</li>\n</ul>\n<p><strong><span>SAP <strong><strong><strong><strong>BASIS</strong></strong></strong></strong> 7.51 (SP0)</span></strong></p>\n<ul>\n<li>2362362 - Runtime error in Code Inspector check for search for usages of simplified objects</li>\n<li>2362553 - Additional and revised S/4HANA readiness Code Inspector checks<br/><strong><em>Attention </em></strong><em>Consider the manual post steps of this note.</em></li>\n</ul>\n<p>It is also recommended to apply the SAP Notes mentioned in SAP Note 2364916 in your system.</p>", "noteVersion": 140, "refer_note": [{"note": "2865234", "noteTitle": "2865234 - SAP S/4HANA custom code checks show different number of findings in different check runs", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You perform the SAP S/4HANA custom code checks. The results of the check runs show different number of findings.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\"> SAP S/4HANA custom code checks</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>If you compare results in different ATC (central) check systems, ensure that in both systems the latest version of SAP Notes mentioned in SAP Note \"<em>2436688 - Recommended SAP Notes for using S/4HANA custom code checks in ATC or Custom Code Migration app\" are applied.</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The number of findings reported by the SAP S/4HANA custom code checks depend on various aspects in your SAP system. If one of these aspects is changed between the check runs, the number of findings might be different.</p>\n<p>The following list describes these aspects:</p>\n<ol start=\"1\">\n<li>The ATC run series has been changed:</li>\n<ol>\n<li>The check variant has been changed</li>\n<li>The object set has been changed</li>\n</ol>\n<li>The parameters of the check variant have been changed</li>\n<li>You uploaded a new version of the Simplification Database. <br/>You can check the current version and the import date by processing the following steps:</li>\n<ol>\n<li>Start transaction SYCM</li>\n<li>Choose \"Simplification Database &gt; Show Information\"</li>\n</ol>\n<li>You upgraded the systemYou applied support packages</li>\n<li>You applied SAP Notes related to SAP S/4HANA custom code checks to your SAP system (refer to SAP Note 2436688)</li>\n<li>You made changes to your custom code which might lead</li>\n<ul>\n<li>(obviously) to fewer findings, if findings of previous check runs have been fixed manually or by applying a quickfix</li>\n<li>to more findings, if new code has been added</li>\n<li>to more findings, if syntax errors have been fixed (programs with syntax errors show no findings)</li>\n<li>to consequential findings, if findings of previous findings have been fixed</li>\n</ul>\n<li>You upgraded your system and you get no findings related to the checks “S/4HANA: Field length extension” and “S/4HANA: Search for database operations” after the upgrade.<br/>In this case, upload the Simplification Database in your upgraded system, again. Even if the latest version has already been uploaded before the upgrade.</li>\n<li>You run the checks against systems with the same custom code by based on different SAP releases (e.g., SAP ERP and SAP S/4HANA). The checks “S/4HANA: Field length extension” and “Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY” might show different results in both releases, because the check results also depend on SAP's code.</li>\n</ol>\n<p><span> </span></p>\n<p>Furthermore, the custom code checks have been improved over time to suppress false-positive findings. These improvements are listed here: </p>\n<ul>\n<li>SELECT statements related to table VBFA are shown only if the statement contains the addition ORDER BY PRIMARY KEY (see also SAP Note 2738251/2768987)</li>\n<li>If you plan to use the material number length with 18 or less characters in SAP S/4HANA, the check <em>S/4HANA: Field length extension</em> will report fewer findings. If you do not plan to use other field length extensions (like the extended amount length), the check reports also fewer findings.<br/><strong>Note:</strong> The check<em> S/4HANA: Field length extension</em> allows the option to specify the used material number field length and other field length extensions by processing the following steps:</li>\n<ol>\n<li>Copy check variant S4HANA_READINESS_* you want to use to create your own check variant</li>\n<li>Change the parameters of the check <em>S/4HANA: Field length extension </em>in your own check variant</li>\n<li>Use your own check variant to execute the SAP S/4HANA custom code checks</li>\n</ol>\n<li>Findings related to simplification item \"2438110 - Material Number Field Length Extension: Code Adaptions for usages of RFC enabled function modules\" are only shown if CALL FUNCTION statements use the addition DESTINATION (see also SAP Note 2874255)</li>\n<li>Findings related to simplification item \"2768887 - S4TWL - SD Billing Document Draft\" are only shown if the where clause does not specify the fields VBELN or DRAFT</li>\n<li>The checks <em>S/4HANA: Search for ABAP Dictionary enhancements </em>and <em>S/4HANA: Search for base tables of ABAP Dictionary views </em>show less false-positive findings for some tables and views (see also SAP Note 2771060)</li>\n<li>The check <em>S/4HANA: Field length extensions</em> reports less fale-psotives for calls to SAP objects with generic parameters (see also SAP Note 2898180) </li>\n<li>The check <em>S/4HANA: Field length extensions </em>behaved wrong for multi-purpose fields (see also SAP Note 2971294) <em><br/></em></li>\n<li>In releases SAP S/4HANA 1809 (SAP_BASIS 7.53) or higher, findings related to the simplification item category \"Change of existing functionality with performance impact\" won't be reported any more if you choose any delivered check variant starting with S4HANA_READINESS*.</li>\n<li>The check <em>S/4HANA: Field length extensions </em>did not consider type conflicts in BAdI calls (see also SAP Note 2364938 (version 67))</li>\n<li>The check <em>S/4HANA: Field length extensions </em>detects usages of extended fields in CONCATENATE statements. A new finding \"CONCATENATE detected\" has been introduced (see also SAP Note 2364938 (version 68)) </li>\n<li>The check <em>S/4HANA: Field length extensions </em>does not report findings with message title \"Related Type\" anymore (see also SAP Note 3032974)</li>\n<li>The default configuration of the ATC check variants for the SAP S/4HANA custom code checks (check variantsstarting with S4HANA_READINESS*) has been changed (see also SAP Note 3057501)</li>\n<li>S/4HANA: Search for Usages of Simplified Objects' ATC check returns too many findings if only ABAP Dictionary fields are simplified (see SAP note 3039646)</li>\n</ul>", "noteVersion": 21}, {"note": "2866977", "noteTitle": "2866977 - Quick Fixes for SAP S/4HANA custom code checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP provides quick fixes for the SAP S/4HANA custom code checks.</p>\n<p>This Note describes which kind of quick fixes are offered.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p class=\"p1\">Since SAP S/4HANA 1809 FPS2 or higher, quick fixes for the SAP S/4HANA custom code checks are available.</p>\n<p class=\"p1\">SAP recommends to install SAP Notes which are listed in SAP Note “<em>2436688 - Recommended SAP Notes for using S/4HANA custom code checks in ATC or Custom Code Migration app</em>” if you want to use the SAP S/4HANA custom code checks and quick fixes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP provides quick fixes for findings related to following Simplification Items (SAP Notes) in SAP S/4HANA:</p>\n<ul>\n<li>2215424 (Material Number Field Length Extension - General Information)</li>\n<ul>\n<li>The Check “<em>S/4HANA: Field length extensions</em>” provides quick fixes for type conflicts and the following ABAP statements:</li>\n<ul>\n<li>WRITE ...</li>\n<li>IMPORT ...</li>\n<li>EXPORT ...</li>\n<li>TRANSFER ...</li>\n<li>READ-DATASET ...</li>\n<li>REPLACE ... </li>\n</ul>\n<li>Prerequisites for quick fixes: The material number field length extension is not used in SAP S/4HANA. I.e., the material number field length is 18 characters or less in SAP S/4HANA</li>\n<li>There are no quick fixes to adapt calls to function modules or BAPIs which are reported by the check \"S/4HANA: Search usages of simplified objects\", currently<br/><br/></li>\n</ul>\n<li>2610650 (Amount Field Length Extension: Code Adaptations)</li>\n<ul>\n<li>The Check “<em>S/4HANA: Field length extensions</em>” provides quick fixes for type conflicts and the following ABAP statements:\r\n<ul>\n<li>WRITE ...</li>\n<li>IMPORT ...</li>\n<li>EXPORT ...</li>\n<li>TRANSFER ...</li>\n<li>READ-DATASET ...</li>\n<li>REPLACE ... </li>\n</ul>\n</li>\n<li>Prerequisites for quick fixes: The amount field length extension is not used in SAP S/4HANA</li>\n<li>There are no quick fixes to adapt calls to function modules or BAPIs which are reported by the check \"S/4HANA: Search usages of simplified objects\", currently<br/><br/></li>\n</ul>\n<li>2198647 - S/4HANA: Data Model Changes in SD</li>\n<ul>\n<li>Database accesses to database tables VBFA, VBUK, VBUP</li>\n<li>Usages of VBTYP data elements in source code<br/><br/></li>\n</ul>\n<li>2768887 - S4TWL - SD Billing Document Draft</li>\n<ul>\n<li>The check provides a quick fix for database accesses to database tables VBRK and VBRP without specification of the fields VBELN or DRAFT in the where clause<br/><br/></li>\n</ul>\n<li>2220005 - S/4HANA: Data Model Changes in Pricing and Condition Technique</li>\n<ul>\n<li>Database accesses to database table KONV<br/><br/></li>\n</ul>\n<li>2431747 - General Ledger: Incompatible changes in S/4HANA compared to classic ERP releases</li>\n<ul>\n<li>Database accesses to database table BSEG<br/><br/></li>\n</ul>\n<li>1912445 - ABAP custom code migration for SAP HANA</li>\n<ul>\n<li>The check \"<em>Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\" provides quick fixes for all messages.</em></li>\n</ul>\n</ul>\n<p><strong>Note</strong></p>\n<p>Not all findings of the categories above can be solved by a quick fix. It depends on the finding in the custom code whether a quick fixes can be offered or not.</p>\n<p> </p>\n<p> </p>", "noteVersion": 8}, {"note": "2364938", "noteTitle": "2364938 - Downport of the infrastructure of the SAP S/4HANA readiness checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use the SAP S/4HANA readiness checks in your system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note contains infrastructure sections of the SAP S/4HANA readiness checks.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the specified Support Package or implement the attached correction instructions.</p>", "noteVersion": 103}, {"note": "2364916", "noteTitle": "2364916 - Recommended SAP Notes for using ATC to perform remote analysis", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the ABAP Test Cockpit (ATC) to perform a remote analysis.</p>\n<p>You have a central check system on release 7.51 or higher and one or more checked systems on releases higher than 7.00.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ABAP Test Cockpit, Notes</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The central check system can only analyze checked systems at the same or lower release levels. Therefore, SAP recommends using the latest SAP_BASIS release as your central check system.</p>\n<p>Furthermore, SAP Note Assistant and related tools have been updated to their latest version. Therefore, ensure that you have applied the latest version of the following SAP Notes in your system <strong>before</strong> implementing any of the notes in the Solution section:</p>\n<ul>\n<li><strong>Note Assistant:</strong></li>\n<ul>\n<li>1668882 - Note Assistant: Important notes for SAP_BASIS 730, 731, ... </li>\n<li>3200109 - SNOTE : Note Analyzer</li>\n</ul>\n<li><strong>Activation Tool:</strong></li>\n<ul>\n<li>2466179 - Not able to activate objects belonging to another user</li>\n<li>2730170 - SCWB/SNOTE Activation processes Methods in DDIC Phase</li>\n</ul>\n<li><strong>Class Builder Tool:</strong></li>\n<ul>\n<li>2970782 - Nested interfaces are not deleted by SAP Note Assistant</li>\n<li>3218983 - Order of nested interfaces is not considered in correction instructions of SAP Notes</li>\n<li>3020634 - Nested interfaces are not correctly stored</li>\n<li>3225158 - Nested interfaces are not created by SAP Note Assistant</li>\n<li>3415029 - Don't abort class creation if comprising interface already exists</li>\n</ul>\n<li><strong>Note Analzyer (if used; see below!):</strong></li>\n<ul>\n<li>3051466 - Note Assistant Made Easy : Revamped Note Assistant</li>\n<li>3200109 - SNOTE : Note Analyzer</li>\n</ul>\n</ul>\n<p>The notes listed in this section are not part of the XML files attached to this note. Restart the note analyzer after implementing the above notes and before implementing any of the notes from the Solution section.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This SAP Note summarizes recommended SAP Notes for the <strong>checked systems</strong> and the <strong>central check system</strong>. <br/>After you have implemented the prerequisite tool notes (see <em>Reason and Prerequisites</em>), <strong>you can use the attached XML files to download the notes in transaction SNOTE</strong>:<br/>Menu path: Transaction SNOTE -&gt; <em>Goto</em> -&gt; <em>Other Tools</em> -&gt; <em>Launch Note Analyzer</em></p>\n<p>For detailed information about authorizations for the RFC users required in ATC remote scenarios, refer to SAP Note 2672703.</p>\n<p><strong>Remarks:</strong></p>\n<ul>\n<li>We recommend to install the notes via support package whenever this is applicable.</li>\n<li>We recommend to use the latest possible release and support package level, especially for the central check system. </li>\n<li>In the list, depending on the release, you will find notes which may not be valid if the support package level is so high that it contains all corrections.</li>\n</ul>\n<p><strong>Checked System﻿</strong></p>\n<p>The ATC checks are using different infrastructures (ATC, Code Inspector, SLIN). In the checked system, we recommend to apply the following notes:</p>\n<ul>\n<li><strong>ATC Framework (components BC-DWB-TOO-ATF / BC-ABA-LA):</strong></li>\n<ul>\n<li><strong>Remote Checks from Development System </strong></li>\n<ul>\n<li>2375864 - ATC: Remote Checks - Developer Scenario</li>\n<li>2381403 - Infrastructure for remote checks (lower releases)</li>\n<li>2485231 - Remote ATC Checks of Modifications and Enhancements</li>\n<li>2587593 - ATC: Checkability of Modifications and Enhancements in Remote Checks - Developer Scenario</li>\n<li>2614006 - ATC remote check fails in developer scenario</li>\n<li>2503289 - ATC: The display of ATC results is extremely slow</li>\n<li>2987119 - CALL_FUNCTION_WRONG_VALUE_LENG terminations for ATC check in developer scenario</li>\n<li>3193098 - ATC - BDEF and ENHO handling </li>\n</ul>\n<li><strong>Stub versions of ATC framework</strong> (please install the latest version!):</li>\n<ul>\n<li>2190065 - ATC/CI: Remote Code Analysis - Object Provider Stub</li>\n<li>2812880 - ATC: Enhanced API Version of Remote Stub</li>\n<li>3073128 - ATC: Long Loading Times in Result Browser in Eclipse / Latest RFC Stub Version</li>\n<li>3193098 - ATC - BDEF and ENHO handling</li>\n</ul>\n</ul>\n<li><strong>Extractor for Compiler-Based Checks(BC-ABA-LA):</strong></li>\n<ul>\n<li><strong>Stub versions (please install the latest release-specific note version!):</strong></li>\n<ul>\n<li>2270689 - RFC Extractor for performing static checks<br/><em>Attention: You must apply a release-dependent SAP Note as mentioned in section \"Solution of SAP Note 2270689.<br/>Attention: Make sure to consider the manual post implementation steps.<br/><br/></em></li>\n</ul>\n</ul>\n<li><strong>Extended Program Check / CVA (component BC-ABA-LA-EPC):</strong></li>\n<ul>\n<li><strong>Stub versions</strong> (please install the latest version!):</li>\n<ul>\n<li>3333009 - RFC-Stub for CVA/SLIN remote checks (version 11) </li>\n<li>(etc.) - (find further versions with title \"RFC-Stub for CVA/SLIN remote checks\" or see 2460491)</li>\n<li>2196792 - RFC-Stub for CVA/SLIN-Remote<br/><br/></li>\n</ul>\n</ul>\n<li><strong>Infrastructure for Pseudo-Remote Checks:</strong></li>\n<ul>\n<li>2916724 - Infrastructure for pseudo-remote ATC checks</li>\n<li>Specific notes for pseudo-remote checks, e.g.</li>\n<ul>\n<li>3037465 - Remote enable ABAP Unit check for ATC</li>\n<li>3049732 - WDA: Remote-Enabling of Consistency Check (Including Active Components Deprecation)</li>\n<li>2964124 - Use pseudo remote execution for ATC check DDIC: DB Tables Logging Check<br/><br/></li>\n</ul>\n</ul>\n<li><strong>Recommended notes in case the central ATC system is located in SAP BTP (not included in the XML files)</strong></li>\n<ul>\n<li>3358660 - Developer Scenario Cloud</li>\n<li>3422320 - View results from a central ATC system in SAP BTP within the ADT ATC Result Browser in connected satellite systems</li>\n</ul>\n</ul>\n<p>We recommend installing latest corrections of all mentioned components. The following notes may be specifically interesting:</p>\n<ul>\n<li>2558283 - Code Inspector: No program-like tests for Smart Forms</li>\n<li>2617401 - ATC: Enable ATC Checks for Adobe Forms</li>\n<li>2617830 - Code Inspector: No program tests for Interactive Forms</li>\n</ul>\n<p><strong>Central Check System<br/></strong></p>\n<p>In general, we recommend to use the latest possible release for the central check system. For checked systems with a SAP_BASIS release up to 7.52, a central check system on SAP_BASIS 7.52 with the latest support package may be suffient. When S4HANA releases greater than <strong>SAP_BASIS 7.52</strong> need to be checked, you may consider using <strong><em>S/4HANA 2021 Foundation</em></strong> and use it as central check system.</p>\n<p>In order to get the latest versions of the checks and further corrections, please make sure that the following notes are installed:</p>\n<ul>\n<li>2375392 - Downport of Code Inspector remote checks</li>\n<li>2375864 - ATC: Remote checks - developer scenario</li>\n<li>2378425 - No Customizing of Priorities for Code Inspector Tests</li>\n<li>2381471 - ATC/CI: Internal error during determination of objects to be checked prevents check run</li>\n<li>2389855 - Remote Navigation for check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</li>\n<li>2391534 - ATC: Creation of Exemption not possible / Referred object does not exist</li>\n<li>2399689 - ATC: Collective corrections for Remote Code Analysis</li>\n<li>2423013 - Code-Inspector-Test Checksum-Infrastructure</li>\n<li>2424260 - Checksum calculation of check \"Critical Statements\"</li>\n<li>2424266 - Checksum calculation of check \"Find CALL TRANSACTION para_trans WITH AUTHORITY-CHECK\"</li>\n<li>2425250 - Checksum calculation of check \"Use of ADBC Interface\"</li>\n<li>2425292 - Checksum calculation of check \"Search SELECT .. FOR ALL ENTRIES-clauses to be transformed\"</li>\n<li>2425380 - Checksum calculation of check \"Search DB Operations in Pool/Cluster Tables\"</li>\n<li>2425461 - Checksum calculation of check \"Test to check handling of type INT8\"</li>\n<li>2425462 - Checksum calculation of check \"Search problematic statements for result of SELECT/OPEN CURSOR without ORDER BY\"</li>\n<li>2425464 - Checksum calculation of check \"Search problematic SELECT * statements\"</li>\n<li>2426087 - Checksum calculation of check \"Search DB Operations in loops across modularization units\"</li>\n<li>2436322 - Checksum calculation of check \"Test for support of Field Extension\"</li>\n<li>2437207 - Checksum calculation of check \"Check of SY-SUBRC Handling\"</li>\n<li>2437281 - Remote enablement of check \"SELECT Statements with Subsequent CHECK\"</li>\n<li>2437338 - Remote enablement and checksum calculation of check \"Changing Database Accesses in Loops\"</li>\n<li>2437361 - Remote enablement and checksum calculation of check \"Instance Creation of BAdIs\"</li>\n<li>2437457 - Remote enablement and checksum calculation of check \"Find Unwanted Language Elements\"</li>\n<li>2437458 - Remote enablement and checksum calculation of check \"Look for WRITE Statements\"</li>\n<li>2437859 - Runtime error ASSERTION_FAILED for execution of CL_CI_TEST_FIELD_EXT</li>\n<li>2437914 - Runtime error CL_CI_TEST_RND in class CL_CI_TEST_RND</li>\n<li>2438863 - Checksum calculation of check \"ABAP Unit\"</li>\n<li>2485231 - Remote ATC Checks of Modifications and Enhancements</li>\n<li>2503289 - ATC: The display of ATC results is extremely slow</li>\n<li>2505671 - Uniform saving of navigation information</li>\n<li>2518335 - ATC findings in SAP code</li>\n<li>2524014 - ATC: Problems When Checking Modified SAP Code</li>\n<li>2527903 - Remote analysis (for check system)</li>\n<li>2587593 - ATC: Checkability of Modifications and Enhancements in Remote Checks - Developer Scenario</li>\n<li>2614006 - ATC remote check fails in developer scenario</li>\n<li>2617401 - ATC: Enable ATC Checks for Adobe Forms</li>\n<li>2617830 - Code Inspector: No program tests for Interactive Forms</li>\n<li>2629856 - CVA/SLIN: Security checks of SFP-Forms ( SAP Interactive Forms by Adobe )</li>\n<li>2695129 - Downport: Remote enabling Code Inspector checks</li>\n<li>2783710 - Failures in ATC check instantiation</li>\n<li>2916724 - Infrastructure for pseudo-remote ATC checks</li>\n<li>2964124 - Use pseudo remote execution for ATC check DDIC: DB Tables Logging Check</li>\n<li>3037465 - Remote enable ABAP Unit check for ATC</li>\n<li>3049732 - WDA: Remote-Enabling of Consistency Check (Including Active Components Deprecation)</li>\n<li>3073128 - ATC: Long Loading Times in Result Browser in Eclipse / Latest RFC Stub Version</li>\n<li>3333433 - CVA: Internal improvements for the extended program check (version 11, find further versions in note 2460491)</li>\n</ul>", "noteVersion": 46}, {"note": "2861842", "noteTitle": "2861842 - Custom Code Migration in SAP BTP, ABAP Environment: Set up SAP Cloud Connector", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>To use the Custom Code Migration App in the SAP BTP, ABAP Environment you need to configure the accessible resources in your cloud connector.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Custom Code Migration App</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To configure the accessible resources in your cloud connector, you can import the CustomCodeMigration.zip scenario. This ZIP file can be found in the attatchments section. Please follow the steps described below:</p>\n<ol>\n<li>Open your cloud connector</li>\n<li>Select your sub account that is connected to your ABAP BTP system and navigate to the details page</li>\n<li>Navigate to the Cloud To On-Premise destinations</li>\n<li>Select the corresponding destination in the \"Mapping Virtual To Internal System\" list</li>\n<li>Upload the ZIP file in the \"Resources Of XXX\" list by clicking on the \"Import resources belonging to a scenario\"</li>\n</ol>\n<p><strong>Note</strong></p>\n<p>After an upgrade of SAP BTP ABAP Environment, it might be necessary to update the accessible resources in your cloud connector. The attached file CustomCodeMigration.zip has been updated for the latest release of SAP BTP ABAP Environment.</p>", "noteVersion": 29}]}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "Before conversion project", "Condition": "Conditional", "Additional_Information": "Adapt usages of PAOBJR found by ATC as described in SAP note <note number>-"}]}