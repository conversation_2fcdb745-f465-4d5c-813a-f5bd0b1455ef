{"guid": "0090FABF323E1ED697ADD217896160CB", "sitemId": "SI6: FIN_MISC_ML", "sitemTitle": "S4TWL - Conversion to S/4HANA Material Ledger and Actual Costing", "note": 2352383, "noteTitle": "2352383 - S4TWL - Conversion to S/4HANA Material Ledger and Actual Costing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP Business Suite or SAP Simple Finance to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Migration On-Premise</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>﻿Description</strong></p>\n<p>In SAP S/4HANA, the material ledger is mandatory. Therefore, migration of the material ledger is required in the following scenarios:</p>\n<ul>\n<li>Migration from SAP ERP to SAP S/4HANA 1610 or higher</li>\n</ul>\n<ul>\n<li>Migration from SAP Simple Finance to SAP S/4HANA 1610 or higher</li>\n</ul>\n<p>You <strong>always</strong> need to migrate the material ledger, even if you were not using the material ledger in the source system.</p>\n<p> </p>\n<p><strong>Required Action(s)</strong></p>\n<p>Before starting the conversion, please make sure to implement relevant SAP Notes that are mentioned in SAP Note 2345739.</p>\n<p> </p>\n<p><strong>Customizing Migration </strong></p>\n<p>Before migrating the material ledger, you need to complete the following IMG activities under <em>Migration to SAP S/4HANA Finance</em>.</p>\n<p>Under <em>Preparations and Migration of Customizing</em>:</p>\n<ul>\n<ul>\n<li><em>Preparations and Migration of Customizing for General Ledger </em>(These activities only need to be executed if you are migrating from SAP ERP Business Suite to SAP S/4HANA. If you are already using SAP Simple Finance 2.0 or higher, they are not relevant.)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li><em>Preparations and Migration of Customizing for Material Ledger -&gt; Migrate Material Ledger Customizing (</em>This activity <strong>always</strong> needs to be executed, even if you are migrating from SAP Simple Finance to SAP S/4HANA.)</li>\n</ul>\n</ul>\n<p><strong>Data Migration</strong></p>\n<p>To start the material ledger migration activities, go to the IMG and navigate to <em>Migration to SAP S/4HANA Finance -&gt; Data Migration</em> and start the activity <em>Start and Monitor Data Migration</em>.</p>\n<p>For further information, see the IMG documentation on the <em>Start and Monitor Data Migration</em> activity.</p>\n<p>The currency conversion in step M10 and M11 is done via standard exchange rate type 'M'.</p>\n<p>The process of migrating the material ledger consists of the following activities in the <em>Start and Monitor Migration</em> activity:</p>\n<ul>\n<ul>\n<li><strong>M10: Migrate Material Ledger Master Data</strong></li>\n</ul>\n</ul>\n<p>This activity ensures that the material ledger is activated for all valuation areas, since it is mandatory in SAP S/4HANA. The activity creates material ledger master data (tables: CKMLHD, CKMLPR, CKMLPP and CKMLCR) in all material ledger currencies for periods more recent than the last period of the previous year.</p>\n<p>In addition, all existing inventory aggregate values (tables MBEW, EBEW, QBEW, OBEW) and their complete historic data (tables MBEWH EBEWH, QBEWH, OBEWH) are migrated into the new universal journal entry table ACDOCA and ACDOCA_M_EXTRACT. Period records more recent than the last period of the previous year are converted into material ledger currencies using standard exchange rate type 'M'. Periods earlier than the last period of the previous year are only migrated in home currency. During migration, balance carryforward records (fields: BSTAT = ‘C’ and MIG_SOURCE = ‘N’) are created in ACDOCA. If you are already using the material ledger, existing material ledger records (tables CKMLPP and CKMLCR) are transferred into ACDOCA and ACDOCA_M_EXTRACT with all existing currency information.</p>\n<p>This migration activity does <strong>not</strong> activate actual costing, since actual costing is still optional in SAP S/4HANA. However, if you are already using actual costing in the migration source system, ML data for actual costing will be transferred to new data structures to enable fast and efficient cost calculation. In particular, the following database tables will be filled for periods more recent than the last period of the year before the previous year until the current period.</p>\n<p><strong>Attention:</strong> The relevant periods are determined via table MARV (fields VVJGJ, VVJMO, LFGJA, LFMON). Make sure that data in MARV is correct. Do not transport table entries of table MARV from different systems, where MARV might not be up-to-date. This can lead to severe data inconsistencies.</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Table Name</strong></p>\n</td>\n<td>\n<p><strong>Description</strong></p>\n</td>\n<td>\n<p><strong>Replacement for</strong></p>\n</td>\n<td>\n<p><strong>Purpose</strong></p>\n</td>\n<td>\n<p><strong>Remark for Migration</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>MLDOC</p>\n</td>\n<td>\n<p>Material Ledger Document</p>\n</td>\n<td>\n<p>MLHD, MLIT, MLPP, MLPPF, MLCR, MLCRF, CKMLPP, CKMLCR, MLCD, CKMLMV003, CKMLMV004, CKMLPPWIP etc.</p>\n</td>\n<td>\n<p>Optimized data structure for ML actual costing. Will be  filled during transactional update of ML data and during ML settlement.</p>\n</td>\n<td>\n<p>During step M10, data in MLDOC is created from the last period of the previous year to the current period for all valuation areas where actual costing is activated.</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>MLDOCCCS</p>\n</td>\n<td>\n<p>Material Ledger Document Cost Component Split</p>\n</td>\n<td>\n<p>MLKEPH, CKMLKEPH, (CKMLPRKEKO)</p>\n</td>\n<td>\n<p>Optimized data structure for cost component split in ML actual costing</p>\n</td>\n<td>\n<p>During step M10, cost component split data are created based on MLDOC entries.</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>MLDOC_EXTRACT</p>\n</td>\n<td>\n<p>Extract of Material Ledger Document</p>\n</td>\n<td>\n<p>Similar to MLDOC, but contains only information about quantity and value. It is possible to compress this table to one entry per period and cost estimate number via report FCML4H_MLDOC_EXTRACT_COMPRESS.</p>\n</td>\n<td>\n<p>During migration, one entry is created for each combination of cost estimate number, currency type, period, category, and process category.</p>\n</td>\n<td></td>\n</tr>\n<tr>\n<td>\n<p>MLDOCCCS_EXTRACT</p>\n</td>\n<td>\n<p>Extract of Material Ledger Document Cost Component Split</p>\n</td>\n<td>\n<p>-</p>\n</td>\n<td>\n<p>Similar to MLDOC_EXTRACT but with information per cost component. Tables MLDOC_EXTRACT and MLDOCCCS_EXTRACT will be used especially to calculate information about beginning inventory in specific period, by cumulating quantities and values from all previous periods.</p>\n</td>\n<td></td>\n</tr>\n<tr>\n<td>\n<p>MLRUNLIST</p>\n</td>\n<td>\n<p>Object List for Costing Run</p>\n</td>\n<td>\n<p>CKMLMV011, status in CKMLPP</p>\n</td>\n<td>\n<p>Information about status of materials and activity types in ML costing runs</p>\n</td>\n<td>\n<p>During migration, table MLRUNLIST is filled based on costing runs created in the start release.</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Note</strong></p>\n<p>For all ML costing runs created in the start release, the <em>Post Closing</em> step needs to be finished. It will not be possible to process costing runs created earlier in the new release.</p>\n<p>The Posting Logic of the new Actual Costing has been changed in some details. These changes require the following adjustments in the Account Determination (Transaction OBYC or IMG -&gt; Materials Management -&gt; Valuation and Account Assignment -&gt; Account Determination -&gt; Account Determination Without Wizard -&gt; Configure Automatic Postings).</p>\n<ul>\n<ul>\n<ul>\n<li>\n<div>Transaction PRL (Activity Price Differences):</div>\n</li>\n</ul>\n</ul>\n</ul>\n<p>This transaction was introduced with SAP S/4HANA 1610. It is used for the offsetting posting of the cost center credit posting (Transaction/Account Modification GBB/AUI). Maintain the rules and posting key for transaction PRL. Then assign the accounts that will receive the activity price differences credited to the cost centers. From SAP S/4HANA 1610 all activity related postings are performed with valuation class &lt;blank&gt;. It is therefore not necessary to activate the Valuation Modification in the rules of transaction PRL.</p>\n<ul>\n<ul>\n<ul>\n<li>\n<div>Transaction GBB/Account Modification AUI:</div>\n</li>\n</ul>\n</ul>\n</ul>\n<p>From SAP S/4HANA 1610 all activity related postings are performed with valuation class &lt;blank&gt;. If you have activated the Valuation Modification in the rules of transaction GBB, make sure to create an account assignment entry for General Modification AUI and Valuation Class &lt;blank&gt; also.</p>\n<ul>\n<ul>\n<ul>\n<li>\n<div>Transactions PRV and KDV:</div>\n</li>\n</ul>\n</ul>\n</ul>\n<p>These transactions are obsolete with SAP S/4HANA 1610 (since the new Actual Costing no longer distinguishes between single-level and multilevel variances).<br/>Their account assignment entries may be removed.</p>\n<ul>\n<ul>\n<li><strong>M20: Check Material Ledger Master Data</strong></li>\n</ul>\n</ul>\n<p>After performing the material ledger master data migration activity, this activity checks and verifies the migrated data. For instance, existing values from the inventory and material ledger tables are compared with aggregation via table ACDOCA.</p>\n<p>Check the error messages. Usually no adjustment is necessary for insignificant errors, such as those from time periods long in the past or from company codes that are not productive. If you have any doubts, discuss them with your auditor. You have the option to accept errors that are not to be corrected After you have accepted all errors, you can continue with the next migration step. If anything was adjusted, you can reset the run and repeat it.</p>\n<ul>\n<ul>\n<li><strong>M11: Migrate Material Ledger Order History</strong></li>\n</ul>\n</ul>\n<p>If the material ledger was not active in any valuation area before SAP S/4HANA conversion, this actvity ensures that all existing purchase order history table records (tables EKBE, EKBEH, EKBZ, EKBZH) and production order history table records (tables MLAUFCR and MLAUFCRH) are converted into the material ledger currencies using standard exchange rate type 'M'.</p>\n<ul>\n<ul>\n<li><strong>M21: Check ML Production Order and Purchase Order History</strong></li>\n</ul>\n</ul>\n<p>This activity verifies whether all production and purchase order history records have been converted into the material ledger currencies.</p>\n<p><strong>Note</strong></p>\n<p>If you want to minimize the amount of data to be migrated, archive or delete any data that is no longer needed. This will decrease the migration downtime. The relevant tables are the following:</p>\n<ul>\n<ul>\n<li>Inventory valuation tables: MBEWH, EBEWH, QBEWH, OBEWH</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Material ledger inventory tables (only relevant if material ledger was active before SAP S/4HANA migration): CKMLPP, CKMLCR</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Purchase order history tables: EKBE, EKBEH, EKBZ, EKBZH</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Production order history tables: MLAUFCR, MLAUFCRH</li>\n</ul>\n</ul>\n<p><span>  </span></p>\n<p>The migration activities are parallelized using background jobs. Make sure that enough background jobs are available.</p>\n<p>During parallelization, a standard package size of 20,000 is used in all material ledger migration activities. You may adapt this size by setting user parameter FML_MIG_PKG_SIZE.</p>\n<p>This makes sense e.g. in case step M10 causes issues in Physical Memory of the system, like Runtime Error ‘TSV_NEW_PAGE_ALOC_FAILED’ (SAPLCKMS).</p>\n<p>To do so go to transaction SU3 / Tab \"Parameters\" -&gt; set parameter FML_MIG_PKG_SIZE to value 5000 (example) for the user, who is running step M10 in transaction FINS_MIG_STATUS.</p>\n<p>After that Step M10 can be repeated in transaction FINS_MIG_STATUS -&gt;‘Control’ Tab -&gt; set Cursor on line with step M10 -&gt; press button ‘Next Activity’ -&gt; Resume Migration.</p>", "noteVersion": 9, "refer_note": [], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If actual costing is  active => customize account determination as of SAP S/4HANA 1610"}, {"Activity": "Data correction", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Software Upgrade / Maintenance", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Implementation of SAP Notes mentioned in SAP Note \"2345739\" for your corresponding Support Package."}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Business Operations", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If Material ledger is already active => complete step \"Post closing\" for all ML costing runs created before conversion/upgrade"}, {"Activity": "Data cleanup / archiving", "Phase": "Before conversion project", "Condition": "Optional", "Additional_Information": "Archiving historic data"}]}