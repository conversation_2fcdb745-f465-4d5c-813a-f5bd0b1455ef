{"guid": "0050569455E21ED5B3E17678390CE09E", "sitemId": "SI14: SD_BILLING_OM", "sitemTitle": "S4TWL - Billing Document Output Management", "note": 2790427, "noteTitle": "2790427 - S4TWL - Billing Document Output Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In SAP S/4HANA, a new output management approach has been implemented, but the old NAST technique is set as default.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Billing Document Output Management, SAP S/4HANA output control</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<div class=\"longtext\">\n<p>This note is relevant for you if your start release for the system conversion is an SAP ERP release. It is also relevant if your start release is SAP S/4HANA and you do not require \"special\" output channels.</p>\n<p>The new output management for SAP S/4HANA, called SAP S/4HANA output control, only supports the \"pure\" output channels, while the NAST-based output can also be used for several kinds of post-processing. SAP S/4HANA output control cannot be used if one of the following NAST transmission mediums is required:</p>\n<ul>\n<li>8 Special function</li>\n<li>9 Events (SAP Business Workflow)</li>\n<li>A Distribution (ALE)</li>\n<li>T Tasks (SAP Business Workflow)</li>\n</ul>\n<p>In this case, please use NAST-based output management. If you don't need these kinds of \"special\" output channels, you can use the new SAP S/4HANA output control.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>As there are valid requirements and use cases for both frameworks, there is a new customizing activity \"Manage Application Object Type Activation\" where the active framework can be maintained.<br/>You can find the Customizing under output control:</p>\n<p>Transaction SPRO<br/><em>SAP Reference IMG -&gt; Cross-Application Components -&gt; Output Control -&gt; Manage Application Object Type Activation</em><br/> <br/>Choose \"Application Active\" if you want to switch to new output management for SAP S/4HANA, which is called SAP S/4HANA output control.</p>", "noteVersion": 2, "refer_note": [{"note": "2228611", "noteTitle": "2228611 - Output Management in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP S/4HANA introduces a new style of output management (OM).</p>\n<p>For SAP S/4HANA 1709 or a higher please check the standard documentation under help.sap.com.</p>\n<p>For releases 1511 and 1610 this SAP Note provides you with an overview of this topic.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Output management, Output control</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The new output management is going to be the successor of all other output management frameworks (SD Output Control, FI Correspondence, FI-CA Print Workbench, CRM Post-Processing). However, all other frameworks are still available and can be used. It is not mandatory to use the new output management.</p>\n<p>Business applications that have already adopted the new output management use it by default. Nonetheless, customers can always revert back to the other supported framework. How this reversion is realized depends on the business application (for example via SAP Note or by implementation of an enhancement point). It is also possible to re-enable the new output management again at a later point in time.</p>\n<p>Hence, the new output management for SAP S/4HANA coexists with other frameworks. Customers can decide on business application level which framework supports their output scenarios best.</p>\n<p>For more details on output management usage in a specific business application please check the simplification item of that business applications.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The output management for SAP S/4HANA comprises all functionalities and processes that are related to the output of documents. This includes the integration of output management functions into the business applications, the reuse component <em>output control</em>, as well as the SAP NetWeaver technologies and the technical infrastructure.</p>\n<p>Output control is the central element of output management. It serves as an interface between business applications and SAP NetWeaver technologies.</p>\n<p>Note that although output control is by definition one of several parts of output management, the term <em>output management</em> is often used to describe the functionality of output control.</p>\n<p>When you use applications that have adopted the new output control, please consider the following aspects and consult the following SAP Notes:</p>\n<ul>\n<li><strong>Technical setup </strong>- SAP Note 2292571</li>\n<li><strong>Configuration </strong>- SAP Note 2292539</li>\n<li>New form templates used by output control:</li>\n<ul>\n<li><strong>Form templates with fragments</strong> - SAP Note 2292646</li>\n<li><strong>Master form templates</strong> - SAP Note 2292681</li>\n<li><strong>Customized forms</strong> - SAP Note 2294198</li>\n<li><strong>Customized master forms</strong> - SAP Note 2367080</li>\n</ul>\n<li>Conversion (see below)</li>\n</ul>\n<p><strong>Features and functions</strong></p>\n<p>The following table provides an overview of existing features and functions of the new output control:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"290\">\n<p><strong>Supported c</strong><strong>hannels</strong></p>\n</td>\n<td valign=\"top\">\n<ul>\n<li>Printer – Using the spool</li>\n<li>Email – Allows flexible configuration of sender and recipient(s) as well as use of email templates</li>\n<li>XML – Integration with the Ariba Network</li>\n<li>IDoc (limited support only; for more information, see below)</li>\n</ul>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"290\">\n<p><strong>Supported UI technologies</strong></p>\n</td>\n<td valign=\"top\">\n<ul>\n<li>Fiori</li>\n<li>SAP GUI and SAP GUI for HTML</li>\n</ul>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"290\">\n<p><strong>Supported form technologies</strong></p>\n</td>\n<td valign=\"top\">\n<ul>\n<li>PDF-based print forms with fragments (default)</li>\n<li>PDF-based print forms</li>\n<li>Smart Forms</li>\n<li>SAPscript</li>\n</ul>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"290\">\n<p><strong>Master form templates</strong></p>\n</td>\n<td valign=\"top\">\n<p>A master form template defines a universally valid layout and features reuseable parts of a form, such as header, footer, or logo (only available for PDF-based print forms with fragments). This allows you to maintain general content and application-specific content separately. If you make changes in the general content (master form), the application forms will be updated accordingly.</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"290\">\n<p><strong>Determination of master form templates</strong></p>\n</td>\n<td valign=\"top\">\n<p>Master form templates can be determined based on preconfigured parameters, such as company, sender country, or channel. This makes it easy to define separate logos or footer texts per company.</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"290\">\n<p><strong>Output parameter determination via BRFplus</strong></p>\n</td>\n<td valign=\"top\">\n<p>All output parameters can be automatically determined via a configuration activity based on BRFplus. The benefits are:</p>\n<ul>\n<li>You can send multiple messages to multiple recipients using multiple channels at the same time.</li>\n<li>You can configure anything without the need for ABAP exits.</li>\n<li>You can base each determination step on application-specific fields that can be extended.</li>\n<li>You can use predelivered content/determination rules to simply run output control out of the box.</li>\n<li>Extensibility via CDS is supported.</li>\n</ul>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"290\">\n<p><strong>Dispatch time</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Immediately </strong>or <strong>scheduled </strong>via batch job (application-specific)</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"290\">\n<p><strong>Application log</strong></p>\n</td>\n<td valign=\"top\">\n<p>The system creates one application log per output request instance. In addition, you can filter messages by output request item. You can display the log in the business application or via transaction SLG1 once an output item has been processed.</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"290\">\n<p><strong>Used frameworks/services</strong></p>\n</td>\n<td valign=\"top\">\n<ul>\n<li>BOPF</li>\n<li>BRFplus</li>\n<li>KPro</li>\n<li>Gateway Services</li>\n<li>Fiori</li>\n<li>Web Dynpro</li>\n<li>SAP GUI</li>\n</ul>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>IDoc</strong></p>\n<p>Currently, IDocs are not fully supported by the new output management. Their use is restricted to business applications that previously used NAST. Furthermore, the following limitations apply:</p>\n<ul>\n<li>Only output types which can be mapped 1:1 to the NAST-KSCHL can use IDoc.</li>\n<li>Only business partner-based communication is supported, but no communication to logical systems (no ALE support).</li>\n<li>Manually created output items cannot be used for IDoc, as the partner role cannot be set for such items.</li>\n</ul>\n<p>Please check your business requirements against these limitations. Refer to the simplification item list of the business applications to check which business scenarios support the use of IDoc.</p>\n<p><strong>Conversion </strong></p>\n<p>In the ECC Business Suite, different output frameworks are used in different applications (SD Output Control, FI Correspondence, FI-CA Print Workbench, CRM Post-Processing Framework).</p>\n<p>Considering the conversion of an existing system to SAP S/4HANA, it becomes evident that <strong>there cannot be one generic approach to cover the conversion</strong> of all other OM frameworks.</p>\n<p>Looking further into the most prominent existing OM framework <em>SD Output control (NAST),</em> it becomes clear that:</p>\n<ul>\n<li>100% feature parity is not a given (and, most likely, is not wanted).</li>\n<li>Migration of data cannot be performed because even the NAST is highly generic.</li>\n</ul>\n<p><strong>Therefore, the recommendation is not to carry out a conversion, but to establish a coexistence of old and new output management.</strong></p>\n<p>This means:</p>\n<ul>\n<li>For <span>existing customers</span> who are using the old output management:</li>\n<ul>\n<li>Data from the old OM is kept.</li>\n<li>Customizing for the old OM is kept.</li>\n<li>Customers need to customize the new OM.</li>\n<li>Documents that were processed in the old OM will still be processed using the old OM.</li>\n<li>New documents will only use the new OM (if the corresponding application has already adopted this framework).</li>\n</ul>\n</ul>\n<p> </p>\n<ul>\n<li>For <span>new customers</span></li>\n<ul>\n<li>Customers need to customize the new OM.</li>\n<li>All documents will be using the new OM (if the corresponding application has already adopted this framework).</li>\n</ul>\n</ul>", "noteVersion": 26, "refer_note": [{"note": "2292681", "noteTitle": "2292681 - SAP S/4HANA output control - master form templates", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP S/4HANA introduces a new style of output management. Output control serves as an interface between the business applications and the SAP NetWeaver technologies.</p>\n<p>For SAP S/4HANA 1709 or a higher please check the standard documentation under help.sap.com.</p>\n<p>For releases 1511 and 1610 this SAP Note provides you with an overview of the new master form templates used in many application forms.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Output management, output control, master form template</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP ships new default form templates for each business application implemented as PDF-based print forms with fragments. A fragment is part of a master form template.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>A master form template is a separate form template (XDP file). It includes different layout definitions (fragments) for one or more master pages. Only one of them at a time can be referenced by the application form.</p>\n<p> </p>\n<p><strong>Fragments</strong></p>\n<p>Every SAP default master form template contains the following fragments:</p>\n<ul>\n<li>Landscape_Factsheet</li>\n<li>Landscape_ItemList</li>\n<li>Portrait_Factsheet</li>\n<li>Portrait_ItemList</li>\n<li>Portrait_OutboundLetter</li>\n</ul>\n<p>Each fragment is designed for a specific page orientation (landscape or portrait) and layout variant (fact sheet, item list or outbound letter).</p>\n<p>The names of the fragments are constants. They should not be changed by customers. However, you can create your own master form template using these constants. The layout of the single fragments can then be freely defined.</p>\n<p>Any fragment, which is not named like the ones listed above, will be ignored by the system. This means that currently no additional fragments can be added by customers. You simply keep the existing fragments and adapt their layout.</p>\n<p>It is not necessary to have all fragments available in each master form template. If, for example, the master form template is only referenced by application forms using \"Portrait_Outbound_Letter\", all other fragments can be removed.</p>\n<p> </p>\n<p><strong>Fragment layout</strong></p>\n<p>Each fragment (layout definition) includes:</p>\n<p>A master page for the <strong>first page (mst1)</strong> which defines the following:</p>\n<ul>\n<li>Paper size and orientation</li>\n<li>Content area</li>\n<li>Layout and positioning of the</li>\n<ul>\n<li>Form title</li>\n<li>Logo</li>\n<li>Page number</li>\n<li>Sender address (where applicable)</li>\n<li>Receiver address (where applicable)</li>\n<li>Footer blocks</li>\n</ul>\n</ul>\n<p>A master page for <strong>subsequent pages (mst2)</strong> which defines the following:</p>\n<ul>\n<li>Paper size and orientation</li>\n<li>Content area</li>\n<li>Layout and positioning of the</li>\n<ul>\n<li>Form title</li>\n<li>Logo</li>\n<li>Page number</li>\n<li>Footer blocks</li>\n</ul>\n</ul>\n<p> </p>\n<p><strong><strong>Content</strong></strong></p>\n<p>The content of a master form template is part of the configuration of the new output control. For more information, check the activity \"Define Rules for Determination of Master Form Template\" (see SAP Note 2292539).</p>\n<p>The master form template itself only includes placeholders for the content. At runtime, the corresponding gateway service will read the data from the configuration.</p>\n<p>Of course it is also possible to directly include the content in the master form template. However, this means that most of its flexibility will be lost.</p>\n<p> </p>\n<p><strong>Default shipment </strong></p>\n<p>SAP ships the following master form templates which all include different layout definitions (fragments) for portrait and landscape orientations:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><em>SOMU_FORM_MASTER_A4</em></td>\n<td>Master Form Template for paper size 'A4'</td>\n</tr>\n<tr>\n<td><em>SOMU_FORM_MASTER_LETTER</em></td>\n<td>Master Form Template for paper size 'Letter'</td>\n</tr>\n</tbody>\n</table></div>\n<p>These variants include placeholders for all static texts (sender address or footer blocks) and logos referenced in the template.</p>\n<p> </p>\n<p><strong><strong>Master form implementation</strong></strong></p>\n<p>As master form templates are technically XDP files, the same editing rules apply as for application form templates. For more information, see SAP Note 2292646.</p>\n<p> </p>\n<p><strong>Translation</strong></p>\n<p>All form templates delivered by SAP are available in all localized languages.</p>\n<p>Customized master form templates need to be translated manually in Adobe LiveCycle Designer (ALD). Use the Fiori app \"Maintain Form Templates\" to maintain the different language variants.</p>", "noteVersion": 5}, {"note": "2294198", "noteTitle": "2294198 - SAP S/4HANA output control - customized forms", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP S/4HANA introduces a new style of output management. Output control serves as an interface between the business applications and the SAP NetWeaver technologies.</p>\n<p>For SAP S/4HANA 1709 or a higher please check the standard documentation under help.sap.com.</p>\n<p>For releases 1511 and 1610 this SAP Note provides you with an overview of creating custom form templates.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Output management, output control, custom form template</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP ships default form templates for each business application. However, the form technique can differ depending on the application.</p>\n<p>New output control supports the following:</p>\n<ul>\n<li>Existing form technologies such as:</li>\n<ul>\n<li>SAPscript</li>\n<li>Smart Forms</li>\n<li>PDF-based print forms without fragments</li>\n</ul>\n<li>New form technologies such as:</li>\n<ul>\n<li>PDF-based print forms with fragments (allows reuse of common parts) </li>\n</ul>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You can create your own form templates and use them with the new output control. However, the forms and especially the corresponding print program need to be compatible with the new output control.</p>\n<p>Depending on the form technology, you need to use different tools. It is recommended that you only use PDF-based print forms with fragments when you create new forms.</p>\n<p> </p>\n<p><strong>Forms using existing form technologies</strong></p>\n<p>For forms using existing technologies, backend access is required. The tools are identical to those used in SAP Business Suite and can be used in the same way. There are no known restrictions regarding the implementation of the form template itself when using these technologies.</p>\n<ul>\n<li>For SAPscript, use transaction SE71                     </li>\n<li>For Smart Forms, use transaction SMARTFORMS  </li>\n<li>For PDF-based print forms (without fragments), use transaction SFP<strong> </strong></li>\n</ul>\n<p>Note that you have to register your existing forms in the customizing activity \"Assign Form Templates\". For more information, see SAP Note 2292539 and the relevant IMG documentation. Only then can you use them in the context of the new output control.</p>\n<p> </p>\n<p><strong>Print programs</strong></p>\n<p>Each existing form technology requires a print program to assemble the data and perform the rendering of the form. For these print programs, the following restrictions apply in SAP S/4HANA:</p>\n<ul>\n<li>The program must only send the rendered document to the spool (from where output control will fetch it).</li>\n<li>The program must create a new spool request. This can be ensured by setting the corresponding attribute in the output parameters for creating a new spool request.</li>\n<li>The program must set UUID of the output item as the title of the spool request (RQTITLE). This UUID is available as NAST-TDCOVTITLE or as importing parameter IS_OR_ITEM-KEY of method RENDER_DOCUMENT_LEGACY.</li>\n<li>The program must not perform any other output.</li>\n<li>The program must not raise any dialogs.</li>\n<li>The program must not raise any messages of the type \"Abort\", \"Error\", \"Warning\" or \"Information\".</li>\n<li>The program must not create more than one document. (A spool request containing several documents, for example, is not supported.)</li>\n<li>The program must not refresh the transactional buffer (e.g. in case of preview). This could lead to data loss.</li>\n<li>The entry routine of the print program has to offer the following parameters: RETURNCODE (Type SYSUBRC) and SCREEN (type CHAR1).</li>\n</ul>\n<p>Example for entry rountine:</p>\n<p>FORM entry<br/>  USING returncode     type sysubrc<br/>             screen           type char1.</p>\n<p>&lt; your logic&gt;</p>\n<p>ENDFORM.</p>\n<p>Existing print programs need to be checked against these restrictions and adapted accordingly. More restrictions may be defined in the future.</p>\n<p>Note that by default the new output control offers a preview functionality for all forms. For form templates used in another output control framework the preview might not have been available before. In such cases, the preview functionality is not guaranteed, and especially a refresh of the transactional buffer (for example, calling RV_INVOICE_REFRESH in billing) will lead to an exception causing the system to raise a dump.</p>\n<p> </p>\n<p><strong>Example</strong></p>\n<p>You created a custom Smart Form for billing named \"YY1_MY_BILLING_FORM\". The print program is called \"YY1_MY_BILLING_FORM\", with entry routine \"START_PROCESSING\".</p>\n<ol>\n<li>Register the form in the IMG activity \"Assign Form Templates\" by creating a new entry:</li>\n</ol>\n<ul>\n<li>Set \"Application Object Type\" to \"BILLING_DOCUMENT\".</li>\n<li>Set \"Output Type\" to \"BILLING_DOCUMENT.</li>\n<li>Set \"Form Template ID\" to \"YY1_MY_BILLING_FORM\".</li>\n<li>Set \"Form Type\" to \"Smart Form\".</li>\n<li>Set \"Program\" to \"YY1_MY_BILLING_FORM\".</li>\n<li>Set \"Form Routine\" to \"START_PROCESSING\".</li>\n<li>Save, and leave the activity.</li>\n</ul>\n<ol start=\"2\">\n<li>Open the IMG activity \"Define Business Rules for Output Determination\".</li>\n</ol>\n<ul>\n<li>Select \"Billing Document\"<em> </em>from the \"Show Rules for\" dropdown list.</li>\n<li>Select \"Form Template\" from the \"Determination\" dropdown list.</li>\n<li>Switch to edit mode, and maintain a rule that uses the form \"YY1_MY_BILLING_FORM\".</li>\n<li>Save, activate the changes, and leave the activity.</li>\n</ul>\n<ol start=\"3\">\n<li>Create a new billing document that matches the criteria maintained for the form template.</li>\n<li>Open the billing document via transaction VF02, and navigate to the output details via the menu \"GoTo\" -&gt; \"Header\" -&gt; \"Output\".</li>\n<li>You should find a line with the form template as \"YY1_MY_BILLING_FORM\".</li>\n<li>Preview or output the document to render the form.</li>\n</ol>\n<p> </p>\n<p><strong>Forms using new technologies</strong></p>\n<p>PDF-based print forms with fragments can only be maintained using the Fiori app \"Maintain Form Templates\". With this app, you can download existing templates, edit them offline, and upload them again.</p>\n<p>For editing you need Adobe LiveCycle Designer (ALD) version 10.4 or higher.</p>\n<p>See SAP Note 2292646 and SAP Note 2292681 for more details.</p>\n<p> </p>\n<p><strong>Example</strong></p>\n<p>You created a custom PDF-based print form with fragments for billing named \"YY1_MY_NEW_BILLING_FORM\".</p>\n<p>Register the form in the IMG activity \"Assign Form Templates\" by creating a new entry:</p>\n<ul>\n<li>Set \"Application Object Type\" to \"BILLING_DOCUMENT\".</li>\n<li>Set \"Output Type\" to \"BILLING_DOCUMENT\".</li>\n<li>Set \"Form Template ID\" to \"YY1_MY_NEW_BILLING_FORM\".</li>\n<li>Set \"Form Type\"<em> </em>to \"PDF-based Print Form With Fragments\".</li>\n<li>Save, and leave the activity.</li>\n</ul>\n<ol start=\"2\">\n<li>Open the IMG activity \"Define Business Rules for Output Determination\".</li>\n</ol>\n<ul>\n<li>Select \"Billing Document\" from the \"Show Rules for\" dropdown list.</li>\n<li>Select \"Form Template\" from the \"Determination\" dropdown list.</li>\n<li>Switch to edit mode, and maintain a rule that uses the form \"YY1_MY_NEW_BILLING_FORM\".</li>\n<li>Save, activate the changes, and leave the activity.</li>\n</ul>\n<ol start=\"3\">\n<li>Create a new billing document that matches the criteria maintained for the form template.</li>\n<li>Open the billing document via transaction VF02, and navigate to the output details via the menu \"GoTo\" -&gt; \"Header\" -&gt; \"Output\".</li>\n<li>You should find a line with the form template as \"YY1_MY_NEW_BILLING_FORM\".</li>\n<li>Preview or output the document to render the form.</li>\n</ol>\n<p> </p>", "noteVersion": 11}, {"note": "2292571", "noteTitle": "2292571 - SAP S/4HANA output control - technical setup", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP S/4HANA introduces a new style of output management.</p>\n<p>For SAP S/4HANA 1709 or a higher please check the standard documentation under help.sap.com.</p>\n<p>For releases 1511 and 1610 this SAP Note provides you with an overview of the required technical setup.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Output management, output control, printing, technical setup</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Prerequisites for output management</strong></p>\n<ul>\n<li><strong>bgRFC configuration</strong> has been set up and prefixes for OM_QBGRFC_INBOUND_DEST (Define Inbound Dest.) are not modified (default value: OM_QUEUE)</li>\n<li><strong>Storage system and category </strong>has been maintained</li>\n<li><strong>BRFplus</strong> is active and usable</li>\n<li><strong>Adobe Document Services</strong> are available (only when using SAP Interactive Forms by Adobe or SAP Cloud Platform Forms by Adobe)</li>\n</ul>\n<p>Please check the corresponding help page for more details: <a href=\"http://help.sap.com/s4hana\" target=\"_blank\">http://help.sap.com/s4hana</a> <br/>(Follow the link for the corresponding SAP S/4HANA release where you then will find the links to the Administration Guide and Operations Guide.)</p>\n<p class=\"Default\"><strong>bgRFC (Background Remote Function Call)</strong></p>\n<p>Output control uses a bgRFC to process output. Therefore, you need to maintain the bgRFC configuration. Otherwise, no output can be performed.</p>\n<p>You can perform all the relevant steps in transaction SBGRFCCONF. One of the most important steps is defining a supervisor destination, as bgRFC doesn’t work without it.</p>\n<p>Steps to create a supervisor destination</p>\n<ol>\n<li>Start transaction SBGRFCCONF.</li>\n<li>Go to tab <em>Define Supervisor Dest</em>.</li>\n<li>Choose <em>Change</em>.</li>\n<li>Choose <em>Create.</em></li>\n<li>In the dialog box <em>Create RFC Destination for Supervisor</em>, enter a <em>Destination</em> <em>name</em>.</li>\n<li>Mark the checkbox <em>Create User.</em></li>\n<li>Enter a <em>User Name </em>and a password.</li>\n<li>Save your settings.</li>\n<li>Check with help of SAP Note 1616303 if bgRFC is working.</li>\n</ol>\n<p>For more information, enter the keyword bgRFC Configuration at <a href=\"http://help.sap.com/disclaimer?site=http%3A%2F%2Fhelp.sap.com\" target=\"_blank\">http://help.sap.com</a> and refer to SAP Note 2309399 and SAP Note 1616303.</p>\n<p class=\"Default\"><strong>Storage System and Category</strong><strong> </strong></p>\n<p>Output control needs a defined storage system (content repository) to save the rendered form output as PDF.</p>\n<p>To set up the storage system, choose the following navigation option:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"378\">\n<p>SAP Menu</p>\n</td>\n<td valign=\"top\" width=\"378\">\n<p>SPRO &gt; Cross-Application Components &gt; Document Management &gt; General Data &gt; Settings for Storage Systems &gt; Maintain Storage System</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"378\">\n<p>Transaction Code</p>\n</td>\n<td valign=\"top\" width=\"378\">\n<p>/nOAC0</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>You can set up the storage type which fits your needs, for example, a SAP System Database or a HTTP content server (such as fileserver, database). Note that storage type structure repository is not supported.</p>\n<p>See SAP Note 2279725 for more information.</p>\n<p>Once the storage system is available, you need to assign it to the predelivered storage category SOMU.</p>\n<p>To do so, choose the following navigation option:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"378\">\n<p>SAP Menu</p>\n</td>\n<td valign=\"top\" width=\"378\">\n<p>SPRO &gt; Cross-Application Components &gt; Document Management &gt; General Data &gt; Settings for Storage Systems &gt; Maintain Storage Category</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"378\">\n<p>Transaction Code</p>\n</td>\n<td valign=\"top\" width=\"378\">\n<p>/nOACT</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Select category SOMU. For column <em>Document Area</em>, choose <em>SOMU</em>. For column <em>Content Repository</em>, choose the content repository you created in the previous step.</p>\n<p><strong>Business Rule Framework plus (BRFplus)</strong></p>\n<p>Output control uses BRFplus for the output parameter determination. Technically, BRFplus is based on WebDynpro applications. Therefore, you need to set up the according ICF services:</p>\n<p>/sap/bc/webdynpro/sap/fdt_wd_workbench               (FDT Workbench)</p>\n<p>/sap/bc/webdynpro/sap/fdt_wd_object_manager       (FDT Object Manager)</p>\n<p>/sap/bc/webdynpro/sap/fdt_wd_catalog_browser       (FDT Catalog Browser)</p>\n<p>For more information, enter the keyword Active Services in SICF at <a href=\"http://help.sap.com/disclaimer?site=http%3A%2F%2Fhelp.sap.com\" target=\"_blank\">http://help.sap.com</a>.</p>\n<p>Once you’ve set up the services, download and install the required BRFplus applications from SAP Note 2248229.</p>\n<p>Procedure</p>\n<ol>\n<li>Access transaction BRF+.<br/>Note: If required, personalize your screen, and change the user mode from <em>Simple</em> to <em>Expert</em>.</li>\n<li>On the <em>Business Rule Framework plus</em> screen, choose <em>Tools &gt; XML Import</em>.</li>\n<li>On the <em>Business Rule Framework plus – XML Import</em> screen, under <em>File and Transport Request</em>, browse for the local *.xml files you want to import. You can import the files one after the other.</li>\n<li>In the <em>Customizing Request</em> field, enter an applicable Customizing Request ID.</li>\n<li>Choose <em>Upload XML File</em>.</li>\n<li>Choose <em>Back to Workbench</em>.</li>\n</ol>\n<p> </p>\n<p><strong><strong>Adobe Document Services (ADS)</strong></strong></p>\n<p>Applications in SAP S/4HANA ship new default form templates implemented as PDF-based print forms with fragments.</p>\n<p>They require ADS for rendering. ADS is available as cloud solution or on-premise solution.</p>\n<p>The cloud solution is a service provided on the HANA Cloud Platform. See SAP Note 2219598 for more information and links to documentation for the new solution <em>Form Service by Adobe</em>.</p>\n<p>The on-premise solution requires an AS Java installation (with ADOBE usage type) to run ADS. ADS with at least version 10.4 (1040.xxx) is required. This ADS version is delivered with NW 7.31 SP-7 (and higher), NW 7.40 SP-2 (and higher) and NW 7.50 (all SPs).</p>\n<p>You do not necessarily need to use ADS, as output management also supports SAPscript and Smart Forms.</p>\n<p>However, special customizing is necessary for these other form technologies, and restrictions apply. For more information, see SAP Notes 2292539 and 2294198.</p>\n<p> </p>\n<p><strong>Printer setup</strong></p>\n<p>Printing is done using the spool. For more information, see the SAP Printing Guide at the SAP Help portal under <a href=\"http://help.sap.com/disclaimer?site=http%3A%2F%2Fhelp.sap.com%2Fs4hana\" target=\"_blank\"><em>http://help.sap.com/s4hana</em></a><em> &gt; &lt;choose a release&gt; &gt; SAP NetWeaver for SAP S/4HANA &gt; Function-Oriented View &gt; Solution Life Cycle Management.</em></p>\n<p>Output control uses the short name of the printer (for example LP01), as defined in transaction SPAD.</p>\n<p><strong>Limitations for printing via spool</strong></p>\n<ol>\n<li>Please note that printing via spool is not available in release S4CORE 1.00 SP00. Upgrade to S4CORE 1.00 SP01 or higher.</li>\n<li>Currently, a PDF is always created for any kind of form.  <br/>This has the following impact:</li>\n<ol>\n<li>Previewing the document from the spool request is only possible when the device type is PDF1 or PDFUC. </li>\n<li>Using another device type can lead to alignment issues for SAPscript and Smart Forms.</li>\n<li>Printer tray handling is not supported.</li>\n</ol>\n<li>Frontend output is currently not supported, as the output is processed via bgRFC.</li>\n</ol>\n<p> </p>\n<p><strong>Email setup</strong></p>\n<p>Output items sent via channel email will stay in status ‘Pending’ until a notification of the email server is sent back to this output item. Depending on this notification, the status will change to either ‘Completed’ or ‘Error’.</p>\n<p>To enable the notification of output items, you need to configure inbound messages for your SAP Web Application Server. For more information, refer to SAP Note 455140.</p>\n<p><strong>Deletion of application logs</strong></p>\n<p>S/4HANA output management stores application logs for each output request instance with application log object name = OUTPUT_CONTROL and application log subobject name = BGRFC.</p>\n<p>Please note that these application logs should not be deleted by any batch jobs or via transactions, such as SLG2. Otherwise the output request will become inconsistent, and output will not be possible anymore.</p>", "noteVersion": 23}, {"note": "2292646", "noteTitle": "2292646 - SAP S/4HANA output control - form templates with fragments", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP S/4HANA introduces a new style of output management. Output control serves as an interface between the business applications and the SAP NetWeaver technologies.</p>\n<p>For SAP S/4HANA 1709 or a higher please check the standard documentation under help.sap.com.</p>\n<p>For releases 1511 and 1610 this SAP Note provides you with an overview of the new functionality for the PDF-based print forms that are part of the standard shipment.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Output management, output control, form templates, fragments</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP ships new default form templates for each business application implemented as PDF-based print forms with fragments.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Overview</strong></p>\n<p>Form templates in SAP S/4HANA are based on a common style guide to provide a unified appearance across different business applications wherever possible. Exceptions are legal forms or forms for special use cases, for example internal forms.</p>\n<p>Technically, SAP S/4HANA form templates are implemented using SAP Cloud Platform Forms by Adobe. They are based on a gateway service instead of an ABAP interface, and are usually consuming master form templates as fragments. The latter two are new features in SAP S/4HANA which are not available in the SAP Business Suite. Currently only print forms are supported, no interactive forms.</p>\n<p>Based on this technical foundation, SAP ships default form templates for each business application (that has adopted the new output management). Customers can adapt these forms or create their own form templates. Customized forms, however, must fulfill certain requirements to be compatible with SAP S/4HANA.</p>\n<p>Because the form templates are part of output management, please also check SAP Note 2228611.</p>\n<p> </p>\n<p><strong>Form Layout</strong></p>\n<p>The basic layout (such as page size or page orientation) of an Adobe form template is defined by one or several master pages. You typically place application content on the \"normal\" pages (referenced simply as \"pages\") on top of the master pages.</p>\n<p>In SAP S/4HANA, application forms (except legal or special forms) applying the common form style guide technically do not include master pages in the application form itself, but just reference them as a fragment. As a result, <strong>master pages are read-only in the application form using fragments</strong>.</p>\n<p>With this separation you can change the layout and content (like logo, footer texts) of the master page centrally, without any need to adapt the application form template.</p>\n<p>Application forms which cannot follow the common form style guide usually include the master pages directly. In these kinds of forms, you can edit the form master pages.</p>\n<p> </p>\n<p><strong>Form Implementation</strong></p>\n<p><span>Editing</span></p>\n<p>To edit the Adobe form templates in SAP S/4HANA, you need Adobe LiveCycle Designer (ALD) 10.4 or higher.</p>\n<p>Editing an application form using form fragments must always be done offline in ALD. You can download the corresponding XDP file via the <strong>Maintain Form Templates</strong> Fiori app. Using this app, you can also upload the form template back into the system.</p>\n<p><span>Data Connection</span></p>\n<p>The Adobe forms in SAP S/4HANA use a gateway service for data assembly, not an ABAP interface. You can find the assigned gateway service in the properties of the form template.</p>\n<p>For custom enhancements, you can use standard extensibility features (field extensibility &amp; custom logic). Prerequisite is that these features have been enabled by the corresponding business application.</p>\n<p>It is currently not possible to modify the SAP gateway service (see note <a href=\"/notes/2734074\" target=\"_blank\">2734074</a>). Also, using a custom gateway service might not work, as some business applications restrict the use to SAP services.</p>\n<p>For more information about extending OData services, see:</p>\n<ul>\n<li>Redefining SAP Gateway Services <a href=\"https://help.sap.com/doc/05d53b2d3bbb43d2ab5efa23829b2777/1511%20000/en-US/frameset.htm?48dd22512c312314e10000000a44176d.html\" target=\"_blank\">https://help.sap.com/doc/05d53b2d3bbb43d2ab5efa23829b2777/1511%20000/en-US/frameset.htm?48dd22512c312314e10000000a44176d.html</a></li>\n<li>Including and OData Service (SAP Gateway) <a href=\"https://help.sap.com/doc/05d53b2d3bbb43d2ab5efa23829b2777/1511%20000/en-US/frameset.htm?28dd22512c312314e10000000a44176d.html\" target=\"_blank\">https://help.sap.com/doc/05d53b2d3bbb43d2ab5efa23829b2777/1511%20000/en-US/frameset.htm?28dd22512c312314e10000000a44176d.html</a></li>\n</ul>\n<p> </p>\n<p><strong>Translation</strong></p>\n<p>All form templates delivered by SAP are available in all localized languages.</p>\n<p>Customized Adobe form templates need to be manually translated in ALD. You can maintain the different language variants via the <strong>Maintain Form Templates </strong>Fiori app.</p>\n<p>All static texts in a form template need to be translated. Visible texts can be changed directly, but form templates might also include invisible static texts. All of them should be grouped in the subform \"frmHiddenStaticTexts\". If you make this subform visible, you can change all of the texts. Afterwards, set its presence to \"Inactive\" again.</p>", "noteVersion": 9}, {"note": "2292539", "noteTitle": "2292539 - SAP S/4HANA output control - configuration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP S/4HANA introduces a new style of output management. Output control serves as an interface between the business applications and the SAP NetWeaver technologies.</p>\n<p>For SAP S/4HANA 1709 or a higher please check the standard documentation under help.sap.com.</p>\n<p>For releases 1511 and 1610 this SAP Note provides you with an overview of the required customizing options.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Output management, output control, customizing</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Prerequisites:</p>\n<ol>\n<li>\n<div>The technical setup as described in SAP Note 2292571 has been completed.</div>\n</li>\n<li>\n<div>SAP Note 2248229 has been implemented.</div>\n</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You can find the Customizing for output control under:</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"162\">\n<p class=\"TableHeading\">Transaction SPRO</p>\n</td>\n<td valign=\"top\" width=\"522\">\n<p>SAP Reference IMG -&gt; Cross-Application Components -&gt; Output Control</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>The following Customizing activities are available in release S4CORE 1.00:</p>\n<ol>\n<li>Define Output Types</li>\n<li>Define Business Rules for Output Determination</li>\n<li>Assign Output Channels</li>\n<li>Define Rules for Determination of Master Form Template</li>\n<li>Assign Form Templates</li>\n</ol>\n<p>Additionally, the following Customizing activity is available as of release S4CORE 1.01:</p>\n<ol>\n<li>Manage Application Object Type Activation</li>\n</ol>\n<p>You can find the documentation with the respective Customizing activity.</p>\n<p> </p>\n<p><strong>Where to start?</strong></p>\n<p>SAP delivers standard settings for each activity. Hence, it is not required to perform any customizing if these settings fit your needs or you just want to start validation.</p>\n<p> </p>\n<p><span>Manage Application Object Type Activation (S4CORE 1.01 and higher only)</span></p>\n<p>With this Customizing activity you can enable or disable output control for SAP S/4HANA per business application. Note that it depends on the individual business application if this is supported.</p>\n<p>Applications not listed in this activity might have another way to enable the respective other supported framework, for example, by implementing a SAP Note. For these applications, refer to the corresponding simplification item.</p>\n<p> </p>\n<p><span>Define Business Rules for Output Determination</span></p>\n<p>This is the central Customizing activity. Here you can define how the system determines output parameters for a specific business document. It is comparable to \"Output Determination Using the Condition Technique\" in the SAP Business Suite.</p>\n<p>Prerequisite to use this activity is to implement SAP Note 2248229.<br/><br/><em>Direct access via transaction OPD</em></p>\n<p> </p>\n<p><span>Assign Form Templates</span></p>\n<p>This Customizing activity is mandatory for all custom forms.</p>\n<p>Forms maintained here can be used for output determination and for manual output items.</p>\n<p><em>Direct access via view APOC_MV_SFORM_PA</em></p>\n<p><em> </em></p>\n<p><span>Define Rules for Determination of Master Form Temp</span><span>late</span></p>\n<p>You only need this Customizing activity if you use PDF-based print forms with fragments. See SAP Note 2292681 for more information on master form templates (fragments).</p>\n<p>For more information on how to create rules for the master form templates, see the documentation in the Customizing. Part of each rule is the content assignment to the corresponding elements of the master form template. You can assign logos created via transaction SE78 and texts created via transaction SO10 by assigning their IDs.</p>\n<p>Please note that currently, only plain text can be used. All other formatting done in transaction SO10 will be lost. If you need special formatting, implement it in the form template.</p>\n<p> <em>Direct access via view APOC_MV_SFORM_PA</em></p>\n<p><em> </em></p>\n<p><span>Assign Output Channels</span></p>\n<p>This Customizing activity is completely optional. For more information, see the Customizing documentation.</p>\n<p><em>Direct access via view APOC_C_CHANNELV</em></p>\n<p> </p>\n<p><span>Define Output Types</span></p>\n<p>You only need this Customizing activity if the output types delivered by SAP do not fit your needs. Please be aware that most of the requirements, like having a separate form for different scenarios, no longer require a new output type to be set up (as it is the case with NAST). Such cases can now be configured in the activity \"Define Business Rules for Output Determination\". We strongly advise you to check your options in this activity before creating new output types.</p>\n<p>A new output type always requires the implementation of an output type-specific callback class. We strongly recommend consulting before starting this activity.</p>\n<p><em>Direct access via view APOC_I_OUTP_TYPV</em></p>", "noteVersion": 10}]}, {"note": "2267376", "noteTitle": "2267376 - S4TWL - Billing Document Output Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In SAP S/4HANA, a new output management approach has been implemented and is set as the default for newly created billing documents.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Billing Document Output Management, SAP S/4HANA output control</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note is only relevant for SAP S/4HANA till 1709, if you are implementing SAP S/4HANA 1809 or younger, please read referenced note 2790427.</p>\n<p>This note is relevant for you when your start release for the system conversion is an ERP release. It is also relevant if your start release is SAP S/4HANA and you require \"special\" output channels.</p>\n<p>The new output management for SAP S/4HANA, called SAP S/4HANA output control, only supports the \"pure\" output channels, while the NAST-based output can also be used for several kinds of post-processing. SAP S/4HANA output control cannot be used if one of the following NAST transmission mediums is required:</p>\n<ul>\n<li>8 Special function</li>\n<li>9 Events (SAP Business Workflow)</li>\n<li>A Distribution (ALE)</li>\n<li>T Tasks (SAP Business Workflow)</li>\n</ul>\n<p>In this case, please switch back to NAST-based output management.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>As there are valid requirements and use cases for both frameworks with release SAP S/4HANA OP 1610, there is a new customizing activity \"Manage Application Object Type Activation\" where the active framework can be maintained.<br/>You can find the Customizing under output control:</p>\n<p>Transaction SPRO<br/> SAP Reference IMG -&gt; Cross-Application Components -&gt; Output Control -&gt; Manage Application Object Type Activation<br/> <br/>Choose \"Application Inactive\" if you want to switch back to NAST.</p>\n<p>For older releases, please refer to the included correction instruction on how to switch back to NAST-based output management for all billing documents.</p>\n<p>The output management framework is determined with the creation of the billing document. If, following this, the framework is changed, the billing document will be processed using the origin framework. This also applies for migrated billing documents.</p>\n<p>For the SAP S/4HANA output control, configuration is different than for NAST-based output management and must to be created in the Output Parameter Determination (BRF+).</p>\n<p>For technical and implementation details, please refer to simplification item “Output Management” (SAP Note <a href=\"/notes/2228611\" target=\"_blank\">2228611</a>).</p>\n<p> </p>\n<p><strong><span><strong>Business Process related information</strong></span></strong></p>\n<p>SAP S/4HANA output control includes cloud qualities such as extensibility enablement, multi- tenancy enablement, and modification-free configuration. Therefore, the entire configuration differs from the configuration that is used when output management is based on NAST. The configuration is technically based on BRFplus, which is accessible in the cloud. The architecture of SAP S/4HANA output control is based on Adobe Document Services and SAP Cloud Platform Forms by Adobe. For the form determination rules (along with other output parameters), BRFplus functionality is used (in combination with message determination).</p>\n<p>The following forms are delivered by SAP and can be adapted:</p>\n<p> - SDBIL_CI_STANDARD_... used for standard customer invoices</p>\n<p> - SDBIL_CI_PROF_SRV_... used for professional service invoices</p>\n<p> - SDBIL_CM_STANDARD_... used for credit memos</p>\n<p> - SDBIL_IL_STANDARD_... used for invoice lists</p>\n<p>Because the forms may differ from country to country, there is a separate form for each country. For example, SDBIL_CI_STANDARD_DE is for Germany.</p>\n<p> </p>\n<p> </p>", "noteVersion": 11, "refer_note": [{"note": "2790427", "noteTitle": "2790427 - S4TWL - Billing Document Output Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In SAP S/4HANA, a new output management approach has been implemented, but the old NAST technique is set as default.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Billing Document Output Management, SAP S/4HANA output control</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<div class=\"longtext\">\n<p>This note is relevant for you if your start release for the system conversion is an SAP ERP release. It is also relevant if your start release is SAP S/4HANA and you do not require \"special\" output channels.</p>\n<p>The new output management for SAP S/4HANA, called SAP S/4HANA output control, only supports the \"pure\" output channels, while the NAST-based output can also be used for several kinds of post-processing. SAP S/4HANA output control cannot be used if one of the following NAST transmission mediums is required:</p>\n<ul>\n<li>8 Special function</li>\n<li>9 Events (SAP Business Workflow)</li>\n<li>A Distribution (ALE)</li>\n<li>T Tasks (SAP Business Workflow)</li>\n</ul>\n<p>In this case, please use NAST-based output management. If you don't need these kinds of \"special\" output channels, you can use the new SAP S/4HANA output control.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>As there are valid requirements and use cases for both frameworks, there is a new customizing activity \"Manage Application Object Type Activation\" where the active framework can be maintained.<br/>You can find the Customizing under output control:</p>\n<p>Transaction SPRO<br/><em>SAP Reference IMG -&gt; Cross-Application Components -&gt; Output Control -&gt; Manage Application Object Type Activation</em><br/> <br/>Choose \"Application Active\" if you want to switch to new output management for SAP S/4HANA, which is called SAP S/4HANA output control.</p>", "noteVersion": 2}]}], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Additionally to the existing output management based on NAST a new Output Management solution is adopted S/4 HANA-wide. Thereby a unified solution can be used for all Output Management activities. The existing Output Management solution however is still the default."}, {"Activity": "Process Design / Blueprint", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": "Additionally to the existing output management based on NAST a new Output Management solution is adopted S/4 HANA-wide. Thereby a unified solution can be used for all Output Management activities. The existing Output Management solution however is still the default."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Replace custom code for previous NAST-based output management."}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Configure new output management."}, {"Activity": "Technical System Configuration", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "Configure technical infrastructure for new output management."}, {"Activity": "Landscape Redesign", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "If new output management is used, setup required technical infrastructure."}, {"Activity": "User Training", "Phase": "After conversion project", "Condition": "Optional", "Additional_Information": ""}]}