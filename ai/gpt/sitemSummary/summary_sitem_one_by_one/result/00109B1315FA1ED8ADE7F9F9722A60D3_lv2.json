{"guid": "00109B1315FA1ED8ADE7F9F9722A60D3", "sitemId": "SI016: CRM", "sitemTitle": "Availability check using SAP APO not supported", "note": 2693662, "noteTitle": "2693662 - Availability check using SAP APO not supported", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are considering or planning a migration of an SAP CRM 7.0 or SAP CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. In this case, the following information about changed or deprecated functionality is relevant.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRMTWL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Availability check using an external SAP APO system is not supported. The ATP check is always performed against the local SAP S/4HANA system, using the features from SAP S/4HANA.</p>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span>Availability check using SAP APO is configured if table SMOFPARSFA contains an entry where the field PARSFAKEY has the value 'CRMAPOATP' and the field PARVAL1 is not initial.</span></p>", "noteVersion": 3, "refer_note": []}