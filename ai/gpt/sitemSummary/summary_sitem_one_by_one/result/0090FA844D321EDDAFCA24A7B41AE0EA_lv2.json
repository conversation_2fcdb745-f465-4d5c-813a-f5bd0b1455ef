{"guid": "0090FA844D321EDDAFCA24A7B41AE0EA", "sitemId": "SI18: Insurance_FS-RI", "sitemTitle": "S4TWL - FS-RI - Correction of misleading method names", "note": 3310949, "noteTitle": "3310949 - Transition to SAP S/4HANA - FS-RI: correction of misleading method names", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are performing a system conversion to SAP S/4HANA 2023.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\"> Anonymization</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note is relevant if your source release for the system conversion is an ERP release or an SAP S/4HANA release up to and including SAP S/4 HANA 2022.</p>\n<p>It is not relevant if your source release is SAP S/4HANA Finance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The following methods were adjusted in Release SAP S/4HANA 2023:</p>\n<ul>\n<li>Class /MSG/CL_VF_DP_RULESET_POLICY</li>\n<ul>\n<li>allowed_movement_status wird forbidden_movement_status</li>\n</ul>\n</ul>\n<p><span>The following parameters were adjusted in Release SAP S/4HANA 2023:</span></p>\n<ul>\n<li>Class /MSG/CL_VF_DP_UNUSED_OBJECTS</li>\n<ul>\n<li>Method restrictions</li>\n<ul>\n<li>et_allowed_status wird et_allowed_pc_status</li>\n<li>et_mov_status wird et_forbidden_mov_status</li>\n</ul>\n</ul>\n</ul>\n<p><strong>Information related to the business process</strong></p>\n<p>The impact on business processes depends on how you use the renamed objects.</p>\n<p><strong>Required and recommended action(s)</strong></p>\n<p>Check whether you are using the renamed objects specified in the object list and in this SAP Note. If so, you must replace the renamed objects.</p>\n<p>To do this, replace the objects as described in the SAP Note text.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Objects are renamed in SAP S/4HANA 2023. Check via ATC if the objects included in the piece list are used in custom code. If yes, you maybe must adapt the custom code."}]}