{"guid": "00109B1315FA1ED9AB8A7C903B3E80DC", "sitemId": "SI14: Logistics_MM-IM", "sitemTitle": "S4TWL - Change in default IDoc inbound processing with message type DESADV", "note": 2404011, "noteTitle": "2404011 - S4TWL - Change in default configuration of IDoc inbound processing with message type DESADV", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, inbound delivery, shipping notification, EDI, DESADV</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Reason: Conversion from SAP ERP to SAP S/4HANA.</p>\n<p lang=\"en-US\">Prerequisite: In SAP ERP, shipping notifications from suppliers are processed by inbound IDocs of message type DESADV using inbound process code DELS, and neither the industry solution for Automotive has been activated nor the SAP Service Parts Management solution has been implemented.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, the default processing of shipping notifications via EDI using inbound IDocs to create inbound deliveries changed, to support in addition the integration to Extended Warehouse Management, processes from automotive industry and processes in Service Parts Management.</p>\n<p><strong>Business Process Related Information</strong></p>\n<p>When receiving an Advanced Shipping Notification (ASN) from a supplier by processing an inbound IDoc of message type DESADV with inbound process code DELS in SAP S/4HANA, by default the IDoc is now processed using function module /SPE/IDOC_INPUT_DESADV1 to create an inbound delivery.</p>\n<p>Function module /SPE/IDOC_INPUT_DESADV1 supports Extended Inbound Delivery Processing used in automotive industry and in service parts management, including an integration to Extended Warehouse Management. For scenarios where those capabilities are not required, still function module IDOC_INPUT_DESADV1 is available.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>In SAP ERP, function module IDOC_INPUT_DESADV1 is assigned to inbound process code DELS.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>It is recommended to use the more extended function module /SPE/IDOC_INPUT_DESADV1, but function module IDOC_INPUT_DESADV1 is still supported.</p>\n<p>Option 1: Continue with function module /SPE/IDOC_INPUT_DESADV1</p>\n<p>In case of customer extension implementations in SAP ERP using the enhancements V55K0004, V55K0011, V55K0012 or V55K0013, refer to SAP Note 987150 for more details on how to move those enhancements to work with function module /SPE/IDOC_INPUT_DESADV1, if necessary.</p>\n<p>Option 2: Continue using the previously used function module IDOC_INPUT_DESADV1</p>\n<p>Call transaction WE42 for maintaining inbound process codes for IDoc processing. Select process code DELS and choose the details view. Exchange assigned function module /SPE/IDOC_INPUT_DESADV1 by function module IDOC_INPUT_DESADV1.</p>", "noteVersion": 1, "refer_note": [{"note": "987150", "noteTitle": "987150 - ERP 2005 Automotive Upgrade Info reg. User Exits / BAdIs", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><br/>You are using SAP for Automotive regarding Goods Receipt Processing based on inbound deliveries and you are planning to upgrade to ERP 2005.<br/><br/>If you are additionally using User Exits and BAdIs available before ERP 2005 it is urgently recommended to study that note carefully regarding the replacement of User Exits with ERP 2005.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><br/>BORGR, BORES, User Exit, BAdI, EXIT_SAPMBORGR_001, USER_EXIT_004, USER_EXIT_011, USER_EXIT_012, USER_EXIT_013, BORES_IDOC_INPUT_DESADV1, BORES_IDOC_INPUT_SHIPMENT, DESADV, SHPMNT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><br/>To integrate the inbound delivery processing of Service Parts Management with the Industry Solution for Automotive, the program structure of inbound delivery processing was redesigned with ERP 2005.<br/><br/>Before ERP 2005, the Industry Solution for Automotive provided a framework for UI (User Interface) - transaction BORGR and the pre-configured transactions BORGR_V, BORGR_B, BORGR_C - and a framework for IDOC processing. Both frameworks were independently from each other in completing the delivery data entered, validating the data and in collecting messages during processing. Both frameworks met only by using the same core function modules for database updates.<br/><br/>With ERP 2005, a layer called ID_HANDLING (Inbound Delivery Handling) is integrated between UI, IDOC processing or BAPI interface on the one hand and the core function modules for database update on the other hand.<br/><br/>The ID_HANDLING provides the preparation and fulfillment of all actions on the inbound delivery provided for UI, IDOC processing including the enrichment of delivery data, the validation of delivery data, the message collection and the dispatching of the delivery data to other systems. The ID_HANDLING thus takes over the main functionality of the former UI or IDOC processing frameworks of the Industry Solution for Automotive.<br/><br/>The ID_HANDLING layer as a single point of entry ensures an equal treatment of inbound delivery data independent from the data entry.<br/><br/>Compatible to the new ID_HANDLING layer, the UI and the IDOC processing framework was redesigned with impact on customer enhancements. The current note provides information about</p>\n<ul>\n<li>which User Exits and BAdIs (Business Add-Ins) available with SAP for Automotive before ERP 2005 with the UI framework and IDOC processing framework are not available any more for Automotive customers</li>\n</ul>\n<ul>\n<li>which BAdIs available with SAP for Automotive in ERP 2005 should be used instead of User Exits and BAdIs not longer available.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><br/><strong>User Exits and Business Add-Ins (BAdIs) for UI (transaction BORGR)</strong><br/><br/>The following user exits and BAdIs are available with SAP for Automotive before ERP 2005 by the configurable transaction BORGR or the pre-configured transaction BORGR_V, BORGR_B or BORGR_C.<br/><br/>1. User Exit EXIT_SAPMBORGR_001 (function group XBORGR)<br/>           As the purpose of that User Exit is the same as of the Business Add-In (BAdI) BORGR_DIALOG, that BAdI should be used instead with SAP for Automotive in ERP 2005.<br/><br/>2. User Exit EXIT_SAPMBORGR_002 (function group XBORGR)<br/>           That User Exit exists but is not used with SAP for Automotive before ERP 2005. Therefore it is not necessary to replace it.<br/><br/>3. Business Add-In BORGR_DIALOG<br/>           The Business Add-In (BAdI) BORGR_DIALOG allows you to use your own classes instead of standard classes in the User Interface of Goods Receipt Processing for Automotive. The User Interface could be adjusted to a great extent by means of inheritance that is without modifying the program.<br/>           The BAdI could still be used for that purpose with SAP for Automotive ERP 2005.<br/><br/>4. Business Add-In BORGR_POD_DETERMIN<br/>           The Business Add-In (BAdI) BORGR_POD_DETERMIN allows you to influence the purchase order determination in the user interface of Goods Receipt Processing for Automotive or in IDOC processing.<br/>           The BAdI could still be used for that purpose with SAP for Automotive in ERP 2005.<br/><br/>5. Business Add-In BORGR_REGISTRATION<br/>           The Business Add-In (BAdI) is intended for triggering actions or events when inbound deliveries are registered, setting the delivery status \"In Plant\" (or \"In Yard\").<br/>           The BAdI could still be used for that purpose with SAP for Automotive in ERP 2005.<br/><br/><strong><strong>User Exits and Business Add-Ins (BAdIs) for IDOC processing</strong></strong><br/><br/>With SAP for Automotive before ERP 2005, the IDOC processing of inbound deliveries is executed by function module BORES_IDOC_INPUT_DESADV1 of function group BORES.<br/><br/>With SAP for Automotive in ERP 2005, the IDOC processing of inbound deliveries is executed by new function module /SPE/IDOC_INPUT_DESADV1.<br/><br/>As the same subroutines of function group BORES are used also for IDOC processing of inbound shipments if inbound deliveries to be processed are included in the shipment IDOC data, the new function module /SPE/IDOC_INPUT_DESADV1 is also used for processing of inbound delivery data included in shipment IDOC data.<br/><br/>The following user exits are used with SAP for Automotive before ERP 2005 by function module BORES_IDOC_INPUT_DESADV1.<br/><br/>1. User Exit USER_EXIT_004 (program SAPLV55K)<br/>           That User Exit allows you to cancel the IDOC processing or to manipulate the IDOC data directly before the parsing of the IDOC data.<br/>           The User Exit \"004\" is not available for IDOC processing with SAP for Automotive in ERP 2005.<br/>           Instead, method PROCESS_IDOC_DELVRY03 of BAdI /SPE/BADI_IBDLV_CONTROL in class /SPE/CL_IBDLV_CONTROL should be used. See more details in SAP Note 1045312.<br/><br/>2. User Exit USER_EXIT_011 (program SAPLV55K)<br/>           That User Exit is called directly after parsing each IDOC data segment to the application-related internal tables. The data of the internal tables could be manipulated by using that User Exit.<br/>           The User Exit \"011\" is not available for IDOC processing with SAP for Automotive in ERP 2005.<br/>           Instead, method PARSING_IDOC of BAdI /SPE/INB_ID_HANDLING should be used.<br/><br/>3. User Exit USER_EXIT_012 (program SAPLV55K)<br/>           That User Exit is called directly after the determination of the default delivery type and allows the overall manipulation of the parsed delivery data, including the special case of manipulating the delivery type.<br/>           The User Exit \"012\" is not available for IDOC processing with SAP for Automotive in ERP 2005.<br/>           The existing methods ENRICH_HEAD and ENRICH of BAdI /SPE/INB_ID_HANDLING could be used instead for IDOC related manipulation of the delivery creation. The method ENRICH_ITEM is only reached for dialog processing (BORGR/VL60).<br/>           All these BAdI methods allow the manipulation of the delivery data after the SAP standard enrichment has been performed.<br/>           These BAdI methods should allow the manipulation of the delivery data with the same result as with the former User Exit \"012\".<br/>           Additional BAdI methods called before the SAP standard enrichment of delivery data is performed will be available soon and stated in that note as well. Please check on future updates of the current note.<br/><br/>4. User Exit USER_EXIT_013 (program SAPLV55K)<br/>           That User Exit is called after processing the function module GN_DELIVERY_CREATE to create the inbound delivery and allows additional actions together with the inbound delivery creation (e.g. update on additional tables) or the manipulation of the processing status or status messages of the IDOC.<br/>           The User Exit \"013\" is not available for IDOC processing with SAP for Automotive in ERP 2005.<br/>           The method SAVE of BAdI /SPE/INB_ID_HANDLING could be used instead for additional actions (e.g. update on additional tables) based on the delivery creation or update.<br/>           An additional BAdI method called after parsing and handling of the inbound delivery for manipulation of the IDOC status and IDOC status messages will be available after you apply the note <a class=\"th-lk\" href=\"/notes/0002126235\" id=\"C46_W142_V143_solutions_table[1].numm\" target=\"_blank\" title=\"2126235\">2126235 </a>.</p></div>", "noteVersion": 4, "refer_note": [{"note": "865969", "noteTitle": "865969 - Current release restrictions for ERP 2005 - ECC-DIMP", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want information about the release restrictions for release ERP 2005- Industry solution SAP for Discrete Industries and Mill Products</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Release restriction Limitations</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>For the release ERP 2005 -Industry solution SAP for Discrete Industries and Mill Products - restrictions still exist for the productive use of certain functions. SAP want to inform its customers of these functions.<br/><br/>Dear Customer,<br/><br/>SAP announces the following restrictions regarding the use of the<br/>release ERP 2005 - Industry solution SAP for Discrete Industries and<br/>Mill Products - the status of the following statements corresponds to<br/>the date when this note was last changed.<br/><br/>SAP welcomes all questions, suggestions, or comments on the productive use of the release ERP 2005</p>\n<ul>\n<ul>\n<li>Industry solution SAP for Discrete Industries and Mill Products -</li>\n</ul>\n</ul>\n<p>or the individual functions.<br/><br/>You may contact SAP via customer messages, see the assigned components<br/>for the applications listed below.<br/><br/>Sincerely,<br/>SAP<br/><br/>General Restrictions<br/><br/><br/>General remark:<br/>           As of Release mySAP ERP 2005, the ECC-DIMP software (includes the industry solutions mySAP for Automotive, mySAP for Aerospace&amp;Defense, mySAP for Mill Products and Cable, mySAP for High Tech and mySAP for Machinery, Engineering and Construction) is not longer delivered separately from the standard system, but is a part of the mySAP ERP Central Component (SAP ECC) and has the name SAP ECC Industry Extension for Discrete Industries and Mill Products. This means that the ECC DIMP software exists on the system as soon as you have installed SAP ECC. To use ECC-DIMP, you must first activate it using a switch. The Switch Framework forms the technical basis for activating the software.</p>\n<ul>\n<ul>\n<li>Please check all restrictions with respect to ERP 2005 in note 852235. All the restrictions and all references to other notes mentioned in this note apply as well to SAP for Discrete Industries and Mill Products</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>The extension EA-PLM needs to be activated for SAP for Discrete Industries and Mill Products.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Consider before using iPPE the note 652829 immediately after the upgrade of your system with release DI46C2, DIMP 4.71 or ECC DIMP 5.0.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>For development objects of the industry solutions Aerospace &amp; Defense, Automotive and High Tech which are discontinued please refer to note 724080.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>For ITS services, for example mySAP Portal for Suppliers components IS-A-SWP and MM-PUR-GF), the MIME files and templates have to be converted according to note 678904. Please refer also to the SAP online help \"Migration of Existing ITS Services \".</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>If an upgrade is performed from an industry add-on release for industry solutions SAP for Aerospace &amp; Defense, SAP for Automotive, SAP for Engineering &amp; Construction, SAP for High Tech or SAP for Mill Products to ERP2005 the following note has to be considered : 838003 &amp; 874473</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Consider the note 893159 before upgrading from release P3G10A-P3G35A, DI 46B, DI 46C1, DI 46C2 or DIMP471 to ECC-DIMP 600.</li>\n</ul>\n</ul>\n<p><br/>The specific functions:</p>\n<ol>1. Release only with the consent of SAP or release only with approval of or after consultation with SAP.</ol>\n<ul>\n<li>SAP for Aerospace &amp; Defense</li>\n</ul>\n<p>           o IS-ADEC-MPD ( Maintenance Program Definition) <br/>           Maintenance Program Definition<br/>           o IS-ADEC-WTY ( Service Integration Warranty)<br/>           Service Integration Warranty including Warranty Workbench</p>\n<ul>\n<li>SAP for Automotive</li>\n</ul>\n<p>           o IS-A-VMS (Vehicle Management System) <br/>           Supply Chain Event Manager.<br/>           o LO-MD-PPE (Integrated Product and Process Engineering)<br/>                    Integration of project system in iPPE.<br/>                    Focus product structure.<br/>                    Joint production with iPPE<br/>           o IS-A-WTY (Warranty)<br/>           If customers use the warranty application in DI46C2 and are still on CRT6 or below then the upgrade to ECC DIMP 5.0 should be done only after consultation with SAP.</p>\n<ul>\n<li>SAP for High Tech</li>\n</ul>\n<p>           o IS-HT-SW (Software Management) <br/>                    Download of serial numbers, equipment, and configuration to SAP CRM.<br/>                    Software maintenance processing.<br/>                    Billing plan synchronization.<br/>                    License split, bump and version change.<br/>                    Contract management enhancements.</p>\n<ol>2. Release with restrictions</ol>\n<ul>\n<li>SAP for Aerospace &amp; Defense</li>\n</ul>\n<p>           o IS-ADEC-LBK (Logbook) : <br/>                    Using signature strategies with multiple signatures does not work for the Logbook certification functions<br/>                    Notification long text : Deleting components in the component list might cause inconsistencies in the long texts of the notifications assigned to the components.<br/>                    The columns for Date and Time can be shown (input enabled) in the component list. Nevertheless, data is lost when maintained there.<br/>           o IS-ADEC-GPD (Grouping Pegging Distribution) :<br/>                    SAP Note 603261 contains the necessary DI 4.6C/2 to ERP2005 (GPD table TBLP) migration information for GPD customers.<br/>                    Any other customer:<br/>                    No special action needed for upgrading from DIMP 4.71 to ERP 2005.<br/>           o IS-ADEC-CC (Configuration Control) :<br/>           Restriction applies to CC IE4N functionality: The simulation / check mode does not simulate the notification creation process. For restrictions concerning the Component List functionality please refer to component IS-ADEC-LBK.<br/>           o IS-ADEC-WTY (Service Integration Warranty):<br/>           Return part process is currently supported using Free Delivery(SD) A outbound delivery document created with reference to the free delivery document depletes materials from unrestricted stock. Currently special stock indicators cannot be used.<br/>           Warranty Workbench: The performance of selecting documents in the warranty workbench will depend on the complexity of the selection.<br/>           There is no REFRESH option in the warranty workbench.<br/>           o IS-ADEC-MPD (Maintenance Program Definition) :<br/>           MPD supports only the creation of one special type of maintenance plans, the so called Multiple Counter Plan. MPD does not support the creation of a  phased letter check (e.g. C-Check) by using a Multiple Counter Plan with more than one item and more than one cycle set.<br/>           o IS-ADEC-MPN (Material Parts Number) :<br/>           Exchange of material via MIGO is applicable only for Goods Receipt for Purchase Orders. The functionality does not include Goods Issue and types of movements<br/>           No consistency checks are done during Master data set for Restricted Interchangeability due to which the functionality will give error during ATP checks if the master data is not set up consistently with respect to ATP related fields.<br/>           Functionality of Automatic Exchange for Stock Transport Order on ATP failure will not be available via the BAPIs BAPI_PO_CREATE1 and BAPI_PO_CHANGE. Hence the processes code STOBAPI will not be delivered.<br/>           Restricted Interchangeability Flag ( \"Support Restricted Interchangeability\" in the table ADPIC_SETTINGS available via customising ) should be set only after consultation with SAP.<br/>           The availability of the main item after the exchange has taken place is a restriction during the Goods Receipt process. Though the original item is not considered during the posting of material document, the item is still considered for the checks before the posting of the material document. The checks taking place during the posting of the material document only considers the exchanged materials. However, the checks which are done on the item itself like errors for mandatory field check will be carried out and have to be adjusted in order to post the document.</p>\n<ul>\n<li>SAP for Automotive</li>\n</ul>\n<p>           o IS-A-VMS (Vehicle Management System) <br/>           For using VMS with TREX (fast search), TREX version 6. 0 or higher release is required. For using VMS with IPC please refer to note 508321. Please note that IPC 3.0 is not unicode compatible .<br/>           o IS-A-WTY (Warranty)<br/>           If warranty analytics with BW should work, this requires release BW 3.2 or higher.<br/>           o IS-A-JIT-SUM (Supply to Production)<br/>           Only the process 'direct transport order for warehouse management' is not released. All other IS-A-JIT-SUM processes are released.<br/>           o IS-A-MON (Monitors for Suppliers)<br/>           PDF Forms for transactions EMORD and EMASN are only available in german language.<br/>           o IS-A-GR (Goods Receipt Processing Automotive)<br/>           Please note that several User Exits from function group V55K and function group BORGR are not available or used anymore for Goods Receipt Processing Automotive.<br/>           In case you used User Exits and Business Add-Ins (BAdIs) for Goods Receipt Processing Automotive regarding EDI processing and User Interface BORGR (BORGR_V, BORGR_B, BORGR_C) please refer to note 987150.</p>\n<ul>\n<li>SAP for High Tech</li>\n</ul>\n<p>           o IS-HT-SW (Software Management) <br/>           Equipment configuration changes are not downloaded to SAP CRM when configuration is changed as a result of a license split.<br/>           Functions to view version change information, split details or to compare configurations in the serial number history are not available in the ALV version of the report. Please see note 642753 for details on how to revert back to the classic list where these functions are available.</p>\n<ul>\n<li>SAP for Mill Products</li>\n</ul>\n<p>           o Combined Orders cannot be used in connection with APO. <br/>           o Multi Item Orders cannot be used in connection with APO. <br/>           o Extended Batch Assignment (Seamless Batch Integration)<br/>           Extended Batch Assignment is released only for SCM 5. 0 or higher In addition this functionality does not work for processes associated with transaction CO11N.<br/><br/>3. Not released <br/>           SAP for Automotive <br/>            o LO-MD-PPE <br/>           Product structure for co-Products.<br/>           <br/>           SAP for Mill Products<br/>           o Rules Based ATP with Characteristics <br/>           o Alternative Unit of Measure in Inventory and Delivery dependent trading unit change (DTUC)<br/>           o Delivery dependent trading unit change and handling unit managed storage location<br/>           o Extended Batch Assignment Routine 004:<br/>           Alternative unit of measure for sales/delivery process<br/>           Alternative unit of measure for component batches in production order<br/>           Delivery dependent trading unit change: Creation of WM transfer requirement from storage bin to cutting area: The creation of a WM transfer request to a cutting bin is not released. Please use only the WM transfer orders. See setup in DTUC customizing.</p>\n<p>           o  Single unit batch material for Outsourced Manufacturing (LOG_MM_OM_1; New in ERP EhP4)</p>\n<p>           o  Fast Entry of Characteristics in Purchase Order for ERP-TMS: Order Integration 3 (LOG_TM_ORD_INT_III; New in ERP Enhancement Package 7)</p>\n<p>           o IS-MP-NF:<br/>           If you change the rate determination key of a purchase with provision in the invoice verification with NE metals a stock correction for the NE provision will take place. It will not be checked if the provision is booked correct during the goods receipt or if a post-run update is created. In this situation the provision stock can be in short term higher.<br/>           Returns which belongs to a sales order with NF Metals will not be supported.<br/>           Scheduling agreements are not supported.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p></p> <b><b>IS-A-GR</b></b><br/> <p></p> <b> <b>No release</b></b><br/> <p><b>Automotive Goods Receipt (BORGR)</b><br/>Functional Instability of BORGR transaction.</p> <b>This restriction is no longer valid.</b><br/> <p>Limitation ended with Support Package 3<br/>( Changed at 26.04.2006 )<br/><br/></p> <b><b>IS-ADEC-LBK</b></b><br/> <p></p> <b> <b>No release</b></b><br/> <p><b>865969Aircraft Logbook</b><br/>Functional Instability of Logbook functionality. Issues resolved with SP02.</p> <b>This restriction is no longer valid.</b><br/> <p>30+ internal messages solved after SP01 (before SP02 close)<br/>( Changed at 04.01.2006 )<br/></p></div>", "noteVersion": 45}, {"note": "1045312", "noteTitle": "1045312 - New BAdI for user-exit EXIT_SAPLV55K_004", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Upgrading from earlier version to SAP ERP 6.0<br/><br/>No inbound DESADV messages are processed, that used to process the code DE<PERSON> with function module BORES_IDOC_INPUT_DESADV1.<br/><br/>See more details in SAP Note: 987150.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p> BOR<PERSON>, BORES, User Exit, BAdI, USER_EXIT_004, BORES_IDOC_INPUT_DESADV1, DESADV, /SPE/IDOC_INPUT_DESADV1</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Reason:<br/>BORES_IDOC_INPUT_DESADV1 function module was replaced by /SPE/IDOC_INPUT_DESADV1 in ECC6 and the user-exit EXIT_SAPLV55K_004 is not called anymore in the function module /SPE/IDOC_INPUT_DESADV1.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>User-exit EXIT_SAPLV55K_004 is replaced by method PROCESS_IDOC_DELVRY03 of /SPE/BADI_IBDLV_CONTROL BAdI in class /SPE/CL_IBDLV_CONTROL.<br/><br/>To eliminate error, please implement the correction instructions.<br/>During the implementation via SNOTE you have to chosse the Package '/SPE/ID_HANDLING' for the new enhancement '/SPE/IBDLV_CONTROL'. The warning message TR013 can be ignored.</p></div>", "noteVersion": 10}, {"note": "989611", "noteTitle": "989611 - Inbound control framework switch redesign", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This note changes the internal design of the SPM control framework with respect to Extended Inbound Delivery Processing (SPM).<br/><br/>You did an upgrade to SAP ERP 2005 (ECC 6.00) and are working with inbound IDOC processing using function module IDOC_INPUT_DESADV1.<br/>You are using an SAP for Automotive solution. In this case, the IDOC inbound processing via IDOC_INPUT_DESADV1 is automatically re-directed to another function module /SPE/IDOC_INPUT_DESADV1.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IDOC_INPUT_DESADV1, /SPE/IDOC_INPUT_DESADV1</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Change of control framework usage.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached coding changes after having done the following<br/>manual activities:</p> <ol>1. Go to transaction SE91 and select message class /SPE/INBVAL.</ol> <p>              Select change mode and create message number 038 with the following text: \"Replacements only accepted for warehouse of type EWM or in TPOP scenario\". Make sure to tick the \"self-explanatory\" checkbox. <p>              Save. <ol>2. Go to transaction SE38 and enter /SPE/INB_ID_DELETE as program name.</ol> <p>              Go to the menu GOTO-&gt;Documentation-&gt;Change. Copy &amp; paste the following text as documentation and save active. <p>              \"Short text <p>              Inbound Delivery Delete Report <p>              Purpose <p>              This report is used for deleting temporary inbound deliveries that have been marked with the deletion flag in the database. <p>              Prerequisites <p>              The temporary deliveries should have the deletion flag selected in the database. <p>              Features <p>              The deliveries to be deleted can be selected based on the following selection criteria: <p>              Selection <p>              You can make a selection according to the following criteria: <p>              Delivery: delivery number <p>              Created on: creation date of the inbound delivery <p>              You have the following processing options: <p>              Test run : show list only <p>              Package size: number of deliveries to be deleted <p>              Output <p>              The result of the report is displayed in an ALV list.\" <ol>3. Go to transaction SPRO. Go to Logistics Execution -&gt; Service Parts Management (SPM) -&gt; Extended Inbound Delivery Processing (SPM)-&gt; Basic Settings for Extended Inbound Delivery Processing</ol><p>              Enter the change mode for this IMG documentation. Scroll down to chapter \"Communication - Vendor\" and add the following sentence at the end of the chapter: <p>              \"Make sure  that function module /SPE/IDOC_INPUT_DESADV1 is assigned to the IDOC Basic Type 'DELVRY05' and Message Type 'DESADV' in the ALE inbound process settings of 'Assignment of function module to Logical Message and IDOC Type'.\" <p>              Scroll down to chapter \"Communication - ICH\" and add the following sentence at the end of the chapter: <p>              \"Make sure  that function module /SPE/IDOC_INPUT_DESADV1 is assigned to the IDOC Basic Type 'DELVRY05' and Message Type 'DESADV' in the ALE inbound process settings of 'Assignment of function module to Logical Message and IDOC Type'.\" <p>              Save active. <ol>4. Go to transaction SE38. Enter /SPE/VAL134 as program name and push button 'Create'.</ol> <p>              Package: /SPE/INB_VALFRW <p>              Type: I Include Program <p>              Title: Form  VAL_ROUT_134 <p>              Save and activate. <p><br/>As SAP for Automotive customer make sure to check the related note 987150 as this explains in the detail the changes to the IDOC inbound processing in an SAP for Automotive system as of ERP 2005 (ECC 6.00).</p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></div>", "noteVersion": 6}, {"note": "1333354", "noteTitle": "1333354 - BADI: Method PARSING_IDOC does not allow error to be issued", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>As described in Note 987150, you want to replace the former user exit USER_EXIT_011 from the program SAPLV55K with an implementation of the method PARSING_IDOC of the BAdI /SPE/INB_ID_HANDLING. Error messages (although correctly forwarded to table XT_MSG in the implementation) disappear and the delivery may be created without any errors.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Status 53, BAdI, IDoc, check, user exit, validation, on hold</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/><br/>Up to now, the prevalidation overwrote all previous error messages.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br/></p> <b>Remark</b><br/> <p>If you want the outbound delivery to have the status \"On Hold\", you must note the following:</p> <ul><li>The field DOCKEY in the error message table must be filled with    '$        1'  and NO_REAL with 'X' at least.</li></ul> <ul><li>The transfer parameter X_RESULT must be left set to 'X'.</li></ul></div>", "noteVersion": 3}, {"note": "1044725", "noteTitle": "1044725 - IDOC's status 52 is missing in /SPE/IDOC_INPUT_DESADV1", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Inbound delivery is created via IDOC entry with warning messages. For such an IDOC the status 52 is expected (ok-incomplete), meanwhile the IDOC's status is set to 53 ('ok').</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IDOC, /SPE/IDOC_INPUT_DESADV1</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The warning messages are ignored and the status is always set to 53 ('ok')</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>In case of warning messages the IDOC status will be set to 52 ('ok  incomplete). The warning messages will be send to the external system with outbound IDOCs. The new BAdI is provided to suppress the warning messages and change the IDOC's status if necessary.<br/>To get rid of the issue implement the attached correction and do the following manual steps:<br/><br/>1. Go to trx.SE18<br/>2. Choose the line \"BAdI Name\", enter \"/SPE/INB_ID_HANDLING\"<br/> and press \"Change<br/>3. Go to \"Interface\" tab and make double click on \"Interface name\"<br/>4. Enter new method  \"IDOC_STATUS\" and the Description \"IDOC's status handling\"<br/>5. Set the cursor on the new method and press button \"Parameters\"<br/>6. Create the following new parameters:<br/>Parameter         Typing Method   Associated Type<br/>IT_IDOC_STATUS      Type            BDTIDOCSTA<br/>X_RESULT            Type           /SPE/INB_RESULT<br/><br/>7. Save and activate changes.<br/><br/></p></div>", "noteVersion": 3}, {"note": "2126235", "noteTitle": "2126235 - IDoc - BAdI idoc_status does not work in function module /SPE/IDOC_INPUT_DESADV1", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>At the end of the function module /SPE/IDOC_INPUT_DESADV1, the BAdI idoc_status is called, but the result does not affect the processing of the IDoc. In addition, the IDoc information to make a decision based on this is also missing in the BAdI.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>idoc_status; /SPE/IDOC_INPUT_DESADV1; BAdI; l_return</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The reason for this is that the BAdI can change the parameter lv_return, but this parameter is afterwards no longer queried.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The call of the BAdI makes sense only if the processing of the IDOC was ok. If the processing was not ok, the BAdI must not change anything.</p>\n<p>If the BAdI changes the l_result parameter, either a commit or a rollback must be performed. In addition, the status of the IDoc can be changed in the BAdI.</p>\n<p>Implement the correction instructions and the manual corrections.</p>", "noteVersion": 4}]}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "See Business Impact Note for details"}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "See Business Impact Note for details"}]}