{"guid": "6CAE8B3E8C3B1ED792D9EB9E8CFFE0C6", "sitemId": "BW101: DataStore Objects", "sitemTitle": "BW4SL - DataStore Objects (classic)", "note": 2451013, "noteTitle": "2451013 - BW4SL - DataStore Objects (classic)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The SAP BW DataStore Object (classic) is not available in SAP BW/4HANA. It can be converted to a DataStore Object (advanced) via the SAP BW/4HANA Transfer Tool.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ODSO, DODS, ADSO, DSO</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are available in or can be converted to SAP BW/4HANA:</p>\n<p>See node: \"Supported by Transfer Tool\" --&gt; \"BW4 Transfer Tool\"</p>\n<p>For the in-place conversion your system needs to be on SAP BW 7.5 SP 5, powered by SAP HANA or higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Under node \"Supported by Transfer Tool\" --&gt; \"BW4 Transfer Tool\" --&gt; \"TLOGO ODSO (DataStore Object (classic))\" the DataStore Objects are listed. They can be transferred \"in-place\" or via \"remote conversion\" to DataStore Objects (advanced) (ADSOs).</p>\n<p>Transfer:</p>\n<ul>\n<li><strong>Write-optimized ODSOs</strong> used in Queries or MultiProviders</li>\n<ul>\n<li>Will be transferred into ADSOs with option \"Keep inbound data, extract from inbound queue\".</li>\n<li>Please note that the write-optimized ODSO needs to have a semantic key. Write-optimized ODSOs w/o semantic key cannot be transferred and will cause the Transfer Tool to stop. Remove the ODSO from the MultiProvider or delete the Queries to continue.</li>\n<li>The data won't automatically be activated after the transfer. </li>\n</ul>\n<li><strong>Write-optimized ODSO</strong> not used in Queries or MultiProviders</li>\n<ul>\n<li>Will be transferred into ADSOs without activation.</li>\n<li>Please note that ADSOs provide more flexibility for write-optimized use-cases like corporate memories.</li>\n</ul>\n<li><strong>Standard ODSOs</strong></li>\n<ul>\n<li>Will be transferred into ADSOs with activation and change log.</li>\n</ul>\n<li><strong>ODSO for direct update</strong></li>\n<ul>\n<li>Will be transferred into ADSOs for direct update.</li>\n</ul>\n<li>In ODSOs, if a characteristic is key, compounding characteristics did not have to be in the key as well. This has changed for ADSOs. The transfer tool will check and adapt the key during transfer, so that also the compounding characteristics are included in the key automatically.</li>\n<li>The Transfer Tool will automatically replace process chain variant \"Activate Data in DataStore Objects (classic)\" with process chain variant \"Activate Data in DataStore Objects (advanced)\".</li>\n<li>The Transfer Tool will automatically replace process chain variant \"Deletion of Requests from Change Log of DSO (classic)\" and \"Deletion of Requests from Write-optimized DSO (classic)\" with process chain variant \"Clean Up Old Requests in DataStore Objects (advanced)\".</li>\n</ul>\n<p>Restrictions:</p>\n<ul>\n<li>In-memory optimized DataStore Objects: ODSOs with option \"HANA-optimized\" cannot be converted into ADSOs. Use report RSDRI_RECONVERT_DATASTORE to revoke the option first.</li>\n<li>Field based DSO are not supported by the BW/4 conversion. Please remodel this kind of scenario manually.</li>\n<li>For write-optimized ADSOs, a semantic key cannot be defined without enabling the option \"Activate Data\". If a write-optimized ODSO is transferred into such an ADSO, the semantic key will be ignored. This might produce an unexpected behavior if the semantic key was used in a transformation for currency conversion for example. Please remodel this kind of scenario manually as follows:</li>\n<ul>\n<li>Create an InfoSource with the corresponding ODSO as template</li>\n<li>Make sure that the semantic key of the ODSO is defined as the key for the InfoSource</li>\n<li>Create a transformation connecting the source of the ODSO with the InfoSource</li>\n<li>Enable the currency conversion in the corresponding rule</li>\n<li>Create a transformation connecting the InfoSource with the ODSO</li>\n</ul>\n<li>If a process variant for the deletion of old requests from changelogs or write-optimized ODSOs is used, please note that there are restrictions on the corresponding process variant for the deletion of old requests from DataStore objects (advanced). See SAP Note <a href=\"/notes/2448086\" target=\"_blank\">2448086</a> for details.</li>\n<li>Transfer of ODSO with Key Figure Aggregation Type NO2 is only supported for direct update ODSOs, for other DSO types manual redesign is required before transfer.</li>\n</ul>\n<p><strong>Related Information</strong></p>\n<p>See the documentation for more details: <br/><a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.2/en-US/b76fca02fa804d569c386b9b437f85d6.html\" target=\"_blank\">Templates for Modeling the Data Warehousing Layers</a></p>", "noteVersion": 8, "refer_note": [{"note": "1849498", "noteTitle": "1849498 - SAP HANA: Reconversion of SAP HANA-optimized DataStores", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the database management system SAP HANA and you have created SAP HANA-optimized DataStore objects (DSOs).<br/><br/>When SAP Note 1849497 is available, you can no longer create new SAP HANA-optimized DataStore objects. Instead, we recommend that you use classic DataStore objects that have a similar performance with the software level mentioned below (for further details, see SAP Note 1849497).<br/><br/>We recommend that you convert existing SAP HANA-optimized DataStore objects back into classic DataStore objects. To do this, proceed as follows:</p> <ul><li>The DataStore object does not contain any data.</li></ul> <ul><ul><li>Call the modeling of the DataStore.</li></ul></ul> <ul><ul><li>Deactivate the \"Optimized for SAP HANA\" flag.</li></ul></ul> <ul><ul><li>Reactivate the DataStore object.</li></ul></ul> <ul><li>The DataStore object contains data.</li></ul> <ul><ul><li>Call transaction SE38.</li></ul></ul> <ul><ul><li>Start the report \"RSDRI_RECONVERT_DATASTORE\".</li></ul></ul> <ul><ul><li>Select the DataStores to be converted.</li></ul></ul> <ul><ul><li>Choose if you want to retain the change log after the conversion.</li></ul></ul> <ul><ul><li>Start the report.</li></ul></ul> <ul><ul><li>Important: You must carry out these steps in all connected systems (development, quality, production) to prevent problems during the transport of the objects.</li></ul></ul> <p><br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RSDRI_DBA_HDB RSDRI DSO DataStore object ODS Operational Data Store HDB SAP HANA HANADB migration<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You use the database management system (DBMS) SAP HANA SP5 with a revision &gt;= 57.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver BW 7.30<br/><br/>Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010) into your BW system. The Support Package is available when <b>SAP Note 1810084</b> \"SAPBWNews NW 7.30 BW ABAP SP10\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.31 (SAP NW BW 7.3 Enhancement Package 1)<br/><br/>Import Support Package 09 for SAP NetWeaver BW 7.31 (SAPKW73109) into your BW system. The Support Package is available when <b>SAP Note</b><b><b>1847231</b></b><b> </b>with the short text \"SAPBWNews NW BW 7.31/7.03 ABAP SP9\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.40<br/><br/>Import Support Package 4 for SAP NetWeaver BW 7.40 (SAPKW74004) into your BW system. The Support Package is available when <b>SAP Note</b><b><b> 1853730</b></b><b> </b>\"SAPBWNews NW BW 7.4 ABAP SP04\", which describes this Support Package in more detail, is released for customers.</li></ul> <p>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 3, "refer_note": [{"note": "1895207", "noteTitle": "1895207 - Delta merge of history tables fails with error 2454", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Delta merge on history tables fails with \"2048: column store error: merge delta index error:  [2454] general error during historisation of data\".</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>SAP HANA, delta merge, historisation, history table, SAP HANA-optimized DSO</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In very rare cases it can happen that internal values of history tables get corrupted. This happens if a delta merge starts when a previous write transaction on the same history table is in a special state of its commit phase. Additionally there are two more prerequisites that this scenario can happen.<br/>1. Before the delta merge starts and the write transaction is in its commit phase there has to be at least one record in the table which is not merged.<br/>2. After the delta merge and the commit phase of the write transaction at least one write transaction for another table has to be executed before the next savepoint is written.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The issue is fixed with SAP HANA Revision 62.<br/><br/>To repair the affected history table you have to do a full reload.<br/><br/>In case the history table is a SAP HANA-optimized DSO you can convert this DSO to a optimized standard DSO (see SAP Note 1849497 and 1849498) without keeping change log.<br/></p></div>", "noteVersion": 1}, {"note": "1879656", "noteTitle": "1879656 - Numeric overflow in commit ids: History table gets unusable", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Changelog extraction for SAP-HANA-optimized DataStores doesn't return any result. Rollback of the request of changelogs return an error. MERGE operation of tables with history table returns an error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>InMemory DSO, SAP-HANA-optimized DataStore, IMO, Changelog extraction, history table, HANA, BW on HANA, BWonHANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The following two prerequisites have to be fullfilled both in order to run into this problem:<br/><br/>1. The problem only exists if the history table was created before Revision 45, e.g. by converting a standard DSO to a SAP-HANA-optimized DataStore.<br/><br/>2. more than 2^31 commits have been executed in the HANA datatabase.<br/><br/>Background:<br/>History tables contain the internal columns $validfrom$ and $validto$. These columns are used for storing the commit ID of the transaction that commits the insert or update of the record into history table.<br/>In revisions prio to SP5 (Revision 45), these columns have been created using datatype INTEGER (4 Byte). In revision 45, this datatype was changed to BIGINT (8 Byte), so all history tables created with Revision 45 or later do not have this problem.<br/>If more than 2^31 commits have already been executed, a numeric overflow will happen when selecting data from these columns, which will result in an empty result set. This means that subsequent data targets into which a delta extraction is done, no data will be loaded (even though the request is green with zero records) and the data target is not consistent anymore with the source DSO.<br/><br/>In order to find out if there are DSOs that still use 4 byte datatype (prerequisit 1), please execute the following SQL statement as SYSTEM user in the HANA studio. The statement will return the active data tables of the DSOs. If this statement does not return a result there is no reason for further checks:<br/><br/>select distinct(table_name),schema_name from sys.m_cs_tables_ where table_oid in (select table_oid from sys.cs_COLUMNS_ where  column_name = '$validfrom$' and  trex_type_id = 73) and table_name like '%00' order by table_name<br/><br/>For finding all tables with a history part, please execute this statement:<br/>select distinct(table_name),schema_name from sys.m_cs_tables_ where table_oid in (select table_oid from sys.cs_COLUMNS_ where  column_name = '$validfrom$' and  trex_type_id = 73) order by table_name<br/><br/>In order to find the current commit Id counter of your HANA (prerequisite 2), please execute the following SQL statements as SYSTEM user in HANA studio:<br/><br/>create table dummy_check_table___(x int primary key);<br/>insert into dummy_check_table___ values (1);<br/>--commit; only needed when autocommit=off<br/>select top 1 last_commit_id from SYS.M_TRANSACTIONS_ where CONNECTION_ID = CURRENT_CONNECTION;<br/>drop table dummy_check_table___;<br/><br/>The difference between last_commit_id and 2^31 will tell you, how urgent it is to take action.<br/><br/>If last_commit_id is larger than 2147483648 (2^31), you can find out all DSOs with problematic requests by the following SQL statement, executed as SAPSID-User (table name is an example):<br/><br/>select distinct(SID) from \"/BI0/A&lt;DSO-NAME&gt;80\" WHERE \"$validfrom$\"&gt;2147483648<br/><br/><br/>Other indicators for the problem:<br/>You find entries in the indexserver_alert like [46978]{-1}[240/718257265] 2013-06-27 10:00:45.369678 e attributes SingleUpdate.cpp(00685) : Additional info for previous 6930: index \"SAPBWN:/BI0/A&lt;DSO-NAME&gt;80 (317462)\", attribute \"$validfrom$\", docid 205, value \"2482785352\"</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The solution for this problem is to upgrade to a HANA SP6 Revision larger than 60. This revision will contain a migration procedure, which converts the datatype of the columns $validfrom$ and $validto$ to BIGINT (8 Bytes). The exact revision number, that contains the migration procedure will be published in this note soon.<br/><br/>If there are SAP-HANA-optimized DataStores with 4 byte datatype in the $validfrom$ and $validto$ column, but the commit ID has not yet reached 2^31, you currently have the following possibility:<br/>Please upgrade your HANA database to Revision 57 or 58.<br/>Please upgrade your BW system to 7.30 SP8 or SP9, 7.31. SP7 or SP8  and implement the SAP Notes 1849498 and 1849497, alternatively please upgrade your BW system to 7.30 SP10 or 7.31 SP9 that contains the relevant ABAP coding.<br/>With the help of these notes and service packs, we recommend to use the new activation procedure for Standard DSOs and to convert all SAP-HANA-optimized DataStores to Standard DSOs.<br/>For more information about this new activation procedure, please read the following blog:<br/>http://scn.sap.com/community/data-warehousing/netweaver-bw/blog/2013/05/10/bwonhana-standard-dso-also-optimized-for-hana<br/><br/>In all other cases, please create a ticket on component HAN-DB or HAN-DB-ENG-BW.</p></div>", "noteVersion": 5}, {"note": "1849497", "noteTitle": "1849497 - SAP HANA: Optimization of standard DataStore objects", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the database management system (DBMS) SAP HANA and load data in DataStore objects (DSOs).<br/><br/>Up to now, you had to model SAP HANA-optimized DataStore objects or convert existing standard DataStore objects into SAP HANA-optimized DataStores using transaction RSMIGRHANADB in order to benefit from the outstanding activation performance. During this process, the table layout was changed in such a way that the data of the change log was calculated with a calculation view.<br/><br/>After you implement the advance correction or import the Support Packages specified below, the conversion or explicit modeling of SAP HANA-optimized DataStores is no longer required.<br/><br/>All standard DataStore objects now use an SAP HANA-optimized activation process or rollback process that has a comparable performance. During this process, the data of the change log is saved in a transparent table so that no conversion of the table layout is required. By supporting the concept for non-active data (see SAP Note 1767880), the memory consumption remains at the level of the SAP HANA-optimized DataStores.<br/><br/>Creating SAP HANA-optimized DataStores is no longer possible when this SAP Note is available. Existing SAP HANA-optimized DataStores are still supported. However, for standardization reasons, we recommend that you convert SAP HANA-optimized DataStore objects back into classic DataStore objects. For more information on this, see SAP Note 1849498.<br/><br/>Further information is available at http://scn.sap.com/docs/DOC-41327.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RSODSO RSODSO_PROCESSING RSODSO_ROLLBACK DSO ODS HDB HANADB change log</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are using the DBMS SAP HANA SP5 with a revision &gt;= 57.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li>SAP NetWeaver BW 7.30<br/><br/>Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010) into your BW system. The Support Package is available once <strong>SAP Note 1810084</strong> (\"SAPBWNews NW BW 7.30 ABAP SP10\"), which describes this SP in more detail, has been released for customers.</li>\n</ul>\n<ul>\n<li>SAP NetWeaver BW 7.31 (SAP NW BW 7.3 Enhancement Package 1)<br/><br/>Import Support Package 09 for SAP NetWeaver BW 7.31 (SAPKW73109) into your BW system. The Support Package is available once <strong>SAP Note 1847231</strong> (\"SAPBWNews NW BW 7.31/7.03 ABAP SP9\"), which describes this SP in more detail, has been released for customers.</li>\n</ul>\n<ul>\n<li>SAP NetWeaver BW 7.40<br/><br/>Import Support Package 4 for SAP NetWeaver BW 7.40 (SAPKW74004) into your BW system. The Support Package is available once <strong>SAP Note 1853730</strong> (\"SAPBWNews NW BW 7.40 ABAP SP04\"), which describes this SP in more detail, has been released for customers.</li>\n</ul>\n<p><br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><strong>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</strong><br/><br/>To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note still contains the words \"Preliminary version\".</p></div>", "noteVersion": 7}, {"note": "1891529", "noteTitle": "1891529 - SPO: Reconversion of SAP HANA-optimized DSO partitions", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The program RSDRI_RECONVERT_DATASTORE described in SAP Note 1849498 enables the reconversion of SAP HANA-optimized DataStore objects. If an SAP HANA-optimized DataStore object is a partition of a semantically partitioned object (SPO), a reconversion was not supported up to now.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Semantically partitioned object, SPO, partitions, DataStore object, DataStore, DSO, SAP HANA-optimized, SAP HANA, HANA, HDB, IMOFL, conversion, reconversion, data, data conversion, migration, RSMIGRHANADB, RSDRI_CONVERT_CUBE_TO_INMEMORY, RSDRI_RECONVERT_DATASTORE.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This SAP Note is relevant only if you use SAP HANA as the database platform.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The program RSDRI_RECONVERT_DATASTORE now also supports the reconversion of semantically partitioned, SAP HANA-optimized DataStore objects. The process for the reconversion of an SPO corresponds to the process for a conversion as described in SAP Note 1685280.</p> <ul><li>SAP NetWeaver BW 7.30<br/><br/>Import Support Package 11 for SAP NetWeaver BW 7.30 (SAPKW73011) into your BW system. The Support Package is available when <b>SAP Note 1878293</b> \"SAPBWNews NW 7.30 BW ABAP SP11\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.31 (SAP NW BW 7.3 Enhancement Package 1)<br/><br/>Import Support Package 10 for SAP NetWeaver BW 7.31 (SAPKW73110) into your BW system. The Support Package is available when <b>SAP Note</b><b><b> 1882717</b></b><b> </b>with the short text \"SAPBWNews NW BW 7.31/7.03 ABAP SP10\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.40<br/><br/>Import Support Package 5 for SAP NetWeaver BW 7.40 (SAPKW74005) into your BW system. The Support Package is available when <b>SAP Note</b><b><b> 1888375</b></b><b> </b>\"SAPBWNews NW BW 7.4 ABAP SP05\", which describes this Support Package in more detail, is released for customers.</li></ul> <p>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 1}, {"note": "1888106", "noteTitle": "1888106 - Table RSODSACTUPDTYPE is not found when activating DSO", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>After implementing SAP notes <a href=\"/notes/1849497\" target=\"_blank\">1849497</a> and <a href=\"/notes/1849498\" target=\"_blank\">1849498</a> to convert a HANA optimized DSO to a standard DSO, the activation of a request fails with the error message below shown in transaction SM21 and the request log:</p>\n<pre>Error during in-memory activation (see long text)<br/>transaction rolled back by an internal error: failed to activate dso with persisted changelog: [410 09] SAPHBW:RSODSACTUPDTYPE<br/>Table SAPHBW:RSODSACTUPDTYPE: Table not found does not exist<br/>Table SAPHBW:RSODSACTUPDTYPE does not exist</pre><h3 class=\"section\" data-toc-skip=\"\" id=\"Environment\">Environment</h3><p>BW on HANA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Cause\">Cause</h3><p>Table RSODSACTUPDTYPE is mistakenly located in the row-store instead of the column-store.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Resolution\">Resolution</h3><p>You can fix this error by transferring table RSODSACTUPDTYPE into the column-store.</p>\n<p>In order to do so, execute the following SQL-statement in HANA Studio (please change the schema owner accordingly):</p>\n<pre>alter table SAPHBW.RSODSACTUPDTYPE column</pre>\n<p>Afterwards the activation should work again.</p>\n<p>This change has already been considered in the latest row-store list attached to SAP note <a href=\"/notes/1659383\" target=\"_blank\">1659383</a> used by SAPINST and report RSDU_TABLE_CONSISTENCY.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Keywords\">Keywords</h3><p>row-store; row store; column store; column-store; alter table; SAPINST; RSDU_TABLE_CONSISTENCY;</p>", "noteVersion": 3}]}, {"note": "2421930", "noteTitle": "2421930 - Simplification List for SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Simplification List for SAP BW/4HANA is published as PDF document on the SAP Help Portal (<a href=\"http://help.sap.com/bw4hana20\" target=\"_blank\">http://help.sap.com/bw4hana20</a>). This SAP Note provides the Simplification List in spreadsheet format.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SIMPL, BW4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Planning to transition from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable our customers to better plan and estimate their path to SAP BW/4HANA, we have created the “Simplification List for SAP BW/4HANA”. In this list we describe in detail, on a functional level, what happens to individual object types and solution capabilities when transitioning to SAP BW/4HANA. Compared to the SAP Business Warehouse products, we have in some cases merged certain functionality with other elements or reflected it within a new solution or architecture.</p>\n<p><strong>A new, web-based UI for searching and displaying Simplification Items</strong></p>\n<p>In parallel to the PDF document and the spreadsheet available via this SAP Note, the <em>SAP Simplification Item Catalog</em> offers direct search and browse of SAP BW/4HANA Simplification Items in their current state via the SAP ONE Support Launchpad at <a href=\"https://launchpad.support.sap.com/#/sic/\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/</a>.</p>", "noteVersion": 6, "refer_note": [{"note": "2347382", "noteTitle": "2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to run or you are already running an SAP BW/4HANA system.<br/>You want to convert your SAP BW system to SAP BW/4HANA.<br/>You want to upgrade your system from SAP BW/4HANA 1.0 to SAP BW/4HANA 2.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revision, BW4, BW/4HANA, BW, NetWeaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note contains information about the following topics:</strong></p>\n<ol>\n<li>General Information about SAP BW/4HANA and the conversion to SAP BW/4HANA</li>\n<li>Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</li>\n<li>Information relevant for SAP BW/4HANA 2023</li>\n<li>Information relevant for SAP BW/4HANA 2021</li>\n<li>Information relevant for SAP BW/4HANA 2.0</li>\n<li>Information relevant for SAP BW/4HANA 1.0</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>1. General information about SAP BW/4HANA and the conversion to SAP BW/4HANA</span></strong></p>\n<p><span>High Level information about BW/4HANA: <a href=\"http://www.sap.com/bw4hana\" target=\"_blank\">http://www.sap.com/bw4hana</a></span></p>\n<p><span>See SAP Note <a href=\"/notes/2347384 \" target=\"_blank\">2347384</a> - SAP BW/4HANA 1.0 Important Notes before you start</span></p>\n<p><span>When planning your system landscape(s) for the next release of SAP BW/4HANA (not release 1.00), you may want to take the available applicaton server platforms into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> </span></p>\n<p><span>SAP changed the <a href=\"https://support.sap.com/en/my-support/knowledge-base/security-notes-news.html\" target=\"_blank\">SAP security strategy</a> to cover security corrections for Support Packages delivered during the past 24 months (formerly 18 months). BW/4HANA will not extend this period. We provide security corrections for Support Packages delivered in the last 18 months as before.</span></p>\n<p><span>See note </span><a href=\"/notes/2733740\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2733740</a> for Information/Restriction about Relase BW/4HANA 2.0</p>\n<p><strong>2. Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Productively used SAP BW/4HANA release</strong></td>\n<td><strong>Recommended minimum SAP HANA SP/revision</strong></td>\n</tr>\n<tr>\n<td>1.0</td>\n<td>Revision 122.12 or higher (HANA 1.0 SPS 12)<br/>Revision 12 or higher (HANA 2.0 SPS 1 or higher)</td>\n</tr>\n<tr>\n<td>2.0</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2021</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2023</td>\n<td>Revision 71 or higher (HANA 2.0 SPS 7 or higher)</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that SAP strongly recommends to apply the <span>latest available</span> SAP HANA revision.</p>\n<p>For details about SAP HANA Maintenance Strategy see SAP Note <em><a href=\"/notes/2021789\" target=\"_blank\">2021789</a> - SAP HANA Revision and Maintenance Strategy</em>.</p>\n<p>Please keep the update paths for Maintenance Revisions in mind. Details can be found in SAP Note <em><a href=\"/notes/1948334\" target=\"_blank\">1948334</a> - SAP HANA Database Update Paths for Maintenance Revisions</em>.</p>\n<p>Notes with regards to SAP HANA Revisions:</p>\n<ul>\n<li>HANA SPS12: SAP Note <a href=\"/notes/2298750\" target=\"_blank\">2298750</a> - SAP HANA Platform SPS 12 Release Note</li>\n</ul>\n<p><span>SAP BW/4HANA and SAP HANA 2.0: </span></p>\n<p>SAP BW/4HANA 1.00 (including SAP_BASIS 7.50) has been released for HANA 2.0. Details can be found in note 2420699.<br/>SAP BW/4HANA 2.00 (including SAP_BASIS 7.53) and newer run on HANA 2.0 only</p>\n<p><strong> </strong></p>\n<p><strong>3. </strong><strong>Information relevant for SAP BW/4HANA 2023 (planned mid of Q4/2023)</strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3365187\" target=\"_blank\">3365187 - Information/Restriction Note for Release SAP BW/4HANA 2023</a>.</p>\n<p><strong><strong>4. Information relevant for SAP BW/4HANA 2021</strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3100794\" target=\"_blank\">3100794 - Release Information/Restrictions Note for SAP BW/4HANA 2021</a>.</p>\n<p><strong><strong><strong>5. Information relevant for SAP BW/4HANA 2.0</strong></strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"https://me.sap.com/notes/2733740\" target=\"_blank\">2733740 - Release Information/Restrictions Note for SAP BW/4HANA 2.0</a></p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>See note 2733740 - Release Information/Restriction Note for SAP BW/4HANA 2.0 for restrictions in BW/4HANA 2.0</li>\n<li>Urgent! Implement note <a href=\"/notes/2770525\" target=\"_blank\" title=\"2770525  - Data Transfer Intermediate Storage (DTIS): Missing package assignment\">2770525 - Data Transfer Intermediate Storage (DTIS): Missing package assignment</a> <span><strong>before</strong></span> upgrading to BW/4HANA 2.0</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Search/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Introduction Video: <a href=\"https://youtu.be/bxH0EzAhSwQ\" target=\"_blank\">https://youtu.be/bxH0EzAhSwQ</a></li>\n</ul>\n<li><span>Upgrading BW/4HANA 1.0 to 2.0</span></li>\n<ul>\n<li>We now introduced tasklist SAP_BW4_BEFORE_UPGRADE and SAP_BW4_AFTER_UPGRADE  to support you upgrading your system. Please see note 2846537 - Taskliste for upgrade of BW/4HANA</li>\n<li>Afte the upgrade to BW/4HANA 2.0 the flag TADIR Popup f. Obj. in View RSADMINC gets initialized. Please see note 2884312 for details.</li>\n</ul>\n<li><span>ABAP Software Stack</span></li>\n<ul>\n<li>The initial Software stack for BW/4HANA 2.0 looks like this:</li>\n<ul>\n<li>SAP_BASIS 7.53 SP01</li>\n<li>SAP_ABA 7.5D SP01</li>\n<li>SAP_UI 7.53 SP02</li>\n<li>SAP_GWFND 7.53 SP01</li>\n<li>ST-PI 7.40 SP09</li>\n<li>DW4CORE 2.00 SP00</li>\n<li>UIBAS100 4.00 SP01</li>\n</ul>\n<li>This should dramatically reduces the effort for installation</li>\n<li>During the upgrade from BW/4HANA 1.0 the missing software components are installed automatically</li>\n<li></li>\n</ul>\n<li><span>BW/4HANA 2.0 SP01</span></li>\n<ul>\n<li>Please install following note on top of SP01 directly after update:</li>\n<ul>\n<li>2779827  Error starting task list SAP_BW4_LAUNCHPAD_SETUP from other task lists</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP02 </span></li>\n<ul>\n<li>SAP_UI 7.54 SP1 as alternative UI version is released. Please check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07</li>\n<li>please also take into consideration the Support Package Upgrade Strategy when running on higher UI Version described in note <strong>2618449</strong></li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP04</span></li>\n<ul>\n<li>SAP_UI 7.54 &gt;= SP2 as alternative UI version is released. lease check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07. Also take into consideration the Support Paackage Strategy when running on higher UI version described in note <strong>2618449</strong></li>\n<li>NSE for DTO --&gt; available since HANA 2.0 SP04 + limitation see HANA Note <a href=\"/notes/2771956\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2771956</a></li>\n<li>Open Hub with SDA --&gt; available since HANA 2.0 SP04 Revision 46</li>\n<li>XDA --&gt; available since HANA 2.0 Sp04 Revision 40 </li>\n<li>Geo Support --&gt; available since HANA 2.0 SP04 Revision 45</li>\n<li>Treespec Partitioning in ADSO --&gt; available since Hana 2.0 SP04 </li>\n</ul>\n</ul>\n<p><strong>6. Information relevant for SAP BW/4HANA 1.0</strong></p>\n<p>Installation/Setup</p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>Please see SAP Note<strong> </strong><a href=\"/notes/2354516\" target=\"_blank\">2354516</a> to enable client copy after installation.</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Serach/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Equivalent Support Packages<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>SAP BW 7.50 (SAP_BW)</td>\n<td>BW/4HANA 1.00(DW4CORE)</td>\n<td>BPC 11 (BPC4HANA)</td>\n<td>SAP UI 7.52 </td>\n</tr>\n<tr>\n<td>               4</td>\n<td>        Initial Installation</td>\n<td>           N.A.</td>\n<td> </td>\n</tr>\n<tr>\n<td>               5</td>\n<td>                   1</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               6</td>\n<td>                   &gt;= 2</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               7</td>\n<td>                   &gt;= 4</td>\n<td>  Initial Installation</td>\n<td> </td>\n</tr>\n<tr>\n<td>               8</td>\n<td>                   &gt;= 5</td>\n<td>             &gt;= 1</td>\n<td> </td>\n</tr>\n<tr>\n<td>               9</td>\n<td>                   &gt;= 6</td>\n<td>             &gt;= 2             </td>\n<td> </td>\n</tr>\n<tr>\n<td>             10</td>\n<td>                   &gt;= 7</td>\n<td>             &gt;= 3</td>\n<td> </td>\n</tr>\n<tr>\n<td>             11*</td>\n<td>                   &gt;=8</td>\n<td>             &gt;=4</td>\n<td>      &gt;=2</td>\n</tr>\n<tr>\n<td>             12*</td>\n<td>                   &gt;=9</td>\n<td>             &gt;=5</td>\n<td>      &gt;=3</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n</ul>\n<p><span>*) Starting with BW/4HANA 1.00 SP08 SAP_UI 7.52 is required</span></p>\n<ul>\n<li><span>BW/4HANA SP08</span></li>\n<ul>\n<li><strong>Requirements:</strong></li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP10</li>\n<li>SAP_UI <strong>7.52 SP01 -- update required</strong></li>\n</ul>\n<li><strong>For new Installs:</strong></li>\n<ul>\n<li>For your convience we created a Support Release for BW/4HANA 1.00 including the following components:</li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP 10</li>\n<li>SAP_UI 7.52 SP02</li>\n<li>DW4CORE SP08</li>\n<li>BW4CONTB SP03</li>\n</ul>\n<li>this installation is based on Software Provisioning Manager 2.0 which requires HANA 2 SP2 Revision 23 see note 2610954.</li>\n<li>Release of the installation is planned for april (CW 17)</li>\n</ul>\n<li><strong>Enhancment in Tasklist</strong></li>\n<ul>\n<li>there are some enhancements made in the tasklists with regards to the new Web UI's: Please refert to note 2351381 for details.</li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP03</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n</ul>\n<li><span>BW/4HANA SP02</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n<li>Please implement the following notes on top of SP02 to avoid issues during setup/tasklisk execution</li>\n<ul>\n<li>2410466                BW Workspace customizing: RSDDTREXADMIN_TO_RSWSPCUST dumps </li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP01</span></li>\n<ul>\n<li>Please implement the following notes on top of SP01 to avoid issues during setup/later SP update</li>\n<ul>\n<li>2385382               Add SAPFSPOR to avoid strange DUMPS during SP update</li>\n<li>2385265               Check if essential objects are active after installation</li>\n<li>2381312               Task list SAP_BW4_SETUP_SIMPLE: Error in \"Check/Generate Packages for BW ODATA Services\" step</li>\n</ul>\n<li>General Information about tasklists and their usage in BW/4HANA: 2351381 SAP BW/4HANA task lists</li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA SP00</span></li>\n<ul>\n<li>If job BI_WRITE_PROT_TO_APPLLOG is not started by the tasklist please see SAP Note <a href=\"/notes/2356498\" target=\"_blank\">2356498</a>.</li>\n<li>In SP0 the tasklist shows an error which can be ignored. See SAP Note <a href=\"/notes/2351344\" target=\"_blank\">2351344</a> for details.</li>\n</ul>\n</ul>", "noteVersion": 36}, {"note": "2383530", "noteTitle": "2383530 - Conversion from SAP BW to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the different paths to get from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP BW/4HANA, SAP BW, Migration, Conversion, Transfer, In-Place, Remote, Shell, Lifecycle Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>0 Disclaimer</strong></span></p>\n<p>Converting a SAP BW system to SAP BW/4HANA is not a simple task. There is no \"wizard\" that magically converts everything. Instead, SAP provides a well-defined process to guide you through the renovation of your data warehouse. We have developed tools to automate this renovation where applicable and feasible, but they are not built or meant to fix badly designed models or clean-up neglected systems. In any conversion there is a need for manual interaction and re-design. The amount of such manual tasks varies from customer to customer and depends on the configuration and state of the SAP BW system. For example, a conversion of newer systems that have been configured using SAP HANA-optimized objects and LSA++ from the beginning will be relatively easy. In contrast to that, a conversion of older systems that are running on non-SAP HANA databases, using out-of-date interfaces, legacy data models, and multiple redundant layers can become quite challenging.</p>\n<p>It is therefore essential that you understand the differences between SAP BW and SAP BW/4HANA and how to handle them, thoroughly analyze your existing system, estimate the complexity and duration of required tasks, properly plan all conversion activities, learn and practice to use the conversion tools, test the conversion processes ideally with a \"copy of production\", and actively manage changes to your business. It speaks for itself that good project management and skilled personnel will be required to execute a timely and efficient conversion of a complete SAP BW system. If done right, your conversion to SAP BW/4HANA will be very successful.</p>\n<p>SAP offers several way to help. To get to started run a <a href=\"https://help.sap.com/viewer/p/SAP_READINESS_CHECK\" target=\"_blank\">SAP Readiness Check</a> or book a <a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">SAP Value Assurance service </a><a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">for SAP BW/4HANA</a>.</p>\n<p>We also highly recommend the Class Room Training <strong>BW4HC</strong>. (<a href=\"https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/\" target=\"_blank\">https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/</a>?).</p>\n<p> </p>\n<p><span><strong>1 Overview</strong></span></p>\n<p><strong>1.1 What is SAP BW/4HANA</strong></p>\n<p>SAP BW/4HANA is a new, next generation data warehouse product from SAP that, like SAP S/4HANA, is optimized for the SAP HANA platform, including inheriting the high performance, simplicity, and agility of SAP HANA. SAP BW/4HANA delivers real time, enterprise wide analytics that minimize the movement of data and can connect all the data in an organization into a single, logical view, including new data types and sources.<strong><br/></strong></p>\n<p><strong>1.2 Related Information</strong></p>\n<p>Please find more information here:</p>\n<ul>\n<li>The Future of Data Warehousing - SAP BW/4HANA  <br/><a href=\"http://sap.com/bw4hana\" target=\"_blank\">http://sap.com/bw4hana</a></li>\n<li>SAP BW/4HANA Community (includes FAQ)<br/><a href=\"https://community.sap.com/topics/bw4-hana\" target=\"_blank\">https://community.sap.com/topics/bw4-hana</a><br/><a href=\"https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html</a></li>\n<li>Online Documentation<br/><a href=\"http://help.sap.com/bw4hana10\" target=\"_blank\">http://help.sap.com/bw4hana10</a></li>\n<li>Product Roadmap<br/><a href=\"https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021\" target=\"_blank\">https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021</a></li>\n<li>SAP BW/4HANA vs. SAP BW<br/><a href=\"https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html</a><br/><br/></li>\n</ul>\n<p>For information related to functionality available in SAP BW but replaced or deleted in SAP BW/4HANA please refer to the Simplification List: <br/><a href=\"https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf\" target=\"_blank\">https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 1.0: <br/><a href=\"https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2.0:</p>\n<p><a href=\"https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2021 and higher:</p>\n<p><a href=\"https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf</a></p>\n<p> </p>\n<p><span><strong>2 How to Get to BW/4HANA</strong></span></p>\n<p><strong>2.1 Paths to SAP BW/4 HANA</strong></p>\n<p><strong><em>2.1.1 New implementation or fresh start</em></strong></p>\n<p>New implementations are the best choice for customers converting from a legacy system or building a system from scratch with new data model only.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, selective transport of BW objects (optional), implement HANA-optimized data models and flows, followed by data load and quality checks.</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), system connection (SAP Source), SAP BW/4HANA ETL, file upload and/or SAP HANA EIM (legacy sources)</p>\n<p>See SAP First Guidance: Complete functional scope (CFS) for SAP BW/4HANA: <a href=\"https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html<br/></a></p>\n<p><em><strong>2.1.2 System conversion</strong></em></p>\n<p>A system conversion addresses customers who want to change their current SAP BW system into a SAP BW/4HANA system. Using the Transfer Cockpit provided by SAP, the logical systemname of the system can be kept (in-place conversion) or a new  logical systemname can be used (remote conversion resp. shell conversion).</p>\n<p>A conversion project typically follows this sequence:</p>\n<ul>\n<li><strong>Discover / Prepare Phase</strong>: check system for BW/4HANA compliance (gather information about objects and code that needs to be transferred or changed), estimate effort for the conversion project</li>\n<li><strong>Explore / Realization Phase</strong>: Transfer legacy objects into HANA-optimized counterparts, system conversion, post conversion tasks</li>\n</ul>\n<p><em>*******. In-Place conversion</em></p>\n<p>Systems running on SAP BW 7.50 powered by SAP HANA can be converted in-place keeping their logical systemname. In the realization phase of the conversion project, classic objects have to be transferred into their HANA optimized replacements using the Transfer Cockpit. This transfer can be performed scenario-by-scenario. When all classic objects have been replaced, the system conversion to SAP BW/4HANA can be triggered.</p>\n<p>SAP highly recommends to use the latest support package for SAP BW/4HANA when performing the conversion. See note https://launchpad.support.sap.com/#/notes/2347382 regarding the equivalent support packages. Please keep the RTC Dates of the different Support Packages in mind when you plan your conversion project.</p>\n<p><strong>Steps</strong>: Migrate and/or upgrade to SAP BW 7.50 powered by SAP HANA (if necessary), install SAP BW/4HANA Starter Add-On (see “Installation” below), install Transfer Cockpit, transfer data models, adjust custom coding, perform system conversion</p>\n<p>To include the Data Comparison Functionality into InPlace-TaskList, please refer to the SAP Note <a href=\"/notes/2607742\" target=\"_blank\">2607742</a>.</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-IPL</p>\n<p><strong>What to use</strong>: SAP BW/4HANA Starter Add-On, SAP BW/4HANA Transfer Cockpit, SPAM/SAINT<br/><em></em></p>\n<p><em></em><em>*******. Remote conversion</em></p>\n<p>For SAP BW systems on releases from 7.30 to 7.50 running on Any-DB, a remote conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models into the new installation and to perform a remote data transfer.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, install DMIS Add-On, transfer data models, transport custom developments (might need adjustment to work with SAP BW/4HANA)</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-RMT</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit, DMIS Add-On</p>\n<p><em>*******. Shell conversion</em></p>\n<p>For SAP BW systems on releases from 7.00 to 7.50 running on Any-DB, a Shell conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models without data into the new installation.</p>\n<p>Our recommendation for shell conversion is to use a sandbox as the sender system. The latest SPs should also be installed on this system. Depending on the version of the current system, this saves a lot of time and effort. If you still decide to import the hints, you should look at the wiki <a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\" target=\"_blank\" title=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718</a> beforehand and follow the information from the wiki when importing.</p>\n<p><em><strong>Steps</strong>: </em>Install SAP BW/4HANA, transfer data models without data using SAP BW/4HANA Transfer Cockpit, and transport custom developments (might need adjustments to work with SAP BW/4HANA).</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-SHL</p>\n<p><strong>What to use: </strong>Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit</p>\n<p><strong><em>2.1.3. Landscape transformation</em></strong></p>\n<p>Landscape transformation is for customers who want to consolidate and optimize their complex SAP BW landscape (multiple production systems) into a single SAP BW/4HANA system or who want to carve-out selected data models or flows into a global SAP BW/4HANA system.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, implement consolidated and HANA-optimized data models and flows, followed by data load and quality checks</p>\n<p><strong>What to use</strong>: Selective data transfer, SAP Landscape Transformation (SLT), SAP Data Services, SAP BW/4HANA ETL or SAP HANA EIM<br/><em></em></p>\n<p>The following sections are focusing on the system conversion.</p>\n<p> </p>\n<p><strong>2.2 Availability</strong></p>\n<p><strong><em>2.2.1 Overview</em></strong></p>\n<p>The tools required to perform an In-Place, Remote or Shell conversion are general available now.</p>\n<p><strong><em>2.2.2 Releases</em></strong></p>\n<p>SAP highly recommends to use the latest support package for a release to start a conversion project with. It will massively reduce the effort for notes implementation and we cannot guarantee that all required notes can be imported from other areas.<em> </em>Nevertheless, the Transfer Cockpit was made available via SAP Notes for the following support packages.</p>\n<p><em>******* Pre-Checks (General Available)</em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 25 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 07 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td>SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>******* In-Place conversion</em></p>\n<p>SAP BW 7.50<strong> minimal </strong>requirement SP 05 ,<strong> recommended </strong>SP 12 and higher.</p>\n<p>An in-place conversion of SAP BW <strong>7.51</strong> or higher to SAP BW/4HANA 1.0 is <strong>not</strong> possible (since SAP BW/4HANA 1.0 is based on SAP Basis <strong>7.50</strong>).</p>\n<p>Starting from Release SAP BW <strong>7.50 SP16</strong> the conversion is only to SAP BW/4HANA <strong>2.0 SP&gt;=04</strong> possible!</p>\n<p>SAP highly recommends to update the systems before starting the conversion process. Support Package Implementation is possible until B4H Mode.</p>\n<p>Other components relevant for SAP BW Systems used for InPlace Conversion</p>\n<ul>\n<li>SAP HANA 1.0 SPS 12 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong>)</li>\n<li>SAP HANA 2.0 SPS 36 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>)</li>\n<li>SAP Business Content 7.57 SP11 and higher</li>\n<li>SPAM version 72 and higher</li>\n<li>SAP UI 7.52 or lower (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong> . SAP BW/4HANA 1.0 does <strong>not</strong> support SAP_UI 7.53. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n<li>SAP UI 7.53 (Possible only by Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n</ul>\n<p> </p>\n<p><em>*******. Remote conversion </em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong>Exceptional usage with increased manual implementation effort</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* Shell conversion</em></em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release </strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong> <strong>Exceptional usage with increased manual implementation effort</strong></strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 28 and higher</td>\n<td>SP 25</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 10 and higher</td>\n<td>SP 07</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* SAP BW/4HANA required release and support package stack</em></em></p>\n<p><em><em></em></em>SAP BW/4HANA General Information see SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a></p>\n<p>SAP BW/4HANA <strong>1.0</strong></p>\n<p>For In-Place / Shell:<strong> minimal</strong> requirement SP 08, <strong>recommended</strong> - latest released SP!</p>\n<p>For Remote: <strong>minimal</strong> requirement SP 09, <strong>recommended</strong> - latest released SP!</p>\n<p>SAP BW/4HANA <strong>2.0</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 02 (released)</p>\n<p>For more information regading SAP BW/4HANA 2.0 Release Restictions see SAP Note <a href=\"/notes/2733740\" target=\"_blank\">2733740</a>.</p>\n<p>SAP BW/4HANA <strong>2021</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2021 Release Restictions see SAP Note <a href=\"/notes/3100794\" target=\"_blank\">3100794</a>.<a href=\"/notes/2733740\" target=\"_blank\"><br/></a></p>\n<p>SAP BW/4HANA <strong>2023</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2023 Release Restictions see SAP Note <a href=\"/notes/3365187\" target=\"_blank\">3365187</a>.</p>\n<p>For Conversion from SAP_BW 7.50 for HANA to SAP BW/4HANA 2023 (SAP_BASIS 7.58) please check the downloaded Upgrade Export according to SAP Note <a href=\"https://me.sap.com/notes/3454844\" target=\"_blank\">3454844</a>.</p>\n<p><span><em>2.2.2.6 Add-On's</em></span></p>\n<p><span>Please see SAP Note <a href=\"/notes/2189708\" target=\"_blank\">2189708</a> regarding usage of Add-Ons in </span><span>SAP BW/4HANA.</span></p>\n<p> </p>\n<p><em></em><em><strong>2.2.3 Installation</strong></em></p>\n<p><em>2.2.3.1 SAP BW/4HANA Starter Add-On</em></p>\n<p>For the In-Place Conversion, the installation of the SAP BW/4HANA Starter Add-on is required. Please follow the instruction of the Conversion Guide.</p>\n<p>For Shell or Remote Conversion, the SAP BW/4HANA Starter Add-On is not required.</p>\n<p><em>2.2.3.2 SAP BW/4HANA Transfer Cockpit</em></p>\n<p>To install the Transfer Cockpit for a transfer of scenarios (in-place, remote, and shell conversion), a set of SAP Notes needs to be applied to the corresponding systems. Please use the SAP BW Note Analyzer to analyze the system and to install the prerequisites. Create the SAP BW Note Analyzer program in each system for which notes have to be applied. Depending on your use case, the following systems needs to be processed:</p>\n<ul>\n<li><strong>SAP BW</strong> (in-place conversion or sending system for a remote/shell conversion)</li>\n<li><strong>Source systems</strong> connected to SAP BW (in-place and remote/shell conversion)</li>\n<li><strong>SAP BW/4HANA</strong> (post conversoin for in-place; receiving system for a remote/shell conversion)</li>\n</ul>\n<p>Run the SAP BW Note Analyzer in each system with the corresponding XML file.</p>\n<p><em>2.2.3.3 SAP BW Note Analyzer</em></p>\n<p>For a detailed documentation of the SAP BW Note Analyzer, please see <a href=\"https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf\" target=\"_blank\">https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf</a></p>\n<p>Attached to that note you’ll find a text file “Z_SAP_BW_NOTE_ANALYZER.txt”. Save the file on your computer and open it in an editor.</p>\n<p>In the corresponding system, use transaction SE38 to create a report called “Z_SAP_BW_NOTE_ANALYZER”. Copy and paste the content of the above-mentioned text file into the report and activate it.</p>\n<p>Attached to that note, you’ll find several XML files. Save those files that are related to your use-case:</p>\n<p>Please find more detailed informations under:</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874</a>.</p>\n<p>You want to run the pre-checks in the discovery phase (RS_B4HANA_CONVERSION_CONTROL in SAP BW 7.0x and RS_B4HANA_RC from SAP BW 7.30 to 7.50):</p>\n<ul>\n<li>For SAP BW 7.0x, use <em>SAP_BW4HANA_<strong>Pre_Checks</strong>_[last_update].xml</em></li>\n<li>For SAP BW from 7.30 to 7.50, use XML<em> SAP_BW4HANA_<strong>Readiness_Check</strong>_<em>[last_update]</em>.xml</em> from note 2575059. </li>\n</ul>\n<p>You want to transfer scenarios and perform a system conversion in the realization phase:</p>\n<ul>\n<li>For SAP BW 7.50 (all cases),</li>\n<ul>\n<li>if you start now with the installation of the tool, use <em>SAP_BW4HANA_Conversion_<strong>SAP_BW_750</strong>_[last_update].xml </em></li>\n<li>if you have already installed most of the notes before and only want the latest updates, continue to use <em>SAP_BW4HANA_<strong>In-place_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>For all systems connected to SAP BW as a data load source system, use <em><strong>Source_System</strong>_for_SAP_BW4HANA_[last_update].xml</em></li>\n<li>Remote Transfer</li>\n<ul>\n<li>For SAP BW from 7.30 to 7.40 acting as sending system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>Shell Transfer</li>\n<ul>\n<li>For SAP BW from 7.00 to 7.40 acting as sending system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n</ul>\n<p>You have successfully converted the system (In-Place) to SAP BW/4HANA (see section 2.3.2.3 Post-Conversion phase)</p>\n<ul>\n<li>Use <em>SAP_BW4HANA_<strong>In-place_Conversion_(After_System_Conversion)</strong>_[last_update].xml</em></li>\n</ul>\n<p>Execute the SAP BW Note Analyzer in each relevant system. Click on the icon “Load XML file” and chose the system specific XML file.</p>\n<p>Select radio button “Check implementation state against information in XML file” and check checkbox “Download needed SAP Notes”. Select radio button “In background”.</p>\n<p>Run the report. A background process will be started which downloads the required notes.</p>\n<p>When the process finished successfully, execute the report again with a de-selected checkbox “Download needed SAP Notes”.</p>\n<p>A list of notes with its implementation status will be shown. You can now install the missing notes by clicking on the corresponding icon in the list.</p>\n<p><em>2.2.3.4 Transport-Enabled Correction Instructions (TCIs)</em></p>\n<p>When installing the SAP BW/4HANA Transfer Cockpit using the SAP BW Note Analyzer, you will be prompted for SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>, <a href=\"/notes/2371120\" target=\"_blank\">2371120</a>, and <a href=\"/notes/2735300\" target=\"_blank\">2735300</a> to install a TCI.</p>\n<p>For more information about TCI, see SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>.<a href=\"/notes/2358953\" target=\"_blank\"><br/></a></p>\n<p><em>******* DMIS Add-On</em></p>\n<p>For a remote conversion, the DMIS Add-On needs to be installed in SAP BW (sending system) and SAP BW/4HANA (receiving system). Follow the instructions in SAP Note <a href=\"/notes/2513088\" target=\"_blank\">2513088</a>.</p>\n<p> </p>\n<p><strong>2.3 Conversion Process</strong></p>\n<p>For a detailed description of the conversion process, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide</a>. In additional we attached a excel file, \"<em><strong>SAP BW4HANA Conversion - Steps to consider.xlsx</strong></em>\", as a kind of check list/project plan.</p>\n<p>List of required authorization objects for conversion, see attachment \"<em><strong>Authorization_Objects_List_Conversion_2383530.xls</strong></em>\".</p>\n<p>Frequencly ask questions are collected in note <a href=\"/notes/2930058\" target=\"_blank\">2930058</a>.</p>\n<p>Each object can only be converted once. However, it can be collected again if it is used in another scenario. For example, an IOBJ that has been converted can be used in different objects such as ADSO, DSO, CUBE. Due to consistency, these IOBJs are also collected several times, but the metadata can only be transferred once.</p>\n<p><strong>2.4 New Features</strong></p>\n<p>Under link, you can find new developed features.</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910</a>.</p>\n<p>The wiki is updated regularly.</p>", "noteVersion": 420}]}]}