{"guid": "00109B131AF41ED9A1D7F1A6C64340DB", "sitemId": "SI065: CRM", "sitemTitle": "Account Identification changed", "note": 2840198, "noteTitle": "2840198 - Account identification for utilities changed", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span #333333;=\"\" 'times=\"\" 107%;=\"\" arial',sans-serif;=\"\" color:=\"\" en-us;=\"\" he;\"=\"\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\" zh-cn;=\"\">You are considering or planning a migration of a CRM 7.0 or CRM 7.0 EhP installation to S/4HANA for customer management. The following information on changed or deprecated functionality is relevant in this case.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRM, Account Identification</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In S/4HANA for customer management, it is not possible to create a new business partner from the \"maintain relationship\" view.</p>\n<p>The object \"partner function\" is no longer used for alternative payer, dunning recipient, payee, correspondence recipient and bill-to party. It has been substituted by the \"Relationship\". The logic remains the same.</p>\n<p>Moreover the HANA free text search has been redesigned in S/4HANA Utilities for customer management: there is no separate free text search in S/4HANA Utilities for customer management, the new S/4HANA free text search has been incorporated into the standard identification view for utilities.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You need to create the new business partner using the standard \"Create BP\" button before creating the business partner relationship.</p>\n<p>Use the new S/4HANA free text search incorporated into the standard identification view.</p>", "noteVersion": 6, "refer_note": []}