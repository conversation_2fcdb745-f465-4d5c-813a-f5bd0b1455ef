{"guid": "0050569455E21ED5B3E17678391A609E", "sitemId": "SI8 Logistics_PS", "sitemTitle": "S4TWL - Claim Management", "note": 2267169, "noteTitle": "2267169 - S4TWL - Claim Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP to SAP S/4HANA or an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this note. The following SAP S/4HANA Simplification Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Compatibility Scope, System Conversion, Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Claim Management is part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, Claim Management can be found under the ID 467.</p>\n<p>Please note that hence also the factsheet/object page Project Claim is not available with SAP S/4HANA (see note 2267283).</p>\n<p><strong>Business Process related information</strong></p>\n<p>The following transactions are part of the compatibility scope:</p>\n<ul>\n<li>CLM1             Create Claim</li>\n<li>CLM10           Claim Overview</li>\n<li>CLM11           Claim Hierarchy</li>\n<li>CLM2             Change Claim</li>\n<li>CLM3             Display Claim</li>\n</ul>\n<p>Claims Management has been modernized and with this modernization the above mentioned transactions can be used as part of the perpetual scope as of SAP S/4HANA 2022. With this modernization an API for reading claim data and status as well as the Fiori app Project Claim Overview (F6497) are available.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Upgrade to SAP S/4HANA 2022 release or higher before expiry date of the compatibility scope.</p>", "noteVersion": 5, "refer_note": [{"note": "2267283", "noteTitle": "2267283 - S4TWL - Project Claim Factsheet", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA 1511 the Project Claim factsheet will not be available anymore. As of SAP S/4HANA 2022 the Fiori app Project Claim Overview (F6497) and the related Project Claim object page is available as a alternative (see note 2267169).</p>\n<p><strong>Business Process related information</strong></p>\n<p>No influence on business processes expected</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ul>\n<li>Use Fiori App for Project Claim (SAP S/4HANA 2022 or higher)</li>\n<li>if applicable also usage of Project Issue and Change Management functionality can be considered  for appropriate business requirement.</li>\n<li>Knowledge Transfer to key- and end-user</li>\n</ul>", "noteVersion": 4, "refer_note": [{"note": "2267169", "noteTitle": "2267169 - S4TWL - Claim Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP to SAP S/4HANA or an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this note. The following SAP S/4HANA Simplification Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Compatibility Scope, System Conversion, Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Claim Management is part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, Claim Management can be found under the ID 467.</p>\n<p>Please note that hence also the factsheet/object page Project Claim is not available with SAP S/4HANA (see note 2267283).</p>\n<p><strong>Business Process related information</strong></p>\n<p>The following transactions are part of the compatibility scope:</p>\n<ul>\n<li>CLM1             Create Claim</li>\n<li>CLM10           Claim Overview</li>\n<li>CLM11           Claim Hierarchy</li>\n<li>CLM2             Change Claim</li>\n<li>CLM3             Display Claim</li>\n</ul>\n<p>Claims Management has been modernized and with this modernization the above mentioned transactions can be used as part of the perpetual scope as of SAP S/4HANA 2022. With this modernization an API for reading claim data and status as well as the Fiori app Project Claim Overview (F6497) are available.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Upgrade to SAP S/4HANA 2022 release or higher before expiry date of the compatibility scope.</p>", "noteVersion": 5}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Data migration", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}]}