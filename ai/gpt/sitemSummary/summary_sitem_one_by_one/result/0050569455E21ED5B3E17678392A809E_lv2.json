{"guid": "0050569455E21ED5B3E17678392A809E", "sitemId": "SI2_IS_DIMP_M", "sitemTitle": "S4TWL - Sales Order Versions", "note": 2270405, "noteTitle": "2270405 - S4TWL - Sales Order Versions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p class=\"bodycopy\">The special Mill Sales Order Versions function is not available in S/4HANA. For more information, see SAP Note 2226678</p>\n<p><strong>Business Process related information</strong></p>\n<p>This sales order versions function cannot be used anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p><span ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" color:=\"\" en;=\"\" lang=\"EN\" minor-fareast;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" simsun;=\"\" zh-cn;=\"\">The simplification item is possibly relevant if the Business Function DIMP_SDUD (Discrete Indus. - Mill Products) is active in your system. You can check its status via transaction SFW_BROWSER (Switch Framework Browser) under object DIMP.</span></p>\n<p>The item is relevant if there are any entries in table <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text108-__clone43\">VSVBUK_CN and <span class=\"sapMText sapMTextBreakWord sapMTextMaxWidth sapUiSelectable\" id=\"__text108-__clone43\">VSVBUP_CN. You can use transaction SE16 to verify.</span></span></p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2226678</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 3, "refer_note": [{"note": "2226678", "noteTitle": "2226678 - S/4 HANA: Deprecation of Mill Specific Sales Order versioning in SD", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using a SAP ERP system and intend to perform the upgrade-like conversion to S/4 HANA.</p>\n<p>The custom code check shows customer objects affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<p>The custom code check refers to note 2226678, which covers deprecation of Mill specific sales order versioning in SD (Sales and Distribution).</p>\n<p>In the transactions VA01, VA02, and VA03 (Sales Order), you would not be able to view the menu paths <em>EDIT -&gt; Additional Functions -&gt; Assign Version Change</em> and <em>EDIT -&gt; Additional Functions -&gt; Version Data -&gt; Transfer / Compare / Copy / Overview</em>.</p>\n<p>The Customizing activities available under <em>Sales and Distribution -&gt; Sales -&gt; Sales Documents -&gt; Sales Order Versions </em>will also be not available.</p>\n<p>The piece list for this functionality is SI_MP_SALES_VERSION.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4_TRANSFORMATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note contains detailed descriptions on how to adopt customer objects to the deprecation of Mill specific sales order versioning in SD.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You must eliminate the indicated usages from your customer objects.</p>", "noteVersion": 1}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "The special Mill Sales Order Versions function is not available in S/4HANA. The related process need to be new designed. see SAP Note 2226678"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "The special Mill Sales Order Versions function is not available in S/4HANA. The related process need to be new designed. see SAP Note 2226678"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "Training to use new process without using Sales Order Version"}]}