{"guid": "6CAE8B3EABFB1ED6B6B13B1C2D2020C3", "sitemId": "SI10: Logistics_MM-IM", "sitemTitle": "S4TWL - FMP in S4HANA", "note": 2414624, "noteTitle": "2414624 - S4TWL - Flexible Material Prices (FMP) in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>If you have activated the Business Function /CWM/FMP of the Industry Solution IS-CWM, the following SAP S/4HANA transition worklist item is applicable.</p>\n<p>Flexible Material Prices (FMP) is not supported in SAP S/4HANA even though objects are technically given in QRT_STAG.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FMP, CWM, IS-CWM, S/4HANA, Simplification List, Flexible Material Prices</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Module FMP was introduced with EHP5 of the Industry Solution IS-CWM to support the customer created price types within the migration to the 'new architecture' of CWM (for more details please see also note 2358928).<br/>In Inventory Accounting as part of the 'old Architecture' all prices were organized in price types. With the migration all existing and important prices were transferred into the standard tables of ERP. But in case a customer had created own price types the related prices had to be stored in FMP since there was no separate data storage in ERP available.<br/>In seldom cases customer used this opportunity and defined own price types. So SAP decided to not support the FMP-module anymore in S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To check the usage of own price types in FMP call transaction SE16, select table '/FMP/D_MP_PRIC' and execute. Press the button 'Number of Entries' and in case the appearing popup shows no entries found you are sure that no own prices were maintained in FMP.</p>\n<p>Please be aware that for S/4HANA:</p>\n<ul>\n<li>FMP-data cannot be migrated</li>\n<li>a call of FMP transactions and reports will cause a dump (Blacklisted objects)</li>\n<li>objects starting with '/FMP/' must not be used in customer coding<br/>(see note 2296016)</li>\n</ul>", "noteVersion": 4, "refer_note": [{"note": "2296016", "noteTitle": "2296016 - SAP S/4HANA custom code adaptation - removal of orphaned objects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As preparation for a system conversion to SAP S/4HANA or a release upgrade to a higher SAP S/4HANA release you are analyzing and adapting your custom code to SAP S/4 HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, custom code adaptation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In the SAP S/4HANA custom code check this SAP note is used as reference for objects which have been deleted in SAP S/4HANA compared to SAP ERP or previous SAP S/4HANA releases and which are not covered by more specific custom code notes or Simplificiation Items. Especially this includes objects which</p>\n<p>- did belong to functionality that was already discontinued in the past, in some cases as far back as in R/3.<br/>- were never delivered as part of any specific functionality but just as individual technical artifacts.<br/>- were left over as orphaned objects in cases where the corresponding functionality and related objects were already removed in the past.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Remove any references to these objects in your custom code before the migration to SAP S/4 HANA.</p>\n<p>Where still required, replace them with equivalent objects in your customer namespace.</p>\n<p>In rare cases some of these these objects may show up twice in the results of your custom code check. Once with a reference to this note and once with a reference to another, more specific custom code note, related to a Simplification Item. In this case please follow the guidance in the more more specific custom code note.</p>\n<p><strong><span>Please note:</span></strong></p>\n<p>Unlike custom code information for other Simplification Items, which is manually maintained and checked, the list of deleted objects related to this note 2296016 is automatically determined.</p>\n<p>Due to the way this is calculated, there can be false positives in this list. Most often this happens if a subobject (e.g. function module, table field) has been moved to another main objects (e.g. function group, table append) and the original main object (e.g. function group, table append) has been deleted. In this case the subobject will also be wrongly shown as deleted, even if it still exists.</p>\n<p>So, <span>only in context of this note 2296016</span>, if ATC brings up a reference to an SAP (sub)object as an error, but the (sub)object still exists on the target release, you can always ignore the ATC error as a false positive.</p>\n<p>It is planned to improve the determination of deleted objects in the future to further reduce the false positives.</p>\n<p>If ATC brings up errors related to table names (e.g. T005) in context of this note 2296016, please pay attention to whether really that table has been deleted or only individual fields of the table (in the before mentioned example of T005 actually only one field LANDGRP_VP has been deleted).</p>\n<p>You can see this either in the detailed description of the ATC error. Or if you go to transaction SYCM, search in the SIDB content for the table in question and in the result ALV show the fields \"SAP internal name\" and \"SAP internal type\". If there are individual fields mentioned in \"SAP internal name\" only these fields have been deleted. And you only need to adapt your code if you are actively using these specific fields. In case you e.g. do a SELECT * on such a table, but later on only use some of the selected fields, the ATC error can also be a false positive, as ATC doesn't always see, which of the selected fields are actually used in your code.</p>", "noteVersion": 4}, {"note": "2358928", "noteTitle": "2358928 - S4TWL - CWM in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>CWM is available in SAP S/4HANA On premise beginning with 1610 FPS01. CWM is not available in earlier releases of SAP S/4HANA even though technical objects are visible.</p>\n<p>Note, that the following features will not be supported by CWM in SAP S/4HANA:</p>\n<ul>\n<li>The 'old architecture' of Industry Solution IS-CWM that was part until SAP enhancement package 3 for SAP ERP 6.0</li>\n<li>The category of the unit of measure for a material with value 'B' = Parallel Unit of Measure (BUoM is Valuation Unit of Measure)</li>\n<li>Flexible Material Prices (FMP) a small application to furthermore support the price types of the ‘old architecture’</li>\n</ul>\n<p>In addition to the mentioned features there are some additional changes which might have impact on customer coding related to CWM (see chapter description).</p>\n<p>A conversion from IS-CWM to CWM in SAP S/4HANA is not available as part of SAP S/4HANA 1610.</p>\n<p>As of SAP S/4HANA 1709, CWM is part of SAP's standard conversion tools.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CWM, IS-CWM, S/4HANA, Simplification List, FMP, Flexible Material Prices, BOR, BAPI, IDOC, ALE, BAdI, Screen Sequence</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>1. No further support of CWM business function /CWM/BF (“old CWM Architecture”) in SAP S/4HANA</p>\n<p>When the CWM-solution is made available in SAP S/4HANA, the ‘old architecture’ of the industry solution IS-CWM (SAP ERP 6.0) will not be supported on SAP S/4HANA anymore.</p>\n<p>The 'new architecture' of IS-CWM was introduced in SAP enhancement package 4 for SAP ERP 6.0. This 'new architecture' is harmonized with the SAP ERP standard and removes the dependencies to components Inventory Accounting (FIN-BAC-INV) and Logistics Inventory Management Engine (LIME). With SAP enhancement package 5 for SAP ERP 6.0, SAP provides migration tools (see note 1450225 - SAP CWM: Data Migration from Old to New Architecture and its attachments).<br/>Please be aware that this migration is a project that requires preparation. SAP strongly recommends to contact the CWM Solution Management before!</p>\n<p>Check if the business functions /CWM/BF or /CWM/CM_1 are active in any client. Use the transaction SFW_BROWSER and expand the subtree for Business Function Set /CWM/BFS. If the business function /CWM/BF is active and you intend to move to SAP S/4HANA you have to do a migration to switch to the ‘new architecture’ of CWM on SAP ERP 6.0.<br/>In case business function /CWM/CM_1 is active no further action is needed, because you are already using the ‘new architecture’.</p>\n<p> </p>\n<p>2. Category of Parallel Unit of Measure</p>\n<p>In ECC 6.0 IS-CWM supports two categories of parallel unit of measure (PUoM). These are maintained in the material master, choosing the additional data tab ’Unit of measure’. The category of the unit of measure for a material with value 'B' (Parallel Unit of Measure (BUoM is Valuation Unit of Measure)) will NOT be supported in SAP S/4HANA.</p>\n<p>CWM does NOT support the unit of measure category 'B' because of the following reasons:</p>\n<ul>\n<li>For CWM it is unnecessary to store quantities in a second unit of measure, such as category 'B', without any depending functionality.</li>\n<li>In SAP S/4HANA the category 'B' is reserved for Extended Warehouse Management (EWM). For EWM the category 'B' will control the usage of a PUoM only for the EWM solution in S4CORE.</li>\n</ul>\n<p>You can check whether you use category 'B' with transaction SE16. Enter table 'MARM' and make sure that field /CWM/TY2TQ is on the selection screen. If done select entry 'B' for this field and execute. In case you get the message 'No table entries found for specified key' no further steps are required. If you find any materials please refer to note 2358909 for further information.</p>\n<p> </p>\n<p>3. Flexible Material Prices (FMP)</p>\n<p>With the removal of the ‘old architecture’ all prices of Inventory Accounting (InvAcc) had to be shifted to standard tables. Especially the last prices for material valuation were transferred into table MBEW. But not all prices could be stored in the particular standard tables.<br/>For compatibility reasons FMP solution was developed to cover these self-ruled price types. With S/4HANA the Flexible Material Prices is not supported.</p>\n<p>You can check whether you use FMP with transaction SE16. Enter table ‘/FMP/D_MP_PRIC’ and check if there are entries available. In case there are no entries you can be sure that this module was not used in the past at all.<br/>For more information, see note: 2414624. See also <a href=\"https://launchpad.support.sap.com/#/sic/itemSet('6CAE8B3EABFB1ED6B6B13B1C2D2020C3','false')\" target=\"_blank\">SI10: Logistics_MM-IM</a>.</p>\n<p>In case of further questions please contact the CWM Solution Management.</p>\n<p> </p>\n<p>4. BOR, BAPI, IDoc and ALE changes</p>\n<p>Business objects (BOR-objects) and its function modules are provided to connect system and solutions.<br/>Following the strategy to bring CWM into S4CORE the decision was taken to omit CWM BOR-objects as well as their respective generated coding. To compensate standard BAPI function modules were CWM-enhanced instead:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><strong>BOR object</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p><strong>BOR method</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p><strong>CWM enhanced Standard BAPI</strong></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>BUS1001006</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>SAVEDATA</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_MATERIAL_SAVEDATA</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>BUS1001006</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>SAVEREPLICA</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_MATERIAL_SAVEREPLICA</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>BUS2015</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_INB_DELIVERY_CONFIRM_DEC</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>BUS2015</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_INB_DELIVERY_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>BUS2015</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>SAVEREPLICA</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_INB_DELIVERY_SAVEREPLICA</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>BUS2017</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>CREATEFROMDATA</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_GOODSMVT_CREATE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_OUTB_DELIVERY_CONFIRM_DEC</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_OUTB_DELIVERY_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>SAVEREPLICA</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_OUTB_DELIVERY_SAVEREPLICA</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"151\">\n<p>SPLITDECENTRAL</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>BAPI_OUTB_DELIVERY_SPLIT_DEC</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>You need to check if any obsolete CWM object (Type definition or Function Module) is used by own customer coding. If any usage location is found the respective coding has to be revised and adjusted.<br/><span 'times=\"\" 107%;=\"\" 13.5pt;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-font-size:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">In case of any question please contact the CWM Solution Management.</span></p>\n<p>The following CWM BOR-objects are not available in SAP S/4HANA:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p><strong>Deleted BOR object</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p><strong>Description</strong></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>Standard material</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>Inbound delivery</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>/CWM/BUS17</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>Goods Movement</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>/CWM/BUS28</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>Material inventory</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>/CWM/BUS42</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>REM Backflush</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>/CWM/BUS70</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>Handling unit</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"159\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"263\">\n<p>Outbound Delivery</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>(Use transaction SWO1 to inspect the Business Objects)</p>\n<p>The following list of dependent objects that are not available can be used to support an investigation of own coding.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p><strong>Obsolete Object Type</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p><strong>Method</strong></p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p><strong>Message Type</strong></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/MATMAS_BAPI</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/MATMAS_MASS_BAPI</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_IBDLV_CONFIRM_DEC</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_IBDLV_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_IBDLV_SAVE_REPLICA</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS17</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CREATEFROMDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/MBGMCR</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_OBDLV_CONFIRM_DEC</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_OBDLV_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_OBDLV_SAVE_REPLICA</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SPLITDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_OBDLV_SPLIT_DECENTRAL</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p><strong>Obsolete Object Type</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p><strong>Method</strong></p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p><strong>IDOC Type</strong></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/MATMAS_BAPI01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/MATMAS_MASS_BAPI01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_IBDLV_CONFIRM_DEC01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_IBDLV_CHANGE01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_IBDLV_SAVE_REPLICA01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS17</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CREATEFROMDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/MBGMCR01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_OBDLV_CONFIRM_DEC01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_OBDLV_CHANGE01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_OBDLV_SAVE_REPLICA01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SPLITDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/SHP_OBDLV_SPLIT_DECENTR01</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p><strong>Obsolete Object Type</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p><strong>Method</strong></p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p><strong>Function module for outbound ALE</strong></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_MATERIAL_SAVEDATA</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_MATERIAL_SAVEREPLICA</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_INB_DELVRY_CONFIRMDEC</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_INB_DELVRY_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_INB_DELVRY_SAVEREPL</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS17</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CREATEFROMDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_GOODSMVT_CREATE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_OUT_DELVRY_CONFIRMDEC</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_OUT_DELVRY_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_OUT_DELVRY_SAVEREPL</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SPLITDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/ALE_OUT_DELVRY_SPLIT_DEC</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p><strong>Obsolete Object Type</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p><strong>Method</strong></p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p><strong>Function module for inbound ALE</strong></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_MATMAS_BAPI</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_MATMAS_MASS_BA</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_SHP_IBDLV_CONF</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_SHP_IBDLV_CHAN</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_SHP_IBDLV_SAVE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS17</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CREATEFROMDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_MBGMCR</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_SHP_OBDLV_CONF</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_SHP_OBDLV_CHAN</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_SHP_OBDLV_SAVE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SPLITDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/IDOC_INPUT_SHP_OBDLV_SPLT</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p><strong>Obsolete Object Type</strong></p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p><strong>Method</strong></p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p><strong>IS-CWM Segment</strong></p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1BP_MBEW</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p> </p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p> </p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1BP_MBEWX</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p> </p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p> </p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1BP_MATRETURN2</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p> </p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p> </p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1BP_MARM</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p> </p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p> </p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1BP_MARMX</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS06</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1BPE1MBEW</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p> </p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p> </p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1BPE1MBEWX</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p> </p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p> </p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1MATMAS_MASS_BAPI</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p> </p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p> </p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1BPE1MARM</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p> </p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p> </p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1BPE1MARMX</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1SHP_IBDLV_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS15</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1SHP_IBDLV_SAVE_REPLI</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/BUS17</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CREATEFROMDATA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p> </p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>CONFIRMDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1SHP_OBDLV_CONFIRM_DE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>DELIVERYCHANGE</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1SHP_OBDLV_CHANGE</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SAVEREPLICA</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1SHP_OBDLV_SAVE_REPLI</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"85\">\n<p>/CWM/LIKP</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"123\">\n<p>SPLITDECENTRAL</p>\n</td>\n<td valign=\"bottom\" width=\"190\">\n<p>/CWM/E1SHP_OBDLV_SPLIT_DECE</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>(Use transaction BDBG to inspect Message types, IDoc types, Segments and Function Modules for ALE.)</p>\n<p> </p>\n<p>5. Screen Sequences CW and CE for Material Master</p>\n<p>Screen sequences 'CW' and 'CE' for Material Master are not delivered as part of CWM in S/4HANA. Although existing sequences can be migrated to S/4HANA they are no longer supported by SAP and should be replaced by standard sequences.</p>\n<p>In IS-CWM special Screen sequences were delivered to integrate required CW information in the material master:</p>\n<ul>\n<li>CW (Catch Weight Management)</li>\n<li>CE (CWM with EWM Views)</li>\n</ul>\n<p>These Screen Sequences CW and CE are not necessary in S/4HANA because the CW related information is integrated in the standard screen sequence '21'.</p>\n<p>In the unlikely case that a customer had defined additional screens these customer entries have to be integrated in the used standard '21'.</p>\n<p>Please use transaction OMT3B to review the definitions.<br/>Please use transaction OMT3E to review the Screen sequence control. <br/>Please use transaction OMT3U to review ‘Organizational levels’.</p>\n<p> </p>\n<p>6. Removal of attributes within CWM</p>\n<p>There are some table attributes which are (or will be) no longer supported in a CWM activated solution.<br/>If one of the objects in the following list is used within customer coding, it has to be adjusted to ensure a correct system behavior.</p>\n<ul>\n<li>Tolerance Group: use field ‘CWQTOLGR’ of table MARA instead of <strong>'/CWM/TOLGR'</strong><br/>CWM and EWM use the same tolerance group values. Different semantic tolerance checks for CWM and EWM have to be harmonized.</li>\n<li>Category/Type of a Unit of Measure: use field ‘TY2TQ’ of table MARM instead of <strong>'/CWM/</strong><strong>TY2TQ'</strong></li>\n<li>In IS-CWM, <strong>KZWSM</strong> of table MARA and KZWSO of table MARM was set to ‘B’ for all CW materials. In S/4HANA this flag will not be set by CWM. For CW material, its content will be set according to the settings of Active Ingredient Management in the same way as for standard materials.</li>\n</ul>\n<p> </p>\n<p>7. Removal of BAdI-Definitions</p>\n<p>The following listed Business Add-In (BAdI) are no longer available in S/4HANA. Any related customer implementation is obsolete and can also be removed.</p>\n<ul>\n<li>/CWM/TOLERANCE_GROUP (Tolerance Group)</li>\n<li>/CWM/DEFAULT_STOCK_O (Conversion of the BUoM or the PUoM to an alternative UoM)</li>\n<li>/CWM/DEFAULT_MIGO (Allows the entry quantity, the parallel entry quantity and the entry units of measure to be proposed for catch weight materials in transaction MIGO)</li>\n</ul>\n<p> </p>\n<p> </p>", "noteVersion": 10, "refer_note": [{"note": "2358909", "noteTitle": "2358909 - CWM in S/4HANA – Handling of materials with category 'B' for the Unit of Measure", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the industry solution IS-CWM.</p>\n<p>The industry solution IS-CWM allows to assign a category 'B' (Parallel Unit of Measure (BUoM is Valuation Unit of Measure)) within the material master.<br/>In SAP S/4HANA the category 'B' is <strong>not supported by CWM</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CWM, IS-CWM, S/4HANA, Category B, TY2TQ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>CWM does not support the unit of measure category 'B' because of the following reasons:</p>\n<ul>\n<li>For CWM it is unnecessary to store quantities in a second unit of measure, such as category 'B', without any depending functionality.</li>\n<li>In SAP S/4HANA the category 'B' is reserved for Extended Warehaouse Management (EWM). For EWM the category 'B' will control the usage of a PUoM only for the EWM solution in S4CORE.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP recommends to remove the category 'B' and to convert the material to a standard material. After that change, quantities will be stored only in one unit of measure that is the base unit of measure (BUoM).</p>\n<p><strong>Manual shift to a Standard Material</strong></p>\n<p>In case there are only a few CW materials of category 'B', SAP recommends to shift these CW materials manually in SAP ERP.</p>\n<p>Prerequisites for a manual shift are very similar to those for changing the materials base UoM:</p>\n<ul>\n<li>No open process documents (such as, for instance, production orders, sales orders or purchase orders)</li>\n<li>Stock quantities are zero for both current period and previous period (as detailed by note 30656)</li>\n<li>No alternative unit of measure with category 'C'.</li>\n</ul>\n<p>For a more detailed list, read note 138767.</p>\n<p>If all prerequisites are fulfilled you can remove the category 'B'.</p>\n<p>If this approach is not feasible for you, please create an incident on component IS-CWM refering to this note.</p>\n<p> </p>\n<p><strong>Remark:</strong><br/>In case of further questions please contact the CWM Solution Management.</p>\n<p> </p>", "noteVersion": 4}, {"note": "2671323", "noteTitle": "2671323 - SAP S/4HANA CWM 1809 or successional release: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>You are using SAP S/4HANA 1809 or a subsequent release. This SAP note informs you about restrictions in CWM for these releases.</p>\n<p>NOTE: This SAP note is subject to change.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IS-CWM, CWM, SAP S/4HANA 1809, Release restriction, SAP S/4HANA 1809</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note provides information about the CWM restrictions that exist as of SAP S/4HANA 1809.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"longtext\">\n<p>1) For Fiori Application, we have the following limitations:<br/>Fiori applications have no CWM adjustments and do not support any CW material.</p>\n<p>2) CWM 'Old Architecture' and Unit of measure Type Category 'B':<br/>No support of these functionalities.<br/>For more information, see CWM pre-check notes: 2182725, 2335828, 2335793.</p>\n<p>3) Flexible Material Prices<br/>Objects of the Flexible Material Prices are not supported with SAP S/4HANA.<br/>For more information, see note: 2414624.</p>\n<p>4) Solution specific restrictions:<br/>Almost all restrictions given with the ECC Industry Solution IS-CWM will also apply for CWM in SAP S/4HANA.<br/>Please see <strong>attached document</strong> for details.</p>\n</div>", "noteVersion": 3}, {"note": "2335793", "noteTitle": "2335793 - <PERSON>4TC IS-CWM Master Check for SAP S/4HANA  System Conversion Checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>Pre-Transition Checks for software component IS-CWM have to be executed before the upgrade to SAP S/4HANA.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC<br/>SAP S/4HANA transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note. The checks for software component IS-CWM will be executed during the transition process.</p>", "noteVersion": 7}, {"note": "1450225", "noteTitle": "1450225 - SAP CWM: Data Migration from Old to New Architecture", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Current Situation:<br/>You have activated SAP Catch Weight Management (CWM) with the old architecture (available till EHP3 for SAP ERP 6.0). That means, you are using the components Logistics Inventory Management Engine (LIME) and Inventory Accounting (Fin-BAC-INV) and you have activated the business functions \"/CWM/BF\" Catch Weight Management and possibly \"/CWM/APPL_INTEGRATION_1\" CWM, Innovations in Logistics and Accounting in your system.<br/><br/>Future Situation:<br/>You would like to use SAP CWM with the new CWM architecture (available since EHP4 for SAP ERP 6.0), which means you would like to activate at least one of the following business functions:<br/>/CWM/CM_1<br/>/CWM/CM_2<br/>/CWM/FMP<br/><br/>Without the below mentioned solution this is not possible and you should not try to do that.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>IS-CWM, Catch Weight Management, /CWM/BF, /CWM/APPL_INTEGRATION_1, /CWM/CM_1, /CWM/CM_2, /CWM/FMP, Migration Cookbook</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>As SAP CWM is based on a new architecture as of EHP4, an upgrade from<br/>your EHP level to EHP4 or higher does require a data migration. SAP does not support such a data migration with EHP4. You have to implement at least EHP5 in order to be able to execute the required data migration. Therefore, with an implementation of EHP4 only you are not able to activate the CWM Business Functions provided with EHP4.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>With the installation of EHP5 for SAP ERP 6.0 a set of programs (migration tools) are available (without having to make any additional activation) to enable a straightforward step by step migration to the new architecture. You should note that you can only activate the business functions /CWM/CM_1 (provided with EHP4), /CWM/CM_2 and /CWM/FMP (both provided with EHP5) once the migration is successful.<br/><br/>The usage of these programs requires the definition of a data migration<br/>project in order to ensure a sufficient planning and preparation of your data migration. In order to support you with the planning and preparation of your individual data migration project, SAP has created a Migration Cookbook which you can access as an attachment of this note.<br/><br/>Additionally we highly recommend to get in contact with SAP Solution Management for Catch Weight Management at an early stage.<br/></p></div>", "noteVersion": 7}, {"note": "2335828", "noteTitle": "2335828 - Pre-Transition Checks for software component IS-CWM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>Pre-Transition Checks for software component IS-CWM </span></p>\n<ul>\n<li><span><span>Check Sub ID: IS-CWM_OLD_ARCHITECTURE</span></span></li>\n<li><span><span><span><span>Check Sub ID: </span></span>IS-CWM_B_MATERIALS</span></span></li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><span>Check Sub ID: IS-CWM_OLD_ARCHITECTURE</span></span></p>\n<p><span><span>The old architecture of IS-CWM is not supported in SAP S/4HANA.</span></span></p>\n<p><span><span>A migration project path is given via note 1399197 - Important information about activating SAP CWM with EHP5.  For more information, see Note 1450225.</span></span></p>\n<p><span><span><span><span>Check Sub ID: </span></span>IS-CWM_B_MATERIALS</span></span></p>\n<p><span><span><span><span> You use a material for which a parallel unit of measure with category 'B' is defined in the master record. </span></span></span></span><span><span><span><span>This type of materials is not supported in SAP S/4HANA.</span></span></span></span></p>\n<p> </p>", "noteVersion": 2}, {"note": "2478683", "noteTitle": "2478683 - SAP S/4HANA CWM 1709: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>You are using SAP S/4HANA 1709. This SAP note informs you about restrictions in CWM for this release.</p>\n<p>NOTE: This SAP note is subject to change.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IS-CWM, CWM, SAP S/4HANA 1709, Release restriction</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note provides information about the CWM restrictions that exist as of SAP S/4HANA 1709.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>1) For Fiori Application, we have the following limitations:<br/>Fiori applications have no CWM adjustments and do not support any CW material.</p>\n<p>2) CWM 'Old Architecture' and Unit of measure Type Category 'B':<br/>No support of these functionalities.<br/>For more information, see CWM pre-check notes: 2182725, 2335828, 2335793.</p>\n<p>3) Flexible Material Prices<br/>Objects of the Flexible Material Prices are not supported with SAP S/4HANA.<br/>For more information, see note: 2414624.</p>\n<p>4) Solution specific restrictions:<br/>Almost all restrictions given with the ECC Industry Solution IS-CWM will also apply for CWM in SAP S/4HANA.<br/>Please see <strong>attached document</strong> for details.</p>", "noteVersion": 5}, {"note": "2357827", "noteTitle": "2357827 - SAP S/4HANA CWM 1610: Restriction Note", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using SAP S/4HANA 1610 FPS01. This SAP note informs you about restrictions in CWM for this release.</p>\n<p>NOTE: This SAP note is subject to change.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IS-CWM, CWM, SAP S/4HANA 1610, Release restriction</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note provides information about the CWM restrictions that exist as of SAP S/4HANA 1610 FPS01.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>1) For Fiori Application, we have the following limitations:<br/>Fiori applications have no CWM adjustments and do not support any CW material.</p>\n<p>2) Conversion / Transition:<br/>A conversion of CWM specific data is not supported.</p>\n<p>3) CWM 'Old Architecture' and Unit of measure Type Category 'B':<br/>No support of these functionalities.<br/>For more information, see CWM pre-check notes: 2182725, 2335828, 2335793.</p>\n<p>4) Flexible Material Prices<br/>Objects of the Flexible Material Prices are not supported with SAP S/4HANA.<br/>For more information, see note: 2414624.</p>\n<p>5) Solution specific restrictions:<br/>Almost all restrictions given with the ECC Industry Solution IS-CWM will also apply for CWM in SAP S/4HANA.<br/>Please see <strong>attached document</strong> for details.</p>\n<p> </p>", "noteVersion": 8}, {"note": "2414624", "noteTitle": "2414624 - S4TWL - Flexible Material Prices (FMP) in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>If you have activated the Business Function /CWM/FMP of the Industry Solution IS-CWM, the following SAP S/4HANA transition worklist item is applicable.</p>\n<p>Flexible Material Prices (FMP) is not supported in SAP S/4HANA even though objects are technically given in QRT_STAG.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FMP, CWM, IS-CWM, S/4HANA, Simplification List, Flexible Material Prices</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Module FMP was introduced with EHP5 of the Industry Solution IS-CWM to support the customer created price types within the migration to the 'new architecture' of CWM (for more details please see also note 2358928).<br/>In Inventory Accounting as part of the 'old Architecture' all prices were organized in price types. With the migration all existing and important prices were transferred into the standard tables of ERP. But in case a customer had created own price types the related prices had to be stored in FMP since there was no separate data storage in ERP available.<br/>In seldom cases customer used this opportunity and defined own price types. So SAP decided to not support the FMP-module anymore in S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To check the usage of own price types in FMP call transaction SE16, select table '/FMP/D_MP_PRIC' and execute. Press the button 'Number of Entries' and in case the appearing popup shows no entries found you are sure that no own prices were maintained in FMP.</p>\n<p>Please be aware that for S/4HANA:</p>\n<ul>\n<li>FMP-data cannot be migrated</li>\n<li>a call of FMP transactions and reports will cause a dump (Blacklisted objects)</li>\n<li>objects starting with '/FMP/' must not be used in customer coding<br/>(see note 2296016)</li>\n</ul>", "noteVersion": 4}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "For S/4HANA FMP-data cannot be migrated and a call of FMP transactions and reports will cause a dump (Blacklisted objects), details in SAP Note 2414624"}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "Remove references to obsolete objects from your Custom Code."}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Flexible Material Prices (FMP) is not supported in SAP S/4HANA with no successor functionality"}]}