{"guid": "0090FABF370E1ED894D809EC916B40DC", "sitemId": "SI2: FIN_AA", "sitemTitle": "S4TWL - ASSET ACCOUNTING", "note": 2270388, "noteTitle": "2270388 - S4TWL - Asset Accounting: Parallel valuation and journal entry", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have been using classic Asset Accounting until now and will be using new Asset Accounting in SAP S/4HANA.</p>\n<p>Other scenarios (such as the switch from SAP ERP 6.0, EhP7 with new Asset Accounting to SAP S/4HANA) are <strong>not</strong> considered here.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA; Asset Accounting; parallel valuation; parallel accounting; ledger approach; accounts approach; ACDOCA; post-capitalization; revaluation; retirement;  partial retirement; depreciation; AFAR; AFAB; smoothing method; posting BAPI; fiscal year change, balance carryforward; Legacy data transfer; legacy asset; RAARCH03; scenario 7; scenario 8; ALE transfer; batch input; worklist; impairment; conversion; migration; deleted programs; deleted transactions</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>To be able to use new Asset Accounting in SAP S/4HANA, you have to also use new General Ledger Accounting. If you were using classic General Ledger Accounting before the conversion to SAP S/4HANA, new General Ledger Accounting is activated automatically with the conversion.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<h3 data-toc-skip=\"\"><span>Parallel Valuation and Journal Entry</span></h3>\n<p>In new Asset Accounting you can map parallel valuations with the ledger approach or accounts approach. A mixture of the two is <strong>not</strong> supported (ledger with accounts approach).</p>\n<ul>\n<li>You can record the leading valuation of Asset Accounting in any depreciation area of new Asset Accounting. It is <strong>no</strong> longer necessary to use depreciation area 01 for this. The system now posts both the actual values of the leading valuation and the values of parallel valuation in real time. This means the posting of delta values has been replaced; as a result, the delta depreciation areas are <strong>no</strong> longer required and are <strong>no</strong> longer supported.</li>\n<li>New Asset Accounting makes it possible to post in real time in all valuations (that is, for all accounting principles). You can track the postings of all valuations, without having to take into account the postings of the leading valuation, as was partly the case in classic Asset Accounting.</li>\n</ul>\n<p>Asset Accounting is based on the universal journal entry. This means there is <strong>no</strong> longer any redundant data store.</p>\n<ul>\n<li>Reconciliation postings are <strong>no</strong> longer necessary with closing operations as General Ledger Accounting and Asset Accounting are reconciled permanently anyway due to the universal journal entry.</li>\n<li>The hitherto existing periodic posting run for APC values is redundant.</li>\n<li>Postings for group assets are redundancy-free.</li>\n</ul>\n<p><span>Parallel Valuation in Realtime</span></p>\n<p>For each asset-related transaction (acquisition, capitalization of assets under construction (AUC), retirement, post-capitalization and so on), the asset values for all posting depreciation areas are updated in realtime.</p>\n<h3 data-toc-skip=\"\">Separate Document for each Valuation</h3>\n<p>Different accounting principles or valuations are represented - as in new General Ledger Accounting – in separate ledgers (for the ledger approach) or in a separate set of accounts (for the accounts approach).</p>\n<p>The depreciation areas have equal status. Separate accounting-principle-specific documents are posted for each accounting principle or valuation.</p>\n<p>A business transaction created using integration is split by the system into the operational part and the valuating part. A posting is made in each case against a technical clearing account. For the asset acquisition using integration you require a <em>new technical clearing account for integrated asset acquisition</em>; however, for the asset retirement using integration the existing clearing accounts asset retirement with <em>revenue and asset retirement clearing</em> are used. The operational part is posted across accounting principles, the valuating part is posted accounting-principle-specifically.</p>\n<h3 data-toc-skip=\"\">Entry of Value Differences for Each Depreciation Area or Accounting Principle (Ledger Group).</h3>\n<p>Value differences for each accounting principle or depreciation area can be entered in a separate transaction (AB01L) for an asset acquisition, a post-capitalization, and a manual depreciation or write up. This has the following consequences:</p>\n<ul>\n<li>It is <strong>no</strong> longer necessary (or possible) to restrict the transaction types to individual depreciation areas.</li>\n<li>The user exit APCF_DIFFERENT_AMOUNTS_GET is <strong>no</strong> longer supported.</li>\n</ul>\n<h3 data-toc-skip=\"\">Entering a Business Transaction</h3>\n<p>Using the appropriate fields, you can restrict the posting to an accounting principle and depreciation areas, or limit it to certain areas using the selection of a depreciation area. The following applies when making entries: The last confirmed entry (restriction) applies for the posting. In addition, you can adjust the line items of particular depreciation areas for certain transactions using the Change Line Items function. If you have changed basic values (such as for the accounting principle, depreciation areas or the posting amount), the line items are generated again. A business transaction is always entered in local currency. If you limit the posting to one or more depreciation areas in the same foreign currency, then the entry (and posting) is made in this foreign currency.</p>\n<p>If you select depreciation areas to which differing foreign currencies are assigned and <strong>no</strong> depreciation area was selected in local currency, the system issues an error message; you have to enter several postings for a business transaction like this.</p>\n<h3 data-toc-skip=\"\">Updating Journal Entries</h3>\n<p>The journal entries are updated in Financial Accounting at asset level. The entry also contains the asset number.</p>\n<h3 data-toc-skip=\"\">Asset Balance Changes in Real Time/Immediately in All Valuations</h3>\n<p>In SAP S/4HANA, all changes to asset balances are posted to the general ledger in real time or immediately. Periodic asset balance postings are therefore superfluous and are <strong>no</strong> longer supported. This also applies for the processing of special reserves.</p>\n<p>This affects the Customizing settings for the depreciation area as follows: When posting to the general ledger, all options previously used to post asset balances to the general ledger periodically, <strong>no</strong> longer apply. Instead, one of the following options must be chosen for the affected depreciation area:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"376\">\n<p><strong>Depreciation Area</strong></p>\n</td>\n<td valign=\"top\" width=\"376\">\n<p><strong>Indicator “Posting in GL”</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"376\">\n<p>Area for special reserves (ledger approach and accounts approach)</p>\n</td>\n<td valign=\"top\" width=\"376\">\n<p>4 - Area Posts APC Immediately, Depreciation Periodically</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"376\">\n<p>Area for parallel valuation with the accounts approach</p>\n</td>\n<td valign=\"top\" width=\"376\">\n<p>4 - Area Posts APC Immediately, Depreciation Periodically</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"376\">\n<p>Area for parallel valuation with the ledger approach</p>\n</td>\n<td valign=\"top\" width=\"376\">\n<p>1 - Area Posts in Real Time</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Recommendation for depreciation areas in a foreign currency:<br/> In classic Asset Accounting, you may have a depreciation area in a foreign currency that posts to the general ledger periodically (in other words, in Customizing for the depreciation area, you have selected the option <em>Area Posts APC and Depreciation on Periodic Basis</em>).<br/> However, in new Asset Accounting it is <strong>no </strong>longer possible that a foreign currency area posts to the general ledger.<br/> We recommend that you choose the option <em>0</em> - <em>Area Does <strong>Not </strong>Post </em>for postings to the general ledger. Consequently, this foreign currency area of Asset Accounting is then managed as a statistical area.</p>\n<p>Note about derived depreciation areas for special reserves:<br/>The following applies for both the ledger approach and the accounts approach: For areas for special reserves, you must choose either option <em>4 </em>– <em>Area Posts APC Immediately, Depreciation Periodically </em>or option <em>0</em> – <em>Area Does Not Post</em>.</p>\n<h3 data-toc-skip=\"\">Reconciliation Between Asset Accounting and the General Ledger</h3>\n<p>It is <strong>no</strong> longer necessary to reconcile the general ledger with the subledger of Asset Accounting as from now on Asset Accounting is permanently reconciled with the general ledger automatically. The programs for reconciliation that existed until now (RAABST01, RAABST02 and FAA_GL_RECON) are therefore <strong>no</strong> longer available in SAP S/4HANA.</p>\n<h3 data-toc-skip=\"\">Unilateral Assets</h3>\n<p>Depending on the legal requirements, it may be necessary to manage a fixed asset in one accounting principle (such as local valuation) as an asset, but in a different accounting principle (for example, IFRS) <strong>not</strong> to manage it as an asset, but post the costs directly to profit and loss instead. You can map these differing capitalization rules in new Asset Accounting using 'unilateral' assets.</p>\n<h3 data-toc-skip=\"\">Transaction Types</h3>\n<p>In new Asset Accounting, it is <strong>not</strong> possible and also <strong>not</strong> necessary to restrict transaction types to depreciation areas. This is <strong>not</strong> necessary since, when you enter a transaction, you can restrict it to a depreciation area or accounting principle. In addition, in a posting transaction, you can select the depreciation areas to be posted to. This can significantly reduce the number of transaction types that need to be defined in the system.</p>\n<p>If you nonetheless have migrated transaction types that are restricted to depreciation areas (table TABWA or view cluster V_TABWA), this is rejected by the system.</p>\n<p>Investment support and also revaluation and new valuation are an exception:</p>\n<ul>\n<li>The transaction types for investment support and revaluation are automatically generated by the system when you create a corresponding measure, and therefore are restricted to the depreciation area to be posted to.</li>\n<li>The transaction types for revaluation and new valuation that relate to transaction type group 81/82/89 must continue to be restricted to depreciation areas.</li>\n</ul>\n<p><em><strong>Customizing for Transaction Types:</strong></em></p>\n<p>Customizing for restricting transaction types to depreciation areas is - aside from the above exceptions - now completely superfluous. Therefore the activities for this are <strong>no</strong> longer included in the IMG structure for Asset Accounting.</p>\n<h3 data-toc-skip=\"\">Default Transaction Types for Down Payments</h3>\n<p>Default transaction types for down payments are <strong>no</strong> longer defined in Customizing activity <em>Determine Transaction Types for Internal Transactions</em> (table TABWV) as previously the case. They are now defined in Customizing activity <em>Specify Default Transaction Types</em> (table TABWD).  The system moves existing entries.</p>\n<p>The same applies for the default transaction type Gain/loss on retirement for internal posting of write-ups from gain/loss from asset retirement (the EVL rule).</p>\n<h3 data-toc-skip=\"\">Document Display</h3>\n<p>A business transaction is split (1) into an operational part that creates an accounting-principle-independent or ledger-group-independent document and (2) into a valuating part that creates an accounting-principle-specific/ledger-group-specific document for each valuation. Several documents are posted, therefore. These documents are displayed together in the document display. The system displays the complete integrated asset acquisition in accordance with a valuation that you have selected.</p>\n<p>If a business transaction is <strong>not</strong> entered in Asset Accounting using integration, then only the valuating documents are posted by the entry.  For each valuation that an asset manages, a valuation-specific document is created.</p>\n<p>For each valuation, an accounting-principle-specific document is displayed in the document display. The operational document is <strong>not</strong> linked with the valuating document, and therefore cannot also be displayed.</p>\n<p>This document display in Asset Accounting is available in both document simulation and in the document display.</p>\n<p>You can switch the view between the individual valuations (<em>choose the Change Accounting Principle pushbutton</em>) and between the currency types (<em>choose the Change Display Currency pushbutton</em>).</p>\n<p><em><strong>Standard Layout for Document Display:</strong></em></p>\n<p>There is the following standard layout for the document display in new Asset Accounting: <em>Standard: Asset Accounting (New) (3SAP)</em>.</p>\n<p>We recommend that you use this standard layout or a copy of it adapted to meet your requirements, since the layout supports you in interpreting the accounting-principle-specific documents.</p>\n<h3 data-toc-skip=\"\">Asset Explorer</h3>\n<p>In the navigation area of the Asset Explorer, the depreciation areas of the relevant assignment are displayed grouped according to accounting principles. The accounting principle with the leading ledger appears uppermost.</p>\n<h3 data-toc-skip=\"\"><span>Changed and deleted functions in detail</span></h3>\n<h3 data-toc-skip=\"\">Post-Capitalization</h3>\n<p>You enter post-capitalization both gross and net in the same transaction (transaction ABNAL). The system always calculates the depreciation values, meaning in the standard system it assumes that you are entering gross values.</p>\n<p>If you want to enter the post-capitalization net, you have to specify the net amount in the transaction, and reset the depreciation values in the line items.</p>\n<h3 data-toc-skip=\"\">Revaluation</h3>\n<p>A revaluation is always posted accounting principle-specific. The <em>Accounting Principle</em> field in transaction ABAWL (<em>Revaluation</em>) is therefore a required entry field.</p>\n<p>It is <strong>not</strong> possible to have manual revaluation postings together with automatic revaluation through indexes on the same fixed asset in the same depreciation area.</p>\n<h3 data-toc-skip=\"\">Retirement: Distribute the proportional value percentage rate to prior-year asset acquisitions and current-year acquisitions</h3>\n<p>If you have been using user exit AFAR0004 to date, you have to move your enhancement to the following BAdI method:</p>\n<p>BAdI: FAA_DC_CUSTOMER, method: PROPVAL_PERCENT_ON_SPECIAL_RET.</p>\n<p>You can copy the code from the user exit 1:1 when doing so, since the enhancement interface has not been changed.</p>\n<h3 data-toc-skip=\"\">Retirement: Posting with net book value</h3>\n<p>If up to now you have been using the \"Post Net Book Value at Retirement\" logic, note the following:</p>\n<p>The restriction of posting logic to certain depreciation areas is done in classic Asset Accounting using area types (as set out in SAP Note 1069166). In new Asset Accounting, this setting is replaced by the new Customizing activity <em>Post Net Book Value Instead of Gain/Loss -&gt;</em> Subactivity <em>Specify Depreciation Areas for Net Book Value Posting</em>.</p>\n<h3 data-toc-skip=\"\">Retirement: Posting with special treatment for gain/loss posting</h3>\n<p>If up to now you have been using the \"Special treatment of retirements - types 1, 2 and 4\" logic, note the following:</p>\n<p>The gain/loss or revenue is no longer directly changing the value adjustment line item. There is an additional line item created with a write-up transaction type.</p>\n<h3 data-toc-skip=\"\">Partial retirement</h3>\n<p>With a partial retirement it is no longer possible to adjust the retirement amount manually, for example for cost-accounting areas.</p>\n<p>Instead you must perform an adjustment posting in an extra step for the cost-accounting area.</p>\n<h3 data-toc-skip=\"\">Depreciation Posting Run</h3>\n<p>The determination of the accumulated depreciation of an asset and the posting of depreciation expense takes place in the system from now on at different points in time:</p>\n<ol>\n<li>Calculate depreciation (transaction AFAR):<br/>The planned depreciation is determined with each master record change and with each posting on the asset and updated in the database accordingly.</li>\n<li>Post depreciation (transaction AFAB):<br/>The depreciation run adopts the planned asset values and posts them in Financial Accounting. The posting document is updated in Financial Accounting at asset level.</li>\n</ol>\n<p>The new program for posting depreciation (depreciation run) has the following characteristics:</p>\n<ul>\n<li>For the update of accumulated depreciation and depreciation expense, the following applies:</li>\n<ul>\n<li>The planned depreciation is determined with master record changes and with postings on the asset and updated in the database accordingly.</li>\n<li>The depreciation run adopts the planned asset values and posts them.</li>\n<li>The journal entry is updated in Financial Accounting at asset level.</li>\n</ul>\n<li>Errors with individual assets do not necessarily need to be corrected before period-end closing; period-end closing can still be performed.</li>\n<li>You have to make sure that all assets are corrected by the end of the year only so that depreciation can be posted completely.</li>\n<li>It is possible to execute a mid-year test run (also the first run of a period) with a restriction on assets.</li>\n<li>The following applies to parallel processing. The program always carries out parallel processing.<br/><strong>Caution:<br/></strong>You can still specify a server group.</li>\n<ul>\n<li>If you specify a server group, the system behaves as it has until now.</li>\n<li>Until now if nothing was specified for the server group then no parallel processing took place. However, in the new program parallel processing takes place for the depreciation run on all available servers.</li>\n</ul>\n<li>\n<p>Only the following statuses exist now for the depreciation run:</p>\n<ul>\n<li>Errors occurred</li>\n<li>Documents posted successfully</li>\n<li>No documents to post</li>\n</ul>\n</li>\n</ul>\n<p>You can check the status of the depreciation run in the log (job log or spool output) or in the Schedule Manager.</p>\n<ul>\n<li>The following constraints still apply:</li>\n<ul>\n<li>In a test run, the depreciation run can be started in dialog with a limit to a maximum of 1000 assets.</li>\n<li>The update run can only be started in the background.</li>\n<li>In an update run, the company code must be capable of being posted to, in other words, the posting periods must be open.</li>\n</ul>\n<li>If the program is started in the background, an entry is written in the Schedule Manager for each combination of company code/accounting principle (ledger group).</li>\n</ul>\n<p>One difference now is that it is no longer necessary to specify a run type because the program recognizes whether the depreciation run is being run for the first time or whether it is a repeat run.</p>\n<p>You can <strong>no</strong> longer jump directly from the depreciation log to line items in an update run as you could before in classic Asset Accounting.<br/>Instead however, you can generate a list of posted depreciation documents for one or more periods in the general ledger. In the journal entry the system updates the documents for depreciation in Financial Accounting at asset level. As such you have the complete detailed information available in the document. For example, create a list of line items in the general ledger by choosing<em> Accounting -&gt; Financial Accounting -&gt; General Ledger -&gt; Account -&gt; Change/Display Items (New)</em> (program FAGL_ACCOUNT_ITEMS_GL) in the SAP Easy Access Menu. Alternatively, you can choose a corresponding report under <em>Accounting -&gt; Financial Accounting -&gt; General Ledger -&gt; Information System -&gt; General Ledger Reports -&gt; Line Items -&gt; G/L Line Items, List for Printing. </em>Select the relevant documents<em> </em>for the desired combination of G/L accounts, document type and posting period/year.<br/>Another option is to use the app <em>Display Line Items in General Ledger</em>. Here you can filter the line items for status \"All Items\" and for ledger, company code, asset and where necessary G/L account.</p>\n<p>The smoothing method is no longer necessary and is therefore no longer supported. In fact, with the possibility of creating new time intervals in the new depreciation engine, a change of depreciation methods or useful life can be done in the period where it occurs. Therefore such a workaround as the smoothing is no longer needed. If you have used this method before the conversion to SAP S/4HANA, you may observe changes in the book values in the period after the conversion.</p>\n<p>As of release SAP S/4HANA 1809, the BSEG table will no longer be updated with the depreciation run (transaction AFAB, AFABN). (See also SAP Note 2383115.) <br/>It is also no longer possible to subsequently activate this update.</p>\n<p>You can still use program RAPOST2001 (transaction AFBP) to display depreciation runs in the Schedule Manager. However, the selection screen of the program was adjusted to the new program logic for posting depreciation.</p>\n<h3 data-toc-skip=\"\">Cross-Company Code Posting of Depreciation and Interest</h3>\n<p>Controlling <strong>no</strong> longer supports any cross-company code processes in a simplified manner. This means: In SAP S/4HANA it is <strong>not</strong> possible to post depreciation and interest expense to a CO receiver object that is in a different company code to the fixed asset. The cross-company code cost center check can <strong>no</strong> longer be activated. For more information, see SAP Note 2913340.</p>\n<h3 data-toc-skip=\"\">Insurance Values</h3>\n<p>Until now there were two ways to map insurable values in the system:</p>\n<ol>\n<li>Defining specific information for insuring the complex fixed asset in the asset master record </li>\n<li>Managing insurance values in a dedicated depreciation area</li>\n</ol>\n<p>In SAP S/4HANA only the second option is supported, the management of insurable values in a dedicated depreciation area.</p>\n<p>The Customizing activities for the first option are <strong>no</strong> longer available. Their corresponding fields/field groups in the asset master record are also <strong>no</strong> longer available.</p>\n<p>To map insurable values in SAP S/4HANA, you must create a new depreciation area for insurance values (area type 05) in the chart of depreciation and define allowed entries for the depreciation area in the asset classes of the affected complex fixed assets. Note that new depreciation areas can only be implemented for new fixed assets, not for existing fixed assets. Existing fixed assets must therefore be transferred manually with the conversion to SAP S/4HANA, alternatively you can also implement and build the new depreciation areas in your ERP system for existing fixed assets before the conversion to SAP S/4HANA. The usual asset lists and journal entry reports are available for reporting. Special reports for reporting insurance values are <strong>no</strong> longer necessary.</p>\n<p>Please check note 2403248 for the availability of a tool to subsequently create new depreciation areas for assets already in use.</p>\n<h3 data-toc-skip=\"\">Leasing Management</h3>\n<p>The leasing opening entry from the asset master record is <strong>not</strong> supported in SAP S/4HANA.</p>\n<p>You can use the Leasing solution of the component <em>Flexible Real Estate Management</em> (RE-FX) instead. For more information, see SAP Note 2255555.</p>\n<h3 data-toc-skip=\"\">BAPIs and BAdIs</h3>\n<p><em><strong>Changed BAPIs</strong></em></p>\n<p>In the BAPIs that already existed in classic Asset Accounting, the fields Accounting Principle/Ledger Group and Depreciation Area have been added.</p>\n<p>You are only allowed to fill these fields if the Customizing switch for new Asset Accounting is active.</p>\n<p>The following BAPIs from classic Asset Accounting have been adapted for new Asset Accounting:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Business Object:<br/>Short Description </strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Business Object:<br/>Object Name</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Function Modules</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Asset Acquisition</p>\n</td>\n<td valign=\"top\">\n<p>AssetAcquisition</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_ACQUISITION_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_ACQUISITION_POST</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Asset Retirement</p>\n</td>\n<td valign=\"top\">\n<p>AssetRetirement</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_RETIREMENT_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_RETIREMENT_POST</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Asset Post-Capitalization</p>\n</td>\n<td valign=\"top\">\n<p>AssetPostCapitaliztn</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_POSTCAP_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_POSTCAP_POST</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Asset Document Reversal</p>\n</td>\n<td valign=\"top\">\n<p>AssetReversalDoc</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_REVERSAL_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_REVERSAL_POST</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><strong>New BAPIs</strong></em></p>\n<p>There are the following new BAPIs:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\">\n<p><strong>Business-Object:<br/>Short Description</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Business Object:<br/>Object Name</strong></p>\n</td>\n<td valign=\"top\">\n<p><strong>Function Modules</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Assets: Down payment</p>\n</td>\n<td valign=\"top\">\n<p>AssetDownPayment</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_DOWNPAYMENT_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_DOWNPAYMENT_POST</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Assets: Investment Support</p>\n</td>\n<td valign=\"top\">\n<p>AssetInvestSupport</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_INV_SUPPORT_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_INV_SUPPORT_POST</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Assets: Revaluation</p>\n</td>\n<td valign=\"top\">\n<p>AssetRevaluation</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_REVALUATION_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_REVALUATION_POST</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Assets: Subsequent Costs and Revenues</p>\n</td>\n<td valign=\"top\">\n<p>AssetSubCostRev</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_SUB_COST_REV_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_SUB_COST_REV_POST</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Assets: Transfer Postings</p>\n</td>\n<td valign=\"top\">\n<p>AssetTransfer</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_TRANSFER_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_TRANSFER_POST</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Assets: Depreciation</p>\n</td>\n<td valign=\"top\">\n<p>AssetValueAdjust</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_VALUE_ADJUST_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_VALUE_ADJUST_POST</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p>Assets: Write-Up</p>\n</td>\n<td valign=\"top\">\n<p>AssetWriteUp</p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_WRITEUP_CHECK,</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p> </p>\n</td>\n<td valign=\"top\">\n<p>BAPI_ASSET_WRITEUP_POST</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><strong>Changed BAdIs</strong></em></p>\n<p>In release SAP S/4HANA 2020, the following Business Add-Ins (BAdIs) for the depreciation calculation have been adjusted for technical reasons:</p>\n<ul>\n<li>Customer-Specific SAP Standard Enhancements (FAA_DC_CUSTOMER) and</li>\n<li>BAdI for Customer-Specific Enhancements (FAA_EE_CUSTOMER)</li>\n</ul>\n<p>If you use implementations of these BAdIs, you must check them and adjust them if necessary. For more information, read the long text of error message AU176 (<em>Check on BAdI implementation for depr. calcul. required; see long text</em>).</p>\n<p><em><strong>New BAdIs</strong></em></p>\n<p>Until now you could use Business Add-In (BAdI) BADI_FIAA_DOCLINES to make customer-specific changes to the asset document. As of release SAP S/4HANA 1909, this BAdI has been replaced with the new BAdI FAA_DOCLINES_CUSTOMER; the system no longer calls existing implementations of the BAdI BADI_FIAA_DOCLINES. If therefore you have used BAdI BADI_FIAA_DOCLINES until now, you must implement the interface of the new BAdI FAA_DOCLINES_CUSTOMER instead.</p>\n<h3 data-toc-skip=\"\">Fiscal Year Change/Balance Carryforward</h3>\n<p>The balance carryforward of Asset Accounting is integrated with the balance carryforward of Financial Accounting with the universal journal entry:</p>\n<ul>\n<li>With the balance carryforward of Financial Accounting, values carried forward are written on both account and ledger level for each asset.</li>\n<li>The new year is opened in Asset Accounting only when all representative ledgers of a company code have been carried forward.</li>\n<li>In order to determine the planned depreciation values for the depreciation run, the system automatically triggers a recalculation of values for all fixed assets in the new year.</li>\n<li>If errors occurred, you should start the recalculation again manually.</li>\n</ul>\n<p>The program Fixed Assets-Fiscal Year Change (RAJAWE00, transaction AJRW) is no longer necessary and no longer has to be performed at fiscal year change.</p>\n<h3 data-toc-skip=\"\">Year-End Closing</h3>\n<p>As of release SAP S/4HANA 1909, year-end closing is executed and reset using transaction FAA_CMP. The following applies for a ledger:</p>\n<ul>\n<li>You can close a fiscal year for accounting for a ledger or a depreciation area.</li>\n<li>You can also reopen the fiscal year last closed for one or more depreciation areas.</li>\n</ul>\n<p>Despite these adjustments, program RAJABS00 (transaction AJAB) is still available in the SAP Easy Access menu (only until release SAP S/4HANA 1909).</p>\n<p>In addition, as of SAP S/4HANA 2020, the program <em>Year-End Closing Asset Accounting (Cross-Company Code and Ledger)</em> (program FAA_CLOSE_FISCAL_YEARS, transaction FAA_CLOSE_FISC_YEARS) is available. You use this program to close a fiscal year from an accounting perspective for one or more company codes or ledgers.</p>\n<h3 data-toc-skip=\"\">Reports for Posted Depreciation</h3>\n<p>Reports for posted depreciation that are based on table ANLP (especially report Posted Depreciation, Cost Center-Related, transaction S_ALR_87010175, InfoSet /SAQPQUERY/AM27) can no longer work on the basis of the account assignment objects of the fixed asset they are based on.</p>\n<p>Exception: The accumulated depreciation accounts are managed as a statistical cost element. (See also SAP Note 2297425.)</p>\n<p>Whilst there is no new fixed asset-specific report, you can display the Fiori app G/L Account Line Items with the corresponding filter criteria instead. The expense items are from now on updated with the information of the source fixed asset.</p>\n<h3 data-toc-skip=\"\">Segment Reporting</h3>\n<p>For segment reporting, the business function <em>FI-AA, Segment Reports on Fixed Assets</em> (FIN_AA_SEGMENT_REPORTING) is available as of SAP S/4HANA 1610, FPS01. This business function is always on. In contrast to the business function <em>FI-GL (New), Profit Ctr Reorganization and Segment Reports</em> (FIN_GL_REORG_1) only available for ECC (as of SAP enhancement package 5 for SAP ERP 6.0), the new FIN_AA_SEGMENT_REPORTING business function supports automatic transfer postings in case of asset master data changes. (See also SAP Note 2257555.)</p>\n<h3 data-toc-skip=\"\">Legacy Data Transfer</h3>\n<p>The legacy data transfer had to be adjusted in Asset Accounting due to the introduction of the universal journal entry.</p>\n<p><em><strong>Manual Legacy Data Transfer</strong></em></p>\n<p>The manual legacy data transfer is now divided into the following steps:</p>\n<ol>\n<li>You create asset master records for the legacy data transfer using transaction AS91.</li>\n<li>You post the transfer values using transaction ABLDT; In doing so, a universal journal entry is posted for the fixed asset.</li>\n<li>With transaction AB01, you post, for a legacy data transfer during the year, the transactions that occurred in the fiscal year of the legacy data transfer between the start of the fiscal year and the date of the legacy data transfer.</li>\n<li>You use transaction ABLDT_OI to post the open items for an asset under construction with line item processing.</li>\n</ol>\n<p>The following functions are also available with the manual legacy data transfer:</p>\n<ul>\n<li>If wrong transfer values were posted, you must reverse the journal entry and then recreate it.</li>\n<li>You can use transaction AS92 to change master data; transaction AS93 to display master data; and transaction AS94 to create subnumbers for the asset master record.</li>\n<li>There are corresponding transactions for group assets with the legacy data transfer (transactions AS81 to AS84).</li>\n</ul>\n<p>You can find the changed Customizing activities and functions for the legacy data transfer in Customizing for Asset Accounting under <em>Asset Data Transfer</em>.</p>\n<p><em><strong>Automatic Legacy Data Transfer</strong></em></p>\n<p>In addition to the manual transfer, the following automatic (mass-compatible) transfer methods are available:</p>\n<ul>\n<li>Legacy data transfer using Microsoft Excel (transaction AS100)<br/>Transaction AS100 is still available; it has been completely adapted to the logic of the universal journal entry.</li>\n<li>Legacy data transfer using the Legacy System Migration Workbench (transaction LSMW)<br/>With the legacy data transfer using transaction LSMW, you can only transfer legacy assets using the business object method. The options Batch Input and Direct Input are no longer available.</li>\n<li>Legacy data transfer using BAPI<br/>With this you create a customer-defined program that calls module BAPI_FIXEDASSET_OVRTAKE_CREATE.<br/>This corresponds with choosing method CREATEINCLVALUES of business object BUS1022 in transaction LSMW.</li>\n<li>Transfer of mid-year transactions<br/>You cannot only transfer mid-year transactions when creating legacy assets; You can also post them subsequently using the standard BAPI for Asset Accounting. All standard functions of the relevant posting BAPI are available with this. The system posts these transactions automatically against the migration clearing account.</li>\n</ul>\n<p>Due to extensive changes in the data store of Asset Accounting, the following mass-compatible transfer methods are no longer supported:</p>\n<ul>\n<li>Program RAALTD01</li>\n<li>Program RAALTD11</li>\n<li>Program RAALTD11_UNICODE</li>\n<li>Any type of batch input on the transactions AS91, AS92, AT91, AT92, AS81, AS82, AT81 and AT82 only allows the creation and change of master data. Values cannot be transferred this way. You must rather use transaction ABLDT; however, this is not mass-compatible. The AT** transactions were also deleted; the above also and explicitly refers to customer-defined replacement transactions created analog.</li>\n</ul>\n<h3 data-toc-skip=\"\">Archiving</h3>\n<p>The reload program for archiving RAARCH03 is <strong>no</strong> longer supported.</p>\n<p>Archived documents of Asset Accounting can only be written but <strong>not</strong> reloaded.</p>\n<h3 data-toc-skip=\"\">Constraints</h3>\n<p>The following constraints apply:</p>\n<ul>\n<li>As of release SAP S/4HANA 1610, user-defined currencies are available in General Ledger Accounting (FI-GL). These are not available in Asset Accounting, but they are translated with the document life. The parallel local currencies used until now are translated in the meantime historically in the fixed assets, provided that depreciation areas are set up for parallel currencies.<br/>For more information on currencies in the universal journal, see SAP Note 2344012.</li>\n<li>The following are not supported in new Asset Accounting: The subsequent implementation of a ledger, the subsequent switch from the accounts approach to the ledger approach (scenario 7 and 8 in the migration scenarios for new General Ledger Accounting), and the implementation of a new depreciation area.<br/>The function subsequent implementation of a further accounting principle is available as of release SAP S/4HANA 1610, provided that you use the ledger approach.<br/>And as of release SAP S/4HANA 1809, you can use the functions available for the subsequent implementation of a depreciation area (program RAFAB_COPY_AREA and BAdI FAA_AA_COPY_AREA).</li>\n<li>Not all transaction data is deleted in Asset Accounting when you reset a company code (using transaction OABL). See also SAP Note 2419194.</li>\n<li>ALE transfer is not available in new Asset Accounting.</li>\n<li>The batch input method for transactions based on AB01 is no longer available; you can use the changed and new BAPIs for postings instead. Information about these BAPIs can be found above.</li>\n<li>The batch input technique for master data is no longer available. You can use a different transfer method instead for the legacy data transfer, for example the BAPI for master data or direct input.</li>\n<li>The integrated posting of investment support on assets using FI transactions is no longer supported. Instead, use transaction ABIFL, and post the customer transaction against a clearing account.</li>\n<li>Impairments from the business function New General Ledger Accounting 3 (FIN_GL_CI_3), SAP enhancement package 5 for SAP ERP 6.0 are no longer available.</li>\n<li>During reversal of a document with asset accounting transaction AB08 you can no longer see derived depreciation areas.</li>\n<li>For Lease Accounting Engine (LAE) see SAP Note 2270391.</li>\n<li>For Joint Venture Accounting (JVA) see SAP Note 2270392.</li>\n<li>For specific business functions see SAP Note 2257555.</li>\n</ul>\n<h3 data-toc-skip=\"\"><span>Existing Data and Conversion/Migration</span></h3>\n<h3 data-toc-skip=\"\">Existing Data</h3>\n<p><em><strong>Master Data</strong></em></p>\n<p>Existing master data is <strong>not</strong> changed by the activation of new Asset Accounting or the upgrade to SAP S/4HANA.</p>\n<p><em><strong>Customizing Data</strong></em></p>\n<p>However, you might possibly have to migrate and adjust your Customizing data. This depends on your source release, so the release you have before the migration to SAP S/4HANA. Existing Customizing settings from classic Asset Accounting or from new Asset Accounting are transferred; this means that to the extent you have already been working with classic or new Asset Accounting, you do not have to make all the Customizing settings again. You have to migrate, to some extent check, change and add to your Customizing data in these two cases: you are migrating from classic General Ledger Accounting with classic Asset Accounting, or from new General Ledger Accounting with classic or from new Asset Accounting.</p>\n<p><em><strong>Transaction Data</strong></em></p>\n<p>Transaction data is migrated from the existing subledger data store of Asset Accounting to the universal journal entry. The data however remains in the original database tables and can be read using the compatibility view in the previous format, for example in customer-defined programs (for the compatibility views, cf. SAP Note 2270387). The tables of the existing subledger entry are no longer required for further processing and can be unloaded from the database if necessary. All the required information is now in the new database tables.</p>\n<p>Note the following also:</p>\n<ul>\n<li>The migration must take place at a time when only one fiscal year is open in Asset Accounting.<br/>Once migration is completed, you are not allowed to open any fiscal year again that occurred before the migration in Asset Accounting.</li>\n<li>You cannot reverse documents that were created before the conversion from classic to new Asset Accounting. Instead, you have to make an inverse posting.</li>\n<li>It is not possible to reverse integrated asset postings that were posted in new Asset Accounting in SAP enhancement package 7 for SAP ERP 6.0, SP02 or higher and were migrated. Instead, you have to make an inverse posting.</li>\n<li>The values for migrated asset line items (MIG_SOURCE = A) are migrated solely from BSEG relevant currencies. Subsequently added currencies which were not available before the migration are not created for migrated asset line items in the standard. This is especially the case for automatically created free defined currencies during the migration (for example if the CO currency was not available in G/L before migration it is automatically added as a free defined currency during migration). If currencies were subsequently added to migrated documents, then it is not possible to reverse migrated asset documents. Instead, you have to make an inverse posting.</li>\n<li>If until now you updated transactions in parallel valuations with different fiscal year variants and want to continue using this update, then you must implement a new representative ledger using the SAP General Ledger Migration Service before you install SAP S/4HANA. For more information about alternative fiscal year variants with parallel valuation, see SAP Note 2220152.</li>\n<li>It may no longer be possible to process worklists that have been created before the changeover to the new Asset Accounting. This involves worklists that create transaction data (however, not master data changes), such as worklists for retirement without revenue, retirement with revenue, intercompany asset transfer, and impairment posting. You should therefore either process these worklists before you activate the Customizing switch, or create them again once you have activated new Asset Accounting.<br/>For more information, see SAP Note 2220152.</li>\n</ul>\n<p>For more information about the conversion or migration to SAP S/4HANA, see the following:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"376\">\n<p><strong>Information Source</strong></p>\n</td>\n<td valign=\"top\" width=\"376\">\n<p><strong>Explanation </strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"376\">\n<p>Conversion Guide for SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"376\">\n<p>Conversion Overview: Getting Started, Preparing the Conversion, Realizing the Conversion</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"376\">\n<p>SAP Note 2332030</p>\n</td>\n<td valign=\"top\" width=\"376\">\n<p>Specification for the Conversion/Migration in Accounting</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"376\">\n<p>Application Documentation of New Asset Accounting</p>\n</td>\n<td valign=\"top\" width=\"376\">\n<p>Specification for the Migration in Asset Accounting: Recommended Time for the Migration, Prerequisites, Migration of Customizing with Many Examples, Among Others.</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<h3 data-toc-skip=\"\">User Interface</h3>\n<p><em><strong>Implementation Guide</strong></em></p>\n<p>New Asset Accounting uses its own IMG structure in the SAP Reference IMG:</p>\n<p>This IMG structure contains some of the same activities as are in classic Asset Accounting, some changed activities, and some new activities; a few of the classic Asset Accounting activities are <strong>no</strong> longer contained in the IMG structure at all.</p>\n<p><em><strong>SAP Easy Access Menu</strong></em></p>\n<p>The SAP Easy Access Menu for new Asset Accounting remains largely the same as for classic Asset Accounting. You can continue to find it under <em>SAP Menu ‑&gt; Accounting ‑&gt; Financial Accounting ‑&gt; Fixed Assets</em>.</p>\n<p>However, there is rerouting to some new transactions and some transactions which have become superfluous are no longer available. (Compare the following explanations of the transactions.)</p>\n<p><em><strong>Transactions</strong></em></p>\n<p>Some of the classic Asset Accounting transactions have been replaced by corresponding new ones. So that you can make accounting-principle-specific postings, the input fields <em>Depreciation Area</em> and <em>Accounting Principle</em> can be found on the screen for the new transactions. The name of the relevant new transaction ends in \"L\". Example: The previous transaction AB01 (Create Asset Transactions) is replaced by the new transaction AB01L.</p>\n<p>The new transactions are: AB01L, ABAAL, ABAKL, ABAOL, ABAVL, ABAWL, ABGFL, ABGLL, ABIFL, ABMAL, ABMRL, ABNAL, ABNEL, ABNKL, ABSOL, ABSTL, ABUML, ABZEL, ABZOL, ABZPL, ABZUL.</p>\n<p>If you enter the transaction familiar from classic Asset Accounting (that does not end in L), you are automatically transferred to the new transaction (that ends in L).</p>\n<p>As of release SAP S/4HANA 1909, both transactions OAAR and OAAQ have been redirected to transaction FAA_CMP.</p>\n<p>As of release SAP S/4HANA 2020, transaction AJAB (program RAJABS00) is redirected to transaction FAA_CLOSE_FISC_YEARS.</p>\n<p>Transaction ABSO_OLD from classic Asset Accounting is no longer available in new Asset Accounting; you can use transaction AB01L instead. All SAP Notes that refer to transaction ABSO_OLD are automatically invalid in SAP S/4HANA.</p>\n<p>The following transactions are <strong>no</strong> longer available:</p>\n<ul>\n<li>ABST, ABST2, ABSTL</li>\n<li>ABAWN</li>\n<li>ABUB</li>\n<li>AW01_AFAR</li>\n<li>ABF1 and ABF1L</li>\n<li>AJRW</li>\n<li>OASV</li>\n<li>AB02</li>\n<li>ASKB and ASKBN</li>\n<li>ABMW</li>\n<li>ABCO</li>\n<li>AUN1, AUN2, AUN3, AUN4, AUN5, AUN6, AUN7, AUN8, AUN9, AUN10, AUN11</li>\n<li>AR16, S_ALR_87101171 (deleted as of release SAP S/4HANA 2022)</li>\n</ul>\n<p><em><strong>Programs</strong></em></p>\n<p>The following programs are <strong>no</strong> longer available:</p>\n<ul>\n<li>RAALTD01, RAALTD11, RAALTD11_UNICODE</li>\n<li>RAPERB2000, RAPOST2000, RAPOST2010</li>\n<li>RAABST01, RAABST02, FAA_GL_RECON</li>\n<li>RAJAWE00</li>\n<li>RAUMFE20</li>\n<li>RAAEND02 (deleted as of release SAP S/4HANA 2022)</li>\n<li>RARUECK1</li>\n</ul>\n<p>Adjustment programs RACORR_0TW_COMPLETE, RACORR_ADD_MISSING_T082AVIEWB, RACORR_ANEK_AWSYS, RACORR_ANLA_AKTIV, RACORR_ANLA_ZUJHR, RACORR_ANLC_ANLP_POSTED_VALUES, RACORR_ANLC_DEL_ANLB_LGJAN, RACORR_ANLH_LANEP, RACORR_ANLW, RACORR_ASSET_GROUP_IND, RACORR_DELETE_ANLBZA, RACORR_DELETE_EMPTY_TABW_ENTRY, RACORR_EUROGSBER, RACORR_LINE_ITEMS_CHECK, RACORR_MIGRATION_NEWGL_LDGRP, RACORR_MISSING_ANEP_REVERSALS, RACORR_NEW_GRP_ASSET_ASSIGN, RACORR_PERFY_REPAIR, RACORR_T082AVIEW1, RACORR_T093_VZANSW, RACORR_TABA_ENTRY_CREATE, RACORR_VIEW0_SET, RACORR_XNEGA, RACORR_XZORG, RACORR_XZORG_46A, RACORR07A, RACORR10_PERFY24, RACORR100, RACORR100_WITOUT_AV, RACORR101, RACORR102, RACORR103, RACORR104, RACORR105, RACORR106, RACORR107, RACORR108, RACORR110_3ERRELEASES, RACORR110_B, RACORR111, RACORR112, RACORR113, RACORR113_TCODE_FILL, RACORR114, RACORR114_A, RACORR115, RACORR116, RACORR117, RACORR118, RACORR119, RACORR120, RACORR121, RACORR122, RACORR125, RACORR126, RACORR127, RACORR128A, RACORR129, RACORR130, RACORR131, RACORR132, RACORR133, RACORR134, RACORR135, RACORR137, RACORR138, RACORR139, RACORR14_OLD, RACORR140, RACORR141, RACORR142, RACORR143, RACORR144, RACORR145, RACORR146, RACORR147, RACORR148, RACORR149, RACORR150, RACORR151, RACORR151_CREATE_ANLC, RACORR152, RACORR16, RACORR17, RACORR18, RACORR20_A, RACORR21, RACORR23_NEW, RACORR24, RACORR45A, RACORR46, RACORR47, RACORR48, RACORR49, RACORR50, RACORR51, RACORR52, RACORR53, RACORR54, RACORR55, RACORR56, RACORR57, RACORR58, RACORR59, RACORR60, RACORR61, RACORR62, RACORR63, RACORR64, RACORR65, RACORR66, RACORR68, RACORR69, RACORR75, RACORR76, RACORR77, RACORR78, RACORR79, RACORR7A, RACORR7B, RACORR80, RACORR81, RACORR82, RACORR83, RACORR83_A, RACORR84, RACORR85, RACORR86, RACORR87, RACORR87A, RACORR88, RACORR89, RACORR89B, RACORR90, RACORR91, RACORR92, RACORR93, RACORR94, RACORR96_SINGLE_ASSETS, RACORR97, RACORR98, RACORR99, RAFABDELETE</p>\n<p><em><strong>Authorization Objects</strong></em></p>\n<p>The following authorization object is <strong>no</strong> longer available:</p>\n<ul>\n<li>A_M_ANLKL (deleted as of release SAP S/4HANA 2022)</li>\n</ul>\n<p> </p>", "noteVersion": 23, "refer_note": [{"note": "2913340", "noteTitle": "2913340 - Error during cross-company code depreciation postings or interest postings", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You post cross-company code depreciation. Depending on the constellation, various symptoms may occur:</p>\n<ul>\n<li>The system issues error message AU390 for depreciation areas that post interest to the general ledger, even though the depreciation posting run was carried out successfully.</li>\n<li>If interest has been posted across company codes in a company code in which Asset Accounting is also active, the reporting of depreciation key figures displays incomprehensible figures for identical asset numbers.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>AFAB, AFABN, FAA_DEPRECIATION_POST, V_T093C_14, ORFA_KOST_UEBER, T093C-BKRAKT, AAPO525, AAPO 525</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>As described in SAP Note 2270404, Controlling in SAP S/4HANA <strong>no longer</strong> supports cross-company-code processes in a simplified way. This means that the function for posting expenses from depreciation and interest to a CO receiver object, which is in a different company code than the asset, is <strong>no longer</strong> supported. Unfortunately, it is still possible to activate the cost center check across company codes in the asset subledger, and the depreciation run creates corresponding documents including company code clearing lines. In addition, in the case of interest postings, the system still posts using an incorrect subledger-specific line item type (SLALITTYPE), which generates various artifacts in reporting and in the determination of interest.</p>\n<p>Program error: This function in the asset subledger should <strong>no longer</strong> have been available in SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li>This SAP Note removes the function from the system, including the corresponding IMG activities.<br/>The depreciation run issues error message AAPO525 during posting when it encounters an asset to which cross-company code CO objects are assigned.</li>\n<li>The simplification item (see SAP Note 2270388 for FI-AA) is enhanced accordingly.</li>\n</ul>", "noteVersion": 2, "refer_note": [{"note": "2270404", "noteTitle": "2270404 - S4TWL - Technical Changes in Controlling", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP_FIN, S4CORE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note is relevant if you are doing a conversion from ERP ECC 6.0 (EHP 7 or higher)  to S/4 HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>In SAP S/4 HANA the totals records for primary and secondary costs have been removed and the universal journal includes all actual cost postings, both primary and secondary.</p>\n<ul>\n<li>This ensures for primary postings that there is a single source of truth, such that all general ledger items with an assignment to cost centers, orders, WBS elements, business processes, or CO-PA characteristics are treated as one data record which is enriched with the relevant profit center, functional area, segment, and so on. Since Financial Accounting and Controlling will always be in synch, the company code is checked during all postings to an account assignment in Controlling and that it is no longer possible to remove the flag \"CoCd Validation\" in the controlling area.</li>\n<li>For secondary postings this ensures that all sender-receiver relationships that are triggered by allocations, settlement, and so on are captured in the universal journal along with the partner profit centers, functional areas, and so on that are affected by the allocation.</li>\n</ul>\n<p><strong>Description</strong></p>\n<p>Actual Items:</p>\n<ul>\n<li>Actual data of COEP (WRTTP = ‘04’) is now stored in ACDOCA.</li>\n<li>Actual statistical data of COEP (WRTTP = ‘11’) is stored in ACDOCA using additional columns for the statistical account assignments and in COEP for compatibility access.</li>\n<li>Actual data needed for long-term orders/projects from COSP_BAK, COSS_BAK is stored in table ACDOCA.</li>\n<li>Currently, COBK is written as before.</li>\n<li>Compatibility views V_&lt;TABLENAME&gt; (for example, V_COEP) are provided to reproduce the old structures.</li>\n<li>Access to old data in tables is still possible via the views V_&lt;TABLENAME&gt;_ORI (for example,  V_COEP_ORI).</li>\n<li>Value types other than ‘04’ and ‘11’ are still stored in COEP, COSP_BAK, COSS_BAK.</li>\n</ul>\n<p><strong>Business Process related information</strong></p>\n<p>The former tables COEP, COSP, and COSS are replaced by views of the same name, so-called compatibility views, that aggregate the data in the universal journal on the fly in accordance with the old table structures. <br/>CO standard transactions are adapted in a stepwise manner to access the new universal journal table (ACDOCA) directly instead of using compatibility views. The conversion process is ongoing.<br/>From a business point of view, all classic transactions are still running - based on compatibility views or using direct acces to ACDOCA.<br/>The goal of the compatibility views is to provide a bridge to the new data model. Using these views may have a performance impact on existing reports and you are recommended to investigate whether the new Fiori reports meet your business needs.  <br/>Customer coding still runs based on compatibility views.</p>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Please check your customer-specific programs using CO tables.<br/>Customer coding should be adapted by replacing the access via compatibility views with direct access to ACDOCA for value types 04 and 11.<br/>Details are described in note 2185026.</p>\n<p> </p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"198\">\n<p>2185026</p>\n</td>\n<td valign=\"top\" width=\"557\">\n<p>Compatibility views COSP, COSS, COEP, COVP: How do you optimize their use.</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"198\">\n<p>Notes linked in 2185026</p>\n</td>\n<td valign=\"top\" width=\"557\">\n<p> </p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 6}]}, {"note": "2403248", "noteTitle": "2403248 - FI-AA (new): Availability of RAFABNEW", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to subsequently create a new depreciation area in Asset Accounting (new) (FI-AA (new)).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Automatic opening of a new depreciation area; subsequent creation of a new depreciation area; RE RAFABNEW; RE FAA_RAFAB_COPY_AREA; BAdI FAA_AA_COPY_AREA; add new depreciation area; transaction AFBN; new accounting principle; scenario 7; migration scenario 7; sFIN; SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You use Asset Accounting (new) productively.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For the subsequent implementation of a new depreciation area or a new accounting principle, the following programs and functions were or are available:</p>\n<ul>\n<li><strong>Program RAFABNEW</strong>:</li>\n<ul>\n<li><strong>Usage</strong>: subsequent implementation of a new depreciation area</li>\n<li><strong>More information</strong>: See the program documentation in the system.</li>\n<li><strong>No longer</strong> available as of the following releases: SAP Simple Finance 1503 and SAP S/HANA, on-premise Edition 1511<br/>Reason: Introduction of the journal entry with changed data structures.</li>\n</ul>\n<li><strong>Program RAFAB_COPY_AREA</strong>:</li>\n<ul>\n<li><strong>Usage</strong>: subsequent implementation of a new depreciation area in an existing valuation<br/><span>Typical example</span>: Within your \"Local GAAP\" local accounting principle, you require a depreciation area in which the insurance values are displayed. The \"Local GAAP\" local accounting principle already exists in the system, but there is no valuation area for insurance values yet.<br/>Adjust the program RAFAB_COPY_AREA to your requirements, and implement the new depreciation area for insurance values.</li>\n<li><strong>More information</strong>: See the program documentation in the system.</li>\n<li><strong>Available as of</strong> SAP S/HANA 1809</li>\n</ul>\n<li><strong><em>Subsequent Implementation of a Further Accounting Principle</em> function</strong>:</li>\n<ul>\n<li><strong>Usage</strong>: subsequent implementation of a new accounting principle, that is, a new valuation<br/><span>Typical example</span>: In your system, currently only the values for the \"Local GAAP\" local accounting principle are displayed in Asset Accounting. In future, you also want to display values according to another accounting principle (for example, according to IFRS).<br/>Start a project and use the <em>Subsequent Implementation of a Further Accounting Principle</em> function to implement the new accounting principle.</li>\n<li><strong>More information</strong>: in the IMG activity with the same name and in the application documentation</li>\n<li><strong>Available as of</strong> SAP S/4HANA Finance 1605 SP05 and SAP S/4HANA 1610</li>\n</ul>\n</ul>\n<p>The following table provides an overview of the Asset Accounting (new) releases in which the programs and functions are available:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"32%\">\n<p> </p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>SAP ERP 6.0 EHP 7 or higher and</p>\n<p>SAP Simple Finance add-on 1.0</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>SAP Simple Finance 1503 and</p>\n<p>SAP S/HANA, on-premise Edition 1511</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>As of SAP S/4HANA Finance 1605, SP05 and</p>\n<p>SAP S/4HANA 1610</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>As of SAP S/4HANA 1809</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"32%\">\n<p>Program RAFABNEW</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>X</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>--</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>--</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>--</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"32%\">\n<p>Program RAFAB_COPY_AREA</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>--</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>--</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>--</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>X</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"32%\">\n<p><em>Subsequent Implementation of a Further Accounting Principle</em> function</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>--</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>--</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>X</p>\n</td>\n<td valign=\"top\" width=\"17%\">\n<p>X</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Additional note</strong>:<br/>In SAP ERP, in General Ledger Accounting (new) with migration scenarios 7 and 8, you can subsequently implement a new ledger and subsequently switch from the accounts approach to the ledger approach. These migration scenarios are supported in SAP ERP in classic Asset Accounting, but <strong>not</strong> in Asset Accounting (new).</p>\n<p> </p>", "noteVersion": 6}, {"note": "1498047", "noteTitle": "1498047 - Changeover from old to new depreciation calculation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You currently use the old depreciation calculation and want to change over to the new depreciation calculation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Changeover, depreciation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The new depreciation calculation is available as of ECC 6.0. If you activate the extension EA-FIN, the new depreciation calculation is active.<br/>For more information about the new depreciation calculation, see SAP Note 965032.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>If you currently use the old depreciation calculation, we recommend that you proceed as follows to change over to the new depreciation calculation:</p>\n<ol>1. Create a test system with your production data, if you have not already done so.</ol><ol>2. In this system, recalculate values using the program RAAFAR00 in the update run to ensure that the data in the old depreciation calculation is consistent. Normally, RAAFAR00 should not perform any changes. As a precaution, you can execute RAAFAR00 in a test run first. If the test run displays changes, analyze these changes before you execute the update run.</ol><ol>3. Activate the extension EA-FIN. Note that this activation can no longer be undone. For more information about the extension EA-FIN, see SAP Note 1121965.</ol><ol><ol>4. Now, execute RAAFAR00 in a test run. Analyze differences that are larger than the expected rounding differences according to point 4b) in SAP Note 965032. Check whether an additional subpoint for point 4) from SAP Note 965032 could explain the differences that occur.</ol></ol><ol>If all of the differences that occur can be explained, you can execute RAAFAR00 in the update run.</ol><ol><ol>5. Test your business processes.</ol></ol>\n<p><br/><br/></p>\n<ol><ol>Refer also to SAP Note 1025126.</ol></ol><ol>If your business processes are processed as required, you can perform the same procedure in the production system as well.</ol>\n<p><br/>The ideal changeover date is immediately after the end of a fiscal year.<br/>If you change over from the old to the new depreciation calculation during the fiscal year, the system may issue the error messages described in SAP Note 1025126 due to a stricter check logic. The system may issue error messages like this, in particular, if you have already posted transactions in the current fiscal year for which the period control of the acquisition is controlled so the acquisition is to be taken into account only after a retirement or investment support.</p></div>", "noteVersion": 5, "refer_note": [{"note": "965032", "noteTitle": "965032 - Differences between old and new depreciation calculation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><br/><strong>Differences between the \"old\" and the \"new\" depreciation calculation - what should I do in the case of differences?</strong><br/><br/>This SAP Note describes the basic differences in function between the \"old\" and \"new\" depreciation calculation, it also describes the most important innovations.<br/><br/>This SAP Note also provides help for cases in which there are different calculation results in both depreciation calculations due to different calculation procedures or more consistent conversion of business conditions.<br/><br/>The SAP Note deals with the following topics:</p>\n<ol>1. Is the new depreciation calculation actually active?</ol><ol>2. Is there actually a problem with the new depreciation calculation?</ol><ol>3. Displaying the calculation parameters (depreciation trace)</ol><ol>4. Differences in the logic, differences that are normal</ol>\n<blockquote dir=\"ltr\">\n<p>a) Calculation procedure</p>\n<p>b) Rounding differences</p>\n<p>c) Management of time-dependent depreciation terms/the option of making a change during a fiscal year</p>\n<p>d) No depreciations on transactions</p>\n<p>e) Enhanced period controls</p>\n<p>f) No correction of unplanned depreciation using proportional ordinary depreciation for the rule for positive/negative net book value \"All Values Allowed\" if the unplanned depreciation returns a net book value below zero</p>\n<p>g) Base value \"net book value\" (24) during interest calculation</p>\n<p>h) Variable definition of interest</p>\n<p>i) Digital depreciation</p>\n<p>j) Treatment of subsequent acquisitions</p>\n<p>k) Changeover procedure</p>\n<p>l) Customer enhancements</p>\n<p>m) Shutdown</p>\n<p>n) EVL depreciation in Finland<br/><br/>o) Update of the fields ANEP-NAFAB, ANEP-SAFAB, ANEP-ZINSB</p>\n<p>p) Different handling during clearing of down payments</p>\n<p>q) Different handling of base value 12 (APC minus unplanned depreciation without write-up)</p>\n<p>r) Different handling of intracompany transfers/transfers relating to a depreciation key with the base value \"Net book value\" (24) and the percentage rate from the remaining life</p>\n<p>s) Different distribution of monthly posting amounts when using the \"Total Percentage Rate\" base method</p>\n<p>t) Different behavior if the base method U (percentage from remaining useful life calculated from changeover year) is used in phase 1 before the changeover.</p>\n</blockquote>\n<p><br/>This list of differences is not final.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>AFAR, new depreciation calculation, AFAMA, AFAMS, XSTIL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ol>\n<li><strong>Is the new depreciation calculation actually active? </strong><strong><br/></strong>The system switches to the new depreciation calculation only after you activate the SAP ECC Extension Financials (EA-FIN). If the add-on is not active, the system uses the old depreciation calculation. <br/>You can use transaction <strong>SFW5</strong> to check if extension EA-FIN is active. For the simple check, call transaction <strong>AW01 </strong>and choose 'Display dep. calculation' to navigate to the fixed asset trace. Depending on the depreciation calculation, a different screen appears. If the period interval procedure is listed in the header line, this is the new depreciation calculation.</li>\n<li><strong>Is there actually a problem with the new depreciation calculation?<br/></strong>The calculated depreciation amounts are firstly compared with one another. To obtain the depreciation values with the \"old\" depreciation calculation, you can use transaction.<strong>AW01_AFAR </strong>(Asset Explorer - old depreciation calculation). This transaction always uses the old depreciation calculation, even if the new one is active.<br/>Procedure:</li>\n<ol>\n<li>Call transaction <strong>AW01</strong> (Asset Explorer) for the affected fixed asset.<br/>If the system generates a dialog box telling you that values of the fixed asset have changed, the depreciation for this fixed asset is not current. This may be caused by a change process that was not properly closed. In this case, the change process is to be analyzed. However, if the fixed asset was not changed and the fixed asset already existed before the changeover to the new depreciation calculation, it is likely a rounding difference caused by the changed mode of operation of the new depreciation calculation.</li>\n<li>Call transaction <strong>AW01_AFAR </strong>(Asset Explorer - old depreciation calculation) in a separate session for the affected fixed asset. <br/>If the system displays a dialog box telling you that values of the fixed asset have changed, the old depreciation calculation arrives at a different result. You can calculate alternative values by choosing \"Recalculate dep\".</li>\n<li>Now you can compare the values of the two depreciation calculations with each other. If the difference is less than one integral currency unit, there is usually a rounding difference due to the changed calculation procedure. For larger differences, the cause can be determined using the fixed assets trace (Display dep.calculation).</li>\n</ol>\n<li><strong>Displaying the calculation parameters (depreciation trace)<br/></strong>SAP Note 964860 describes the new fixed asset trace.</li>\n<li><strong>Differences in the logic; differences that are normal<br/></strong></li>\n<ol>\n<li><strong>Calculation procedure,<br/></strong>The calculation procedure was converted from transaction-oriented to period-interval-oriented. The period-based depreciation calculation replaces the previous depreciation calculation that was based on individual transactions. The \"old\" depreciation calculation sequentially calculates each transaction separately and in the sequence in which a line item was posted to a fixed asset. However, the \"new\" calculation groups together and cumulates all transactions and value changes that are included in a time period (calculation period) and calculates them as a time segment. The system groups together the transactions of a fixed asset according to the calculation periods. <br/>The system uses the asset value date as well as the period control group of the transaction type group to determine the calculation period. The system assigns each transaction to a calculation period. Then the system creates period intervals from the determined calculation periods and calculates the depreciation on the basis of these intervals.</li>\n<li><strong>Rounding differences<br/></strong>Rounding differences between the two calculation methods may occur naturally due to the conversion of the calculation procedure (see point 4a). If the size of the difference of the calculation results between new and old depreciation calculation is integral currency unit or lower, this generally occurs due to a rounding difference.<br/>If you round to entire currency units and have posted many transactions to the fixed asset, for which the amounts are summarized in the new depreciation calculation in a calculation period, the differences may extend beyond one currency unit. The old depreciation calculation rounded after every transaction in this case. The new depreciation calculation, on the other hand, calculates an amount that is rounded once.</li>\n<li><strong>Managing time-dependent depreciation terms /the option of making a change during a fiscal year<br/></strong>With the old depreciation calculation, changes to the depreciation terms were always related to all open fiscal years. With the new depreciation calculation, you now have the option to change depreciation terms during the fiscal year. This means that the changed parameters are included in the calculation always from the period for which the period is also valid. The \"old\" depreciation calculation does not support this. The AW01_AFAR functions for time-dependent depreciation terms work only with the parameters of the last valid (newest) interval.<br/>The following depreciation terms can be maintained time-dependently on the fixed asset:</li>\n<ul>\n<li>Depreciation key</li>\n<li>Useful life</li>\n<li>Scrap value/scrap value percentage rate</li>\n<li>Variable depreciation portion</li>\n</ul>\n<li><strong>No depreciations on transactions,<br/></strong>The period-based calculation procedure cumulates all transactions that are included in a time period and determines the depreciation on these cumulative values. Therefore, it is no longer possible to retrospectively determine a redistribution of the calculation results on individual transactions. Therefore, if you use the new depreciation calculation, the system no longer saves depreciations on transactions in the line items. Exceptions to this rule are - proportional value adjustments in the case of retirements or retirement transfers, since these must continue to be calculated at single transaction level.</li>\n<li><strong>Extended period controls<br/></strong>With the new depreciation calculation, you can define the following additional period control in the depreciation key for the previous period controls that can be assigned to a phase of a depreciation key:</li>\n<ul>\n<li>Period control that is used for revaluations</li>\n<li>Period control that is used for investment support</li>\n<li>Period control that is used for unplanned depreciations</li>\n<li>Period control that is used for write-ups<br/><br/>The old depreciation calculation will not interpret any of these new period controls for such transactions, but it will use other period controls (as in the past) on the basis of specified criteria.</li>\n</ul>\n<li><strong>No correction of unplanned depreciation using proportional depreciation for the rule for positive/negative net book value \"All Values Allowed\" if the unplanned depreciation returns a net book value below zero<br/><br/></strong>If you do not set any \"Depreciation below net book value zero\" in a phase of a depreciation key to be calculated in the base method, this is valid only for the value type to which this phase refers (normally ordinary depreciation). If a net book value below zero is achieved by an unplanned in a year, since this for example, is allowed in the depreciation area rules, the old depreciation calculation tries to reach the net book value zero by correcting or adjusting the ordinary depreciation. In certain cases, this results in a positive ordinary depreciation in a fiscal year.<br/>In the new depreciation calculation, the feature in the base method of the depreciation key relates only to the value type (normally ordinary depreciation) to which this phase refers. In this respect, the new depreciation calculation may retain a negative net book value that the \"old\" calculation - as far as possible - would have corrected to net book value zero.<br/>This is a function that is required to separate the effect on individual value types and adjustment factors from one another more strictly and more uniquely. If you do not want a net book value under zero in a depreciation area, you can ensure this using only the specific feature of the net book value rule (VZREST) for the depreciation area. If you maintain normal depreciation and unplanned depreciation, you must post an appropriate amount of the unplanned interest to ensure that no negative result is achieved if it is not wanted.</li>\n<li><strong>Base value \"net book value\" (24) during the interest calculation,<br/></strong>The base value \"Net book value\" for interest in the old depreciation calculation is the net book value for the fiscal year start AFTER the deduction of the planned ordinary depreciation and special depreciation. In other words, the base value 24 for interest in the old depreciation calculation represents the net book value for the end of the fiscal year. The previous recommendation for the interest calculation was to use \"Average net book value without special depreciation\" (22) to eliminate certain effects. The base value 24 for interest according to this logic is problematic, for example, if the fixed asset reaches net book value zero in the year. In these cases, the base value of the interest is also zero, which would not result in any interest calculation. The sample depreciation keys (delivered by SAP) that manage interest, also use base value 22 if the net book value is to be depreciated.<br/>For base value 24, the new depreciation calculation uses the net book value for the fiscal year start, in the same way as for the ordinary depreciation. For reasons of consistency, this was standardized within the value types.</li>\n<li><strong>Defining variable interest rates<br/></strong>For information about how to define interest rates as variable, see SAP Note 1136788.</li>\n<li><strong>Digital depreciation<br/></strong>The restrictions from SAP Note 68518 on digital depreciation for a depreciation starting date during the fiscal year (point 3) is no longer given with the new depreciation calculation.<br/>Restriction for the old depreciation calculation: The distribution of the digital depreciation for a depreciation starting date during the fiscal year occurs in linear fashion. Consequence: 1/12 of the annual percentage rate is used to calculate in all periods.<br/>Due to the conversion of the calculation method from transaction-oriented to period-interval-oriented, this restriction is no longer given in the new depreciation calculation. The system determines the precise depreciation percentage rates for the individual time segments pro rata in the fiscal years.<br/>Consequence in practice: Both depreciation methods determine the same, correct annual depreciation value. Viewed from a period basis however, there is a difference between the old and the new depreciation calculation for an asset report executed during the fiscal year or the depreciation values periodically posted.</li>\n<li><strong>Treatment of subsequent acquisitions<br/></strong>SAP Note 92260 is valid for subsequent acquisitions for the old depreciation calculation. The new depreciation calculation performs reductions for the relevant calculation period. Reductions that had to be executed in a previous calculation period are no longer influenced by transactions in subsequent calculation periods.<br/>The differences between \"old\" and \"new\" depreciation calculation can be very large if you do not post the subsequent acquisitions as described in SAP Note 92260 with \"year start date\" period control or alternatively to subnumbers.</li>\n<li><strong>Changeover procedure<br/></strong>If you use the changeover methods, the system carries out a check in the \"new\" depreciation calculation for each calculation period to see if the changeover condition is fulfilled. If it is fulfilled, is changes over from the first period in the year in which the changeover condition is fulfilled. If you want a real mid-year changeover, you have to program a BAdI with a maximum segment length as described in SAP Note 1131960, point 7. The system then forms calculation segments for each period, checks the changeover condition for each segment, and the changeover occurs with the period of this changeover.<br/>Changeover methods 5 and 8 (changeover after the end of the useful life) always occur mid-year, that is, after the first period outside of the useful life, the system performs the calculation with the next phase.<br/>The \"old\" depreciation calculation does not change over mid-year under any circumstances.</li>\n<li><strong>Customer enhancements <br/></strong>For customer-specific enhancements, you can comment out four specified user exits in the old depreciation calculation. However, the new depreciation calculation does not interpret these. For customer enhancements in the new depreciation calculation, two Business Add-Ins (BAdIs) are available:<br/><br/><span>Add-in for calculation bases<br/></span>This BAdI enables you to influence the calculation of revaluations, depreciations and interest. Using the methods provided, you can:<br/>- Change the calculation sequence.<br/>- Change the calculation parameters.<br/>- Specify the length of the period intervals.<br/>- Define the changeover year and (if required) the changeover period. Furthermore, change the rounding settings.<br/><br/><span>Add-in for depreciation calculation<br/></span>This BAdI enables you to influence the calculation of revaluations, depreciations, and interest. Using the methods provided, you can: <br/>- Define individual base values.<br/>- Define individual calculation methods.<br/>- Define individual cutoff values for derived depreciation areas.<br/> <br/>For a detailed description of both Business Add-Ins in the context of general functions, existing methods, and import and export interfaces, see SAP Note 1131960. <br/>In addition, there are Business Add-Ins available for specific country-specific developments in the new depreciation calculation. For more information, contact your local SAP consultant.</li>\n<li><strong>Shutdown <br/></strong>In ECC Releases 6.0 to 6.06, the following applies: The calculation periods in which a fixed asset is shut down or terminates in shutdown are always considered in the new depreciation calculation as \"shut down\", with the result that no depreciation is calculated for these calculation periods. The old depreciation calculation, on the other hand, always considers these periods as \"not shut down\". <br/><span>Example:</span> A fixed asset is shut down on January 17 of a fiscal year (= calculation period 1). The new depreciation calculation will not calculate any depreciation for the period 1, as you have already valuated this period as \"shut down\". The old depreciation calculation valuates this period as \"not shut down\", and would calculate depreciation amounts for period 1.<br/>As of EA-FIN Release 617, a calculation period is considered to be shut down if the last day is shut down.<br/>In general, you should set the ends of the time-dependent intervals to a period end to specify exactly how the depreciation calculation should take a period into account.</li>\n<li><strong>EVL depreciation Finland<br/></strong>Note that the derived area 04 must not have any effect on segmentation in tax area 02 (EVL area). For this, proceed according to SAP Note 1161265 and set the purpose of the area to 1 for the derived area 04.</li>\n<li><strong>Update of the fields ANEP-NAFAB, ANEP-SAFAB, ANEP-ZINSB</strong><br/>As described in point 4d), the fields for the depreciations on transactions are obsolete. Therefore, the fields ANEP-NAFAB, ANEP-SAFAB and ANEP-ZINSB are not updated by the new depreciation calculation.</li>\n<li><strong>Different handling during clearing of down payments</strong><br/>In cases where a down payment in the previous year (transaction type 180) was cleared with a down payment in the current year (transaction type 181) and additional current-year acquisitions were entered in the current year, the system behaves as follows for intracompany transfers: An intercompany transfer of the current-year acquisition transactions takes place and the cleared down payments (transaction type 180/181) are balanced to zero. For this reason, no transfer posting of prior-year acquisition transactions takes place.</li>\n<li>SAP Note 720311 has introduced the new <strong>base value 12</strong>, which prevents write-ups in the current year from being included in the base value determination. In the new depreciation calculation, the base values 10 and 12 are identical. The enhanced period control (section e) for write-ups can be used to include write-ups in the new depreciation calculation. If you set up the depreciation key in such a way that the following year is the period control for write-ups and if you use the new transaction types, which use transaction type group 78, the write-ups are included only in the following year for the base value 12 as before.</li>\n<li>During an intracompany transfer (or transfer), the net book value at the start of the fiscal year and the remaining life at the start of year are always included in the old depreciation calculation. In the new depreciation calculation, segmentation can be performed to provide a more accurate calculation. Therefore, the remaining life on the asset value date and the net book value on the asset value date are used here. This may lead to differences, for example, if the parameters (for example, useful life on sender or target asset) are different.</li>\n<li>If you use the <strong>\"Total Percentage Rate\"</strong> base method, you should maintain the percentages in the multilevel method for each period. In this way, an even distribution of depreciation amounts over the individual posting periods can be achieved (-&gt; see SAP Note 2044432). </li>\n<li>If you use base method U (percentage rate from remaining useful life calculated from changeover year) in the first phase of a depreciation key, the calculation is different. The use of such a method BEFORE a conversion is not defined and should always be avoided. In the old depreciation calculation, the method is almost ignored and calculation takes place from the start year. In the new depreciation calculation, the expired useful life is recalculated according to the method. This means that there is no expired UL and no remaining useful life as of the changeover year. As a result, the system neither changes over nor depreciates.</li>\n</ol></ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>*</p>", "noteVersion": 35}, {"note": "1121965", "noteTitle": "1121965 - Documentation Enterprise Extension EA-FIN", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The documentation for the Enterprise Extension \"Financials Extension (EA_FIN)\" does not have an adequate level of detail in the original delivery.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Business Function, BF, EA-FIN, Enterprise Extension, Financials Extension</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The documentation is too unspecific.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>In the Help Portal, as of SAP ERP 6.0 SR1 Support Package 14, we provide detailed documentation for the Enterprise Extension EA_FIN:<br/>English: http://help.sap.com/saphelp_erp60_sp/helpdata/en/44/558b1099c93672e10000000a114a6b/frameset.htm<br/>German: http://help.sap.com/saphelp_erp60_sp/helpdata/de/44/558b1099c93672e10000000a114a6b/frameset.htm<br/>Japanese:<br/>http://help.sap.com/erp2005_ehp_03_sp/helpdata/ja/42/fbded750e61febe10000000a422035/frameset.htm</p></div>", "noteVersion": 6}]}], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If Simplification Item Check shows an error message customizing might be required"}, {"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Yes or No decision for ledger approach needs to be done."}, {"Activity": "Interface Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adapt or replace obsolete or changed legacy data transfer methods."}, {"Activity": "Data cleanup / archiving", "Phase": "Before conversion project", "Condition": "Optional", "Additional_Information": "Reduce number of asset accounting documents to avoid unnecessary data error corrections and to minimize conversion downtime."}, {"Activity": "Data correction", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Check and correct inconsistent data which is relevant for the conversion."}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Perform data migration to new SAP S/4HANA data model."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Inform users about changes in Asset Accounting."}, {"Activity": "Business Operations", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Execute year-end and month-end closing activities."}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Perform customizing and configuration required for preparing the data migration and for the future asset accounting design in SAP S/4HANA."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "For required custom code adaption refer to simplification item SI1: FIN_AA and SAP note 2270387."}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Decide about future ledger approach for asset accounting."}, {"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Create blueprint for required Asset Accounting configuration in SAP S/4HANA."}]}