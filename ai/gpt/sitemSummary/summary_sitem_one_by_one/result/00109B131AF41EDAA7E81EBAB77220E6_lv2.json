{"guid": "00109B131AF41EDAA7E81EBAB77220E6", "sitemId": "SI27: MasterData_BP", "sitemTitle": "S4TWL - Removal of Gender Domain Fixed Values", "note": 2928897, "noteTitle": "2928897 - S4TWL - Removal of Gender Domain Fixed Values", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Business Partner Gender Code, Nonbinary, Divers, BU_SEXID, AD_SEX, SEXKZ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Support for new gender codes in SAP Business Partner, Business Address Services, Customer and Supplier Master</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>You are looking to maintain additional gender values such as Neutral or Diverse in SAP Business Partner (BP). It was not possible to maintain gender values other than Male, Female and Unknown in a Business Partner so far. With S/4HANA On-premise 2020, in addition to the existing values Male, Female and Unknown, two new gender values are available in Business Partner master data:</p>\n<p>Nonbinary - an identity other than male or female. For example, Divers in Germany.<br/>Not specified - any identity with legal rights; but chooses not to specify the gender</p>\n<p>Further details are available in SAP Help documentation for S/4HANA 2020.</p>\n<p>To support new gender codes, the existing fixed values for gender related domains in Business Partner data model have been replaced with new value tables. These value tables will hold gender codes Male, Female and Unknown (existing from older releases) and new gender codes Nonbinary and Not specified (new from S/4HANA 2020)</p>\n<p><strong>Business Value </strong></p>\n<p>SAP Business Partner now provides a possibility to maintain gender codes other than Male and Female maintainence.</p>\n<p><strong>Business Process relation Information </strong></p>\n<p>All business processes which involve maintenance of Business Partner Master Data and in that specifically the Gender information for the Business Partner are affected. This includes primarily Business Partner maintenance via</p>\n<ul>\n<li>Transaction BP</li>\n<li>Fiori Apps for Business Partner, Customer and Supplier maintenance</li>\n<li>Supported external interfaces for like oData, SOAP, iDocs</li>\n<li>Released APIs </li>\n</ul>\n<p>SAP delivered business processes have been adjusted to make use of new value tables for gender codes instead of domain fixed values. Further sections in this Note provide guidelines on relevancy and recommended actions for customer specific coding for Business Partner maintenance.</p>\n<p><strong>Compatibility Considerations </strong></p>\n<p>To support the new gender codes, a new field GENDER has been introduced in Business Partner main table BUT000. This field will support all existing gender codes (Female, Male and Unknown) and new gender codes (Nonbinary and Not Specified). To ensure compatibility, old fields XSEXF, XSEXM and XSEXU from the table BUT000 will continue to be maintained with gender values when chosen gender value is Female, Male, or Unknown.</p>\n<p>Similarly, a new field GENDER has been introduced in the interface structures and can be used to maintain all five gender codes. Existing field SEX will continue to support gender codes Female, Male or Unknown. Further details are available in the cookbook attached to this SAP Note.</p>\n<p><strong>How to determine Relevancy</strong></p>\n<p>As Business Partner is one of the most fundamental business objects in S/4HANA and as there would invariably be Business Partner of type Person in any organization, this simplification item is relevant in most customer cases.</p>\n<p>ABAP domains listed below have been changed to support additional gender codes.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Domain</td>\n<td>Fixed Values Removed</td>\n<td>Replacement Value Table</td>\n</tr>\n<tr>\n<td>BU_SEXID</td>\n<td>Female, Male and Unknown</td>\n<td>TB995</td>\n</tr>\n<tr>\n<td>AD_SEX</td>\n<td>Female, Male and Unknown</td>\n<td>TSAD15</td>\n</tr>\n<tr>\n<td>SEXKZ</td>\n<td>Female, Male</td>\n<td>TSAD15</td>\n</tr>\n</tbody>\n</table></div>\n<p>If you have custom ABAP artefacts (screens, programs, classes etc.) for BP Maintenance referring to these domains, the changes are relevant for you.</p>\n<p><strong>Required and Recommended actions</strong></p>\n<p>Attached PDF document titled 'Cookbook_GenderDomainChanges.pdf' describes in detail the how the custom adoption has to be done for the data model changes listed above.</p>", "noteVersion": 2, "refer_note": [{"note": "2816791", "noteTitle": "2816791 - S/4HANA - New Gender Values in SAP NetWeaver Business Partner", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are looking to maintain additional gender values such as Neutral or Diverse in SAP NetWeaver Business Partner and Business Address Services. It is however not possible to maintain gender values other than Male, Female and Unknown in a Business Partner.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Gender codes, Gender values, Divers, Diverse, BU_SEXID, AD_SEX, BUS000N_CHAR-GENDER, BUT000-GENDER, BUT000-XSEXU, BUT000-XSEXF, BUT000-XSEXM, Business Address Services, TSAD3</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">SAP NetWeaver Business Partner and Business Address Services currently supports only three gender values namely Female, Male and Unknown.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In addition to the existing values Male, Female and Unknown, two new gender values are available in Business Partner and Business Address Services master data:</p>\n<p>Nonbinary - an identity other than male or female. For example, Divers in Germany.<br/>Not specified - any identity with legal rights; but chooses not to specify the gender</p>\n<p>Further details are available in SAP Help documentation for S/4HANA On-Premise Edition 2020.</p>", "noteVersion": 1}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Usages of fixed values of domains AD_SEX, BU_SEXID and SEXKZ needs to be replaced with values from check tables TSAD15 and TB995. Refer to SAP Note 2928897 for details."}, {"Activity": "Business Process Redesign", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Ensure that your business processes (also in connected systems) are prepared to handle the new gender codes properly."}]}