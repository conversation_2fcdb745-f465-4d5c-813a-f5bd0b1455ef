{"guid": "901B0E6D3ED11ED6A5D4E67DF06640C7", "sitemId": "SI25: Logistics_General", "sitemTitle": "S4TWL - Perishables Procurement", "note": 2368739, "noteTitle": "2368739 - S4TWL - Perishables Procurement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>A perishables buyer needs to have all the relevant data available in order to make a qualified decision about the perishables assortements, quantities, prices, source of supply, delivery dates and the distribution. To address this need, the central Perishables Procurement Work Center was developed. This work center supported the buyer with tools and consolidated informations that are required to manage purchasing\\distribution decisions for perishables across the company.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, perishables procurement is not available from release SAP S/4HANA 1610 till 1909. Starting with SAP S/4HANA 2020 Perishables Procurement is available again.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>There is no equivalent functionality to perishables procurement available in SAP S/4HANA 1610 till 1909.</p>\n<p>In case you reuse ABAP objects of packet WO+G in your custom code, please see attached note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if perishables procurement is used. <br/>This is the case if transactions  WDFR, WDFR2, WDFR3 are used. <br/>This also can be checked via transaction SE16N. Enter table WDFR_DISP and check whether there are any entries.</p>", "noteVersion": 4, "refer_note": [{"note": "3129707", "noteTitle": "3129707 - Transactions MCH8 dump with SYSTEM_ABAP_ACCESS_DENIED", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Transactions MCH8 dumps with SYSTEM_ABAP_ACCESS_DENIED.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">Perishables Procurement</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Perishables Procurement has been made available starting with SAP S/4HANA 1909, see note 2368739.</p>\n<p>It was forgotten the de-blocklist transaction MCH8.</p>\n<p>In general regarding block listing, please check note 2249880.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the steps provided in note 2249880 with the following values:</p>\n<ul>\n<li>Type: TRAN</li>\n<li>Name: MCH8</li>\n<li>Check the checkbox \"Allow for internal usage\"</li>\n</ul>", "noteVersion": 1, "refer_note": [{"note": "2368739", "noteTitle": "2368739 - S4TWL - Perishables Procurement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>A perishables buyer needs to have all the relevant data available in order to make a qualified decision about the perishables assortements, quantities, prices, source of supply, delivery dates and the distribution. To address this need, the central Perishables Procurement Work Center was developed. This work center supported the buyer with tools and consolidated informations that are required to manage purchasing\\distribution decisions for perishables across the company.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, perishables procurement is not available from release SAP S/4HANA 1610 till 1909. Starting with SAP S/4HANA 2020 Perishables Procurement is available again.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>There is no equivalent functionality to perishables procurement available in SAP S/4HANA 1610 till 1909.</p>\n<p>In case you reuse ABAP objects of packet WO+G in your custom code, please see attached note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if perishables procurement is used. <br/>This is the case if transactions  WDFR, WDFR2, WDFR3 are used. <br/>This also can be checked via transaction SE16N. Enter table WDFR_DISP and check whether there are any entries.</p>", "noteVersion": 4}, {"note": "2249880", "noteTitle": "2249880 - Dump SYSTEM_ABAP_ACCESS_DENIED caused through Blocklist Monitor in SAP S/4HANA on premise", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>A callable ABAP development was tried to be executed, which is registered as a so called Blocklist Item.</p>\n<p>Blocklist items are executable development objects like Transactions, Function Modules, Reports, Class Methods or Form Routines. They have been registered inside the ABAP kernel to perform a dump because</p>\n<ul>\n<li>they are not (or not yet) adjusted to the prerequisites of changed data models in S/4HANA and may create disruptive data</li>\n<li>they are not part of the Feature Scope Description and by that not licensed for use by customer</li>\n<li>they are not decoupled from the S/4HANA Code base and will be deleted at a later point in time</li>\n</ul>\n<p>Executables might be...</p>\n<p>Transactions (TRAN =&gt; Type 'T'</p>\n<p>Function Modules (FUNC) =&gt; Type 'C'</p>\n<p>Reports (PROG) =&gt; Type 'P'</p>\n<p>Class Methods (METH) =&gt; Type 'M'</p>\n<p>Form Routines (FORM) =&gt; Type 'F'</p>\n<p>They are also part of the Simplification List and Simplification Database where you'll find further information about the reason of the deactivation of the executable.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Simplification List, Simplification Database</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Pre-requisites: Ensure you are using SAP S/4HANA on premise Edition 1511 SP01 and following Support Packages (SPs) or Releases</p>\n<p>Also, please note that the blocklisted objects belong to SAP and SAP has blocklisted them (either temporarily or permanently) in SAP S/4HANA releases due to reasons mentioned above, and one among them is the fact that the functionality is not compatible with SAP S/4HANA architecture or other reasons, the functionality is no longer required and/or supported in the related application/product under SAP S/4HANA.</p>\n<p>In case an object is not set forth in the SAP S/4HANA Feature Scope Description, the customer has no license right to use the object.</p>\n<p>SAP may allow a temporarily blocklisted objects and SAP may allow its general usage by removing them from the blocklist in any future support package or – prior to the provisioning of an support package - through a dedicated note where compatibility or enablement has been downported.</p>\n<p><strong><em><span>In such cases, please do the following:</span></em></strong></p>\n<p><span>Consult with development support</span></p>\n<ul>\n<ul>\n<li>Raise a ticket on the component of the blocklisted object concerned and inquire with the development support of SAP S/4HANA whether the particular object is blocklisted and how customer may use it according to the SAP allowlist or dedicated note</li>\n<li>Based on the approval of development support via the ticket, you get a clearance to proceed according to the instructions mentioned below</li>\n</ul>\n</ul>\n<p><strong> </strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span><em>After clearence through SAP, please proceed as follows:</em></span></strong></p>\n<p>This is to be done for each system (based on System-Id SID) in one client of your choice through a system user which has the authorization S_ADMI_FCD (System Authorizations).</p>\n<ul>\n<li>Ensure that the programm 'ABLM_MODIFY_ITEMS' is on latest state as mentioned in note 2933993</li>\n<li>Start transaction SE38 and enter program 'ABLM_MODIFY_ITEMS'</li>\n<li>Based on the given information of the Dump, select via F4 the corresponding Type (TRAN, FUNC, PROG, METH, FORM)</li>\n<ul>\n<li>For TRAN and PROG enter as 'Name' the executable mentioned as 'Object' in the Dump</li>\n<li>For FUNC and METH enter as 'Name' the executable mentioned as 'Procedure' in the Dump and as 'Main' the executable mentioned as ''Object' in the Dump</li>\n</ul>\n<li>Execute (F8) the report first in 'Testmode' - this is the default and keep the Radio Button on '<strong>No Action | Check Entry</strong>'</li>\n<li>Check whether the Blocklist Entry has been found and the current status is &lt;<strong>Blocked for internal usage</strong>&gt;</li>\n<li>Go back (F3) and select the appropriate Radio Button '<strong>Allow Entry for Internal Usage</strong>'</li>\n<li>Deselect the checkbox for 'Testmode'</li>\n<li>Execute (F8) the report - this might take between 5sec and 180sec</li>\n<li>The report shall show the correct state of the executable and the general message 'Modification is effective'</li>\n</ul>\n<p>Retry your application which caused the initial dump.</p>\n<p><span><strong><em>Procedure to restore the original blocking of executables after a former clearance through SAP and its Modification:</em></strong></span></p>\n<p>This is to be done for each system (based on System-Id SID) in one client of your choice through a system user which has the authorization S_ADMI_FCD (System Authorizations).</p>\n<ul>\n<li>Start transaction SE38 and enter program 'ABLM_MODIFY_ITEMS'</li>\n<li>Based on the given information of the former modifcation, select via F4 the corresponding Type (TRAN, FUNC, PROG, METH, FORM)</li>\n<ul>\n<li>For TRAN and PROG please enter as 'Name' the executable mentioned as 'Object' in the fromer Dump</li>\n<li>For FUNC and METH please enter as 'Name' the executable mentioned as 'Procedure' in the fromer Dump and as 'Main' the executable mentioned as ''Object' in the former Dump</li>\n</ul>\n<li>Execute (F8) the report first in 'Testmode' - this is the default and keep the Radio Button on '<strong>No Action | Check Entry</strong>'</li>\n<li>Check whether the Blocklist Entry has been found and the current status is &lt;<strong>Modified to be allowed for usage by user...</strong>&gt;</li>\n<li>Go back (F3) and select the appropriate Radio Button '<strong>Restore once modified entries</strong>'</li>\n<li>Deselect the checkbox for 'Testmode'</li>\n<li>Execute (F8) the report - this might take between 5sec and 180sec</li>\n<li>The report shall show the original state of the executable and the general message 'Modification is effective'</li>\n</ul>\n<p>Retry your application - this shall now create again a dump.</p>", "noteVersion": 16}]}, {"note": "2383533", "noteTitle": "2383533 - S4TWL - Retail Deprecated Applications Relevance for Custom Code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using some Retail related business applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management some applications and the respective development objects are not available anymore. <br/>This might be relevant for customer specific coding. If customer specific coding re-used those development objects in ERP, the coding needs to be adjusted accordingly.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Adjust customer specific coding accordingly.</p>", "noteVersion": 2}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "The activity is relevant in case you reuse ABAP objects of packet WO+G in your custom code"}]}