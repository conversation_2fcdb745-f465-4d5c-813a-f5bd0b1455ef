{"guid": "901B0E6C72891ED68CB014E74523C0C9", "sitemId": "SI8: Oil_Financial-Based-netting-deprecation", "sitemTitle": "S4TWL - Financial Based netting deprecation", "note": 2328017, "noteTitle": "2328017 - S4TWL-Financial based Netting functionality in exchanges", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Customer may face errors while executing transaction O3A4(Create netting document)  and O54X(Create Selection) that refers to financial based netting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span>IS-OIL-DS, EXG, Exchanges, Financial Based Netting, Netting, Netting Selection Criteria, Netting Document, O54X, O3A4.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Movement based netting has more advantages over Financial Based Netting functionality</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Description</p>\n<p>Financial Based Netting is not available within SAP S/4HANA 1610. But you can still display and process existing Financial Based Netting documents.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The Transaction codes O54X(Create Selection) , O3A4(Create netting document) used to create Financial Based Netting are removed from SAP S/4HANA 1610.</p>\n<p>As an alternative, you should use Movement Based Netting functionality in Exchanges module.</p>\n<p>Transactions not available in SAP S/4HANA on-premise edition 1610 : O54X and O3A4</p>\n<p><strong>How to Determine Relevancy </strong></p>\n<p>This item is relevant for all IS-Oil customer</p>", "noteVersion": 2, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "decide on use of successor functionality Movement Based Netting in Exchanges module"}, {"Activity": "Implementation project required", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Implement successor functionality Movement Based Netting, if required"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Remove or replace any references to depricated ABAP objects in your custom code"}]}