{"guid": "901B0E6D3ED11ED699A91010BAB660C6", "sitemId": "SI15: Logistics_EHS - IM Workflows", "sitemTitle": "S4TWL - Simplification of Incident Management workflows", "note": 2350795, "noteTitle": "2350795 - S4TWL - Simplification of Incident Management workflows for fatality and cost collector notification", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Environment, Health, and Safety; EHS Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Renovation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In case of a fatality or when a cost collector has been added to an incident, workflows can be triggered to notify relevant people. These workflows have been re-worked.</p>\n<p><span>The following functions in Incident Management have been replaced with new functions:</span></p>\n<ul>\n<li><strong>Fatality notification</strong> workflow to inform stakeholders when a fatality was maintained in an incident</li>\n<li><strong>Inform involved </strong>workflow which allows the user to notify involved persons about an added cost collector</li>\n</ul>\n<p>The workflow templates <em>500006 – Fatality Email</em> and <em>800022 – Inform Involved</em> and the contained tasks remain the same. Only internal logic of workflow templates and tasks has been changed.</p>\n<p><strong>Business Process related information</strong></p>\n<p>There are no changes in the general business logic.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>If you have used the standard template, no action is required.</p>\n<p>If you have copied one of the above mentioned workflow templates, perform the following actions:</p>\n<ul>\n<li>Deactivate the delivered workflow template 500006 / 800022.</li>\n<li>Double check the triggering event of your copied template and also check the parameter binding of the event.</li>\n<li>If required, copy the new template and adapt it to your requirements.</li>\n</ul>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if you use Incident Management.  This is usually the case if table EHHSSD_INC_ROOT has 20 or more entries.</p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If you have copied standard workflow templates, perform actions described in SAP note 2350795"}]}