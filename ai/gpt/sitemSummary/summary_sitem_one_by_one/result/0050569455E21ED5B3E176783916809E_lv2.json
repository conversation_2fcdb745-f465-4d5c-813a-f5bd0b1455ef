{"guid": "0050569455E21ED5B3E176783916809E", "sitemId": "SI14: Logistics_PP", "sitemTitle": "S4TWL - Forecast Based Planning", "note": 2268095, "noteTitle": "2268095 - S4TWL - Forecast Based Planning", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In forecast based planning (MRP type VV), the MRP creates planned orders or purchase requisitions if total forecast demand up to a certain point of time exceeds the total firmed receipt quantity up to the same point of time. The total firmed receipt quantity includes inventory, production orders, purchase orders, firmed planned orders, and firmed purchase requisitions. Forecast based planning is a simple planning procedure which does not require managing planned independent requirements. For Forecast based planning the following things need to be considered:</p>\n<ul>\n<li>MRP live does not perform forecast based planning in HANA but sends the affected materials into the classic ABAP planning procedure. This results in a performance loss.</li>\n<li>Forecast demand and planned independent requirements are semantically very similar. Planned independent requirements can be created from forecast demand</li>\n<li>The system records all changes of planned independent requirements which facilitates reporting </li>\n</ul>\n<p><strong>Business Process related information</strong></p>\n<p>If you decide to replace forecast based planning by MRP type planning, proceed as follows:</p>\n<ul>\n<li>Step 1: Convert forecast requirements into planned independent requirements (PIR)</li>\n<li>Step 2: Change MRP type VV to one of the MRP types PD, P1, P2, P3, or P4</li>\n</ul>\n<p>Using PIRs instead of forecast demands provides several advantages in S/4:</p>\n<ul>\n<li>SAP provides  <a href=\"https://api.sap.com/api/API_PLND_INDEP_RQMT_SRV/resource\" target=\"_blank\">APIs</a> to handle PIRs.</li>\n<li>SAP provides with new FIORI-apps to handle PIRs (e.g. 'Maintain PIRs').</li>\n<li>PIRs allow for a consumption with sales demands and dependent requirements or stock transfer demands.</li>\n<li>It is possible to split PIRs.</li>\n<li>It is possible to re-organize unused PIR demands.</li>\n<li>For PIRs the history can be recorded.</li>\n</ul>\n<p>A detailed description of all actions which can be executed for Step 1 can be found in the How-To-Guide mentioned in <a href=\"/notes/2844909\" target=\"_blank\">note 2844909</a> - Data Transfer of Forecast Demands to Planned Independent Requirements (PIRs) and PIR Reorganization ( - the document focuses on cloud systems, but the corresponding classical report or transactions are named as well - ).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Forecast Based Planning is part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, Forecast Based Planning can be found under the ID 450.</p>\n<p>As Forecast Based Planning can be used until the expiry date of the compatibility pack license, it’s up to each customer to decide, when they want to migrate to the designated alternative functionality. Though the decision, at which point in time this will be done, should be taken early in advance in order to plan accordingly for the migration efforts.</p>", "noteVersion": 5, "refer_note": [], "activities": [{"Activity": "User Training", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Business Operations", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "If you decide to replace forecast-based planning by MRP type planning, proceed as follows: Step 1: Convert forecast requirements into planned independent requirements (PIR). Step 2: Change MRP type VV to one of the MRP types PD, P1, P2, P3, or P4."}]}