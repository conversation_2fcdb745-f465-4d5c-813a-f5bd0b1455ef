{"guid": "00109B1318BE1EEE9FE18CA1F8337D2E", "sitemId": "SI21: Insurance_FS-RI", "sitemTitle": "S4TWL - FS-RI - Renaming of a field in PMQ", "note": 3399310, "noteTitle": "3399310 - Transition to SAP S/4HANA - FS-RI - renaming a field in PMQ", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are performing a system conversion to SAP S/4HANA 2022 FPS02 or higher.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PM, PMQ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note is relevant to you if your source release for the system conversion is one of the following releases:</p>\n<ul>\n<li>SAP ERP</li>\n<li>SAP S/4HANA up to and including SAP S/4 HANA 2022 FPS01</li>\n<li>SAP S/4HANA Finance Releases</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In release SAP S/4HANA 2022 FPS02, the following view entries have been adjusted due to the introduction of a generic function, which leads to problems:</p>\n<ul>\n<li>View /MSG/4P_TABLEFLD</li>\n<ul>\n<li>Line with entry for <em>table name</em> /MSG/VDOESC and <em>field name</em> ESC_ROUND: Characteristic \"Round\" renamed to \"Rounding_Factor\"</li>\n</ul>\n</ul>\n<p><strong>Information related to the business process</strong></p>\n<p>The impact on business processes depends on how you use the changed objects.</p>\n<p><strong>Required and recommended action(s)</strong></p>\n<p>Check whether you are using the renamed objects specified in the object list and in this SAP Note. If so, you must replace the renamed objects.</p>\n<p>To do this, replace the objects as described in the SAP Note text.</p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Objects are renamed in SAP S/4HANA 2023. Check via ATC if the objects included in the piece list are used in custom code. If yes, you maybe must adapt the custom code."}]}