{"guid": "0090FA844D321EDCA089E0FB5080E0D8", "sitemId": "SI31: Logistics_EHS - Manage Compliance Requirement", "sitemTitle": "S4TWL - Deprecation of Manage Compliance Requirement app", "note": 3139973, "noteTitle": "3139973 - S4TWL - Deprecation of Manage Compliance Requirement app", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this SAP Note. The following SAP S/4HANA Simplification Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Upgrade, WebDynpro</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA 2022, the WebDynpro app <em>Manage Compliance Requirement</em> has been deprecated and is no longer available on the SAP Fiori launchpad. The corresponding functionalities are covered by a new SAP Fiori app.</p>\n<p><strong>Business Value</strong></p>\n<p>The successor app comes with a state-of-the-art SAP Fiori user interface and provides an improved user experience.</p>\n<p><strong>Business Process Related Information</strong></p>\n<p>You can find more information about the deprecated app and its successor app below.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Web Dynpro App/App ID              </strong></td>\n<td><strong>Deprecated App</strong></td>\n<td><strong>Successor App</strong></td>\n</tr>\n<tr>\n<td>EHENV_REQ_ENTRY_OIF</td>\n<td><em>Manage Compliance Requirement</em></td>\n<td><em>Manage Compliance Requirements - Regulations, Permits, Policies </em>(F5038)</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ul>\n<li>Make sure that you switch to the successor app as soon as possible. This need to happen latest before the next release upgrade of your SAP S/4HANA system.</li>\n<li>If you use any custom setup or exstensions, note that they cannot be migrated automatically to the SAP Fiori app. You need to recreate them with the support of your service team.</li>\n</ul>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Fiori Implementation", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "Implement and roll-out new Fiori app."}, {"Activity": "Custom Code Adaption", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "Adjust or rebuild any custom enhancements used by WebDynpro app."}, {"Activity": "Business Decision", "Phase": "Any time", "Condition": "Mandatory", "Additional_Information": "Define when to switch to new app."}]}