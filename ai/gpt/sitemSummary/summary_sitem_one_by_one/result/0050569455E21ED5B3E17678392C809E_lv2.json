{"guid": "0050569455E21ED5B3E17678392C809E", "sitemId": "SI1: Utilities_EDM1", "sitemTitle": "S4TWL - Monitoring Profile Value Import", "note": 2270502, "noteTitle": "2270502 - S4TWL - Monitoring Profile Value Import", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>SAP EDM (Energy Data Management) provides a transaction for monitoring the import of profile data from external sources into the SAP EDM data repository. The former transaction EDM1 has been replaced by the new transaction EDM1N that provides more functional features, improved enhancement capabilities and improved user interfaces.</p>\n<p>Usage of SAP EDM and specifically import of EDM profile data is indicated by entries in table EEDMIMPORTLOG_DB (checked via transaction SE16).</p>\n<p>For using the new transaction EDM1N, activation of ISU_EDM_1 is required.</p>\n<p><strong>Business Process related information</strong></p>\n<p>No change in business process</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"229\">\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"366\">\n<p>EDM1</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 3, "refer_note": [{"note": "2092500", "noteTitle": "2092500 - IS-U: Enhancement of monitoring of profile value import", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note introduces the new monitoring of the profile value import that provides new and enhanced functions compared to the existing transaction EDM1.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The prerequisite for this SAP Note is <a href=\"/notes/2081717\" target=\"_blank\">SAP Note 2081717</a> (Information: The improvements of SAP Note 2092500 are available via the relevant Support Package. If you import this Support Package, SAP Note 2081717 is also implemented.).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The new transaction for the monitoring of the profile value import (transaction EDM1N) provides new and enhanced functions compared to the existing transaction EDM1.</p>\n<p>Most notably the following:</p>\n<ul>\n<li>additional selection options</li>\n<li>user-friendly status selection</li>\n<li>saving of selection variants</li>\n<li>new fields and enhancement options for customers in the ALV list for a clear monitoring of the import documents</li>\n</ul>\n<p>You can call the transaction via the utilities menu under \"Energy Data Management -&gt; Monitoring -&gt; Profile Value Import (New)\".</p>\n<p><strong>Delivery</strong></p>\n<p>You can obtain this improvement via the relevant Support Package. Activate the business function ISU_EDM_1 and thus the switch ISU_EDM_1_MON.</p>", "noteVersion": 1, "refer_note": [{"note": "2081717", "noteTitle": "2081717 - IS-U: Interface note - BF ISU_EDM_1", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p><a href=\"/notes/2092500\" target=\"_blank\">SAP Note 2092500</a> introduces improvements in IS-U. Changes to the existing source code were required to implement this improvement.</p>\n<p>To ensure the smooth implementation of subsequent correction notes, the existing source code and the new source code have been decoupled. As a result, changes to the existing source code in these interface notes and in their relevant correction instructions are documented.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<div class=\"longtext\">\n<p>This interface note is a preparatory note that does not cause an interruption.</p>\n<p>If this SAP Note is a prerequisite for later corrections, you should implement it.<br/>The correction instructions provide information about the validity of the corrections.<br/><br/>You must also refer to the related SAP Notes that describe the functions that are based on this SAP Note.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the correction instructions or import the relevant Support Package.</p>", "noteVersion": 1}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Transaction EDM1 has been replaced by the new transaction EDM1N."}, {"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "For using the new transaction EDM1N activatie ISU_EDM_1"}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}