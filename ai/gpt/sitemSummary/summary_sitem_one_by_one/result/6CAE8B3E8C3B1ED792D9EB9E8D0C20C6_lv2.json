{"guid": "6CAE8B3E8C3B1ED792D9EB9E8D0C20C6", "sitemId": "BW134: Transfer Rules", "sitemTitle": "BW4SL - Transfer Rules", "note": 2470352, "noteTitle": "2470352 - BW4SL & BWbridgeSL - Data Flows (3.x)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the following object types related to 3.x data flows and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge:</p>\n<p>Those objects need to be replaced:</p>\n<ul>\n<li>Update Rules</li>\n<li>3.x InfoSources</li>\n<li>Transfer Rules</li>\n<li>3.x DataSources</li>\n<li>Transfer and Communication Structures</li>\n</ul>\n<p>Those objects need to be adapted:</p>\n<ul>\n<li>InfoPackages</li>\n<li>Process Chains</li>\n<li>Process Chain Variants</li>\n<li>VirtualProviders based on 3.x InfoSource (Cube Type=V, Cube Sub-type=S)</li>\n</ul>\n<p>The 3.x data flow migration tool (transaction RSMIGRATE) is therefore also not available in SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>UPDR, DUPD, ISCS, DSCS, ISTD, DSTD, ISTS, SHTR, ISMP, SHMP, ISFS, SHFS, ISIP, SHIP, RSPC, DSPC, RSPV, RSPV, CUBE, DCUB</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run the pre-check program RS_B4HANA_RC to determine which objects are available or can be converted to SAP BW/4HANA and SAP Datasphere, SAP BW bridge.</p>\n<p>See node: Supported by Transfer Tool --&gt; 3.x Migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In order to migrate the data flows to objects types compatible with SAP BW/4HANA or SAP Datasphere, SAP BW bridge, transaction RSMIGRATE can be called in order to create migration projects.</p>\n<p>However, in releases lower than SAP BW 7.3 the migration has to be performed manually. Also hierarchy DataSources and their associated transfer rules cannot be migrated automatically. See documentation links below.</p>\n<p>This migration from 3.x to 7.x data flows has to be performed <strong>before</strong> the SAP BW Transfer Cockpit can be used to convert data flows and model to objects compatible with SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\n<p><strong>Related Information</strong></p>\n<p>See the documentation for details:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td> </td>\n<td>Migrating a Data Flow</td>\n<td>Changes to the Data Flow</td>\n</tr>\n<tr>\n<td>BW 7.00</td>\n<td><a href=\"https://help.sap.com/viewer/12abd3db6c531014b524bf646cdec6ed/7.0.33/en-US/43fa2f42e8fc6befe10000000a11466f.html \" target=\"_blank\">Documentation</a></td>\n<td><a href=\"https://help.sap.com/viewer/12abd3db6c531014b524bf646cdec6ed/7.0.33/en-US/45ddba6e750d5592e10000000a1553f7.html\" target=\"_blank\">Documentation</a></td>\n</tr>\n<tr>\n<td>BW 7.01</td>\n<td><a href=\"https://help.sap.com/viewer/6f45651d6c4b1014a50f9ef0fc8df39d/7.01.18/en-US/43fa2f42e8fc6befe10000000a11466f.html\" target=\"_blank\">Documentation</a></td>\n<td><a href=\"https://help.sap.com/viewer/6f45651d6c4b1014a50f9ef0fc8df39d/7.01.18/en-US/45ddba6e750d5592e10000000a1553f7.html\" target=\"_blank\">Documentation</a></td>\n</tr>\n<tr>\n<td>BW 7.02</td>\n<td><a href=\"https://help.sap.com/viewer/ff5a1fb46c551014a931890caa648c6a/7.02.18/en-US/43fa2f42e8fc6befe10000000a11466f.html\" target=\"_blank\">Documentation</a></td>\n<td><a href=\"https://help.sap.com/viewer/ff5a1fb46c551014a931890caa648c6a/7.02.18/en-US/45ddba6e750d5592e10000000a1553f7.html\" target=\"_blank\">Documentation</a></td>\n</tr>\n<tr>\n<td>BW 7.30</td>\n<td><a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.3.17/en-US/8d6b1b58cc1744e1bce7898a50e19368.html\" target=\"_blank\">Documentation</a></td>\n<td><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.3.17/en-US/45ddba6e750d5592e10000000a1553f7.html\" target=\"_blank\">Documentation</a></td>\n</tr>\n<tr>\n<td>BW 7.31</td>\n<td><a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.31.20/en-US/8d6b1b58cc1744e1bce7898a50e19368.html\" target=\"_blank\">Documentation</a></td>\n<td><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.31.20/en-US/45ddba6e750d5592e10000000a1553f7.html\" target=\"_blank\">Documentation</a></td>\n</tr>\n<tr>\n<td>BW 7.40</td>\n<td><a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.4.17/en-US/8d6b1b58cc1744e1bce7898a50e19368.html\" target=\"_blank\">Documentation</a></td>\n<td><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.4.17/en-US/45ddba6e750d5592e10000000a1553f7.html\" target=\"_blank\">Documentation</a></td>\n</tr>\n<tr>\n<td>BW 7.50</td>\n<td><a href=\"https://help.sap.com/viewer/ccc9cdbdc6cd4eceaf1e5485b1bf8f4b/7.5.7/en-US/8d6b1b58cc1744e1bce7898a50e19368.html\" target=\"_blank\">Documentation</a></td>\n<td><a href=\"https://help.sap.com/viewer/08ad0e9c40b8480e9e0e0e35d0681530/7.5.7/en-US/45ddba6e750d5592e10000000a1553f7.html\" target=\"_blank\">Documentation</a></td>\n</tr>\n</tbody>\n</table></div>\n<p> See also <a href=\"https://archive.sap.com/documents/docs/DOC-26014\" target=\"_blank\">Data Flow Migration - Best Practice</a></p>", "noteVersion": 15, "refer_note": [{"note": "1610259", "noteTitle": "1610259 - Control of return code behavior in update rules", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As a result of the implementation of this SAP Note, the return code in update rules for releases &lt; BW 7.30 is interpreted in the same way as for the routines in transformations. This simplifies a migration of the update rules to a transformation.<br/><br/>You can activate the BW 7.30 standard system behavior by implementing this note in BW Releases 7.00, 7.01, 7.02, 7.11. This also applies to releases above BW 7.30.<br/><br/>For BW Release 7.30 and above, you can use this SAP Note to restore the old system behavior.<br/><br/>With this SAP Note, the different handling of 'RETURNCODE &lt;&gt; 0' with and without an error message in update rules can be deactivated for BW &lt; 7.30 so that error handling in transfer rules then behaves like the transformation logic.<br/><br/>In the case of a data status of this kind, a routine error is triggered and the message RSAU 727 'Routine &amp;, return code = &amp; without error message. See Note 1530791' is sent.</p>\n<p>If the 'SKIP Value' behavior is desired in BW 7.30 or above, this can also be realized by the initialization of the return parameter 'RESULT' in the transformation rule routine.</p>\n<p>As of SAP Note 2293381, RSAU 727 default error handling as per BW 7.30 or above has been adopted for the migration of update rules to a transformation. The settings of this consulting note are used for the migration so that the error handling of the corresponding transformation and transfer rule should normally behave in the same way.<br/>The system response is frozen in accordance with the current settings when the transformation is generated. Unlike for transfer rules, a later change to the settings has no effect on error handling for transformations. <br/><br/>The documentation adjustment in the data element 'RSROUTTXTLG' (description of the routine parameters) is contained in the delivery of SAP Note 1610009.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RSAU727, RSAU499, RSAU 499, UPDATE_INFOCUBE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Prerequisite: You have implemented Note 1610009 or imported the relevant BW Support Package.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The described function is activated across the entire system as follows for BW 7.00, 7.01, 7.02, and 7.11:</p>\n<ul>\n<li>Execute the program 'SAP_RSADMIN_MAINTAIN'.</li>\n</ul>\n<ul>\n<li>In the 'OBJECT' field, enter the value RSAU_RETURNCODE_NEW, and in the 'VALUE' field, enter the value X.</li>\n</ul>\n<p><br/>You can restore the old logic across the entire system as follows for BW 7.30 or above:</p>\n<ul>\n<li>Execute the program 'SAP_RSADMIN_MAINTAIN'.</li>\n</ul>\n<ul>\n<li>In the 'OBJECT' field, enter the value RSAU_RETURNCODE_OLD, and in the 'VALUE' field, enter the value X.</li>\n</ul>\n<p><br/>Note that activation must take place in all of the relevant systems.</p>\n<ul>\n<li>The change of the return code behavior takes effect after the relevant update rule is regenerated.</li>\n</ul>\n<ul>\n<li>You can force a regeneration of all update rules as follows:</li>\n</ul>\n<ul>\n<ul>\n<li>Call transaction RSSGPCLA.</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Select the program class RSAUTMPLUR (Generated Update Programs).</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Choose \"Set Status\" (Ctrl+F3).</li>\n</ul>\n</ul>\n<p>The change can be<br/>transported via the object <strong>R3TR TABU RSADMIN</strong> and the <strong>object key RSAU_RETURNCODE_NEW or RSAU_RETURNCODE_OLD</strong>.<br/><br/>Transporting this type of RSADMIN entry is at your own risk. Note particularly that completely transporting the contents of the table RSADMIN may damage the receiving systems irreparably.</p></div>", "noteVersion": 8, "refer_note": [{"note": "1610009", "noteTitle": "1610009 - Return code in update rules: Error RSAU 727", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>After you import BW 7.00 Support Package 27, BW 7.01 Support Package 10, BW 7.02 Support Package 08, error RSAU 727 may occur during loading as a result of Note 1530791.<br/><br/><br/>This note sets the handling of the return code &lt;&gt; 0 in update rules for BW Releases 7.00, 7.01, 7.02, and 7.11 back to the original behavior in accordance with the documentation.<br/><br/>With this return code handling, the creation of error requests for activated error handling was intransparent and error-prone.<br/><br/>For BW 7.30, the new system behavior described in Note 1530791 is delivered for compatibility reasons for the migration of update rules. If you also require the old behavior in BW 7.30, you can control this using consulting Note 1610259.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Error handling, RSAU727, RSAU499, RSAU 499, UPDATE_INFOCUBE<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p></p> <ul><li>SAP NetWeaver BW 7.00<br/><br/>Import Support Package 28 for SAP NetWeaver BW 7.00 (SAPKW70028) into your BW system. The Support Package is available when <b>Note 1600222</b> \"SAPBWNews NW BW 7.00 BW ABAP SP28\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.01 (SAP NW BW 7.0 Enhancement Package 1)<br/><br/>Import Support Package 11 for SAP NetWeaver BW 7.01 (SAPKW70111) into your BW system. The Support Package is available when <b>Note 1601974</b> \"SAPBWNews NW 7.01 BW ABAP SP11\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.02 (SAP NW BW 7.0 Enhancement Package 2)<br/><br/>Import Support Package 10 for SAP NetWeaver BW 7.02 (SAPKW70210) into your BW system. The Support Package is available when <b>Note 1604436</b> \"SAPBWNews NW 7.02 BW ABAP SP10\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.11<br/><br/>Import Support Package 8 for SAP NetWeaver BW 7.11 (SAPKW71108) into your BW system. The Support Package is available when <b>Note 1510977</b> \"SAPBWNews NW BW 7.11 BW ABAP SP8\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.30<br/><br/>Import Support Package 07 for SAP NetWeaver BW 7.30 (SAPKW73007) into your BW system. The Support Package is available when <b>Note 1652579</b> \"SAPBWNews NW 7.30 BW ABAP SP7\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.31 (SAP NW BW 7.0 Enhancement Package 3)<br/><br/>Import Support Package 3 for SAP NetWeaver BW 7.31 (SAPKW73103) into your BW system. The Support Package is available when <b>Note 1652580</b> \"SAPBWNews NW BW 7.31/7.03 ABAP SP3\", which describes this Support Package in more detail, is released for customers.</li></ul> <p>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 9}]}, {"note": "1052648", "noteTitle": "1052648 - Migration of transfer rules and update rules for BW7.x", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to migrate or convert transfer rules and update rules that already exist in the system into a corresponding transformation.<br/>You can use the function 'Create Transformation' in the context menu for the transfer rule or update rule in the Data Warehousing Workbench (transaction RSA1). Below, we refer to this function as 'migration'.<br/><br/>The migration tool cannot migrate scenarios of unlimited complexity. Therefore, the transformation created during migration may be incomplete or incorrect in rare cases.<br/><br/>During the migration of complex routines, for example, it may always be required to make subsequent manual adjustments.<br/><br/>Possible errors, existing restrictions and required adjustments according to the current level of knowledge are described in the solution.<br/><br/>Depending on the type and complexity of the implementation of routines, <br/>there may be additional problems that are not described in this note during migration.<br/><br/>The section 'Related notes' lists notes concerning known errors that may occur during migration. We recommend that you implement these notes if migration problems occur.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Corrections and notes concerning the migration tool do not correct transformations with errors automatically unless this is explicitly stated in a given note.<br/>If the correction or the note corrects an incorrect behavior of the migration tool, you must delete the transformation you created and perform the migration again. Alternatively, you may adjust the transformation you created manually.<br/><br/>The following describes the limitations known to date, that is, the scenarios not supported automatically:<br/><br/><br/>Rule type:</p>\n<ul>\n<li><strong>Routine:<br/></strong>In a transformation, on the entry page, a rule of the type 'routine' must contain all the fields used in the routine. The entry fields of the routine are determined by parsing the source code in the template. Parsing is currently supported only at the highest source code level. If you are using 'includes' or 'macros' in routine source code, these are not 'triggered' and the migrated routine will generally contain syntax errors.<br/> </li>\n<ul>\n<li>A routine migrated from a transfer rule is generally structured according to the following template:</li>\n</ul>\n</ul>\n<p>                    <br/>METHOD compute_&lt;target_InfoObject&gt;.<br/>...<br/>  TRAN_STRUCTURE  type _ty_s_SC_1__RULE_'n',<br/>...<br/>*  Migrated transfer rule call<br/>  MOVE-CORRESPONDING SOURCE_FIELDS to TRAN_STRUCTURE.<br/>...<br/>  Perform compute_&lt;target_field_name&gt;<br/>    USING<br/>      SOURCE_FIELDS-record<br/>      TRAN_STRUCTURE<br/>    CHANGING<br/>      RESULT<br/>      l_subrc<br/>...<br/>ENDMETHOD.<br/>           <br/>You can use 'perform compute_&lt;target_field_name&gt;' to call the original routine source code for the original transfer rule. The migration process copies this source code to the source code area '2nd part global' of the migrated transformation. Each routine contains an interface structure with a rule-specific type _ty_s_SC_1__RULE_'n'. Here, 'n' is the internal number of the rule.</p>\n<ul>\n<ul>\n<li>A routine migrated from an update rule is generally set up according to the following template:</li>\n</ul>\n</ul>\n<p>                    <br/>METHOD compute_&lt;target_InfoObject&gt;.<br/>...<br/>  COMM_STRUCTURE  type _ty_s_SC_1__RULE_'n',<br/>...<br/>* Runtime attributes<br/>    SOURCE_SYSTEM  = p_r_request-&gt;get_logsys( ).<br/>    MOVE-CORRESPONDING SOURCE_FIELDS to COMM_STRUCTURE.<br/>...<br/>*  Migrated update rule call<br/>  Perform routine_'m'<br/>  USING<br/>    COMM_STRUCTURE<br/>  CHANGING<br/>    RESULT<br/>    l_subrc<br/>    l_abort.<br/>...<br/>ENDMETHOD.<br/>           <br/>The original routine source code was called using 'perform routine_'m'. Here, 'm' is the internal number of the routine in the original update rules.<br/>The migration process copies this source code to the source code area 'c' of the migrated transformation. Each routine contains an interface structure with a rule-specific type _ty_s_SC_1__RULE_'n'. Here, 'n' is the internal number of the rule.<br/><br/>Syntax errors that occur after the migration may have different causes and can mostly be corrected by simple manual adjustments:<br/>The following types of syntax errors may occur:</p>\n<ul>\n<ul>\n<li>Runtime attributes from the BW3.x structure <strong>'G_S_MINFO'</strong> for example,<br/>'G_S_MINFO-LOGSYS' are not known       </li>\n</ul>\n</ul>\n<p><strong>Solution:</strong><br/>In BW 7.00, runtime attributes are provided for the transformation using the attribute 'P_R_REQUEST of the type 'ref' to 'if_rsbk_request_admintab_view'. You can now use the methods of the interface to determine the source system at runtime for example:<br/><br/>Data: l_sourcesystem type RSSLOGSYS.<br/><br/>l_sourcesystem = p_r_request-&gt;get_logsys( ).<br/><br/>For the relevant runtime information to be known in the migrated source code, too, the necessary runtime attributes must be added to interface of the subroutine on both sides (PERFORM/FORM). The required changes for the source system (SourceSystem), for example, are flagged with '&lt;--- insert'.<br/><br/><br/>  Perform compute_&lt;target_field_name&gt;<br/>    USING<br/>      l_sourcesystem                      '&lt;--- insert'<br/>      SOURCE_FIELDS-record<br/>      TRAN_STRUCTURE<br/>    CHANGING<br/>      RESULT<br/>      l_subrc<br/><br/><br/>FORM compute_&lt;target_fieldname&gt;<br/>  USING                                    '&lt;--- insert'<br/>    sourcesystem  type RSSLOGSYS          '&lt;--- insert'<br/>  CHANGING<br/>    RESULT        TYPE _ty_s_TG_1_full-&lt;target_field_name&gt;<br/>    RETURNCODE    LIKE sy-subrc<br/>    ABORT          LIKE sy-subrc<br/>  RAISING<br/>    cx_sy_arithmetic_error<br/>    cx_sy_conversion_error.<br/><br/></p>\n<ul>\n<ul>\n<li>Source code such as, 'l_s_errorlog-record = RECORD_NO' causes syntax errors because the field in the monitor structure cannot be filled.    In this example, the field 'RECORD_NO' is not known. </li>\n</ul>\n</ul>\n<p><strong>Solution:</strong><br/>This information is no longer required in the transformation. The relevant <strong>'RECORD'</strong> record number is automatically made available to error handling by the transformation framework. Comment out or delete this line.</p>\n<ul>\n<li><strong>Start routine:<br/></strong>In the start routine, all the fields from the source are always available.<br/>During the migration, a type '_ty_t_SC_1_full' is generated in '2nd part global'. Since Note 1325124, this type has been adjusted when the field list is changed. In the past, a change to the field list caused syntax errors of the type:</li>\n<ul>\n<li>For update rules:</li>\n</ul>\n</ul>\n<p>E: For PERFORM or CALL FUNCTION \"ROUTINE_9998\", the actual parameter \"SOURCE_PACKAGE\" is not compatible with the formal parameter \"DATA_PACKAGE\".</p>\n<ul>\n<ul>\n<li>For transfer rules:</li>\n</ul>\n</ul>\n<p>E:For PERFORM or CALL FUNCTION \"STARTROUTINE\", the actual parameter \"SOURCE_PACKAGE\" is not compatible with the formal parameter \"DATAPAK\".</p>\n<p><br/>If this error continues to occur, go to the solution section to correct it.<br/><br/><strong>Solution</strong>:<br/>This error should be corrected automatically with SAP Note 1325124. Access the incorrect start routine in change mode. The type '_ty_s_SC_1_full' is then automatically adjusted to the source structure. If this does not work, you should use the tool 'RSTRAN_MIGRATION_CHECK' (described in Note 1369395) to check and possibly repair the transformation in question. <br/>If this error still occurs, you can correct it manually as follows:<br/>Copy the field list of the type     '_ty_s_SC_1' from the 'private section' of the CLASS lcl_transform DEFINITION (you can find this by scrolling right to the top in the routine editor of the start routine) to the type '_ty_s_SC_1_full' of '2nd part global'.</p>\n<p>E: Type \"TAB_TRANSTRU\" is unknown.</p>\n<p><strong>Solution: <br/></strong>This error should be corrected automatically with SAP Note 2181745. Alternatively, a type definition:</p>\n<p>TYPES tab_transtru       type table of  transfer_structure.</p>\n<p>can be inserted after the type definition of 'transfer_structure'.</p>\n<ul>\n<li><strong>Return table:</strong></li>\n</ul>\n<p><strong>Solution</strong>:<br/>The rule type 'return table' is currently not supported in the transformation. As a workaround, you can use an end routine in the transformation to create a distribution scenario. In the case of simple scenarios, this can be mapped using multiple transformation groups if necessary. (Information about the usage of transformation groups is available in the online documentation -&gt; F1 help. )<br/>The solution with the best performance for a return table or generally for distribution scenarios is an expert routine. For additional information with regard to implementing an expert routine, see consulting Note 1227667 \"Guidelines for expert routine: Design rules\".</p>\n<ul>\n<li><strong>Usage of generated structures (for example, communication structure)</strong></li>\n</ul>\n<p>The usage of communication structures is not supported.<br/>Used data declarations that refer to a generated communication structure in the namespace '/BIC/*' within the routine frameword must be replaced by a transportable ABAP Dictionary structure or another type declaration.<br/>An internal structure definition with reference to the generated data elements of the InfoObjects is also a possible solution.<br/>However, if the routine source code area of the transformations is exited by a 'CALL FUNCTION' or 'CALL METHOD' and so on, a global TYPE definition may be required in the ABAP Dictionary.</p>\n<ul>\n<li><strong>Transfer rules for loading master data that are linked to the InfoObject '0SOURSYSTEM':</strong></li>\n</ul>\n<p>A conversion routine of the InfoObject was always executed for transfer rules, even if no rule was defined for the target InfoObject '0SOURSYSTEM'. In contrast, a transformation requires a dummy rule for technical reasons. You must manually create a rule of the type 'Constant' with the value ' ' for this.</p>\n<ul>\n<li><strong>Time distribution with reference to the factory calendar:</strong></li>\n</ul>\n<p>A time distribution with reference to the factory calendar is currently not supported in the transformation.</p>", "noteVersion": 14, "refer_note": [{"note": "1369395", "noteTitle": "1369395 - Transformations generated from migration: Repair tools", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This SAP Note contains check and repair tools for the automatic<br/>correction:</p> <ul><li>Of transformations that were generated via migration from transfer rules or update rules</li></ul> <ul><li>Are technically incomplete.</li></ul> <p><br/>See consulting note 1052648 for details.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/> </p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p><br/>    Go to the section \"Manual Post-Implement.\" for the solution.</p> <ul><li>SAP NetWeaver BI 7.00</li></ul> <p>           Import Support Package 24 for SAP NetWeaver BI 7. 00 (SAPKW70024) into your BI system. The Support Package is available when <b>SAP Note 1407598</b> \"SAPBINews NW BI 7.0 ABAP SP24\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.01 (SAP NW BI 7.0 Enhancement Package 1)</li></ul> <p>           Import Support Package 07 for SAP NetWeaver BI 7.01  (SAPKW70107) into your system. The Support Package is available when <b>SAP Note 1369294</b> \"SAPBINews NW BI 7.01 ABAP SP07\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.02 (SAP NW BI 7.0 Enhancement Package 2)</li></ul> <p>           Import Support Package 03 for SAP NetWeaver BI 7.02  (SAPKW70203) into your BI system. The Support Package is available when <b>SAP Note 1407600</b> \"SAPBINews NW BI 7.02 ABAP SP03\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.10</li></ul> <p>           Import Support Package 10 for SAP NetWeaver BI 7.10  (SAPKW71010) into your system. The Support Package is available when <b>SAP Note 1419541</b> \"SAPBINews NW BI 7.10 ABAP SP10\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.11</li></ul> <p>           Import Support Package 05 for SAP NetWeaver BI 7. 11 (SAPKW71105) into your BI system. The Support Package is available when <b>SAP Note 1392433</b> \"SAPBINews NW BI 7.11 ABAP SP05\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.20</li></ul> <p>           Import Support Package 03 for SAP NetWeaver BI 7. 20 (SAPKW72003) into your BI system. The Support Package is available when <b>SAP Note 1407599</b> \"SAPBINews NW BI 7.2 ABAP SP03\", which describes this Support Package in more detail, is released for customers.<br/>           <br/>           <br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read SAP Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 18}, {"note": "981194", "noteTitle": "981194 - Creating transformation using template in SP09/SP10", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The function for creating transformations in the transfer or update rule node terminates with a syntax error or a runtime error.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Migration of transfer rules or update rules</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Support Package 10 and the correction instructions contained in this note provide the following improvements for the function for creating transformations from an existing transfer or update rule:</p><ul><li>The start routine is converted automatically.</li></ul> <ul><li>The field list of the rule is automatically restricted to the source fields used in the routine.</li></ul> <ul><li>The conversion of a routine occurs in such a way that any fields that may be used in the routine but that are not filled cause a syntax error.</li></ul> <ul><li>For formulas, the field list of the rule is restricted to the fields used in the formula.</li></ul> <ul><li>The system no longer creates transformation groups that have an identical key.</li></ul> <p><br/>The following rule types are not automatically supported:</p> <ul><li>Return table</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>For Support Package 10, you can use the improved migration tool version after you have implemented this note.<br/>For Support Package 9, Note 986199, resets the migration tool to the old version.</p> <ul><li>SAP NetWeaver 2004s BI</li></ul> <p>           Import Support Package 12 for SAP NetWeaver 2004s BI (BI Patch 12 or SAPKW70012) into your BI system. The Support Package is available once Note 914306 \"SAPBINews BI 7.0 Support Package 12\", which describes this Support Package in more detail, has been released for customers.<br/><br/>In urgent cases, you can implement the correction instructions.<br/><br/><b>You must first implement Notes 932065, 935140, 948389, 964580 and 969846, which provide information about transaction SNOTE. Otherwise, problems and syntax errors may occur when you deimplement some notes. </b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/>Before you implement an advance correction (if one exists and you want to implement it), see Note 875986. It contains notes regarding the SAP Note Assistant. These notes prevent problems during the implementation.<br/></p></div>", "noteVersion": 9}, {"note": "1260582", "noteTitle": "1260582 - Transformations for Product corrupted in BI Content", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The Transformations from InforFources 0PRODUCT_TEXT and 0PRODUCT_ATTR have corrupted start routines. The activation from BI Content fails with an error message and the Transformations cannot be activated manually because of syntax errors in the start routines.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>The effected transformations have the technical keys \"0PRODUCT_ATTR -&gt; IOBJ 0BBP_PROD\" 053IXVX06RQCYCBQRBKRUCW268QXXOAJ and \"0PRODUCT_TEXT -&gt; IOBJ 0BBP_PROD\" 0I930KDE3B26QWZQPISNIJMWVS91IMQI.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The Transformation are migrated from corresponding update rules. Due to changes in the transformation framework some of the migrated Transformations inherited syntax errors.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>In Support Package 2 for BI Content 7.04 the problem is solved. The support package will also contain the missing Transformations for the underlying DataSources and complete the data flow. Note that InfoSource 0PRODUCT_ATTR has been declared obsolete. It is therefore recommended to use InfoSource 0PRODUCT_GENERAL_ATTR. The underlying Teransformation, which conects the InfoSOurce with the DataSource 0PRODUCT_ATTR will be part of the Support Package 2. In case you need to fix the problem prior to the availabloty of the support package, a manual correction instruction can be found in note 1052648. The note describes precisely how to remove the syntax error in the start routines.</p></div>", "noteVersion": 2}, {"note": "1345161", "noteTitle": "1345161 - Migration transfer rule/update rule: Syntax error", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>There is a syntax error in a rule of the \"Routine\" type in a transformation.<br/>The migration framework of the transfer rule or the update rule does not recognize the source fields that are used when the routine is created. After the migration, the source field list of the rule is empty.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RSTRAN 523, RSTRAN523</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/>Prerequisites: The source routine contains an INCLUDE statement that contains the actual source code.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p></p> <ul><li>SAP NetWeaver BI 7.00</li></ul> <p>           Import Support Package 22 for SAP NetWeaver BI 7. 00 (SAPKW70022) into your BI system. The Support Package is available when <b>Note 1325072 </b>\"SAPBINews NW 7.00 BI Support Package 22\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.01 (SAP NW BI 7.0 Enhancement Package 1)</li></ul> <p>           Import Support Package 05 for SAP NetWeaver BI 7. 01 (SAPKW70105) into your BI system.  The Support Package is available when <b>Note 1324445 </b>\"SAPBINews NW 7.01 BI ABAP Support Package 05\", which describes this Support Package in more detail, has been released for customers.</p> <ul><li>SAP NetWeaver BI 7.02 (SAP NW BI 7.0 Enhancement Package 2)</li></ul> <p>           Import Support Package 01 for SAP NetWeaver BI 7. 02 (SAPKW70201) into your system. The Support Package is available when <b>Note</b><b><b>1332017</b></b> \"SAPBINews NW 7.02 BI ABAP Support Package 01\", which describes this Support Package in more detail, has been released for customers.</p> <ul><li>SAP NetWeaver BI 7.10</li></ul> <p>           Import Support Package 08 for SAP NetWeaver BI 7. 10 (SAPKW71008) into your BI system. The Support Package is available when <b>Note 1260071 </b>\"SAPBINews NW 7.10 BI Support Package 8\", which describes this Support Package in more detail, has been released for customers.<br/></p> <ul><li>SAP NetWeaver BI 7.11</li></ul> <p>           Import Support Package 03 for SAP NetWeaver BI 7. 11 (SAPKW71103) into your BI system. The Support Package is available when <b>Note 1263691 </b>\"SAPBINews NW 7.11 BI Support Package 03\", which describes this Support Package in more detail, has been released for customers.<br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 1}, {"note": "1069325", "noteTitle": "1069325 - Problems occur when you create a constant rule", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you create a constant rule, problems occur. For example, the system does not change the rule type to constant, or the creation of a transformation from an update rule or from a transfer rule terminates with a short dump.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Migration, rule type, CONSTANT<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 15 for SAP NetWeaver 7.0 BI (BI Patch 15 or <b>SAPKW70015</b>) into your BI system. The Support Package is available when <b>Note 991095 </b>\"SAPBINews BI7.0 Support Package 15\", which describes this Support Package in more detail, is released for customers.<br/> <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 5}, {"note": "1736351", "noteTitle": "1736351 - InfoObject '0DF_FORCE':  Data type CHAR32 is incorrect", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The data element /BI0/OIDF_FORCE with the data type CHAR32 is assigned to the InfoObject 0DF_FORCE.<br/><br/>The data type CHAR32 of the data element is incorrect.<br/><br/>Since the InfoObject 0DF_FORCE represents the object HROBJID, the data type must be NUMC8.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>0DF_LMMIS_ATTR, ODF_LMMIS, 0DF_IS_DFS_18, 0DF_DS03</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This concerns the program design.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the relevant Support Package or implement the corrections manually as described below.</p>\n<p>Important:<br/>Implement SAP Note 2009062 as well.</p></div>", "noteVersion": 3}, {"note": "1116410", "noteTitle": "1116410 - BI7.0(SP17) Dump when editing transformations", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>A dump may occur when you call rule maintenance for transformations that are generated from an update rule.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>ASSERTION_FAILED; CL_RSTRAN_GUI_RULE; CREATE_COPY_FOR_UNDO; ASSERT 1 = 0.; cx_rstran_rule_invalid<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 17 for SAP NetWeaver 7.0 BI (BI Patch 17 or <b>SAPKW70017</b>) into your BI system. The Support Package is available when <b>Note 1106569 </b>\"SAPBINews BI7.0 Support Package 17\", which describes this Support Package in more detail, is released for customers.<br/> <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 3}, {"note": "1080640", "noteTitle": "1080640 - Migration of rule type 'Read master data' 70SP16/71SPS04", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The following problems in rule type \"Read Master Data\" are resolved:</p> <ul><ul><li>When update rules (that contain rules of type \"Read Master Data) are migrated, the system issues error mesage RSTRAN 661 (Rule according to &amp;1 (group: &amp;2, type: &amp;3) created. Error in rule details) in the log. The source fields for these rules are missing from the target transformation.</li></ul></ul> <ul><ul><li>If you choose a source characteristic for the master data in the transformation maintenance, it is not automatically entered as a source field of the rule.</li></ul></ul> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RSTRAN661, RSTRAN 661, MASTER, MASTERDATA, MASTERDATAREAD<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 16 for SAP NetWeaver 7.0 BI (BI Patch 16 or <b>SAPKW70016</b>) into your BI system. The Support Package is available when <b>Note 1074388 </b>\"SAPBINews BI7.0 Support Package 16\", which describes this Support Package in more detail, is released for customers.<br/> <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 4}, {"note": "1048423", "noteTitle": "1048423 - R7 105 during migration of update rules", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you create a transformation using an update rule as a template, the system issues message R7 105 \"The object name is not allowed to be empty\".<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 14 for SAP NetWeaver 7.0 BI (BI Patch 14 or <b>SAPKW70014</b>) into your BI system. The Support Package is available once <b>Note 991094 </b>\"SAPBINews BI 7.0 Support Package 14\", which describes this Support Package in more detail, has been released for customers.<br/> <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 1}, {"note": "1244935", "noteTitle": "1244935 - Endless loop in CL_FOBU_FORMULA->ALL_OPERANDS_GET_INTERNAL", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the migration of transfer rules or update rules, an endless loop occurs in the method 'ALL_OPERANDS_GET_INTERNAL' of the class<br/>'CL_FOBU_FORMULA'.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Migration, memory overflow, endless loop</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/>Prerequisites: Formulas are used in the transfer rules/update rules to be migrated.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver BI 7.00</li></ul> <p>           Import Support Package 20 for SAP NetWeaver BI 7. 00 (SAPKW70020) into your BI system. The Support Package is available when <b>Note 1136885 </b>\"SAPBINews NW7.01 BI Support Package 20\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.01 (SAP NW BI 7.0 Enhancement Package 1)</li></ul> <p>           Import Support Package 03 for SAP NetWeaver BI 7. 01 (SAPKW70103) into your BI system.  The Support Package is available once <b>Note 1227875 </b>\"SAPBINews NW 7.01 BI ABAP Support Package 04\", which describes this Support Package in more detail, has been released for customers.</p> <ul><li>SAP NetWeaver BI 7.10</li></ul> <p>           Import Support Package 07 for SAP NetWeaver BI 7. 10 (SAPKW71007) into your system.  The Support Package is available when <b>Note 1158395 </b>\"SAPBINews NW 7.10 BI Support Package 7\", which describes this Support Package in more detail, has been released for customers.<br/></p> <ul><li>SAP NetWeaver BI 7.11</li></ul> <p>           Import Support Package 01 for SAP NetWeaver BI 7. 11 (SAPKW71101) into your system. The Support Package is available when <b>Note 1011771</b>\"SAPBINews NW 7.11 BI Support Package 00\", which describes this Support Package in more detail, has been released for customers.<br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 1}, {"note": "1351125", "noteTitle": "1351125 - <PERSON>SE<PERSON><PERSON>_FAILED in class CL_RSTRAN_GEN_STEP->SET_CONTAINER", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The runtime error ASSERTION_FAILED occurs in the<br/>class CL_RSTRAN_GEN_STEP (method SET_CONTAINER).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Migration</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The target object of the transformation is of the type non-cumulative InfoCube.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>If the transformation with the errors was created by means of migration, you must refer to Note 1052648 and implement the manual steps and restrictions according to that note.<br/></p> <ul><li>SAP NetWeaver BI 7.00</li></ul> <p>           Import Support Package 22 for SAP NetWeaver BI 7. 00 (SAPKW70022) into your BI system. The Support Package is available when <b>Note 1325072 </b>\"SAPBINews NW 7.00 BI Support Package 22\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.01 (SAP NW BI 7.0 Enhancement Package 1)</li></ul> <p>           Import Support Package 05 for SAP NetWeaver BI 7. 01 (SAPKW70105) into your BI system.  The Support Package is available when <b>Note 1324445 </b>\"SAPBINews NW 7.01 BI ABAP Support Package 05\", which describes this Support Package in more detail, has been released for customers.</p> <ul><li>SAP NetWeaver BI 7.02 (SAP NW BI 7.0 Enhancement Package 2)</li></ul> <p>           Import Support Package 01 for SAP NetWeaver BI 7. 02 (SAPKW70201) into your system. The Support Package is available when <b>Note</b><b><b>1332017</b></b> \"SAPBINews NW 7.02 BI ABAP Support Package 01\", which describes this Support Package in more detail, has been released for customers.</p> <ul><li>SAP NetWeaver BI 7.10</li></ul> <p>           Import Support Package 08 for SAP NetWeaver BI 7. 10 (SAPKW71008) into your BI system. The Support Package is available when <b>Note 1260071 </b>\"SAPBINews NW 7.10 BI Support Package 8\", which describes this Support Package in more detail, has been released for customers.<br/></p> <ul><li>SAP NetWeaver BI 7.11</li></ul> <p>           Import Support Package 03 for SAP NetWeaver BI 7. 11 (SAPKW71103) into your BI system. The Support Package is available when <b>Note 1263691 </b>\"SAPBINews NW 7.11 BI Support Package 03\", which describes this Support Package in more detail, has been released for customers.<br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 1}, {"note": "979607", "noteTitle": "979607 - Runtime attribute: For example, source system is unavailable", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Up until now, you could not access any runtime information of the request in the start, end, expert and rule routines. This note provides attribute 'P_R_REQUEST' of type ref to 'if_rsbk_request_admintab_view' in the routine.<br/>   You can now use the methods of the interface to determine the source system at runtime for example:<br/><br/>Data: l_sourcesystem type RSSLOGSYS.<br/><br/>l_sourcesystem = p_r_request-&gt;get_logsys( ).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Start routine, end routine, expert routine, routine<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 2004s BI</li></ul> <p>           Import Support Package 10 for SAP NetWeaver 2004s BI (BI Patch 10 or <b>SAPKW70010</b>) into your BI system. The Support Package is available once <b>Note 914304</b> \"SAPBINews BI 7.0 Support Package 10\", which describes this Support Package in more detail, has been released for customers.<br/><br/>In urgent cases, you can implement the correction instructions.<br/><br/><b>You must first implement Notes 932065, 935140, 948389, 964580 and 969846, which provide information using transaction SNOTE. Otherwise, problems and syntax errors may occur when you deimplement some notes.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Packages are released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/>Before you implement an advance correction (if one exists and you want to implement it), see Note 875986. It contains notes regarding the SAP Note Assistant. These notes prevent problems during the implementation.<br/></p></div>", "noteVersion": 3}, {"note": "1364916", "noteTitle": "1364916 - Migration generates formula with incorrect 'FORMTP'", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you transport a transformation with a formula, the system issues an:</p> <ul><li>ABAP runtime error 'ASSERTION_FAILED'</li></ul> <p>           or</p> <ul><li>error message R7 105</li></ul> <p>           in the transport log of 'After_Import' processing in the following area:<br/>after-import method RS_TRFN_AFTER_IMPORT for object type(s) TRFN (activation mode)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Short dump, dump, R7105, R7 105, 105(R7)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/>Prerequisites: The transformation was generated via migration from transfer or update rules.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Note 1369395 provides a repair tool that can be used to automatically correct this metadata inconsistency.<br/></p> <ul><li>SAP NetWeaver BI 7.00</li></ul> <p>           Import Support Package 22 for SAP NetWeaver BI 7. 00 (SAPKW70022) into your BI system. The Support Package is available when <b>Note 1325072 </b>\"SAPBINews NW 7.00 BI Support Package 22\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.01 (SAP NW BI 7.0 Enhancement Package 1)</li></ul> <p>           Import Support Package 05 for SAP NetWeaver BI 7. 01 (SAPKW70105) into your BI system.  The Support Package is available when <b>Note 1324445</b>\"SAPBINews NW 7.01 BI ABAP Support Package 05\", which describes this Support Package in more detail, has been released for customers.</p> <ul><li>SAP NetWeaver BI 7.02 (SAP NW BI 7.0 Enhancement Package 2)</li></ul> <p>           Import Support Package 01 for SAP NetWeaver BI 7. 02 (SAPKW70201) into your system. The Support Package is available when <b>Note</b><b><b>1332017</b></b> \"SAPBINews NW 7.02 BI ABAP Support Package 01\", which describes this Support Package in more detail, has been released for customers.</p> <ul><li>SAP NetWeaver BI 7.10</li></ul> <p>           Import Support Package 08 for SAP NetWeaver BI 7. 10 (SAPKW71008) into your BI system. The Support Package is available when <b>Note 1260071 </b>\"SAPBINews NW 7.10 BI Support Package 8\", which describes this Support Package in more detail, has been released for customers.<br/></p> <ul><li>SAP NetWeaver BI 7.11</li></ul> <p>           Import Support Package 03 for SAP NetWeaver BI 7. 11 (SAPKW71103) into your BI system. The Support Package is available when <b>Note 1263691 </b>\"SAPBINews NW 7.11 BI Support Package 03\", which describes this Support Package in more detail, has been released for customers.<br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 6}, {"note": "1292311", "noteTitle": "1292311 - Extraction monitor probs. for transformations from migration", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><ul><li>A data transfer process (DTP) request terminates with the runtime error TSV_NEW_PAGE_ALLOCATION_FAILED<br/> or<br/>TSV_TNEW_BLOCKS_NO_ROLL_MEMORY.</li></ul> <ul><li>Data-based errors cause the DTP request to terminate with error RSBK 257. A detailed error message (stating which record caused the error and providing further useful information about how to find the cause) is lost.</li></ul><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Migration of transfer rule or update rule</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>With regard to correcting a transformation created from a migration, the restrictions described in consulting note 1052648 apply.<br/><br/>You can automatically correct the incorrect source code using a remigration, as described in Note 1052648,<br/>or using the tools provided in Note 1369395.<br/><br/>This note additionally contains the tool RS_ABAPSOURCE_COMPARE. This allows you, for example, to compare the transformation program before and after the new migration to find a possible manual adjustment.<br/>In this case, you must first copy the \"old\" transformation program with the ID \"GP*\" to \"ZZ_GP*\".<br/></p> <ul><li>SAP NetWeaver BI 7.00</li></ul> <p>           Import Support Package 21 for SAP NetWeaver BI 7. 00 (SAPKW70021) into your BI system. The Support Package is available when <b>Note 1270629 </b>\"SAPBINews NW 7.00 BI Support Package 21\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.01 (SAP NW BI 7.0 Enhancement Package 1)</li></ul> <p>           Import Support Package 04 for SAP NetWeaver BI 7. 01 (SAPKW70104) into your BI system. The Support Package is available once <b>Note 1227876</b>\"SAPBINews NW 7.01 BI ABAP Support Package 04\", which describes this Support Package in more detail, has been released for customers.</p> <ul><li>SAP NetWeaver BI 7.10</li></ul> <p>           Import Support Package 08 for SAP NetWeaver BI 7. 10 (SAPKW71008) into your BI system. The Support Package is available when <b>Note 1260071 </b>\"SAPBINews NW 7.10 BI Support Package 8\", which describes this Support Package in more detail, has been released for customers.<br/></p> <ul><li>SAP NetWeaver BI 7.11</li></ul> <p>           Import Support Package 02 for SAP NetWeaver BI 7. 11 (SAPKW71102) into your system.  The Support Package is available when <b>Note 1260072 </b>\"SAPBINews NW 7.11 BI Support Package 02\", which describes this Support Package in more detail, is released for customers.<br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 7}, {"note": "1227667", "noteTitle": "1227667 - Expert routine consulting: Design rules", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note contains a template for design rules required to create a user-defined expert routine.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The functioning of any expert routine programmed by the customer remains the responsibility of the customer. This note provides guidance to help ensure that this will work successfully.<br/><br/>Note the following design rules in particular, in order to ensure consistent runtime behavior in the case of a data-driven error situation. The default behavior of a data status that is not expected in the case of the expert routine is as follows: the data package that has already been processed is terminated, resulting in the termination of the entire request with an error status. The example below deals with the following topics:</p>\n<ul>\n<li><strong>Sending messages to the data transfer process (DTP) monitor.</strong></li>\n</ul>\n<ul>\n<li><strong>Creating reference relationships between the data records of the inbound data package SOURCE_PACKAGE and the outbound data package RESULT_PACKAGE. These are required for the functioning of the settings in DTP error handling.</strong></li>\n</ul>\n<p>In the following example, the error message XXX 900 is sent to the monitor if the value in the field 'a' is initial. The relevant data record is not transferred to RESULT_PACKAGE, and the 'skip' information is transferred to the monitor. The reference relationships between SOURCE_PACKAGE and RESULT_PACKAGE are therefore created without gaps.<br/>The following source code is <strong>not executable</strong> and is only to be used as a template.<br/><br/>METHOD expert_routine.<br/><br/><br/><br/> DATA: l_t_msg type rstr_ty_t_monitors,<br/> l_s_msg type rstmonitor.<br/><br/><br/> LOOP AT source_package ASSIGNING &lt;source_fields&gt;.<br/><br/>*-- put the message to corresponding fields of structure l_s_msg<br/>*- append l_s_msg to l_t_msg<br/><strong>*-- note: if you like to skip the inbound data record</strong><br/><strong>*- you have to set the skip flag SKIPPED in structure l_s_msg</strong><br/><strong>*- and don't append that record into the outbound RESULT_PACKAGE</strong><br/><strong>* The RECNO field in l_s_msg has to be filled by the record#</strong><br/><strong>* of the inbound data record of SOURCE_PACKAGE.</strong><br/> IF &lt;source_fields&gt;-'a' IS INITIAL.<br/><strong>*-- send message to monitor and skip record</strong><br/> l_s_msg-msgid = 'XXX'.<br/> l_s_msg-msgty = 'E'. \" Error<br/> l_s_msg-msgno = '900'.<br/> l_s_msg-msgv1, v2, v3, v4, <strong>\"set context according the definition</strong><br/> <strong>\"of the variables</strong><br/><strong>*-- source record needs to be set as erroneous and skipped</strong><br/>l_s_msg-recno = &lt;source_fields&gt;-record<br/> l_s_msg-skipped = 'X'.<br/><br/> APPEND l_s_msg TO l_t_msg.<br/><br/> CALL METHOD cl_rstran_expert_rout_srv=&gt;send_message<br/> EXPORTING<br/> i_r_log = log<br/> i_rule_context = p_curr_rule<br/> i_seg_id = \"i_sc\"<br/> <strong> \"i_sc value depends on segment id of the SOURCE_PACKAGE</strong><br/><strong> \"is only &lt;&gt; 1 in case of a segmented DataSource</strong><br/><strong> \"in this case naming of source_package is like</strong><br/><strong> \"source_package_i i = segment ID</strong><br/> CHANGING<br/> c_t_msg = l_t_msg.<br/><br/> CONTINUE. \"continue with the next source packages record<br/><br/> ELSE.<br/> CALL METHOD new_record__expert_routine<br/> EXPORTING<br/> log = log<br/> source_segid = \"i_sc\" <strong>\"i_sc see above</strong><br/> source_record = &lt;source_fields&gt;-record<br/> target_segid = \"i_tg\"<br/> <strong>\"Release 7.0x i_tg is always '1' </strong><br/><strong> \"Release 7.3+ </strong><br/> <strong> \"i_tg value depends on segment id of the RESULT_PACKAGE</strong><br/><strong> \"is only &lt;&gt; 1 in case of InfoObject with Hierarchy</strong><br/><strong> \"in this case naming of result_package is like</strong><br/><strong> \"result_package_i i = segment ID  </strong><br/> IMPORTING<br/> record_new = result_fields-record.<br/>*-- Put source_record to result_package<br/> APPEND result_fields TO result_package.<br/> ENDIF.<br/> ENDLOOP.<br/><br/>ENDMETHOD. \"expert_routine</p>\n<p>Maintain the parameters in transaction SE91 if user-defined message IDs are used (starting with Z).</p></div>", "noteVersion": 7}, {"note": "1045192", "noteTitle": "1045192 - Syntax error: 'RECORD field missing in SOURCE_FIELDS'", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><ul><li>A transformation that displays syntax errors of the form in the migration log or during the check through migration (the function to create with a template): 'Data object \"SOURCE_FIELDS\" has no component \"RECORD\"'.</li></ul> <ul><li>The 'RECORD' field (record number in the source package) is not provided in a rule of the type 'Routine' or 'Routine with Unit'.</li></ul> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 14 for SAP NetWeaver 7.0 BI (BI Patch 13 or <b>SAPKW70014</b>) into your BI system. The Support Package is available once <b>Note 991094 </b>\"SAPBINews BI 7.0 Support Package 14\", which describes this Support Package in more detail, has been released for customers.<br/><br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 5}, {"note": "1533114", "noteTitle": "1533114 - Direct migration: Routines from transfer and update rules", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>IMPORTANT: This note is only for internal use for consulting and support. CRM applications in particular want to access this direct migration. In general, this note is classified clearly as a consulting note.<br/>The function for automatic migration is implemented for customers. If customers have problems with this direct migration, they should refer to consulting. A consultant<br/>can use this note with the required know-how to correct potential problems on a consulting basis.<br/><br/>Do not set this note to \"Released for Customer\"; it is only \"Released Internally\".<br/><br/>--------<br/><br/>Up to now, the migration of transfer and update rules was carried out using the 'second global part' and the subroutines 'FORM' ... 'ENDFORM' contained in it.   This worked automatically apart from<br/>the restrictions described in Note 1052648.<br/><br/>However, the resulting source code is unclear and it contains an<br/>additional PERFORM call that is not required.<br/><br/>This note provides the direct migration of routine source code.<br/>However, direct-migrated source code usually requires manual post-implementation steps.<br/>This note contains the following in the \"Solution\" section:</p> <ul><li>How direct migration is activated</li></ul> <ul><li>Help on how direct-migrated source code can be adjusted.<br/></li></ul> <p>In particular, we point out that the restrictions with regard to routines according to Note 1052648 are also valid for the direct migration of the routine source code.<br/><br/>The content of this note corresponds to the current knowledge level<br/>with regard to the migration of routine source code.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Activating direct migration of routine source code:<br/>              Execute the program 'SAP_RSADMIN_MAINTAIN'. You now have the following setting options: <ul><li>For a package (development class), for example 'ZZZZ', activate 'DirektMigration':<br/>In this case, enter the value 'RS_ROUT_MIG/&lt;packet&gt;', for example RS_ROUT_MIG/ZZZZ for the object parameter.<br/>For the value parameter, there are the following two options: 'X' (global for this package) or the &lt;username&gt;; the setting is then valid only for one user.</li></ul> <ul><li>For one user globally In this case, enter the value RS_ROUT_MIG for the object parameter and the &lt;username&gt; for the value parameter.<br/></li></ul> <p><br/>Syntax errors in direct-migrated source code:<br/>              Usually, syntax errors occur for the direct migration of routines. Unfortunately, there is currently a cursor problem in the routine editor so that the system does not automatically go to the correct cursor position when there are syntax errors. Currently, only the following workaround is possible to find syntax errors. <ul><li>Copy the entire source code from the routine editor to transaction SE38 (editor). You can then navigate here as usual. However, when you copy back to the routine editor, ensure that you copy back only the individual code sections without the parts that cannot be changed.</li></ul> <p><br/><br/>Using <b>includes</b> in global areas:<br/>              The restrictions from ABAP objects apply: This means that includes in the 'global part' of the routine editor must be from one origin only; they can contain only declarations. Components in structures may have to be renamed since strict rules apply with regard to the names allowed in routines in ABAP objects. <p>              This restriction is valid only for the 'second global part' since this is outside of a class definition. Subroutines (FORM) or local classes can still be implemented here.<br/><br/> <p><b>Obsolete interface parameters</b>:<br/>              The direct migration parses the source code and checks it for interface parameters that are not supported by the transformation routine. If this is the case, corresponding comment lines are included in the migrated source code. <ul><li>G_S_MINFO: Replace the corresponding source code by using the attribute 'P_R_REQUEST' of the type REF TO  if_rsbk_request_admintab_view'.<br/>(In addition, see the description in Note 979607).</li></ul> <ul><li>...<br/></li></ul></p></p></p></p></p></div>", "noteVersion": 2}, {"note": "1171547", "noteTitle": "1171547 - Incomplete migration of routines to transformation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>A transformation generated from a transfer rule or update rule has the following syntax error:<br/></p> <ul><li>E:Field \"TRAN_STRUCTURE-*\" is unknown</li></ul> <ul><li>E:Field \"COMM_STRUCTURE-*\" is unknown</li></ul> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Reason: Some of the fields that are used are not recognized during the parsing of the routine source code. The migrated routine therefore contains the syntax errors described in the symptom.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>After implementing the corrections or importing the Support Package, you must regenerate the affected transformation from the source object. When doing this, read consulting note<br/>1052648 about migrating transfer rules and update rules.<br/></p> <ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 19 for SAP NetWeaver 7.0 BI (BI Patch 19 or <b>SAPKW70019</b>) into your BI system. The Support Package is available once <b>Note 1136884</b>\"SAPBINews BI 7.0 Support Package 19\", which describes this Support Package in more detail, has been released for customers.<br/></p> <ul><li>SAP NetWeaver 7.1 BI</li></ul> <p>           Import Support Package 07 for SAP NetWeaver 7.1 BI (BI Patch 07 or <b>SAPKW71007</b>) into your BI system. The Support Package is available once <b>Note 1158395 </b>\"SAPBINews BI 7.1 Support Package 7\", which describes this Support Package in more detail, has been released for customers.<br/><br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 1}, {"note": "1554150", "noteTitle": "1554150 - NW BW 7.0 (Support Package 26): Dump during migration", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the migration of update rules that fill an InfoCube and for which a routine exists for the time-reference characteristic, the following dump occurs: ASSERTION FAILED in the class \"CL_RSTRAN_TEMPLATE\", method \"CREATE_ROUTINE\".</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p></p> <ul><li>SAP NetWeaver BW 7.00<br/><br/>Import Support Package 26 for SAP NetWeaver BW 7.00 (SAPKW70026) into your BW system. The Support Package is available when <b>SAP Note 1524896</b> \"SAPBWNews NW BW 7.0 ABAP SP26\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.01 (SAP NW BW 7.0 Enhancement Package 1)<br/><br/>Import Support Package 09 for SAP NetWeaver BW 7.01 (SAPKW70109) into your BW system. The Support Package is available when Note <b>1369296</b> \"SAPBINews NW7.01 BW ABAP SP09\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.02 (SAP NW BW 7.0 Enhancement Package 2)<br/><br/>Import Support Package 07 for SAP NetWeaver BW 7.02 (SAPKW70207) into your BW system. The Support Package is available when <b>Note 1510974</b> \"Preliminary Version SAPBWNews NW BW 7.02 ABAP SP07\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.11<br/><br/>Import Support Package 07 for SAP NetWeaver BW 7.11 (SAPKW71107) into your BW system. The Support Package is available when <b>Note 1510976</b> \"Preliminary Version SAPBINews NW 7.11 BW ABAP SP7\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.30<br/><br/>Import Support Package 03 for SAP NetWeaver BW 7.30 (SAPKW73003) into your BW system. The Support Package is available when <b>Note 1538941</b> \"SAPBWNews NW 7.30 BW ABAP SP03\", which describes this Support Package in more detail, is released for customers.</li></ul> <p>           <br/>           <br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 2}, {"note": "1076291", "noteTitle": "1076291 - RECORD_NO and RECORD_ALL not filled", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The parameters RECORD_NO and RECORD_ALL are not filled in migrated transformations.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Update rule, routine, characteristic routine, key routine, migration<br/>perform read_md_person<br/>HR PA-PA<br/>0HR_PA_0<br/>ZHR_PA_0<br/>RSTRAN527<br/>RSTRAN 527<br/>RSTRAN523<br/>RSTRAN 523<br/>GP_ERR_RSTRAN_MASTER_TMPL<br/>&lt;L_RECORD_NO&gt;<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>After you implement this correction, you only have to reactivate the transformation in question to supply the missing parameters. You do not have to repeat the migration from the original update rule.<br/></p> <ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 16 for SAP NetWeaver 7.0 BI (BI Patch 16 or <b>SAPKW70016</b>) into your BI system. The Support Package is available when <b>Note 1074388</b>\"SAPBINews BI7.0 Support Package 16\", which describes this Support Package in more detail, is released for customers.<br/> <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 6}, {"note": "1236154", "noteTitle": "1236154 - 2LIS_13_VDITM Transformation internal error: 38 0CRM_PROD", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>0MATERIAL, 0CRM_PROD, 2LIS_13_VDITM, RSTRAN 000<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><b>Reason:</b></p> <ul><li>When the system performs the check (table RSTRANSTEPMASTER) during the activation, it recognizes the hierarchy property \"Time-independent\" for the InfoObject 0CRM_PROD, but the hierarchy property \"Time-dependent\" for the InfoObject 0MATERIAL.</li></ul> <ul><li>If you display the table entries (RSTRANSTEPMASTER) for the InfoObjects  (IOBJNM=0MATERIAL), you can see that a time-dependency (MPER = 3) is set. However, there should not be a value for time-independent hierarchies.</li></ul> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>The transformation can be repaired as follows:<br/><br/>1. Remove of the affected (individual) rule 0CRM_PROD.<br/>2. Activate the transformation<br/>3. Reconstruct the affected (individual) rule 0CRM_PROD.<br/>4. Activate the transformation<br/>5. Activate the DTP<br/><br/>OR<br/><br/>Use transaction SE16 to correct the table entry in the table RSTRANSTEPMASTER manually by deleting the value in the field MPER for the InfoObject 0MATERIAL (IOBJNM).<br/>Afterwards, try to activate the transformation again.<br/><br/>Additional tip:<br/>If you cannot activate a transformation because of a syntax error in the start routine, see Note 1052648.<br/><br/>1052648 Migrating transfer rules and update rules for BW7.x<br/><br/>There is a manual fix...<br/><br/> Copy the field list of the type     '_ty_s_SC_1' from the<br/> 'private section' of the CLASS lcl_transform DEFINITION (you can<br/> find this when you scroll up in the routine editor) to the type<br/> '_ty_s_SC_1_full' of '2nd part global'.<br/></p></div>", "noteVersion": 2}, {"note": "2181745", "noteTitle": "2181745 - SP35: Syntax Errors after Migrating Transfer Rule to a Transformation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You encounter a syntax error while migrating Transfer Rule which has a 'Start routine'  to a Transfromation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RSTRAN527</p>\n<p>Syntax error in Start Routine</p>\n<p>E:Type \"TAB_TRANSTRU\" is unknown.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program Error</p>\n<p>Pre-Requisite: The Transfer Rule must have a Start routine</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To solve the issue, apply the following Support Packages:</p>\n<ul>\n<li>SAP Business Warehouse (SAP BW) 7.0<br/><br/>Import Support Package 35 for SAP BW 7.00 (SAPKW70035) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2009937 </strong>with the short text \"SAPBWNews NW BW 7.0 ABAP SP35\", which describes this Support Package in more detail, is released for customers.</li>\n<li>SAP BW 7.01 (SAP BW 7.0 EHP 1)<br/><br/>Import Support Package 18 for SAP BW 7.01 (SAPKW70118) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2123573 </strong>with the short text \"SAPBINews NW7.01 BW ABAP SP18\", which describes this Support Package in more detail, is released for customers.</li>\n<li>SAP BW 7.02 (SAP BW 7.0 EHP 2)<br/><br/>Import Support Package 18 for SAP BW 7.02 (SAPKW70218) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2126275 </strong>with the short text \"Preliminary Version SAPBWNews NW BW 7.02 ABAP SP18\", which describes this Support Package in more detail, is released for customers.</li>\n<li>SAP BW 7.30<br/><br/>Import Support Package 14 for SAP BW 7.30 (SAPKW73014) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2132939 </strong>with the short text \"SAPBWNews NW7.30 BW ABAP SP14\", which describes this Support Package in more detail, is released for customers.</li>\n<li>SAP BW 7.31 (SAP BW 7.3 EHP 1)<br/><br/>Import Support Package 17 for SAP BW 7.31 (SAPKW73117) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2139356 </strong>with the short text \"Preliminary Version SAPBWNews NW BW 7.31/7.03 ABAP SP17\", which describes this Support Package in more detail, is released for customers.</li>\n<li>SAP BW 7.40<br/><br/>Import Support Package 13 for SAP BW 7.40 (SAPKW74013) into your BW system. The Support Package will be available as soon as <strong>SAP Note 2176288 </strong>with the short text \"Preliminary Version SAPBWNews NW BW 7.4 ABAP SP13\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<p><br/><br/>In urgent cases you can use the correction instructions.<br/><br/>Before you use the correction instructions, make sure that you check <strong>SAP Note 1668882</strong> for transaction SNOTE.<br/><br/>This SAP Note might already be available before the Support Package is released. In this case, however, the short text still contains the term \"preliminary version\".</p>\n<p> </p>", "noteVersion": 2}, {"note": "1235825", "noteTitle": "1235825 - Error BRAIN 381 occurs for currency translation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Error BRAIN 381 occurs when you execute a data transfer process (DTP). A currency translation in the transformation is the reason for the error.<br/><br/>This error occurs for transformations that were generated from \"Update rules\" (migration).<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>BRAIN381, BRAIN 381, 381(BRAIN),<br/>'RSW_CURRENCY_TRANSLATION', migration of update rules<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>This correction improves the F4 help for currency translation types within the transformation. The list contains only the currency translation types that are suitable for use in the respective transformation rule.<br/><br/>After you implement this correction, currency translation types that cannot be used may be deleted in the transformation. In particular, currency translation types that could be used in the update rule may not be suitable for the transformations. In this case, a new currency translation type must be created as a copy from the old currency translation type. The determination of the target currency must be specified. The setting \"Target Currency Selection with Translation\" is not allowed when using transformations.<br/><br/>The following applies when you use currency translation types in the transformation:<br/>If the target currency in the currency translation type that is converted into the key figure is defined directly as an InfoObject of the currency type, the source field list of the transformation must contain the InfoObject. If the InfoObject in the source differs from the one used in the currency translation type, the InfoObject of the source may be mapped by an assignment within the transformation rule. An assignment of the relevant InfoObject is always required in the case of a field (the DataSource as a source object for example).<br/><br/><br/></p> <ul><li>SAP NetWeaver BI 7.00</li></ul> <p>           Import Support Package 19 for SAP NetWeaver BI 7. 00 (SAPKW70019) into your system.  The Support Package is available when Note 1136884 \"SAPBINews NW7.0 BI Support Package 19\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.01 (SAP NW BI 7.0 Enhancement Package 1)</li></ul> <p>           Import Support Package 02 for SAP NetWeaver BI 7. 01 (SAPKW70102) into your system.  The Support Package is available when Note 1227874 \"SAPBINews NW7.01 BI ABAP Support Package 02\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver 7.1 BI</li></ul> <p>           Import Support Package 07 for SAP NetWeaver 7.1 BI (BI Patch 07 or <b>SAPKW71007</b>) into your BI system. The Support Package is available once <b>Note 1158395 </b>\"SAPBINews BI 7.1 Support Package 7\", which describes this Support Package in more detail, has been released for customers.<br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".</p></div>", "noteVersion": 5}, {"note": "1115010", "noteTitle": "1115010 - BI7.0(SP17): Migration of update rule incorrect", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>If an update rule is to be migrated to a transformation, the transformation has no rules and there are incorrect error messages during the check.<br/>The update rule writes to a DataStore object.<br/>You have imported Support Package 14 or higher or you have implemented Note 1043343.<br/><br/>Note: After you implement the correction instruction, you must delete the transformation and create it again from the update rule.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 17 for SAP NetWeaver 7.0 BI (BI Patch 17 or <b>SAPKW70017</b>) into your BI system. The Support Package is available when <b>Note 1106569 </b>\"SAPBINews BI7.0 Support Package 17\", which describes this Support Package in more detail, is released for customers.<br/> <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 3}, {"note": "1527023", "noteTitle": "1527023 - Technical enhancement: Routine source code; direct migration", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Up to now, the migration of transfer and update rules was carried out using the 'second global part' and the subroutines 'FORM' ... 'ENDFORM' contained in it.   After you implement this note, a 'direct migration' of the source code can be carried out in accordance with consulting Note 1533114.<br/>At present, the direct migration is released only for use in SAP Content development.<br/><br/>This note also contains improvements that relate to the 'standard' migration as described in Note 1052648.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>There is a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p></p> <ul><li>SAP NetWeaver BW 7.00<br/><br/>Import Support Package 26 for SAP NetWeaver BW 7.00 (SAPKW70026) into your BW system. The Support Package is available when <b>SAP Note 1524896</b> \"SAPBWNews NW BW 7.0 ABAP SP26\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.01 (SAP NW BW 7.0 Enhancement Package 1)<br/><br/>Import Support Package 09 for SAP NetWeaver BW 7.01 (SAPKW70109) into your BW system. The Support Package is available when Note <b>1369296</b> \"SAPBINews NW7.01 BW ABAP SP09\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.02 (SAP NW BW 7.0 Enhancement Package 2)<br/><br/>Import Support Package 07 for SAP NetWeaver BW 7.02 (SAPKW70207) into your BW system. The Support Package is available when <b>Note 1510974</b> \"Preliminary Version SAPBWNews NW BW 7.02 ABAP SP07\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.11<br/><br/>Import Support Package 07 for SAP NetWeaver BW 7.11 (SAPKW71107) into your BW system. The Support Package is available when <b>Note 1510976</b> \"Preliminary Version SAPBINews NW 7.11 BW ABAP SP7\", which describes this Support Package in more detail, is released for customers.</li></ul> <ul><li>SAP NetWeaver BW 7.30<br/><br/>Import Support Package 03 for SAP NetWeaver BW 7.30 (SAPKW73003) into your BW system. The Support Package is available when <b>Note 1538941</b> \"SAPBWNews NW 7.30 BW ABAP SP03\", which describes this Support Package in more detail, is released for customers.</li></ul> <p>           <br/>           <br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 14}, {"note": "1325124", "noteTitle": "1325124 - Migration: Syntax error in start routine", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The start routine in a transformation is syntactically incorrect after a change is made to the source field list. The system issues the following message:</p> <ul><li>RSTRAN 527 'Startroutine: Syntax error in routine'.</li></ul> <p><br/>The syntax check in the editor for the start routine reports the following error:</p> <ul><li>E: For PERFORM or CALL FUNCTION \"ROUTINE_9998\", the actual parameter \"SOURCE_PACKAGE\" is not compatible with the formal parameter \"DATA_PACKAGE\".<br/></li></ul> <p>When generating the template 'RSTRAN_SECTIONS_GEN_1', the system may issue the error</p> <ul><li>Type 'TY_S_SECTION' is unknown<br/></li></ul> <p>when generating the meta class in the program 'GP_ERR_RSSG_METACLASS'.<br/><br/>When you call a start routine in edit mode, the system may issue error</p> <ul><li>R7 105 'The object name is not allowed to be empty'.</li></ul> <p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Migration</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/>Prerequisites: The transformation was generated via migration from transfer or update rules. This error may occur for customer-specific transformations as well as transformations from SAP BI_CONT 7.03 and 7.04.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p></p> <ul><li>SAP NetWeaver BI 7.00</li></ul> <p>           Import Support Package 24 for SAP NetWeaver BI 7. 00 (SAPKW70024) into your BI system. The Support Package is available when <b>Note 1407598 </b>\"SAPBINews NW 7.00 BI Support Package 24\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.01 (SAP NW BI 7.0 Enhancement Package 1)</li></ul> <p>           Import Support Package 07 for SAP NetWeaver BI 7.01  (SAPKW70107) into your system. The Support Package is available when <b>Note 1369624 </b>\"SAPBINews NW 7.01 BI ABAP Support Package 05\", which describes this Support Package in more detail, has been released for customers.</p> <ul><li>SAP NetWeaver BW 7.02 (SAP NW BW 7.0 Enhancement Package 2)</li></ul> <p>           Import Support Package 05 for SAP NetWeaver BW 7. 02 (SAPKW70205) into your BW system. The Support Package is available when <b>Note 1450990</b> \"SAPBINews NW BI 7.02 ABAP SP05\", which describes this Support Package in more detail, is released for customers.</p> <ul><li>SAP NetWeaver BI 7.11</li></ul> <p>           Import Support Package 05 for SAP NetWeaver BI 7. 11 (SAPKW71105) into your BI system. The Support Package is available when <b>Note 1392433 </b>\"SAPBINews NW 7.11 BI Support Package 05\", which describes this Support Package in more detail, has been released for customers.<br/>           <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 18}, {"note": "1071255", "noteTitle": "1071255 - Short dump during migration after transformation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>If you try to create a transformation from an update rule or from a transfer rule, short dump ASSERTION_FAILED occurs.<br/><br/>You cannot change the record mode rule.  The system always issues the message that the rule has constant value 'initial', even though you have made other settings.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CL_RSTRAN_TEMPLATE, CREATE_CONST, RECORDMODE, 0RECORDMODE, rule type, CONSTANT, constant<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 15 for SAP NetWeaver 7.0 BI (BI Patch 15 or <b>SAPKW70015</b>) into your BI system. The Support Package is available when <b>Note 991095 </b>\"SAPBINews BI7.0 Support Package 15\", which describes this Support Package in more detail, is released for customers.<br/> <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 4}, {"note": "1046544", "noteTitle": "1046544 - Problems: Migrating w/source fields with fixed unit/currency", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Problems occur during the migration if the update rules use several source fields and if one of these fields is a key figure with a fixed unit or currency. Dumps may occur during the migration or errors may occur when you process the relevant transformation.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 14 for SAP NetWeaver 7.0 BI (BI Patch 14 or <b>SAPKW70014</b>) into your BI system. The Support Package is available once <b>Note 991094 </b>\"SAPBINews BI 7.0 Support Package 14\", which describes this Support Package in more detail, has been released for customers.<br/> <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 2}, {"note": "1052644", "noteTitle": "1052644 - Tranformation with template (migration); RSTRAN669 in log", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>Error message RSTRAN 669 appears in the log of function 'Create transformation' (migration of transfer/update rules).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RSTRAN669; RSTRAN 669; 669(RSTRAN); field does not exist in new source<br/>RSTRAN523; RSTRAN 523; 523(RSTRAN)</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/>Reason:<br/>The outbound code to be migrated contains assignments with offset '+n or length '(m)'. During the parse of the outbound code, the system does not recognize field usages of this type.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 14 for SAP NetWeaver 7.0 BI (BI Patch 14 or <b>SAPKW70014</b>) into your BI system. The Support Package is available once <b>Note 991094 </b>\"SAPBINews BI 7.0 Support Package 14\", which describes this Support Package in more detail, has been released for customers.<br/><br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 3}, {"note": "1077930", "noteTitle": "1077930 - Transformation with template: Errors RSTRAN523 and RSTRAN669", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>When you create a transformation using a template (migration) for a transfer rule or an update rule, error messages RSTRAN 523 and RSTRAN 669 are displayed in the log.<br/>The message text of RSTRAN 669 is as follows: 'Field PO_&amp;RU&amp; does not exist in new source'.<br/><br/>If you check the transformation that was generated by the migration, syntax error RSTRAN 523 occurs for rules of the routine type.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>RSTRAN523, RSTRAN669, RSTRAN 523, RSTRAN 669<br/><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a program error.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ul><li>SAP NetWeaver 7.0 BI</li></ul> <p>           Import Support Package 16 for SAP NetWeaver 7.0 BI (BI Patch 16 or <b>SAPKW70016</b>) into your BI system. The Support Package is available when <b>Note 1074388 </b>\"SAPBINews BI7.0 Support Package 16\", which describes this Support Package in more detail, is released for customers.<br/> <br/>In urgent cases, you can implement the correction instructions as an advance correction.<br/><br/><b>You must first read Note 875986, which provides information about transaction SNOTE.</b><br/><br/>To provide information in advance, the notes mentioned above may already be available before the Support Package is released. In this case, the short text of the note still contains the words \"Preliminary version\".<br/></p></div>", "noteVersion": 5}]}, {"note": "2421930", "noteTitle": "2421930 - Simplification List for SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Simplification List for SAP BW/4HANA is published as PDF document on the SAP Help Portal (<a href=\"http://help.sap.com/bw4hana20\" target=\"_blank\">http://help.sap.com/bw4hana20</a>). This SAP Note provides the Simplification List in spreadsheet format.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SIMPL, BW4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Planning to transition from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable our customers to better plan and estimate their path to SAP BW/4HANA, we have created the “Simplification List for SAP BW/4HANA”. In this list we describe in detail, on a functional level, what happens to individual object types and solution capabilities when transitioning to SAP BW/4HANA. Compared to the SAP Business Warehouse products, we have in some cases merged certain functionality with other elements or reflected it within a new solution or architecture.</p>\n<p><strong>A new, web-based UI for searching and displaying Simplification Items</strong></p>\n<p>In parallel to the PDF document and the spreadsheet available via this SAP Note, the <em>SAP Simplification Item Catalog</em> offers direct search and browse of SAP BW/4HANA Simplification Items in their current state via the SAP ONE Support Launchpad at <a href=\"https://launchpad.support.sap.com/#/sic/\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/</a>.</p>", "noteVersion": 6, "refer_note": [{"note": "2347382", "noteTitle": "2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to run or you are already running an SAP BW/4HANA system.<br/>You want to convert your SAP BW system to SAP BW/4HANA.<br/>You want to upgrade your system from SAP BW/4HANA 1.0 to SAP BW/4HANA 2.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revision, BW4, BW/4HANA, BW, NetWeaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note contains information about the following topics:</strong></p>\n<ol>\n<li>General Information about SAP BW/4HANA and the conversion to SAP BW/4HANA</li>\n<li>Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</li>\n<li>Information relevant for SAP BW/4HANA 2023</li>\n<li>Information relevant for SAP BW/4HANA 2021</li>\n<li>Information relevant for SAP BW/4HANA 2.0</li>\n<li>Information relevant for SAP BW/4HANA 1.0</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>1. General information about SAP BW/4HANA and the conversion to SAP BW/4HANA</span></strong></p>\n<p><span>High Level information about BW/4HANA: <a href=\"http://www.sap.com/bw4hana\" target=\"_blank\">http://www.sap.com/bw4hana</a></span></p>\n<p><span>See SAP Note <a href=\"/notes/2347384 \" target=\"_blank\">2347384</a> - SAP BW/4HANA 1.0 Important Notes before you start</span></p>\n<p><span>When planning your system landscape(s) for the next release of SAP BW/4HANA (not release 1.00), you may want to take the available applicaton server platforms into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> </span></p>\n<p><span>SAP changed the <a href=\"https://support.sap.com/en/my-support/knowledge-base/security-notes-news.html\" target=\"_blank\">SAP security strategy</a> to cover security corrections for Support Packages delivered during the past 24 months (formerly 18 months). BW/4HANA will not extend this period. We provide security corrections for Support Packages delivered in the last 18 months as before.</span></p>\n<p><span>See note </span><a href=\"/notes/2733740\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2733740</a> for Information/Restriction about Relase BW/4HANA 2.0</p>\n<p><strong>2. Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Productively used SAP BW/4HANA release</strong></td>\n<td><strong>Recommended minimum SAP HANA SP/revision</strong></td>\n</tr>\n<tr>\n<td>1.0</td>\n<td>Revision 122.12 or higher (HANA 1.0 SPS 12)<br/>Revision 12 or higher (HANA 2.0 SPS 1 or higher)</td>\n</tr>\n<tr>\n<td>2.0</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2021</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2023</td>\n<td>Revision 71 or higher (HANA 2.0 SPS 7 or higher)</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that SAP strongly recommends to apply the <span>latest available</span> SAP HANA revision.</p>\n<p>For details about SAP HANA Maintenance Strategy see SAP Note <em><a href=\"/notes/2021789\" target=\"_blank\">2021789</a> - SAP HANA Revision and Maintenance Strategy</em>.</p>\n<p>Please keep the update paths for Maintenance Revisions in mind. Details can be found in SAP Note <em><a href=\"/notes/1948334\" target=\"_blank\">1948334</a> - SAP HANA Database Update Paths for Maintenance Revisions</em>.</p>\n<p>Notes with regards to SAP HANA Revisions:</p>\n<ul>\n<li>HANA SPS12: SAP Note <a href=\"/notes/2298750\" target=\"_blank\">2298750</a> - SAP HANA Platform SPS 12 Release Note</li>\n</ul>\n<p><span>SAP BW/4HANA and SAP HANA 2.0: </span></p>\n<p>SAP BW/4HANA 1.00 (including SAP_BASIS 7.50) has been released for HANA 2.0. Details can be found in note 2420699.<br/>SAP BW/4HANA 2.00 (including SAP_BASIS 7.53) and newer run on HANA 2.0 only</p>\n<p><strong> </strong></p>\n<p><strong>3. </strong><strong>Information relevant for SAP BW/4HANA 2023 (planned mid of Q4/2023)</strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3365187\" target=\"_blank\">3365187 - Information/Restriction Note for Release SAP BW/4HANA 2023</a>.</p>\n<p><strong><strong>4. Information relevant for SAP BW/4HANA 2021</strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3100794\" target=\"_blank\">3100794 - Release Information/Restrictions Note for SAP BW/4HANA 2021</a>.</p>\n<p><strong><strong><strong>5. Information relevant for SAP BW/4HANA 2.0</strong></strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"https://me.sap.com/notes/2733740\" target=\"_blank\">2733740 - Release Information/Restrictions Note for SAP BW/4HANA 2.0</a></p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>See note 2733740 - Release Information/Restriction Note for SAP BW/4HANA 2.0 for restrictions in BW/4HANA 2.0</li>\n<li>Urgent! Implement note <a href=\"/notes/2770525\" target=\"_blank\" title=\"2770525  - Data Transfer Intermediate Storage (DTIS): Missing package assignment\">2770525 - Data Transfer Intermediate Storage (DTIS): Missing package assignment</a> <span><strong>before</strong></span> upgrading to BW/4HANA 2.0</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Search/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Introduction Video: <a href=\"https://youtu.be/bxH0EzAhSwQ\" target=\"_blank\">https://youtu.be/bxH0EzAhSwQ</a></li>\n</ul>\n<li><span>Upgrading BW/4HANA 1.0 to 2.0</span></li>\n<ul>\n<li>We now introduced tasklist SAP_BW4_BEFORE_UPGRADE and SAP_BW4_AFTER_UPGRADE  to support you upgrading your system. Please see note 2846537 - Taskliste for upgrade of BW/4HANA</li>\n<li>Afte the upgrade to BW/4HANA 2.0 the flag TADIR Popup f. Obj. in View RSADMINC gets initialized. Please see note 2884312 for details.</li>\n</ul>\n<li><span>ABAP Software Stack</span></li>\n<ul>\n<li>The initial Software stack for BW/4HANA 2.0 looks like this:</li>\n<ul>\n<li>SAP_BASIS 7.53 SP01</li>\n<li>SAP_ABA 7.5D SP01</li>\n<li>SAP_UI 7.53 SP02</li>\n<li>SAP_GWFND 7.53 SP01</li>\n<li>ST-PI 7.40 SP09</li>\n<li>DW4CORE 2.00 SP00</li>\n<li>UIBAS100 4.00 SP01</li>\n</ul>\n<li>This should dramatically reduces the effort for installation</li>\n<li>During the upgrade from BW/4HANA 1.0 the missing software components are installed automatically</li>\n<li></li>\n</ul>\n<li><span>BW/4HANA 2.0 SP01</span></li>\n<ul>\n<li>Please install following note on top of SP01 directly after update:</li>\n<ul>\n<li>2779827  Error starting task list SAP_BW4_LAUNCHPAD_SETUP from other task lists</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP02 </span></li>\n<ul>\n<li>SAP_UI 7.54 SP1 as alternative UI version is released. Please check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07</li>\n<li>please also take into consideration the Support Package Upgrade Strategy when running on higher UI Version described in note <strong>2618449</strong></li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP04</span></li>\n<ul>\n<li>SAP_UI 7.54 &gt;= SP2 as alternative UI version is released. lease check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07. Also take into consideration the Support Paackage Strategy when running on higher UI version described in note <strong>2618449</strong></li>\n<li>NSE for DTO --&gt; available since HANA 2.0 SP04 + limitation see HANA Note <a href=\"/notes/2771956\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2771956</a></li>\n<li>Open Hub with SDA --&gt; available since HANA 2.0 SP04 Revision 46</li>\n<li>XDA --&gt; available since HANA 2.0 Sp04 Revision 40 </li>\n<li>Geo Support --&gt; available since HANA 2.0 SP04 Revision 45</li>\n<li>Treespec Partitioning in ADSO --&gt; available since Hana 2.0 SP04 </li>\n</ul>\n</ul>\n<p><strong>6. Information relevant for SAP BW/4HANA 1.0</strong></p>\n<p>Installation/Setup</p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>Please see SAP Note<strong> </strong><a href=\"/notes/2354516\" target=\"_blank\">2354516</a> to enable client copy after installation.</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Serach/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Equivalent Support Packages<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>SAP BW 7.50 (SAP_BW)</td>\n<td>BW/4HANA 1.00(DW4CORE)</td>\n<td>BPC 11 (BPC4HANA)</td>\n<td>SAP UI 7.52 </td>\n</tr>\n<tr>\n<td>               4</td>\n<td>        Initial Installation</td>\n<td>           N.A.</td>\n<td> </td>\n</tr>\n<tr>\n<td>               5</td>\n<td>                   1</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               6</td>\n<td>                   &gt;= 2</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               7</td>\n<td>                   &gt;= 4</td>\n<td>  Initial Installation</td>\n<td> </td>\n</tr>\n<tr>\n<td>               8</td>\n<td>                   &gt;= 5</td>\n<td>             &gt;= 1</td>\n<td> </td>\n</tr>\n<tr>\n<td>               9</td>\n<td>                   &gt;= 6</td>\n<td>             &gt;= 2             </td>\n<td> </td>\n</tr>\n<tr>\n<td>             10</td>\n<td>                   &gt;= 7</td>\n<td>             &gt;= 3</td>\n<td> </td>\n</tr>\n<tr>\n<td>             11*</td>\n<td>                   &gt;=8</td>\n<td>             &gt;=4</td>\n<td>      &gt;=2</td>\n</tr>\n<tr>\n<td>             12*</td>\n<td>                   &gt;=9</td>\n<td>             &gt;=5</td>\n<td>      &gt;=3</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n</ul>\n<p><span>*) Starting with BW/4HANA 1.00 SP08 SAP_UI 7.52 is required</span></p>\n<ul>\n<li><span>BW/4HANA SP08</span></li>\n<ul>\n<li><strong>Requirements:</strong></li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP10</li>\n<li>SAP_UI <strong>7.52 SP01 -- update required</strong></li>\n</ul>\n<li><strong>For new Installs:</strong></li>\n<ul>\n<li>For your convience we created a Support Release for BW/4HANA 1.00 including the following components:</li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP 10</li>\n<li>SAP_UI 7.52 SP02</li>\n<li>DW4CORE SP08</li>\n<li>BW4CONTB SP03</li>\n</ul>\n<li>this installation is based on Software Provisioning Manager 2.0 which requires HANA 2 SP2 Revision 23 see note 2610954.</li>\n<li>Release of the installation is planned for april (CW 17)</li>\n</ul>\n<li><strong>Enhancment in Tasklist</strong></li>\n<ul>\n<li>there are some enhancements made in the tasklists with regards to the new Web UI's: Please refert to note 2351381 for details.</li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP03</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n</ul>\n<li><span>BW/4HANA SP02</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n<li>Please implement the following notes on top of SP02 to avoid issues during setup/tasklisk execution</li>\n<ul>\n<li>2410466                BW Workspace customizing: RSDDTREXADMIN_TO_RSWSPCUST dumps </li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP01</span></li>\n<ul>\n<li>Please implement the following notes on top of SP01 to avoid issues during setup/later SP update</li>\n<ul>\n<li>2385382               Add SAPFSPOR to avoid strange DUMPS during SP update</li>\n<li>2385265               Check if essential objects are active after installation</li>\n<li>2381312               Task list SAP_BW4_SETUP_SIMPLE: Error in \"Check/Generate Packages for BW ODATA Services\" step</li>\n</ul>\n<li>General Information about tasklists and their usage in BW/4HANA: 2351381 SAP BW/4HANA task lists</li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA SP00</span></li>\n<ul>\n<li>If job BI_WRITE_PROT_TO_APPLLOG is not started by the tasklist please see SAP Note <a href=\"/notes/2356498\" target=\"_blank\">2356498</a>.</li>\n<li>In SP0 the tasklist shows an error which can be ignored. See SAP Note <a href=\"/notes/2351344\" target=\"_blank\">2351344</a> for details.</li>\n</ul>\n</ul>", "noteVersion": 36}, {"note": "2383530", "noteTitle": "2383530 - Conversion from SAP BW to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the different paths to get from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP BW/4HANA, SAP BW, Migration, Conversion, Transfer, In-Place, Remote, Shell, Lifecycle Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>0 Disclaimer</strong></span></p>\n<p>Converting a SAP BW system to SAP BW/4HANA is not a simple task. There is no \"wizard\" that magically converts everything. Instead, SAP provides a well-defined process to guide you through the renovation of your data warehouse. We have developed tools to automate this renovation where applicable and feasible, but they are not built or meant to fix badly designed models or clean-up neglected systems. In any conversion there is a need for manual interaction and re-design. The amount of such manual tasks varies from customer to customer and depends on the configuration and state of the SAP BW system. For example, a conversion of newer systems that have been configured using SAP HANA-optimized objects and LSA++ from the beginning will be relatively easy. In contrast to that, a conversion of older systems that are running on non-SAP HANA databases, using out-of-date interfaces, legacy data models, and multiple redundant layers can become quite challenging.</p>\n<p>It is therefore essential that you understand the differences between SAP BW and SAP BW/4HANA and how to handle them, thoroughly analyze your existing system, estimate the complexity and duration of required tasks, properly plan all conversion activities, learn and practice to use the conversion tools, test the conversion processes ideally with a \"copy of production\", and actively manage changes to your business. It speaks for itself that good project management and skilled personnel will be required to execute a timely and efficient conversion of a complete SAP BW system. If done right, your conversion to SAP BW/4HANA will be very successful.</p>\n<p>SAP offers several way to help. To get to started run a <a href=\"https://help.sap.com/viewer/p/SAP_READINESS_CHECK\" target=\"_blank\">SAP Readiness Check</a> or book a <a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">SAP Value Assurance service </a><a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">for SAP BW/4HANA</a>.</p>\n<p>We also highly recommend the Class Room Training <strong>BW4HC</strong>. (<a href=\"https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/\" target=\"_blank\">https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/</a>?).</p>\n<p> </p>\n<p><span><strong>1 Overview</strong></span></p>\n<p><strong>1.1 What is SAP BW/4HANA</strong></p>\n<p>SAP BW/4HANA is a new, next generation data warehouse product from SAP that, like SAP S/4HANA, is optimized for the SAP HANA platform, including inheriting the high performance, simplicity, and agility of SAP HANA. SAP BW/4HANA delivers real time, enterprise wide analytics that minimize the movement of data and can connect all the data in an organization into a single, logical view, including new data types and sources.<strong><br/></strong></p>\n<p><strong>1.2 Related Information</strong></p>\n<p>Please find more information here:</p>\n<ul>\n<li>The Future of Data Warehousing - SAP BW/4HANA  <br/><a href=\"http://sap.com/bw4hana\" target=\"_blank\">http://sap.com/bw4hana</a></li>\n<li>SAP BW/4HANA Community (includes FAQ)<br/><a href=\"https://community.sap.com/topics/bw4-hana\" target=\"_blank\">https://community.sap.com/topics/bw4-hana</a><br/><a href=\"https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html</a></li>\n<li>Online Documentation<br/><a href=\"http://help.sap.com/bw4hana10\" target=\"_blank\">http://help.sap.com/bw4hana10</a></li>\n<li>Product Roadmap<br/><a href=\"https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021\" target=\"_blank\">https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021</a></li>\n<li>SAP BW/4HANA vs. SAP BW<br/><a href=\"https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html</a><br/><br/></li>\n</ul>\n<p>For information related to functionality available in SAP BW but replaced or deleted in SAP BW/4HANA please refer to the Simplification List: <br/><a href=\"https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf\" target=\"_blank\">https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 1.0: <br/><a href=\"https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2.0:</p>\n<p><a href=\"https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2021 and higher:</p>\n<p><a href=\"https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf</a></p>\n<p> </p>\n<p><span><strong>2 How to Get to BW/4HANA</strong></span></p>\n<p><strong>2.1 Paths to SAP BW/4 HANA</strong></p>\n<p><strong><em>2.1.1 New implementation or fresh start</em></strong></p>\n<p>New implementations are the best choice for customers converting from a legacy system or building a system from scratch with new data model only.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, selective transport of BW objects (optional), implement HANA-optimized data models and flows, followed by data load and quality checks.</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), system connection (SAP Source), SAP BW/4HANA ETL, file upload and/or SAP HANA EIM (legacy sources)</p>\n<p>See SAP First Guidance: Complete functional scope (CFS) for SAP BW/4HANA: <a href=\"https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html<br/></a></p>\n<p><em><strong>2.1.2 System conversion</strong></em></p>\n<p>A system conversion addresses customers who want to change their current SAP BW system into a SAP BW/4HANA system. Using the Transfer Cockpit provided by SAP, the logical systemname of the system can be kept (in-place conversion) or a new  logical systemname can be used (remote conversion resp. shell conversion).</p>\n<p>A conversion project typically follows this sequence:</p>\n<ul>\n<li><strong>Discover / Prepare Phase</strong>: check system for BW/4HANA compliance (gather information about objects and code that needs to be transferred or changed), estimate effort for the conversion project</li>\n<li><strong>Explore / Realization Phase</strong>: Transfer legacy objects into HANA-optimized counterparts, system conversion, post conversion tasks</li>\n</ul>\n<p><em>*******. In-Place conversion</em></p>\n<p>Systems running on SAP BW 7.50 powered by SAP HANA can be converted in-place keeping their logical systemname. In the realization phase of the conversion project, classic objects have to be transferred into their HANA optimized replacements using the Transfer Cockpit. This transfer can be performed scenario-by-scenario. When all classic objects have been replaced, the system conversion to SAP BW/4HANA can be triggered.</p>\n<p>SAP highly recommends to use the latest support package for SAP BW/4HANA when performing the conversion. See note https://launchpad.support.sap.com/#/notes/2347382 regarding the equivalent support packages. Please keep the RTC Dates of the different Support Packages in mind when you plan your conversion project.</p>\n<p><strong>Steps</strong>: Migrate and/or upgrade to SAP BW 7.50 powered by SAP HANA (if necessary), install SAP BW/4HANA Starter Add-On (see “Installation” below), install Transfer Cockpit, transfer data models, adjust custom coding, perform system conversion</p>\n<p>To include the Data Comparison Functionality into InPlace-TaskList, please refer to the SAP Note <a href=\"/notes/2607742\" target=\"_blank\">2607742</a>.</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-IPL</p>\n<p><strong>What to use</strong>: SAP BW/4HANA Starter Add-On, SAP BW/4HANA Transfer Cockpit, SPAM/SAINT<br/><em></em></p>\n<p><em></em><em>*******. Remote conversion</em></p>\n<p>For SAP BW systems on releases from 7.30 to 7.50 running on Any-DB, a remote conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models into the new installation and to perform a remote data transfer.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, install DMIS Add-On, transfer data models, transport custom developments (might need adjustment to work with SAP BW/4HANA)</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-RMT</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit, DMIS Add-On</p>\n<p><em>*******. Shell conversion</em></p>\n<p>For SAP BW systems on releases from 7.00 to 7.50 running on Any-DB, a Shell conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models without data into the new installation.</p>\n<p>Our recommendation for shell conversion is to use a sandbox as the sender system. The latest SPs should also be installed on this system. Depending on the version of the current system, this saves a lot of time and effort. If you still decide to import the hints, you should look at the wiki <a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\" target=\"_blank\" title=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718</a> beforehand and follow the information from the wiki when importing.</p>\n<p><em><strong>Steps</strong>: </em>Install SAP BW/4HANA, transfer data models without data using SAP BW/4HANA Transfer Cockpit, and transport custom developments (might need adjustments to work with SAP BW/4HANA).</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-SHL</p>\n<p><strong>What to use: </strong>Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit</p>\n<p><strong><em>2.1.3. Landscape transformation</em></strong></p>\n<p>Landscape transformation is for customers who want to consolidate and optimize their complex SAP BW landscape (multiple production systems) into a single SAP BW/4HANA system or who want to carve-out selected data models or flows into a global SAP BW/4HANA system.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, implement consolidated and HANA-optimized data models and flows, followed by data load and quality checks</p>\n<p><strong>What to use</strong>: Selective data transfer, SAP Landscape Transformation (SLT), SAP Data Services, SAP BW/4HANA ETL or SAP HANA EIM<br/><em></em></p>\n<p>The following sections are focusing on the system conversion.</p>\n<p> </p>\n<p><strong>2.2 Availability</strong></p>\n<p><strong><em>2.2.1 Overview</em></strong></p>\n<p>The tools required to perform an In-Place, Remote or Shell conversion are general available now.</p>\n<p><strong><em>2.2.2 Releases</em></strong></p>\n<p>SAP highly recommends to use the latest support package for a release to start a conversion project with. It will massively reduce the effort for notes implementation and we cannot guarantee that all required notes can be imported from other areas.<em> </em>Nevertheless, the Transfer Cockpit was made available via SAP Notes for the following support packages.</p>\n<p><em>******* Pre-Checks (General Available)</em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 25 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 07 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td>SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>******* In-Place conversion</em></p>\n<p>SAP BW 7.50<strong> minimal </strong>requirement SP 05 ,<strong> recommended </strong>SP 12 and higher.</p>\n<p>An in-place conversion of SAP BW <strong>7.51</strong> or higher to SAP BW/4HANA 1.0 is <strong>not</strong> possible (since SAP BW/4HANA 1.0 is based on SAP Basis <strong>7.50</strong>).</p>\n<p>Starting from Release SAP BW <strong>7.50 SP16</strong> the conversion is only to SAP BW/4HANA <strong>2.0 SP&gt;=04</strong> possible!</p>\n<p>SAP highly recommends to update the systems before starting the conversion process. Support Package Implementation is possible until B4H Mode.</p>\n<p>Other components relevant for SAP BW Systems used for InPlace Conversion</p>\n<ul>\n<li>SAP HANA 1.0 SPS 12 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong>)</li>\n<li>SAP HANA 2.0 SPS 36 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>)</li>\n<li>SAP Business Content 7.57 SP11 and higher</li>\n<li>SPAM version 72 and higher</li>\n<li>SAP UI 7.52 or lower (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong> . SAP BW/4HANA 1.0 does <strong>not</strong> support SAP_UI 7.53. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n<li>SAP UI 7.53 (Possible only by Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n</ul>\n<p> </p>\n<p><em>*******. Remote conversion </em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong>Exceptional usage with increased manual implementation effort</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* Shell conversion</em></em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release </strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong> <strong>Exceptional usage with increased manual implementation effort</strong></strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 28 and higher</td>\n<td>SP 25</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 10 and higher</td>\n<td>SP 07</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* SAP BW/4HANA required release and support package stack</em></em></p>\n<p><em><em></em></em>SAP BW/4HANA General Information see SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a></p>\n<p>SAP BW/4HANA <strong>1.0</strong></p>\n<p>For In-Place / Shell:<strong> minimal</strong> requirement SP 08, <strong>recommended</strong> - latest released SP!</p>\n<p>For Remote: <strong>minimal</strong> requirement SP 09, <strong>recommended</strong> - latest released SP!</p>\n<p>SAP BW/4HANA <strong>2.0</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 02 (released)</p>\n<p>For more information regading SAP BW/4HANA 2.0 Release Restictions see SAP Note <a href=\"/notes/2733740\" target=\"_blank\">2733740</a>.</p>\n<p>SAP BW/4HANA <strong>2021</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2021 Release Restictions see SAP Note <a href=\"/notes/3100794\" target=\"_blank\">3100794</a>.<a href=\"/notes/2733740\" target=\"_blank\"><br/></a></p>\n<p>SAP BW/4HANA <strong>2023</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2023 Release Restictions see SAP Note <a href=\"/notes/3365187\" target=\"_blank\">3365187</a>.</p>\n<p>For Conversion from SAP_BW 7.50 for HANA to SAP BW/4HANA 2023 (SAP_BASIS 7.58) please check the downloaded Upgrade Export according to SAP Note <a href=\"https://me.sap.com/notes/3454844\" target=\"_blank\">3454844</a>.</p>\n<p><span><em>2.2.2.6 Add-On's</em></span></p>\n<p><span>Please see SAP Note <a href=\"/notes/2189708\" target=\"_blank\">2189708</a> regarding usage of Add-Ons in </span><span>SAP BW/4HANA.</span></p>\n<p> </p>\n<p><em></em><em><strong>2.2.3 Installation</strong></em></p>\n<p><em>2.2.3.1 SAP BW/4HANA Starter Add-On</em></p>\n<p>For the In-Place Conversion, the installation of the SAP BW/4HANA Starter Add-on is required. Please follow the instruction of the Conversion Guide.</p>\n<p>For Shell or Remote Conversion, the SAP BW/4HANA Starter Add-On is not required.</p>\n<p><em>2.2.3.2 SAP BW/4HANA Transfer Cockpit</em></p>\n<p>To install the Transfer Cockpit for a transfer of scenarios (in-place, remote, and shell conversion), a set of SAP Notes needs to be applied to the corresponding systems. Please use the SAP BW Note Analyzer to analyze the system and to install the prerequisites. Create the SAP BW Note Analyzer program in each system for which notes have to be applied. Depending on your use case, the following systems needs to be processed:</p>\n<ul>\n<li><strong>SAP BW</strong> (in-place conversion or sending system for a remote/shell conversion)</li>\n<li><strong>Source systems</strong> connected to SAP BW (in-place and remote/shell conversion)</li>\n<li><strong>SAP BW/4HANA</strong> (post conversoin for in-place; receiving system for a remote/shell conversion)</li>\n</ul>\n<p>Run the SAP BW Note Analyzer in each system with the corresponding XML file.</p>\n<p><em>2.2.3.3 SAP BW Note Analyzer</em></p>\n<p>For a detailed documentation of the SAP BW Note Analyzer, please see <a href=\"https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf\" target=\"_blank\">https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf</a></p>\n<p>Attached to that note you’ll find a text file “Z_SAP_BW_NOTE_ANALYZER.txt”. Save the file on your computer and open it in an editor.</p>\n<p>In the corresponding system, use transaction SE38 to create a report called “Z_SAP_BW_NOTE_ANALYZER”. Copy and paste the content of the above-mentioned text file into the report and activate it.</p>\n<p>Attached to that note, you’ll find several XML files. Save those files that are related to your use-case:</p>\n<p>Please find more detailed informations under:</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874</a>.</p>\n<p>You want to run the pre-checks in the discovery phase (RS_B4HANA_CONVERSION_CONTROL in SAP BW 7.0x and RS_B4HANA_RC from SAP BW 7.30 to 7.50):</p>\n<ul>\n<li>For SAP BW 7.0x, use <em>SAP_BW4HANA_<strong>Pre_Checks</strong>_[last_update].xml</em></li>\n<li>For SAP BW from 7.30 to 7.50, use XML<em> SAP_BW4HANA_<strong>Readiness_Check</strong>_<em>[last_update]</em>.xml</em> from note 2575059. </li>\n</ul>\n<p>You want to transfer scenarios and perform a system conversion in the realization phase:</p>\n<ul>\n<li>For SAP BW 7.50 (all cases),</li>\n<ul>\n<li>if you start now with the installation of the tool, use <em>SAP_BW4HANA_Conversion_<strong>SAP_BW_750</strong>_[last_update].xml </em></li>\n<li>if you have already installed most of the notes before and only want the latest updates, continue to use <em>SAP_BW4HANA_<strong>In-place_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>For all systems connected to SAP BW as a data load source system, use <em><strong>Source_System</strong>_for_SAP_BW4HANA_[last_update].xml</em></li>\n<li>Remote Transfer</li>\n<ul>\n<li>For SAP BW from 7.30 to 7.40 acting as sending system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>Shell Transfer</li>\n<ul>\n<li>For SAP BW from 7.00 to 7.40 acting as sending system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n</ul>\n<p>You have successfully converted the system (In-Place) to SAP BW/4HANA (see section 2.3.2.3 Post-Conversion phase)</p>\n<ul>\n<li>Use <em>SAP_BW4HANA_<strong>In-place_Conversion_(After_System_Conversion)</strong>_[last_update].xml</em></li>\n</ul>\n<p>Execute the SAP BW Note Analyzer in each relevant system. Click on the icon “Load XML file” and chose the system specific XML file.</p>\n<p>Select radio button “Check implementation state against information in XML file” and check checkbox “Download needed SAP Notes”. Select radio button “In background”.</p>\n<p>Run the report. A background process will be started which downloads the required notes.</p>\n<p>When the process finished successfully, execute the report again with a de-selected checkbox “Download needed SAP Notes”.</p>\n<p>A list of notes with its implementation status will be shown. You can now install the missing notes by clicking on the corresponding icon in the list.</p>\n<p><em>2.2.3.4 Transport-Enabled Correction Instructions (TCIs)</em></p>\n<p>When installing the SAP BW/4HANA Transfer Cockpit using the SAP BW Note Analyzer, you will be prompted for SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>, <a href=\"/notes/2371120\" target=\"_blank\">2371120</a>, and <a href=\"/notes/2735300\" target=\"_blank\">2735300</a> to install a TCI.</p>\n<p>For more information about TCI, see SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>.<a href=\"/notes/2358953\" target=\"_blank\"><br/></a></p>\n<p><em>******* DMIS Add-On</em></p>\n<p>For a remote conversion, the DMIS Add-On needs to be installed in SAP BW (sending system) and SAP BW/4HANA (receiving system). Follow the instructions in SAP Note <a href=\"/notes/2513088\" target=\"_blank\">2513088</a>.</p>\n<p> </p>\n<p><strong>2.3 Conversion Process</strong></p>\n<p>For a detailed description of the conversion process, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide</a>. In additional we attached a excel file, \"<em><strong>SAP BW4HANA Conversion - Steps to consider.xlsx</strong></em>\", as a kind of check list/project plan.</p>\n<p>List of required authorization objects for conversion, see attachment \"<em><strong>Authorization_Objects_List_Conversion_2383530.xls</strong></em>\".</p>\n<p>Frequencly ask questions are collected in note <a href=\"/notes/2930058\" target=\"_blank\">2930058</a>.</p>\n<p>Each object can only be converted once. However, it can be collected again if it is used in another scenario. For example, an IOBJ that has been converted can be used in different objects such as ADSO, DSO, CUBE. Due to consistency, these IOBJs are also collected several times, but the metadata can only be transferred once.</p>\n<p><strong>2.4 New Features</strong></p>\n<p>Under link, you can find new developed features.</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910</a>.</p>\n<p>The wiki is updated regularly.</p>", "noteVersion": 420}]}]}