{"guid": "00109B13199E1EDBAFA7C6EB41EBE109", "sitemId": "SI6: HR_SRA004 Simplified Model", "sitemTitle": "S4TWL - Travel Management SRA004 Simplified Model", "note": 3062197, "noteTitle": "3062197 - \"S4TWL - HR_SRA004 Simplified Model", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\"> You are doing a system conversion to SAP S/4HANA On-Premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Compatibility Scope, System Conversion, Upgrade</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note is relevant for you when your start release for the system conversion is an ERP release or below S/4 HANA 2021.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The first version of the Fiori application \"My Travel Request\" aka MTR, corresponding to the Fiori ID F0409, was developped some years ago in Business Suite Technology Stack. It was available for ERP release ECC6.0 until ECC6.0 EHP8 and S/4 HANA releases until 2020.</p>\n<p>A second version of this FIORI application \"My Travel Requests Version 2\" (MTR V2) with Fiori ID F0409A, is also available since ECC6.0 EHP8. This second application is much more powerfull in term of functionalities and technology.</p>\n<p>When you plan a conversion to S/4HANA, there is no reason to keep the first version as the landscape includes by default the second application.</p>\n<p>For more information about Travel Management in S/4HANA please read the following note: 2976262 - S4TWL - General Travel Management approach within SAP S/4HANA.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Implications: Customer roles (menus and authorizations) need to be adjusted in order to display the new Fiori application in the SAP Fiori Launchpad.</p>\n<p>All information about Gateway Service and configuration can be found in the SAP Fiori Application library <a href=\"https://fioriappslibrary.hana.ondemand.com/sap/fix/externalViewer/#/detail/Apps('F0409A')/S18OP\" target=\"_blank\">here </a></p>\n<p>The old Gateway Service can simply be switch off.</p>\n<p>There is no data conversion or impact as the change impacts only on the UI.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>If you are using the ODATA Service SRA004_SRV then this simplification item is relevant for you. <strong><br/></strong></p>\n<p>The new ODATA Service TRV_MTR_SRV is available and must be registered and activated on the front-end server.</p>\n<p><strong>Check - How to Determine Relevancy</strong></p>\n<p>Check if the existing Odata  Service SRA004_SRV is activated on the Front End Server. If yes perform the simplification Process, the new Odata Service should be TRV_MTR_SRV Now and you should deactivate the obsolete OData Service SRA004_SRV.</p>\n<p>You can use the transaction /IWFND/MAINT_SERVICE, one entry can be found with external service name “SRA004_SRV”.</p>", "noteVersion": 6, "refer_note": [{"note": "2707544", "noteTitle": "2707544 - Release Information Note for My Travel Requests (Version 2) - (UI Layer)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This is the collective SAP Note corresponding to the SP 00 of UITRV001 300 of the Fiori application My Travel Requests (Version 2).</p>\n<p>My Travel Requests underwent a complete redesign. In general, it now supports all functionalities found in WD ABAP Travel Management applications. (In particular instances, a modification-free enhancement/adaptation of the UI may be necessary.)</p>\n<p>Key features:</p>\n<p>- delegation (acting on-behalf-of)</p>\n<p>- support of all cost objects</p>\n<p>- additional destinations</p>\n<p>- pre-defined addresses</p>\n<p>- travel services</p>\n<p>- advances</p>\n<p>- files, notes, links, business documents (ArchiveLink) as attachment types on the trip level</p>\n<p>- integration to Travel Planning.</p>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FIN_TR_CRE_V2; UI_TRV_REQUEST_CREATE; F0409A</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><span>My Travel Requests (Version 2) was developed due to technological advances regarding SAP UI5 and new design guidelines. Functional enhancements are based on customer requests submitted via a Customer Connection project (https://influence.sap.com/sap/ino/#/campaign/112). </span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please apply the initial SP of the component UITRV001 300.</p>", "noteVersion": 1, "refer_note": [{"note": "2693032", "noteTitle": "2693032 - Fiori My Travel Requests (Version 2) - Extensibility concept", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the Fiori app \"My Travel Requests (Version 2)\", you want to extend the standard app to <span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\" verdana',sans-serif;=\"\">fulfill</span> your own <span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\" verdana',sans-serif;=\"\">requirements</span>. Read this KBA to understand the different possibilities to extend the app on Backend or UI Level.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Environment\">Environment</h3>\n<p>Business Suite</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\n<p>The standard app did not allow you to fulfill your specific business <span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\" verdana',sans-serif;=\"\">requirement</span>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Cause\">Cause</h3>\n<p>You want to extend the app \"My Travel Requests (Version 2)\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Resolution\">Resolution</h3>\n<h3 data-toc-skip=\"\">Odata Layer </h3>\n<p>Business Add-Ins (BAdIs)</p>\n<p class=\"p\">The following Business Add-In is available for extensibility purposes:</p>\n<ul class=\"ul\" id=\"loioe01e4e40efd64c3fbcccf47ea81e44b5__ul_whq_yfr_c5\">\n<li class=\"li\">\n<p class=\"p\"><strong>PAOC_MTR_BADI</strong></p>\n<div class=\"p\">You can use this BAdI to change the standard behavior of the OData Class CL_TRV_MTR_DPC_EXT.</div>\n</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" class=\"table\" frame=\"border\" id=\"loioe01e4e40efd64c3fbcccf47ea81e44b5__table_o1m_3tf_v2b\" rules=\"all\" summary=\"\"><colgroup></colgroup>\n<thead class=\"thead\">\n<tr class=\"row\"><th class=\"entry\" id=\"d2569e60\">\n<p class=\"p\">Method Name</p>\n</th><th class=\"entry\" id=\"d2569e66\">\n<p class=\"p\">Description</p>\n</th></tr>\n</thead>\n<tbody class=\"tbody\">\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">CHANGE_ADVANCES_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity details of advance</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">CHANGE_ADVANCES_ENTITY_SET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of advances</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_REQUEST_CANCEL</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Cancel travel request</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">CONTACT_GET_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity details of contact</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">CONTACT_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of contact</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_REQUEST_DUPLICATE</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Duplicate the travel request</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">EXIT_APPLICATION</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Exit the application</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_REQUEST_SAVE</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Save the travel request</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_REQUEST_GET_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity details of the travel request</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_REQUEST_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of the travel request</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">USERPROFILE_GET_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity details of the user profile</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">USERPROFILE_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of the user profile</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">CURRENCY_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of currencies</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">ADVANCE_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of advance</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">COST_ASSIGNMENT_GET_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity details of cost assignment</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">ESTIMATEDCOSTCAT_GET_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity details of estimated cost categories</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">ESTIMATEDCOSTCAT_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of estimated cost categories</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">COST_ASSIGNMENT_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of cost assignment</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">DESTINATION_GET_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity details of destination</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">DESTINATION_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of of destination</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_SERVICE_GET_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity details of travel service</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_SERVICE_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of travel service</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">COMMENT_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of comments</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">ATTACHMENT_GET_ENTITYSET</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get entity list of attachments</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_SERVICE_UPDATE_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Update travel service entity</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_SERVICE_CREATE_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Create travel service entity</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_SERVICE_DELETE_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Delete travel service entity</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e60\">\n<p class=\"p\">TRAVEL_PLANS_GET_ENTITY</p>\n</td>\n<td class=\"entry\" headers=\"d2569e66\">\n<p class=\"p\">Get travel plan entity</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Extension Includes</p>\n<p class=\"p\">In addition, the following extension includes are available:</p>\n<div class=\"table-wrapper tablenoborder\">\n<div class=\"table-responsive\"><table border=\"1\" class=\"table\" frame=\"border\" id=\"loioe01e4e40efd64c3fbcccf47ea81e44b5__table_iss_fjr_c5\" rules=\"all\" summary=\"\"><colgroup></colgroup>\n<thead class=\"thead\">\n<tr class=\"row\"><th class=\"entry\" id=\"d2569e553\">\n<p class=\"p\">Extension Include</p>\n</th><th class=\"entry\" id=\"d2569e559\">\n<p class=\"p\">Use</p>\n</th></tr>\n</thead>\n<tbody class=\"tbody\">\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">\n<p class=\"p\">MTR_S_ADVANCE_INCL</p>\n</td>\n<td class=\"entry\" headers=\"d2569e559\">\n<p class=\"p\">Dummy structure for Advances</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">\n<p class=\"p\">MTR_S_ATTACHMENT_INCL</p>\n</td>\n<td class=\"entry\" headers=\"d2569e559\">\n<p class=\"p\">Dummy structure for Attachment entity</p>\n</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_COMMENT_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy Structure for Comment entity (Extensibility)</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_COMMUNITY_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Community structure for extensibility concept</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_CONTACT_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy Structure for Contact entity</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_COST_ASSIGNMENT_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy Structure for cost Assignment (Extensibility)</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_CURRENCY_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy Structure for currencies</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_DESTINATION_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Destination structure for extensibility concept</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_DOCUMENT_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy structure for Request document</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_ESTIMATED_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Estimated Cost structure for extensibility concept</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_OTHER_SERVICE_STATUS</td>\n<td class=\"entry\" headers=\"d2569e559\">Other Service Status</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_PRINT_PREVIEW_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy structure for Print Preview</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_SERVICE_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy Structure for Service Request Entity (Extensibility)</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_TRAVEL_PLAN_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy structure for Travel Plan Entity (Extensibility)</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_TRAVEL_REQUEST_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy Structure for Travel Request Entity (Extensibility)</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_TRAVEL_SERVICE_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy Structure for Travel Service Extensibility concept</td>\n</tr>\n<tr class=\"row\">\n<td class=\"entry\" headers=\"d2569e553\">MTR_S_USER_PROFILE_INCL</td>\n<td class=\"entry\" headers=\"d2569e559\">Dummy Structure for User Profile Entity (Extensibility)</td>\n</tr>\n</tbody>\n</table></div>\n</div>\n<h3 data-toc-skip=\"\"><strong>OData definition and UI</strong></h3>\n<p>SAP FIORI application « My travel Requests (Version 2)” was developed using smart controls.</p>\n<p>Smart controls are based on ODATA annotations already part of metadata model.</p>\n<p>(GW service: TRV_MTR_SRV)</p>\n<p>Depending on your preferences and expertise, 2 options are available to enhance OData annotations:</p>\n<ul>\n<li>Customers may use Annotation Modeler to extend/overwrite standard annotations in an SAP Web IDE project.</li>\n<li>Using enhancement framework, the ABAP classes could be enhanced to add a Pre/Post/Overwrite Exit to the standard methods. (Classes: CL_TRV_MTR_ANNOTATION_HELPER and CL_TRV_ANNOTATION_HELPER)</li>\n</ul>\n<p>That will allow you to add/remove fields, sections and actions for smart tables and object pages.</p>\n<p>Data fields displayed in My Travel Requests (Version 2) depend on OData service data model and semantic annotations. As a consumer it is possible to influence data content and display by modifying annotations.</p>\n<p>Following the list of possible changes:</p>\n<ul>\n<li>Fields displayed in the List Page filter bar</li>\n<li>Entity columns to be displayed</li>\n<li>Entity fields displayed in the Detail Page header</li>\n<li>Sections to be displayed in the Detail Page</li>\n<li>Entity create action list button in the Detail Page</li>\n<li>Entity delete action button in the Detail Page (enabled by default)</li>\n<li>Inline edition in the Detail Page entity list</li>\n<li>Action button in the Detail Page entity list</li>\n<li>Entity field side effects</li>\n</ul>\n<p><strong>Frontend controller extension:</strong></p>\n<p>\"My Travel Requests (Version 2)\" supports the extension of a base controller by merging the delivered standard controller with a custom controller.</p>\n<p>Extension must be added from application descriptor file in section “sap.ui5.extends.extensions”.</p>\n<p>Please note you can find an example of controller extension in \"My Travel Requests (Version 2)\" application.</p>\n<p><strong>Frontend view extension:</strong></p>\n<p>\"My Travel Requests (Version 2)\" app supports view extensions by using extension points to insert, replace or modify custom content (views or fragments).</p>\n<p>Extension must be added from app descriptor file in section “sap.ui5.extends.extensions”.</p>\n<p>Please note you can find an example of view extension in My Travel Requests version 2 app.</p>\n<p>Please refer to the following UI extension point list to know which UI section can be extended.</p>\n<p><strong>List of UI extension point:</strong></p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Copy.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>topCopyDialog</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Copy.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>belowCopyDialogInfo</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Copy.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>belowCopyDialogForm</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>TripBreak.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>topTripBreakDialog</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>TripBreak.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>belowTripBreakDialogInfo</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>TripBreak.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>belowTripBreakDialogForm</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|TravelRequests|AdvanceSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|TravelRequests|CostAssignmentSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|TravelRequests|DestinationSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|TravelRequests|EstimatedCostSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|TravelRequests|GeneralInfoCollection</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|TravelRequests|TravelPlanId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|TravelRequests|TravelServiceSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|TravelServices|TravelServiceDetails</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|CostAssignments|CostAssignmentDetails</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>BeforeFacet|Destinations|DestinationDetails</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|CostAssignments|CostAssignmentDetails</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|Destinations|DestinationDetails</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|TravelRequests|AdvanceSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|TravelRequests|CostAssignmentSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|TravelRequests|DestinationSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|TravelRequests|EstimatedCostSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|TravelRequests|GeneralInfoCollection</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|TravelRequests|TravelPlanId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|TravelRequests|TravelServiceSetId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>ReplaceFacet|TravelServices|TravelServiceDetails</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>SmartForm.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>SmartFormExtension|CostAssignments|CostAssignmentDetails</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>SmartForm.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>SmartFormExtension|Destinations|DestinationDetails</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>SmartForm.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>SmartFormExtension|TravelRequests|General2Info</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>SmartForm.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>SmartFormExtension|TravelRequests|General3Info</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>SmartForm.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>SmartFormExtension|TravelRequests|GeneralInfo</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>SmartForm.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>SmartFormExtension|TravelRequests|TravelPlanId</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"164\">\n<p>SmartForm.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"396\">\n<p>SmartFormExtension|TravelServices|TravelServicesDetails</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>For the \"AfterFacet\" extension points, you have to put your own name instead of the generic term {FacetName}.</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|CostAssignments|CostAssignmentDetails|{FacetName}</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|Destinations|DestinationDetails|{FacetName}</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|TravelRequests|AdvanceSetId|{FacetName}</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|TravelRequests|CostAssignmentSetId|{FacetName}</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|TravelRequests|DestinationSetId|{FacetName}</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|TravelRequests|EstimatedCostSetId|{FacetName}</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|TravelRequests|GeneralInfoCollection|{FacetName}</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|TravelRequests|TravelPlanId|{FacetName}</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|TravelRequests|TravelServiceSetId|{FacetName}</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" width=\"156\">\n<p>Sections.fragment.xml</p>\n</td>\n<td nowrap=\"nowrap\" width=\"404\">\n<p>AfterFacet|TravelServices|TravelServiceDetails|{FacetName}</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"See Also\">See Also</h3>\n<p>For more information about extension includes, see the extensibility documentation for the respective SAP NetWeaver release on your front-end server at <a class=\"xref\" href=\"http://help.sap.com/fiori_implementation\" target=\"_blank\" title=\"http://help.sap.com/fiori_implementation\">http://help.sap.com/fiori_implementation</a></p>\n<p class=\"p\">Extensibility Information for SAP Fiori</p>\n<p class=\"p\">Extending the UI Layer</p>\n<p class=\"p\">UI Extensibility Workflow</p>\n<p class=\"p\">Checking the SAP-Enabled Extension Options</p>\n<p class=\"p\">Extension Includes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Keywords\">Keywords</h3>\n<p>Extensibility, OData, SAP UI5</p>", "noteVersion": 5}]}, {"note": "2503938", "noteTitle": "2503938 - Release Information NOTE for My Travel Requests (Version 2) - (ODATA layer)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This is the collective note for the OData Layer (Backend) corresponding to the SAP FIORI application \"My Travel Requests V2\" (aka Version 2).</p>\n<p>In order to find corresponding SP of EA-HRGXX for your release, please check the special chapter Support Packages &amp; Patches.</p>\n<p><strong>EA-HRGXX 608 Minimum SP 59</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FIORI, MTR V2, TRV_MTR_SRV</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Advance development</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the corresponding support package.</p>\n<p>With the transactional app \"My Travel Requests {Version 2), you can manage your own travel requests in real time using your desktop or mobile device. <br/>The application allows you to create a new  or copy  an existing  travel request - including additional destinations, cost assignment, estimated costs, advances, travel services and travel planning. <br/>Therefore you can also:<br/>• Submit travel requests – including attachments  - to your organizational manager<br/>• Browse existing travel requests in detail<br/>• Edit existing travel requests and resubmit<br/>• Delete existing travel requests</p>\n<p> For further details about the full features of MTR Version 2, please refer to the UI Release Information Note <strong><span ar-sa;\"=\"\" arial',sans-serif;=\"\" background:=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" white;=\"\">2707544</span></strong></p>", "noteVersion": 3, "refer_note": [{"note": "2567218", "noteTitle": "2567218 - Role Consumption for Odata Service TRV_MTR_SRV", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using FIORI launchpad to run FiORI applications.</p>\n<p>This note contains an example (template) for the definition of the PFCG role for the Odata Service consumption TRV_MTR_SRV.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PFCG Role, TRV_MTR, SU22</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>It is recommended to define a dedicated role for the consumption of the Odata service for the application \"My Travel Requests (Version 2)\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Launch transaction PFCG,</p>\n<p>Create your own role based for Odata service TRV_MTR_SRV.</p>\n<p>For example you can create the role SAP_TRV_MTR.</p>\n<p>Description : Role for Odata service consumption TRV_MTR_SRV</p>\n<p>Menu -&gt; Role Menu create new entry</p>\n<p>Program ID: R3TR</p>\n<p>Object Type: IWSV</p>\n<p>Object Name: -&gt; pick the service name and service number from the list corresponding to the MTR application.</p>\n<p>Save your entry then assign the role to the corresponding Profile/User authorization.</p>\n<p>By this only people who have this role will be able to run the application \"My Travel Requests V2\"</p>", "noteVersion": 2}]}, {"note": "2708777", "noteTitle": "2708777 - Add new tile MTR V2 into Business Catalog SAP_FI_BC_TRV", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The tile \"My Travel Requests V2\" is not available under the Catalog \"Travel - Employee Self Services\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FIORI, My Travel Requests V2, SAP_FI_BC_TRV</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Advance development</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the corresponding Support Package.</p>", "noteVersion": 2}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "If you have Custom code, refer to the additional notes to implement your code with Extensibility concept in the new application."}, {"Activity": "Customizing / Configuration", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Refer to the Application Library to setup proper configuration in FIORI Launchpad"}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "Een if the UI is intuitive, you can plan a training session for your users as new features are available with the version 2 of the application"}]}