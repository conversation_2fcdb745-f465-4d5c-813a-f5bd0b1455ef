{"guid": "0050569455E21ED5B3E17678390DA09E", "sitemId": "SI1: PROCR_SRM", "sitemTitle": "S4TWL - Co-Deployment of SAP SRM", "note": 2271166, "noteTitle": "2271166 - S4TWL - Co-Deployment of SAP SRM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP ERP it is possible to deploy SAP SRM as an AddOn. With SAP S/4 HANA this AddOn deployment is not possible anymore. Therefore, a de-installation of the SRM AddOn is necessary when converting the SAP ERP system to SAP S/4 HANA.</p>\n<p><strong>Business Process related information</strong></p>\n<p>SRM Self-Service Procurement capabilities are re-implemented within SAP S/4 HANA. The functional scope is described in the feature scope description under the solution capability of Self-Service Requisitioning. The functional scope is not identical with the SAP SRM functionality. However it is planned to reach functional equivalence over time by continuous improvement of the S/4 HANA capabilities. There are migration reports available to transfer the business data from the SAP SRM AddOn to the SAP S/4 HANA system.</p>\n<p><strong>Business Value</strong></p>\n<p>Providing Self-Service Requisitioning as an integral part of S/4 HANA has several business benefits compared to the old AddOn deployment:</p>\n<ul>\n<li>Data structures are simplified, e.g. no shopping cart data anymore, instead only purchase requisition data is used</li>\n<li>\n<p>Seamless UI integration based on SAP Fiori (formerly GUI and WebDynpro) as well as seamless process integration</p>\n</li>\n<li>\n<p>No master data replication (formerly data replication between SRM AddOn and ERP core)</p>\n</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Perform Conversion pre-check to identify the constellation on SAP Business Suite start release.</p>\n<p><strong>Related SAP Note</strong></p>\n<p>2216943   S4TC SRM_SERVER master check for S/4 system conversion checks</p>\n<p>2251946   Informational note about migration of SAP SRM shopping carts to SAP S/4 HANA SSP</p>\n<p> </p>", "noteVersion": 3, "refer_note": [{"note": "2478308", "noteTitle": "2478308 - S4TC SRM_SERVER pre-check class for S/4 system conversion checks (SIC)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to perform a transition of an SAP ERP system on which the SAP SRM (\"ERP/SRM OneClient\") application is installed to S/4HANA.</p>\n<p>This SAP Note provides a pre-check class for the SRM_SERVER software component (for the new pre-check framework) that is started before and during the transition process and executes the relevant checks in the source release in order to facilitate the transition process.</p>\n<p>Read SAP Note <strong>2251946</strong> with the attached <strong>Conversion Guides</strong> to gain an overview of the transition process from the SAP ERP/SRM OneClient scenario to SAP S/4HANA and understand system preparations for the transition process.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SRM, Supplier Relationship Management, SRM_SERVER, S/4HANA, transition to S/4HANA, shopping cart migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>none</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note using the SNOTE transaction.</p>", "noteVersion": 13}, {"note": "2216943", "noteTitle": "2216943 - S4TC SRM_SERVER master check for S/4 system conversion checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to perform a transition of an SAP ERP system on which the SAP SRM (\"ERP/SRM OneClient\") application is installed to S/4HANA.</p>\n<p>This SAP Note provides a pre-check class that is started before and during the transition process and executes the relevant checks in the source release in order to facilitate the transition process.</p>\n<p>Read SAP Note <strong>2251946</strong> with the attached <strong>Conversion Guides</strong> to gain an overview of the transition process from the SAP ERP/SRM OneClient scenario to SAP S/4HANA and understand system preparations for the transition process.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, SRM, Supplier Relationship Management, S/4HANA, transition to S/4HANA, shopping cart migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>None</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note using the SNOTE transaction.</p>", "noteVersion": 20, "refer_note": [{"note": "2182725", "noteTitle": "2182725 - S4TC Delivery of the SAP S/4HANA System Conversion Checks for SAP S/4HANA 1511 or 1610", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Checks have to be executed before the conversion to SAP S/4HANA, if all preliminary steps in the source system have been performed.</p>\n<p><strong><strong>Note: </strong>The check report delivered via this note was exclusively used for system conversions to SAP S/4HANA 1511 and SAP S/4HANA 1610. As system conversions to SAP S/4HANA 1511 are no longer supported since May 2018 (and respectively to SAP S/4HANA 1610 since May 2019), this report and this note are obsolete and will be removed in the near future.</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note as well as the SAP notes mentioned in the manual activities document.</p>\n<p>For further information please refer to the respective SAP S/4HANA conversion procedure guide.</p>\n<p>The report R_S4_PRE_TRANSITION_CHECKS, which is delivered with this SAP note,</p>\n<ul>\n<li>can be executed <strong>standalone</strong> and calls all available pre–conversion checks which are delivered with the SAP notes mentioned in the manual activities document of this note.</li>\n<li>can be executed as often as required. When called in 'Simulation Mode', nothing is persisted, otherwise the report output is saved as 'Application Log' entry. Use the respective display option on the report selection screen to search for respective application log entries and to display them.</li>\n<li>is called automatically in the conversion procedure by SUM (Software Update Manager) to execute the pre-conversion checks.</li>\n</ul>\n<p>When you execute the pre–conversion checks <strong>standalone </strong>using report R_S4_PRE_TRANSITION_CHECKS:</p>\n<ul>\n<li>We recommend to always choose the option 'Simulation Mode', so that all available pre–conversion checks are executed despite of erroneous or missing pre–check classes.</li>\n<li>We recommend to save the entries on the selection screen of report R_S4_PRE_TRANSITION_CHECKS as a report variant.</li>\n<li>We recommend to execute the report as batch job using the above mentioned report variant.</li>\n<li>You can ignore error messages in the pre–conversion check result list concerning missing pre–conversion check classes. Only when the checks are executed by SUM, information about software components that do not require a pre–conversion check class is available.</li>\n</ul>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed for the very first time, field labels for the selection screen fields are missing, because such texts can not be delivered via correction instruction. But these texts are generated for the current logon language and persisted during this very first report execution.</p>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed <strong>with the option 'Check Class Consistency Check'</strong>, the report only checks if the respective check class methods can be dynamically called, <strong>but the real pre-conversion checks are <span><em>not executed</em></span></strong>.</p>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed and the option <strong>'Pre-Conversion </strong><strong>Check Results' </strong>is selected:</p>\n<ul>\n<li>If the checkbox ‘Simulation Mode’ is selected:</li>\n<ul>\n<li><strong>no</strong> application log entry is persisted, the output is shown on screen (online execution) or in spool (batch execution).</li>\n<li>consistency check errors <strong>are ignored</strong>, all usable checks are executed.</li>\n</ul>\n<li>If the checkbox ‘Simulation Mode’ is not selected:</li>\n<ul>\n<li>The output is only persisted as application log.</li>\n<li>consistency check errors <strong>are not ignored, </strong>checks are only executed if all checks are consistent.</li>\n</ul>\n</ul>\n<p>If one of the dynamically called pre-check classes returns more than 10000 check result lines, a respective error message is written into the check result (and the huge amount of more than 10000 lines is ignored in the result list in order to prevent an internal memory overflow of the used application log functionality). Contact in such a case the pre-check class responsible so that the class code can be corrected. Use the application component that is mentioned in the pre-check result to create an incident.</p>", "noteVersion": 56}, {"note": "2327980", "noteTitle": "2327980 - [obsolete note] DDIC objects for SRM_SERVER Pre-Check class", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><strong>The current note is obsolete.</strong> We changed the pre-check class and now it doesn't need additional DDIC objects to be generated. So, the current note is not needed any more.</p>\n<p><em>[obsolete]: [This note provides the report RBBP_DDIC_OBJECTS_NOTE_2216943that generates DDIC objects used in the note 2216943</em>]</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Supplier Relationship Management, Shopping Cart, Purchase Requisition, Migration, Conversion to S/4HANA, DDIC objects</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>none</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>You should not use this note.</strong></p>\n<p>[obsolete]:</p>\n<ul>\n<li><em>[Apply the attached correction instruction by use of the transaction SNOTE. </em></li>\n<li><em>Execute the report RBBP_DDIC_OBJECTS_NOTE_2216943, if you plan to apply the note 2216943. The report will generate relevant DDIC objects - in this case a function group and relevant includes.</em></li>\n<li><em>Afterwards apply the note 2216943. At applying the note 2216943 you should select the check boxes enabling overwritting of the objects created by the DDIC report mentioned above. After applying the note 2216943 the class CLS4H_SRM_SERVER will be created.</em></li>\n<li><em>As soon as the note 2216943 was applied you should execute the DDIC report RBBP_DDIC_OBJECTS_NOTE_2216943 once more to generate the text elements for the created class.]</em></li>\n</ul>", "noteVersion": 2}]}, {"note": "2251946", "noteTitle": "2251946 - Informational note about migration of SAP SRM shopping carts to SAP S/4 HANA SSP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using one-client installation of SAP SRM 7.0 (or higher) on top of SAP ERP 6.04 (or higher) or a standalone installation of SAP SRM 7.0 (or higher) with one or multiple SAP ERP 6.0 (or higher) backend systems. You are planning to migrate your <strong>SAP SRM shopping carts</strong> to SAP S/4 HANA <strong>OnPremise</strong>. The SRM application is technically not a part of SAP S/4 HANA and its Shopping Cart functionality was replaced with the SAP S/4 HANA Self-Service Procurement functionality based on the purchase requisition document. Therefore, the solution for migration of SRM shopping carts to S/4HANA includes as a first step conversion of SRM shopping carts to purchase requisitions in a corresponding ERP backend (or ERP client) followed by the upgrade of that ERP system to SAP S/4 HANA.</p>\n<p>As soon as the ERP system has been upgraded to SAP S/4 HANA the Self-Service Procurement scenario in S/4 has to be configured (see configuration guides in the note <strong>2214564)</strong>.  Afterwards a post-conversion report <strong>MMPUR_MIG_EBAN</strong> has to be executed for the purchase requisitions originated from SRM shopping carts. As a result the purchase requsitions are getting visible in the Fiori app 'My Purchase Requisitions'. For purchase requisitions originated from incomplete shopping carts being in status <strong>SAVED</strong> the draft instances of purchase requisitions will be created. For purchase requisitions originated from shopping carts in status <strong>AWAITING_APPROVAL</strong> the corresponding release strategy process as well as a workflow approval will be started.</p>\n<p>Please have a look at the attached Conversion Guide to learn more about the migration options for your SAP SRM data. The document will guide you through the migration process.</p>\n<p><strong><em>Migration options</em></strong>:</p>\n<ul>\n<li>If migration of SAP SRM Shopping carts to SAP S/4 HANA <strong>is not needed</strong>, that is, you just want to convert your ERP system to S/4 HANA and remove the SRM_SERVER component, you can set this option by executing the report <strong>BBP_SC_S4MIG_SET_SCENARIO</strong> with the parameter \"<strong>No migration is needed at all</strong>\". The report must be executed in the start release system during the preparation phase before execution of conversion pre-checks. The report stores the migration option in a corresponding DB table that will be checked during the conversion process.</li>\n</ul>\n<ul>\n<li>Execution of the above-mentioned report <strong>BBP_SC_S4MIG_SET_SCENARIO</strong> is <strong>optional</strong>. If it was not executed, the DB table containing migration option is empty. This is interpreted as selection of the default migration option \"<strong>Migration during Downtime</strong>\". This option can only be applied to the conversion of the ERP/SRM one-client business scenario.</li>\n</ul>\n<p><strong>Deletion of SRM_SERVER:</strong></p>\n<ul>\n<li>\n<div><em>At the moment the deletion of the <strong>SRM_SERVER</strong> component <strong>is only possible out of the SUM tool</strong> during execution of the conversion of ERP/ SRM one-client to S/4HANA.</em></div>\n</li>\n<li>\n<div><em></em><em><strong>It is not possible</strong> to delete the SRM_SERVER component by use of the <strong>SAINT</strong> tool. The SAINT tool can only check the effects of the SRM uninstallation by performing the uninstallation in the test mode. The SAINT tool also executes a “where-used” check and provides a list of customer objects using SRM objects.</em></div>\n</li>\n</ul>\n<p><em><strong>Preparations before a conversion:</strong></em></p>\n<p>To enable migration of SRM shopping carts during downtime a couple of preparation steps (like creation of DDIC user in all relevant clients, creation of RFC destinations to application clients, installation of pre-check class, migration reports, functions and classes etc) has to be fulfilled. These preparation steps are checked during execution of the pre-check class for the SRM_SERVER component. Besides that, it is required that the SRM client(s) that have to be converted are configured properly and running one-client business scenario. For example, one part of the one-client scenario configuration is the maintenance of the table <strong>BBP_BACKEND_DEST</strong> that controls the backend clients where the shopping carts are being transferred. This table should certainly be filled in order the migration of shopping carts were possible.</p>\n<p><strong><em>Supported migration paths:</em></strong></p>\n<ul>\n<li>The migration paths for SAP SRM releases lower than SRM 7.0 are not supported.</li>\n</ul>\n<p><em>For example, this includes the one-client installation of SAP SRM 5.0 together with SAP ERP 6.0, or a standalone installation of SAP SRM 5.0 with SAP ERP 6.0 (or higher) backend system(s). If you want to bring SAP SRM 5.0 to S/4HANA, you have to carry out an upgrade to SAP SRM 7.0. For SRM/ERP one-client installation this will also require an upgrade to SAP ERP 6.04.</em></p>\n<ul>\n<li>Supported target releases of S/4HANA are <strong>1511 OP SR1</strong> (aka 1511 OP SP02) and higher releases</li>\n</ul>\n<ul>\n<li>The supported migration paths are only relevant for conversion to <strong>S/4HANA</strong> <strong>OnPremise </strong>and by use of the SUM tool</li>\n</ul>\n<ul>\n<li>The provided implementations are focused only on the migration of the SAP SRM Shopping Carts. Migration of other Business Objects types of SAP SRM is not supported yet.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SRM Shopping Cart Migration, Conversion, Purchase Requisition, S/4HANA, SAP Supplier Relationship Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>none</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Find below important information relevant for the SRM Shopping Carts conversion process. It provides the notes to be installed before execution of conversion and also the notes required after execution of upgrade to S/4 HANA to fix the bugs found in the 1511 OP SR1 and other releases. In addition there is information how to handle the messages of type 'P' occuring during the conversion phase XPRAS_AIMMRG.</p>\n<p>1. The following notes are relevant for the migration of SAP SRM shopping carts to SAP S/4 HANA SSP and have to be installed in the start release system (see also the list of referred notes) before execution of conversion pre-checks.</p>\n<p><span class=\"urTxtStd\">(a) Notes for all SWC versions of SRM_SERVER and SAP_APPL (<strong>except SWCVs listed in paragraph 'b'</strong>). These include the following combinations:</span></p>\n<ul>\n<ul>\n<li>\n<div><span class=\"urTxtStd\">for SAP ERP/ SRM one-client installation -  SRM_SERVER from 700 to 714 in combination with SAP_APPL from 604 to 618  </span></div>\n</li>\n<li>\n<div><span class=\"urTxtStd\">for SAP SRM / SAP ERP side-by-side installation -  SRM_SERVER from 700 to 714 in combination with SAP_APPL from 600 to 618</span></div>\n</li>\n</ul>\n</ul>\n<p><span class=\"urTxtStd\">2250115     <a class=\"urLnk\" href=\"/notes/2250115\" id=\"CORR_GR_C_refs_5_4\" tabindex=\"0\" target=\"_blank\">Create DDIC objects for SAP Note 2252346</a>       ( prerequisite for the note 2252346)</span></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\">2252346     <a class=\"urLnk\" href=\"/notes/2252346\" id=\"CORR_GR_C_refs_3_4\" tabindex=\"0\" target=\"_blank\">SRM coding to enable migration of SRM Shopping Carts to S4HANA</a>  (needs as a prerequiste installation of the note 2250115 and execution of DDIC report)</span></span></p>\n<p><span class=\"urTxtStd\">2250113     <a class=\"urLnk\" href=\"/notes/2250113\" id=\"CORR_GR_C_refs_6_4\" tabindex=\"0\" target=\"_blank\">Create DDIC objects for SAP Note 2252240</a>        ( prerequisite for the note 2252240)</span></p>\n<p><span class=\"urTxtStd\">2252240     <a class=\"urLnk\" href=\"/notes/2252240\" id=\"CORR_GR_C_refs_4_4\" tabindex=\"0\" target=\"_blank\">ERP coding to enable migration of SRM Shopping Carts to S4HANA</a>  (needs as a prerequiste installation of the note 2250113 and execution of DDIC report)</span></p>\n<p><span class=\"urTxtStd\">2249984    <a class=\"urLnk\" href=\"/notes/2249984\" id=\"CORR_GR_C_refs_7_4\" tabindex=\"0\" target=\"_blank\">Set options for migration of SRM Shopping Carts to S4HANA</a>         (for all SRM releases)</span></p>\n<p><span class=\"urTxtStd\">2216943    <a class=\"urLnk\" href=\"/notes/2216943\" id=\"CORR_GR_C_refs_10_4\" tabindex=\"0\" target=\"_blank\">S4TC SRM_SERVER master check for S/4 system conversion checks</a>       (for all SRM releases and S/4 HANA target releases below 1709 OP)</span></p>\n<p><span class=\"urTxtStd\">2478308    <a href=\"/notes/2478308\" target=\"_blank\">SRM_SERVER pre-check class for S/4 system conversion checks (SIC)</a>    (for all SRM releases and S/4 HANA target release 1709 OP and higher)</span></p>\n<p>2317386    <a href=\"/notes/2317386\" target=\"_blank\">S/4HANA Conversion:  PlugIn class for deletion of generated SRM objects</a>       (for all SRM releases)</p>\n<p><em><strong>Remark:</strong></em></p>\n<p><em>If you plan to take the migration option \"<strong>No migration is needed at all</strong>\", the list of the notes to be installed in the start release system is smaller as the notes delivering migration coding are not required. The following notes have to be installed:</em></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\"><span class=\"urTxtStd\">2250115     <a class=\"urLnk\" href=\"/notes/2250115\" id=\"CORR_GR_C_refs_5_4\" tabindex=\"0\" target=\"_blank\">Create DDIC objects for SAP Note 2252346</a>       (a prerequisite for the note 2252346)</span></span></span></p>\n<p><span class=\"urTxtStd\">2252346     <a class=\"urLnk\" href=\"/notes/2252346\" id=\"CORR_GR_C_refs_3_4\" tabindex=\"0\" target=\"_blank\">SRM coding to enable migration of SRM Shopping Carts to S4HANA</a>  (needs as a prerequiste installation of the note 2250115 and execution of DDIC report)</span></p>\n<p><span class=\"urTxtStd\">2249984    <a class=\"urLnk\" href=\"/notes/2249984\" id=\"CORR_GR_C_refs_7_4\" tabindex=\"0\" target=\"_blank\">Set options for migration of SRM Shopping Carts to S4HANA</a>       (for all SRM releases)</span></p>\n<p><span class=\"urTxtStd\">2216943    <a class=\"urLnk\" href=\"/notes/2216943\" id=\"CORR_GR_C_refs_10_4\" tabindex=\"0\" target=\"_blank\">S4TC SRM_SERVER master check for S/4 system conversion checks</a>      (for all SRM releases and S/4 HANA target releases below 1709 OP)</span></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\">2478308    <a href=\"/notes/2478308\" target=\"_blank\">SRM_SERVER pre-check class for S/4 system conversion checks (SIC)</a>    (for all SRM releases and S/4 HANA target release 1709 OP and higher)</span></span></p>\n<p>2317386    <a href=\"/notes/2317386\" target=\"_blank\">S/4HANA Conversion:  PlugIn class for deletion of generated SRM objects</a>      (for all SRM releases)</p>\n<p> (b) Notes for SWC versions:  SRM_SERVER 713 (SP11 and higher SPs) in combination with SAP_APPL 617 (SP11 and higher SPs)</p>\n<p><span class=\"urTxtStd\">2291147    <a class=\"urLnk\" href=\"/notes/2291147\" id=\"CORR_GR_C_refs_1_4\" tabindex=\"0\" target=\"_blank\">Create DDIC objects for SAP Note 2233517</a>          (a prerequisite for the note 2233517)</span></p>\n<p><span class=\"urTxtStd\">2233517    <a class=\"urLnk\" href=\"/notes/2233517\" id=\"CORR_GR_C_refs_8_4\" tabindex=\"0\" target=\"_blank\">ERP 6.17 SP11 coding for migration of SRM Shopping Carts to S4HANA</a>  (needs as a prerequiste installation of the note 2291147 and execution of DDIC report)</span></p>\n<p>2341826    <a href=\"/notes/2341826\" target=\"_blank\">SAP_APPL 6.17 SP12 stack corrections for migration of SRM SCs to S4HANA</a>       (only for SAP_APPL/ ERP 6.17 SP12 stack)</p>\n<p><span class=\"urTxtStd\">2290018    <a class=\"urLnk\" href=\"/notes/2290018\" id=\"CORR_GR_C_refs_2_4\" tabindex=\"0\" target=\"_blank\">Create DDIC objects for SAP Note 2233514</a>                (a prerequisite for the note 2233514)</span></p>\n<p><span class=\"urTxtStd\">2233514     <a class=\"urLnk\" href=\"/notes/2233514\" id=\"CORR_GR_C_refs_9_4\" tabindex=\"0\" target=\"_blank\">SRM 7.13 SP11 coding for migration of SRM Shopping Carts to S4HANA</a>  (needs as a prerequiste installation of the note 2290018 and execution of DDIC report)</span></p>\n<p><span class=\"urTxtStd\">2341850    <a href=\"/notes/2341850\" target=\"_blank\">SRM 7.13 SP12 stack coding for migration of SRM SCs to S4HANA</a>         (only for SRM 7.13 SP12 stack)</span></p>\n<p><span class=\"urTxtStd\">2468792    <a href=\"/notes/2468792\" target=\"_blank\">SRM 7.13 SP13 stack coding for migration of SRM SCs to S4HANA</a>       (only for SRM 7.13 SP13 stack)</span></p>\n<p><span class=\"urTxtStd\">2249984    <a class=\"urLnk\" href=\"/notes/2249984\" id=\"CORR_GR_C_refs_7_4\" tabindex=\"0\" target=\"_blank\">Set options for migration of SRM Shopping Carts to S4HANA</a>            (for all SRM releases)</span></p>\n<p><span class=\"urTxtStd\">2216943    <a class=\"urLnk\" href=\"/notes/2216943\" id=\"CORR_GR_C_refs_10_4\" tabindex=\"0\" target=\"_blank\">S4TC SRM_SERVER master check for S/4 system conversion checks</a>     (for all SRM releases and S/4 HANA target releases below 1709 OP)</span></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\">2478308    <a href=\"/notes/2478308\" target=\"_blank\">SRM_SERVER pre-check class for S/4 system conversion checks (SIC)</a>    (for all SRM releases and S/4 HANA target release 1709 OP and higher)</span></span></p>\n<p>2317386    <a href=\"/notes/2317386\" target=\"_blank\">S/4HANA Conversion:  PlugIn class for deletion of generated SRM objects</a>      (for all SRM releases)</p>\n<p><em><strong>Remark:</strong></em></p>\n<p><em>If you plan to take the migration option \"<strong>No migration is needed at all</strong>\", the list of the notes to be installed in the start release system is smaller as the notes delivering migration coding are not required. The following notes have to be installed:</em></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\"><span class=\"urTxtStd\">2290018    <a class=\"urLnk\" href=\"/notes/2290018\" id=\"CORR_GR_C_refs_2_4\" tabindex=\"0\" target=\"_blank\">Create DDIC objects for SAP Note 2233514</a>           (a prerequisite for the note 2233514)</span></span></span></p>\n<p><span class=\"urTxtStd\">2233514     <a class=\"urLnk\" href=\"/notes/2233514\" id=\"CORR_GR_C_refs_9_4\" tabindex=\"0\" target=\"_blank\">SRM 7.13 SP11 coding for migration of SRM Shopping Carts to S4HANA</a>  (needs as a prerequisite installation of the note 2290018 and execution of DDIC report)</span></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\"><span class=\"urTxtStd\">2341850    <a href=\"/notes/2341850\" target=\"_blank\">SRM 7.13 SP12 stack coding for migration of SRM SCs to S4HANA</a>     (only for SRM 7.13 SP12 stack)</span></span></span></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\">2468792    <a href=\"/notes/2468792\" target=\"_blank\">SRM 7.13 SP13 stack coding for migration of SRM SCs to S4HANA</a>     (only for SRM 7.13 SP12 stack)</span></span></p>\n<p><span class=\"urTxtStd\">2249984    <a class=\"urLnk\" href=\"/notes/2249984\" id=\"CORR_GR_C_refs_7_4\" tabindex=\"0\" target=\"_blank\">Set options for migration of SRM Shopping Carts to S4HANA</a>               (for all SRM releases)</span></p>\n<p><span class=\"urTxtStd\">2216943    <a class=\"urLnk\" href=\"/notes/2216943\" id=\"CORR_GR_C_refs_10_4\" tabindex=\"0\" target=\"_blank\">S4TC SRM_SERVER master check for S/4 system conversion checks</a>       (for all SRM releases and S/4 HANA target releases below 1709 OP)</span></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\">2478308    <a href=\"/notes/2478308\" target=\"_blank\">SRM_SERVER pre-check class for S/4 system conversion checks (SIC)</a>    (for all SRM releases and S/4 HANA target release 1709 OP and higher)</span></span></p>\n<p>2317386    <a href=\"/notes/2317386\" target=\"_blank\">S/4HANA Conversion:  PlugIn class for deletion of generated SRM objects</a>     (for all SRM releases)</p>\n<p>2. Following note has to be installed in the start release system, only if you are using SPAM/SAINT version 0061:</p>\n<p>2348177     <a href=\"/notes/2348177\" target=\"_blank\">Uninstallation of software component SRM_SERVER during the SRM one client conversion to S4HANA 1511 SR1</a></p>\n<p>3. Required notes after upgrade to S/4HANA:</p>\n<p><strong>Target release 1511 OP SR1 (aka 1511 OP SP02)</strong>:</p>\n<p>2310718    <a href=\"/notes/2310718\" target=\"_blank\">Short dump while accessing Fiori based S4H Purchasing applications</a></p>\n<p>2285354    <a href=\"/notes/2285354\" target=\"_blank\">Changes in CL_MMPUR_REQ_CONTEXT class</a></p>\n<p>2341098    <a href=\"/notes/2341098\" target=\"_blank\">Not able to open the Migrated PR</a></p>\n<p>2343978     <a href=\"/notes/2343978\" target=\"_blank\">Migrated Shopping Cart shows incorrect status and descriptions are missing in PR apps</a></p>\n<p>2344932     <a href=\"/notes/2344932\" target=\"_blank\">Migrated Shopping Carts shows incorrect status in PR apps</a></p>\n<p>2346072    <a href=\"/notes/2346072\" target=\"_blank\">Status of Rejected PR's are shown as Deleted</a></p>\n<p>2346301    <a href=\"/notes/2346301\" target=\"_blank\">Application allows to create draft for rejected and deleted PR</a></p>\n<p>2341875    <a href=\"/notes/2341875\" target=\"_blank\">Corrections in post-processing report MMPUR_MIG_EBAN</a></p>\n<p>2402931    <a href=\"/notes/2402931\" target=\"_blank\">Incorrect processing of PRs in Awaiting Approval by report MMPUR_MIG_EBAN</a></p>\n<p> <strong>Target Release 1610 OP SP00:</strong></p>\n<p>2402931    <a href=\"/notes/2402931\" target=\"_blank\">Incorrect processing of PRs in Awaiting Approval by report MMPUR_MIG_EBAN</a></p>\n<p>4. Messages of type 'P' occuring during conversion in the phase XPRAS_AIMMRG</p>\n<p>There is a number of error messages of type 'P' encounted by the SUM tool during the conversion of the ERP/ SRM one-client installation to S/4HANA. These messages can be ignored. For this type of uncritical error messages the SUM tool doesn't interrupt the conversion run but stores the messages into the LONGPOST.LOG file. The error messages occur during the phase XPRAS_AIMMRG and are displayed below. The errors are obviously dealing with the fact that the generated condition tables used in the SAP SRM pricing and condition scenarios are not in place as they were deleted together with the SRM_SERVER software component during conversion to S/4HANA. Nevertheless, after import of the deletion request into the productive system the method \"/SAPCND/TRN_AFTER_IMPORT_O_OW is executed and tries to process the missing BBP** condition tables. No post-processing after upgrade is neccessary for these condition tables as the SRM_SERVER component where they were used is deleted.</p>\n<p>### CURRENT PHASE: XPRAS_AIMMRG<br/>1PEPU203X--&gt; Messages extracted from log file \"SAPR750XPRA90000181.QE1\" &lt;--<br/>A2PE/SAPCND/GENERATOR 088 Working set structure \"/1CN/WORKING_SET_I_D____\" could not be written into DDIC<br/>A2PE/SAPCND/GENERATOR 088 Working set structure \"/1CN/WORKING_SET_I_S____\" could not be written into DDIC<br/>A2PE/SAPCND/GENERATOR 088 Working set structure \"/1CN/WORKING_SET_E_D____\" could not be written into DDIC<br/>A2PE/SAPCND/GENERATOR 088 Working set structure \"/1CN/WORKING_SET_E_S____\" could not be written into DDIC<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP001__\"<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP016__\"<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP019__\"<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP068__\"<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP118__\"<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP001__\"<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP016__\"<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP019__\"<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP068__\"<br/>A2PE/SAPCND/TRANSPORT 044 Definition for condition table not found\"BBP\"\"PR\"\"ASAP118__\"</p>\n<p>      A2PE/SAPCND/GENERATOR 209 Object \"/1CN/WORKING_SET_I_D_BBP\" is still referenced to object [\"TABL\", …… , \" \"]<br/>      A2PE/SAPCND/GENERATOR 209 Object \"/1CN/WORKING_SET_E_S_BBP\" is still referenced to object [\"TABL\", …… , \" \"]</p>\n<p>In the case of the following P-type messages you should proceed as described below.</p>\n<p>### CURRENT PHASE: XPRAS_AIMMRG<br/>1PEPU203X--&gt; Messages extracted from log file \"SAPR753XPRA90000448.CJC\" &lt;--<br/>2PE1R462     Table \"/SRMNXP/CAT_IMG\" for document contents does not exist<br/>2PE1R462     Table \"BBPCONT\" for document contents does not exist<br/>2PE1R462     Table \"BDSCONT19\" for document contents does not exist</p>\n<p>- Go to the transaction <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><strong>OAC0</strong> and delete the following DBS content repositories:  </span>SRMNXP_IMG, <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">BBPFILESYSTEM and <span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">BDS_DB19</span></span></p>\n<p>5.  Informational note for configuration of Self-Service Procurement scenario in the target release 1511 OP SP02 of S/4HANA. The configuration should be carried after upgrade of the start release system to S/4HANA and only if the migration of SRM Shopping Carts is required.</p>\n<p><span class=\"urTxtStd\">2214564     <a class=\"urLnk\" href=\"/notes/2214564\" id=\"CORR_GR_C_refs_11_4\" tabindex=\"0\" target=\"_blank\">Configuration guide for Self Service Procurement scenario in S/4HANA.</a></span></p>", "noteVersion": 22, "refer_note": [{"note": "2291147", "noteTitle": "2291147 - Create DDIC objects for SAP Note 2233517", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides the report <strong>RBBP_DDIC_OBJECTS_NOTE_2233517 </strong>that generates DDIC objects used in the note <strong>2233517</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Supplier Relationship Management, Shopping Cart, Purchase Requisition, one-client scenario, Migration, Conversion, DDIC objects</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>none</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Proceed as follows:</p>\n<ul>\n<li>Apply the attached correction instruction by use of the transaction <strong>SNOTE. </strong></li>\n<li>Execute the report <strong>RBBP_DDIC_OBJECTS_NOTE_2233517</strong>, if you need to apply the note <strong>2233517.</strong></li>\n</ul>", "noteVersion": 3}, {"note": "2250113", "noteTitle": "2250113 - Create DDIC objects for SAP Note 2252240", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252240 </strong>that generates DDIC objects used in the note <strong>2252240</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Supplier Relationship Management, Shopping Cart, Purchase Requisition, Migration, Conversion, DDIC objects</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>none</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Proceed as follows:</p>\n<ul>\n<li>Apply the attached correction instruction by use of the transaction <strong>SNOTE</strong></li>\n<li>Afterwards, execute the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252240 </strong>provided by this note, if you plan to apply the note <strong>2252240.</strong> At applying the note <strong>2252240</strong> you should select the check boxes enabling overwritting of the objects created by the DDIC report mentioned above. After applying the note <strong>2252240</strong> the classes, function modules and reports will be created.</li>\n<li>Having applied the note <strong>2252240</strong> run the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252240</strong> again to generate the text elements for created classes, reports and functions</li>\n</ul>", "noteVersion": 5}, {"note": "2249984", "noteTitle": "2249984 - Set options for migration of SRM Shopping Carts to S4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides a report to set options for migration of SRM Shopping Carts to S4HANA. For example, one can define point of time for shopping carts conversion. It can take place only during uptime or both at uptime and downtime.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Supplier Relationship Management, Shopping Cart, Purchase Requisition, Migration, Conversion, ERP/ SRM one-client installation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>none</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the correction instruction attached to the note by use of the transaction SNOTE.</p>", "noteVersion": 3}, {"note": "2252346", "noteTitle": "2252346 - SRM coding to enable migration of SRM Shopping Carts to S4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note is used during migration of SAP SRM/ERP one-client installation to S4HANA. It provides the coding (reports, classes) that enables conversion of SRM Shopping Carts to Purchase Requisitions that are the successor documents of shopping carts in S4HANA. For more details how to execute conversion read the Conversion Guidelines attached to the informational note <strong>2251946</strong>.</p>\n<p><span><strong>Remark:</strong></span></p>\n<p>1. If the SAP SRM product version in your one-client installation is as of <strong>7.13 SP11</strong> or <strong>7.13 SP12</strong>, you have to use the migration notes <strong>2233514</strong> and <strong>2341869,</strong> correspondingly, that cover the relevant implementation objects for this particular SP stacks.</p>\n<p>2. Generally, to enable Shopping Cart migration you also have to install the corresponding implementations in the SAP_APPL component (part of SAP ERP). These implementations are handled by the notes <strong>2233517</strong> (ERP 6.17 SP 11 stack or higher SPs) and <strong>2252240</strong> (all other ERP releases: 6.00 and higher).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Supplier Relationship Management, Shopping Cart, Purchase Requisition, Migration, Conversion, SRM/ERP one-client scenario</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><span>Attention:</span></p>\n<ul>\n<li>The note <strong>2250115</strong> is the prerequisite for the current note and has to be installed first! It provides the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252346 </strong>that generates DDIC objects used in the current note. This report has to be executed prior to installation of the current note.</li>\n</ul>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Proceed as follows:</p>\n<ol>\n<li>Install the note <strong>2250115</strong> and execute the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252346</strong> to generate DDIC objects that are used in the current note</li>\n<li>Apply the current note by use of the transaction <strong>SNOTE. </strong> At applying the note you should select the check boxes enabling overwritting of the objects created by the DDIC report mentioned above. After applying the note classes, function modules and reports will be created.</li>\n<li>Execute the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252346</strong> one more time. This will create the text elements (selection texts and text symbols) used in the created reports, functions and classes.</li>\n</ol>\n<p>In addition, there are a few manual post-installation implementation steps that have to be done.</p>\n<ol>\n<li>Upload the PFCG role <strong>BBP_SC_HANA_MIG_ROLE</strong> attached as a ZIP file to the current note into your system</li>\n<ul>\n<li>save the attachment \"<strong>bbp_sc_hana_mig_role</strong>.<strong>zip</strong>\" to your local hard disk and unzip it</li>\n<li>go to transaction PFCG</li>\n<li>go to Menu bar--&gt;Role--&gt;Upload and execute upload of the unzipped role <strong>bbp_sc_hana_mig_role</strong>.<strong>sap</strong></li>\n<li>generate authorization profile of the role</li>\n<li>save the role</li>\n</ul>\n<li>Upload the smartform <strong>BBP_SC_MIG_APPR_DATA</strong> attached as a ZIP file to the current note into your system</li>\n<ul>\n<li>save the attachment \"<strong>bbp_sc_mig_appr_data</strong>.<strong>zip</strong>\" to your local hard disk and unzip it</li>\n<li>go to transaction SMARTFORMS</li>\n<li>go to Menu bar--&gt;Utilities--&gt;Upload Form and execute upload of the unzipped form <strong>bbp_sc_mig_appr_data</strong>.<strong>xml</strong></li>\n<li>save the created smartform in the package <strong>BBP_XPRA</strong> and activate it</li>\n</ul>\n</ol>", "noteVersion": 27}, {"note": "2233514", "noteTitle": "2233514 - SRM 7.13 SP11 coding for migration of SRM Shopping Carts to S4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have a one-client ERP/SRM installation. The SAP SRM product version is as of 7.13 SP11. You want to migrate the Shopping Carts to S/4HANA.</p>\n<p>This note contain the implementations in the SRM_SERVER component needed to enable migration. The correction instructions attached to note have to be installed on top of SRM_SERVER 7.13 SP11 stack.</p>\n<p><span><strong>Remark:</strong></span></p>\n<p>Generally, to enable Shopping Cart migration you also have to install the corresponding implementations in the SAP_APPL component (part of SAP ERP). These implementations are handled by the notes <strong>2233517</strong> (ERP 6.17 SP 11 stack only) and <strong>2252240</strong> (all other ERP releases: 6.00 and higher).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SRM/ ERP one-client, S4HANA, migration, conversion, Shopping cart, Purchase Requisition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><span>Attention:</span></p>\n<ul>\n<li>The note <strong>2290018 </strong>is the prerequisite for the current note and has to be installed first! It provides the report <strong>RBBP_DDIC_OBJCTS_NOTE_2233514 </strong>that  generates DDIC objects used the current note</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Proceed as follows:</p>\n<ol>\n<li>Install the note <strong>2290018</strong> and execute the report <strong>RBBP_DDIC_OBJCTS_NOTE_<strong>2233514</strong></strong> to generate DDIC objects that are used in the current note</li>\n<li>Apply the current note by use of the transaction <strong>SNOTE. </strong>At applying the note you should select the check boxes enabling overwritting of the objects created by the DDIC report mentioned above. After applying the note the classes, function modules and reports will be created.</li>\n<li>As soon as the current note was applied, execute the report <strong>RBBP_DDIC_OBJCTS_NOTE_<strong>2233514</strong></strong> one more time. This will create the text elements (selection texts and text symbols) used in the created functions, reports and classes.</li>\n</ol>\n<p>In addition, there are a few manual post-installation implementation steps that have to be done.</p>\n<p>1. Upload the PFCG role <strong>BBP_SC_HANA_MIG_ROLE</strong> attached as a ZIP file to the current note into your system</p>\n<ul>\n<li>save the attachment \"<strong>bbp_sc_hana_mig_role</strong>.<strong>zip</strong>\" to your local hard disk and unzip it</li>\n<li>go to transaction PFCG</li>\n<li>go to Menu bar--&gt;Role--&gt;Upload and execute upload of the unzipped role <strong>bbp_sc_hana_mig_role</strong>.<strong>sap</strong></li>\n<li>if the role already exists, overwrite it</li>\n<li>generate authorization profile of the role</li>\n<li>save the role</li>\n</ul>\n<p>2. Upload the smartform <strong>BBP_SC_MIG_APPR_DATA</strong> attached as a ZIP file to the current note into your system</p>\n<ul>\n<li>save the attachment \"<strong>bbp_sc_mig_appr_data</strong>.<strong>zip</strong>\" to your local hard disk and unzip it</li>\n<li>go to transaction SMARTFORMS</li>\n<li>go to Menu bar--&gt;Utilities--&gt;Upload form and execute upload of the unzipped form <strong>bbp_sc_mig_appr_data</strong>.<strong>xml</strong></li>\n<li>if the smartform already exists, overwrite it</li>\n<li>save and activate the created smartform</li>\n</ul>", "noteVersion": 9}, {"note": "2290018", "noteTitle": "2290018 - Create DDIC objects for SAP Note 2233514", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides the report <strong>RBBP_DDIC_OBJCTS_NOTE_2233514 </strong>that generates DDIC objects used in the note <strong>2233514</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Supplier Relationship Management, Shopping Cart, Purchase Requisition, one-client scenario, Migration, Conversion, DDIC objects</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>none</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Proceed as follows:</p>\n<ul>\n<li>Apply the attached correction instruction by use of the transaction <strong>SNOTE. </strong></li>\n<li>Execute the report <strong>RBBP_DDIC_OBJCTS_NOTE_2233514</strong>, if you plan to apply the note <strong>2233514. </strong>﻿\r\n<p><span>Remark:</span> When you run “Generate Maintenance” option of the report RBBP_DDIC_OBJCTS_NOTE_2233514, you might sometime get an error message “TOBJ   BP_SC_MIG_HIST  Function code cannot be selected” . This error message can be ignored.</p>\n</li>\n<li>At applying the note <strong>2233514</strong> you should select the check boxes enabling overwriting of the objects created by the DDIC report mentioned above. After applying the note <strong>2233514</strong> the classes, function modules and reports will be created.</li>\n<li>As soon as the note <strong>2233514</strong> has been applied you should execute the DDIC report RBBP_DDIC_OBJECTS_NOTE_2233514 once more to generate the text elements for the created objects.</li>\n</ul>", "noteVersion": 6}, {"note": "2233517", "noteTitle": "2233517 - ERP 6.17 SP11 coding for migration of SRM Shopping Carts to S4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have a one-client ERP/SRM installation. The SAP ERP product version is as of 6.17 SP11. You want to migrate the SRM Shopping Carts to S/4HANA.</p>\n<p>This note contain the implementations in the SAP_APPL component needed to enable migration. The correction instructions attached to note have to be installed on top of SAP_APPL 6.17 SP11 stack.</p>\n<p><span><strong>Remark:</strong></span></p>\n<p>Generally, to enable Shopping Cart migration you also have to install the corresponding implementations in the SRM_SERVER component. These implementations are handled by the notes <strong>2233514</strong> (SRM 7.13 SP 11 stack only) and <strong>2252346</strong> (all other SAP SRM releases: 7.0 and higher).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SRM Shopping Carts migration to S4HANA, SAP SRM, Purchase Requisition, Migration, Conversion</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><span>Attention:</span></p>\n<ul>\n<li>The note <strong>2291147 </strong>is the prerequisite for the current note and has to be installed first! It provides the report <strong>RBBP_DDIC_OBJECTS_NOTE_2233517 </strong>that generates DDIC objects used in the current note.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Proceed as follows:</p>\n<ol>\n<li>Install the note <strong>2291147</strong> and execute the report <strong>RBBP_DDIC_OBJECTS_NOTE_<strong>2233517</strong></strong> to generate DDIC objects that are used in the current note</li>\n<li>Apply the current note by use of the transaction <strong>SNOTE. </strong>This will install the coding used during conversion of shopping carts to S4HANA.</li>\n<li>Execute the report <strong>RBBP_DDIC_OBJECTS_NOTE_<strong>2233517</strong></strong> one more time. This will create the text elements (selection texts and text symbols) used in the migration reports and classes.</li>\n</ol>\n<p>In addition, execute the following manual post-installation implementation step.</p>\n<p>1. Upload the PFCG role <strong>SAP_SC_HANA_MIG_ROLE</strong> attached as a ZIP file to the current note into your system</p>\n<ul>\n<li>save the attachment \"<strong>SAP_SC_HANA_MIG_ROLE.zip</strong>\" to your local hard disk and unzip it</li>\n<li>go to transaction PFCG</li>\n<li>go to Menu bar--&gt;Role--&gt;Upload and execute upload of the file <strong>SAP_SC_HANA_MIG_ROLE.SAP</strong></li>\n<li>if the role already exists, overwrite it</li>\n<li>generate authorization profile of the role</li>\n<li>save the role</li>\n</ul>", "noteVersion": 5}, {"note": "2250115", "noteTitle": "2250115 - Create DDIC objects for SAP Note 2252346", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252346 </strong>that generates DDIC objects used in the note <strong>2252346</strong>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Supplier Relationship Management, Shopping Cart, Purchase Requisition, one-client scenario, Migration, Conversion, DDIC objects</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>none</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Proceed as follows:</p>\n<ul>\n<li>Apply the attached correction instruction by use of the transaction <strong>SNOTE. </strong>This will install a report.</li>\n<li>Execute the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252346</strong>, if you plan to apply the note <strong>2216943.</strong> The report will generate relevant DDIC objects - e.g function group and relevant includes.</li>\n<li>Afterwards apply the note <strong>2252346.</strong> At applying the note 2252346 you should select the check boxes enabling overwritting of the objects created by the DDIC report mentioned above. After applying the note <strong>2252346</strong> classes, function modules and reports will be created.</li>\n<li>As soon as the note <strong>2252346</strong> was applied you should execute the DDIC report <strong>RBBP_DDIC_OBJECTS_NOTE_2252346</strong> once more to generate the text elements for the created objects.</li>\n</ul>", "noteVersion": 15}, {"note": "2214564", "noteTitle": "2214564 - Configuration guide for Self Service Procurement scenario in S/4HANA.", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note provides steps to configure the Self Service Procurement scenario in S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, Self Service Procurement, Configuration, Flexible workflow</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Configuration Guide</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Configuration Guide is attached to this note. Kindly follow the steps given in the document to configure Self Service Procurement scenario in S/4HANA.</p>\n<p>Flexible workflow can be activated for Self Service Purchase Requisitions at both Header and Item level.</p>\n<p>Note: In case item level approval is used and one or more of the items is rejected, it is strongly recommended not to edit the rejected items from app My Purchase Requisition, as this would lead to inconsistencies in status of Purchase Requisition.</p>\n<p> </p>\n<p> </p>", "noteVersion": 7}, {"note": "2317386", "noteTitle": "2317386 - S/4HANA Conversion:  PlugIn class for deletion of generated SRM objects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert an SRM/ ERP one-client installation to S/4HANA. During upgrade to S/4HANA the whole SRM_SERVER software component will be deleted that is done out of the SUM tool by use of the deletion framework SAINT. This SAP Note provides the plug-in class CL_SRM_SERVER_DEINSTALL that determines the list of local generated objects (for example, condition tables used for pricing via Internet Pricing and Configuration component) created during the productive use of the SRM application. The plug-in class is called during the deletion process and inform the deletion framework about the local generated objects that also have to be deleted as they are using the data elements that don't exist after conversion to S/4HANA.</p>\n<p>The Plug-In class should be installed into the source release system before the conversion and be in the system already during the preparation phase. The  pre-check class of the SRM_SERVER component checks the existence of the above-mentioned class. This class is one of the prerequisites for successful conversion of ERP/ SRM one-client to S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SRM Shopping Cart Migration, Conversion, Purchase Requisition, S/4HANA, SAP Supplier Relationship Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>none</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>If you want to convert SRM/ERP one-client installation to S/4HANA, install this SAP Note AND the mentioned SAP Note 2407103 below to bring into the source release system the plug-in class CL_SRM_SERVER_DEINSTALL and the pricing class /SAPCND/CL_TRANSPORT_SRV. The installation of both SAP Notes should be already done in the preparation phase before start of conversion.</p>\n<p>NOTE<br/>To take care about special pricing condition objects you MUST install as well all SAP Notes mentioned in collecting SAP Note <a href=\"/notes/2407103\" target=\"_blank\" title=\"2407103  - Condition technique and pricing enhancements for Add-On De-Installation\">2407103 - Condition technique and pricing enhancements for Add-On De-Installation </a>otherwise the conversion will fail.<br/><br/>ATTENTION<br/>In case you applied the workflow SAP Note <span>2254228 S/4HANA conversion is not possible at the moment</span></p>", "noteVersion": 19}, {"note": "2252240", "noteTitle": "2252240 - ERP coding to enable migration of SRM Shopping Carts to S4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note is used during upgrade and conversion of SAP SRM/ERP one-client installation to S4HANA. The coding delivered by the note enables conversion of SRM Shopping Carts to Purchase Requisitions that are the successor documents of shopping carts in S4HANA. For more details how to execute conversion you should read the conversion guidelines.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Supplier Relationship Management, Shopping Cart, Purchase Requisition, Migration, Conversion, SAP SRM/ ERP one-client scenario</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><span>Attention:</span></p>\n<ul>\n<li>The note <strong>2250113</strong> is the prerequisite for the current note and has to be installed first! It provides the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252240 </strong>that  generates DDIC objects used in the current note.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Proceed as follows:</p>\n<ul>\n<li>First, install the note <strong>2250113</strong> and execute the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252240</strong> to generate DDIC objects that are used in the current note</li>\n<li>Afterwards, apply the current note by use of the transaction <strong>SNOTE</strong>. At applying the note you should select the check boxes enabling overwritting of the objects created by the DDIC report mentioned above. After applying the note the classes, function modules and reports will be created.</li>\n<li>Afterwards, execute the report <strong>RBBP_DDIC_OBJECTS_NOTE_2252240</strong> one more time. This will create the text elements (selection texts, text symbols, report titles) used in the created reports, functions and classes.</li>\n</ul>\n<p>In addition, carry out the following manual post-implementation step.</p>\n<ul>\n<li>Upload PFCG role <strong>SAP_SC_HANA_MIG_ROLE</strong> attached as a ZIP file to the current note to your system.</li>\n<li>save the attachment \"<strong>SAP_SC_HANA_MIG_ROLE.zip</strong>\" to your local hard disk and unzip it</li>\n<li>go to transaction PFCG</li>\n<li>go to Menu bar--&gt;Role--&gt;Upload and execute upload of the unzipped role <strong>SAP_SC_HANA_MIG_ROLE.SAP</strong></li>\n<li>generate authorization profile of the role</li>\n<li>save the role</li>\n</ul>", "noteVersion": 9}]}], "activities": [{"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Train users in process changes."}, {"Activity": "Technical System Configuration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Install all required notes in the start release system as described in the note 2251946 before execution of conversion pre-checks."}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Either execute report BBP_SC_S4MIG_SET_SCENARIO before conversion if you do not want to migrate SRM data to SAP S/4HANA Self-Service Procurement or perform migration in context of SUM execution as described in note 2251946."}, {"Activity": "Fiori Implementation", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Consider setting up purchase Fiori apps."}, {"Activity": "Landscape Redesign", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "The functional scope of self-service requisitioning is not identical with SAP SRM functionality."}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Interface Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "With SAP ERP it is possible to deploy SAP SRM as an AddOn. With SAP S/4 HANA this AddOn deployment is not possible anymore. Therefore, a de-installation of the SRM AddOn is necessary when converting the SAP ERP system to SAP S/4HANA."}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Set up Self-Service Requisitioning as an integral part of S/4HANA"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "De-installation of the SRM AddOn is necessary when converting the SAP ERP system to SAP S/4 HANA"}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Decide if you want to use the SAP S/4HANA Self-Service Procurement functionality and if so, if you want to migrate data from the co-deployed SRM server component in SAP ERP to SAP S/4HANA."}, {"Activity": "Implementation project required", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If decided, configure SAP S/4HANA Self-Service Procurement according to configuration guide in SAP note 2214564."}, {"Activity": "Software Upgrade / Maintenance", "Phase": "Before conversion project", "Condition": "Conditional", "Additional_Information": "If you have an one-client installation of SAP SRM 5.0 together with SAP ERP 6.0 and decided to migrate to SAP S/4HANA Self-Service Procurement, you need to perform an upgrade to minimum EHP4 for SAP ERP with SAP SRM 7.0."}]}