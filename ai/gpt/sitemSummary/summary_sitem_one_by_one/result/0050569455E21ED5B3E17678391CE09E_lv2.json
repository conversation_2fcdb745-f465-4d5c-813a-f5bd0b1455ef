{"guid": "0050569455E21ED5B3E17678391CE09E", "sitemId": "SI7: Logistics_PSS", "sitemTitle": "S4TWL - Add-On: Genifix (Labelling solution)", "note": 2267427, "noteTitle": "2267427 - S4TWL - Add-On: Genifix (Labeling solution)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The add-on Genifix (labeling solution) will not be supported within SAP S/4HANA. Successor functionality is available in SAP component <a href=\"http://help.sap.com/erp2005_ehp_07/helpdata/en/54/c86cb93bdc4d698eb8e2524b59692b/frameset.htm\" target=\"_blank\">SAP Global Label Management (EHS-SAF-GLM).</a></p>\n<p><strong>Business Process related information</strong></p>\n<p>All transaction codes which were delivered with the Genifix add-on are not available within SAP S/4HANA.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>You can migrate to SAP Global Label Management (EHS-SAF-GLM).</p>\n<p>You need to uninstall the add-on before you do a system conversion to SAP S/4HANA. It is not yet possible to uninstall the add-on via the SAP Add-On Installation Tool (SAINT).</p>", "noteVersion": 2, "refer_note": [{"note": "2221779", "noteTitle": "2221779 - SAP S/4HANA Simplification Item: EHS Genifix Add-On (Labeling Solution)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have implemented own (custom) coding using the objects belonging to the add-on<em> EHS Genifix</em>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4 simplification</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>EHS Genifix is not available in S/4HANA. All objects belonging to the add-on are removed with the uninstallation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Remove any references to these objects in your custom code before the migration to SAP S/4 HANA.</p>\n<p>Where still required, replace them with equivalent objects in your customer namespace.</p>\n<p>See note <a href=\"/notes/2307907\" target=\"_blank\">2307907</a> for more details on the uninstallation and preparatory steps.</p>", "noteVersion": 4}, {"note": "2307907", "noteTitle": "2307907 - Uninstalling Genifix 2.2", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The uninstallation of the add-on <em>EHS Genifix 2.2</em>, using transaction SAINT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAINT, add-on, EHS Genifix 2.2, software component TDAG_GF 220_600, /TDAG/GF*</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You need to uninstall the add-on <em>EHS Genifix 2.2</em> from your system.</p>\n<p>See the prerequisites below.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This SAP Note is updated regularly. Make sure you have the current version of this SAP Note before you start the uninstall process.<br/><br/>Contents<br/>  1. Change history<br/>  2. Prerequisites for uninstalling the add-on<br/>  3. Manual preparation steps before uninstalling<br/>  4. Uninstalling the add-on with SAINT</p>\n<p>1. Change history<br/><br/>Date             Short Description<br/>01.09.2016   SAP Note released</p>\n<p>2. Prerequisites for uninstalling the add-on</p>\n<p>Read and implement the following SAP Notes before you begin the installation:</p>\n<ul>\n<li>SAP Note <a href=\"/notes/70228\" target=\"_blank\">70228</a>: Add-ons: Conditions and upgrade planning</li>\n<li>SAP Note <a href=\"/notes/2011192\" target=\"_blank\">2011192</a>: Uninstalling ABAP add-ons</li>\n<li>SAP Note <a href=\"/notes/2313869\" target=\"_blank\">2313869</a>: Genifix: Support for the add-on uninstallation</li>\n</ul>\n<p>Release your transports for the SAP Note implementation to avoid locks during the uninstallation.</p>\n<p>Import the latest update of SPAM/SAINT (at least SPAM version 63).</p>\n<p>Make sure that the latest available <em>Attribute Change Package (ACP)</em> of the software component version TDAG_GF 220_600 is installed in the system.</p>\n<p>3. Manual preparation steps before uninstalling</p>\n<p>Most of the Genifix functions have been transferred to the standard enhancements for the Global Label Management (GLM) in EHS-GLM (in SAP ERP).<br/>In case you want to continue using the enhanced GLM functions, a migration to standard GLM is mandatory before uninstalling Genifix. For more information, refer to SAP Note <a href=\"/notes/1934253\" target=\"_blank\">1934253</a>.</p>\n<p>Perform the following steps on each client and repeat them after each client copy:</p>\n<ol>\n<li>With the deinstallation of the add-on the following application and transactional data is deleted. Data in deleted database tables is lost as long as you do not save it before the add-on uninstallation. You can export the data into spreadsheets using transaction SE16n (General Table Display) or SQVI (QuickViewer) for data that needs to be joined to be easily human readable.<br/>- Genifix print requests (tables /TDAG/GFM_LBLORH (header), /TDAG/GFM_LBLORD (parameter), /TDAG/GFM_LBLORO (order), /TDAG/GFM_LBLORS (serialization))<br/>- Genifix components for WWI templates (tables /TDAG/GFWWIM_CMP, /TDAG/GFWWIM_CGP, /TDAG/GFWWIM_CDT)<br/><br/></li>\n<li>Genifix background jobs<br/>Unschedule active jobs with the ABAP program name /TDAG/GF* in the job overview (SM37).<br/>Genifix background jobs are checked for their usage during the uninstallation. If released jobs are found that are still referencing /TDAG/GF objects, the uninstallation will stop with an according message (\"Background jobs check failed\"). You need to adapt or delete the remaining jobs manually.<br/><br/></li>\n<li>Genifix report symbols on WWI templates and reports<br/>WWI reporting must not use any /TDAG/GF development objects, such as table structures or function modules, to avoid runtime errors.<br/>The Customizing activities listed below can be found under Environment, Health and Safety -&gt; Basic Data and Tools -&gt; Report Definition -&gt; Report Symbols.<br/><br/>Ensure that there are no report symbols with table name starting with /TDAG/GF (the ones delivered by the add-on start with EGF* or SGF*) in the IMG activity Check and Specify Report Symbols or in the database table TCGA6.<br/>If there are no reports using these symbols, you can delete the report symbols completely.<br/>If reports using these symbols exist, copy the used table in the customer namespace and replace the table name.<br/>Ensure that there are no report symbol groups with function modules starting with /TDAG/GF* (the ones delivered by the add-on start with GF*) in the IMG activity Specify Report Symbol Groups or in the database table TCGA1.<br/>If there are no reports using these symbols, you can delete the report symbol groups completely.<br/>If reports using these symbols exist, copy the used function module in the customer namespace and replace the function module for the field Foreign key - function module.<br/>Genifix report symbols are checked for their usage on reports and templates during the uninstallation (tables ESTLS, TCGA1, TCGA6). If symbols are found, that are still based on /TDAG/GF objects, the uninstallation will stop with an according message (\"Report symbols check failed\" / \"Report symbol groups check failed\"). You need to adapt or delete the remaining symbols and groups manually.<br/><br/></li>\n<li>Genifix report symbols as GLM user entries<br/>Ensure that Genifix report symbols are not used as GLM user entries at scenario or template level. Verify that tables CCGLT_OEDL, CCGLC_MPDP, CCGLC_MPDP_NEW don't contain any entries with Genifix report symbols that are based on /TDAG/GF objects (LSYID starting with EGF* or SGF*).<br/>GLM user entries are checked for Genifix report symbols during the uninstallation (tables CCGLT_OEDL, CCGLC_MPDP, CCGLC_MPDP_NEW). If used symbols are found, that are still based on /TDAG/GF objects, the uninstallation will stop with an according message (\"Report symbols check failed\" / \"Report symbol groups check failed\"). You need to adapt or delete the remaining user entries manually.<br/>Maintain user entries at scenario level in the IMG activity <em>Environment, Health and Safety -&gt; Global Label Management -&gt; Specify Labeling Scenarios for Label Printing -&gt; Define Print Scenarios -&gt; </em>select scenario<em> -&gt; User Entries</em>.<br/>Maintain user entries at template level in transaction CG42 (<em>Edit Report Template) -&gt; </em>select template<em> -&gt; Goto -&gt; User Entry</em>.<br/><br/></li>\n<li>Genifix print scenarios<br/>Ensure that no GLM scenarios use objects from the /TDAG/GF namespace in the IMG activity <em>Environment, Health and Safety -&gt; Global Label Management -&gt; Specify Labeling Scenarios for Label Printing -&gt; Define Print Scenarios</em>.<br/>All Genifix scenarios should have been migrated to standard GLM scenarios during Genifix to GLM migration. Delete the remaining scenarios if they are not in use anymore.<br/>Genifix scenarios are checked for their usage during the uninstallation (tables CCGLC_SCEN, CCGLC_MPD, CCGLC_MPD_NEW). If scenarios are found, that are still based on /TDAG/GF objects, the uninstallation will stop with an according message (\"Print scenario check failed\"). You will need to adapt or delete the remaining scenarios manually.<br/><br/></li>\n<li>Genifix report application types<br/>Ensure that the Genifix specific report application PAPERTYPE is not used in report symbol groups in the IMG activity <em>Environment, Health and Safety -&gt; Basic Data and Tools -&gt; Report Definition -&gt; Report Symbols -&gt; Specify Report Symbol Groups</em> or in the database table TCGA1.<br/>Change all appearances of report application PAPERTYPE to MATMASTER.<br/>Ensure that the Genifix specific report application PAPERTYPE is not used in generation variants in transaction CG2B -&gt; select generation variant -&gt; <em>Goto -&gt; Application Objects</em> or in the database table ESTDO.<br/>Change all appearances of PAPERTYPE to LABELSTOCK and enter a matching object key.<br/>Genifix report application PAPERTYPE is checked for its usage during the uninstallation (tables TCGA1, ESTDO, ESTLS). If usages are found, the uninstallation will stop with an according message (\"Report applications check failed\"). You will need to adapt or delete the remaining entries manually.<br/><br/></li>\n<li>Genifix trigger implementations<br/>Ensure that the Genifix triggers for automatic print are deactivated (Process Order and Delivery Note) or don't use any /TDAG/GF objects.<br/>- Default implementation for Process Order trigger:<br/>Transaction CMOS -&gt; Project ZSAVEPP -&gt; <em>Components -&gt; Function exit</em> EXIT_SAPLCOBT_001. Deactivate or delete the enhancement.<br/>- Default implementation for Delivery Note trigger:<br/>IMG activity <em>Environment, Health and Safety -&gt; Product Safety -&gt; Report Shipping -&gt; Basic Settings for Shipping from SD Documents -&gt; Settings for the SD Interface: Shipping Documents -&gt; Output Determination</em><br/>  -&gt; <em>Check Output Types</em> -&gt; Output Type ZGFD. Delete the output type.<br/>  -&gt; <em>Check Output Determination Procedures</em> -&gt; select Procedure V10000 -&gt; Control. Delete the row with Condition Type ZGFD.<br/>Transaction VV22 -&gt; Output Type ZGFD -&gt; Enter -&gt; Execute -&gt; Delete all entries.<br/>Revise these steps accordingly in case of a custom implementation of these triggers.<br/>In case further custom triggers are implemented (e.g. Handling Unit), ensure they are also deactived or don't use any /TDAG/GF objects.<br/><br/></li>\n<li>Genifix labeling workbench functions<br/>Ensure that the GLM labeling workbench (transaction CBGLWB) does not include any business processes that use /TDAG/GF objects in the IMG activity <em>Environment, Health and Safety -&gt; Global Label Management -&gt; Define Layout of Function Workbench</em>.<br/>Genifix business processes that are to be reused in GLM must be copied in the customer namespace and /TDAG/GF objects must be replaced.<br/>GLM labeling workbench business functions are checked for usage of /TDAG/GF objects during the uninstallation (table CCGLC_WBC_BP). If usages are found, the uninstallation will stop with an according message (\"Labeling workbench functions check failed\"). You will have to adapt or delete the remaining entries manually.<br/><br/></li>\n<li>Genifix component database<br/>Genifix components for WWI templates will be deleted during the uninstallation.<br/>Genifix components for CG42 layouting are not reusable in GLM. Ensure that all components are migrated to the GLM building block database and/or deleted from the Genifix component database.<br/>Genifix components can be exported in transaction CG42 -&gt; select template -&gt; Goto Document -&gt; Component tab -&gt; select component -&gt; Export.<br/>Adapt the export XML file from Genifix format to GLM format:<br/>Genifix format:<br/>&lt;GF_GENIFIX_WWI VERSION=\"1.00\" SAPSystemType=\"Unicode\" SAPCodePage=\"U2L\" DOC_NAME=\"GFWWIS_COMPONENT\"&gt;<br/>  &lt;ROW NUMBER=\"1 \"&gt;<br/>    &lt;COL NAME=\"MANDT\"&gt;001&lt;/COL&gt;<br/>    &lt;COL NAME=\"COMPID\"&gt;block-id-key&lt;/COL&gt;<br/>    &lt;COL NAME=\"GROUPID\"&gt;group-id&lt;/COL&gt;<br/>    &lt;COL NAME=\"COMPNAME\"&gt;block-id&lt;/COL&gt;<br/>    &lt;COL NAME=\"COMPCOMMENT\"&gt;block-desc&lt;/COL&gt;<br/>    &lt;COL NAME=\"LANGU\"&gt;EN&lt;/COL&gt;<br/>    &lt;COL NAME=\"DOKAR\"/&gt;<br/>    &lt;COL NAME=\"DOKNR\"/&gt;<br/>    &lt;COL NAME=\"DOKVR\"/&gt;<br/>  &lt;/ROW&gt;<br/>&lt;/GF_GENIFIX_WWI&gt;<br/><br/>GLM format:<br/>&lt;building_block&gt;<br/>  &lt;desc&gt;<br/>    &lt;group_id&gt;group-id&lt;/group_id&gt;          &lt;=&gt; &lt;COL NAME=\"GROUPID\"&gt;<br/>    &lt;block_id&gt;block-id&lt;/block_id&gt;             &lt;=&gt; &lt;COL NAME=\"COMPNAME\"&gt;<br/>    &lt;langu_iso&gt;EN&lt;/langu_iso&gt;                 &lt;=&gt; &lt;COL NAME=\"LANGU\"&gt;<br/>    &lt;description&gt;block-desc&lt;/description&gt; &lt;=&gt; &lt;COL NAME=\"COMPCOMMENT\"&gt;<br/>  &lt;/desc&gt;<br/>&lt;/building_block&gt;<br/><br/>GLM building blocks can be imported in transaction CG42 -&gt; select template -&gt; Goto Document -&gt; Building Block Catalog tab -&gt; Import Block.<br/>Genifix components are checked during the uninstallation (tables /TDAG/GFWWIM_CGP, /TDAG/GFWWIM_CMP, /TDAG/GFWWIM_CDT). If components are found, the uninstallation will stop with an according message (\"WWI layout components check failed\"). You will have to delete the remaining entries manually.<br/><br/></li>\n<li>Ensure that no development objects from the /TDAG/GF namespace are used for user exits in the IMG activity <em>Environment, Health and Safety -&gt; Basic Data and Tools -&gt; Basic Settings -&gt; Manage User Exits</em> or in the database table TCGUEFU.<br/>This would lead to runtime errors when the functionality is used. If you want to keep user exits that are delivered with function modules in the /TDAG/GF namespace, copy the function modules in the customer namespace, ensure that there are no references to development objects in the /TDAG/GF namespace and adapt the customizing.<br/><br/></li>\n<li>In case a new role was created in the customer namespace from the SAP roles starting with /TDAG*, unassign the role from users and then delete the role via transaction PFCG.<br/>Ensure that no authorization objects use /TDAG/GF authorization fields in transaction SU21 or in database table TOBJ.<br/>Genifix authorization fields are checked during the uninstallation. If used fields are found, the uninstallation will stop with an according message (\"Authorization fields check failed\"). You will have to adapt or delete the affected authorization objects manually.<br/><br/></li>\n<li>The usage of coding from software component TDAG_GF in local coding will stop the add-on uninstallation and might lead to runtime errors in other applications if dynamic calls (that might not be found by the add-on uninstallation) are not cleaned up. The software component contains packages starting with /TDAG/GF. Use the add-on piece list to see in detail which development objects will be deleted. To do so you can open the Installation and Support Package Directory via Goto from the Add-On Installation Tool (transaction SAINT). Select the OCS package TDAGGF and open the object lists for the piece lists below.<br/>You can use report RS_ABAP_SOURCE_SCAN to search for the usage of string /TDAG/ in local objects (package $TMP). Check if found results are related to this add-on.</li>\n</ol>\n<p><span> </span>4. Uninstalling the add-on with SAINT</p>\n<p>You can use the Add-On Installation Tool to uninstall add-ons. Deletion of some add-ons is only possible if certain prerequisites apply, however.</p>\n<p>Prerequisites</p>\n<ol>\n<li>You are logged on to client 000.</li>\n<li>You have entered transaction code SAINT to start the <em>Add-On Installation Tool</em>.</li>\n<li>The add-on you want to delete is displayed in the tab page of components that can be uninstalled.</li>\n<li>Your system is configured correctly and you have imported the latest update of SPAM/SAINT.</li>\n<li>You have read this SAP Note carefully and have followed the instructions.</li>\n</ol>\n<p>Procedure</p>\n<ol>\n<li>Choose the tab page for components that can be uninstalled.</li>\n<li>From the list, select the add-on that you want to uninstall.</li>\n<li>Choose Start to start the uninstallation process.</li>\n<li>A confirmation prompt describes potential dangers of uninstallation and refers to an SAP Note with additional important information. Be sure to read this SAP Note and follow its instructions.</li>\n<li>If you have read the SAP Note and followed its instructions, choose \"Yes\". To display the SAP Note, choose \"Display\". To cancel the uninstallation process, choose \"Cancel\".</li>\n<li>After you start the uninstallation process, the Add-On Installation Tool runs a predefined sequence of phases. Should an error occur in any of these phases, the uninstallation process is stopped and the error is described to the extent possible. Once you have corrected the problem, you can choose \"Continue\" to continue the uninstallation process.</li>\n<li>At first, the Add-On Installation Tool performs preparation and check steps. If the errors cannot be corrected during these phases, you can choose \"Back\" to stop and reset the uninstallation process. In later phases, when the system has already made changes and deletions, a reset is no longer possible and the system issues an appropriate error message. In this case, you have to correct any errors and then complete the uninstallation process.</li>\n<li>Once the add-on has been uninstalled successfully, you can choose \"Logs\" to see the import logs or choose \"Exit\" to complete the uninstallation process.</li>\n</ol>", "noteVersion": 1, "refer_note": [{"note": "1934253", "noteTitle": "1934253 - Enhanced functions for EHS Global Label Management (GLM) - release information note (RIN)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to use the enhanced functions in the EHS module <em>Global Label Management</em>.</p>\n<p>The enhancements in the EHS module <em>Global Label Management</em> allow you to use significantly more business processes for labeling than in the previous version. In particular, the new automation concept enables the integration of label generation into different business processes such as a delivery, process order, or handling unit. All labeling jobs can now be managed centrally. Flexible labeling scenarios enable output on Windows Wordprocessor Integration (WWI) and SAPspool. The newly introduced building block catalog reduces efforts for the generation of label templates.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Global Label Management<br/>GLM GLMplus GLM+<br/>Labeling Workbench<br/>LWB<br/>CBGLWB</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Enhanced <em>Global Label Management</em> is available in these SAP ERP releases:</p>\n<ul>\n<li><span>SAP Enhancement Package 8 for SAP ERP 6.0</span></li>\n<li><span>SAP Enhancement Package 7 for SAP ERP 6.0</span></li>\n<li><span>SAP Enhancement Package 6 for SAP ERP 6.0, Version for SAP HANA, Support Package 3</span></li>\n<li><span>SAP Enhancement Package 6 for SAP ERP 6.0, Support Package 8</span></li>\n<li><span>SAP Enhancement Package 5 for SAP ERP 6.0, Support Package 11</span></li>\n</ul>\n<p>The enhancements for the label layout in <em>EH&amp;S WWI</em> are available as of:</p>\n<ul>\n<li><span>SAP GUI für Windows Release 730 Patch Level 6</span></li>\n</ul>\n<p>The enhancements for print job processing in <em>EH&amp;S WWI</em> are available as of</p>\n<ul>\n<li><span>SAP EH&amp;S WWI Support Package SP31. For more information, see SAP Note 1895061.</span></li>\n</ul>\n<p>With regard to the following business function:</p>\n<ul>\n<li><span>LOG_EHS_GLM_CI_4</span></li>\n</ul>\n<p>This business function must be activated.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Activation</strong></p>\n<p>Following the activation of the business function LOG_EHS_GLM_CI_4, you can find enhanced <em>Global Label Management</em> in the SAP menu at the following path:<br/><em>Logistics -&gt; Environment, Health and Safety -&gt; Global Label Management</em></p>\n<p>For the Customizing activities for <em>Global Label Management</em>, navigate to the SAP Customizing Implementation Guide (transaction SPRO) under <em><br/>Environment, Health and Safety -&gt; Global Label Management</em></p>\n<p><strong>Known problems</strong></p>\n<p>If the business function LOG_EHS_GLM_CI_4 is activated during the import of <em>SAP Enhancement Package 7 for SAP ERP 6.0 Support Package 1</em>, the import terminates with an error. This is due to BC sets with errors. The correction to this problem is delivered with SAP Note 1911054.</p>\n<p><strong>Recommended SAP Notes</strong></p>\n<p>SAP Note 1927428 - Adjustments for the user interface of the labeling workbench</p>\n<p><strong>Further documentation</strong></p>\n<p><span>Documentation in the SAP Help Portal</span> <a href=\"http://help.sap.com/ehs\" target=\"_blank\">help.sap.com/ehs</a><br/>Choose <em>SAP EHS Management as part of SAP Enhancement Package 07 for SAP ERP<br/></em>and for the<em> SAP Library </em>the relevant language. The list of chapters appears on the left-hand side of the screen.<br/>Choose <em>Global Label Management (EHS-SAF-GLM).</em></p>\n<p><span>Documentation in SAP Solution Manager <br/></span>In SAP Solution Manager, the section <em>Global Label Management</em> contains descriptions of the individual process steps, transactions, configurations, and test cases.</p>\n<p><span>RKT Learning Map</span><br/>There is a lot of documentation in SAP Service Marketplace (<a href=\"http://service.sap.com\" target=\"_blank\">http://service.sap.com</a>) at <em>Early Adoption -&gt; Ramp-Up Knowledge Transfer</em>. Choose <em>SAP Business Suite -&gt; SAP Business Suite Innovations 2013 -&gt; SAP EHP 7 for SAP ERP 6.0 -&gt; EHS Management - Global Label Management. </em>Note the prerequisites for using RKT documentation.</p>\n<p><em> </em></p>", "noteVersion": 4}, {"note": "2313869", "noteTitle": "2313869 - Genifix: Support for add-on uninstallation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Component: EHS-SAF-GLM<br/>Module: Genifix<br/><br/>You have installed the add-on Genifix (TDAGGF). You want to uninstall Genifix using transaction SAINT.<br/>This SAP Note provides a plug-in for the uninstallation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span class=\"SNO_DNT\" translate=\"no\">﻿Genifix, plugin, AddOn﻿, /TDAG/GFTLCL_S4_AOF_PLUGIN</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>Reason</strong></p>\n<p>Add-on uninstallation</p>\n<p><strong>Prerequisites</strong></p>\n<p>Genifix add-on TDAGGF version 220_600<br/><br/>For information about the validity of the corrections, see the correction instructions.</p>\n<p>In addition, all of the prerequisites from SAP Note <a href=\"/notes/2011192\" target=\"_blank\">2011192</a>must be met.</p>\n<p>Use the latest version of SPAM/SAINT.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The \"Reference to Support Packages\" section specifies the Support Packages that contain the corrections.</p>\n<p>Alternatively, you can implement the attached correction instructions.<br/><br/>Perform the manual steps in SAP Note <a href=\"/notes/2307907\" target=\"_blank\">2307907</a>.<br/><br/>Test the behavior with the program /TDAG/GFTLR_S4_SIMUL_REMOVAL.<br/>This program simulates the checks and changes in the system without you saving.</p>", "noteVersion": 3}, {"note": "2011192", "noteTitle": "2011192 - Uninstallation of ABAP add-ons", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note provides information about uninstalling ABAP add-ons with the SAP Add-On Installation Tool (transaction SAINT).</p>\n<p><strong>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</strong></p>\n<p><strong>Due to the rapidly increasing number of uninstallable </strong></p>\n<p><strong>add-ons, this list is not complete. <strong><strong>Additional SAP Notes about uninstallable add-ons </strong></strong></strong></p>\n<p><strong><strong>are located in the reference list of this SAP Note.</strong></strong></p>\n<p><strong><strong><strong>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</strong> </strong></strong></p>\n<p><strong>A list of deletable objects is contained in the attachment \"Deletable_Objects\". The </strong></p>\n<p><strong>list is updated with every SPAM update. </strong></p>\n<p><strong>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAINT, add-on uninstallation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>As of Version 0053 of the SAP Add-On Installation Tool, not only can you install ABAP add-ons, but you can also uninstall them again in certain circumstances.<br/>The following sections provide the most important information about uninstalling ABAP add-ons as well as a list of previously available add-ons that can be uninstalled (<strong>examples</strong>).</p>\n<p><strong>Caution</strong>: The uninstallation of ABAP add-ons can result in unintentional data loss. Read this SAP Note thoroughly before starting an uninstallation. It contains further information and steps that might be required for the preparation and postprocessing of the uninstallation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Technical Prerequisites:</strong></p>\n<ul>\n<li>The system is based on SAP NetWeaver Release 7.0 or above.</li>\n<li>You have installed at least SPAM/SAINT Version 0053.</li>\n<li>You use a kernel with at least Release 7.2.</li>\n<li>The transport tool tp has at least Version 380.07.22.</li>\n<li>The transport tool R3trans has at least the version from AUG/06/2013.</li>\n<li>The last ACP of the add-on to be uninstalled has been installed in the system.</li>\n</ul>\n<p>During the uninstall process, the system checks and ensures the existence of the minimum versions of the kernel, tp, and R3trans.</p>\n<p><strong>Uninstall process:</strong></p>\n<p>When you trigger the uninstall process for an add-on with the SAP Add-On Installation Tool, the tool searches the system for content that belongs to the add-on that is to be deleted:</p>\n<ul>\n<li>All content contained in installation packages, upgrade packages, and support packages of the add-on</li>\n<li>Content generated automatically by SAP Notes for the add-on</li>\n<li>Content created manually in development packages that belong to the add-on</li>\n</ul>\n<p>Based on this, the tool creates a list of objects that must be deleted to uninstall the add-on. The tool also checks the following:</p>\n<ul>\n<li>Are there any dependencies between the objects in the piece list and other content?</li>\n<li>Have the objects been modified by the customer?</li>\n<li>Are the objects active?</li>\n<li>Do the objects belong to different software components?</li>\n</ul>\n<p>If objects with errors are identified during these checks, these objects cannot be deleted until the errors have been corrected. This occurs at the end of the check phase, partly automatically and partly manually.</p>\n<p>When all checks have been successfully completed, the SAP Add-On Installation Tool removes the objects to be deleted from the system and changes the system status accordingly.</p>\n<p>The product data of the add-on can then be deleted using the CISI process as described in SAP Note 1816146.</p>\n<p><strong></strong> <strong>List of ABAP add-ons that can be uninstalled:</strong></p>\n<p><strong><em>CAUTION:</em></strong> <em>This list is not complete. Please do not make any changes or additions. </em></p>\n<p>Since the deletion of add-ons without errors cannot be ensured only by using the functions of the SAP Add-On Installation Tool, the add-ons must fulfill the relevant prerequisites, must be explicitly earmarked for deletion, and must be flagged as deletable.</p>\n<p>Currently, the following add-ons can be uninstalled:</p>\n<ul>\n<li>ARIBA CLOUD INT SAP ERP 1.0 (component ARBCI1 100, ARBCI2 100, SAP Note 3243704)</li>\n<li>Ariba Network Integration 1.0 for SAP Business Suite (components ARBFNDI1 100, ARBFNDI2 100, ARBSRMI1 100, ARBERPI1 100, SAP Note 2067891)</li>\n<li>Cloud Lifecycle Management 100, SAP Note 2398413</li>\n<li>Concur Integration (components CTE_HCM 100, CTE_FIN 100, CTE_INV 100 , CTE_FND 100, CTE_FGM 100, SAP Note 2313330)</li>\n<li>Concur Integration (components CTE_HCM 10S , CTE_FIN 10S, CTE_INV 10S, CTE_FND 10S, SAP Note 2407737)</li>\n<li>DATA XCHANGE SWISS UTILITY 1.0 (component IDXPF 604, SAP Note 2525068)</li>\n<li>Desktop Connection for SAP CRM (component CRMGWS 700, SAP Note 2200413)</li>\n<li>Duet Enterprise 2.0 (component IW_TNG 200, SAP Note 2503641)</li>\n<li>Flexible Benefits for Utilities UI 600 (component FB4R 600, SAP Note 2152381)</li>\n<li><span 107%;=\"\" ar-sa;=\"\" arial;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;\"=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" verdana',sans-serif;=\"\">IDEX F SWISS ELECTRIC COMP 1.0 (component IDEXCH 604, SAP Note 2609756)</span></li>\n<li><span 107%;=\"\" ar-sa;=\"\" arial;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;\"=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" verdana',sans-serif;=\"\">Integration to ERP 6.0 Mobile Instance value application ERP SD 601 (component </span><span 107%;=\"\" ar-sa;=\"\" arial;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;\"=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" verdana',sans-serif;=\"\">MIVERPSD 601, SAP Note 2313327)</span></li>\n<li>RCS Asset Accounting Multiple Calendar Tool (component MTO 100, SAP Note 2132260)</li>\n<li>Regulatory reporting 3.0 by iBS (component RRA 300_BA70, SAP Note 2244901)</li>\n<li>SAP Access Approver 2.0.0 (component LWMGRC01 100, SAP Note 2662186)</li>\n<li>SAP ACCESS CONTROL 10.1/SAP Process CONTROL 10.1 (component GRCPIERP V1100_700, SAP Note 2456300, components GRCPINW V1100_731 &amp; 710 &amp; 700, SAP Note 2453593, components GRCFND_A V800 &amp; V1100, SAP Note 2463636)</li>\n<li>SAP ACCESS CONTROL 10.0/SAP Process CONTROL 10.0 (component GRCPIERP V1000_700, SAP Note 2456300, components GRCPINW V1000_700 &amp; V1000_731, SAP Note 2453593, component GRCFND_A V800, SAP Note 2463636)</li>\n<li>SAP Add-On Assembly Kit 4.0 (component AOFTOOLS 400_*, SAP Note 921103)</li>\n<li>SAP Add-On Assembly Kit 5.0 (component AOFTOOLS 500_7*, SAP Note 2179441)</li>\n<li>SAP Advanced Track and Trace for Pharmaceuticals 1.0 (component STTP 100, SAP Note 2441827)</li>\n<li>SAP AR Warehouse Picker 1.0, QR code generator (component NWMQC01 100, SAP Note 2202923)</li>\n<li>SAP Citizen Connect 1.0.0 (component LWMCR002 700, SAP Note 2206380)</li>\n<li>SAP Cloud for Customer 1208 integration with SAP ERP (component CODEXTCT 600, SAP Note 2373504)</li>\n<li>SAP Cloud for Customer 4.0, integration with SAP ERP (component CODEXTCT 600, SAP Note 2373504 and NWSEXTFW 600, SAP Note 2228009)</li>\n<li>SAP Cloud for Travel and Expense integration 4.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\n<li>SAP Cloud for Travel and Expense integration 4.0 (components TEMODFI 600, OTM_EXTR 600, ODTHCMER 600, ODTGEN 600, ODTFINCO 600, ODTFINCC 600, and DCFLPROC 600, SAP Note 2227939)</li>\n<li>SAP Cloud for Travel and Expense integration 5.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\n<li>SAP Cloud for Travel and Expense integration 5.0 (components TEMODFI 600, OTM_EXTR 600, ODTHCMER 600, ODTGEN 600, ODTFINCO 600, ODTFINCC 600, and DCFLPROC 600, SAP Note 2227939)</li>\n<li>SAP Configure, Price, and Quote for Solution Sales Configuration 2.0 (component SLCE 607, SAP Note 2437183)</li>\n<li>SAP Cross-Channel Order Management for Retail 2.0 (component WOM 200, SAP Note 2529018)</li>\n<li>SAP Customer Financial Fact Sheet 2.0.0 (component LWMFI001 600, SAP Note 2373183)</li>\n<li>SAP Deduction Management Component 6.0 (Component GSCDMC 600, SAP Note 2189971)</li>\n<li>SAP DMP 2.0 (component FMFMS 604, SAP Note 2319813)</li>\n<li>SAP DMP 2.0 (component PSFMS 600, SAP Note 2319803)</li>\n<li>SAP EHP1 for SAP NetWeaver Mobile 7.3 (component SUPDOE 731, SAP Note 2420945)</li>\n<li>SAP EHP 4 for SAP ERP 6.0, localization for Russia for public sector accounting (components GSEAFS 100 &amp; 604, SAP Note 2410217)</li>\n<li>SAP EHS Management, Add-In for Genifix 2.2 (component TDAGGF 220_600, SAP Note 2307907)</li>\n<li>SAP Employee Lookup 2.0.0 (component MIVHREMP 601, SAP Note 2356000)</li>\n<li>SAP ERP Add-On for MULTICHANNEL UTILITIES/PS 1.0 (component UMCERP01 604, SAP Note 2461782)</li>\n<li>SAP ERP add-on for shop floor dispatching and monitoring tool 1.0 (component SFDM_ABAP 100, SAP Note 2261544)</li>\n<li>SAP ERP Order Status 600 (component LWMSD001 600, SAP Note 2333985)</li>\n<li>SAP ERP Quality Issue 1.0.0 (component LWMQAMMI 600, SAP Note 2318477)</li>\n<li>SAP EWM 9.3 (components SCM_EXT 713 &amp; SCMB_EXT 713, SAP Note 2403053)</li>\n<li>SAP EWM 9.3/9.4 (component SCM_BASIS 713/714, SAP Note 2403053)</li>\n<li>SAP EWM 9.4 (components SCMEWMUI 940 &amp; SCMEWM 940, SAP Note 2360806)</li>\n<li>SAP Extended Warehouse Management 9.3 (components SCMEWM UI 930 &amp; SCMEWM 930, SAP Note 2360806, component SCM_BASIS 713 &amp; 714, SAP Note 2403053, component SCM_EXT 713, SAP Note 2403053, component QIE 200, SAP Note 2403694)</li>\n<li>SAP Fiori App Implementation Foundation 1.0 (component SAPUIFT 100, SAP Note 2622613)</li>\n<li>SAP Fiori 1.0 for SAP Demand Signal Management (component UIDSM001 100, SAP Note 2409404)</li>\n<li>SAP Fiori Front-End Server 2.0 (component UIBAS001 100, SAP Note 2256000)</li>\n<li>SAP Fiori 1.0 for SAP Business Suite foundation component (component UIFND001 100, SAP Note 2217323)</li>\n<li>SAP Fiori 1.0 for SAP Commercial Project Management (component CPD001 100, SAP Note 2217279)</li>\n<li>SAP Fiori 1.0 for SAP Customer Activity Repository retail applications bundle (components UIRAP001 100 and UISCAR01, SAP Note 2217230)</li>\n<li>SAP Fiori 1.0 for SAP Demand Signal Management (components UIDDF001 100 and UIDDF0011 100, SAP Note 2409404)</li>\n<li>SAP Fiori 1.0 for SAP EHS Management (component UIEHSM01 100, SAP Note 2203691)</li>\n<li>SAP Fiori 1.0 for SAP ERP HCM (component UIHR001 100, SAP Note 2167372)</li>\n<li>SAP Fiori 1.0 for SAP Event Management (component UIEM001 100, SAP Note 2209418)</li>\n<li>SAP Fiori 1.0 for SAP HANA Live for SAP Advanced Planning and Optimization (component UIHSCM01 100, SAP Note 2176724)</li>\n<li>SAP Fiori 1.0 for SAP HANA Live for SAP solutions for GRC (component UIHGRC01 100, SAP Note 2200172)</li>\n<li>SAP Fiori 1.0 for SAP hybris Marketing (component UICUAN 100, SAP Note 2200656)</li>\n<li>SAP Fiori 1.0 for SAP Information Lifecycle Management (component UIILM001 100, SAP Note 2195035)</li>\n<li>SAP Fiori 1.0 for SAP Master Data Governance (component UIMDG001 100, SAP Note 2209288)</li>\n<li>SAP Fiori 1.0 for SAP Portfolio and Project Management (component UIPPM001 100, SAP Note 2176725)</li>\n<li>SAP Fiori 1.0 for SAP solutions for GRC (component UIGRC001 100, SAP Note 2176696)</li>\n<li>SAP Fiori 1.0 for SAP SRM (component UISRM200 100, SAP Note 2176792)</li>\n<li>SAP Fiori 1.0 for the SAP Simple Finance add-on for SAP Business Suite powered by SAP HANA (component UIFSCM70 100, SAP Note 2144806, und component UIAPFI70 100, SAP Note 2144806)</li>\n<li>SAP Fiori 2.0 for SAP Customer Activity Repository retail applications bundle (component UICAR001 100, SAP Note 2433703)</li>\n<li>SAP Fiori for request approvals 1.0 (component UIX01CA1 100, SAP Note 2217255)</li>\n<li>SAP Fiori for SAP DMIS 1.0 (component UICSLO01 100, SAP Note 2209387)</li>\n<li>SAP Fiori for SAP ERP 1.0 (components UIEAAP01 100, UIEAPS01 100, UIFICA01 100, UIGLT001 100, UIISPSCA 100, UIRT401 100, SAP Note 2134432)</li>\n<li>SAP Fiori for SAP ERP HCM 1.0 (components GBX01HR 600 and GBX01HRS5 605, SAP Note 2180598)</li>\n<li>SAP Fiori for SAP S/4HANA 1709 (component UIS4HOP1 300, SAP Note 2408541)</li>\n<li>SAP Fiori oData component for Approve Purchase Orders 1.0 (component GBAPP002 600, SAP Note 2131368)</li>\n<li>SAP Fiori oData component for Approve Purchase Contracts 1.0 (component SRA001 600, SAP Note 2128051)</li>\n<li>SAP Fiori oData component for Approve Timesheets 1.0 (component SRA010 600, SAP Note 2131301)</li>\n<li>SAP Fiori oData component for Approve Travel Expenses 1.0 (component SRA008 600, SAP Note 2131274)</li>\n<li>SAP Fiori oData component for Approve Travel Requests 1.0 (component SRA009 600, SAP Note 2131278)</li>\n<li>SAP Fiori oData component for Change Sales Orders 1.0 (component SRA003 600, SAP Note 2131814)</li>\n<li>SAP Fiori oData component for Check Price and Availability 1.0 (component SRA016 600, SAP Note 2131351)</li>\n<li>SAP Fiori oData component for Create Sales Orders 1.0 (component SRA017 600, SAP Note 2131352)</li>\n<li>SAP Fiori oData component for Customer Invoices 1.0 (component SRA021 600, SAP Note 2131364)</li>\n<li>SAP Fiori oData component for My Benefits 1.0 (component SRA007 600, SAP Note 2131187)</li>\n<li>SAP Fiori oData component for My Paystubs 1.0 (component SRA006 600, SAP Note 2131186)</li>\n<li>SAP Fiori oData component for My Spend 1.0 (component SRA012 600, SAP Note 2131303)</li>\n<li>SAP Fiori oData component for My Timesheet 1.0 (component SRA002 600, SAP Note 2131147)</li>\n<li>SAP Fiori oData component for My Travel Requests 1.0 (component SRA004 600, SAP Note 2131183)</li>\n<li>SAP Fiori oData component for Order from Requisitions 1.0 (component SRA013 600, SAP Note 2131310)</li>\n<li>SAP Fiori oData component for Track Purchase Orders 1.0 (component SRA020 600, SAP Note 2131360)</li>\n<li>SAP Fiori oData component for Track Sales Orders 1.0 (component SRA018 600, SAP Note 2131353)</li>\n<li>SAP Fiori oData component for Track Shipments 1.0 (component SRA019 600, SAP Note 2131358)</li>\n<li>SAP Fiori principal apps 1.0 for SAP SRM (component UIX01SRM 100, SAP Note 2217260)</li>\n<li>SAP Fiori principal apps for SAP ERP 1.0 - Central App (component UIX01EAP 100, SAP Note 2167334)</li>\n<li>SAP Fiori principal apps for SAP ERP 1.0 - HCM (component UIX01HCM 100, SAP Note 2091515, component GBHCM002 600, SAP Note 2131381, component GBHCM003, SAP Note 2131383)</li>\n<li>SAP Fiori principal apps for SAP ERP 1.0 - Travel (component UIX01TRV 100, SAP Note 2167334)</li>\n<li>SAP Fiori Travel Expense Approval 2.0 (component GBTRV002 600, SAP Note 2124793)</li>\n<li>SAP Fiori UI SAP ANALYTICAL SERVICES 1.0 (component UISSB001 100, SAP Note 2266130)</li>\n<li>SAP Fiori UI Approve Leave Requests 1.0 (component UIHCM003 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Purchase Contracts 1.0 (component UISRA001 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Purchase Orders 1.0 (component UIAPP002 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Requests 1.0 (component UIGIB001 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Requisitions 1.0 (component UIAPP001 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Shopping Carts 1.0 (component UISRM001 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Timesheets 1.0 (component UISRA010 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Travel Expenses 1.0 (component UISRA008 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Approve Travel Requests 1.0 (component UISRA009 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Change Sales Orders 1.0 (component UISRA003 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Check Price and Availability 1.0 (component UISRA016 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Create Sales Orders 1.0 (component UISRA017 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Customer Invoices 1.0 (component UISRA021 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI for SAP ERP, add-on for embedded production planning and detailed scheduling 1.0 (component UIPPDS01 100, SAP Note 2280928)</li>\n<li>\n<div>SAP Fiori for SAP Fashion Management 1.0 (component UIFM001 100, SAP Note 2229796)</div>\n</li>\n<li>SAP Fiori UI for SAP S/4HANA Finance 1605 (component UIAPPL01 100, SAP Note 2280900)</li>\n<li>SAP Fiori UI for SAP Master Data Governance 1.0 (component <span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">UIMDC001 100</span>, SAP Note <span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\">2230429</span>)</li>\n<li>SAP Fiori UI for SAP SCM 1.0 (component SCMB_UI 100, SAP Note 2203656)</li>\n<li>SAP Fiori UI for SAP Simple Finance On-Premise Edition 1503 (component UIAPPFI702 600, SAP Note 2238575)</li>\n<li>SAP Fiori UI for SAP Supply Network Collaboration 1.0 (component SCMSNCE1 100, SAP Note 2176346)</li>\n<li>SAP Fiori UI for SAP S/4HANA (component UIX01CA1 200, SAP Note 2280715)</li>\n<li>SAP Fiori UI My Benefits 1.0 (component UISRA007 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Leave Requests 1.0 (component UIHCM002 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Paystubs 1.0 (component UISRA006 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Shopping Cart 1.0 (component UISRA014 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Spend 1.0 (component UISRA012 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Timesheet 1.0 (component UISRA002 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI My Travel Requests 1.0 (component UISRA004 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Order from Requisitions 1.0 (component UISRA013 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Track Purchase Orders 1.0 (component UISRA020 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Track Sales Orders 1.0 (component UISRA018 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Track Shipments 1.0 (component UISRA019 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI Track Shopping Carts 1.0 (component UISRA011 100, SAP Note 2034588)</li>\n<li>SAP Fiori UI SAP ANALYTICAL SERVICES 1.0 (component UISSB001 100, SAP Note 2266130)</li>\n<li>SAP Fiori transactional apps 1.0 for SAP CRM (components UIX02CRM 100 and UICRM001 100, SAP Note 2189023)</li>\n<li>SAP Fiori transactional apps for SAP ERP 1.0 (component UIX02EA4 100, SAP Note 2125074, component UIX02EAP 100, SAP Note 2125074, and component UIX02RT4 100, SAP Note 2125074)</li>\n<li>SAP Fiori/Hana Live Content for ERP 100 (component UIHERP01 100, SAP Note 2131580)</li>\n<li>SAP Fiori/Hana Live Content for Sentiment Analysis 100 (component UIHFND01 100, SAP Note 2132758)</li>\n<li>SAP Funding Management 3.0 (component 300, SAP Note 2198972)</li>\n<li>SAP GATEWAY 2.0 (components IW_CBS 200 &amp; IW_CNT 200, SAP Note 2312680, component IW_FNDGC 100, SAP Note 2319096, component IW_GIL, SAP Note <span class=\"sapUxAPObjectPageHeaderIdentifierContainer\" id=\"__xmlview2--idObjectPageHeader-identifierLineContainer\">2319114</span>, component IW_SPI 100, SAP Note 2313222 &amp; component IW_SCS 200, SAP Note 2319097, component WEBCUIF 746 &amp; 747 &amp; 748, SAP Note 2417905)</li>\n<li>SAP Global Batch Trace 1.0 ERP Integration (component GBTRINT 100, SAP Note 2149065)</li>\n<li>SAP Global Trade Services (GTS) 10.0 (component SLL-LEG 900, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 10.1 (component SLL-LEG 901, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 7.0 (component SLL-LEG 7.00, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 7.1 (component SLL-LEG 710, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 7.2 (component SLL-LEG 720, SAP Note 2356692)</li>\n<li>SAP Global Trade Services (GTS) 8.0 (component SLL-LEG 800, SAP Note 2356692)</li>\n<li>SAP Global Trade Services, identity-based preference processing 1.0 (add-on IBPP_100, SAP Note 2795075, add-on IBPP_PI 100, SAP Note 2832367)</li>\n<li>SAP Global Trade Services, identity-based preference processing 2.0 (add-on IBPP_200, SAP Note 2795075, add-on IBPP_PI 200 and IBPP_PI 800, SAP Note 2832367)</li>\n<li>SAP GRC Access Control 5.3 (components VIRSANH 530_700 &amp; 530_731, SAP Note 2536230)</li>\n<li>SAP GTS 11.0 (component SLL-LEG V1100, SAP Note 2356692)</li>\n<li>SAP In-Store Product Lookup 1.0.0 (component LWMRT401 604, SAP Note 2209914)</li>\n<li>SAP Manager Insight 1.0.0 (component LWMHR401 604, SAP Note 2611360)</li>\n<li>SAP Management of Change 1.0 (component MOC 100, SAP Note 2355120)</li>\n<li>SAP MOB FIELD SERV ERP INT 2.0.0 (component MCRMFERP 200, SAP Note 2370516)</li>\n<li>SAP Multichannel Foundation for Utilities and Public Sector 1.0 (component UMCUI501 100, SAP Note 2217321)</li>\n<li>SAP Multiresource Scheduling 9.0 (components MRSS 900 and MRSS_NW 900, SAP Note 2169600)</li>\n<li>SAP NETWEAVER 7.5 (component BW4HANA 100, SAP Note 2246699)</li>\n<li>SAP NetWeaver Master Data Management 7.1 (component MDM_TECH 710_700, SAP Note 2342708)</li>\n<li>SAP Payment Approvals 2.0.0 (component LWMFI401 604, SAP Note 2571634)</li>\n<li>SAP PC 10.1 For SAP NW (component POASBC 100_731, SAP Note 2322477)</li>\n<li>SAP ProductivityPak by RWD adapter 1.0 for SAP Solution Manager 7.0 (component ST-SPA, SAP Note 1109650)</li>\n<li>SAP QIM 1.0 (component QAM 100, SAP Note 2357898)</li>\n<li>SAP REACH COMPLIANCE 2.0 (component TDAGBCA 200_600, SAP Note 2298456)</li>\n<li>SAP RealSpend 1.0 (component LWMSIM01 600, SAP Note 2234715)</li>\n<li><span 107%;=\"\" ar-sa;=\"\" arial;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" line-height:=\"\" minor-bidi;\"=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" verdana',sans-serif;=\"\">SAP Retail Execution integration with SAP CRM (component MOB_CRM 100, SAP Note 2324260)</span></li>\n<li>SAP Screen Personas 1.0 (component PERSOS 100, SAP Note 2226123)</li>\n<li>SAP Screen Personas 2.0 (component PERSOS 200, SAP Note 2226123)</li>\n<li>SAP Screen Personas 3.0 (component PERSONAS 300, SAP Note 2246593)</li>\n<li>SAP Solution Manager adapter for SAP Quality Center 1.0 by HP (component ST-QCA, SAP Note 1109650)</li>\n<li>SAP Smart Business 1.0 for retail promotion execution (component UISRTL01 100, SAP Note 2201852)</li>\n<li>SAP Smart Business 1.0 for SAP CRM (component UISCRM01 100, SAP Note 2176772)</li>\n<li>SAP Smart Business 1.0 for SAP ERP (component UISERP01 100, SAP Note 2176775)</li>\n<li>SAP Smart Business 1.0 for SAP Fashion Management (component UISFM001 100, SAP Note 2201905)</li>\n<li>SAP Smart Business 1.0 for SAP Information Lifecycle Management (component UISDTG01 100, SAP Note 2201850)</li>\n<li>SAP Smart Business 1.0 for SAP PLM (component UIHPLM01 100, SAP Note 2201843)</li>\n<li>SAP Smart Business 1.0 foundation component (component UISAFND1 100, SAP Note 2201856)</li>\n<li>SAP Smart Business 1.0, component for KPI modeling (component UISKPI01 100, SAP Note 2176779)</li>\n<li>SAP Smart Business for the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (component UIHSFIN01 100, SAP Note 2201846)</li>\n<li>SAP Supplier Lifecycle Management 1.0 (component SMCERPIT 100, SAP Note 2192644)</li>\n<li>SAP Supplier Lifecycle Management 2.0 (component SMCERPIT 100, SAP Note 2192644)</li>\n<li>SAP Supplier Lifecycle Management 3.0 (component SMCERPIT 100, SAP Note 2192644)</li>\n<li>SAP Travel Expense Report 1.0.0 (component GBTRV001 600, SAP Note 2162719)</li>\n<li>SAP Travel Receipt Capture 2.0.0 (component MIVHRTRV 601, SAP Note 2195588)</li>\n<li>SAP Travel OnDemand integration 1.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\n<li>SAP Travel OnDemand integration 1.0 (components TEMODFI 600 and DCFLPROC 600, SAP Note 2227939)</li>\n<li>SAP Travel OnDemand integration 3.0 (components NWSEXTFW 600, TEMEXFIN 600, and TEMEXHCM 600, SAP Note 2228009)</li>\n<li>SAP Travel OnDemand integration 3.0 (components TEMODFI 600, ODTFINCO 600, and DCFLPROC 600, SAP Note 2227939)</li>\n<li>SAP Workforce Deployment for Retail and Wholesale Distribution 1.0 (component WFMCORE 200, SAP Note 2409846, component LCAPPS 2005_700, SAP Note 2424906)</li>\n<li>SAP Utilities Customer Engagement 1.0.0, 2.1.0 &amp; 2.1.0, cloud edition (component MUTILCRM 100, SAP Note 2388251)</li>\n<li>SRM Shopping Cart Approval 700 (component GBSRM001 700, SAP Note: 2362137)</li>\n<li>SuccessFactors Employee Central Payroll 1.0 (component Cloud Pay 100, SAP Note 2457573)</li>\n<li>SuccessFactors Integration Add-on 1.0 (component SFIHCM01 600, SAP Note: 2375289)</li>\n<li>SuccessFactors Integration Add-on 2.0 (component SFIHCM02 600, SAP Note: 2375320)</li>\n<li>SuccessFactors Integration Add-on 3.0 (component SFIHCM03 600, SAP Note: 2276816)</li>\n<li>Web interface for SAP EHS Management 2.6 (component TDAGWI 260_600, SAP Note 2307624)</li>\n</ul>\n<p><strong>More information:</strong></p>\n<p>For detailed information about the uninstall process, see transaction SAINT in your ABAP system.</p>", "noteVersion": 60}, {"note": "1620452", "noteTitle": "1620452 - Genifix: Central note", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note contains release information for Genifix 220 (previously TechniData Genifix 2.2).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Genifix, Support Package, SP, service pack, patch level</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note provides basic knowledge in relation to installation steps, upgrade steps, and configuration steps.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Genifix 220 supports the following operating systems:<br/>Microsoft Windows Server 2003 (32/64 bit)<br/>Microsoft Windows Server 2008 (32/64 bit)<br/>Microsoft Windows Server 2012 (64 bit)<br/>Microsoft Windows XP (32/64 bit)<br/>Microsoft Windows Vista (32/64 bit)<br/>Microsoft Windows 7 (32/64 bit)<br/>Microsoft Windows 8 (32/64 bit)<br/><br/>Genifix 220 requires one of the following Microsoft Word releases:<br/>Microsoft Word 2003<br/>Microsoft Word 2007<br/>Microsoft Word 2010 (32/64 bit)<br/>Microsoft Word 2013 (32/64 bit)</p>\n<p><br/>Note the following: The TechniData Labeling printer driver is included in the delivery of Genifix as a 32 bit version and a 64 bit version.<br/>On the 64 bit operating system, you must use the 64 bit version of the driver. The special architecture of the driver makes printing with a 64 bit version of Microsoft Word a prerequisite.<br/><br/>Note the following: Genifix 220 supports the following languages:<br/>- English (default language)<br/>- German<br/><br/>The software package contains an installation guide in which all of the steps required for the installation of the Genifix are described.<br/><br/>The software package is available in the SAP Software Download Center at<br/><br/>http://service.sap.com/swdc<br/>-&gt; Installations and Upgrades<br/>-&gt; Browse our Download Catalog<br/>-&gt; SAP Application Components<br/>-&gt; SAP EHS Management<br/>-&gt; TechniData legacy applications<br/>-&gt; EHS-GENIFIX 2.2<br/><br/>From here, you can also get to the Support Packages for the Genifix Windows component that are available at<br/><br/>-&gt; Related topics for EHS-GENIFIX 2.2<br/>-&gt; Support Packages and Patches<br/>-&gt; EHS-GENIFIX 2.2<br/><br/>We strongly recommend that you always import the most current Support Package and apply the most current patch level. Error corrections are delivered via the patch level and are limited to the most current version of a Support Package.<br/><br/>The release dates for the Genifix 220 patch levels of the most current Support Package are listed below.<br/><br/>Release information for Genifix 220:<br/><br/>Support Package 01, patch level 00 is available as of July 6, 2012.<br/>Support Package 01, patch level 01 is available as of July 8, 2013.<br/>Support Package 01, patch level 02 is available as of May 20, 2014.<br/><br/><strong>Note the end of maintenance for Genifix on December 31, 2014.</strong> For more information, see SAP Note 1978839.</p></div>", "noteVersion": 7}]}, {"note": "2198406", "noteTitle": "2198406 - Pre-Transition Checks for software component EA-APPL", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>Pre-Transition Checks for software component EA-APPL</span></p>\n<p><strong><span>1. Checks for Industrial Hygiene and Safety</span></strong></p>\n<ul>\n<li><span>Check ID: EHS_IHS_IH: </span><span>Industrial Hygiene and Safety is not supported in S/4HANA. Therefore, the infotypes 'Exposure Group' and 'Work Area' are no longer supported in Organizational Management.</span></li>\n<li><span>Check ID: EHS_IHS_IAL: Incident/accident log entries</span><span><span> are not supported in S/4HANA. However, you can export your  Incident/accident log entries from your SAP ERP system and import them into Environment, Health, and Safety as part of S/4HANA. <br/>For more information, see attached migration information.<br/></span></span></li>\n</ul>\n<p><strong><span>2. Checks for Occupational Health</span></strong></p>\n<ul>\n<li><span>Check ID:  EHS_HEA: </span><span>Occupational Health is not supported in S/4HANA. </span></li>\n</ul>\n<p><span><strong><span>3. Checks for Waste Management</span></strong></span></p>\n<ul>\n<li><span><span><span>Check ID:  EHS_WA: <span>Waste Management is not supported in S/4HANA. </span></span></span></span></li>\n</ul>\n<p><span><strong><span>4. Checks for EC - EAM Integration</span></strong></span></p>\n<ul>\n<li><span><span><span>Check ID:  XAP_EM: Integration from SAP Environmental Compliance 3.0 to SAP ERP Industrial Hygiene work areas <span>is not supported in S/4HANA</span>.</span></span></span></li>\n<li><span><span><span><span><span><span>Check ID:  XAP_EM: Integration</span></span></span> from SAP Enterprise Asset Management (SAP EAM) functional locations or equipments to <span>SAP Environmental Compliance 3.0</span> is not supported in S/4HANA.<br/><br/></span></span></span></li>\n</ul>\n<p><span><strong><span>5. Checks for Selection Criteria in Substance Volume Tracking</span></strong></span></p>\n<ul>\n<li><span><span><span>Check ID:  SAP_EA_APPL_PSS_SVT_CHECKS: <span> Using fields of the tables VBUP and VBUK as selection criteria for substance volume tracking is not supported in S/4HANA</span>.<br/></span></span></span></li>\n</ul>\n<p>You use fields of the tables VBUP and VBUK as selection criteria for substance volume tracking. Due to a data model change in S/4HANA, the tables VBUK and VBUP are not supported anymore.</p>\n<p>The selection criteria which relate to the status of a sales document need to be defined based on the corresponding header and item tables (VBAK, VBAP, LIKP, and LIPS). Adjust the selection criteria accordingly in the IMG activity \"Specify Selection Criteria for Volume Tracking\".</p>\n<p><span><strong><span>6. Checks for Enterprise Search Model for Searches within EHS Specification Data Base</span></strong></span></p>\n<ul>\n<li><span><span><span>Check ID:  SAP_EA_APPL_PSS_ESH_CHECKS: Using the Enterprise Search model for the EHS specification data base is not supported in S/4HANA.</span></span></span></li>\n</ul>\n<p>You use the Enterprise Search model for the EHS specification data base (based on data replication). With SAP S/4HANA the Enterprise Search models within SAP Product Lifecycle Management (SAP PLM) are now replication free search models (using HANA DB tables instead).</p>\n<p>Replace the replication based Enterprise Search Model for the EHS specification data base with the replication free search models.</p>\n<p><span><strong><span>7. Checks for Genifix Add-On Installation</span></strong></span></p>\n<ul>\n<li><span><span><span>Check ID:   SAP_EA_APPL_PSS_GENIFIX_CHECKS:  Add-On Genifix is no longer supported and needs to be deinstalled</span></span></span></li>\n</ul>\n<p>The check detected that the Genifix Add-On is installed in your system. With SAP S/4HANA, on-premise edition 1511 this add-on is not supported anymore.</p>\n<p>You need to uninstall the Genifix Add-On. The uninstallation via the SAP Add-On Installation Tool (SAINT) is currently in work.</p>\n<p>SAP Note <a href=\"/notes/2221779\" target=\"_blank\">2221779</a> will contain all relevant details regarding the uninstallation as soon as the deinstallation is possible.</p>\n<p><span><strong><span>8. Checks for EHS Web-Interface Add-On Installation</span></strong></span></p>\n<ul>\n<li><span><span><span>Check ID:  SAP_EA_APPL_PSS_WEB_INF_CHECKS:  Add-On EHS Web-Interface is no longer supported and needs to be deinstalled</span></span></span></li>\n</ul>\n<p><span><span><span>The check detected that the EHS Web-Interface Add-On is installed in your system. With SAP S/4HANA, on-premise edition 1511 this add-on is not supported anymore.</span></span></span></p>\n<p>You need to uninstall the Web-Interface Add-On. SAP Note <a href=\"/notes/2307624\" target=\"_blank\">2307624</a> contains all relevant details regarding the uninstallation.</p>\n<p><span><strong><span>9. Checks for Compliance For Products CFP Add-On and SAP Product and REACH Compliance (SPRC) Add-On Installation</span></strong></span></p>\n<ul>\n<li><span><span><span>Check ID:  SAP_EA_APPL_PSS_SPRC_CHECKS:  Add-Ons CfP and SPRC are no longer supported and need to be deinstalled</span></span></span></li>\n</ul>\n<p><span><span><span>The check detected that the SAP Compliance for Products (CFP) Add-On or SAP Product and REACH Compliance (SPRC) Add-On is installed in your system. With SAP S/4HANA, on-premise edition 1511 these add-ons are not supported anymore.</span></span></span></p>\n<p>You need to uninstall the CFP or SPRC Add-On. SAP Note <a href=\"/notes/2298456\" target=\"_blank\">2298456</a> contains all relevant details regarding the uninstallation.</p>\n<p><strong><span><span><span>10. Checks for Business Partners with the partner function of the MSDS recipient</span></span></span></strong></p>\n<ul>\n<li><span><span><span>Check ID: SAP_EA_APPL_PSS_BUSINESS_PARTNER_CHECKS: Using of the SAP Business Partners with the partner function of the MSDS recipient<br/><br/></span></span></span></li>\n</ul>\n<p>You use the report shipping functionality, and you have defined contact persons as safety data sheet (SDS) recipients within the customer master.</p>\n<p>During the conversion of an ERP system to an SAP S/4HANA system, customer master data is synchronized with SAP Business Partner data. As part of this data synchronization, contact person data in the table <em><cite>Customer Master Contact Partner</cite> </em>(KNVK) is replicated to the table <cite>BP Relationship: Contact Person Relationship</cite> (BUT051). In order to ensure that contact persons with the function <cite>SDS recipient</cite> are synchronized correctly with the corresponding business partner table, additional configuration steps have to be carried out.</p>\n<p>Prerequisites</p>\n<ul>\n<li>\n<div>Within configuration for Environment Health and Safety, under  <em>Basic Data and Tools -&gt; Basic Settings -&gt; Specify Environment Parameters</em> , you have specified a value for the environment parameter <em>Function number for MSDS contact person (SRE_DS_SDSREC)</em>, for example the value SR.</div>\n</li>\n</ul>\n<ul>\n<li>\n<p>You have defined the same code as a function for contact persons under  <em>Sales and Distribution -&gt; Master Data -&gt; Business Partners -&gt; Contact Person -&gt; Define Contact Person Functions</em>.</p>\n</li>\n<li>\n<p>You have maintained contacts persons with this function code in the customer master.</p>\n</li>\n</ul>\n<p>This SDS recipient function code must also be maintained as the contact person function within the SAP Business Partner configuration and the Master Data synchronization for Customer/Vendor Integration. SAP recommends you use the same function code for SDS contact persons in the customer master and for the corresponding function for SAP business partner contact persons. To do so, carry out the following:</p>\n<p>1. Define an SAP business partner function for SDS recipients.</p>\n<p>The corresponding setting for the contact person function can be found in Customizing under  <em>Cross-Application Components -&gt; SAP Business Partner -&gt; Business Partner Relationships -&gt; Contact Person -&gt; Define Functions</em>.</p>\n<p>2. Link the customer master contact person function for SDS recipients to the corresponding business partner contact person function.</p>\n<p class=\"sapkwdita-p1\">The corresponding settings for master data synchronization can be found in Customizing under  <em>Cross-Application Components -&gt; Master Data Synchronization -&gt; Customer/Vendor Integration -&gt; Business Partner Settings -&gt; Settings for Customer Integration -&gt; Field Assignment for Customer Integration -&gt; Assign Attributes Contact Person -&gt; Assign Functions of Contact Person.</em></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC<br/>S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement SAP Note <a href=\"/notes/2198401\" target=\"_blank\">2198401</a> \"S4TC EA-APPL - Checks for software component EA-APPL\". The checks for software component EA-APPL will be executed during the transition process.</p>", "noteVersion": 11, "refer_note": [{"note": "2221776", "noteTitle": "2221776 - SAP S/4HANA Simplification Item: EHS Web-Interface Add-On", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have implemented own (custom) coding using the objects belonging to the add-on <em>EHS Web Interface</em>.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA simplification</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>EHS Web Interface is not available in S/4HANA. All objects belonging to the add-on are removed with the uninstallation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Remove any references to these objects in your custom code before the migration to SAP S/4 HANA.</p>\n<p>Where still required, replace them with equivalent objects in your customer namespace.</p>\n<p>See note <a href=\"/notes/2307624\" target=\"_blank\">2307624</a> for more details on the uninstallation and preparatory steps.</p>", "noteVersion": 4}, {"note": "2198401", "noteTitle": "2198401 - S4TC EA-APPL Master Check for S/4 System Conversion Checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span>Pre-Transition Checks for software component EA-APPL have to be executed before the upgrade to S/4.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC<br/>S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>S/4 transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note. The checks for software component EA-APPL will be executed during the transition process.</p>", "noteVersion": 14}, {"note": "2221717", "noteTitle": "2221717 - SAP S/4HANA Simplification Item: SAP Product and REACH Compliance (SPRC) Add-On", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You have implemented own (custom) coding using the objects belonging to the add-on SAP Product and REACH Compliance (SPRC).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4 simplification</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP Product and REACH Compliance is not available in S/4HANA. All objects belonging to the add-on are removed with the uninstallation.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Remove any references to these objects in your custom code before the migration to SAP S/4 HANA.</p>\n<p>Where still required, replace them with equivalent objects in your customer namespace.</p>\n<p>See note <a href=\"/notes/2298456\" target=\"_blank\">2298456</a> for more details on the uninstallation and preparatory steps.</p>\n<p> </p>", "noteVersion": 4}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "The Genefix add-on is replaced by a successor solution in SAP component SAP Global Label Management (EHS-SAF-GLM), adjust the processses"}, {"Activity": "Data cleanup / archiving", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "You need to uninstall the add-on, follow SAP Note 2307907"}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "Remove the references to the Genefix add-on from your Custom Code following SAP Note 2221779"}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}