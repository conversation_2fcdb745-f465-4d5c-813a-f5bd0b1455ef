{"guid": "0050569455E21ED5B3E176783913A09E", "sitemId": "SI18: Logistics - PLM", "sitemTitle": "S4TWL - PH_Sample iView", "note": 2271201, "noteTitle": "2271201 - S4TWL - PH_Sample iView", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The iView was as an option for Pharmaceutical Sales Reps who were running CRM Mobile Sales to manage their inventory of free samples. This functionality is not available within SAP S/4HANA. No functional equivalent is avialble.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business Processes using this solution will no longer work. An alternative solution has to be developed in a customer project.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Customers using the solution have to develop their own UI on their platform of choice to be deployed to mobile devices of their sales reps.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>The iView was meant to be used to provide pharmaceutical sales reps with the ability to review their stock of drug samples (van stock). It provides a view into the standard ERP inventory.</p>\n<p>This note is relevant for customers usign the SAP Portal and have configured this iView.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"378\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"378\">\n<p>As this is an iView, customers were not able to develop custom code</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Business processes using this solution will no longer work. An alternative solution has to be developed in a customer project."}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "The iView for Pharmaceutical Sales Reps to manage their inventory of free samples is not available within SAP S/4HANA. No functional equivalent is avialble. Consider alternative solutions"}, {"Activity": "Custom Code Adaption", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Customers using the solution have to develop their own UI on their platform of choice to be deployed to mobile devices of their sales reps, therefore this should be considered before the conversion project"}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}