{"guid": "00109B1315FA1ED8ADE47F8B5ED720D3", "sitemId": "SI033: CRM", "sitemTitle": "Account Identification", "note": 2693504, "noteTitle": "2693504 - Account Identification", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are considering or planning a migration of a CRM 7.0 or CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. The following information on changed or deprecated functionality is relevant in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRMTWL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Search by employee is not supported in the account identification. The search by Installed Base/Object has been replaced by the search by equipment (see also Note 2693595).</p>", "noteVersion": 2, "refer_note": [{"note": "2693595", "noteTitle": "2693595 - Individual objects not supported; equipment and functional location used as reference object", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are considering or planning a migration of an SAP CRM 7.0 or SAP CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. In this case, the following information about changed or deprecated functionality is relevant.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRMTWL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Objects, sometimes also referred to as Individual Objects or IObjects, are no longer supported in SAP S/4HANA Service transactions. They are based on the SAP product master model and hence are blacklisted (also see SAP Note 2691651). IObjects were previously used as reference objects in service transactions and were downloaded from equipments or functional locations. Equipments and functional locations, as well as products, can now directly be entered as reference objects.</p>\n<p>Installed Base (IBase) or Installed Base components are not supported as reference objects.</p>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span>You can check whether objects are maintained in your SAP CRM 7.0 system by checking if there are records in table COMM_PRODUCT where the field OBJECT_FAMILY is not initial.</span></p>", "noteVersion": 4, "refer_note": [{"note": "2691651", "noteTitle": "2691651 - S/4HANA Service exclusively uses S/4 material master; SAP product master not supported", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are considering or planning a migration of a CRM 7.0 or CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. The following information on changed or deprecated functionality is relevant in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRMTWL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>All S/4HANA Service transactions use now the S/4 material master instead of the SAP product master. It is no longer possible to maintain products in the SAP product master; all corresponding transactions and APIs have been blacklisted. Service products are now modeled via S/4 material master; they are distinguished from physical products through a new product type code which is an attribute of the material type. A couple of attributes relevant for service processes have been added to the material master data model.</p>\n<p>As a consequence, the set-type technology is no longer supported; customers have to use the extensibility concept of the S/4 material master for custom fields.<br/>Also, the maintenance of arbitrary product hierarchies and categories is no longer supported. Only the product hierarchy from material master is used in S/4HANA Service transactions.</p>\n<p><strong>Business Value</strong></p>\n<p>The harmonization of the product master between S/4 and CRM makes synchronization or replication of products through the CRM Middleware completely obsolete, which results in a considerable reduction of TCO. With the transition to the S/4 products, customers also benefit from the latest developments, such as Fiori apps, CDS views or services.</p>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span>This item is relevant if you are using products in CRM. Technically, this is the case if the root table COMM_PRODUCT contains records.</span></p>", "noteVersion": 3}]}]}