{"guid": "6EAE8B28C6E11ED7BEF498D664EFE0D4", "sitemId": "SI24: CMM_MTM_Derivatives", "sitemTitle": "S4TWL - Mark-to-Market & Profit-and-Loss Reporting for Derivatives", "note": 2596369, "noteTitle": "2596369 - S4TWL - Mark-to-Market & Profit-and-Loss Reporting for Derivatives", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Commodity Risk Management, Commodity Mark-to-Market Reporting for Derivatives, Commodity Profit -and -Loss Reporting for Derivatives</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA the Commodity Risk Management provides Markt-to-Market and Profit-and-Loss Reporting for the Financial Transaction Management. All analytical data and analytical results are persisted based on versioned data records and exposed through Core Data Servcies (DCS) to the analytical tools like SAP Analysis for Microsoft Excel .</p>\n<p><strong>Business Process related information</strong></p>\n<p>In the Business Suite the Financial Transaction data that is relevant for the analytical source data for Markt-to-Market and Profit-and-Loss reporting was stored in Market Risk Analyzer (MRA) data base tables. The analytical results were provided in the framework of the MRA functionality 'Determination of Single Records' and stored in the Result Data Base.</p>\n<p>In S/4HANA 1709 FPS02 there is a new data flow to support analytical reporting. The Financial Transaction data is updated automatically from all relevant Financial Transaction business processes into new data base tables. In case of an error it is possible to monitor messages in the application log and to re-process.</p>\n<p>In S/4HANA 1709 FPS02 the analytical reporting key figures are provided in one single technology that provides a reporting CDS stack in order to allow Mark-to-Market and Profit-and-Loss snapshot reporting across commodities.</p>\n<p> </p>\n<p><strong>Functional Restrictions</strong></p>\n<p>In S/4HANA 1709 FPS02 the Position, Markt-to-Market and Profit-and-Loss Reporting for Financial Transactions does not support option delta factors.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>For the Simplification in Markt-to-Market and Profit-and-Loss Reporting for Financial Transactions a couple of steps need to be considered:</p>\n<ul>\n<li>historical position data can not be migrated to the new S/4HANA analytical data model. The data should be exported BEFORE the S/4HANA upgrade to keep it accessible from an XLS or BW InfoCube</li>\n<li>Financial Transaction data can be loaded initially into the database tables which is used in S/4HANA 1709 FPS2 onwards for Markt-to-Market and Profit-and-Loss reporting</li>\n<li>if BEX Queries on top of Operational Data Providers are used in the Business Suite, these BEX Queries need to be converted into CDS Queries in S/4HANA on top of the new CDS InterfaceViews</li>\n<li>if CDS Queries have been used for Markt-to-Market and Profit-and-Loss Reporting in the Suite, these need to be converted into CDS Queries in S/4HANA on top of the new CDS InterfaceViews</li>\n</ul>", "noteVersion": 3, "refer_note": [{"note": "2226096", "noteTitle": "2226096 - Simplification List: Commodity Management in SAP S/4HANA OP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><span 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" line-height:=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">Commodity Management is activated in your system and you are planning to migrate to SAP S/4HANA. A pre-check of the migration preparation indicates, that you are not able to convert to SAP S/4HANA with this installation. </span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Commodity Management, Commodity Pricing Engine (CPE), Commodity Procurement, Commodity Sales, Commodity Risk Management, Commodity Derivatives, Commodity Dervative Contract Specification (DCS), Commodity Configurable Parameters and Formulas (CPF)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>Target Release SAP S/4HANA, on-premise edition 1511 and SAP S/4HANA 1610:</strong></p>\n<p>If SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\n<p>If one or more of the following Business Functions are active in your system, you cannot migrate to SAP S/4HANA:</p>\n<ul>\n<li>LOG_CPE_SD_MM</li>\n<li>LOG_CPE_FA_FE</li>\n<li>LOG_MM_COMMODITY</li>\n<li>LOG_MM_COMMODITY_02</li>\n<li>LOG_MM_COMMODITY_03</li>\n<li>LOG_MM_COMMODITY_04</li>\n<li>LOG_MM_COMMODITY_05</li>\n<li>LOG_SD_COMMODITY</li>\n<li>LOG_SD_COMMODITY_02</li>\n<li>LOG_SD_COMMODITY_03</li>\n<li>LOG_SD_COMMODITY_04</li>\n<li>LOG_SD_COMMODITY_05</li>\n<li>ISR_GLT_CMMINTEG</li>\n<li>ISR_GLT_CMMINTEG_2</li>\n<li>ISR_GLT_IFW</li>\n<li>LOG_TRM_INT_1</li>\n<li>LOG_TRM_INT_2</li>\n<li>FIN_TRM_COMM_RM</li>\n<li>FIN_TRM_COMM_RM_2</li>\n<li>FIN_TRM_COMM_RM_3</li>\n<li>FIN_TRM_COMM_RM_4</li>\n<li>FIN_TRM_COMM_RM_5</li>\n<li>FIN_TRM_COMM_RM_6</li>\n<li>COMMODITY_PROC_SALES_01</li>\n<li>COMMODITY_MANAGEMENT_01</li>\n<li>COMMODITY_MANAGEMENT_02</li>\n<li>COMMODITY_MANAGEMENT_03</li>\n</ul>\n<p>These Business Functions support the following main business processes and features:</p>\n<ul>\n<li>Commodity Management Integration in Sales E2E Process and Documents</li>\n<li>Commodity Management Integration in Procurement E2E Process and Documents</li>\n<li>Commodity Management Integration in Global Trade Management</li>\n<li>Commodity Pricing / Commodity Pricing Engine (CPE) / Configurable Parameters &amp; Formulas (CPF)</li>\n<li>Basis &amp; Future Pricing</li>\n<li>Differential / Final Invoicing &amp; Billing</li>\n<li>Commodity master data</li>\n<li>Market data management for commodity prices and price curves</li>\n<li>Exposure Management for commodity price risk exposures</li>\n<li>Financial transaction management for commodity derivatives</li>\n<li>Market Risk Analyzer functionality for commodity derivatives and commodity price risk exposures</li>\n<li>Day End / Month End Processing</li>\n<li>Commodity Analytics (End of Day, Position Reporting, Mark to Market, Profit and Loss)</li>\n</ul>\n<p> </p>\n<p><strong>Target Release SAP S/4HANA 1709 (on premise):</strong></p>\n<blockquote>\n<p><strong>FPS0:</strong></p>\n<p>If the following SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\n<p>If one or more of the following Business Functions are active in your system, you cannot migrate to SAP S/4HANA:</p>\n<ul>\n<li>ISR_GLT_CMMINTEG</li>\n<li>ISR_GLT_CMMINTEG_2</li>\n<li>ISR_GLT_IFW</li>\n<li>LOG_TRM_INT_1</li>\n<li>LOG_TRM_INT_2</li>\n<li>FIN_TRM_COMM_RM</li>\n<li>FIN_TRM_COMM_RM_2</li>\n<li>FIN_TRM_COMM_RM_3</li>\n<li>FIN_TRM_COMM_RM_4</li>\n<li>FIN_TRM_COMM_RM_5</li>\n<li>FIN_TRM_COMM_RM_6</li>\n<li>COMMODITY_MANAGEMENT_01</li>\n<li>COMMODITY_MANAGEMENT_02</li>\n<li>COMMODITY_MANAGEMENT_03</li>\n<li>COMMODITY_MANAGEMENT_04</li>\n</ul>\n<p>A conversion is possible if only Business Function of the following list are activated:</p>\n<ul>\n<li>LOG_CPE_SD_MM</li>\n<li>LOG_CPE_FA_FE</li>\n<li>LOG_MM_COMMODITY</li>\n<li>LOG_MM_COMMODITY_02</li>\n<li>LOG_MM_COMMODITY_03</li>\n<li>LOG_MM_COMMODITY_04</li>\n<li>LOG_MM_COMMODITY_05</li>\n<li>LOG_SD_COMMODITY</li>\n<li>LOG_SD_COMMODITY_02</li>\n<li>LOG_SD_COMMODITY_03</li>\n<li>LOG_SD_COMMODITY_04</li>\n<li>LOG_SD_COMMODITY_05</li>\n<li>COMMODITY_PROC_SALES_01</li>\n</ul>\n<p>If the requirements for a migration are fulfilled, then additional simplification items are relevant, please see also the notes:</p>\n<ul>\n<li>2461007       S4TWL - CM: CPE Simplified Data Model</li>\n<li>2461004       S4TWL - CM: CPE Simplified CPE Activation</li>\n<li>2461021       S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\n<li>2461014       S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\n<li>2460737       S4TWL - CPE Simplified Market Data</li>\n</ul>\n<p><strong>FPS1:</strong></p>\n<p>If the following SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\n<p>If one or more of the following Business Functions are active in your system, you cannot migrate to SAP S/4HANA:</p>\n<ul>\n<li>ISR_GLT_CMMINTEG</li>\n<li>ISR_GLT_CMMINTEG_2</li>\n<li>ISR_GLT_IFW</li>\n</ul>\n<p>A conversion is possible if only Business Function of the following list are activated:</p>\n<ul>\n<li>LOG_CPE_SD_MM</li>\n<li>LOG_CPE_FA_FE</li>\n<li>LOG_MM_COMMODITY</li>\n<li>LOG_MM_COMMODITY_02</li>\n<li>LOG_MM_COMMODITY_03</li>\n<li>LOG_MM_COMMODITY_04</li>\n<li>LOG_MM_COMMODITY_05</li>\n<li>LOG_SD_COMMODITY</li>\n<li>LOG_SD_COMMODITY_02</li>\n<li>LOG_SD_COMMODITY_03</li>\n<li>LOG_SD_COMMODITY_04</li>\n<li>LOG_SD_COMMODITY_05</li>\n<li>COMMODITY_PROC_SALES_01</li>\n<li>LOG_TRM_INT_1</li>\n<li>LOG_TRM_INT_2</li>\n<li>FIN_TRM_COMM_RM</li>\n<li>FIN_TRM_COMM_RM_2</li>\n<li>FIN_TRM_COMM_RM_3</li>\n<li>FIN_TRM_COMM_RM_4</li>\n<li>FIN_TRM_COMM_RM_5</li>\n<li>FIN_TRM_COMM_RM_6</li>\n<li>COMMODITY_MANAGEMENT_01</li>\n<li>COMMODITY_MANAGEMENT_02</li>\n<li>COMMODITY_MANAGEMENT_03</li>\n<li>COMMODITY_MANAGEMENT_04</li>\n</ul>\n<p>If the requirements for a migration are fulfilled, then additional simplification items are relevant, please see also the notes:</p>\n<ul>\n<li>2461007       S4TWL - CM: CPE Simplified Data Model</li>\n<li>2461004       S4TWL - CM: CPE Simplified CPE Activation</li>\n<li>2461021       S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\n<li>2461014       S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\n<li>2460737       S4TWL - CPE Simplified Market Data</li>\n<li>2551943       S4TWL - CM: Simplified Commodity Curves</li>\n<li>2551960       S4TWL - CM: Simplified DCS Access</li>\n<li>2556089       S4TWL - Simplification in Position Reporting for Financial Transactions</li>\n<li>2547347       S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data</li>\n<li>2547343       S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting</li>\n<li>2556106       S4TWL - CM: Hedge Management for Logistics Transactions</li>\n<li>2556134       S4TWL - CM: Agri-Specific Commodity Position Queries</li>\n<li>2560298       S4TWL - Commodity Pricing in MM Contracts and Scheduling Agreements</li>\n<li>2555990       S4TWL - CM: Unification of Technologies for Analytical Data Provision</li>\n<li>2556063       S4TWL - CM: Commodity Price Risk Determination from Schedule Lines</li>\n<li>2556164       S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:  DCS ID Extension</li>\n<li>2556220       S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions</li>\n<li>2556176       S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics</li>\n<li>2554440       S4TWL - Simplified Commodity Management Data Model / Master Data</li>\n<li>2556103       S4TWL - CM: Mark-to-Market (MtM) and Profit/Loss (P&amp;L) Reporting</li>\n<li>2551942       S4TWL - Integration to GTM not supported</li>\n</ul>\n<p><strong>FPS2:</strong></p>\n<p>The following simplification items have to be observed in addition to the list for OP1709 FPS1. See the following notes:</p>\n<ul>\n<li>2591598       S4TWL - CM: Mark-to-Market &amp; Profit-and-Loss for logistics transactions</li>\n<li>2596369       S4TWL - Mark-to-Market &amp; Profit-and-Loss Reporting for Derivatives</li>\n<li>2608098       S4TWL - Mark to Market Accounting Changes</li>\n</ul>\n<p>Note 2556103 becomes obsolete.</p>\n</blockquote>\n<p><strong>Target Release SAP S/4HANA 1809 (on premise):</strong><strong> </strong></p>\n<blockquote>\n<p><strong>FPS0:</strong></p>\n<p>If the following SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\n<p>If one or more of the following Business Functions are active in your system, you cannot migrate to SAP S/4HANA:</p>\n<ul>\n<li>ISR_GLT_CMMINTEG_2</li>\n<li>ISR_GLT_IFW</li>\n</ul>\n<p>A conversion is possible if only Business Functions of the following list are activated:</p>\n<ul>\n<li>ISR_GLT_CMMINTEG</li>\n<li>LOG_CPE_SD_MM</li>\n<li>LOG_CPE_FA_FE</li>\n<li>LOG_MM_COMMODITY</li>\n<li>LOG_MM_COMMODITY_02</li>\n<li>LOG_MM_COMMODITY_03</li>\n<li>LOG_MM_COMMODITY_04</li>\n<li>LOG_MM_COMMODITY_05</li>\n<li>LOG_SD_COMMODITY</li>\n<li>LOG_SD_COMMODITY_02</li>\n<li>LOG_SD_COMMODITY_03</li>\n<li>LOG_SD_COMMODITY_04</li>\n<li>LOG_SD_COMMODITY_05</li>\n<li>COMMODITY_PROC_SALES_01</li>\n<li>LOG_TRM_INT_1</li>\n<li>LOG_TRM_INT_2</li>\n<li>FIN_TRM_COMM_RM</li>\n<li>FIN_TRM_COMM_RM_2</li>\n<li>FIN_TRM_COMM_RM_3</li>\n<li>FIN_TRM_COMM_RM_4</li>\n<li>FIN_TRM_COMM_RM_5</li>\n<li>FIN_TRM_COMM_RM_6</li>\n<li>COMMODITY_MANAGEMENT_01</li>\n<li>COMMODITY_MANAGEMENT_02</li>\n<li>COMMODITY_MANAGEMENT_03</li>\n<li>COMMODITY_MANAGEMENT_04</li>\n</ul>\n<p>If the requirements for a migration are fulfilled, then additional simplification items are relevant, please see also the notes:</p>\n<ul>\n<li>2461007       S4TWL - CM: CPE Simplified Data Model</li>\n<li>2461004       S4TWL - CM: CPE Simplified CPE Activation</li>\n<li>2461021       S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\n<li>2461014       S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\n<li>2460737       S4TWL - CPE Simplified Market Data</li>\n<li>2551943       S4TWL - CM: Simplified Commodity Curves</li>\n<li>2551960       S4TWL - CM: Simplified DCS Access</li>\n<li>2556089       S4TWL - Simplification in Position Reporting for Financial Transactions</li>\n<li>2547347       S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data</li>\n<li>2547343       S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting</li>\n<li>2556106       S4TWL - CM: Hedge Management for Logistics Transactions</li>\n<li>2556134       S4TWL - CM: Agri-Specific Commodity Position Queries</li>\n<li>2560298       S4TWL - Commodity Pricing in MM Contracts and Scheduling Agreements</li>\n<li>2555990       S4TWL - CM: Unification of Technologies for Analytical Data Provision</li>\n<li>2556063       S4TWL - CM: Commodity Price Risk Determination from Schedule Lines</li>\n<li>2556164       S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:  DCS ID Extension</li>\n<li>2556220       S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions</li>\n<li>2556176       S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics</li>\n<li>2554440       S4TWL - Simplified Commodity Management Data Model / Master Data</li>\n<li>2591598       S4TWL - CM: Mark-to-Market &amp; Profit-and-Loss for logistics transactions</li>\n<li>2596369       S4TWL - Mark-to-Market &amp; Profit-and-Loss Reporting for Derivatives</li>\n<li>2608098       S4TWL - Mark to Market Accounting Changes</li>\n<li>2671009       S4TWL - Integration of Pricing and Payment Events in GTM with Commodity Management</li>\n<li>2672696       S4TWL - Integration of Contract Processing Monitor(CPM) and Logistical Options(LOP) with GTM not supported</li>\n<li>2671010       S4TWL - Functionalities on roadmap for CM-GTM Integration</li>\n<li>2710843       S4TWL – Functionalities on roadmap for CM-GTM Integration (Period-End Valuation)</li>\n</ul>\n<p>Note 2551942 becomes obsolete.</p>\n<p><strong>FPS1:</strong></p>\n<p>If the following SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\n<p>If one or more of the following Business Functions are active in your system, you cannot migrate to SAP S/4HANA:</p>\n<ul>\n<li>ISR_GLT_IFW</li>\n</ul>\n<p>A conversion is possible if only Business Functions of the following list are activated:</p>\n<ul>\n<li>ISR_GLT_CMMINTEG</li>\n<li>ISR_GLT_CMMINTEG_2</li>\n<li>LOG_CPE_SD_MM</li>\n<li>LOG_CPE_FA_FE</li>\n<li>LOG_MM_COMMODITY</li>\n<li>LOG_MM_COMMODITY_02</li>\n<li>LOG_MM_COMMODITY_03</li>\n<li>LOG_MM_COMMODITY_04</li>\n<li>LOG_MM_COMMODITY_05</li>\n<li>LOG_SD_COMMODITY</li>\n<li>LOG_SD_COMMODITY_02</li>\n<li>LOG_SD_COMMODITY_03</li>\n<li>LOG_SD_COMMODITY_04</li>\n<li>LOG_SD_COMMODITY_05</li>\n<li>COMMODITY_PROC_SALES_01</li>\n<li>LOG_TRM_INT_1</li>\n<li>LOG_TRM_INT_2</li>\n<li>FIN_TRM_COMM_RM</li>\n<li>FIN_TRM_COMM_RM_2</li>\n<li>FIN_TRM_COMM_RM_3</li>\n<li>FIN_TRM_COMM_RM_4</li>\n<li>FIN_TRM_COMM_RM_5</li>\n<li>FIN_TRM_COMM_RM_6</li>\n<li>COMMODITY_MANAGEMENT_01</li>\n<li>COMMODITY_MANAGEMENT_02</li>\n<li>COMMODITY_MANAGEMENT_03</li>\n<li>COMMODITY_MANAGEMENT_04</li>\n</ul>\n<p>If the requirements for a migration are fulfilled, then additional simplification items are relevant, please see also the notes:</p>\n<ul>\n<li>2461007       S4TWL - CM: CPE Simplified Data Model</li>\n<li>2461004       S4TWL - CM: CPE Simplified CPE Activation</li>\n<li>2461021       S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\n<li>2461014       S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\n<li>2460737       S4TWL - CPE Simplified Market Data</li>\n<li>2551943       S4TWL - CM: Simplified Commodity Curves</li>\n<li>2551960       S4TWL - CM: Simplified DCS Access</li>\n<li>2556089       S4TWL - Simplification in Position Reporting for Financial Transactions</li>\n<li>2547347       S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data</li>\n<li>2547343       S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting</li>\n<li>2556106       S4TWL - CM: Hedge Management for Logistics Transactions</li>\n<li>2556134       S4TWL - CM: Agri-Specific Commodity Position Queries</li>\n<li>2560298       S4TWL - Commodity Pricing in MM Contracts and Scheduling Agreements</li>\n<li>2555990       S4TWL - CM: Unification of Technologies for Analytical Data Provision</li>\n<li>2556063       S4TWL - CM: Commodity Price Risk Determination from Schedule Lines</li>\n<li>2556164       S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:  DCS ID Extension</li>\n<li>2556220       S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions</li>\n<li>2556176       S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics</li>\n<li>2554440       S4TWL - Simplified Commodity Management Data Model / Master Data</li>\n<li>2591598       S4TWL - CM: Mark-to-Market &amp; Profit-and-Loss for logistics transactions</li>\n<li>2596369       S4TWL - Mark-to-Market &amp; Profit-and-Loss Reporting for Derivatives</li>\n<li>2608098       S4TWL - Mark to Market Accounting Changes</li>\n<li>2671009       S4TWL - Integration of Pricing and Payment Events in GTM with Commodity Management</li>\n<li>2672696       S4TWL - Integration of Contract Processing Monitor(CPM) and Logistical Options(LOP) with GTM not supported</li>\n<li>2710843       S4TWL – Functionalities on roadmap for CM-GTM Integration (Period-End Valuation)</li>\n</ul>\n<p>Note 2671010 (S4TWL - Functionalities on roadmap for CM-GTM Integration) becomes obsolete.</p>\n<p><strong>FPS2:</strong></p>\n<p>No SAP Commodity Management Business Function blocks the conversion to SAP S/4 HANA beginning with target release S/4 HANA OP 1809 FPS2.</p>\n<p>The following simplification items are furtheron relevant and have to be observed:</p>\n<ul>\n<li>2461007       S4TWL - CM: CPE Simplified Data Model</li>\n<li>2461004       S4TWL - CM: CPE Simplified CPE Activation</li>\n<li>2461021       S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\n<li>2461014       S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\n<li>2460737       S4TWL - CPE Simplified Market Data</li>\n<li>2551943       S4TWL - CM: Simplified Commodity Curves</li>\n<li>2551960       S4TWL - CM: Simplified DCS Access</li>\n<li>2556089       S4TWL - Simplification in Position Reporting for Financial Transactions</li>\n<li>2547347       S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data</li>\n<li>2547343       S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting</li>\n<li>2556106       S4TWL - CM: Hedge Management for Logistics Transactions</li>\n<li>2556134       S4TWL - CM: Agri-Specific Commodity Position Queries</li>\n<li>2560298       S4TWL - Commodity Pricing in MM Contracts and Scheduling Agreements</li>\n<li>2555990       S4TWL - CM: Unification of Technologies for Analytical Data Provision</li>\n<li>2556063       S4TWL - CM: Commodity Price Risk Determination from Schedule Lines</li>\n<li>2556164       S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:  DCS ID Extension</li>\n<li>2556220       S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions</li>\n<li>2556176       S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics</li>\n<li>2554440       S4TWL - Simplified Commodity Management Data Model / Master Data</li>\n<li>2591598       S4TWL - CM: Mark-to-Market &amp; Profit-and-Loss for logistics transactions</li>\n<li>2596369       S4TWL - Mark-to-Market &amp; Profit-and-Loss Reporting for Derivatives</li>\n<li>2608098       S4TWL - Mark to Market Accounting Changes</li>\n<li>2671009       S4TWL - Integration of Pricing and Payment Events in GTM with Commodity Management</li>\n<li>2672696       S4TWL - Integration of Contract Processing Monitor(CPM) and Logistical Options(LOP) with GTM not supported</li>\n<li>2671010       S4TWL - Functionalities on roadmap for CM-GTM Integration</li>\n<li>2757768       Deletion of business function ISR_GLT_IFW</li>\n</ul>\n<p>Note 2710843 (S4TWL – Functionalities on roadmap for CM-GTM Integration (Period-End Valuation)) becomes obsolete.</p>\n</blockquote>\n<div><strong>Target Release SAP S/4HANA 1909 (on premise) and following:</strong></div>\n<blockquote>\n<p>Observe the section for release SAP S/4HANA OP1809 FPS2.</p>\n<blockquote></blockquote>\n</blockquote>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Further Commodity Management functionality is on the roadmap for S/4 HANA.</p>\n<p>For further information please contact us on <a href=\"http://www.sap.com/products/commodity-management.html\" target=\"_blank\">www.sap.com/products/commodity-management.html</a> or write an email to <a href=\"mailto:<EMAIL>\" target=\"_blank\"><EMAIL></a></p>\n<p> </p>\n<p> </p>", "noteVersion": 9, "refer_note": [{"note": "2671009", "noteTitle": "2671009 - S4TWL – Integration of Pricing and Payment Events in GTM with Commodity Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1809 FPS0, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With SAP S/4HANA, on-premise edition 1809, the integration of Global Trade Management (GTM) with Commodity Management processes (not ACM!) is partially supported.</p>\n<p>Pricing and Payement Events integration in Commodity Management is now supported in GTM. This functionaility is encapsulated within the Business Function ISR_GLT_CMMINTEG.</p>\n<p>NOTE: For functionalities that existed earlier in business function ISR_GLT_CMMINTEG, please refer to notes 2551942 and 2671010</p>", "noteVersion": 1}, {"note": "2547347", "noteTitle": "2547347 - S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ODP, BEx Query, CDS views</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709 the database table and available infosources for commodiy price risk data required for commodity position reporting got unified and revised.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The Business Suite's <em>Commodity Position Reporting</em> used raw expsoures created from logistics documents and transactions.</p>\n<p>Based on the raw exposures, the commodity position table TEXT_CTY_EXPOS was updated for the Commodity Position Reporting. In S4H, the  data footprint and processing layers were reduced.</p>\n<p>Key figures, which were formerly exposed in different technology stacks (ODP/BEx versus CDS views) are now provided in one single technology and reporting stack only to allow a unified reporting for different commodities and scenarios.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As described in the attached '<em>Function in Details - Position Reporting on Versioned Pricing Data</em>' word file, the following steps need to be performed:</p>\n<ul>\n<li>Historical position data should be exported BEFORE the S4HANA upgrade to keep the data accessible from an XLS or BW InfoCube</li>\n<li>Logistics transactions need to be loaded to the database table, which is used in S4H 1709 FPS1 onwards for the <em>Commodity Position Reporting</em></li>\n<li>If BEx queries on top of Operational Data Providers (ODP) are used in the Business Suite, these BEx queries must be converted to CDS queries in S4H on top of the new CDS interface views</li>\n<li>If CDS queries have been used for the <em>Commodity Position Reporting</em> in the Business Suite, these queries need to be converted to CDS queries in S4H on top of the new CDS interface views</li>\n</ul>", "noteVersion": 1}, {"note": "2461007", "noteTitle": "2461007 - S4TWL -  CM: CPE Simplified Data Model", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709 the data model of the Commodity Pricing Engine has been simplified.</p>\n<p><strong>Business Process related information</strong></p>\n<ul>\n<li>The necessary conversions are done during installation</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Actions are only relevant for customer specific coding. Details are described in the attached document 'Function in Details - Simplified Data Model'.</p>", "noteVersion": 1}, {"note": "2461004", "noteTitle": "2461004 - S4TWL -  CM: CPE Simplified CPE Activation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709 the activation of the Commodity Pricing Engine (CPE), the flagging of Condition Types to be relevant for CPE and the required settings in the pricing procedure are simplified. This is especially relevant if you are using CPE together with IS-OIL.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The settings for the activation of the CPE have to be adjusted manually, if you are using CPE together with IS-OIL. If no IS-OIL is active, the conversion is done automatically during the update.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In the assigned document 'Function in Details - Simplified CPE Activation', the process how to adjust the relevant settings is described.</p>", "noteVersion": 1}, {"note": "2461021", "noteTitle": "2461021 - S4TWL -  CM: CPE Simplified Formula Assembley & Formula Evaluation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709 the Formula Assembly of the Commodity Pricing Engine (CPE) is done based on BRF+ and for Forumla Evaluation, the access to document data has been simplified.</p>\n<p><strong>Business Process related information</strong></p>\n<ul>\n<li>The configuration of Formula Assembly has to be transferred to BRF+.</li>\n<li>Customer coding using the field mapping information in Formula Evaluation has to be adjusted.</li>\n<li>The necessary adjustments are done in the target system</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ol>\n<li>In the assigned document 'Cookbook Formula Assembly based on BRFplus', the process how to transfer Formula Assembly to BRF+ is described.</li>\n<li>In the assigned document 'Function in Details - Formula Evaluation', all the necessary information how to adjust customer coding is included.</li>\n</ol>", "noteVersion": 1}, {"note": "2608098", "noteTitle": "2608098 - S4 PreChecks: Conversion Report of Mark-to-Market Accounting for transition from Business Suite to S/4 HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>S4TC Pre-Transition Checks for Mark-to-Market Accounting returns following error messages:</p>\n<p>\"Action Required in client XXX: WBRP entries available for conversion exist. Migration cannot proceed without pre-conversion. See SAP Note 2608098.\"</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4 HANA migration, Pre-Checks, LO-CMM, Conversion Report, Markt-to-Market Accounting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Mark-to-Market(MtM) Accounting uses Agency Business (AB) as a vehicle to create and reverse accounting documents. Data base optimization in S/4 HANA are performed in AB.The pre-conversion report RCMM_MTM_CONVERSION from this note executes the obligatory conversion of data for already created AB documents with MtM references. The S4TC Pre-Transition Checks for Mark-to-Market Accounting has to be successful after the execution of the pre-conversion report RCMM_MTM_CONVERSION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li>For source release SAP_APPL 618 implement this note. (Further releases up to and including S/4HANA 1709 FPS1 are added here to keep the report version in sync.)</li>\n<li>For all conversions, run the report RCMM_MTM_CONVERSION in the source system in execution mode using the check box. Do this for each client indicated in the error messages.</li>\n</ol>", "noteVersion": 3}, {"note": "2556176", "noteTitle": "2556176 - S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics Calculator", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Commodity Risk Management, Value at Risk, Market Data Shift, Market Data Scenario, Market Risk Analyzer (MRA) MRA simulation, Statistics Calculator for Volatilities, Correlations</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span>Business Process-Related Information</span></p>\n<p>In the Business Suite (ERP), the market data that was not related to derivative contracts specification (DCS) and was based on quotation name, quotation type, quotation source could be used for</p>\n<ul>\n<li>volatilities with moneyness for OTC commodity options</li>\n<li>the definition of shifts and scenarios</li>\n<li>the statistics calculator for volatilities and correlations</li>\n<li>calculation of value at risk results </li>\n<li>creation of generic transactions for market risk analyzer analytics</li>\n<li>Hedge Accounting for commodity price risks and exposures from financial and logistics transactions</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>These functions are not avaliable in S/4HANA 1709. It is planned to support these functions in a future release of Commodity Management.</p>\n<p> </p>\n<p> </p>", "noteVersion": 2}, {"note": "2672696", "noteTitle": "2672696 - S4TWL – Integration of Contract Processing Monitor(CPM) and Logistical Options(LOP) with GTM not supported", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With SAP S/4HANA, All on-premise editions, the integration of Global Trade Management (GTM) with Commodity Management processes (not ACM!) is partially supported.</p>\n<p>Contract Processing Monitor (CPM) and Logistical Options (LOP) are not supported in S/4HANA. Also, there is no equivalent available for the same.</p>\n<p>If you are okay with this limitation, you can add an exemption and continue in report /SDF/RC_START_CHECK.</p>", "noteVersion": 3}, {"note": "2556103", "noteTitle": "2556103 - S4TWL - CM: Mark-to-Market (MtM) and Profit/Loss (P&L) Reporting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA transition worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Commodity Position Reporting, CDS views</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709, data structures and reporting views for the mark-to-market (MtM) and profit and loss (PnL) valuation require adjustments.</p>\n<p>These are not available yet.</p>\n<p><strong><strong>Required and Recommended Action(s)</strong></strong></p>\n<p>If you need MtM or PnL data structures and reporting views for commodities, please consult you account manager.</p>\n<p>The required adjustments are planned for the future scope of Commodity Management.</p>", "noteVersion": 2}, {"note": "2460737", "noteTitle": "2460737 - S4TWL -  CPE Simplified Market Data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709 the Commodity Pricing Engine only supports market data based on Derivative Contract Specifications (DCS). The market data defined using Quotation Source, - Type and - Name are no longer supported.</p>\n<p><strong>Business Process related information</strong></p>\n<ul>\n<li>Quotations have to be defined based on Quotation Rules</li>\n<li>All relevant quotation rules have to be transfered to use market data based on DCS.</li>\n<li>Relevant open documents have to be closed before the conversion </li>\n<li>MM Contracts are not supported in the current version</li>\n</ul>\n<p>The assigned document 'Cookbook From Exchange to DCS' describes examples how to set up Derivative Contract Specifications (DCS).</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Source system EHP 7 and EHP 8 (Conversion to DCS based market data has to be done in the source system)</p>\n<ol>\n<li>In the assigned document 'Cookbook Market Data in Commodity Managment', the process how to replace quotation Source, -Type and - Name based market data to DCS based market data is described. This step has to be executred in the source system before starting the conversion.</li>\n<li>Accessing market data moved to the quotation rule, the definition as part of the term itself is no longer supported. All relevant settings have to be transferred to a quoation rule, assigned to the term. The assigned document 'Function in Details - Quotation Rule' describes the necessary steps to convert the data.</li>\n</ol>\n<p>Source System before EHP 7 (Conversion to DCS based market data has to be done in the target system)</p>\n<p>In the assigned document 'Conversion to DCS based Market Data before EHP7' a project based solution how to convert the data is described.</p>", "noteVersion": 1}, {"note": "2461014", "noteTitle": "2461014 - S4TWL -  CM: CPE Simplification of Routines & Rules", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709 some of the delivered routiones for the Commodity Pricing Engine (CPE) are simplified, some others are no longer available.</p>\n<p><strong>Business Process related information</strong></p>\n<ul>\n<li>All relevant routines and rules have to be adjusted.</li>\n<li>Relevant open documents have to be closed before the conversion (see attached document 'Cookbook Open Documents using not supported routines').</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In the attached document 'Cookbook Supported CPE Routines', the affected routines are described.</p>\n<p>The attached document 'Cookbook Application independent Reference Date Routines in Commodity Pricing Eingine' describes the simpliefied application independent reference date routines and the necessary steps required in conversion.</p>", "noteVersion": 1}, {"note": "2671010", "noteTitle": "2671010 - S4TWL – Functionalities on roadmap for CM-GTM Integration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\"> You are doing a system conversion to SAP S/4HANA 1809 FPS0, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With SAP S/4HANA, on-premise edition 1809, the integration of Global Trade Management (GTM) with Commodity Management processes (not ACM!) is partially supported.</p>\n<p>Risk Integration and Analytics, Free Characteristics and Contract Revaluation are not supported in S/4HANA, on premise edition 1809 FPS0. Although these functionalities are on the roadmap and are planned for future relases.</p>", "noteVersion": 3}, {"note": "2547343", "noteTitle": "2547343 - S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA transition worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Error handling, Commodity Position Reporting</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709 the data flow and persistencies of commodity price risk data derived from logistics documents and transactions have been revised.</p>\n<p><strong>Business Process related information</strong></p>\n<ul>\n<li>As described in the attached word file, a reduced number of background queues needs to be monitored for errors.</li>\n<li>Error handling for commodity position data does not require the usage of transaction LITRMS anymore.</li>\n<li>Instead, queues in error need to be monitored and re-started, after the errors were resolved.</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In the attached document, you can find information on the revised handling of background processes and thus requirements on system monitoring.</p>\n<p> </p>", "noteVersion": 1}, {"note": "2554440", "noteTitle": "2554440 - S4TWL - Simplified Commodity Management Data Model / Master Data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Commodity Risk Management, Commodity ID, Commodity Master Data, FCZZ, Financial Transactions, Real Commodity, Abstract Commodity, Physical Commodity, Derivative Contract Specification, DCS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The master data model for the commodity master data in S/4HANA has been simplified and allows a single definition of the commodity as an attribute of the Derivative Contract Specification (DCS).  This allows  a standardized access to the DCS-based market data for derivative transactions and processes, and for logistics transactions.</p>\n<p>The determination of market data by quotation name, quotation type and quotation source is deprecated in S/4HANA (see reference note).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA on-premise edition 1709 FPS1 the data model of Commodity Management master data has been simplified. The commodity as a system entity to represent traded material is defined as attribute of derivative contact specifications (DCS).  To make the deprecation of commodity master data possible, the definition of market data and the market data access were adjusted accordingly. For the assignment of the DCS and market identifier code (MIC) to financial transaction data, in order to replace the Commodity ID, all financial transactions and processes were adjusted.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The following functional areas are adjusted:</p>\n<ul>\n<li>Financial Transaction Management</li>\n<li>BAPIs for Financial Transactions</li>\n<li>FUT and LOPT  - Class Data</li>\n<li>BAPIs for Class Data   </li>\n<li>Commodity Price Adjustments</li>\n<li>Mass Cash Settlement</li>\n<li>MRA Basic Functions and Reports: Analysis Parameters, JBRX, TPM60,  AISGENKF</li>\n</ul>\n<p><strong>Functional Restrictions</strong></p>\n<p>see business impact note 2556176</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>For customer specific coding and configuration please refer to the Custom Code Impact Note. Manual conversion steps are needed. Please refer to the details that are described in the SAP Note 2553281 - Cookbook Deprecation of Commodity ID in Commodity Risk Management.</p>", "noteVersion": 1}, {"note": "2596369", "noteTitle": "2596369 - S4TWL - Mark-to-Market & Profit-and-Loss Reporting for Derivatives", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Commodity Risk Management, Commodity Mark-to-Market Reporting for Derivatives, Commodity Profit -and -Loss Reporting for Derivatives</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA the Commodity Risk Management provides Markt-to-Market and Profit-and-Loss Reporting for the Financial Transaction Management. All analytical data and analytical results are persisted based on versioned data records and exposed through Core Data Servcies (DCS) to the analytical tools like SAP Analysis for Microsoft Excel .</p>\n<p><strong>Business Process related information</strong></p>\n<p>In the Business Suite the Financial Transaction data that is relevant for the analytical source data for Markt-to-Market and Profit-and-Loss reporting was stored in Market Risk Analyzer (MRA) data base tables. The analytical results were provided in the framework of the MRA functionality 'Determination of Single Records' and stored in the Result Data Base.</p>\n<p>In S/4HANA 1709 FPS02 there is a new data flow to support analytical reporting. The Financial Transaction data is updated automatically from all relevant Financial Transaction business processes into new data base tables. In case of an error it is possible to monitor messages in the application log and to re-process.</p>\n<p>In S/4HANA 1709 FPS02 the analytical reporting key figures are provided in one single technology that provides a reporting CDS stack in order to allow Mark-to-Market and Profit-and-Loss snapshot reporting across commodities.</p>\n<p> </p>\n<p><strong>Functional Restrictions</strong></p>\n<p>In S/4HANA 1709 FPS02 the Position, Markt-to-Market and Profit-and-Loss Reporting for Financial Transactions does not support option delta factors.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>For the Simplification in Markt-to-Market and Profit-and-Loss Reporting for Financial Transactions a couple of steps need to be considered:</p>\n<ul>\n<li>historical position data can not be migrated to the new S/4HANA analytical data model. The data should be exported BEFORE the S/4HANA upgrade to keep it accessible from an XLS or BW InfoCube</li>\n<li>Financial Transaction data can be loaded initially into the database tables which is used in S/4HANA 1709 FPS2 onwards for Markt-to-Market and Profit-and-Loss reporting</li>\n<li>if BEX Queries on top of Operational Data Providers are used in the Business Suite, these BEX Queries need to be converted into CDS Queries in S/4HANA on top of the new CDS InterfaceViews</li>\n<li>if CDS Queries have been used for Markt-to-Market and Profit-and-Loss Reporting in the Suite, these need to be converted into CDS Queries in S/4HANA on top of the new CDS InterfaceViews</li>\n</ul>", "noteVersion": 3}, {"note": "2556134", "noteTitle": "2556134 - S4TWL - CM: Agri-Specific Commodity Position Queries", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA transition worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ACM, agricultural industry, commodity position reporting, CDS views</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709, the database table and available infosources for risk data required for position reporting got unified and revised.</p>\n<p>New Core Data Services (CDS) views are provided to determine keyfigures for position reporting.</p>\n<p>For keyfigures, which are specific for Futures/Basis pricing - as common in the Agricultural industry - CDS (interface) views are available.</p>\n<p>However, queries on top of these are not provided with the software layer of Commodity Management.</p>\n<p>Instead, these are provided in the Agicultural Contract Managament (ACM) software layer.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Agri-specific Queries include</p>\n<p>- Futures Risk / Slate report with a keyfigure layout tailored to Future / Basis pricing</p>\n<p>- Basis / Premium report</p>\n<p>- Price Type report</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>If you require Agri-specific queries, check the availability of Agicultural Contract Managament (ACM) in your system.</p>\n<p>If ACM is not available you may need to create queries on top of the available (Commodity Management) interface views during system implementation.</p>", "noteVersion": 1}, {"note": "2551943", "noteTitle": "2551943 - S/4HANA: Commodity Management - Simplified Commodity Curves", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using an SAP ERP system and intend to perform the upgrade-like conversion to S/4HANA.</p>\n<p>Commodity Curves have been simplified in S/4HANA, which requires adjustments in your configuration of the Commodity Curves.</p>\n<p>It may also be required, that customer specific coding regarding the Commodity Curves has to be adjusted.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The attached note 2553281 includes a detailed description about the necessary conversions. Please see chapter 'Conversion of Commodity Curves'.</p>\n<p> </p>", "noteVersion": 1}, {"note": "2556220", "noteTitle": "2556220 - S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Commodity Risk Management, Derivative Contract Specification/DCS, Market Identifier Code/MIC, commodity</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Check in your business suite (ERP) implementation, whether and which market data are based on DCS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"longtext\">\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709, the commodity market data is based only on definitions of derivative constract specifications (DCS). The following derivative categories are supported for using the DCS:</p>\n<ul>\n<li>Commodity Future</li>\n<li>Listed Option</li>\n<li>Commodity Forward Index</li>\n</ul>\n<p>Financial transaction pricing data uses DCS-based market data only.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>To support the DCS-based market data including DCS-based commodity curves, the Financial Transaction Management and its commodity-specific processes like price adjustments for unpriced commodity derivatives were adjusted accordingly.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Check your Business Suite (SAP ERP) implementation, whether you used market data based on quotation source, quotation type, quotation name.</p>\n<p>The market data must be converted to support DCS-based market data.</p>\n<p>The financial transactions pricing data based on the previous market data concept can be converted according to the cookbook (see SAP note 2553281 - Cookbook Deprecation of Commodity ID in Commodity Risk Management).</p>\n</div>", "noteVersion": 1}, {"note": "2556089", "noteTitle": "2556089 - S4TWL - Simplification in Position Reporting for Financial Transactions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Commodity Risk Management, Commodity Exposure Positions</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>With SAP S/4HANA the Commodity Risk Management provides Position Reporting for the Financial Transaction Management. All analytical data is persisted based on versioned data records and exposed through Core Data Servcies (DCS) to the analytical tools like SAP Analysis for Microsoft Excel .</p>\n<p><strong>Business Process related information</strong></p>\n<p>In the Business Suite the Financial Transaction data that is relevant for the analytical source data for position reporting was stored in the versioned data base table for commodity exposures (TEXT_CTY_EXPOS) . This data base table was also used for logistical data via the Logistics to Treasury Management interface (LOG2TRM). The update happened via a background process.</p>\n<p>In S4 1709 FPS01 there is a new data flow to support analytical reporting. The Financial Transaction data is updated automatically from all relevant Financial Transaction business processes into a new data base table (CMM_VFIND)  in real time. In case of an error it is possible to monitor messages in the application log and to re-process.</p>\n<p>S4 1709 FPS01 the analytical reporting keyfigures are provided in one single technology that provides a reporting CDS stack in order to allow commodity position snapshot reporting across commodities.</p>\n<p><strong>Functional Restrictions</strong></p>\n<p>In S4 1709 FPS01 the Position Reporting for Financial Transactions does not support option delta factors.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As described in the attached 'Function in Details - Simplification in Postion Reporting for Financial Transactions'  a couple of steps need to be considered:</p>\n<ul>\n<li>historical position data can not be migrated to the new S/4HANA analytical data model. The data should be exported BEFORE the S4 upgrade to keep it accessible from an XLS or BW InfoCube</li>\n<li>Financial Transaction data can be loaded initially into the database table which is used in S4 1709 FPS1 onwards for position reporting</li>\n<li>if BEX Queries on top of Operational Data Providers are used in the Business Suite, these BEX Queries need to be converted into CDS Queries in S4 on top of the new CDS InterfaceViews</li>\n<li>if CDS Queries have been used for Position Reporting in the Suite, these need to be converted into CDS Queries in S4 on top of the new CDS InterfaceViews</li>\n</ul>\n<p> </p>", "noteVersion": 1}, {"note": "2556063", "noteTitle": "2556063 - S4TWL - CM: Commodity Price Risk Determination from Schedule Lines", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Schedule Line Item, Purchase Order, Sales Order, Delivery Schedule</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709, the commodity risk determination for commodity price risks got simplified.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In the Business Suite it was possible to activate the commodity price risk determination based on schedule lines of a purchase order or sales order.</p>\n<p>For this purpose a price simulation was called in the background for each schedule line with the schedule line date as reference date and pertaining quantity.</p>\n<p>This is not supported in S4HANA.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>If you require to split and distribute commodity price risk information, you need to split it on the item level accordingly.</p>", "noteVersion": 1}, {"note": "2556106", "noteTitle": "2556106 - S4TWL - CM: Hedge Management for Logistics Transactions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Hedge Accounting for Exposures, HACC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709, the raw exposure interface for logistics transactions has been deprecated.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In the Business Suite, raw exposures were created from logistics documents based on the commodity pricing information. <br/>For these raw exposures, risk hedges could be managed.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Check, whether you used raw exposure information for <em>Hedge Accounting for Exposures</em>.</p>\n<p>In S4H 1709 FPS01, <em>Commodity Position Reporting</em> is provided without raw exposures.</p>\n<p>Based on reported positions of logistics transactions, the corresponding risk hedges must be triggered manually.</p>", "noteVersion": 1}, {"note": "2591598", "noteTitle": "2591598 - S4TWL - CM: Mark-to-Market & Profit-and-Loss for logistics transactions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CMM_VLOGP CMM_MTM_PO CMM_MTM_SO WB2B_CMM_MTM_TC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709, the database table used for Mark-to-Market Reporting got unified.</p>\n<p>Instead of three database tables storing valuation results for Purchasing, Sales and Trading Contracts, only one table is supported storing versioned pricing source data.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The Business Suite's <em>Mark-to-Market Reporting</em> stored valuation results in the tables CMM_MTM_PO, CMM_MTM_SO and WB2B_CMM_MTM_TC.</p>\n<p>However, this did not facilitate snapshot-based reporting (such as the End-of-Day Snapshot Mark-To-Market reporting) since the market data may not be present in the system at the time when these snapshots need to be valued.</p>\n<p>For this purpose, to store versioned source data, table CMM_VLOGP had been introduced with SAP ERP 6.0 Enhancement Package 8.</p>\n<p>Based on table CMM_VLOGP, the valuation with market data is performed at reporting time.</p>\n<p>In S4H only, table CMM_VLOGP is supported as basis for the Mark-to-Market reporting on logistics transactions.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>As described in the attached document '<em>Function in Details - Mark-to-Market and Profit-and-Loss for Logistics Transactions</em>', the following steps need to be performed:</p>\n<ul>\n<li>\n<p>If using discounting, you need to make use of forward FX rates instead to convert between currencies towards a statistics currency.</p>\n</li>\n<li>\n<p>If using aggregation, you need to define reporting groups in the Customizing for Commodity Risk Management and aggregate at these groups at reporting time.</p>\n</li>\n</ul>\n<ul>\n<li>If BEx queries on top of Operational Data Providers (ODP) are used in SAP ERP, these BEx queries must be converted to CDS queries in S4H on top of the provided CDS interface views.</li>\n</ul>", "noteVersion": 1}, {"note": "2555990", "noteTitle": "2555990 - S4TWL - CM: Unification of Technologies for Analytical Data Provision", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA transition worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ODP, BEx Query, CDS view, Commodity Position Reporting, Commodity Position Overview, MtM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709 the technologies by which analytical data is provided got unified:</p>\n<p>- Operational Data Providers (ODP), BW Extractors and BEX Queries on top of these providing Position Reporting and Mark-to-Market are deprecated</p>\n<p>- HANA Live calculation views are not supported for Commodity Position Reporting and Mark-to-Market valuation/reporting.</p>\n<p>Instead, all analytical data is exposed through Core Data Services (CDS) views.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In the Business Suite's Commodity Position Reporting, different sources of analytical information were available in different technologies:</p>\n<p>Some data were reported based on Operational Data Providers (ODPs), some data based on CDS views, some information was exposed through HANA Live calculation views.</p>\n<p>In S4HANA, all analytical data for commodities is exposed through CDS views reducing the TCO by using a unified technology.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Check, whether the required analytical data is provided by CDS views with S4H 1709 FPS01.</p>\n<p>Note, that some analytical information for commodities formerly provided in the Business Suite may not be supported yet. These restrictions are documented in other simplification items.</p>\n<p>You may need to convert your queries to enable the consumption by the new CDS views available in S4H.</p>", "noteVersion": 2}, {"note": "2560298", "noteTitle": "2560298 - S4TWL - Commodity Pricing in Purchasing Contracts and Scheduling Agreements", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Within S/4HANA 1709 - from FPS00 until FPS02, MM Contracts and Scheduling Agreements do not support the following functionality:</p>\n<ul>\n<li>Risk Distribution Plan (RDP)</li>\n<li>Usage of Commodity Pricing Engine (CPE) and Configurable Parameters and Formulas (CPF)</li>\n</ul>\n<p>Starting with S/4HANA 1709 FPS03, MM Contracts are supported with the following functionality:</p>\n<ul>\n<li>Usage of Commodity Pricing Engine (CPE) and Configuable Parameters and Formulas (CPF) using time independent conditions (document conditions).<br/>Existing MM Contracts (with time-dependent conditions) have to be closed and new MM contracts with time independent conditions have to be created for the open quantity and validity period.</li>\n<li>Risk Distribution Plan (RDP) for time independent conditions.</li>\n</ul>\n<p> Starting with S/4HANA 2020 FPS00 Scheduling Agreements are supported with commodity pricing (CPE, CPF) using time-dependent conditions.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RDP, CPE, CPF, Purchasing Contract, Scheduling Agreements</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before S/4HANA 1709 FPS03, Customers converting to S/4HANA using RDPs or CPE/CPF within MM Contracts or Scheduling Agreements cannot convert.</p>", "noteVersion": 5}, {"note": "2556164", "noteTitle": "2556164 - S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:  DCS ID Extension", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Derivative Contract Specification (DCS), DCS ID, market data feed</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The DCS ID with data type 6 chars used to be a restriction for customers and did not allow to enter a meaningful description, and to support longer market data codes from market data providers for commodity prices based on commodity forward indexes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With SAP S/4HANA, on-premise edition 1709 FPS1, the Derivative Contract Specification Identifier (DCSID) as defined in the system configuration has been extended by using a DCSID data type with 20 CHAR. With this data type you can describe the entity in more detail such as, for example, with reference to the material, the exchange and the location.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>The market data interface is adjusted to support the new DCSID data type.</p>\n<p>The access to market data and commodity curves is now based on the longer DCSID. The Financial Transaction Management and the related processes are adjusted accordingly.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Actions are only nessecary for customer-specific coding. Manual conversion steps are need for the conversion of datafeed functionality. For more information, see SAP Note 2553281 - Cookbook Deprecation of Commodity ID in Commodity Management.</p>\n<p> </p>\n<p> </p>\n<p> </p>", "noteVersion": 1}, {"note": "2551942", "noteTitle": "2551942 - S4TWL - Integration to GTM not supported", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1709 FPS1, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With SAP S/4HANA, on-premise edition 1709, the integration of Global Trade Management (GTM) with Commodity Management processes (not ACM!) is not supported. Customers, who activated business function <span class=\"sapMText sapMTextMaxWidth sapUiSelectable sapUiTinyMarginTopBottom tdbCheckListFont\" id=\"__text81-__xmlview7--checkList-0\">ISR_GLT_CMMINTEG, cannot perfom a system conversion to SAP S/4 HANA 1709.</span></p>", "noteVersion": 2}, {"note": "2551960", "noteTitle": "2551960 - S4TWL - CM: Simplified DCS Access", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With SAP S/4HANA, on-premise edition 1709 FPS1, the access to DCS based Market Data, Commodity Curves and Financial Transactions has been simplified. Details about the simplification can be found in the attached document 'Function in Details - Simplified DCS Access'.</p>", "noteVersion": 1}]}], "activities": [{"Activity": "Miscellaneous", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Export still required historical position data to keep it accessible from an Microsoft Excel or SAP BW InfoCube"}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "Perform initial load of financial transaction into new database tables"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "If you used own BEX queries or CDS queries for your Markt-to-Market and Profit-and-Loss Reporting reporting in SAP ERP, convert them into CDS Queries in SAP S/4HANA on top of the new CDS InterfaceViews"}]}