{"guid": "00109B131AF41EDB8293A6F4A65BC0EE", "sitemId": "SI20: AS_ABAP_WORKFLOW_USER_JOBS", "sitemTitle": "ABAPTWL - Change of workflow system user and workflow system jobs", "note": 2568271, "noteTitle": "2568271 - Change of workflow system user and workflow system jobs with S/4HANA On-Premise 1709", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You upgraded your system to S/4HANA On-Premise 1709, 1809, 1909, 2020, 2021, 2022, 2023 (or following releases) from a release prior S/4HANA On-Premise 1709. In \"Automatic Workflow Customizing\", transaction SWU3 you notice the status is not green for \"Edit Runtime Environment\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SJOBREPO, SAP_WFRT, SWU3, 1709, 1809, 1909, 2020, 2021, 2022, 2023</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Change of workflow system user and workflow system jobs with S/4HANA On-Premise 1709.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Starting with S/4 Hana OnPremise 1709 the workflow system user and workflow system jobs changed. The workflow system user is called SAP_WFRT now instead of WF-BATCH. The workflow system jobs start with SAP_WORKFLOW now and are scheduled automatically by \"Technical Job Repository\", transaction SJOBREPO.</p>\n<p>Starting with S/4 Hana OnPremise 1809 the workflow system jobs are scheduled under the user, under which the system jobs of Technical Job Repository run. This might be a different user than SAP_WFRT.</p>\n<p><strong><strong>Business-Process-Related Information</strong></strong></p>\n<p>SAP Business Workflow does not continue after upgrade.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ol>\n<li>Deschedule the \"old\" workflow jobs in <strong>all system clients</strong>, in case these are still scheduled from the time before the upgrade. These are the following jobs:<br/>SWWDHEX<br/>SWWERRE<br/>SWWCOND<br/>SWWWIM<br/>SWEQSRV<br/>SWWCLEAR</li>\n<li>Make sure that user SAP_WFRT exists in the system and has role SAP_BC_BMT_WFM_SERV_USER_PLV01 assigned.</li>\n<li>Check role SAP_BC_BMT_WFM_SERV_USER_PLV01 e.g. in transaction PFCG:<br/>Ensure that there is a green traffic light on tab \"Authorizations\". Regenerate the profile if necessary.<br/>Afterwards check the traffic light on the tab \"User\". Run the \"User Comparison\" if the traffic light is not green.</li>\n<li>In \"Technical Job Repository\" (transaction SJOBREPO) the automatic scheduling of jobs must be switched on. Note 2190119 - Background information about S/4HANA technical job repository - describes the prerequisites needed.</li>\n<li>Wait for the next run of system job  \"R_JR_BTCJOBS_GENERATOR\" for the Technical Job Repository. It runs by default every hour and schedules all new workflow system jobs starting with SAP_WORKFLOW.<br/>After the workflow jobs are scheduled, you will see green lights in transaction SWU3.</li>\n<li>Job SAP_WORKFLOW_RESTART is automatically scheduled in the system. It runs once a day and restarts all workflows in error status automatically. These are workflows, you can find in transaction SWPR. If you do not want these workflows to be restarted, please deactivate the job in transaction SJOBREPO.</li>\n</ol>", "noteVersion": 10, "refer_note": [{"note": "2190119", "noteTitle": "2190119 - Background information about SAP S/4HANA technical job repository", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>In SAP S/4HANA (cloud edition and on-premise edition), there is a technical job repository that automatically schedules certain (periodic) background jobs. This SAP Note contains background information about the job repository.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SJOBREPO, technical job repository, REORGJOBS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP S/4HANA, the technical job repository carries out the scheduling of periodic technical jobs in the clients ADMIN and BUSINESS. This mechanism is carried out automatically by the system and does not require any user interaction, unlike the earlier \"Standard Jobs\" function in SM36.</p>\n<p>Among other things, the technical job repository contains job definitions for the standard jobs mentioned in SAP Note 16083.</p>\n<p>Transaction SM36 in SAP S/4HANA systems has the \"Job Repository\" function, which branches to transaction SJOBREPO, instead of the \"Standard Jobs\" function.</p>\n<p>Transaction SJOBREPO displays an overview of all job definitions delivered by SAP in the technical job repository.</p>\n<p>You can use SJOBREPO to change a job definition (and the background job generated from it) within certain limits or to completely deactivate it.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Caution:</strong></p>\n<p>To execute the technical jobs scheduled by the job repository, a technical user is required in each client that has authorizations to <strong>execute all technical jobs</strong> in addition to the authorizations for processing background jobs.</p>\n<p><strong>For more information about the user for the technical job repository, see SAP Note 2731999.</strong></p>\n<p>By default, the SAP S/4HANA technical job repository expects the user <strong>SAP_SYSTEM</strong> in the clients ADMIN (=000) and BUSINESS or, if this is not available, the user <strong>DDIC</strong>. If neither SAP_SYSTEM nor DDIC exists in a client (or if these users do not have the required authorizations), the technical job repository cannot schedule any jobs in this client.</p>\n<p>In this case, enter a user that should be used to execute the technical jobs in the client for each individual client.</p>\n<p>As of SAP S/4HANA 1610, transaction SJOBREPO_STEPUSER exists for this purpose. It is used to create a suitable user and assign this user to the job repository in the current client. As of SAP S/4HANA 1709, this transaction can be called using a button in the menu bar of transaction SJOBREPO.</p>\n<p>Before SAP S/4HANA 1610, the user must be created manually using SU01. There, only the report R_JR_UTIL_1 exists in order to make the desired user name known to the job repository (\"Set Default Step User\" function).</p>\n<p>The technical user for the job repository must have the following properties:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Alias name assigned?</td>\n<td>NO</td>\n</tr>\n<tr>\n<td>User type</td>\n<td>System</td>\n</tr>\n<tr>\n<td>Password deactivated?</td>\n<td>YES</td>\n</tr>\n<tr>\n<td>SNC name assigned?</td>\n<td>NO</td>\n</tr>\n<tr>\n<td>Validity period</td>\n<td>JAN/01/1900 - DEC/31/9999</td>\n</tr>\n<tr>\n<td>Roles assigned?</td>\n<td>NO</td>\n</tr>\n<tr>\n<td>Assigned profile</td>\n<td>\n<p>SAP S/4HANA 1610: SAP_ALL mandatory</p>\n<p>As of SAP S/4HANA 1709: See SAP Note 2731999</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>The attached PDF document explains the functions and customizing possibilities of the technical job repository in SAP S/4HANA.</p>\n<p>(SJOBREPO_STEPUSER and R_JR_UTIL_1 are relevant only for SAP S/4HANA, on-premise edition. In SAP S/4HANA cloud edition, the system administration creates the SAP_SYSTEM user required for the job repository automatically in each client.)</p>", "noteVersion": 14, "refer_note": [{"note": "2731999", "noteTitle": "2731999 - Assign custom step user for Technical Job Repository (SJOBREPO)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use the Technical Job Repository for scheduling technical jobs. For this, it is obligatory to create and assign a default stepuser in all clients. This user will be used for scheduling ALL jobs in the Technical Job Repository.</p>\n<p><span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-gb;=\"\" en-us;=\"\" lang=\"EN-GB\" minor-bidi;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-ascii-theme-font:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" mso-hansi-theme-font:=\"\" new=\"\" roman';=\"\">You need to use transaction SJOBREPO_STEPUSER, but this transaction only allows to create new or assign existing users with SAP_ALL profile in your current release/support package. </span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SJOBREPO, SJOBREPO_STEPUSER</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Your security policies do not allow users with SAP_ALL profile. As a result, no stepuser can be created and assigned and therefore no job from the Technical Job Repository can be scheduled in this system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>I.</strong> Implement the correction instructions if necessary (see section validity).</span></p>\n<p><span><strong>II.</strong> Then create a new user in SU01. This user must be of type SYSTEM (other user types are not allowed) and must have the authorizations which are required to execute the reports of ALL job definitions in the Technical Job Repository. </span></p>\n<p><span>There are several options to collect the required authorizations:</span></p>\n<p><span>1. You can use profile SAP_APP to cover the application authorizations. Note that this profile needs to be regenerated using program REGENERATE_SAP_APP according to SAP Notes </span><a href=\"https://me.sap.com/notes/1703299\" target=\"_blank\">1703299</a><span>, </span><a href=\"https://me.sap.com/notes/2437635\" target=\"_blank\">2437635 </a><span>and </span><a href=\"https://me.sap.com/notes/2421103\" target=\"_blank\">2421103</a><span>. Make sure that Basis and HR objects </span><strong>are included</strong><span>, otherwise no job can be executed and HR related job definitions might fail. It is essential to follow the documentation available in program REGENERATE_SAP_APP.</span></p>\n<p><span>2. You can use the long-term user trace (transaction STUSERTRACE) to gather the authorizations for your specific user. See SAP Note </span><a href=\"https://me.sap.com/notes/2220030\" target=\"_blank\">2220030</a><span> or SAP Note </span><a href=\"https://me.sap.com/notes/2562374\" target=\"_blank\">2562374</a><span> (section 6. Authorization Trace) and the help.sap.com documentation for more information. SAP Note </span><a href=\"https://me.sap.com/notes/2353127\" target=\"_blank\">2353127</a><span> explains how to import the trace evaluations in role maintenance in transaction PFCG. Note: The majority of job definitions are time-based with a period &lt;= once per month. So, if you activate STUSERTRACE for a month, most of the jobs will be executed within this period. But: There are a couple of event-based job definitions as well. Make sure to keep the trace activated until all jobs have been executed once. </span></p>\n<p><span>The execution terms of the job definitions are listed in the following SAP Notes:</span></p>\n<p><a href=\" https://me.sap.com/notes/3389524\" target=\"_blank\">3389524</a><span> - Jobs in the Technical Job Repository (SJOBREPO) in SAP S/4HANA 2023</span></p>\n<p><a href=\" https://me.sap.com/notes/3195909\" target=\"_blank\">3195909</a><span> - Jobs in the Technical Job Repository (SJOBREPO) in SAP S/4HANA 2022</span></p>\n<p><a href=\" https://me.sap.com/notes/2849364\" target=\"_blank\">2849364</a><span> - Jobs in the Technical Job Repository (SJOBREPO) in SAP S/4HANA 2021</span></p>\n<p><a href=\" https://me.sap.com/notes/2992214\" target=\"_blank\">2992214</a><span> - Jobs in the Technical Job Repository (SJOBREPO) in SAP S/4HANA 2020</span></p>\n<p><a href=\" https://me.sap.com/notes/2849402\" target=\"_blank\">2849402</a><span> - Jobs in the Technical Job Repository (SJOBREPO) in SAP S/4HANA 1909</span></p>\n<p>If you consider using the reference user concept, please read the following blog:</p>\n<p><a href=\"https://community.sap.com/t5/financial-management-blogs-by-members/sap-authorization-testing-with-reference-users/ba-p/13562072\" target=\"_blank\">SAP authorization testing with reference users - SAP Community</a></p>\n<p>Note: If not all authorizations required for the execution of a report are assigned to the user, the corresponding job will still be scheduled but every execution will be cancelled in SM37.</p>\n<p><span><strong>III.</strong> When you are finished with the user creation, open transaction SJOBREPO_STEPUSER. Select </span><em>Create and assign step user</em><span> button and choose your user from the F4 help. You will receive a warning that this user does not have SAP_ALL profile assigned. Click okay, and the user will be assigned to the Technical Job Repository framework. <strong>Do not create the user directly in SJOBREPO_STEPUSER, because this user will be automatically assigned SAP_ALL profile.</strong></span></p>\n<p>Note: It is technically possible but not recommended to assign DDIC as a default stepuser in transaction SJOBREPO_STEPUSER because the Technical Job Repository uses it as a fallback user when no default stepuser is assigned. Users assigned to specific job definitions in SJOBREPO_STEPUSER (also called local users) are ignored when DDIC is the default stepuser.</p>", "noteVersion": 5}, {"note": "16083", "noteTitle": "16083 - Standard jobs, reorganization jobs", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>There are a number of jobs that must periodically run in a live SAP installation, for example, to delete outdated jobs or spool objects.<br/>As of Release 4.6C, you can easily schedule these jobs as follows:<br/>Transaction SM36 -&gt; 'Standard jobs' button. We recommend scheduling the jobs in client 000. <br/><br/>Unfortunately, there is no easy-to-use support for such jobs within Basis customizing before Release 4.6C. Therefore, you must schedule the jobs explicitly for these release levels.<br/>This note contains a list of the required programs, their parameters, and the recommended repeat interval. In addition, names are suggested for the required jobs. Adhere to the recommendations, as the naming conventions enable us to check quickly and easily whether these jobs have been activated in your system.<br/>This note applies as of Releases 2.1G and 2.2A.</p>\n<p>For S/4HANA, this SAP Note is not valid. Please see SAP Note <a href=\"/notes/2190119\" target=\"_blank\">2190119</a>, instead.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>REORGJOBS.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Note: Application-specific reorganization programs are not included in this list.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Job name:</td>\n<td> </td>\n<td>Program</td>\n<td> </td>\n<td>Variant</td>\n<td> </td>\n<td>Repeat interval</td>\n</tr>\n<tr>\n<td> </td>\n</tr>\n<tr>\n<td>SAP_REORG_JOBS</td>\n<td> </td>\n<td>RSBTCDEL2</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Daily</td>\n</tr>\n<tr>\n<td>SAP_REORG_SPOOL</td>\n<td> </td>\n<td>RSPO0041/1041</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Daily</td>\n</tr>\n<tr>\n<td>SAP_REORG_BATCHINPUT</td>\n<td> </td>\n<td>RSBDCREO</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Daily</td>\n</tr>\n<tr>\n<td>SAP_REORG_ABAPDUMPS</td>\n<td> </td>\n<td>RSSNAPDL</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Daily</td>\n</tr>\n<tr>\n<td>SAP_REORG_JOBSTATISTIC</td>\n<td> </td>\n<td>RSBPSTDE</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Monthly</td>\n</tr>\n<tr>\n<td>SAP_COLLECTOR_FOR_JOBSTATISTIC</td>\n<td> </td>\n<td>RSBPCOLL</td>\n<td> </td>\n<td>No</td>\n<td> </td>\n<td>Daily</td>\n</tr>\n<tr>\n<td>SAP_COLLECTOR_FOR_PERFMONITOR</td>\n<td> </td>\n<td>RSCOLL00</td>\n<td> </td>\n<td>No</td>\n<td> </td>\n<td>Hourly</td>\n</tr>\n<tr>\n<td>SAP_COLLECTOR_FOR_NONE_R3_STAT</td>\n<td> </td>\n<td>RSN3_STAT_</td>\n<td> </td>\n<td>No</td>\n<td> </td>\n<td>Hourly</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>COLLECTOR</td>\n</tr>\n<tr>\n<td>SAP_REORG_PRIPARAMS</td>\n<td> </td>\n<td>RSBTCPRIDEL,</td>\n<td> </td>\n<td>Yes*</td>\n<td> </td>\n<td>Monthly</td>\n</tr>\n<tr>\n<td>SAP_REORG_XMILOG</td>\n<td> </td>\n<td>RSXMILOGREORG</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Weekly</td>\n</tr>\n<tr>\n<td>SAP_CCMS_MONI_BATCH_DP</td>\n<td> </td>\n<td>RSAL_BATCH_</td>\n<td> </td>\n<td>No</td>\n<td> </td>\n<td>Hourly</td>\n</tr>\n<tr>\n<td> </td>\n<td> </td>\n<td>TOOL_DISPATCHING</td>\n</tr>\n<tr>\n<td>SAP_SPOOL_CONSISTENCY_CHECK</td>\n<td> </td>\n<td>RSPO1043</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Daily</td>\n</tr>\n<tr>\n<td>SAP_REORG_ORPHANED_JOBLOGS</td>\n<td> </td>\n<td>RSTS0024</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Weekly</td>\n</tr>\n<tr>\n<td>SAP_CHECK_ACTIVE_JOBS</td>\n<td> </td>\n<td>BTCAUX07</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Hourly</td>\n</tr>\n<tr>\n<td>SAP_DELETE_ORPHANED_IVARIS</td>\n<td> </td>\n<td>BTC_DELETE_ORPHANED_IVARIS</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Weekly</td>\n</tr>\n<tr>\n<td>SAP_REORG_ORPHANED_TEMSE_FILES</td>\n<td> </td>\n<td>RSTS0043</td>\n<td> </td>\n<td>Yes</td>\n<td> </td>\n<td>Weekly</td>\n</tr>\n</tbody>\n</table></div>\n<p>* <em>As of Release 6.20</em></p>\n<p><br/>In any case, refer to Note 48400 regarding the spool and TemSe<br/>consistency check.<br/><br/>Note:</p>\n<ul>\n<li>SAP_CCMS_MONI_BATCH_DP is not a reorganization job but is needed to start tools/methods in the system monitoring area in the background -&gt; transaction RZ20/RZ21.</li>\n</ul>\n<ul>\n<li>SAP_REORG_XMILOG reorganizes the table TXMILOGRAW. This table contains log information on the XMI interface (-&gt; SAP Note 182963).</li>\n</ul>\n<ul>\n<li>As of Release 4.6A, the job steps are managed separately from the print parameters. This means that report RSBTCDEL no longer deletes any print parameters. You must therefore schedule the following report, too:<br/><br/> RSBTCPRIDEL<br/><br/>It reorganizes print parameters in a cross-client manner. Since the number of print parameters increases more slowly than the number of background processing steps, you can execute this report after longer periods of time ( longer than one month).<br/>You must refer to Note 307970 in connection with RSBTCPRIDEL.</li>\n</ul>\n<ul>\n<li>The job SAP_COLLECTOR_FOR_PERFMONITOR was previously also called COLLECTOR_FOR_PERFORMANCEMONITOR.</li>\n</ul>\n<ul>\n<li>The job SAP_COLLECTOR_FOR_NONE_R3_STAT is available as of SAP Web Application Server 6.20 only.</li>\n</ul>\n<ul>\n<li>In addition, you should regularly run a consistency check of the spooler and the TemSe (Note 48400).</li>\n</ul>\n<ul>\n<li></li>\n</ul>\n<ul>\n<li>***** Caution: The standard job SAP_REORG_UPDATERECORDS that was contained in some delivered versions must no longer be executed (see Note 67014). In its new version, the underlying ABAP Program RSM13002 is programmed so that it terminates when running in a job (that is, in the background). Therefore, the job SAP_REORG_UPDATERECORDS always terminates in this case. In any case, the job SAP_REORG_UPDATERECORDS should be deleted from the pool of standard jobs, if it is still there: SM36 -&gt; 'Standard jobs' button -&gt; 'Delete standard jobs' button. In addition, released jobs that may exist and contain report RSM13002 should be deleted. You can find and delete these jobs using sm37.</li>\n</ul>\n<ul>\n<li>***** Caution: The job SAP_WP_CACHE_RELOAD_FULL is normally used to update data from the workplace component system to the workplace server.<br/>This job is allowed to be run on a workplace server only; otherwise it terminates. In the future, this job will no longer be delivered as a standard job.<br/>If this job has been scheduled for your system, but you do not need it, delete the scheduled job in transaction SM37.</li>\n</ul>\n<ul>\n<li>To eliminate ABAP dumps that are created due to runtime errors within an ABAP program, use the program RSSNAPDL. To simplify the associated job scheduling, you also have the program RSNAPJOB. This program schedules RSSNAPDL within a job. Implicit assumptions:</li>\n</ul>\n<ul>\n<ul>\n<li>Job name: RSSNAPDL</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Variant: DEFAULT (therefore it must exist)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Start date: From the following day, 1:00 am</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Repeat interval: Daily</li>\n</ul>\n</ul>\n<ul>\n<li>Using the Support Packages specified in Note 666290, report RSTS0024 deletes job logs that no longer belong to any job. Contact SAP if you need the report in a release that is not specified in SAP Note 666290.</li>\n</ul>\n<p><strong>Clients and authorizations</strong></p>\n<p>Some of the jobs specified work with client-specific objects (for example, jobs). Whether a reorganization has any cross-client influence then normally depends on particular authorizations. All client-specific jobs are listed below. None of the other jobs are client-specific.<br/><br/>Jobs that are not client-dependent perform a reorganization in all affected clients. They require neither special authorizations nor a special user name.</p>\n<p>The job SAP_COLLECTOR_FOR_PERFMONITOR must always be scheduled in client 000 with user DDIC or with a user with the same authorization.<br/><br/>For some jobs, note the following correlations:</p>\n<ul>\n<li>Job SAP_REORG_JOBS:</li>\n</ul>\n<ul>\n<ul>\n<li>An authorization for the object S_BTCH_ADM = 'Y' is required in order to be able to delete jobs of other users and in other clients.</li>\n</ul>\n</ul>\n<ul>\n<li>Job SAP_REORG_SPOOL:</li>\n</ul>\n<ul>\n<ul>\n<li>Authorization S_ADMI_FCD-S_ADMI_FCD = 'SPAD'<br/>Reorganization runs in chosen client (Client = '*', then runs in all clients)</li>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>Authorization S_ADMI_FCD-S_ADMI_FCD = 'SPAR'<br/>Reorganization only in the client in which the actual job is running</li>\n</ul>\n</ul>\n<ul>\n<li>Job SAP_REORG_BATCHINPUT:</li>\n</ul>\n<ul>\n<ul>\n<li>Authorization profile S_BDC_MONI - BDCAKTI = 'REOG'<br/> S_BDC_MONI - BDCGROUPID = '*'<br/>Reorganization only in the client in which the job is running (as of SAP Note <a href=\"/notes/2177694\" target=\"_blank\">2177694</a> and Release 7.00, cross-client).</li>\n</ul>\n</ul>\n<p><br/><br/>Comments:</p>\n<ul>\n<li>The authorizations always relate to the user under whose ID the job is being processed.</li>\n</ul>\n<ul>\n<li>If this user has the authorizations required to work in a cross-client manner, the client in which the job actually runs is irrelevant.</li>\n</ul>\n<p><strong>Examples</strong></p>\n<ul>\n<li>User ADMIN has the Authorization S_BTCH_ADM = 'Y'. If Job SAP_REORG_JOBS is now scheduled with User ADMIN, the jobs are reorganized in all clients.</li>\n</ul>\n<ul>\n<li>User REORG has the authorization profile S_BDC_ALL. among others. If the job SAP_REORG_BATCHINPUT is now scheduled with the user REORG in client 002, the batch input objects are reorganized in all clients as of Release 7.00. In previous releases, they are reorganized only in client 002.</li>\n</ul>\n<ul>\n<li>If the job SAP_REORG_ABAPDUMPS is scheduled in any client, all ABAP short dumps in all clients are reorganized.</li>\n</ul>\n<ul>\n<li>User SPOOLADM has the authorization S_ADMI_FCD-S_ADMI_FCD = 'SPAD'. If the job SAP_REORG_SPOOL is now scheduled with user SPOOLADM and client 123 is specified for the program parameters, then the spool objects in client 123 are reorganized irrespective of the client in which the actual job is running. If you enter '*' as the client, all clients are reorganized.</li>\n</ul></div>", "noteVersion": 83}]}], "activities": [{"Activity": "Technical System Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adapt your workflow jobs as described in SAP note 2568271."}]}