{"guid": "00109B1318B61EDAB2CEBF1834E90108", "sitemId": "SI32: Logistics_PLM", "sitemTitle": "S4TWL - Classification - Data Cleanup before Migration", "note": 2949845, "noteTitle": "2949845 - S4TWL - Classification - Data Cleanup Before Migration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1909 or higher on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Transition Worklist, SAP S/4HANA, on-premise, CLF_HDR</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>None</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>During an upgrade to S/4HANA 1909OP or higher version there is a conversion logic to fill the CLF_HDR database table.</p>\n<p>This logic requires that the INOB table does not contain any duplicates. If the results of the pre-check contains any inconsistencies, please follow the steps in SAP Note 2948953.</p>", "noteVersion": 1, "refer_note": [{"note": "2948953", "noteTitle": "2948953 - Duplicate entries in INOB table", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>There are duplicate entries in table INOB. To find these inconsistencies and fix them, you should run the report RCL_CHECK_DUPL_CLF.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Classification, INOB, duplicates, inconsistency</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Programming error</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions, and run the report: RCL_CHECK_DUPL_CLF.<br/>When there are several CUOBJs for a Business Object, only one should be left, all others should be deleted by selecting them and using the delete icon in the program. Keep only the one which suits best for later use. Decide which one to keep by comparing the Classification valuation data listed by the program.<br/><br/>Notice 1: This program runs only in the current client, where the user is logged in. Be sure you run the program in the right client.<br/>Notice 2: The message \"<em>Class type w/o \"multiple objects\"-category is not relevant</em>\" is just a small notice which indicates that some class types are not processed because they do not have INOB entries. This is normal.<br/>Notice 3: To actually commit to the deletion of INOB entries, <em>Update Mode</em> should be checked on the first screen of the program.</p>", "noteVersion": 7}], "activities": [{"Activity": "Data correction", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "If any inconsistencies are found in table INOB, fix them as described in SAP Note 2949845."}]}