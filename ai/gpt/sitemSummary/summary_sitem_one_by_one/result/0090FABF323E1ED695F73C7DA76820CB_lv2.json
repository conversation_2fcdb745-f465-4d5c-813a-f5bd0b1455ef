{"guid": "0090FABF323E1ED695F73C7DA76820CB", "sitemId": "SI10: FIN_CO", "sitemTitle": "S4TWL - Reference and Simulation Costing", "note": 2349294, "noteTitle": "2349294 - S4TWL - Reference and Simulation Costing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Base Planning Object</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p dir=\"ltr\"><strong>Description</strong></p>\n<p dir=\"ltr\">You are currently using base planning objects to store cost estimates that can be included in cost estimates for projects, sales orders and so on as items of type B (base planning object). The creation of cost estimates with reference to base planning objects is no longer supported in SAP S/4HANA. Instead you are recommended to use transaction CKECP to prepare ad-hoc cost estimates and CKUC to prepare multilevel material cost estimates where BOMs and routings are not available for the material in question.</p>\n<p dir=\"ltr\">Task: Check, if base planning objects are used in the customer system.</p>\n<p dir=\"ltr\">Procedure: Call transaction se16 and select CKHS. On the selection screen you have to enter BZOBJ ='1'  and use action 'Number of Entries' to determine the number of base planning objects.</p>\n<p dir=\"ltr\">Rating:<br/>Simplification item and note is not relevant for the customer, if no CKHS entries exists with BZOBJ = '1'.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p dir=\"ltr\"><strong>Business Process related information</strong></p>\n<p dir=\"ltr\">Check your existing processes for cost estimation to determine whether you use costing items of type B (base planning object) as references for other cost estimates.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td>Transaction Code</td>\n</tr>\n<tr>\n<td>\n<p>Create Base Planning Object</p>\n</td>\n<td>KKE1</td>\n</tr>\n<tr>\n<td>\n<p>Change Base Planning Object</p>\n</td>\n<td>KKE2</td>\n</tr>\n<tr>\n<td>\n<p>Display Base Planning Object</p>\n</td>\n<td>KKE3</td>\n</tr>\n<tr>\n<td>\n<p>Revaluate Base Planning Object</p>\n</td>\n<td>KKEB</td>\n</tr>\n<tr>\n<td>\n<p>Base Planning Object vs. Other Unit Cost Estimate</p>\n</td>\n<td>KKEC</td>\n</tr>\n<tr>\n<td>\n<p>Costed Multilevel BOM (only Base Planning Object Exploded)</p>\n</td>\n<td>KKED</td>\n</tr>\n<tr>\n<td>\n<p>Itemization</p>\n</td>\n<td>KKB4</td>\n</tr>\n<tr>\n<td>\n<p>Overview of Base Planning Objects</p>\n</td>\n<td>\n<p>S_ALR_87013028</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Where Used List of Base Planning Objects</p>\n</td>\n<td>\n<p>S_ALR_87013029</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Multilevel Explosion of Base Planning Object</p>\n</td>\n<td>\n<p>S_ALR_87013036</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p dir=\"ltr\"><strong>Required and Recommended Action(s)</strong></p>\n<p dir=\"ltr\">Remove above transactions from existing roles and train users to work with alternative transactions.</p>", "noteVersion": 2, "refer_note": [], "activities": [{"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Remove mentioned transactions from existing roles and train users to work with alternative transactions"}]}