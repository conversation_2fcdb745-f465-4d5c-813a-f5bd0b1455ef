{"guid": "6CAE8B3EA08B1ED6A5D643004359C0C1", "sitemId": "SI45: Logistics_General", "sitemTitle": "S4TWL - Retail Factsheets", "note": 2377780, "noteTitle": "2377780 - S4TWL - Retail Factsheets", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using some Retail related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Fiori Factsheets allow to user friendly display of business objects.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, retail specific Fiori Factsheets for article, site, allocation, promotion, and bonus buy are not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Starting with SAP S/4HANA 1709 Object Pages for product, site and allocation table are available. Starting with SAP S/4HANA 1709 FPS01 Object Page for promotion is available. The development of further Object Pages are under evaluation for future releases.</p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Users will have to be trained to use Object Pages available instead of retail factsheets."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If customer specific coding re-used development objects in ERP which are no longer available in SAP S4/HANA, the coding needs to be adjusted accordingly."}]}