{"guid": "0050569455E21ED5B3E17678392AA09E", "sitemId": "SI3_IS_DIMP_M", "sitemTitle": "S4TWL - Stock Overview and Object Search by Characteristics", "note": 2270409, "noteTitle": "2270409 - S4TWL - Stock Overview and Object Search by Characteristics", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>This function displays stocks and objects (such as orders) with particular characteristics.</p>\n<p>The following Customizing activity is not available in S/4HANA:</p>\n<p><strong>Path: </strong><em>Materials Management --&gt; Inventory Management and Physical Inventory --&gt; Reporting --&gt; Define Profiles for Selection by Characteristics</em></p>\n<p>Transaction: OMILL_MMCL</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>S_KM5_93000002</p>\n</td>\n<td>\n<p>MILL_CUST</p>\n</td>\n<td>\n<p> </p>\n</td>\n<td>\n<p>IMG Activity: SIMGMPWMM_OMILL_MMCL</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p>The following Customizing activity is available in S/4HANA:</p>\n<p><strong>Path: </strong><em>Production --&gt; Shop Floor Control --&gt; Master Data </em><em>à</em><em> Order </em><em>à</em><em> Define Profiles for Selection by Characteristics</em></p>\n<p>Transaction: MILL_MSP</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>S_KM5_93000002</p>\n</td>\n<td>\n<p>MILL_CUST</p>\n</td>\n<td>\n<p> </p>\n</td>\n<td>\n<p>IMG Activity: SIMGMPWMM_OMILL_MMCL</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>The following transaction is not available in S/4HANA:</p>\n<p><strong>SAP Easy Access Menu: </strong><em>Logistics -&gt;Material Management -&gt;Inventory Management -&gt;Environment</em></p>\n<ul>\n<li><em>Stock</em></li>\n<li><em>Stock overview by characteristics</em></li>\n</ul>\n<p>Transaction: MMCL - Stock overview and Object Search by characteristics</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p>MMCL</p>\n</td>\n<td>\n<p>MILL_BAS</p>\n</td>\n<td>\n<p> </p>\n</td>\n<td>\n<p>Stock   Overview by Characteristics</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>The transaction MMCL has been replaced with the transaction BMBC (Batch Information Cockpit).</p>\n<p> </p>\n<p><strong>Business Process related information</strong></p>\n<p>Business Process supported based on BMBC - Batch Information Cockpit can be used to fulfil the requirements.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>BMBC - Batch Information Cockpit can be used to fulfil the requirements.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p><span ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" color:=\"\" en;=\"\" lang=\"EN\" minor-fareast;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" simsun;=\"\" zh-cn;=\"\">The simplification item is possibly relevant if the Business Function DIMP_SDUD (Discrete Indus. - Mill Products) is active in your system. You can check its status via transaction SFW_BROWSER (Switch Framework Browser) under object DIMP.</span></p>\n<p>The item is relevant if you are using transaction MMCL.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"158\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"446\">\n<p>If you have enhanced MMCL Report (Stock Overview and Object Search by Characteristics), you need to bring the enhancements to the  successor transaction BMBC (Batch Information Cockpit) also</p>\n<p>For more information, see SAP Note 2226677</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 4, "refer_note": [{"note": "2226677", "noteTitle": "2226677 - S/4 HANA: Deprecation of Mill Specific Stock Overview", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using a SAP ERP system and intend to perform the upgrade-like conversion to S/4 HANA.</p>\n<p>The custom code check shows customer objects affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<p>The custom code check refers to note 2226677, which covers deprecation of Mill specific stock overview changes in the area CA (Cross Applications).</p>\n<p>Transaction MMCL gets re-directed to BMBC.</p>\n<p>The piece list for this functionality is SI_MP_MMCL_TO_BMBC.</p>\n<p>Please refer to the note 2258914 - Movement from Transaction MMCL to Transaction BMBC for further details.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4_TRANSFORMATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note contains detailed descriptions on how to adopt customer objects to the deprecation of Mill specific stock overview in CA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You must eliminate the indicated usages from your customer objects.</p>", "noteVersion": 3}], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Customizing activity Transaction: OMILL_MMCL is not available in S/4HANA: Path: Materials Management --> Inventory Management and Physical Inventory --> Reporting --> Define Profiles for Selection by Characteristics Customizing activity Transaction: MILL_MSP is available in S/4HANA: Path: Production --> Shop Floor Control --> Master Data --> Order --> Define Profiles for Selection by Characteristics"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "The transaction MMCL has been replaced with the transaction BMBC (Batch Information Cockpit)."}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "If you have enhanced MMCL Report (Stock Overview and Object Search by Characteristics), you need to bring the enhancements to the successor transaction BMBC (Batch Information Cockpit) also. For more information, see SAP Note 2226677"}]}