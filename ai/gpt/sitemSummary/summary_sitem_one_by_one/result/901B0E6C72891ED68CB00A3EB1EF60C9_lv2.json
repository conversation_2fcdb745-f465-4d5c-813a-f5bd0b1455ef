{"guid": "901B0E6C72891ED68CB00A3EB1EF60C9", "sitemId": "SI7: Oil_Dynamic-Scheduling", "sitemTitle": "S4TWL - Dynamic Scheduling deprecation", "note": 2328538, "noteTitle": "2328538 - S4TWL-Dynamic scheduling functionality in TSW module is not available in SAP S/4HANA 1610", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As preparation for a system upgradation to SAP S/4HANA 1610.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span>IS-OIL-DS-TSW, TSW, Dynamic scheduling, Stock Projection Worksheet (SPW), Three-way pegging (3WP)</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Dynamic scheduling application is an integration of Stock Projection Worksheet (SPW) and Three-way pegging (3WP) functionality.The application integrates these two applications so that when a user makes a data-change in one application, he can see the effect of it in the other application in real-time (without requiring a data-save). This allows the user the create simulations in 3-way pegging, see its effects on the inventory in the Stock Projection, modify the simulations, undo-redo changes, make changes to already published nomination. Once satisfied, the user can save these changes to the database.As this application is not used much and thus is removed in SAP S/4HANA 1610.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description: </strong></p>\n<p>Dynamic scheduling application is an integration of Stock Projection Worksheet (SPW) and Three-way pegging (3WP) functionality. Dynamic Scheduling integrates these two applications so that when a user makes a data-change in one application, they can see the effect of it in the other application in real-time (without requiring a data-save). This allows the user to create simulations in 3-way pegging, see its effect on the inventory in the Stock Projection, modify the simulations, undo/redo changes, and make changes to already published nomination. Once satisfied, the user can save these changes to the database. As SAP has come up with similar applications recently, it creates confusion to customers with similar solutions and hence Dynamic Scheduling has been removed in SAP S/4HANA 1610.</p>\n<p><strong>Business Process related information</strong>: Dynamic scheduling is an integration of SPW and 3WP functionality which has been removed from SAP S/4HANA 1610.</p>\n<p><strong>Re</strong><strong>quired and Recommended Action:</strong> There is no equivalent functionality of dynamic scheduling available in SAP S/4HANA 1610, but advanced functionalities like Inventory Planning Workbench (IPW) and Nomination Planning Workbench (NPW) are available.</p>\n<p> <strong>How to Determine Relevancy </strong></p>\n<p>This item is relevant for all IS-Oil customer</p>", "noteVersion": 2, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "decide on use of successor functionalities such Inventory Planning Workbench (IPW) and Nomination Planning Workbench (NPW)"}, {"Activity": "Implementation project required", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Implement successor functionalities such Inventory Planning Workbench (IPW) and Nomination Planning Workbench (NPW), if required"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Remove or replace any references to depricated ABAP objects in your custom code"}]}