{"guid": "0090FABF370E1ED78F808F1A9D1360D2", "sitemId": "SI01: OGSD_OLD_DATACOLL", "sitemTitle": "S4TWL - OGSD - Classic Data Collation", "note": 2477777, "noteTitle": "2477777 - S4TWL - OGSD - Classic Data Collation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are converting a system with installed Add-on OGSD to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Simplification Items, System Conversion to SAP S/4HANA, Transactions /ICO/MO_R1, /ICO/MO_R2, /ICO/MO_R3, /ICO/MO_R9</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are using the OGSD application \"Classic Data Collation\" - that is the Data Collation using SAP-GUI.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description:</strong></p>\n<p>The old or \"Classic Data Collation\" incorporates the processes for the retrospective entry of all activities resulting from the purchase and procurement of petroleum products and the delivery of these products to customers. This means, it processes sales data \"after the fact\" and calls all needed functions or transactions from SD and MM for the actual process. It is used mainly for mass processing of sales data coming a) via interfaces (ALE) and b) to some smaller extent by entering data online, meaning that either you or an automatic process started by ALE enters the actual values from the purchasing and supply areas and from the sale and delivery of the procured and delivered materials.</p>\n<p><strong>Business related information:</strong></p>\n<p>Data collation (old and new alike) integrates and automates frequently used processes mainly in Materials Management (MM) and Sales and Distribution (SD).<br/>It therefore represents a direct connection of MM and SD processes, such as procuring materials, creating and changing sales contracts, sales orders, and deliveries.</p>\n<p>For further information about the old or \"Classic Data Collation\"  see:</p>\n<p><a href=\"https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/16c7aa533dae0077e10000000a4450e5.html\" target=\"_blank\">https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/16c7aa533dae0077e10000000a4450e5.html</a></p>\n<p>For further information about the Floorplan-Manager(FPM)-WebDynpro based New Data Collation see here:</p>\n<p><a href=\"https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/8e0cbd535862aa23e10000000a441470.html\" target=\"_blank\">https://help.sap.com/viewer/ac2edcbb31de46e48bb3986017667d9a/7.0.6/en-US/8e0cbd535862aa23e10000000a441470.html</a></p>\n<p>This page contains a comparison between both versions allowing for a better assessment of the usability and scope of both.</p>\n<p><strong>Required and recommended action(s):</strong></p>\n<ol>\n<li>If you want to continue using Data Collation, you need to switch to the <em>New Data Collation</em>, which is based on FPM and WebDynpro technology. In this case you have to check, if you have created some own functionality inside or for use by the now called \"Classic Data Collation\". If such own functionalities exists, you need to manually migrate your functionalities into the New Data Collation or adapt them in order to make use of them in the future too.</li>\n<li>You need to consider this as a project, as there are no migration tools which may assist you in transfering code of form-routines into class-methods or similar. All adaptations need to be executed manually.</li>\n<li>You do not need to migrate OGSD-own funtionalities of the Classic Data Collation, these have been migrated into the succcessor application \"New Data Collation\".</li>\n<li>You need to organize knowledge transfer to all users working with Classic Data Collation\" as their pattern of work will change when working with the \"New Data Collation\". Users will have to use new applications for creating, changing, displaying  and processing a new Data Collation document.</li>\n<li>An introduction to the new Data Collation can be found in a course available on openSAP: <br/>https://open.sap.com/courses/ogsd1<br/>Week 2 Units 1 - 4 deal with the new Data Dollation.</li>\n</ol>\n<p> </p>", "noteVersion": 4, "refer_note": [], "activities": [{"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Introduce and configure new data collation based on Web Dynpro."}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Transfer custom code in form-routines into class-methods of new data collation."}, {"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Design new data collation "}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}