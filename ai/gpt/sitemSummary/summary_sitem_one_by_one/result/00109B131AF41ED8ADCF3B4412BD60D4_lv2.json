{"guid": "00109B131AF41ED8ADCF3B4412BD60D4", "sitemId": "SI022: CRM", "sitemTitle": "\"Custom Fields and Logic\" replaces EEW and AET as extensibility tool", "note": 2692845, "noteTitle": "2692845 - \"Custom Fields and Logic\" replaces EEW and AET as extensibility tool", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are considering or planning a migration of a CRM 7.0 or CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. The following information on changed or deprecated functionality is relevant in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRMTWL Advanced Search Reporting Framework</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In order to add custom fields to business transactions, the S/4 app \"Custom Fields and Logic\" has to be used. The use of the Easy Enhancement Workbench (EEW) or the Application Enhancement Tool (AET) is no longer supported for append fields. AET is still supported for adding table-like extensions to business transactions. Note that table extensions generated by AET are <strong>not</strong> supported in any search application.<br/>Moreover, the \"Rapid Applications\" feature of AET is deprecated as well. In order to create custom applications from scratch, the S/4 app \"Custom Business Objects\" must be used.</p>", "noteVersion": 3, "refer_note": []}