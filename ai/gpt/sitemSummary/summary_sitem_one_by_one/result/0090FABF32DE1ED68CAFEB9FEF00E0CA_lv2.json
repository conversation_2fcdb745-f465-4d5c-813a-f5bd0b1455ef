{"guid": "0090FABF32DE1ED68CAFEB9FEF00E0CA", "sitemId": "SI6: Oil_eNetting", "sitemTitle": "S4TWL - eNetting Deprecation", "note": 2328548, "noteTitle": "2328548 - S4TWL-eNetting functionality in exchanges module is not available in S/4 HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As preparation for a system conversion to SAP S/4HANA 1610 exchange reconciliation through eNetting.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<div class=\"WordSection1\">\n<p>IS-OIL-DS-EXG, IS-OIL-NET, Exchanges, eNetting</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>eNetting functionality in exchanges module is not available in SAP S/4HANA 1610.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong>:</strong></p>\n<p>eNetting application is used for exchange reconciliation and was available till 4.6C version of ERP. The exchange detail statement generated from R/3 is transmitted to eNetting application for exchange reconciliation. The data transfer between an R/3 system and a non-R/3 system is typically achieved using the IDOC interface. In the eNetting scenario, the exchange detail statement IDOC is generated from the R/3 system and is transmitted to the SAP Business Connector (middleware tool) through ALE integration technology.  The SAP Business Connector is then responsible for the transformation of the data from IDOC to XML that is used for further processing by the eNetting application. SAP has stopped supporting middleware tool ‘SAP Business Connector’ with the latest solutions like Process integration availability.</p>\n<p><strong>Business Process related information</strong></p>\n<p>eNetting functionality was available till 4.6C version of ERP which is not available in SAP S/4HANA 1610.</p>\n<p><strong>Re</strong><strong>quired and Recommended Action:</strong></p>\n<p>There is no equivalent functionality of eNetting available in SAP S/4HANA 1610.</p>\n<p> <strong>How to Determine Relevancy </strong></p>\n<p>This item is relevant for all IS-Oil customer</p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Remove any references to depricated ABAP objects in your custom code"}, {"Activity": "Interface Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "if still required, implement new interfaces to external netting application"}]}