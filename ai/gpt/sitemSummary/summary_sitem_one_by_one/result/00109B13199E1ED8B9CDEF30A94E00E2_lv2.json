{"guid": "00109B13199E1ED8B9CDEF30A94E00E2", "sitemId": "SI07: CPD_Personal_Activity_List", "sitemTitle": "S4TWL - CPM - Personal Activity List", "note": 2712156, "noteTitle": "2712156 - S4TWL - CPM - Personal Activity List", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion  to SAP Commercial Project Management for SAP S/4HANA 1809 or upgrading from earlier versions of S/4HANA Commercial Project Management. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Personal Activity List, SAP Commercial Project Management, CA-CPD, S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Project activity app provides simillar functionality to that of personal activity list</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With <em>SAP Commercial Project Management for SAP S/4HANA 1809</em>, \"<em>Personal Activity List\"</em> is not available.Navigation to the <em>Personal Activity List</em> is withdrawn from multiproject overview application and from project workspace.Note that a functional equivalent is available.</p>\n<p><strong>Required and Recommended Actions</strong></p>\n<ul>\n<li>Use <strong><em>Project Activities</em></strong><strong> </strong>application for managing activities.</li>\n<li>Adapt related custom enhancements. </li>\n<li>Inform key users and end users.</li>\n</ul>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Fiori Implementation", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "Use Fiori App \"Project Activities\" for managing activities."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adapt related custom enhancements."}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Inform key users and end users."}]}