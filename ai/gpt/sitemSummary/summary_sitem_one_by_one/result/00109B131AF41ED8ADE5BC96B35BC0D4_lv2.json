{"guid": "00109B131AF41ED8ADE5BC96B35BC0D4", "sitemId": "SI005: CRM", "sitemTitle": "Product configuration performed in AVC; IPC not supported as config. engine", "note": 2693516, "noteTitle": "2693516 - Product configuration performed in AVC; IPC not supported as configuration engine", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are considering or planning a migration of an SAP CRM 7.0 or SAP CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. In this case, the following information about changed or deprecated functionality is relevant.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4CRMTWL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Product configuration of configurable products in SAP S/4HANA Service is always executed using the Advanced Variant Configuration (AVC) from SAP S/4HANA. Internet Pricing and Configurator (IPC) is no longer used as the configuration engine.</p>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span>If you are using product configuration in business transactions in your SAP CRM 7.0 system, then the table IBIB contains entries where the field KBID is not initial. Alternatively, you can check whether table CRMD_SCE_IBKB contains entries.</span></p>", "noteVersion": 3, "refer_note": []}