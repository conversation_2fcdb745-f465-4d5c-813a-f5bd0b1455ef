{"guid": "0050569455E21ED5B3E176783919C09E", "sitemId": "SI3: Logistics_PS ", "sitemTitle": "S4TWL - Production Resources and Tools functions for projects", "note": 2270262, "noteTitle": "2270262 - S4TWL - Production Resources and Tools functions for projects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP to SAP S/4HANA or an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this note. The following SAP S/4HANA Simplification Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA, Compatibility Scope, System Conversion, Upgrade, PRT</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Production Resources and Tools functions for projects is part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it’s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, Production Resources and Tools functions for projects can be found under the ID 463.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Production Resources and Tools functions for projects can be used as part of the compatibility scope.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Knowledge Transfer to key- and end user, usage of alternatives to PRTs need to be considered</p>\n<div>\n<div>\n<p> </p>\n</div>\n</div>", "noteVersion": 4, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Any time", "Condition": "Optional", "Additional_Information": "Provide training to key and end users. Consider alternatives to PRTs."}, {"Activity": "User Training", "Phase": "Any time", "Condition": "Optional", "Additional_Information": "Provide training to key and end users. Consider alternatives to PRTs."}]}