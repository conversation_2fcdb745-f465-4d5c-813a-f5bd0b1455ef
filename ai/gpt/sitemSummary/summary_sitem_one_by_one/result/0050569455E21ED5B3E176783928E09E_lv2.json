{"guid": "0050569455E21ED5B3E176783928E09E", "sitemId": "SI7: IS_DIMP_AUT", "sitemTitle": "S4TWL - Planning Regular Route Shipments", "note": 2270399, "noteTitle": "2270399 - S4TWL - Planning Regular Route Shipments", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Automotive, ECC-DIMP, S/4HANA, Transportation, Regular Routes</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Reason: Conversion from SAP ERP for Discrete Industries &amp; Mill Products (ECC-DIMP) to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The automotive industry solution for planning inbound shipments for regular routes based on route schedules in SAP ERP Discrete Industries &amp; Mill Products (ECC-DIMP) is not available in SAP S/4HANA.</p>\n<p>Regular routes like milk-run tours lead to recurrent shipments. Route schedules could be defined as basis for generating these recurrent inbound shipments. Inbound deliveries created based on shipping notifications from suppliers could be directly linked as items to the generated inbound shipments. The inbound shipments could then be used for goods receipt processing collectively for all inbound deliveries shipped together.</p>\n<p>Planning regular route shipments has a low customer adoption and is not available in SAP S/4HANA.</p>\n<p>With SAP Transportation Management, a new solution for planning, executing and settling shipments is available.</p>\n<p> </p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, the automotive industry solution for maintaining schedules for regular routes and planning and creating inbound shipments based on route schedules is not available.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Transactions not available in SAP S/4HANA</td>\n<td>\n<p>VL51A, VL52A, VL53A, VL54A</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>The Simplification Item is relevant, if:</p>\n<ul>\n<li>Route schedules have been maintained (check for entries in table BORRTTS with transaction SE16)</li>\n<li>Route schedule determination records have been maintained (check for entries in table BORRTFI with transaction SE16)</li>\n</ul>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Evaluate SAP Transportation Management for planning and scheduling shipments before conversion to SAP S/4HANA.</p>\n<p>Remove route schedules and route schedule determination records before conversion to SAP S/4HANA.</p>\n<p> </p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom Code Check</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note 2227568</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 1, "refer_note": [{"note": "2227568", "noteTitle": "2227568 - SAP S/4HANA Simplification: DIMP Automotive - Regular route shipments", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using an SAP ERP system and intend to perform the system conversion to SAP S/4HANA.</p>\n<p>The custom code check shows those customer objects that are affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, Simplification List, DIMP, Automotive, VL51A, VL52A, VL53A, VL54A</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The automotive solution for regular route shipments, which was a part of Discrete Industries and Mill Products (DIMP) industry solutions, is not available in SAP S/4HANA. The custom code using the related objects needs to be adapted.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You must remove the usages of SAP objects in the piece list SI_AUT_07_TP from your custom code, as indicated by the custom code check.</p>\n<p> </p>", "noteVersion": 3, "refer_note": [{"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Evaluate SAP Transportation Management for planning and scheduling shipments."}, {"Activity": "Data cleanup / archiving", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Remove route schedules and route schedule determination records"}, {"Activity": "Customizing / Configuration", "Phase": "During or after conversion project", "Condition": "Conditional", "Additional_Information": "Set up SAP Transportation Management as new solution for planning, executing and settling shipments"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Data migration", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "New developments", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}]}