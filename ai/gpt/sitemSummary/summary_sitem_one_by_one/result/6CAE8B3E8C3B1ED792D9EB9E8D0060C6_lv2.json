{"guid": "6CAE8B3E8C3B1ED792D9EB9E8D0060C6", "sitemId": "BW120: InfoObject Catalogs", "sitemTitle": "BW4SL - InfoObject Catalogs", "note": 2442621, "noteTitle": "2442621 - BW4SL - InfoObject Catalogs and InfoObjects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><strong>InfoObject Catalogs (IOBC)</strong></p>\n<p>InfoObject Catalogs are not available in SAP BW/4HANA. InfoObject Catalogs have been replaced by InfoAreas (AREA).</p>\n<p><strong>InfoObjects (IOBJ)</strong></p>\n<p>The following types of InfoObjects are not available in SAP BW/4HANA:</p>\n<ul>\n<li>Characteristics that use 0RECORDMODE as an attribute</li>\n<li>Characteristics that are enabled for real-time-data acquisition</li>\n<li>Characteristics that use class CL_RSR_REMOTE_MASTERDATA as their master data read class</li>\n<li>Key figures of data type \"Date\" or \"Time\" with aggregation SUM</li>\n</ul>\n<p>Also note that extraction out of InfoObjects in SAP BW/4HANA to target systems is no longer possible using Export DataSources but only using ODP-BW. For further information see SAP Note <a href=\"/notes/2483299\" target=\"_blank\">2483299</a> - BW4SL - Export DataSources (SAP BW/4HANA as Source).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IOBC, DIOC, IOBJ, DIOB</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are available in or can be converted to SAP BW/4HANA.</p>\n<p>See node: Automated Clean-up --&gt; Adjustments --&gt; TLOGO IOBC (InfoObject Catalog)</p>\n<p>See note: Manual Redesign (take the necessary action) --&gt; BW Object Type --&gt; TLOGO IOBJ (InfoObject)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>InfoObject Catalogs (IOBC)</strong></p>\n<p>You can migrate the assignments of InfoObjects to InfoObject Catalogs to InfoAreas by using report RSDG_IOBJ_IOBC_MIGRATE_TO_AREA. When running the report the system is set to an \"InfoArea-only\" mode. Please note that once the system has been switched to the \"InfoArea-only\" mode the authorization object S_RS_IOBJA is evaluated instead of S_RS_IOBJ.</p>\n<p><strong>InfoObjects (IOBJ)</strong></p>\n<p>You need to adjust the identified InfoObjects manually.</p>\n<p>Material InfoObjects with conversion exits MATN1 or MATNB are handled in a special way. For details, see SAP Note <a href=\"/notes/2635167\" target=\"_blank\">2635167</a> - Handling of Material InfoObjects with Conversion to SAP BW/4HANA.</p>\n<p>For details about key figures of type DATS or TIMS and SUM aggregation, see SAP Note <a href=\"/notes/1940630\" target=\"_blank\">1940630</a>.</p>\n<p>InfoObjects that are shipped with SAP BW or SAP BW/4HANA (see table RSDIOBJFIX) might require special handling. These InfoObjects are not part of BI or BW4 Content and generally activated at installation time. This includes for example the well-known time characteristics. We recommend to use the latest version of and keep the delivered settings for these InfoObject (adding custom attributes is allowed)  If you are using an out-of-date, the system might not function properly or missing features. This includes for example pre-delivered master data read classes (value help might not work properly without it) and attributes for time characteristics (like number of days per period). If you are doing an in-place conversion, we therefore recommend to reactivate these InfoObjects after conversion to SAP BW/4HANA to ensure you have the latest version. For remote conversion, the latest version will be used when installing SAP BW/4HANA (at the beginning of the project), so adjustments are not required.</p>\n<p><strong>Related Information</strong></p>\n<p>For more information refer to the documentation:</p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/ab216bfb8b2b452a860267b1e40e270f/7.5.5/en-US/2a06df6c091148ab9d61e48ae711f6a0.html\" target=\"_blank\">InfoAreas</a></li>\n<li><a href=\"https://help.sap.com/viewer/04030263a0d041309a039fa3ea586720/7.5.5/en-US/0469851ddfaa456781a431fc4c88e17b.html\" target=\"_blank\">Authorizations for InfoObjects</a></li>\n<li><a href=\"https://help.sap.com/viewer/04030263a0d041309a039fa3ea586720/7.5.5/en-US/f3722f8bd0e04d3e8f93bd7477c9980d.html\" target=\"_blank\">Creating InfoObjects</a></li>\n</ul>", "noteVersion": 9, "refer_note": [{"note": "2421930", "noteTitle": "2421930 - Simplification List for SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Simplification List for SAP BW/4HANA is published as PDF document on the SAP Help Portal (<a href=\"http://help.sap.com/bw4hana20\" target=\"_blank\">http://help.sap.com/bw4hana20</a>). This SAP Note provides the Simplification List in spreadsheet format.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SIMPL, BW4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Planning to transition from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To enable our customers to better plan and estimate their path to SAP BW/4HANA, we have created the “Simplification List for SAP BW/4HANA”. In this list we describe in detail, on a functional level, what happens to individual object types and solution capabilities when transitioning to SAP BW/4HANA. Compared to the SAP Business Warehouse products, we have in some cases merged certain functionality with other elements or reflected it within a new solution or architecture.</p>\n<p><strong>A new, web-based UI for searching and displaying Simplification Items</strong></p>\n<p>In parallel to the PDF document and the spreadsheet available via this SAP Note, the <em>SAP Simplification Item Catalog</em> offers direct search and browse of SAP BW/4HANA Simplification Items in their current state via the SAP ONE Support Launchpad at <a href=\"https://launchpad.support.sap.com/#/sic/\" target=\"_blank\">https://launchpad.support.sap.com/#/sic/</a>.</p>", "noteVersion": 6, "refer_note": [{"note": "2347382", "noteTitle": "2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to run or you are already running an SAP BW/4HANA system.<br/>You want to convert your SAP BW system to SAP BW/4HANA.<br/>You want to upgrade your system from SAP BW/4HANA 1.0 to SAP BW/4HANA 2.0</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Revision, BW4, BW/4HANA, BW, NetWeaver</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p><strong>This note contains information about the following topics:</strong></p>\n<ol>\n<li>General Information about SAP BW/4HANA and the conversion to SAP BW/4HANA</li>\n<li>Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</li>\n<li>Information relevant for SAP BW/4HANA 2023</li>\n<li>Information relevant for SAP BW/4HANA 2021</li>\n<li>Information relevant for SAP BW/4HANA 2.0</li>\n<li>Information relevant for SAP BW/4HANA 1.0</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><span>1. General information about SAP BW/4HANA and the conversion to SAP BW/4HANA</span></strong></p>\n<p><span>High Level information about BW/4HANA: <a href=\"http://www.sap.com/bw4hana\" target=\"_blank\">http://www.sap.com/bw4hana</a></span></p>\n<p><span>See SAP Note <a href=\"/notes/2347384 \" target=\"_blank\">2347384</a> - SAP BW/4HANA 1.0 Important Notes before you start</span></p>\n<p><span>When planning your system landscape(s) for the next release of SAP BW/4HANA (not release 1.00), you may want to take the available applicaton server platforms into account. Please refer to SAP note <a href=\"/notes/2620910\" target=\"_blank\">2620910</a> </span></p>\n<p><span>SAP changed the <a href=\"https://support.sap.com/en/my-support/knowledge-base/security-notes-news.html\" target=\"_blank\">SAP security strategy</a> to cover security corrections for Support Packages delivered during the past 24 months (formerly 18 months). BW/4HANA will not extend this period. We provide security corrections for Support Packages delivered in the last 18 months as before.</span></p>\n<p><span>See note </span><a href=\"/notes/2733740\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2733740</a> for Information/Restriction about Relase BW/4HANA 2.0</p>\n<p><strong>2. Recommendations with regards to SAP BW/4HANA releases and SAP HANA revisions</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Productively used SAP BW/4HANA release</strong></td>\n<td><strong>Recommended minimum SAP HANA SP/revision</strong></td>\n</tr>\n<tr>\n<td>1.0</td>\n<td>Revision 122.12 or higher (HANA 1.0 SPS 12)<br/>Revision 12 or higher (HANA 2.0 SPS 1 or higher)</td>\n</tr>\n<tr>\n<td>2.0</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2021</td>\n<td>Revision 64 or higher (HANA 2.0 SPS 6 or higher)</td>\n</tr>\n<tr>\n<td>2023</td>\n<td>Revision 71 or higher (HANA 2.0 SPS 7 or higher)</td>\n</tr>\n</tbody>\n</table></div>\n<p>Please note that SAP strongly recommends to apply the <span>latest available</span> SAP HANA revision.</p>\n<p>For details about SAP HANA Maintenance Strategy see SAP Note <em><a href=\"/notes/2021789\" target=\"_blank\">2021789</a> - SAP HANA Revision and Maintenance Strategy</em>.</p>\n<p>Please keep the update paths for Maintenance Revisions in mind. Details can be found in SAP Note <em><a href=\"/notes/1948334\" target=\"_blank\">1948334</a> - SAP HANA Database Update Paths for Maintenance Revisions</em>.</p>\n<p>Notes with regards to SAP HANA Revisions:</p>\n<ul>\n<li>HANA SPS12: SAP Note <a href=\"/notes/2298750\" target=\"_blank\">2298750</a> - SAP HANA Platform SPS 12 Release Note</li>\n</ul>\n<p><span>SAP BW/4HANA and SAP HANA 2.0: </span></p>\n<p>SAP BW/4HANA 1.00 (including SAP_BASIS 7.50) has been released for HANA 2.0. Details can be found in note 2420699.<br/>SAP BW/4HANA 2.00 (including SAP_BASIS 7.53) and newer run on HANA 2.0 only</p>\n<p><strong> </strong></p>\n<p><strong>3. </strong><strong>Information relevant for SAP BW/4HANA 2023 (planned mid of Q4/2023)</strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3365187\" target=\"_blank\">3365187 - Information/Restriction Note for Release SAP BW/4HANA 2023</a>.</p>\n<p><strong><strong>4. Information relevant for SAP BW/4HANA 2021</strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"3100794\" target=\"_blank\">3100794 - Release Information/Restrictions Note for SAP BW/4HANA 2021</a>.</p>\n<p><strong><strong><strong>5. Information relevant for SAP BW/4HANA 2.0</strong></strong></strong></p>\n<p>You can find the detailed information in SAP Note: <a href=\"https://me.sap.com/notes/2733740\" target=\"_blank\">2733740 - Release Information/Restrictions Note for SAP BW/4HANA 2.0</a></p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>See note 2733740 - Release Information/Restriction Note for SAP BW/4HANA 2.0 for restrictions in BW/4HANA 2.0</li>\n<li>Urgent! Implement note <a href=\"/notes/2770525\" target=\"_blank\" title=\"2770525  - Data Transfer Intermediate Storage (DTIS): Missing package assignment\">2770525 - Data Transfer Intermediate Storage (DTIS): Missing package assignment</a> <span><strong>before</strong></span> upgrading to BW/4HANA 2.0</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Search/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Introduction Video: <a href=\"https://youtu.be/bxH0EzAhSwQ\" target=\"_blank\">https://youtu.be/bxH0EzAhSwQ</a></li>\n</ul>\n<li><span>Upgrading BW/4HANA 1.0 to 2.0</span></li>\n<ul>\n<li>We now introduced tasklist SAP_BW4_BEFORE_UPGRADE and SAP_BW4_AFTER_UPGRADE  to support you upgrading your system. Please see note 2846537 - Taskliste for upgrade of BW/4HANA</li>\n<li>Afte the upgrade to BW/4HANA 2.0 the flag TADIR Popup f. Obj. in View RSADMINC gets initialized. Please see note 2884312 for details.</li>\n</ul>\n<li><span>ABAP Software Stack</span></li>\n<ul>\n<li>The initial Software stack for BW/4HANA 2.0 looks like this:</li>\n<ul>\n<li>SAP_BASIS 7.53 SP01</li>\n<li>SAP_ABA 7.5D SP01</li>\n<li>SAP_UI 7.53 SP02</li>\n<li>SAP_GWFND 7.53 SP01</li>\n<li>ST-PI 7.40 SP09</li>\n<li>DW4CORE 2.00 SP00</li>\n<li>UIBAS100 4.00 SP01</li>\n</ul>\n<li>This should dramatically reduces the effort for installation</li>\n<li>During the upgrade from BW/4HANA 1.0 the missing software components are installed automatically</li>\n<li></li>\n</ul>\n<li><span>BW/4HANA 2.0 SP01</span></li>\n<ul>\n<li>Please install following note on top of SP01 directly after update:</li>\n<ul>\n<li>2779827  Error starting task list SAP_BW4_LAUNCHPAD_SETUP from other task lists</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP02 </span></li>\n<ul>\n<li>SAP_UI 7.54 SP1 as alternative UI version is released. Please check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07</li>\n<li>please also take into consideration the Support Package Upgrade Strategy when running on higher UI Version described in note <strong>2618449</strong></li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA 2.0 SP04</span></li>\n<ul>\n<li>SAP_UI 7.54 &gt;= SP2 as alternative UI version is released. lease check notes <strong>2903662 </strong>and<strong> 2908858</strong>; please note that \"SAP Quartz Light\" introduced with 1.65 is not yet supported but will be supported as of FP07. Also take into consideration the Support Paackage Strategy when running on higher UI version described in note <strong>2618449</strong></li>\n<li>NSE for DTO --&gt; available since HANA 2.0 SP04 + limitation see HANA Note <a href=\"/notes/2771956\" target=\"_blank\">https://launchpad.support.sap.com/#/notes/2771956</a></li>\n<li>Open Hub with SDA --&gt; available since HANA 2.0 SP04 Revision 46</li>\n<li>XDA --&gt; available since HANA 2.0 Sp04 Revision 40 </li>\n<li>Geo Support --&gt; available since HANA 2.0 SP04 Revision 45</li>\n<li>Treespec Partitioning in ADSO --&gt; available since Hana 2.0 SP04 </li>\n</ul>\n</ul>\n<p><strong>6. Information relevant for SAP BW/4HANA 1.0</strong></p>\n<p>Installation/Setup</p>\n<ul>\n<li><span>General</span></li>\n<ul>\n<li>Please see SAP Note<strong> </strong><a href=\"/notes/2354516\" target=\"_blank\">2354516</a> to enable client copy after installation.</li>\n<li><strong>Before </strong>you start the setup via tasklist (stc01) SAP_BW4_SETUP_SIMPLE check if all HANA authorizations are maintained like in SAP Note <a href=\"/notes/2362807\" target=\"_blank\">2362807</a> - BW Serach/Value Help/Open in SAP BW Modeling Tools does not give a result.</li>\n<li>Equivalent Support Packages<br/>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>SAP BW 7.50 (SAP_BW)</td>\n<td>BW/4HANA 1.00(DW4CORE)</td>\n<td>BPC 11 (BPC4HANA)</td>\n<td>SAP UI 7.52 </td>\n</tr>\n<tr>\n<td>               4</td>\n<td>        Initial Installation</td>\n<td>           N.A.</td>\n<td> </td>\n</tr>\n<tr>\n<td>               5</td>\n<td>                   1</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               6</td>\n<td>                   &gt;= 2</td>\n<td> </td>\n<td> </td>\n</tr>\n<tr>\n<td>               7</td>\n<td>                   &gt;= 4</td>\n<td>  Initial Installation</td>\n<td> </td>\n</tr>\n<tr>\n<td>               8</td>\n<td>                   &gt;= 5</td>\n<td>             &gt;= 1</td>\n<td> </td>\n</tr>\n<tr>\n<td>               9</td>\n<td>                   &gt;= 6</td>\n<td>             &gt;= 2             </td>\n<td> </td>\n</tr>\n<tr>\n<td>             10</td>\n<td>                   &gt;= 7</td>\n<td>             &gt;= 3</td>\n<td> </td>\n</tr>\n<tr>\n<td>             11*</td>\n<td>                   &gt;=8</td>\n<td>             &gt;=4</td>\n<td>      &gt;=2</td>\n</tr>\n<tr>\n<td>             12*</td>\n<td>                   &gt;=9</td>\n<td>             &gt;=5</td>\n<td>      &gt;=3</td>\n</tr>\n</tbody>\n</table></div>\n</li>\n</ul>\n</ul>\n<p><span>*) Starting with BW/4HANA 1.00 SP08 SAP_UI 7.52 is required</span></p>\n<ul>\n<li><span>BW/4HANA SP08</span></li>\n<ul>\n<li><strong>Requirements:</strong></li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP10</li>\n<li>SAP_UI <strong>7.52 SP01 -- update required</strong></li>\n</ul>\n<li><strong>For new Installs:</strong></li>\n<ul>\n<li>For your convience we created a Support Release for BW/4HANA 1.00 including the following components:</li>\n<ul>\n<li>SAP_BASIS 7.50 SP10</li>\n<li>SAP_GWFND 7.50 SP10</li>\n<li>SAP_ABA 7.5A SP 10</li>\n<li>SAP_UI 7.52 SP02</li>\n<li>DW4CORE SP08</li>\n<li>BW4CONTB SP03</li>\n</ul>\n<li>this installation is based on Software Provisioning Manager 2.0 which requires HANA 2 SP2 Revision 23 see note 2610954.</li>\n<li>Release of the installation is planned for april (CW 17)</li>\n</ul>\n<li><strong>Enhancment in Tasklist</strong></li>\n<ul>\n<li>there are some enhancements made in the tasklists with regards to the new Web UI's: Please refert to note 2351381 for details.</li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP03</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n</ul>\n<li><span>BW/4HANA SP02</span></li>\n<ul>\n<li>In some cases the execution of the tasklist SAP_BW4_SETUP_SIMPLE is dumping. Please have a look at 2351381 how to disable the invalid BAPI</li>\n<li>Please implement the following notes on top of SP02 to avoid issues during setup/tasklisk execution</li>\n<ul>\n<li>2410466                BW Workspace customizing: RSDDTREXADMIN_TO_RSWSPCUST dumps </li>\n</ul>\n</ul>\n<li><span>BW/4HANA SP01</span></li>\n<ul>\n<li>Please implement the following notes on top of SP01 to avoid issues during setup/later SP update</li>\n<ul>\n<li>2385382               Add SAPFSPOR to avoid strange DUMPS during SP update</li>\n<li>2385265               Check if essential objects are active after installation</li>\n<li>2381312               Task list SAP_BW4_SETUP_SIMPLE: Error in \"Check/Generate Packages for BW ODATA Services\" step</li>\n</ul>\n<li>General Information about tasklists and their usage in BW/4HANA: 2351381 SAP BW/4HANA task lists</li>\n</ul>\n</ul>\n<ul>\n<li><span>BW/4HANA SP00</span></li>\n<ul>\n<li>If job BI_WRITE_PROT_TO_APPLLOG is not started by the tasklist please see SAP Note <a href=\"/notes/2356498\" target=\"_blank\">2356498</a>.</li>\n<li>In SP0 the tasklist shows an error which can be ignored. See SAP Note <a href=\"/notes/2351344\" target=\"_blank\">2351344</a> for details.</li>\n</ul>\n</ul>", "noteVersion": 36}, {"note": "2383530", "noteTitle": "2383530 - Conversion from SAP BW to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note describes the different paths to get from SAP BW to SAP BW/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP BW/4HANA, SAP BW, Migration, Conversion, Transfer, In-Place, Remote, Shell, Lifecycle Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>0 Disclaimer</strong></span></p>\n<p>Converting a SAP BW system to SAP BW/4HANA is not a simple task. There is no \"wizard\" that magically converts everything. Instead, SAP provides a well-defined process to guide you through the renovation of your data warehouse. We have developed tools to automate this renovation where applicable and feasible, but they are not built or meant to fix badly designed models or clean-up neglected systems. In any conversion there is a need for manual interaction and re-design. The amount of such manual tasks varies from customer to customer and depends on the configuration and state of the SAP BW system. For example, a conversion of newer systems that have been configured using SAP HANA-optimized objects and LSA++ from the beginning will be relatively easy. In contrast to that, a conversion of older systems that are running on non-SAP HANA databases, using out-of-date interfaces, legacy data models, and multiple redundant layers can become quite challenging.</p>\n<p>It is therefore essential that you understand the differences between SAP BW and SAP BW/4HANA and how to handle them, thoroughly analyze your existing system, estimate the complexity and duration of required tasks, properly plan all conversion activities, learn and practice to use the conversion tools, test the conversion processes ideally with a \"copy of production\", and actively manage changes to your business. It speaks for itself that good project management and skilled personnel will be required to execute a timely and efficient conversion of a complete SAP BW system. If done right, your conversion to SAP BW/4HANA will be very successful.</p>\n<p>SAP offers several way to help. To get to started run a <a href=\"https://help.sap.com/viewer/p/SAP_READINESS_CHECK\" target=\"_blank\">SAP Readiness Check</a> or book a <a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">SAP Value Assurance service </a><a href=\"http://sapsupport.info/offerings/sap-value-assurance/\" target=\"_blank\">for SAP BW/4HANA</a>.</p>\n<p>We also highly recommend the Class Room Training <strong>BW4HC</strong>. (<a href=\"https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/\" target=\"_blank\">https://training.sap.com/course/bw4hc-system-conversion-to-sap-bw4hana-classroom-017-de-en/</a>?).</p>\n<p> </p>\n<p><span><strong>1 Overview</strong></span></p>\n<p><strong>1.1 What is SAP BW/4HANA</strong></p>\n<p>SAP BW/4HANA is a new, next generation data warehouse product from SAP that, like SAP S/4HANA, is optimized for the SAP HANA platform, including inheriting the high performance, simplicity, and agility of SAP HANA. SAP BW/4HANA delivers real time, enterprise wide analytics that minimize the movement of data and can connect all the data in an organization into a single, logical view, including new data types and sources.<strong><br/></strong></p>\n<p><strong>1.2 Related Information</strong></p>\n<p>Please find more information here:</p>\n<ul>\n<li>The Future of Data Warehousing - SAP BW/4HANA  <br/><a href=\"http://sap.com/bw4hana\" target=\"_blank\">http://sap.com/bw4hana</a></li>\n<li>SAP BW/4HANA Community (includes FAQ)<br/><a href=\"https://community.sap.com/topics/bw4-hana\" target=\"_blank\">https://community.sap.com/topics/bw4-hana</a><br/><a href=\"https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/08/c4458a08-877c-0010-82c7-eda71af511fa.html</a></li>\n<li>Online Documentation<br/><a href=\"http://help.sap.com/bw4hana10\" target=\"_blank\">http://help.sap.com/bw4hana10</a></li>\n<li>Product Roadmap<br/><a href=\"https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021\" target=\"_blank\">https://roadmaps.sap.com/board?range=FIRST-LAST&amp;PRODUCT=73554900100800000681#Q1%202021</a></li>\n<li>SAP BW/4HANA vs. SAP BW<br/><a href=\"https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2017/08/30aa5e11-cf7c-0010-82c7-eda71af511fa.html</a><br/><br/></li>\n</ul>\n<p>For information related to functionality available in SAP BW but replaced or deleted in SAP BW/4HANA please refer to the Simplification List: <br/><a href=\"https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf\" target=\"_blank\">https://help.sap.com/doc/590752e646cc4b15a9092f32353b209a/1.0/en-US/SAP_BW4HANA_10_Simplification_List.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 1.0: <br/><a href=\"https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c3f4454877614bc7b9e85ae1f9d1d2c7/1.0/en-US/SAP_BW4HANA_10_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2.0:</p>\n<p><a href=\"https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/999ae5f8c578402dab1fea94fa4599f9/2.0/en-US/SAP_BW4HANA_20_Conversion_Guide.pdf</a></p>\n<p>For a holistic view of the Conversion topic, see Conversion Guide for SAP BW/4HANA 2021 and higher:</p>\n<p><a href=\"https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf\" target=\"_blank\">https://help.sap.com/doc/c8e1ac37fee64e0f887a2d6d6af32a33/2021.00/en-US/SAP_BW4HANA_2021_Conversion_Guide.pdf</a></p>\n<p> </p>\n<p><span><strong>2 How to Get to BW/4HANA</strong></span></p>\n<p><strong>2.1 Paths to SAP BW/4 HANA</strong></p>\n<p><strong><em>2.1.1 New implementation or fresh start</em></strong></p>\n<p>New implementations are the best choice for customers converting from a legacy system or building a system from scratch with new data model only.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, selective transport of BW objects (optional), implement HANA-optimized data models and flows, followed by data load and quality checks.</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), system connection (SAP Source), SAP BW/4HANA ETL, file upload and/or SAP HANA EIM (legacy sources)</p>\n<p>See SAP First Guidance: Complete functional scope (CFS) for SAP BW/4HANA: <a href=\"https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">https://www.sap.com/documents/2016/09/b001a9de-8a7c-0010-82c7-eda71af511fa.html<br/></a></p>\n<p><em><strong>2.1.2 System conversion</strong></em></p>\n<p>A system conversion addresses customers who want to change their current SAP BW system into a SAP BW/4HANA system. Using the Transfer Cockpit provided by SAP, the logical systemname of the system can be kept (in-place conversion) or a new  logical systemname can be used (remote conversion resp. shell conversion).</p>\n<p>A conversion project typically follows this sequence:</p>\n<ul>\n<li><strong>Discover / Prepare Phase</strong>: check system for BW/4HANA compliance (gather information about objects and code that needs to be transferred or changed), estimate effort for the conversion project</li>\n<li><strong>Explore / Realization Phase</strong>: Transfer legacy objects into HANA-optimized counterparts, system conversion, post conversion tasks</li>\n</ul>\n<p><em>*******. In-Place conversion</em></p>\n<p>Systems running on SAP BW 7.50 powered by SAP HANA can be converted in-place keeping their logical systemname. In the realization phase of the conversion project, classic objects have to be transferred into their HANA optimized replacements using the Transfer Cockpit. This transfer can be performed scenario-by-scenario. When all classic objects have been replaced, the system conversion to SAP BW/4HANA can be triggered.</p>\n<p>SAP highly recommends to use the latest support package for SAP BW/4HANA when performing the conversion. See note https://launchpad.support.sap.com/#/notes/2347382 regarding the equivalent support packages. Please keep the RTC Dates of the different Support Packages in mind when you plan your conversion project.</p>\n<p><strong>Steps</strong>: Migrate and/or upgrade to SAP BW 7.50 powered by SAP HANA (if necessary), install SAP BW/4HANA Starter Add-On (see “Installation” below), install Transfer Cockpit, transfer data models, adjust custom coding, perform system conversion</p>\n<p>To include the Data Comparison Functionality into InPlace-TaskList, please refer to the SAP Note <a href=\"/notes/2607742\" target=\"_blank\">2607742</a>.</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-IPL</p>\n<p><strong>What to use</strong>: SAP BW/4HANA Starter Add-On, SAP BW/4HANA Transfer Cockpit, SPAM/SAINT<br/><em></em></p>\n<p><em></em><em>*******. Remote conversion</em></p>\n<p>For SAP BW systems on releases from 7.30 to 7.50 running on Any-DB, a remote conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models into the new installation and to perform a remote data transfer.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, install DMIS Add-On, transfer data models, transport custom developments (might need adjustment to work with SAP BW/4HANA)</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-RMT</p>\n<p><strong>What to use</strong>: Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit, DMIS Add-On</p>\n<p><em>*******. Shell conversion</em></p>\n<p>For SAP BW systems on releases from 7.00 to 7.50 running on Any-DB, a Shell conversion can be performed. For that conversion type, a green field installation (new  logical systemname) of SAP BW/4HANA is used. The Transfer Cockpit is able to transport selected data models without data into the new installation.</p>\n<p>Our recommendation for shell conversion is to use a sandbox as the sender system. The latest SPs should also be installed on this system. Depending on the version of the current system, this saves a lot of time and effort. If you still decide to import the hints, you should look at the wiki <a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\" target=\"_blank\" title=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=562729718</a> beforehand and follow the information from the wiki when importing.</p>\n<p><em><strong>Steps</strong>: </em>Install SAP BW/4HANA, transfer data models without data using SAP BW/4HANA Transfer Cockpit, and transport custom developments (might need adjustments to work with SAP BW/4HANA).</p>\n<p><strong>Incident Component</strong>: BW-B4H-CNV-SHL</p>\n<p><strong>What to use: </strong>Software Provisioning Manager (SWPM), SAP BW/4HANA Transfer Cockpit</p>\n<p><strong><em>2.1.3. Landscape transformation</em></strong></p>\n<p>Landscape transformation is for customers who want to consolidate and optimize their complex SAP BW landscape (multiple production systems) into a single SAP BW/4HANA system or who want to carve-out selected data models or flows into a global SAP BW/4HANA system.</p>\n<p><strong>Steps</strong>: Install SAP BW/4HANA, implement consolidated and HANA-optimized data models and flows, followed by data load and quality checks</p>\n<p><strong>What to use</strong>: Selective data transfer, SAP Landscape Transformation (SLT), SAP Data Services, SAP BW/4HANA ETL or SAP HANA EIM<br/><em></em></p>\n<p>The following sections are focusing on the system conversion.</p>\n<p> </p>\n<p><strong>2.2 Availability</strong></p>\n<p><strong><em>2.2.1 Overview</em></strong></p>\n<p>The tools required to perform an In-Place, Remote or Shell conversion are general available now.</p>\n<p><strong><em>2.2.2 Releases</em></strong></p>\n<p>SAP highly recommends to use the latest support package for a release to start a conversion project with. It will massively reduce the effort for notes implementation and we cannot guarantee that all required notes can be imported from other areas.<em> </em>Nevertheless, the Transfer Cockpit was made available via SAP Notes for the following support packages.</p>\n<p><em>******* Pre-Checks (General Available)</em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 25 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 07 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 09 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td>SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td>SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td>SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em>******* In-Place conversion</em></p>\n<p>SAP BW 7.50<strong> minimal </strong>requirement SP 05 ,<strong> recommended </strong>SP 12 and higher.</p>\n<p>An in-place conversion of SAP BW <strong>7.51</strong> or higher to SAP BW/4HANA 1.0 is <strong>not</strong> possible (since SAP BW/4HANA 1.0 is based on SAP Basis <strong>7.50</strong>).</p>\n<p>Starting from Release SAP BW <strong>7.50 SP16</strong> the conversion is only to SAP BW/4HANA <strong>2.0 SP&gt;=04</strong> possible!</p>\n<p>SAP highly recommends to update the systems before starting the conversion process. Support Package Implementation is possible until B4H Mode.</p>\n<p>Other components relevant for SAP BW Systems used for InPlace Conversion</p>\n<ul>\n<li>SAP HANA 1.0 SPS 12 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong>)</li>\n<li>SAP HANA 2.0 SPS 36 and higher (Relevant for Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>)</li>\n<li>SAP Business Content 7.57 SP11 and higher</li>\n<li>SPAM version 72 and higher</li>\n<li>SAP UI 7.52 or lower (Relevant for Conversion to Target Release SAP BW/4HANA <strong>1.0</strong> . SAP BW/4HANA 1.0 does <strong>not</strong> support SAP_UI 7.53. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n<li>SAP UI 7.53 (Possible only by Conversion to Target Release SAP BW/4HANA <strong>2.0</strong>. See SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a>)</li>\n</ul>\n<p> </p>\n<p><em>*******. Remote conversion </em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release</strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong>Exceptional usage with increased manual implementation effort</strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* Shell conversion</em></em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Release </strong></td>\n<td><strong>Recommended Support Package</strong></td>\n<td><strong> <strong>Exceptional usage with increased manual implementation effort</strong></strong></td>\n</tr>\n<tr>\n<td>SAP BW 7.00</td>\n<td>SP 28 and higher</td>\n<td>SP 25</td>\n</tr>\n<tr>\n<td>SAP BW 7.01</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.02</td>\n<td>SP 10 and higher</td>\n<td>SP 07</td>\n</tr>\n<tr>\n<td>SAP BW 7.30</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.31</td>\n<td colspan=\"2\">SP 10 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.40</td>\n<td>SP 12 and higher</td>\n<td>SP 09</td>\n</tr>\n<tr>\n<td>SAP BW 7.50</td>\n<td colspan=\"2\">SP 05 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.51</td>\n<td colspan=\"2\">SP 08 and higher</td>\n</tr>\n<tr>\n<td>SAP BW 7.58</td>\n<td colspan=\"2\">SP 00 and higher</td>\n</tr>\n</tbody>\n</table></div>\n<p><em><em>******* SAP BW/4HANA required release and support package stack</em></em></p>\n<p><em><em></em></em>SAP BW/4HANA General Information see SAP Note <a href=\"/notes/2347382\" target=\"_blank\">2347382</a></p>\n<p>SAP BW/4HANA <strong>1.0</strong></p>\n<p>For In-Place / Shell:<strong> minimal</strong> requirement SP 08, <strong>recommended</strong> - latest released SP!</p>\n<p>For Remote: <strong>minimal</strong> requirement SP 09, <strong>recommended</strong> - latest released SP!</p>\n<p>SAP BW/4HANA <strong>2.0</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 02 (released)</p>\n<p>For more information regading SAP BW/4HANA 2.0 Release Restictions see SAP Note <a href=\"/notes/2733740\" target=\"_blank\">2733740</a>.</p>\n<p>SAP BW/4HANA <strong>2021</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2021 Release Restictions see SAP Note <a href=\"/notes/3100794\" target=\"_blank\">3100794</a>.<a href=\"/notes/2733740\" target=\"_blank\"><br/></a></p>\n<p>SAP BW/4HANA <strong>2023</strong></p>\n<p>For In-Place / Shell / Remote: <strong>minimal</strong> requirement SP 00 (released)</p>\n<p>For more information regading SAP BW/4HANA 2023 Release Restictions see SAP Note <a href=\"/notes/3365187\" target=\"_blank\">3365187</a>.</p>\n<p>For Conversion from SAP_BW 7.50 for HANA to SAP BW/4HANA 2023 (SAP_BASIS 7.58) please check the downloaded Upgrade Export according to SAP Note <a href=\"https://me.sap.com/notes/3454844\" target=\"_blank\">3454844</a>.</p>\n<p><span><em>2.2.2.6 Add-On's</em></span></p>\n<p><span>Please see SAP Note <a href=\"/notes/2189708\" target=\"_blank\">2189708</a> regarding usage of Add-Ons in </span><span>SAP BW/4HANA.</span></p>\n<p> </p>\n<p><em></em><em><strong>2.2.3 Installation</strong></em></p>\n<p><em>2.2.3.1 SAP BW/4HANA Starter Add-On</em></p>\n<p>For the In-Place Conversion, the installation of the SAP BW/4HANA Starter Add-on is required. Please follow the instruction of the Conversion Guide.</p>\n<p>For Shell or Remote Conversion, the SAP BW/4HANA Starter Add-On is not required.</p>\n<p><em>2.2.3.2 SAP BW/4HANA Transfer Cockpit</em></p>\n<p>To install the Transfer Cockpit for a transfer of scenarios (in-place, remote, and shell conversion), a set of SAP Notes needs to be applied to the corresponding systems. Please use the SAP BW Note Analyzer to analyze the system and to install the prerequisites. Create the SAP BW Note Analyzer program in each system for which notes have to be applied. Depending on your use case, the following systems needs to be processed:</p>\n<ul>\n<li><strong>SAP BW</strong> (in-place conversion or sending system for a remote/shell conversion)</li>\n<li><strong>Source systems</strong> connected to SAP BW (in-place and remote/shell conversion)</li>\n<li><strong>SAP BW/4HANA</strong> (post conversoin for in-place; receiving system for a remote/shell conversion)</li>\n</ul>\n<p>Run the SAP BW Note Analyzer in each system with the corresponding XML file.</p>\n<p><em>2.2.3.3 SAP BW Note Analyzer</em></p>\n<p>For a detailed documentation of the SAP BW Note Analyzer, please see <a href=\"https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf\" target=\"_blank\">https://help.sap.com/doc/b235b5bd94664d9986f721f049d72cbb/1.0/en-US/User_Guide_for_SAP_BW_Note_Analyzer.pdf</a></p>\n<p>Attached to that note you’ll find a text file “Z_SAP_BW_NOTE_ANALYZER.txt”. Save the file on your computer and open it in an editor.</p>\n<p>In the corresponding system, use transaction SE38 to create a report called “Z_SAP_BW_NOTE_ANALYZER”. Copy and paste the content of the above-mentioned text file into the report and activate it.</p>\n<p>Attached to that note, you’ll find several XML files. Save those files that are related to your use-case:</p>\n<p>Please find more detailed informations under:</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=572523874</a>.</p>\n<p>You want to run the pre-checks in the discovery phase (RS_B4HANA_CONVERSION_CONTROL in SAP BW 7.0x and RS_B4HANA_RC from SAP BW 7.30 to 7.50):</p>\n<ul>\n<li>For SAP BW 7.0x, use <em>SAP_BW4HANA_<strong>Pre_Checks</strong>_[last_update].xml</em></li>\n<li>For SAP BW from 7.30 to 7.50, use XML<em> SAP_BW4HANA_<strong>Readiness_Check</strong>_<em>[last_update]</em>.xml</em> from note 2575059. </li>\n</ul>\n<p>You want to transfer scenarios and perform a system conversion in the realization phase:</p>\n<ul>\n<li>For SAP BW 7.50 (all cases),</li>\n<ul>\n<li>if you start now with the installation of the tool, use <em>SAP_BW4HANA_Conversion_<strong>SAP_BW_750</strong>_[last_update].xml </em></li>\n<li>if you have already installed most of the notes before and only want the latest updates, continue to use <em>SAP_BW4HANA_<strong>In-place_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>For all systems connected to SAP BW as a data load source system, use <em><strong>Source_System</strong>_for_SAP_BW4HANA_[last_update].xml</em></li>\n<li>Remote Transfer</li>\n<ul>\n<li>For SAP BW from 7.30 to 7.40 acting as sending system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a remote conversion, use <em>SAP_BW4HANA_<strong>Remote_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n<li>Shell Transfer</li>\n<ul>\n<li>For SAP BW from 7.00 to 7.40 acting as sending system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Original_System)</strong>_[last_update].xml</em></li>\n<li>For SAP BW/4HANA acting as receiving system for a shell conversion, use <em>SAP_BW4HANA_<strong>Shell_Conversion_(Target_System)</strong>_[last_update].xml</em></li>\n</ul>\n</ul>\n<p>You have successfully converted the system (In-Place) to SAP BW/4HANA (see section 2.3.2.3 Post-Conversion phase)</p>\n<ul>\n<li>Use <em>SAP_BW4HANA_<strong>In-place_Conversion_(After_System_Conversion)</strong>_[last_update].xml</em></li>\n</ul>\n<p>Execute the SAP BW Note Analyzer in each relevant system. Click on the icon “Load XML file” and chose the system specific XML file.</p>\n<p>Select radio button “Check implementation state against information in XML file” and check checkbox “Download needed SAP Notes”. Select radio button “In background”.</p>\n<p>Run the report. A background process will be started which downloads the required notes.</p>\n<p>When the process finished successfully, execute the report again with a de-selected checkbox “Download needed SAP Notes”.</p>\n<p>A list of notes with its implementation status will be shown. You can now install the missing notes by clicking on the corresponding icon in the list.</p>\n<p><em>2.2.3.4 Transport-Enabled Correction Instructions (TCIs)</em></p>\n<p>When installing the SAP BW/4HANA Transfer Cockpit using the SAP BW Note Analyzer, you will be prompted for SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>, <a href=\"/notes/2371120\" target=\"_blank\">2371120</a>, and <a href=\"/notes/2735300\" target=\"_blank\">2735300</a> to install a TCI.</p>\n<p>For more information about TCI, see SAP Note <a href=\"/notes/2358953\" target=\"_blank\">2358953</a>.<a href=\"/notes/2358953\" target=\"_blank\"><br/></a></p>\n<p><em>******* DMIS Add-On</em></p>\n<p>For a remote conversion, the DMIS Add-On needs to be installed in SAP BW (sending system) and SAP BW/4HANA (receiving system). Follow the instructions in SAP Note <a href=\"/notes/2513088\" target=\"_blank\">2513088</a>.</p>\n<p> </p>\n<p><strong>2.3 Conversion Process</strong></p>\n<p>For a detailed description of the conversion process, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide</a>. In additional we attached a excel file, \"<em><strong>SAP BW4HANA Conversion - Steps to consider.xlsx</strong></em>\", as a kind of check list/project plan.</p>\n<p>List of required authorization objects for conversion, see attachment \"<em><strong>Authorization_Objects_List_Conversion_2383530.xls</strong></em>\".</p>\n<p>Frequencly ask questions are collected in note <a href=\"/notes/2930058\" target=\"_blank\">2930058</a>.</p>\n<p>Each object can only be converted once. However, it can be collected again if it is used in another scenario. For example, an IOBJ that has been converted can be used in different objects such as ADSO, DSO, CUBE. Due to consistency, these IOBJs are also collected several times, but the metadata can only be transferred once.</p>\n<p><strong>2.4 New Features</strong></p>\n<p>Under link, you can find new developed features.</p>\n<p><a href=\"https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910\" target=\"_blank\">https://wiki.scn.sap.com/wiki/pages/viewpage.action?pageId=550043910</a>.</p>\n<p>The wiki is updated regularly.</p>", "noteVersion": 420}]}, {"note": "1940630", "noteTitle": "1940630 - Key Figure with Data Type DATS or TIMS and Aggregation SUM on gives SQL error", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Using a key figure having data type DATS or TIMS with aggregation type SUM leads to incorrect and unusable results.</p>\n<p>The following SQL errors are displayed:</p>\n<ul>\n<li>SAP HANA</li>\n<ul>\n<li>QueryMediator failed executing query, reason: Error executing physical plan: exception 6900</li>\n<li>Attribute engine failed; $function$=checkIfSupported; $message$=SUM Aggregation is not supported. Invalid keyfiguretype</li>\n</ul>\n<li>Sybase ASE: SQL409 \"The SUM OR AVERAGE AGGREGATE operation cannot take a VARCHAR datatype as an argument\"</li>\n<li>DB4: SQL0402 \"SUM use not valid\"</li>\n</ul>\n<p>These errors occur during data load or when executing queries on InfoProviders containing these kind of key figures.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Key Figure DATS TIMS, SUM Aggregation, SUM Aggregation is not supported</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Although SUM aggregation is not a reasonable aggregation type for key figures with data type DATS or TIMS, it can still be selected during key figure modeling. But, when using SUM aggregation, the output during query execution can lead to unexpected results when aggregation is actually used or no filters are set that reduce the result set to one entry.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply this note or one of the mentioned Support Packages below. Regardless of what you are implementing, the following changes are made to the system:</p>\n<ul>\n<li>\n<div>SAP BW 7.40 SP05: Aggregation type SUM is not available for key figures with data type DATS or TIMS</div>\n</li>\n<li>SAP BW 7.31/7.30: Aggregation type SUM Is not available for newly created key figures with data type DATS or TIMS. For already existing key figures with data type DATS or TIMS an error will popup during check of the key figure once the key figure is reactivated. Reactivation of these key figures is not possible until the aggregation type is changed to MIN or MAX</li>\n</ul>\n<p>To solve the issue please apply the Support Package as mentioned below.</p>\n<ul>\n<li><strong>SAP NetWeaver BW 7.30</strong><br/><br/>Import Support Package 11 for SAP NetWeaver BW 7.30 (SAPKW73011) into your BW system. The Support Package will be available as soon as <strong>SAP Note 1878293</strong> with the short text \"SAPBWNews NW7.30 BW ABAP SP11\", which describes this Support Package in more detail, is released for customers.<br/><br/></li>\n<li><strong>SAP NetWeaver BW 7.31 (SAP NW BW 7.3 EnhP 1)</strong><br/><br/>Import Support Package 11 for SAP NetWeaver BW 7.31 (SAPKW73111) into your BW system. The Support Package will be available as soon as <strong>SAP Note 1914639</strong> with the short text \"SAPBWNews NW BW 7.31/7.03 ABAP SP11\", which describes this Support Package in more detail, is released for customers.<br/><br/></li>\n<li><strong>SAP NetWeaver BW 7.40</strong><br/><br/>Import Support Package 6 for SAP NetWeaver BW 7.40 (SAPKW74006) into your BW system. The Support Package will be available as soon as <strong>SAP Note 1920525</strong> with the short text \"SAPBWNews NW BW 7.4 ABAP SP06\", which describes this Support Package in more detail, is released for customers.</li>\n</ul>\n<p>In urgent cases you can use the correction instructions.</p>\n<p>Before you use the correction instructions, make sure that you check <strong>SAP Note 1668882</strong> for transaction SNOTE.</p>\n<p>This SAP Note might already be available before the Support Package is released. In this case, however, the short text still contains the term \"preliminary version\".</p>", "noteVersion": 8}, {"note": "2364958", "noteTitle": "2364958 - InfoObject 0RECORDMODE no longer supported as attribute in BW/4HANA system", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You model a characteristic and use the InfoObject 0RECORDMODE as an attribute. The InfoObject 0RECORDMODE is not supported as an attribute in a BW/4HANA system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this SAP Note. The check of the characteristic terminates with the following error message if the InfoObject 0RECORDMODE is used as an attribute:</p>\n<ul>\n<li>\n<div>\"0RECORDMODE is not allowed as attribute for characteristic &amp;1\" (R7B684)</div>\n</li>\n</ul>\n<p>Alternatively, you can import the following Support Package:</p>\n<p> </p>\n<ul>\n<li><strong><span>SAP BW 7.50</span></strong><br/>Import Support Package 6 for SAP BW 7.50 (SAPK-75006INSAPBW) into your BW system. The Support Package is available once <strong>SAP Note 2346340</strong> (\"SAPBWNews NW BW 7.50 ABAP SP6\"), which describes this SP in more detail, has been released for customers.</li>\n</ul>\n<p> </p>\n<ul>\n<li><span><strong>SAP BW 7.51</strong></span><br/>Import Support Package 1 for SAP BW 7.51 (SAPK-75101INSAPBW) into your BW system. The Support Package is available once <strong>SAP Note 2345927</strong> (\"SAPBWNews NW BW 7.51 ABAP SP1\"), which describes this SP in more detail, has been released for customers.</li>\n</ul>\n<p> </p>\n<ul>\n<li><strong>SAP BW/4HANA 1.0</strong><br/>Import Support Package 1 for SAP BW/4HANA 1.0 (SAPK-10001INDW4CORE) into your BW system. The Support Package is available once <strong>SAP Note 2364946</strong> (\"SAPBWNews SAP BW/4 HANA 1.0 SP01\"), which describes this SP in more detail, has been released for customers.</li>\n</ul>\n<p> </p>\n<p>In urgent cases, you can implement the correction instructions as an advance correction.</p>\n<p><strong>You must first read SAP Notes 1668882 and 2248091, which provide information about transaction SNOTE.</strong></p>\n<p>To provide information in advance, the SAP Note(s) specified above might already be available before the Support Package is released. In this case, however, the short text of the SAP Note still contains the term \"preliminary version\".</p>", "noteVersion": 2}, {"note": "2635167", "noteTitle": "2635167 - Handling of Material InfoObjects with Conversion to SAP BW/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert a SAP BW system (sender/original system) to SAP BW/4HANA (receiver/target system).</p>\n<p>This SAP Note explains how infoObjects with conversion exits MATN1 and MATNB - like 0MATERIAL - will be handled during conversion to SAP BW/4HANA. You can use the attached program ZBW_CONV_EXIT_MATN to find all impacted InfoObjects in your system.</p>\n<p>In the original system, conversion functionality in Material InfoObjects can be used with conversion exits MATN1 or MATNB (above SAP BW 7.4 SP10). These conversion exits use the below tables that can be maintained by the customer.</p>\n<ul>\n<li>TMCNV (transaction OMSL) - Conversion exit MATN1 uses customizing settings maintained in this table. Maximum material length can be 18 using conversion exit MATN1.</li>\n<li>RSTMCNV (Optional) – Conversion exit MATNB uses customizing settings maintained in this table.  Maximum material length can be 40 using conversion exit MATNB for above SAP BW releases. For numeric material, maximum length is 18.</li>\n</ul>\n<p>Starting from SAP BW 7.40 SP18, SAP BW 7.50 SP09, SAP BW 7.51 SP04 (see SAP Note <a href=\"/notes/2469073\" target=\"_blank\">2469073</a> for installation on lower SPs), it is possible to extract data containing material numbers longer than 18 characters into InfoObjects using the conversion exit MATNB (replacement of MATN1 for longer material numbers). Conversion exit MATNB is available as of BI CONT 7.47 SP 21 and 7.57 SP 14.</p>\n<p>In the target system, only one conversion exit MATN1 exists which uses customizing settings maintained in table TMCNV. Conversion exit MATNB and table RSTMCNV are not available in SAP BW/4HANA.</p>\n<p>In SAP BW/4HANA, maximum material length can be 40. With standard customizing (lexicographic flag not set, i.e. leading zeroes are not significant), purely numeric material numbers are still restricted to 18 characters and will be filled up in the database up to only 18 characters by leading zeroes.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>BW/4HANA, DMIS, BW/4 Remote Conversion, BW/4 Migration, <span>BW/4HANA Conversion Cockpit, Conversion to BW/4HANA, 0MATERIAL, 0MAT_PLANT, 0MAT_SALES</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to SAP BW/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Customization Comparison Logic</strong></p>\n<p>Comparison of customization will happen:</p>\n<ul>\n<li>Within the original system between customization maintained in table RSTMCNV and TMCNV only if RSTMCNV customization exists</li>\n<li>Between customization maintained in table TMCNV in target system with one of the customization maintained in the original system in table TMCNV and RSTMCNV, whichever holds higher value of “Length of Material” (valid only for Remote Conversion)</li>\n</ul>\n<p>Logic is as follows:</p>\n<ol>\n<li>Comparison of <strong>Lexicographic indicator</strong>, if it is different, then process will end with an error.</li>\n<li>Comparison of <strong>Length of Material</strong>, among 2 customization settings which ever holds higher length of the material must also have higher value of “Significant” length of the material, if not then the process will end with an error.</li>\n</ol>\n<p><strong>Pre-Check executed in the Transfer Cockpit</strong></p>\n<p>Here, the checks are done within original system. There can be different scenarios based on the TMCNV and RSTMCNV customizations in original system. Scenarios are explained with its output in the Pre-Check report:</p>\n<ul>\n<li>Table RSTMCNV doesn’t exists OR No customization maintained in the table RSTMCNV in the original system.<br/>In this situation, process should end with success.</li>\n<li>Customization maintained in table RSTMCNV in the original system then comparison logic will be executed (mentioned above).</li>\n<ul>\n<ul>\n<li>If there are no difference, then process will end with success</li>\n<li>If there are differences, then process error will end with an error</li>\n</ul>\n</ul>\n</ul>\n<p><strong>Note</strong>: During data migration, if table TMCNV is already customized in target system then customization comparison of the original system will also be done with the target system.</p>\n<p><strong>For In-place Conversion</strong></p>\n<p>In case of In-place Conversion the conversion exit MATNB will be replaced automatically with MATN1 during the change of the operation mode to ‘Ready to Conversion’ (see task “Replace Conversion Exit MATNB with MATN1”).</p>\n<p>The customization in the table RSTMCNV must be manually removed and maintained only in the table TMCNV.</p>\n<p><strong><em>For Remote Conversion</em></strong><strong><br/><br/></strong>During import to the target system,</p>\n<ul>\n<li>the conversion exit of all InfoObjects currently using MATNB is replaced with MATN1.</li>\n<li>the length of all InfoObjects using MATN1 (including those from 1) is set to 40 or to a length greater than 40 if the currently active version is of a length greater than 40.</li>\n</ul>\n<p>There can be different scenarios based on TMCNV and RSTMCNV customizations in original and target system. These scenarios are explained below:</p>\n<ol>\n<li><strong>Scenario 1: </strong><em>Only TMCNV is configured in original system</em></li>\n<ol>\n<li><em>TMCNV is configured in target system</em>, then customization will be compared based on the logic mentioned above.</li>\n<ol>\n<li>If any setting is different and error can’t be removed by the relevant adjustment in the customizations, then please contact SAP consulting for support.<br/><br/><strong>Example: </strong>Lexicographical indicator is set in original system and it is not set in target system. Material will be stored in format as shown below:<br/><br/>Original System:<br/>      Defined length: 18 characters<br/>      Internally/externally assigned number: 12<br/>      Stored number: 123<br/><br/>Target System:<br/>      Defined length: 40 characters<br/>      Internally/externally assigned number: 123<br/>      Stored number: 000000000000000123<br/><br/>This difference in storing the same material, is a problem from data consistency perspective. This scenario needs to be analyzed and approach must be checked for data migration.</li>\n<li>If all settings are same, higher value of material length among original system and target system is used in target system settings.<br/><strong>Example<br/></strong>If material length is 18 in original system and 20 in target system, then length 20 is kept in target system.</li>\n</ol>\n<li>If TMCNV is not configured in target system, then customizing settings maintained in table TMCNV from original system are replicated in target system.</li>\n</ol>\n<li><strong>Scenario 2: </strong>Both TMCNV and RSTMCNV are configured in original system.</li>\n<ol>\n<li>If customization maintained in TMCNV and RSTMCNV in original system is same as per the comparison logic, then</li>\n<ol>\n<li>If customization in table TMCNV is not maintained in the target system then customizing settings from original system will be replicated in target system during data migration. Higher value of Material Length among TMCNV and RSTMCNV is used in target system.<br/><strong>Example:<br/></strong>If material length is 17 in TMCNV and 18 in RSTMCNV in original system, then length 18 is set in target system.</li>\n<li>If customization in table TMCNV is maintained in the target system, then target system customizing settings will be compared with original system and</li>\n<ul>\n<li>If there are no differences found in comparison then higher value of material length among 2 settings will be used in target system.<br/><strong>Example<br/></strong>If material length is 18 in original system and 17 in target system, then length 18 is set in target system.</li>\n<li>If there are differences found and error can’t be removed by the relevant adjustment in the customizations, then please contact SAP consulting for support.<br/><strong>Example<br/></strong>Lexicographical indicator is set in original system and it is not set in target system. Material will be stored in format as shown below: <br/><br/>Original System:<br/>      Defined length: 8 characters<br/>      Internally/externally assigned number: 123<br/>      Stored number: 123<br/><br/>Target System:<br/>      Defined length: 40 characters<br/>      Internally/externally assigned number: 123<br/>      Stored number: 000000000000000123</li>\n</ul>\n</ol>\n<li>If customization maintained in TMCNV and RSTMCNV in original system is different as per the comparison logic and if error can’t be removed by the relevant adjustment in the customizations, then please contact SAP consulting for support.<br/><strong>Example<br/></strong>Lexicographical indicator is not set in TMCNV and it is set in RSTMCNV.<br/><br/>InfoObjects with conversion exit MATN1:<br/>     Defined length: 18 characters<br/>     Internally/externally assigned number: 123<br/>     Stored number: 000000000000000123<br/><br/>InfoObjects with conversion exit MATNB:<br/>     Defined length: 40 characters<br/>     Internally/externally assigned number: 123<br/>     Stored number: 123<br/><br/>This creates a problem of consolidation of material InfoObjects with conversion exit MATN1 and MATNB in target system as the target system can only have one customization TMCNV for InfoObjects with conversion exit MATN1. This scenario needs to be analyzed and approach needs to be finalized for data migration.</li>\n</ol></ol>", "noteVersion": 1, "refer_note": [{"note": "2462639", "noteTitle": "2462639 - BW4SL - Interfaces and Customer-Specific ABAP Development", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Converting from SAP BW to SAP BW/4HANA might have an impact on customer-specific ABAP development. Custom Code Migration might be required.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>$CUST, ABAP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note lists changes to system software that could have an impact on custom code.</p>\n<p>Custom Code Migration describes the tools that help you with the migration of custom code – for example, if you want to migrate your current database to SAP HANA and/or convert your SAP Business Warehouse system to SAP BW/4HANA. In this SAP Note, you will find information about the tools that support you in this process.</p>\n<p>The SAP BW/4HANA Transfer Cockpit provides a Code Scan Tool to check custom code objects as well as code embedded in transformations and many other locations in SAP BW. For more details, see the <a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide for SAP BW/4HANA</a>. A list of DDIC and ABAP objects that are <strong>not </strong>available in SAP BW/4HANA is included in the Code Scan Tool (see class CL_RS_B4HANA_CODE_SCAN method GET_BLACKLIST_WITH_TLOGO).</p>\n<p>Note: SAP Readiness Check for SAP BW/4HANA and the Pre-check Tool of the SAP BW/4HANA Transfer Cockpit do <strong>not </strong>scan or check any custom code.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Impact Due to Change of Database</strong> <br/><br/>When converting to SAP BW/4HANA running on SAP HANA from SAP BW running a different database platform, specific ABAP coding needs to be investigated, revisited, and possibly adapted. Functional adjustments and SQL performance tuning might be required.</p>\n<p><strong>Impact Due to Simplification of Application Functionality</strong></p>\n<p>As documented in other simplification items, SAP BW/4HANA provides many simplifications of application functionality compared to SAP BW. Therefore, many programs, function modules, and classes (millions of lines of standard code) and thousands of standard dictionary objects of SAP BW are not available in SAP BW/4HANA. Any use of such objects needs to be replaced in custom code. It's recommended to use only documented standard interfaces (API). See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.2/en-US/a845dbe7e2704cde85da85411fb7a12d.html\" target=\"_blank\">SAP Documentation</a>.</p>\n<p><strong>Impact Due to Change of Application Technology</strong><br/><br/>SAP BW-specific customer enhancements (often called \"customer exits\", transaction CMOD) are not available in SAP BW/4HANA. For several SAP BW releases, SAP has offered corresponding enhancement spots. If customer enhancements are used in SAP BW, the code will have to be adjusted and converted to enhancement spots in SAP BW/4HANA. <br/><br/>The following customer exists are not available with SAP BW/4HANA:<br/><br/></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><em>Customer Exits</em></td>\n<td><em>Description</em></td>\n<td><em>Enhancement Spots</em></td>\n</tr>\n<tr>\n<td>AIBW0001, AIBW0002</td>\n<td>IM-BCT: Assignment of Actual Values to Budget Categories / Corporate IM: Settings for Group Currency</td>\n<td>n/a</td>\n</tr>\n<tr>\n<td>RSR00001, RSR00002</td>\n<td>BI: Enhancements for Global Variables in Reporting / BI: Virtual Characteristics and Key Figures in Reporting</td>\n<td>RSROA_VARIABLES_EXIT and RSROA, see SAP Note <a href=\"/notes/2458521\" target=\"_blank\">2458521</a> for details on how to switch to the enhancement spot</td>\n</tr>\n<tr>\n<td>SEMBPS01, SEMBPS02</td>\n<td>SEM-BPS: Enhancement for checking characteristic value combinations / Enhancement for characteristic derivation</td>\n<td>Corresponding functionality in Integrated Planning or SAP Business Planning and Consolidation (BPC)</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Impact Due to Simplification of Object Types</strong><br/><br/>Several types of objects are not available in SAP BW/4HANA. Depending on the type of object, active versions of obsolete object types can be converted to objects that are compatible with SAP BW/4HANA. For example, the InfoCubes or classic DataStore objects can be converted to DataStore objects (advanced). If unavailable object types are used in custom code, the code might have to be adjusted. <br/><br/>For example, if custom code contains a lookup of a classic DataStore object (SELECT ... FROM /BIx/ATESTDSO00 ...), the lookup might fail after converting to an advanced DataStore object which has a different database representation (SELECT ... FROM /BIx/ATESTDSO2 ...). After implementing SAP Note 2539205 (or the equivalent support package), the system will generate compatibility views corresponding to the activation queue and the active data table of the classic DSO reducing the effort to adjust custom code significantly. Nevertheless, note that depending on the type of object, there might also be different technical fields, like \"Request TSN\" instead of \"Request ID\" in change logs and there is no compatibility view for the change log itself.<br/><br/>See the other object-specific simplification items, for recommendations on how to deal with custom code embedded in object types unavailable in SAP BW/4HANA (like Virtual InfoCubes).</p>\n<p><strong>Impact Due to Simplification of Content Objects</strong><br/><br/>Several BI Content and SAP BW Technical Content objects are not available in SAP BW/4HANA. Depending on the type of object, active versions of the content can be taken over or converted to objects that are compatible with SAP BW/4HANA (see \"simplification of object types\" above). If content that is used in custom code is not available in SAP BW/4HANA, the code will have to be adjusted.<br/><br/>For example, InfoObject \"Request ID\" (0REQUID) is not used in SAP BW/4HANA. Therefore, any custom code built around \"Request ID\" will not work with SAP BW/4HANA. Instead \"Request Transaction Sequence Number\" (0REQTSN) should be used.</p>\n<p>Another example is reusing logic provided by BI Content like SAP exit variables in custom code (CALL FUNCTION 'RSVAREXIT...'). Since some SAP exit variables are not available, custom code might have to be adjusted.</p>\n<p>For more details, see SAP Note <a href=\"/notes/2673734\" target=\"_blank\">2673734</a>.</p>\n<p><strong>Impact Due to Change of Personalization Objects</strong></p>\n<p>DataStore objects (0PERS_*) for persistency of personalization data are not available in SAP BW/4HANA. Instead the system uses transparent tables (RSPERS*). Custom code using these DataStore objects needs to be adjusted.</p>\n<p><strong>Impact Due to Simplification of Authorization Objects</strong><br/><br/>Several authorization objects, for example S_RS_ICUBE, are not available in SAP BW/4HANA. In case these objects are used for authorization checks in custom code, the code will have to be adjusted. See SAP Note <a href=\"/notes/2468657\" target=\"_blank\">2468657</a>, for details.</p>\n<p><strong>Impact Due to Changes of Application Interfaces</strong><br/><br/></p>\n<p>Usage of standard application interfaces in custom code or external applications needs to be evaluated and in some cases adjusted. The following table contains the most prominent changes that have been made in SAP BW/4HANA:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><em>SAP BW Interface</em></td>\n<td><em>Description</em></td>\n<td><em>SAP BW/4HANA Interface</em></td>\n</tr>\n<tr>\n<td>\n<p>RSNDI_MD_ATTRIBUTES_UPDATE<br/>RSDMD_WRITE_ATTRIBUTES_TEXTS<br/>RSNDI_MD_TEXTS_UPDATE<br/>RSNDI_MD_...</p>\n</td>\n<td>APIs for Master Data</td>\n<td>\n<p>RSDMD_API_ATTRIBUTES_UPDATE<br/>RSDMD_API_ATTR_TEXTS_MAINTAIN<br/>RSDMD_API_DELETE<br/>RSDMD_API_TEXTS_UPDATE<br/>RSDMD_API_XXL_ATTR_UPDATE<br/><br/>See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.5/en-US/4c1a1b8a54914c86e10000000a42189e.html\" target=\"_blank\">SAP Documentation</a> and SAP Note <a href=\"/notes/2362955\" target=\"_blank\">2362955</a> for details</p>\n</td>\n</tr>\n<tr>\n<td>RSNDI_SHIE_ACTIVATE<br/>RSNDI_SHIE_CATALOG_GET<br/>RSNDI_SHIE_DELETE<br/>RSNDI_SHIE_MAINTAIN<br/>RSNDI_SHIE_STRUCTURE_GET3<br/>RSNDI_SHIE_STRUCTURE_UPDATE3<br/>RSNDI_SHIE_SUBTREE_DELETE<br/>RSNDI_SHIE_...</td>\n<td>APIs for Hierarchies</td>\n<td>RSDMD_API_HI_ACTIVATE<br/>RSDMD_API_HI_BW_NODENMCREATE<br/>RSDMD_API_HI_DELETE<br/>RSDMD_API_HI_EX_NODENMCREATE<br/>RSDMD_API_HI_HEADER_GET_ALL<br/>RSDMD_API_HI_NODENAME_CREATE<br/>RSDMD_API_HI_STRUCTURE_GET<br/>RSDMD_API_HI_STRUCTURE_UPDATE<br/>RSDMD_API_HI_SUBTREE_DELETE<br/><br/>See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.5/en-US/4c1a1b8d54914c86e10000000a42189e.html\" target=\"_blank\">SAP Documentation</a> and SAP Note <a href=\"/notes/2362955\" target=\"_blank\">2362955</a> for details</td>\n</tr>\n<tr>\n<td>\n<p>BAPI_ODSO_READ_DATA_UC<br/>RSDRI_ODSO_INSERT<br/>RSDRI_ODSO_INSERT_RFC<br/>RSDRI_ODSO_MODIFY<br/>RSDRI_ODSO_MODIFY_RFC<br/>RSDRI_ODSO_UPDATE<br/>RSDRI_ODSO_UPDATE_RFC<br/>RSDRI_ODSO_DELETE_RFC<br/>RSDRI_ODSO_ARRAY_DELETE<br/>RSDRI_ODSO_ARRAY_DELETE_RFC</p>\n</td>\n<td>APIs for DataStore Objects</td>\n<td>\n<p>RSDSO_WRITE_API<br/>RSDSO_WRITE_API_RFC<br/>RSDSO_ACTIVATE_REQ_API_RFC<br/>RSDSO_DU_WRITE_API<br/>RSDSO_DU_WRITE_API_RFC<br/>RSDSO_DU_DELETE_API_RFC<br/>RSDSO_DU_CLEANUP_API_RFC<br/><br/>See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.5/en-US/72e16c936fb94cffb71ce90edd5f8f8e.html\" target=\"_blank\">SAP Documentation</a></p>\n</td>\n</tr>\n<tr>\n<td>BAPI_...</td>\n<td>APIs for other object types like InfoCube, <br/>MultiProvider, etc. which are not available <br/>in SAP BW/4HANA</td>\n<td>Functions still exist but will raise <br/>an error message when called in <br/>SAP BW/4HANA</td>\n</tr>\n<tr>\n<td>BAPI_REP...<br/>RSCRM...<br/>CL_RSCRM...</td>\n<td>CRM Tools and CRM BAPI are not available <br/>in SAP BW/4HANA</td>\n<td>n/a, see SAP Note <a href=\"/notes/2463800\" target=\"_blank\">2463800</a></td>\n</tr>\n<tr>\n<td>\n<p>RSDRI_DF_...<br/>RSDRI_INFOPROV_READ_DF</p>\n</td>\n<td>Data Federator Facade is not available <br/>in SAP BW/4HANA</td>\n<td>n/a, see SAP Note <a href=\"/notes/2444890\" target=\"_blank\">2444890</a></td>\n</tr>\n<tr>\n<td>\n<p>RSSEM_...<br/>CL_RSSEM...</p>\n</td>\n<td>SEM APIs are not available in <br/>SAP BW/4HANA</td>\n<td>n/a, see SAP Note <a href=\"/notes/2526508\" target=\"_blank\">2526508</a></td>\n</tr>\n<tr>\n<td>\n<p>RRW3_GET_QUERY_VIEW_DATA</p>\n</td>\n<td>Web Service API</td>\n<td>\n<p>Functions still exist but is not <br/>supported with SAP BW/4HANA.</p>\n<p>Related to the use case you have 2 options:</p>\n<p>The functionality can be replaced<br/>by the OData interface for BW<br/>Queries. (external access)</p>\n<p>See <a href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.6/en-US/33d6f5a17a3149c3907089059e484f61.html\" target=\"_blank\">SAP Documentation</a></p>\n<p><strong>or</strong></p>\n<p>starting with SAP BW/4 2021 we have the LBA Lightwaight BICS API availible. This can be used for replacment too. (internal access)</p>\n<p>See</p>\n<p><a href=\"https://community.sap.com/t5/technology-blogs-by-sap/how-to-consume-an-analytical-query-bex-query-in-abap/ba-p/13570280\" target=\"_blank\">https://community.sap.com/t5/technology-blogs-by-sap/how-to-consume-an-analytical-query-bex-query-in-abap/ba-p/13570280</a></p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Impact Due to Enhanced Data Types for Characteristics and Constants</strong><br/><br/>When converting to SAP BW/4HANA from a SAP BW release below 7.4, data types and lengths for characteristics will be changed. Previously, the maximum length of characteristic values was 60 characters. The maximum length is now 250 characters, which corresponds to 500 bytes. Domain RSCHAVL has therefore been changed from CHAR60 to SSTRING.<br/><br/>For details, see <a href=\"https://help.sap.com/saphelp_nw74/helpdata/en/b1/26a42229e04101b7b8140b3c7af966/frameset.htm\" target=\"_blank\">SAP Documentation</a> and SAP Note <a href=\"/notes/1823174\" target=\"_blank\">1823174</a>.</p>\n<p><strong>Impact Due to Change of Front-end Technology</strong><br/><br/>SAP Business Explorer is not available with SAP BW/4HANA. Any custom code build on SAP BEx Web Templates or SAP BEx Workbooks (in 3.x as well as 7.x versions) will not work with SAP BW/4HANA.</p>\n<p>Data Federator Façade is not available in SAP BW/4HANA. Instead, generated SAP HANA views can be used. Easy Query is not available in SAP BW/4HANA. Instead, OData queries can be used. Custom code using either Data Federator Façade or Easy Query needs to be adjusted.</p>\n<p><strong>Impact Due to Changes in Other Software Components</strong></p>\n<p>SAP BW/4HANA 1.0 is delivered with the following software components and version:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Software Component</td>\n<td>Release</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>750</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>75A</td>\n</tr>\n<tr>\n<td>SAP_GWFND</td>\n<td>750</td>\n</tr>\n<tr>\n<td>SAP_UI*</td>\n<td>750 / 752</td>\n</tr>\n<tr>\n<td>DW4CORE</td>\n<td>100</td>\n</tr>\n</tbody>\n</table></div>\n<p>* Starting with SAP BW/4HANA 1.0 SP 8, SAP_UI 7.52 is required.</p>\n<p>SAP BW/4HANA 2.0 is delivered with the following software components and version:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>Software Component</td>\n<td>Release</td>\n</tr>\n<tr>\n<td>SAP_BASIS</td>\n<td>753</td>\n</tr>\n<tr>\n<td>SAP_ABA</td>\n<td>75D</td>\n</tr>\n<tr>\n<td>SAP_GWFND</td>\n<td>753</td>\n</tr>\n<tr>\n<td>SAP_UI</td>\n<td>753</td>\n</tr>\n<tr>\n<td>DW4CORE</td>\n<td>200</td>\n</tr>\n<tr>\n<td>UIBAS001</td>\n<td>400</td>\n</tr>\n</tbody>\n</table></div>\n<p>When converting to SAP BW/4HANA, these components can undergo a release upgrade, which could therefore impact custom code. SAP_ABA release 75A and higher, for example, supports long material numbers (CHAR 40 instead of CHAR 18). Custom code which uses 0MATERIAL - or variations thereof - should be checked for compatibility with long material numbers. See also SAP Note <a href=\"/notes/2635167\" target=\"_blank\">2635167</a> - Handling of Material InfoObjects with Conversion to SAP BW/4HANA.</p>\n<p>See the following SAP Notes for details:</p>\n<p>SAP Note <a href=\"/notes/2215424\" target=\"_blank\">2215424</a> - Material Number Field Length Extension - General Information<br/>SAP Note <a href=\"/notes/2215852\" target=\"_blank\">2215852</a> - Material Number Field Length Extension: Code Adaptions<br/>SAP Note <a href=\"/notes/2267140\" target=\"_blank\">2267140</a> - S4TWL - Material Number Field Length Extension<br/>SAP Note <a href=\"/notes/2272014\" target=\"_blank\">2272014</a> - Code Inspector check for field length extensions</p>\n<p><strong>Impact on System Modifications and Enhancements</strong></p>\n<p>Modifications and enhancements need to be adapted using the standard transactions SPDD, SPAU and SPAU_ENH. This is the same process as in previous upgrades of SAP BW, only the tools SPDD and SPAU have been renewed. Especially when moving from older system to SAP BW/4HANA many modifications and enhancements can be removed or set to SAP standard. For this purpose, the UI was invented for SPAU, which supports mass activities in order to adjust modifications and enhancements or reset objects to SAP standard more easily. The general recommendation is to reset as many objects as possible to SAP standard.</p>\n<p><strong>SAP Custom Development Projects (CDP)</strong></p>\n<p>If the SAP BW system contains any Custom Development Projects - customer-specific implementations done by SAP -, please contact SAP to ensure that the solution is properly evaluated and transitioned to SAP BW/4HANA.</p>\n<p><strong>Partner Development Projects</strong></p>\n<p>If the SAP BW system contains solutions developed by Partners, please contact the original vendor to see if the solution can be transitioned to SAP BW/4HANA.</p>\n<p><strong>Recommended Process for Custom Code Adjustments</strong></p>\n<p>The custom code adjustment process consists of two major phases. <strong>Before SAP BW/4HANA system conversion</strong> – during discovery and preparation phase – we recommend to get rid of old unused custom code (custom code evaluation) and then analyze remaining custom ABAP code (based on the Simplification List) and find out which objects need to be changed to get adapted to the SAP HANA and SAP BW/4HANA. <strong>After SAP BW/4HANA system conversion</strong> – during the realization phase – you need to adapt custom ABAP code to the new SAP BW/4HANA software (functional adaptation) and optimize performance for SAP HANA database (performance tuning).</p>\n<p><strong>Related Tools</strong></p>\n<p>The following table gives you an overview of how to detect potential functional, application, and performance-related issues during transition and of the relevant tools in the context of conversion to SAP BW/4HANA:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p class=\"p\"><em>Use Case</em></p>\n</td>\n<td>\n<p class=\"p\"><em>Description</em></p>\n</td>\n<td>\n<p class=\"p\"><em>Tools</em></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">Custom code evaluation</p>\n</td>\n<td>\n<p class=\"p\">For this purpose, we recommend to turn on the Usage Procedure Log (UPL) in your productive system to find out, which custom ABAP objects are used within your running business processes. You can also use this step for prioritization: to find out which objects are more important as the others.</p>\n<p class=\"p\">An alternative is to use the ABAP Call Monitor (SCMON). The advantage compared to the UPL is that using this tool you not only collect the usage data (how often a specific ABAP object was called), but also the information about the calling context.</p>\n<p class=\"p\">Solution Manager Custom Code Lifecycle Management (CCLM) can retrieve the usage data from your productive system and visualize them for your convenience. Based on the graphical presentation you get better transparency of the usage of your custom ABAP code and can easier analyze it then using only UPL or SCMON technical data.</p>\n</td>\n<td>\n<p class=\"p\"><a href=\"http://www.sap.com/documents/2017/01/a28f29f1-9f7c-0010-82c7-eda71af511fa.html\" target=\"_blank\">Usage and Procedure Logging (UPL)</a></p>\n<p class=\"p\"><a href=\"https://blogs.sap.com/2017/04/06/abap-call-monitor-scmon-analyze-usage-of-your-code/\" target=\"_blank\">ABAP Call Monitor (SCMON)</a></p>\n<p class=\"p\"><a href=\"http://www.sap.com/documents/2017/01/123695c2-a17c-0010-82c7-eda71af511fa.html\" target=\"_blank\">Custom Code Management Decommissioning</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">Functional adaptations</p>\n</td>\n<td>\n<p class=\"p\">The ABAP Test Cockpit (ATC) and the Code Inspector provide static code checks that enable you to detect ABAP code which relies on database-specific features.</p>\n<p class=\"p\">Example: ABAP source code that relies on the sort order of the result of an SQL statement.</p>\n</td>\n<td>\n<p class=\"p\"><a class=\"xref\" href=\"https://help.sap.com/viewer/7bfe8cdcfbb040dcb6702dada8c3e2f0/7.5.7/en-US/2b99ce231e7e45e6a365608d63424336.html\" target=\"_blank\" title=\"\">ABAP Test Cockpit and Code Inspector in Context of HANA Migration</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">SQL performance optimization</p>\n</td>\n<td>\n<p class=\"p\">The SQL Monitor allows you to monitor all SQL statements and operations that are executed by running ABAP applications. The collected SQL Monitor data then enables you to detect performance hot spots.</p>\n</td>\n<td>\n<p class=\"p\"><a class=\"xref\" href=\"https://help.sap.com/viewer/7bfe8cdcfbb040dcb6702dada8c3e2f0/7.5.7/en-US/f1be2e59f44d448180703d6a497ec1e2.html\" target=\"_blank\" title=\"\">SQL Monitor</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">Runtime checks</p>\n</td>\n<td>\n<p class=\"p\">The Runtime Check Monitor allows you to execute a limited range of runtime checks in your productive system. You can use the check results to identify issues that lead to poor performance or high memory consumption.</p>\n</td>\n<td>\n<p class=\"p\"><a class=\"xref\" href=\"https://help.sap.com/viewer/7bfe8cdcfbb040dcb6702dada8c3e2f0/7.5.7/en-US/3e1365542d8041d9ac472f5496114428.html\" target=\"_blank\" title=\"\">Runtime Check Monitor</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p class=\"p\">Functional adaptation and optimization of <br/>custom code embedded in generated programs</p>\n</td>\n<td>\n<p class=\"p\">The SAP BW/4HANA Transfer Cockpit (since the March 2018 update) can be used to scan the system for custom code embedded in SAP BW configuration like update and transfer rules, transformations, planning functionality, analysis process designer, as well as SAP BW-specific customer exits, business add-ins, and enhancement spots. The Transfer Cockpit scans for usage of incompatible BW objects and obsolete ABAP code.</p>\n<p class=\"p\">In addition, you can use the SAP BW ABAP Routine Analyzer, which will perform a similar scan but focus on best practices and performance optimization.</p>\n</td>\n<td>\n<p class=\"p\"><a href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\" target=\"_blank\">Conversion Guide for SAP BW/4HANA &gt; Custom Code Scan</a></p>\n<p class=\"p\"><a href=\"/notes/1847431\" target=\"_blank\">SAP BW ABAP Routine Analyzer</a> (part of <a href=\"/notes/1909597\" target=\"_blank\">SAP BW Migration Cockpit</a>)</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-gb;=\"\" en-us;=\"\" lang=\"EN-GB\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"> </span></p>", "noteVersion": 21}, {"note": "2442621", "noteTitle": "2442621 - BW4SL - InfoObject Catalogs and InfoObjects", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><strong>InfoObject Catalogs (IOBC)</strong></p>\n<p>InfoObject Catalogs are not available in SAP BW/4HANA. InfoObject Catalogs have been replaced by InfoAreas (AREA).</p>\n<p><strong>InfoObjects (IOBJ)</strong></p>\n<p>The following types of InfoObjects are not available in SAP BW/4HANA:</p>\n<ul>\n<li>Characteristics that use 0RECORDMODE as an attribute</li>\n<li>Characteristics that are enabled for real-time-data acquisition</li>\n<li>Characteristics that use class CL_RSR_REMOTE_MASTERDATA as their master data read class</li>\n<li>Key figures of data type \"Date\" or \"Time\" with aggregation SUM</li>\n</ul>\n<p>Also note that extraction out of InfoObjects in SAP BW/4HANA to target systems is no longer possible using Export DataSources but only using ODP-BW. For further information see SAP Note <a href=\"/notes/2483299\" target=\"_blank\">2483299</a> - BW4SL - Export DataSources (SAP BW/4HANA as Source).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IOBC, DIOC, IOBJ, DIOB</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are available in or can be converted to SAP BW/4HANA.</p>\n<p>See node: Automated Clean-up --&gt; Adjustments --&gt; TLOGO IOBC (InfoObject Catalog)</p>\n<p>See note: Manual Redesign (take the necessary action) --&gt; BW Object Type --&gt; TLOGO IOBJ (InfoObject)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>InfoObject Catalogs (IOBC)</strong></p>\n<p>You can migrate the assignments of InfoObjects to InfoObject Catalogs to InfoAreas by using report RSDG_IOBJ_IOBC_MIGRATE_TO_AREA. When running the report the system is set to an \"InfoArea-only\" mode. Please note that once the system has been switched to the \"InfoArea-only\" mode the authorization object S_RS_IOBJA is evaluated instead of S_RS_IOBJ.</p>\n<p><strong>InfoObjects (IOBJ)</strong></p>\n<p>You need to adjust the identified InfoObjects manually.</p>\n<p>Material InfoObjects with conversion exits MATN1 or MATNB are handled in a special way. For details, see SAP Note <a href=\"/notes/2635167\" target=\"_blank\">2635167</a> - Handling of Material InfoObjects with Conversion to SAP BW/4HANA.</p>\n<p>For details about key figures of type DATS or TIMS and SUM aggregation, see SAP Note <a href=\"/notes/1940630\" target=\"_blank\">1940630</a>.</p>\n<p>InfoObjects that are shipped with SAP BW or SAP BW/4HANA (see table RSDIOBJFIX) might require special handling. These InfoObjects are not part of BI or BW4 Content and generally activated at installation time. This includes for example the well-known time characteristics. We recommend to use the latest version of and keep the delivered settings for these InfoObject (adding custom attributes is allowed)  If you are using an out-of-date, the system might not function properly or missing features. This includes for example pre-delivered master data read classes (value help might not work properly without it) and attributes for time characteristics (like number of days per period). If you are doing an in-place conversion, we therefore recommend to reactivate these InfoObjects after conversion to SAP BW/4HANA to ensure you have the latest version. For remote conversion, the latest version will be used when installing SAP BW/4HANA (at the beginning of the project), so adjustments are not required.</p>\n<p><strong>Related Information</strong></p>\n<p>For more information refer to the documentation:</p>\n<ul>\n<li><a href=\"https://help.sap.com/viewer/ab216bfb8b2b452a860267b1e40e270f/7.5.5/en-US/2a06df6c091148ab9d61e48ae711f6a0.html\" target=\"_blank\">InfoAreas</a></li>\n<li><a href=\"https://help.sap.com/viewer/04030263a0d041309a039fa3ea586720/7.5.5/en-US/0469851ddfaa456781a431fc4c88e17b.html\" target=\"_blank\">Authorizations for InfoObjects</a></li>\n<li><a href=\"https://help.sap.com/viewer/04030263a0d041309a039fa3ea586720/7.5.5/en-US/f3722f8bd0e04d3e8f93bd7477c9980d.html\" target=\"_blank\">Creating InfoObjects</a></li>\n</ul>", "noteVersion": 9}]}]}