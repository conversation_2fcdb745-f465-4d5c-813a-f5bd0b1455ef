{"guid": "00109B13199E1EDAB4E637ED186E40FA", "sitemId": "SI6_FIN_AA", "sitemTitle": "S4TWL - Check for duplicate internal asset line items (FAAT_DOC_IT)", "note": 2977180, "noteTitle": "2977180 - S4TWL - Checking for duplicate records in the asset line item table (FAAT_DOC_IT)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Previously, a program error caused duplicate records to be updated in the asset line item table. However, this error does not affect reported asset values in this release or in a previous release.<br/> Nevertheless, due to an incompatible change in SAP NetWeaver, the presence of these records during an upgrade now causes a technical termination that stops the upgrade process as such. The Software Update Manager (SUM) can be used to identify and resolve this situation before the upgrade to SAP S/4HANA 1909, 2020, and so on.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">FAAT_DOC_IT, ﻿CLS4SIC_FI_AA, FAA_DELETE_DUPLICATE_DOC_IT, SI6_FIN_AA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\"> Program error</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For technical details about the cause of the error and how to resolve it, see SAP Note 2948964.</p>", "noteVersion": 2, "refer_note": [{"note": "2948964", "noteTitle": "2948964 - Checks for preparing the upgrade to SAP S/4HANA 1909, 2020, or higher for FI-AA: Error messages and possible solutions", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use Software Update Manager (SUM) to check whether Asset Accounting meets the prerequisites for an upgrade to a target release of <span class=\"SNO_DNT\" translate=\"no\">SAP S/4HANA</span> 1909, 2020, or <strong>higher</strong>. SUM has issued one or more of the error messages listed below.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><span class=\"SNO_DNT\" translate=\"no\">﻿CLS4SIC_FI_AA</span>; <span class=\"SNO_DNT\" translate=\"no\">FAA_DELETE_DUPLICATE_DOC_IT</span>; <span class=\"SNO_DNT\" translate=\"no\">SAP S/4HANA</span>; preliminary check; Asset Accounting; <span class=\"SNO_DNT\" translate=\"no\">FAAT_DOC_IT﻿</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Before you upgrade to a target release of <span class=\"SNO_DNT\" translate=\"no\">SAP S/4HANA</span> 1909, 2020, or <strong>higher</strong>, you must check whether your system meets the prerequisites. You perform this check as follows:</p>\n<ul>\n<li>For a source release of <span class=\"SNO_DNT\" translate=\"no\">SAP S/4HANA 1809</span>: using Software Update Manager (SUM).</li>\n</ul>\n<p>For a <strong>source release of <span class=\"SNO_DNT\" translate=\"no\">SAP S/4HANA 1809﻿</span></strong>, check your system with regard to Asset Accounting as follows:</p>\n<p><strong>1.</strong> Implement the SI Check Framework in your system. The first version of this framework is provided in SAP Note <a href=\"/notes/2503309\" target=\"_blank\">2503309</a>.</p>\n<p><strong>2.</strong> Obligatory, automatic in SUM: Your system is checked in the precheck phase automatically.</p>\n<p>It is also possible to perform the checks manually: To do so, use the program <span class=\"SNO_DNT\" translate=\"no\">/SDF/RC_START_CHECK</span> to perform the checks. The element with the check sub ID <strong><span class=\"SNO_DNT\" translate=\"no\">FI_AA_CHECK_DUPLICATE_DOC_IT﻿</span></strong> provides the preliminary checks for Asset Accounting.<br/>In the detailed check results, each check result has a check sub ID. You can use the check sub ID to find more information below in this SAP Note. A possible cause for each error is explained and—if possible—the procedure to correct the problem is described.</p>\n<p><strong>4. </strong>Obligatory, automatic in SUM: In the realize phase (immediately before the installation of SAP S/4HANA) your system is checked again automatically.</p>\n<p>The following still applies: In the detail check results, each check result has a check sub ID. You can use the check sub ID to find more information below in this SAP Note. A possible cause for each error is explained and—if possible—the procedure to correct the problem is described.</p>\n<p><strong>+++Errors and how to correct them in detail+++</strong></p>\n<p><strong>Preliminary checks during upgrade</strong></p>\n<p><strong>Check sub ID: <span class=\"SNO_DNT\" translate=\"no\">FI_AA_CHECK_DUPLICATE_DOC_IT﻿</span></strong></p>\n<p><strong>From the message overview</strong></p>\n<p><em>Message: <strong>\"<span class=\"SNO_DNT\" translate=\"no\">Duplicate entries found in table FAAT_DOC_IT</span>\"</strong></em></p>\n<p><strong>Message details</strong></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">﻿- Duplicate entries found in table FAAT_DOC_IT</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">- See note: 2948964</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">- Please correct this using following report:</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">- FAA_DELETE_DUPLICATE_DOC_IT</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">- The report consolidates the duplicate entries</span></p>\n<p><span class=\"SNO_DNT\" translate=\"no\">- The balances are kept stable at asset level﻿</span></p>\n<p>Diagnosis</p>\n<p>Duplicate entries in the database table <span class=\"SNO_DNT\" translate=\"no\">FAAT_DOC_IT</span> have been found in the system.</p>\n<p>You must <strong>not</strong> upgrade to the SAP S/4HANA target release.</p>\n<p>Procedure</p>\n<p><strong>Before</strong> the upgrade to the <span class=\"SNO_DNT\" translate=\"no\">SAP S/4HANA</span> target release, you must execute the program <em><strong><span class=\"SNO_DNT\" translate=\"no\">﻿FAA_DELETE_DUPLICATE_DOC_IT</span></strong></em> (<span class=\"SNO_DNT\" translate=\"no\">Correct Duplicate Entries in Table FAAT_DOC_IT</span><em>)</em>.</p>\n<p>If the program outputs the following message in the log</p>\n<ul>\n<li><span class=\"SNO_DNT\" translate=\"no\">﻿Errors Occurred -&gt; Button <em>Message Log</em> (F7)﻿</span></li>\n</ul>\n<p>In this case, create a customer incident under the component <tt>FI-AA-AA-C</tt> with the following title:</p>\n<p><span class=\"SNO_DNT\" translate=\"no\">﻿\"S/4 HANA upgrade Simplification Item Check failed due to duplicate entries in FAAT_DOC_IT\"﻿</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p id=\"\">See above.</p>", "noteVersion": 2}], "activities": [{"Activity": "Data correction", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Correct duplicate entries in table FAAT_DOC_IT according to SAP note 2948964 before the upgrade."}]}