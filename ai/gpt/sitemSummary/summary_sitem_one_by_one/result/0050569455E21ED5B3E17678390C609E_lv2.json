{"guid": "0050569455E21ED5B3E17678390C609E", "sitemId": "SI10: SD_ROLES", "sitemTitle": "S4TWL - Business Role Internal Sales Representative", "note": 2271150, "noteTitle": "2271150 - S4TWL - Business Role Internal Sales Representative", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The Internal Sales Representative (ISR) business role was originally built to support sales representatives who work in close coordination with customers, as well as logistical and financial departments collaborating with sales. This role became available with SAP ERP 6.0 EhP2 and was enhanced in SAP ERP EhP3 and SAP ERP EhP4. Two options to run this role existed, accessible through either the SAP Portal or by using the Business Client (as part of SAP NetWeaver).<br/>The related functionality is assigned to these Business Functions:<br/>LOG_SD_SIMP_02              (EhP4)<br/>LOG_SD_CI_01                 (EhP3)<br/>SD_01                              (EhP2)</p>\n<p>In SAP S/4HANA, the Internal Sales Representative (ISR) business role is not available. This also includes the POWLs (Personal Object Work Lists).</p>\n<p>Instead, a set of new FIORI roles, such as SAP_BR_INTERNAL_SALES_REP, are offered as successors. In the first S/4HANA release these roles offer a basic set of transactions that are typically needed for the day-to-day work of a sales representative. During the subsequent releases, these roles are enhanced with further suitable FIORI apps. For more information about this new concept, see SAP Note 2223838.</p>\n<p><strong>Business Process related information</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"286\">\n<p>Transaction not available in SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"458\">\n<p>VPW1     Change View \"Tasks\"<br/>VPWL     Change View \"Maintain Portal Targets\"</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Adaption to the new FIORI role concept</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Transition Worklist Item is relevant if the customer has activated one of the following Business Functions:<br/>LOG_SD_SIMP_02              <br/>LOG_SD_CI_01                 <br/>SD_01</p>\n<p> </p>", "noteVersion": 5, "refer_note": [{"note": "2223838", "noteTitle": "2223838 - S/4HANA: SD - Business Role Internal Sales Representative", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>You are using a SAP ERP system and intend to perform the upgrade-like conversion to S/4HANA.</p>\n<p>The custom code check shows customer objects affected by simplifications.</p>\n<p>The custom code check refers to note 2223838.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4_TRANSFORMATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The functionality 'Business Role Internal Sales Representative' in SD is not supported in S/4HANA.</p>\n<p>SAP objects used by the customer objects are deprecated and shall not be used any more.</p>\n<p>Custom code does not comply with the scope and data structure of SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>All usages of SAP objects in customer objects for which the custom code check refers to this note 2223838 will no longer work, and must be removed.</p>", "noteVersion": 1}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": ""}]}