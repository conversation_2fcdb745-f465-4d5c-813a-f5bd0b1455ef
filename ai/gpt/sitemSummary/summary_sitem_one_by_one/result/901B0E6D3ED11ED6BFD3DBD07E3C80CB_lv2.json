{"guid": "901B0E6D3ED11ED6BFD3DBD07E3C80CB", "sitemId": "SI48: Logistics_General", "sitemTitle": "S4TWL - Retail Store Fiori Apps - Transfer Products/Order Products (F&R enhanc.)", "note": 2436143, "noteTitle": "2436143 - S4TWL - Retail Store Fiori App - Transfer Products", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1610, and you are using the Retail Store Fiori App for Transfer Products (F2449). The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The retail store Fiori App Transfer Products allows to create a product transfer.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The retail store Fiori app Transfer Product is provided starting with SAP S/4HANA 1709.</p>\n<p>In addition this functionality has been downported to Enhancement Package 8 Support Package Stack 6 of SAP ERP 6.0.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>This note and the related checks shall inform you about this fact and in case you are actively using this functionality on SAP ERP 6.0 prevent a system conversion to older SAP S/4HANA releases in order to prevent a loss of features or data.</p>\n<p>In this case a system conversion is only possible to the SAP S/4HANA release which contains this functionality (or higher).</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>Transfer Products: This note is only relevant if there are entries in table RTST_TP_TRANSFER. You can check this via transaction SE16.</p>", "noteVersion": 2, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Conditional", "Additional_Information": "The activity is relevant if the customer is  actively using the  retail store Fiori App Transfer Products and  is planning a Sytem conversion to SAP S4/HANA 1610. Business must choose a higher version where the functionality is available as the pre conversion checks will prevent a system conversion to older SAP S/4HANA releases in order to prevent a loss of features or data."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": ""}]}