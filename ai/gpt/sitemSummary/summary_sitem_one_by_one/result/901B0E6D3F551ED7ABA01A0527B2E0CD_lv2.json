{"guid": "901B0E6D3F551ED7ABA01A0527B2E0CD", "sitemId": "SI12: CM_LOGISTICS_AGRI_POS", "sitemTitle": "S4TWL - Position Reports delivered by Agricultural Contract Management", "note": 2556134, "noteTitle": "2556134 - S4TWL - CM: Agri-Specific Commodity Position Queries", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA transition worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ACM, agricultural industry, commodity position reporting, CDS views</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With SAP S/4HANA, on-premise edition 1709, the database table and available infosources for risk data required for position reporting got unified and revised.</p>\n<p>New Core Data Services (CDS) views are provided to determine keyfigures for position reporting.</p>\n<p>For keyfigures, which are specific for Futures/Basis pricing - as common in the Agricultural industry - CDS (interface) views are available.</p>\n<p>However, queries on top of these are not provided with the software layer of Commodity Management.</p>\n<p>Instead, these are provided in the Agicultural Contract Managament (ACM) software layer.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Agri-specific Queries include</p>\n<p>- Futures Risk / Slate report with a keyfigure layout tailored to Future / Basis pricing</p>\n<p>- Basis / Premium report</p>\n<p>- Price Type report</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>If you require Agri-specific queries, check the availability of Agicultural Contract Managament (ACM) in your system.</p>\n<p>If ACM is not available you may need to create queries on top of the available (Commodity Management) interface views during system implementation.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Define business requirements for position reporting and decide if SAP Agricultural Contract Managament (ACM) shall be used, if not available yet"}, {"Activity": "Implementation project required", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If you want to use SAP ACM, but it is not yet available in the system include the software in your target software stack and implement functionality"}, {"Activity": "New developments", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If you do not want to use SAP ACM, consider creating additional queries on top of the available (Commodity Management) interface views"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Remove or replace any references to depricated infosources in your custom code"}]}