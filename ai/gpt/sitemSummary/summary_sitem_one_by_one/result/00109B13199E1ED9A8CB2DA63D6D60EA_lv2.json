{"guid": "00109B13199E1ED9A8CB2DA63D6D60EA", "sitemId": "SI01: Insurance_FS-SR", "sitemTitle": "S4TWL - Technical changes to Statutory Reporting Tables", "note": 2810717, "noteTitle": "2810717 - S4TWL - FSSR: Correction index 1 (ISSRFLDEO and ISSRFLDEC)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You execute a system conversion in SAP S/4HANA, On Premise edition. The following SAP S/4HANA transition worklist entry is relevant in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Worklist</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>A change of Index-1 for ISSRFLDEO and ISSRFLDEC to unique is required.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<div class=\"longtext\">\n<p><strong>Description</strong></p>\n<p>For the tables mentioned above, there is an index that is not unique. This may lead to duplicate entries that cause terminations in the application.</p>\n<p><strong>Required and recommended action(s)</strong></p>\n<p>Proceed as described in SAP Note 2771381.</p>\n</div>", "noteVersion": 1, "refer_note": [{"note": "2771381", "noteTitle": "2771381 - FSSR: Technical correction index 1 required before the upgrade (ISSRFLDEO and ISSRFLDEC)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The ledger tables in FSSR have two object tables (ISSRFLDEO and ISSRFLDEC). The index 1 is not set to unique for both object tables. To exclude possible database errors such as duplicate entries, you must set this index to unique.</p>\n<p>Duplicate entries in these tables lead to a <strong>termination during an upgrade</strong>. Therefore, you must clean these up first (see below under \"Solution\").</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ISSR, FISL, ledger, object tables, totals table, DUPREC, upgrade, S/4Hana</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Incorrect index</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>1) The correction report ZZ_SL_CORRECT_OBJECTNUMBERS is attached to this SAP Note. Refer to the information at the beginning of the correction report.</p>\n<p>2) Implement, activate, and execute this correction report ZZ_SL_CORRECT_OBJECTNUMBERS in the affected systems and for the defined ledger in FSSR.</p>\n<p>As a result, the information \"Unique index of the object tables can be activated\" should be displayed.</p>\n<p>3) Call transaction SE11 and set the \"Unique\" indicator for index 1 of the tables ISSRFLDEO and ISSRFLDEC. Make sure that no database system is excluded.</p>\n<p>4) Note that after an upgrade, this index must be set to \"Unique\" again.</p>", "noteVersion": 3}], "activities": [{"Activity": "Technical System Configuration", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Run correction report from note 2771381 and set index 1 of tables ISSRFLDEO and ISSRFLDEC to unique"}]}