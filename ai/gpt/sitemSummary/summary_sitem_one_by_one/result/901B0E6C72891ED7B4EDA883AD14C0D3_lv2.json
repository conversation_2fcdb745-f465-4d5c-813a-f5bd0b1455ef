{"guid": "901B0E6C72891ED7B4EDA883AD14C0D3", "sitemId": "SI21: BUSINESS_USER", "sitemTitle": "S4TWL - Business User Management", "note": 2570961, "noteTitle": "2570961 - Simplification item S4TWL - Business User Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You need information about Business User Management in SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Business User Management in SAP S/4HANA is mandatory and is automatically activated during the conversion from SAP Business Suite to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li><a href=\"#what_has_changed_in_business_user_management\" target=\"_self\">What Has Changed in Business User Management in SAP S/4HANA?</a></li>\n<li><a href=\"#what_is_the_difference_between_business_users_and_su01_users\" target=\"_self\">What is the difference between business users and SU01 users?</a></li>\n<li><a href=\"#what_is_the_business_impact_of_that_change\" target=\"_self\">What is the business impact of that change?</a></li>\n<li><a href=\"#limitations\" target=\"_self\">Limitations</a></li>\n<ul>\n<li><a href=\"#limitations_user_management\" target=\"_self\">User Management</a></li>\n<li><a href=\"#limitations_business_partner\" target=\"_self\">Business Partner</a></li>\n<li><a href=\"#limitations_human_capital_management\" target=\"_self\">Human Capital Management (HCM)</a></li>\n</ul>\n<li><a href=\"#hcm_integration_active_or_inactive_what_does_it_mean\" target=\"_self\">﻿HCM Integration active or inactive: What does it mean?</a></li>\n<ul>\n<li><a href=\"#hcm_integration_switch_system_table_t77s0\" target=\"_self\">HCM Integration Switch (System Table T77S0)</a></li>\n<li><a href=\"#external_resources_switch_system_table_t77s0\" target=\"_self\">External Resources Switch (System Table T77S0)</a></li>\n</ul>\n<li><a href=\"#how_to_create_business_users_in_sap_s4hana\" target=\"_self\">﻿How to create Business Users in SAP S/4HANA</a></li>\n<ul>\n<li><a href=\"#how_to_create_business_users_hcm_integration_is_active\" target=\"_self\">HCM Integration is Active</a></li>\n<li><a href=\"#how_to_create_business_users_hcm_integration_is_inactive\" target=\"_self\">HCM Integration is Inactive</a></li>\n</ul>\n<li><a href=\"#maintenance_of_the_business_user_workplace_address\" target=\"_self\">Maintenance of the Business User Workplace Address</a></li>\n<li><a href=\"#conversion\" target=\"_self\">Conversion</a></li>\n<li><a href=\"#how_to_determine_relevance\" target=\"_self\">How to Determine Relevance</a> </li>\n</ul>\n<p> </p>\n<p><strong><a name=\"what_has_changed_in_business_user_management\" target=\"_blank\"></a>﻿What Has Changed in Business User Management in SAP S/4HANA?</strong></p>\n<p>SAP S/4HANA is introducing a new identity model for business users, which is based on the \"principle of one\". A business user is defined as a natural person who is represented by a business partner and a link to a user in the system. Business users interact with the software in the context of a business process, for example, in the role of a purchaser, a sales representative, or a production planner.</p>\n<p>In SAP S/4HANA, the business partner is the natural person who maintains the user data for the person, work center, and communication-related information. For logon, changing the user ID (BNAME) is no longer supported. However, one person can still log on with a user alias or email. The user alias or email can be changed (see SAP Note <a href=\"/notes/2883989\" target=\"_blank\">2883989</a>). The identity model for business users ensures correct auditing, workflows, and jobs. It also avoids errors in documents after a change of the user ID (BNAME). Auditors require a consistent history of all documents that contains the user ID (BNAME).</p>\n<p>SAP S/4HANA Business User Management enables and supports the entire life cycle maintenance of business users such as organizational changes, change of employment, or retirement. A user in SAP S/4HANA has a one-to-one relationship with a corresponding business partner (natural person). This reduces redundant maintenance and prevents outdated information.</p>\n<p> </p>\n<p><strong><a name=\"what_is_the_difference_between_business_users_and_su01_users\" target=\"_blank\"></a>﻿What is the difference between business users and SU01 users?</strong></p>\n<p>The business user is a SU01 user, but also has a one-to-one relation to the corresponding business partner. This relationship is time independent and cannot be changed anymore. For more details about important user related constraints see SAP Note <a href=\"/notes/2571544\" target=\"_blank\">2571544</a>.</p>\n<p>The business user concept is used in many new applications in SAP S/4HANA. SU01 users with Classic Address (Identity Address Type 00 - User's Old Type 3 Address) lead to limitations because the new business user model is a prerequisite for many business applications. As soon as Fiori apps are activated and used, business users are mandatory (for example Teams, CreditAnalyst in Credit Management or Situations).</p>\n<p>The business partner contains the personal information, for example private address, workplace address, bank details, vendor and customer related data. The business partner and SU01 user share personal details and workplace address related data. The advantage of the new business user model is that the entire lifecycle of that person works without redundant maintenance of user address data. Business users can still be managed using transaction SU01, Central User Administration or identity management systems.</p>\n<p>In the SAP Business Suite, you can use transaction BP (Business Partner Maintenance) to assign users to business partners. In SAP S/4HANA this is not possible anymore to avoid an inconsistent data model for business users. Existing business partners cannot be converted to business users, because of Data Protection and Privacy (DPP). Already existing business partners could have been part of a distribution scenario.</p>\n<p> </p>\n<p><strong><a name=\"what_is_the_business_impact_of_that_change\" target=\"_blank\"></a>﻿What is the business impact of that change?</strong></p>\n<p>With the new business user model in SAP S/4HANA we have a clear maintenance ownership. It can be owned by Human Capital Management (HCM), Business Partner (BP) or User Management. The ownership of HCM is only relevant when HCM integration is active. (More details about HCM integration see section <a href=\"#hcm_integration_active_or_inactive_what_does_it_mean\" target=\"_self\">HCM Integration active or inactive: What does it mean?</a>)</p>\n<p>Using the example of SU01 the following data categories exist:</p>\n<ol>\n<li><strong>Person:</strong> The personal data for the business user is derived from the corresponding business partner. In case HCM integration is active this data is mapped from the corresponding employee of the HCM system (SAP SuccessFactors or SAP HCM).</li>\n<li><strong>Work Center:</strong> The work center data for the business user is derived from the workplace address of the corresponding business partner. In case HCM integration is active, the function and department fields are mapped by default from the corresponding employee of the HCM system (for customizing options, see SAP Note <a href=\"/notes/2548303\" target=\"_blank\">2548303</a>).</li>\n<li><strong>Communication:</strong> The communication data for the business user is derived from the workplace address of the corresponding business partner (for customizing options, see SAP Note <a href=\"/notes/2548303\" target=\"_blank\">2548303</a>).</li>\n<li><strong>Company:</strong> During the conversion of a SU01 user from SAP Business Suite (classic user) to a business user in SAP S/4HANA the company address is copied to the business partner workplace address.</li>\n</ol>\n<p> </p>\n<p><strong><a name=\"limitations\" target=\"_blank\"></a>﻿<a name=\"limitations_user_management\" target=\"_blank\"></a>﻿Limitations - User Management</strong></p>\n<p>Due to the new business user model the database view USER_ADDR can no longer be used in SAP S/4HANA. This view can only display the users with classic address but not the business users. It is not possible to enhance this view. Instead of the database view USER_ADDR use the search help USER_ADDR (which is equivalent to an official API for searching users). In exceptional cases, you can use the SAP internal CDS view P_USER_ADDR, but have in mind that it can be changed by SAP at any time. With the ABAP statement \"with privileged access\" the authorization check is ignored. As a reference, use the coding of the search help exit F4IF_USER_ADDR_SUID_EXIT.</p>\n<p> </p>\n<p><strong><a name=\"limitations_business_partner\" target=\"_blank\"></a>﻿Limitations - Business Partner</strong></p>\n<p>In general, business partners that are neither employee nor contingent workforce cannot be managed as business users.</p>\n<p>In transaction BP (Business Partner Maintenance) you can no longer assign SU01 users to business partners in SAP S/4HANA. This feature has been removed.</p>\n<p>You can no longer maintain the following fields in transaction BP (Business Partner Maintenance):</p>\n<ul>\n<li>Personnel Number (BUS000EMPL-EMPLOYEE_ID)</li>\n<li>User (BUS000EMPL-USERNAME), for example, shown for role category WFM001 (Resource)</li>\n<li>Internet User (BUS000EMPL-INTERNETUSER), for example, shown for role category BUP005 (Internet User)</li>\n</ul>\n<p>You can no longer assign the roles belonging to the following role categories in transaction BP:</p>\n<ul>\n<li>Employee (BUP003)</li>\n<li>Freelancer (BBP010) → Contingent worker</li>\n<li>External Resource / Service Performer (BBP005) →  Contingent worker with additional supplier relation (staffed from external supplier)</li>\n<li>Collaboration Business User (BUP012)</li>\n<li>Resource (WFM001) → Only needed for SAP Portfolio and Project Management (component PPM-PFM-RES).</li>\n</ul>\n<p>This means you have to create the business partners in one of the mentioned role categories. (New Procedures see section <a href=\"#how_to_create_business_users_in_sap_s4hana\" target=\"_self\">How to create Business Users in SAP S/4HANA</a>)</p>\n<p>Existing business partners cannot be expanded by the following role categories later on: Employee, Freelancer, Service Performer. The reason is business partners with the referenced roles have special behavior regarding:</p>\n<ul>\n<li>Authorization management (driven by GDPR/DPP requirements)</li>\n<li>Replication/distribution (no replication from SAP S/4HANA to any system)</li>\n</ul>\n<p>For business partners which are assigned to one of these role categories, the fields of the following field groups cannot be maintained via transaction BP or the customer/supplier master Fiori UIs (independent from whether HCM integration is active or inactive):</p>\n<ul>\n<li>Field group 1: Partner Number (+ Role for Change Doc. Display)</li>\n<li>Field group 8: Form of Address</li>\n<li>Field group 9: Bank Details (for bank detail’s ID HC*)</li>\n<li>Field group 25: Person: First Name</li>\n<li>Field group 26: Person: Academic Title</li>\n<li>Field group 29: Person: Correspondence Language</li>\n<li>Field group 30: Person: Complete Name Line</li>\n<li>Field group 33: Person: Gender</li>\n<li>Field group 38: Person: Birth Name</li>\n<li>Field group 41: Person: Middle Name</li>\n<li>Field group 43: Person: Initials of Person</li>\n<li>Field group 54: Person: Last Name</li>\n<li>Field group 61: Address: Address Usage (HCM001, HCM002). Note that the address data can be edited in transaction BP but will be overwritten by the periodically scheduled sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL.</li>\n</ul>\n<p>Workplace address (table MOM052) attributes such as phone number or email address are not displayed in transaction BP. There are no plans to enhance transaction BP as of now. Please use transaction SU01 to display the workplace address attributes.</p>\n<p>Relationships of relationship category BUR025 (Service Provider) cannot be maintained for roles of role category BBP005 (Service Performer).</p>\n<p>Business partners which are assigned to one of these role categories cannot be maintained using SAP Master Data Governance for business partners, customers or suppliers.</p>\n<p>The usage of such workforce business partners in SAP Master Data Governance is also restricted:</p>\n<ul>\n<li>In partner function maintenance: Only the ID of the workforce business partners is visible, and navigation to that business partner is not possible.</li>\n<li>In business partner relationships: Relationships to workforce business partners are not visible and thus can’t be maintained.</li>\n</ul>\n<p> </p>\n<p><strong><a name=\"limitations_human_capital_management\" target=\"_blank\"></a>﻿Limitations - Human Capital Management (HCM)</strong></p>\n<p>In case of multiple employments (concurrent / global employment / country transfer) only the main employment is allowed to have a user assigned. Additional user assignments are not allowed.</p>\n<p> </p>\n<p><strong><a name=\"hcm_integration_active_or_inactive_what_does_it_mean\" target=\"_blank\"></a>﻿HCM Integration active or inactive: What does it mean?</strong></p>\n<p>HCM integration active means that you rely on the HR mini master (HR infotype based PERNR data model including the PA-Tables). This HR mini master can be locally maintained (for example via transaction PA30 or PA40) or via real integration scenarios with SAP SuccessFactors Employee Central or an external (third party) HCM system.</p>\n<p>If you don't need the HR mini master you should deactivate HCM integration.</p>\n<p>Based on the HCM integration, business users are handled the following way:</p>\n<ul>\n<li>If the HCM integration is active, it is mandatory to schedule the sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL regularly as a background job so that the business users are automatically synchronized with the PA-Tables.<br/>You can maintain business users via</li>\n<ul>\n<li>Transaction PA30 or PA40</li>\n<li>HCM integration.</li>\n</ul>\n<li>If the HCM integration is inactive, you can maintain business users via</li>\n<ul>\n<li>Fiori app Maintain Employees</li>\n<li>Fiori app Maintain External Resources</li>\n<li>SOAP Service ManageBusinessUserIn</li>\n<li>Report RFS4BPU_IMPORT_BUSINESS_USER.</li>\n</ul>\n</ul>\n<p>An example of why we need the HCM integration:</p>\n<p>For using the standard SD partner functions for employees, it is necessary to maintain the employees with the features of the HCM compatibility pack in SAP S/4HANA.<br/>It is not sufficient to maintain persons without active HCM integration (see SAP Note <a href=\"/notes/2763781\" target=\"_blank\">2763781</a>).</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td colspan=\"5\"><strong><a name=\"hcm_integration_switch_system_table_t77s0\" target=\"_blank\"></a>﻿HCM Integration Switch (System Table T77S0)</strong></td>\n</tr>\n<tr>\n<td>Description</td>\n<td>Group (GRPID)</td>\n<td>Sem. abbr. (SEMID)</td>\n<td><strong>HCM Integration inactive</strong></td>\n<td><strong>HCM Integration active</strong></td>\n</tr>\n<tr>\n<td>Activate HCM Integration</td>\n<td>HRALX</td>\n<td>HRAC</td>\n<td><strong>SPACE</strong> (Blank)</td>\n<td><strong>X</strong></td>\n</tr>\n<tr>\n<td>Integration P-BP Activated</td>\n<td>HRALX</td>\n<td>PBPON</td>\n<td><strong>OFF</strong></td>\n<td><strong>ON</strong></td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><a name=\"external_resources_switch_system_table_t77s0\" target=\"_blank\"></a>﻿HCM Integration for External Resources (BBP005) is set by the External Resources Switch. It interacts with the HCM Integration Switch as follows:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td colspan=\"7\"><strong>External Resources Switch (System Table T77S0)</strong></td>\n</tr>\n<tr>\n<td>Description</td>\n<td>Group (GRPID)</td>\n<td>Sem. abbr. (SEMID)</td>\n<td><strong>HCM Integration inactive</strong></td>\n<td><strong><strong>HCM Integration i</strong>nactive</strong></td>\n<td><strong><strong><strong>HCM Integration i</strong>nactive</strong></strong></td>\n<td><strong><strong><strong><strong>HCM Integration a</strong>ctive</strong></strong></strong></td>\n</tr>\n<tr>\n<td>Activate External Resources Switch</td>\n<td>HRALX</td>\n<td>PEXON</td>\n<td><strong><strong>OFF</strong></strong></td>\n<td><strong><strong>OFF</strong></strong></td>\n<td><strong><strong><strong>ON</strong></strong></strong></td>\n<td><strong><strong><strong><strong>ON</strong></strong></strong></strong></td>\n</tr>\n<tr>\n<td>Activate HCM Integration</td>\n<td>HRALX</td>\n<td>HRAC</td>\n<td><strong><strong><strong>SPACE</strong></strong></strong> (Blank)</td>\n<td><strong><strong><strong>X</strong></strong></strong></td>\n<td><strong><strong>SPACE</strong></strong> (Blank)</td>\n<td><strong>X</strong></td>\n</tr>\n<tr>\n<td>Integration P-BP Activated</td>\n<td>HRALX</td>\n<td>PBPON</td>\n<td><strong>OFF</strong></td>\n<td><strong>ON</strong></td>\n<td><strong>OFF</strong></td>\n<td><strong>ON</strong></td>\n</tr>\n</tbody>\n</table></div>\n<p><strong> </strong></p>\n<p><strong><a name=\"how_to_create_business_users_in_sap_s4hana\" target=\"_blank\"></a>﻿﻿﻿﻿<a name=\"how_to_create_business_users_hcm_integration_is_active\" target=\"_blank\"></a>﻿How to create Business Users in SAP S/4HANA - HCM Integration is Active</strong></p>\n<p>The HR mini master is the origin of business users (see HCM simplification SAP Note <a href=\"/notes/2340095\" target=\"_blank\">2340095</a>). The user assignment in the HR mini master has to be maintained in infotype 105 subtype 0001.</p>\n<p>The creation of business users is managed by the periodically scheduled sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL. This report performs the following steps:</p>\n<ol>\n<li>Creation of business partner in one role of the role categories Employee (BUP003), Freelancer (BBP010) or External Resource / Service Performer (BBP005) with</li>\n<ul>\n<li>private address</li>\n<li>bank details</li>\n<li>workplace address</li>\n<li>customer/vendor related data (see <a href=\"https://help.sap.com/doc/saphelp_crm60/********/en-US/24/6d62c210f841f1af3cb85070b67338/content.htm\" target=\"_blank\">Customer Vendor Integration [CVI]</a>)</li>\n<li>relationships</li>\n</ul>\n<li>Assignment of SU01 user. If the user doesn't exist, then the assignment takes place once the SU01 user is created.</li>\n</ol>\n<p>Errors of the sync report are stored in the application log. The application log can be accessed via transaction SLG1 (or SLGD) for object SHCM_EE_INTEGRATION, Subobject BUPA_SYNC (filter via External ID field to Personnel Number PERNR).</p>\n<p>Exception:</p>\n<p>The creation of Collaboration Business User (BUP012) and Resource (WFM001) does not work with sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL. They can only be created by using the following Fiori apps:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Fiori ID</strong></td>\n<td><strong>App Title</strong></td>\n<td><strong>Business Partner Role Category</strong></td>\n<td><strong>As Of Release</strong></td>\n<td><strong>Comments</strong></td>\n</tr>\n<tr>\n<td>F4911</td>\n<td>Maintain Collaboration Users</td>\n<td>BUP012</td>\n<td>2020 FPS01</td>\n<td> </td>\n</tr>\n<tr>\n<td>F3505</td>\n<td>Maintain Resources</td>\n<td>WFM001</td>\n<td>1809 FPS01</td>\n<td>Only needed for SAP Portfolio and Project Management (component PPM-PFM-RES).</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong> </strong></p>\n<p><strong><a name=\"how_to_create_business_users_hcm_integration_is_inactive\" target=\"_blank\"></a>﻿How to create Business Users in SAP S/4HANA - HCM Integration is Inactive</strong></p>\n<p>There are three options to create business users:</p>\n<ul>\n<li>Business users can be created in SAP S/4HANA by using the following Fiori apps (including CSV import for mass processing):</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Fiori ID</strong></td>\n<td><strong>App Title</strong></td>\n<td><strong>Business Partner Role Category</strong></td>\n<td><strong>As Of Release</strong></td>\n<td><strong>Comments</strong></td>\n</tr>\n<tr>\n<td>F2288A</td>\n<td>Maintain Employees</td>\n<td>BUP003</td>\n<td>2020 FPS01</td>\n<td> </td>\n</tr>\n<tr>\n<td>F2288</td>\n<td>Maintain Employees</td>\n<td>BUP003</td>\n<td>1709 FPS02</td>\n<td>Replaced by Fiori app F2288A.</td>\n</tr>\n<tr>\n<td>F4911</td>\n<td>Maintain Collaboration Users</td>\n<td>BUP012</td>\n<td>2020 FPS01</td>\n<td> </td>\n</tr>\n<tr>\n<td>F3505A</td>\n<td>Maintain External Resources</td>\n<td>BBP005</td>\n<td>2020 FPS01</td>\n<td> </td>\n</tr>\n<tr>\n<td>F3505</td>\n<td>Maintain Resources</td>\n<td>WFM001</td>\n<td>1809 FPS01</td>\n<td>Only needed for SAP Portfolio and Project Management (component PPM-PFM-RES).</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>For mass maintenance, you can also use the report RFS4BPU_IMPORT_BUSINESS_USER (As of SAP S/4HANA 1809). For SAP S/4HANA 1610 you can use report RFS4BPU_EMPLOYEE_UPLOAD - see SAP Note <a href=\"/notes/2567604\" target=\"_blank\">2567604</a>.</li>\n<li>SOAP Service ManageBusinessUserIn - can be set up via SOA Manager (Transaction SOAMANAGER).</li>\n</ul>\n<p> </p>\n<p><strong><a name=\"maintenance_of_the_business_user_workplace_address\" target=\"_blank\"></a>﻿Maintenance of the Business User Workplace Address</strong></p>\n<p>There are three possible application areas to maintain the workplace address of the business user:</p>\n<ul>\n<li>Human Resources (HR)</li>\n<li>Business Partner Management (BP)</li>\n<li>User Management (US)</li>\n</ul>\n<p>SAP S/4HANA 1709 FPS02 provides a configuration view that defines the maintenance source of the workplace address attributes. SAP delivers a default proposal for both variants, HCM integration active/inactive. See SAP Note <a href=\"/notes/2548303\" target=\"_blank\">2548303</a> for details. This configuration ensures that updates to the workplace address are restricted to one dedicated application area.</p>\n<p>The configuration view TBZ_V_EEWA_SRC can be set via Transaction SPRO (SAP Reference IMG) Under 'SAP Business Partner' at node 'Additional customizing for SAP Business Partner' (see SAP Note <a href=\"/notes/2591138\" target=\"_blank\">2591138</a>).</p>\n<p>The configuration view has the following constraints:</p>\n<ul>\n<li>If a field is configured for the application area User Management and the Central User Administration (CUA) is active, the maintenance rules of the CUA apply. (Transaction SCUM)</li>\n<li>If the HCM integration switch is active, BP is not applicable as maintenance source.</li>\n<li>If the HCM integration switch is inactive, HR is not applicable as maintenance source.</li>\n<li>The location part of the workplace address (company address - transaction SUCOMP) is not part of configuration view TBZ_V_EEWA_SRC because it can only be maintained by User Management. (See SAP Note <a href=\"/notes/2571544\" target=\"_blank\">2571544</a>)</li>\n</ul>\n<p> </p>\n<p><strong><a name=\"conversion\" target=\"_blank\"></a>﻿Conversion</strong></p>\n<p>If HCM integration is active and the conversion from SAP Business Suite to SAP S/4HANA is done, employees are automatically converted to business users via the periodically scheduled sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL.</p>\n<p>If HCM integration is inactive:</p>\n<ul>\n<li>Business partners that exist in an ERP system with user linkage via table HRP1001 from BP-CP-US are automatically converted to business users in SUM-Phase as of SAP S/4HANA 1809 (see conversion report RFS4BPU_CONVERT_BUSINESS_USER).</li>\n<li>Business partners that exist in an ERP system with user linkage via table BP001 are automatically converted to business users in Silent Data Migration (SDM) as of SAP S/4HANA 2020 (see class CL_SDM_BUPA_BPU_BP001).</li>\n</ul>\n<p> </p>\n<p><strong><a name=\"how_to_determine_relevance\" target=\"_blank\"></a>﻿How to Determine Relevance</strong></p>\n<p>Simplification item S4TWL - Business User Management is relevant for all SAP S/4HANA conversion customers.</p>", "noteVersion": 48, "refer_note": [{"note": "2340095", "noteTitle": "2340095 - S4TWL - Conversion of Employees to Business Partners", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong>﻿</p>\n<p>With the conversion to SAP S/4HANA the implementation of the employee business partner is mandatory if HR data (HR infotype based PERNR data model including the PA-Tables) is needed. The HR data can be locally maintained (for example via transaction PA30 or PA40) or via integration scenarios with SAP HCM as hub implementation, SAP SuccessFactors Employee Central or an external (third party) HCM system.</p>\n<ol>\n<li><strong>Constellation SAP HCM embedded in SAP S/4HANA<br/></strong>It is mandatory to synchronize employees to employee business partners.</li>\n<li>\n<p><strong>Constellation SAP HCM ERP as hub implementation (non SAP S/4HANA)<br/></strong>The synchronization of employees to employee business partners does not take place in the SAP HCM hub system. If the HR mini master record is distributed to connected systems, it is mandatory to synchronize employees to employee business partners in the <strong>receiving SAP S/4HANA system(s).</strong></p>\n</li>\n<li><strong><strong><strong>Constellation SAP HCM in SAP S/4HANA as hub implementation<br/></strong></strong></strong>The synchronization of employees to employee business partners takes place in the SAP HCM hub system. Additionally, if the HR mini master record is distributed to connected systems, it is mandatory to synchronize employees to employee business partners in the<strong> receiving SAP S/4HANA system(s).<strong><strong><br/></strong></strong></strong><strong><strong><br/></strong></strong></li>\n<li><strong><strong>Constellation SAP SuccessFactors in a hybrid scenario<br/></strong></strong>In a hybrid scenario where employees are maintained in SAP SuccessFactors Employee Central and distributed to SAP S/4HANA system(s) it is mandatory to synchronize employees to employee business partners.</li>\n</ol>\n<p>To generate the employee business partners the HCM integration must be activated via the HCM Integration Switch in system table T77S0.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p><strong>Description       </strong></p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p><strong>Group (GRPID)</strong></p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p><strong>Sem. abbr. (SEMID)</strong></p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p><strong>HCM Integration inactive</strong></p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p><strong>HCM Integration active</strong></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"121\">\n<p>Activate HCM Integration</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>HRALX</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>HRAC</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>Space (blank)</p>\n</td>\n<td valign=\"top\" width=\"121\">\n<p>X</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>If the HR mini master is not needed the HCM integration can be deactivated.</p>\n<p>As of SAP S/4HANA 2020 OP a new Employee Business Partner Model was introduced. This new model was also provided with Support Packages for the lower releases SAP S/4HANA 1809 OP SP06 and 1909 OP SP04. See SAP Note <a href=\"/notes/2954033\" target=\"_blank\">2954033</a><strong>. It is recommended to use the New Employee Business Partner Model.</strong></p>\n<p>Nevertheless, you can also use the legacy models (VMODE1, VMODE2). For more information regarding legacy employee business partner models please refer to this <a href=\"https://blogs.sap.com/2021/08/16/new-employee-business-partner-data-model-in-sap-s-4hana-2020-on-premise/?preview_id=1377633\" target=\"_blank\">blog post</a> and the integration guide attached to this Note.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The data model in SAP S/4HANA is based on Business Partners (BP). A BP must be assigned to each employee.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>After the conversion to SAP S/4HANA, the migration report /SHCM/RH_SYNC_BUPA_FROM_EMPL must be executed before productive use of the system. In case of concerns regarding performance caused by large data volume it is recommended to use report /SHCM/RH_SYNC_BUPA_EMPL_SINGLE for the initial load. Due to customizing dependencies, the reports cannot be executed in technical downtime. For the daily synchronization the report /SHCM/RH_SYNC_BUPA_FROM_EMPL needs to be scheduled. It is recommended to run the report once a day after the initial run in order to synchronize future-dated employee changes to the Business Partner. Non future-dated changes to employee data are synchronized to the Business Partner automatically.</p>", "noteVersion": 22, "refer_note": [{"note": "2323301", "noteTitle": "2323301 - Customizing document - Synchronization of Business Partner for SAP HCM Employee Role", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>If you are using S/4HANA 1511 or S/4HANA 1511 FPS01, employee data will not be available in the CDS views.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA, CDS,Employee, Employment, Synchronization</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Prerequisite note:  2328695</p>\n<p>Missing customizing steps.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To resolve this error in your system, apply the customizing steps mentioned in the attached document.</p>\n<p>In S/4HANA 1511 FPS02, the documentation is available as a customizing node under <span 'times=\"\" ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-bidi;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-ascii-theme-font:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" mso-hansi-theme-font:=\"\" new=\"\" roman';=\"\"><strong>Personnel Management -&gt; SAP S/4HANA for Human Resources -&gt; Synchronization of Business Partner for SAP HCM Employee Role</strong>.</span></p>", "noteVersion": 4}, {"note": "2364207", "noteTitle": "2364207 - Employee to Business Partner synchronization in S/4HANA performance issues and parallel job scheduling is required", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Employee to Business partner synchronization has performance issues and due to large number of employees in the system it takes a very long time process. It is not possible to parallel process the synchronization since the daily sync job does not have a selection screen offered.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>/SHCM/RH_SYNC_BUPA_EMPL_SINGLE</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This note is released for customers who has large number of employees and would like to process the synchronization job in parallel sesssions by using selection screen.</p>\n<p>Import note 2364202 as a pre-requisite.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please follow the steps in attached document.</p>\n<p>See attached document</p>", "noteVersion": 6}]}, {"note": "2558350", "noteTitle": "2558350 - S/4HANA: Integration of EEWA changes into identity API", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The work center data of a user with a business partner assignment cannot be changed via the user management transactions and APIs or via transaction BP or the APIs of the business partner. Transaction SU01 can only display the address data of the users converted with the report /SHCM/RH_SYNC_BUPA_EMPL_SINGLE<span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">.</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IDADTYPE, 04</p>\n<p>SU01, SU10, BAPI_USER_CHANGE, IDENTITY_MODIFY</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In S/4HANA On Premise releases, there can be users with a business partner assignment as well as users with a classic address. <br/>With the initial delivery of the S/4HANA On Premise releases, it was defined that address data of a business partner was not allowed to be changed using user management transactions or APIs.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the specified support package.<br/>For technical reasons, the provision of correction instructions is <span>not</span> possible.</p>\n<p><span><strong>Information about the solution:</strong></span></p>\n<p>With this support package, you are provided with corrections from several areas ('Business Partner Foundation' and 'User Management\") in order to enable the maintenance of the workplace addresses of business partners.<br/>The basis of the maintainability of the workplace addresses of business partners is new customizing (maintenance view TBZ_V_EEWA_SRC). This is described in SAP Note <a href=\"/notes/%value\" target=\"_blank\">2548303</a><span ar-sa;\"=\"\" calibri',sans-serif;=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">.</span><br/>Following the import of this support package, all attributes of the workplace address that are set to 'User Management' in customizing (technically: 'US') can be changed using the user management transactions and APIs, too.</p>\n<p><span><strong>Caution:</strong></span></p>\n<ul>\n<li>Only attributes set to 'Local' in SCUM and 'User Management' in the maintenance view TBZ_V_EEWA_SRC can be changed directly in the child system using the user management transactions and APIs.<br/> </li>\n<li>In IDoc inbound processing (message type: USERCLONE, method: CLONE), all attributes that are set to 'Global' in SCUM and to 'User Management' in the maintenance view TBZ_V_EEWA_SRC can be transferred.</li>\n</ul>", "noteVersion": 3, "refer_note": [{"note": "2558693", "noteTitle": "2558693 - <PERSON><PERSON>_BUPA_EEWA_API=>MODIFY always calls do_save", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The method CL_BUPA_EEWA_API=&gt;MODIFY covers two steps of modifications:<br/>- applying the changes to the transactional memory<br/>- saving the changes to the database.</p>\n<p>Some callers require both steps, some callers need a separation where the MODIFY method should only perform the first step.<br/>The method does not consider this. It always processes both steps.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>A new optional importing parameter IV_DO_SAVE of type boolean is added to the method.<br/>The default value of the parameter is ABAP_TRUE.</p>\n<p>If the method is called with parameter value ABAP_FALSE then the second step - saving the changes to the database - is skipped.</p>", "noteVersion": 1}]}, {"note": "2265093", "noteTitle": "2265093 - S4TWL - Business Partner Approach", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, any of the listed on-premise editions -&gt; 1511, 1610, 1709,1809, 1909, 2020, 2021, 2022. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>In SAP S/4HANA, Business Partner is the leading object and single entry point to maintain Business Partner, Customer and Supplier (formerly known as Vendor) master data. This is to ensure ease of maintenance of the above master data and to achieve harmonization between them. Compared to classical ERP transactions, maintenance of Customer and Supplier master data via Business Partner has multiple advantages. Some of them are as follows:</p>\n<ul>\n<li>Business Partner allows maintenance of multiple addresses with corresponding address usages.</li>\n<li>In classical transactions, one customer can only be associated to one account group. But in Business Partner, multiple roles can be associated to the same Business Partner.</li>\n<li>Maximal data sharing and reuse of data which lead to an easier data consolidation.</li>\n<li>General Data available for all different Business Partner roles, specific data is stored for each role.</li>\n<li>Maintenance of multiple relationships to the same Business Partner.</li>\n<li>Maintenance of Time Dependency at different sub-entities roles, address, relationship, bank data etc.</li>\n</ul>\n<p><strong>Description</strong></p>\n<p>There are redundant object models in the traditional ERP system. Here the vendor master and customer master is used. The (mandatory) target approach in S/4HANA is the Business Partner approach.</p>\n<p>Business Partner is now capable of centrally managing master data for business partners, customers, and vendors. With current development, BP is the single point of entry to create, edit, and display master data for business partners, customers, and vendors.</p>\n<p>Additional Remarks:</p>\n<ul>\n<li>It is planned to check the introduction of the Customer/Vendor Integration in the pre-checks and the technical Conversion procedure of SAP S/4HANA on-premise edition 1511, 1610, 1709, 1809, 1909 and 2020. A system where the customer/vendor integration is not in place will be declined for the transition.</li>\n<li>The Business Partner Approach is not mandatory for the SAP Simple Finance, on-premise edition 1503 and 1605.</li>\n</ul>\n<p><strong>Business Process related information</strong></p>\n<p>Only SAP Business Suite customer with C/V integration in place can move to SAP S/4HANA, on-premise(Conversion approach). It is recommended but not mandatory that BuPa ID and Customer-ID / Vendor ID are the same.</p>\n<p>The user interface for SAP S/4HANA is transaction BP. There is no specific user interface for customer/vendor like known from SAP Business Suite (the specific transactions like XD01, XD02, XD03 or VD01, VD02, VD03/XK01, XK02, XK03 or MK01, MK02, MK03 etc. are not available in SAP S/4HANA on-premise)</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td rowspan=\"2\" valign=\"top\" width=\"182\">\n<p>Transactions not available in SAP S/4HANA on-premise edition</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>Transactions that get redirected to transaction BP:</p>\n<p>FD01,FD02,FD03, FK01,FK02,FK03,MAP1,MAP2,MAP3, MK01, MK02, MK03, V-03,V-04,V-05,V-06,V-07,V-08,V-09, V-11, VAP1, VAP2, VAP3, VD01, VD02,VD03, XD01, XD02, XD03, XK01, XK06, XK07, XK02, XK03</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"414\">\n<p>Transactions that are obsolete:</p>\n<p>FD06, FK06, MK06, MK12, MK18, MK19, VD06, XD06, V+21, V+22, V+23</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>Points to Note in SAP S/4HANA:</p>\n<p>In addition, note the following points while using the following functionalities in SAP S/4HANA:</p>\n<ul>\n<li><strong>Consumer</strong> - While creating a consumer, ensure that you use the BP Grouping that is associated to the customer account group (Consumer) in TBD001 (Assignment of Account Group BP Grouping). Note that customer itself is a contact person, hence no further relationships can be maintained.</li>\n<li><strong>One-time Customer/Supplier</strong> - While creating a one-time customer or supplier, ensure that you use the BP Grouping that is associated to the customer/supplier account group (One time customer/supplier) in TBD001 and TBC001 (Assignment of Account Group BP Grouping).</li>\n<li>The idocs <strong>DEBMS/CREMAS</strong> and cremas are not recommended for data integration between S/4HANA systems. In S/4HANA, BP is the leading object. Therefore, only customer or supplier integration should be avoided. In S/4HANA, Business Partner is the leading object. Therefore, customer or supplier only integration should be avoided. You can use Business Partner web services (SOAP) via API Business Hub for SAP S/4HANA integration. It is recommended to use IDocs only if the source system has Customer or Supplier (no Business Partner) master data.</li>\n<li><strong>Role Validity</strong> - In SAP S/4HANA, you can maintain the validity of roles. With this feature, the user can close the (prospect) role and move to another after the mentioned validity period. For example, a business partner with prospect role can set the role change to sold-to-party by specifying the validity and by closing the previous validity. </li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Before you begin the BP conversion from an SAP ERP system to an SAP S/4 HANA system, you have to answer the questions</p>\n<ul>\n<li>Whether the Business Partner ID and Customer-ID /Vendor ID should be the same in the S/4 HANA System?</li>\n</ul>\n<p>The keys for a smooth synchronization of the ERP customer/vendor into the S/4 system with the business partner as the leading object are beside Business Partner Know-How also consistent customer/vendor data and valid and consistent custom/vendor and Business Partner customizing entries. For this reason, the customer/vendor data has to be cleaned up before it can be converted into the S/4 Business Partner.</p>\n<p>Prepare: Pre-Checks and clean-up customer/vendor data in the ERP System</p>\n<ul>\n<li>Implement SAP S/4HANA Conversion Pre-Checks according to the SAP S/4HANA Conversion guide chapter Pre-Checks.</li>\n<li>Activate Business Function CA_BP_SOA.<br/>In case that the Business Function CA_BP_SOA not yet in the system exist, you have to create a new Business Function in the customer namespace with the switches VENDOR_SFWS_SC1 and VENDOR_SFWS_SC2. The new customer specific Business Function must be of type Enterprise Business Function (G) - see also Mandatory checks for customer, vendor and contact.</li>\n<li>Check CVI customizing and trigger necessary changes  e.g. missing BP Role Category, Define Number Assignments according to the S/4 Conversion guide chapter Introduce Business Partner Approach (Customer Vendor Integration).</li>\n<li>Check and maintain BP customizing e.g. missing tax types.</li>\n<li>Check master data consistency using CVI_MIGRATION_PRECHK and maintain consistency.</li>\n<li>Check and clean-up customer/vendor data e.g. missing @-sign in the e-mail address.</li>\n</ul>\n<p>Synchronization</p>\n<ul>\n<li>Synchronization (Data load) is done via <em>Synchronization Cockpit</em> according to the attached BP Conversion Document.pdf &gt; Chapter 5. <em>Convert Customer/Supplier Data into Business Partner</em>.</li>\n<li>In case of an error during the synchronization process due to data/customizing mismatch you can find the errors using Logs button. You can also view this via MDS_LOAD_COCKPIT&gt; Monitor tab &gt; Call PPO button in Synchronization Cockpit.</li>\n</ul>\n<p>Conversion Process</p>\n<ul>\n<li>Conversion Process must be triggered according to the BP Conversion Document.pdf attached to this SAP Note.<strong><br/></strong></li>\n</ul>\n<p>Business Partner Post Processing</p>\n<ul>\n<li>The customer/vendor transformation is bidirectional. You can both process customer/vendor master records from business partner maintenance as well as populate data from customer/vendor processing to the business partner. After the successful S/4 conversion process you have to activate the post processing for direction Business Partner a Customer /Vendor.</li>\n</ul>\n<p><strong>Related SAP Notes &amp; Additional Information</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"158\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"446\">\n<p>SAP Notes: 2211312, 2210486, 2216176</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"158\">\n<p>Other SAP Notes</p>\n</td>\n<td valign=\"top\" width=\"446\">\n<p>SAP Note: 1623677, 954816</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"158\">\n<p>SAP Help</p>\n</td>\n<td valign=\"top\" width=\"446\">\n<p>Refer to the attachments (in SAP Note 2265093):</p>\n<ul>\n<li>BP_Conversion Document.pdf: <em>Description of Busines Partner Approach and conversion activities</em></li>\n<li>01_TOP-Item_MD_Business-Partner-Approach_Version1.0.pdf: <em>Presentation on Business Partner Approach and Customer Vendor Integration during Conversion</em></li>\n</ul>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Custom Code Adaption</strong></p>\n<p>If you are writing a direct write statement or a custom code on the tables mentioned in piece list SI_MD_BP, Please follow the below mentioned steps instead.</p>\n<p>Reason and Prerequisites:</p>\n<ul>\n<li>You want to create BP/Customer/Supplier with direct write statements or a particular piece of custom code.</li>\n<li>You need an API to create Business Partner with Customer and Supplier roles.</li>\n</ul>\n<p>The following are possible use-cases:</p>\n<ol start=\"1\">\n<li>Requirement to create business partners in the same system: You can use API CL_MD_BP_MAINTAIN. Pass the data in CVIS_EI_EXTERN structure format and pass it to the method VALIDATE_SINGLE. This method validates all the data that is passed; after that, you can use the method MAINTAIN to create business partners.</li>\n<li>Integration of data with various landscapes: If you want to integrate BP/Customer/Supplier master data across different systems, you can use following interfaces:<br/> <br/> a. IDOCs <br/> <br/> There are two types of IDocs available that can be used:</li>\n<ol start=\"1\">\n<li>DEBMAS: If you want to integrate customer (without BP) data between two systems, this IDOC can be used. </li>\n<li>CREMAS: If you want to integrate supplier (without BP) data between two systems, this IDOC can be used.</li>\n</ol></ol>\n<p>        b. SOA Services</p>\n<ol start=\"1\"><ol>\n<li>If the system has data of Business Partners, use SOA services for integration. Business Partner SOAP services enable you to replicate data between two systems. There are both inbound and outbound services available. Refer to SAP Note 2472030 for more information.</li>\n</ol></ol>\n<p>However, the IDocs DEBMAS and CREMAS are not recommended for data integration between S/4HANA systems. In S/4HANA, Business Partner is the leading object. Therefore, you can use Business Partner web services (SOAP) for SAP S/4HANA integration.</p>\n<p><strong>Industry Specific Actions</strong></p>\n<p>IS-OIL Specific Actions:</p>\n<ul>\n<li> In S4HANA all the IS-OIL specific fields (including customer BDRP data fields) have been adapted as per the new Framework of BP.</li>\n<li> The IS-OIL Fields related to Vendor and Customer now have been moved to BP under the roles FLCU01 (Customer), FLVN01 (Vendor).</li>\n</ul>\n<p>Retail Sites Specific Action:</p>\n<ul>\n<li>Customers and vendors assigned to Retail sites are not handled by CVI synchronization (MDS_LOAD_COCKPIT) on SAP ERP as described in this note, but by a specific migration process during SAP S/4HANA conversion. See SAP Note <a href=\"/notes/2310884\" target=\"_blank\">2310884</a> for more information.</li>\n</ul>", "noteVersion": 43, "refer_note": [{"note": "2344034", "noteTitle": "2344034 - SAP S/4HANA Automation for Master Data Migration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This report checks the necessary CVI and Business Partner Customizing for each client and proposes a solution for missing or wrong Customizing entries.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>CVI_UPGRADE_CHECK_RESOLVE, AUTOMATION , UPGRADATION , CVIC_CUST_TO_BP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Automation of the existing precheck.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Perform the following steps:</p>\n<ol>\n<li>Implement the Note 2780288 first to set Migration scenario ON from CVI_UPGRADE_CHECK_RESOLVE</li>\n<li>Perform the manual pre implementation steps.</li>\n<li>Implement this note.</li>\n<li>After implementation, perform the manual post-implementation steps. <br/><br/></li>\n</ol>", "noteVersion": 80}, {"note": "2210486", "noteTitle": "2210486 - Obsolete : S/4HANA Business Partner Conversion Reports", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>** The report \"CVI_UPGRADE_CHECK_RESOLVE\" has been deprecated. Please use the new report \"CVI_CUSTOMIZING_RESOLVE\". For more information, see SAP Note 2891455.**</p>\n<p>This note contains information regarding the checks for necessary Customizing and synchronization for Customer/Supplier Integration (CVI), which have to be executed during the conversion from ERP to S/4HANA. During synchronization, the customer/supplier data get synchronized to Business Partner.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Conversion report, PRECHECK_UPGRADATION_REPORT, Precheck</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You need to ensure the necessary CVI mappings are maintained during conversion.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The CVI_UPGRADE_CHECK_RESOLVE performs the necessary checks per client, to find out missing customizing entries, and solution to create/correct these entries for each client. For information about this report, see SAP Note <a href=\"/notes/2344034\" target=\"_blank\">2344034 - S/4HANA Automation for Master Data Migration</a>.</p>\n<p>For completeness after the execution of CVI_UPGRADE_CHECK_RESOLVE Report and the CVI synchronization is completed (Customer and supplier data synchronized with Business partner)</p>\n<p>1. Deactivate the Badi's CVI_MIGRATION_SUPPRESS_CHK - Suppress Mandatory Checks, CVI_CONSUMER_RELATION - Restrict contact person creation for consumer which is available in the enhancement spot CVI_CUSTOM_MAPPER.</p>\n<p> 2. Click on COMPLETE flag in the report CVI_UPGRADE_CHECK_RESOLVE report (CVI Synchrionization)</p>\n<p> </p>\n<p>In addition, run the PRECHECK_UPGRADATION_REPORT (client independent) which determines the mapping entries that are missing between CVI data and BP data, and missing Customizing entries. Refer to SAP Note <a href=\"/notes/2216176\" target=\"_blank\">2216176 - Precheck report from business partner</a></p>\n<p>For more information on conversion activities for Business Partner, refer to <strong><em>BP_Conversion Document.pdf</em> </strong>which is attached to the SAP Note <a href=\"/notes/2265093\" target=\"_blank\">2265093 - Business Partner Approach</a>.</p>", "noteVersion": 9}, {"note": "3011764", "noteTitle": "3011764 - SEPA Mandate for Vendors", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>A consistency check to ensure debitors (customers) in table <em>TRFKREDEB_SYNC</em> are linked to same business partner as that of the creditors (vendors), prior to S/4HANA conversion.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SEPA Mandate, S/4HANA conversion, migration, BP/CVI, TRFKREDEB_SYNC, RFKREDEB_SYNC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>With SAP S/4HANA, customer and supplier (vendor) master data can be created and maintained using only the business partner transactions. A business partner can be created in both the customer and supplier (vendor) roles.</p>\n<p>The vendors and their mapped customers in table <em>TRFKREDEB_SYNC </em>must be synchronized to same business partner.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>A new simplification check is introduced in the class <em>“CLS4SIC_LO_MD_BP\" </em> to ensure debitors (customers) in table <em>TRFKREDEB_SYNC</em> are linked to the same business partner as that of creditors (vendors), prior to S/4HANA conversion.The simplification check will ensure that for all the entries in <em>TRFKREDEB_SYNC</em> table the vendors and mapped customers are linked to the same business partner. If not, it will throw error messages.</p>\n<p><strong>Additional Information</strong></p>\n<p>Before the upgrade, run <strong><em>RFKREDEB_SYNC</em></strong> report or execute <strong><em>MDS_LOAD_COCKPIT</em></strong>  to ensure that vendor and mapped customer are synchronized to same business partner.</p>\n<p>In case the error persists, perform the following steps:</p>\n<ol>\n<li>Create additional customer role for the business partner of that vendor.</li>\n<li>Archive the business partner of customer.</li>\n</ol>", "noteVersion": 2}, {"note": "2216176", "noteTitle": "2216176 - Obsolete :Precheck report from business partner", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>** The report \"PRECHECK_UPGRADATION_REPORT\" is obsolete and integrated into the \"CVI_COCKPIT\" transaction code. For more information, see SAP Note 2832085.**</p>\n<p>This note delivers the pre-conversion checks for Business Partner which have to be executed before the conversion to S/4.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Conversion report, CL_S4_Checks_BP, PRECHECK_UPGRADATION_REPORT, Precheck</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Pre-conversion check is done to ensure all the necessary <span ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">CVI Link and </span>CVI mappings are done for both customer and vendor.</p>\n<p>As a preliminary step cvi_link check is carried out in the direction customer/vendor to BP. This check determines customer/vendor to BP link is established.</p>\n<p>Post-conversion checks are done to check whether all CVI mappings are done for Business partner which belong to customer or vendor role.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>To check after the conversion process, please download this note. Once you have downloaded the note, execute the same in transaction SE38 after providing the report name <strong>PRECHECK_UPGRADATION_REPORT. </strong> This report includes both mandatory and optional checks, and if you do not perform the mandatory checks at least, ideally, the conversion should not be continued. If status turns red after the execution, we can click on each error status and get the information on what needs to be maintained.</p>", "noteVersion": 34}, {"note": "1623677", "noteTitle": "1623677 - BP_CVI: Check report for checking CVI Customizing", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You use the new Customer Vendor Integration (CVI), which is delivered with enhanced functions as of Release ERP 6.00, to synchronize the business partner object with the customer object and/or vendor object. You want to check the existing CVI Customizing that is required for this.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>CVI, customer vendor integration, customer/vendor, customer-vendor, business partner, BP, FI customer, FI vendor, synchronization, synch, Customizing, Implementation Guide, check, check report, CVI_FS_CHECK_CUSTOMIZING</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Extensive Customizing is required for the synchronization between the business partner object and the customer object and/or vendor object, as described in SAP Note 956054 \"BP_CVI: Customer/vendor integration as of ERP 6.00\". Errors or terminations that occur during synchronization are often due to incorrect Customizing.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><b>General</b><br/> <p>The report CVI_FS_CHECK_CUSTOMIZING provided with this SAP Note can be used to display and check the Customizing for the customer vendor integration (CVI) that has been made for the business partner. The results of the checks return information about possible causes of error in the Customizing settings.<br/></p> <b>Installation</b><br/> <p>Install the report by importing the relevant Support Package of the underlying release.<br/></p> <b>Report call</b><br/> <p>Start the report using transaction CVI_FS_CHECK_CUST.<br/>Choose \"Display Help\" to call the detailed help for the relevant setting overview selected. You can choose \"Customizing\" to navigate back to Customizing and to check or adjust the settings for CVI.<br/><br/>The display of the current Customizing settings and the relevant check results are divided into three categories:</p> <ol>1. General settings</ol> <p>              These include the general settings for the synchronization directions, the connection between CVI and postprocessing using Post Processing Office (PPO), and the function modules required for inbound/outbound processing. <ol>2. Customer settings</ol> <p>              These include role assignments and number range assignments for the synchronization directions \"business partner -&gt; customer\", and \"customer -&gt; business partner\". <ol>3. Vendor settings</ol> <p>              These include role assignments and number range assignments for the synchronization directions \"business partner -&gt; vendor\" and \"vendor -&gt; business partner\".</p></p></p></div>", "noteVersion": 2}, {"note": "954816", "noteTitle": "954816 - BP_CVI: Transactions for creating/linking BPs", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>In Release ERP2005, you use the following transactions to create business partners from customers or vendors or link them with existing business partners:<br/><br/>FLBPD1 - Create BP from customer<br/>FLBPD2 - Link BP to customer<br/>FLBPC1  Create BP from vendor<br/>FLBPC2 - Link BP to vendor<br/><br/>In the process, you find these perform differently to the previous releases.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Customer, vendor, FLBPD1, FLBPC1, FLBPD2, FLBPC2, MDSV_CTRL_OPT_A, CVIV_CUST_TO_BP1, CVIV_CUST_TO_BP2, CVIV_VEND_TO_BP1, CVIV_VEND_TO_BP2, TBD001, TBD002, TBC001, TBC002</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In Release ERP2005, the integration of the SAP Business Partner with the customer and the vendor (CVI) was redesigned. Starting with this release, the replication works bi-directionally and can be switched on and off for individual replication paths.<br/>Through the support of the replication of the customer/vendor to the business partner and the related technical constraints, it was necessary to create new Customizing tables and views and to adjust the function of the dialogs for the transactions accordingly.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>To be able to use Transactions FLBPD1, FLBPD2, FLBPC1 and FLBPC2, Customizing is required for the CVI relevant tables. Call all maintenance views using the IMG menu path \"Cross-Application Components\" -&gt; \"Master Data Synchronization\" and the entries below. Specifically, these are:</p> <ul><li>Activate synchronization objects (MDSV_CTRL_OPT_A)</li></ul> <p>           Here, you can activate the replications for business partner to customer, business partner to vendor, customer to business partner, and vendor to business partner.</p> <ul><li>Determine number assignment for direction BP to customer (V_TBD001)</li></ul> <p>           Account groups of the customer are assigned to the business partner groupings relevant for the replication. Using the related number ranges, the determination is made as to whether the customer numbers are assigned internally or externally or whether there is number equivalence.</p> <ul><li>Determine number assignment for direction BP to vendor (V_TBC001)</li></ul> <p>           Account groups of the vendor are assigned to the business partner groupings relevant for the replication. Using the related number ranges, the determination is made as to whether the customer numbers are assigned internally or externally or whether there is number equivalence.</p> <ul><li>Determine number assignment for direction customer to BP (CVIV_CUST_TO_BP1)</li></ul> <p>           Business partner groupings are assigned to the customers account groups relevant for the replication. Using the related number ranges, the determination is made as to whether the business partner numbers are assigned internally or externally. For external number assignment, number equivalence is absolutely necessary.</p> <ul><li>Determine number assignment for direction vendor to BP (CVIV_VEND_TO_BP1 )</li></ul> <p>           Business partner groupings are assigned to the vendors account groups relevant for the replication. Using the related number ranges, the determination is made as to whether the business partner numbers are assigned internally or externally. For external number assignment, number equivalence is absolutely necessary.</p> <ul><li>Set BP role category for direction BP to customer (V_TBD002)</li></ul> <p>           Here, the definition is made for which role categories of the SAP BP customers are created. Optional customer roles cannot be taken into account for creating BPs from customers.</p> <ul><li>Set BP role category for direction BP to vendor (V_TBC002)</li></ul> <p>           Here, the definition is made for which role categories of the SAP BP vendors are created. Optional vendor roles cannot be taken into account for creating BPs from vendors.<br/>           Determine BP role for direction customer to BP (CVIV_CUST_TO_BP2)<br/>           Defines the roles in which business partners are created from customers of a certain account group.</p> <ul><li>Determine BP role for direction vendor to BP (CVIV_VEND_TO_BP2)</li></ul> <p>           Defines the roles in which business partners are created from customers of a certain account group.<br/>Through Transactions FLBPD1 and FLBPC1, the same processes are triggered as run for the creation of a customer or vendor. For Transactions FLBPD2 and FLBPC2, the same processes are triggered as run for creating a business partner who is a vendor or customer within Transaction BP.<br/>For this reason, Customizing entries must be maintained in all tables and the synchronizations must be active for the correct execution of the transactions. If the synchronization for the direction customer/vendor -&gt; BP is not active, the customer or vendor is classified as irrelevant for the replication and it is not possible to create a business partner or to establish a link from that.<br/><br/>Following conditions must be fulfilled:<br/></p> <b>FLBPD1</b><br/> <ul><li>Customer already exists and was not linked to BP yet.</li></ul> <ul><li>Customer is not consumer or retail site.</li></ul> <ul><li>Synchronization customer -&gt; BP is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>A GP grouping was assigned to the current customer accounts group (CVIV_CUST_TO_BP1).</li></ul> <ul><li>At least one BP role was assigned to the current customer accounts group (CVIV_CUST_TO_BP2).</li></ul> <b>FLBPC1</b><br/> <ul><li>Vendor already exists and was not linked to BP yet.</li></ul> <ul><li>Synchronization vendor -&gt; BP is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>A GP grouping was assigned to the current vendor account group (CVIV_VEND_TO_BP1).</li></ul> <ul><li>At least one BP role was assigned to the current vendor account group (CVIV_VEND_TO_BP2).</li></ul> <b>FLBPD2</b><br/> <ul><li>Business partner already exists and was not linked to a customer yet.</li></ul> <ul><li>Customer already exists and was not linked to a business partner yet.</li></ul> <ul><li>Synchronization BP -&gt; customer is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>Synchronization customer -&gt; BP is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>A customer accounts group was assigned to the current GP grouping (V_TBD001).</li></ul> <ul><li>A GP grouping was assigned to the current customer accounts group (CVIV_CUST_TO_BP1).</li></ul> <ul><li>The selected BP role is a customer role (V_TBD002).</li></ul> <ul><li>The business partner grouping used and the customer accounts group used do not have to be assigned to each other.</li></ul> <b>FLBPC2</b><br/> <ul><li>Business partner already exists and was not linked to a vendor yet.</li></ul> <ul><li>Vendor already exists and was not linked to a business partner yet.</li></ul> <ul><li>Synchronization BP -&gt; vendor is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>Synchronization vendor -&gt; BP is active (MDSV_CTRL_OPT_A).</li></ul> <ul><li>A vendor account group was assigned to the current GP grouping (V_TBC001 ).</li></ul> <ul><li>A GP grouping was assigned to the current vendor account group (CVIV_VEND_TO_BP1).</li></ul> <ul><li>The selected BP role is a vendor role (V_TBC002).</li></ul> <ul><li>The business partner grouping used and the vendor account group used do not have to be assigned to each other.</li></ul> <p></p> <b>Additional notes:</b><br/> <ol>1. Substitute transaction</ol> <p>Instead of Transactions FLBPD1 and FLBPC1, you can use the synchronization cockpit (Transaction MDS_LOAD_COCKPIT). This can be used for large-scale creation of business partners from customers and vendors.</p> <ol>2. A BP exists with customer and vendor role.</ol> <p>If a business partner already has customer or vendor roles without being linked to customers and vendors, a link is no longer possible using Transactions FLBPD2/FLBPC2. If internal number assignment was set for the objects involved, this results in a new entry of a vendor that is linked with the business partner, in addition to the link with the customer for FLBPD2. If the external number assignment is set, this results in a termination since either the vendor number does not exist or the vendor already exists (number equivalence).<br/><br/>This problem can only occur if:</p> <ul><li>The roles are created when synchronization is switched off.</li></ul> <ul><li>Role Customizing was subsequently changed (V_TBD002, V_TBC002).</li></ul> <ul><li>Roles/BPs are created using paths other than those provided with the standard system (own programs, direct inserts into table BUT100).</li></ul> <p><br/>In this case, the following workarounds may help:</p> <ul><li>Switch off of the synchronization for the other object (MDSV_CTRL_OPT_A). After the successful program run, it is switched on again and the link with the other object is repeated.</li></ul> <ul><li>Manually create the assignments in tables CVI_CUST_LINK and CVI_VEND_LINK and subsequently synchronize the business partners involved using the synchronization cockpit.</li></ul> <ol>3. Required entry field comparison</ol> <p>So that no errors occur for the update of the data, the required entry fields of the objects involved must first be reconciled with each other and the data filled. Refer to Note 928616 for more information on this topic.</p></div>", "noteVersion": 2}, {"note": "2211312", "noteTitle": "2211312 - S4TC SAP_APPL – Pre-Conversion check for Business Partner", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note delivers the pre-conversion checks for Business Partner which have to be executed before the conversion to S/4 HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Conversion report, CL_S4_Checks_BP.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Pre-conversion check is done to ensure that all the necessary CVI mappings are done for both customer and vendor.</p>\n<p>Post-conversion checks are done to check whether all CVI mappings are done for business partner which belong to customer or vendor role.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Refer to SAPNote (<strong>2210486)</strong> for all the checks which were executed. This also provides the Customizing (IMG) path where the missing entries need to be maintained.</p>\n<p>To check after the conversion process, refer SAP note 2832085 for CVI_COCKPIT transaction code which has PRECHECK_UPGRADATION_REPORT integrated into it.</p>", "noteVersion": 29}, {"note": "2780288", "noteTitle": "2780288 - S/4 HANA Migration: The BP checks can be suppressed in the direction Customer/Vendor->BP during synchronization from MDS_LOAD_COCKPIT", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During synchronization from MDS_LOAD_COCKPIT, Business Partner would not get created if Customer/Vendor has errors. In a Migration scenario, these checks can be suppressed through suppression customizing.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SUPPRESSION CHECKS, MDS_LOAD_COCKPIT, SYNCHRONIZATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Suppression of business checks during synchronization</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Perform the following steps:</p>\n<ol>\n<li>Implement the pre-requisite notes 2345087, 2784166, 2323598, 2749015 and 2807124 prior to the current note.</li>\n<li>Perform the manual pre implementation steps.</li>\n<li>Implement the automatic correction instructions.</li>\n<li>After implementation, perform the manual post-implementation steps. </li>\n</ol>\n<p>During synchronization from MDS_LOAD_COCKPIT, there is default BAdI implementation (CVI_CUSTOM_MAPPER) called up in the direction Customer/Supplier to Business partner, to bypass the following checks:</p>\n<ul>\n<li>Postal Code check &amp; Address Regional check</li>\n<li>Geo Code checks</li>\n<li>Mandatory Field checks</li>\n<li>Tax Number checks</li>\n<li>Bank checks</li>\n<li>Tax Jusrisdiction checks</li>\n<li>Email checks</li>\n</ul>\n<p><strong>Note: </strong>Postal code check &amp; Address Regional checks are always suppressed by default in normal synchronization process(non-migration scenario). These checks can be enabled/disabled <strong>ONLY</strong> in a Migration scenario</p>\n<p><strong>Note</strong><span>: Only the Mandatory check suppression is logged and can be viewed from CVI Synchronization.</span></p>\n<p><span><strong>Note</strong>: By default the checks are enabled and unless the checks are disabled explicitly in the customizing delivered for controlling the suppression checks, the checks would be executed always.</span></p>\n<p>For BP Synchronization:</p>\n<ol>\n<li>Go to se38 and execute the report <em>CVI_CUSTOMIZING_RESOLVE</em>. <br/>For<em> CVI_CUSTOMIZING_RESOLVE </em>documentation, kindly look into<em> SAP Note 2265093 &gt; BP_Conversion Document.PDF (attachment) &gt; Appendix &gt; <em>CVI_CUSTOMIZING_RESOLVE </em>.</em></li>\n<li>Choose the radio button CVI Synchronization.</li>\n<li>This will enable Migration scenario and the checks would be perfomred based on the customing maintained for suppression checks</li>\n</ol>\n<p><strong>Note</strong>: You have to de-activate the BAdI implementation using Note 2651202 to disable the suppression checks after succesful conversion to S/4.</p>", "noteVersion": 11}]}, {"note": "2567604", "noteTitle": "2567604 - Uploading business users in SAP S/4HANA, on-premise edition", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You use an SAP S/4HANA, on-premise edition system in which HR integration is inactive (table T77S0 with configuration switches HRALX/HRAC and HRALX/PBPON). You cannot create any business partners with the \"Employee\" role in transaction BP. The reason for this is the new identity model for business users in SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RFS4BPU_EMPLOYEE_UPLOAD RFS4BPU_IMPORT_BUSINESS_USER</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>New identity model in SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You can use the report RFS4BPU_EMPLOYEE_UPLOAD in SAP_ABA 75B SP6 to upload employees.</p>\n<p>As of SAP S/4HANA 1709 FP1, the SAP Fiori app \"Maintain Employees\" is available.</p>\n<p>As of SAP S/4HANA 1809, you can use the report RFS4BPU_IMPORT_BUSINESS_USER to upload employees.</p>\n<p>Due to technical reasons, no correction instructions can be provided for the report.</p>", "noteVersion": 3}, {"note": "2571544", "noteTitle": "2571544 - EEWA: New functions for the user", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With SAP S/4HANA, on-premise edition, extensive changes were made regarding user and business partner integration. For background information, see SAP Note <a href=\"/notes/2570961\" target=\"_blank\">2570961</a>.<br/>In the user administration environment, some adjustments are still necessary for this. The abbreviation 'EEWA' used in the following stands for '<span><strong>E</strong></span>mploye<span><span><strong>E</strong></span></span> <strong><span>W</span></strong>orkplace <strong><span>A</span></strong>ddress', the workplace address of the assigned business partner.</p>\n<ol>\n<li>In the input help for the user, you cannot see whether the users are users with a classic address, technical users (without addresses), users with business partner assignment without a workplace address, or users with business partner assignment a and workplace address.<br/> </li>\n<li>If you integrate an SAP S/4HANA, on-premise edition system as a new child system in a Central User Administration (CUA), the system does not inform you that this is such a system and that there are therefore corresponding restrictions for user administration.<br/> </li>\n<li>\n<div>The workplace address of a user with business partner assignment (frequently also called 'workplace address') cannot be changed due to the new EEWA Customizing, even if you have not set the field to 'Global' in transaction SCUM. In transaction SCUM, you do not find any information telling you that the CUA distribution parameters are not effective.<br/> </div>\n</li>\n<li>During the migration of users with a classic address to users with business partner assignment and a workplace address, the address data of the company assigned to the user is <span>copied</span> to the company address component of the workplace address of the business partner. No more changes can be made to the company components of the workplace addresses of the business partners.<br/> </li>\n<li>The 'Short Description' of <span>users with business partner assignment </span>(see the 'Documentation' tab in transaction SU01) was changed by the migration of the users and during each additional comparison from the SAP Fiori app 'Maintain Employees' or Human Capital Management (HCM).</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Missing functionalities</p>\n<ol>\n<li><a href=\"/notes/2570961\" target=\"_blank\">2570961</a> - Simplification item S4TWL - business user administration</li>\n<li>\n<div><a href=\"/notes/2558350\" target=\"_blank\">2558350</a> - SAP S/4HANA: Integration of EEWA changes into identity API</div>\n</li>\n<li><a href=\"/notes/2558350\" target=\"_blank\">2558350</a> - SAP S/4HANA: Integration of EEWA changes into identity API</li>\n<li><a href=\"/notes/2548303\" target=\"_blank\">2548303</a> - SAP S/4HANA: Configuration of maintenance for workplace address</li>\n<li><a href=\"/notes/2570961\" target=\"_blank\">2570961</a> - Simplification item S4TWL - business user administration</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the specified support package. For technical reasons, we cannot provide a correction in the form of correction instructions.</p>\n<p><span><strong>Information about the solution</strong></span></p>\n<ol>\n<li>The input help for the user (for example, in SU01 or SU10) now provides an additional field '<em>Identity Address Type</em>' on the tab page '<em>Users by Address Data</em>', and a new result column '<em>ID Type</em>'. The result column contains the technical value for the individual address types of the users:</li>\n<ol>\n<li>'00' - User with Classic Address</li>\n<li>'01' - Technical User</li>\n<li>'02' - User With Business Partner Assignment <span>without</span> Workplace Address</li>\n<li>'04' - User With Business Partner Assignment <span>with</span> Workplace Address<br/> </li>\n</ol>\n<li>After you save this new child system in transaction SCUA, the log contains a new message stating that the identity model is activated, with a reference to the SAP Note <a href=\"/notes/2558350\" target=\"_blank\">2558350</a>.<br/> </li>\n<li>\n<p>As of this support package, the new column '<em>External</em>' is available on the 'Address' tab in transaction <strong>SCUM</strong>. SAP Note <a href=\"/notes/2694029\" target=\"_blank\">2694029</a> renames this column as ''<em>HR/BP</em>'. The displayed radio button position always refers to the local system and is relevant only for users who have a business partner assignment with workplace address.<br/> <br/>If the radio button is set in the column '<em>External</em>' ('<em>HR/BP</em>'), there is no maintainability in the user administration. The corresponding field values can then be maintained neither in transaction SU01 nor using IDoc inbound processing, BAPI_USER_CHANGE, or SAP Identity Management (IdM). Transaction SM30 with the maintenance view TBZ_V_EEWA_SRC (see also SAP Note <a href=\"/notes/2548303\" target=\"_blank\">2548303</a>) shows whether the maintainability of this address field is in Human Resources (HR) or in the business partner (BP).<br/> <br/>The maintainability of the name components of the address of users with business partner assignment (in the 'Person' box on the 'Address' tab in transaction SU01) is <span>always</span> in HR if HR is active in your system. If HR is <span>not</span> active, the maintainability of the name components continues to be in the business partner (BP). In this case, the field values can be maintained using the SAP Fiori app \"Maintain Employees\" or the SOAP service \"ManageBusinessUserIn\". The field values can be reading using the SOAP service \"QueryBusinessUserIn\".</p>\n</li>\n<li>Each user with business partner assignment now has a so-called template company so they can transfer central changes made to company address data using transaction SUCOMP to the workplace addresses of the business partners as well. The address number and the type of the template company are saved in the new fields TEMPLATE_ORGADDR and TEMPLATE_ORGTYPE in the table USR21. The following changes result from the new template company:<br/> </li>\n<ol>\n<li>Transaction <strong>SUCOMP</strong>:</li>\n<ol>\n<li>With the button <em>'Usage in Users (Shift+F8)</em>', the system now also finds users with business partner assignment which have entered the specified company as a template company.<br/> </li>\n<li>Assignments of users to a template company also result in the following: this company <span>cannot</span> be deleted.<br/>When you try to delete such a company, the system issues the new error message 01 663 '<em>Company Address &amp;1 is still used as Template Company</em>'.<br/>During the deletion of such a company in a CUA central system, this check can be carried out only in the SAP S/4HANA, on-premise edition child system.<br/> </li>\n<li>The transfer of changed company address data to the company address components of the workplace addresses of the linked business partners is automatically executed in the local client. To do this, use the new pushbutton '<em>Save and adopt Employees (SHIFT+F9)</em>' on the maintenance screen of a company address. The form of saving the company address known to date does <span>not</span> adjust the company address components of the workplace addresses of the linked business partners.<br/>If you change the company address data in a CUA central system, the change to the company address components of the workplace addresses of the relevant business partners of the IDoc inbound processing of the CCLONE IDoc is carried out in the SAP S/4HANA, on-premise edition child systems. <br/>If problems occur during this mass processing of users with business partner assignment (for example: lock or authorization problems), they are saved in the new table USZBVPROTCU. In addition, the errors are displayed in a dialog box. If these errors occur during the IDoc inbound processing of the CCLONE IDoc, the system also returns the new message 01 668 '<em>Error while changing Workplace Address Data at Business Partners</em>' to the central CUA system. You can find them in transaction SCUL in the changed company address. <br/>With the new pushbutton '<em>Try Again to Adjust the Workplace Address Data (Shift +F9)</em>' on the initial screen of transaction SUCOMP, you can subsequently make the failed adjustments for the users <span>in the local client</span>.<br/>With the pushbutton '<em>Adopt Workplace Addresses of all related Employees (Ctrl+F9)</em>', you can force the current data of the specified company address to be transferred to <span>all</span> company address components of the workplace addresses of the linked business partners.<br/>You can also make all of the above adjustments <span>to the</span> company address components of workplace addresses of the relevant business partners using the new report RSUID_COMPANY_TO_EEWA. <br/> </li>\n</ol>\n<li>Transaction <strong>SU01/SU10</strong>:</li>\n<ol>\n<li>The pushbutton '<em>Assign Other Company Address</em>' in the section '<em>Company</em>' on the '<em>Address</em>' tab in transaction <strong>SU01</strong> has the following effect for users with business partner assignment <span>and</span> a workplace address: data from the new company address is read, and the company address component of the workplace address of the business partner is overwritten as a result. The new company address for this user is defined as a company template at the same time.<br/>In transaction <strong>SU10</strong>, you can find this function on the 'Address' tab in change mode.<br/>Programmatically speaking, you can also make this change using <strong>BAPI_USER_CHANGE</strong> or <strong>IDENTITY_MODIFY</strong>. You do not have to change anything for the call of both function modules.<br/><br/> </li>\n</ol></ol>\n<li>For <span>users with business partner assignment</span>, the field 'Short Description' can be changed only using the SAP Fiori app 'Maintain Employees', Human Capital Management (HCM), or similar applications for employee maintenance. For all previous forms of user maintenance, such as transactions SU01 or the function modules <strong>BAPI_USER_CHANGE</strong> or <strong>IDENTITY_MODIFY</strong>, the system ignores changes to the short description. When this is attempted, the system issues or returns the new message SUID01 083 '<em>Description can be changed using Business Partner Transactions only</em>'. This message has a descriptive long text.</li>\n</ol>", "noteVersion": 3, "refer_note": [{"note": "2570961", "noteTitle": "2570961 - Simplification item S4TWL - Business User Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You need information about Business User Management in SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Business User Management in SAP S/4HANA is mandatory and is automatically activated during the conversion from SAP Business Suite to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ul>\n<li><a href=\"#what_has_changed_in_business_user_management\" target=\"_self\">What Has Changed in Business User Management in SAP S/4HANA?</a></li>\n<li><a href=\"#what_is_the_difference_between_business_users_and_su01_users\" target=\"_self\">What is the difference between business users and SU01 users?</a></li>\n<li><a href=\"#what_is_the_business_impact_of_that_change\" target=\"_self\">What is the business impact of that change?</a></li>\n<li><a href=\"#limitations\" target=\"_self\">Limitations</a></li>\n<ul>\n<li><a href=\"#limitations_user_management\" target=\"_self\">User Management</a></li>\n<li><a href=\"#limitations_business_partner\" target=\"_self\">Business Partner</a></li>\n<li><a href=\"#limitations_human_capital_management\" target=\"_self\">Human Capital Management (HCM)</a></li>\n</ul>\n<li><a href=\"#hcm_integration_active_or_inactive_what_does_it_mean\" target=\"_self\">﻿HCM Integration active or inactive: What does it mean?</a></li>\n<ul>\n<li><a href=\"#hcm_integration_switch_system_table_t77s0\" target=\"_self\">HCM Integration Switch (System Table T77S0)</a></li>\n<li><a href=\"#external_resources_switch_system_table_t77s0\" target=\"_self\">External Resources Switch (System Table T77S0)</a></li>\n</ul>\n<li><a href=\"#how_to_create_business_users_in_sap_s4hana\" target=\"_self\">﻿How to create Business Users in SAP S/4HANA</a></li>\n<ul>\n<li><a href=\"#how_to_create_business_users_hcm_integration_is_active\" target=\"_self\">HCM Integration is Active</a></li>\n<li><a href=\"#how_to_create_business_users_hcm_integration_is_inactive\" target=\"_self\">HCM Integration is Inactive</a></li>\n</ul>\n<li><a href=\"#maintenance_of_the_business_user_workplace_address\" target=\"_self\">Maintenance of the Business User Workplace Address</a></li>\n<li><a href=\"#conversion\" target=\"_self\">Conversion</a></li>\n<li><a href=\"#how_to_determine_relevance\" target=\"_self\">How to Determine Relevance</a> </li>\n</ul>\n<p> </p>\n<p><strong><a name=\"what_has_changed_in_business_user_management\" target=\"_blank\"></a>﻿What Has Changed in Business User Management in SAP S/4HANA?</strong></p>\n<p>SAP S/4HANA is introducing a new identity model for business users, which is based on the \"principle of one\". A business user is defined as a natural person who is represented by a business partner and a link to a user in the system. Business users interact with the software in the context of a business process, for example, in the role of a purchaser, a sales representative, or a production planner.</p>\n<p>In SAP S/4HANA, the business partner is the natural person who maintains the user data for the person, work center, and communication-related information. For logon, changing the user ID (BNAME) is no longer supported. However, one person can still log on with a user alias or email. The user alias or email can be changed (see SAP Note <a href=\"/notes/2883989\" target=\"_blank\">2883989</a>). The identity model for business users ensures correct auditing, workflows, and jobs. It also avoids errors in documents after a change of the user ID (BNAME). Auditors require a consistent history of all documents that contains the user ID (BNAME).</p>\n<p>SAP S/4HANA Business User Management enables and supports the entire life cycle maintenance of business users such as organizational changes, change of employment, or retirement. A user in SAP S/4HANA has a one-to-one relationship with a corresponding business partner (natural person). This reduces redundant maintenance and prevents outdated information.</p>\n<p> </p>\n<p><strong><a name=\"what_is_the_difference_between_business_users_and_su01_users\" target=\"_blank\"></a>﻿What is the difference between business users and SU01 users?</strong></p>\n<p>The business user is a SU01 user, but also has a one-to-one relation to the corresponding business partner. This relationship is time independent and cannot be changed anymore. For more details about important user related constraints see SAP Note <a href=\"/notes/2571544\" target=\"_blank\">2571544</a>.</p>\n<p>The business user concept is used in many new applications in SAP S/4HANA. SU01 users with Classic Address (Identity Address Type 00 - User's Old Type 3 Address) lead to limitations because the new business user model is a prerequisite for many business applications. As soon as Fiori apps are activated and used, business users are mandatory (for example Teams, CreditAnalyst in Credit Management or Situations).</p>\n<p>The business partner contains the personal information, for example private address, workplace address, bank details, vendor and customer related data. The business partner and SU01 user share personal details and workplace address related data. The advantage of the new business user model is that the entire lifecycle of that person works without redundant maintenance of user address data. Business users can still be managed using transaction SU01, Central User Administration or identity management systems.</p>\n<p>In the SAP Business Suite, you can use transaction BP (Business Partner Maintenance) to assign users to business partners. In SAP S/4HANA this is not possible anymore to avoid an inconsistent data model for business users. Existing business partners cannot be converted to business users, because of Data Protection and Privacy (DPP). Already existing business partners could have been part of a distribution scenario.</p>\n<p> </p>\n<p><strong><a name=\"what_is_the_business_impact_of_that_change\" target=\"_blank\"></a>﻿What is the business impact of that change?</strong></p>\n<p>With the new business user model in SAP S/4HANA we have a clear maintenance ownership. It can be owned by Human Capital Management (HCM), Business Partner (BP) or User Management. The ownership of HCM is only relevant when HCM integration is active. (More details about HCM integration see section <a href=\"#hcm_integration_active_or_inactive_what_does_it_mean\" target=\"_self\">HCM Integration active or inactive: What does it mean?</a>)</p>\n<p>Using the example of SU01 the following data categories exist:</p>\n<ol>\n<li><strong>Person:</strong> The personal data for the business user is derived from the corresponding business partner. In case HCM integration is active this data is mapped from the corresponding employee of the HCM system (SAP SuccessFactors or SAP HCM).</li>\n<li><strong>Work Center:</strong> The work center data for the business user is derived from the workplace address of the corresponding business partner. In case HCM integration is active, the function and department fields are mapped by default from the corresponding employee of the HCM system (for customizing options, see SAP Note <a href=\"/notes/2548303\" target=\"_blank\">2548303</a>).</li>\n<li><strong>Communication:</strong> The communication data for the business user is derived from the workplace address of the corresponding business partner (for customizing options, see SAP Note <a href=\"/notes/2548303\" target=\"_blank\">2548303</a>).</li>\n<li><strong>Company:</strong> During the conversion of a SU01 user from SAP Business Suite (classic user) to a business user in SAP S/4HANA the company address is copied to the business partner workplace address.</li>\n</ol>\n<p> </p>\n<p><strong><a name=\"limitations\" target=\"_blank\"></a>﻿<a name=\"limitations_user_management\" target=\"_blank\"></a>﻿Limitations - User Management</strong></p>\n<p>Due to the new business user model the database view USER_ADDR can no longer be used in SAP S/4HANA. This view can only display the users with classic address but not the business users. It is not possible to enhance this view. Instead of the database view USER_ADDR use the search help USER_ADDR (which is equivalent to an official API for searching users). In exceptional cases, you can use the SAP internal CDS view P_USER_ADDR, but have in mind that it can be changed by SAP at any time. With the ABAP statement \"with privileged access\" the authorization check is ignored. As a reference, use the coding of the search help exit F4IF_USER_ADDR_SUID_EXIT.</p>\n<p> </p>\n<p><strong><a name=\"limitations_business_partner\" target=\"_blank\"></a>﻿Limitations - Business Partner</strong></p>\n<p>In general, business partners that are neither employee nor contingent workforce cannot be managed as business users.</p>\n<p>In transaction BP (Business Partner Maintenance) you can no longer assign SU01 users to business partners in SAP S/4HANA. This feature has been removed.</p>\n<p>You can no longer maintain the following fields in transaction BP (Business Partner Maintenance):</p>\n<ul>\n<li>Personnel Number (BUS000EMPL-EMPLOYEE_ID)</li>\n<li>User (BUS000EMPL-USERNAME), for example, shown for role category WFM001 (Resource)</li>\n<li>Internet User (BUS000EMPL-INTERNETUSER), for example, shown for role category BUP005 (Internet User)</li>\n</ul>\n<p>You can no longer assign the roles belonging to the following role categories in transaction BP:</p>\n<ul>\n<li>Employee (BUP003)</li>\n<li>Freelancer (BBP010) → Contingent worker</li>\n<li>External Resource / Service Performer (BBP005) →  Contingent worker with additional supplier relation (staffed from external supplier)</li>\n<li>Collaboration Business User (BUP012)</li>\n<li>Resource (WFM001) → Only needed for SAP Portfolio and Project Management (component PPM-PFM-RES).</li>\n</ul>\n<p>This means you have to create the business partners in one of the mentioned role categories. (New Procedures see section <a href=\"#how_to_create_business_users_in_sap_s4hana\" target=\"_self\">How to create Business Users in SAP S/4HANA</a>)</p>\n<p>Existing business partners cannot be expanded by the following role categories later on: Employee, Freelancer, Service Performer. The reason is business partners with the referenced roles have special behavior regarding:</p>\n<ul>\n<li>Authorization management (driven by GDPR/DPP requirements)</li>\n<li>Replication/distribution (no replication from SAP S/4HANA to any system)</li>\n</ul>\n<p>For business partners which are assigned to one of these role categories, the fields of the following field groups cannot be maintained via transaction BP or the customer/supplier master Fiori UIs (independent from whether HCM integration is active or inactive):</p>\n<ul>\n<li>Field group 1: Partner Number (+ Role for Change Doc. Display)</li>\n<li>Field group 8: Form of Address</li>\n<li>Field group 9: Bank Details (for bank detail’s ID HC*)</li>\n<li>Field group 25: Person: First Name</li>\n<li>Field group 26: Person: Academic Title</li>\n<li>Field group 29: Person: Correspondence Language</li>\n<li>Field group 30: Person: Complete Name Line</li>\n<li>Field group 33: Person: Gender</li>\n<li>Field group 38: Person: Birth Name</li>\n<li>Field group 41: Person: Middle Name</li>\n<li>Field group 43: Person: Initials of Person</li>\n<li>Field group 54: Person: Last Name</li>\n<li>Field group 61: Address: Address Usage (HCM001, HCM002). Note that the address data can be edited in transaction BP but will be overwritten by the periodically scheduled sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL.</li>\n</ul>\n<p>Workplace address (table MOM052) attributes such as phone number or email address are not displayed in transaction BP. There are no plans to enhance transaction BP as of now. Please use transaction SU01 to display the workplace address attributes.</p>\n<p>Relationships of relationship category BUR025 (Service Provider) cannot be maintained for roles of role category BBP005 (Service Performer).</p>\n<p>Business partners which are assigned to one of these role categories cannot be maintained using SAP Master Data Governance for business partners, customers or suppliers.</p>\n<p>The usage of such workforce business partners in SAP Master Data Governance is also restricted:</p>\n<ul>\n<li>In partner function maintenance: Only the ID of the workforce business partners is visible, and navigation to that business partner is not possible.</li>\n<li>In business partner relationships: Relationships to workforce business partners are not visible and thus can’t be maintained.</li>\n</ul>\n<p> </p>\n<p><strong><a name=\"limitations_human_capital_management\" target=\"_blank\"></a>﻿Limitations - Human Capital Management (HCM)</strong></p>\n<p>In case of multiple employments (concurrent / global employment / country transfer) only the main employment is allowed to have a user assigned. Additional user assignments are not allowed.</p>\n<p> </p>\n<p><strong><a name=\"hcm_integration_active_or_inactive_what_does_it_mean\" target=\"_blank\"></a>﻿HCM Integration active or inactive: What does it mean?</strong></p>\n<p>HCM integration active means that you rely on the HR mini master (HR infotype based PERNR data model including the PA-Tables). This HR mini master can be locally maintained (for example via transaction PA30 or PA40) or via real integration scenarios with SAP SuccessFactors Employee Central or an external (third party) HCM system.</p>\n<p>If you don't need the HR mini master you should deactivate HCM integration.</p>\n<p>Based on the HCM integration, business users are handled the following way:</p>\n<ul>\n<li>If the HCM integration is active, it is mandatory to schedule the sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL regularly as a background job so that the business users are automatically synchronized with the PA-Tables.<br/>You can maintain business users via</li>\n<ul>\n<li>Transaction PA30 or PA40</li>\n<li>HCM integration.</li>\n</ul>\n<li>If the HCM integration is inactive, you can maintain business users via</li>\n<ul>\n<li>Fiori app Maintain Employees</li>\n<li>Fiori app Maintain External Resources</li>\n<li>SOAP Service ManageBusinessUserIn</li>\n<li>Report RFS4BPU_IMPORT_BUSINESS_USER.</li>\n</ul>\n</ul>\n<p>An example of why we need the HCM integration:</p>\n<p>For using the standard SD partner functions for employees, it is necessary to maintain the employees with the features of the HCM compatibility pack in SAP S/4HANA.<br/>It is not sufficient to maintain persons without active HCM integration (see SAP Note <a href=\"/notes/2763781\" target=\"_blank\">2763781</a>).</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td colspan=\"5\"><strong><a name=\"hcm_integration_switch_system_table_t77s0\" target=\"_blank\"></a>﻿HCM Integration Switch (System Table T77S0)</strong></td>\n</tr>\n<tr>\n<td>Description</td>\n<td>Group (GRPID)</td>\n<td>Sem. abbr. (SEMID)</td>\n<td><strong>HCM Integration inactive</strong></td>\n<td><strong>HCM Integration active</strong></td>\n</tr>\n<tr>\n<td>Activate HCM Integration</td>\n<td>HRALX</td>\n<td>HRAC</td>\n<td><strong>SPACE</strong> (Blank)</td>\n<td><strong>X</strong></td>\n</tr>\n<tr>\n<td>Integration P-BP Activated</td>\n<td>HRALX</td>\n<td>PBPON</td>\n<td><strong>OFF</strong></td>\n<td><strong>ON</strong></td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><a name=\"external_resources_switch_system_table_t77s0\" target=\"_blank\"></a>﻿HCM Integration for External Resources (BBP005) is set by the External Resources Switch. It interacts with the HCM Integration Switch as follows:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td colspan=\"7\"><strong>External Resources Switch (System Table T77S0)</strong></td>\n</tr>\n<tr>\n<td>Description</td>\n<td>Group (GRPID)</td>\n<td>Sem. abbr. (SEMID)</td>\n<td><strong>HCM Integration inactive</strong></td>\n<td><strong><strong>HCM Integration i</strong>nactive</strong></td>\n<td><strong><strong><strong>HCM Integration i</strong>nactive</strong></strong></td>\n<td><strong><strong><strong><strong>HCM Integration a</strong>ctive</strong></strong></strong></td>\n</tr>\n<tr>\n<td>Activate External Resources Switch</td>\n<td>HRALX</td>\n<td>PEXON</td>\n<td><strong><strong>OFF</strong></strong></td>\n<td><strong><strong>OFF</strong></strong></td>\n<td><strong><strong><strong>ON</strong></strong></strong></td>\n<td><strong><strong><strong><strong>ON</strong></strong></strong></strong></td>\n</tr>\n<tr>\n<td>Activate HCM Integration</td>\n<td>HRALX</td>\n<td>HRAC</td>\n<td><strong><strong><strong>SPACE</strong></strong></strong> (Blank)</td>\n<td><strong><strong><strong>X</strong></strong></strong></td>\n<td><strong><strong>SPACE</strong></strong> (Blank)</td>\n<td><strong>X</strong></td>\n</tr>\n<tr>\n<td>Integration P-BP Activated</td>\n<td>HRALX</td>\n<td>PBPON</td>\n<td><strong>OFF</strong></td>\n<td><strong>ON</strong></td>\n<td><strong>OFF</strong></td>\n<td><strong>ON</strong></td>\n</tr>\n</tbody>\n</table></div>\n<p><strong> </strong></p>\n<p><strong><a name=\"how_to_create_business_users_in_sap_s4hana\" target=\"_blank\"></a>﻿﻿﻿﻿<a name=\"how_to_create_business_users_hcm_integration_is_active\" target=\"_blank\"></a>﻿How to create Business Users in SAP S/4HANA - HCM Integration is Active</strong></p>\n<p>The HR mini master is the origin of business users (see HCM simplification SAP Note <a href=\"/notes/2340095\" target=\"_blank\">2340095</a>). The user assignment in the HR mini master has to be maintained in infotype 105 subtype 0001.</p>\n<p>The creation of business users is managed by the periodically scheduled sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL. This report performs the following steps:</p>\n<ol>\n<li>Creation of business partner in one role of the role categories Employee (BUP003), Freelancer (BBP010) or External Resource / Service Performer (BBP005) with</li>\n<ul>\n<li>private address</li>\n<li>bank details</li>\n<li>workplace address</li>\n<li>customer/vendor related data (see <a href=\"https://help.sap.com/doc/saphelp_crm60/********/en-US/24/6d62c210f841f1af3cb85070b67338/content.htm\" target=\"_blank\">Customer Vendor Integration [CVI]</a>)</li>\n<li>relationships</li>\n</ul>\n<li>Assignment of SU01 user. If the user doesn't exist, then the assignment takes place once the SU01 user is created.</li>\n</ol>\n<p>Errors of the sync report are stored in the application log. The application log can be accessed via transaction SLG1 (or SLGD) for object SHCM_EE_INTEGRATION, Subobject BUPA_SYNC (filter via External ID field to Personnel Number PERNR).</p>\n<p>Exception:</p>\n<p>The creation of Collaboration Business User (BUP012) and Resource (WFM001) does not work with sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL. They can only be created by using the following Fiori apps:</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Fiori ID</strong></td>\n<td><strong>App Title</strong></td>\n<td><strong>Business Partner Role Category</strong></td>\n<td><strong>As Of Release</strong></td>\n<td><strong>Comments</strong></td>\n</tr>\n<tr>\n<td>F4911</td>\n<td>Maintain Collaboration Users</td>\n<td>BUP012</td>\n<td>2020 FPS01</td>\n<td> </td>\n</tr>\n<tr>\n<td>F3505</td>\n<td>Maintain Resources</td>\n<td>WFM001</td>\n<td>1809 FPS01</td>\n<td>Only needed for SAP Portfolio and Project Management (component PPM-PFM-RES).</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong> </strong></p>\n<p><strong><a name=\"how_to_create_business_users_hcm_integration_is_inactive\" target=\"_blank\"></a>﻿How to create Business Users in SAP S/4HANA - HCM Integration is Inactive</strong></p>\n<p>There are three options to create business users:</p>\n<ul>\n<li>Business users can be created in SAP S/4HANA by using the following Fiori apps (including CSV import for mass processing):</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Fiori ID</strong></td>\n<td><strong>App Title</strong></td>\n<td><strong>Business Partner Role Category</strong></td>\n<td><strong>As Of Release</strong></td>\n<td><strong>Comments</strong></td>\n</tr>\n<tr>\n<td>F2288A</td>\n<td>Maintain Employees</td>\n<td>BUP003</td>\n<td>2020 FPS01</td>\n<td> </td>\n</tr>\n<tr>\n<td>F2288</td>\n<td>Maintain Employees</td>\n<td>BUP003</td>\n<td>1709 FPS02</td>\n<td>Replaced by Fiori app F2288A.</td>\n</tr>\n<tr>\n<td>F4911</td>\n<td>Maintain Collaboration Users</td>\n<td>BUP012</td>\n<td>2020 FPS01</td>\n<td> </td>\n</tr>\n<tr>\n<td>F3505A</td>\n<td>Maintain External Resources</td>\n<td>BBP005</td>\n<td>2020 FPS01</td>\n<td> </td>\n</tr>\n<tr>\n<td>F3505</td>\n<td>Maintain Resources</td>\n<td>WFM001</td>\n<td>1809 FPS01</td>\n<td>Only needed for SAP Portfolio and Project Management (component PPM-PFM-RES).</td>\n</tr>\n</tbody>\n</table></div>\n<ul>\n<li>For mass maintenance, you can also use the report RFS4BPU_IMPORT_BUSINESS_USER (As of SAP S/4HANA 1809). For SAP S/4HANA 1610 you can use report RFS4BPU_EMPLOYEE_UPLOAD - see SAP Note <a href=\"/notes/2567604\" target=\"_blank\">2567604</a>.</li>\n<li>SOAP Service ManageBusinessUserIn - can be set up via SOA Manager (Transaction SOAMANAGER).</li>\n</ul>\n<p> </p>\n<p><strong><a name=\"maintenance_of_the_business_user_workplace_address\" target=\"_blank\"></a>﻿Maintenance of the Business User Workplace Address</strong></p>\n<p>There are three possible application areas to maintain the workplace address of the business user:</p>\n<ul>\n<li>Human Resources (HR)</li>\n<li>Business Partner Management (BP)</li>\n<li>User Management (US)</li>\n</ul>\n<p>SAP S/4HANA 1709 FPS02 provides a configuration view that defines the maintenance source of the workplace address attributes. SAP delivers a default proposal for both variants, HCM integration active/inactive. See SAP Note <a href=\"/notes/2548303\" target=\"_blank\">2548303</a> for details. This configuration ensures that updates to the workplace address are restricted to one dedicated application area.</p>\n<p>The configuration view TBZ_V_EEWA_SRC can be set via Transaction SPRO (SAP Reference IMG) Under 'SAP Business Partner' at node 'Additional customizing for SAP Business Partner' (see SAP Note <a href=\"/notes/2591138\" target=\"_blank\">2591138</a>).</p>\n<p>The configuration view has the following constraints:</p>\n<ul>\n<li>If a field is configured for the application area User Management and the Central User Administration (CUA) is active, the maintenance rules of the CUA apply. (Transaction SCUM)</li>\n<li>If the HCM integration switch is active, BP is not applicable as maintenance source.</li>\n<li>If the HCM integration switch is inactive, HR is not applicable as maintenance source.</li>\n<li>The location part of the workplace address (company address - transaction SUCOMP) is not part of configuration view TBZ_V_EEWA_SRC because it can only be maintained by User Management. (See SAP Note <a href=\"/notes/2571544\" target=\"_blank\">2571544</a>)</li>\n</ul>\n<p> </p>\n<p><strong><a name=\"conversion\" target=\"_blank\"></a>﻿Conversion</strong></p>\n<p>If HCM integration is active and the conversion from SAP Business Suite to SAP S/4HANA is done, employees are automatically converted to business users via the periodically scheduled sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL.</p>\n<p>If HCM integration is inactive:</p>\n<ul>\n<li>Business partners that exist in an ERP system with user linkage via table HRP1001 from BP-CP-US are automatically converted to business users in SUM-Phase as of SAP S/4HANA 1809 (see conversion report RFS4BPU_CONVERT_BUSINESS_USER).</li>\n<li>Business partners that exist in an ERP system with user linkage via table BP001 are automatically converted to business users in Silent Data Migration (SDM) as of SAP S/4HANA 2020 (see class CL_SDM_BUPA_BPU_BP001).</li>\n</ul>\n<p> </p>\n<p><strong><a name=\"how_to_determine_relevance\" target=\"_blank\"></a>﻿How to Determine Relevance</strong></p>\n<p>Simplification item S4TWL - Business User Management is relevant for all SAP S/4HANA conversion customers.</p>", "noteVersion": 48}]}, {"note": "2548303", "noteTitle": "2548303 - S/4HANA: Configuration of maintenance for workplace address", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are in your S/4HANA On Premise system. The maintenance of the address data for users with a business partner assignment is not possible in user management (transaction SU01, function module BAPI_USER_CHANGE, Central User Administration or an identity management system). Maintenance in transaction BP is not possible, either.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Workplace address TBZ_V_EEWA_SRC EEWA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Missing functionality.</p>\n<p>A new business user model exists in S/4HANA On Premise. A business user is a natural person who is depicted in the system by means of a business partner and a (preset) link to a user in the system. A business user takes part in business processes in the role \"Purchaser\" or \"Production Planner\", for example.</p>\n<p>A business user can be created in human resources (HR) management and its external interfaces or using the SAP Fiori \"Maintain Employees\" application. A workplace address is assigned to each business user.</p>\n<p>With the maintenance view TBZ_V_EEWA_SRC, it is not possible to change the workplace address at the same time using different applications. When changes are made to the workplace address, a check takes place on the basis of the maintenance view TBZ_V_EEWA_SRC to determine whether these changes are permitted.</p>\n<p>There are three application areas that enable the maintenance of the workplace address:</p>\n<ul>\n<li>Human resources management (HR)</li>\n<li>Business partner management (BP)</li>\n<li>User management (US)</li>\n</ul>\n<p>The following restrictions apply for the maintenance view TBZ_V_EEWA_SRC:</p>\n<ul>\n<li>If Central User Administration (CUA) is active, the configuration of the CUA has higher priority than the configuration of the maintenance view TBZ_V_EEWA_SRC.</li>\n<li>If human resources (HR) management is not active, HR cannot be selected as the maintenance source in the maintenance view, either.</li>\n<li>If the business user consists only of a business partner and workplace address, and no user has yet been assigned, the configuration of the maintenance view TBZ_V_EEWA_SRC is ignored.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Import the specified support package. For technical reasons, correction instructions cannot be provided.</p>\n<p>This support package makes the new maintenance view TBZ_V_EEWA_SRC available. You can use it to choose the maintenance autonomy of individual nodes or attributes.<br/>To do so, call transaction SM30 and choose the maintenance view TBZ_V_EEWA_SRC.</p>\n<p>Caution:</p>\n<p>The maintenance autonomy of the central address data of the business partner (employee) cannot be set, and continues to remain in the maintenance autonomy of HR. Update this data in HR. The regularly scheduled report /SHCM/RH_SYNC_BUPA_FROM_EMP takes on the synchronization with the business partner.</p>\n<p>The maintenance of the organizational data of a business partner (employee) is not possible at present.</p>", "noteVersion": 1}, {"note": "2883989", "noteTitle": "2883989 - Conversion of clear user names in SAP S/4HANA OP", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>We do not recommend using clear user names. Therefore, the change of user names is not supported in SAP S/4HANA OnPrem. If a clear user name is used anyway, such as a last name, it may be necessary to change the user name as the result of marriage or divorce, for example.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Clear user name, user ID</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>If clear user names are used and HR integration is active, you can implement the customer enhancement using the BAdI FS4BPU_B_BUS_USER_CUST_ENH. The customer enhancement cannot be used if HR integration is inactive because the user name change cannot be propagated through infotype 0105, subtype 001.</p>\n<p>An example implementation is maintained in the BAdI.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Important information: If you have to implement this SAP Note only because there are dependencies to another SAP Note, do not perform the specified manual steps. In this case, implement only the correction instructions.</p>\n<p>If you require the customer enhancement as described in the symptom: Implement the correction instructions to enable the customer enhancement. Then read and follow the BAdI documentation of the BAdI FS4BPU_B_BUS_USER_CUST_ENH. The BAdI documentation also mentions an example implementation that you can use and adjust to suit your requirements.</p>\n<p>Prerequisites for changing the user name: In infotype 105 subtype 0001, you must delimit the old user and enter the new user with a suitable validity. In addition, the new user must not yet be assigned to any other business partner (classic user).</p>", "noteVersion": 20}, {"note": "2751389", "noteTitle": "2751389 - MDG-BP/C/S: Remove BP with Employee Data in S/4", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>To adhere to the requirements of SAP Note 2570961 - \"Simplification item S4TWL - Business User Management\" - SAP MDG, Central Governance needs to restrict the maintenance of employees in S/4.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Master Data Governance, Central Governance</p>\n<p>MDGBP, MDGC, MDGS, MDG-C, MDG-S</p>\n<p>SAP MDG for Business Partners</p>\n<p>SAP MDG for Customers</p>\n<p>SAP MDG for Suppliers</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Apply the attached correction instructions or the related support package to the affected system(s).</p>", "noteVersion": 2}, {"note": "2763781", "noteTitle": "2763781 - SAP S/4HANA and SAP S/4HANA Cloud Private Edition: Use Sales Employee in SD without HR integration", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using SAP S/4HANA or SAP S/4HANA Cloud Private Edition.</p>\n<p>You want to use the employee-related partner functions (partner type PE) in SD documents, <br/>e.g. sales employee or employee responsible.<br/>In your system HR integration is switched off.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PE, VE, PER<PERSON>, Employee Responsible, Person Responsible, Infotype, role BUP003.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>New data model for Employee and Business User Management in SAP S/4HANA.<br/>For using the standard SD partner functions for employees, it is necessary to maintain the employees with the features of the HCM compatibility pack in SAP S/4HANA. <br/>It is not sufficient to maintain persons without active HR integration.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP recommends the following solution approach:</p>\n<ul>\n<li>Switch on the HR integration switch in SAP S/4HANA via the system table T77S0 with the configuration switches HRALX/HRAC and HRALX/PBPON.<br/>Please note: This is just a technical setting. No cross system activity is needed, and no external HCM system is mandatory (e.g. SAP SuccessFactors or SAP HCM).</li>\n<li>Manage the integration between employee (and employee-like persons) and the business user with the periodically scheduled sync report /SHCM/RH_SYNC_BUPA_FROM_EMPL. You need to ensure that the employee records contain the corresponding user assignments (infotype 0105 in HR master data).</li>\n<li>All technical preparation steps, also for the HR integration switch, are described in the IMG activity <br/>Personnel Management --&gt; SAP S/4HANA for Human Resources --&gt; Synchronization of Business Partner for SAP HCM Employee Role.</li>\n<li>Use the HCM transactions (PA30 and PA40) for managing employee master data as part of the Compatibility Package (see SAP Note <a href=\"/notes/2269324\" target=\"_blank\">2269324</a>). After the UserID is written to the infotype 0105 of the employee records, you can assign the corresponding user. The sync report will then ensure that the business user will be completed.</li>\n<li>In case transactions PA30/PA40 do not work out-of-the-box due to missing basic HCM configuration you can use the report RPUTRL00 to fill transport requests from client 000 with the necessary entries of control tables and configuration tables. These transport requests have to be imported to the target client where the HCM transactions shall be used.</li>\n</ul>\n<p>For additional detailed explanation, please read note # <a href=\"/notes/2570961\" target=\"_blank\">2570961</a>.</p>\n<p>Furthermore you can have a look at the SAP blog <a href=\"https://blogs.sap.com/2017/04/21/sap-s4hana-using-sales-employee-with-business-partner-customer-role/\" target=\"_blank\">https://blogs.sap.com/2017/04/21/sap-s4hana-using-sales-employee-with-business-partner-customer-role/</a></p>\n<p>How to use HCM in S/4HANA after 2025 (end of validity of compatibility pack):</p>\n<p>After 31.12.2025, the compatibility pack for SAP HCM is no longer applicable.<br/> For using HCM functionality as of 01.01.2026<br/> - you either activate business function H4S4_1: By activating this business function, your HCM no longer runs in compatibility mode, but automatically becomes SAP Human Capital Management for SAP S/4HANA, which requires special licenses. See notes<br/><a href=\"/notes/3091160\" target=\"_blank\" title=\"3091160  - SAP HCM for SAP S/4HANA – General Approach and Strategy\">3091160 - SAP HCM for SAP S/4HANA – General Approach and Strategy</a><br/>and<br/><a href=\"https://me.sap.com/notes/3200799\" target=\"_blank\">3200799 - Business Function for SAP HCM for SAP S/4HANA - SAP for Me</a> for details.</p>\n<p>- Or you just use the HR mini master with a restricted set of info types. In that case no additional licenses are needed.<br/> See note <a href=\"https://me.sap.com/notes/3369920\" target=\"_blank\">3369920 - SAP S/4HANA without HCM - use of HR mini-master (subset of HCM infotypes) - SAP for Me</a> for details.<br/> The HR mini master includes (among others) the following infotypes:<br/> 0000 – Actions<br/> 0001 - Organizational Assignment<br/> 0002 - Personal Data<br/> 0006 – Addresses<br/> 0105 – Communication</p>\n<p>In addition, Infotype<br/> 0900 - Sales Data<br/> can also be used without additional HCM license.</p>", "noteVersion": 6, "refer_note": [{"note": "2269324", "noteTitle": "2269324 - Compatibility Scope Matrix for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Through the Compatibility Packages (CP) listed in the attachment \"Compatibility Scope Matrix\", SAP provides a limited use right to SAP S/4HANA on-premise customers to run certain classic SAP ERP solutions on their SAP S/4HANA installation. Condition is that these customers have licensed the applicable solutions as set forth in their License Agreements. Compatibility Pack use rights may apply to selected private cloud deployments as well, without the prerequisite of an on-premise classic license. Please refer to the respective Service Description Guide for details.</p>\n<p>This use right expires on Dec 31, 2025, and is available to installed-base as well as net-new customers.</p>\n<p>The <a href=\"https://news.sap.com/2020/02/sap-s4hana-maintenance-2040-clarity-choice-sap-business-suite-7/\" target=\"_blank\">announcement</a> about the extension of maintenance for Business Suite solutions has no influence on the end of compatibility pack use rights - they will be terminated after 2025.<sup>(1)</sup></p>\n<p>Besides reading the attached documents, SAP recommends the latest SAP Community webinar on the topic: <a href=\"https://www.youtube.com/live/muPrV5J7ffM?feature=shared\" target=\"_blank\">https://www.youtube.com/live/muPrV5J7ffM</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA Compatibility Scope Matrix, Way Forward</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In the “Attachments” section, the “Overview Presentation” and “Detail FAQ” documents explain the “why”, “what” and “how” of CPs. For functional details, please also refer to the Feature Scope Description (FSD) of SAP S/4HANA, <a href=\"https://help.sap.com/doc/e2048712f0ab45e791e6d15ba5e20c68/latest/\" target=\"_blank\">latest version</a> (or through http://help.sap.com/s4hana), chapter 5.</p>\n<p>The “Way Forward” presentation and overview list, also under “Attachments”, provide information about solution alternatives in the perpetual scope of SAP S/4HANA. They describe the status of each Compatibility Pack and provide links to more detailed information about its strategy. This information is provided via SAP standard documentation, such as the Roadmaps and the Innovation Discovery. The “Item ID” helps to cross-reference between the original, descriptive CP matrix and the future-oriented “Way Forward” spreadsheet.</p>\n<p>This blog <a href=\"https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/\" target=\"_blank\">https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/</a> provides more details about the procedure for Compatibility Packs after their use right expiry in 2025/2030.</p>\n<p>SAP is planning regular updates of the attachments. All forward-looking information is non-binding and with preview character. ​The released SAP Roadmaps are the proper source of information.</p>\n<p>For questions and feedback about this note and its content, please create a customer message on component XX-SER-REL with the prefix ‘CompScope’​.</p>\n<p>(1) In the exceptional cases of CS, LE-TRA and PP-PI, the usage right to their respective compatibility pack items (cf. matrix) terminates at the end of 2030.</p>", "noteVersion": 84}, {"note": "854390", "noteTitle": "854390 - FAQ: Use of the sales representative in SD without HR", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><ol>1. Does the HR module have to be in use in order to use the sales representative as a partner in the SD module?</ol> <ol>2. What must be done to make the necessary HR tables available?</ol> <ol>3. There are problems when creating, changing or displaying sales representatives. What is the procedure for troubleshooting?</ol> <ol>4. Why does the sales document not display the name or other data for the sales representative?</ol> <ol>5. Why is the personal data (such as telephone number) of the sales representative not displayed in the sales document?</ol> <p><br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>PAL1, PAL2, PAL3, sales representative, representative, employee, sales support, RPUTRL00<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>-<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p></p> <ol>1. <b>Question: </b><b>Does t</b><b>he HR module </b><b>have to</b><b> be in use in order to use the sales </b><b>representative</b><b> as a partner</b><b> in the SD module</b><b>?</b></ol> <p><br/>Answer: No, only an HR master record is needed for the use of the sales representative. If you do not have the Human Resources department (application HR) in use, you can transport the HR tables from the client 000 to the target client that you need for creating, changing or displaying a sales representative.<br/></p> <ol>2. <b>Question: </b><b>What</b><b> must be </b><b>done to make </b><b>the necessary HR tables available?</b></ol> <p><br/>Answer: In order to transport the necessary HR tables from the client 000 to the target client, two transfer orders must be available. The called RPUTRL00 report writes the Customizing tables as well as the system tables to separate transfer orders. You must then use Transaction SCC1 (called in client 000) to copy both transfers to the target client.<br/></p> <ol>3. <b>Question: </b><b>There are p</b><b>roblems </b><b>when </b><b>creatin</b><b>g</b><b>, </b><b>c</b><b>hang</b><b>ing</b><b> or display </b><b>ing</b><b> sales r</b><b>epresentatives</b><b>. </b><b>What is </b><b>the procedure for troubleshooting?</b></ol> <p><br/>Answer: Note 28418 gives a detailed description of the necessary Customizing settings and approaches for troubleshooting and correcting the problem.<br/></p> <ol>4. <b>Question: Why </b><b>does </b><b>the sales document </b><b>not </b><b>display </b><b>the name or </b><b>other </b><b>d</b><b>ata for the sales</b><b>representative</b><b>?</b></ol> <p><br/>Answer: Check whether the infotype 0001 (Transaction PAL3, Organizational Assignment) is maintained correctly for the sales representative.<br/></p> <ol>5. <b>Question</b><b>: Why </b><b>is </b><b>the </b><b>p</b><b>ersonal </b> <b>data</b><b> (</b><b>such </b><b>as </b><b>telephone number</b><b>) of the sales</b><b>representative</b><b> not display</b><b>ed in the sales</b><b> document?</b></ol> <p><br/>Answer: If a personnel number is used in a document (for example, as sales representative), the system does not display the private address of the sales representative. This is the standard behavior. Data from the HR must not be displayed in the document. The sales representative can be assigned to a sales group and the sales group can be assigned to a sales office. The address of the sales office is then used for the sales representative.<br/><br/>With the following modification, data, such as telephone number or fax-number, is displayed in the sales document from the private address (infotype 0006):<br/><br/>Function module SD_REPRESENTANT_GET_DATA<br/>...<br/>  DATA: SD_ADDRESSNUMBER LIKE TVKO-ADRNR,<br/>        WA_ADDRESS_SELECTION LIKE ADDR1_SEL,<br/>        WA_ADDRESS_VALUE     LIKE ADDR1_VAL.<br/>* &lt;&lt;&lt;&lt; INSERTATION START &gt;&gt;&gt;&gt;<br/>  data: lv_comm_data_source type char1 value space.<br/><br/>  if &lt;my_field&gt; = &lt;my_value&gt;.   \"&lt;&lt;--THERE YOU CAN DEFINE OWN IF-CLAUSE<br/>     lv_comm_data_source = 'X'. \"comm.data from private<br/>  endif.<br/>* &lt;&lt;&lt;&lt; INSERTATION END &gt;&gt;&gt;&gt;<br/><br/>* get telephone number (handy) of employee from infotyp 105<br/>  CALL FUNCTION 'HR_REPRESENTANT_GET_DATA'<br/>       EXPORTING<br/>            P_PERNR          = FI_PERNR<br/>            P_DATE_FROM      = FI_DATE_FROM<br/>* &lt;&lt;&lt;&lt; DELETION START &gt;&gt;&gt;&gt;<br/>            P_PRIVATE_ADDRESS = SPACE<br/>* &lt;&lt;&lt;&lt; DELETION END &gt;&gt;&gt;&gt;<br/>* &lt;&lt;&lt;&lt; INSERTATION START &gt;&gt;&gt;&gt;<br/>            P_PRIVATE_ADDRESS = lv_comm_data_source<br/>* &lt;&lt;&lt;&lt; INSERTATION END &gt;&gt;&gt;&gt;<br/>...<br/><br/>* determine work address from SD organization units<br/>  CLEAR SD_ADDRESSNUMBER.<br/>* &lt;&lt;&lt;&lt; INSERTATION START &gt;&gt;&gt;&gt;<br/>   if lv_comm_data_source = 'X'.   \"comm.data from private<br/>      clear: FE_HRMR_REP-SALES_OFFICE, FE_HRMR_REP-SALES_ORG.<br/>   endif.<br/>* &lt;&lt;&lt;&lt; INSERTATION END &gt;&gt;&gt;&gt;<br/>...<br/><br/><b>However, note the following</b><b>:</b><br/>These source code corrections are a modification. Among other things this means that these changes are neither contained in a following SAP Support Package nor scheduled for a future development cycle. In particular, this modification is not covered by SAP standard support (for more detailed information, see Note 170183).<br/></p></div>", "noteVersion": 1}, {"note": "3091160", "noteTitle": "3091160 - SAP HCM for SAP S/4HANA – General Approach and Strategy", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This SAP Note provides you with advance information on how to use the planned SAP Human Capital Management for SAP S/4HANA (in the following called SAP HCM for SAP S/4HANA), release 2022 incl. SAP HCM in RISE with SAP S/4HANA Cloud, private edition. For any specific HCM local version solution, please refer to SAP Note <a href=\"/notes/3217773\" target=\"_blank\">3217773</a>.</p>\n<p>In general, from a technical perspective the transition to SAP HCM for SAP S/4HANA will be seamless/non-disruptive. If none of the simplifications affect your current SAP HCM configuration, there is no action required. This SAP Note is relevant for you if you plan to carry out one of the following scenarios:</p>\n<p><strong>Scenario 1 - Target SAP HCM for SAP S/4HANA in SAP S/4HANA 2022 and beyond</strong></p>\n<p>You are doing a</p>\n<ol>\n<li>system conversion to SAP S/4HANA 2022, and want to use SAP HCM for SAP S/4HANA</li>\n<li>system upgrade from SAP S/4HANA, former releases using SAP HCM in compatibility scope, to SAP S/4HANA 2022 and want to use SAP HCM for SAP S/4HANA</li>\n</ol>\n<p><strong>Scenario 2 - Target SAP HCM Compatibility Pack in <strong>SAP S/4HANA 2022<strong> and beyond</strong></strong></strong></p>\n<p>You are doing a</p>\n<ol start=\"3\">\n<li>system conversion to SAP S/4HANA 2022 and want to use SAP HCM in compatibility scope</li>\n<li>system upgrade from SAP S/4HANA former release using SAP HCM in compatibility scope to SAP S/4HANA 2022 and want to continue using SAP HCM in compatibility scope.</li>\n</ol>\n<p><strong>Scenario 3 - Target SAP HCM in RISE with SAP S/4HANA Cloud, private edition</strong></p>\n<p>You are doing a</p>\n<ol start=\"5\">\n<li>system conversion or a system upgrade to SAP S/4HANA 2022 Cloud, private edition, and want to use the subscribed SAP HCM functionalities.</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Human Capital Management, SAP HCM, SAP HCM for SAP S/4HANA, H4S4, Compatibility Package, Compatibility Pack, Compatibility Scope, conversion, upgrade, simplification item</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>SAP HCM for SAP S/4HANA will be part of the initial shipment of SAP S/4HANA 2022. It is not the legal successor of SAP HCM. It contains additional functionality that can be viewed in the SAP <a href=\"https://roadmaps.sap.com/board?PRODUCT=73554900100800000266&amp;range=CURRENT-LAST&amp;PROCESS=E2E_TWM#Q4%202021\" target=\"_blank\">Roadmap Explorer</a>. For more information and the included functionality, see <a href=\"https://blogs.sap.com/2019/09/25/sap-human-capital-management-for-sap-s4hana-on-premise-edition-updates-available-in-2022-with-a-technical-co-deployment-in-sap-s4hana/\" target=\"_blank\">this</a> blog.</p>\n<p>As part of SAP Business Suite wide simplification incl. the 'principle of one strategy', some of the known SAP HCM ERP functionality will not be part of SAP HCM for SAP S/4HANA. Special constellations also exist under SAP S/4HANA Cloud, private edition. To inform customers about simplification before a conversion or upgrade to SAP HCM for SAP S/4 HANA, this SAP Note serves as a summary for listing the affected functionalities. These functionalities are listed in the following table. If there is a go-to solution, you can find it in the fourth column ‘Alternative’. In the fifth column you find the Business Impact Note related to the simplification and additional SAP Notes for further details. If SAP Notes are listed under the term \"already obsoletes\", the functionality was already obsolete in SAP ERP HCM. However, the corresponding SAP notes can be found. The Simplification Items are part of the SAP S/4HANA Readiness Check and SAP Simplification Item Catalog.</p>\n<p><strong><span>NOTE</span></strong><strong>: Starting with </strong><strong>SAP S/4HANA 2025, the H4S4_1 Business Function is always on. For more information and for lower releases please refer to SAP Note: <a href=\"https://me.sap.com/notes/3443536\" target=\"_blank\">3443536</a></strong></p>\n<p><strong>Please keep in mind</strong>: This SAP Note can be changed and enhanced from time to time with new information.</p>\n<p><em>Table 1 - Simplifications in SAP HCM for SAP S/4HANA for<strong> SAP S/4HANA 2022 on-premise and <em><strong>SAP S/4HANA Cloud, private edition</strong></em></strong></em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Sub Component (ERP functionality unavailable in SAP S/4HANA)</strong></td>\n<td><strong>Simplification Item-ID</strong></td>\n<td><strong>Compatibility Scope Matrix Item ID</strong></td>\n<td><strong>Status</strong></td>\n<td><strong>Alternative</strong></td>\n<td><strong>Related information</strong></td>\n</tr>\n<tr>\n<td>Appraisal Systems (PA-PD-AP)</td>\n<td>SI9</td>\n<td>418</td>\n<td>alternative exists</td>\n<td>\n<p>Objective Settings &amp; Appraisals (PA-PD-PM) or SAP SuccessFactors Performance &amp; Goals</p>\n<p>Objective Settings &amp; Appraisals (PA-PD-PM) <strong>is not part of SAP S/4HANA Cloud, private edition <strong>(see table 2)</strong></strong></p>\n</td>\n<td>\n<p>SAP Note<br/><a href=\"/notes/3224316\" target=\"_blank\">3224316</a></p>\n</td>\n</tr>\n<tr>\n<td>Career and Succession Planning (PA-PD-SP)</td>\n<td>SI9</td>\n<td>422</td>\n<td>alternative exists</td>\n<td>\n<p>Talent Management &amp; Development (PA-TM) or SAP SuccessFactors Succession &amp; Development</p>\n<p>Talent Management &amp; Development (PA-TM) <strong>is not part of SAP S/4HANA Cloud, private edition</strong> <strong>(see table 2)</strong></p>\n</td>\n<td>SAP Note<br/><a href=\"/notes/3224316\" target=\"_blank\">3224316</a></td>\n</tr>\n<tr>\n<td>Compensation Management (PA-CM)</td>\n<td>SI10</td>\n<td>416</td>\n<td>alternative exists</td>\n<td>\n<p>Enterprise Compensation Management (PA-EC) or SAP SuccessFactors Compensation</p>\n<p>Enterprise Compensation Management (PA-EC)<strong> is not part of SAP S/4HANA Cloud, private edition</strong> <strong>(see table 2)</strong></p>\n</td>\n<td>SAP Note <br/><a href=\"/notes/3224317\" target=\"_blank\">3224317</a></td>\n</tr>\n<tr>\n<td>Cost Planning (PA-CM-CP)</td>\n<td>SI11</td>\n<td>132; 137</td>\n<td>alternative exists</td>\n<td>Cost Planning and Simulation (PA-CP)</td>\n<td>SAP Note<br/><a href=\"/notes/3224288\" target=\"_blank\">3224288</a></td>\n</tr>\n<tr>\n<td>Development Plans (PA-PD-DP)</td>\n<td>SI9</td>\n<td>423</td>\n<td>alternative exists</td>\n<td>\n<p>Talent Management &amp; Development (PA-TM) or SAP SuccessFactors Succession &amp; Development</p>\n<p>Talent Management &amp; Development (PA-TM) <strong>is not part of SAP S/4HANA Cloud, private edition <strong>(see table 2)</strong></strong></p>\n</td>\n<td>\n<p>SAP Note<br/><a href=\"/notes/3224316\" target=\"_blank\">3224316</a><a href=\"/notes/3224316\" target=\"_blank\"> <br/></a><a href=\"/notes/3257544\" target=\"_blank\">3257544</a></p>\n</td>\n</tr>\n<tr>\n<td>Employee Interaction Center (PA-EIC)</td>\n<td>SI12</td>\n<td>-</td>\n<td>no alternative planned</td>\n<td>Interaction center / help desk applications offered by SAP S/4HANA (CRM-S4-IC); SAP CRM (CRM-CIC); SAP Service Cloud; SAP SuccessFactors Employee Central Service Center</td>\n<td>SAP Note<br/><a href=\"/notes/3224334\" target=\"_blank\">3224334</a></td>\n</tr>\n<tr>\n<td>\n<p>Enterprise Services - Personnel Administration (PA-PA-SOA) and Time Management SOA (PT-SOA)</p>\n</td>\n<td>SI13</td>\n<td>419</td>\n<td>no alternative planned</td>\n<td> </td>\n<td>SAP Note<br/><a href=\"/notes/3224335\" target=\"_blank\">3224335</a></td>\n</tr>\n<tr>\n<td>\n<p>ESS/MSS:</p>\n<p>ESS on ITS<br/>ESS/MSS on WD Java<br/>MSS based on HTMLB<br/>MSS List reports based on ODP<br/>ESS/MSS UI5 (from HR Renewal)</p>\n</td>\n<td>SI5</td>\n<td>417; 420</td>\n<td>alternative exists</td>\n<td>Employee Self Service (ESS); Manager Self Service (MSS) based on Fiori and WDA; SAP SuccessFactors Employee Central and SAP SuccessFactors Time Tracking</td>\n<td>SAP Note<br/> <a href=\"/notes/2383879\" target=\"_blank\">2383879</a></td>\n</tr>\n<tr>\n<td>Expert Finder (PA-XF)</td>\n<td>SI15</td>\n<td>-</td>\n<td>no alternative planned</td>\n<td> </td>\n<td>SAP Note<br/><a href=\"/notes/3224305\" target=\"_blank\">3224305</a></td>\n</tr>\n<tr>\n<td>HR Funds and Position Management 'old' parts of (PA-PM)</td>\n<td>SI16</td>\n<td>424</td>\n<td>alternative exists</td>\n<td>Position Budgeting and Control (PA-PM-PB)</td>\n<td>\n<p>SAP Note<br/><a href=\"/notes/3224336\" target=\"_blank\">3224336</a></p>\n</td>\n</tr>\n<tr>\n<td>HCM Processes &amp; Forms with Adobe Forms</td>\n<td>SI17</td>\n<td>137</td>\n<td>alternative exists</td>\n<td>\n<p><strong>Java / Adobe Forms </strong></p>\n<div class=\"OutlineElement Ltr SCXW130086871 BCX9\">\n<p class=\"Paragraph SCXW130086871 BCX9\">HCM Processes and Forms JAVA and Adobe (PA-AS) functionality is only available in SAP ERP HCM. HCM Processes and Forms JAVA is not part of SAP S/4HANA as NW AS Java is no longer supported. See SAP Note <a href=\"/notes/2560753\" target=\"_blank\">2560753</a>.</p>\n</div>\n<div class=\"OutlineElement Ltr SCXW130086871 BCX9\">\n<p class=\"Paragraph SCXW130086871 BCX9\"><strong>WebDynpro ABAP / Adobe Forms </strong></p>\n</div>\n<div class=\"OutlineElement Ltr SCXW130086871 BCX9\">\n<p class=\"Paragraph SCXW130086871 BCX9\">HCM Processes and Forms based WebDynpro ABAP with Adobe form technology is part SAP HCM in Compatibility Pack in SAP S/4HANA only, which comes with limited usage rights. See SAP Note <a href=\"/notes/3006433\" target=\"_blank\">3006433</a></p>\n</div>\n</td>\n<td>SAP Note<br/><a href=\"/notes/3224318\" target=\"_blank\">3224318</a><br/><br/>Already obsolete - see also SAP Note <a href=\"/notes/3006433\" target=\"_blank\">3006433</a></td>\n</tr>\n<tr>\n<td>HIS Reporting (PA-IS)</td>\n<td>SI25</td>\n<td>132; 137</td>\n<td>no alternative planned</td>\n<td> </td>\n<td>SAP Note<br/><a href=\"/notes/3224340\" target=\"_blank\">3224340</a></td>\n</tr>\n<tr>\n<td>HR Renewal Landing Pages 'Lanes' (PA-PAO) and Suite Page Builder </td>\n<td>SI18</td>\n<td>137</td>\n<td>alternative exists</td>\n<td>SAP Fiori Launchpad</td>\n<td>SAP Note<br/><a href=\"/notes/3224337\" target=\"_blank\">3224337</a></td>\n</tr>\n<tr>\n<td>Manager's Desktop (PA-MA)</td>\n<td>SI7</td>\n<td>420</td>\n<td>alternative exists</td>\n<td>Manager Self Service (MSS) based on Fiori and WDA</td>\n<td>SAP Note<br/><a href=\"/notes/3224315\" target=\"_blank\">3224315</a></td>\n</tr>\n<tr>\n<td>\n<p>Packages (PA-PA; PA-PA-XX):</p>\n<p>PDEL<br/>PZ1R<br/>PBAS_BPO</p>\n</td>\n<td>SI19</td>\n<td>132; 137</td>\n<td>no alternative planned</td>\n<td> </td>\n<td>SAP Note<br/><a href=\"/notes/3224319\" target=\"_blank\">3224319</a></td>\n</tr>\n<tr>\n<td>\n<p>Recruiting (PA-RC)</p>\n</td>\n<td>SI20</td>\n<td>421</td>\n<td>alternative exists</td>\n<td>\n<p>SAP E-Recruiting (PA-ER); SAP SuccessFactors Recruiting</p>\n<p>SAP E-Recruiting (PA-ER)<strong> is not part of SAP S/4HANA Cloud, private edition <strong>(see table 2)</strong></strong></p>\n</td>\n<td>SAP Note<br/><a href=\"/notes/3224353\" target=\"_blank\">3224353</a></td>\n</tr>\n<tr>\n<td>\n<p>Reports (PY-XX)</p>\n<p>RPCS0000<br/>RPCSC000</p>\n</td>\n<td>SI21</td>\n<td>117</td>\n<td>alternative exists</td>\n<td>\n<p>The reports RPCS0000 and RPCSC000 must be replaced with process model(s) using the process workbench (transactions PEST and PUST). This enables to execute all process steps of the payroll process in a structured way. Each step can be customized as parallelizable. When running a process, the framework will schedule several jobs running in parallel. If a program is not parallelizable, please contact SAP.</p>\n</td>\n<td>\n<p>SAP Notes<br/><a href=\"/notes/3224325\" target=\"_blank\">3224325</a><br/><a href=\"/notes/2078937\" target=\"_blank\">2078937<br/></a><a href=\"/notes/3006263\" target=\"_blank\">3006263</a><br/><br/>See <a href=\"https://help.sap.com/viewer/0bf5ad6f9018461b94d26080e0db6084/6.18.latest/en-US/50a1dd53c88c424de10000000a174cb4.html\" target=\"_blank\">SAP Help</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Reports (PY-XX-DT) - Part of Package PCPO_DEPREC)</p>\n<p>RPCIPE00_OLD<br/>RPCIPE00_OLD_CE<br/>RPCIPE01CE <br/>&amp; TemSe Funtionality<br/>RPCIPI00<br/>RPCIPX00<br/>RPCIPT00<br/>RPCIPL00<br/>RPCIPM00</p>\n</td>\n<td>SI21</td>\n<td>117</td>\n<td>alternative exists</td>\n<td>\n<p>RPCIPE00/01 <br/>RPCIPE01_CE (see SAP Note <a href=\"/notes/3146771\" target=\"_blank\">3146771</a>)<br/>Without TemSe functionality<br/><br/>Reports RPCIPE00/RPCIPE01 and RPCIPE01_CE Distribution via ALE (SAP S/4HANA OP) works</p>\n<p>Web Service in case FIN runs in S/4HANA Cloud for payroll posting</p>\n</td>\n<td>SAP Notes<br/><a href=\"/notes/3224325\" target=\"_blank\">3224325</a><br/> <a href=\"/notes/3145947\" target=\"_blank\">3145947</a></td>\n</tr>\n<tr>\n<td>\n<p>Shared Service Framework for HCM-Integration (CRM-IC-HCM-BF)</p>\n</td>\n<td>SI22</td>\n<td>118</td>\n<td>no alternative planned</td>\n<td>SAP Shared Service Framework for S/4HANA; SAP CRM; SAP SuccessFactors Employee Central Service Center</td>\n<td>SAP Note<br/><a href=\"/notes/3224338\" target=\"_blank\">3224338</a></td>\n</tr>\n<tr>\n<td>\n<p>Structural Graphic (BC-BMT-OM-GRF)</p>\n</td>\n<td>SI25</td>\n<td>132; 137</td>\n<td>no alternative planned</td>\n<td> </td>\n<td>SAP Note<br/><a href=\"/notes/3224340\" target=\"_blank\">3224340</a></td>\n</tr>\n<tr>\n<td>\n<p>Parts of Time Management</p>\n<p>Time Data Recording and Management (PT-RC):</p>\n<p><br/>Infotypes (0005, 0083, 2005) Reports, and Function modules + Views on Infotypes (2050, 2051, 2052), see listed SAP Notes for details</p>\n</td>\n<td>SI26</td>\n<td>132; 137</td>\n<td>alternative exists</td>\n<td>\n<p>IT2006 'Absence Quotas'</p>\n<p>IT0416 'Time Quota Compensation'</p>\n<p>IT2002 'Attendances'</p>\r\nSee SAP Note for further details</td>\n<td>\n<p>SAP Notes<br/><a href=\"/notes/3224341\" target=\"_blank\">3224341</a><br/><a href=\"/notes/3139908\" target=\"_blank\">3139908<br/></a><a href=\"/notes/3346202\" target=\"_blank\">3346202</a></p>\n<p>Already obsolete - see also:<br/>SAP Note <a href=\"/notes/399468\" target=\"_blank\">399468<br/></a>SAP Note <a href=\"/notes/185963\" target=\"_blank\">185963<br/></a>SAP Note <a href=\"/notes/314669\" target=\"_blank\">314669</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Parts of Time Management</p>\n<p>Time Evaluation (PT-EV):</p>\r\nInfotypes (0005, 0083, 2005), operations, and reports, see listed SAP Notes for details</td>\n<td>SI26</td>\n<td>132; 137</td>\n<td>alternative exists</td>\n<td>See SAP Notes for details</td>\n<td>\n<p>SAP Notes<br/><a href=\"/notes/3224341\" target=\"_blank\">3224341</a><br/> <a href=\"/notes/3138281\" target=\"_blank\">3138281</a></p>\n<p>Already obsolete - see also:<br/>SAP Note <a href=\"/notes/386690\" target=\"_blank\" title=\"RPTIME00: Operation HRS=Q\">386690<br/></a>SAP Note <a href=\"/notes/952860\" target=\"_blank\">952860<br/></a>SAP Note <a href=\"/notes/185963\" target=\"_blank\">185963</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Parts of Time Management</p>\n<p>Integration of external time recording systems: Interface CC1 (KK1) is no longer available (PT-RC)</p>\n</td>\n<td>SI26</td>\n<td>132; 137</td>\n<td>alternative exists</td>\n<td>HR-PDC Interface</td>\n<td>SAP Notes<br/><a href=\"/notes/3224341\" target=\"_blank\">3224341</a><br/> <a href=\"/notes/3140931\" target=\"_blank\">3140931</a>\n<p>Already obsolete - see also: <br/>SAP Note <a href=\"/notes/211918\" target=\"_blank\">211918<br/></a><span>SAP Note </span><a href=\"/notes/2879911\" target=\"_blank\">2879911</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Parts of Time Management</p>\n<p>BAdIs no longer available in attendance/absence counting (PT-RC):</p>\n<ul>\n<li>TIM00ATTABSCOUNTING</li>\n<li>TIM00ABSCOUNTRY_DAY</li>\n</ul>\n</td>\n<td>SI26</td>\n<td>132; 137</td>\n<td>alternative exists</td>\n<td>BAdI: PT_ABS_ATT_COUNTRY </td>\n<td>\n<p>SAP Notes<br/><a href=\"/notes/3224341\" target=\"_blank\">3224341</a><br/> <a href=\"/notes/3138644\" target=\"_blank\">3138644</a></p>\n<p>Already obsolete - see also: <br/>SAP Note <a href=\"/notes/383750\" target=\"_blank\">383750</a></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>Training and Event Management (PE)</p>\n</td>\n<td>SI23</td>\n<td>135; 136</td>\n<td>alternative exists</td>\n<td>\n<p>SAP Learning Solution (PE-LSO); SAP SuccessFactors Learning</p>\n<p>SAP Learning Solution (PE-LSO)<strong> is not part of SAP S/4HANA Cloud, private edition (see table 2)</strong></p>\n</td>\n<td>SAP Note<br/><a href=\"/notes/3224320\" target=\"_blank\">3224320</a></td>\n</tr>\n<tr>\n<td>\n<p>Workforce Viewer (PA-PAO-WFV)</p>\n</td>\n<td>SI24</td>\n<td>137</td>\n<td>no alternative planned</td>\n<td>My Team (Fiori App) or SAP SuccessFactors Employee Central (for e.g. customers having an already existing hybrid HR architecture)</td>\n<td>SAP Note<br/><a href=\"/notes/3224339\" target=\"_blank\">3224339</a></td>\n</tr>\n</tbody>\n</table></div>\n<p><em>Table 2 - <strong>In RISE with SAP S/4HANA Cloud, private edition </strong>the following Sub Components are <span><strong>not</strong></span> part of the offering</em></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Sub Component <br/>(ERP functionality <strong>unavailable <br/>in SAP S/4HANA PCE</strong>)</strong></td>\n<td>\n<p><strong>Simplification</strong></p>\n<p><strong> Item-ID</strong></p>\n</td>\n<td><strong>Compatibility Scope <br/>Matrix Item ID</strong></td>\n<td><strong>Status</strong></td>\n<td><strong>Alternative</strong></td>\n<td><strong>Related information</strong></td>\n</tr>\n<tr>\n<td>SAP E-Recruiting (PA-ER)</td>\n<td>SI4</td>\n<td>134; 421</td>\n<td>alternative exists</td>\n<td>\n<p>SAP SuccessFactors Recruiting</p>\n</td>\n<td>\n<p>SAP Note <a href=\"/notes/2383888\" target=\"_blank\">2383888</a></p>\n</td>\n</tr>\n<tr>\n<td>SAP Learning Solution (PE-LSO)</td>\n<td> </td>\n<td>135; 136</td>\n<td>alternative exists</td>\n<td>SAP SuccessFactors Learning</td>\n<td>SAP Note <a href=\"/notes/2383837\" target=\"_blank\">2383837</a></td>\n</tr>\n<tr>\n<td>\n<p>SAP Talent Management solutions</p>\n<ul>\n<li>Objective Settings and Appraisals (PA-PD-PM)</li>\n<li>Talent Management &amp; Development (PA-TM)</li>\n<li>Enterprise Compensation Management (PA-EC)</li>\n</ul>\n</td>\n<td> </td>\n<td>416; 422; 423 </td>\n<td>alternative exists</td>\n<td>SAP SuccessFactors Performance &amp; Goals; SAP SuccessFactors Succession &amp; Development</td>\n<td> </td>\n</tr>\n</tbody>\n</table></div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong><span><strong>NOTE</strong></span><strong><span>:</span> Starting with </strong><strong>SAP S/4HANA 2025, the H4S4_1 Business Function is always on. For more information and for lower releases please refer to SAP Note: <a href=\"https://me.sap.com/notes/3443536\" target=\"_blank\">3443536</a></strong></strong></strong></p>\n<p><strong>Scenario 1 - Target SAP HCM for SAP S/4HANA in SAP S/4HANA 2022 edition and beyond</strong></p>\n<p>If you are doing a system conversion (a) or upgrade (b) to SAP S/4HANA 2022 and want to use SAP HCM for SAP S/4HANA the following information must be taken into account:</p>\n<p>In the first step, a technical transition to SAP S/4HANA 2022 takes place. Technically, HCM is automatically in the so-called Compatibility Pack. Please keep in mind that already existing HCM simplifications can apply here as well. These can be found by executing a <a href=\"https://help.sap.com/docs/SAP_READINESS_CHECK\" target=\"_blank\">SAP Readiness Check for S/4HANA</a> or in the <a href=\"https://launchpad.support.sap.com/#/sic/overviewWithfilter?appArea=Human%20Resources&amp;targetProdValidity=73554900100900005134\" target=\"_blank\">Simplification Item Catalog</a>. See also the Compatibility Scope Matrix in SAP Note <a href=\"/notes/2269324\" target=\"_blank\">2269324</a>.</p>\n<p>In a second step business function for SAP HCM for SAP S/4HANA (H4S4_1) must be activated in the switch framework. Before switching on the business function H4S4_1 please read SAP Note <a href=\"/notes/3200799\" target=\"_blank\">3200799</a> carefully and check if simplifications apply. In general: The check can be executed before or after the conversion or upgrade to SAP S/4HANA 2022 with the SAP Readiness Check for S/4HANA. SAP HCM for SAP S/4HANA specific simplifications (listed in the tables above) are part of the SAP Readiness Check for S/4HANA. If simplifications apply, the affected functionalities must be replaced by their alternatives, if applicable.</p>\n<p>After switching on the business function, SAP HCM for SAP S/4HANA is activated and new features can be used. <strong>Please keep in mind:</strong> Simplified functionalities are technically deactivated. The activation of the business function cannot be undone as it is irreversible.</p>\n<p><strong>Scenario 2 - Target SAP HCM Compatibility Pack in <strong>SAP S/4HANA 2022 and beyond</strong></strong></p>\n<p>If you are doing a system conversion (c) or upgrade (d) to SAP S/4HANA 2022 and still want to use SAP HCM in compatibility scope you can continue to use all functionalities of the Compatibility Pack. Please keep in mind that usage rights of compatibility packages will expire as outlined in SAP Note <a href=\"/notes/2269324\" target=\"_blank\">2269324</a>. This means after this point in time the migration to SAP HCM for SAP S/4HANA and the other designated alternative functionalities outlined above must happen immediately with the technical conversion to SAP S/4HANA.</p>\n<p><strong>Scenario 3 - Target SAP HCM in RISE with SAP S/4HANA Cloud, private edition</strong></p>\n<p>SAP S/4HANA Cloud, private edition (e) consists of a bundle of SAP S/4HANA and other software products provided as a cloud service subscription. In case of SAP HCM there is a <strong>predefined range of HCM functionality which</strong> can be used. See attached PDF document. All <strong>Talent Solutions </strong>incl.<strong> </strong>SAP E-Recruiting and SAP Learning Solution <strong>are not part of this offering.</strong> See SAP Note <a href=\"/notes/2383888\" target=\"_blank\">2383888</a> and SAP Note <a href=\"/notes/2383837\" target=\"_blank\">2383837</a> and Table 2 of this SAP Note. The <strong>predefined range of HCM functionality</strong> corresponds with the functional scope of SAP HCM for SAP S/4HANA. Thus, the simplifications are also relevant for RISE with SAP S/4HANA Cloud, private edition customers. If you are affected by simplifications listed in <strong>Table 1 or Table 2</strong>, you have to ensure that the simplified functionalties are replaced by their go-to solutions until end of 2025. Starting with 1. Jan 2026 the adoption of SAP HCM for SAP S/4HANA is mandatory, also for SAP S/4HANA Cloud, private edition.</p>\n<p lang=\"en-US\"><strong>Further information:</strong></p>\n<p lang=\"en-US\">In general, simplified functionalities won't be accessible with SAP HCM for SAP S/4HANA or SAP HCM in RISE with SAP S/4HANA Cloud, private edition. This means: Unless otherwise stated in the SAP Notes for a simplification item, development objects (programs, methods, tables, reports, transactions, etc.) related to a simplification item are <strong>not deleted</strong>. Transactions and reports are currently blocked from access and IMG activities are hidden. Customers can still access tables manually, e.g. via SE11. However, SAP reserve the right to delete development objects in the future. Customers will be informed beforehand.</p>\n<p>For questions and feedback about this note and its content, please create a customer message on component XX-SER-REL with the prefix ‘CompScope’​.</p>", "noteVersion": 27}, {"note": "3369920", "noteTitle": "3369920 - SAP S/4HANA without HCM - use of HR mini-master (subset of HCM infotypes)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to know if you need to license SAP Core Human Capital Management for SAP S/4HANA if</p>\n<ul>\n<li>you use SAP S/4HANA without HCM and require the business partner in the \"Employee\" role, or</li>\n<li>you use a different HCM system (SAP SuccessFactors Employee Central), but you require a minimum amount of employee data for non-HCM processes in an SAP S/4HANA system and want to replicate this data in the system.</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA without HCM, HCM data replication with external HR system,  HR mini-master, business partner in S/4HANA,</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p lang=\"de\">You can create or replicate the following HCM infotypes (employee data) in an SAP S/4HANA system without licensing SAP Core Human Capital Management for SAP S/4HANA:</p>\n<p lang=\"de\"><span> PA mini-master infotypes:</span></p>\n<p lang=\"de\">0000 - Actions</p>\n<p lang=\"de\">0001 - Organizational Assignment</p>\n<p lang=\"de\">0002 - Personal Data</p>\n<p lang=\"de\">0006 - Addresses</p>\n<p lang=\"de\">0009 - Bank Details</p>\n<p lang=\"de\">0105 - Communication</p>\n<p lang=\"en-US\"><span>Additional (technically required) infotypes </span>can also be used without SAP Core Human Capital Management for SAP S/4HANA licenses:</p>\n<p lang=\"en-US\">0003 - Payroll Status</p>\n<p lang=\"de\">0712 - Main Personnel Assignment</p>\n<p lang=\"de\">3435 - Supplier</p>\n<p lang=\"en-US\"><span>Special cases</span>:</p>\n<ul>\n<li lang=\"en-US\">Infotype 0315 - Time Sheet Defaults: can be used for CATS, but without using the HCM Time Management infotypes (that is, CATS postings may be made only to non-HCM objects such as projects, WBS elements).</li>\n<li lang=\"en-US\">Infotype 900 - Sales Data: can be used for the Sales and Distribution (SD) component.</li>\n<li lang=\"en-US\">For SAP Travel Management for SAP S/4HANA</li>\n<ul>\n<li lang=\"en-US\">Infotype 0017 - Travel Privileges</li>\n<li lang=\"en-US\">Infotype 0027 - Cost Distribution  </li>\n</ul>\n</ul>\n<p><span> </span></p>", "noteVersion": 3}, {"note": "3200799", "noteTitle": "3200799 - Business Function for SAP HCM for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>After your conversion or upgrade to SAP S/4HANA Release 2022 or higher, you want to activate SAP Human Capital Management for SAP S/4HANA by switching on the business function for SAP Human Capital Management for SAP S/4HANA (H4S4_1).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>H4S4_1, SAP HCM for SAP S/4HANA, HCM simplification, HCM on Premise, Human Capital Management, Business Function</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>You can use the business function H4S4_1 to activate SAP Human Capital Management for SAP S/4HANA, which is available as of SAP S/4HANA Release 2022.</p>\n<p>Before you activate this business function, the following points must be considered:</p>\n<ul>\n<li>The H4S4_1 business function is <strong>NOT</strong> <strong>reversible. </strong>A certificate or license key is not requested by the system.</li>\n<li>With activation of the business function, your SAP HCM no longer runs in compatibility mode, but automatically becomes SAP Human Capital Management for SAP S/4HANA, which requires special licenses. If you have any questions about these licenses, please contact your SAP Account Executive.</li>\n<li>New functionalities will be available when you activate the business function. For more information, please refer to the documentation for the business function H4S4_1.</li>\n<li>After you activate the business function H4S4_1, certain simplified functionalities are no longer available. This means that if the business function H4S4_1 is activated and you then, e.g. execute a simplified transaction, you receive an error message.</li>\n<li>Using the so-called HR mini-master in an SAP S/4HANA system does not require licensing SAP HCM for SAP S/4HANA, even if the Business Function H4S4_1 is active. Please also refer to SAP Notes <a href=\"https://me.sap.com/notes/3369920\" target=\"_blank\">3369920</a> and <a href=\"https://me.sap.com/notes/3443536\" target=\"_blank\">3443536</a>. </li>\n</ul>\n<p>SAP Notes <a href=\"/notes/3091160\" target=\"_blank\">3091160</a> and <a href=\"/notes/3217773\" target=\"_blank\">3217773</a> contain a list of all simplified functionalities. Please also note that some of the listed functionalities are generally not available with SAP S/4HANA (for example: ESS/MSS on Java, ITS, …).</p>", "noteVersion": 6}]}], "activities": [{"Activity": "Data correction", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "check user data (as part of general business partner introduction)"}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Configure update channel for workplace address data If HR integration is active, configure and maintain employee master data"}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "If HR integration is active, run HCM business partner synchronization as described in  HCM simplification SAP Note 2340095 If HR integration is not active, create or upload person data wiht Fiori app \"Maintain employee\""}, {"Activity": "Fiori Implementation", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If HR integration is not active, implement Fiori app \"Maintain employee\""}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}