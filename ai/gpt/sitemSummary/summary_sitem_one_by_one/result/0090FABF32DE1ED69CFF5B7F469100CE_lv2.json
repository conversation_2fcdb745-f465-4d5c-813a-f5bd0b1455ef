{"guid": "0090FABF32DE1ED69CFF5B7F469100CE", "sitemId": "SI14: PPM_INTGR_SRM_01", "sitemTitle": "S4TWL - SRM Integration in SAP Portfolio and Project Management for SAP S/4HANA", "note": 2359354, "noteTitle": "2359354 - S4TWL - SRM Integration in SAP Portfolio and Project Management for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SRM, Supplier Relationship Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The SRM Integration for external service procurement is not available in SAP Portfolio and Project Management for SAP S/4HANA.</p>\n<p>SAP Note 2445069 provides a pre-conversion check for this feature.</p>\n<p><strong>Business Process related information</strong></p>\n<p>It is not possible anymore to order external materials and resources directly from Project Management through SRM.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>The SRM Integration related data cannot be migrated to SAP Portfolio and Project Management for SAP S/4HANA, therefore it is required to clean up the data before the system conversion in order to prevent any data loss. Archive the corresponding projects or delete not needed SRM Integration related data before you continue the system conversion.</p>", "noteVersion": 2, "refer_note": [{"note": "2445069", "noteTitle": "2445069 - S4TC CPRXRPM Master Check for S/4 System Conversion Checks  (new Simplification Item Checks)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Simplification Item Checks (SIC-checks) formerly called Pre-conversion checks for SAP Portfolio and Project Management need to be executed before the conversion to SAP Portfolio and Project Management for S/4HANA.</p>\n<p>As a follow up of the pre-conversion check or to prepare a migration, the currently used authorizations maybe need to be analyzed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 Conversion, S/4 Portfolio and Project Management <br/>Authorization Inheritance Information Migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the correction instructions in this SAP Note in order to enable a SIC-checks. Perform a detailed check via the new readyness check <a href=\"https://launchpad.support.sap.com/#readiness\" target=\"_blank\">https://launchpad.support.sap.com/#readiness</a> to make the system easily ready for the S/4HANA conversion.</p>\n<p><strong>SIC-checks to prepare a conversion to SAP Portfolio and Project Management for S/4HANA</strong></p>\n<p>The following checks will be executed during the SIC-check:</p>\n<p><span>Simplified ACLs (Access Control Lists) concept in SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2343374, below the messages that could be raised during the check.</p>\n<ul>\n<li>'Authorizations on Organizational unit level are not supported anymore (for details please check SAP Note 2343374).' (Check ID: PPM_FUNC_ACL_01_03)</li>\n<li>'Option \"None\" is not supported anymore on authorization level (for details please check SAP Note 2343374).'  (Check ID: PPM_FUNC_ACL_01_04)</li>\n<li>'Object Type-Related Activities for Authorization Check contain changes in fix activity hierarchy Admin -&gt; Write -&gt; Read (for details please check SAP Note 2343374).'  (Check ID: PPM_FUNC_ACL_01_01)</li>\n<li>'Hierarchies of User Groups to User Group for Authorization are only allowed up to a level of 3 (for details please check SAP Note 2343374).'  (Check ID: PPM_FUNC_ACL_01_02)</li>\n</ul>\n<p><span>Control Plans in SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2353885 and in consulting note 2363785, below the messages that could be raised during the check.</p>\n<ul>\n<li>'The control plan integration is not available anymore (for details please check SAP Note 2353885).' (Check ID: PPM_FUNC_CNTR_PLAN_01)</li>\n<li>'The control plan UI field customizing is deprecated (for details please check SAP Note 2363785).'(Check ID: PPM_FUNC_CNTR_PLAN_02)</li>\n</ul>\n<p><span>KM Documents in SAP Portfolio and Project Management for SAP S/4HANA<br/></span>Details can be found in SAP Note 2354985, below the messages that could be raised during the check.<span><br/></span></p>\n<ul>\n<li>'The usage of Knowledge Management (KM) for Document Management is no longer available (for details please check SAP Note 2354985).' (Check ID: PPM_INTGR_KM_DOC_01)</li>\n</ul>\n<p><span>cFolders not available anymore in SAP Portfolio and Project Management</span></p>\n<p>Details can be found in SAP Note 2348430, below the messages that could be raised during the check.</p>\n<ul>\n<li>'SAP cFolders is not available anymore (for details please check SAP Note 2348430).' (Check ID: PPM_FUNC_CFOLDERS_01)</li>\n</ul>\n<p><span>SRM Integration in SAP Portfolio and Project Management for SAP S/4HANA</span></p>\n<p><br/>Details can be found in SAP Note 2321885, below the messages that could be raised during the check.</p>\n<ul>\n<li>The SRM integration is not available anymore (for details please check SAP Note 2359354). (Check ID: PPM_INTGR_SRM_01)</li>\n</ul>\n<p><strong>Detail analysis of used authorizations</strong></p>\n<p>In addition, this SAP Note delivers the detailed readyness check, which can be used to further analyze the used authorizations. The section below describes how a customer can use the detailed readyness check.</p>\n<p><span>To identify </span><span>before</span> the conversion to SAP S/4 HANA if your definition of authorization in PPM is affected by the described changes, you can execute the detailed readyness check and inspect the execution result of the readyness check. The following execution results may be displayed (Changes to projects authorizations should be made before data migration in case return code is 4, return code 0 signals that all prerequisite for migration of Authorization Inheritance Information are met):</p>\n<p> a) 'Authorizations on Organizational unit level not supported anymore':</p>\n<p>Depending on the displayed client logon to the client of the system mentioned. Display the project element related to the following Object types DPO (Project), PPO (Phase), TTO (Task), CTO (Checklist), ITO (Checklist Item), RAG (Checklist reference) by navigating to the work center Project Management. Choose Service -&gt; Search. On the appearing search popup choose \"Search for:\" according to the Object type listed. Fill the listed EXTERNAL_ID (Number) into the Search Criteria field Project Number/Phase Number/Number and click \"Find button\". Click on the search result to display the project element detail screen. Navigate to the tab \"Authorizations\" -&gt; \"Organizational Units\". Be aware that the maintained Organizational Unit(s) are not considered in S/4 PPM.</p>\n<p><br/> b) 'Option \"None\" is not supported anymore on authorization level':</p>\n<p>Depending on the displayed client logon to the client of the system mentioned. Display the project element related to the following Object types DPO (Project), PPO (Phase), TTO (Task), CTO (Checklist), ITO (Checklist Item), RAG (Checklist reference) by navigating to the work center Project Management. Choose Service -&gt; Search. On the appearing search popup choose \"Search for:\" according to the Object type listed. Fill the listed EXTERNAL_ID (Number) into the Search Criteria field Project Number/Phase Number/Number and click \"Find button\". Click on the search result to display the project element detail screen. Navigate to the tabs</p>\n<p>\"Authorizations\" -&gt; \"Users\"<br/>\"Authorizations\" -&gt; \"User Groups\"<br/>\"Authorizations\" -&gt; \"Organizational Units\"<br/>\"Authorizations\" -&gt; \"Roles\"</p>\n<p>Be aware that the functionality maintained by checking the \"None\" flag is not supported in S/4 PPM anymore. Versions of a project are not listed in detail in the report protocol. In case versions to a project exist the protocol may contain duplicate entries related to a project number.</p>\n<p>The impact of the new concept (without 'no Auth') is that 'strictly confidential' documents after changing the authorizations in a project can now be accessed by users that should not have access according <br/>to business reasons. Be aware of that and take that into account when changing authorizations as result of pre-check or the authorization analysis report results.</p>\n<p><br/> c) 'Object Type-Related Activities for Authorization Check contain changes in fix activity hierarchy Admin -&gt; Write -&gt; Read'</p>\n<p>Depending on the displayed client logon to the client of the system mentioned. Call transaction ACO3 to display \"Object Type-Related Activities for Authorization Check\". Remember the content of column OBJECT_TYPE listed in the result of the pre-check report and click on the button \"Position ...\" of the view. Enter the OBJECT_TYPE into the field Object Category and confirm the value. Be aware that the Object Type-Related Activities for Authorization Check have a fixed activity hierarchy Admin -&gt; Write -&gt; Read'. This restiction is valid for the fiori UI's. There are no restrictions regarding the Web dynpro UI.</p>\n<p><br/> d) 'Hierarchies of User Groups to User Group for Authorization are only allowed up to a level of 3':</p>\n<p>Depending on the displayed client logon to the client of the system mentioned. Navigate to the work center \"Portfolio and Project Administration\" and choose \"Project User Groups\". Click button \"Open\" and enter the displayed User Group Name listed in the result of the report into the field \"Name\". Be aware that the User Group Hierarchy is only evaluated up to a level of 3.</p>\n<p>In order to maintain the project user groups call transaction /NNWBC and navigate to workcenter Portfolio and Project Administration -&gt; Project User Groups</p>\n<p> </p>", "noteVersion": 4, "refer_note": [{"note": "2358463", "noteTitle": "2358463 - S4TWL - Microsoft Project Import/Export in SAP Portfolio and Project Management for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP Portfolio and Project Management for SAP S/4HANA, the import/export function for Microsoft Project Client data has been changed. The system no longer uses the binary file format of Microsoft Project (.MPP). Instead the XML format for Microsoft Project is used. After downloading the the file, you can open it with you Microsoft Project application.</p>\n<p>Neither an ActiveX installation nor a definition of security lists for office controls is required.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>No effects on business processes are expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>No further actions are expected.</p>\n<p><strong>How to determine relevancy</strong></p>\n<p>This transition worklist item is relevant, if Microsoft Project Import or Export is used.</p>", "noteVersion": 11}, {"note": "2361657", "noteTitle": "2361657 - Moving from PPM Releases to SAP Portfolio and Project Management 1.0 for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><strong>Maintenance of SAP Portfolio and Project Management 1.0 for SAP S/4HANA ended 2021, so move to S/4HANA 1709, 1809, 1909, 2020 or 2021 instead.</strong></p>\n<p>******************************************************************************************************************************************</p>\n<p>You want to <strong>move </strong>from an SAP Portfolio and Project Management installation to a SAP Portfolio and Project Management 1.0 for SAP S/4HANA installation.</p>\n<p>In general, the following <strong>SAP Portfolio and Project Management start releases</strong> are supported:</p>\n<ul>\n<li>cProject Suite 4.0 (CPRXRPM 400)</li>\n<li>cProject Suite 4.5 (CPRXRPM 450_700)</li>\n<li>SAP RPM 4.5       (CPRXRPM 450_700) (regarding KM based document management, see below)</li>\n<li>SAP Portfolio and Project Management 5.0 (CPRXRPM 500_702)</li>\n<li>SAP Portfolio and Project Management 6.0 (CPRXRPM 600_740)</li>\n<li>SAP Portfolio and Project Management 6.1 (CPRXRPM 610_740)</li>\n</ul>\n<p><strong>There are different types of installation of the start release on the source system which require different types of move:</strong></p>\n<p>In general, see the <em>Moving to SAP S/4HANA</em> chapter under <a href=\"http://help.sap.com/disclaimer?site=http%3A%2F%2Fhelp.sap.com%2Fs4hana\" target=\"_blank\" title=\"Go to: http://help.sap.com/s4hana\">http://help.sap.com/s4hana</a> - SAP S/4HANA 1610  - <em>Getting Started With SAP S/4HANA 1610</em></p>\n<ul>\n<li>(A) Installation on top of SAP ERP 6.0, EhPx (for details, see <em>Moving to SAP S/4HANA </em>chapter mentioned above, e.g. Unicode systems only)</li>\n<li>(B) Installation on top of CRM, SCM, SRM, or on NW stand-alone.</li>\n</ul>\n<p>Only in case (A): A <strong>conversion</strong> to SAP Portfolio and Project Management 1.0 for SAP S/4HANA <strong>on the same system</strong> is possible. The conversion is supported by SAP standard tools.</p>\n<p>In case (B): A <strong>migration to </strong>a (new) installation of SAP Portfolio and Project Management 1.0 for SAP S/4HANA on <strong>a different system</strong> is required. <br/>(Remark: To achieve this, migrate your relevant data from the source system to the target system. As this is not covered by SAP standard functionality, set up a custom migration project.)</p>\n<p><strong>Please note:</strong></p>\n<ul>\n<li><strong>SAP Portfolio and Project Management 1.0 for SAP S/4HANA can only be installed on top of SAP S/4HANA 1610. </strong><br/><strong>An installation on NW stand-alone (without SAP S/4HANA 1610) is not supported.</strong><br/> </li>\n<li>SAP S/4HANA 1610 requires <strong>SAP NetWeaver 7.51 for SAP S/4HANA 1610</strong>.<br/> </li>\n<li>As a prerequisite for the <strong>conversion</strong>, <strong>perform the relevant <em>pre-checks</em> </strong>in your existing source system landscape for the software units you want to convert. For more information about performing the pre-checks, see the <em>Pre-Checks</em> chapter under <a href=\"http://help.sap.com/disclaimer?site=http%3A%2F%2Fhelp.sap.com%2Fs4hana\" target=\"_blank\" title=\"Go to: http://help.sap.com/s4hana\">http://help.sap.com/s4hana</a> - SAP S/4HANA 1610 - <em>Conversion Guide:<br/> <br/></em><strong>Pre-checks and custom code checks <br/></strong>When converting an existing SAP Business Suite system to SAP S/4HANA in your system landscape, you need to do some adaptations, for example, convert your existing business processes to the simplified SAP S/4HANA processes. Some of the SAP Business Suite processes are no longer supported, some have been changed and there are also new processes. How to convert your existing processes to the SAP S/4HANA processes, see <strong>Simplification List</strong> at <a href=\"http://help.sap.com/s4hana_op_1610\" target=\"_blank\">http://help.sap.com/s4hana_op_1610</a> - Simplification List. <br/>SAP provides <strong>pre-checks</strong> and custom code checks that you need to perform, before doing the conversion.<br/><strong> <br/>Recommendation <br/></strong>We recommend to run the Maintenance Planner, Pre-Check and Custom Code Migration worklists early on in the system conversion project, to get an overview of the adaptations your systems require during the conversion. For detailed information about these tasks, see the <em>Conversion Guide for SAP S/4HANA 1610</em> at <a href=\"http://help.sap.com/s4hana_op_1610\" target=\"_blank\">http://help.sap.com/s4hana_op_1610</a> - Product Documentation <br/>  <br/>We also recommend to read the SAP S/4HANA Cookbook at <a href=\"http://scn.sap.com/docs/DOC-64980\" target=\"_blank\">http://scn.sap.com/docs/DOC-64980</a><br/>   <br/>It is also highly recommended to <strong>perform the relevant <em>pre-checks</em> </strong>in your existing source system landscape before you start a <strong>migration</strong>.<br/> <br/>For the pre-checks for component CPRXRPM, see SAP Note <a href=\"/notes/2321885\" target=\"_blank\" title=\"2321885  - S4TC CPRXRPM Master Check for S/4 System Conversion Checks\">2321885 - S4TC CPRXRPM Master Check for S/4 System Conversion Checks</a>.<br/> </li>\n<li><strong>If the start release is SAP RPM 4.5</strong> (CPRXRPM 450_700) <strong>and Knowledge Management (KM) for Document Management has been used</strong> and these documents should also be moved: This is not possible in one step:</li>\n<ul>\n<li>First of all, an upgrade to one of the releases SAP Portfolio and Project Management 5.0/6.0/6.1 is required.</li>\n<li>Then, the switch from the KM based document management to a KPRO based solution has to be done, see SAP Note <a href=\"/notes/2354985\" target=\"_blank\">2354985</a>.</li>\n<li>Only afterwards, a move to a SAP Portfolio and Project Management 1.0 for SAP S/4HANA is possible.</li>\n</ul>\n</ul>\n<p> </p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>PPM CPRXRPM EPPM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You want to do a <strong>migration</strong> (i.e. case (B) above) from an SAP Portfolio and Project Management installation<strong> to a SAP Portfolio and Project Management 1.0 for SAP S/4HANA installation</strong>.</p>\n<p>The SAP Portfolio and Project Management start release on the source system is <span>not</span> installed on top of SAP ERP, so that <span>no conversion with automatic execution of XPRAs is supported</span>.</p>\n<p>The relevant data from the source system is already available in the target system. (Remark: To achieve this, migrate your relevant data from the source system to the target system. As this is not covered by SAP standard functionality, set up a custom migration project.)</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Only in case you want to do a <strong>migration from an SAP Portfolio and Project Management installation to a SAP Portfolio and Project Management 1.0 for SAP S/4HANA installation:</strong></p>\n<p>On the SAP Portfolio and Project Management 1.0 for SAP S/4HANA installation, you need to execute specific reports in the target system, depending on the release of the source system.</p>\n<p>If your <strong>source system has version cProject Suite 4.0 (CPRXRPM 400)</strong>, execute the following reports in the order as listed below:</p>\n<ul>\n<li>/RPM/XPRA_450_CUSTOMIZING</li>\n<li>/RPM/XPRA_450_ILINK_2_OBJLINK</li>\n<li>/RPM/XPRA_450_ITEM_HEADER</li>\n<li>/RPM/XPRA_450_OBJ_LINK_TYPE</li>\n<li>/RPM/XPRA_450_SMODEL_CONFIG</li>\n<li>R_DPR_OBJ_TYPE_CUST_XPRA_450</li>\n<li>R_DPR_OBL_GROUP_XPRA_450</li>\n<li>RPM/XPRA_500_DOC_TEMPL_AREA</li>\n<li>S/RPM/XPRA_500_OBL_OBJ_KEY</li>\n<li>/RPM/XPRA_500_RES_UPDATE_GUID</li>\n<li>/S4PPM/XPRA_S4MIG_PROJECT</li>\n<li>/S4PPM/XPRA_S4MIG_AUTH_REF</li>\n</ul>\n<p>If your <strong>source system has version cProject Suite 4.5 and/or SAP RPM 4.5  (CPRXRPM 450_700)</strong>, execute the following reports in the order as listed below:</p>\n<ul>\n<li>/RPM/XPRA_500_DOC_TEMPL_AREAS</li>\n<li>/RPM/XPRA_500_OBL_OBJ_KEY</li>\n<li>/RPM/XPRA_500_RES_UPDATE_GUID</li>\n<li>/S4PPM/XPRA_S4MIG_PROJECT</li>\n<li>\n<p>/S4PPM/XPRA_S4MIG_AUTH_REF</p>\n</li>\n</ul>\n<p>If your <strong>source system has version SAP Portfolio and Project Management 5.0, 6.0, 6.1 (CPRXRPM 500_702, 600_740, 610_740)</strong>, execute the following reports in the order as listed below:</p>\n<ul>\n<li>/S4PPM/XPRA_S4MIG_PROJECT</li>\n<li>/S4PPM/XPRA_S4MIG_AUTH_REF</li>\n</ul>\n<p>Check the log of every report before executing the next one. In case of errors, the error root cause must be identified and fixed before restarting the reports. Check the error messages and long texts of the reports for details. In case of issues, see the report documentation.</p>\n<p> </p>", "noteVersion": 3}, {"note": "2358376", "noteTitle": "2358376 - S4TWL - Web Dynpro Applications for Resource Maintenance in SAP Portfolio and Project Management for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The Web Dynpro application 'Create Resource' is no longer available. Furthermore, it is no longer possible to edit standard fields of a business partner with the Web Dynpro application 'Edit Resource'. Use the standard functions for creating and editing business partner data.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>No effects on business processes are expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Please see note 2379542 for further information on Business Partner in SAP Portfolio and Project Management for SAP S/4HANA. No further actions expected.</p>\n<p><strong>How to determine relevancy</strong></p>\n<p>This transition worklist item is relevant for you, if you are using the WebDynpro applications 'Create resource' or 'Edit resource'.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"168\"></td>\n<td valign=\"top\" width=\"436\"></td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 11}, {"note": "2340075", "noteTitle": "2340075 - FAQs - SAP Portfolio and Project Management 1.0 for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p><strong>SAP Portfolio and Project Management 1.0 for SAP S/4HANA is out of maintenance since 01.01.2022.</strong></p>\n<p>FAQs - SAP Portfolio and Project Management 1.0 for SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>xRPM, SAP RPM, cProjects, CPRXRPM, EPPM, cProject, ICF, Internet Communication Framework, SICF, HTTP, HTTPS, Performance, FAQ</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You want to use SAP Portfolio and Project Management 1.0 for SAP S/4HANA as your portfolio and/or project management tool.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>SAP Portfolio and Project Management 1.0 for SAP S/4HANA is the first release of a new product. It is an AddOn to SAP S/4HANA OP 1610.</p>\n<p>The corresponding SAP Fiori UI is contained in the new product SAP Fiori for SAP Portfolio and Project Management 1.0 for SAP S/4HANA.</p>\n<p><strong>Part 1: General Information</strong></p>\n<ul>\n<li><strong>SAP Portfolio and Project Management 1.0 for SAP S/4HANA in context:</strong><br/>SAP Portfolio and Project Management 1.0 for SAP S/4HANA is a comprehensive solution combining strategic and operational portfolio management with project management capabilities including prebuilt integration to SAP. SAP Portfolio and Project Management 1.0 for SAP S/4HANA addresses your organization's need for strategic and operational portfolio management. An enterprise-level solution for the management of a range of portfolios - including product innovation management, professional service portfolios, and enterprise IT - SAP Portfolio and Project Management 1.0 for SAP S/4HANA enables you to better control and innovate projects, processes, products, and services across their life cycles. To be successful, companies need powerful solutions that allow them to employ sophisticated project management methods that drive speed and quality. SAP Portfolio and Project Management 1.0 for SAP S/4HANA helps you standardize and improve project management execution and reduce associated administrative and system costs, by providing reliable project management functions integrated to your back-end systems (such as HR and Financials).<br/> </li>\n<li>SAP Portfolio and Project Management 1.0 for SAP S/4HANA is included in the <strong>software components EPPM 100 (ABAP) and UIS4EPPM 100 (Fiori UI)</strong>. <br/> </li>\n<li><strong>System requirements and installation options:<br/>  </strong><br/>For the <strong>system requirements</strong>, see the Master Guide for \"SAP Portfolio and Project Management 1.0 for SAP S/4HANA \" (available on SAP Service Marketplace, see  Part 4 of this note for more details).<br/> <br/><strong>Installation options:</strong> <br/><strong>SAP Portfolio and Project Management 1.0 for SAP S/4HANA is released for installation only on top of SAP S/4HANA 1610. </strong><br/><strong>Stand-alone deployment on NW (without SAP S/4HANA 1610) is not supported.</strong><br/>SAP S/4 HANA 1610 requires <strong>SAP NetWeaver 7.51 for SAP S/4HANA 1610</strong>.<br/>For information about moving from SAP Portfolio and Project Management releases 4.0 - 6.1 to SAP Portfolio and Project Management 1.0 for SAP S/4 HANA, see <strong>SAP Note 2361657 - Moving from PPM Releases to SAP Portfolio and Project Management 1.0 for SAP S/4HANA.</strong><br/>   <br/>See the <em>Moving to SAP S/4HANA</em> chapter under <a href=\"http://help.sap.com/disclaimer?site=http%3A%2F%2Fhelp.sap.com%2Fs4hana\" target=\"_blank\" title=\"Go to: http://help.sap.com/s4hana\">http://help.sap.com/s4hana</a> - SAP S/4HANA 1610 - <em>Getting Started With SAP S/4HANA 1610</em>.<br/> <br/>See the <em>Conversion Guide</em> under <a href=\"http://help.sap.com/disclaimer?site=http%3A%2F%2Fhelp.sap.com%2Fs4hana\" target=\"_blank\" title=\"Go to: http://help.sap.com/s4hana\">http://help.sap.com/s4hana</a> - SAP S/4HANA 1610 - <em>Conversion Guide</em>.<br/>     <br/><span ar-sa;=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;\"=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\"><strong>Fiori UI component:</strong><br/>For the SAP Fiori UI contained in the new product SAP Fiori for SAP Portfolio and Project Management 1.0 for SAP S/4HANA, there are different installation options, see the Master Guide for \"SAP Portfolio and Project Management 1.0 for SAP S/4HANA \" (available on SAP Service Marketplace, see Part 4 of this note for more details) and the <em>UI Technology Guide for SAP S/4HANA 1610</em> under <a href=\"http://help.sap.com/disclaimer?site=http%3A%2F%2Fhelp.sap.com%2Fs4hana\" target=\"_blank\" title=\"Go to: http://help.sap.com/s4hana\">http://help.sap.com/s4hana</a> - SAP S/4HANA 1610 - <em>UI Technology Guide for SAP S/4HANA 1610.</em><br/>   </span><br/>See SAP Note 2340065 for the required NW SP level for SAP Portfolio and Project Management 1.0 for SAP S/4HANA SPs.<br/><br/>During the installation, see also SAP Note 517484 (using transaction SICF to activate ICF nodes) and SAP Note 2340065 when importing Support Packages. For the SAP Portfolio and Project Management 1.0 for SAP S/4HANA specific ICF nodes, and, in general, for configuration topics, see the Configuration Guide content (See Part 4 of this note for more details).<br/>   <br/>Note:<br/>Approval documents, project status reports only run using SAP Interactive Forms by Adobe (PDF). Approval documents, project status reports, print forms in SAP Portfolio and Project Management 1.0 for SAP S/4HANA require Adobe Document Services (ADS). You must install ADS 7.50 on the Java Stack. <br/>For more information about SAP Interactive Forms by Adobe, see <a href=\"http://www.sdn.sap.com/irj/sdn/adobe\" target=\"_blank\">http://www.sdn.sap.com/irj/sdn/adobe</a>.<br/> </li>\n<li>No portal content (Business Package, roles, worksets, pages, iViews, ...) is being delivered for usage of SAP Portfolio and Project Management 1.0 for SAP S/4HANA in <strong>SAP Enterprise Portal (EP)</strong>. But the integration of SAP Portfolio and Project Management 1.0 for SAP S/4HANA in SAP Fiori Launchpad (FLP) in SAP Enterprise Portal (EP) is possible. See the <a href=\"http://help.sap.com/saphelp_nw75/helpdata/en/cc/87120279fc444ebc8280c201feb179/content.htm?frameset=/en/1b/1f1c6e3de24c37899e2369386febed/frameset.htm&amp;current_toc=/en/3d/184bddb38e486187648e733f8846b4/plain.htm&amp;node_id=22\" target=\"_blank\">Release Notes</a> and the  <a href=\"https://support.sap.com/content/dam/website/roadmaps/en_us/platform-and-technology/SAP%20HANA%20Cloud%20Platform%20portal%20service%20and%20SAP%20Enterprise%20Portal.pdf?logActivity=true\" target=\"_blank\">Portal Roadmap</a> under <a href=\"http://www.sap.com/solution/roadmaps.html\" target=\"_blank\">http://www.sap.com/solution/roadmaps.html</a>.</li>\n<li>\n<p><strong>SAP Portfolio and Project Management: GANTT Chart</strong>: <br/>If you want to replace the JGantt (Java Applet and Java Browser plugin based solution) by the UI5 Gantt (HTML5 based solution) <br/>(\"Project - Structure - Graphic\"), see SAP Note <a href=\"/notes/2645827\" target=\"_blank\" title=\"2645827  - GANTT Chart: Replacement of JGantt by UI5 Gantt: Availability and Prerequisites\">2645827</a> for availability and prerequisites.</p>\n</li>\n<li>\n<p><strong>SAP Portfolio and Project Management: Replacement of Interactive Forms Status Reports and Approvals</strong>: <br/>If you want to replace the SAP Interactive Forms by Adobe by the File download to forms Status Reports (\"Project - Status Reports - New Status Report\") and Approvals (\"Project - Structure - Approval\"), see SAP Note <a href=\"/notes/2850870\" target=\"_blank\" title=\"2850870  - Implementation Note : Replacement of Adobe Interactive forms in Project Management\">2850870</a> for availability and prerequisites.</p>\n</li>\n<li>\n<p><strong>SAP Portfolio and Project Management: Replacement of Interactive Forms Print Factsheets and Print Notes</strong>: <br/>If you want to replace the SAP Interactive Forms by Adobe with the File download to forms<strong> Print Factsheet </strong>(\"Project - Print Factsheet\") and <strong>Print Notes </strong>(\"Project - Notes tab - Print Notes\"), see SAP Note <a href=\"/notes/2879652\" target=\"_blank\" title=\"2879652  - Implementation Note : Replacement of Adobe Interactive forms in Project Management (Print Fact sheet, Print Notes)\">2879652</a> for availability and prerequisites.</p>\n</li>\n<li><strong><strong>SAP Portfolio and Project Management: Multi-Project Monitor (MPMon)</strong>: <br/></strong>If you want to replace the Java MPMon (Java Applet and Java Browser plugin based solution) by the UI5 MPMon (HTML5 based solution) <br/>(\"Projects - Open Multi Project Monitor\"), see SAP Note <a href=\"/notes/2945333\" target=\"_blank\" title=\"2945333  - Replacement of Java Multi-project Monitor by UI5 Multi-project Monitor\">2945333</a> for availability and prerequisites. <strong><br/></strong></li>\n<li><strong>Licenses: <br/></strong><em>SAP Portfolio and Project Management 1.0 for SAP S/4HANA</em> is available as an Add-On for SAP S/4HANA. To use the capabilities provided by this Add-On, you will need a separate license in addition to your SAP S/4HANA Enterprise Management license. This is true for Portfolio Management as well as for Project Management capabilities within <em>SAP Portfolio and Project Management 1.0 for SAP S/4HANA. </em>The license for this Add-On is calculated based on the number of Professional and Standard Users for both capabilities. The usage of <em>SAP Fiori for SAP Portfolio and Project Management 1.0 for SAP S/4HANA</em> is included in the license of <em>SAP Portfolio and Project Management 1.0 for SAP S/4HANA</em> and, therefore, doesn’t require any additional license. For further information, please contact your sales personnel.</li>\n<li><strong>Availability:</strong><br/>SAP Portfolio and Project Management 1.0 for SAP S/4HANA is in unrestricted shipment / General Availability (GA) as of March 17, 2017.<br/> </li>\n<li><strong>Security:</strong><br/>Please also note that it is highly recommended to carefully review and follow the current Security Guide (see link in Part 4 of this note).<br/> </li>\n<li><strong>Support Packages:</strong><br/>For information about SAP Portfolio and Project Management 1.0 for SAP S/4HANA (EPPM 100, UIS4EPPM 100) Support Packages, see SAP Notes 2340065 and 2341122. See SAP Note 2340065 for the current SAP Portfolio and Portfolio Management 1.0 for SAP S/4HANA Support Package Schedule.<br/> </li>\n<li><strong>Performance:</strong><br/>For a collection of performance relevant notes see SAP Note 2340092.<br/> </li>\n<li><strong>SAP Fiori for SAP Portfolio and Project Management 1.0 for SAP S/4HANA:</strong><br/>The new product SAP Fiori for SAP Portfolio and Project Management 1.0 for SAP S/4HANA provides a new (optional) UI for SAP Portfolio and Project Management 1.0 for SAP S/4HANA  in SAP Fiori design (see <a href=\"http://help.sap.com/fiori\" target=\"_blank\">http://help.sap.com/fiori</a>).<br/> </li>\n<li><strong>Restriction List:</strong><br/>SAP Note 2340116 contains a list of product restrictions concerning SAP Portfolio and Project Management 1.0 for SAP  S/4HANA.</li>\n</ul>\n<p><br/>***********************************************************************</p>\n<p><strong>Part 2: Product Documentation - Feature Scope Description</strong></p>\n<p><span 'times=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">A description of all features can be found in the Feature Scope Description (see Part 4).</span></p>\n<p><br/>***********************************************************************</p>\n<p><strong>Part 3: Technical Tips</strong></p>\n<ul>\n<li><strong>Supported Browsers, Java versions, Microsoft Project (MSP) versions, SAP Business Client versions, etc.</strong> for SAP Portfolio and Project Management 1.0 for SAP S/4HANA, see SAP Note <a href=\"/notes/2340078\" target=\"_blank\">2340078</a>.</li>\n</ul>\n<ul>\n<li><strong>Personalization of user interface elements:</strong><br/>You can personalize user interface elements (for example, tables) in Web Dynpro ABAP by placing your cursor over the relevant element (for example, by clicking a table line) and then by choosing the secondary mouse button. For further details, see Note 938521.</li>\n</ul>\n<ul>\n<li>For further information about <strong>customer enhancements in Web Dynpro ABAP</strong>:<br/>In German:<br/><a href=\"http://help.sap.com/saphelp_nw75/helpdata/de/2a/efaa0fc9a041c9b8558b55c9f7a565/content.htm?frameset=/de/7c/3545415ea6f523e10000000a155106/frameset.htm\" target=\"_blank\">http://help.sap.com/saphelp_nw75/helpdata/de/2a/efaa0fc9a041c9b8558b55c9f7a565/content.htm?frameset=/de/7c/3545415ea6f523e10000000a155106/frameset.htm</a> <br/>In English:<br/><a href=\"http://help.sap.com/saphelp_nw75/helpdata/en/2a/efaa0fc9a041c9b8558b55c9f7a565/content.htm?frameset=/en/7c/3545415ea6f523e10000000a155106/frameset.htm\" target=\"_blank\">http://help.sap.com/saphelp_nw75/helpdata/en/2a/efaa0fc9a041c9b8558b55c9f7a565/content.htm?frameset=/en/7c/3545415ea6f523e10000000a155106/frameset.htm</a></li>\n</ul>\n<ul>\n<li>See SAP Note 875986 for information about important SAP Notes regarding transaction <strong>SNOTE (SAP Note Assistant)</strong> to import SAP Notes.</li>\n</ul>\n<p>***********************************************************************</p>\n<p><strong>Part 4: Additional information:</strong></p>\n<p>(planned to be available at RTC)</p>\n<ul>\n<li><strong>Product Availability Matrix:</strong><br/><a href=\"http://service.sap.com/pam\" target=\"_blank\"><span>http://service.sap.com/pam</span><br/></a>-&gt; SAP Application Components<br/>-&gt; SAP Portfolio and Project Management 1.0 for SAP S/4HANA  <br/> </li>\n<li><strong>Feature Scope Description, Application Help, Master Guide, Conversion Guide, Application Operations Guide, and Guide for Implementing Own Object Links:</strong><br/><span><a href=\"http://help.sap.com/ppms4\" target=\"_blank\">http://help.sap.com/ppms4</a></span><br/>-&gt; using SAP Portfolio and Project Management 1.0 for SAP S/4HANA<br/> </li>\n<li><strong>Configuration Content: <br/></strong>Note 2340108 contains information about the SAP Portfolio and Project Management 1.0 for SAP S/4HANA Configuration Content (aka \"Configuration Guide\").<br/> </li>\n<li><strong>Security Guide:</strong><br/><span>http://service.sap.com/securityguide</span><br/>-&gt; SAP Portfolio and Project Management 1.0 for SAP S/4HANA<br/> </li>\n<li><strong>Sizing Guide:</strong><br/><span><a href=\"http://www.sap.com/sizing\" target=\"_blank\">http://www.sap.com/sizing</a></span><br/>Sizing<br/>-&gt; Sizing Guidelines<br/>-&gt; SAP S/4HANA<br/>-&gt; Sizing SAP Portfolio and Project Management 1.0 for SAP S/4HANA<br/> </li>\n<li><strong>Ramp-up Knowledge Transfer (RKT) - Learning Map:</strong><br/><span>http://service.sap.com/rkt-plm</span><br/>-&gt; SAP PLM<br/>-&gt; SAP Portfolio and Project Management 1.0 for SAP S/4HANA  <br/> </li>\n<li>\n<p><strong>SCN Forum \"SAP Portfolio and Project Management (SAP RPM, cProjects) and cFolders\":</strong><br/><span><a href=\"http://scn.sap.com/community/plm/rpm-collaborative-project-management-and-cfolders\" target=\"_blank\">http://scn.sap.com/community/plm/rpm-collaborative-project-management-and-cfolders</a> </span></p>\n</li>\n<li>\n<p><strong>Documentation for Web Dynpro ABAP:</strong><br/>German:<br/><a href=\"http://help.sap.com/saphelp_nw75/helpdata/de/4e/161363b81a20cce10000000a42189c/content.htm?current_toc=/de/7c/3545415ea6f523e10000000a155106/plain.htm\" target=\"_blank\">http://help.sap.com/saphelp_nw75/helpdata/de/4e/161363b81a20cce10000000a42189c/content.htm?current_toc=/de/7c/3545415ea6f523e10000000a155106/plain.htm</a> <br/>English:<br/><a href=\"http://help.sap.com/saphelp_nw75/helpdata/en/4e/161363b81a20cce10000000a42189c/content.htm?current_toc=/en/7c/3545415ea6f523e10000000a155106/plain.htm\" target=\"_blank\">http://help.sap.com/saphelp_nw75/helpdata/en/4e/161363b81a20cce10000000a42189c/content.htm?current_toc=/en/7c/3545415ea6f523e10000000a155106/plain.htm</a></p>\n</li>\n<li><strong>Documentation for SAP Business Client:</strong><br/>For more information about SAP Business Client:<br/>German:<br/><a href=\"http://help.sap.com/saphelp_nw75/helpdata/de/66/48a793bc2f4ec5bdb8e7e93ea6cd9f/frameset.htm\" target=\"_blank\">http://help.sap.com/saphelp_nw75/helpdata/de/66/48a793bc2f4ec5bdb8e7e93ea6cd9f/frameset.htm</a> <br/>English:<br/><a href=\"http://help.sap.com/saphelp_nw75/helpdata/en/66/48a793bc2f4ec5bdb8e7e93ea6cd9f/frameset.htm\" target=\"_blank\">http://help.sap.com/saphelp_nw75/helpdata/en/66/48a793bc2f4ec5bdb8e7e93ea6cd9f/frameset.htm</a> <br/> <br/>For more information, seeSAP Note 900000.</li>\n<ul>\n<li>SAP Note <a href=\"http://help.sap.com/disclaimer?site=https://launchpad.support.sap.com/#/notes/2227396\" target=\"_blank\">2227396</a> and the related notes.</li>\n<li>SAP Community Network (SCN):<br/><span>http://scn.sap.com/community/netweaver-business-client</span></li>\n</ul>\n</ul>\n<p> </p>", "noteVersion": 15}, {"note": "2368899", "noteTitle": "2368899 - S4TWL - Deletion of obsolete packages", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP Portfolio and Project Management for SAP S/4HANA, packages with development objects that were already obsolete in SAP Porfolio and Project Management 6.1 have been deleted.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>No effects on business processes are expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>No further actions necessary.</p>\n<p><strong>Related SAP Notes</strong></p>\n<p> </p>", "noteVersion": 2}, {"note": "2399707", "noteTitle": "2399707 - Simplification Item Check", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>As part of your system’s SAP Readiness Check assessment, you are interested in performing a simplification item relevance check, compatibility scope relevancy check, or a simplification item consistency check. Your intent in conducting this analysis is to support the scoping and planning of either the conversion from SAP ERP to SAP S/4HANA or the upgrade from one SAP S/4HANA product version to another SAP S/4HANA product version.</p>\n<p>Alternatively, you are interested in manually performing a simplification item relevancy or consistency check outside SAP Readiness Check.</p>\n<p>To learn more about how to perform simplification item checks and the relation to SAP Readiness Check, you can explore the following links:</p>\n<p><a href=\"https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./\" target=\"_blank\">https://blogs.sap.com/2018/03/26/sap-s4hana-simplification-item-check-how-to-do-it-right./</a></p>\n<p><a href=\"https://blogs.sap.com/2020/01/02/simplification-item-catalog-simplification-item-check-and-sap-readiness-check-for-sap-s-4hana/\" target=\"_blank\">https://blogs.sap.com/2020/01/02/simplification-item-catalog-simplification-item-check-and-sap-readiness-check-for-sap-s-4hana/</a></p>\n<p><strong>We strongly recommend analyzing a production system; otherwise, the results might be incomplete or misleading. </strong>If you choose to use another environment, for instance, a copy of a production system, implement and follow SAP Note <a href=\"/notes/2568736\" target=\"_blank\">2568736</a> (in both the productive and non-production systems) to capture and upload the necessary ST03N data.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Readiness Check, SAP S/4HANA Conversion; SAP S/4HANA Upgrade; Simplification Item, Simplification List, Compatibility Scope</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Before implementing this SAP Note (2399707), we strongly recommend implementing the latest version of SAP Note <a href=\"/notes/1668882\" target=\"_blank\">1668882</a>.</p>\n<p>In addition, before executing the simplification item analysis in your system, we strongly recommend implementing SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a>. This note delivers check classes used to refine the list of relevant simplification items.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Contents<br/></strong>  1.  Introduction<br/>  2.  Enabling the Simplification Item Checks<br/>       2.1. Note Implementation<br/>       2.2. Simplification Item Catalog Maintenance<br/>  3.  Executing the Simplification Item Checks<br/>       3.1. Using the Data Collection Framework for SAP Readiness Check<br/>       3.2. Executing the Checks Manually<br/>  4.  Reviewing the Results in /SDF/RC_START_CHECK<br/>       4.1. Interpreting the Check Results<br/>       4.2. Administrative Functions within the Check Results View<br/>  5.  Additional Information<br/>  6.  Frequently Asked Questions</p>\n<p><strong>  1. Introduction</strong></p>\n<p>While scoping and planning the conversion of your SAP ERP system to SAP S/4HANA, or the upgrade of an SAP S/4HANA system, we recommend analyzing the impact on your system based on the list of documented simplification items. This note provides the simplification item check capabilities to analyze your system. There are two types of checks integrated into this solution:</p>\n<ul>\n<li><strong>Relevance Check:</strong> Produces a customized list of relevant simplification items for your system. The relevance is determined based on rules maintained in the <a href=\"https://launchpad.support.sap.com/#sic\" target=\"_blank\">simplification item catalog</a>. </li>\n</ul>\n<ul>\n<ul>\n<li>If the rule is not maintained or cannot be processed, the item has the status <em>Relevance to Be Checked</em>.</li>\n<li>For simplification items where the rule evaluates database table content, the analysis evaluates table entries across all clients within the system.</li>\n</ul>\n</ul>\n<p>The results of the relevance check are presented in the <em>Simplification Items</em> tile and the <em>Compatibility Scope Analysis</em> tile within SAP Readiness Check for SAP S/4HANA and SAP Readiness Check for SAP S/4HANA upgrades.</p>\n<ul>\n<li><strong>Consistency Check:</strong> When initiated in client 000, this check analyzes the consistency of the system in preparation for the conversion or upgrade using Software Update Manager. The consistency check only evaluates those simplification items marked as relevant or potentially relevant to the system.</li>\n</ul>\n<p>We recommend resolving all identified inconsistencies before the downtime in Software Update Manager. Otherwise, when Software Update Manager encounters unresolved consistency issues, it will need to be reset or restarted. The check also warns you about critical changes during the conversion or upgrade, for example, potential data loss. To continue, you must confirm that you have understood the warning.</p>\n<p>Individual check classes, developed by the associated application area, are leveraged during the consistency check evaluation. The implementation of SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a> delivers these classes to your system.</p>\n<p>To initiate the consistency check, you can either activate the option within the SAP Readiness Check selection screen (report RC_COLLECT_ANALYSIS_DATA) or use the simplification item check report (/SDF/RC_START_CHECK).</p>\n<p>The results of the consistency check, when initiated by SAP Readiness Check (report RC_COLLECT_ANALYSIS_DATA) in client 000, can be uploaded to an existing analysis for the productive client. The results are then visible within the detailed view of the <em>Simplification Items</em> tile.</p>\n<p>Detailed results are available in the simplification item check report (/SDF/RC_START_CHECK) by selecting <em>Display Last Check Result</em>. The results are only visible in the client used to perform the analysis (for instance, client 000).</p>\n<p><strong>Note</strong>: The check results include references to SAP Notes that describe how to resolve identified issues.</p>\n<p>To identify and resolve possible issues within time, you must <strong>correctly implement and run the simplification item checks before</strong> starting the technical conversion or upgrade. To allocate sufficient time and resources to resolve any potential issues, we recommend initially performing the analysis as part of the scoping and planning phase of the project within SAP Readiness Check. The check is performed one additional time by the Software Update Manager toolset shortly before the technical downtime of the conversion or upgrade of a system. But do not wait to implement and execute the check just before starting Software Update Manager; otherwise, there is a potential risk to the project timeline.</p>\n<p><strong>  2. Enabling the Simplification Item Checks</strong></p>\n<p>2.1. Note Implementation</p>\n<p>Depending on the target software level of your system conversion or release upgrade, specific <strong>minimum versions</strong> of this SAP Note (2399707) and SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a> are required. Otherwise, the check may be incomplete or inaccurate.</p>\n<p>The minimum versions are (If the Support Package Stack is not specifically listed, then its version requirement defaults to match that of the closest Support Package Stack.):</p>\n<ul>\n<ul>\n<ul>\n<li>SAP S/4HANA 2021 Initial Shipment</li>\n<ul>\n<li>2399707: Minimum recommended version 147 (Minimum technical version 82)</li>\n<li>2502552: version 97</li>\n</ul>\n<li>SAP S/4HANA 2022 Initial Shipment</li>\n<ul>\n<li>2399707: Minimum recommended version 154 (Minimum technical version 82)</li>\n<li>2502552: version 97</li>\n</ul>\n<li>SAP S/4HANA 2022 Feature Package 1</li>\n<ul>\n<li>2399707: Minimum recommended version 155 (Minimum technical version 82)</li>\n<li>2502552: version 97</li>\n</ul>\n<li>SAP S/4HANA 2023 Initial Shipment</li>\n<ul>\n<li>2399707: Minimum recommended version 161 (Minimum technical version 82)</li>\n<li>2502552: version 105</li>\n</ul>\n</ul>\n</ul>\n</ul>\n<p>While the minimum technical version of SAP Note 2399707 enables the conversion process to proceed, the minimum version may not reveal all application-level issues. As a result, we advise using at least the recommended version referenced above.</p>\n<p>Regardless of the minimum note version, when starting your project, it is recommended to <strong>use the most recent version of SAP Notes 2399707 and 2502552</strong>. When you reach the hard-freeze phase in your project, you should also freeze the version of these notes.</p>\n<p>2.2. Simplification Item Catalog Maintenance</p>\n<p>The simplification item catalog contains a list of generally available target product versions and documented simplification items. A local replica is imported into the system to support the execution of the simplification item checks.</p>\n<p>The initial implementation of the check report (/SDF/RC_START_CHECK) includes the most recent version of the simplification item catalog. While continuing to scope and plan the project, we recommend using an up-to-date version of the simplification item catalog. Since conversion projects can take some time, you should consider and plan catalog updates during the project.</p>\n<p>While we advise you to use the most recent simplification item catalog content in early project phases, you should freeze and synchronize this content as part of the hard-freeze phase in your project. As you prepare to enter the hard-freeze phase of your project, <strong>download the local replica of the simplification item catalog </strong>from the system where you plan to perform your final test conversion. You can download the catalog using the corresponding button on the /SDF/RC_START_CHECK selection screen. We recommend downloading the content before converting the system. Similarly, you can then upload this content using report /SDF/RC_START_CHECK to synchronize the version before performing checks in any subsequent systems.</p>\n<p>2.2.1. Updating the Simplification Item Catalog</p>\n<p>The SAP hosted version of the simplification item catalog is updated periodically to accommodate new product versions, introduce new simplification items, and integrate lessons learned from project experience. By default, the report /SDF/RC_START_CHECK does not automatically update the local replica of the simplification item catalog from SAP servers. If you want to update the content from the SAP servers, you explicitly need to trigger this via the <em>Update catalog with latest version from SAP</em> button in /SDF/RC_START_CHECK.</p>\n<p>If no connection exists between the system and the SAP support backbone, you could download the content as an archive directly from the <a href=\"https://me.sap.com/sic\" target=\"_blank\">simplification item catalog</a> site. Once you download the archive to your local machine, you can manually upload it in /SDF/RC_START_CHECK. The <em>Local Version</em> will be marked with a watch icon when the local replica is over 30 days old.</p>\n<p>The available functions for managing the local replica in /SDF/RC_START_CHECK are:</p>\n<ul>\n<ul>\n<ul>\n<li><em>Update Catalog with latest version from SAP: </em>Used to directly download the latest catalog version from SAP to update the local replica.</li>\n<li><em>Upload Simplification Item Catalog from file</em>: Used to upload a version of the catalog stored on your local machine. This option supports environments without direct connectivity to the SAP support backbone. Additionally, it supports synchronizing the catalog version across systems. </li>\n<li><em>Download Current Simplification Item Catalog</em>: Used to download a copy of the local replica version to your local machine as a backup before updating the catalog. It also supports synchronizing the catalog version across systems.</li>\n</ul>\n</ul>\n</ul>\n<p><strong>  3. Executing the Simplification Item Checks<br/></strong></p>\n<p>As stated previously, the simplification item checks (that is, the relevance check and the consistency check) are available via both the data collection framework for SAP Readiness Check (using report RC_COLLECT_ANALYSIS_DATA) and manually (using report /SDF/RC_START_CHECK).</p>\n<p>When running the simplification item checks close to the technical execution of the conversion from SAP ERP to SAP S/4HANA or the upgrade to a higher SAP S/4HANA product version, it is essential to follow the guidance below. <strong>Otherwise</strong>, the technical conversion or upgrade could end with an error, and you may have to <strong>reset and</strong> <strong>repeat the Software Update Manager procedure.</strong> If forced to reset Software Update Manager, update this SAP Note (2399707) and SAP Note <a href=\"/notes/2502552\" target=\"_blank\">2502552</a>, repeat the analysis, and resolve any new issues found before restarting the Software Update Manager procedure.</p>\n<p>3.1. Using the Data Collection Framework for SAP Readiness Check</p>\n<p>To enable the data collection framework to analyze the simplification item checks, enable the related options within the selection screen of report RC_COLLECT_ANALYSIS_DATA. The data collection framework includes the following options related to simplification items:</p>\n<ul>\n<ul>\n<li><em>Simplification Item and Compatibility Scope relevance</em>: By default, this check is active. We recommend performing this analysis on each of the system’s productive clients. Note that each client requires a separate analysis on the landing page for SAP Readiness Check.</li>\n<li><em>Simplification Item Effort Drivers</em>: This check is not enabled by default as it takes some additional time to collect the related information. However, we recommend enabling this analysis for clarity on the effort required to remediate simplification items that are ranked<em> potentially high</em>. Additionally, by analyzing the effort drivers, the default effort ranking for some <em>potentially high</em> simplification items may be reclassified, further supporting the project’s scoping and planning.</li>\n<li><em>Simplification Item Consistency</em>: This check should be performed within client 000, as this is the same client where Software Update Manager will analyze the system. Log on to client 000 and clear all other options within RC_COLLECT_ANALYSIS_DATA to enable this check. The relevance check will be automatically added to the scope when enabling the consistency check. You can then extend the analysis of the production client by updating it with the archive produced by the consistency check in client 000. Select the <em>Update Analysis</em> button on the dashboard view to add the archive.</li>\n</ul>\n</ul>\n<p>3.2. Executing the Checks Manually</p>\n<p>These are the steps required to execute the simplification item checks manually:</p>\n<ol><ol>\n<li>Start report /SDF/RC_START_CHECK in transaction SA38.</li>\n<li>In the <em>Simplification Item Check Options</em> section, choose the target SAP S/4HANA product version.</li>\n</ol></ol>\n<p>Note: If the target product version is not available in the list, follow the steps above to update the local replica of the simplification item catalog. Only released-to-customer product versions are visible in the list.</p>\n<ol><ol start=\"3\">\n<li>Choose the mode you want to perform the check:</li>\n</ol></ol><ol>\n<ul>\n<ul>\n<li><em>New relevance check in Online mode</em>: The result will be displayed immediately after the check completes.</li>\n<li><em>New relevance &amp; consistency check as background job</em>: We recommend this option when you expect a long runtime or you are uncertain of the runtime.</li>\n</ul>\n</ul>\n</ol><ol><ol start=\"4\">\n<li>Execute the check to receive the simplification item list.</li>\n<li>Review the results (see below).</li>\n</ol></ol>\n<p><strong>  4. Reviewing the Results in /SDF/RC_START_CHECK</strong></p>\n<p>When using the data collection framework for SAP Readiness Check to execute the simplification item checks, the results are collected in the generated archive and can be uploaded to the landing page for SAP Readiness Check: <a href=\"https://me.sap.com/readinesscheck\" target=\"_blank\">https://me.sap.com/readinesscheck</a>.</p>\n<p>Independently of how you initiated the simplification items evaluation, /SDF/RC_START_CHECK can be used to review the detailed results. To find the results, log on to client 000, start report /SDF/RC_START_CHECK, select the targeted SAP S/4HANA product version, then select the <em>Display Last Check Result</em> option from the <em>Simplification Item Check Options </em>section<em>,</em> and then choose <em>Execute</em>.</p>\n<p>Solve any errors identified by the consistency check, as this is <strong>mandatory</strong> for any conversion or upgrade to SAP S/4HANA 1809 or higher.</p>\n<p>4.1. Interpreting the Check Results</p>\n<p>Within the check result view, you will find the following:</p>\n<ul>\n<ul>\n<li>Basic information about a simplification item, including application area, ID, title, application component, category, and business impact note.</li>\n<li>Indication of whether the simplification item is relevant to the system or not.</li>\n<li>Results of the most recent consistency check, with an indication of whether the system is consistent, related to the simplification item, or not. Select the simplification item and choose <em>Display Consistency Check Log</em> to find the latest result and learn how to resolve the inconsistency.</li>\n<li>Indication of whether an exemption is possible for those simplification items ending in error. Some simplification items only require the acknowledgment of the customer. You need only apply an exemption to the simplification item in such cases. In doing so, you acknowledge that you have read and understood the message. Once an item is exempt, it will no longer block the conversion to the corresponding target SAP S/4HANA product version.</li>\n<li>A summary of the relevancy assessment of the simplification item.</li>\n</ul>\n</ul>\n<p>4.2. Administrative Functions within the Check Results View</p>\n<p>From the check results view, various operations regarding the consistency check are possible, including:</p>\n<ul>\n<ul>\n<li>Initiate a consistency check for all relevant items.</li>\n<li>Perform a detailed consistency check for a selected item.</li>\n<li>Display the consistency check log.</li>\n<li>Apply or revoke exemption for consistency check errors able to be skipped.</li>\n<li>Display exemption log.</li>\n</ul>\n</ul>\n<p><strong>  5. Additional Information</strong></p>\n<p>5.1. Executing simplification item checks for a newly released SAP S/4HANA Product Version, Feature Package Stack, or Support Package Stack</p>\n<p>Suppose you encounter the issue where you cannot see the newly released SAP S/4HANA feature package stack or support package stack in the target list. In that case, you need to update the simplification item catalog to the latest version available from SAP. See section 2.2.1 for more information.</p>\n<p>5.2. Framework for storing and managing the simplification item check logs</p>\n<p>The simplification item checks use the application log to retain the check logs. These logs are client-specific; therefore, you must ensure you are in the correct client when analyzing the check logs.</p>\n<p>The check logs produced by Software Update Manager are only available in client 000.</p>\n<p>5.3. Adding an exemption for an inconsistent simplification item</p>\n<p>The simplification item check only supports the exemption of an item when the consistency check return code is 7.</p>\n<p>When creating the exemption, you must make sure the selected target version in /SDF/RC_START_CHECK is the same as the target product version processed by Software Update Manager.</p>\n<p>5.4. Using a central download service system to download simplification item catalog updates</p>\n<p>If you use a central download service system as a communication server and the ST-PI version in the landscape is less than or equal to ST-PI 2008_1_700 SP27, ST-PI 2008_1_710 SP27, or ST-PI 740 SP17, you need to implement this SAP Note (2399707) in the central download service system.</p>\n<p>Additionally, you need to implement SAP Note <a href=\"/notes/2945785\" target=\"_blank\">2945785</a> in the central download service system.</p>\n<p>5.5. Determining the relevant application component to use when creating an SAP incident related to a simplification item</p>\n<p>If you encounter an issue with a specific simplification item, it is best to create the customer support incident under the item’s relevant application component.</p>\n<p>The correct component can be identified by following these steps:</p>\n<ol><ol>\n<li>Open the URL <a href=\"https://me.sap.com/sic\" target=\"_blank\">https://me.sap.com/sic</a> with your S-User.</li>\n<li>Search the item with the Check item ID.</li>\n<li>Open the item.</li>\n<li>Go to the <em>Application Component</em> tab on the detail page.</li>\n</ol></ol>\n<p><strong>  6. Frequently Asked Questions</strong></p>\n<p>6.1. What do I need to check before implementing this SAP Note (2399707)?</p>\n<ul>\n<ul>\n<li>We <strong>strongly recommend </strong>checking that the latest version of SAP Note <a href=\"/notes/1668882\" target=\"_blank\">1668882</a> is “completely implemented” before you implement this SAP Note.</li>\n</ul>\n</ul>\n<p>If you encounter an issue while installing or updating this SAP Note (2399707), for example, syntax error after implementation, perform the following steps:</p>\n<ul>\n<ul>\n<ul>\n<li>Reset the implementation of this SAP Note (2399707) using transaction SNOTE.</li>\n<li>Implement the latest version of SAP Note <a href=\"/notes/1668882\" target=\"_blank\">1668882</a>.</li>\n<li>Exit the transaction SNOTE.</li>\n<li>Open SNOTE and reimplement this SAP Note (2399707).</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<ul>\n<li>When encountering the runtime error DDIC_TYPELENG_INCONSISTENT with report /SDF/RC_START_CHECK, first check to see whether SAP Note <a href=\"/notes/1229062\" target=\"_blank\">1229062</a> is valid for the system. If the SAP Note is not applicable, manually activate the following DDIC structures: SWNCAGGTASKTYPE, SWNCAGGUSERTCODE, SWNCAGGUSERWORKLOAD, and SWNCAGGTASKTIMES.</li>\n</ul>\n</ul>\n<p>6.2. Why do I get syntax errors when activating the objects in this SAP Note (2399707)?</p>\n<p>Usually, this is because the code context for the correction instruction is inconsistent. You can reset the implementation of this SAP Note (2399707) and then reimplement it.</p>\n<p>If this process does not resolve the issue, create a customer support incident under the component CA-TRS-PRCK.</p>\n<p>6.3. Where can I find the SAP Note for performing the simplification item check for SAP Readiness Check for SAP BW/4HANA?</p>\n<p>The SAP Readiness Check for SAP BW/4HANA approach evolved from analyzing a list of simplification items to evaluating the objects within the SAP BW system. Now, simplification item-related SAP Notes are available in the <em>Learn More</em> text and the tables provided within the detailed views.</p>\n<p>6.4. Is it possible to skip a simplification item consistency check?</p>\n<p>Yes, it is technically possible to skip a simplification item consistency check. However, you must create a customer support incident under the component CA-TRS-TDBT requesting access to SAP Note <a href=\"/notes/2641675\" target=\"_blank\">2641675</a>.</p>\n<p>6.5. How can I resolve the warning in /SDF/RC_START_CHECK that I cannot fetch the simplification item catalog through the SAP-SUPPORT_PORTAL HTTP connection?</p>\n<p>This issue arises when the SAP-SUPPORT_PORTAL RFC destination is not working. Reference the steps in SAP Note <a href=\"/notes/91488\" target=\"_blank\">91488</a> to resolve the communication issue.</p>\n<p>Alternatively, you can use the manual download and upload process described above to update the local simplification item catalog replica.</p>\n<p>6.6. In which client should I perform the simplification item checks?</p>\n<ul>\n<ul>\n<li><strong>Relevance Check</strong>: We recommend analyzing each of the system’s productive clients. If there is more than one productive client in the system, we recommend evaluating each client. Note that results from each client will require a separate analysis to display the results. </li>\n<li><strong>Consistency Check</strong>: Client 000 is the recommended client, as this is the same client the Software Update Manager will evaluate during the technical conversion. These results can enhance a system’s analysis when you upload them to the analysis results of the productive client.</li>\n</ul>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>Version</strong></td>\n<td><strong>Change</strong></td>\n</tr>\n<tr>\n<td>167</td>\n<td>\n<p>Text change for specific minimum versions</p>\n</td>\n</tr>\n<tr>\n<td>166</td>\n<td>\n<p>Enhance application log when item is relevant but no consistency check defined.</p>\n</td>\n</tr>\n<tr>\n<td>165</td>\n<td>\n<p>Bugfix: The results from the first check didn't automatically sort when clicking \"Display Last Check Result\".</p>\n</td>\n</tr>\n<tr>\n<td>164</td>\n<td>\n<p>Includes ST-PI 740 SP25 and ST-PI 2008_1_700 SP35 into validation component.</p>\n<p>You don't need update to this version if your SP is smaller than above.</p>\n</td>\n</tr>\n<tr>\n<td>162/163</td>\n<td>\n<p>Bugfix: check class is available in the system but got error 'not found'.</p>\n</td>\n</tr>\n<tr>\n<td>161</td>\n<td>\n<p>Includes ST-PI 740 SP24 and ST-PI 2008_1_700 SP34 into validation component.</p>\n<p>You don't need update to this version if your SP is smaller than above.</p>\n</td>\n</tr>\n<tr>\n<td>160</td>\n<td>\n<p>Add the stack into source release adjustment.</p>\n</td>\n</tr>\n<tr>\n<td>158/159</td>\n<td>\n<p>Bugfix: when relevant simple check results contains MISS_ST03N or RULE_ISSUE, the relevent check result may not correct.</p>\n</td>\n</tr>\n<tr>\n<td>157</td>\n<td>\n<p>ST-PI 740 SP21 and ST-PI 2008_1_700/2008_1_710 SP31 will include the code of this version.</p>\n</td>\n</tr>\n<tr>\n<td>156</td>\n<td>\n<p>Enhance the SUM log if the target product version does not exist in Simplification Item check framework.</p>\n</td>\n</tr>\n<tr>\n<td>155</td>\n<td>\n<p>Enhance note check text for SAP Note 2399707. ST-PI 740 SP20 and ST-PI 2008_1_700/2008_1_710 SP30 will include the code of this version.</p>\n</td>\n</tr>\n<tr>\n<td>153/154</td>\n<td>\n<p>Enhance the application log text for note check of consistency check.</p>\n</td>\n</tr>\n<tr>\n<td>152</td>\n<td>\n<p>Bugfix: if \"New relevance &amp; consistency check as background job\" selected and click \"Execute in Background\" (F9) in the menu, there's  too much batch jobs \"RC_NEW_CHECK_IN_JOB\" created.</p>\n<p>Change the note description.</p>\n</td>\n</tr>\n<tr>\n<td>151</td>\n<td>\n<p>Bugfix: replace the SY-DATUM with the date value when checking DB.</p>\n</td>\n</tr>\n<tr>\n<td>150</td>\n<td>\n<p>Fix the bug which from download service server.</p>\n</td>\n</tr>\n<tr>\n<td>148/149</td>\n<td>\n<p>Enhance the IDoc relevance check performance issue. The client number is added in the select SQL statement to avoid performance issue in some system environments.</p>\n</td>\n</tr>\n<tr>\n<td>146/147</td>\n<td>\n<p>Fix the IDoc relevance check bug that the check result is not correct when exectued in different client.</p>\n</td>\n</tr>\n<tr>\n<td>145</td>\n<td>\n<p>Remove the SAP S/4HANA ON-PREMISE 1511 from target version list since it's out of maintenance.</p>\n<p>Add the application log \"commit work\" statement for performance optimization.</p>\n</td>\n</tr>\n<tr>\n<td>144</td>\n<td>\n<p>Fix bug: the xml format error when update catalog with latest version from SAP.</p>\n</td>\n</tr>\n<tr>\n<td>143</td>\n<td>\n<p>Fix bug: Class /SDF/CL_RC_CHK_UTILITY, Public Section, Field \"ICON_CHECKED\" is unknown.</p>\n</td>\n</tr>\n<tr>\n<td>142</td>\n<td>\n<p>Fix bug: avoid creating too many work processes when download service is active</p>\n</td>\n</tr>\n<tr>\n<td>141</td>\n<td>\n<p>Update the note description to add the minimum recommendation for SAP S/4HANA 2020 Feature Package 1.</p>\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\n</td>\n</tr>\n<tr>\n<td>140</td>\n<td>\n<p>Bugfix: SAP S/4HANA 1709 is still supported for a system upgrade, do not show error message when upgrade to 1709.</p>\n</td>\n</tr>\n<tr>\n<td>139</td>\n<td>\n<p>Includes ST-PI 740 SP14 and ST-PI 2008_1_700 SP24 into validation component.</p>\n<p>You don't need update to this version if your SP is smaller than above.</p>\n</td>\n</tr>\n<tr>\n<td>138</td>\n<td>\n<p>Enhance error message for consistency check since that the target release SAP S/4HANA 1709 is no longer supported for a system conversion.</p>\n</td>\n</tr>\n<tr>\n<td>137</td>\n<td>\n<p>Update the note description to add the minimum recommendation for SAP S/4HANA 1909 Feature Package 2 and SAP S/4HANA 2020 Initial Shipment.</p>\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\n</td>\n</tr>\n<tr>\n<td>136</td>\n<td>\n<p>Fix issue: Type \"CL_ABAP_DYN_PRG\" is unknown.</p>\n<p>You can ignore this version if you don't have this issue.</p>\n</td>\n</tr>\n<tr>\n<td>134/135</td>\n<td>\n<p>Support Download Service to update the Simplification Item Catalog. Please check more description of this issue in SAP Note <a href=\"/notes/2882166\" target=\"_blank\">2882166</a>.</p>\n<p>You can ignore this version if you are not using Download Service to connect to the SAP Support Backbone.</p>\n</td>\n</tr>\n<tr>\n<td>133</td>\n<td>\n<p>Fix bug: do not block the process when ICM is unavailable.</p>\n<p>You can ignore this version if ICM service is active.</p>\n</td>\n</tr>\n<tr>\n<td>132</td>\n<td>\n<p>Fix bug: the consistency result can not show the latest result for items with exemption applied.</p>\n</td>\n</tr>\n<tr>\n<td>131</td>\n<td>\n<p>Improve the performance of cluster table checking if the database is Sybase.</p>\n<p>You can ignore this version if your database isn't Sybase.</p>\n</td>\n</tr>\n<tr>\n<td>130</td>\n<td>\n<p>Fix bug: The program is blocked when updating BALSUB.</p>\n</td>\n</tr>\n<tr>\n<td>129</td>\n<td>\n<p>Use SAP-SUPPORT_PORTAL HTTP connection instead of SAPOSS RFC connection. Note requirement check works and Simplification Item Catalog content can automatically update from SAP servers in this version. Please check more description of this issue in SAP Note <a href=\"/notes/2882166\" target=\"_blank\">2882166</a>.</p>\n</td>\n</tr>\n<tr>\n<td>128</td>\n<td>\n<p>Remove remote note requirement check because SAPOSS does not work anymore.</p>\n</td>\n</tr>\n<tr>\n<td>127</td>\n<td>\n<p>Includes ST-PI 740 SP level 12 into validation component. You don't need update to this version if your ST-PI 740 SP is smaller than SP12.</p>\n</td>\n</tr>\n<tr>\n<td>126</td>\n<td>\n<p>Update Note description and enhance ST03N data collection.</p>\n</td>\n</tr>\n<tr>\n<td>125</td>\n<td>\n<p>Add new fields \"LoB/Technology\" and \"Business Area\" into check result; Remove usage of CL_ABAP_DYN_PRG.</p>\n</td>\n</tr>\n<tr>\n<td>123/124</td>\n<td>\n<p>Includes ST-PI 740 SP level 11 into validation component. You don't need update to this version if your ST-PI 740 SP is smaller than SP11.</p>\n</td>\n</tr>\n<tr>\n<td>122</td>\n<td>\n<p>Enhance error message for consistency check; Update note text for minimum version requirement; Fix bug: filter lost when resizing check result</p>\n</td>\n</tr>\n<tr>\n<td>120-121</td>\n<td>\n<p>Fix bug: Stop <em>SUM-Phase: PREP_EXTENSION/RUN_S4H_SIF_CHECK_INIT even if the return code is 4.</em></p>\n</td>\n</tr>\n<tr>\n<td>118/119</td>\n<td>\n<p>Add Simplification Item Consistency support for Readiness Check 2.0.</p>\n<p>Add SUM return code support and write the SUM log when error occurs in SUM mode.</p>\n</td>\n</tr>\n<tr>\n<td>114/115/116/117</td>\n<td>\n<p>Change ST03N data check: Do the entry point check if the ST03N entries are available.</p>\n</td>\n</tr>\n<tr>\n<td>113</td>\n<td>\n<p>Enhance performance issue for IDoc relevance check.</p>\n</td>\n</tr>\n<tr>\n<td>109~110</td>\n<td>\n<p>Bugfix:</p>\n<ul>\n<li>Right propagation of highest return code (8 + 7 + applied exemption is still rc=8)</li>\n<li>Check for the missing putstatus “I” to get the correct SUM phase</li>\n</ul>\n</td>\n</tr>\n<tr>\n<td>106~107</td>\n<td>\n<p>Bugfix: the check result is wrong when check the table with DATS or TIMS data type relevant condition.</p>\n</td>\n</tr>\n<tr>\n<td>104~105</td>\n<td>\n<p>Bugfix for relevance check.</p>\n</td>\n</tr>\n<tr>\n<td>103</td>\n<td>\n<p>Update the note title and description. <strong>SAP Note 2502552 is not required for SAP Readiness Check</strong>. It is only for consistency check.</p>\n</td>\n</tr>\n<tr>\n<td>100</td>\n<td>\n<p>Update the note description to add the minimum recommendation for SAP S/4HANA 1809 Initial Shipment.</p>\n<p>No code updated, so for customers who have already implemented the SAP Note, which fulfills the minimum recommendation, it is not required to update.</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 167}, {"note": "2343374", "noteTitle": "2343374 - S4TWL - Simplified ACLs (Access Control Lists) concept in SAP Portfolio and Project Management for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 Transition, Pre-conversion check, Pre-check, Pre-transformation check to S/4, S/4 PPM, S4PPM, S/4 Portfolio and Project Management <br/>Authorization Inheritance Information Migration, /S4PMM/AUTH_REF, report DPR_PRE_CHECK_S4MIG_AUTHREF</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>A simplified ACLs (Access Control Lists) concept in SAP Portfolio and Project Management for SAP S/4HANA is now in place to reduce business complexity. Some options are not available any longer and a simplified logic leads to different results. Major changes are:</p>\n<p>a) All Authorization holders have same weight. For example priority of ‘User’ level authorization before ‘User Group’ authorization will be skipped.</p>\n<p>b) Local Authorizations are no longer stronger than inherited ones. All activities are inherited down from all levels above.</p>\n<p>c) Authorizations related to organizational units of project elements are not available in SAP Portfolio and Project Management for SAP S/4HANA.</p>\n<p>d) 'None' activity is not available in SAP Portfolio and Project Management for SAP S/4HANA.</p>\n<p>e) Versions have no own authorizations any more but refer to authorization of corresponding operative project.</p>\n<p>f) User Groups are only considered up to the third level until level 3.</p>\n<p>d) Activity hierarchy Admin - Write - Read is fixed for Fiori user interfaces. There is no restriction for Web Dynpro user interfaces.</p>\n<p>e) Dedicated Authorizations for folders and documents are not supported any more. They inherit the authorizations from their parent project element, e.g. task.</p>\n<p>f) New Super User concept available.</p>\n<p>SAP Note 2445069 provides a pre-conversion check for this feature.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Since the new and simplified ACLs (Access Control Lists) concept is now used, depending on the maintained authorizations the results of authorization determination could now differ from results of the non simplified authorization concept in the SAP Portfolio and Project Management solution. You can now check and adapt the authorizations maintained in your projects according to the information given by the pre-check.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In case you used some of the options not available any longer, adjust the maintained authorizations and in addition make knowledge transfer to key and end users.</p>\n<p>The note 2321885 contains the report DPR_PRE_CHECK_S4MIG_AUTHREF to analyze the authorizations in a more detailed way than the corresponding pre-conversion check.</p>\n<p><strong><span en-us;\"=\"\" lang=\"EN-US\" mso-ansi-language:=\"\" verdana',sans-serif;=\"\">How to Determine Relevancy</span></strong></p>\n<p><span 'times=\"\" ar-sa;\"=\"\" calibri;=\"\" en-us;=\"\" lang=\"EN-US\" minor-bidi;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-font-family:=\"\" mso-bidi-language:=\"\" mso-bidi-theme-font:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman';=\"\" verdana',sans-serif;=\"\">This Simplification Item is relevant  in case options have been used that are no longer supported or in case authorizations have been set up in such a way that the new authorization logic leads to different results.</span></p>\n<p> </p>", "noteVersion": 7}, {"note": "2348430", "noteTitle": "2348430 - S4TWL - cFolders not available anymore in SAP Portfolio and Project Management for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Renovation and Simplification of Functionality</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>With SAP Portfolio and Project Management for SAP S/4HANA, the cFolders component is not available anymore. SAP recommends to evaluate integration between Portfolio and Project Management and SAP Enterprise Product Development as a substitute for selected collaboration scenarios.</p>\n<p>SAP Note 2445069 provides a pre-conversion check for this feature.</p>\n<p><strong>Business Process related information</strong></p>\n<p>All the features of cFolders application and all the integration between cFolders and other applications, e.g. SAP PLM, SAP SRM etc., are not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Because cFolders is not available anymore with SAP Portfolio and Project Management for SAP S/4HANA, customers need to archive existing cFolders data before the system conversion by following the steps below:</p>\n<ol>\n<li>This step is mandatory to continue the conversion. Go to the collaboration overview page, select the collaborations you would like to archive, and click 'Archive' button in the toolbar above the list. Then the system will set a deletion indicator and remove the collaboration from the collaboration overview.</li>\n<li>This step is not mandatory for the conversion, but it's highly recommended to keep the integrity of your data. Use SAP Data Archiving and archive object 'CFX_ARCH' to archive the collaboration data.</li>\n</ol>\n<p>For the collaboration with partners, customers could consider to use other products like SAP Enterprise Product Development (EPD). Integration between SAP EPD collaboration and project elements is available via object links as of SAP S/4HANA 2021, with SAP S/4HANA 2021 FPS1 integration to portfolio items and initiatives is planned. Further information is available via <a href=\"https://help.sap.com/viewer/db719753e69f4e8eb9902aaea0fd8471/LATEST/en-US/7177d4286bc54c6b8aefe15898b6c06f.html\" target=\"_blank\">help.sap.com</a>.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if:</p>\n<ul>\n<li>cFolders is used if entries exist in Table CFX_COL, and column TO_BE_ARCHIVED is not set to 'X' (check with SE16)</li>\n</ul>", "noteVersion": 3}, {"note": "2321885", "noteTitle": "2321885 - S4TC CPRXRPM Master Check for S/4 System Conversion Checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Pre-conversion checks for SAP Portfolio and Project Management need to be executed before the conversion to SAP Portfolio and Project Management for S/4HANA.</p>\n<p>As a follow up of the pre-conversion check or to prepare a migration, the currently used authorizations maybe need to be analyzed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 Conversion, DPR_PRE_CHECK_S4MIG_AUTHREF, S/4 Portfolio and Project Management <br/>Authorization Inheritance Information Migration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the correction instructions in this SAP Note in order to enable a pre-conversion check or to use the report DPR_PRE_CHECK_S4MIG_AUTHREF to analyze the authorizations in detail in your system.</p>\n<p><strong>Pre-conversion checks to prepare a conversion to SAP Portfolio and Project Management for S/4HANA</strong></p>\n<p>The following checks will be executed during the pre-conversion check:</p>\n<p><span>Simplified ACLs (Access Control Lists) concept in SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2343374, below the messages that could be raised during the check.</p>\n<ul>\n<li>'Authorizations on Organizational unit level are not supported anymore (for details please check SAP Note 2343374).' (Check ID: PPM_FUNC_ACL_01_03)</li>\n<li>'Option \"None\" is not supported anymore on authorization level (for details please check SAP Note 2343374).'  (Check ID: PPM_FUNC_ACL_01_04)</li>\n<li>'Object Type-Related Activities for Authorization Check contain changes in fix activity hierarchy Admin -&gt; Write -&gt; Read (for details please check SAP Note 2343374).'  (Check ID: PPM_FUNC_ACL_01_01)</li>\n<li>'Hierarchies of User Groups to User Group for Authorization are only allowed up to a level of 3 (for details please check SAP Note 2343374).'  (Check ID: PPM_FUNC_ACL_01_02)</li>\n</ul>\n<p><span>Control Plans in SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2353885 and in consulting note 2363785, below the messages that could be raised during the check.</p>\n<ul>\n<li>'The control plan integration is not available anymore (for details please check SAP Note 2353885).' (Check ID: PPM_FUNC_CNTR_PLAN_01)</li>\n<li>'The control plan UI field customizing is deprecated (for details please check SAP Note 2363785).'(Check ID: PPM_FUNC_CNTR_PLAN_02)</li>\n</ul>\n<p><span>SRM Integration in SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2359354, below the messages that could be raised during the check.</p>\n<ul>\n<li>'The SRM integration is not available anymore (for details please check SAP Note 2359354).' (Check ID: PPM_INTGR_SRM_01)</li>\n</ul>\n<p><span>Global Settings in SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2359662, below the messages that could be raised during the check.</p>\n<ul>\n<li>'Global Settings: Language independent Notes: All Notes are now language independent (for details please check SAP Note 2359662).' (Check ID: PPM_GLOBAL_SETTINGS_07_17)</li>\n<li>'Global Settings: Check-in/Check-out of Documents: The ability to Check-in/Check-out documents using a plain HTTP connection was removed (for details please check SAP Note 2359662).' (Check ID:  PPM_GLOBAL_SETTINGS_07_27)</li>\n</ul>\n<p><span>Web Dynpro Applications for Resource Maintenance in SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2358376, below the messages that could be raised during the check.</p>\n<ul>\n<li>‘The ability to edit Business Partner data in the UI of SAP Portfolio and Project Management for SAP S/4HANA is not available anymore (for details please check SAP Note 2358376).’ (Check ID:  PPM_UI_RES_02)</li>\n</ul>\n<p><span>Microsoft Project In- and Export of SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2358463, below the messages that could be raised.</p>\n<ul>\n<li>‘The ability to export or import Microsoft Project data based on .MPP files is not available anymore (for details please check SAP Note 2358463).’ (Check ID: PPM_INTGR_MSPC_01)</li>\n</ul>\n<p><span>SAP  Workforce Deployment (WFD) integration is not available anymore in SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2358519, below the messages that could be raised during the check.</p>\n<ul>\n<li>'SAP Workforce Deployment (WFD) integration in SAP Portfolio and Project Management is not available anymore (for details please check SAP Note 2358519).' (Check ID: PPM_ INTGR_WFM_WFD_01)</li>\n</ul>\n<p><span>SAP cFolders not available anymore in SAP Portfolio and Project Management for SAP S/4HANA</span><br/>Details can be found in SAP Note 2348430, below the messages that could be raised during the check.</p>\n<ul>\n<li>'SAP cFolders is not available anymore (for details please check SAP Note 2348430).’ (Check ID: PPM_FUNC_CFOLDERS_01)</li>\n</ul>\n<p><span>KM Documents in SAP Portfolio and Project Management for SAP S/4HANA<br/></span>Details can be found in SAP Note 2354985, below the messages that could be raised during the check.<span><br/></span></p>\n<ul>\n<li>'The usage of Knowledge Management (KM) for Document Management is no longer available (for details please check SAP Note 2354985).' (Check ID: PPM_INTGR_KM_DOC_01)</li>\n</ul>\n<p><span>The release of SAP Portfolio and Project Management is lower than 6.1</span><br/>Details can be found in SAP Note 2349633, below the messages that could be raised during the check.</p>\n<ul>\n<li>'The release of SAP Portfolio and Project Management is lower than 6.1(for details please check SAP Note 2349633)’ (Check ID: PPM_SIMPL_BEFORE_S4H_01)</li>\n</ul>\n<p><strong>Detail analysis of used authorizations</strong></p>\n<p>In addition, this SAP Note delivers the report DPR_PRE_CHECK_S4MIG_AUTHREF, which can be used to further analyze the used authorizations. The section below describes how a customer can use that report.</p>\n<p><span>To identify </span><span>before</span> the conversion to SAP S/4 HANA if your definition of authorization in PPM is affected by the described changes, you can execute the report DPR_PRE_CHECK_S4MIG_AUTHREF and inspect the execution result of the report. The following execution results may be displayed (Changes to projects authorizations should be made before data migration in case return code is 4, return code 0 signals that all prerequisite for migration of Authorization Inheritance Information are met):</p>\n<p> a) 'Authorizations on Organizational unit level not supported anymore':</p>\n<p>Depending on the displayed client logon to the client of the system mentioned. Display the project element related to the following Object types DPO (Project), PPO (Phase), TTO (Task), CTO (Checklist), ITO (Checklist Item), RAG (Checklist reference) by navigating to the work center Project Management. Choose Service -&gt; Search. On the appearing search popup choose \"Search for:\" according to the Object type listed. Fill the listed EXTERNAL_ID (Number) into the Search Criteria field Project Number/Phase Number/Number and click \"Find button\". Click on the search result to display the project element detail screen. Navigate to the tab \"Authorizations\" -&gt; \"Organizational Units\". Be aware that the maintained Organizational Unit(s) are not considered in S/4 PPM.</p>\n<p><br/> b) 'Option \"None\" is not supported anymore on authorization level':</p>\n<p>Depending on the displayed client logon to the client of the system mentioned. Display the project element related to the following Object types DPO (Project), PPO (Phase), TTO (Task), CTO (Checklist), ITO (Checklist Item), RAG (Checklist reference) by navigating to the work center Project Management. Choose Service -&gt; Search. On the appearing search popup choose \"Search for:\" according to the Object type listed. Fill the listed EXTERNAL_ID (Number) into the Search Criteria field Project Number/Phase Number/Number and click \"Find button\". Click on the search result to display the project element detail screen. Navigate to the tabs</p>\n<p>\"Authorizations\" -&gt; \"Users\"<br/>\"Authorizations\" -&gt; \"User Groups\"<br/>\"Authorizations\" -&gt; \"Organizational Units\"<br/>\"Authorizations\" -&gt; \"Roles\"</p>\n<p>Be aware that the functionality maintained by checking the \"None\" flag is not supported in S/4 PPM anymore. Versions of a project are not listed in detail in the report protocol. In case versions to a project exist the protocol may contain duplicate entries related to a project number.</p>\n<p>The impact of the new concept (without 'no Auth') is that 'strictly confidential' documents after changing the authorizations in a project can now be accessed by users that should not have access according <br/>to business reasons. Be aware of that and take that into account when changing authorizations as result of pre-check or the authorization analysis report results.</p>\n<p><br/> c) 'Object Type-Related Activities for Authorization Check contain changes in fix activity hierarchy Admin -&gt; Write -&gt; Read'</p>\n<p>Depending on the displayed client logon to the client of the system mentioned. Call transaction ACO3 to display \"Object Type-Related Activities for Authorization Check\". Remember the content of column OBJECT_TYPE listed in the result of the pre-check report and click on the button \"Position ...\" of the view. Enter the OBJECT_TYPE into the field Object Category and confirm the value. Be aware that the Object Type-Related Activities for Authorization Check have a fixed activity hierarchy Admin -&gt; Write -&gt; Read'. This restiction is valid for the fiori UI's. There are no restrictions regarding the Web dynpro UI.</p>\n<p><br/> d) 'Hierarchies of User Groups to User Group for Authorization are only allowed up to a level of 3':</p>\n<p>Depending on the displayed client logon to the client of the system mentioned. Navigate to the work center \"Portfolio and Project Administration\" and choose \"Project User Groups\". Click button \"Open\" and enter the displayed User Group Name listed in the result of the report into the field \"Name\". Be aware that the User Group Hierarchy is only evaluated up to a level of 3.</p>\n<p>In order to maintain the project user groups call transaction /NNWBC and navigate to workcenter Portfolio and Project Administration -&gt; Project User Groups</p>\n<p> </p>", "noteVersion": 11}, {"note": "2182725", "noteTitle": "2182725 - S4TC Delivery of the SAP S/4HANA System Conversion Checks for SAP S/4HANA 1511 or 1610", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Checks have to be executed before the conversion to SAP S/4HANA, if all preliminary steps in the source system have been performed.</p>\n<p><strong><strong>Note: </strong>The check report delivered via this note was exclusively used for system conversions to SAP S/4HANA 1511 and SAP S/4HANA 1610. As system conversions to SAP S/4HANA 1511 are no longer supported since May 2018 (and respectively to SAP S/4HANA 1610 since May 2019), this report and this note are obsolete and will be removed in the near future.</strong></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Conversion to SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement this note as well as the SAP notes mentioned in the manual activities document.</p>\n<p>For further information please refer to the respective SAP S/4HANA conversion procedure guide.</p>\n<p>The report R_S4_PRE_TRANSITION_CHECKS, which is delivered with this SAP note,</p>\n<ul>\n<li>can be executed <strong>standalone</strong> and calls all available pre–conversion checks which are delivered with the SAP notes mentioned in the manual activities document of this note.</li>\n<li>can be executed as often as required. When called in 'Simulation Mode', nothing is persisted, otherwise the report output is saved as 'Application Log' entry. Use the respective display option on the report selection screen to search for respective application log entries and to display them.</li>\n<li>is called automatically in the conversion procedure by SUM (Software Update Manager) to execute the pre-conversion checks.</li>\n</ul>\n<p>When you execute the pre–conversion checks <strong>standalone </strong>using report R_S4_PRE_TRANSITION_CHECKS:</p>\n<ul>\n<li>We recommend to always choose the option 'Simulation Mode', so that all available pre–conversion checks are executed despite of erroneous or missing pre–check classes.</li>\n<li>We recommend to save the entries on the selection screen of report R_S4_PRE_TRANSITION_CHECKS as a report variant.</li>\n<li>We recommend to execute the report as batch job using the above mentioned report variant.</li>\n<li>You can ignore error messages in the pre–conversion check result list concerning missing pre–conversion check classes. Only when the checks are executed by SUM, information about software components that do not require a pre–conversion check class is available.</li>\n</ul>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed for the very first time, field labels for the selection screen fields are missing, because such texts can not be delivered via correction instruction. But these texts are generated for the current logon language and persisted during this very first report execution.</p>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed <strong>with the option 'Check Class Consistency Check'</strong>, the report only checks if the respective check class methods can be dynamically called, <strong>but the real pre-conversion checks are <span><em>not executed</em></span></strong>.</p>\n<p>When the report R_S4_PRE_TRANSITION_CHECKS is executed and the option <strong>'Pre-Conversion </strong><strong>Check Results' </strong>is selected:</p>\n<ul>\n<li>If the checkbox ‘Simulation Mode’ is selected:</li>\n<ul>\n<li><strong>no</strong> application log entry is persisted, the output is shown on screen (online execution) or in spool (batch execution).</li>\n<li>consistency check errors <strong>are ignored</strong>, all usable checks are executed.</li>\n</ul>\n<li>If the checkbox ‘Simulation Mode’ is not selected:</li>\n<ul>\n<li>The output is only persisted as application log.</li>\n<li>consistency check errors <strong>are not ignored, </strong>checks are only executed if all checks are consistent.</li>\n</ul>\n</ul>\n<p>If one of the dynamically called pre-check classes returns more than 10000 check result lines, a respective error message is written into the check result (and the huge amount of more than 10000 lines is ignored in the result list in order to prevent an internal memory overflow of the used application log functionality). Contact in such a case the pre-check class responsible so that the class code can be corrected. Use the application component that is mentioned in the pre-check result to create an incident.</p>", "noteVersion": 56}, {"note": "2353984", "noteTitle": "2353984 - S4TWL - HTTP-Based Document Management in SAP Portfolio and Project Management for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP Portfolio and Project Management for SAP S/4HANA, the HTTP-Based Document Management for the check-in/check-out of documents is no longer available. The HTTP-Based check-in/check-out feature had provided the possibility to upload and download documents including the possibility to checkout a document and open a document with the specified client application. The HTTP-based check-in/check-out required a Java installation and a valid certificate on client-side.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>No effects on business processes are expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>The functionality to upload and download documents is still available and can be used. No further actions necessary.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>Please check the Global Settings in Customizing: SAP Portfolio and Project Management -&gt; Project Management -&gt; Basic Settings -&gt; Override Default Global Settings.<br/>If Master Switch 0007 (General Default Values) 0027 (Check-in/Check-out of Documents (' ': Normal, 'X': HTTP-Based)) is set to ‘X’, the Transition Worklist item is relevant.</p>", "noteVersion": 8}, {"note": "2359662", "noteTitle": "2359662 - S4TWL - Global Settings in SAP Portfolio and Project Management for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your existing system landscape from SAP Portfolio and Project Management to SAP Portfolio and Project Management for SAP S/4HANA. The following SAP Portfolio and Project Management for SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The following Customizing settings under SAP Portfolio and Project Management -&gt; Project Management -&gt; Basic Settings -&gt; Check Global Settings have been changed in SAP Portfolio and Project Management for SAP S/4HANA.</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"1\" cellspacing=\"1\" class=\"table table-bordered table-striped col-resizeable\"><colgroup> <col width=\"67\"/> <col width=\"294\"/> <col width=\"50\"/> <col width=\"519\"/> <col width=\"127\"/> <col width=\"253\"/></colgroup>\n<tbody>\n<tr>\n<td class=\"xl71\" height=\"46\" width=\"67\">Area</td>\n<td class=\"xl72\" width=\"294\">Text</td>\n<td class=\"xl71\" width=\"50\">Name</td>\n<td class=\"xl72\" width=\"519\">Text</td>\n<td class=\"xl73\" width=\"127\">Delivered Default Settings Value</td>\n<td class=\"xl74\" width=\"253\">Availability in S/4HANA PPM</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">2</td>\n<td class=\"xl80\">Master Switches</td>\n<td class=\"xl79\">5</td>\n<td class=\"xl80\">Integration with Other Components (see SAP Note 1391444)</td>\n<td class=\"xl81\">                                        </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the features require further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">2</td>\n<td class=\"xl80\">Master Switches</td>\n<td class=\"xl79\">7</td>\n<td class=\"xl80\">Integration with PS and FI/CO (see SAP Note 1427365)</td>\n<td class=\"xl81\">                                        </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">2</td>\n<td class=\"xl80\">Master Switches</td>\n<td class=\"xl79\">8</td>\n<td class=\"xl80\">Enhanced Capital and Resource Mgmt Switch (see SAP Note 1457426)</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">2</td>\n<td class=\"xl85\">Master Switches</td>\n<td class=\"xl84\">13</td>\n<td class=\"xl85\">Flag is Obsolete (SAP Note 1759150)</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">2</td>\n<td class=\"xl85\">Master Switches</td>\n<td class=\"xl84\">14</td>\n<td class=\"xl85\">Flag is Obsolete - Change Documents for Financial and Capacity Planning</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">2</td>\n<td class=\"xl80\">Master Switches</td>\n<td class=\"xl79\">15</td>\n<td class=\"xl80\">Milestone Filter for Tasks  ('X'=On, ' '=Off)</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">2</td>\n<td class=\"xl80\">Master Switches</td>\n<td class=\"xl79\">16</td>\n<td class=\"xl80\">Accumulate the Number of Objects in the Dashboard ('X'=On, ' '=Off)</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">2</td>\n<td class=\"xl85\">Master Switches</td>\n<td class=\"xl84\">17</td>\n<td class=\"xl85\">Flag is obsolete (SAP Note 1780409)</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">2</td>\n<td class=\"xl85\">Master Switches</td>\n<td class=\"xl84\">18</td>\n<td class=\"xl85\">Flag is Obsolete - Automatic Refresh of Project Dashboards ('X'=On, ' '=Off)</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">2</td>\n<td class=\"xl80\">Master Switches</td>\n<td class=\"xl79\">21</td>\n<td class=\"xl80\">Integration with PLM Engineering Record ( ‘X’ = ON, '' = OFF)</td>\n<td class=\"xl81\">                                        </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">2</td>\n<td class=\"xl80\">Master Switches</td>\n<td class=\"xl79\">22</td>\n<td class=\"xl80\">Activate Operative Object Links ( 'X' = ON, '' = OFF)</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">6</td>\n<td class=\"xl85\">User Interface Settings</td>\n<td class=\"xl84\">21</td>\n<td class=\"xl85\">DEPRECATED: Display Additional Information on Gantt Chart (' ': No, 'X': Yes)</td>\n<td class=\"xl86\">                                        </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">6</td>\n<td class=\"xl85\">User Interface Settings</td>\n<td class=\"xl84\">23</td>\n<td class=\"xl85\">Flag is obsolete (SAP Note 1756616)</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">6</td>\n<td class=\"xl85\">User Interface Settings</td>\n<td class=\"xl84\">24</td>\n<td class=\"xl85\">Flag is obsolete (SAP Note 1769591)</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">26</td>\n<td class=\"xl80\">Display Additional Information on Multi-Project Monitor (' ': No, 'X': Yes)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">28</td>\n<td class=\"xl80\">Adjustment of Structure Tree Area by Splitter ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">29</td>\n<td class=\"xl80\">Delete Button in Project Dashboard ('X'=Yes, ' '=No)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"21\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">30</td>\n<td class=\"xl80\">Data Aggregation in Projects ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">31</td>\n<td class=\"xl80\">Fullscreen Mode for Graphical View ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">32</td>\n<td class=\"xl80\">Move-Up/Move-Down Buttons in Project ('X'=Yes, ' '=No)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">33</td>\n<td class=\"xl80\">Summary of Project Data in Multi-Project Monitor ('X'=Yes, ' '=No)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">34</td>\n<td class=\"xl80\">Concurrent Status Changes for Multiple Tasks ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">35</td>\n<td class=\"xl80\">Display Mirrored Task Relationships in MP Monitor ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">36</td>\n<td class=\"xl80\">Assignment of Multiple Roles to Tasks at a Time (‘X’=Enabled, ‘ ‘=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated  by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">37</td>\n<td class=\"xl80\">Allow Constraint Icons without Add. Info. in Graphical View ('X'=On, ' '=Off)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">38</td>\n<td class=\"xl80\">Enhance Display of Details Window in Graphical View ('X'=On, ' '=Off)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">40</td>\n<td class=\"xl80\">Additional Display Options for Assignments to Project Elements (' '=No, 'X'=Yes)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">41</td>\n<td class=\"xl80\">One-Click Completion of Subordinate Project Elements (' '=No, 'X'=Yes)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">6</td>\n<td class=\"xl80\">User Interface Settings</td>\n<td class=\"xl79\">42</td>\n<td class=\"xl80\">Improved Alignment of Project Evaluation Display (' '=No, 'X'=Yes)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">7</td>\n<td class=\"xl80\">General Default Values</td>\n<td class=\"xl79\">17</td>\n<td class=\"xl80\">SAP Notes Language Independent</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">7</td>\n<td class=\"xl80\">General Default Values</td>\n<td class=\"xl79\">22</td>\n<td class=\"xl80\">Document Alignment(' ': KM, 'X': KPRO)</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">7</td>\n<td class=\"xl85\">General Default Values</td>\n<td class=\"xl84\">23</td>\n<td class=\"xl85\">Default number of columns initially displayed in Financial and Capacity Planning</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl88\" height=\"20\">7</td>\n<td class=\"xl89\">General Default Values</td>\n<td class=\"xl88\">27</td>\n<td class=\"xl89\">Check-in/Check-out of Documents (' ': Normal, 'X': HTTP-Based)</td>\n<td class=\"xl90\"> </td>\n<td class=\"xl91\">Switch will be Removed. Feature will no longer be available.</td>\n</tr>\n<tr>\n<td class=\"xl88\" height=\"20\">7</td>\n<td class=\"xl89\">General Default Values</td>\n<td class=\"xl88\">28</td>\n<td class=\"xl89\">HTTP Destination for SAP BusinessObjects Explorer</td>\n<td class=\"xl90\"> </td>\n<td class=\"xl91\">Switch will be Removed. Feature will no longer be available.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">7</td>\n<td class=\"xl85\">General Default Values</td>\n<td class=\"xl84\">31</td>\n<td class=\"xl85\">Flag is obsolete (SAP Note 1677356)</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">7</td>\n<td class=\"xl85\">General Default Values</td>\n<td class=\"xl84\">36</td>\n<td class=\"xl85\">Flag is obsolete: Activate calculation of derived capacity attribute</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">7</td>\n<td class=\"xl85\">General Default Values</td>\n<td class=\"xl84\">38</td>\n<td class=\"xl85\">Flag is obsolete (SAP Note 1755486)</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">7</td>\n<td class=\"xl80\">General Default Values</td>\n<td class=\"xl79\">39</td>\n<td class=\"xl80\">Activate Multiple Object Selection for Reporting Cockpit ('X' =Yes, ' ' = No )</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl84\" height=\"20\">7</td>\n<td class=\"xl85\">General Default Values</td>\n<td class=\"xl84\">40</td>\n<td class=\"xl85\">Flag is obsolete</td>\n<td class=\"xl86\"> </td>\n<td class=\"xl87\">Switch is obsolete and will be deleted.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">7</td>\n<td class=\"xl80\">General Default Values</td>\n<td class=\"xl79\">46</td>\n<td class=\"xl80\">Override Authorizations for Documents in Item Versions ('X'=Yes, ' '=No)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated available by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">9</td>\n<td class=\"xl80\">Portfolio Item Project Integration</td>\n<td class=\"xl79\">9</td>\n<td class=\"xl80\">Use Project Calendar for Linked Item ('X'=Yes, ' '=No)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">11</td>\n<td class=\"xl80\">Staffing and Resource Management</td>\n<td class=\"xl79\">1</td>\n<td class=\"xl80\">Updating Staffing According To Tasks ('X'=Enabled, '  '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">11</td>\n<td class=\"xl80\">Staffing and Resource Management</td>\n<td class=\"xl79\">2</td>\n<td class=\"xl80\">Confirmed Role Efforts in Staffing Overview of Projects ('X'=Yes, '  '=No)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">11</td>\n<td class=\"xl80\">Staffing and Resource Management</td>\n<td class=\"xl79\">3</td>\n<td class=\"xl80\">Export of Cross-Project Resource/Staffing Data ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">11</td>\n<td class=\"xl80\">Staffing and Resource Management</td>\n<td class=\"xl79\">4</td>\n<td class=\"xl80\">Consider User Group Authorization in Resource Management (Yes = 'X', No = ' ')</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">11</td>\n<td class=\"xl80\">Staffing and Resource Management</td>\n<td class=\"xl79\">5</td>\n<td class=\"xl80\">Personalized Layout for Resource Management Screens ('X'=Yes, ' '=No)</td>\n<td class=\"xl81\">                                        </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">11</td>\n<td class=\"xl80\">Staffing and Resource Management</td>\n<td class=\"xl79\">6</td>\n<td class=\"xl80\">SAP Notes in Cross-Project Staffing Overview ('X'=Yes, ' '=No)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">11</td>\n<td class=\"xl80\">Staffing and Resource Management</td>\n<td class=\"xl79\">7</td>\n<td class=\"xl80\">Updating Multiple Roles According to Tasks ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">11</td>\n<td class=\"xl80\">Staffing and Resource Management</td>\n<td class=\"xl79\">8</td>\n<td class=\"xl80\">Project Number as Search Criterion in Role Search ('X'=Yes, ' '=No)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">11</td>\n<td class=\"xl80\">Staffing and Resource Management</td>\n<td class=\"xl79\">12</td>\n<td class=\"xl80\">Retain Node Expansion After Saving in Overview Screens ('X'=Yes, ' '=No)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">12</td>\n<td class=\"xl80\">Scheduling and Project Structuring</td>\n<td class=\"xl79\">1</td>\n<td class=\"xl80\">Automatic Scheduling of All Project Elements ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">12</td>\n<td class=\"xl80\">Scheduling and Project Structuring</td>\n<td class=\"xl79\">3</td>\n<td class=\"xl80\">Creation/Deletion of Multiple Relationships ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">12</td>\n<td class=\"xl80\">Scheduling and Project Structuring</td>\n<td class=\"xl79\">4</td>\n<td class=\"xl80\">Optimization of Task and Milestone Relationships ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">12</td>\n<td class=\"xl80\">Scheduling and Project Structuring</td>\n<td class=\"xl79\">5</td>\n<td class=\"xl80\">Changing Scheduling Type in Project ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">12</td>\n<td class=\"xl80\">Scheduling and Project Structuring</td>\n<td class=\"xl79\">6</td>\n<td class=\"xl80\">Preserve Relationships When Canceling Tasks ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">13</td>\n<td class=\"xl80\">Additional Features</td>\n<td class=\"xl79\">1</td>\n<td class=\"xl80\">Sending E-Mails from Project Elements ('X' = Enabled, ' ' = Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">13</td>\n<td class=\"xl80\">Additional Features</td>\n<td class=\"xl79\">2</td>\n<td class=\"xl80\">Export/Import of Templates ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">13</td>\n<td class=\"xl80\">Additional Features</td>\n<td class=\"xl79\">4</td>\n<td class=\"xl80\">Comparison with Actual Dates in Graphical View ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">13</td>\n<td class=\"xl80\">Additional Features</td>\n<td class=\"xl79\">5</td>\n<td class=\"xl80\">Export of Multi-Project Monitor Data ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\"> </td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">13</td>\n<td class=\"xl80\">Additional Features</td>\n<td class=\"xl79\">6</td>\n<td class=\"xl80\">Export of Resource Planning Data ('X‘=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">13</td>\n<td class=\"xl80\">Additional Features</td>\n<td class=\"xl79\">7</td>\n<td class=\"xl80\">Export of Graphical View ('X' =Enabled, ' ' =Disabled)</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">13</td>\n<td class=\"xl80\">Additional Features</td>\n<td class=\"xl79\">8</td>\n<td class=\"xl80\">Export of Evaluation Data ('X‘=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n<tr>\n<td class=\"xl79\" height=\"20\">13</td>\n<td class=\"xl80\">Additional Features</td>\n<td class=\"xl79\">9</td>\n<td class=\"xl80\">Project-Specific Display of Earliest or Latest Dates ('X'=Enabled, ' '=Disabled)</td>\n<td class=\"xl81\">X</td>\n<td class=\"xl70\">Switch will be removed. Feature will be activated by default. In most cases, using the feature requires further configuration.</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p>SAP Note 2321885 provides a pre-conversion check for some of these features.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>No effects on business processes are expected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Review the settings in your system before the conversion. If you have overwritten the default values, SAP recommends to delete the entries that have been deleted by SAP.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Transition Worklist item is relevant if you are using SAP Portfolio and Project Management for SAP S/4HANA.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"168\">\n<p>Conversion pre-checks</p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>SAP Note 2321885<span> </span></p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"168\">\n<p>Http-based Check-In/Check-out</p>\n</td>\n<td valign=\"top\" width=\"436\">\n<p>SAP Note 2353984</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 8}]}], "activities": [{"Activity": "Data cleanup / archiving", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "The SRM Integration for external service procurement is not available in SAP Portfolio and Project Management for SAP S/4HANA. SAP Note 2321885 provides a pre-conversion check for this feature. The SRM Integration related data cannot be migrated to SAP Portfolio and Project Management for SAP S/4HANA, therefore it is required to clean up the data before the system conversion in order to prevent any data loss. Archive the corresponding projects or delete not needed SRM Integration related data before you continue the system conversion."}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}]}