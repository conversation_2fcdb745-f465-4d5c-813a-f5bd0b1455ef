{"guid": "0050569455E21ED5B3E17678391F809E", "sitemId": "SI1: Logistics_TRA", "sitemTitle": "S4TWL - Transportation (LE-TRA)", "note": 2270199, "noteTitle": "2270199 - S4TWL - Transportation (LE-TRA)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Logistics Execution Transportation (LE-TRA) is not the target architecture within SAP S/4HANA. The application LE-TRA is within SAP S/4HANA in the Compatibility Scope - for details please refer to note 2269324. The functionality is available in SAP S/4HANA but not considered as future technology. The  alternative functionality is selected features of Transportation Management which is already available within SAP S/4HANA.</p>\n<p>Please note however the following exception (details please see in the attachment below):</p>\n<ul>\n<li>The<strong> Route</strong> object of application component LE-TRA and the corresponding objects, which are needed for the definition and determination of this route, have been moved from the Compatibility Scope into the Perpetual Scope of SAP S/4HANA. <br/> In other words: The customers can use those objects after end of the Compatibility Scope (planned currently for end of 2030) in SAP S/4HANA.<br/><br/>That allows the customer to use these objects for existing funcionality in other applications e.g. scheduling in sales order / stock transport order / delivery, etc.<br/><br/><strong>Important</strong>: All other objects from LE-TRA which are used for the Transportation functionality will remain in the Compatibility Scope and cannot be used after end of compatibility scope which is currently planned for end of 2030.<br/>This would primarily be the shipment document + shipment cost document and all corresponding objects / customizing (e.g. transportation connection point, route stage definition) / master data / interfaces.<br/><br/><strong>The Route in SAP S/4HANA is no strategic object:<br/></strong>SAP does not plan further innovations for those objects. As well no integration into new innovations e.g. Product Compliance is planned.In particular no integration into SAP S/4HANA Supply Chain for Transportation Management is available or planned. No standard migration tool from LE-TRA to SAP S/4HANA TM is planned either.<br/><br/></li>\n<li>\n<p>LE-TRA and the <strong>industry solution IS-OIL</strong> are functionally different solutions, however they share some development objects on a technical level. These shared development objects can continued to be used, even after the expiry of the compatibility pack license, but only in OIL&amp;GAS context. These are for example: the customizing for the Shipment Type (TVTK) or for the Transportation Planning Point (TTDS).</p>\n<p>As well for OIL&amp;GAS Customers: Especially the usage of the LE-TRA shipment cost document is not possible in SAP S/4HANA after the expiry of the compatibility pack. Instead the settlement of freight costs from TD Shipments / TSW Nominations can be handled via TM Freight Cost Settlement Management.</p>\n</li>\n<li>\n<p>For an <strong>EWM integration scenario</strong>, the SHPMNT IDOC and the TPSSHT IDOC which belong technically to LE-TRA can be used in the Perpetual Scope of SAP S/4HANA. This is valid ONLY for the remote integration of data from a Business Suite SAP ERP or Non SAP Systems to create EWM Transportation Units. This specific EWM integration will allow to receive planning data into a EWM Transportation Unit and send planning / execution data back.<br/><br/></p>\n</li>\n</ul>\n<p><strong>Business Process related information</strong></p>\n<p>No influence is expected on the current business processes realted to Logistics Execution Transportation (LE-TRA). The related functionality is still availalbe within SAP S/4HANA.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Logistics Execution Transportation (LE-TRA) is not the target architecture anymore within SAP S/4HANA,.</p>\n<p>The alternative functionality is the basic variant of Transportation Management within SAP S/4HANA, which is already available.<br/>The main functions of LE-TRA have already been replaced with those selected functions of SAP S/4HANA Supply Chain for Transportation Management. <br/><br/>Currently, that means until the expiration of the usage rights of the Compatibility Packs, both technical applications (LE-TRA and Transportation Management) are independent from each other and can be used next to each other in the same SAP S/4HANA database instance.<br/>However they are not integrated with each other and there is no data exchange.</p>\n<p><strong>How to determine Relevancy </strong></p>\n<p>The Simplification Item is relevant in the following cases:</p>\n<p>-  the related customizing for LE-TRA is maintained<br/>--&gt; SPRO: Logistics Execution - Transportation (exception would be the LE-TRA Route determination)</p>\n<p>- the readyness check lists table entries e.g.<br/>-&gt; Tables VTTK, VTPA, VTTS, VFKK are checked</p>\n<p><strong>Detailed description in SAP S/4HANA:  <br/>LE-TRA functionality and the equivalent with basic Transportation Management</strong></p>\n<p><strong><a href=\"https://www.sap.com/germany/documents/2021/08/7231ed24-f47d-0010-bca6-c68f7e60039b.html\" target=\"_blank\">https://www.sap.com/germany/documents/2021/08/7231ed24-f47d-0010-bca6-c68f7e60039b.html</a></strong></p>\n<p>In addition please find the detailed description of the parcel process in SAP S/4HANA und in the attachments:<br/>LE-TRA-XSI functionality and the equivalent process within basic Transportation Management</p>\n<p><strong>For convenience here is the link to the corresponing simplification item of LE-TRA: </strong></p>\n<p><a href=\"https://launchpad.support.sap.com/#sic/itemSet('0050569455E21ED5B3E17678391F809E','false')\" target=\"_blank\"><strong>https://launchpad.support.sap.com/#sic/itemSet('0050569455E21ED5B3E17678391F809E','false')</strong></a></p>\n<p><strong> </strong></p>", "noteVersion": 14, "refer_note": [{"note": "3065464", "noteTitle": "3065464 - Definition: Basic Shipping vs basic Transportation Management vs advanced Transportation Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You would like to understand the difference between Basic Shipping, (basic)Transportation Management and advanced Transportation Management. In addition you need to know which licenses are required for each option.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>License Information, Basic Shipping, (basic) Transportation Management, Advanced Transportation Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This note describes the differences between the solutions Basic Shipping, (basic) Transportation Management and Advanced Transportation Management.</p>\n<ol>\n<li><strong>Definition of solutions within SAP S/4HANA: Basic Shipping / (basic) Transportation Management / Advanced Transportation Management</strong></li>\n</ol>\n<p> </p>\n<p>In SAP S/4HANA a defined collection of business processes are relevant for shipping:</p>\n<p> </p>\n<p><strong>Basic Shipping</strong></p>\n<p>These processes can be implemented starting with SAP S/4HANA Release 1511 using the application LE-TRA. They can be used until end of life of the <a href=\"/notes/2269324\" target=\"_blank\">compatibility scope</a> which is currently planned by end of 2030. These features can be used without additional licenses.</p>\n<p><strong>(Basic) </strong><strong>Transportation Management                                                                          </strong></p>\n<p>Main functions of LE-TRA will be eventually replaced by parts of the SAP S/4HANA Supply Chain for transportation management functions.</p>\n<p>As of release 1709  SAP S/4HANA TM* is available in the digital core of SAP S/4HANA. Selected functions of SAP S/4HANA TM* define the base variant, which is referred to as Solution Capability 'Transportation Management'. This base variant <strong>does not require additional</strong> license fees as it is included in the standard SAP S/4HANA license.</p>\n<p>Please note:<br/>Before release 2020, the solution capability name <em>Basic Shipping</em> has been used not only for the LE-TRA functionality but as well for the base variant of SAP S/4 HANA TM*. With the release 2020 the solution capability has been renamed to <em>Transportation Management</em>. In the text for better transparency the termin (basic) Transportation Management will be used.</p>\n<p>Both technical solution capabilities – <em>Basic Shipping</em> and <em>Transportation Management</em> - are independent from each other and can be used in parallel on the same SAP S/4HANA database instance. They have no data exchange and no integration within the shipping process.</p>\n<p><strong>Advanced Transportation Management</strong></p>\n<p>This is the solution capability which requires additional TM engine licenses. The holistic end-to-end functionality from SAP S/HANA TM* can be used.</p>\n<p> </p>\n<ol start=\"2\">\n<li><strong>Documents with more details<br/><br/></strong>The documents (Slide deck + PDF) attached to this note describe the difference between what is available in (basic) Transportation Management and the holistic End-to-End variant Advanced Transportation Management within SAP S/4HANA. If the feature requires Advanced Transportation Management, then the respective license is listed.<strong><br/><br/></strong></li>\n<li><strong>Report to distinguish between basic TM vs. Advanced TM</strong></li>\n</ol>\n<p>  Please find in the referenced note below the link to the report.</p>\n<p> </p>\n<p><span>Disclaimer:</span></p>\n<p>These documents will be updated regularly for the most current release.</p>\n<p>The document will always be valid for current and prior releases.</p>\n<p>List includes features which are not available for all releases.</p>\n<p> </p>\n<p>*Term used as abbreviation for SAP S/4HANA Supply Chain for transportation management</p>", "noteVersion": 19, "refer_note": [{"note": "3289248", "noteTitle": "3289248 - Auditing TM vs. Advanced TM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>SAP S/4HANA Supply Chain for transportation management (further referred to as SAP S/4HANA TM) on premise can be used in an S/4HANA installation with different solution capabilities:<br/><br/>The core S/4HANA license includes a basic variant of a transportation management process. The solution capability 'Transportation Management' (further referred to as basic TM) can therefore be used.<br/>In order to use SAP S/4HANA TM with all features, the solution capability 'Advanced Transportation Management' has to be used. This requires the Advanced TM license engine.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>TM Audit Report, basic TM vs. Advanced TM, S/4HANA TM License Information, Transportation Management, Advanced Transportation Management</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>When you are implementing SAP S/4HANA TM, you want to understand whether you are using functionality which needs the additional Advanced TM license. In order to support this request, a report has been created.</p>\n<p>The report, which can be called via Transaction “/SCMTMS/AUDIT”, will deliver a good indication of your current implementation concerning the usage of main features in Transportation Management - for basic or advanced Transportation Management.<br/><br/>A detailed differentiation of the two solution capabilities however has to be based on the PDF ‘basicTM_vs._AdvancedTM…’  in Note 3065464. For license discussions please contact your SAP account manager.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instruction or install the corresponding Support Package.</p>", "noteVersion": 5}, {"note": "3305459", "noteTitle": "3305459 - TM: Cumulative corrections in report /SCMTMS/AUDIT_BASIC_ADVANCED", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\">Transportation Management in SAP S/4HANA: the self-verification report /SCMTMS/AUDIT_BASIC_ADVANCED, introduced with SAP Note <strong>3289248</strong>, delivers incorrect results for the following metrics:</p>\n<ul>\n<li><em>Charge Management &gt; Calculation Method - Deficit Weight Rating</em></li>\n<li><em>Integration</em> &gt; <em>Side-by-Side Deployment</em></li>\n<li><em>Integration &gt; Transit Warehouse<br/></em></li>\n<li><em>Planning &gt; Incompatibilities<br/></em></li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">Program error.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the attached correction instructions or install the corresponding Support Package.</p>", "noteVersion": 2}]}, {"note": "2269324", "noteTitle": "2269324 - Compatibility Scope Matrix for SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Through the Compatibility Packages (CP) listed in the attachment \"Compatibility Scope Matrix\", SAP provides a limited use right to SAP S/4HANA on-premise customers to run certain classic SAP ERP solutions on their SAP S/4HANA installation. Condition is that these customers have licensed the applicable solutions as set forth in their License Agreements. Compatibility Pack use rights may apply to selected private cloud deployments as well, without the prerequisite of an on-premise classic license. Please refer to the respective Service Description Guide for details.</p>\n<p>This use right expires on Dec 31, 2025, and is available to installed-base as well as net-new customers.</p>\n<p>The <a href=\"https://news.sap.com/2020/02/sap-s4hana-maintenance-2040-clarity-choice-sap-business-suite-7/\" target=\"_blank\">announcement</a> about the extension of maintenance for Business Suite solutions has no influence on the end of compatibility pack use rights - they will be terminated after 2025.<sup>(1)</sup></p>\n<p>Besides reading the attached documents, SAP recommends the latest SAP Community webinar on the topic: <a href=\"https://www.youtube.com/live/muPrV5J7ffM?feature=shared\" target=\"_blank\">https://www.youtube.com/live/muPrV5J7ffM</a></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA Compatibility Scope Matrix, Way Forward</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In the “Attachments” section, the “Overview Presentation” and “Detail FAQ” documents explain the “why”, “what” and “how” of CPs. For functional details, please also refer to the Feature Scope Description (FSD) of SAP S/4HANA, <a href=\"https://help.sap.com/doc/e2048712f0ab45e791e6d15ba5e20c68/latest/\" target=\"_blank\">latest version</a> (or through http://help.sap.com/s4hana), chapter 5.</p>\n<p>The “Way Forward” presentation and overview list, also under “Attachments”, provide information about solution alternatives in the perpetual scope of SAP S/4HANA. They describe the status of each Compatibility Pack and provide links to more detailed information about its strategy. This information is provided via SAP standard documentation, such as the Roadmaps and the Innovation Discovery. The “Item ID” helps to cross-reference between the original, descriptive CP matrix and the future-oriented “Way Forward” spreadsheet.</p>\n<p>This blog <a href=\"https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/\" target=\"_blank\">https://blogs.sap.com/2022/08/09/compatibility-scope-what-happens-after-2025-2030/</a> provides more details about the procedure for Compatibility Packs after their use right expiry in 2025/2030.</p>\n<p>SAP is planning regular updates of the attachments. All forward-looking information is non-binding and with preview character. ​The released SAP Roadmaps are the proper source of information.</p>\n<p>For questions and feedback about this note and its content, please create a customer message on component XX-SER-REL with the prefix ‘CompScope’​.</p>\n<p>(1) In the exceptional cases of CS, LE-TRA and PP-PI, the usage right to their respective compatibility pack items (cf. matrix) terminates at the end of 2030.</p>", "noteVersion": 84}], "activities": [{"Activity": "Implementation project required", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": "The alternative functionality is the basic Transportation Management within SAP S/4HANA, which is already available. The main functions of LE-TRA have already been replaced with selected functions of SAP S/4HANA Supply Chain for Transportation Management."}, {"Activity": "Business Decision", "Phase": "Any time", "Condition": "Optional", "Additional_Information": "Decide if successor functionality of SAP S/4HANA Supply Chain for Transportation Management should be used"}, {"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "The alternative functionality is the Basic Shipping solution within SAP S/4HANA, which is already available. The main functions of LE-TRA have already been replaced with selected functions of SAP S/4HANA Supply Chain for Transportation Management."}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Optional", "Additional_Information": ""}]}