{"guid": "00109B1318B61EDBA391A01641F4E115", "sitemId": "SI28: Logistics_EHS – Office Integration: RAS (1)", "sitemTitle": "S4TWL - Replacement of Office integration in Risk Assessment Step", "note": 3036325, "noteTitle": "3036325 - S4TWL - Refactoring Office Integration in EHS Risk Assessment (1)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are carrying out a system conversion or an upgrade to SAP S/4HANA 2021.</p>\n<p>The following SAP S/4HANA transition worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Environment, Health and Safety; Health and Safety Management; Operational Risk Assessment</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">Renovation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The <em>Manage Risk Assessments</em> app enables you to configure additional assessment steps. One possible such step is the use of a predefined document created in Microsoft Word or Microsoft Excel to perform a risk assessment. This document could be, for example, a predefined checklist to fill in.</p>\n<p>Such a document used to be displayed and edited in place within the browser. As the Microsoft Office integration into browsers is no longer supported, this feature has been reworked to use the download and upload of the relevant document.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>Microsoft Office documents can no longer be displayed and edited in place within the <em>Manage Risk Assessments</em> app. This feature has been reworked in the following way:</p>\n<ol>\n<li>The user selects one of the available template documents.</li>\n<ul>\n<li>With this renovation, the system now offers multiple templates.</li>\n<li>However, it is only possible to upload one result document.</li>\n</ul>\n<li>The user downloads the chosen document to the frontend.</li>\n<li>The user edits the document using the appropriate Microsoft Office applicaiton.</li>\n<li>The user uploads the edited document as the result.</li>\n</ol>\n<p>The business process as such has not changed, only the way in which such a document is handled.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ol>\n<li>Check if you have defined assessment steps using Microsoft Office documents as the analysis method.</li>\n<ul>\n<li>Open the Customizing activity <em>Environment, Health and Safety -&gt; Health and Safety Management -&gt; Risk Analysis -&gt; Specify Assessment Steps and Analysis Methods</em>.</li>\n<li>Check if the analysis method <em>Office Integration (Word)</em> (EHHSS_ANM_OFFICE_DOC) or <em>Office Integration (Excel) (</em>EHHSS_ANM_OFFICE_DOC2) has been assigned somewhere.</li>\n</ul>\n<li>If so, inform your business users about the new approach described above.</li>\n<ul>\n<li>The changed handling on the UI should be self-explanatory enough that no extensive training is necessary.</li>\n</ul>\n</ol>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This simplification item is relevant if you have assessment steps defined that use use Microsoft Office integration as the analysis method. This is the case if table EHHSSC_ANMASSIGN contains entries where the field TAB_ID = \"OFFICE_DOC\".</p>", "noteVersion": 1, "refer_note": [{"note": "3036664", "noteTitle": "3036664 - Remove Office Integration from Risk Assessment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using the Manage Risk Assessment Projects application and want to perform a analysis method for a risk analysis step with integrated Microsoft Office usage.<br/>These analysis methods are Exposure Assessment (Quantitative) and Office Document.<br/>In the past these steps were only working with Internet Explorer as Web browser.</p>\n<p>This SAP Note changes the behaviour of these steps. Now the Office Document is no longer displayed inline, but downloaded to the client machine where it could be used with a local implemention of e.g. Microsoft Office. When the required work on the documents is done the documents could be uploaded again.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>RAS<br/>Exposure Assessment<br/>Statistical Analysis<br/>Word<br/>Excel<br/>Workplace Sampling<br/>Sampling Data<br/>Measurement Data</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Reason: Program error.</p>\n<p>Prerequisites: The validity of the correction is maintained in the correction instructions.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The Support Packages that contain this correction are listed in the \"Support Packages\" section.<br/>Alternatively, you can implement the correction instructions of this SAP Note.</p>", "noteVersion": 4}, {"note": "3039679", "noteTitle": "3039679 - S4TWL - Refactoring Office integration in EHS Risk Assessment (2)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You carrying out a system conversion or an upgrade to SAP S/4HANA 2021.</p>\n<p>The following SAP S/4HANA transition worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">Environment, Health and Safety; Health and Safety Management; Operational Risk Assessment</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">Renovation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The <em>Manage Risk Assessments</em> app enables you to configure the process step <em>Exposure Assessment (Quantitative) / Quantitative Exposure Assessment</em>. Within this step it was possible to interface to a Microsoft Excel document for the calculation of the exposure assessment result synchronously and inside the browser.</p>\n<p>As the integration of Microsoft Office into browsers is no longer supported, this feature has been reworked using explicit downloading and uploading.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>Microsoft Office documents can no longer be displayed and edited in place within the <em>Manage Risk Assessments</em> app. This feature has been reworked the following way:</p>\n<ol>\n<li>The user downloads the Microsoft Excel document to the frontend.</li>\n<ul>\n<li>All relevant measurement and sampling data is downloaded with the file. </li>\n</ul>\n<li>The user edits the document using Microsoft Excel.</li>\n<li>The user uploads the edited document.</li>\n<ul>\n<li>The exposure assessment calculated within that document is read from the file and written back to the SAP system.</li>\n</ul>\n</ol>\n<p>The business process as such does not change, only the in which such a document is handled.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ol>\n<li>Check if you have the assessment step <em>Quantitative Exposure Assessment</em> in use.</li>\n<ul>\n<li>Open the Customizing activity <em>Environment, Health and Safety -&gt; Health and Safety Management -&gt; Risk Analysis -&gt; Specify Assessment Steps and Analysis Methods</em>.</li>\n<li>Check if Analysis Method <em>Quantitative Exposure Assessment</em> (EXP_ASSMNT_QUANT) has been assigned somewhere.</li>\n</ul>\n<li>If yes, inform your business users about the new approach described above.</li>\n<ul>\n<li>The changed handling in the UI should be self-explanatory enough that no extensive training is necessary.</li>\n</ul>\n</ol>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This simplification item is relevant if you have <em>Quantitative Exposure Assessment</em> defined as the analysis method. This is the case if table EHHSSC_ANMASSIGN contains entries where the field TAB_ID = \"EXP_ASSMNT_QUANT\".</p>", "noteVersion": 2, "refer_note": [{"note": "3036325", "noteTitle": "3036325 - S4TWL - Refactoring Office Integration in EHS Risk Assessment (1)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are carrying out a system conversion or an upgrade to SAP S/4HANA 2021.</p>\n<p>The following SAP S/4HANA transition worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Environment, Health and Safety; Health and Safety Management; Operational Risk Assessment</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p id=\"\">Renovation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The <em>Manage Risk Assessments</em> app enables you to configure additional assessment steps. One possible such step is the use of a predefined document created in Microsoft Word or Microsoft Excel to perform a risk assessment. This document could be, for example, a predefined checklist to fill in.</p>\n<p>Such a document used to be displayed and edited in place within the browser. As the Microsoft Office integration into browsers is no longer supported, this feature has been reworked to use the download and upload of the relevant document.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>Microsoft Office documents can no longer be displayed and edited in place within the <em>Manage Risk Assessments</em> app. This feature has been reworked in the following way:</p>\n<ol>\n<li>The user selects one of the available template documents.</li>\n<ul>\n<li>With this renovation, the system now offers multiple templates.</li>\n<li>However, it is only possible to upload one result document.</li>\n</ul>\n<li>The user downloads the chosen document to the frontend.</li>\n<li>The user edits the document using the appropriate Microsoft Office applicaiton.</li>\n<li>The user uploads the edited document as the result.</li>\n</ol>\n<p>The business process as such has not changed, only the way in which such a document is handled.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ol>\n<li>Check if you have defined assessment steps using Microsoft Office documents as the analysis method.</li>\n<ul>\n<li>Open the Customizing activity <em>Environment, Health and Safety -&gt; Health and Safety Management -&gt; Risk Analysis -&gt; Specify Assessment Steps and Analysis Methods</em>.</li>\n<li>Check if the analysis method <em>Office Integration (Word)</em> (EHHSS_ANM_OFFICE_DOC) or <em>Office Integration (Excel) (</em>EHHSS_ANM_OFFICE_DOC2) has been assigned somewhere.</li>\n</ul>\n<li>If so, inform your business users about the new approach described above.</li>\n<ul>\n<li>The changed handling on the UI should be self-explanatory enough that no extensive training is necessary.</li>\n</ul>\n</ol>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This simplification item is relevant if you have assessment steps defined that use use Microsoft Office integration as the analysis method. This is the case if table EHHSSC_ANMASSIGN contains entries where the field TAB_ID = \"OFFICE_DOC\".</p>", "noteVersion": 1}]}], "activities": [{"Activity": "User Training", "Phase": "After conversion project", "Condition": "Optional", "Additional_Information": ""}]}