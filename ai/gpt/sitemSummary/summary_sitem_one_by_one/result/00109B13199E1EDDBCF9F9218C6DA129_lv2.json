{"guid": "00109B13199E1EDDBCF9F9218C6DA129", "sitemId": "SI22: PROC_MM_PUR_CI_INCLUDE_CONFLICTS", "sitemTitle": "S4TWL - CI Include Conflicts in Table MMPUR_ANA_E", "note": 3326565, "noteTitle": "3326565 - Upgrade issue in MMPUR_ANA_EKET table from OP2022: CI Include merged into EEW for EKKO & EKPO", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Activation issue with duplicate fields in analytical table MMPUR_ANA_EKET while upgrade to S/4HANA 2022</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>MMPUR_ANA_EKET, CI_EKKODB, CI_EKPODB, phase ACT_UPG</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Technically until S/4HANA 2022, the CI includes of DB tables EKKO &amp; EKPO were not included in respective extension includes EKKO_INCL_EEW_PS &amp; EKPO_INCL_EEW_PS. Since release S/4HANA 2022, CI include got merged into respective extension includes.</p>\n<p>Analytical table (MMPUR_ANA_EKET) is a denormalised table with EKKO,EKPO and EKET data and it also contains the extension includes of EKKO &amp; EKPO.</p>\n<p>SAP proposes to use unique field names across CI Includes of EKKO &amp; EKPO (see also note 2899836 and 3101906). If you do not follow this proposal from S/4HANA 2022 onwards there will be an error during upgrade on the analytical table (due to field name conflict).</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The solution for this is to implement the attached correction in source release before upgrading to S/4HANA 2022. There are two new specific extension stuctures &amp; one report delivered as part of this note. Once the note is implemented report MMPUR_ANA_EKET_GEN_EEW_FIELDS has to be executed and verified whether the custom fields of includes EKKO_INCL_EEW_PS &amp; EKPO_INCL_EEW_PS are appended to EKKO_INCL_EEW_PS_ANA &amp; EKPO_INCL_EEW_PS_ANA respectively. The new specific extension structures EKKO_INCL_EEW_PS_ANA &amp; EKPO_INCL_EEW_PS_ANA will only contain custom fields from extension includes EKKO_INCL_EEW_PS &amp; EKPO_INCL_EEW_PS, but not from CI includes CI_EKKODB &amp; CI_EKPODB.</p>\n<p>If custom fields from CI_EKKODB &amp; CI_EKPODB are also required to be included in table MMPUR_ANA_EKET, these need be added manually via append mechanism.<br/><br/>Once the new structures with custom fields are ready in source release, the upgrade can happen where old extension includes EKKO_INCL_EEW_PS &amp; EKPO_INCL_EEW_PS in table MMPUR_ANA_EKET are replaced with new includes EKKO_INCL_EEW_PS_ANA &amp; EKPO_INCL_EEW_PS_ANA.</p>", "noteVersion": 2, "refer_note": [], "activities": [{"Activity": "Technical System Configuration", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "If you have conflicting custom fields in CI includes of table MMPUR_ANA_EKET, follow the instructions in SAP note 3326565."}]}