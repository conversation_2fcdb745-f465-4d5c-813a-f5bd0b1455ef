{"guid": "0050569455E21ED5B3E17678390E009E", "sitemId": "SI4: PROC_MM_VM_SET", "sitemTitle": "S4TWL - Subsequent Settlement - Vendor Rebate Arrangements", "note": 2267415, "noteTitle": "2267415 - S4TWL - Subsequent Settlement - Vendor Rebate Arrangements", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>Condition Contract Management is superior to Subsequent Settlement. It offers the following advantages:</p>\n<ul>\n<li>Central, standardized solution for the administration of supplier and customer conditions</li>\n<li>Transparency which documents are relevant and related to each other</li>\n<li>Detailed overview of settlements</li>\n<li>Flexible settlement calendar</li>\n<li>Support of various data sources to allow modelling of flexible scenarios (including, but not limited to sales-related rebates, scanback rebates, customer funds, purchase-related-rebates)</li>\n<li>Fully integrated with the Order-to-Cash and the Procure-to-Pay Processes</li>\n<li>Designed for high performance</li>\n<li>Architected to benefit from SAP HANA</li>\n</ul>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, the Subsequent Settlement (MM-PUR-VM-SET) application is replaced by the new Contract Settlement (LO-GT-CHB) application. For this reason, the functional scope of subsequent settlement has been restricted, that is, in SAP S/4HANA, it is no longer possible to:</p>\n<ul>\n<li>Create new rebate arrangements using transaction MEB1 or</li>\n<li>Extend existing rebate arrangements using transactions MEBV / MEB7 / MEBH</li>\n</ul>\n<p>Additionally, as a consequence of the material field length extension in the SAP S/4HANA landscape, the structure of table S111 was adjusted. This can have some impact if the business volume data has to be rebuilt in the SAP S/4HANA system.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, Contract Settlement replaces Subsequent Settlement, which means that existing rebate agreements can only be processed up until the end of the validity date of the agreement and must then be closed by a final settlement. Afterwards new agreements can only be created based on condition contracts.</p>\n<p>Furthermore, if recompilation of the business volume data in SAP S/4HANA, on-premise edition 1511 is required, the index table S111 has to be rebuilt as described in SAP Note 73214 (Subseq.settl.: Retrospec.compltn/recompltn of busin.vol.data).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"229\">\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"366\">\n<p>MEB1; MEB7; MEBV, MEBH</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In SAP S/4HANA there are no direct activities required for continuing the business process for subsequent settlement, with the only exception that the agreements have to be closed after the end of the validity period.</p>\n<p>If a recompilation of the business volume data is required, the table S111 has to be rebuilt with the report RMEBEIN3 as described in note 73214</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Notes: 2194923, 2215220</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Related SAP Notes</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 73214</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 3, "refer_note": [{"note": "2386003", "noteTitle": "2386003 - S4TWL - Changed Retail Functionality", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management, there are changes in several functional areas.</p>\n<p><strong>Business Process related information</strong></p>\n<p>See details in the respective referenced notes.</p>\n<p><strong>Required and Recommended Action</strong></p>\n<p>See details in the respective referenced notes.</p>", "noteVersion": 1, "refer_note": [{"note": "2365503", "noteTitle": "2365503 - S4TWL - Retail Revaluation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Data model changes in Material Ledger and Accounting (see note #2337368) have affected the way, retail revaluations are posted. When you are using the stock valuation at sales price, as this is usual in retail systems, this note describes the changes, that may be relevant for custom coding.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Revaluations affecting margins are posting financial documents</strong></p>\n<p>Function Module STOCK_SALES_PRICE_REVALUATION, as well as the FORMS purchase_price_calculate and purchase_prepare_price_data have been changed, so that they are posting financial documents at the end. In SAP ERP this was the case for the revaluation not affecting margins only. The financial posting leads to entries in tables ACDOCA and ACDOCA_M_EXTRACT which are used to aggregate the MBEW-VKSAL field. (See changes in Material Ledger note #2337368).</p>\n<p><strong>Revaluations not affecting margins are updating stock values at cost in alternative material ledger currencies</strong></p>\n<p>When a revaluation leads to changes in the stock value at purchasing price, this change is now posted in all material ledger currencies. This is normally the case, when you use a revaluation profile \"not affecting margins\". This change is implemented in FORM add_material_ledger_currencies in include LWBW1F09.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if sales price valuation, or value-only articles are used.<br/>This can be checked via transaction SE16N. <br/>Sales price valuation: Enter table T001K and check whether there are entries with field XVKBW (Sales price valuation active) not equal blank.<br/>Value-only article: Enter table T023W and check whether there are entries with field WWGPA (Material group material) not equal blank. If this is the case, that is a good indicator that value-only articles are used.</p>", "noteVersion": 2}, {"note": "2365665", "noteTitle": "2365665 - S4TWL- Retail Season Conversion ((SAP ERP to SAP S/4HANA 1610)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1610, and you are using some Retail related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Season, Fashion Season, Retail</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have to activate busines function ISR_RETAILSYSTEM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA certain functionality is not supported anymore, among others the Retail Season (e.g. customizing tables T6WSP, TWSAI).</p>\n<p>With the conversion, the following new season fields are introduced:</p>\n<ul>\n<li>SEASON YEAR</li>\n<li>SEASON</li>\n<li>COLLECTION</li>\n<li>THEME</li>\n</ul>\n<p>The old season fields in the article master on the Basic Data (table MARA) are not used anymore.  The new season fields will be maintained on Basic Data 2 (table FSH_SEASONS_MAT).</p>\n<p><strong>Business Process related information</strong></p>\n<p>The season will not be defined in the customizing anymore, instead there is the Season Workbench (transaction FSH_SWB) available as a application transaction.</p>\n<p>Part of the conversion from SAP ERP to SAP S/4HANA 1610, article need to be resasigned from \"old\" to \"new\" season.</p>\n<p>With the conversion, the following tables are filled automatically:</p>\n<ul>\n<li>Assignment season to article</li>\n<ul>\n<li>FSH_SEASONS_MAT </li>\n</ul>\n<li>Season definition</li>\n<ul>\n<li>FSH_SEASONS</li>\n<li>FSH_SEASONS_T</li>\n<li>FSH_SD_PERIODS</li>\n<li>FSH_MM_PERIODS</li>\n<li>FSH_COLLECTIONS</li>\n<li>FSH_COLLECTION_T<strong> </strong></li>\n</ul>\n</ul>\n<p>Note: The season fields are no longer maintained in table MARA. All relevant season information is now maintained in table FSH_SEASONS_MAT.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>During the conversion, the following reports are executed automatically:</p>\n<ul>\n<li>R_FSH_S4_SEASONS_MD_MIG to convert the season Customizing data</li>\n<li><span>R_FSH_S4_SEASONS_MAT_MIG_XPRA </span>(XPRA) to convert the season assignment to article</li>\n</ul>\n<p>In case of problems during the conversion, you can execute these reports manually.</p>\n<p>Note that <span><span>R_FSH_S4_SEASONS_MAT_MIG_XPRA</span></span> is a cross-client enabled report, whereas R_FSH_S4_SEASONS_MD_MIG has to be executed in each client required.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if retail season is used. <br/>This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with field SAISO (Season) not equal blank.</p>\n<p> </p>", "noteVersion": 5}, {"note": "2417861", "noteTitle": "2417861 - S4TWL - Omichannel Promotion Pricing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Omichannel promotion pricing includes pricing information for the effective sales price in sales orders based on information from the central omnichannel promotion pricing solution.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Omichannel promotion pricing is planned to be provided in an upcoming SAP S/4HANA release. In addition this functionality has been downported to Enhancement Package 8 Support Package Stack 6 of SAP ERP 6.0, and Enhancement Package 7 Support Package Stack 14 of SAP ERP 6.0.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>This note and the related checks shall inform you about this fact and in case you are actively using this functionality on SAP ERP 6.0 prevent a system conversion to older SAP S/4HANA releases in order to prevent a loss of features or data.</p>\n<p>In this case a system conversion is only possible to the SAP S/4HANA release which contains this functionality (or higher).</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This note is only relevant if Business Function ISR_RETAIL_OPP is active.</p>", "noteVersion": 2}, {"note": "2339010", "noteTitle": "2339010 - S4TWL - Generic Article Harmonization", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your SAP ERP for Retail/Fashion system to an SAP S/4HANA system and you want to use existing generic articles in SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The way of storing characteristic valuations of variants of a generic article and a configurable material were different.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With S/4HANA, generic article harmonization with configurable materials becomes available. With that a generic article is assigned to a configuration class and the variant-creating characteristics for that generic article are taken from that configuration class. The valuation of the variant-creating characteristics of the variants is stored within the configuration storage (IBase). For this reason, the existing generic articles, the variant-creating characteristics and the variants need to be migrated during the upgrade.</p>\n<p><strong>Business Process related information</strong></p>\n<p>As the variant-creating characteristics are no longer linked to a merchandise category or characteristics profile, but to configuration classes, a re-classification of the merchandise category/characteristics profile of a generic article is no longer restricted by the assignment of variant-creating characteristics to merchandise categories/characteristics profiles.</p>\n<p>As a consequence of the data model change the following functionality is not available anymore:</p>\n<ul>\n<li>Assigning a single article to a generic article via report MMASSIGNMAT, see also attached note 2772474</li>\n<li>Transforming a variant of a generic article to a single article via report MMUNHINGEVAR</li>\n<li>Characteristic value change of a variant of a generic article via report MMCHANGEVAR</li>\n<li>Convert Material Master data to Article Master data via report RMMMMPOI (transaction WRST); starting with SAP S/4HANA 1909 report RFM_CONV_MATERIAL_TO_ARTICLE is available which has similar functionality, see note 2843173.</li>\n<li>Conversion of a single article to a generic article with logistical variants via report MMLOGMETRANSFORM</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>The complete data migration process is divided into three phases: manual started pre-migration activities, automated migration processes during downtime, and manual started post-migration activities.</p>\n<p>The 1st phase includes the required manual execution of Pre-Migration reports in the SAP ERP for Retail system in all clients. Please implement the SAP Note 2331707 to get the pre-migration reports.</p>\n<p>The 2nd phase includes the automatic execution of another migration report which will be executed by the SUM tool during the downtime.</p>\n<p>The 3rd and the last phase includes the recommended manual execution of a Post-Migration report in the converted SAP S/4HANA system in all clients. Please refer to SAP Note 2350650 for more details.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if generic articles and variants are used. <br/>This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with field ATTYP (Material Category) 01 (generic article) or 02 (variant).</p>", "noteVersion": 8}, {"note": "2930396", "noteTitle": "2930396 - S4TWL - Screen Sequences for Article Maintenance and Area Menus", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are converting an existing SAP ERP system to SAP S/4HANA Retail for merchandise management or SAP S/4HANA for fashion and vertical business. The following SAP S/4HANA Transition Worklist Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Screen sequences allow you to define which screens are available in article maintenance (transactions MM41, MM42, and MM43). Furthermore, you can define in which sequence the screens appear and which subscreens a screen comprises. Screen sequences are defined in Customizing.</p>\n<p>SAP delivers predefined screen sequences that can be used out of the box or can be adjusted or can be used as a copy-from template.</p>\n<p>In SAP ERP, the screen sequences delivered were 23/33 for Retail and FS for Fashion. These screen sequences are still available in SAP S/4HANA; however, to support data model changes for season, supply assignment, and segmentation, new screen sequences have been created. Screen sequence 24 for Retail and screen sequence F4 for Fashion.</p>\n<p>Furthermore, SAP delivers area menus comprising relevant retail and fashion transactions in a structured way. In SAP ERP, area menu W10M contains merchandise management transactions and area menu. W10T contains merchandise management transactions plus transactions from other areas, such as Finance and Human Resources. In SAP S/4HANA, area menu W10M is still available and has been adjusted to reflect the simplifications and innovations available in SAP S/4HANA. Area menu W10T is no longer available; in its place, W10U has been introduced.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes are not affected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In case you are using seasons, segmentation, or supply assignment function, switch from screen sequence 23 to 24, or 33/FS to F4 respectively. If you are using custom screen sequences, adjust them accordingly. For more details check the attached files.</p>\n<p>In case you are using area menu W10T in SAP ERP, in SAP S/4HANA, use area menu W10U.</p>\n<p>Custom code is not affected.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if articles are maintained using transactions MM41, MM42, MM43.</p>", "noteVersion": 7}, {"note": "2340264", "noteTitle": "2340264 - S4TWL - Global Data Synchronization (GDS)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using GDS funcionality. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA GDS only works if the latest Open Catalog Interface (either enterprise service or IDoc PRICECATALOGUE) is used. IDoc PRICAT is not supported anymore. <br/>Also transactions PRICAT* (PRICAT, PRICATCUS1, PRICATCUS2, PRICATCUS3, PRICATCUS6, PR<PERSON>ATCUS7, <PERSON><PERSON><PERSON><PERSON>O<PERSON>, PR<PERSON><PERSON><PERSON><PERSON>GOUT) are not supported anymore. Transactions W_SYNC or W_PRICAT_MAINTAIN need to be used.</p>\n<p><strong>Business Process related information</strong></p>\n<p>It is required that the latest Open Catalog Interface is used.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Execute process as described in referenced note 2326511.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if GDS is used. <br/>This is the case if transactions PRICAT*, W_SYNC, W_PRICAT_MAINTAIN are used.</p>", "noteVersion": 2}, {"note": "2339317", "noteTitle": "2339317 - S4TWL - Retail Business Function Harmonization", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using some Retail related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA the structure of the  Retail Business Functions was reorganized and simplified so that most of the Retail functions and  features can be switched on by the Enterprise Business Function  ISR_RETAILSYSTEM..</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes will not be affected directly. For details see referenced note.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>For details see referenced note <span class=\"urTxtStd\">2304479 and <span class=\"urTxtStd\">2303155.</span></span></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\">In case you have activated one of the following Business Functions, but don't use the functionality, please, create an incident, and we will release the respective pilot note for you:<br/><span ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">- ISR_RETAIL_INDIA_LOC<br/>- <span ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">ISR_RET_PERISH_PROCUREMENT<br/>- ISR_RETAIL_MAP_IF<br/>- ISR_RETAIL_MAP_IF2<br/>- ISR_RETAIL_INSTORE_FPD<br/><span ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" calibri;=\"\" color:=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">- ISR_RETAIL_RMA<br/>- ISR_RETAIL_RMA_2<br/>- ISR_RETAIL_RMA_3 </span></span></span></span></span></p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if you have activated the Business Function Set ISR_RETAIL. This can be checked via transaction SFW5.</p>", "noteVersion": 2}, {"note": "2339008", "noteTitle": "2339008 - S4T<PERSON>L - Business Partner for Sites", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using sites in your source system. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With S/4HANA 1610 the Business Partner harmonization becomes available for the site. Sites can be migrated during the upgrade so they fulfill the new Business Partner paradigm in S/4HANA (every customer/supplier master record must have a Business Partner as leading entity; this was not possible on ERP for customers/vendors assigned to retail sites).</p>\n<p><strong>Business Process related information</strong></p>\n<p>The Business Partner will be the leading object.</p>\n<p>Certain functionality of department store/shop concept is not available SAP S/4HANA. Transactions WRFCATEGORYSHOP, WRFCLOSESHOPS, WRFDELIVERYSITESHOPS, WRF<PERSON>GIONSHOPS, <PERSON><PERSON><PERSON><PERSON>CA<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WRF_ARC_DEPSHOP, WRF_TCHAIN_MIGR, WSHOPMIGR are not available.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Follow instructions in the referenced note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if retail sites are used. <br/>Relevance of this note can be checked via transaction SE16N. Enter table T001W and check whether there are entries with field VLFKZ (Plant Category) not equal blank.</p>\n<p>For functionality not availble anymore, please check whether transactions WRFCATEGORYSHOP, WRFCLOSESHOPS, WRFDELIVERYSITESHOPS, WRFREGIONSHOPS, WRFSHOPCAT, WRFSHOPDEP, WRF_ARC_DEPSHOP, WRF_TCHAIN_MIGR, WSHOPMIGR are used.</p>", "noteVersion": 4}, {"note": "2267742", "noteTitle": "2267742 - S4TWL - Automatic Document Adjustment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, the following differences to the process of automatic document adjustment apply:</p>\n<ol>\n<li>The direct entry function is activated per default for all document categories to simplify the creation of worklist entries.</li>\n<li>The field length of the Material Number data element (MATNR) is 40 characters. As a consequence, the field length of data element 'VAKEY' is enhanced and the database tables WIND and S111 have also been adjusted.</li>\n</ol>\n<p><strong>Business Process related information</strong></p>\n<ol>\n<li>In SAP S/4HANA, the indicator for direct entry is set now automatically during the update process of conditions. This means that:</li>\n</ol>\n<ul>\n<li>When a user saves a condition change - including the creation of a new condition - an entry is made in the worklist (table WIND) for all document categories with valid Customizing settings.</li>\n</ul>\n<p>For more information, please see SAP Note 519685 (Conversion of the procedure for Worklist creation).</p>\n<p>Note also that change pointers do not have to be updated for message type CONDBI if direct entries are activated. With the conversion to the 'direct entry' procedure, you can deactivate the <strong><span>update of change pointers</span></strong> if you do not use it for other purposes</p>\n<p>2. Rebuilding business data volume (tables WIND and S111)</p>\n<ul>\n<li>SAP recommends that you process all open worklist entries completely before conversion to SAP S/4HANA. In this case, no activities for automatic document adjustment are required after the system conversion process</li>\n<li>If the worklist cannot be completed in the SAP ERP system, you can proceed with the procedure described in section 'Required and Recommended Actions on Start Release.</li>\n</ul>\n<p><strong><br/>Required and Recommended Action(s)</strong></p>\n<p>If in the ERP system the worklist could not be completely processed, a rebuilding of the worklist for automatic document adjustment is required in SAP S/4HANA, on-premise edition 1511 -&gt; please follow in this case the following procedure:</p>\n<ul>\n<li>Delete the content for automatic document adjustment of table S111 ( Report RMEBEIN6 (transaction MEI6)) and</li>\n<li>Rebuild the table entries for S111 for the used document categories by using the appropriate setup reports (see below). Make sure that the filter selection for rebuilding the S111 entries is meaningful  by selecting the appropriate organizational criteria ( time, org data, docnr, ...) [Rebuild is in SAP S/4HANA]</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Doc. category </strong></p>\n</td>\n<td>\n<p><strong>Description</strong></p>\n</td>\n<td>\n<p><strong>Report for rebuilding  S111 entries</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>01</p>\n</td>\n<td>\n<p>Purchase Order</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>02</p>\n</td>\n<td>\n<p>Scheduling Agreement</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>10</p>\n</td>\n<td>\n<p>Sales Price Calculation</p>\n</td>\n<td>\n<p>RWVKP012</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>11</p>\n</td>\n<td>\n<p>Sales Price Calculation Index</p>\n</td>\n<td>\n<p>RWVKP012</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>20</p>\n</td>\n<td>\n<p>Customer billing document</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>30</p>\n</td>\n<td>\n<p>Vendor billing document</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>31</p>\n</td>\n<td>\n<p>Expense document</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>32</p>\n</td>\n<td>\n<p>Vendor settlement document</p>\n</td>\n<td>\n<p>RMEBEIV3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>35</p>\n</td>\n<td>\n<p>Remuneration list</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>40</p>\n</td>\n<td>\n<p>Vendor settlement request</p>\n</td>\n<td>\n<p>RMEBEIZ3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>50</p>\n</td>\n<td>\n<p>Assortment List</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>55</p>\n</td>\n<td>\n<p>POS-Outbound</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>60</p>\n</td>\n<td>\n<p>Customer settlement</p>\n</td>\n<td>\n<p>RMEBEIK3</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>If you want to process multiple applications in one step (subsequent settlement and automatic document adjustment), you can also use report RMCENEUA.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Notes: 2192984, 2215169</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 3}, {"note": "2451873", "noteTitle": "2451873 - S4TWL - Retail Store Fiori App - Order Product (F&R Enhancements)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1610, and you are using the Retail Store Fiori App for Order Products (F0752) with F&amp;R enhancements. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>There are enhancements to the basic retail store Fiori app Order Products for the integration of F&amp;R Replenishment Orders (RPO) and the display of additional F&amp;R forecast information (SAP Notes <a href=\"/notes/0002397064\" target=\"_blank\">2397064</a>/<a href=\"/notes/0002393342\" target=\"_blank\">2393342</a>).</p>\n<p><strong>Business Process related information</strong></p>\n<p>The enhancement is provided starting with SAP S/4HANA 1709.</p>\n<p>In addition this functionality has been downported to Enhancement Package 8 (Support Package Stack 5) and Enhancement Package 7 (Support Package Stack 14) of SAP ERP 6.0. The basic app Order Products without the F&amp;R enhancements continues to be available in SAP S/4HANA 1610.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>This note and the related checks shall inform you about this fact and in case you are actively using this functionality on SAP ERP 6.0 prevent a system conversion to older SAP S/4HANA releases in order to prevent a loss of features or data.</p>\n<p>In this case a system conversion is only possible to the SAP S/4HANA release which contains this functionality (or higher).</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This note is only relevant if order document type \"F&amp;R Purchase Order\" ('003') is selected in the relevant basic settings in customizing. You can check this, e.g. via transaction SE16, table RTST_C_OP_BASICS, whether there are entries with value \"003\" for field ORDER_DOCUMENT_TYPE.</p>", "noteVersion": 4}, {"note": "2436143", "noteTitle": "2436143 - S4TWL - Retail Store Fiori App - Transfer Products", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1610, and you are using the Retail Store Fiori App for Transfer Products (F2449). The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The retail store Fiori App Transfer Products allows to create a product transfer.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The retail store Fiori app Transfer Product is provided starting with SAP S/4HANA 1709.</p>\n<p>In addition this functionality has been downported to Enhancement Package 8 Support Package Stack 6 of SAP ERP 6.0.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>This note and the related checks shall inform you about this fact and in case you are actively using this functionality on SAP ERP 6.0 prevent a system conversion to older SAP S/4HANA releases in order to prevent a loss of features or data.</p>\n<p>In this case a system conversion is only possible to the SAP S/4HANA release which contains this functionality (or higher).</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>Transfer Products: This note is only relevant if there are entries in table RTST_TP_TRANSFER. You can check this via transaction SE16.</p>", "noteVersion": 2}, {"note": "2267415", "noteTitle": "2267415 - S4TWL - Subsequent Settlement - Vendor Rebate Arrangements", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>Condition Contract Management is superior to Subsequent Settlement. It offers the following advantages:</p>\n<ul>\n<li>Central, standardized solution for the administration of supplier and customer conditions</li>\n<li>Transparency which documents are relevant and related to each other</li>\n<li>Detailed overview of settlements</li>\n<li>Flexible settlement calendar</li>\n<li>Support of various data sources to allow modelling of flexible scenarios (including, but not limited to sales-related rebates, scanback rebates, customer funds, purchase-related-rebates)</li>\n<li>Fully integrated with the Order-to-Cash and the Procure-to-Pay Processes</li>\n<li>Designed for high performance</li>\n<li>Architected to benefit from SAP HANA</li>\n</ul>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, the Subsequent Settlement (MM-PUR-VM-SET) application is replaced by the new Contract Settlement (LO-GT-CHB) application. For this reason, the functional scope of subsequent settlement has been restricted, that is, in SAP S/4HANA, it is no longer possible to:</p>\n<ul>\n<li>Create new rebate arrangements using transaction MEB1 or</li>\n<li>Extend existing rebate arrangements using transactions MEBV / MEB7 / MEBH</li>\n</ul>\n<p>Additionally, as a consequence of the material field length extension in the SAP S/4HANA landscape, the structure of table S111 was adjusted. This can have some impact if the business volume data has to be rebuilt in the SAP S/4HANA system.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, Contract Settlement replaces Subsequent Settlement, which means that existing rebate agreements can only be processed up until the end of the validity date of the agreement and must then be closed by a final settlement. Afterwards new agreements can only be created based on condition contracts.</p>\n<p>Furthermore, if recompilation of the business volume data in SAP S/4HANA, on-premise edition 1511 is required, the index table S111 has to be rebuilt as described in SAP Note 73214 (Subseq.settl.: Retrospec.compltn/recompltn of busin.vol.data).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"229\">\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"366\">\n<p>MEB1; MEB7; MEBV, MEBH</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In SAP S/4HANA there are no direct activities required for continuing the business process for subsequent settlement, with the only exception that the agreements have to be closed after the end of the validity period.</p>\n<p>If a recompilation of the business volume data is required, the table S111 has to be rebuilt with the report RMEBEIN3 as described in note 73214</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Notes: 2194923, 2215220</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Related SAP Notes</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 73214</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 3}, {"note": "2385984", "noteTitle": "2385984 - S4TWL - SAP S/4HANA Retail for Merchandise Management, SAP S/4HANA for Fashion and Vertical Business - Simplification Items", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management, and in SAP S/4HANA for fashion and vertical business several functional areas have changed, or are not available anymore, or are not stratgic anymore.</p>\n<p><strong>Business Process related information</strong></p>\n<p>See details in the respective referenced notes.</p>\n<p><strong>Required and Recommended Action</strong></p>\n<p>See details in the respective referenced notes.</p>", "noteVersion": 3}, {"note": "2342914", "noteTitle": "2342914 - S4TWL - Retail Store Fiori App", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using the Fiori app for transfer stock. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, some table structures have changed. Thus, open transfer stock documents need to be closed prior to the conversion.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes will not be affected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Please, follow instructions of referenced note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if the Fiori app for transfer stock is used. <br/><span 'times=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" color:=\"\" de;=\"\" en;=\"\" lang=\"EN\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">This can be checked via transaction SE16N. Enter table TRF_DOC_HEAD and check whether the table exists or there are any entries in the table.</span></p>", "noteVersion": 3}, {"note": "2885547", "noteTitle": "2885547 - S4TWL - Vendor Characteristic Values", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA Retail for merchandise management or SAP S/4HANA for fashion and vertical business. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Via classification system it is possible to assigen characteristic values to articles.<br/>Vendor Characteristic Values are used to maintain the descriptions of a characteristic value a supplier uses, in case this description deviates from the description you are using. E.g. you use \"red\" for a characteristic value whereas the supplier uses \"fire engine red\".</p>\n<p>In ECC it was possible to maintain the vendor characteristic values either in vendor maintenance (transactions MK0x, XK0x) or in purchasing view of the article maintenance (tramsacations MM4x). Because of the Business Partner harmonization in S/4HANA, it is not possible anymore to maintain the values via supplier maintenance. It is still possible to maitain the values via article maintenance.</p>\n<p>In addition it is possible to achieve a simliar via Characteristic Value Conversion.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes are not affected besides the maintenance as such.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Use maintenance capabilities of article maintenance or switch to Characteristic Value Conversion.</p>\n<p>This does not affect your custom code.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is potentially relevant if Vendor Characteristic Values are used.<br/>This can be checked via transaction SE16N. Enter table WYT2 and check whether there are any entries.</p>", "noteVersion": 2}]}, {"note": "73214", "noteTitle": "73214 - Subseq.settl.:Retrospec.compltn/recompltn of busin.vol.data", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>This note describes the procedure during retrospective update of business volume data within subsequent settlement.<br/>It indicates the most frequent problems and errors together with this function and should help you analyze the problems and solve them.<br/>At the same time, there are functional restrictions for this function which should be indicated again at this point.<br/>Refer to information in Note 72199.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (purchasing), volume-based rebate, retrospective compilation of business volume data, recompilation of business volume data, Transactions MEBA, MERA, reports RWMBON08, RWMBON38</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Note on the general procedure, functional restrictions</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><h3 data-toc-skip=\"\">Problem description</h3> <p><br/>You first enter an arrangement with the respective condition records during the normal process chain of the subsequent settlement.<br/>When you subsequently enter purchase orders and scheduling agreements (without master conditions, that is, with price determination), the price determination adopts the relevant condition records of the subsequent settlement into the document conditions. As of Release 4.5A, vendor billing documents (invoices without reference to purchase order) and settlement requests are also relevant for subsequent settlement.<br/>The system logs the document items for the respective arrangements, that is, if you perform the 'Detailed statement' function, Transactions MEB8 (vendor rebate arrangements) or MER8 (customer arrangements, as of Release 4.5A), the corresponding document item for the condition record of the arrangement is displayed.<br/>The business volume data is updated in the information structures during invoice receipt (Release 3.0 ), as of Release 4.0 also from the purchase order or goods receipt. Vendor billing documents and settlement requests (as of Release 4.5) are updated during release of the document for accounting. If you perform the 'Detailed statement' function after the document entry, the document items and the determined business volume data is displayed in addition.<br/>The following information strutures with business volume data is available in the standard R/3 System:</p> <ul><li>S074 - Operative data</li></ul> <ul><li>S015 - Evaluation (standard analysis, planning)</li></ul> <p><br/>In some cases, however, you cannot maintain an arrangement in time (for example, annual negotiations in April lead to a condition that has already been valid as of January 1st). You have two options here.</p> <ul><li>Enter an arrangement for the follwoing year already at year-end based on empirical values or with an income of 0, so that condition already exists by creating the documents and the business volumes are updated. You can then change the condition income, if required.</li></ul> <ul><li>Subsequently enter an arrangement in April backdated to the beginning of the year, that is, the validity period of the arrangement starts at or before the entry period.</li></ul> <p><br/>Attention:<br/>Note that a document item is only relevant for subsequent settlement if the price determination date of the document item is within the validity period of the condition record. In particular, this applies to subsequent updates. You can update goods receipt or an invoice document referring to a purchase order item. Its document condition contains the condition record or would contain it when creating the condition record on time (subsequent update). The reason for this is that goods receipt and invoice verification do not support any price determination. The condition records of the subsequent settlement, for example by means of the document date, are not redetermined. The condition records/arrangements of the purchase order item are necessary for this.<br/><br/>The problem in the second case is that purchase orders, scheduling agreements, vendor billing documents and settlement requests that are relevant for the arrangement can exist already. However, the condition records entered later could not be considered for the price determination. You have to determine the business volume data later and update them into the information structures.<br/>All outline agreements must also be corrected. Otherwise, purchase orders with reference to one of these outline agreements are not relevant for the subsequent settlement. Use report ZRAHM to adjust all purchase orders with reference to an outline agreement automatically. You only have to maintain the 'Settlement' indicator (EKPO-EBONF) for all items of the outline agreement first.<br/>This can also occur if you want to activate the subsequent settlement as new function in the current operation, that is, if relevant documents exist already.<br/>In this case you can use Transaction MEBA, report RWMBON08 for arrangements by vendor. You must use Transaction MERA, report RWMBON38 for arrangements by customer (new in Release 4.5).<br/></p> <h3 data-toc-skip=\"\">Preconditions</h3> <p></p> <ul><li>All vendors (supplier of goods/condition granter) who are relevant for subsequent settlement must carry a corresponding indicator in the vendor master ('Subseq. settlement', Purchasing data view, field LFM1-BOLRE). In the purchasing info record you can exclude the subsequent settlement for the selected level (field EBONF) at vendor, material, purchasing organization level.</li></ul> <ul><li>For all vendors (suppliers of goods) for whom you expect documents that could be relevant for a subsequently entered arrangement, set the 'Sub. Set.index' indicator (Purchasing data view, field LFM1-BOIND). At present an appropriate field for customers (settlement request) does not exist in the customer master. The index entries are created if the vendor of the settlement request provides an update of the index. During settlement requests without reference to a purchasing organization, the index entries are always created.</li></ul> <p>Attention:<br/>Bear in mind that you should carefully set this indicator (in the case of a large data volume in the index file).<br/><br/>If the 'Sub. Set.index' indicator of the document item is set, the system logs certain data in an index by creating/change documents (purchase order, vendor billing document, settlement request) to efficiently access all possibly relevant purchasing documents. This index is information structure (S111) in the Logistics Information System.<br/>You can check the index update using Transaction SE16, table S111. Enter the document number in field BELNR and the identifier for each document category in field BLTYP (-&gt; F4 input help). One or more entries should be displayed in the list output (the number of entries depends on the price determination settings).<br/>If you set the 'Sub. Set.index' indicator also after the creation of the documents or if you create new access sequences in Customzing, the respective index entries are missing.<br/>The situation can also occur if you want to activate subsequent settlement as a new function in the update setting, that is, documents already exist which you want to update later.<br/></p> <h3 data-toc-skip=\"\">Subsequently creating index entries</h3> <p><br/>Release 3.0: You can create the missing index entries by using report RMCENEUA (Statistics setup: Purchasing), Transaktion OLI3. You must restrict the information structures for table S111 and run the report across all relevant documents, for larger datasets this should be done in the background. You can directly write it to version 000 (IST data). The usual security measures (backup, and so on) are not (absolutely) required for table S111.<br/>As of Release 4.0: You can create the missing index entries by using reports RMEBEIN3 (purchase orders/scheduling agreements), RMEBEIL3 (vendor billing documents, as of Release 4.5) and RMEBEIZ3 (settlement requests, as of Release 4.5).<br/>You can also use report RMCENEUA (Statistics setup: Purchasing) for purchase orders/scheduling agreements (see above). This procedure is recommended if you want to compile information structures in purchasing (S011, S012, S013, but not S074, S015) simultaneously. However, report RMEBEIN3 is more efficient for the single compilation of information structure S111.<br/><br/>The index entries should be available after the program run (test, see above). Thus, you can set the 'Sub. Set.index' indicator at any time later for individual suppliers of goods, if required. However, if documents exist already, you have to create the required index entries as described above.<br/>This may cause another problem. If the vendor has not been defined as relevant for subsequent settlement during the document creation (see above), every document item will also not be defined as relevant for subsequent settlement (field EKPO-EBONF).<br/>Maintain the indicator as well as the settlement groups, if necessary (Item -&gt; Other -&gt; Additional data (manually or by means of your own report).<br/></p> <h3 data-toc-skip=\"\">Update of the subsequent business volume</h3> <p><br/>You can start the actual subsequent business volume update by using Transaction MEBA, report RWMBON08, or MERA, report RWMBON38. Do not enter information structure because the program run can be carried out only once. A restriction is only useful during a complete recompilation, that is, if all business volume data must be recompiled completely.<br/>Carry out a test run, if necessary. Consider possible error messages (as of Release 3.0F). You can also first write to one of the versions beginning with '&amp;('. The third character is available for your use.<br/>You can use Transactions MEB6 and MEB9 (detailed business volume data) or MER6 and MER9 as well as MCE+ to check whether and which business volume data for the arrangement have been updated. If you enter '000' for user parameter MCR (System -&gt; User profiles -&gt; User parameters), a 'Version' field will be ready for input in all reports. You can then analyze data directly from the test versions.<br/>Transactions MEB8 (Detailed Statement) determines again the business volumes directly from the respective purchasing documents and possibly indicates error messages in the list header. The same values should appear in the total result as during the other transactions (version 000, IST data).<br/>You recognize a subsequently updated document in the list output of the detailed statement by a special characteristic. When you branch to the document conditions of the document item, no corresponding document condition exists here.<br/>The 'Retrospective compilation of business volume data' function does not change the document conditions and particularly also does not execute subsequent postings of provisions for accrued income. As a result, the function is restricted to a certain extent (see the 'Basic functional restrictions' section).<br/>You must possibly repeat (schedule) 'Retrospective compilation of business volume data' function periodically.<br/>Example: Invoice receipts exist for a purchase order dated 05/15 and 06/16 (document date 05/15 and 06/16). If you start the report on 06/01, the first invoice receipt will be updated. The second invoice receipt will be updated with a program run after 06/16. The functions 'Detailed Statement' and 'Check for open documents' indicate the missing second program run after you have entered the documents for the second invoice document.<br/>Bear in mind that a list log with possible error messages will only be available as of Release 3.0F. As of Release 3.1G, you can check by using an analysis function why a business volume has subsequently been updated for a document item or for which reason it has not been updated.<br/></p> <h3 data-toc-skip=\"\">Basic functional restrictions</h3> <p><br/>Since the document conditions have not been changed and particularly the price determination is not repeated (for reasons of performance), there are some restrictions when using the 'Retrospective compilation of business volume date':</p> <ul><li>No provisions for accrued income are posted because the valuation of the document is adjusted here and you would have to create respective follow-on documents (accounting). Particularly the subtotals could change (changing other conditions, changing the business volume update for other condition records of the subsequent settlement, and the like).</li></ul> <ul><li>You cannot use the condition exclusion. Examples:</li></ul> <ul><ul><li>Condition type A001 (Rebates) should have priority over the rebate of an individual purchasing organization (condition type A002).</li></ul></ul> <ul><ul><li>The immediately effective vendor discount RA01 is only granted if no subsequent rebate (A001) is granted.</li></ul></ul><p></p> <ul>The following problems can occur in this case: Within the subsequent business volume data update, condition records for condition types A001 and A002 will subsequently be determined. The condition exclusion is not considered. The exclusion would only be considered if you adjusted the document conditions, that is, setting the subsequently determined condition records in the document conditions. This will be available in a later release.</ul> <ul>If condition type A002 is determined during the price determination within the purchase order and if a condition record for condition type A001 is subsequently determined during the subsequent update, the vendor business volume update of the condition record for condition type A002 would have to be cancelled. However, this is not done because the cancellation by means of the document condition would be incomprehensible. The condition for condition type A002 would have to be characterized as inactive (adjusting the document conditions). This is, as already said, only defined for a later release.</ul> <ul>In the second example (condition record for condition type RA01 is inapplicable because a condition record for condition type A002 has been determined) the entire valuation of the order document would have to be adjusted and the respective follow-on documents (accounting) would have to be created. Changing the subtotals could have far-reaching effects. Vendor discount RA01 therefore remains in existence, that is, the condition exclusion is not considered.</ul> <p></p></div>", "noteVersion": 11, "refer_note": [{"note": "128279", "noteTitle": "128279 - Automatic deletion business volume data during recompilation", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>During the recompilation of the business volume data for arrangements of the subsequent settlement, before you execute the report you must delete the business volume data separately before the recompilation run.This is unpratical and errors are likely to occur (danger of multiple update).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (purchasing), volume-based rebate, recompilation of business volume data, report RWMBON08, Transaction MEBA</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is caused by a non-available function.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>See the Hot Package or the attached program correction.<br/>Note: Customer-specific information structures cannot be edited.If necessary modify the report.<br/>As of Release 4.5A, the function is not suported in the R/3 standard system.Then, you can edit all information structures with key figures of the subsequent settlement as far as the information structures contain characteristic Rebate arrangement or Condition record number.<br/><br/></p></div>", "noteVersion": 4}, {"note": "72199", "noteTitle": "72199 - Problem analysis update business volume data", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>The update of the business volumes (subsequent settlement) is not carried out or the updated scale basis or condition basis is zero.<br/>This note should help you to run your own problem analysis.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement, volume-based rebate, purchasing, update business volume data, subsequent compilation of business volumes, business volume comparison and agreement, Transactions MEB1, MEB2, MEB3, MEU2, MEU3, MEB4, MEB5, MEB6, MEB8, MEB9, MEBA, MER4, MER5, MER6, MER8, MER9, MERA, programs SAPMV13A, SAPMNB01, report RW<PERSON>ON01, RW<PERSON>ON02, RW<PERSON>ON03, RW<PERSON>ON04, R<PERSON><PERSON>ON06, RW<PERSON>ON08, R<PERSON><PERSON><PERSON>31, <PERSON><PERSON><PERSON><PERSON>32, <PERSON><PERSON><PERSON><PERSON>33, <PERSON><PERSON><PERSON><PERSON>34, <PERSON><PERSON><PERSON><PERSON>36, RW<PERSON><PERSON>38.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The subsequent settlement is currently expanded functionally according to customer requests. Note that this leads to differences in the range of functions depending on Release status. Consider the Release notes and Note 104683.<br/>When you update business volume data, an error message can be generated in rare cases, see below. However, due to technical reasons the error message cannot be generated on the screen. The update of the business volumes is not carried out in order not to damage the datasets.<br/>Cause of the error messages:</p> <ul><li>An incorrect maintenance of the used calculation schema of the order document, of the vendor billing document, the settlement request possibly has the result that the update of the business volumes is not carried out or (in some cases) that the update is carried out incorrectly.</li></ul> <p></p> <ul><li>The tax code of the company code for non-taxable transactions is not maintained for company codes for which taxes are calculated via Tax Jurisdiction Codes (for example in the U.S.A.).</li></ul> <ul><li>A unit of measure conversion or currency translation is not possible.</li></ul> <p><br/>The update problem affects both the update in productive operation (through an invoice receipt) and the supplementary business volume with function Organize (arrangement created after the purchase order).<br/>You should consider this note for all kinds of update problems of the subsequent settlement and carry out the following steps first.</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>Proceed step by step.</p> <ol>1. The system logs all documents that are relevant for the subsequent settlement of an arrangement in an index file. You can display these with functions Recompilation business volume data (Transactions MEBA or MERA, set Compile business volume data! ) or detailed statement (Transactions MEB8 or MER8). The system reads the documents for the arrangement and determines the business volumes again directly from the documents.</ol> <p>              Note the error messages in the list output. In particular function Recompilation business volume data may show you the messages which led to nonexecution of the normal update when saving the documents. <p>              The determined values should - if no error messages occurred - correspond for each condition record with the values in the function statements statistics (Transactions MEB9 or MER9). Function Statements statistics displays the business volumes actually updated up to now. <ol>2. Tax calculation with Tax Jurisdiction Codes</ol> <p>              Check the tax code for non-taxable transactions for all company codes for which purchase orders are created (Transaction OBCL), see Note 48078. <ol>3. Check the pricing procedures.</ol> <p>              In addition to the order document pricing procedure, a separate pricing procedure (settlement structure) is used for creating the accounting documents in the settlement of an arrangement. Notes on maintaining settlement procedures can be found in Customizing of subsequent settlement. <p>              Note the following explanations (pricing procedure, order document, vendor billing document, settlement request): <p>              If a condition type of the subsequent settlement (these are all condition types of condition class C, see condition type Customizing) is entered in a pricing procedure, field 'From reference level' must be set on the pricing procedure line to which the volume-based rebate refers. This would generally be one of the subtotals (for example, net value including discounts). At the same time, a subtotal (field 'Subtotal') must be entered on this level. <p>              Subtotal 7 is provided for the subsequent settlement in particular (value transfer to KOMP-BONBA). However, you can also use subtotals 1 - 6, providing these are not yet used elsewhere in your system. <p>              The value of this subtotal is updated as a business volume on a value basis in particular if the condition refers to business volume on a value basis. In latter case with the determined line of the document conditions, no business volume on a value basis occurs. The system access to the value of the subtotal. <p>              For example, you can determine that the business volume on a value basis is calculated for condition type A001 of the subtotal in level 20. <p>              The entries are as follows (only relevant fields are mentioned): <p><br/>              Level Countr CondTyp Description               Frm Subtotal Condition <p>              ... <p>              20    0              Net value incl disc.          7 <p>              ... <p>              37    1      A001    Discount                  20          26 <p>              ... <p><br/>              You can also determine that the condition type A002 should be calculated from another reference base (that is, subtotal), for example. <p><br/>              Level Countr CondTyp Description                Frm Subtotal Condition <p>              ... <p>              22    0              Net value incl input tax.      2 <p>              ... <p>              38    2      A002    Material discount          22          26 <p>              ... <p><br/>              It is a requirement that subtotal 2 is not already used elsewhere. Note that in Customizing you can determine the arrangement type that the verified invoice net final amount from the invoice verification document should be updated instead of the business volume on a value basis from the purchasing document (field settlement basis). <p>              Every entry of a subtotal into a line of the procedure leads to the values being accumulated if the line is put into the document conditions during price determination. Consequently, the entry of subtotal 7 in levels 20 and 22 would cause the addition of both the net values. The update of this value is not generally useful of course. Therefore only enter each subtotal in the procedure once, or use conditions (only one line may be generated), see Note 134822. <p>              A double entry would only be useful if you can ensure that exactly (!) one line of the pricing procedure is set in the document (via conditions), that is, neither none nor two lines should be set. <p>              You can verify your pricing procedures using report RWMBON20. <p>              Known errors: <ul><ul><li>In error message MN372, the parameters Level, Usage, Usage are swapped in the long text (corrected in Release 3.1H or 4.0). The (only) number is the level in question of the procedure.</li></ul></ul> <ul><ul><li>The report is under development, that is, it does not currently cover all conceivable checks.</li></ul></ul> <p><br/>Error elimination:<br/>Updates have generally not been carried out. You can do this now without problems by executing the reports RWMBON08 or RWMBON38. It suffices to run the program in the mode subsequent update, however to process all arrangements (not only the ones entered later) (delete indicator).<br/>Missing updates are then automatically carried out, even if you do not at all work with the mechanism of subsequent update. The index table S111 does not have to contain any entries. Since only business volume data is missing, this procedure is sufficient.<br/>Check business volume data as described above.<br/>If the business volume data differs between the different functions (recompilation, detailed statement, statement statistics), however, a complete new compilation of business volume data (step 1) and of the incomes (step 2) are required.<br/>For more information, see Note 73214.</p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></p></div>", "noteVersion": 6}, {"note": "104668", "noteTitle": "104668 - Collective note: Subsequent settlement (Purch.) Release 4.0", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>-- Collective note: Problems and errors in                          --<br/>--                  subsequent settlement (purchasing)               --<br/>--                  Releases 4.0A and 4.0B                          --<br/>--                  Version: December 20, 2001                      --</p> <b>***********************************************************************</b><br/> <b>* European Monetary Union (important):                                *</b><br/><b>*                                                                    *</b><br/> <b>* 480984 Currency conversion, message MN 471</b><br/> <b>*        (rebate arrangement currency cannot be converted)</b><br/> <b>*</b><br/> <b>* 439493 Incorrect document currency w/ billing via billing interface *</b><br/> <b>*        (incorrect posting in Financial Acctng, currency exchanged)  *</b><br/> <b>*                                                                    *</b><br/> <b>* 452365 Rebate arrangement currency not copied to condition records  *</b><br/> <b>*        (undesired currency in condition record)                    *</b><br/> <b>*                                                                    *</b><br/> <b>* 437429 Message SG105 List settlement documents, setup incomes       *</b><br/> <b>*        (Revenues in Financial Accounting and/or S074 incorrect)     *</b><br/> <b>*                                                                    *</b><br/> <b>* 400432 Expiring currencies, rebate arrangmnt maint. (purch., sales) *</b><br/> <b>*                                                                    *</b><br/> <b>* 398739 Incorrect conversion of EMU currencies - Part 2              *</b><br/> <b>***********************************************************************</b><br/> <b>* Notes that you should definitely implement as a precaution since    *</b><br/> <b>* the consequences may be difficult to correct:                      *</b><br/> <b>*                                                                    *</b><br/> <b>* 422649 Error in validity display (agrmt maintenance)- deletion      *</b><br/> <b>*                                                                    *</b><br/> <b>* 398739 Incorrect conversion of EMU currencies - Part 2              *</b><br/> <b>*        This note must be implemented (even by customers not         *</b><br/> <b>*        affected by the currency changeover)!                        *</b><br/><b>*                                                                    *</b><br/> <b>* 400893 Detailed statement, message MN 514                          *</b><br/> <b>*                                                                    *</b><br/> <b>* 408521 MN 495 (Update simulation), MN 514 (detailed statement)      *</b><br/> <b>*                                                                    *</b><br/> <b>* 394619 Provisions f income n cancelled if cancelling settlement doc *</b><br/> <b>*                                                                    *</b><br/> <b>* 315769 Subsequent settlement: Subsequent business volume update     *</b><br/> <b>* Goods receipt pricing                                              *</b><br/> <b>*                                                                    *</b><br/> <b>* 305388 Subs.Settlmnt: Subs.busi.vol.update in wrong period          *</b><br/> <b>*                                                                    *</b><br/> <b>* In Release 4.0A/B, you must implement Note                          *</b><br/><b>* 303047 RMCENEUA - Error in selection by info structures             *</b><br/> <b>* Otherwise, the wrong revenues might be settled!                    *</b><br/> <b>*                                                                    *</b><br/> <b>* In Release 4.0A/B, you must implement Note                          *</b><br/><b>* 175341 Subsq. Settlement: Multiple updates of income in             *</b><br/> <b>* customer billing documents. Otherwise, the wrong incomes            *</b><br/> <b>* might be settled!                                                  *</b><br/> <b>*                                                                    *</b><br/> <b>* 180434 Sub.settlmt: Verifcatn o.rel docmnts befre archving          *</b><br/> <b>*                                                                    *</b><br/> <b>* 179864 PURCHIS - commitments - problems during IR or GR             *</b><br/> <b>* Releases 4.0A and 4.0B                                              *</b><br/><b>*                                                                    *</b><br/> <b>* 176433 Subsq. Settlement: Recompilation of income, incomes are      *</b><br/> <b>*        completely deleted                                          *</b><br/> <b>* Releases 4.0A and 4.0B                                              *</b><br/><b>*                                                                    *</b><br/> <b>* 175160 PURCHIS - GR for several PO items with PO generation         *</b><br/> <b>* Releases 4.0A and 4.0B                                              *</b><br/><b>*                                                                    *</b><br/> <b>* 175138 PURCHIS - GR with delivery generation - volume-based rebate  *</b><br/> <b>* Releases 4.0A and 4.0B                                              *</b><br/><b>*                                                                    *</b><br/> <b>* 160570 Subsequent settlement: Subs. update and price determination  *</b><br/> <b>*        date                                                        *</b><br/> <b>* Releases 4.0A and 4.0B                                              *</b><br/><b>*                                                                    *</b><br/> <b>* 160440 Subs. settlement: Wrong update of PI recompilation           *</b><br/> <b>* Releases 4.0A and 4.0B                                              *</b><br/><b>*                                                                    *</b><br/> <b>* 153954 Subs. Settlement: incorrect subsequent update with value 0   *</b><br/> <b>* Releases 4.0B                                                      *</b><br/> <b>*                                                                    *</b><br/> <b>* 153577 Subs. Settlement: Too many recompilations/subsequent updates *</b><br/> <b>* Releases 4.0B                                                      *</b><br/> <b>*                                                                    *</b><br/> <b>* 153054 Subsequent settlement: Recompilation of business volume data *</b><br/> <b>*        deletes all business volume data                            *</b><br/> <b>* Release 4.0B                                                        *</b><br/><b>*                                                                    *</b><br/> <b>* 151398 PURCHIS - double update for delivery costs for several       *</b><br/> <b>*                  purchase orders                                    *</b><br/><b>* Releases 4.0A und 4.0B                                              *</b><br/><b>*                                                                    *</b><br/> <b>* 104867 Sub. settlnt: Updating business volume data incorrect        *</b><br/> <b>* Releases 4.0A and 4.0B                                              *</b><br/><b>*                                                                    *</b><br/> <b>* 120610 Subs. settl: Data loss vendor busn. volume comp.             *</b><br/> <b>*                                                                    *</b><br/> <b>* 206060 Ind. 'Subseq. settlement', deviating vendor data             *</b><br/> <b>*        see also Note 214146!                                        *</b><br/><b>***********************************************************************</b><br/> <p><br/>This Note refers to Release 4.0B only. For Release 4.5B, refer to special Note 152725, for Release 4.6, Note 183379 as well as Note 40147 for Release 3.0A to 3.1I.<br/></p> <b>General information</b><br/> <p>Above all, this note should point out the most frequent problems and errors during subsequent settlement (purchasing). It should help to analyze frequently occuring problems and perhaps to solve them.<br/>You find a list of all notes on subsequent settlement which refer to known problems in section \"Related notes\". All notes to correct program errors will be delivered with the next available Hot Package.</p> <b>Handling</b><br/> <p>If you have problems with subsequent settlement (purchasing), you can first try to solve the problem yourself using this note. Check whether one of these notes refers to your problem. Read the note and execute the checks specified there. In it you may find references to notes which may solve the errors.<br/>Please note that you can access various informational consulting notes on the topics of purchasing, goods receipt, etc., using search criteria MM-PUR* (component) and FAQ (search text).</p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement, volume-based rebate, collective note<br/>Transactions MEB1, MEB2, MEB3, MEB4, MEB6, MEB8, MEB9, MCE+, MEBA<br/>Programs SAPMV13A, SAPMNB01, RWMBON01, RWMBON03, RWMBON04, RWMBON06, RWMBON08, RMCE0900<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><ol>1. Notes on problem solution/consulting</ol> <p><br/>167284 Subsequent settlement: general notes (Q&amp;As and Customizing)<br/><br/>104683 Sub. settlement: changes in Release 4.0A/B<br/>                    Innovations/changes in Release 4.0A/B in the overview<br/><br/>75655 Error message VK358: 'PurchOrg &amp; with CoCode &amp; deviates from the<br/>      CoCode in the arrangement'.<br/>                    Underlying problem:</p> <ul><ul><li>You work with several company codes, that is, your purchasing organization is not permanently assigned to a company code. Also see Note 153694.</li></ul></ul> <p><br/>153694: Subsequent settlement: Credit-side or debit side<br/>        settlement type<br/>Note on the importance of the settlement type.<br/><br/>72199 Subsequent settlement: updating vendor business volumes<br/>                    Frequent symptoms:</p> <ul><ul><li>No update of business volume data is executed for a document.</li></ul></ul> <ul><ul><li>For a document, an update of business volume data is executed but the scale basis value or condition basis has value 0.</li></ul></ul> <p><br/>112413 Subsequent settlement: units of measure<br/>                    Notes:</p> <ul><ul><li>Possible problems when using units of measure</li></ul></ul> <p><br/>113031 Subsequent settlement: Taxes<br/>                    Notes:</p> <ul><ul><li>Tax handling (calculation) in the process of subsequent settlement</li></ul></ul> <p><br/>77258 Subsequent settlement: Required fields settlement doc.<br/>                    Frequent symptoms:</p> <ul><ul><li>When settling an arrangement error messages</li></ul></ul> <p>                    00055 \"Required entry not made\" or<br/>                    00344 \"No batch input data for screen SAPMM08R xxxx\"<br/>                    are generated.<br/><br/>80233 Debit-side settlement: Customizing, error message<br/>                    Frequent symptoms:</p> <ul><ul><li>Error messages of message class VF</li></ul></ul> <p>                    Notes:</p> <ul><ul><li>Procedure during SD Customizing from the point of view of the subsequent settlement</li></ul></ul> <p><br/>73214 Subs.settl.: Retrospec. compltn/rcompltn o.bus.vol.data<br/>                    Frequent symptoms:</p> <ul><ul><li>With the retrospective compilation of vendor business volumes, no update is executed.</li></ul></ul> <p>                    Notes:</p> <ul><ul><li>Procedure when using the function</li></ul></ul> <ul><ul><li>Functional restrictions of the function</li></ul></ul> <p><br/>381831 Consignment processing and subsequent billing<br/>Frequent symptoms:<br/>No update of business volume for settlement documents in consignment processing<br/><br/>333914 Subsequent settlement: Performance (composite note)<br/></p> <ol>2. Notes on known problems</ol> <p>See section \"Related notes\".<br/></p></div>", "noteVersion": 55}, {"note": "40147", "noteTitle": "40147 - Collective note: Subsequent settlement (Purch.) Release 3.0", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>-- Collective note: Problems and errors with                        --<br/>--                  subsequent settlement (Purchasing)               --<br/>--                  Releases 3.0A through 3.1I                      --<br/>--                  Version: December 04, 2001                      --</p> <b>***********************************************************************</b><br/> <b>* European Monetary Union (important - Release 3.1I only):            *</b><br/> <b>*                                                                    *</b><br/> <b>* 439493 Incorrect document currency with billing via billing         *</b><br/> <b>*        interface (incorrect posting in Financial Accounting,        *</b><br/> <b>*        currency exchanged)                                          *</b><br/><b>*                                                                    *</b><br/> <b>* 452365 Rebate arrangement currency not copied to condition records  *</b><br/> <b>*        (undesired currency in condition record)                    *</b><br/> <b>*                                                                    *</b><br/> <b>* 437429 Message SG105 List settlement documents, recompiling incomes *</b><br/> <b>*        (incomes in Financial Accounting and/or S074 incorrect)      *</b><br/> <b>*                                                                    *</b><br/> <b>* 400432 Expiring currencies, rebate arrangement maintenance.         *</b><br/> <b>*        (puchasing, sales)                                          *</b><br/> <b>*                                                                    *</b><br/> <b>* 398739 Incorrect conversion of EMU currencies - Part 2              *</b><br/> <b>***********************************************************************</b><br/> <b>* Notes you must implement beforehand since it might be complicated   *</b><br/> <b>* to correct the consequences:                                        *</b><br/><b>*                                                                    *</b><br/> <b>* 422649 Condition records were deleted in error                      *</b><br/><b>*                                                                    *</b><br/> <b>* 398739 Incorrect conversion of EMU currencies - Part 2              *</b><br/> <b>*        This note must be implemented (even by customers not         *</b><br/> <b>*        affected by the currency changeover)!                        *</b><br/><b>*                                                                    *</b><br/> <b>* In Release 3.0, you must implement Note                            *</b><br/> <b>* 303047 RMCENEUA - Error in selection by info structures             *</b><br/> <b>* Otherwise, the wrong incomes might be settled!                      *</b><br/><b>*                                                                    *</b><br/> <b>* In Release 3.0, you must implement Note                            *</b><br/> <b>* 175341 Subsequent Settlement: Multiple income updates when changing *</b><br/> <b>* customer billing document. Otherwise, the wrong incomes might be    *</b><br/> <b>* settled!                                                            *</b><br/><b>*                                                                    *</b><br/> <b>* 216306 Subsequent settlement: Missing income updates of customer    *</b><br/> <b>*        billing document                                            *</b><br/> <b>* Release 3.1I, occurs between Support Package SAPKH31I43 and         *</b><br/> <b>* SAPKH31I50 (inclusive) or after implementing Note 193221.           *</b><br/> <b>*                                                                    *</b><br/> <b>* 180434 Subsequent settlement: Validation of relevant documents      *</b><br/> <b>*        before archiving                                            *</b><br/> <b>*                                                                    *</b><br/> <b>* 179864 PURCHIS - commitments - problems during IR or GR             *</b><br/> <b>* Releases 3.0C through 3.1I                                          *</b><br/><b>*                                                                    *</b><br/> <b>* 151398 PURCHIS - double update for delivery costs from several POs  *</b><br/> <b>* Releases 3.0 through 3.1I                                          *</b><br/> <b>*                                                                    *</b><br/> <b>* 120610 Subsequent settlement: Data loss compilation of vendor       *</b><br/> <b>*        business volume data for short dump                          *</b><br/><b>* Releases 3.0F through 3.1I                                          *</b><br/><b>*                                                                    *</b><br/> <b>* 206060 Indicator 'Subsequent settlement', different vendor data     *</b><br/> <b>* See Note 214146                                                    *</b><br/> <b>* Release 3.0F through 3.1I                                          *</b><br/> <b>***********************************************************************</b><br/> <p><br/>This note refers to all Releases 3.0A to 3.1I including the industry-specific solution IS Retail. For Release 4.0B, refer to Note 104668, for Release 4.5B, refer to Note 152725, and for Release 4.6, refer to Note 183379.<br/></p> <b>General Information</b><br/> <p>In part 1, this note is intended to point out the most frequent problems and errors related to subsequent settlement (purchasing). It is meant to help analyze and solve frequent problems.<br/>In part 2, there is a list of all notes that relate to known problems with subsequent settlement.</p> <b>Handling</b><br/> <p>If you have problems with subsequent settlement (purchasing), you can try to solve the problem by referring to this note. Check whether one of the notes from part 1 refers to your problem. Choose the appropriate note and carry out the checks specified in the note. If necessary see references to notes in part 2 to correct the errors.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement, volume-based rebate, collective note.<br/>Transactions MEB1, MEB2, MEB3, MEB4, MEB6, MEB8, MEB9, MCE+, MEBA<br/>Programs SAPMV13A, SAPMNB01, RWMBON01, RWMBON03, RWMBON04, RWMBON06, RWMBON08, RMCE0900<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><ol>1. Notes on problem solution/consulting</ol> <p><br/>167284 Subsequent settlement. General notes (FAQs and Customizing)<br/><br/>75655  Error message VK358: PurchOrg &amp; with CoCode &amp; is different<br/>       from CoCode &amp; in the agreement.<br/>                    Underlying problem:</p> <ul><ul><li>You are using several company codes, so your purchasing organization is not assigned to one single company code (see Note 153694).</li></ul></ul> <p><br/>153694 Subsequent settlement: Settlement type credit-side or debit-side<br/></p> <ul><ul><li>Note on the relevance of the settlement type</li></ul></ul> <p><br/>72199 Subsequent settlement: Problem analysis when updating vendor<br/>      business volume<br/>                    Frequent symptoms:</p> <ul><ul><li>Vendor business volumes for a document are not updated.</li></ul></ul> <ul><ul><li>Vendor business volumes for a document are updated, however the scale basis or condition basis has value 0.</li></ul></ul> <p><br/>77258 Subsequent settlement: Required fields settlement document<br/>                    Frequent symptoms:</p> <ul><ul><li>When settling an agreement, the following error messages are generated:</li></ul></ul> <p>                    00055 \"Required entry not made\"<br/>                    00344 \"Batch input data for screen SAPMM08R xxxx does not exist\"<br/><br/>113031 Subsequent settlement: Taxes<br/>                    Notes:</p> <ul><ul><li>Tax treatment (calculation) when processing subsequent settlement</li></ul></ul> <p><br/>80233 Debit-side settlement: Customizing, error messages<br/>                    Frequent symptoms:</p> <ul><ul><li>Error messages in message class VF</li></ul></ul> <p>                    Notes:</p> <ul><ul><li>Procedure during SD Customizing in terms of subsequent settlement</li></ul></ul> <p><br/>73214 Subsequent settlement: Subsequent compilation of vendor business<br/>      volume<br/>                    Frequent symptoms:</p> <ul><ul><li>Nothing is updated during the subsequent compilation of vendor business volumes.</li></ul></ul> <p>                    Notes:</p> <ul><ul><li>Procedure when using the function</li></ul></ul> <ul><ul><li>Functional restrictions</li></ul></ul> <p><br/>381831 Consignment processing and subsequent settlement<br/>                    Frequent symptoms:</p> <ul><ul><li>No update of business volume for settlement documents in consignment processing</li></ul></ul> <p><br/>333914 Subsequent settlement: Performance (composite note)<br/></p> <ol>2. Notes on known problems</ol> <p><br/>Refer to Composite Note 67481 for debit-side settlement which groups all problems in connection with the general billing interface.<br/>Refer to the following notes in relation to subsequent settlement (volume-based rebate) in purchasing.<br/>These notes are arranged by the release up to which they are valid. For example, for Release 3.0F all notes from sections 3.0F, 3.1G and higher should be referred to, as far as they apply to Release 3.0F.<br/><br/>Valid until further notice:<br/><br/>74020 Updating and posting provisions for accrued income<br/><br/>60174 Arrangement and settlement calendar AJ, AM (incorrect special<br/>      rules)<br/><br/>Valid up to and including Release 3.1I:<br/><br/>118080 Subsequent settlement: Message MN319 during detailed statement<br/>                    Releases 3.0F through 3.1I<br/><br/>117988 Subsequent settlement: messages MN514 and MN515<br/>                    Releases 3.0F through 3.1I<br/><br/>111366 Subsequent settlement: Error messages MN164 and MN310<br/>                    Releases 3.00 and 3.1I<br/><br/>113315 VK894: Screen generation error for rebate<br/>                    Releases 3.0D and 3.1I<br/><br/>107699 Subsequent settlement: Error MN305 during extension<br/>                    Release 3.1I<br/><br/>105687 ME41: Volume rebate indicator from vendor master<br/>                    Releases 3.00 and 3.1I<br/><br/>103695 Inconsistent deletion of period-specific conditions<br/>                    Releases 3.0F through 3.1I<br/><br/>100695 Subsequent settlement: No update during cancellation<br/>                    Releases 3.00 through 3.1I<br/><br/>Valid up to and including Release 3.1H:<br/><br/>97483 Subsquent settlement: Too many items in settlement document<br/>                    Releases 3.0F through 3.1H<br/><br/>96375 Subsequent settlement: Error vendor business volumes for<br/>      subsequent debit/credit<br/>                    Releases 3.00 through 3.1H<br/><br/>96224 Subsequent settlement: recompilation, Message MN380<br/>                    Releases 3.1G through 3.1H<br/><br/>96131 Subsequent settlement: default account assigments not copied<br/>                    Releases 3.00 through 3.1H<br/><br/>94381 Incorrect currency translation for detailed statement<br/>                    Releases 3.0C through 3.1H<br/><br/>90661 Subsequent settlement: Delete purchase order item<br/>                    Releases 3.0F through 3.1H<br/><br/>89671 Incorrect update incomes for debt-side settlement account.<br/>                    Releases 3.0F through 3.1H<br/><br/>89027 Update termination after settlement document cancellation<br/>                    Releases 3.0F through 3.1H<br/><br/>85831 Subsequent settlement: Incorrect price determination<br/>                    Releases 3.0F through 3.1H<br/><br/>84256 Extension copies deleted condition records<br/>                    Releases 3.0D through 3.1H<br/><br/>78695 Termination when checking settlement material<br/>                    Releases 3.0A through 3.1H<br/><br/>76917 Condition info: Syntax error in program RV13A<br/>                    Release 3.1H<br/><br/>76713 Subsequent settlement: Foreign payments<br/>                    Releases 3.0A through 3.1H<br/><br/>66054 Subsequent settlement - no update<br/>                    Releases 3.00 through 3.0F<br/><br/>Valid up to and including Release 3.1G:<br/><br/>77509 Subsequent settlement: Error FF718 in business volume comparison<br/>      and agreement<br/>                    Releases 3.0F through 3.1G<br/><br/>75100 SD-FI: Transfer of incorrect tax indicators<br/>      (72173 Incorrect tax code for several taxes)<br/>                    Releases 3.0D through 3.1G<br/><br/>Caution:</p> <ul><li>Only implement the source code from Note 75100!</li></ul> <ul><li>Only for debit-side settlement type!</li></ul> <p><br/>73183 Subsequent settlement - meaning of error message MN042<br/>                    Releases 3.0A through 3.1G<br/><br/>69800 Subsequent settlement: Error message - FF758<br/>                    Releases 3.0A through 3.1G<br/><br/>Valid up to and including Release 3.0F:<br/><br/>62882 LIS - Subsequent settlement - Field catalog EKOK<br/>                    Releases 3.0A through 3.0F<br/><br/>Valid up to and including Release 3.0E:<br/><br/>67669 Subsequent settlement: Expand field catalog<br/>                    Releases 3.0A through 3.0E<br/><br/>62426 Accounting document contains incorrect incomes<br/>                    Releases 3.0A through 3.0E<br/><br/>60397 View cluster: Problem maintaining a sub-level<br/>      (create or change access sequence)<br/>                    Releases 3.0D through 3.0E<br/><br/>49528 Subsequent settlement itemization error MN I 355<br/>                    Releases 3.0C through 3.0E<br/><br/>49067 Conversion problem when updating vendor business volume<br/>                    Releases 3.0C through 3.0E<br/><br/>48968 Incorrect update during credit memo cancellation<br/>                    Releases 3.0C through 3.0E<br/><br/>48497 Subsequent settlement: Item text, allocation number<br/>                    Releases 3.0A through 3.0E<br/><br/>48245 No provisions posting for accrued income goods receipt<br/>                    Releases 3.0A through 3.0E<br/><br/>48078 Subsequent settlement and tax jurisdiction code<br/>                    Releases 3.0C through 3.0E<br/><br/>48063 Subsequent settlement: Error message FF758<br/>                    Releases 3.0A through 3.1G<br/><br/>47987 Subsequent statistics structure, incorrect data<br/>                    Releases 3.0D and 3.0E<br/><br/>47199 Double update for subsequent statistics structure<br/>                    Releases 3.0D and 3.0E<br/><br/>Valid up to and including Release 3.0D:<br/><br/>49575 Subsequent Settlement itemization error MN I 355<br/>                    Release 3.0D<br/><br/>48679 Error setting reference fields in Financial Accounting (RW012)<br/>                    Releases 3.0C through 3.0D<br/><br/>46612 Incorrect itemization arrangements purchasing<br/>                    Releases 3.0C through 3.0D<br/><br/>46128 Incorrect itemization arrangements purchasing<br/>                    Releases 3.0C through 3.0D<br/><br/>45057 Error when creating with reference to arrangement in purchasing<br/>                    Releases 3.0A through 3.0D<br/><br/>41380 Error when checking organization data for arrangement<br/>                    Release 3.0D<br/><br/>41353 No purchasing group when creating arrangement<br/>                    Releases 3.0A through 3.0D<br/><br/>41086 Arrangements in purchasing cannot be saved<br/>                    Releases 3.0A through 3.0D<br/><br/>41072 Subsequent statistics structure arrangements purchasing<br/>                    Release 3.0D<br/><br/><br/>Valid up to and including Release 3.0C:<br/><br/>45579 Vendor business volume update incomplete<br/>                    Release 3.0C<br/><br/>39574 Income update<br/>                    Release 3.0C<br/><br/>39557 Document balance credit memo<br/>                    Releases 3.00 through 3.0C<br/><br/>39327 Vendor not relevant to volume rebate<br/>                    Releases 3.0A through 3.0C<br/><br/>39172 Incorrect payment method check in maintenance arrangements<br/>                    Releases 3.0A through 3.0C<br/><br/>34584 Error when creating purchasing organization arrangement without<br/>      company code<br/>                    Releases 3.0A through 3.0C<br/></p></div>", "noteVersion": 77}]}, {"note": "2194923", "noteTitle": "2194923 - S4TC SAP_APPL   Pre-Transition Checks for Subsequent Settlement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Before conversion to SAP S/4HANA, pre-transition checks for the application component Subsequent Settlement ( MM-PUR-VM-SET ) must be performed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC; S111; KONA;</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Installation of pre-transition check class for Subsequent Settlement.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Refer to the attached Correction Instructions</p>", "noteVersion": 8}, {"note": "2215220", "noteTitle": "2215220 - S/4 PreChecks: Information for Susequent Settlement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>During the pre-transition checks for 'Subsequent Settlement' the following message is displayed</p>\n<p>'Check if Subsequent Settlement is active - See SAP Note 2194923 2015'</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC; KONA; S111</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<ol>\n<li>In SAP S/4HANA Release the application Subsequent Settlement ( MM-PUR-VM-SET ) is replaced by the new Contract Settlement (LO-GT-CHB) application. <span 'times=\"\" 12pt;=\"\" ar-sa;\"=\"\" arial','sans-serif';=\"\" en-us;=\"\" font-size:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">For this reason, the functional scope of subsequent settlement has been restricted, </span>that is, in SAP S/4HANA, it is no longer possible to: <br/>-  Create new rebate arrangements using transaction MEB1 or <br/>-  Extend existing rebate arrangements using transactions MEBV / MEB7 / MEBH<br/><br/></li>\n<li>Additionally, as a consequence of the material field length extension in the SAP S/4HANA landscape, the structure of table S111 was adjusted. This can have some impact if the business volume data (table S074) has to be rebuilt in the SAP S/4HANA system.</li>\n</ol>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<ol>\n<li>In SAP S/4HANA, Contract Settlement replaces Subsequent Settlement, which means that existing rebate agreements can only be processed up until the end of the validity date of the agreement and must then be closed by a final settlement. Afterwards new agreements can only be created based on condition contracts.<br/><br/></li>\n<li>Furthermore, if a recompilation of the business volume data in SAP S4HANA is required, the index table S111 has to be rebuilt as described in SAP Note 73214 (Subseq.settl.: Retrospec.compltn/recompltn of busin.vol.data).</li>\n</ol>", "noteVersion": 2}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "The rebate process will need to be redesigned based on condition contracts."}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If business has long running rebate agreements that will continue to be active post system conversion to SAP S4/HANA, such agreements will have to be migrated to equivalent condition contracts."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Customer code will have to be adopted as per the new design for Rebate functionality using Condition Contracts"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Users needs to be trained in using Condition contracts for their rebate scenarios "}]}