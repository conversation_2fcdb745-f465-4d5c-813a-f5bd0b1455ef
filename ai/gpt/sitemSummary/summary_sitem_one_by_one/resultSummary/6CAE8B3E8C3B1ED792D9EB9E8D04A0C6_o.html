<h3>BW402: BEx Web Templates (7.x)</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2444138">2444138 - BW4SL - BEx Objects (to be redesigned)</a></strong></p>
<p><strong>Description:</strong></p>
<p>The following SAP Business Explorer (BEx) objects are not available in SAP BW/4HANA:
    BEx Workbooks,
    BEx Web Item (Format SAP BW 7.x),
    BEx Web Template (Format SAP BW 7.x),
    BEx Broadcast Settings,
    BEx Precalculation Server.

The following SAP Business Explorer (BEx) settings are not available in SAP BW/4HANA:
    BEx Web Design Time Item and Parameter Metadata,
    BEx Themes,
    BEx Conversion Services.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The absence of BEx objects and settings in SAP BW/4HANA requires redesigns for existing reporting and analysis tools:
    <ul>
        <li>BEx Workbooks may not be used in their current form.</li>
        <li>BEx Web Items and Templates (7.x format) need revisions to ensure compatibility.</li>
        <li>BEx Broadcast Settings and Precalculation Servers are not supported.</li>
        <li>BEx Web and Design Time settings like Themes and Metadata are deprecated.</li>
    </ul>
Users are recommended to transition to new reporting tools provided by SAP BusinessObjects Analysis, Lumira Designer, or SAP Analytics Cloud for their relevant scenarios.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Required and recommended actions for transitioning from BEx objects include:
    <ul>
        <li>Redesign BEx Workbooks using SAP BusinessObjects Analysis, edition for Microsoft Office.</li>
        <li>Transition BEx Web scenarios to SAP Lumira Designer or SAP Analytics Cloud.</li>
        <li>Use SAP Crystal Reports for publishing and distribution of Broadcasting scenarios.</li>
        <li>Run program RS_B4HANA_CONVERSION_CONTROL to check which objects can be supported or converted.</li>
        <li>Manual Deletion of unsupported objects using program RS_DELETE_TLOGO.</li>
    </ul></p>
<p><strong>Reference Notes:</strong>
    <ul>
        <li><a href="https://launchpad.support.sap.com/#/notes/2496706">2496706 - BEx Web Application Add-on for SAP BW/4HANA - Deployment and Limitations</a></li>
        <li><a href="https://launchpad.support.sap.com/#/notes/2542290">2542290 - Pilot: Enable Web Application Designer</a></li>
        <li><a href="https://launchpad.support.sap.com/#/notes/2620191">2620191 - BEx: Connecting to a BW System undergoing In-Place Conversion to BW4HANA is not possible with Patches for SAP GUI 7.40</a></li>
        <li><a href="https://launchpad.support.sap.com/#/notes/2444138">2444138 - BW4SL - BEx Objects (to be redesigned)</a></li>
        <li><a href="https://launchpad.support.sap.com/#/notes/2246699">2246699 - SAP BW/4HANA Starter Add-On - Pre-Requisites/Installation/De-Installation/Update</a></li>
        <li><a href="https://launchpad.support.sap.com/#/notes/2383530">2383530 - Conversion from SAP BW to SAP BW/4HANA</a></li>
    </ul>
</p>