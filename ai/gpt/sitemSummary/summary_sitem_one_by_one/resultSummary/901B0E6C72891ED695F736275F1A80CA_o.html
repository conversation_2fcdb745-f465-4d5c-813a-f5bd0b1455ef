<h3>SI9: FIN_CO</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2349278">2349278 - 2349278 - S4TWL - Profitability Analysis</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case. Account-based CO-PA Margin Analysis S/4HANA S/4 HANA Migration Profitability Analysis Task: Check if Profitability Analysis is activated in your system. Procedure: Go to implementation guide (Transaction SPRO) --> Controlling --> Profitability Analysis --> Flows of Actual Values --> Activate Profitability Analysis. If there is an activation flag in the columns “costing-based” or “margin analysis” this note is relevant for you.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The universal journal (ACDOCA) is the heart of Accounting and includes all Margin Analysis characteristics in order to allow multi-dimensional reporting by market segment. Existing reporting transactions, such as KE30 and KE24, will continue to work, but also investigate possible usage of the Fiori apps to display information by market segment, including additional currencies and data from the extension ledgers.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Analyze existing reporting requirements to understand how market segment reporting is handled today. Also include transfer of profitability information to other systems, including data warehouses and consolidation. During conversion, the system will create columns in the universal journal for all characteristics in the operating concern. If costing-based CO-PA is currently used, analyze what workarounds are being used to reconcile the profit and loss statement with costing-based CO-PA to determine whether these will be required in the future. Check whether the functions available with Margin Analysis can already satisfy your reporting requirements or whether you will continue to need costing-based CO-PA for an interim period. If combined Profitability Analysis is currently used, check whether the functions available with Margin Analysis can already satisfy your requirements. If you only used costing-based CO-PA but no Margin Analysis before the conversion you need to understand when a profitability segment is created in the sales process and how COGS are posted during the delivery. In Margin Analysis the billing and the delivery are recorded as <strong>separate</strong> documents and both journal entries are assigned to a profitability segment. This means a change to include the assignment to a profitability segment in the COGS posting and the need to change the account determination to select an account/cost element.
<ul>
<li>The profitability segment is always derived for the sales order item and referenced during the delivery. Since the delivery document is now assigned to a profitability segment, you will need to ensure that you are updating an account of type P (primary costs and revenues) rather than an account of type N (non-operating expenses). Check the account determination for transaction GBB (Offsetting Entry for Inventory Posting) and account modification constants VAX and VAY. COGS posted before the conversion are posted without a cost element (type N), but after the conversion with a cost element (type P). To avoid issues, assign an account of type N to account modification VAX and an account of type P to account modification VAY (Margin Analysis works with VAY as a default).</li>
<li>If you only worked with costing-based CO-PA and did not use incoming sales orders (record type A) prior to the migration, you will have open sales order items and delivery documents with no assignment to a profitability segment, even though this is required once Margin Analysis is active and the system will issue a message that the assignment to a profitability segment is missing if you reschedule the sales order item or post a goods issue after the migration. You can use transaction KE4F to derive the missing profitability segments for a small numbers of sales order items. For larger volumes of data, run the transaction in parallel over multiple packets of different sales orders (see SAP Note 2225831).</li>
<li>Where sales orders are partially delivered at the time of conversion, it is recommended to cancel the deliveries and create new ones as this will result in the profitability segment being selected with reference to the sales order during delivery. However, this solution is not practical where goods movements have already been posted with reference to the delivery. The COGS for these goods movements will not be visible in Margin Analysis.</li>
<li>If you choose to use the same G/L account for account modification constants VAX and VAY, you should also cancel all deliveries posted prior to the conversion and create new ones to ensure that the assignment to the profitability segment is correct.</li>
</ul>
</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/1972819">1972819 - Setup SAP BPC optimized for S/4 HANA Finance and Embedded BW Reporting (aka Integrated Business Planning for Finance)</a>, <a href="https://launchpad.support.sap.com/#/notes/2225831">2225831 - Activation of incoming sales orders or account-based PA in case of large sales order volumes</a>, <a href="https://launchpad.support.sap.com/#/notes/2805090">2805090 - Changed display options for types of profitability analysis</a>, <a href="https://launchpad.support.sap.com/#/notes/2413524">2413524 - S/4 HANA Finance: Additional unit of measures not displayed</a>