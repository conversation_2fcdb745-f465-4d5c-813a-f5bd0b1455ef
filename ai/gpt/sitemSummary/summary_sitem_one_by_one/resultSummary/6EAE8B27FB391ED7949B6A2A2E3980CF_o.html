<h3>SI25: OIL_ARCHIVE_EXCHANGES</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2534442">2534442 - S4TWL Data Archiving for Exchanges (Data Archiving Object OIA_EXG)</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition 1709. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Exchange agreements can be archived using the Data Archiving Object OIA_EXG. All the exchange agreements that have the status of 'To be archived' can be archived using this object. Note: Exchanges Data Archiving Object OIZ_EXG is responsible only for Exchanges related tables. Full use requires archiving MM and SD objects first to archive underlying documents in exchange agreements, such as Sales contracts and Purchase contracts.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Data cleanup / archiving is to be performed after the conversion project, but is optional. Delivery of data archiving object OIA_EXG is required for this process.</p>