<h3>SI24: OIL_CMM_INTEGRATION</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2534268">2534268 - S4TWL IS_OIL & Commodity Pricing Engine Integration 1709</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition 1709. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>During the integration of Commodity Pricing Engine in an S/4 IS-OIL System, specific changes were made to ensure smooth functionality. These include the creation of the OI0_CPE package for changes, adjustments to condition types in SD and MM transactions, and the introduction of new BC Sets for configuration tables.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Use the new package created to capture changes related to the integration of commodity pricing engine.</p>
<p>Provide user training on the UI changes in certain SD and MM transactions.</p>
<p>Use the new SAP for Oil and Gas customizing templates (BC sets) in the commodity pricing engine as a reference for functional implementation projects.</p>
<p><strong>Reference Notes:</strong> 
<a href="https://launchpad.support.sap.com/#/notes/2226096">2226096 - Simplification List: Commodity Management in SAP S/4HANA OP</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2671009">2671009 - S4TWL – Integration of Pricing and Payment Events in GTM with Commodity Management</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2547347">2547347 - S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2461007">2461007 - S4TWL -  CM: CPE Simplified Data Model</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2461004">2461004 - S4TWL -  CM: CPE Simplified CPE Activation</a>
</p>