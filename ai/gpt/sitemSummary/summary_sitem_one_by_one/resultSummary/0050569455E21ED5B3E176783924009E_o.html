<h3>SI12: FIN_TRM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270526">2270526 - S4TWL - Offers</a></strong></p>
<p><strong>Description:</strong></p>
<p>Offers in transaction management are not available in S4/HANA.</p>
<p>Read the following information in case the pre-upgrade check in your system issues a message for the check ID SI12_OFFERS "Offers": This check issues a warning if it finds entries in the database which indicate that offers were created in some client of the system. The warning message is for your information, no further mandatory action is required.</p>
<p>Read the following information in case the custom code analysis has detected customer coding related to this transition worklist item: SAP objects which are used by the detected customer code shall not be used any more. Adapt the customer code accordingly.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The unavailability of offers in transaction management could affect any business processes dependent on this functionality. Custom code utilizing these offers must be adapted to work with the new SAP S/4HANA environment.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Adapt custom code that relied on the transaction management offers.</p>
<p>Provide training for users during the conversion project.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2190420">2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code</a>, <a href="https://launchpad.support.sap.com/#/notes/2241080">2241080 - SAP S/4HANA: Content for checking customer specific code</a>, <a href="https://launchpad.support.sap.com/#/notes/2436688">2436688 - Recommended SAP Notes for Using S/4HANA Custom Code Checks in ATC or Custom Code Migration App</a>, <a href="https://launchpad.support.sap.com/#/notes/2294371">2294371 - S4TC EA-FINSERV Master Check for S/4 System Conversion Checks</a>