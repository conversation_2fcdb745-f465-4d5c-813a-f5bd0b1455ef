<h3>SI13: Logistics_PP</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2268089">2268089 - S4TWL - Total Dependent Requirements</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. MRP runtime depends among many other factors on the number of material receipts and requirements that have to be read to compute a material's shortages. Materials, which are component of very many BOMs, can have many thousands of dependent requirements. Total dependent requirements reduced the number of dependent requirements, which had to be read by MRP at the expense of possible locking conflicts. HANA is very good at summing up many rows in a table. There is no need to compute and write totals every time a single record is changed. Neither MRP Live nor the classic MRP support total dependent requirements in SAP S/4HANA.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Business processes are not changed. MRP reads individual dependent requirements rather than total dependent requirements.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Follow the instructions for "Clean up Total Requirements" in the SAP help portal <a href="http://help.sap.com/erp2005_ehp_07/helpdata/EN/8f/1be5514ec5c90ae10000000a44176d/frameset.htm" target="_blank">Link</a></p>
<p>Because of in-memory computing capabilities of HANA, there is no need to work with total dependent requirements. MRP Live creates many planned orders in parallel, where locking conflicts could be caused using total dependent requirement and affect runtime. Custom code reading one of the tables RQIT, RQHD, or MDRI will no longer work. Read table RESB instead. See SAP Note 2229126.</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2229126">2229126 - SAP S/4HANA simplification item SI_PP13: Logistics_PP-MRP - Total dependent requirements</a></p>