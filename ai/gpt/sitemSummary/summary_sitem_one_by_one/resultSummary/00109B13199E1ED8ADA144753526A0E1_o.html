<h3>SI040: CRM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2692033">2692033 - Sales orders and contracts based on CRM transactional model not supported</a></strong></p>
<p><strong>Description:</strong></p>
<p>Sales orders and sales contracts based on the CRM transactional model are not supported in S/4HANA. These objects are available in S/4HANA Sales, based on the SD data model. Similarly, sales agreements are not supported.</p>
<p><strong>Business Process Impact:</strong></p>
<p>If you are considering or planning a migration of a CRM 7.0 or CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release, the deprecated functionality of sales orders and contracts based on the CRM transactional model may affect your business processes.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Review your use of sales orders/sales quotations and sales contracts:
<ul>
<li>If you are using sales orders/sales quotations, check for entries in table CRMD_ORDERADM_H with OBJECT_TYPE = 'BUS2000115'.</li>
<li>If you are using sales contracts, check for entries in table CRMD_ORDERADM_H with OBJECT_TYPE = 'BUS2000121'.</li>
</ul>