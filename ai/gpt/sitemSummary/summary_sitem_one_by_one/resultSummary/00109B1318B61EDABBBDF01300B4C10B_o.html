<h3>SI26: CT_JVA</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2967210">2967210 - S4TWL - Joint Venture Accounting on ACDOCA</a></strong></p>
<p><strong>Description:</strong></p>
<p>The classic Joint Venture Accounting (JVA) is based on separate database tables that contain copies of documents posted to the FI and CO ledgers. With the introduction of the new JVA_ON_ACDOCA business function in SAP S/4HANA, these tables are not used anymore. Instead, all JVA processing is based on the ACDOCA table ("Universal Journal Entries" - UJE). The new data model reduces the footprint of JV accounting data, guarantees automatic reconciliation between FI, JVA, and CO, and allows JVA to utilize new functionalities like up to 10 local currencies, fast retrieval of aggregated data, and new reporting tools. It is important to note that using the new data model from the start is critical as there is no standard solution to migrate from classic JVA to JVA on ACDOCA later.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Despite the change in the underlying data model, the general business logic for JVA remains the same. Major changes include the replacement of Venture Bank Switching transactions with Funding transactions, the elimination of the reconciliation transaction GJ90, and the requirement to use FI transactions instead of JVA variants of FI valuation transactions. Additionally, user experience changes in JVA reporting, replacing SAP GUI-based transactions with FIORI apps accessed via Fiori Launchpad.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Before starting a conversion project, stakeholders should read and understand the following notes:</p>
<ul>
<li>Note 2941622 - Migration to S4HANA with Joint Venture Accounting running on ACDOCA</li>
<li>Note 3044558 - Configuration/customizing help for JVA on ACDOCA</li>
<li>Note 2993369 - Restrictions for SAP Joint Venture Accounting on ACDOCA</li>
<li>Execute technical consistency checks of the JVA ledgers using reports RGUSLSEP and RGUREP11. For more information, see Note 764523 and Note 2941622.</li>
</ul>
<p><strong>Activities:</strong></p>
<ul>
<li>Business Decision (Mandatory, Before conversion project): Decide if you want to migrate to ACDOCA during conversion.</li>
<li>Data migration (Conditional, Before conversion project): If using classic GL with classic JVA, migrate to the JVA-integrated NewGL first.</li>
<li>Data correction (Conditional, Before or during conversion project): Perform technical consistency check of the JVA ledgers.</li>
<li>Data migration (Conditional, During conversion project): Migrate to JVA on ACDOCA as part of the system conversion process.</li>
</ul>