<h3>SI1: FIN_CO</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270404">2270404 - S4TWL - Technical Changes in Controlling</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The former tables COEP, COSP, and COSS are replaced by views of the same name, so-called compatibility views, that aggregate the data in the universal journal on the fly in accordance with the old table structures. CO standard transactions are adapted in a stepwise manner to access the new universal journal table (ACDOCA) directly instead of using compatibility views. The conversion process is ongoing. From a business point of view, all classic transactions are still running - based on compatibility views or using direct access to ACDOCA. The goal of the compatibility views is to provide a bridge to the new data model. Using these views may have a performance impact on existing reports and you are recommended to investigate whether the new Fiori reports meet your business needs. Customer coding still runs based on compatibility views.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Please check your customer-specific programs using CO tables. Customer coding should be adapted by replacing the access via compatibility views with direct access to ACDOCA for value types 04 and 11. Details are described in note 2185026.</p>
<p><strong>Reference Notes:</strong></p>
<ul>
<li><a href="https://me.sap.com/notes/2579584">2579584 - Recommendations for Usage of Reports in Financial Reporting in S/4 HANA</a></li>
<li><a href="https://me.sap.com/notes/2185026">2185026 - Compatibility views COSP, COSS, COEP, COVP: How do you optimize their use?</a></li>
<li><a href="https://me.sap.com/notes/1976487">1976487 - Information about adjusting customer-specific programs to the simplified data model in SAP Simple Finance</a></li>
<li><a href="https://me.sap.com/notes/2535903">2535903 - How to create your custom CDS Queries and leverage existing CDS Reporting Queries in S/4HANA Cloud and On Premise 1709 and subsequent OP releases</a></li>
<li><a href="https://me.sap.com/notes/2349297">2349297 - S4TWL - Reporting/Analytics in Controlling</a></li>
<li><a href="https://me.sap.com/notes/2575530">2575530 - Performance after Upgrade to S/4 HANA in Accounting</a></li>
</ul>