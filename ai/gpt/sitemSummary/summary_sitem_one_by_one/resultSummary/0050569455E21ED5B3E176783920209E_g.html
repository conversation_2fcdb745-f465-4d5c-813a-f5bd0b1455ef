<h3>SI2_FIN_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2742613">2742613 - Obsolete or replaced transaction codes and programs in Finance applications of S/4</a></strong></p>
<p><strong>Description:</strong></p>
<p>This SAP Note provides a list of transaction codes and programs in the Finance application areas, like Controlling, General Ledger, Asset Accounting, Accounts Payable and Receivable, Treasury and Risk Management, that have been removed or replaced with newer transactions, programs or WebDynpro applications in S/4HANA compared to EhP7 for SAP ERP 6.0.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The note highlights several business process impacts due to the simplification items. Some key impacts include:</p>
<ul>
<li>The classic General Ledger is transformed into a basic implementation of the New General Ledger, requiring users to adapt to new transaction codes.</li>
<li>Classic CO-OM planning transactions are not included in the SAP Easy Access Menu, and customers are encouraged to transition to SAP BPC optimized for SAP S/4HANA.</li>
<li>BW extractors for extracting FI-GL plan data are replaced with the SAP BPC optimized for SAP S/4HANA functionality.</li>
<li>Obsolete transactions and programs in Treasury and Risk Management, Asset Accounting are detailed in specific SAP Notes (2726778, 2270387, 2270388, 2257555).</li>
<li>Resource planning is not completely replaced by SAP BPC optimized for SAP S/4HANA, requiring customers to consider alternative solutions or modifications for classic resource planning functions.</li>
</ul>
<p><strong>Required and Recommended Actions:</strong></p>
<ul>
<li>Check if any of the listed transactions or programs are used by the customer using transaction ST03N.</li>
<li>Adjust custom code referencing obsolete transactions.</li>
<li>Provide user training on new transactions and processes.</li>
<li>Configure settings for replacement functionalities as needed.</li>
</ul>
<p><strong>Reference Notes:</strong>
<a href="https://launchpad.support.sap.com/#/notes/2557502">2557502 - Error message SFIN_FI 004: Transaction KPR8, KPR9, KPRA, KPRB, KBRC and KBRD cannot be called</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2257555">2257555 - S4TWL - Asset Accounting: Business Functions from FI-AA and FI-GL</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2187468">2187468 - Transaction OCCI: Error Message SFIN_FI 004 'Function is not available'</a><br>
<a href="https://launchpad.support.sap.com/#/notes/1736828">1736828 - Errors KU364 or KU363</a><br>
<a href="https://launchpad.support.sap.com/#/notes/1880317">1880317 - FAQ: Initial steps with new cost planning</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2035863">2035863 - SAP AC on HANA: Data extractors do not indicate new planning functions</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2726778">2726778 - S4TWL - Obsolete Transaction Codes and Programs in Treasury and Risk Management (TRM)</a><br>
<a href="https://launchpad.support.sap.com/#/notes/1719702">1719702 - FAQ: New user interface for project cost planning</a><br>
<a href="https://launchpad.support.sap.com/#/notes/1976487">1976487 - Information about adjusting customer-specific programs to the simplified data model in SAP Simple Finance</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2084604">2084604 - Error message SFIN_FI 005: Master data transactions in CO cannot be called</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2349294">2349294 - S4TWL - Reference and Simulation Costing</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2270408">2270408 - S4TWL - Activity-Based Costing</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2270419">2270419 - S4TWL - Account/Cost Element</a><br>
<a href="https://launchpad.support.sap.com/#/notes/1777947">1777947 - FAQ: New user interfaces for cost center planning</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2119188">2119188 - Release Scope Information: SAP Simple Finance, on-premise edition 1503</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2270407">2270407 - S4TWL - Profit and Loss Planning,  profit center planning, cost center planning, order planning, and project planning</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2029012">2029012 - SAP Simple Finance:  Obsolete transaction codes and programs in Financials localizations</a><br>
<a href="https://launchpad.support.sap.com/#/notes/1836149">1836149 - Business functions FIN_CO_CCMGMT, FIN_CO_CCPLAN, and FIN_CO_ORPLAN</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2270388">2270388 - S4TWL - Asset Accounting: Parallel valuation and journal entry</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2415191">2415191 - FAGL_FCV is not in menu</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2313304">2313304 - OKB2, OKB3, OKC3, OKC5 are obsolete</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2270411">2270411 - S4TWL - General Cost Objects and Cost Object Hierarchies</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2270387">2270387 - S4TWL - Asset Accounting: Changes to Data Structure</a><br>
<a href="https://launchpad.support.sap.com/#/notes/2203760">2203760 - Profit center document entry terminates with error message 004 (SFIN_FI)</a></p>
