<h3>SI2: MasterData_BP</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2265093">2265093 - S4TWL - Business Partner Approach</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, any of the listed on-premise editions -> 1511, 1610, 1709,1809, 1909, 2020, 2021, 2022. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Only SAP Business Suite customer with C/V integration in place can move to SAP S/4HANA, on-premise (Conversion approach). It is recommended but not mandatory that BuPa ID and Customer-ID / Vendor ID are the same. The user interface for SAP S/4HANA is transaction BP. There is no specific user interface for customer/vendor like known from SAP Business Suite (the specific transactions like XD01, XD02, XD03 or VD01, VD02, VD03/XK01, XK02, XK03 or MK01, MK02, <PERSON>K03 etc. are not available in SAP S/4HANA on-premise)</p>
<p>Points to Note in SAP S/4HANA:</p>
<ul>
<li><strong>Consumer</strong> - While creating a consumer, ensure that you use the BP Grouping that is associated to the customer account group (Consumer) in TBD001 (Assignment of Account Group BP Grouping). Note that customer itself is a contact person, hence no further relationships can be maintained.</li>
<li><strong>One-time Customer/Supplier</strong> - While creating a one-time customer or supplier, ensure that you use the BP Grouping that is associated to the customer/supplier account group (One time customer/supplier) in TBD001 and TBC001 (Assignment of Account Group BP Grouping).</li>
<li>IDocs <strong>DEBMS/CREMAS</strong>: Not recommended for data integration between S/4HANA systems. Use Business Partner web services (SOAP) via API Business Hub for SAP S/4HANA integration.</li>
<li><strong>Role Validity</strong> - Maintain the validity of roles. A user can close the (prospect) role and move to another after the mentioned validity period.</li>
<li>Transactions redirected to transaction BP: FD01, FD02, FD03, FK01, FK02, FK03, MK01, MK02, MK03, VD01, VD02, VD03, XD01, XD02, XD03, XK01, XK02, XK03.</li>
<li>Transactions that are obsolete: FD06, FK06, MK06, MK12, MK18, MK19, VD06, XD06, V+21, V+22, V+23.</li>
</ul>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Before you begin the BP conversion from an SAP ERP system to an SAP S/4 HANA system, you have to answer the questions</p>
<ul>
<li>Whether the Business Partner ID and Customer-ID /Vendor ID should be the same in the S/4 HANA System?</li>
<li>The keys for a smooth synchronization of the ERP customer/vendor into the S/4 system with the business partner as the leading object are beside Business Partner Know-How also consistent customer/vendor data and valid and consistent custom/vendor and Business Partner customizing entries. For this reason, the customer/vendor data has to be cleaned up before it can be converted into the S/4 Business Partner.</li>
</ul>
<p>Prepare: Pre-Checks and clean-up customer/vendor data in the ERP System</p>
<ul>
<li>Implement SAP S/4HANA Conversion Pre-Checks according to the SAP S/4HANA Conversion guide chapter Pre-Checks.</li>
<li>Activate Business Function CA_BP_SOA.<br/>In case that the Business Function CA_BP_SOA not yet in the system exist, create a new Business Function in the customer namespace with the switches VENDOR_SFWS_SC1 and VENDOR_SFWS_SC2.</li>
<li>Check CVI customizing and trigger necessary changes e.g. missing BP Role Category, Define Number Assignments according to the S/4 Conversion guide chapter Introduce Business Partner Approach (Customer Vendor Integration).</li>
<li>Check and maintain BP customizing e.g. missing tax types.</li>
<li>Check master data consistency using CVI_MIGRATION_PRECHK and maintain consistency.</li>
<li>Check and clean-up customer/vendor data e.g. missing @-sign in the e-mail address.</li>
</ul>
<p>Synchronization</p>
<ul>
<li>Synchronization (Data load) is done via <em>Synchronization Cockpit</em> according to the attached BP Conversion Document.pdf &gt; Chapter 5. <em>Convert Customer/Supplier Data into Business Partner</em>.</li>
<li>In case of an error during the synchronization process due to data/customizing mismatch you can find the errors using Logs button. You can also view this via MDS_LOAD_COCKPIT&gt; Monitor tab &gt; Call PPO button in Synchronization Cockpit.</li>
</ul>
<p>Conversion Process</p>
<ul>
<li>Conversion Process must be triggered according to the BP Conversion Document.pdf attached to this SAP Note.</li>
</ul>
<p>Business Partner Post Processing</p>
<ul>
<li>Legitimate BP records processing post-conversion. Activate post-processing for the direction Business Partner to Customer /Vendor.</li>
</ul>
<p><strong>Reference Notes:</strong></p>
<p><a href="https://launchpad.support.sap.com/#/notes/2344034">2344034 - SAP S/4HANA Automation for Master Data Migration</a>, <a href="https://launchpad.support.sap.com/#/notes/2210486">2210486 - Obsolete : S/4HANA Business Partner Conversion Reports</a>, <a href="https://launchpad.support.sap.com/#/notes/3011764">3011764 - SEPA Mandate for Vendors</a>, <a href="https://launchpad.support.sap.com/#/notes/2216176">2216176 - Obsolete :Precheck report from business partner</a>, <a href="https://launchpad.support.sap.com/#/notes/1623677">1623677 - BP_CVI: Check report for checking CVI Customizing</a>, <a href="https://launchpad.support.sap.com/#/notes/954816">954816 - BP_CVI: Transactions for creating/linking BPs</a></p>