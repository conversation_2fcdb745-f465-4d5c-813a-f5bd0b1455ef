<h3>SI2_FIN_CM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2870766">2870766 - S4TWL - Cash Management - BAM</a></strong></p>
<p><strong>Description:</strong></p>
<p>Once customers have installed SAP S/4HANA, SAP S/4HANA Finance for cash management is the only compatible cash management product to be used. The classic Cash and Liquidity Management (Cash Management and Liquidity Planner) is not officially available in SAP S/4HANA. An SAP Business Suite customer using classic Cash and Liquidity Management needs to activate the new SAP S/4HANA Finance for cash management after converting to SAP S/4HANA.</p>
<p>As part of cash management, BAM (Bank Account Management) offers customers the capability in maintaining banks and bank accounts, among other relevant features.</p>
<p><strong>Business Process Impact:</strong></p>
<p>With BAM, customers are able to:
1. Manage their banks and bank accounts (including house banks and house bank accounts)
2. Manage their bank account groups and bank hierarchies
3. Control bank account maintenance process via dual control mode or workflow mode
4. Analyze their bank fee data</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Prior to SAP S/4HANA 1809, the old transactions of maintaining house bank accounts are not available. Instead, you manage your house bank accounts in the new bank account master data, using either the basic cash management capability or the full scope of Bank Account Management (with SAP S/4HANA Finance for cash management). As a result, before you go live with SAP S/4HANA, you must migrate house bank accounts if there are any. For more information about the migration activity, in the <a href="https://help.sap.com/viewer/ac319d8fa4ea4624b40a58d23e3c4627/LATEST/en-US/590d65568a527b5ce10000000a44147b.html" target="_blank">Configuration Guide</a>, select your software version and then search for "Migration of House Bank Accounts".</p>
<p>All the apps must be accessed from the Fiori Launchpad.</p>
<p>Customers who have been using the full scope of cash management and are converting to SAP S/4HANA 1610 from Simple Finance 1503, S/4HANA Finance 1605, and S/4HANA OP 1511 must configure the customizing activity <strong>Define Basic Settings </strong>under "Financial Supply Chain Management" -> "Cash and Liquidity Management" -> "General Settings".</p>
<p>Data migration: Migrate House Banks into Bank Account Management.</p>
<p>Customizing / Configuration: Create number ranges, account types, import of bank statements.</p>
<p>Custom Code Adaption: Adopt custom code to fit to the new logic described in note 2870766.</p>
<p>User Training: Inform users about changes to user interface and business logics.</p>
<p>Fiori Implementation: Implement Fiori apps required to manage banks and bank accounts.</p>