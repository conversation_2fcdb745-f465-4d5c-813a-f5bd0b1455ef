<h3>SI06: ACM_Tmp_Lots_Draft_Contracts</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/3086524">3086524 - S4TWL - ACM Temporary Lots for Draft Contracts</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p>A system upgrade/conversion is about to be done and the source system is one of the following releases:</p>
<ul>
<li>ACM 4.0</li>
<li>ACM on SAP S/4HANA 1709 - All releases</li>
<li>ACM on SAP S/4HANA 1809 - FPS00</li>
<li>ACM on SAP S/4HANA 1809 - FPS01</li>
<li>ACM on SAP S/4HANA 1809 - FPS02</li>
<li>ACM on SAP S/4HANA 1909 - FPS00</li>
<li>ACM on SAP S/4HANA 1909 - FPS01</li>
<li>ACM on SAP S/4HANA 1909 - FPS02</li>
<li>ACM on SAP S/4HANA 2020 - FPS00</li>
</ul>
<p>The target system is 'ACM on SAP S/4HANA 2020 FPS01' or a higher release.</p>
<p>Post-conversion/upgrade, dumps occur because of the existence of multiple active BAdI implementations of the BAdI definition BADI_CPE_FE_RED_DATE_ROUTINE for the same filter value '9999999'. This prevents execution of various ACM transactions and prevents successful execution of the SDM /ACCGO/CL_SDM_ACM_V_SDMNPEL.</p>
<p><strong>Business Process Impact:</strong></p>
<p>This step is needed to prevent the occurrence of dumps because of the existence of multiple BAdI implementations of the BAdI definition BADI_CPE_FE_REF_DATE_ROUTINE for the same filter value '9999999'.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
<ul>
<li>Run the steps mentioned in SAP Note - 3094714 - S4TC ACM Pre-Checks for Data Migration of /ACCGO/T_NPELOTS table in the source system</li>
<li>The manual instruction in this note must only be implemented after all the productive activities in the system have been halted in preparation for the conversion/upgrade.</li>
<li>The transport-request in which the implementation of this note is captured must be released to all follow-on systems to prevent occurrence of this issue in those systems.</li>
<li>Please implement the manual correction instructions attached to this note.</li>
</ul>
</p>