<h3>SI5_IS_DIMP_M</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270412">2270412 - S4TWL - Process Batch</a></strong></p>
<p><strong>Description:</strong></p>
<p>The Mill specific Process Batch functionality is not available in SAP S/4HANA as the successor core solution WIP Batch (Work in Process Batch) is already available. The process batch is a batch that you create for the production order so that you can document the properties of a material during the production process.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The WIP Batch provides similar functionality of process batch with additional features, but has the limitation in SAP S/4HANA, on-premise edition 1511, that the Mill specific order combination function is not available. As of SAP S/4HANA 1610, WIP Batch is enhanced to support order combination for both production and process orders.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>It is required to use/convert to WIP Batch functionality in SAP S/4HANA. Please refer to SAP Note 2326769 for migration from process batch to WIP batch. Please refer to SAP Note 2346054 for information on enhancement to support order combination with WIP Batch.</p>
<p><strong>NN Generated Actions:</strong></p>
<p>Use WIP Batch in S/4HANA replacing process batch. The WIP Batch provides similar functionality of process batch with additional features, but has the limitation in SAP S/4HANA, on-premise edition 1511, that the Mill specific order combination function is not available. As of SAP S/4HANA 1610, WIP Batch is enhanced to support order combination for both production and process orders.</p>
<p><strong>Process Design / Blueprint</strong> - <em>Condition:</em> Mandatory</p>
<p>Use WIP Batch in S/4HANA replacing process batch. The WIP Batch provides similar functionality of process batch with additional features, but has the limitation in SAP S/4HANA, on-premise edition 1511, that the Mill specific order combination function is not available. As of SAP S/4HANA 1610, WIP Batch is enhanced to support order combination for both production and process orders.</p>
<p><strong>Customizing / Configuration</strong> - <em>Condition:</em> Mandatory</p>
<p>Use WIP Batch in S/4HANA replacing process batch. The WIP Batch provides similar functionality of process batch with additional features, but has the limitation in SAP S/4HANA, on-premise edition 1511, that the Mill specific order combination function is not available. As of SAP S/4HANA 1610, WIP Batch is enhanced to support order combination for both production and process orders.</p>
<p><strong>Data migration</strong> - <em>Condition:</em> Mandatory</p>
<p>SAP Note 2326769 / The Process Batch data and confirmation data using Process Batch for the existing customers can be migrated to WIP Batch using the report program R_CONVERT_WIPBATCH. To reduce the downtime, customers can run this report before starting the system conversion. However, the pre-requisite for running this report before system conversion is to implement the SAP Note 2336984 for existing EhP7 and EhP8 for SAP ERP 6.0 customers or SAP Note 2336975 for SAP S/4HANA 1511 customers. After the migration, the process batches will be treated as WIP batches by the system.</p>
<p><strong>Custom Code Adaption</strong> - <em>Condition:</em> Mandatory</p>
<p><strong>User Training</strong> - <em>Condition:</em> Optional</p>
<p>The item is also relevant to you if you have been using Goods Movements via Mill GI/GR detail screens in transaction CO11N. (see SAP Help for Mill Products -&gt; Enhancements in Production Planning and Control (PP) -&gt; Goods Movements in the Confirmation Transaction).</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2326769">2326769 - Migration from Process Batches to WIP Batches</a>, <a href="https://me.sap.com/notes/2346054">2346054 - Confirmation of Combined Orders using WIP Batches</a></p>