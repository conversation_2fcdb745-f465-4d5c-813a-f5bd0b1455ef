<h3>SI08: Insurance_FS-RI</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2972152">2972152 - S4TWL - FS-RI - Ensuring the functionality of the Extension Service</a></strong></p>
<p><strong>Description:</strong></p>
<p>Extension Service objects are no longer available in SAP Reinsurance Management for SAP S/4HANA and in SAP S/4HANA Insurance for reinsurance management. The impact on business processes depends on the use of the obsolete objects.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The impact on business processes depends on the use of the obsolete objects.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Check whether you used the obsolete objects. If you did, you must perform the following steps:</p>
<p>During the SPAU and SPDD adjustment, the system may issue warnings stating that an adjustment is required for modified objects. This affects Extension Service structures with the name /MSG/BAPI_ED_* or /MSG/BAPI_EC_*. You can ignore these warnings.<br/>You can also reset the modifications. However, this may result in errors and should therefore be avoided.<br/>When the Extension Service is regenerated after the upgrade, these BAPI structures are also regenerated. As a result, the Extension Service is consistent and functioning again.</p>
<p>Create customer variant</p>
<ol>
<li>Call transaction /MSG/7FW_START.</li>
<li>Double-click "Workplace" and open the application "10000".</li>
<li>Right-click "10000" and choose "Customer Variant".</li>
<li>
<div>Populate the following fields:</div>
</li>
<ul>
<li>
<div>Customer Variant</div>
</li>
<li>License Key</li>
<li>Storage Package of Classes</li>
<li>Class Prefix</li>
</ul>
<li>
<div>Choose "New".</div>
</li>
<li>
<div>Save the change and choose a suitable request to do so.</div>
</li>
</ol>
<p>Choose "Generate".</p>

<p>Call transaction /MSG/7FW_START again and, before choosing "Generate", ensure that all requests that contain the relevant objects have been released.</p>
<ol>
<li>Call transaction /MSG/2_ES_EXT_SRVC.</li>
<li>Under "Applications", select FS-RI-RML-SRA-PM.</li>
<li>Under "Extendable Tables", select the table /MSG/VDOCLAIM.</li>
<li>Under "Enhancements", select /MSG/ED_OCLM01 and choose "Activate" (Ctrl+F3).</li>
<li>In the dialog box that is displayed, create a request and save it.</li>
<li>Confirm any other dialog boxes that may be displayed. If the system displays a dialog box for the IDocs, choose "No".</li>
<li>If no errors occur, navigate back to transaction /MSG/2_ES_EXT_SRVC and repeat the steps 3 to 6 for the following tables:</li>
<ul>
<li>/MSG/VDOCOND</li>
<li>/MSG/VDOCONDBASE</li>
<li>/MSG/VDOINSPER</li>
<li>/MSG/VDOLOAD</li>
<li>/MSG/VDOPCSHR</li>
<li>/MSG/VDOPOL</li>
<li>/MSG/VDOPOLCOMP</li>
</ul>
<li>Navigate back to transaction /MSG/2_ES_EXT_SRVC and select FS-RI-RML-SRA under "Applications".</li>
<li>Repeat the steps 3 to 6 for the following tables:</li>
<ul>
<li>/MSG/VDOUWASS</li>
<li>/MSG/VDOUWPOLDAT</li>
</ul>
</ol>
<p>Note that two enhancements must be generated for /MSG/VDOUWASS.</p>
<p>If errors occur during generation, call the transaction again and generate the enhancement again.</p>
The system may issue the following error messages:
<ul>
<li>"Unable to load business object element ENTITY"</li>
<li>"Unable to load business object element PERSISTENCE"</li>
<li>"Unable to load business object element CHMODEL"</li>
<li>"Unable to load business object element CTRLMODEL"</li>
</ul>
<p>Del-tool for problems with COND</p>
<ol>
<li>Add the following entry to the table /MSG/0CHECK:</li>
<ul>
<li>KORVAS-SAP</li>
</ul>
<li>Call transaction /MSG/2_ES_DEL_TOOL.</li>
<li>Select the entry RCOND01.</li>
<li>Choose "Generate Standard".</li>
</ol>