<h3>SI27: CT_SECURE_BY_DEFAULT</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2926224">2926224 - Collection Note: New security settings for SAP S/4HANA and SAP BW/4HANA using SL Toolset and SUM</a></strong></p>
<p><strong>Description:</strong></p>
<p>This is the central entry point for all information around new secure-by-default settings for SAP S/4HANA. You want to know more details about the new secure-by-default settings for SAP S/4HANA systems. During SAP S/4HANA provisioning, you were asked to review the profile parameters set by SL Toolset (e.g. SWPM, SUM).</p>
<p><strong>Business Process Impact:</strong></p>
<p>You are using the latest SL Toolset or SUM. This will introduce the latest recommended security settings for SAP S/4HANA systems and SAP BW/4HANA systems. From SAP S/4HANA 1909 onwards and SAP BW/4HANA 2021 onwards, new installations and system conversions will automatically receive the recommended security settings. Opting out of these settings is possible but not recommended by SAP. During upgrades, security settings are not adjusted automatically; however, it is recommended to apply the updated security settings post-upgrade.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p><strong>Technical System Configuration - Mandatory</strong>: Decide if you want to take over the new security settings during conversion or if you want to opt out (not recommended).</p>
<p><strong>User Training - Conditional</strong>: In case you accept the new security settings to be applied during conversion, educate your administrators on the changes.</p>
<p><strong>Customizing / Configuration - Conditional</strong>: In case you accept the new security settings to be applied during conversion, check if you need to extend roles with additional authorizations for calling specific function modules (only from SAP S/4HANA 2020 onwards) and for calling WebDynpro applications (only from SAP S/4HANA 2021 onwards).</p>
<p><strong>Business Operations - Conditional</strong>: After the conversion or upgrade, check if you need to configure log retention for the different logs (e.g., SAP S/4HANA Security Audit Log, SAP S/4HANA Table Logging, SAP HANA Audit Log) to control the size of the different logs.</p>
<p><strong>Reference Notes:</strong></p>
<ul>
    <li><a href="https://launchpad.support.sap.com/#/notes/2713544">2713544 - New security settings during conversion to SAP S/4HANA 1909 (and later) and SAP BW/4HANA 2021 (and later) with SUM 2.0</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2253549">2253549 - The SAP Security Baseline Template</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2714839">2714839 - New security settings for SAP S/4HANA 1909 (and later) and SAP BW/4HANA 2021 installations and system copies using SL Toolset 1.0</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2975959">2975959 - Security settings during upgrade to SAP S/4HANA 2020 (and later) with SUM 2.0 SP09 (and later) or SAP BW/4HANA 2021 (and later) with SUM 2.0 SP10</a></li>
</ul>