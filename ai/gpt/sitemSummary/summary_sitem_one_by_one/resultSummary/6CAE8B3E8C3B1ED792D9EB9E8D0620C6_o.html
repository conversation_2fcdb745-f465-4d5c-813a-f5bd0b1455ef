<h3>BW203: Unit of Measure Conversions</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2443281">2443281 - BW4SL - Currency Translations, Quantity Conversions, Key Date Derivations</a></strong></p>
<p><strong>Description:</strong></p>
<p>Currency translation types, unit conversion types, or key date derivations that are using a field/InfoObject from an InfoSet in their definition are not available in SAP BW/4HANA, since InfoSets are not available. Additionally, unit conversion types that are based on a classic DataStore object are not available in SAP BW/4HANA.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The unavailability of currency translation types and unit conversion types that rely on InfoSets or classic DataStore objects in SAP BW/4HANA may require manual adaptation or deletion of these objects, affecting various business processes reliant on these conversions.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are supported or can be converted to SAP BW/4HANA. The currency translation types, unit conversion types, and key date derivations based on InfoSets need to be adapted manually or deleted. Unit conversion types based on classic DataStore objects can be converted to use DataStore objects (advanced) using the Transfer Tools.</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2421930">2421930 - Simplification List for SAP BW/4HANA</a>, <a href="https://me.sap.com/notes/2347382">2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)</a>, <a href="https://me.sap.com/notes/2383530">2383530 - Conversion from SAP BW to SAP BW/4HANA</a></p>