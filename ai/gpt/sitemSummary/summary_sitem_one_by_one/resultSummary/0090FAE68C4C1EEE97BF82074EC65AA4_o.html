<h3>SI24: PROC_MM_PUR_CI_INCLUDE_CONFLICTS_PO</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/3092824">3092824 - Upgrade to S/4HANA 2022 and higher: New Includes in Structures 'EKKO_INCL_EEW_PS' and 'EKPO_INCL_EEW_PS'</a></strong></p>
<p><strong>Description:</strong></p>
<p>In S/4HANA 2022, structures 'EKKO_INCL_EEW_PS' and 'EKPO_INCL_EEW_PS' now contain Includes CI_EKKODB (on header level) and CI_EKPODB (on item level), respectively. This may lead to an activation error in phase ACT_UPG for tables/structures where both the structures are included. The error message in ACT_UPG is DT963 "Field &lt;<em>your customer field</em>&gt; in table &lt;<em>your table where both structures are included</em>&gt; is specified twice". Another possible error is "CI_EKPODB" is not included in table "EKPO" or "CI_EKKODB" is not included in table "EKKO".</p>
<p><strong>Business Process Impact:</strong></p>
<p>This change will allow On Premise customers to directly use custom fields in CI Includes in Fiori applications without any data loss. These fields are then available for both Fiori UI as well as GUI screens (VH).</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>To avoid the error "Field is specified twice", follow these steps:

1. In development systems, during phase ACT_UPG when prompted for SPDD, delete the CI_EKKODB / CI_EKPODB Includes or the field(s) of CI_EKKODB / CI_EKPODB from the table/Include where 'EKKO_INCL_EEW_PS' or 'EKPO_INCL_EEW_PS are also included. Also delete the CI_EKKODB / CI_EKPODB or the field(s) of CI_EKKODB / CI_EKPODB from the Extension views in the CDS stack.
   - Make sure to remove only fields that are contained in CI_EKKODB / CI_EKPODB from tables/Includes by these steps to avoid any data loss.
   - If you remove fields from an appended structure or from table, just save your changes. DO NOT PERFORM 2-step activation at this time, this could lead to data loss. Mass activation will be performed automatically later in phase ACT_UPG.
   - If you delete the appended structure, there is a warning "Deleting the appended structure results in reactivation of and adjustment of the table &lt;<em>your table</em>&gt; on the database". In this situation this warning can be ignored as an adjustment would only happen later during mass activation when &lt;<em>your table</em>&gt; will contain CI_EKKODB / CI_EKPODB directly, so the contents of CI_EKKODB / CI_EKPODB fields will not be lost.

2. Continue with ACT_UPG. The deletion of the appended structure or the change to table/structures will be activated together with the standard change that adds CI_EKKODB / CI_EKPODB to your table/Include, there should be no activation error.
   - In case you didn't perform these steps at SPDD you can still perform them after ACT_UPG was stopped with error DT963 "Field &lt;ZZ-field&gt; in table &lt;your table&gt; is specified twice". In this case you might get the error message DT002 "Table &lt;<em>your appended structure to table</em>&gt; does not exist" when repeating the activation step ACT_UPG after deleting the appended structure. If there is no activation error for other tables or structures, especially no error for table, this DT002 error for the deleted appended structure can be ignored.

3. These changes will be recorded in a transport request. It is advised to do these adjustments and activation in one step in a single transport request. In follow-up systems, include the transport containing the deleted appended structure or the change to table in the upgrade.

In case the CI_EKKODB / CI_EKPODB includes get deleted accidentally from 'EKKO_INCL_EEW_PS' or 'EKPO_INCL_EEW_PS' inside of EKKO / EKPO, there are two options:
- The include(s) can be retrieved from the version history. Individual objects have their versions management that should be as follows:
  1. EKKO/EKPO table has version 1 (needs to be reverted to remove CI)
  2. Include EEW also has version 1 (with the CI include)
  3. CI-include (which was deleted, can be retrieved via its version management).

- The upgrade can be repeated or

- In the shadow system of the upgrade, customer owned objects but also SAP objects may be modified in the so-called SPDD-phase: before activation! The goal is that customers can transfer or adjust his/her modifications to the new release. In this phase, it is possible to enter the CI_ includes into their respective *EEW* includes inside of EKKO and EKPO. It is important to only save but not activate until all modifications are done. Here, you can check whether the CI_EKKODB / CI_EKPODB includes that should not be directly inside of EKKO and EKPO table is really not there, otherwise delete it but save only, no activation. Then, in the end, when all is done and checked: mass activate.

</p>
<p><strong>Reference Notes:</strong> 
<a href="https://launchpad.support.sap.com/#/notes/3326565">3326565 - Upgrade issue in MMPUR_ANA_EKET table from OP2022: CI Include merged into EEW for EKKO & EKPO</a>,
<a href="https://launchpad.support.sap.com/#/notes/3334975">3334975 - Upgrade to S/4 HANA 2022: New Includes in Structures 'EKKO_INCL_EEW_X' and 'EKPO_INCL_EEW_X'</a>,
<a href="https://launchpad.support.sap.com/#/notes/3319787">3319787 - Upgrade error on structure PRITEMFORGR due to extension includes</a>,
<a href="https://launchpad.support.sap.com/#/notes/3338301">3338301 - Upgrade issue for CI_EKKODBX and CI_EKPODBX: duplicate fields found -> tool</a>,
<a href="https://launchpad.support.sap.com/#/notes/3345237">3345237 - Upgrade to SAP S/4HANA 2022: Error when changing CI_EKPODB includes. This note tackles the "package MLHELPDESK does not exist" and "table MLREPEKPO does not exist" errors.</a>,
<a href="https://launchpad.support.sap.com/#/notes/3282761">3282761 - Object Activation Errors during Upgrade: Field is specified twice</a>
</p>