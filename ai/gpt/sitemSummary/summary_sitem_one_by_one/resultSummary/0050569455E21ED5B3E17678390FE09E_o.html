<h3>SI1: Logistics_ATP</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267745">2267745 - S4TWL - New advanced ATP in SAP S/4HANA, on-premise edition – Table VBBS</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>
<p>In S/4HANA it is possible to aggregate data in real time, so the aggregate-table for sales order requirements VBBS is not necessary any more.</p>
<p>Database table VBBS contained pre-aggregated sales requirements. The requested (OMENG) and confirmed (VMENG) quantities were aggregated on a daily basis. With the new HANA ATP, we do not need pre-aggregation anymore, and therefore this table is obsolete.</p>
<p>Instead of the VBBS we use the VBBE, where each ATP-relevant requirement is explicitly stored. The old ERP-ATP check code is also redirected to use VBBE.</p>
<p>No impact on business processes is expected. The customizing where the VBBS could previously be activated in the Business Suite has been deactivated in SAP S/4HANA, on-premise edition 1511. New entries will automatically "choose" to use VBBE instead of VBBS.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No impact on business processes is expected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Depending on the target release of S/4HANA you may have to apply following actions:</p>
<p><strong>S/4HANA 1511 and S/4HANA 1610</strong></p>
<p>There are two optional checks to be carried out before the upgrade to verify if the VBBS is used:</p>
<ol>
<li>Start transaction "SE16" and display the entries in table "VBBS". If there are any entries, the XPRA has to run. If there are no entries, the second step has to be executed.</li>
<li>Start transaction "OVZ2" and check the entries for the fields "SUMAU" and "SUMLF". If there is any entry other than "A: Single records", the XPRA needs to be executed.</li>
</ol>
<p>If you are still using table VBBS, please read the related note 2209696 how to convert your data after upgrade.</p>
<p>If you have custom code calling table VBBS, create a db view on table VBBE, and use that view instead of the table VBBS.</p>
<p><strong>S/4HANA 1709 and newer</strong></p>
<p>The necessary actions are automated and executed in <em>SUM</em> phase using identifying name <em>ATP_XPRA_SUITE_TO_S4_V2</em>. Manual actions as described for release S/4HANA 1511 and S/4HANA 1610 are not needed.</p>
<p><strong>Data migration</strong> - During conversion project - Mandatory - Manual step only for SAP S/4HANA 1511 and SAP S/4HANA 1610, For SAP S/4HANA 1709 and newer, the necessary actions are automated and executed in SUM phase using identifying name ATP_XPRA_SUITE_TO_S4_V2. SAP Note: 2209696</p>
<p><strong>Custom Code Adaption</strong> - During conversion project - Mandatory - If you have custom code calling table VBBS, create a db view on table VBBE, and use that view instead of the table VBBS. SAP Note: 2209696</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2209696">2209696 - ATP Transition to S/4</a></p>