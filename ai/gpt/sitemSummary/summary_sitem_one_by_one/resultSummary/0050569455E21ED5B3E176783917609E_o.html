<h3>SI21: Logistics_PP</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2268131">2268131 - S4TWL - Electronic Records</a></strong></p>
<p><strong>Description:</strong></p>
<p>Electronic Records (LO-ELR) is not considered as future technology and therefore part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and its expiry date (end of usage right) and links to further information please refer to SAP note <a href="https://me.sap.com/notes/2269324" target="_blank">2269324 Compatibility Scope Matrix for SAP S/4HANA</a>. You can find the respective scope item under item ID 447 in the attached document with Compatibility Scope matrix details.</p>
<p>Electronic Records (LO-ELR) provide tools for evaluation of the logs that can be written for database table entry changes. These logs are stored in database table DBTABLOG. It is also possible to display change documents and logs of long text changes. Electronic Records are part of the SAP functionality that supports legal compliance in the regulated environment (e.g. production of active ingredients, pharmaceutical products).</p>
<p>Audit Trail (BC-SRV-ASF-AT) as part of SAP Information Lifecycle Managements (ILM) in software component SAP_BASIS is the functional successor of Electronic Records (LO-ELR). In addition to the functionality of the Electronic Records, Audit Trail provides the following functions:</p>
<ul>
<li>Access to archived change documents and database table records</li>
<li>Compliance with the Information Lifecycle management (ILM) standard of SAP</li>
<li>Improved functionality to define custom selections on database table logs and change documents</li>
</ul>
<p>Audit Trail provides migration report AUT_MIGRATE_ENHAT_DB_DATA to migrate the configuration of Electronic Records to the successor feature Audit Trail. For details please see note <a href="https://me.sap.com/notes/1425152" target="_blank">1425152</a>.</p>
<p>Electronic Records development objects which are part of Compatibility Scope are located in package ENHAT.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No changes to business processes; no changes to entries of database table DBTABLOG</p>
<p>Transactions</p>
<ul>
<li>AUT01 Configuration of Logging</li>
<li>AUT02 Configuration of Navigation Help</li>
<li>AUT03 Display Configuration</li>
<li>AUT04 Configuration of Long Text Logs</li>
<li>AUT05 Deletion of Long Texts</li>
<li>AUT10 Evaluation of Audit Trail</li>
</ul>
<p>are still available in SAP S/4HANA as Compatibility Scope.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Transfer the configuration/customizing settings using SAP report AUT_MIGRATE_ENHAT_DB_DATA in SAP ERP before transforming/migrating to SAP S/4HANA.</p>
<p>After migration from Electronic Records to new Audit Trail solution check your custom code for usage of Electronic Records development objects, which are belonging to compatibility scope and clean-up your custom code. The below (incomplete) list is an entry point for the analysis of your custom code. Make sure that with end of support of the compatibility scope no data is saved (insert/update) in Electronic Records database tables with any custom code.</p>
<p>Transactions (no call or clone in custom code)</p>
<ul>
<li>AUT01 Configuration of Logging</li>
<li>AUT02 Configuration of Navigation Help</li>
<li>AUT03 Display Configuration</li>
<li>AUT04 Configuration of Long Text Logs</li>
<li>AUT05 Deletion of Long Texts</li>
<li>AUT10 Evaluation of Audit Trail</li>
</ul>
<p>Database tables (no save of data through custom code)</p>
<ul>
<li>AUT_ARCH_HIST <em>History for Archiving of Long Text Logs</em></li>
<li>AUT_STXL <em>SAPscript Text File Lines: Dummy Table for Audit Trails</em></li>
<li>AUT_STXH_ARCH <em>Buffer and Conversion Table for Archiving</em></li>
<li>AUT_STXL_ARCH <em>Buffer and Conversion Table for Archiving</em></li>
<li>AUTDELTTXID <em>Long Text Objects Not To Be Logged on DBTABLOG</em></li>
<li>AUTDTELLOG <em>Customizing for Change Document Creation</em></li>
<li>AUTDTELLOGT <em>Text Table for Customizing of Change Document Creation</em></li>
<li>AUTTABLOG <em>Customizing for Table Logging</em></li>
<li>AUTTABLOGT <em>Text Table for Customizing of Table Logging</em></li>
<li>AUTTRANSLOG <em>Customizing, Assignment of Transaction/Objects via AUT02</em></li>
</ul>
<p>Authorization object</p>
<ul>
<li>C_AUT_TCD</li>
</ul>
<p>BAdI's</p>
<ul>
<li>BADI_AUT_ARCHIVE <em>Intern: Badi for Archive Evaluation of Audit Trail - AUT10</em></li>
<li>BADI_AUT_CONV <em>AUT10: BAdI for Value and Key Conversion</em></li>
</ul>
<p>Programs (no call or clone in custom code)</p>
<ul>
<li>AUT_ARCH01 <em>Archiving of Long Text Logs: Write Program</em></li>
<li>AUT_ARCH02 <em>Archiving of Long Text Logs: Delete Program</em></li>
<li>AUT_ARCH03 <em>Archiving of Long Text Logs: Reload Program</em></li>
<li>AUT_ARCHV <em>Archiving of Long Text Logs: Preprocessing Program</em></li>
<li>AUT_ARCH_UNDO <em>Archiving of Long Text Logs: Deletion of Last Preprocessing</em></li>
<li>AUT_CUST01 <em>Customizing tool for AUDIT TRAILs</em></li>
<li>AUT_CUST_DEL04</li>
<li>AUT_DEL <em>Deletion of Long Text Logs</em></li>
<li>AUT_DEL05 <em>Deleting of long texts</em></li>
<li>AUT_REP10 <em>Reporting Tool for AUDIT TRAILs</em></li>
</ul>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/1425152">1425152 - Replacing Electronic Records w/ Audit Trail (Basis function)</a>, <a href="https://launchpad.support.sap.com/#/notes/2269324">2269324 - Compatibility Scope Matrix for SAP S/4HANA</a></p>