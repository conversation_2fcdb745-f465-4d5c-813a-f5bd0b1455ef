<h3>BW202: Currency Translations</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2443281">2443281 - BW4SL - Currency Translations, Quantity Conversions, Key Date Derivations</a></strong></p>
<p><strong>Description:</strong></p>
<p>Currency translation types, unit conversion types, or key date derivations that are using a field/InfoObject from an InfoSet in their definition (name contains three underscores) are not available in SAP BW/4HANA (since InfoSets are not available). Unit conversion types that are based on a classic DataStore object are not available in SAP BW/4HANA.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Businesses relying on currency translation types, unit conversion, and key date derivations that use InfoObjects from InfoSets or classic DataStore objects may face disruptions as these elements need manual adaptation or deletion to transition to SAP BW/4HANA.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are supported or can be converted to SAP BW/4HANA. Currency translation types, unit conversion types, and key date derivations from InfoSets need to be adapted manually or deleted. Unit conversion types based on classic DataStore objects should be converted to use DataStore objects (advanced) using the Transfer Tools.</p>
<p><strong>Reference Notes:</strong></p>
<ul>
    <li><a href="https://launchpad.support.sap.com/#/notes/2421930">2421930 - Simplification List for SAP BW/4HANA</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2347382">2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2383530">2383530 - Conversion from SAP BW to SAP BW/4HANA</a></li>
</ul>