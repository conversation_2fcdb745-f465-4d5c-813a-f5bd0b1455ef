<h3>BW210: Data Federator Façade</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2444890">2444890 - BW4SL - Data Federator Facade</a></strong></p>
<p><strong>Description:</strong></p>
<p>The SAP BW Data Federator Facade is not available in SAP BW/4HANA. Instead of the Data Federator Facade, you can use the external SAP HANA views of the SAP BW/4HANA InfoProviders. This allows direct SQL access to SAP BW/4HANA data. The external SAP HANA views need to be generated for all objects where a SQL access is necessary.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The transition from SAP BW to SAP BW/4HANA will require replacing the Data Federator Facade with external SAP HANA views. This change affects how SQL access to SAP BW/4HANA data is handled.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Check your systems for the usage of function modules RSDRI_INFOPROV_READ_DF and RSDRI_DF_GET_SYSTEM_INFO. Generate external SAP HANA views for all objects where SQL access is necessary.</p>
<p><strong>Reference Notes:</strong> 
<a href="https://launchpad.support.sap.com/#/notes/2421930">2421930 - Simplification List for SAP BW/4HANA</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2347382">2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2383530">2383530 - Conversion from SAP BW to SAP BW/4HANA</a>
</p>