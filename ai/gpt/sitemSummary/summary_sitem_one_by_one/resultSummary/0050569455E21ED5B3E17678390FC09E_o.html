<h3>SI1: Logistics_AB</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267743">2267743 - S4TWL - Agency Business</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<ul>
    <li>Simplification of DB Key Fields and tables structures (WBRL, WBRRE, WBRF, WBRFN, and WBRP)</li>
    <li>Replacement of concatenated fields by corresponding new fields in mentioned DB tables (Details mentioned notes below)</li>
    <li>Replaced fields are moved to the dynamical part of the communication structures to avoid runtime errors and to ensure the same system behaviour as before</li>
    <li>All redundant transactions and reports or business processes were replaced by one unified set of transactions, reports, and business processes</li>
    <li>Different Usage of Copy Requirements in Transfer Manager</li>
</ul>
<p><strong>Business Process Impact:</strong></p>
<p>The application simplification offers now for each business process step one single transaction instead of many. Redundant functionalities were combined into one transaction or business process. This will help to streamline each single business step and to reduce unnecessary complexity by eliminating similar business processes steps.</p>
<p>The Remuneration Request List is not available within SAP S/4HANA. The functional equivalent in SAP S/4HANA is the Extended Remuneration List, which offers the same business capability. This means that the customizing of the Extended Remuneration List has to be set up to ensure that the same documents are created. The replacement of the Remuneration List by the extended one can be done before or after the system conversion to SAP S/4HANA, on-premise edition 1511. Details about the system configuration can be found in the note: 2197892</p>
<p>The Standard Settlement Methods to generate Settlement Documents is not available in SAP S/4HANA. The functional equivalent in SAP S/4HANA is the New Settlement Method. The System will adjust all documents which were created with reference to the standard settlement method and the customizing data automatically. So you have to process the documents with respect to the business process steps of the new settlement method. Details about the system configuration can be found in the note: 2197898</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Pre-Check is performed for the component LO-AB. Custom code adoption on the new DB-Design can be checked in the start release to identify the code places which have to adjust to the new DB-Structures. Batch or online processes which needed the obsolete transactions or reports have to be adjusted so that the new transactions and reports are used instead of the obsolete ones. Mostly all replaced reports and transactions were part of the solution portfolio of SAP_APPL and EA-RETAIL of the Business Suite. To simplify the Conversion to S/4 it is recommended to adjust the business process in the start release by a step-by-step replacement of the obsolete transactions by the ones that will be part of S/4. You have to activate the software component EA-RETAIL to use the successor reports or transactions. You can also adopt the business processes in S/4 without changing the processes in the current release.</p>
<p>Please adjust copy requirements if you have used the copy requirement 4 or 5 and if you use them for different source and target fields. Please adjust the settings after the upgrade accordingly.</p>
<p><strong>Reference Notes:</strong></p>
<p><a href="https://launchpad.support.sap.com/#/notes/2204135">2204135 - S4TC SAP_APPL - Obsolete Reports, PFCG Roles and Transactions</a></p>
<p><a href="https://launchpad.support.sap.com/#/notes/2299208">2299208 - S4TC SAP_APPL Credit Limit Check in Agency Business</a><p>
<p><a href="https://launchpad.support.sap.com/#/notes/2203518">2203518 - S4TC SAP_APPL -  DB Field Changes within Agency Business</a></p>
<p><a href="https://launchpad.support.sap.com/#/notes/2197898">2197898 - S4TC SAP_APPL - Checks for Settlement Method</a></p>
<p><a href="https://launchpad.support.sap.com/#/notes/2197892">2197892 - S4TC SAP_APPL - Checks for Remuneration List</a></p>