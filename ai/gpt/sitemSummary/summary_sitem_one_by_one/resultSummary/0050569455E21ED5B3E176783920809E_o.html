<h3>SI3_FIN_GL</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2270360">2270360 - S4TWL - Calc View-Based Reporting (HANA live)</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p>With Simple Finance 1.0, one reporting option was to use Embedded BW functionality based on ODPs, which in turn use Calculation Views for real-time data access. A strategic decision was taken to switch the technology from Calculation Views to CDS Views with SAP S/4HANA Reporting scenarios built with these ODPs. Simple Finance installations 1.0 or 1503 have to be rebuilt manually with CDS Views in SAP S/4HANA and Simple Finance 1602 SP 1602.</p>
<p><strong>Business Process Impact:</strong></p>
<p>It should be possible to rebuild all existing reporting scenarios with CDS Views.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>If such ODPs are used, time and resources should be planned for migration to CDS Views.</p>
<p>Customers will have to rebuild the reporting scenarios using CDS Views. End users will have to redo personalizations and variants of reports.</p>
<p><strong>Custom Code Adaption:</strong> Customers using ODPs should plan time and resources for migration to CDS Views.</p>
<p><strong>Interface Adaption:</strong> Interface adjustment is potentially required.</p>
<p><strong>User Training:</strong> Customers will have to rebuild the reporting scenarios using CDS Views. End users will have to redo personalization and variants of reports.</p>