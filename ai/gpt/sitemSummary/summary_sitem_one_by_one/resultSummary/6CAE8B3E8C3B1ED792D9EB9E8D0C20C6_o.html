<h3>BW134: Transfer Rules</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2470352">2470352 - BW4SL & BWbridgeSL - Data Flows (3.x)</a></strong></p>
<p><strong>Description:</strong></p>
<p>You use the following object types related to 3.x data flows and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge:</p>
<p>Those objects need to be replaced:</p>
<ul>
<li>Update Rules</li>
<li>3.x InfoSources</li>
<li>Transfer Rules</li>
<li>3.x DataSources</li>
<li>Transfer and Communication Structures</li>
</ul>
<p>Those objects need to be adapted:</p>
<ul>
<li>InfoPackages</li>
<li>Process Chains</li>
<li>Process Chain Variants</li>
<li>VirtualProviders based on 3.x InfoSource (Cube Type=V, Cube Sub-type=S)</li>
</ul>
<p>The 3.x data flow migration tool (transaction RSMIGRATE) is therefore also not available in SAP BW/4HANA.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The described 3.x data flow objects must be replaced or adapted to ensure compatibility with SAP BW/4HANA. This transition helps in maintaining data flow integrity and compatibility with the new system architecture of SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>- Run the pre-check program RS_B4HANA_RC to determine which objects are available or can be converted to SAP BW/4HANA and SAP Datasphere, SAP BW bridge.</p>
<p>- Use transaction RSMIGRATE to create migration projects for converting data flows to compatible object types.</p>
<p>- Perform manual migration for SAP BW releases lower than 7.3, as automatic tools will not handle hierarchy DataSources and their associated transfer rules.</p>
<p><strong>Reference Notes:</strong> 
<a href="https://launchpad.support.sap.com/#/notes/1610259">1610259 - Control of return code behavior in update rules</a>, 
<a href="https://launchpad.support.sap.com/#/notes/1052648">1052648 - Migration of transfer rules and update rules for BW7.x</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2421930">2421930 - Simplification List for SAP BW/4HANA</a>
</p>