<h3>SI5: CT_ES</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2318521">2318521 - S4TWL - Enterprise Search</a></strong></p>
<p><strong>Description:</strong></p>
<p>ESH_SEARCH was the generic search UI of Enterprise Search in previous releases. Its successor in SAP S/4HANA is the integrated search functionality in the Fiori Launchpad.</p>
<p><strong>Business Process Impact:</strong></p>
<p>If a customer migrates from an older release to SAP S/4HANA and if they created their own search models or enhanced existing ones before, they need to migrate their enhancements to the potentially changed standard search models of SAP S/4HANA. Note that the navigation targets are not maintained any longer in the Enterprise Search model but in the Fiori Launchpad Designer.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>The search functionality is always available in the header area of the Fiori Launchpad, eliminating the need for a transaction code to start it.</p>
<p>The users may need a general introduction to the Fiori Launchpad. Only if the customer created their own search models or enhanced existing ones before, they need to migrate their enhancements to the possibly changed standard search models of SAP S/4HANA. Note that the navigation targets are not maintained any longer in the Enterprise Search model but in the Fiori Launchpad Designer.</p>
<p><strong>Reference Notes:</strong> 
<ul>
<li><a href="https://me.sap.com/notes/2227007">2227007 - SAP S/4 HANA Simplification Item: Transaction ESH_SEARCH not available</a></li>
<li><a href="https://me.sap.com/notes/2190420">2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code</a></li>
</ul>
</p>