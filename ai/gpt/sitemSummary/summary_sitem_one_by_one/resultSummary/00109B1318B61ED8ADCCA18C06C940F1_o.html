<h3>SI013: CRM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2692810">2692810 - Partner function handling in business partner</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are considering or planning a migration of a CRM 7.0 or CRM 7.0 EhP installation to S/4HANA OP 1909 or a higher release. The following information on changed or deprecated functionality is relevant in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>In business partner maintenance, the CRM partner functions are no longer used. Partner functions are not assigned to the relationship any longer, but maintained in the customer master on sales area level. In the customizing of access sequences for partner determination in transactions, the corresponding source CRM_PARTNER_A (BP Relationships By Sales Organization) has been replaced by CRM_KNVP (Sales Area Dependent Partners).</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>If you are using partner functions on relationships, the database tables CRMM_BUT_FRG0080 and CRMM_BUT_FRG0081 will have entries. If you are using access sequences with origin "BP Relationships By Sales Organization", then there is at least one entry in table CRMC_PARTNER_AS where the field ORIGIN has the value CRM_PARTNER_A.</p>