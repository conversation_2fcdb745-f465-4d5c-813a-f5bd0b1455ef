<h3>SI14: FIN_TRM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270529">2270529 - S4TWL - Quantity Ledger Always Active for Money Market Transactions</a></strong></p>
<p><strong>Description:</strong></p>
<p>The quantity ledger is now always used for OTC transactions. It is a technical prerequisite for the function valuation class transfer.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The pre-check for the SAP S/4 HANA transition of the quantity ledger functionality raises an error with check-id SI14_TRQ if it finds company codes in at least one client, with the exception of clients 000 and 066, that require generation of quantity positions. This generation is required if an entry exists in table TRGT_MIG_CC_PT with a defined conversion status which is incomplete. If there is no entry or an undefined status is found, the check further examines table TRQT_BUSTRANS for corresponding entries for each business transaction. Lacking entries will also prompt the same error.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Before converting to SAP S/4HANA, activate the quantity ledger for OTC transactions in your ERP system. Customize Treasury and Risk Management by selecting the migration type 'Conversion from >= Enterprise 2.0 and <= ERP 6.0', assigning your transport request, and specifying the logical system names for your production, test, and customizing systems. Execute the steps H02 OTC: Adjust Valuation Class in Bus. Transactions (Optional) and H03 OTC: Generate Quantity Positions (Optional) using the transaction TPM_MIGRATION.</p>
<p>For SAP ERP 6.0 EhP&lt;3, perform the conversion customizing before the SAP S/4HANA conversion. For EhP&lt;3, the conversion customization should be completed after SAP S/4HANA conversion.</p>