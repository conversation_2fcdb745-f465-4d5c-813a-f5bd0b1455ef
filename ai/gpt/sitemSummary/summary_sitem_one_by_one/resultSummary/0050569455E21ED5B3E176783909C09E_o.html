<h3>SI5: Master<PERSON><PERSON>_PM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2330063">2330063 - S4TWL - Simplification of copy/reference handling</a></strong></p>
<p><strong>Description:</strong></p>
<p>During the preparation of the migration to an S/4 system, the preliminary checks return either an error or a warning with the check ID SAP_APPL_LO_MD_MM_T130F_CHECK. The error message indicates that the table T130F contains entries that are either not yet included in the table T130F_C or that have differing contents.</p>
<p>In an S/4HANA system, the entries delivered by SAP can no longer be adjusted customer-specifically, with the result that deviating settings are overwritten with the SAP delivery settings during an upgrade. To prevent any customer-specific settings in T130F for the copy control being replaced in the material master and the article master, an SAP S/4HANA system Release 1610 or higher contains an additional Customizing table T130F_C. This allows alternative maintenance of the copy control to the SAP delivery. The settings in T130F_C are then taken into account during the maintenance of material master and article master data.</p>
<p>Therefore, all entries from the table T130F that are relevant for the copy control must be copied to the new table T130F_C, and their field contents must be identical.</p>
<p>If the precheck issues a warning message, it may be the case that the fields relevant for copying control from T130F have to be copied to the new table T130F_C. However, it is only necessary to copy the entries if the S/4HANA target release is higher than or equal to SAP S/4HANA 1610. In this case, it is advisable to migrate the entries from T130F to the table T130F_C to when the warning appears, as described in the "Solution" section. Otherwise the system will later issue an error message during the migration phase when you execute the precheck because the system only then contains the information concerning the S/4HANA target release to which the data is migrated. To proceed further, you must then first carry out the data migration.</p>
<p>If the S/4HANA target release is equal to SAP S/4HANA 1511, the system only issues a warning during the migration phase because the new table does not exist in this release. In this case, proceed with the migration process.</p>
<p>If you use an SAP ERP for Retail system that requires at least the S/4HANA target release version SAP S/4HANA 1610, the system issues an error message at all times if the precheck determines inconsistencies. The data migration must be carried out in any case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The information provided in this Simplification Item ensure that customer-specific settings referring to the copy control of fields in the product master are still available after the migration to S/4HANA. This means that the copy control in S/4HANA will work in the same way as before the migration. The changes explained above under "Symptom" are necessary to define the Virtual Data Model (VDM), which is needed for the new SAP Fiori application.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Execute the migration precheck.</p>
<p>Implement the report RMMT130F_CPY in your SAP ERP system with the aid of SAP Note 2323185. In addition, you must implement the new table T130F_C in your system based on the correction instructions in SAP Note 2324325.</p>
<p>Execute the report with the standard settings. If entries are to be migrated, remove the indicator for the test mode on the initial screen and execute the report again. Check the messages output to establish whether the entries in all clients have been migrated successfully.</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2206932">2206932 - Migration precheck</a>, <a href="https://me.sap.com/notes/2323185">2323185 - Migration report</a>, <a href="https://me.sap.com/notes/2324325">2324325 - Migration report</a></p>