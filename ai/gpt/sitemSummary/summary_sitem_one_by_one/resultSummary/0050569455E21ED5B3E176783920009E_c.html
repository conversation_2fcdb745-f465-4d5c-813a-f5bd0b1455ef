<h3>SI1_FIN_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270333">2270333 - S4TWL - Data Model Changes in FIN</a></strong></p>
<p><strong>Description:</strong></p>
<p>The universal journal significantly changes the way transactional data is stored for financial reporting. It offers huge benefits in terms of the ability to harmonize internal and external reporting requirements by having both read from the same document store where the account is the unifying element. You still create general journal entries in General Ledger Accouting, acquire and retire assets in Asset Accounting, run allocation and settelement in Controlling, capitalize research and development costs in Investment Management, and so on, but in reporting, you read from one source, regardless of whether you want to supply data to your consolidation system, report to tax authorities, or make internal mangement decisions.</p>
<p>Totals records that store the data in the General Ledger, Controlling, Material Ledger, Asset Accounting, Accounts Payable and Accounts Receivable by period have been removed as have index tables that are designed to optimize certain selections. This simplifies the data structure and makes accounting more flexible in the long term as reporting is no longer limited by the number of fields in the totals records.</p>
<p><strong>Business Process Impact:</strong></p>
<p>For the business process impact, please refer to the notes listed in the Business Impact note 2270333.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Adapt access to the new simplified Finance data model in your customer-specific programs – refer to SAP note 1976487.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2579584">2579584 - Recommendations for Usage of Reports in Financial Reporting in S/4 HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2219527">2219527 - Notes about using views BSID, BSAD, BSIK, BSAK, BSIS, and BSAS in customer-defined programs in SAP S/4HANA Finance</a>, <a href="https://launchpad.support.sap.com/#/notes/2431747">2431747 - General Ledger: Incompatible changes in S/4HANA compared to classic ERP releases</a></p>