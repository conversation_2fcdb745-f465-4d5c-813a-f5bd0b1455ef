<h3>SI1: SD_GENERAL</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267306">2267306 - S4TWL - SD Simplified Data Models</a></strong></p>
<p><strong>Description:</strong></p>
<p>The following data model simplifications have been implemented for the SD area:</p>
<ul>
<li>Elimination of status tables VBUK and VBUP: Status fields have been moved to the corresponding header and item tables, leading to increased performance.</li>
<li>Simplification of document flow table VBFA.</li>
<li>Field length extension of SD document category, eliminating redundancies.</li>
<li>Elimination of document index tables such as VAKPA, VAPMA, VLKPA, VLPMA, VRKPA, VRPMA.</li>
<li>Rebate index table VBOX and LIS tables S066, S067 have been eliminated.</li>
</ul>
<p><strong>Business Process Impact:</strong></p>
<p>No effects on business processes are expected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
<ol>
<li>Adapt customer code according to the custom code check results, see SAP Note 2198647 for a detailed description.</li>
<li>If you have added append structures to VBUK or VBUP, ensure these fields are added to the respective document tables.</li>
</ol>
An append field to VBUK must be added to VBAK, LIKP, or VBRK. Similarly, an append field to VBUP must be added to VBAP or LIPS. Ensure the fields have the same name and data type as in the source table.
<p><em>When and where to add append fields during conversion?</em> Add fields during phase ACT_UPG in the development system. A check for such fields is provided by SAP Note 2224436 and is integrated into upgrade tools (SUM) as of S/4HANA 1709.</p>
<p><strong>Reference Notes:</strong></p>
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2198647">2198647 - S/4 HANA: Data Model Changes in SD</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2224436">2224436 - S4TC SAP_APPL - Checks for SD Data Model</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2267377">2267377 - S4TWL - SD Rebate Processing Replaced by Settlement Management</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2270544">2270544 - S4TWL - Credit Management</a></li>
</ul>