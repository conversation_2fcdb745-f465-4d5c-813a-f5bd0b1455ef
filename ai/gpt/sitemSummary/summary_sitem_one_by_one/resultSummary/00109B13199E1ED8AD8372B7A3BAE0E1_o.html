<h3>SI007: CRM</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2691605">2691605 - Persistency of business transactions optimized for HANA</a></strong></p>
<p><strong>Description:</strong></p>
<p>The persistency of S/4HANA Service business transactions (sometimes referred to as "One Order" documents) has been redesigned completely, in order to support fast queries and analytics on the primary persistency. The separation into numerous small tables which are tied together via the link handler has been abandoned. Every business object is now represented via a flat table (header and item level) which absorbs all flat sets and extensions. Complex components, such as partners or reference objects, are still held in a separate table, but the key structure of this table has been simplified, and the link handler is no longer used.</p>
<p>For a guide how to convert SQL statements in custom developments to the new persistency, please see Note 2788517.</p>
<p><strong>Business Process Impact:</strong></p>
<p>With the redesigned persistency, fast analytical queries are now possible on the primary tables of the data model. This avoids redundancies, improves performance and reduces memory usage in the database.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>This item is relevant if you are using any kind of business transactions (for example, activities, leads, or service orders) in your CRM 7.0 system. Technically, this is the case if the root table CRMD_ORDERADM_H for business transactions contains records.</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2788517">2788517 - SELECT Statements on Business Transaction Tables in Custom Code: Migration from CRM 7.0 to S/4HANA</a></p>