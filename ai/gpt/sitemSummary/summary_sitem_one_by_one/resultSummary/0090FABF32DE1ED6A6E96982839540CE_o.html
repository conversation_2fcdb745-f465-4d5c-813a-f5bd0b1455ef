<h3>SI3: HR_LEARN</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2383837">2383837 - S4TWL - SAP Learning Solution with Compatibility Mode and beyond</a></strong></p>
<p><strong>Description:</strong></p>
<p>SAP Learning Solution as well as SAP Enterprise Learning Environment are part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope, its expiry date and links to further information please refer to SAP Note 2269324 – Compatibility Scope Matrix for SAP S/4HANA on-premise. The corresponding entries can be found in the Compatibility Scope Matrix via ID 135 (SAP Learning Solution) and 136 (SAP Enterprise Learning Environment).</p>
<p>SAP S/4HANA customers using SAP Learning Solution or SAP Enterprise Learning Environment can therefore continue using SAP Learning Solution and SAP Enterprise Learning Environment in the so-called Compatibility Mode.</p>
<p>SAP Learning Solution / SAP Enterprise Learning Environment with component LSOFE was technically still part of S4CORE with SAP S/4HANA releases 1511, 1610 and 1709. For SAP S/4HANA releases 1809, 1909, 2020 and 2021, LSOFE is removed from S4CORE. SAP provides a new add-on S4LSOFE for the SAP S/4HANA releases 1809, 1909, 2020 and 2021 as interim solution. This enables customers to continue using SAP Learning Solution and SAP Enterprise Learning Environment in SAP S/4HANA as part of the Compatibility Pack until 2025. The add-on S4LSOFE is available as of October 30<sup>th</sup>, 2020. Please refer to SAP Note 2966518 – SAP Learning Solution for SAP S/4HANA: Release Information Note.</p>
<p>With the release of SAP Human Capital Management for SAP S/4HANA, on-premise edition (SAP HCM for SAP S/4HANA) in 2022, SAP Learning Solution with the component LSOFE will be part of the product offering. The support of SAP Learning Solution continues after 2025 in SAP Human Capital Management for SAP S/4HANA, on-premise edition. Please find more information about SAP HCM for SAP S/4HANA in SAP Note 2273108 - S4TWL - General HCM Approach within SAP S/4HANA.</p>
<p>SAP S/4HANA does not contain the NetWeaver Application Server for Java (AS Java). Therefore the Java components such as the Java based content player, authoring environment and offline player, need to be deployed on a stand-alone-server. For this SAP NW 7.5 AS Java can be used.</p>
<p><strong>Business Process Impact:</strong></p>
<p>SAP SuccessFactors remains the primary focus of our HCM innovations and is therefore the go-to architecture in the HCM area. For customers who are not willing or able to move to SAP SuccessFactors in the near future, continuity and investment protection for HCM functionality on-premise is provided via SAP HCM for SAP S/4HANA.</p>
<p><strong>Please note:</strong> SAP Learning Solution in Compatibility Mode and HCM for SAP S/4HANA is not available in the RISE with SAP S/4HANA Cloud, private edition. If you are planning to move to RISE with SAP S/4HANA Cloud, private edition you need to replace SAP Learning Solution with SAP SuccessFactors Learning.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Before executing the system conversion to SAP S/4HANA and if SAP Learning Solution is integrated, please carefully read SAP Note 2966518 – SAP Learning Solution for SAP S/4HANA: Release Information Note. It contains information about prerequisites.<br/>To continue using SAP Learning Solution, the add-on S4LSOFE needs to be installed after the system conversion to SAP S/4HANA. As a prerequisite, if custom pages are maintained in view LSPAGE_ALIAS_C, a manual backup of both custom BSP pages and the view entries is required before executing the system conversion. Once the system is upgraded, the custom BSP pages and the view entries have to be created manually based on the backup.</p>
<p>Please execute, before the system conversion to SAP S/4HANA is started, the following activities:</p>
<ul>
<li>Check if SAP Learning Solution is active in the system by having a look at customizing table T77S0 for switch HRLSO HRLSO</li>
<ul>
<li>If the value is ‘X’ then SAP Learning Solution is active</li>
<li>If the entry is blank, SAP Learning Solution is inactive</li>
</ul>
<li>Execute Function Module LSO_ACTIVATION_CONSISTENCY_CHK in each client. <br/>If there are <span>no database entries present</span>, the value of variable LSO_ACTIVE_STATUS is returned as 2 and the following messages are displayed:<br/>
<ul>
<li>Relation 614 is not used</li>
<li>Relation 25 is not used</li>
<li>SAP Learning Solution specific relations 612, 616, 617, or 618 are not available in the system</li>
</ul>
<br/>In this case, switch off SAP Learning Solution by setting the value in customizing table T77S0 for switch HRLSO HRLSO to &lt;blank&gt;. This can also be done by executing transaction LSO_ACTIVATE.<br/>In doing so, you may lose data such as customer specific entries (if they exist) in the following tables: Action (T777M), infotypes for each object type (T777I), and allowed relationship (T777E).<br/>Customer-specific entries need to be inserted manually into these tables.<br/><br/>If there are <span>database entries present</span>, the value of variable LSO_ACTIVE_STATUS is also returned as 2 but the following messages are displayed:<br/>
<ul>
<li>Relation 614 has been found in the system and is actively used</li>
</ul>
<ul>
<li>Relation 25 has been found in the system and is actively used</li>
<li>SAP Learning Solution specific relations 612, 616, 617, or 618 are available in the system</li>
</ul>
<br/>If SAP Learning Solution is inactive but database entries in the aforementioned tables are present, please raise an incident to SAP via component PE-LSO-TM for further analysis. Such a constellation could occur if e.g. SAP Learning Solution was used in the past.<br/><br/></li>
</ul>
<p>If the above-mentioned activities are not executed before the system conversion, the following message could occur during the system conversion:<br/>“SAP Learning solution is used in client but LSO activation is inconsistent. Refer SAP Note 2383837 for more details.” <br/>This message can occur in either of the following 2 cases:</p>
<ul>
<li>SAP Learning Solution is active in the system, but there are no database entries.<br/> This means SAP Learning Solution is not in use. In this case, the error can be skipped, if SAP Learning Solution is not used in any of the available clients.</li>
<li>SAP Learning solution is inactive in the system, but there are database entries present. <br/> This could occur if e.g. SAP Learning Solution was used in the past. In this case, please raise an incident to SAP via component PE-LSO-TM for further analysis.</li>
</ul>
<p>It could also be possible that the following message occurs during the system conversion:<br/>“Target release contains EA-HRGXX. Additionally, install S4LSOFE - Refer SAP Note 2383837 and 2966518 for more details.”<br/>In this case, the error can be skipped and proceed with the system conversion. Install the add-on S4LSOFE in the SAP S/4HANA system after the system conversion to continue using SAP Learning Solution. As a prerequisite, if custom pages are maintained in view LSPAGE_ALIAS_C, a manual backup of both custom BSP pages and the view entries is required before proceeding to convert. Once the system is upgraded, the custom BSP pages and the view entries have to be created manually based on the backup.</p>
<p>In general, customers running SAP S/4HANA on-premise, SAP HCM and SAP Learning Solution integrated in one system have the following options:</p>
<ul>
<li>Replace SAP Learning Solution with SAP SuccessFactors Learning</li>
<li>Install add-on S4LSOFE for SAP S/4HANA releases 1809, 1909, 2020 and 2021</li>
<ul>
<li>This option is not available in RISE with SAP S/4HANA Cloud, private edition</li>
</ul>
<li>Move to SAP Human Capital Management for SAP S/4HANA, on-premise edition, once it is available</li>
<ul>
<li>This option is not available in RISE with SAP S/4HANA Cloud, private edition</li>
</ul>
</ul>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2269324">2269324 - Compatibility Scope Matrix for SAP S/4HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2273108">2273108 - S4TWL - General HCM Approach within SAP S/4HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2966518">2966518 - SAP Learning Solution for SAP S/4HANA: Release Information Note</a>