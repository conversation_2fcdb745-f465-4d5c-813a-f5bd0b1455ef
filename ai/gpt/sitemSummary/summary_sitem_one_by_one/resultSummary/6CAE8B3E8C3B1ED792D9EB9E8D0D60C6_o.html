<h3>BW150: Myself Source System</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2479674">2479674 - BW4SL & BWbridgeSL - Myself Source System</a></strong></p>
<p><strong>Description:</strong></p>
<p>You use the "Myself" Source System and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The "Myself" Source System serves two data load scenarios and will not be available with SAP BW/4HANA or SAP Datasphere, SAP BW bridge:</p>
<ul>
<li>Extraction via 3.x Export DataSources (prefixed with 8) into the same BW cannot be transferred to SAP BW/4HANA or SAP Datasphere, SAP BW bridge. These scenarios are deprecated from BW 7.00 and should be replaced by DTP loads from InfoProvider to InfoProvider.</li>
<li>Extraction from customer-owned (prefixed with Z) or other application DataSources into the same BW needs to be replaced by ODP extraction. Such scenarios are not supported with SAP Datasphere, SAP BW bridge.</li>
</ul>
<p><strong>Required and Recommended Actions:</strong></p>
<p>For SAP BW/4HANA:</p>
<ul>
<li>Replace extraction via 3.x Export DataSources with DTP loads from InfoProvider to InfoProvider.</li>
<li>For extraction from customer-owned or other application DataSources, transfer them to ODP-CDS or ODP-SAPI. Ensure corresponding SAPI-DataSources are transported from the sender to the receiver system prior to the transfer.</li>
<li>Execute the switch to "Ready for conversion mode" and ensure no existing DataSources for the Myself Source System are present; otherwise, deletion errors occur.</li>
</ul>
<p>For SAP Datasphere, SAP BW bridge:</p>
<ul>
<li>Replace extraction via 3.x Export DataSources with DTP loads from InfoProvider to InfoProvider as the scenarios cannot be transferred with the transfer tool.</li>
<li>Recognize that extraction from customer-owned or other application DataSources is not supported.</li>
</ul>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2473145">2473145 - BW4SL & BWbridgeSL - SAP and BW Source Systems</a>, <a href="https://launchpad.support.sap.com/#/notes/2483299">2483299 - BW4SL & BWbridgeSL - Export DataSources</a>, <a href="https://launchpad.support.sap.com/#/notes/2443863">2443863 - BW4SL & BWbridgeSL - S-API DataSources</a></strong></p>