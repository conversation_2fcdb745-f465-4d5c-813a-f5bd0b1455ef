<h3>SI8_FIN_GL</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2628654">2628654 - S4TWL: Amount Field Length Extension</a></strong></p>
<p><strong>Description:</strong></p>
<p>Selected currency amount field lengths and related data types have been extended. This is relevant if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>
<p><strong>Business Process Impact:</strong></p>
<p>This impacts the way currency amounts are handled and stored by extending the length of amount fields. The primary impact is on financial and controlling modules, but extends to other modules because of shared dependencies. Any component managing currency amounts, general ledger, journal entries, and related functionality now supports extended length amounts. Furthermore, it affects BAPIs, IDocs, and external interfaces, necessitating adaptation in order to maintain compatibility.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>During the conversion project, it is mandatory to adapt custom code to handle the extended amount fields without encountering runtime or syntax errors. Software upgrade and maintenance might be conditional based on whether the new functionality is to be used or not. For necessary adjustments, activities include code changes, such as the correct handling of extended amount fields within the ABAP coding, overflow handling, and ensuring compatibility of internal and external interfaces. Additional actions include adapting table field definitions, user interfaces, and integration setups.</p>
<p><strong>Reference Notes:</strong> 
2628699 - Amount Field Length Extension: Code Adaptations for Compatibly Enhanced Local Function Modules<br>
2628724 - Amount Field Length Extension: Code Adaptations/Usages of BAPIs and RFCs<br>
2628704 - Amount Field Length Extension: Code Adaptations for Usages of Adjusted Interfaces<br>
2628641 - Amount Field Length Extension: IDoc Interface/ALE Adaptations<br>
2628706 - Amount Field Length Extension: Code Adaptations for Usages of RFC Enabled Function Modules<br>
2610650 - Amount Field Length Extension: Code Adaptations<br>
2628040 - Amount Field Length Extension: General Information<br>
2628617 - Amount Field Length Extension: Adaptations for ALE/BDBG Generation</p>