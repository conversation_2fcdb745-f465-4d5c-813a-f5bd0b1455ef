<h3>SI2: Oil_Cross_Border_Excise_Duty</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2347421">2347421 - S4TWL - Optimization in Oil & Gas - Inter company sales- Cross border excise duty</a></strong></p>
<p><strong>Description:</strong></p>
<p>This is an IS-OIL specific note for the SAP S/4HANA on premise edition 1610 release. If you do not have IS-OIL, this note doesn’t apply to you. This note does not apply to you if ‘TDP-‘Cross-Company/Cross-Border Sales and Stock Transfers’ BC set is not activated in your system. You are doing a system conversion to SAP S/4HANA on premise edition 1610, the following SAP S/4HANA note supports inter-company sales- Cross Border Excise duty movement cancellation.</p>
<p><strong>Business Process Impact:</strong></p>
<p>In TDP, Intercompany sales – Cross Border Excise duty business process concept of notional plant is introduced to handle the tax determination. When performing PGI against the outbound delivery, the following movements will be posted: 601 from supplying plant, 501(Goods receipt without PO) movement into notional plant & 601 from notional plant. However, during material movement (intercompany) cancellation (through VL09), an availability check will occur at every line item, potentially throwing a deficit of stock error at the notional plant. This impacts different delivery variants: delivered; CoT@Load, delivered; CoT@Discharge outside TD, and delivered: Customer Pickup.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p><strong>New developments (Mandatory during conversion project):</strong> Implement BADI ‘BADI_NSDM_READ_STOCK’ which checks whether there is a notional plant in OIH79 table. If exists, assigns 'A' (array processing) to variable IS_SINGLE_OR_ARRAY, which will take care of inter company Sales (cross border excise duty) reversal scenarios.</p>