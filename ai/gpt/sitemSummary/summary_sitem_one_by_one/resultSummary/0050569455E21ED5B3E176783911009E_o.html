<h3>SI1: Logistics_MM-IM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2206980">2206980 - 2206980 - Material Inventory Managment: change of data model in S/4HANA</a></strong></p>
<p><strong>Description:</strong></p>
<p>You want to install SAP S/4HANA and need additional information how to adjust your customer enhancements, modifications or own functionalities to the new, simplified data model of SAP S/4HANA Supply Chain (MM - Inventory Management).</p>
<p>You want to have information about what is different in SAP S/4HANA Supply Chain (MM - Inventory Management) compared to Suite on HANA MM-IM.</p>
<p><strong>Business Process Impact:</strong></p>
<p>With S/4HANA, the material inventory management data model has changed, replacing several stock tables such as MKPF, MSEG, MARC, MARD, and introducing a new de-normalized table, MATDOC. This impacts business processes by:
- Reducing database locks, as the system shifts to an INSERT-only mode without DB locks.
- Calculating actual stock quantity on-the-fly from MATDOC.
- Enhancing reporting capabilities due to consolidated information in MATDOC.
However, this also necessitates adjustments in customer enhancements and modifications to align with the new data model. Processes that relied on pre-aggregated stock quantities in tables will now need to access on-the-fly calculations via MATDOC or proxy CDS views.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<ul>
  <li><strong>Business Decision (Before conversion project, Optional):</strong> Decide which locking methods to be used for goods movements.</li>
  <li><strong>Customizing / Configuration (During conversion project, Mandatory):</strong> Adapt MM-IM settings and configurations to the new data model structure.</li>
  <li><strong>Technical System Configuration (During conversion project, Optional):</strong> Adjust system configurations to support the new data model.</li>
  <li><strong>Custom Code Adaption (During conversion project, Mandatory):</strong> Review and modify custom codes to ensure compatibility with MATDOC and the new data structures.</li>
  <li><strong>Customizing / Configuration (During or after conversion project, Mandatory):</strong> Update and verify customizing settings to ensure seamless integration with the new data model.</li>
</ul>
<p><strong>Reference Notes:</strong> 
<ul>
  <li><a href="https://launchpad.support.sap.com/#/notes/2242679">2242679 - Redirect inconsistency - Proxy Substitution</a></li>
  <li><a href="https://launchpad.support.sap.com/#/notes/2194618">2194618 - S4TC SAP_APPL - Checks for MM-IM</a></li>
  <li><a href="https://launchpad.support.sap.com/#/notes/2240878">2240878 - MM-IM: Add customer include CI_COBL to MATDOC</a></li>
  <li><a href="https://launchpad.support.sap.com/#/notes/2378796">2378796 - Material classification: Change in data model in SAP S/4HANA 1610</a></li>
</ul></p>