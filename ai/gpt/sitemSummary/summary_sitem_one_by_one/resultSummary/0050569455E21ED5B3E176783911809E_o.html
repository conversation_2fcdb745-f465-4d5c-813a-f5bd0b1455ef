<h3>SI1: Logistics_PLM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267836">2267836 - S4TWL - Enterprise Search</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>PLM Enterprise Search models will not work after an upgrade to S/4HANA. With SAP S/4HANA, on-premise edition 1511, the Enterprise Search models are now replication-free search models (using HANA DB tables instead) within SAP Product Lifecycle Management (SAP PLM).</p>
<p>With the new solution the following searches are not available in SAP S/4HANA on-premise 1511 release:
<ul>
<li>Document search with linked documents</li>
<li>Document search with attached document content</li>
<li>Synchronization BOM to BOM search with assigned MBOMs</li>
<li>Synchronization PSM to BOM search with source PSM Structure and target MBOMs</li>
<li>MBOM, Recipe and PSM search with assigned classification data</li>
<li>MBOM, Recipe and PSM search with assigned document data</li>
<li>Specification and PSM search with status data</li>
<li>Label search with recipe data origin</li>
<li>Recipe search with formula item quantity</li>
<li>Search for any object with IDs without leading zeroes</li>
</ul>
</p>
<p>With the SAP S/4HANA on-premise 1610 release the following searches are still not available:
<ul>
<li>Document search with attached document content</li>
<li>Recipe search with formula item quantity</li>
<li>Search for any object with IDs without leading zeroes</li>
</ul>
</p>
<p>From the SAP S/4HANA on-premise 1709 release on the following searches are still not available:
<ul>
<li>Recipe search with formula item quantity</li>
<li>Search for any object with IDs without leading zeroes (with the exception of the material number, see the note 2935353)</li>
</ul>
</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Customizing / Configuration (During conversion project): Mandatory
<br>With SAP S/4HANA the Enterprise Search models are now replication-free search models (using HANA DB tables instead) within SAP Product Lifecycle Management (SAP PLM). The Enterprise Search models' names have changed and due to this, the related customizing (Define Settings for PLM Search under PLM Search) needs to be adjusted. SAP Note 2212593
</p>
<p>Custom Code Adaption (During conversion project): Mandatory
<br>Custom code needs to be adjusted to reference to the new model names
</p>
<p><strong>Reference Notes:</strong> 
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2217234">2217234 - PLM Enterprise Search in S/4HANA OP</a></li>
</ul>
</p>