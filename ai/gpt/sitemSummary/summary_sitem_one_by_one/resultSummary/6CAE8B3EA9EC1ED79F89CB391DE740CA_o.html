<h3>SI11: Logistics_MM-IM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2516223">2516223 - S4TWL - Blocked customer or supplier in Inventory Management</a></strong></p>
<p><strong>Description:</strong></p>
<p>The end of purpose check for customer and supplier master data has changed in Inventory Management. From SAP S/4HANA 1709 onwards the following requirements must be fulfilled for blocking customer or supplier master data:
  <ul>
    <li>Material documents that are related to the customer or supplier to be blocked must be archived.</li>
    <li>Special stocks that are related to the customer or supplier to be blocked must be archived or deleted.</li>
    <li>Physical inventory documents that are related to the customer or supplier to be blocked must be archived.</li>
    <li>Open reservations that are related to the customer or supplier to be blocked must be deleted.</li>
    <li>The customer or supplier to be blocked must be removed from plant and storage location data.</li>
    <li>The condition tables used to control the sending of receipt request messages to the Ariba Network must not contain records related to the supplier to be blocked.</li>
    <li>Batch input test data that is related to the supplier to be blocked must be deleted (or changed).</li>
    <li>Short documents (as substitutes for archived material documents) that are related to the supplier to be blocked must be deleted.</li>
    <li>Correction data (contained in database table MMINKON_UP) that is related to the customer or supplier to be blocked must be deleted.</li>
    <li>Consistency check results of aged material documents that are related to the customer or supplier to be blocked must be deleted.</li>
    <li>In case of an upgrade from 1511/1610/1709 to 1709-FPS01 or higher, please read note 2629400</li>
  </ul>
</p>
<p><strong>Business Process Impact:</strong></p>
<p>The changes significantly affect how you manage and maintain customer and supplier master data in Inventory Management. Ensure all related documents and stocks are archived or deleted, and customers or suppliers are removed from plant and storage location data. This will ensure compliance with the end of purpose check requirements, preventing potential disruptions during the system conversion to SAP S/4HANA.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
  <ul>
    <li>Material documents, special stocks, physical inventory documents and open reservations related to blocked customer or supplier master data shall be archived or deleted before converting or upgrading to SAP S/4HANA 1709 (and above).</li>
    <li>Open reservations can be deleted (or flagged for deletion) using transactions MB22 and MBVR. Material documents, special stocks and physical inventory documents can be archived using archiving objects MM_MATBEL, MM_SPSTOCK and MM_INVBEL. Special stocks can be deleted using data destruction objects MM_STO_CONSI_DEST and MM_STO_SOBES_DEST.</li>
    <li>The assignment of a blocked customer to a plant can be removed via SAP Reference IMG / SAP Customizing Implementation Guide (transaction SPRO): Materials Management -> Purchasing -> Purchase Order -> Set up Stock Transport Order -> Define Shipping Data for Plants.</li>
    <li>The assignment of a blocked supplier to a plant can be removed via transaction XK02 -> Purchasing data -> Extras -> Additional Purchasing Data.</li>
    <li>The assignment of a blocked customer or supplier to a storage location can be removed via SAP Reference IMG / SAP Customizing Implementation Guide (transaction SPRO): Materials Management -> Purchasing -> Purchase Order -> Set up Stock Transport Order -> Set Up Stock Transfer Between Storage Locations -> Define Shipping Data for Stock Transfers Between Storage Locations.</li>
    <li>Condition tables can be changed using transaction NACE:
      <ol>
        <li>Select application ME (Inventory Management) and enter “Condition records (F8)”</li>
        <li>Select Output Type ARIB (Ariba Rcpt Request)</li>
        <li>Select key combination ("Supplier/CoCode" or "Supplier")</li>
        <li>In the selection screen for the condition records enter the supplier for that you want to create or display conditions and press execute (F8).</li>
        <li>Enter the conditions (supplier, respectively company code; the other attributes can be ignored).</li>
        <li>Save the changes.</li>
      </ol>
    </li>
    <li>Batch input test data, short documents, correction data and consistency check results shall be deleted:
      <ul>
        <li>Batch input test data can be deleted (or changed) using maintenance view V_159A.</li>
        <li>Short documents (as substitutes for archived material documents) can be deleted using report RM07MAID.</li>
        <li>Correction data (contained in database table MMINKON_UP) can only be deleted manually.</li>
        <li>Consistency check results of aged material documents can be deleted using report NSDM_MATDOC_CHECK_AGED_DATA.</li>
      </ul>
    </li>
    <li>If you upgrade from 1511/1610/1709 to 1709-FPS01 or higher -> please read SAP Note 2629400.</li>
  </ul>
</p>
<p><strong>Reference Notes:</strong> 
  <ul>
    <li><a href="https://launchpad.support.sap.com/#/notes/2629400">2629400 - DPP: Archiving and Masking after migration</a></li>
  </ul>
</p>