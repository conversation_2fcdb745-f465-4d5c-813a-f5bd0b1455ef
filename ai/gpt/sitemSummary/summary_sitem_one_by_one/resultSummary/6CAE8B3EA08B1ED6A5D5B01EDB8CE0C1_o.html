<h3>SI33: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2370131">2370131 - S4TWL - Retail Information System (RIS) and Category Management Workflow</a></strong></p>
<p><strong>Description:</strong></p>
<p>The information systems used to plan, control, and monitor business events at different stages in the decision-making process. Tools were available in Customizing to allow a self-defined information system to be created and tailored to specific requirements.</p>
<p><strong>Business Process Impact:</strong></p>
<p>In SAP S/4HANA, RIS functionality and Category Management Workflow are not available anymore.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>RIS (Retail Information System) technology is not available anymore.<br>
To cover reporting requirements usage of <a href="https://help.sap.com/nwbw" target="_blank">SAP BW</a> or <a href="https://help.sap.com/viewer/p/SAP_BW4HANA" target="_blank">SAP BW/4HANA</a> is recommended.<br>
There is content for <a href="https://help.sap.com/viewer/d1e4c9f0ffc047ec9f945e64026ffab1/7.07.22/en-US/d09a865342bc3d58e10000000a174cb4.html" target="_blank">Retail</a> for SAP BW, which however does not cover an identical scope as RIS. There is no retail specific content for SAP BW/4HANA. Thus, content needs to be created or adjusted part of the implementation project.<br>
To cover operational reporting requirements <a href="https://help.sap.com/viewer/8308e6d301d54584a33cd04a9861bc52/2020.000/en-US/5418de55938d1d22e10000000a44147b.html" target="_blank">CDS</a> (Core Data Services) technology should be used. It is planned to offer retail specific content in a future release. Till then, content needs to be created part of the implementation project.<br>
In case you reuse ABAP objects of packets WRB or WRBA in your custom code, please see attached note.<br>
Information structure S160 is used for perishables procurement. In SAP S/4HANA, perishables procurement is not available from release SAP S/4HANA 1610 till 1909. Starting with SAP S/4HANA 2020 perishables procurement is available again. Thus, information structure S160 can be used from SAP S/4HANA 2020. It is planned to substitute functionality of information structure S160 by CDS technology in a future release.<br>
Coding for retail specific enhancements is controlled via settings in table TMCW. Please deactivate all settings, or delete the entry in table TMCW. Active settings can lead to reduction in performance or even errors. You can maintain entries in table TMCW through transaction SM30 view V_TMCW.</p>
<p><strong>Reference Notes:</strong></p>
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2352561">2352561 - SAP S/4HANA Conversion Pre-Checks: Information for Industry Solution ISR_RETAIL and Retail Information System</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2486476">2486476 - Removing RFC flag for Function Modules in Retail Category Management</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2383533">2383533 - S4TWL - Retail Deprecated Applications Relevance for Custom Code</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2356537">2356537 - S4TC SAP_APPL – Pre-Transition Checks for Retail Information System (RIS)</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2750278">2750278 - Wrong return code for pre-transition checks for Retail Information System (RIS)</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2944418">2944418 - Retail starter pack service for SAP BW/4HANA</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2543543">2543543 - Restrictions for BW extractors relevant for S/4HANA in the area of Retail</a></li>
</ul>