<h3>SI4: Logistics_MM-IM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267835">2267835 - S4TWL - Material Valuation - Statistical moving average price</a></strong></p>
<p><strong>Description:</strong></p>
<p>One major innovation of SAP S/4HANA in the area of MM-IM is the significant increase of transactional data throughput for high-volume transactions, especially goods movements. To enable this from a technical point of view, it is necessary for the application logic to avoid exclusive locking on the application layer.</p>
<p>For goods movements changing the valuated inventory, the system uses exclusive locks to calculate a consistent change in inventory value in situations where concurrent transactions for the same material are ongoing. But, the need for setting exclusive locks is mainly driven by the inventory valuation method:</p>
<ul>
<li>An inventory valuation following the "Standard Price" method does (with a few exceptions) not require to set an exclusive lock. This is because the relationship between unit costs for material inventory (standard price) on the one hand, and inventory quantity and inventory value on the other hand, is kept constant during goods movements.</li>
<li>Whereas an inventory valuation following the "Moving Average Price" method often requires to set an exclusive lock. This is because the relationship between unit costs for material inventory (moving average price) on the one hand, and inventory quantity and inventory value on the other hand, is often changing during goods movements.</li>
</ul>
<p>In SAP ERP, for materials with price control "Standard" the system calculates the inventory valuation following the "Standard Price" method mentioned above. Additionally, it does an inventory valuation following the "Moving Average Price" method in parallel, the so-called "statistical" moving average price. Unfortunately, this "statistical" moving average price valuation requires exclusive locking as well.</p>
<p>As mentioned at the beginning, exclusive locking limits the transactional data throughput, and therefore needs to be avoided to increase transactional data throughput significantly for scenarios with a high volume of transactions.</p>
<p>If you do a system conversion to SAP S/4HANA on-premise edition, you have the <strong>option</strong> to change locking behavior during goods movements to increase transactional data throughput. As said, this is not mandatory for you, but nevertheless recommended in order to achieve a significant increase of transactional data throughput for goods movements.</p>
<p><strong>If you select option</strong> to increase your transactional data throughput during goods movements, this will have following effects:</p>
<ul>
<li>Materials with price control 'S': System will set (with a few exceptions) only a shared lock during goods movements.</li>
<li>Materials with price control 'V': System will set an exclusive lock during goods movements in release SAP S/4HANA 1511. Beginning with release SAP S/4HANA 1610, system will set an exclusive lock during goods movements only in dedicated situations, for other situations it sets only a shared lock (see OSS note 2338387 for details).</li>
<li>As described before, setting only a shared lock for materials with price control 'S' is in conflict with concept of calculating "statistical" moving average price, which would require an exclusive lock. Therefore, to gain the increase of transactional data throughput, <strong>system deactivates "statistical" moving average price and does not calculate it anymore</strong>.</li>
<li>In addition, the following database table fields are affected by the deactivation of the "statistical" moving average price:</li>
<ul>
<li>In table MBEW, EBEW, OBEW, QBEW the fields SALKV and VERPR are not updated anymore for materials with price control “Standard”. The same applies to the history tables MBEWH, EBEWH, OBEWH and QBEWH.</li>
<li>In table CKMLCR the fields PVPRS and SALKV are not updated anymore for materials with price control “Standard”.</li>
<li>In table MLCR the fields SALKV, SALKV_OLD and PVPRS_OLD are not updated anymore for materials with price control “Standard”.</li>
<li>If Actual Costing is used, the above-mentioned fields will still be updated with the periodic unit price during the Actual Costing Closing Steps.</li>
</ul>
<li>As a consequence of not updating the above-mentioned fields, the "statistical" moving average price and the value based on the "statistical" moving average price are no longer available on the user interface. This concerns the following transaction codes: MM01, MM02, MM03, CKM3, and MR21.</li>
<li>It is important to note that selecting this option <strong>can not be un-done</strong>.</li>
</ul>
<p><strong>If you do not select option</strong> to increase your transactional data throughput during goods movements, this will have the following effects:</p>
<ul>
<li>Locking behavior during goods movements is the same you know from SAP ERP. Means, exclusive locks are set during goods movements and transactional data throughput is limited.</li>
<li>Materials with price control 'S': <strong>system still calculates "statistical" moving average price</strong> in parallel.</li>
</ul>
<p><strong>Business Process Impact:</strong></p>
<p>As its name implies, the "statistical" moving average price is purely statistical and does not have any impact on actual financials-relevant valuation.</p>
<p>Without the "statistical" moving average price, an alternative way of valuation needs to be used in few scenarios. This concerns, for instance, Balance Sheet Valuation and Product Cost Planning, where selection/valuation variants must be checked and adapted.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>If within custom code the fields mentioned above under "Description" are evaluated for materials with price control “Standard”, then this usage should be removed. One possible reaction to this change could be to use instead of the V-Price (VERPR) either the S-Price (STPRS) or the V-Price (VERPR) depending on the configured price control for the respective material.</p>
<p>To identify such locations, it is required to make use of the where-used functionality of transaction code SE11 and to consider other techniques like transaction code CODE_SCANNER to find locations which SE11 cannot handle – such as dynamic programming or native SQL statements.</p>
<p>Finally, if you like to select the option to increase your transactional data throughput during goods movements and you accept the drawback that "statistical" moving average price gets deactivated, you can do the following:</p>
<ul>
<li>In SAP S/4HANA 1511 and SAP S/4HANA 1610 execute report SAPRCKM_NO_EXCLUSIVELY_LOCKING. As changes in the customizing table TCURM are not recorded in a customizing request, you have to run the report in each of your systems (customizing, development, production) separately.</li>
<li>Beginning with SAP S/4HANA 1709 you can use IMG activity "Set Material Lock for Goods Movements" (SPRO -> Materials Management -> General Settings for Materials Management).</li>
</ul>
<p>As said, this <strong>is optional</strong> for converted systems running the S/4HANA on-premise edition.</p>
<p><strong>Reference Notes:</strong> 
<a href="https://me.sap.com/notes/2338387">2338387 - S4TWL - Goods movements without exclusive locking by material valuation</a>,
<a href="https://me.sap.com/notes/1985306">1985306 - Performance guide for goods movements</a>,
<a href="https://me.sap.com/notes/2277568">2277568 - Activation of locking behavior "no exclusively locking" deprecates statistical moving average price</a>
</p>