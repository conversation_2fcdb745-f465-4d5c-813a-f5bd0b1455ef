<h3>SI01: ARIBA_CLOUD_INTEG</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2400737">2400737 - Ariba Cloud Integration Solutions for SAP: Supported SAP Product Versions</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are planning to use SAP and Ariba Solutions integrated with Integration Adapters provided by the SAP Ariba Cloud Integration program, or you are already live with it and are considering an upgrade of one of the solutions. To plan your project, you need to know the supported versions and constraints with respect to upgrades.</p>
<p>During upgrade or conversion check, an error is issued in check class CLS4SIC_ARIBA_CI_CHECK (RC = 7): Go to Solution -> Information for upgrade and conversion</p>
<p><strong>Business Process Impact:</strong></p>
<p>Using a non-supported SAP Product Version with Ariba Cloud Integration can lead to broken integration scenarios and unsupported incidents. Upgrading to the Ariba Cloud Integration Gateway (CIG) is recommended to avoid potential conflicts and take advantage of ongoing development and support.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p><strong>Before conversion project:</strong></p>
<ul>
    <li>Business Decision: Decide if you migrate to Ariba Cloud Integration Gateway CIG before conversion or if you keep the current Ariba Cloud Integration solution CI-x</li>
    <li>Customizing / Configuration: If you migrate to add-on CIG, perform prerequisites for migrating transactions to the add-on</li>
    <li>Software Upgrade / Maintenance: Download and install the required software components for CIG (add-on).</li>
    <li>Custom Code Adaption: Perform migration of enhancements (SAP Ariba BAdI).</li>
    <li>Technical System Configuration: Perform post-migration tasks.</li>
    <li>Customizing / Configuration: Perform the required configuration and prerequisites for migrating transactions to CIG.</li>
    <li>Data migration: Perform migration of data and configuration.</li>
</ul>
<p><strong>During conversion project:</strong></p>
<ul>
    <li>Miscellaneous: If you keep your Ariba Cloud Integration version CI-X, update TADIR before conversion or upgrade to save the delivered software and revoke changes again after conversion or upgrade as described in SAP note 2402191</li>
</ul>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2402191">2402191 - Ariba Cloud Integration objects are deleted during upgrade to S/4 HANA or ECC system</a></p>