<h3>SI11: IS_DIMP_AUT</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2633572">2633572 - S4TWL - Handling Unit Management - Obsolete Transactions</a></strong></p>
<p><strong>Description:</strong></p>
<p>1. The usage of transaction COWBPACK is no longer allowed; error message VHUAP 037 is raised indicating that this transaction is obsolete and transactions COP1 or COP2 should be used instead.</p>
<p>2. The usage of transaction HUP1 is no longer allowed; error message VHUAP 039 is raised indicating that this transaction is obsolete and transactions MFP1 or MFP2 should be used instead.</p>
<p>Both transactions COWBPACK and HUP1 have been set to obsolete already from SAP R/3 4.6B and therefore also in SAP ERP by the industry solution for automotive as part of industry solutions for Discrete Industries and Mill Products. Only customers who did not install or activate these industry solutions could still use these transactions in SAP ERP. As the industry solutions for Discrete Industries and Mill Products are always active in SAP S/4HANA, these replacements now affect also customers who are doing a system conversion from SAP ERP without these industry solutions activated before.</p>
<p><strong>Business Process Impact:</strong></p>
<p>During a system conversion to SAP S/4HANA, customers using obsolete transactions COWBPACK and HUP1 for Handling Unit Management need to switch to the recommended transactions (COP1, COP2, MFP1, MFP2, etc.) to handle the required packing functions. This impacts processes related to creating handling units in both shop-floor and repetitive manufacturing scenarios. Additionally, the handling unit statuses are predefined in the packing transaction profile and are no longer editable in Customizing.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Change the business process as follows:<br/>Switch to the transactions recommended as replacement for the obsolete transactions. Use transaction COP21 (or any other COP* transaction mentioned above) instead of transaction COWBPACK and/or use transaction MFP21 (or any other MFP* transaction mentioned above) instead of transaction HUP1.</p>
<p>No further configuration for automatic packing is needed if you just use transaction COP21 (or COP22, COP23, COP24, COPP2) instead of transaction COWBPACK, as both use packing transaction profile 0010.<br/>No further configuration for automatic packing is needed if you just use transaction MFP21  (or MFP22, MFP23, MFP24, MFPP2) instead of transaction HUP1, as both use packing transaction profile 0008.</p>
<p>You need to check the assignment of HU status before the conversion to ensure a consistent behaviour afterwards for the packing transaction profiles 0008, 0010, 0017 and 0018, as described above.<br/>Only in case you would like to create planned handling units instead with transactions COP1, COP11, COP12, COP13, COP14 and COPP1, you should check packing transaction profile 0015 and adapt it, if needed.<br/>Only in case you would like to create planned handling units instead with transactions MFP1, MFP11, MFP12, MFP13, MFP14 and MFPP1, you should check packing transaction profile 0014 and adapt it, if needed.<br/>IMG: Logistics - General &gt; Handling Unit Management &gt; Automatic Packing &gt; Maintain Packing Transaction Profile</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/362626">362626 - HUP1 and COWBPACK are obsolet and replaced by new transact.</a></p>