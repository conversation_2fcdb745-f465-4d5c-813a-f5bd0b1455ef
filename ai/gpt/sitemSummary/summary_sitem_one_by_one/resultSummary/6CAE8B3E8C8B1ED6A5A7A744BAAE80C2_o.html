<h3>SI6: Logistics_MM-IM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2337383">2337383 - 2337383 - S4TWL - Material Valuation Data Model Simplification in S/4HANA 1610 and Higher</a></strong></p>
<p><strong>Description:</strong></p>
<p>
You are doing an upgrade from SAP S/4HANA 1511 to SAP S/4HANA 1610. The following SAP S/4HANA Transition Worklist item is applicable in this case.
If you upgrade from SAP S/4HANA 1511 to SAP S/4HANA 1610, the Material Ledger (ML) functionalities will be changed for simplification.
The impacts on the inventory valuation tables xBEW(H) - tables: EBEW, EBEWH, MBEW, MBEWH, OBEW, OBEWH, QBEW, QBEWH - are described in the following:
The transactional fields LBKUM, SALK3 and VKSAL will be retrieved from the Universal Journal Entry Line Items table (ACDOCA) with on-the-fly aggregation. And the transactional field SALKV will be retrieved from the Material Ledger table.
For compatibility reasons there are Core Data Service (CDS) Views assigned as proxy objects to all those tables, ensuring that each read access to one of the mentioned tables still returns the data as before. The CDS views consist of database joins in order to retrieve both master data from the original xBEW(H) table and transactional data from Universal Journal Entry Line Items table and Material Ledger table.
Hence all customer coding, reading data from those tables, will work as before because each read access to one of the tables will get redirected in the database interface layer of NetWeaver to the assigned CDS view. Write accesses to those tables have to be adjusted if transactional fields are affected.
One impact of the simplified MM-IM Inventory Valuation data model does exist if there are customer appends on the mentioned tables. The NetWeaver redirect capability requires that the database table and the assigned proxy view are compatible in the structure with respect to the number of fields and their type. Thus, if there is an append on one of the above mentioned tables, the assigned DDL source of the CDS proxy view must be made compatible.
Another impact of the simplified inventory valuation data model is a performance decrease of database read operations on the above mentioned tables. A data fetch on one of the mentioned tables in S/4HANA is slower than in SAP ERP 6.0 due to JOIN operations and on-the-fly aggregation. Hence performance-critical customer coding may need to be adjusted to improve performance.
</p>
<p><strong>Business Process Impact:</strong></p>
<p>
If customers are not already using the material ledger, it will be activated during the conversion process. In MM02 and MR21 material prices can now be maintained in multiple currencies. In Financials the inventory account balances are calculated separately for each currency and result therefore in a cleaner and more consistent valuation in other currencies than the local currency.
</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
Adjust custom code according to ATC check results and pre-check findings described in note 2197392
</p>
<p><strong>Reference Notes:</strong> 
<a href="https://launchpad.support.sap.com/#/notes/2337368">2337368 - Inventory Valuation (part of Materials Management - Inventory Management) : Change of data model in S/4HANA 1610</a>,
<a href="https://launchpad.support.sap.com/#/notes/2267834">2267834 - S4TWL - Material Ledger Obligatory for Material Valuation</a>,
<a href="https://launchpad.support.sap.com/#/notes/2194618">2194618 - S4TC SAP_APPL - Checks for MM-IM</a>,
<a href="https://launchpad.support.sap.com/#/notes/1804812">1804812 - MB transactions: Limited maintenance/decommissioning</a>,
<a href="https://launchpad.support.sap.com/#/notes/2129306">2129306 - Check Customizing Settings Prior to Upgrade to S/4HANA Finance or S/4HANA</a>
</p>