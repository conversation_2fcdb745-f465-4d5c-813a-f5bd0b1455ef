<h3>SI7: GSFIN_Insurance_RU</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2978033">2978033 - S4TWL - RU insurance localization - deprecation of package /GSINS/RU</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion from SAP ERP to SAP S/4HANA or an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this Note. The following SAP S/4HANA Simplification Item is applicable in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Package /GSINS/RU and its contents become obsolete because of no customer usage. Objects stored in the package and corresponding subpackages are removed including their references from other objects. Insurance localization for Russia stored in the package /GSINS/RU and its subpackages is not being used and has been deprecated in S/4HANA OP2021 and became obsolete in all the supported lower versions. For the transition period of 2 years, the package and its subpackages have been moved to the SW component 'S4DEPREC' if the restoration of the functionality is needed.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>In general, SAP recommends that you install a solution by applying a Support Package. You can find more information about the Note Assistant in SAP Service Marketplace, under <a href="https://service.sap.com/note-assistant" target="_blank">service.sap.com/note-assistant</a>. However, if you need to install a solution earlier, use the Note Assistant to implement and follow these steps:<br /><strong>IMPORTANT:</strong><br />To complete the installation procedure for <strong>S4TWL - RU insurance localization - deprecation of package /GSINS/RU</strong> you have to implement the automatic correction instructions from this Note 2978033.<br />Remember, that you cannot use your own coding/functionality anymore which is based on the removed coding/functionality, explained in this Note 2978033.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2289424">2289424 - S4TWL - SAP S/4HANA and SAP Business Warehouse Content - BI_CONT / BW4CONT</a></p>