<h3>SI5: Logistics_MM-IM</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2319579">2319579 - S4TWL - Performance optimizations within Material Document Processing - lock behavior and stock underrun protection</a></strong></p>
<p><strong>Description:</strong></p>
<p>With the introduction of the new MM-IM data model in SAP S/4HANA OP1511, there are opportunities and drawbacks. Opportunities include new process modes where material document processing can be executed in parallel from a database perspective, though still requiring logical locks for consistency at the business level. There are different lock strategies such as early exclusive quantity lock and late exclusive quantity lock, with further options to increase throughput in S/4HANA OP1610 and CE1608 like setting no exclusive lock at all. The on-the-fly calculation model in the new MM-IM data model improves upon older aggregated data storage.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The new lock strategies impact how material documents are processed and ensure consistency in material master data and stock availability. However, these strategies can lead to differences in process performance in scenarios with high parallel processing. Custom modifications and Logistics Information System (LIS) integration with late lock strategies could lead to system state inconsistencies and require redesigning process structures or interfaces to maintain performance and data consistency.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>
  <ul>
    <li><strong>Customizing / Configuration:</strong> Set late lock as needed.</li>
    <li><strong>Business Decision:</strong> A mandatory decision to proceed with the necessary lock strategies during the conversion project.</li>
    <li><strong>Custom Code Adaption:</strong> Conditional removal of modifications to conform to the new data model.</li>
    <li><strong>Process Design / Blueprint:</strong> Define replacements for LIS if opting for late lock strategies.</li>
    <li><strong>New developments:</strong> Implement the necessary replacements for LIS in scenarios with late lock usage.</li>
    <li><strong>Interface Adaption:</strong> Adjust IDOC interface accordingly when using late lock strategies.</li>
  </ul>
</p>
<p><strong>Reference Notes:</strong></p>
<ul>
  <li><a href="https://me.sap.com/notes/2206980">2206980 - Material Inventory Management: change of data model in S/4HANA</a></li>
  <li><a href="https://me.sap.com/notes/2242679">2242679 - Redirect inconsistency - Proxy Substitution</a></li>
  <li><a href="https://me.sap.com/notes/2194618">2194618 - S4TC SAP_APPL - Checks for MM-IM</a></li>
  <li><a href="https://me.sap.com/notes/2240878">2240878 - MM-IM: Add customer include CI_COBL to MATDOC</a></li>
  <li><a href="https://me.sap.com/notes/28022">28022 - Customer system: Where-used list for SAP Objects</a></li>
  <li><a href="https://me.sap.com/notes/2197392">2197392 - Resolve findings of core ERP MM-IM S/4HANA pre checks</a></li>
  <li><a href="https://me.sap.com/notes/2378796">2378796 - Material classification: Change in data model in SAP S/4HANA 1610</a></li>
  <li><a href="https://me.sap.com/notes/2267835">2267835 - S4TWL - Material Valuation - Statistical moving average price</a></li>
  <li><a href="https://me.sap.com/notes/2338387">2338387 - S4TWL - Goods movements without exclusive locking by material valuation</a></li>
  <li><a href="https://me.sap.com/notes/2277568">2277568 - Activation of locking behavior "no exclusively locking" deprecates statistical moving average price</a></li>
</ul>