<h3>SI62: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2640859">2640859 - S4TWL - Conversion of Season Master Data to S/4HANA 1809</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are using season master data and you are doing a system conversion from SAP ERP (SAP Fashion Management, or SAP Merchandising) to SAP S/4HANA 1809 (S4CORE 103) or an upgrade from S/4HANA 1610 (S4CORE 101), or S/4HANA 1709 (S4CORE 102) to SAP S/4HANA 1809 (S4CORE 103). The season master data model is simplified in the SAP S/4HANA 1809 which requires the existing season master data in respective tables to be converted accordingly. This simplification means that tables like FSH_SEASONS, FSH_COLLECTIONS, FSH_THEMES, etc., will be replaced by simplified season master data tables RFM_SEASONS and RFM_SEASONS_T.</p>
<p><strong>Business Process Impact:</strong></p>
<p>As part of the conversion/upgrade of the system to SAP S/4HANA 1809 (S4CORE 103), the season master data tables are filled automatically according to the conversion steps provided. This involves moving data from various existing tables to new simplified tables. The conversion tasks will be conducted as follows:
<ul>
<li>For SAP Fashion Management, S/4HANA 1610 (S4CORE 101), or S/4HANA 1709 (S4CORE 102):
  <ul>
    <li>FSH_SEASONS, FSH_COLLECTIONS, FSH_THEMES -> RFM_SEASONS</li>
    <li>FSH_SEASONS_T, FSH_COLLECTIONS_T, FSH_THEMES_T -> RFM_SEASONS_T</li>
  </ul>
</li>
<li>For SAP ERP (Retail):
  <ul>
    <li>First Step:
      <ul>
        <li>TWSAI -> FSH_SEASONS</li>
        <li>T6WST -> FSH_SEASONS_T</li>
        <li>TWSAI -> FSH_COLLECTIONS</li>
        <li>T6WSAT -> FSH_COLLECTIONS_T</li>
      </ul>
    </li>
    <li>Second Step:
      <ul>
        <li>FSH_SEASONS, FSH_COLLECTIONS, FSH_THEMES -> RFM_SEASONS</li>
        <li>FSH_SEASONS_T, FSH_COLLECTIONS_T, FSH_THEMES_T -> RFM_SEASONS_T</li>
      </ul>
    </li>
  </ul>
</li>
</ul>
</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>During the conversion/upgrade, the following reports are executed automatically:
<ul>
<li>For SAP Fashion Management, S/4HANA 1610 (S4CORE 101), or S/4HANA 1709 (S4CORE 102):
  <ul>
    <li>R_FSH_TO_RFM_SEASONS_MD_XPRA</li>
  </ul>
</li>
<li>For SAP ERP (Retail):
  <ul>
    <li>First Step:
      <ul>
        <li>R_FSH_S4_SEASONS_MD_MIG</li>
      </ul>
    </li>
    <li>Second Step:
      <ul>
        <li>R_FSH_TO_RFM_SEASONS_MD_XPRA</li>
      </ul>
    </li>
  </ul>
</li>
</ul>
Additional mandatory activities during the conversion project include:
<ul>
<li>Custom Code Adaption: Ensure custom codes are adapted for the new data model.</li>
<li>Data Migration: Reports are executed to automatically fill the new tables during the conversion/upgrade.</li>
</ul>
</p>
<p><strong>Reference Notes:</strong> 
<a href="https://launchpad.support.sap.com/#/notes/2516743">2516743 - S4TWL - Fashion Functionality</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2566648">2566648 - Delete FSH_CO09 user settings</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2532764">2532764 - SAP S/4HANA 1709 Feature Package Stack 00: Additional Release Information for Retail and Fashion</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2467506">2467506 - S4TWL -  Order Allocation Run (ARun) in Fashion Management</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2481829">2481829 - S4TWL - Fashion Season Conversion (SAP ERP to SAP S/4HANA 1709)</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2590824">2590824 - Post upgrade clean up of obsolete objects generated by POGT</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2535093">2535093 - S4TWL - Season Active in Inventory Management</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2481891">2481891 - S4TWL - Season Conversion (SAP S/4HANA 1610 to 1709)</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2522971">2522971 - S4TWL - Segment Field Length Extension</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2465556">2465556 - S4TWL - Master Data for Order Allocation Run (ARun) in Fashion Management</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2465612">2465612 - S4TWL - Segmentation</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2468952">2468952 - S4TWL - Fashion Contract (Requirement Relevant Contracts) in SAP Fashion Management</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2727960">2727960 - S4TWL - Fashion Purchase Order Generation Tool</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2627238">2627238 - S4TWL - Fashion changes in 1709 FPS02</a>, 
<a href="https://launchpad.support.sap.com/#/notes/2385984">2385984 - S4TWL - SAP S/4HANA Retail for Merchandise Management, SAP S/4HANA for Fashion and Vertical Business - Simplification Items</a>
</p>