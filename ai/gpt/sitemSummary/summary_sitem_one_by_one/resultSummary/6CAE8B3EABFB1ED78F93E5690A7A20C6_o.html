<h3>SI01: OTAS_DISPATCH</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2490435">2490435 - S4TWL - OTAS - Integrated Dispatch Management (IDM)</a></strong></p>
<p><strong>Description:</strong></p>
<p>The OTAS application Integrated Dispatch Management (IDM) provides various functions with which you can plan and monitor your outbound delivery tours.</p>
<p><strong>Business Process Impact:</strong></p>
<p>IDM is discontinued in SAP S/4HANA, so all its program and DDIC-objects as well as all data are lost after a migration to SAP S/4HANA. Depending on the release of S4SCSD you are using or planning to use, you may or may not have a functional alternative. 
<ul>
	<li>If you are planning to use S4SCSD Release 1.0, you will not have any functionality from IDM, nor does any successor application with such functionality exist in S4SCSD 1.0.</li>
	<li>If you are planning to use S4SCSD Release 2.0, you will be able to use a successor application called S4IDM, which works with SAPUI5 and ABAP on HANA. However, you cannot migrate anything from IDM (from the Business Suite) to S4IDM in S4SCSD 2.0 running on S/4HANA.</li>
</ul>
</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>1. IDM is discontinued in SAP S/4HANA, so all its program and DDIC objects as well as all data are lost after a migration to SAP S/4HANA.<br/> 
2. If you want to continue using functionalities provided for the Business Suite by IDM also in SAP S/4HANA, you need to consider switching either to S4SCSD 2.0, where a successor application exists, or to other SAP modules/applications.<br/> 
3. In case of switching to another module, tool, functionality, or application (like S4IDM in S4SCSD 2.0), you need to organize knowledge transfer to all users working with IDM as their pattern of work will change considerably after such a switch. Users will have to use new or different applications for creating, changing, displaying, processing, planning, and monitoring your outbound delivery tours.
</p>