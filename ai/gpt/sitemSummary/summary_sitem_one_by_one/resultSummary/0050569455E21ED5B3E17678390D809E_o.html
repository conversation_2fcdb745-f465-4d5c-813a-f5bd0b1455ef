<h3>SI1: CS_CIC</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2267412">2267412 - S4TWL - Customer Interaction Center (CIC)</a></strong></p>
<p><strong>Description:</strong></p>
<p>Transaction CIC0 provides a basic Call Center help desk application which is technically outdated and not available within SAP S/4HANA.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Business processes that require a help desk / call center integration cannot be executed in the Customer Interaction Center anymore. Instead, they have to be integrated into the interaction center/help desk applications offered by SAP S/4HANA, SAP CRM, or SAP C/4HANA.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Business processes that require a help desk / call center integration should be integrated into the interaction center/help desk applications offered by SAP S/4HANA, SAP CRM, or SAP C/4HANA.</p>
<p>
    <strong>Business Decision:</strong> Transaction CIC0 is unavailable in SAP S/4HANA. Alternatives are CRM on Premise, Cloud 4 Service, and SAP Hybris.</p>
<p><strong>Process Design / Blueprint:</strong> Redesign your Call Center/help desk process using capabilities of CRM on Premise, Cloud 4 Service, and SAP Hybris.</p>
<p><strong>Custom Code Adaption:</strong> If you have Custom Code using transaction CIC0, adjust it according to <a href="https://me.sap.com/notes/2229726">SAP Note 2229726</a>.</p>
<p><strong>User Training:</strong> Conduct user training during or after the conversion project.</p>
<p><strong>Reference Notes:</strong> 
<a href="https://me.sap.com/notes/2229726">2229726 - SAP S/4 HANA Simplification Item: Deprecation of Customer Interaction Center (Transaction CIC0) in CS-CI</a>, 
<a href="https://me.sap.com/notes/2190420">2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code</a>
</p>