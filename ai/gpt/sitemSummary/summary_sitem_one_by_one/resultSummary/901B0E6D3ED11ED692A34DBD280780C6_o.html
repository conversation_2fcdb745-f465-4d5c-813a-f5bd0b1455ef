<h3>SI19: SD-GDS</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2340264">2340264 - S4TWL - Global Data Synchronization (GDS)</a></strong></p>
<p><strong>Description:</strong></p>
<p>In SAP S/4HANA, GDS only works if the latest Open Catalog Interface (either enterprise service or IDoc PRICECATALOGUE) is used. IDoc PRICAT is not supported anymore. Also, transactions PRICAT* (PRICAT, PRICATCUS1, PRICATCUS2, PRICATCUS3, PRICATCUS6, PRICATCUS7, PRICATLOG, PRICATLOGOUT) are not supported anymore. Transactions W_SYNC or W_PRICAT_MAINTAIN need to be used.</p>
<p><strong>Business Process Impact:</strong></p>
<p>It is required that the latest Open Catalog Interface is used.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Execute process as described in referenced note 2326511.</p>
<ul>
<li>Interface Adaption: IDoc PRICAT is not supported anymore. It is required that the latest Open Catalog Interface is used. (Mandatory)</li>
<li>Data migration: In case the preliminary checks return an error stating that the Global Data Synchronization (GDS) application still contains open inbound price catalog messages in an old format that S/4 no longer supports please carry out the steps outlined in SAP Note 2326511. (Conditional)</li>
<li>Custom Code Adaption: (Mandatory)</li>
<li>User Training: Users must be aware of changes due to interface adaption (Mandatory)</li>
</ul>
<p><strong>Reference Notes:</strong></p>
<ul>
<li><a href="https://me.sap.com/notes/2358356">2358356 - S4TWL - GDS simplified inbound price catalog interface</a></li>
<li><a href="https://me.sap.com/notes/2325526">2325526 - Global data synchronization precheck for migration to S/4</a></li>
<li><a href="https://me.sap.com/notes/2326511">2326511 - S4 transformation: GDS migration to simplified price catalog inbound interface</a></li>
<li><a href="https://me.sap.com/notes/2326521">2326521 - S4TC EA_RETAIL master check class for S/4 system transformation checks</a></li>
</ul>