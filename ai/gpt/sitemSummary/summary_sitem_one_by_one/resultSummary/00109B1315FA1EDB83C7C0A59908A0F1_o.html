<h3>SI6_FIN_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/3006586">3006586 - S4TWL - Cost of Sales Ledger</a></strong></p>
<p><strong>Description:</strong></p>
<p>The usage of the cost of sales ledger is part of the SAP S/4HANA compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and its expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324, cost of sales ledger can be found under the ID 430. This means that you need to migrate from cost of sales ledger to its designated alternative functionality cost of sales accounting (CSA) reporting in SAP S/4HANA based on the Universal Journal before the expiry of the compatibility pack license. Also refer to the SAP S/4HANA Feature Scope Description section <a href="https://help.sap.com/viewer/029944e4a3b74446a9099e8971c752b9/2020/en-US/deb673565181c159e10000000a441470.html?q=cost%20of%20sales%20ledger" target="_blank">Cost of Sales Ledger</a>.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The cost of sales ledger is used to provide a type of profit and loss statement that matches sales revenues to the costs of the products sold. It is based on functional areas. In SAP ERP classic General Ledger Accounting, functional areas are always managed and reported only in FI Special Ledger. Table GLFUNCT, ledger 0F, contains the totals records for cost of sales accounting. In SAP S/4HANA the contents of the cost of sales ledger and all special ledgers created in table GLFUNCT are included in the Universal Journal (table ACDOCA).</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>To use CSA reporting in SAP S/4HANA based on the Universal Journal, you need to fulfill several prerequisites. Refer to SAP note <a href="https://launchpad.support.sap.com/#/notes/932272" target="_blank">932272</a> for a detailed explanation. The main points to consider for SAP S/4HANA are as follows:</p>
<ul>
<li>If you have used classic General Ledger Accounting or General Ledger Accounting (new) without CSA in SAP ERP, CSA reporting cannot be used in S/4 for the years in question because the system cannot derive functional areas properly. Thus, after a conversion to SAP S/4HANA, CSA reporting would initially still need to be done in the FI-SL ledger 0F.</li>
<li>Once the correct derivation of a functional area has been set-up and complete and correct account assignments in Accounting can be ensured, CSA reporting based on the Universal Journal can take place. However, SAP recommends that you still use CSA reporting in FI-SL for a transition period of at least one year.</li>
<li>To enable the complete assignment of allocations and postings from CO in SAP S/4HANA, refer to SAP Note <a href="https://launchpad.support.sap.com/#/notes/2680760" target="_blank">2680760</a>.</li>
<li>Decide when to switch to Cost of Sales Accounting based on Universal Journal.</li>
<li>Set-up correct derivation of a functional area at least one year ahead of switch to Cost of Sales Accounting (CSA) in SAP S/4HANA as described in SAP note 932272.</li>
</ul>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/932272">932272 - FAQ: CSA reporting in G/L / discontinuation of FI-SL ledger 0F</a>, <a href="https://launchpad.support.sap.com/#/notes/2680760">2680760 - SAP S/4HANA: Functional area derivation for company-code clearing line</a></p>