<h3>BW109: DataStore Objects (advanced)</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2487023">2487023 - 2487023 - BW4SL & BWbridgeSL - DataStore Object (advanced)</a></strong></p>
<p><strong>Description:</strong></p>
<p>You use DataStore Objects (advanced) with Data Transfer Processes created before SAP BW release 7.5 SP 4 that store request IDs as meta data information and you want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>
<p><strong>Business Process Impact:</strong></p>
<p>DataStore objects (advanced) that include request IDs (old request management) in the meta data are not supported anymore with SAP BW/4HANA and SAP Datasphere, SAP BW bridge. These objects must be processed using the Transfer Tool to clean-up the meta data.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Run program RS_B4HANA_RC to determine which objects are available in or can be converted to SAP BW/4HANA or SAP Datasphere, SAP BW bridge. Clean-up the meta data for DataStore objects created before SAP BW 7.5 SP 4 using the Transfer Tool.</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2421930">2421930 - Simplification List for SAP BW/4HANA</a>, <a href="https://me.sap.com/notes/2347382">2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)</a>, <a href="https://me.sap.com/notes/2383530">2383530 - Conversion from SAP BW to SAP BW/4HANA</a></p>