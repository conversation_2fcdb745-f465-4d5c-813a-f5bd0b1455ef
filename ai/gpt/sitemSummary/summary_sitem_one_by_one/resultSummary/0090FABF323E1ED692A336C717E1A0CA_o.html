<h3>SI2: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2339010">2339010 - S4TWL - Generic Article Harmonization</a></strong></p>
<p><strong>Description:</strong></p>
<p>This note addresses the conversion of SAP ERP 6.0 Retail/Fashion systems to SAP S/4HANA using existing generic articles. It discusses how the storage of characteristic valuations for variants of a generic article and a configurable material changes with S/4HANA.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The transition to S/4HANA means that generic articles will be managed through configuration classes rather than merchandise categories or characteristics profiles, eliminating restrictions associated with re-classifications. The data model change also means certain functions (e.g., reports for assigning single articles to generic articles, converting variants, etc.) will no longer be available.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>The data migration process for transitioning to S/4HANA is divided into three phases: manual execution of Pre-Migration reports in the SAP ERP for Retail system, an automated migration process during downtime, and manual execution of a Post-Migration report in the SAP S/4HANA system.</p>
<ul>
<li>In the 1st phase, execute Pre-Migration reports manually in the SAP ERP for Retail system across all clients (refer to SAP Note 2331707 for the reports).</li>
<li>The 2nd phase involves an automatic migration during downtime.</li>
<li>In the 3rd phase, execute the Post-Migration report manually in the converted SAP S/4HANA system across all clients (refer to SAP Note 2350650 for details).</li>
<li>Custom code adaptation is required to align with SAP S/4HANA’s new functionality.</li>
</ul>
<p><strong>Reference Notes:</strong></p>
<ul>
<li><a href="https://launchpad.support.sap.com/#/notes/2331707">2331707 - Pre-migration of Retail generic articles and variants to configurable materials</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2350650">2350650 - Post-migration cleanup of Retail generic articles and variants data</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2843173">2843173 - Report to convert standard material to retail single article.</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2381429">2381429 - S/4HANA data model changes In Retail Generic Article</a>: Additional notes 2352831 and 2356142</li>
<li><a href="https://launchpad.support.sap.com/#/notes/2453010">2453010 - Migration of variants with multiple characteristic default values</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2798896">2798896 - Management of work processes for parallelized migration of generic articles and variants</a></li>
<li><a href="https://launchpad.support.sap.com/#/notes/2796457">2796457 - Increase in the work processes that can be used in parallel for the migration of generic articles and variants</a></li>
</ul>