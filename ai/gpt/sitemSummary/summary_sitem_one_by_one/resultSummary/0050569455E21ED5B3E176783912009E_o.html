<h3>SI5: Logistics_PLM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267845">2267845 - S4TWL - cFolders</a></strong></p>
<p><strong>Description:</strong></p>
<p>With SAP S/4HANA, on-premise edition 1511 cFolders interfaces are not available. SAP recommends evaluating SAP Enterprise Product Development as a substitute for selected collaboration scenarios. No screen enhancements are known.</p>
<p><strong>Business Process Impact:</strong></p>
<p>It is no longer possible to exchange data (Documents, Materials, Bill of Materials, iPPE Objects) between PLM and cFolders. The following transactions are not available in SAP S/4HANA on-premise edition 1511: CFI01 – Importing Documents, CFE01 – Exporting Documents, CFI02 – Importing Documents, Materials, and Bills of Material, CFE02 – Exporting Documents, Materials, and Bills of Material, CFI03 – Importing Documents, Materials, Bills of Material, and iPPE Objects, CFE03 – Exporting Documents, Materials, Bills of Material, and iPPE Objects, CFC01 – Customizing for cFolders backend integration, and CFC02 – Customizing for cFolders backend integration, BOMs.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Customers have to establish data exchange processes with partners using other techniques like SAP Enterprise Product Development. Mandatory activities include:</p>
<ul>
    <li><strong>Business Decision:</strong> With SAP S/4HANA, on-premise edition 1511 the current cFolder interfaces are not available. SAP recommends evaluating SAP Document Center or SAP Intelligent Product Design as a substitute for selected collaboration scenarios. Decision to use SAP Document Center or IPD.</li>
    <li><strong>Process Design / Blueprint:</strong> Depending on business decision, either SAP Document Center or SAP IPD solution has to be used instead of cFolders.</li>
    <li><strong>User Training:</strong> Depending on business decision, either SAP Document Center or SAP IPD solution has to be used instead of cFolders.</li>
    <li><strong>Custom Code Adaption:</strong> cFolders related custom code need to be adjusted.</li>
</ul>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2413806">2413806 - Support of Java Browser-Plugins in cFolders</a>, <a href="https://launchpad.support.sap.com/#/notes/2224778">2224778 - SAP S/4HANA Simplification Item: PLM-cFolders-Integration</a>, <a href="https://launchpad.support.sap.com/#/notes/2358921">2358921 - S4TWL - Integration to SAP cFolders in SAP Portfolio and Project Management for SAP S/4HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2348430">2348430 - S4TWL - cFolders not available anymore in SAP Portfolio and Project Management for SAP S/4HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2283330">2283330 - SAP browser information about the Java Plugin roadmap</a></p>