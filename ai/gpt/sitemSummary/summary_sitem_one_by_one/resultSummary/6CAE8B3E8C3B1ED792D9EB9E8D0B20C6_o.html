<h3>BW950: Other Objects</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2470123">2470123 - BW4SL & BWbridgeSL - Other Unavailable Objects</a></strong></p>
<p><strong>Description:</strong></p>
<p>The following object types are not available in SAP BW/4HANA or SAP Datasphere, SAP BW bridge:</p>
<ul>
<li>Analytical Service</li>
<li>Analytical Service Types</li>
<li>Data Access Service</li>
<li>Data Mining Model</li>
<li>Inspection Plan</li>
<li>KPI Catalog Entry</li>
<li>KPI Definition</li>
<li>Hierarchy Object</li>
<li>Enhancement Object</li>
</ul>
<p>These object types are already obsolete in SAP BW.</p>
<p>The following object types are related to objects not available in SAP BW/4HANA or SAP Datasphere, SAP BW bridge:</p>
<ul>
<li>Call Package</li>
<li>Transfer InfoSource master data</li>
<li>Intelligent Services</li>
<li>Intelligent Services Classes</li>
<li>Operational Data Provider (Enterprise Search-Based)</li>
<li>BPC Application</li>
<li>BPC Web documents - LiveReport</li>
<li>BPC Member Access Profile</li>
<li>BPC Appset</li>
<li>BPC Web document category</li>
<li>BPC Web document folder</li>
<li>BPC Web document - workspace</li>
<li>BPS Excel Templates</li>
<li>BPS STS Documents</li>
<li>BI Conversion Services</li>
</ul>
<p><strong>Business Process Impact:</strong></p>
<p>The impacted businesses need to transition their usage of the listed object types to supported alternatives in SAP BW/4HANA or SAP Datasphere, SAP BW bridge, as these objects will be obsolete and removed in the new environment.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>1. Run program RS_B4HANA_RC to determine which objects are available in or can be converted to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.<br>2. Objects will be automatically deleted when switching to "B4H mode". If any functionality provided by these objects is needed, replacement with supported alternatives must be implemented before making the switch.<br>3. Ensure no reliance on these obsolete or unsupported objects in future implementations.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2421930">2421930 - Simplification List for SAP BW/4HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2347382">2347382 - SAP BW/4HANA – General Information</a>, <a href="https://launchpad.support.sap.com/#/notes/2383530">2383530 - Conversion from SAP BW to SAP BW/4HANA</a>