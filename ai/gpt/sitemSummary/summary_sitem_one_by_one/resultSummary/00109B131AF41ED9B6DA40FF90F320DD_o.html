<h3>SI8: Logistics_WM</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2889638">2889638 - S4TWL - Value Added Services</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p>The functionality of Value added Services in Warehouse Management (LE-WM) is not the target architecture anymore within SAP S/4HANA, on-premise edition. The (already available) alternative functionality is Value added Services in Extended Warehouse Management (SAP EWM).</p>
<p><strong>Business Process Impact:</strong></p>
<p>No immediate influence on business processes expected related to Value added Services. The functionality Value added Services in Warehouse Management (LE-WM) is still available within the SAP S/4HANA architecture as part of the compatibility packages. All related functionalities can be used in an unchanged manner for a limited period of time under the conditions of the compatibility packages (see SAP note 2269324 for more information).</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>As part of your SAP S/4HANA system conversion or upgrade project, you need to decide if you want to continue using Value added Services in Warehouse Management (LE-WM) for a limited period under the conditions of the compatibility packages (see SAP note 2269324 for more information), or if you want to migrate to Extended Warehouse Management (SAP EWM). If you decide to migrate to Extended Warehouse Management (SAP EWM), plan accordingly for this in your project. Please also check the Release information and restrictions for EWM in SAP S/4HANA of the S/4HANA Release you will implement. Once you migrate to Extended Warehouse Management (SAP EWM), but latest with the expiry of the compatibility scope license (see SAP note 2269324 for more information), you need to remove all usages of Value Added Services development objects delivered by SAP from your custom code, as these are no longer supported after the expiry of the compatibility scope license. Such usages are shown by the SAP S/4HANA specific custom code checks in ABAP Test Cockpit (see SAP note 2241080).</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2882809">2882809 - Scope Compliance Check for Stock Room Management</a>, <a href="https://me.sap.com/notes/2269324">2269324 - Compatibility Scope Matrix for SAP S/4HANA</a></p>