<h3>SI33: Logistics_PLM_AVC-SalesOrderSET</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/3190519">3190519 - S4TWL - Behavior change for AVC Sales Order (SET) scenario configuration models</a></strong></p>
<p><strong>Description:</strong></p>
<p>In the future, it shall be possible to safely reuse planned order/production order configuration models in bundles (configuration models with sales order (SET) configuration profiles) in advanced variant configuration (AVC).</p>
<p>The safe reuse is enabled by introducing a form of encapsulation for object dependencies from planned order/production order configuration models if they are used in bundles/SETs.</p>
<p><strong>Business Process Impact:</strong></p>
<p>This change impacts the way AVC supports PPO and SET profiles as configurable components in bundles/SETs. Previously, reusing PPO models in bundles caused issues with the interpretation of object dependencies, particularly those involving $parent and $root references and constraints across different classes. The new behavior introduced encapsulation for object dependencies, ensuring that they affect only the PPO model they belong to.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>1. Decide when to switch to the new behavior. By SAP S/4HANA 2023, this change is mandatory.</p>
<p>2. Check your sales order (SET) scenario configuration models and adjust them as needed:</p>
<ul>
<li>If you relate characteristics from the bundle with characteristics from the PPO model using $self, $parent, and $root, use the following idioms to replace dependencies:</li>
<ul>
<li>For value inheritance (both top-to-bottom and vice versa):</li>
<pre>OBJECTS: COMP IS_A (300) C_COMP, BUND IS_A (300) C_BUND.
CONDITION: PART_OF(COMP,BUND,'0020').
RESTRICTION: COMP.X = BUND.Y.</pre>
<li>For top-to-bottom inheritance only:</li>
<pre>OBJECTS: COMP IS_A (300) C_COMP, BUND IS_A (300) C_BUND.
CONDITION: PART_OF(COMP,BUND,'0020').
RESTRICTION: COMP.X = BUND.Y IF SPECIFIED BUND.Y.</pre>
<li>For keeping authorship (when inheriting a value as a default):</li>
<pre>OBJECTS: COMP IS_A (300) C_COMP, BUND IS_A (300) C_BUND.
CONDITION: PART_OF(COMP,BUND,'0020').
RESTRICTION: COMP.XHelper = BUND.Y.
Procedure: $self.X ?= $self.XHelper.</pre>
<li>Move any constraints meant to relate different items in a bundle to the bundle level instead of to the PPO items.</li>
<li>Selection conditions in the bundle model do not need adjusting as they are part of the bundle model, not the PPO model.</li>
</ul>
<p>3. Adjust your sales order (SET) scenario configuration models and switch to the new behavior before or during the conversion project.</p>