<h3>BW156: External Source Systems</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2441836">2441836 - BW4SL & BWbridgeSL - SAP Data Services and External System Source Systems</a></strong></p>
<p><strong>Description:</strong></p>
<p>You use source systems of type "SAP Data Services" or "External System (Partner ETL)" and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge. Run program RS_B4HANA_RC to determine which objects are supported with or can be converted to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Source systems of type "SAP Data Services" or "External System (Partner ETL)" are not available in SAP BW/4HANA. A new capability "Write Interface" for DataStore Objects allowing to push data into inbound queue tables of Staging DataStore Objects and Standard DataStore Objects replaces the push capability of PSA tables. For SAP Datasphere, SAP BW bridge, similar changes apply, with integration through the new (RFC) interface.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p><strong>For manual conversion:</strong></p>
<ul>
<li>Create a Staging or Standard DataStore Object with an existing Data Source as a template.</li>
<li>Ensure all data from the PSA table is distributed before conversion.</li>
<li>Enable "Write Interface" and activate the DataStore Object after conversion.</li>
</ul>
<p><strong>For semi-automated conversion:</strong></p>
<ul>
<li>Execute task list SAP_BW4_TRANSFER_INPLACE with the Web Service DataSource.</li>
<li>Add and activate "Write Interface enabled" property.</li>
</ul>
<p><strong>For remote- or shell-transfer:</strong></p>
<ul>
<li>Execute task list SAP_BW4_TRANSFER_REMOTE_PREPARE/SHELL with the SAP Data Services or External System DataSource.</li>
<li>Create and activate an ADSO with "Write Interface enabled" property.</li>
</ul>
<p><strong>Recommended alternative for BW/4HANA 1.0:</strong></p>
<p>SAP Data Services or external systems should write to a native SAP HANA database table and use a SAP HANA Source System for data integration.</p>
<p><strong>Reference Notes:</strong></p>
<ul>
<li>2421930 - <a href="https://launchpad.support.sap.com/#/notes/2421930" target="_blank">Simplification List for SAP BW/4HANA</a></li>
<li>2347382 - <a href="https://launchpad.support.sap.com/#/notes/2347382" target="_blank">SAP BW/4HANA – General Information</a></li>
<li>2383530 - <a href="https://launchpad.support.sap.com/#/notes/2383530" target="_blank">Conversion from SAP BW to SAP BW/4HANA</a></li>
</ul>