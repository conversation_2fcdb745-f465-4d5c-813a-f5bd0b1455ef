<h3>SI20: Logistics_PP</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2268120">2268120 - S4TWL - Digital Signature</a></strong></p>
<p><strong>Description:</strong></p>
<p>Digital signature legacy functionality is based on function modules of package CJ in SAP_APPL. The code needed for the legacy functionality is still available in SAP S/4HANA, on-premise edition 1610 (Functionality available in SAP S/4HANA on-premise edition 1610 delivery but not considered as future technology. Functional equivalent is available).</p>
<p>Since SAP_ABA 6.20 the successor functionality (class-based signature tool, package DS) is available. Implementations of digital signature functionality using class-based signature tool is easier to establish. Use the class-based signature tool instead of the legacy functionality.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No influence on business processes expected. Transaction DSAL is still available in SAP S/4HANA, on-premise edition 1610 but will be replaced by a successor functionality in a future release.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Check before the upgrade that all open signature processes are completed. This is generally relevant for all signature processes, independent of the signature functionality used (legacy or class-based signature tool).</p>
<p><strong>Activities:</strong></p>
<p>User Training (Optional, After conversion project)</p>
<p>Data cleanup / archiving (Optional, Before conversion project)</p>
<p>Customizing / Configuration (Conditional, During or after conversion project): Implementation of the new digitial signature tool</p>
<p><strong>Reference Notes:</strong></p>
<ul>
    <li><a href="https://launchpad.support.sap.com/#/notes/700495">700495 - Implementation of digital signature with help of signature tool</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/827417">827417 - Signature tool: Output of signed documents using DSAL</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/910238">910238 - Signature tool: Example program for implementation missing</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/1103666">1103666 - Signature tool: Revision of example program DSIG_BOOKING_EX</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/768700">768700 - Signature tool: Dump OBJECT_NOT_STRUCTURED during logging</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/1104510">1104510 - Signature tool: Problems w/ asynchronous signature processes</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/1139038">1139038 - Signature tool: Missing log display in SAP_ABA</a></li>
</ul>