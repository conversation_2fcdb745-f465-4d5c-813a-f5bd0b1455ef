<h3>SI38: Logistics_General</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2370148">2370148 - 2370148 - S4TWL - In-store food production integration</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p>In the retail grocery industry it can be relevant to manage processes involving a store's fresh items. The in-store food production integration enabled integration to an external In-Store Food Production (ISFP) software solution.</p>
<p><strong>Business Process Impact:</strong></p>
<p>In SAP S/4HANA, in-store food production integration is not available anymore.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>There are other communication scenarios in SAP S/4HANA to allow integration with non-SAP solutions. In case you reuse ABAP objects of packets WSFP_* in your custom code, please see attached note. This Simplification Item is relevant if in-store food production integration is used. This is the case if enterprise services StoreERPReplicationBulkRequest_Out, StoreGroupERPReplicationBulkRequest_Out, SupplierERPReplicationBulkRequest_Out, PhysicalInventoryCountERPRequest_Out, PhysicalInventoryCountERPCreateRequest_In, PhysicalInventoryCountERPConfirmation_In, GoodsMovementERPCreateRequest_In, GoodsMovementERPNotification_Out are used.</p>
<p><strong>Mandatory Actions:</strong></p>
<p>Interface Adaption: In case integration to third-party In-Store Food Production (ISFP) software solution is required, the integration will have to use other communication scenarios in SAP S/4HANA to allow integration with non-SAP solutions.</p>
<p><strong>Conditional Actions:</strong></p>
<p>Custom Code Adaption: If you reuse ABAP objects of packets WSFP_* in your custom code then the impacted customer code will have to be adopted.</p>
<p><strong>Reference Notes:</strong></p>
<p><a href="https://me.sap.com/notes/2383533">2383533 - Retail Deprecated Applications Relevance for Custom Code</a></p>