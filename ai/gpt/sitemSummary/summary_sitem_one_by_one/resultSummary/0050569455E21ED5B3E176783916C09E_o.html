<h3>SI16: Logistics_PP</h3>
<p><strong>Business Impact Note: <a href="https://me.sap.com/notes/2268112">2268112 - S4TWL - Release status for different RFC function modules revoked</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p>RFC function modules use dictionary structures to type exporting or importing parameters. Some of the fields in the dictionary structures have been extended (for example the material ID fields, which are usually named "MATERIAL" and typed with domain "MATNR“). It was not possible to keep the short version of these fields and add an additional longer version as the same dictionary structures are also used in very many internal interfaces. Therefore the released status of the following RFC function modules was revoked.</p>
<div class="table-responsive"><table border="0" cellpadding="0" cellspacing="0" class="TableGrid">
<tbody>
<tr>
<td width="168">
<p><strong>Function Module</strong></p>
</td>
<td width="160">
<p><strong>Structure</strong></p>
</td>
<td width="255">
<p><strong>Affected fields</strong></p>
</td>
</tr>
<tr>
<td width="168">
<p>COCF_SN_CREATE_EAM</p>
</td>
<td width="160">
<p>RIQS5</p>
</td>
<td width="255">
<p>BAUTL, MATNR, EMATNR, RM_MATNR</p>
</td>
</tr>
<tr>
<td width="168">
<p>COCF_SN_CREATE_PP</p>
</td>
<td width="160">
<p>RIQS5</p>
</td>
<td width="255">
<p>BAUTL, MATNR, EMATNR, RM_MATNR</p>
</td>
</tr>
<tr>
<td width="168">
<p>COCF_SN_GET_LIST_EAM</p>
</td>
<td width="160">
<p>COCF_S_SN_LIST_FLAT</p>
</td>
<td width="255">
<p>MATNR, EMATNR, RM_MATNR</p>
</td>
</tr>
<tr>
<td width="168">
<p>COCF_SN_GET_LIST_PP</p>
</td>
<td width="160">
<p>COCF_S_SN_LIST_FLAT</p>
</td>
<td width="255">
<p>MATNR, EMATNR, RM_MATNR</p>
</td>
</tr>
</tbody>
</table></div>
<p>&nbsp;</p>
<p><strong>Business Process Impact:</strong></p>
<p>No influence on business processes expected</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Check custom code</p>
<p>If you call the above mentioned RFC function modules from within custom code and if you do not use data elements typed with domain "MATNR" then you have to adjust the data types used in the custom code. See note 2199837</p>
<p><strong>Reference Notes:</strong> <a href="https://me.sap.com/notes/2199837">2199837 - Field length change in the interface of RFC function modules</a></p>