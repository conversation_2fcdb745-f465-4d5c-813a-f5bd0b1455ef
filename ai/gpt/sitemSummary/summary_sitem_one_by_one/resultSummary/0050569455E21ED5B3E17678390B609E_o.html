<h3>SI2: SD_PRIC</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267308">2267308 - S4TWL - Data Model Changes in SD Pricing</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA. Data model simplifications in Sales and Distribution (SD) pricing offer several benefits, such as direct access to pricing results for analytical purposes, extended field lengths for more flexible pricing, and a reduction in memory footprint. Table KONV has been replaced by PRCD_ELEMENTS for data persistence, but KONV still defines the structure within the application code. Various data elements and interfaces have been updated or obsoleted.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No impact on business processes is expected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Carefully follow the instructions in SAP Notes 2188695, 2189301, and 2220005.</p>
<ul>
<li>During conversion:
    <ul>
        <li>Custom Code Adaption: Ensure all necessary adjustments in your code to deal with the changed data model.</li>
        <li>Technical System Configuration: If pricing table KONV has been enhanced with customer-specific fields, ensure that the appended fields are also added to the new structure PRCS_ELEMENTS_DATA.</li>
    </ul>
</li>
<li>After conversion:
    <ul>
        <li>Data Migration: SAP provides an automated migration for transferring data from KONV to PRCD_ELEMENTS. Post-processing report PRC_MIG_POST_PROCESSING needs to be run as soon as possible to fill the document currency field WAERK consistently.
        </li>
    </ul>
</li>
<li>Check for append fields using pre-check delivered via SAP Note 2188735 and follow specific steps for including them in the structure PRCS_ELEMENTS_DATA as needed.</li>
<li>Execute the recommended post-processing report for data consistency after conversion.
</li>
</ul>
<p><strong>Reference Notes:</strong></p>
<ul>
    <li><a href="https://launchpad.support.sap.com/#/notes/2188695">2188695 - WEGXX and STUFE fields are probably filled in the pricing table KONV</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2188735">2188735 - S4TC SAP_APPL - Checks for Pricing</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2189301">2189301 - Pricing table KONV has been enhanced with customer-specific fields</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2220005">2220005 - S/4 HANA: Data Model Changes in Pricing and Condition Technique</a></li>
</ul>