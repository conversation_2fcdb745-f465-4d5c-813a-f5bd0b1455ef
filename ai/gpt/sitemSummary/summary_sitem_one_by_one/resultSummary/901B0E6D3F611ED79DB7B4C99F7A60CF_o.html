<h3>SI25: MasterData_BP</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2492904">2492904 - S4TWL - Batch Input for Customer Master/Supplier Master</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><em>Batch Input</em> as a technology to maintain customer master and supplier master data will not be supported in release from SAP S/4HANA 1809 onwards.</p>
<p>Since we follow "Business Partner first" approach in SAP S/4HANA, Batch Input programs will now work with API based approach which would replace the traditional Batch Input technology as classical customer and supplier transactions have been made obsolete.</p>
<p>As an alternative, we recommend the usage of the following:</p>
<ul>
<li>Business Partner SOAP Services. Refer to SAP Note <a href="https://me.sap.com/notes/2472030" target="_blank">2472030</a> for more information.</li>
<li>IDocs (CREMAS/DEBMAS)</li>
<li>Mass Transactions (XK99/XD99)</li>
</ul>
<p><strong>Business Process Impact:</strong></p>
<p>No influence on business processes is expected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Please check if you use <em>Batch Input</em> for creating/changing customer master or supplier master data. If yes, please switch to the recommended alternatives.</p>
<p><strong>Before conversion project:</strong></p>
<ul>
<li>Process Design / Blueprint: Batch Input to maintain customer master and supplier master data is not supported since SAP S/4HANA 1809, please use one of the following alternatives: Business Partner SOAP Services. Refer to SAP Note 2472030 for more information, IDocs (CREMAS/DEBMAS), Mass Transactions (XK99/XD99).</li>
</ul>
<p><strong>Before or during conversion project:</strong></p>
<ul>
<li>User Training</li>
<li>Custom Code Adaption: Adjust your custom code according to the new process.</li>
</ul>