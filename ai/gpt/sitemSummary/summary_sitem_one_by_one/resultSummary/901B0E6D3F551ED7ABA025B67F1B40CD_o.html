<h3>SI13: CM_LOGISTICS_RDP_AND_PRICING_MM_CONTRACT</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2560298">2560298 - S4TWL - Commodity Pricing in Purchasing Contracts and Scheduling Agreements</a></strong></p>
<p><strong>Description:</strong></p>
<p>Within S/4HANA 1709 - from FPS00 until FPS02, MM Contracts and Scheduling Agreements do not support the following functionality:</p>
<ul>
<li>Risk Distribution Plan (RDP)</li>
<li>Usage of Commodity Pricing Engine (CPE) and Configurable Parameters and Formulas (CPF)</li>
</ul>
<p>Starting with S/4HANA 1709 FPS03, MM Contracts are supported with the following functionality:</p>
<ul>
<li>Usage of Commodity Pricing Engine (CPE) and Configurable Parameters and Formulas (CPF) using time-independent conditions (document conditions). Existing MM Contracts (with time-dependent conditions) have to be closed and new MM contracts with time-independent conditions have to be created for the open quantity and validity period.</li>
<li>Risk Distribution Plan (RDP) for time-independent conditions.</li>
</ul>
<p>Starting with S/4HANA 2020 FPS00, Scheduling Agreements are supported with commodity pricing (CPE, CPF) using time-dependent conditions.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Before S/4HANA 1709 FPS03, customers converting to S/4HANA using RDPs or CPE/CPF within MM Contracts or Scheduling Agreements cannot convert. This impacts the ability to use Risk Distribution Plan and Commodity Pricing Engine within Materials Management contracts and scheduling agreements.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p><strong>User Training (During conversion project - Mandatory):</strong> Inform users about changes.</p>
<p><strong>Customizing / Configuration (During conversion project - Mandatory):</strong> As of SAP S/4HANA 1709 FPS03, create new contract types to support document conditions.</p>
<p><strong>Business Operations (During conversion project - Mandatory):</strong> As preparation for the conversion, complete and close existing MM contracts in ERP. If required, create new contracts for the remaining validity period in SAP S/4HANA 1709 FPS03 or later after the conversion.</p>