


General information - SAP Support Backbone Connectivity Troubleshooting in Solution Manager 7.2  - Guided Answers



Available ValuesInformation Message Strip CloseInformation Message Strip ClosePositive ActionNegative ActionMoreEmphasized
Loading ...

HomeSAP Support Backbone Connectivity Troubleshooting in Solution Manager 7.2 close-command-fieldTimelineGeneral information General informationmoreBack one step.General informationStart overOn 1 January, 2020 SAP’s Support Backbone communication infrastructure will finalize its overhaul by finally disconnecting its legacy communication channels which are currently operating parallel to the new Support Backbone.  
The new communication channels and their underlying infrastructure must be activated in SAP Solution Manager 7.2 prior to 1 January, 2020. These new channels, shown in the diagram below, address architecture challenges such as the architecture of the proprietary RFC protocol and backend stability. Read the Support Backbone Update Guide for more details.
 
Configure and check your communication channels to SAP’s updated Support Backbone by following the instructions provided in the SAP Support Backbone Update Checklists for SAP Solution Manager 7.2 SP05 and above. 
Use the following Guided Answer to address problems which might arise during or after your update to SAP’s new Support Backbone.This solved my issueNext steps...1. Issues with Support Hub Connectivity2. Error Messages in Job Logs Using Asynchronous Communication3. False Positives in Self-Diagnosis (SP06) alerts 00036 or 000374. Changes in communication channel with the SAP Backend5. Checking the Asynchronous Communication Channelopen-command-fieldcommentprintshare-2Is this content helpful?icon-face-happyicon-face-bad SubmitMessage Strip InformationYour feedback is anonymous, we do not collect any personal data.Terms of UseCopyright and TrademarksLegal DisclosurePrivacy

