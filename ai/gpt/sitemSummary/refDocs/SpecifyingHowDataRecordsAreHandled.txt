Specifying How Data Records Are Handled | SAP Help PortalHomeSAP BW/4HANASAP BW/4HANAData Acquisition...Loading DataCreating a Data Transfer ProcessEditing Update PropertiesSpecifying How Data Records Are HandledSAP BW/4HANA1.0 SPS20Available Versions: 2023 SPS01  2023  2021 SPS08  2021 SPS07  2021 SPS06  2021 SPS05  2021 SPS04  2021 SPS03  2021 SPS02  2021 SPS01  2021  2.0 SP15  2.0 SP14  2.0 SP13  2.0 SP12  2.0 SP11  2.0 SP10  2.0 SP09  2.0 SP08  2.0 SP07  2.0 SP06  2.0 SP05  2.0 SP04  2.0 SP03  2.0 SP02  2.0 SP01  2.0  1.0 SPS20  1.0 SP12  1.0 SP11  1.0 SP10  1.0 SP09  1.0 SP08  1.0 SP07  1.0 SP06  1.0 SP05  1.0 SP04  1.0 SP03  1.0 SP02  1.0 SP01  1.0 EnglishAvailable Languages: English  German (Deutsch) ProductionStates:DraftProductionThis documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Full DocumentCreate Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents SAP BW∕4HANA  Overview  Data Modeling  Data Acquisition  Source System  Working with Source Systems  DataSource  Working with DataSources  Transformation  Creating Transformations  InfoSource  Creating InfoSources  Loading Data  Data Transfer Process  Creating a Data Transfer Process  Creating a Data Transformation Process Using the Wizard  Editing General Properties  Editing Extraction Properties  Editing Update Properties  Specifying How Data Records Are Handled  Options for Error Handling  What is the relationship between existing data transfer processes with error handling and SAP HANA execution?  Editing Runtime Properties  Excuting Data Transfer Process  Displaying Properties of the Data Transfer Process  Manual Data Entry  Analysis  Agile Information Access: BW Workspace  Configuration  Operation  Administration  Interfaces  Application Server for ABAP  Security Guide SAP BW∕4HANA  Specifying How Data Records Are HandledOn this pageContextProcedureTo write data records with errors to the data transfer intermediate storage,  so that they can be updated to the target of the data transfer process once the error has been resolved, a number of settings have to be made in the data transfer process (DTP).
Context
In the default setting, error handling is completely deactivated (option Request is canceled, records are not tracked and target is not updated). This setting produces the best performance in all cases where the data quality is satisfactory, and the data flow has been tested. If the Track records after failed requestfield is selected on the Update tab, the DTP is executed again in the following cases with the option Request is canceled, first incorrect record is tracked and target is not updated:

If a red request is restarted (because of the DTP setting Automatically repeat red requests in process chains for example).

If the check triggered by the Track records after failed request field when request processing begins detects that the previous request has the technical status red.


If a request contains an error, selecting this field makes it possible to perform error analysis if executed again, and to track records back to the source in order to find out which one caused the error. 
We recommend activating error handling if errors occur during execution of the DTP. If error handling is activated, the data records with errors are written to the data transfer intermediate storage, where they can be corrected before being written to the data target using an error DTP.
Note
Error handling is not possible if execution takes place in SAP HANA.

ProcedureDefine the key for error handling under Extraction Grouped By on the Extraction tab.If errors occur, all subsequent records with the same key are written to the data transfer intermediate storage along with the incorrect record; they are not updated to the target. This guarantees the serialization of the data records, and consistent data processing. The serialization of the data records and thus the explicit definition of key fields for the error stack is not relevant for targets that are not updated by overwriting.Note
The key should be as detailed as possible. A maximum of 16 key fields is permitted. The fewer the number of key fields defined, the more records are updated to the data transfer intermediate storage.For targets that are not updated by overwriting, the system automatically creates the key fields of the target as key fields for error handling. In this case, you cannot change the key fields.
On the Update tab page under Request Handling, specify how you want the system to respond to data records with errors.For error handling, the following options are available. The main difference between them is how the status of the request is set once all data packages have been processed:Request is set to failed, error stack is written and valid records are updatedRequest is set to success, error stack is written and valid records are updated
Specify the acceptable number of invalid records for error handling. If more than this specified number of errors occur, the transfer is terminated.Note
If you leave this blank, handling for incorrect data records is not activated, and the update is terminated as soon as the first error occurs.
On the Runtime Properties tab, make the settings for the temporary storage .The intermediate storage supports you with troubleshooting and when restarting the load process. In these settings, you specify the processing steps after which you want the system to temporarily store the DTP request (such as extraction, filtering, removing new records with the same key and transformation). You also specify when the temporary storage should be deleted. This can be done either after the request has been updated successfully to the target, when the request is deleted or a specified amount of time after the request is processed. Under Level of Detail, you specify how you want to track the transformation.
Once you have activated your data transfer process, create an error DTP on the Update tab page by pressing  and include it in a process chain or start manually if errors occur.
Analyze the error and correct the data records with errors.The intermediate storage for the individual processing steps can be displayed in the Request Monitor for the DTP by pressing the button in the Data column. The records with errors can be identified from the icon.If the request contains records with errors, you can call the data transfer intermediate storage (DTIS) (error stack) by pressing Open DTIS Maintenance on the Update tab in the data transfer process and correcting the records there. The authorizations for DTIS maintenance are checked using authorization object S_RS_DTP. You require activity 23 (Maintain DTP Definition).
Execute the error DTP manually or via a process chain.

On this pageContextProcedureCommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

