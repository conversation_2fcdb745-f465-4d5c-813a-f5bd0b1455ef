Archiving Customer Master Data (FI-AR, SD) | SAP Help PortalHomeSAP ERPFinancial Accounting (FI)Archiving Financial Accounting Data (FI)Archiving Customer Master Data (FI-AR, SD)Financial Accounting (FI)6.0 EHP8 LatestAvailable Versions: 6.0 EHP8 Latest  6.0 EHP7 Latest  6.0 EHP6 on HANA Latest * 6.0 EHP6 Latest * This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)EnglishAvailable Languages: English  German (Deutsch) This documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteTo mark this page as a favorite, you need to log in with your SAP ID.If you do not have an SAP ID, you can create one for free from the login page.Log onDownload PDFThe following PDF options are available for this document:Create Custom PDFShareTable of Contents Financial Accounting (FI)  Archiving Financial Accounting Data (FI)  Dependencies in Financial Accounting (FI)  Archiving Authorizations (FI)  Available Archiving Programs (FI)  Archiving Deferred Taxes Using FI_DEFTAX  Archiving Financial Accounting Documents (FI-GL, FI-AR, FI-AP)  Archiving FI Transaction Figures (FI-GL, FI-AR, FI-AP)  Archiving G/L Account Master Data (FI-GL)  Archiving Customer Master Data (FI-AR, SD)  Checks (FI-AR , SD)  Application Customizing (FI-AR, SD)  Variant Settings for Writing (FI-AR, SD)  ILM-related Information for FI_ACCRECV  Archiving Vendor Master Data (FI-AP)  Archiving Banks Using FI_BANKS  Archiving FI Check Data (FI-AR, FI-AP, FI-BL)  Archiving Payment Requests  Archiving Electronic Bank Data (FI-BL)  Archiving Cash Journal Documents (FI-BL)  Archiving Reorganization Plans  Archiving Payment Release List Items Using FI_FPRL  Archiving FI Asset Data (FI-AA)  Archiving Totals Records and Line Items (FI-SL)  Archiving Funds Management Data (FI-FM)  Highlighting results for "Archiving Vendor Master Data" Archiving Customer Master Data (FI-AR, SD)On this pageDefinitionUseStructureRelated Information
Definition
Archiving object  FI_ACCRECV is used to archive and delete customer master data in the  Financial Accounting
(FI) and Sales and
Distribution (SD) components.

UseCustomer master data is stored and archived in three different areas, as follows:  

General data


FI data (for a specific company code)


SD data (for a specific sales organization)

The term customer master data is used in both the
FI and
SD components.To archive master data, you must set the deletion flag in the master record. You can set this flag for a complete customer or for individual company codes and sales organizations.For more information
about how to set the deletion flag, see the following:

Archiving and Deleting a Customer Master Record


Application Customizing
(FI-AR, SD)

In addition to the deletion flag, a series of other prerequisites must be met so that you can archive customer master data. For more information about the checks performed by the system during archiving, see  Checks (FI-AR, SD).
Calling the Archiving Function
You can archive customer master data on the SAP Easy Access screen under  Tools → Administration →
Administration → Data Archiving.

Structure
Tables
Archiving object  FI_ACCRECV is used to archive data from different tables. For more information about how to display the table names for individual archiving objects, see  Tables and Archiving Objects.
The following table shows the reports available:



Function


Report 





Write


FI_ACCRECV_WRI




Delete


FI_ACCRECV_DEL




Integration
You can use archiving object  AM_ASSET in
Information Lifecycle Management. You must have activated the corresponding
business functions to do this. The system then also shows the ILM
Actions group box. You can use these actions to carry out archiving, for example, where the retention periods stored in the  Information Retention
Manager can be evaluated. Additionally, you can create snapshots (copies) of data or destroy data that matches the requirements. For more information, see  Activating SAP ILM.

Displaying Archived Customer Master Data
To display individual documents for archiving object  FI_ACCRECV using the Archive
Information System, you require an information structure that has been
created based on one of the following standard SAP field catalogs:


SAP_FI_ACCREC_1
(FI customer master data)


SAP_FI_ACCREC_2
(SD customer master data)


Each information structure must be active and configured correctly.
For more information about information structures, see Using the Archive Information System.

 Related InformationArchive Information SystemOn this pageDefinitionUseStructureRelated InformationWas this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

