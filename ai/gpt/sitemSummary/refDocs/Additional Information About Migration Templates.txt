Additional Information About XML Template Files | SAP Help PortalHomeSAP S/4HANA CloudData MigrationMigrate Your Data - Migration Cockpit...Migrate Data Using Staging TablesFilling Template Files with DataFilling XML Template Files with DataAdditional Information About XML Template FilesData Migration2402.1Available Versions: 2408 Latest  2402.2  2402.1  2402  2308.4  2308.3  2308.2  2308.1  2308  2302.4 * 2302.3 * 2302.2 * 2302.1 * 2302 * 2208.3 * 2208.2 * 2208.1 * 2208 * 2202.4 * 2202.3 * 2202.2 * 2202.1 * 2202 * 2111.1 * 2111 * 2108.1 * 2108 * 2105 * 2011 DE** This product version is out of mainstream maintenance. The documentation is no longer regularly updated.For more information, see the Product Availability Matrix (PAM)EnglishAvailable Languages: English  Chinese Simplified (简体中文)  French (Français)  German (Deutsch)  Japanese (日本語)  Portuguese for Brazil (Português do Brasil)  Russian (Русский)  Spanish (Español) ProductionStates:TestProductionDraftThis documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareEdit in IXIA CCMS Web You can now go directly to IXIA CCMS Web to edit the topic. Simply select Edit in IXIA CCMS Web link under the More… menu. Don't show me againMoreMetadata Analytics Subscribe Edit in IXIA CCMS WebTable of Contents Data Migration  Migrate Your Data - Migration Cockpit  Migrate Data Using Staging Tables  The Migration Process  Creating Migration Projects  The Migration Project Screen  The Migration Object Screen  Viewing Messages  Downloading Template Files Filling Template Files with Data Filling XML Template Files with Data  Additional Information About XML Template Files  Filling CSV Template Files with Data  Transferring Data to Staging Tables  Viewing Migration Object Instances  Preparing the Staging Tables  Mapping Tasks  Simulating the Migration  Migrating Data to SAP S/4HANA Cloud  Creating Correction Files  Viewing the Migration Results  Migrate Data Directly from SAP System  Migration Object Modeler  Read Access Logging Configurations in Migration Cockpit  Available Migration Objects  Data Migration Status  Situation Handling for Data Migration  Video Library for Data Migration  Migrate Your Data - Migration Cockpit (Old) - Deprecated  Additional Information About XML Template FilesOn this pagePrerequisitesThe Field List SheetWorking with SheetsImportant Information



The migration templates (Microsoft Excel XML Spreadsheet 2003 files) consist of different sheets which are visible at the bottom of the migration template. You use the different sheets to specify the data that belongs to different data structures. For example the migration template for the migration object Material, contains a sheet for basic data, a sheet for plant data, and so on. Some sheets are mandatory, and some are optional.
A migration template is based on the active view of the relevant migration object. You can find information about the active view in the Microsoft Excel XML file. In the file, navigate to File  Info. You can find the active view name under Properties  Tags.



Prerequisites
In the Microsoft Excel XML Spreadsheet 2003 file, navigate to File  Options  Advanced. Under the option When calculating this workbook:, ensure that the option Set precision as displayed is selected.


Note

For Mac OS machines, navigate to Excel  Preferences. Under Formulas and Lists, choose Calculation. Under the option When calculating workbooks, ensure that the option Set precision as displayed is selected.





The Field List Sheet
The Field List sheet is one of the first sheets in the migration template. You use this sheet to get an overview of the expected data in one central location.
It contains information about the mandatory and optional sheets, as well as detailed information for each sheet (for example the expected data type and length for the fields in each sheet).
On the Field List sheet, you can view the following information for each field in the migration template:


The name of the sheet, and whether it is mandatory or optional. Only mandatory sheets have the suffix Mandatory, for example Basic Data (Mandatory). All other sheets are optional.


Note

You can quickly get an overview of the mandatory and optional sheets by looking at the color of the sheet names at the bottom of the migration template. The name of the mandatory sheets have the color orange, while the optional sheets have the color blue.


The group name for the fields in a sheet.

The individual fields in each sheet, as well as whether fields are mandatory for a sheet.

Information about the expected format of the individual fields, for example the data type and length.


Note

In the field list sheet, certain technical information about the fields is hidden by default. The columns SAP Structure and SAP Field (columns 8 and 9) are hidden by default. The column SAP Structure is the technical name of the structure that the field belongs to. The column SAP Field is the technical name of the field. To unhide these columns, select the columns adjacent to either side of the columns that you want to unhide. Right-click your selection, and choose Unhide.
If you want to extract data from SAP ERP, the technical name of structure and the technical name of the field often corresponds to the SAP ERP table name and field name. Also, the SAP Release Note (2568909) for SAP S/4HANA Cloud data migration content uses the technical names of the structures and fields.






Working with Sheets
For each migration template, you need to specify data for the mandatory sheets, and for the optional sheets that are relevant for your project:


Mandatory sheets (orange)
Mandatory sheets represent the minimum set of data you must provide for data migration. Fill in all mandatory fields.

Optional sheets (blue)
Use optional sheets depending on your migration scope and available legacy data.


Viewing Additional Information for Each Column
In row 8, you can view the field names in SAP S/4HANA Cloud, as well as additional information such as the expected format (for example the data type and length). Note that you must expand the row to view this additional information.
Some fields are mandatory, and some are optional. The wildcard character (‘*’) beside the name of a field indicates that the field is mandatory.


Note

Although an optional sheet may contain mandatory columns, if the sheet is not relevant for your project, there is no need to fill the mandatory columns in the sheet with data.



Note

Rows 4, 5, and 6 are hidden by default. Row 4 is the technical name of the structure (corresponds to the sheet name). Row 5 is the technical name of the field (corresponds to row 8 - the field description). Row 6 contains technical information such as the data type and length.
To unhide these rows, select the rows adjacent to either side of the rows that you want to unhide. Right-click your selection, and choose Unhide.



Working with Different Data Types
You can view the data type for a field in row 8 (see Viewing Additional Information for Each Column above). Depending on the field, one of the following data types may be required:


Text
Letters, numbers, and special characters (such as ‘.’ and ‘&’) are permitted. In the SAP S/4HANA migration cockpit, you can map the values of certain fields with the data type text (usually those fields with Length: 80) to their correct SAP S/4HANA Cloud target values. You can do this value mapping in the SAP S/4HANA migration cockpit when you start the transfer (in the step Convert Values).

Number
Enter numbers with decimals in the relevant country-specific format, for example 12.34 (United States) or 12,34 (Germany). For fields with decimal places, the length includes the number of decimal places (if required), for example if the information for the column states Length: 8, Decimals: 3, then a number such as 12345.678 is permitted. Note that decimal places are not mandatory. In is this example, you can specify a whole number up to length 8 without decimal places, for example ‘1’. This number would be set to ‘1.000’ internally. For negative numbers, ensure that a minus sign (‘-‘) directly precedes the number, for example ‘-100’.
Note that the maximum field length supported by Microsoft Excel is 15 digits (including decimals). If you have longer numbers, use the option for transferring data to S/4HANA using staging tables. For more information, see SAP Knowledge Base Article 2718516.

Date
Enter the date in the country-specific format of your operating system, for example 12/31/1998 (United States) or 31.12.1998 (Germany). Note that Microsoft Excel automatically recognizes different date formats and transforms them automatically to the correct XML format.

Time
Enter the time in the format HH:MM:SS, for example 02:52:40.

Date-Time
Enter the date in your country-specific format followed by the time in the format HH:MM:SS, for example 12/31/1998 02:52:40 (United States) and 31.12.1998 02:52:40 (Germany). Separate the date and time with a blank space.



Copying Data to a Sheet
When copying data to a sheet from Microsoft Excel, always right-click the relevant cell and choose the paste option Values (V). Avoid pasting data that includes formatting and formulas into the migration template, as this will corrupt the structure of the XML migration template.


Using the Find and Replace Function
Do not use the Microsoft Excel function Find and Replace. If you change data by using this function, you may also unintentionally change the field names and corrupt the structure of the XML migration template.


Saving the Migration Template
Ensure that you only save the migration template as a Microsoft Excel XML Spreadsheet 2003 file. Other file types are not supported by the SAP S/4HANA migration cockpit.





Important Information

Do not make any changes to the structure of the migration template, specifically:


Do not delete, rename or change the order any sheet in the migration template.

Do not change the formatting of any cells.

Do not use formulas.
Do not hide, remove, or change the order of any of the columns in the migration template.



Note

Any changes to the sheets will result in a corrupted XML structure. Such modified migration templates are not supported by the SAP S/4HANA migration cockpit.





On this pagePrerequisitesThe Field List SheetWorking with SheetsImportant InformationCommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

