
Using Excel to open XML and save value 22.725, but actually the valuse - Microsoft Community






























































































Skip to main content







Microsoft



Community



Community



                            Community
                        




 Home 



Products
BingGaming and XboxMicrosoft 365 and OfficeMicrosoft 365 InsiderMicrosoft AdvertisingMicrosoft EdgeMicrosoft TeamsOutlookSkypeSurfaceWindowsWindows Client for IT ProsWindows Insider ProgramWindows Server

 
 

Get Started
Ask a Question



Tips for Beginners

FAQ
Community Code of Conduct
Visit the Community Center

Meet our Community Leaders




More





Ask a question


Buy Microsoft 365




 



 All Microsoft


Global


Microsoft 365


Teams


Copilot


Windows


Surface


Xbox


Deals


Small Business


Support




Software
Software


Windows Apps


AI


Outlook


OneDrive


Microsoft Teams


OneNote


Microsoft Edge


Skype




PCs & Devices  
PCs & Devices  


Computers


Shop Xbox


Accessories


VR & mixed reality


Certified Refurbished


Trade-in for cash




Entertainment
Entertainment


Xbox Game Pass Ultimate


PC Game Pass


Xbox games


PC and Windows games


Movies & TV




Business
Business


Microsoft Cloud


Microsoft Security


Dynamics 365


Microsoft 365 for business


Microsoft Power Platform


Windows 365


Microsoft Industry


Small Business




Developer & IT  
Developer & IT  


Azure


Developer Center


Documentation


Microsoft Learn


Microsoft Tech Community


Azure Marketplace


AppSource


Visual Studio




Other
Other


Microsoft Rewards 


Free downloads & security


Education


Gift cards


Licensing


Unlocked stories




View Sitemap










Search
Search the Community




 No results




Cancel

Sign in to your accountSign in

















 

                We use optional cookies to improve your experience on our websites, such as through social media connections, and to display personalized advertising based on your online activity. If you reject optional cookies, only cookies necessary to provide you the services will be used. You may change your selection by clicking “Manage Cookies” at the bottom of the page. Privacy Statement Third-Party Cookies



Accept
Reject
Manage cookies











Contribute to the Microsoft 365 and Office forum!

Click HERE to learn more 💡 







March 14, 2024
Contribute to the Microsoft 365 and Office forum!


Click HERE to learn more 💡






Top Contributors in Excel:

HansV MVP - Andreas Killer - Ashish Mathur - Jim_ Gordon - Rory Archibald ✅ 







March 11, 2024
Top Contributors in Excel:


HansV MVP - Andreas Killer - Ashish Mathur - Jim_ Gordon - Rory Archibald ✅















Choose where you want to search below







Search
Search the Community






Search the community and support articles


Excel


Microsoft 365 and Office


Search Community member












                            Ask a new question
                            





























FR






                    Frank0014
                


Created on September 17, 2018








            Using Excel to open XML and save value 22.725, but actually the valuse is saved as 22.725000000000001
        

Hi，expert，
when I use excel to open a xml, and fill the value 22.725, but after save, we check xml directly by notepad,  the xml is  <Cell ss:Index="19"><Data ss:Type="Number">22.725000000000001</Data></Cell>.

You can see actually the xml save the value 22.725000000000001, not 22.725.
We want 22.725.

my question:
1. After fill 22.725 by excel, why 22.725 show in excel, but in xml is 22.725000000000001.
2. We fill 22.725 and upload the xml to our SAP system, but get value is 22.725000000000001. This is not we want, we want 22.725
Do you have solution to keep 22.725 in xml, not 22.725000000000001?






                    This thread is locked. You can vote as helpful, but you cannot reply or subscribe to this thread.
                





        I have the same question (0)
    



                        Report abuse
                    
















                    Answer
                





JO






                    joeu2004
                


Replied on September 19, 2018

                        Report abuse
                    










In reply to Frank0014's post on September 18, 2018
                        






(Note:  Do not provide any Excel file in private messages.  It is completely unnecessary.)


Frank wrote:


If there is some way to round the value in xml by excel? I mean, user fill 22.725, excel calculate it as 22.725000000000001.



I'm afraid not, in this case.  Here is what's happening....

Excel uses 64-bit binary floating-point to represent numeric values.  Consequently, most decimal fractions cannot be represented exactly.  And the binary approximation of a particular decimal fraction varies depending on the magnitude of the integer part.

Consequently, the exact internal representation of 22.725 in decimal is:

22.7250000000000,0142108547152020037174224853515625

(I use period for the decimal point and comma to demarcate the first 15 significant digits, which is all that Excel formats (rounded), an arbitrary limitation.)

You can see where the 1 comes from in the 17th significant digit. The constant 22.725 is already rounded.  So ROUND(22.725,3) would not change the internal representation.

Contrary to most incorrect descriptions, it takes 
17, not 15, significant decimal digits to reproduce the exact binary representation, as specified by the IEEE 754 standard.  So Excel stores 17 significant decimal digits in XML files.

Arguably, Excel could recognize that the binary representation of 22.725000000000001 is the same as the binary representation of 22.725, so Excel could write 22.725 into the XML file instead.

On the other hand, another application might reasonably convert 22.725 into the following different binary representation, shown in decimal:

22.7249999999999,97868371792719699442386627197265625

The difference depends on how the least-significant binary bit is rounded.

So arguably, Excel is correct to store 22.725000000000001 in order to ensure that any IEEE754-comforming application will convert it to the original binary representation, which again in decimal is:

22.7250000000000,0142108547152020037174224853515625

-----

I cannot comment on how SAP interprets the 17-significant-digit number, or how you might ensure that SAP treats it as 22.725.















Was this reply helpful?


                        Yes
                    

                        No
                    




                Sorry this didn't help.
            



Great! Thanks for your feedback.
How satisfied are you with this reply?


Thanks for your feedback, it helps us improve the site.
How satisfied are you with this reply?












                    Thanks for your feedback.
                











                    Replies (5)
                    









 



 




        Question Info
    


        Last updated November 12, 2023
    

        Views 475
    



Applies to:



Microsoft 365 and Office

/

Excel

/

For home

/

Windows



 



























What's new


Surface Laptop Studio 2


Surface Laptop Go 3


Surface Pro 9


Surface Laptop 5


Microsoft Copilot


Copilot in Windows


Microsoft 365


Windows 11 apps




Microsoft Store


Account profile


Download Center


Microsoft Store support


Returns


Order tracking


Certified Refurbished


Microsoft Store Promise


Flexible Payments




Education


Microsoft in education


Devices for education


Microsoft Teams for Education


Microsoft 365 Education


How to buy for your school


Educator training and development


Deals for students and parents


Azure for students






Business


Microsoft Cloud


Microsoft Security


Dynamics 365


Microsoft 365


Microsoft Power Platform


Microsoft Teams


Copilot for Microsoft 365


Small Business




Developer & IT


Azure


Developer Center


Documentation


Microsoft Learn


Microsoft Tech Community


Azure Marketplace


AppSource


Visual Studio




Company


Careers


About Microsoft


Company news


Privacy at Microsoft


Investors


Diversity and inclusion


Accessibility


Sustainability






English (United States)


Your Privacy Choices Opt-Out Icon





Your Privacy Choices




Your Privacy Choices Opt-Out Icon





Your Privacy Choices





Sitemap


Contact Microsoft


Privacy 


Manage cookies

Terms of Use

Trademarks


Safety & eco


Recycling


About our ads

© Microsoft 2024











                    This site in other languages
                    
x




Čeština
Dansk
Deutsch
Español
Français
Italiano


Magyar
Nederlands
Norsk Bokmål
Polski
Português
Suomi


Svenska
Türkçe
Ελληνικά
Русский
עברית
العربية


ไทย
한국어
中文(简体)
中文(繁體)
日本語

























https://answers.microsoft.com/en-us/site/disambigsilentsignin











Report abuse

Type of abuse

Harassment or threats
Inappropriate/Adult content
Nudity
Profanity
Software piracy
SPAM/Advertising
Virus/Spyware/Malware danger
Other Term of Use or Code of Conduct violation
Child exploitation or abuse



                                        Harassment is any behavior intended to disturb or upset a person or group of people. Threats include any threat of suicide, violence, or harm to another.
                                    

                                        Any content of an adult theme or inappropriate to a community web site.
                                    

                                        Any image, link, or discussion of nudity.
                                    

                                        Any behavior that is insulting, rude, vulgar, desecrating, or showing disrespect.
                                    

                                        Any behavior that appears to violate End user license agreements, including providing product keys or links to pirated software.
                                    

                                        Unsolicited bulk mail or bulk advertising.
                                    

                                        Any link to or advocacy of virus, spyware, malware, or phishing sites.
                                    

                                        Any other inappropriate content or behavior as defined by the Terms of Use or Code of Conduct.
                                    



                                        Any image, link, or discussion related to child pornography, child nudity, or other child abuse or exploitation.
                                    
Details (required):




                                    250 characters remaining
                                



                                        Cancel
                                    

                                        Submit
                                    







Report abuse

Type of abuse

Harassment or threats
Inappropriate/Adult content
Nudity
Profanity
Software piracy
SPAM/Advertising
Virus/Spyware/Malware danger
Other Term of Use or Code of Conduct violation
Child exploitation or abuse



                                        Harassment is any behavior intended to disturb or upset a person or group of people. Threats include any threat of suicide, violence, or harm to another.
                                    

                                        Any content of an adult theme or inappropriate to a community web site.
                                    

                                        Any image, link, or discussion of nudity.
                                    

                                        Any behavior that is insulting, rude, vulgar, desecrating, or showing disrespect.
                                    

                                        Any behavior that appears to violate End user license agreements, including providing product keys or links to pirated software.
                                    

                                        Unsolicited bulk mail or bulk advertising.
                                    

                                        Any link to or advocacy of virus, spyware, malware, or phishing sites.
                                    

                                        Any other inappropriate content or behavior as defined by the Terms of Use or Code of Conduct.
                                    



                                        Any image, link, or discussion related to child pornography, child nudity, or other child abuse or exploitation.
                                    
Details (required):




                                    250 characters remaining
                                



                                        Cancel
                                    

                                        Submit
                                    



