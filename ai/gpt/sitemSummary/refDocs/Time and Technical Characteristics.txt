Special and Time Characteristics and Own Implementation | SAP Help PortalHomeSupport ContentBusiness Warehouse – Analytic Engine (OLAP) and PlanningOLAP TechnologyBW OLAP ServicesMasterData Read Services and Input (F4) HelpSpecial and Time Characteristics and Own ImplementationBusiness Warehouse – Analytic Engine (OLAP) and PlanningProductionStates:DraftProductionAdditional Content This documentSearch Scopes:All SAP productsThis productThis documentAll of these words:Any of these words:This exact word or phrase:None of these words:ClearSearchAdvanced SearchTable of ContentsFavoriteThis page has been removed from your favorites.Download PDFThe following PDF options are available for this document:Create Custom PDFFeedback You can now add comments and questions right into SAP documentation and start a conversation with SAP authors and experts. Don't show me againFeedbackShareMoreMetadata Analytics Subscribe Table of Contents Business Warehouse – Analytic Engine (OLAP) and Planning  OLAP Technology  BW OLAP Layer  BW OLAP Services  MasterData Read Services and Input (F4) Help  F4 BADI  F4 Modes  F4 in a query based on a classic Infoset (SQ02)  F4: Consulting Notes  F4: Sorting  Key Figure Attributes  Performance Impact of F4 Modes  SSG BW-BEX-OT-F4  Special and Time Characteristics and Own Implementation  Technical Characteristics: 0CURRENCY  Technical Characteristics: 0INFOPROV  Technical Characteristics: 0UNIT  Time Characteristics: 0CALDAY  Time Characteristics: 0CALMONTH  Time Characteristics: 0CALMONTH2  Time Characteristics: 0CALQUART1  Time Characteristics: 0CALQUARTER  Time Characteristics: 0CALWEEK  Time Characteristics: 0CALYEAR  Time Characteristics: 0FISCPER  Time Characteristics: 0FISCPER3  Time Characteristics: 0FISCVARNT  Time Characteristics: 0FISCYEAR  Time Characteristics: 0HALFYEAR1  Time Characteristics: 0WEEKDAY1  Transaction RSRHIERARCHYVIRT  Text and Key in F4  Transitive Attributes  Report-Report Interface  Query Variables  Characteristic Hierarchies  BW Currency Translation  BW Unit Conversion  OLAP Authorizations  BW InfoProvider Layer  BW running on HANA and SAP BW/4HANA  BW APIs to OLAP  Other BW OT Topics  OLAP Media Center  BW-PLA - Planning  Special and Time Characteristics and Own ImplementationOn this pageIntroductionTime CharacteristicsTechnical CharacteristicsIntroduction
Starting with BW release 7.0, master data type 'Own Implementation' can be used. A master data read class is then
  created for master data retrieval.
Time characteristics as well as some technical characteristics (e.g. 0INFOPROV, 0FISCYEAR, 0CALMONTH...) have such a
  master data read class(see note 1387166).
  All master data and texts are returned by these ABAP classes. There is no need to load the master data for these
  objects. It is recommended to use the latest/current delivered content version(as explained in notes  2194279 and  2074801) 
Important SAP Notes/KBAs

 2194279 Check report for
      technical characteristics and time characteristics

Report RSD_TIME_TCT_MREADCLASS_CHECK can be used to check for active
          versions that differ from current content versions.


 2074801 Dumps and Issues with
      special InfoObjects like 0FISCYEAR, 0CALMONTH...

After an upgrade to BW releases 7.x different kinds of errors can occur when older content
          versions are in use that do not yet have the master data read class enabled.


 1387166 Master Data Read
      Class

A list of infoobjects and their respective master data read classes can be found in this note.



Time Characteristics
The exact definition depends on the content release. E.G. on BW/4Hana systems 0CALDAY is shipped with
    (navigation) attributes in contrast to BW75 systems. Please check the corresponding links below for a detailed
    discussion of a certain object. In case the characteristic has navigation attributes you can
    use transaction RSRHIERARCHYVIRT to
    rebuild the master data tables(note 2608688). In case
    of 0CALDAY, 0CALMONTH and 0CALQUATER, also the SID table is filled with all values according to the definition of
    the time interval in this transaction. The characteristics 0FISCPER depends on the fiscal
  variant(0FISCVARNT) and is maintained with transaction OB29. A detailed discussion of all these time characteristics
  can be found below.
As already mentioned above, it is recommended to use the latest/current delivered content version of these
  characteristics. For some characteristics it is possible to use navigation attributes, see Navigation Attributes for Time Characteristics and the corresponding page of the object
  below. 
SAP Notes and
    Online Docu

 2937435
      BW Time Characteristics: Error in CL_RS_TIME_SERVICE
 
2608688   New Rebuild Option for Time
      Characteristics Master Data
2241237 Texts of characteristic 0FISCPER are not
    displayed correctly
The note describes customizing settings in OB29, RSRHIERARCHYVIRT etc. 


Navigation Attributes for Time Characteristics

Transactions

RSRV: Check
    "Comparison of size of SID table and X table of time characteristics"
RSRHIERARCHYVIRT

Object-Related Discussion

0CALDAY(0DATE)
0CALMONTH
0CALMONTH2
0CALWEEK
0WEEKDAY1
0CALQUARTER
0CALQUARTER1
0FISCVARNT
0FISCPER
0FISCPER3
0FISCYEAR
0HALFYEAR1
0CALYEAR
0TIME

Technical Characteristics

0HIENM
0HIER_HIEID
0HIER_NODE
0HIER_VERS
0INFOPROV
0IOBJNM
0LANGU
0LOGSYS
0SOURSYSTEM
0RATE_TYPE
0RTYPE
0UNIT
0CURRENCY

SAP Notes/KBAs

 2419270
 Duplicate SIDs in table /BI0/SIOBJNM
 2147104
      SQL-Error: "T000*"."LANGU": invalid identifier
 2825801
      Issues with 0LANGU and 0BCTCOMP during postmigration
 2074801
      Dumps and Issues with special InfoObjects
On this pageIntroductionTime CharacteristicsTechnical CharacteristicsCommentsThere are no comments on this page. Leave a comment Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedback Was this page helpful?YesNoThank you. Please enter any additional feedback you would like to leave, then press submit.How can we make the page better for you?CancelSubmitThank you for your feedbackAdditional Content This is additional content provided by SAP but not considered the Documentation as defined by the applicable SAP General Terms and Conditions. Don't show this on page loadGot itCopyrightDisclaimerPrivacy StatementLegal DisclosureTrademarkTerms of UseCookie PreferencesAccessibility and SustainabilitySystem StatusAsk a question about the SAP Help PortalFind us onFollow us on FacebookSAP on FacebookFollow us on YouTubeSAP on YouTubeFollow us on LinkedInSAP on LinkedInFollow us on InstagramSAP on InstagramShare

