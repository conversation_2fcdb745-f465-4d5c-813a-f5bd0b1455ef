This SAP Help document outlines details regarding the migration objects available for SAP S/4HANA Cloud. The document features a table sorted alphabetically by Migration Object Name, which provides an overview of all available migration objects along with their characteristics, such as Business Object Type, Business Catalog, Migration Approach, Custom Field Support, and Component.

Key functionalities described in the document include:

- Ability to search for specific migration objects by name.
- Options to filter migration objects based on criteria such as Master or Transactional data types, required business catalogs, support for custom fields, and application components.
- Customization of the table's visible columns to show or hide information as needed.
- Download functionalities for the list of migration objects in various formats (e.g., XLSX, CSV) with different delimiter options for CSV files.

The document warns that data protection laws may require the deletion of personal data after its original purpose is fulfilled and when it is not under legal data retention requirements. It suggests that migrating such data might be considered processing without legal justification.

It also includes notes highlighting:

- The presence of "restricted" and "deprecated" labels for some migration objects indicating limited coverage of business processes ("restricted") or availability of a newer version ("deprecated").
- The intent of migration objects, which is for the initial migration of data into SAP S/4HANA systems and not for changing or updating existing data.
- Template roles provided, which can be tailored according to a company's security policies.

The document provides further information with SAP Help Portal aliases for topics related to migration objects, data migration, and data migration status. Additionally, it mentions the discontinued support for the "CO - Cost rate" migration object in SAP S/4HANA Cloud, recommending the use of the "Manage Cost Rates - Plan" SAP Fiori app for uploading activity prices instead.

It refers to a couple of SAP Notes for additional information on migrating historical financial transactions and mapping units of measure, and provides links to resources for release comparisons of migration object templates, the SAP Fiori apps reference library, and relevant SAP Knowledge Base Articles.

Overall, the document serves as a guide to understanding the migration objects available for use in SAP S/4HANA Cloud for data migration purposes.