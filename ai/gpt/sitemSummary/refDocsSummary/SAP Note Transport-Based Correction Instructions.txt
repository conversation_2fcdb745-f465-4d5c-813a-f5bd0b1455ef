The SAP Help Portal document concerns Transport-Based Correction Instructions (TCIs), which present a new method for delivering ABAP correction instructions to SAP customers. This system offers more flexibility compared to previous delivery mechanisms like automatic SAP Notes, manual SAP Notes, and Support Packages (SPs). In the document, Correction Instructions in general are referred to as CIs.

The intended audience for this guide is System Administrators and Technology Consultants. The comparison table in the document contrasts the standard delivery methods with the new TCI approach, highlighting that TCIs can cover a range of SPs and may depend on other CIs. Unlike singular corrections provided by traditional SAP Notes, TCIs can encapsulate multiple corrections, thereby reducing dependencies and update artifacts.

TCIs are similar to SP deliveries as they contain an ABAP transport but are bundled with SAP Notes as a novel form of CIs enabling simplified and faster correction consumption. TCIs support any ABAP objects linked to a transport connection and include an ABAP transport request along with installation attributes like software component version, minimal SP, and languages.

The validity of a TCI is tied to the software component version and a span of SP levels. The document notes that if an SP imported into the system exceeds the TCI's validity range, the TCI's corrections are absorbed by the SP, rendering the TCI obsolete. It also clarifies that differing software component versions or code lines cannot be mixed, prompting the need for individual TCIs respective to each software component version. An example provided in the guide illustrates the delivery of a correction for the SAP_BASIS software components across three different versions (7.00, 7.01, 7.02,) using one SAP Note and three respective TCIs.

In summary, the document outlines the flexibility and benefits of using TCIs for the delivery of ABAP corrections, highlighting their capabilities, scope, and comparison with legacy methods.