The SAP Help Portal document describes the New Employee Business Partner (BP) Model in SAP S/4HANA, an enhancement in the handling of employee data which introduces separate Business Partner instances, roles, and identification types for employment data.

Key points of the New Employee BP Model:

1. **Activation**: The business function /SHCM/EE_BP_1 activates the new BP model starting from the SAP S/4HANA 1809 version onwards.

2. **Separation of Roles**: There are now separate roles for Employment and Employee.
   - The BP with the Employment role reflects a specific contract with the personnel number.
   - The BP with the Employee role represents the natural person (Central Person CP) and contains main employment data like private address and bank details.

3. **Issues Tackled**: The new BP model overcomes limitations of the old model such as lack of clear relationship between employee and vendor roles and issues with data synchronization in concurrent and global employment scenarios.

4. **Integration**: Existing accesses to BP data remain functional after activating the new function. CDS views and APIs will yield consistent results post-integration.

5. **Prerequisites**: Installation of software component S4CORE 105 and activation of the switch /SHCM/EE_BP_1 in SAP S/4HANA are required. Implementation of SAP Note 2991508 is also necessary.

6. **Features**:
   - Adapted BP synchronization: Updated synchronization reports (/SHCM_RH_SYNC_BUPA_EMPL_SINGLE and /SHCM/RH_SYNC_BUPA_FROM_EMPL) manage Employee and Employment roles and create necessary associations.
   - Flexible role handling: New reports allow adding roles flexibly to BP instances.
   - Additional BP identification types: Personnel Number (Main Employment) and Personnel Number (Employment) for better data linking.
   - New BP relationship category: 'Has Employment' to link Employee and Employment roles.
   - New role type: Employment role (BUP010) is added and associated with Employee role (BUP003).
   - New address usage type: Address of Main Employment for Employee BPs.

The document emphasizes on the importance of the new BP model for reflecting accurate and current employment relationships, enhancing data accuracy, and managing global employment scenarios. It also notes that activating this business function is a prerequisite for using Workforce Integration in SAP S/4HANA with SAP Master Data Integration Service.

Links to installation guides and blogs provide further information and context on implementing and understanding the new model.