This SAP Help Portal document provides instructions on specifying how data records are handled during the execution of a Data Transfer Process (DTP) in SAP BW/4HANA. It explains the procedures for managing records with errors and ensuring data consistency when updating the data target.

The document highlights that:

1. By default, error handling is disabled for DTPs, ensuring the best performance when data quality is high and data flows have been tested. However, if issues occur, error handling can be enabled to allow problematic records to be analyzed and corrected.

2. Error handling settings are adjusted under the "Extraction" and "Update" tabs of the DTP. Users can define a key for error handling that groups records with the same key together, allowing the system to serialize data records and ensure consistent data processing.

3. There are different options for how the system should respond to records with errors during the DTP execution, affecting the status of the request:

   - Request is set to failed, error stack is written and valid records are updated.
   - Request is set to success, error stack is written and valid records are updated.

4. Users can specify a threshold for the acceptable number of invalid records. If more errors occur than the specified threshold, the transfer is terminated.

5. In the "Runtime Properties" tab, settings are made for temporary storage of the DTP request to assist with troubleshooting and reloading processes. Users can define when the temporary storage should be deleted and the level of detail for tracking the transformation.

6. After configuring error handling, an error DTP needs to be created and included in a process chain or started manually when errors occur.

7. Error analysis and correction involve using the intermediate storage, accessible via the Request Monitor, and the Data Transfer Intermediate Storage (DTIS), where records can be corrected.

8. The error DTP is then executed manually or via a process chain to update the corrected records to the data target.

The document also notes that error handling is not possible during execution in SAP HANA and suggests that the key fields should be as detailed as possible, with a maximum of 16 fields. For targets that do not require overwriting updates, the system automatically creates key fields for error handling. Additionally, authorization object S_RS_DTP with activity 23 (Maintain DTP Definition) is used for checking authorizations related to DTIS maintenance.