import json
import os
import pandas as pd

sitem_df = pd.read_csv('../sitem.csv', keep_default_na=False)

sitem_list = sitem_df.to_dict('records')

print("first level")

first_ref = {}
first_ref_notes = set()

for sitem in sitem_list:
    note = sitem['SAPNOTE']
    with open(f'../sitemNotes/{note}.json', 'r') as f:
        data = json.load(f)
        refer_list = data['Response']['SAPNote']['References']['RefTo']['Items']
        if len(refer_list) > 0:
            for refer_note in refer_list:
                if refer_note['RefTitle'].startswith('https://') and not refer_note['RefUrl'].startswith('https://'):
                    refer_note["RefTitle"], refer_note["RefUrl"] = refer_note["RefUrl"], refer_note["RefTitle"]
                if refer_note['RefTitle'].startswith('https://launchpad.support.sap.com/#/notes/'):
                    url_parts = refer_note['RefTitle'].split('/')
                    refer_note['RefNumber'] = url_parts[-1]
                if refer_note['RefNumber'] != '':
                    first_ref_notes.add(refer_note['RefNumber'])
            first_ref[note] = refer_list

with open('first_ref.json', 'w') as file:
    json.dump(first_ref, file)
    
with open('first_ref_note.json', 'w') as file:
    json.dump(list(first_ref_notes), file)

print("second level")

second_ref = {}
second_ref_notes = set()
for first_ref_note in first_ref_notes:
    filename = f'../sitemNotes/{first_ref_note}.json'
    if os.path.exists(filename):
        with open(filename, 'r') as f:
            data = json.load(f)
            refer_list = data['Response']['SAPNote']['References']['RefTo']['Items']
            if len(refer_list) > 0:
                for refer_note in refer_list:
                    if refer_note['RefTitle'].startswith('https://') and not refer_note['RefUrl'].startswith('https://'):
                        refer_note["RefTitle"], refer_note["RefUrl"] = refer_note["RefUrl"], refer_note["RefTitle"]
                    if refer_note['RefTitle'].startswith('https://launchpad.support.sap.com/#/notes/'):
                        url_parts = refer_note['RefTitle'].split('/')
                        refer_note['RefNumber'] = url_parts[-1]
                    if refer_note['RefNumber'] != '':
                        second_ref_notes.add(refer_note['RefNumber'])
                second_ref[first_ref_note] = refer_list

with open('second_ref.json', 'w') as file:
    json.dump(second_ref, file)
    
with open('second_ref_note.json', 'w') as file:
    json.dump(list(second_ref_notes), file)


print("third level")

third_ref = {}
third_ref_notes = set()
for second_ref_note in second_ref_notes:
    filename = f'../sitemNotes/{second_ref_note}.json'
    if os.path.exists(filename):
        with open(filename, 'r') as f:
            data = json.load(f)
            refer_list = data['Response']['SAPNote']['References']['RefTo']['Items']
            if len(refer_list) > 0:
                third_ref[second_ref_note] = refer_list
                for refer_note in refer_list:
                    if refer_note['RefTitle'].startswith('https://') and not refer_note['RefUrl'].startswith('https://'):
                        refer_note["RefTitle"], refer_note["RefUrl"] = refer_note["RefUrl"], refer_note["RefTitle"]
                    if refer_note['RefTitle'].startswith('https://launchpad.support.sap.com/#/notes/'):
                        url_parts = refer_note['RefTitle'].split('/')
                        refer_note['RefNumber'] = url_parts[-1]
                    if refer_note['RefNumber'] != '':
                        third_ref_notes.add(refer_note['RefNumber'])

with open('third_ref.json', 'w') as file:
    json.dump(third_ref, file)
    
with open('third_ref_note.json', 'w') as file:
    json.dump(list(third_ref_notes), file)