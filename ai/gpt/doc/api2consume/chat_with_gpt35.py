import time
import requests
import re
from common import ai_api_url
from common import gpt35_turbo_16k_deployment


def get_answer_with_history_gpt35(msg: str, chat_history: list, api_token: str, context: str) -> str:

    chat_history_text = ""
    if len(chat_history) != 0:
        chat_history_text = f"Chat History: {chat_history}"
    prompt = f"""Answer the question based on chat history and context
    Context: {context}
    {chat_history_text}
    Question: {msg}
    """
    headers = {
        "Accept": "application/json",
        "Authorization": f"Bearer {api_token}",
        "AI-Resource-Group": "default",
        "Content-Type": "application/json; charset=utf-8"
    }
    pre_check_request_body = {
        "messages": [
            {"role": "system",
             "content": "If user asks about check results related to a specific \"sitem\", you need to extract the \"sitem\" name and return it in the format sitem:sitem_name."
             },
            {"role": "user", "content": msg}
        ],
        "temperature": 0,
        "presence_penalty": 0,
        "stop": "null",
        # "max_tokens": 100,
        "frequency_penalty": 0
    }
    pre_check_response = requests.post(
        f"{ai_api_url}/v2/inference/deployments/d3defda2415de8bd/chat/completions?api-version=2023-05-15",
        headers=headers, json=pre_check_request_body, verify=False)
    pre_check_result = pre_check_response.json()['choices'][0]['message']['content']
    sitem_pattern = r'^sitem:\s*(.*)$'
    match = re.match(sitem_pattern, pre_check_result)
    if match:
        return pre_check_result
    else: 
        request_body = {
            "messages": [
                {"role": "system",
                "content":
                    """
    I want you to act as a document that I am having a conversation with. Your name is "Readiness Check AI Assistant\". 
    Using the provided context, answer the user's question to the best of your ability using the resources provided. 
    If user ask what can he/she do or what can you do, please return with "Please provide your page information.". 
    If the answer contains link, always use this format "link: [This is title](This is link)."
    If there is nothing in the context relevant to the question at hand, just say "Hmm, I'm not sure." and stop after that. 
    Refuse to answer any question not about the info. Never break character.
    """
                },
                {"role": "user", "content": prompt}
            ],
            "temperature": 0,
            "presence_penalty": 0,
            "stop": "null",
            # "max_tokens": 100,
            "frequency_penalty": 0
        }
        start_time = time.time()
        response = requests.post(
            f"{ai_api_url}/v2/inference/deployments/d3defda2415de8bd/chat/completions?api-version=2023-05-15",
            headers=headers, json=request_body, verify=False)
        resp_json = response.json()

        return response.json()['choices'][0]['message']['content']


def get_standalone_question(msg: str, chat_history: list, api_token: str) -> str:
    if len(chat_history) == 0:
        return msg
    prompt = f"""
    Combine the chat history and follow up question into a standalone question. 
    Chat History: {chat_history}
    Follow up question: {msg}
    """
    headers = {
        "Accept": "application/json",
        "Authorization": f"Bearer {api_token}",
        "AI-Resource-Group": "default",
        "Content-Type": "application/json; charset=utf-8"
    }
    request_body = {
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0,
        "presence_penalty": 0,
        "stop": "null",
        # "max_tokens": 100,
        "frequency_penalty": 0
    }
    response = requests.post(
        f"{ai_api_url}/v2/inference/deployments/{gpt35_turbo_16k_deployment}",
        headers=headers, json=request_body, verify=False)
    return response.json()['choices'][0]['message']['content']