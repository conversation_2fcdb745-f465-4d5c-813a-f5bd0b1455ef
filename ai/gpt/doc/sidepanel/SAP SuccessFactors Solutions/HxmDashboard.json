{"next": "\n          <p>To see your check results in more detail, choose a check title.</p>\n          <p>To download the complete list of your analysis results in a spreadsheet format and a summary of the results, choose the <cite>Generate Document</cite> icon in the header area of the dashboard.</p>\n          <p>The available checks support you to plan and organize your transformation project. To explore the check results, you can take the following steps:</p>\n          <p>\n            <ul>\n              <li>Contact SAP or your preferred implementation partner.</li>\n              \n              \n              <li>Sign up for an <a href='https://webinars.sap.com/customer-evolution-kit/en/hxm'>SAP Customer Evolution Kit workshop for HXM</a>.</li>\n              <li>Consider the <a href='https://servicescatalog.cvdp3eof-dbsservic1-p1-public.model-t.cc.commerce.ondemand.com/Solution-Area-Hierarchy-2023/SuccessFactors-%28HXM%29/SuccessFactors-Cross/SuccessFactors-Cross/HR-Cons-Services/HR-Consulting-Services/Transition-planning-service-for-HXM-solutions-from-SAP/p/000000000050156148'>Transition Planning Service for HXM Solutions from SAP</a> or <a href='https://store.sap.com/dcp/en/product/display-9999954541_live_v1/advisory-service-for-sap-readiness-check-for-sap-successfactors-solutions'>Advisory Service for SAP Readiness Check for SAP SuccessFactors Solutions</a>.</li>\n              <li>Learn more about how to <a href='https://www.sap.com/products/hcm/hxm-move.html'>Simplify Your Move from On-Premise HCM to the Cloud</a>.</li>\n              <li>Explore the <a href='https://go.support.sap.com/roadmapviewer/#/group/658F507A-D6F5-4B78-9EE1-0300C5F1E40F/roadmapOverviewPage/METHCLDSF'>SAP Activate Methodology for SuccessFactors</a>.</li>\n              \n              <li>See the <a href='https://community.successfactors.com/t5/SAP-SuccessFactors-Architecture/ct-p/architecture_leading_practices'>Architecture Leading Practices</a> for SAP SuccessFactors solutions.</li>\n              <li>See the <a href='https://community.successfactors.com/t5/Implementation-Design-Principles/ct-p/ImplementationDesignPrinciples'>Implementation Design Principles for SAP SuccessFactors Solutions</a>.</li>\n            </ul>\n          </p>\n          <p>Please also consider that beyond the information provided in this tool, additional restrictions and landscape requirements might be relevant for your system. For more information, see the release information and release restriction notes, which appear on SAP Help Portal at <a href='https://help.sap.com/viewer/product/SAP_SUCCESSFACTORS_HXM_SUITE/LATEST/en-US'>SAP SuccessFactors HXM Suite</a>.</p>\n        ", "info": "\n          <p>With SAP Readiness Check for SAP SuccessFactors solutions, SAP provides a self-service tool to analyze the existing SAP ERP HCM implementation in preparation for a transition to SAP SuccessFactors. This early insight enables you to scope and plan your transition project with a higher degree of accuracy. For useful transactional data and history, we recommend running SAP Readiness Check for SAP SuccessFactors solutions on a production system (or a recent copy of one).</p>\n          <p>Based on the analysis of the source system, the tool provides a comprehensive overview of specific parameters throughout various topics:</p>\n          <p><strong><cite>Employee Insights:</cite></strong> This reveals the distribution of employee master data figures per region, process, or status to be used in further discussions with an implementation partner to assess the migration project complexity. Employee Insights contains the following cards:</p>\n          <p>\n            <ul>\n                  <p><li><cite>Employee Overview:</cite> This card provides an overview of the employee master data in the analyzed system, displaying the infotypes used per country and the distribution of employees by country. Employees are shown as active or inactive. Inactive employees may indicate potential obsolete master data which could be included in a data cleansing step before the migration.</li></p>\n              <p><li><cite>Administrative Structure:</cite> This card provides an overview of the administrative structure and how employees are assigned to this structure and contract elements in the analyzed system.</li></p>\n              <p><li><cite>Process Results:</cite> This card provides an overview of how payroll, time management, and other processes are managed in the analyzed system.</li></p>\n              </ul>\n          </p>\n          <p><strong>Organizational Structure Analysis:</strong> Insight into the organizational structure master data will help to establish an understanding of key figures related to the allocation of employee-related data. Organizational Structure Analysis contains the following cards:</p>\n          <p>\n            <ul>\n                  <p><li><cite>Organizational Structure Overview:</cite> The scope of the check is to show the current state of the organizational structure master data and to focus on those topics that need to be analyzed to assess implementation complexity and readiness.</li></p>\n              <p><li><cite>Organizational Structure Objects:</cite> The organizational structure consists of objects and their relationships; in particular, Organizational Units (O), Positions (S), and Persons (P) within the SAP ERP HCM system. These objects are linked by the relationship between the objects. The analysis highlights object types used to help identify the scope of objects that may need to be created in Employee Central.</li></p>\n              </ul>\n          </p>\n          <p><strong>Important HCM Data:</strong> This provides insight into other HCM objects relevant for the HCM system setup. Examples of these objects are payroll calendars and payroll frequency defined within the analyzed system.  </p>\n          <p><strong>Custom Code Analysis:</strong> This provides insight into the volume and variety of custom code objects. Custom Code Analysis contains followings cards:</p>\n          <p>\n            <ul>\n                  <p><li><cite>HCM-Specific Custom Objects:</cite> SAP Readiness Check for SAP SuccessFactors solutions provides information about the enhancements made to the SAP ERP HCM system and lists different types of ABAP developments. Reviewing this list with SAP or an implementation partner helps customers understand those enhancements that need to be carried forward to the target solution, adapted, or retired.</li></p>\n              <p><li><cite>Interfaces:</cite> This section provides an overview of the standard and non-standard interfaces that were identified in the SAP ERP HCM system (that is, IDoc, Web service, RFC and BAPI, BW extractors, OData services, SLT replication, and flat file). Some of these custom solutions may be required in the target solution and could influence the design of the target architecture.</li></p>\n              <p><li><cite>Custom Code Analysis:</cite> This section provides an inventory of identified custom ABAP objects within the SAP ERP HCM system, grouped by object type. Some of these custom extensions may be required in the target solution and could influence the design of the target architecture. It should be checked whether the functionality that is currently delivered by the identified custom code is available as a standard option in the target architecture.</li></p>\n              </ul>\n          </p>\n        ", "disclaim": "<p>The findings presented by SAP Readiness Check include a mix of both client-dependent and client-independent results. If the analyzed system contains more than one active client, it is recommended to collect and upload the results as separate analyses. More information about the nature of each check is available within the respective <cite>Learn More</cite> side panel content.</p>", "title": "Learn More About SAP SuccessFactors Solutions"}