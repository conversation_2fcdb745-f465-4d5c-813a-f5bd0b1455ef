{"next": "\n          \n          \t<p>\n              To see interface-specific information in the <cite>Learn More</cite> side panel, choose an interface tab above the chart.\n            </p>\n          \n          \n          \n          \n          \n          \n          <p>\n            Check all interfaces with the impact category <cite>Functionality Unavailable</cite>. In the <cite>Details</cite> column in the table below, you can find corresponding simplification items, business impact notes, or SAP Notes reported by the ABAP test cockpit analysis (if available). You might need to redesign and/or rebuild the interface, or even remove it completely from the system if it becomes obsolete in SAP S/4HANA.\n          </p>\n        \n          \n            <p>\n              Check all interfaces with the impact category <cite>Serialization Issue</cite>: \n            </p>\n            <ul>\n              <li>\n                For Remote Function Call (RFC) interfaces, note that the impact is only relevant for external inbound calls. Perform the following steps: \n                <ol>\n                  <li>Ensure that the identified RFC interface is inbound and that the call comes from an external system; otherwise consider this to be a false-positive finding.</li>\n                  <li>For standard function modules, follow the procedure described in SAP Note <a href=\"https://me.sap.com/notes/2408693\">2408693</a>. Depending on the feedback from SAP, an interface might be exempted as an exception.</li>\n                  <li>For custom function modules, adjust the signature of the function module either on the remote side or on the SAP S/4HANA side to restore binary compatibility. Alternatively, familiarize yourself with the fast RFC serialization, which might offer an easier solution if a high number of function modules needs to be corrected. For more information, see SAP Note <a href=\"https://me.sap.com/notes/2315100\">2315100</a>.</li>\n                </ol>\n              </li>\n              <li>\n                For IDoc interfaces, rework data types behind custom fields. For example, change the associated data element MATNR to MATNR18 to restore field length and, if required, add a new field with a long material number using data element MATNR. For more information, see SAP Note <a href=\"https://me.sap.com/notes/2218350\">2218350</a>. \n              </li>\n            </ul>\n          \n          \n            <p>\n              Check all interfaces with the impact category <cite>Blocked</cite> and find out if identified blocked objects can be substituted. Search for simplification items in the functional area the object belongs to. You might need to redesign and/or rebuild the interface, or even remove it completely from the system if it becomes obsolete in SAP S/4HANA.\n            </p>\n          \n          \n          \n            <p>\n              The custom code scan results that are based on the ABAP test cockpit were uploaded to the current SAP Readiness Check analysis. However, the results do not contain any findings related to <cite>S/4HANA: IDoc Check</cite>. This might indicate that this check was not included in the ABAP test cockpit run. Please make sure that <cite>S/4HANA: IDoc Check</cite> is part of the ABAP test cockpit check variant (by default, <cite>S4HANA_READINESS</cite>) used for your custom code analysis. If the check is not included, please follow SAP Note <a href=\"https://me.sap.com/notes/3035012\">3035012</a> and manually include the check in your ABAP test cockpit check variant. Then reschedule the ABAP test cockpit run and upload the results to the current SAP Readiness Check analysis again.\n            </p>\n          \n            <p>\n                Once the interfaces identified as impacted are analyzed and adjusted or removed, continue with the testing of the most critical interfaces.\n            </p>\n            <p>\n                For more information, see:\n                <ul>\n              <li><a href=\"https://blogs.sap.com/2021/09/27/extended-integration-analysis-in-sap-readiness-check-is-now-live/\">Extended Integration Analysis in SAP Readiness Check</a> blog series</li>\n                    <li>SAP Note <a href=\"https://me.sap.com/notes/2500202\">2500202</a> (central SAP Note for BW extractors in SAP S/4HANA)</li>\n                    <li>SAP Note <a href=\"https://me.sap.com/notes/2416705\">2416705</a> (blocked Remote Function Calls)</li>\n                    <li>SAP Note <a href=\"https://me.sap.com/notes/2215424\">2215424</a> (field length extension of material numbers)</li>\n                    <li>SAP Note <a href=\"https://me.sap.com/notes/2218350\">2218350</a> (field length extension of material numbers in IDoc interfaces and/or application link enabling)</li>\n                    <li>SAP Note <a href=\"https://me.sap.com/notes/2781766\">2781766</a> (enabling the export of the ABAP test cockpit check results for SAP Readiness Check)</li>\n                    <li>SAP Note <a href=\"https://me.sap.com/notes/3322944\">3322944</a> (enabling the data collector of flat file interfaces and information about the file interface discovery tool)</li>\n                    <li>If you are using SAP Landscape Transformation Replication Server, refer to SAP Note <a href=\"https://me.sap.com/notes/2755741\">2755741</a> (potential impact of the replication from SAP Landscape Transformation Replication Server during system conversions) and <a href=\"https://help.sap.com/doc/9d634180094347038e7b8c96661dc36b/latest/en-US/Compatibility_Views_and_SAP_LT_Replication_Server.pdf\">Compatibility Views and SAP Landscape Transformation Replication Server</a> (impact of compatibility views on the replication in detail).</li>\n                </ul>\n            </p>\n        ", "info": "\n          <p>\n            For the transition to SAP S/4HANA, a selection of follow-up activities in the area of integration is required due to certain simplifications in different application areas. \n          </p>\n          <p>\n            This section contains a list of identified interfaces and highlights the ones that are most certainly impacted by the transition.\n          </p>\n          <p>\n            The header displays key features about the identified interfaces, and groups the interface types by the following tabs: \n            <ul>\n                <li><cite>IDoc Interfaces:</cite> Active and inactive IDoc interfaces that are configured in the system are displayed. The identification of the interfaces is based on the outbound (table EDP13) and inbound (table EDP21) partner profiles, while the usage information is pulled from table EDIDC.</li>\n                <li><cite>Web Service Interfaces:</cite> All consumer and provider ABAP proxies that are both registered and not registered in SOA Manager are displayed. The usage is based on the Web Services runtime data (class CL_SOAP_DB_MONI).</li>\n                <li><cite>RFC and BAPI Interfaces:</cite> The interfaces are collected based on the runtime statistics (transaction ST03N). The same statistics provide the usage information. The list is extended with all Remote-Function-Call-enabled (RFC-enabled) custom function modules, which represent explicitly defined potential RFC interfaces.</li>\n                <li><cite>BW Extractors:</cite> The BW extractors are identified based on the ROOSOURCE table.</li>\n              \n          <li><cite>OData Services:</cite> The inventory of OData services in this analysis includes interfaces for OData versions 2 and 4.</li>\n          The interface determination logic detects all OData services based on programming models available from SAP_BASIS releases 700 and above:\n            <ul style='list-style-type:circle'>\n              <li>SAP Gateway Service Builder</li>\n              <li>SAP ABAP programming model</li>\n              <li>SAP Service Adaptation Description Language (SADL)</li>\n              <li>SAP ABAP RESTful Application Programming Model (RAP)</li>\n              <li>Core Data Services (CDS) views exposed as OData services</li>\n            </ul>\n          There is no usage information associated with the OData services. \n          \n              <li><cite>SLT Replication</cite>: A list of tables for which SAP Landscape Transformation Replication Server triggers were created is displayed. The main data source is the function module IUUC_GET_TRIGGERS. Instead of usage data, the number of table rows is displayed for the analyzed client (MANDT).</li>\n              \n          <li><cite>Flat File Interfaces</cite>: A list of active flat file interfaces is provided. Flat file interfaces are not centrally managed and are identified indirectly using the file interface discovery tool (SAP Note <a rel='noopener noreferrer' href=\"https://me.sap.com/notes/3322944\">3322944</a>). Using the aggregated authorization trace, the data collector detects the OPEN DATASET ABAP command, which is used by flat file interfaces to access the underlying file system. The data collector evaluates and aggregates the data during a specific time frame and compiles a list of active flat file interfaces.\n                <br/>The data collection period defines the precision of the analysis results. Due to different project requirements and objectives, no general recommendation about the duration of the data collection period can be provided.  Instead, the project team needs to evaluate and agree on the duration. If required, the analysis data of flat file interfaces can be uploaded to the current SAP Readiness Check analysis several times.\n                <br/>The data collection process for flat file interfaces runs independently from the rest of the SAP Readiness Check framework. This decoupled approach enables the time-consuming analysis of flat file interfaces to continue without impacting the completion of the other SAP Readiness Check data collectors. The flat file analysis can start before or after performing the rest of the SAP Readiness Check analyses.\n                <br/>The data collector aggregates and filters the analysis results of the flat file interfaces to focus on interfaces relevant to SAP Readiness Check and exclude interfaces classified as SAP technical file interfaces (for example, in the transport system or for interfaces related to ABAP design time traces). The list of used filter criteria is available in SAP Note <a rel='noopener noreferrer' href=\"https://me.sap.com/notes/3322944\">3322944</a>. Use the file interface discovery tool to explore the complete flat file interface analysis data.</li>\n        \n            </ul>\n          </p>\n          <p>\n            The chart and table provide an overview of discovered interfaces and the results of the impact analysis performed by SAP Readiness Check. The analysis focuses on the most critical impact types that require immediate action during system conversion. It does not cover topics such as using outdated technology, security risks, performance, and so on.\n          </p>\n          <p>\n            The analysis covers the following impact types:\n            <ul>\n                  <li><cite>Functionality Unavailable</cite>:\n                      <p>\n                        One standard ABAP object directly associated with an interface or referenced by an associated custom ABAP object is on the simplification list. The identified objects can be categorized as follows:\n                        <ul style='list-style-type:circle'>\n                            <li>Standard objects are checked against the simplification item database and the data dictionary of the target SAP S/4HANA release. The findings include cases where an object is missing in the target release, exists but is neither used nor supported, and where a table is partially used in SAP S/4HANA compared to SAP ERP.</li>\n                            <li>Custom objects are checked against the ABAP test cockpit scan results. The results become available once you upload the ABAP test cockpit results (S4HANA_READINESS) to your SAP Readiness Check analysis.</li>\n                        </ul>\n                      </p>\n                  </li>\n                <li><cite>Serialization Issue</cite>:\n                      <p>\n                        One of the objects associated with an interface becomes binarily incompatible upon system conversion due to a field length extension. These interfaces should not be used without adjustments on the caller side to avoid possible data inconsistencies.\n                      </p>\n                      <p>\n                        There are three types of cases:\n                        <ul style='list-style-type:circle'>\n                            <li>An SAP S/4HANA version of a standard function module and its SAP ERP version are binary incompatible. These function modules are listed in the external blocklist. </li>\n                            <li>A custom function module becomes binarily incompatible upon system conversion. The ABAP test cockpit identifies such custom function modules.</li>\n                            <li>A custom or extension IDoc segment becomes binarily incompatible upon system conversion. The ABAP test cockpit identifies such IDoc segments.</li>\n                        </ul>\n                      </p>\n                      <p>\n                         Interfaces with this impact category will either dump with a syntax error on first use when calling to a missing object or become unstable, as the referenced standard object is neither supported nor maintained in SAP S/4HANA.\n                      </p>\n                </li>\n                <li><cite>Blocked</cite>:\n                      <p>\n                        One of the standard programs or function modules associated with an interface is on the internal blocklist and cannot be used with SAP S/4HANA. Interfaces with this impact category will dump on first use when accessing to a blocked object.\n                      </p>\n                </li>\n\n            </ul>\n          </p>\n          \n        ", "disclaim": "\n          <p>\n            The interface analysis identifies interfaces that depend on restricted functionality or break upon transition. The analysis is not intended to be exhaustive and does not produce a complete worklist for the interface adaptation.\n          </p>\n          <p>The definition and development objects of all interface types are client independent. However, the configuration and usage determination for the interfaces can be client dependent. If the analyzed system contains more than one active client, we recommend running a separate SAP Readiness Check analysis for each active client.</p>\n          ", "title": "Learn More About Integration", "integration": "<p>\n          The integration with SAP Cloud ALM enables you to set up references between SAP Readiness Check findings and your SAP Cloud ALM project. With this integration functionality, you can create new and assign existing follow-ups (that is, requirements and user stories) to the findings within your SAP Readiness Check analysis, and manage these follow-ups in your SAP Cloud ALM project. As a result, this functionality allows you to thoroughly oversee and administrate your SAP Cloud ALM project, providing an end-to-end traceability.<br/><br/>\n          To use the integration functionality, you can select one or more findings in the <cite>Items</cite> table within the current check and choose <cite>Follow-Up</cite> above the table. By choosing <cite>Create New</cite> or <cite>Assign to Existing</cite> in the dropdown list, the subsequent popup windows guide you through the process of creating one or more follow-ups for the selected items or assigning the selected items to an existing follow-up. Note that new follow-ups can only be created for findings in the <cite>Items</cite> table if no follow-ups have been assigned yet. <br/><br/>\n          The <cite>Follow-Up Status</cite> column indicates the progress of the creation and assignment of follow-ups: \n          <ul>\n            <li><cite>Assigned</cite> (in all checks except the <cite>Simplification Items</cite> check): You can create one follow-up for each SAP Readiness Check finding. If a finding is already assigned to a follow-up, the follow-up status is <cite>Assigned</cite>. </li>\n            <li><cite>Partially</cite> (only in the <cite>Simplification Items</cite> check): You can create one or more follow-ups for activities and/or consistency checks belonging to one simplification item. If there are any activities and/or consistency checks related to a simplification item that are not assigned to follow-ups yet, the follow-up status is <cite>Partially</cite>.</li>\n            <li><cite>All Assigned</cite> (only in the <cite>Simplification Items</cite> check): You can create one or more follow-ups for activities and/or consistency checks belonging to one simplification item. If all activities and/or consistency checks related to a simplification item are assigned to follow-ups, the follow-up status is <cite>All Assigned</cite>. </li>\n          </ul>\n          To view follow-ups of your SAP Readiness Check findings in SAP Cloud ALM, choose the status of an item in the <cite>Follow-Up Status</cite> column. The follow-up title will take you to the related requirement or user story in SAP Cloud ALM. <br/><br/>\n          To change the integration settings for the current SAP Readiness Check analysis, go to the <cite>SAP Readiness Check Integration</cite> app on the SAP Cloud ALM launchpad.<br/><br/>\n        </p>"}