{"next": "\n            <p>This section shows the identified ABAP enhancements in your SAP CRM system in more detail, grouped by object type, development package, or CRM area using the views above the chart.</p>\n            <p>To evaluate the findings, you can perform the following tasks:</p>\n            <p>\n                <ul>\n                    <li>Contact SAP or your preferred implementation partner to check whether the functionality that is currently delivered by the identified custom code is available as a standard option in the target architecture.</li>\n                    <li>If SAP S/4HANA is a part of the target architecture, see SAP Note <a rel='noopener noreferrer' href='https://me.sap.com/notes/1912445'>1912445</a> (ABAP custom code migration for SAP HANA).</li>\n                    <li>For a detailed analysis of your custom code, run the ABAP test cockpit. This tool can be executed on a locally installed SAP NetWeaver 7.52 system, on an SAP S/4HANA sandbox, or by using the Custom Code Migration app in the SAP BTP, ABAP environment.</li>\n                    <li>Use the <a rel='noopener noreferrer' href='https://blogs.sap.com/2017/04/06/abap-call-monitor-scmon-analyze-usage-of-your-code/'>ABAP call monitor (SCMON)</a> to assess which part of the existing custom code is actually used.</li>\n                </ul>\n            </p>", "info": "\n            <p>Before your transition, a custom code analysis is required to find out whether and how enhancements to the standard functionality are used in your SAP CRM solution. The analysis is fundamental to understand the volume, complexity, and usage of custom code in the analyzed system. Depending on your target architecture, this helps you to determine how to proceed, and to plan the transition of relevant enhancements.</p>\n            <p>This section shows the identified ABAP enhancements in your SAP CRM system in more detail, grouped by object type, development package, or CRM area using the views above the chart.</p>\n            <p>In addition, the area of the SAP CRM solution to which each enhancement is related can be selected. You can select an individual enhancement from any view and categorize that line, or select multiple lines and categorize them by choosing the <cite>Set CRM Area</cite> button. You can also filter and select several lines by choosing a bar in the chart from any of the views.</p>\n            <p>Note that you might need to redesign, rebuild, or disable enhancements in the analyzed system if they become obsolete or redundant in the target architecture.</p>", "disclaim": "\n            <p>The data collected for the current check is client independent.</p>", "title": "Learn More About Custom Code - ABAP Enhancements"}