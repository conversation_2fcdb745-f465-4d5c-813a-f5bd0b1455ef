{"Request": {"Number": "2255261", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 604, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013349792017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002255261?language=E&token=4B2381CE78B13677D7325C79EACD4C09"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002255261", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002255261/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2255261"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.06.2018"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-MON-BPM-ANA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Analytics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Monitoring & Alerting", "value": "SV-SMG-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Process Operations", "value": "SV-SMG-MON-BPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-MON-BPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Analytics", "value": "SV-SMG-MON-BPM-ANA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-MON-BPM-ANA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2255261 - Corrections for ST-A/PI 01S SP0 (framework)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>1. Performance problem in form TCURX_CURRENCIES when reading table LT_TCURX<br />2. A problem is solved when you use ad hoc analysis and jump into the detail analysis using several filter values for the same characteristic.<br />3. Error \"No Detail List available\" while jumping into Detail List within ST13 TBI_REPORTS for Customer Exit&#180;s<br />4. In analytics you get the technical name of a semantic ID shown instead of the value of a characteristic (foer example ERP_BUKRS).<br />&#160;&#160;&#160; This happens only for those monitoring objects which&#160;are defined without using&#160;the functionality of defining a&#160;mapping definition&#160;between the detail list fields and the cube fields.<br />5. Occasionally measured values shown in analytics are seem to be multiplied by an integer&#160;factor. Reason for this behaviour is the fact that the same data package is retrieved several&#160;&#160;times from the managed system.<br />6. Correction&#160;because of&#160;GDPR. User Data will&#160;be saved in Detail List depending on Authority-Check.&#160;&#160;OBJECT&#160;'SM_BPM_DET'&#160;/&#160;'FUNC_MS' =&#160;'BENCHMARKING_ADV_USR'</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Business Process Monitoring, Business Process Analytics</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Coding error, coding improvement</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Coding corrections provided with this note</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-MON-BPM-MON (Monitoring)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D028828)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D028828)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002255261/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002255261/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002255261/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002255261/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002255261/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002255261/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002255261/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002255261/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002255261/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2748075", "RefComponent": "SV-SMG-MON-BPM-ANA", "RefTitle": "Message error \"Error while data collection\" in job log while running RC_VALUE_DISCOVERY_COLL_DATA", "RefUrl": "/notes/2748075 "}, {"RefNumber": "2494103", "RefComponent": "SV-SMG-MON-BPM-MON", "RefTitle": "Troubleshooting for Table Entry Counter unavailable for selection in Solution Documentation for SAP Solution Manager 7.2", "RefUrl": "/notes/2494103 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-A/PI", "From": "01S_640", "To": "01S_640", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01S_700", "To": "01S_700", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01S_710", "To": "01S_710", "Subsequent": ""}, {"SoftwareComponent": "ST-A/PI", "From": "01S_731", "To": "01S_731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-A/PI", "NumberOfCorrin": 6, "URL": "/corrins/0002255261/389"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ST-A/PI", "ValidFrom": "01S_640", "ValidTo": "01S_731", "Number": "2255261 ", "URL": "/notes/2255261 ", "Title": "Corrections for ST-A/PI 01S SP0 (framework)", "Component": "SV-SMG-MON-BPM-ANA"}, {"SoftwareComponent": "ST-A/PI", "ValidFrom": "01S_640", "ValidTo": "01S_731", "Number": "2351176 ", "URL": "/notes/2351176 ", "Title": "Main Extractor for Analytics does not retrieve any data", "Component": "SV-SMG-MON-BPM-ANA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}