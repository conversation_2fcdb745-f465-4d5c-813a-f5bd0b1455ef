{"Request": {"Number": "973450", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 238, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019013722017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000973450?language=E&token=7C267D38FC8B66FA60A4F26E0A595FE9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000973450", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000973450/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "973450"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 25}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.08.2021"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "973450 - Oracle Database network encryption and data integrity"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>Secure data transfer between Oracle Database and the SAP application</strong></p>\r\n<p>This SAP Note describes how the data transmitted between the SAP application servers (SAP application) and Oracle Database can be encrypted using Oracle Database functions and how the integrity of the transmitted data can be ensured.</p>\r\n<p>This SAP Note applies for all SAP installations based on the SAP NetWeaver architecture with Oracle Database Version 10.2 or above.</p>\r\n<ul>\r\n<li>Oracle Database 19c</li>\r\n<li>Oracle Database 18c</li>\r\n<li>Oracle Database 12c</li>\r\n<li>Oracle Database 11g (11.2)</li>\r\n<li>Oracle Database 10g (10.2)</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Caution</span></p>\r\n<p>This SAP Note describes the configuration of the Oracle Database Native Network Encryption. The encryption of data traffic using Secure Socket Layer (SSL), so using TCPS or TCP/SSL, is not described in this SAP Note.</p>\r\n<p>The use of TCPS or TCP/SSL for the encryption of data traffic between Oracle Database and SAP NetWeaver is permitted but without support from SAP (no support for configuration questions or if problems occur). For more information, see SAP Note <a target=\"_blank\" href=\"/notes/105047\">105047</a>.</p>\r\n<p><span style=\"text-decoration: underline;\">References</span></p>\r\n<p>For a detailed description about Oracle Database network encryption or Oracle Database data integrity, see the Oracle Database documentation (<a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/index.html\">https://docs.oracle.com/en/database/oracle/oracle-database/index.html</a>) for the release in question in the 'Security' area of the 'Database Security Guide' or 'Advanced Security Guide'.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Oracle Release</td>\r\n<td>Oracle documentation</td>\r\n</tr>\r\n<tr>\r\n<td>19c</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/dbseg/part_4.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/dbseg/part_4.html</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>18c</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/18/dbseg/part_4.html\">https://docs.oracle.com/en/database/oracle/oracle-database/18/dbseg/part_4.html</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>12c (12.2)</td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/dbseg/part_4.html\">https://docs.oracle.com/en/database/oracle/oracle-database/12.2/dbseg/part_4.html</a></td>\r\n</tr>\r\n<tr>\r\n<td>12c (12.1)</td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/DBSEG/part_4.htm\">https://docs.oracle.com/database/121/DBSEG/part_4.htm</a></td>\r\n</tr>\r\n<tr>\r\n<td>11g (11.2)</td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/cd/E11882_01/network.112/e40393/asoconfg.htm\">https://docs.oracle.com/cd/E11882_01/network.112/e40393/asoconfg.htm</a></td>\r\n</tr>\r\n<tr>\r\n<td>10g (10.2)</td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/cd/B19306_01/network.102/b14268/asoconfg.htm\">https://docs.oracle.com/cd/B19306_01/network.102/b14268/asoconfg.htm</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Table: References</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle Database security, data protection, data integrity<br />Oracle Database own network encryption, Oracle Database Native Network Encryption<br />Data integrity<br />Crypto checksumming</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Secure, protected, encrypted data transfer between SAP NetWeaver and Oracle Database via Oracle Database Native Network Encryption</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong><strong>Secure data transfer between Oracle Database and the SAP application</strong></strong></p>\r\n<p>As administrator, you want to ensure that data is transferred securely, that is, the data should be protected from being read and/or corrupted by unauthorized personnel. The Advanced Security Option (ASO) of Oracle Database provides various procedures and algorithms for encrypting the data traffic as well as crypto-checksum procedures that ensure data integrity.</p>\r\n<ul>\r\n<li>Encryption protects data from unauthorized access during transmission.</li>\r\n<li>Checksum procedures detect 'man-in-the-middle' data corruption.</li>\r\n</ul>\r\n<p>Encryption and checksums in combination ensure a secure, reliable, and protected data transfer.</p>\r\n<p><span style=\"text-decoration: underline;\">Oracle Advanced Security (ASO) option/adapters</span><br /><br />In Oracle Releases &gt;= 10.2, the Oracle Advanced Security (ASO) option is installed by default in an SAP installation, even if you do not use it. You can use the '<span style=\"text-decoration: underline;\">adapters</span>' command to check that this option is installed and which algorithms are supported.</p>\r\n<p>Example:</p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;OS&gt;&#x00A0;adapters</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Installed Oracle Net transport protocols are:</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; IPC</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; BEQ</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; TCP/IP</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; SSL</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; RAW</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; SDP/IB</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; ExaDirect</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Installed Oracle Net naming methods are:</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; Local Naming (tnsnames.ora)</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; Oracle Directory Naming</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; Oracle Host Naming</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">Installed Oracle Advanced Security options are:</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; RC4 40-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; RC4 56-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; RC4 128-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; RC4 256-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; DES40 40-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; DES 56-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; 3DES 112-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; 3DES 168-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; AES 128-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; AES 192-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; AES 256-bit encryption</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; MD5 crypto-checksumming</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; SHA-1 crypto-checksumming</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; Kerberos v5 authentication</span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#x00A0;&#x00A0;&#x00A0; RADIUS authentication</span></p>\r\n<p><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">OS&gt;</span></p>\r\n<p><span style=\"text-decoration: underline;\">Supported algorithms</span></p>\r\n<p>Oracle Database supports various algorithms for network encryption and checksum procedures depending on the version of Oracle Database and the type and version of the Oracle client. The Oracle documentation describes which algorithms are supported and how they are configured in detail.</p>\r\n<ul>\r\n<li>For Oracle Database 12.1 or higher: In the Oracle Database Security Guide</li>\r\n<li>For Oracle Database 10.2 and 11.2: In the Advanced Security Administrator&#x2019;s Guide</li>\r\n</ul>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Oracle Release</td>\r\n<td>Oracle documentation</td>\r\n<td>Parameters</td>\r\n</tr>\r\n<tr>\r\n<td>19c</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/dbseg/part_4.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/dbseg/part_4.html</a></p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/dbseg/data-encryption-and-integrity-parameters.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/dbseg/data-encryption-and-integrity-parameters.html</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>18c</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/18/dbseg/part_4.html\">https://docs.oracle.com/en/database/oracle/oracle-database/18/dbseg/part_4.html</a></p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/18/dbseg/data-encryption-and-integrity-parameters.html\">https://docs.oracle.com/en/database/oracle/oracle-database/18/dbseg/data-encryption-and-integrity-parameters.html</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>12c (12.2)</td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/dbseg/part_4.html\">https://docs.oracle.com/en/database/oracle/oracle-database/12.2/dbseg/part_4.html</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/12.2/dbseg/data-encryption-and-integrity-parameters.html\">https://docs.oracle.com/en/database/oracle/oracle-database/12.2/dbseg/data-encryption-and-integrity-parameters.html</a></td>\r\n</tr>\r\n<tr>\r\n<td>12c (12.1)</td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/DBSEG/part_4.htm\">https://docs.oracle.com/database/121/DBSEG/part_4.htm</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/database/121/DBSEG/asoappa.htm\">https://docs.oracle.com/database/121/DBSEG/asoappa.htm</a></td>\r\n</tr>\r\n<tr>\r\n<td>11g (11.2)</td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/cd/E11882_01/network.112/e40393/asoconfg.htm\">https://docs.oracle.com/cd/E11882_01/network.112/e40393/asoconfg.htm</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/cd/E11882_01/network.112/e40393/asoappa.htm\">https://docs.oracle.com/cd/E11882_01/network.112/e40393/asoappa.htm</a></td>\r\n</tr>\r\n<tr>\r\n<td>10g (10.2)</td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/cd/B19306_01/network.102/b14268/asoconfg.htm\">https://docs.oracle.com/cd/B19306_01/network.102/b14268/asoconfg.htm</a></td>\r\n<td><a target=\"_blank\" href=\"https://docs.oracle.com/cd/B19306_01/network.102/b14268/asoappa.htm\">https://docs.oracle.com/cd/B19306_01/network.102/b14268/asoappa.htm</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#x00A0;</p>\r\n<p><strong>Prerequisites</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Oracle license</span></p>\r\n<p>Up to and including Release 10.2, the use of features of the Oracle Advanced Security Option is subject to a license.&#x00A0;See SAP Note <a target=\"_blank\" href=\"/notes/740897\">740897</a>. As of Oracle Database 11g Release 2 (11.2), Oracle Database network encryption and network data integrity are no longer part of the Advanced Security Option. For more detailed information, see the Oracle Database Licensing Guide for the relevant Oracle Database release.</p>\r\n<ul>\r\n<li>19c: <a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/dblic/Licensing-Information.html\">https://docs.oracle.com/en/database/oracle/oracle-database/19/dblic/Licensing-Information.html</a></li>\r\n<li>11.2: <a target=\"_blank\" href=\"http://docs.oracle.com/cd/E11882_01/license.112/e47877/options.htm#DBLIC143\">http://docs.oracle.com/cd/E11882_01/license.112/e47877/options.htm#DBLIC143</a></li>\r\n</ul>\r\n<p>From the Oracle Database Licensing Information User Manual for 19c:</p>\r\n<p style=\"padding-left: 30px;\"><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">&#xFEFF;Network encryption (native network encryption, network data integrity, and SSL/TLS) and strong authentication services (Kerberos, PKI, and RADIUS) are no longer part of Oracle Advanced Security and are available in all licensed editions of all supported releases of Oracle Database.&#xFEFF;</span></p>\r\n<p><span style=\"text-decoration: underline;\">SAP Kernel and Oracle Release</span></p>\r\n<p>The prerequisites for using Oracle Database with its own network encryption and data integrity in an SAP NetWeaver environment are as follows:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>SAP Kernel</td>\r\n<td>Oracle Database server</td>\r\n<td>Oracle client version</td>\r\n</tr>\r\n<tr>\r\n<td>&gt;= 7.xx</td>\r\n<td>&gt;= 10.2</td>\r\n<td>&gt;= 10.2</td>\r\n</tr>\r\n<tr>\r\n<td>&gt;= 6.40</td>\r\n<td>&gt;= 10.2</td>\r\n<td>&gt;= *******</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><span style=\"text-decoration: underline;\">General information</span></strong></p>\r\n<p>The following relates to a) the protection of data transmitted between Oracle Database and the SAP application using Oracle Database encryption and b) ensuring the correctness of the transmitted data using checksum procedures (data integrity).</p>\r\n<p>Both functions - encryption procedures and checksum procedures - can be activated independently of each other. By default, both functions are switched off. From the point of view of the SAP application, the use of both functions is completely transparent.</p>\r\n<p>As a system administrator or database administrator, you decide whether you want to a) use encryption and, if so, which algorithms to use for it and b) use checksum procedures and, if so, which algorithms to use for that. The algorithm determines the security and speed, and therefore the performance overhead. For each database connection, exactly one algorithm is used for encryption and exactly one algorithm is used for data integrity.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">General recommendations</span></strong></p>\r\n<p>It makes sense to activate both functions, encryption procedures and checksum procedures, together.</p>\r\n<p>You can leave the decision about the best algorithm to the database without impairing security.&#x00A0;The database decides on the optimal algorithm for encryption and data integrity.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SQLNET.ORA</strong></span></p>\r\n<p>Both functions, so encryption and checksumming, are configured using parameters in the Oracle Net Services parameter file sqlnet.ora. This is located as follows:</p>\r\n<ul>\r\n<li>On the database server: $ORACLE_HOME/network/admin</li>\r\n<li>On the SAP application server: $TNS_ADMIN</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">SAP NetWeaver ABAP/SAP NetWeaver Java</span></p>\r\n<p>For SAP NetWeaver (ABAP), the configuration can be performed on both the database server and on the SAP application servers. <br />For SAP NetWeaver (Java), the configuration must be performed centrally on the database server.<br />Central configuration on the database server is therefore generally recommended. This recommendation applies to all SAP installations and Oracle database architectures:</p>\r\n<ul>\r\n<li>SAP standard installation and SAP MCOD</li>\r\n<li>SAP NetWeaver (ABAP), SAP NetWeaver (Java)</li>\r\n<li>Oracle Single Instance and Oracle RAC</li>\r\n<li>Oracle Non-CDB and Oracle Multitenant (CDB)</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">SQLNET.ORA - various configuration information</span></p>\r\n<ul>\r\n<li>You no longer have to set the SQLNET.CRYPTO_SEED parameter as of Release 10.2.</li>\r\n<li>You can use identical sqlnet.ora parameter files on the client and on the server. It is fine for the sqlnet.ora file on the Oracle client side to contain parameters with the suffix \"_SERVER\" and vice versa.</li>\r\n<li>Parameters with the suffix '_SERVER' apply to the server role. Parameters with the suffix '_CLIENT' apply to the client role. In this case, note that the Oracle database server acts as the client when it uses a database link to access another database (server). For this reason, you should set the parameters for both the server and client role in the sqlnet.ora configuration file of the database server. Then the system uses the database links to encrypt the network traffic. </li>\r\n<li>For Oracle RAC installations, the sqlnet.ora files of all of the RAC instances must be adjusted.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">SQLNET.ORA &#x2013; recommended configuration for SAP NetWeaver</span></p>\r\n<p>Add the following parameters to the file SQLNET.ORA on the database server:</p>\r\n<p style=\"padding-left: 30px;\"><em>SQLNET.ENCRYPTION_SERVER=REQUIRED</em><br /><em>SQLNET.ENCRYPTION_CLIENT=REQUIRED</em><br /><em>SQLNET.CRYPTO_CHECKSUM_SERVER=REQUIRED</em><br /><em>SQLNET.CRYPTO_CHECKSUM_CLIENT=REQUIRED</em></p>\r\n<p>This configuration is suitable for SAP NetWeaver (ABAP) and SAP NetWeaver (Java). With this configuration, the data traffic is encrypted and the integrity of the transmitted data is ensured using checksums. Oracle automatically determines the optimum algorithm.</p>\r\n<p><span style=\"text-decoration: underline;\">SQLNET.ORA -&#x00A0;parameters for the network encryption</span></p>\r\n<p>SQLNET.ENCRYPTION_CLIENT<br />SQLNET.ENCRYPTION_SERVER<br />SQLNET.ENCRYPTION_TYPES_CLIENT (optional)<br />SQLNET.ENCRYPTION_TYPES_SERVER (optional)</p>\r\n<p>The parameters SQLNET.ENCRYPTION_SERVER and SQLNET.ENCRYPTION_CLIENT define whether a connection between the Oracle server and Oracle client is encrypted. With the Oracle default setting (ACCEPTED/ACCEPTED), encryption does not take place. The following table lists all possible combinations:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td colspan=\"4\">SQLNET.ENCRYPTION_CLIENT</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>ACCEPTED</td>\r\n<td>REJECTED</td>\r\n<td>REQUESTED</td>\r\n<td><strong>REQUIRED (*)</strong></td>\r\n</tr>\r\n<tr>\r\n<td rowspan=\"4\">SQLNET.ENCRYPTION_SERVER</td>\r\n<td>ACCEPTED</td>\r\n<td>OFF</td>\r\n<td>OFF</td>\r\n<td>ON</td>\r\n<td>ON</td>\r\n</tr>\r\n<tr>\r\n<td>REJECTED</td>\r\n<td>OFF</td>\r\n<td>OFF</td>\r\n<td>OFF</td>\r\n<td>ORA-12660</td>\r\n</tr>\r\n<tr>\r\n<td>REQUESTED</td>\r\n<td>ON</td>\r\n<td>OFF</td>\r\n<td>ON</td>\r\n<td>ON</td>\r\n</tr>\r\n<tr>\r\n<td><strong>REQUIRED (*)</strong></td>\r\n<td>ON</td>\r\n<td>ORA-12660</td>\r\n<td>ON</td>\r\n<td><strong>ON</strong></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Tab: SQLNET.ENCRYPTION_SERVER/SQLNET.ENCRYPTION_CLIENT combinations</p>\r\n<p>The parameters ENCRYPTION_TYPES_SERVER and ENRYPTION_TYPES_CLIENT define which algorithms may be used. The first algorithm in the list of the Oracle server that is also supported by the Oracle client is used.&#x00A0;Without these parameters, the database selects the best possible algorithm when setting up the connection. \"Best\" means the most secure and then the one with the best performance.</p>\r\n<p><span style=\"text-decoration: underline;\">SQLNET.ORA -&#x00A0;parameters for checksums/data integrity</span></p>\r\n<p>SQLNET.CRYPTO_CHECKSUM_CLIENT<br />SQLNET.CRYPTO_CHECKSUM_SERVER<br />SQLNET.CRYPTO_CHECKSUM_TYPES_CLIENT (optional)<br />SQLNET.CRYPTO_CHECKSUM_TYPES_SERVER (optional)</p>\r\n<p>The parameter SQLNET.CRYPTO_CHECKSUM_SERVER or SQLNET.CRYPTO_CHECKSUM_CLIENT determines whether 'crypto_checksumming', so the data integrity check, is active. The following combinations can occur:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td colspan=\"4\">SQLNET.CRYPTO_CHECKSUM_CLIENT</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>&nbsp;</td>\r\n<td>ACCEPTED</td>\r\n<td>REJECTED</td>\r\n<td>REQUESTED</td>\r\n<td><strong>REQUIRED (*)</strong></td>\r\n</tr>\r\n<tr>\r\n<td rowspan=\"4\">SQLNET.CRYPTO_CHECKSUM_SERVER</td>\r\n<td>ACCEPTED</td>\r\n<td>OFF</td>\r\n<td>OFF</td>\r\n<td>ON</td>\r\n<td>ON</td>\r\n</tr>\r\n<tr>\r\n<td>REJECTED</td>\r\n<td>OFF</td>\r\n<td>OFF</td>\r\n<td>OFF</td>\r\n<td>ORA-12660</td>\r\n</tr>\r\n<tr>\r\n<td>REQUESTED</td>\r\n<td>ON</td>\r\n<td>OFF</td>\r\n<td>ON</td>\r\n<td>ON</td>\r\n</tr>\r\n<tr>\r\n<td><strong>REQUIRED (*)</strong></td>\r\n<td>ON</td>\r\n<td>ORA-12660</td>\r\n<td>ON</td>\r\n<td><strong>ON</strong></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Tab: SQLNET.CRYPTO_CHECKSUM_SERVER/SQLNET.CRYPTO_CHECKSUM_CLIENT combination</p>\r\n<p>The parameter CRYPTO_CHECKSUM_TYPES_SERVER or SQLNET.CRYPTO_CHECKSUM_TYPES_CLIENT defines which checksum procedure should be used for the data integrity check.&#x00A0;If you do not specify these parameters, Oracle chooses the best one when the connection is established.</p>\r\n<p><span style=\"text-decoration: underline;\">SQLNET.ORA -&#x00A0;CVE-2021-2351</span></p>\r\n<p>Due to CVE-2021-2351&#xFEFF; there are new configuration parameters for Oracle Native Network Encryption (NNE) as of SBP 202108:</p>\r\n<p style=\"padding-left: 30px;\"><em>SQLNET.ALLOW_WEAK_CRYPTO_CLIENTS (sqlnet.ora database server)<br /></em><em>SQLNET.ALLOW_WEAK_CRYPTO (sqlnet.ora database client)</em></p>\r\n<p>For more information, see SAP Note <a target=\"_blank\" href=\"/notes/3085920\">3085920</a>.</p>\r\n<p>Reference: MOS 2791571.1</p>\r\n<ul>\r\n<li>The default for both new parameters is 'TRUE' (backward compatibility). Older clients can still successfully establish a database connection with a weaker encryption algorithm.</li>\r\n<li>With&#x00A0;<em>SQLNET.ALLOW_WEAK_CRYPTO_CLIENTS=FALSE</em>, weaker encryption algorithms are no longer allowed. Connections via older clients that do not support sufficiently secure encryption are blocked. The checksum algorithm MD5 is also blocked. Only SHA1 is then allowed.</li>\r\n<li>The following weaker encryption algorithms are less secure and should therefore no longer be used:</li>\r\n<ul>\r\n<li>3DES112, 3DES168</li>\r\n<li>DES, DES40</li>\r\n<li>RC4_40, RC4_56, RC4_128, RC4_256</li>\r\n</ul>\r\n<li>The used algorithms can be identified via <em>V$SESSION_CONNECT_INFO.NETWORK_SERVICE_BANNER</em>. You can only block old clients with SQLNET.ALLOW_WEAK_CRYPTO_CLIENTS=FALSE once all clients are up-to-date and none of the weaker algorithms are used due to older clients.</li>\r\n</ul>\r\n<p><strong>Appendix</strong></p>\r\n<p><span style=\"text-decoration: underline;\">Check of network encryption</span></p>\r\n<p>To check whether data is really actually when it is transferred after you have changed the SQLNET configuration, you can activate Oracle SQL*NET tracing. We recommend that you only do this after you have consulted Oracle support. Note that, in trace mode, the system writes a large number of trace files and this fills the file system very quickly. As a result, the system performance is also considerably reduced. You should therefore always deactivate SQL*Net tracing again after the check.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Oracle SQL*NET Trace (Server)</span></td>\r\n<td><span style=\"text-decoration: underline;\">Oracle SQL*NET Trace (Client)</span></td>\r\n</tr>\r\n<tr>\r\n<td>TRACE_LEVEL_SERVER=SUPPORT</td>\r\n<td>TRACE_LEVEL_CLIENT=SUPPORT</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>The following SQLNET trace files then contain the following entries, which indicate whether or not encryption is active:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><span style=\"text-decoration: underline;\">Encryption</span></td>\r\n<td><span style=\"text-decoration: underline;\">Content of SQL*Net trace</span></td>\r\n</tr>\r\n<tr>\r\n<td>active</td>\r\n<td>[16-AUG-2006 19:42:41:962] na_tns: authentication is not active<br />[16-AUG-2006 7:42:41 PM:962] na_tns: <strong>encryption is active</strong>, using RC4_256<br />[16-AUG-2006 7:42:41 PM:962] na_tns: <strong>crypto-checksumming is active</strong>, using MD5</td>\r\n</tr>\r\n<tr>\r\n<td>not active</td>\r\n<td>[16-AUG-2006 19:40:21:597] na_tns: authentication is not active<br />[16-AUG-2006 19:40:21:597] na_tns: <strong>encryption is not active</strong><br />[16-AUG-2006 19:40:21:597] na_tns: <strong>crypto-checksumming is not active</strong></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If network encryption is active, SQL commands, user names, table names, table columns and so on are no longer available in plain text in the SQLNET trace. If network encryption is not active, you can easily recognize this type of data in the SQLNET trace, and therefore in the transferred data package, too.</p>\r\n<p><span style=\"text-decoration: underline;\">B Compatibility of </span><span style=\"text-decoration: underline;\">SAP J2EE, Java VM, Oracle JDBC driver</span></p>\r\n<p>For the Java stack, the algorithms that are available for Oracle network encryption and data integrity depend on the version of the used Oracle JDBC driver, the SAP J2EE Engine, and the Java VM. The following SAP Notes help you to determine which versions of the aforementioned components are used:</p>\r\n<ul>\r\n<li>SAP Note <a target=\"_blank\" href=\"/notes/867176\">867176</a> - FAQ: Oracle JDBC</li>\r\n<li>SAP Note <a target=\"_blank\" href=\"/notes/2246884\">2246884</a> - How to find the JVM version of the AS Java</li>\r\n</ul>\r\n<p>&#x00A0;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-SEC (Security Messages)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Database System", "Value": "Oracle 10"}, {"Key": "Database System", "Value": "Oracle 10.2"}, {"Key": "Database System", "Value": "Oracle 11.2"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (********)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5014980)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000973450/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000973450/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000973450/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000973450/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000973450/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000973450/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000973450/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000973450/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000973450/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "974876", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Transparent Data Encryption (TDE)", "RefUrl": "/notes/974876"}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268"}, {"RefNumber": "3085920", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Security Alert for CVE-2021-2351 and CPU July 2021 in SAP Environments", "RefUrl": "/notes/3085920"}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094"}, {"RefNumber": "105047", "RefComponent": "BC-DB-ORA", "RefTitle": "Support for Oracle functions in the SAP environment", "RefUrl": "/notes/105047"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2822039", "RefComponent": "BC-DB-ORA", "RefTitle": "TNS-12599 caused by SQLNET.CRYPTO_CHECKSUM_TYPES_SERVER= (MD5)", "RefUrl": "/notes/2822039 "}, {"RefNumber": "3085920", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Security Alert for CVE-2021-2351 and CPU July 2021 in SAP Environments", "RefUrl": "/notes/3085920 "}, {"RefNumber": "974876", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Transparent Data Encryption (TDE)", "RefUrl": "/notes/974876 "}, {"RefNumber": "105047", "RefComponent": "BC-DB-ORA", "RefTitle": "Support for Oracle functions in the SAP environment", "RefUrl": "/notes/105047 "}, {"RefNumber": "1868094", "RefComponent": "BC-DB-ORA-SEC", "RefTitle": "Overview: Oracle Security SAP Notes", "RefUrl": "/notes/1868094 "}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}