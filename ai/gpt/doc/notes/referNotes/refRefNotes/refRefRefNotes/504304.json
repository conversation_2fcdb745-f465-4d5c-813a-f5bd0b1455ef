{"Request": {"Number": "504304", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 588, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015188002017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000504304?language=E&token=1DD62F2B48BAA6163BC6B2F4325BE353"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000504304", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "504304"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released Internally"}, "ReleasedOn": {"_label": "Released On", "value": "10.02.2014"}, "SAPComponentKey": {"_label": "Component", "value": "PY-US-RP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Reporting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "USA", "value": "PY-US", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-US*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Reporting", "value": "PY-US-RP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-US-RP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "504304 - CCR: Cost Center Report - Phase 1"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The Cost Center Report (RPCPCC00) has been developed to facilitate the reconciliation process of balancing payroll results with FI/CO postings.<br />The report is for use by the cost center managers and payroll administrators. It can be used for the following purposes, based on the user's role:</p> <UL><LI><B>Cost Center Manager</B></LI></UL> <UL><UL><LI>Viewing charges incurred on a cost center (or group of cost centers).</LI></UL></UL> <UL><UL><LI>Facilitating cost planning in case of future expansions</LI></UL></UL> <UL><UL><LI>Calculating average cost incurred per employee per cost center.</LI></UL></UL> <UL><LI><B>Payroll Administrator</B></LI></UL> <UL><UL><LI>Reconciling the FI postings with the payroll results. The Payroll Administrator can run the report on a periodic basis (especially during the year-end) to balance the FI postings with the Audit report (Canada) or the Reconciliation report (U.S.).</LI></UL></UL> <UL><UL><LI>Investigating an employee's payroll data to determine a cause of imbalance in payments. This investigation can be initiated by a cost center manager who finds a discrepancy in the regular payments made to employees or a specific employee.<br /></LI></UL></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Cost Center Report, Reconciliation, RPCPCC00<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The Cost Center Report was developed as part of the reconciliation project initiated by SAP. Phase 1 of the report has been released (HRSP details in the <B>Corrections</B> part of this note).<br /><br /><B>NOTE:</B> Phase 2 of the report development has been deferred, pending initial customer feedback. For information on Phase 2 availability, we recommend that you check the monthly development newsletters that are published on:</p> <UL><LI><B>http://service.sap.com/hrusa</B> for the U.S. and</LI></UL> <UL><LI><B>http://service.sap.com/hrcanada</B> for Canada.<br /></LI></UL> <p>If you have feedback that you would like to have considered during Phase 2 of development, please contact the following SAP representatives:</p> <UL><LI>In the U.S., contact Mani Kak, at \"<EMAIL>\".</LI></UL> <UL><LI>In Canada, contact Kim LaPierre, at \"<EMAIL>\".<br /></LI></UL> <p><B>Prerequisites</B><br />The following configuration is necessary for the Cost Center Managers:</p> <UL><LI>Master data: Infotype 105 (Communications), Subtype 0001 (System User Name) has to be set up so that cost center managers can access information for their cost centers.</LI></UL> <UL><LI>A new Wage Type Application group must be created for cost center managers. This is important, as the cost center managers should not be able to view certain wage components for their employees (for example, garnishments). The Wage Type Application area should be set to <B>CCRP </B> (T5963-SAGRP = CCRP). For more information, see the documentation for the following topics:</LI></UL> <UL><UL><LI>Customizing and maintaining wage type applications:<br /><B>IMG -&gt; Payroll U.S (or Canada) -&gt; Reporting -&gt; Reconciliation -&gt; Maintain wage type applications.</B></LI></UL></UL> <UL><UL><LI>Reconciliation report (U.S.) RPCPRRU0</LI></UL></UL> <UL><UL><LI>Audit report (Canada) RPCAUDK0</LI></UL></UL> <UL><LI>You must configure feature <B>10CCM</B> for the default Wage Type Application for a cost center manager. To do this, see the IMG steps:</LI></UL> <UL><UL><LI>U.S.: Payroll USA -&gt; Reporting -&gt; Cost Center Report -&gt; Configure feature 10CCM.</LI></UL></UL> <UL><UL><LI>Canada: Payroll Canada -&gt; Reporting -&gt; Cost Centre Report -&gt; Configure feature 10CCM.<br /><br /><B>Note: The IMG step will not be available until HRSP 49. If you wish to configure the feature immediately, select transaction PE03 and enter 10CCM. </B><br /><br /><B>Caution: Any configuration changes you make to feature 10CCM will be lost (overwritten) once you apply HR Support package 48. Therefore, we recommend that you configure the feature after applying the package. </B></LI></UL></UL> <UL><LI>Payroll results must have been posted before the user can view the Cost Center Report.</LI></UL> <UL><LI>Proper relationship should be maintained between the cost center manager's position and the organization unit the manager is responsible for, in order to use the Cost Center Manager option of the report (Area - Personnel Development, Transaction PO10). For Example:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Organization Unit-------&gt;Relationship----------&gt;Position Production--------------&gt;Is managed by...------&gt;Production Manager</p> <UL><LI>You must have applied, at a minimum, HR Support Package 31 in order to use the report.<br /></LI></UL> <p>NOTE: If you use TEMSE files for payroll posting, the report will not provide any output.<br /><br />Refer to the program documentation (RPCPCC00) for more details.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Apply the HR support package indicated in the <B>Corrections</B> part of this note.<br />The following transactions should be associated to different user roles for the cost center report:</p> <UL><LI>Cost Center Manager&#x00A0;&#x00A0; - PC00_MNA_CC_MAN</LI></UL> <UL><LI>Payroll Administrator - PC00_MNA_CC_ADM<br /></LI></UL> <p>Menu path for the Cost Center Report for the <B>U.S.</B>:<br /><br /><B>Cost Center Manager</B><br />Human Resources -&gt; Payroll -&gt; America -&gt; USA -&gt; Info System -&gt; Payroll results.<br /><br /><B>Payroll Administrator</B><br />Human Resources -&gt; Payroll -&gt; America -&gt; USA -&gt; Subsequent Activities -&gt; Per Payroll Period -&gt; Lists/Statistics<br /><br />Menu path for the Cost Center Report for <B>Canada</B>:<br /><br /><B>Cost Center Manager</B><br />Human Resources -&gt; Payroll -&gt; America -&gt; Canada -&gt; Subsequent Activities -&gt; Period-Independent -&gt; Reporting.<br /><br /><B>Payroll Administrator</B><br />Human Resources -&gt; Payroll -&gt; America -&gt; Canada -&gt; Subsequent Activities -&gt; Per Payroll Period -&gt; Lists/Statistics<br /><br />If you require the report immediately, apply the SAPServ4 files from the following path:<br />/general/R3server/abap/note.0504304/R093681.L9C<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;/K093681.L9C<br />For more information on how to apply SAPServ transports, please refer to OSS Note 0013719.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "PY-CA-RP (Reporting)"}, {"Key": "Transaction codes", "Value": "PE03"}, {"Key": "Transaction codes", "Value": "PO10"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I026522)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I826908)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504304/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504304/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504304/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504304/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504304/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504304/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504304/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504304/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000504304/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "521210", "RefComponent": "PY-US-RP", "RefTitle": "Q&A: Master Note for U.S. Payroll Reporting", "RefUrl": "/notes/521210"}, {"RefNumber": "418176", "RefComponent": "PY-CA", "RefTitle": "Q&A: Master Note on general Canadian Payroll issues.", "RefUrl": "/notes/418176"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "521210", "RefComponent": "PY-US-RP", "RefTitle": "Q&A: Master Note for U.S. Payroll Reporting", "RefUrl": "/notes/521210 "}, {"RefNumber": "693980", "RefComponent": "PY-US-RP", "RefTitle": "CCR: Changes for Internationalization of Cost Center Report.", "RefUrl": "/notes/693980 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "418176", "RefComponent": "PY-CA", "RefTitle": "Q&A: Master Note on general Canadian Payroll issues.", "RefUrl": "/notes/418176 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C49", "URL": "/supportpackage/SAPKE46C49"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C54", "URL": "/supportpackage/SAPKE46C54"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C48", "URL": "/supportpackage/SAPKE46C48"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47001", "URL": "/supportpackage/SAPKE47001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}