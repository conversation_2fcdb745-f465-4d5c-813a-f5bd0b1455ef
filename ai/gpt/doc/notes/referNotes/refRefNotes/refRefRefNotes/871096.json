{"Request": {"Number": "871096", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 398, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015936762017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000871096?language=E&token=3D4C78386DF7408273C27BE64C90235A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000871096", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000871096/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "871096"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 191}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.06.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "871096 - Oracle 10.2.0: Patches/patch collections for Oracle ********"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p></p> <b>This note contains information about required patches and released patch sets for Oracle Release 10.2.0.</b><br /> <p><br />You use Oracle Database Release 10.2 on your SAP system. This note describes which Oracle patch sets are released for use in the SAP environment, and which additional Oracle patch sets you have to install.<br /><br /><B><B>Note that the recommendation given in this note may be subject to change. Therefore, we recommend that you check the latest version of this note once a month and make the required changes.</B></B><br /></p> <b>History of note changes</b><br /> <p></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT>Date</TH><TH ALIGN=LEFT> Change</TH><TH ALIGN=LEFT> Comment</TH></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> <TR><TD>10.06.2010</TD><TD> Patch 9147110 (#62)</TD><TD> New DBV* merge fix</TD></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> <TR><TD>19.01.2010</TD><TD> Patch 9227075</TD><TD> New kdi* merge fix</TD></TR> <TR><TD></TD><TD> replaces 6771608 (#15)</TD></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> <TR><TD>10.11.2009</TD><TD> Patch 7573151 (#61)</TD><TD> Note 1336810</TD></TR> <TR><TD></TD></TR> <TR><TD></TD></TR> <TR><TD>10.08.2009</TD><TD> Patch 8726798</TD><TD> New merge fix</TD></TR> <TR><TD></TD></TR> <TR><TD></TD><TD> Patch 4883635 (#28)</TD></TR> <TR><TD></TD><TD> removed because it is contained in the new merge fix 8726798</TD></TR> <TR><TD></TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD>12.01.2009</TD><TD> Patch 7608184</TD><TD> New Optimizer Merge Fix</TD></TR> <TR><TD></TD><TD> replaces 7168928 (#12)</TD></TR> <TR><TD></TD></TR> <TR><TD></TD><TD> Patch 6663258 (#13)</TD></TR> <TR><TD></TD><TD> removed because it is contained in Optimizer Merge Fix 7608184</TD></TR> <TR><TD></TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD>18.08.2008</TD><TD> Patch 7237154</TD><TD> New kts* Merge Fix</TD></TR> <TR><TD></TD><TD> replaces 6819785 (#20)</TD></TR> <TR><TD></TD></TR> <TR><TD>15.05.2008</TD><TD> Patch 7168928</TD><TD> New Optimizer Merge Fix</TD></TR> <TR><TD></TD><TD> replaces 6740811 (#12)</TD></TR> <TR><TD></TD></TR> <TR><TD>15.07.2008</TD><TD> ******** is released.</TD></TR> <TR><TD></TD></TR> <TR><TD>16.06.2008</TD><TD> Opatch versions</TD><TD> Only OPatch version</TD></TR> <TR><TD></TD><TD> </TD><TD> 4898608 is released.</TD></TR> <TR><TD></TD><TD> </TD><TD> OPatch version 6880880</TD></TR> <TR><TD></TD><TD> </TD><TD> is not released.</TD></TR> <TR><TD></TD></TR> <TR><TD>06.06.2008</TD><TD> Patch 7021805</TD><TD> Due to a new</TD></TR> <TR><TD></TD><TD> removed and </TD><TD> performance problem that</TD></TR> <TR><TD></TD><TD> replaced with </TD><TD> was apparently caused</TD></TR> <TR><TD></TD><TD> patch 6740811 </TD><TD> by patch 7021805</TD></TR> <TR><TD></TD></TR> <TR><TD>02.06.2008</TD><TD> Patch 7133360 (#59)</TD><TD> New tbs* merge fix</TD></TR> <TR><TD></TD><TD> </TD><TD> see Note 1170233</TD></TR> <TR><TD></TD></TR> <TR><TD>15.05.2008</TD><TD> Patch 7021805</TD><TD> New Optimizer Merge Fix</TD></TR> <TR><TD></TD><TD> replaces 6740811 (#12)</TD></TR> <TR><TD></TD></TR> <TR><TD>05.05.2008</TD><TD> formal change</TD><TD> Note 1144178</TD></TR> <TR><TD></TD><TD> </TD><TD> HotNews selected</TD></TR> <TR><TD></TD></TR> <TR><TD>02.05.2008</TD><TD> Patch 5895190 (#58)</TD><TD> Note 1166264</TD></TR> <TR><TD></TD></TR> <TR><TD>24.04.2008</TD><TD> Patch 6826661 (#57)</TD><TD> Note 1144178 (HOTNEWS)</TD></TR> <TR><TD></TD></TR> <TR><TD>11.04.2008</TD><TD> Patch 6455161 (#56)</TD><TD> Note 1159243</TD></TR> <TR><TD></TD></TR> <TR><TD>03.03.2008</TD><TD> Patch 6819785</TD><TD> Merge fix</TD></TR> <TR><TD></TD><TD> replaces 6455795</TD><TD> (# 20)</TD></TR> <TR><TD></TD></TR> <TR><TD>21.02.2008</TD><TD> Patch 6740811</TD><TD> New Optimizer Merge Fix</TD></TR> <TR><TD></TD><TD> replaces 6488148 (#12)</TD></TR> <TR><TD></TD></TR> <TR><TD>08.02.2008</TD><TD> Patch 4271217 (#55)</TD><TD> Note 1139816</TD></TR> <TR><TD></TD></TR> <TR><TD></TD><TD> Patch 5442780 (#38)</TD></TR> <TR><TD></TD><TD> obsolete because contained in Optimizer Merge Fix 6488148</TD></TR> <TR><TD></TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD>31.01.2008</TD><TD> Patch 6024730 (#54)</TD><TD> Note 1137389</TD></TR> <TR><TD></TD></TR> <TR><TD>29.01.2008</TD><TD> Patch 6771608</TD><TD> New kdi* merge fix</TD></TR> <TR><TD></TD><TD> replaces 4742607 (#15)</TD><TD> also see Note</TD></TR> <TR><TD></TD><TD> </TD><TD> 1135589 <B>HOTNEWS</B></TD></TR> <TR><TD></TD></TR> <TR><TD>28.01.2008</TD><TD> Footnote (*29)</TD></TR> <TR><TD></TD></TR> <TR><TD>24.01.2008</TD><TD> Patch 4704890</TD><TD> Note 1135420</TD></TR> <TR><TD></TD><TD> (#53) (*30)</TD></TR> <TR><TD></TD></TR> <TR><TD>22.01.2008</TD><TD> HotNews selected</TD></TR> <TR><TD></TD></TR> <TR><TD>15.01.2008</TD><TD> Patch 6663258</TD><TD> New qka* merge fix</TD></TR> <TR><TD></TD><TD> replaces 5571916 (#13)</TD></TR> <TR><TD></TD></TR> <TR><TD>09.01.2008</TD><TD> Patch 6729801</TD><TD> New kcr* merge fix</TD></TR> <TR><TD></TD><TD> replaces 6406234 (#35)</TD></TR> <TR><TD></TD></TR> <TR><TD></TD><TD> Patch 5724540 (#42)</TD></TR> <TR><TD></TD><TD> obsolete because contained in Optimizer Merge Fix 6488148</TD></TR> <TR><TD></TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD>04.01.2008</TD><TD> Patch 6005996</TD><TD> Note 1129347</TD></TR> <TR><TD></TD><TD> (#52) (*G)</TD></TR> <TR><TD></TD></TR> <TR><TD>18.12.2007</TD><TD> Patch 5188321</TD><TD> Note 1126517 <B>HOTNEWS</B></TD></TR> <TR><TD></TD></TR> <TR><TD>18.12.2007</TD><TD> Footnote (*19)</TD></TR> <TR><TD></TD></TR> <TR><TD>17.12.2007</TD><TD> Patch category </TD><TD> Optional ********</TD></TR> <TR><TD></TD><TD> 'optional patches'</TD><TD> Patches #1 to #13</TD></TR> <TR><TD></TD><TD> removed</TD><TD> as of now #38 to #50</TD></TR> <TR><TD></TD><TD> </TD><TD> </TD></TR> <TR><TD>07.12.2007</TD><TD> Patch 6488148</TD><TD> New Optimizer Merge Fix</TD></TR> <TR><TD></TD><TD> replaces 6321245</TD><TD> see comment (*9)</TD></TR> <TR><TD></TD></TR> <TR><TD>05.12.2007</TD><TD> Patch 6447320</TD><TD> Note 1121838</TD></TR> <TR><TD></TD><TD> Optional patch(#13)</TD></TR> <TR><TD></TD></TR> <TR><TD>29.11.2007</TD><TD> Patch 6455795</TD><TD> Merge fix</TD></TR> <TR><TD></TD><TD> replaces 6033289</TD><TD> (# 20)</TD></TR> <TR><TD></TD></TR> <TR><TD>22.11.2007</TD><TD> Patch 6471079</TD><TD> Note 1116442</TD></TR> <TR><TD></TD><TD> Platform-specific </TD><TD> see comment (*23)</TD></TR> <TR><TD></TD></TR> <TR><TD>07.11.2007</TD><TD> Check OPatch version</TD></TR> <TR><TD></TD><TD> for CPU installations</TD></TR> <TR><TD></TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD>02.11.2007</TD><TD> Patch 6435823</TD><TD> Note 1110749</TD></TR> <TR><TD></TD><TD> replaces 5029334</TD></TR> <TR><TD></TD><TD> Optional patch(#4)</TD></TR> <TR><TD></TD></TR> <TR><TD>30.10.2007</TD><TD> MOPatch ignores CPUs</TD></TR> <TR><TD></TD></TR> <TR><TD>26.10.2007</TD><TD> Patch 5458753</TD><TD> Note 1107700 <B>HOTNEWS</B></TD></TR> <TR><TD></TD></TR> <TR><TD>26.10.2007</TD><TD> Patch 5737613</TD><TD> Merge fix</TD></TR> <TR><TD></TD><TD> replaces 5377099</TD><TD> see comment (*20)</TD></TR> <TR><TD></TD></TR> <TR><TD>25.10.2007</TD><TD> Release of </TD><TD> Note 850306</TD></TR> <TR><TD></TD><TD> CPU patch from </TD><TD> Note 1107601</TD></TR> <TR><TD></TD><TD> 16.10.2007;</TD></TR> <TR><TD></TD><TD> Recommendation for</TD></TR> <TR><TD></TD><TD> CPU patches</TD></TR> <TR><TD></TD></TR> <TR><TD>22.10.2007</TD><TD> Patch 3748430</TD><TD> Note 1101090</TD></TR> <TR><TD></TD><TD> Optional patch(#12)</TD></TR> <TR><TD></TD></TR> <TR><TD>26.09.2007</TD><TD> Patch 6406234</TD><TD> New kcr* merge fix</TD></TR> <TR><TD></TD><TD> replaces 5391568</TD><TD> see comment (*21)</TD></TR> <TR><TD></TD></TR> <TR><TD>07.09.2007</TD><TD> Patch 6340979</TD><TD> New prvtstat* Merge Fix</TD></TR> <TR><TD></TD><TD> replaces 5915774</TD></TR> <TR><TD></TD></TR> <TR><TD>04.09.2007</TD><TD> Patch 5308692</TD><TD> Note 1089680</TD></TR> <TR><TD></TD></TR> <TR><TD>31.08.2007</TD><TD> Patch 6321245</TD><TD> New Optimizer Merge Fix</TD></TR> <TR><TD></TD><TD> replaces 5984705</TD></TR> <TR><TD></TD></TR> <TR><TD>27.08.2007</TD><TD> Patch 5941030</TD><TD> Note 1039393</TD></TR> <TR><TD></TD></TR> <TR><TD>15.08.2007</TD><TD> Patch 6153847</TD><TD> Note 1033315</TD></TR> <TR><TD></TD><TD> replaces 6055783</TD></TR> <TR><TD></TD></TR> <TR><TD>06.08.2007</TD><TD> Patch 5724540</TD><TD> Note 1081325</TD></TR> <TR><TD></TD></TR> <TR><TD>24.07.2007</TD><TD> Release of </TD><TD> Note 850306</TD></TR> <TR><TD></TD><TD> CPU patch from </TD><TD> Note 1076362</TD></TR> <TR><TD></TD><TD> 16.07.2007;</TD></TR> <TR><TD></TD><TD> Recommendation for</TD></TR> <TR><TD></TD><TD> CPU patches</TD></TR> <TR><TD></TD></TR> <TR><TD>23.07.2007</TD><TD> Oracle Client for </TD><TD> Note 1060539</TD></TR> <TR><TD></TD><TD> 640_EX2-SAP kernel</TD></TR> <TR><TD></TD></TR> <TR><TD>17.07.2007</TD><TD> Downgrade ********</TD><TD> Note 871096</TD></TR> </TABLE> <p></p> <b>Current patch set for Oracle 10.2.0</b><br /> <p><br /><B>******** is released</B><br />You must install at least Oracle patch set ******** if you want to use Oracle Release 10.2 in the SAP environment. In addition, apply the patches specified in this note. All patches currently required are available for ********. New patches will also be made available for this release.<br /><br /><B>******** is NOT certified for SAP</B><br />Oracle Release ******** has not been certified for use in the SAP environment, and will not be certified in future. This release is not supported by SAP and cannot be used in an SAP environment as a result. <B>You must not install this patch set.</B><br /><br />If you decide to install this patch set in your SAP system, you will not be eligible for SAP support.<br /><br />We decided to skip Release ******** for the SAP environment and not release it because you would have to apply almost as many individual patches as for Release ********.<br /><br /><B>******** is released for SAP.</B><br />Oracle Release ******** is released for SAP.<br />For more information about this release concerning the installation, the upgrade, patches and parametrization, see Note 1137346.<br /></p> <b>Requirements for the Oracle client version</b><br /> <b></b><br /> <p><B>Oracle Instant Client</B><br />For SAP installations with the Oracle Instant Client, you must use at least Instant Client Version <B>********</B>. You cannot use Instant Client ********.<br /><br /><B>Oracle9i Client</B><br />All SAP installations on Unix platforms or Linux platforms that are based on an SAP EXT kernel (for example, 4.6D_EXT) or a 6.40 kernel with <B>Oracle9i Client</B> must use a new SAP kernel with at least Oracle Client ******* if you want to upgrade the database to 10.2. For more information:</p> <UL><LI>For ******* Oracle Client, see Note 886783.</LI></UL> <UL><LI>For 9.2.0.8 Oracle Client (recommended because 9.2.0.8 is the last release of Oracle9i), see Note 1017418.</LI></UL> <p><br /><B>New 640_EX2 kernel with Oracle Instant Client</B><br />For SAP installations on Unix/Linux platforms, a 640_EX2 kernel linked against Oracle Release 10.2 is currently being prepared. For more information about installing this new SAP kernel, see Notes 1058988 and 1060539.</p> <b></b><br /> <b>Patch installation with OPatch</b><br /> <p><br />To install Oracle patches for Oracle Release ******** or ********, use the Opatch version of patch 4898608 only. This is available on SAP Service Marketplace.<br /><br />The use of the OPatch version of patch 6880880 (particularly in connection with MOPatch) is currently not released.<br />In this case, see OPatch Note 839182.</p> <b></b><br /> <b>Patch installation with MOPatch</b><br /> <p><br />You must apply a relatively large number of patches for Oracle Release ********. Information about the patch installation using MOPatch, see Note 1027012.<br /></p> <b>Installation of Critical Patch Updates (CPUs)</b><br /> <p><br />The following Oracle CPUs for Oracle Release ******** are currently released for SAP:<br />CPU patches July 2007&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: SAP Note 1076362<br />CPU patches October 2007&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; : SAP Note 1107601<br /><br />Note 850306 contains an overview of all of the CPUs currently released for SAP.<br /><br />Before you install a CPU, we recommend that you check whether you have to install a new version of OPatch. The CPU README documents the version you require to install a CPU. Use the command 'opatch version' to obtain the installed version. The procedure for installing new Opatch versions is described Note 839182.<br /><br />Note that CPU patches should be installed separately (individually) using OPatch, rather than in a collective installation using MOPatch. As of Version 1.6, MOPatch itself recognizes whether a CPU exists and does not install it together with the other individual patches.<br /><br />If a conflict arises between a CPU patch and another mandatory individual patch, you must decide which patch you will install. If you are unsure, the functional patch should take priority over the CPU patch.<br /></p> <b>Difficulties with the installation?</b><br /> <p><br />If you encounter difficulties when installing Oracle software, first consult Note 841728 to see if this note describes a workaround that is already known.<br /></p> <b>Individual patches for UNIX and Linux platforms</b><br /> <p><br />The individual patches, which are listed below as required patches, are available for UNIX and Linux platforms only.&#x00A0;&#x00A0;These are prerequisites for a smooth operation of your SAP system on Oracle Release 10.2.0.<br /><br />Note 871735 provides information about the patch sets for Oracle 10.2.0 that have been released for use in the SAP environment and which patch set is the current patch set.<br /></p> <b>Bundle patches for Windows platforms</b><br /> <p><br />The patches listed below are available as individual patch for UNIX and Linux platforms only. For Windows platforms, these individual patches are combined to form 'bundle patches'. Note 871735 provides information about which Oracle bundle patches for Windows have been released for the particular patch set for use in the SAP environment.<br /><br />Equivalent names for these Windows collective patches are:<br /><B>- <B>Bundle Patch</B></B> <B><B></B></B><br /><B>- <B>Patch collection</B></B> (see Note 871735)<br />- <B><B>Mini patch</B></B><br /><br />The Oracle release and the patch number are part of the unique description of a mini patch. These are always specified in the README of a bundle patch. Example:<br /><B>Oracle database server Version ********.0 patch 5</B><br />Abbreviated forms:<br /><B>********.0 patch 5</B><br /><B>********.5P</B><br /><br /><B></B></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br /><B>********</B><br /><B>********</B><br /><B>Oracle patch </B><br /><B>Oracle patch set</B><br /><B>Oracle patch set</B><br /><B></B></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br /><B>Applying Oracle database patches for Release 10.2.0</B><br /><B></B></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p></p> <b>Oracle Patches and Patch Sets on SAP Service Marketplace</b><br /> <p><br />Oracle Database 10g patch sets and patches are available for download from SAP Service Marketplace. See Note 871735.<br /><br />You can now find <B>generic, in other words platform-independent patches</B> for each release under 'Generic'. They are no longer available under the platform-specific patches.<br /><br />The generic patches for release ******** (for 32-bit and 64-bit platforms!) are available under <B>'Database Patches-&gt;Oracle-&gt;Oracle 64-bit-&gt;Oracle ********-&gt;Generic'</B>.<br /><br />Link to the <B>SAP Software Center for database patches</B>:<br />http://service.sap.com/swcenter-3pmain<br /></p> <b>Relevant SAP Notes</b><br /> <p><br />Applying Oracle patches&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: SAP Note 839182<br />Installing Oracle patch sets: SAP Note 839187<br />MOPatch Note&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: SAP Note 1027012<br />Current patch set/mini patch&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; : SAP Note 871735<br />Software installation problems&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; :&#x00A0;&#x00A0;SAP Note 841728<br />Oracle on Itanium&#x00AE; 2 (Montecito): SAP Note 986578<br />History of database upgrades&#x00A0;&#x00A0;&#x00A0;&#x00A0;: SAP Note 1017936<br />Critical Patch Updates (CPUs)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;: SAP Note 850306<br /></p> <b>Recommendations for applying patches:</b><br /> <p></p> <UL><LI>After you install and uninstall one or more patches, execute '<B>opatch lsinventory</B>' to record the new patch status in an OPatch log.</LI></UL> <UL><LI>To speed up the installation of patches, call OPatch with the option '<B>silent</B>', in order to suppress the interaction between OPatch and the user. Before you do this, you must ensure that all prerequisites for applying the patch have been met.</LI></UL> <UL><LI>You can also speed up the installation by calling OPatch with the option '<B>no_relink</B>', to save time when you relink several times. If you use this option, you must then activate the link process yourself ('relink all'). MOPatch uses this option by default, and also ensures that the required linking operations are executed exactly once.</LI></UL> <UL><LI>Instead of the recommendations mentioned above, you can use the <B>MOPatch</B> tool to simplify and speed up the installation of patches.. MOPatch was developed to automate, simplify and speed up the installation of patches using OPatch. Further information about MOPatch is available in Note 1027012.</LI></UL> <p><br /><B>A note about the output of 'opatch Isinventory':</B><br /><br />For individual patches, the patch number is usually identical with the number of the bug is solved by the patch.&#x00A0;&#x00A0;However, in some cases the patch number and the bug number are different:</p> <UL><LI>With merge patches: The merge patch number is different from the number of the bug that is solved in the merge patch.</LI></UL> <UL><LI>Depending on your platform, the number displayed under 'Patch' may not be the same as the number displayed under 'Bugs fixed' (individual fixes).<br /><br />For example (AIX 5L):<br />Patch 5090357: applied on Tue Nov 14 11:37:01 CST 2006<br />Created on 16 Mar 2006, 14:33:45 hrs US/Pacific<br />Bugs fixed: 4604970</LI></UL><p><br />If you want to use opatch to check whether a specific patch has been installed, search for the patch number.&#x00A0;&#x00A0;However, if you want to check whether a correction has been implemented for a specific bug, search for the relevant bug number in the 'opatch lsinventory' output.<br /></p> <b>Oracle Server Patches for Oracle Release ******** (UNIX)</b><br /> <b></b><br /> <p>You must apply the following patches:<br /><br /> 1. Patch 5117016 Base Bug(s): 5117016 <B>(*P) (*1)</B><br /> 2. Patch 5092134 Base Bug(s): 4523125, 4669305 <B>(*M)</B><br /> 3. Patch 5107960 <B>obsolete</B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;contained in Optimizer Merge Fix 6488148 (#12)<br /><br /> 4. Patch 4604970 Base Bug(s): 4604970<br /> 5. Patch 6046043 Base Bug(s): 5579457, 4460775 <B>(*5)</B><br /> 6. Patch 4864648 Base Bug(s): 4864648 <B>(*6)</B><br /> 7. Patch 4770693 Base Bug(s): 4770693 <B>(*P) (*7)</B><br /> 8. Patch 4638550 Base Bug(s): 4638550 <B>(*8)</B><br /> 9. Patch 5103126 Base Bug(s): 5103126<br />10. Patch 5369855 Base Bug(s): 5369855<br />11. Patch 5363584 Base Bug(s): 5363584 (see Note: 980805 <B><B>HOTNEWS</B></B>)<br />12. Patch 7608184 Base Bug(s): 5131645 5724540 6007259 6011182<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6077514 6087237 6147372 6151963<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6370247 4708389 5718007 5763245<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5882954 6350462 5944076 6051211<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6082745 6134565 5705630 5990716<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6070954 5976822 5838613 5949981<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;3901785 5680702 5380055 5966822<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5884780 5397482 5381446 5172444<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5084239 5129407 5766310 5766310<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5084239 5129407 4273361 4372359<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4483240 4648181 4698023 4712638<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;4722645 4904838 5033476 5043097<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5057695 5089444 5113934 5126551<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5245494 5399282 5449488 5483301<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5547058 5548510 5649765 5690241<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5694984 5705257 5741121 5762598<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6316993 6062266 4724074 6251917<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6159522 4878299 6740811 4567767<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6239971 6660162 6819865 6626018<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6120483 7021805 6440977 7325597<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6430500 6399597 5648287 7448790<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5571916 6151380 6663258<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 981875 <B>(</B><B><B>*M)</B></B><br />13. Patch 6663258 <B>obsolete</B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;contained in Optimizer Merge Fix 7608184 (#12)<br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 985118 (<B><B>HOTNEWS)</B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 1119859 <B>(*M)</B><br />14. Patch 5212539 Base Bug(s): 5212539 (see Note 983230 <B><B>HOTNEWS</B></B>)<B>(*10)</B><br />15. Patch 9227075 Base Bug(s): 4742607 6646613 7329252<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See Note 1020225.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 1135589 (<B><B>HOTNEWS)</B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 1413928 (<B><B>HOTNEWS)</B></B><br />16. Patch 5010657 Base Bug(s): 5010657 (see Note 960417 ) <B>(*P) (*16)</B><br />17. Patch 5052029 Base Bug(s): 5052029 (see Note 960417 ) <B>(*P) (*17)</B><br />18. Patch 5363442 Base Bug(s): 5363442 (see Note 978807 ) <B>(*P) (*18)</B><br />19. Patch 5496862 Base Bug(s): 5496862 (see Note 980152 ) <B>(*P) (*19)</B><br />20. Patch 7237154 Base Bug(s): 5396550 7015250<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5970301 6996749 6376915 6819785<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5747462 6455795 6140309 6086497<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;6033289 5093837 5728380<B> </B>5959612<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5752105 5386204 5667736 5212539<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5376783 4430244 4771672 3279497<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 1002062 <B>(*M) (*29)</B><br />21. Patch 5063279 Base Bug(s):<B> </B>5063279 (see Note 1003217)<br />22. Patch 5635254 Base Bug(s): 5635254 (see Note 1003198) <B>(*G)</B><br />23. Patch 4899479 Base Bug(s):<B></B>4899479 (see Note 1013476)<br />24. Patch 5737613 Base Bug(s):<B> </B>5377099 5458753<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 1014418 <B>(*P) (*M) (*20)</B><br />25. Patch 4668719 Base Bug(s):<B> </B>4668719 (see Note 1015152)<br />26. Patch 4518443 Base Bug(s): 4518443 (see Note 841728 point 13)<br />27. Patch 5636728 Base Bug(s):<B></B>5636728 (see Note 1021454 <B><B>HOTNEWS</B></B>)<br />28. Patch 4883635 <B>obsolete</B>; contained in new merge fix 8726798<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(See Notes 1034919 and 1368640 <B><B>HOTNEWS</B></B>.)<br />29. Patch 6340979 Base Bug(s): 4599763 4772145 5284303 5709414<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5842686<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 948197 <B>(*M)</B><br />30. Patch 5530958 Base Bug(s):<B> </B>5530958 (see Note 1028099)<br />31. Patch 5618049 Base Bug(s):<B></B>5618049 (see Note 1031682)<br />32. Patch 6153847 Base Bug(s):<B> </B>5500044<B>, </B>5638146, 5692368 <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See Notes 1033315, 1046765<br />33. Patch 5345999 Base Bug(s):<B> </B>5345999 (see Note 1038659)<br />34. Patch 5941030 Base Bug(s):<B> </B>5941030 (see Note 1039393 <B><B>HOTNEWS</B></B>)<br />35. Patch 6729801 Base Bug(s): 4522561 4883174 5573544 5399901<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;5181800 4670363 4877360<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 978464 <B>(*M) (*21)</B><br />36. Patch 5458753 Base Bug(s): 5458753 (see Note 1107700) <B><B>HOTNEWS</B> (*22)</B><br />37. Patch 6471079 Base Bug(s):<B> </B>6471079 (see Note 1116442) <B>(*P) (*23)</B><br />38. Patch 5442780 <B>obsolete</B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;contained in Optimizer Merge Fix 6488148 (#12)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(see Note 972574 ) <B>(*P) (*23)</B><br />39. Patch 4707226 Base Bug(s): 4707226 (see Note 961648)<br />40. Patch 4952782 Base Bug(s): 4952782 (see Note 1010392)<br />41. Patch 5442919 Base Bug(s): 5442919 (see Note 1045320)<br />42. Patch 5724540 <B>obsolete</B>&#x00A0;&#x00A0;&#x00A0;&#x00A0;contained in Optimizer Merge Fix 6488148 (#12)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(see Note 1081325)<br />43. Patch 5308692 Base Bug(s): 5308692 (see Note 1089680)<br />44. Patch 3748430 Base Bug(s): 4748430 (see Note 1101090)<br />45. Patch 6447320 Base Bug(s): 6447320 (see Note 1121838) <B>(*24)</B><br />46. Patch 5593621 Base Bug(s): 5593621 (see Note 1054897 ) <B>(*P) (*25)</B><br />47. Patch 5581518 Base Bug(s): 5508574, 5567141<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 1025537 <B>(*M) (*26)</B><br />48. Patch 6435823 Base Bug(s): 5029334 5258887 6084512 <B>(*M) (*G)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See Note 1110749.<br />49. Patch 4773931 Base Bug(s): 4773931 <B>(*27) (*G) (*opt)</B><br />50. Patch 4689959 Base Bug(s): 4689959 (see Note 1019133) <B>(*28) (*opt)</B><br />51. Patch 5188321 Base Bug(s): 5188321 (see Note 1126517) <B><B>HOTNEWS</B></B><br />52. Patch 6005996 Base Bug(s): 6005996 (see Note 1129347) <B>(*G)</B><br />53. Patch 4704890<B> </B>Base bug(s): 4704890 (see Note 1135420) <B>(*30)</B><br />54. Patch 6024730<B> </B>Base bug(s): 6024730 (see Note 1137389) <B>(*opt)</B><br />55. Patch 4271217<B> </B>Base bug(s): 4271217 (see Note 1139816) <B>(*opt)</B><br />56. Patch 6455161<B> </B>Base bug(s): 6455161 (see Note 1159243) <B>(*opt)</B><br />57. Patch 6826661<B> </B>Base bug(s): 6826661 (see Note 1144178) <B><B>HOTNEWS</B></B><br />58. Patch 5895190<B> </B>Base bug(s): 5895190 (see Note 1166264)<br />59. Patch 7133360<B> </B>Base bug(s): 5076617 5623467 7022905 <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See Note 1170233.<br />60. Patch 8726798<B> </B>Base bug(s): 4883635 7662491 <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;see Note 1368640 <B><B>HOTNEWS</B></B><br />61. Patch 7573151<B> </B>Base bug(s): 7573151 (see Note 1336810) <B><B>HOTNEWS</B></B><br />62. Patch 9147110<B> </B>Base bug(s): 4631530 5031712 <B>(*M)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See Note 1395074.<br /><br /><br />Key:<br /><B>(*M) </B>Merge patch<br /><B>(*G)</B> Generic patch<br /><B>(*P)</B> Port-specific patch<br /><B>(*opt)</B> optional patch<br /><br />Explanation of footnotes</p> <UL><LI><B>(*1)</B><br />Patch 5117016: For the Solaris 64-bit platform only<br />You must apply this patch. You must apply this patch BEFORE you apply any other patches.</LI></UL> <UL><LI><B>(*5)</B> Generic patch: See Notes 830577, 841728 and 1054131.</LI></UL> <UL><LI><B>(*6)</B> Generic patch: See Note 841728.</LI></UL> <UL><LI><B>(*7)</B> For the Solaris-x86_64 platform only</LI></UL> <UL><LI><B>(*8)</B> Generic patch: See Note 819830.</LI></UL> <UL><LI><B>(*9)</B><br />This has been deleted.</LI></UL> <UL><LI><B>(*10)</B><br /> The corrections of patch 5212539 are included in merge patch 5959612 (No. 20). Therefore, individual patch 5212539 has been removed from SAP Service Marketplace.&#x00A0;&#x00A0;Install merge patch 5959612 instead. If you have already installed patch 5212539, this patch will be automatically removed during the installation of merge patch 5959612.</LI></UL><UL><LI><B>(*16)</B> For HP-UX IA64 platforms only</LI></UL> <UL><LI><B>(*17)</B> For HP-UX 64-bit platforms only</LI></UL> <UL><LI><B>(*18)</B> For HP-UX IA64 platforms only</LI></UL> <UL><LI><B>(*19)</B> For AIX 5.3 TL 5 (5300-05) and AIX 5.3 TL6 (5300-06) platforms only</LI></UL> <UL><LI><B>(*20)</B> For HP-UX 64-bit and AIX 5L platforms only<br />The individual patch 5377099 was replaced by merge patch 5737613. This merge patch contains patches 5377099 and 5458753.</LI></UL> <UL><LI><B>(*21)</B> The previous merge patch 5391568 is contained in the merge patch 6729801 or 6406234. The merge patch 5391568 was previously optional. Since the new merge patch contains patches in the Oracle Data Guard environment, you are now required to install this new merge patch. Therefore, a renumbering of the optional patches was performed.</LI></UL> <UL><LI><B>(*22)</B> The individual patch 5458753 is contained in merge patch 5737613 for the platforms HP-UX 64-bit and AIX 5L (see <B>*20</B>)</LI></UL> <UL><LI><B>(*23)</B> For HP-UX IA64 platforms only</LI></UL> <UL><LI><B>(*24)</B><br />This patch is only relevant if you use segment shrinking for partitions in accordance with SAPNet Note 910389.</LI></UL> <UL><LI><B>(*25)</B><br />For platform HP-UX IA64 only<br />This patch is only relevant for systems on HP Itanium VPAR platforms for which the number of CPUs is changed during productive operation without restarting the instance.</LI></UL> <UL><LI><B>(*26)</B><br />This note is only relevant for large systems with more than 31 CPUs.</LI></UL> <UL><LI><B>(*27)</B><br />This patch is only relevant when you upgrade a database from 10.1.0.5 to ********.</LI></UL> <UL><LI><B>(*28)</B><br />Impact of 2007 USA daylight saving (DST) changes on Oracle:<br />If the database server runs in a time zone that is listed in the README file of the patch, install the patch on the server.&#x00A0;&#x00A0;Download the patch for platform AIX and refer to the README file that is contained in the patch.</LI></UL> <UL><LI><B>(*29)</B><br />The following two Hotnews belong to this patch:<br />Hotnews 983230 (see Note 1002062)<br />Hotnews 1136063 (concerning bug 5386204)</LI></UL> <UL><LI><B>(*30)</B><br />The patch is relevant only for platforms with a dynamic CPU configuration.</LI></UL> <p><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS-DB-ORA (BW ORACLE)"}, {"Key": "Other Components", "Value": "BC-DB-ORA-INS (Installation SAP System)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "C1399114"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000871096/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871096/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871096/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871096/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871096/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871096/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871096/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871096/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000871096/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "986578", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle support in Itanium 2 systems (Montecito & following)", "RefUrl": "/notes/986578"}, {"RefNumber": "983230", "RefComponent": "BC-DB-ORA", "RefTitle": "LOB corruption for NOCACHE LOBs in ASSM (ORA-1555/ORA-22924)", "RefUrl": "/notes/983230"}, {"RefNumber": "981875", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Optimizer merge fix for Oracle ********", "RefUrl": "/notes/981875"}, {"RefNumber": "980805", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-600 kdtigetrow-2 w/ array insert leads to corruptions", "RefUrl": "/notes/980805"}, {"RefNumber": "972574", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-07445: [$cold_qerfxArrayMaxSize()+XXXXX]", "RefUrl": "/notes/972574"}, {"RefNumber": "965912", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU patches July 2006 (Oracle Critical Update Program)", "RefUrl": "/notes/965912"}, {"RefNumber": "961648", "RefComponent": "BC-DB-ORA", "RefTitle": "Auto Space Advisor For Tablespaces Fails ORA-20000", "RefUrl": "/notes/961648"}, {"RefNumber": "960417", "RefComponent": "BC-DB-ORA", "RefTitle": "MMNL or M000 trace files on HP with Oracle 10.2.0", "RefUrl": "/notes/960417"}, {"RefNumber": "949847", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/949847"}, {"RefNumber": "945315", "RefComponent": "BC-OP-PLNX", "RefTitle": "SAP installation on Linux on POWER/Oracle 10.2", "RefUrl": "/notes/945315"}, {"RefNumber": "933778", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/933778"}, {"RefNumber": "900345", "RefComponent": "BC-UPG-RDM", "RefTitle": "Backward Release of ORACLE 10.2 - Upgrade Information", "RefUrl": "/notes/900345"}, {"RefNumber": "886783", "RefComponent": "BC-DB-ORA", "RefTitle": "Installing Oracle 9207 Client Software on UNIX", "RefUrl": "/notes/886783"}, {"RefNumber": "871735", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Current patch set", "RefUrl": "/notes/871735"}, {"RefNumber": "850306", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Critical Patch Update Program", "RefUrl": "/notes/850306"}, {"RefNumber": "841728", "RefComponent": "BC-DB-ORA", "RefTitle": "10.2: Solutions for installation and upgrade problems", "RefUrl": "/notes/841728"}, {"RefNumber": "839187", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Applying patch set/patches/patch collection", "RefUrl": "/notes/839187"}, {"RefNumber": "839182", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle patch installation with OPatch", "RefUrl": "/notes/839182"}, {"RefNumber": "836517", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle database 10g: Preparation for SAP upgrade", "RefUrl": "/notes/836517"}, {"RefNumber": "820062", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Patch sets/patches for 10.1.0", "RefUrl": "/notes/820062"}, {"RefNumber": "819830", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Additional Information on upgrade:UNIX", "RefUrl": "/notes/819830"}, {"RefNumber": "819655", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP  ORACLE", "RefUrl": "/notes/819655"}, {"RefNumber": "720886", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Integration into the SAP environment", "RefUrl": "/notes/720886"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "563359", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance optimization for tables with LOB columns", "RefUrl": "/notes/563359"}, {"RefNumber": "1228643", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Oracle Clusterware: <PERSON><PERSON> on top of Oracle ********", "RefUrl": "/notes/1228643"}, {"RefNumber": "1178120", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Patchnumbers on Windows Platforms", "RefUrl": "/notes/1178120"}, {"RefNumber": "1175996", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g/11g patches check", "RefUrl": "/notes/1175996"}, {"RefNumber": "1137346", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/1137346"}, {"RefNumber": "1135420", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00600 [KSLGETL:1] after changing dynamic CPU's", "RefUrl": "/notes/1135420"}, {"RefNumber": "1129347", "RefComponent": "BC-DB-ORA", "RefTitle": "DBMS_METADATA.GET_DDL, HIDDEN/UNUSED columns and ORA-947", "RefUrl": "/notes/1129347"}, {"RefNumber": "1126517", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-1445 or incorrect values with 10.2", "RefUrl": "/notes/1126517"}, {"RefNumber": "1121838", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "SELECT on partition hangs if a shrink compact is running", "RefUrl": "/notes/1121838"}, {"RefNumber": "1110749", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance on DBA_EXTENTS/DBA_FREE_SPACE_COALESCED", "RefUrl": "/notes/1110749"}, {"RefNumber": "1107700", "RefComponent": "BC-DB-ORA", "RefTitle": "********: Execution of SQL statements in wrong schema", "RefUrl": "/notes/1107700"}, {"RefNumber": "1107601", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches October 2007 (Oracle Critical Update Program)", "RefUrl": "/notes/1107601"}, {"RefNumber": "1089680", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-01426 occurs in dbms_stats.gather_fixed_objects_stats", "RefUrl": "/notes/1089680"}, {"RefNumber": "1081325", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-1 for queries that use subquery partition pruning", "RefUrl": "/notes/1081325"}, {"RefNumber": "1076362", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU patches July 2007 (Oracle Critical Update Program)", "RefUrl": "/notes/1076362"}, {"RefNumber": "1060539", "RefComponent": "BC-DB-ORA", "RefTitle": "Installing EX2 Kernels on Oracle based SAP Systems", "RefUrl": "/notes/1060539"}, {"RefNumber": "1054897", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-07445 [_kill()+48][SIGIOT] after adding dynamic CPU's", "RefUrl": "/notes/1054897"}, {"RefNumber": "1054131", "RefComponent": "BC-DB-ORA-INS", "RefTitle": "Invalid DB objects after upgrade from 9.2.0.8 to ********", "RefUrl": "/notes/1054131"}, {"RefNumber": "1046765", "RefComponent": "BC-DB-ORA", "RefTitle": "st04 - Oracle sessions - slow data dictionary query", "RefUrl": "/notes/1046765"}, {"RefNumber": "1045320", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-30036 despite high number expired/unexpired undo extents", "RefUrl": "/notes/1045320"}, {"RefNumber": "1038659", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-600[17147] during SELECT on V$SPPARAMETER", "RefUrl": "/notes/1038659"}, {"RefNumber": "1034919", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-600 [KDSGRP1] when you compress using MERGE", "RefUrl": "/notes/1034919"}, {"RefNumber": "1034126", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00832 when using Oracle Data Pump", "RefUrl": "/notes/1034126"}, {"RefNumber": "1033315", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00600: [kqlfFillBindData:1]", "RefUrl": "/notes/1033315"}, {"RefNumber": "1027012", "RefComponent": "BC-DB-ORA", "RefTitle": "MOPatch - Install Multiple Oracle Patches in One Run", "RefUrl": "/notes/1027012"}, {"RefNumber": "1026237", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: OPatch and Universal Installer 10.2", "RefUrl": "/notes/1026237"}, {"RefNumber": "1021454", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Segment Shrinking may cause LOB corrupt data", "RefUrl": "/notes/1021454"}, {"RefNumber": "1020225", "RefComponent": "BC-DB-ORA", "RefTitle": "High number of 'cache buffer chains' in Oracle ********", "RefUrl": "/notes/1020225"}, {"RefNumber": "1019587", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU patches January 2007 (Oracle Critical Update Program)", "RefUrl": "/notes/1019587"}, {"RefNumber": "1017936", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Upgrade history", "RefUrl": "/notes/1017936"}, {"RefNumber": "1015152", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA 600 [kdtdelrow-2] during insert in Oracle ********", "RefUrl": "/notes/1015152"}, {"RefNumber": "1014418", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-07445 [KKSFREEHEAP] and database crash on ********", "RefUrl": "/notes/1014418"}, {"RefNumber": "1013476", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-600 kcbzpbuf_1 during array insert causes corruption", "RefUrl": "/notes/1013476"}, {"RefNumber": "1010392", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-600 [qernsRowP] for Oracle ********", "RefUrl": "/notes/1010392"}, {"RefNumber": "1010217", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle CRS: Patches for RAC enabled SAP Systems", "RefUrl": "/notes/1010217"}, {"RefNumber": "1003217", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-600 [kdiblLockRange:not validated] during an insert", "RefUrl": "/notes/1003217"}, {"RefNumber": "1003198", "RefComponent": "BC-DB-ORA", "RefTitle": "J2EE: Incorrect data with Oracle10g JDBC lower than ********", "RefUrl": "/notes/1003198"}, {"RefNumber": "1002062", "RefComponent": "BC-DB-ORA", "RefTitle": "Additional merge fix for Oracle ********", "RefUrl": "/notes/1002062"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "871735", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Current patch set", "RefUrl": "/notes/871735 "}, {"RefNumber": "563359", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance optimization for tables with LOB columns", "RefUrl": "/notes/563359 "}, {"RefNumber": "839182", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle patch installation with OPatch", "RefUrl": "/notes/839182 "}, {"RefNumber": "1027012", "RefComponent": "BC-DB-ORA", "RefTitle": "MOPatch - Install Multiple Oracle Patches in One Run", "RefUrl": "/notes/1027012 "}, {"RefNumber": "720886", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Integration into the SAP environment", "RefUrl": "/notes/720886 "}, {"RefNumber": "841728", "RefComponent": "BC-DB-ORA", "RefTitle": "10.2: Solutions for installation and upgrade problems", "RefUrl": "/notes/841728 "}, {"RefNumber": "1137346", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Patches/patch collections for Oracle ********", "RefUrl": "/notes/1137346 "}, {"RefNumber": "1175996", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g/11g patches check", "RefUrl": "/notes/1175996 "}, {"RefNumber": "986578", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle support in Itanium 2 systems (Montecito & following)", "RefUrl": "/notes/986578 "}, {"RefNumber": "1178120", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Patchnumbers on Windows Platforms", "RefUrl": "/notes/1178120 "}, {"RefNumber": "1228643", "RefComponent": "BC-DB-ORA-RAC", "RefTitle": "Oracle Clusterware: <PERSON><PERSON> on top of Oracle ********", "RefUrl": "/notes/1228643 "}, {"RefNumber": "981875", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Optimizer merge fix for Oracle ********", "RefUrl": "/notes/981875 "}, {"RefNumber": "819655", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info.: Upgrade to SAP NW 2004s ABAP  ORACLE", "RefUrl": "/notes/819655 "}, {"RefNumber": "1002062", "RefComponent": "BC-DB-ORA", "RefTitle": "Additional merge fix for Oracle ********", "RefUrl": "/notes/1002062 "}, {"RefNumber": "1020225", "RefComponent": "BC-DB-ORA", "RefTitle": "High number of 'cache buffer chains' in Oracle ********", "RefUrl": "/notes/1020225 "}, {"RefNumber": "945315", "RefComponent": "BC-OP-PLNX", "RefTitle": "SAP installation on Linux on POWER/Oracle 10.2", "RefUrl": "/notes/945315 "}, {"RefNumber": "839187", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Applying patch set/patches/patch collection", "RefUrl": "/notes/839187 "}, {"RefNumber": "836517", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle database 10g: Preparation for SAP upgrade", "RefUrl": "/notes/836517 "}, {"RefNumber": "1034919", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-600 [KDSGRP1] when you compress using MERGE", "RefUrl": "/notes/1034919 "}, {"RefNumber": "980805", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-600 kdtigetrow-2 w/ array insert leads to corruptions", "RefUrl": "/notes/980805 "}, {"RefNumber": "900345", "RefComponent": "BC-UPG-RDM", "RefTitle": "Backward Release of ORACLE 10.2 - Upgrade Information", "RefUrl": "/notes/900345 "}, {"RefNumber": "1010217", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle CRS: Patches for RAC enabled SAP Systems", "RefUrl": "/notes/1010217 "}, {"RefNumber": "1017936", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Upgrade history", "RefUrl": "/notes/1017936 "}, {"RefNumber": "961648", "RefComponent": "BC-DB-ORA", "RefTitle": "Auto Space Advisor For Tablespaces Fails ORA-20000", "RefUrl": "/notes/961648 "}, {"RefNumber": "972574", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-07445: [$cold_qerfxArrayMaxSize()+XXXXX]", "RefUrl": "/notes/972574 "}, {"RefNumber": "960417", "RefComponent": "BC-DB-ORA", "RefTitle": "MMNL or M000 trace files on HP with Oracle 10.2.0", "RefUrl": "/notes/960417 "}, {"RefNumber": "1010392", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-600 [qernsRowP] for Oracle ********", "RefUrl": "/notes/1010392 "}, {"RefNumber": "1003217", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-600 [kdiblLockRange:not validated] during an insert", "RefUrl": "/notes/1003217 "}, {"RefNumber": "1045320", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-30036 despite high number expired/unexpired undo extents", "RefUrl": "/notes/1045320 "}, {"RefNumber": "1121838", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "SELECT on partition hangs if a shrink compact is running", "RefUrl": "/notes/1121838 "}, {"RefNumber": "1135420", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00600 [KSLGETL:1] after changing dynamic CPU's", "RefUrl": "/notes/1135420 "}, {"RefNumber": "820062", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Patch sets/patches for 10.1.0", "RefUrl": "/notes/820062 "}, {"RefNumber": "1081325", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-1 for queries that use subquery partition pruning", "RefUrl": "/notes/1081325 "}, {"RefNumber": "1129347", "RefComponent": "BC-DB-ORA", "RefTitle": "DBMS_METADATA.GET_DDL, HIDDEN/UNUSED columns and ORA-947", "RefUrl": "/notes/1129347 "}, {"RefNumber": "1126517", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-1445 or incorrect values with 10.2", "RefUrl": "/notes/1126517 "}, {"RefNumber": "819830", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Additional Information on upgrade:UNIX", "RefUrl": "/notes/819830 "}, {"RefNumber": "886783", "RefComponent": "BC-DB-ORA", "RefTitle": "Installing Oracle 9207 Client Software on UNIX", "RefUrl": "/notes/886783 "}, {"RefNumber": "1060539", "RefComponent": "BC-DB-ORA", "RefTitle": "Installing EX2 Kernels on Oracle based SAP Systems", "RefUrl": "/notes/1060539 "}, {"RefNumber": "1003198", "RefComponent": "BC-DB-ORA", "RefTitle": "J2EE: Incorrect data with Oracle10g JDBC lower than ********", "RefUrl": "/notes/1003198 "}, {"RefNumber": "1107700", "RefComponent": "BC-DB-ORA", "RefTitle": "********: Execution of SQL statements in wrong schema", "RefUrl": "/notes/1107700 "}, {"RefNumber": "1014418", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-07445 [KKSFREEHEAP] and database crash on ********", "RefUrl": "/notes/1014418 "}, {"RefNumber": "1107601", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches October 2007 (Oracle Critical Update Program)", "RefUrl": "/notes/1107601 "}, {"RefNumber": "1110749", "RefComponent": "BC-DB-ORA", "RefTitle": "Performance on DBA_EXTENTS/DBA_FREE_SPACE_COALESCED", "RefUrl": "/notes/1110749 "}, {"RefNumber": "1026237", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: OPatch and Universal Installer 10.2", "RefUrl": "/notes/1026237 "}, {"RefNumber": "1015152", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA 600 [kdtdelrow-2] during insert in Oracle ********", "RefUrl": "/notes/1015152 "}, {"RefNumber": "1089680", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-01426 occurs in dbms_stats.gather_fixed_objects_stats", "RefUrl": "/notes/1089680 "}, {"RefNumber": "1033315", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00600: [kqlfFillBindData:1]", "RefUrl": "/notes/1033315 "}, {"RefNumber": "1038659", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-600[17147] during SELECT on V$SPPARAMETER", "RefUrl": "/notes/1038659 "}, {"RefNumber": "983230", "RefComponent": "BC-DB-ORA", "RefTitle": "LOB corruption for NOCACHE LOBs in ASSM (ORA-1555/ORA-22924)", "RefUrl": "/notes/983230 "}, {"RefNumber": "1076362", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU patches July 2007 (Oracle Critical Update Program)", "RefUrl": "/notes/1076362 "}, {"RefNumber": "1046765", "RefComponent": "BC-DB-ORA", "RefTitle": "st04 - Oracle sessions - slow data dictionary query", "RefUrl": "/notes/1046765 "}, {"RefNumber": "1054131", "RefComponent": "BC-DB-ORA-INS", "RefTitle": "Invalid DB objects after upgrade from 9.2.0.8 to ********", "RefUrl": "/notes/1054131 "}, {"RefNumber": "1054897", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-07445 [_kill()+48][SIGIOT] after adding dynamic CPU's", "RefUrl": "/notes/1054897 "}, {"RefNumber": "1021454", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Segment Shrinking may cause LOB corrupt data", "RefUrl": "/notes/1021454 "}, {"RefNumber": "826468", "RefComponent": "PA-ER", "RefTitle": "Improved performance in e-recruiting services", "RefUrl": "/notes/826468 "}, {"RefNumber": "1034126", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00832 when using Oracle Data Pump", "RefUrl": "/notes/1034126 "}, {"RefNumber": "949847", "RefComponent": "BC-DB-ORA", "RefTitle": "SAP on Oracle 10g: Help for Error Analysis", "RefUrl": "/notes/949847 "}, {"RefNumber": "1013476", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-600 kcbzpbuf_1 during array insert causes corruption", "RefUrl": "/notes/1013476 "}, {"RefNumber": "1019587", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU patches January 2007 (Oracle Critical Update Program)", "RefUrl": "/notes/1019587 "}, {"RefNumber": "965912", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU patches July 2006 (Oracle Critical Update Program)", "RefUrl": "/notes/965912 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}