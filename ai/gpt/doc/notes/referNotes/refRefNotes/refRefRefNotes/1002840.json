{"Request": {"Number": "1002840", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 294, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016194082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001002840?language=E&token=77D6A8F30CACBCD89F2249B3B72A35CF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001002840", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001002840/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1002840"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.10.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Monitors for Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Monitors for Oracle", "value": "BC-CCM-MON-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1002840 - No data or obsolete data in DB Space Statistic monitor"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have already activated the new collector (SAP Note 868063) but the new space statistic monitor does not display any data or data is not up-to-date.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RSORACOL, SDBORA3, SDBORA4, TCOLL, SAP_COLLECTOR_FOR_PERFMONITOR, DB02, DB02N, DBACockpit, DBA Cockpit</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This problem can be caused by many different reasons. The following check list can help you identifying the root cause.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Follow all steps of this check list to find out the root cause of the problem:<br /><br />1. Check that your Support Package level is higher than:<br /> R/3 620 - 60<br /> R/3 640 - 18<br /> R/3 700 - 09<br /><br />Otherwise apply SAP note 1019847.<br /><br />2. Check if standard job SAP_COLLECTOR_FOR_PERFMONITOR is scheduled hourly and run successfully. Go to transaction SM37 and check the execution of this job during the last days. It is also important that it is scheduled only once per hour and close to o'clock (it has been recently observed that jobs planned at half past or at anytime not close to o'clock may not collect data correctly). For more general information about standard jobs refer to SAP note 16083.<br /><br />3. Check if report RSORACOL is contained in table TCOLL. Go to transaction SE16 and show the contents of table TCOLL. You should find the entry:<br /><br />&#160;&#160;RNAME&#160;&#160;&#160;&#160;RSORACOL<br />&#160;&#160;RENUM&#160;&#160;&#160;&#160;1<br />&#160;&#160;SYTYP&#160;&#160;&#160;&#160;S (or C if 620)<br />&#160;&#160;ALTSY<br />&#160;&#160;DAYOW&#160;&#160;&#160;&#160;XXXXXXX<br />&#160;&#160;TIMOD&#160;&#160;&#160;&#160;XXXXXXXXXXXXXXXXXXXXXXXX<br /><br />For more information refer to SAP Notes 12103 for 620, 970449 for 640, 966309 for 700 and 966631 for 710.<br /><br />4. Check if your monitored database is contained in table ORA_MON_DBCON and it is active (STATUS=A). Go to transaction SE16 and show the contents of table ORA_MON_DBCON. For the local database your should find an entry like:<br /><br />&#160;&#160;CON NAME&#160;&#160;&#160;&#160;&#160;&#160; DEFAULT<br />&#160;&#160;STATUS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A<br />&#160;&#160;MAX RUNTIME&#160;&#160;&#160;&#160;1.800<br />&#160;&#160;MAX UPLOAD&#160;&#160;&#160;&#160; 1.000.000<br />&#160;&#160;LOG LIFE&#160;&#160;&#160;&#160;&#160;&#160; 100<br />&#160;&#160;DESCRIPTION&#160;&#160;&#160;&#160;Local Oracle database<br /><br />Note that the name \"DEFAULT\" is mandatory for the local database, as the collector code is hard coded with this string.<br /><br />In case you also want to monitor a remote database you should find an entry like:<br /><br />&#160;&#160;CON NAME&#160;&#160;&#160;&#160;&#160;&#160; DBX_UNIC1<br />&#160;&#160;STATUS&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; A<br />&#160;&#160;MAX RUNTIME&#160;&#160;&#160;&#160;1.800<br />&#160;&#160;MAX UPLOAD&#160;&#160;&#160;&#160; 1.000.000<br />&#160;&#160;LOG LIFE&#160;&#160;&#160;&#160;&#160;&#160; 100<br />&#160;&#160;DESCRIPTION&#160;&#160;&#160;&#160;Remote Oracle database<br /><br />Note that this database must have been previously maintained in transaction DBCO. For more information about remote DB native connection refer to SAP notes 323151 and 339092. (As well remember that, in case you have not maintained this database in transaction DBCO, it should not show up within the F4 help of report RSORACUP)<br /><br />In case the status column shows inactive (I), it is most probably caused by connection errors during the collection job. Therefore check your DB connections.<br /><br />5. Check if all collector modules are contained in table DB02_COLL_PLAN and are active (STATUS=A). Go to DB02-&gt;Additional functions-&gt;Collector logs-&gt;Click on tab \"Modules\". You should find around 60 to 65 modules (the exact number depend on your R/3 release/patch). All modules must be active (STATUS=A). If not, then refer to the collection log to know the causes about its deactivation. You can find the collection log in table DB02_COLL_LOG. Go to transaction SE16 and display only entries of DB02_COLL_LOG for the affected SMON_ID. Normally you only need to check the return code of the last collection date ever. The meaning of each return code is the following:<br /><br />&#160;&#160;00&#160;&#160;Successful execution<br />&#160;&#160;01&#160;&#160;Successful execution: no data collected<br />&#160;&#160;02&#160;&#160;Successful execution: dataset was already up-to-date<br />&#160;&#160;10&#160;&#160;Database connection not possible<br />&#160;&#160;20&#160;&#160;Monitor deactivated: Oracle release &lt; 9<br />&#160;&#160;30&#160;&#160;Submonitor deactivated: source submonitor was inactive<br />&#160;&#160;31&#160;&#160;Submonitor deactivated: data upload exceeded fixed limit<br />&#160;&#160;32&#160;&#160;Submonitor deactivated: runtime exceeded fixed limit<br /><br />You can re-activate collector modules as follows:<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;1. Call report RSORACUP with parameter:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CON_NAME = &lt;DB connection&gt; (enter 'DEFAULT' for local DB)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;OPERATIO = DELETE<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;2. If you are asked to delete entries in MONI, say NO.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;3. Call report RSORACUP again with parameters:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CON_NAME = &lt;DB connection&gt; (enter 'DEFAULT' for local DB)<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;OPERATIO = CREATE<br /><br />After this, the name of the DB connection is inserted in table ORA_MON_DBCON and table DB02_COLL_PLAN shows the corresponding entries, all with status active.<br /><br />6. Check for possible short dumps. Go to transaction ST22 and analyze all short dumps caused by the user owner of the job SAP_COLLECTOR_FOR_PERFMONITOR. Some known errors are described in SAP notes:<br /><br />&#160;&#160;940878&#160;&#160;DB02N collector: ITAB_DUPLICATE_KEY<br />&#160;&#160;941041&#160;&#160;DB02N not correctly set up<br />&#160;&#160;980367&#160;&#160;DB02N: Improper LUW strategy for data collection<br />&#160;&#160;992775&#160;&#160;ITAB_DUPLICATE_KEY in SMON_ID = 700<br />&#160;&#160;1019847 DB collector does not collect data<br />&#160;&#160;1064524 DB02 ITAB_DUPLICATE_KEY<br />&#160;&#160;1064524 DB02 ITAB_DUPLICATE_KEY<br />&#160;&#160;1066044 COMPUTE_BCD_OVERFLOW during DB02N refresh<br />&#160;&#160;1042725 DB02 Refresh terminates in short dump<br /><br />7. DBACockpit/DB02N on rel. 700 sp. 12 is not stable. You must upgrade to sp. 13 or apply the above mentioned notes 1066044 and 1042725.<br /><br />8. It should be considered also a possible error during data displaying. I.e. the data is properly collected but it cannot be displayed due to a possible bug in the transaction DB02N itself.<br /><br />9. Finally, if you cannot find out and correct the problem yourself, please open a message under component BC-CCM-MON-ORA.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D036430)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D041372)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001002840/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001002840/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001002840/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001002840/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001002840/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001002840/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001002840/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001002840/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001002840/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "868063", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Database space statistics monitor for Oracle", "RefUrl": "/notes/868063"}, {"RefNumber": "1482296", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM Service: analysed data volume incorrect (Oracle)", "RefUrl": "/notes/1482296"}, {"RefNumber": "1072066", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: New function for DB monitoring", "RefUrl": "/notes/1072066"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2746948", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "CONNE_IMPORT_WRONG_COMP_LENG dump even after following KBA 1637421", "RefUrl": "/notes/2746948 "}, {"RefNumber": "2616001", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Dump 'CONNE_IMPORT_WRONG_COMP_LENG' after deleting/archiving the monikey/DEF", "RefUrl": "/notes/2616001 "}, {"RefNumber": "2544466", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Grey metrics and obsolete/no data in DB02 - First steps", "RefUrl": "/notes/2544466 "}, {"RefNumber": "2544446", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Auto deactivation of the modules in table DB02_COLL_PLAN", "RefUrl": "/notes/2544446 "}, {"RefNumber": "2205695", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Inactive modules in DB02", "RefUrl": "/notes/2205695 "}, {"RefNumber": "1482296", "RefComponent": "SV-SMG-DVM", "RefTitle": "DVM Service: analysed data volume incorrect (Oracle)", "RefUrl": "/notes/1482296 "}, {"RefNumber": "868063", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "Database space statistics monitor for Oracle", "RefUrl": "/notes/868063 "}, {"RefNumber": "1072066", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "DBA Cockpit: New function for DB monitoring", "RefUrl": "/notes/1072066 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}