{"Request": {"Number": "2561976", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 726, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000581522018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002561976?language=E&token=4A16691CAA2172829903BAC4BD126EAE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002561976", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002561976/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2561976"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.07.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2561976 - SAPBWNews SAP BW/4HANA 1.0 SP 08"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note deals with ABAP Support Package&#160;08 for SAP BW/4HANA 1.0</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for SAP BW/4HANA 1.0 SP 08 Patches</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBWNews for ABAP Support Package&#160;08 of SAP BW/4 HANA 1.0&#160; <br />It provides a list of all notes describing the corrections or enhancements in Support Package 08. This note will be updated when other notes are added.<br /><br /><strong>Factors you must take into account when you import the Support Package:</strong></p>\r\n<ul>\r\n<li>The update to SP 08 must be executed using SAP Software Update Manager (SUM) or the Add-on Installation Tool (SAINT). Using Software Patch Manager (SPAM) is not possible, since a component upgrade to SAP_UI 7.52 is included in this support package stack.</li>\r\n<li>SAP strongly recommends to apply the latest available SAP HANA revision.</li>\r\n<li>\r\n<p><strong>Before</strong> you import the Note please read note <a target=\"_blank\" href=\"/notes/2248091\">2248091 - Change to reimplementation handling </a></p>\r\n</li>\r\n<li>\r\n<p>Support for some application server platforms will be discontinued with the next release of SAP BW/4HANA. See SAP Note <a target=\"_blank\" href=\"/notes/2620910\">2620910</a> for recommended application server platforms.</p>\r\n</li>\r\n</ul>\r\n<p><strong>Issues that may occur after you import the Support Package:</strong></p>\r\n<ul>\r\n<li>For further information on fixes please see the referenced notes</li>\r\n</ul>\r\n<p><strong>Errors corrected /Important Enhancements delivered with this Support Package:</strong></p>\r\n<ul>\r\n<li>\r\n<p><span style=\"font-family: Calibri; color: #1f497d;\">&#160;</span>For full list please see <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10008INDW4CORE\">https://launchpad.support.sap.com/#/supportpackage/SAPK-10008INDW4CORE</a></p>\r\n</li>\r\n</ul>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D031867)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002561976/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002561976/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002561976/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002561976/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002561976/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002561976/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002561976/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002561976/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002561976/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_BW4HANA_100_SP_08_Release_Notes.xlsx", "FileSize": "36", "MimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900003113632017&iv_version=0004&iv_guid=00109B36BC761ED8A2BBB1F936E8C0C7"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2620910", "RefComponent": "BC-OP-PLNX", "RefTitle": "SAP S/4HANA 1511, 1610, 1709, 1809 and SAP BW/4HANA 1.0, 2.0: Recommended and released Application Server Platforms", "RefUrl": "/notes/2620910"}, {"RefNumber": "2347382", "RefComponent": "BW-B4H-LM", "RefTitle": "SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "RefUrl": "/notes/2347382"}, {"RefNumber": "2248091", "RefComponent": "BC-UPG-NA", "RefTitle": "Change to reimplementation handling", "RefUrl": "/notes/2248091"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2786839", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "How to upgrade DW4CORE 100 to SP08 with SAP_UI 750", "RefUrl": "/notes/2786839 "}, {"RefNumber": "2347382", "RefComponent": "BW-B4H-LM", "RefTitle": "SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "RefUrl": "/notes/2347382 "}, {"RefNumber": "2545274", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Long runtime for search for characteristic 0WBS_ELEMT", "RefUrl": "/notes/2545274 "}, {"RefNumber": "2550333", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSEC_CHLOG archiving: Termination due to main memory bottleneck", "RefUrl": "/notes/2550333 "}, {"RefNumber": "2615464", "RefComponent": "BW4-DM-DTO", "RefTitle": "Hadoop/Spark: Pushdown of csv conversion to HANA", "RefUrl": "/notes/2615464 "}, {"RefNumber": "2611597", "RefComponent": "BW", "RefTitle": "Subsequent correction for SAP Note 2602055", "RefUrl": "/notes/2611597 "}, {"RefNumber": "2611571", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Incorrect internal key for the value 'Not assigned'", "RefUrl": "/notes/2611571 "}, {"RefNumber": "2607370", "RefComponent": "BW-BEX-OT-BICS-EQ", "RefTitle": "ODATA: Generation of OData service fails", "RefUrl": "/notes/2607370 "}, {"RefNumber": "2610877", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Missing unit/unexpected number of decimal places for key figure attribute", "RefUrl": "/notes/2610877 "}, {"RefNumber": "2610731", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Partition pruning: Incorrect time derivation", "RefUrl": "/notes/2610731 "}, {"RefNumber": "2610162", "RefComponent": "BW-MT-IOBJ", "RefTitle": "BWMT InfoObject : Runtime Properties are enabled by default", "RefUrl": "/notes/2610162 "}, {"RefNumber": "2610112", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Long runtime in method AUTHORITY_04 of class CL_RSR_RRK0_AUTHORIZATION", "RefUrl": "/notes/2610112 "}, {"RefNumber": "2609392", "RefComponent": "BW4-DM-DTO", "RefTitle": "Termination 'ITAB_ILLEGAL_SORT_ORDER_BLK'", "RefUrl": "/notes/2609392 "}, {"RefNumber": "2608435", "RefComponent": "BW4-DM-DTO", "RefTitle": "DTO: Corrections after ECC SP8", "RefUrl": "/notes/2608435 "}, {"RefNumber": "2608371", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "\"Key Figure Definition\" Steps in RSTT Traces don't return data", "RefUrl": "/notes/2608371 "}, {"RefNumber": "2508938", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: New line check service and master data planning (ABAP)", "RefUrl": "/notes/2508938 "}, {"RefNumber": "2608276", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: Data activation error", "RefUrl": "/notes/2608276 "}, {"RefNumber": "2607569", "RefComponent": "BW4-AE-CORE-NC", "RefTitle": "NCUM: Query on HCPR fails", "RefUrl": "/notes/2607569 "}, {"RefNumber": "2607507", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view: CX_RSD_INFOPROV_NOT_FOUND", "RefUrl": "/notes/2607507 "}, {"RefNumber": "2605168", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZC: incorrect copy of a query component within one InfoProvider", "RefUrl": "/notes/2605168 "}, {"RefNumber": "2605358", "RefComponent": "BW4-ME-IOBJ", "RefTitle": "BWMT InfoObject - Saving the Key Figure gives error even after giving valid Unit InfoObject name", "RefUrl": "/notes/2605358 "}, {"RefNumber": "2604083", "RefComponent": "BW4-ME-ADSO", "RefTitle": "SEGR: Error when saving", "RefUrl": "/notes/2604083 "}, {"RefNumber": "2604810", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception aggregation in SAP HANA and variable or constant in BNR_LRECH", "RefUrl": "/notes/2604810 "}, {"RefNumber": "2604992", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Error 34.011 (MetadataProvider)", "RefUrl": "/notes/2604992 "}, {"RefNumber": "2604864", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Exception aggregation average returns incorrect non-cumulative data", "RefUrl": "/notes/2604864 "}, {"RefNumber": "2604460", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Missing texts after SAP Note 2528889 is implemented", "RefUrl": "/notes/2604460 "}, {"RefNumber": "2604515", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Execute and Explain: Exception Aggregation in SAP HANA/BWA", "RefUrl": "/notes/2604515 "}, {"RefNumber": "2604151", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Wertehilfe: Fehler CX_SY_DYN_CALL_ILLEGAL_TYPE", "RefUrl": "/notes/2604151 "}, {"RefNumber": "2602123", "RefComponent": "BW4-DM-DTO", "RefTitle": "HDFS access from SAP GUI", "RefUrl": "/notes/2602123 "}, {"RefNumber": "2602055", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Wrong data for FI-GL queries", "RefUrl": "/notes/2602055 "}, {"RefNumber": "2600668", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The Mass Maintenance for Query Properties changes all Queries displayed", "RefUrl": "/notes/2600668 "}, {"RefNumber": "2600579", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "InfoObject 1CUDIM is not available after conversion using the report RSO_CONVERT_IPRO_TO_HCPR", "RefUrl": "/notes/2600579 "}, {"RefNumber": "2600516", "RefComponent": "BW-BEX-OT-BICS-PROV", "RefTitle": "BICS InA: Faster search for non-transient DefaultQueries", "RefUrl": "/notes/2600516 "}, {"RefNumber": "2600322", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZTABLES: improvements and enhancements (2)", "RefUrl": "/notes/2600322 "}, {"RefNumber": "2600078", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Sorting on an Attribute leads to Runtime Exception", "RefUrl": "/notes/2600078 "}, {"RefNumber": "2599588", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Corrections for creation from template", "RefUrl": "/notes/2599588 "}, {"RefNumber": "2460830", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Support tool for Hadoop NLS", "RefUrl": "/notes/2460830 "}, {"RefNumber": "2598991", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: RSDSO_ACTIVATION 013", "RefUrl": "/notes/2598991 "}, {"RefNumber": "2593335", "RefComponent": "BW-BEX-OT-BICS-EQ", "RefTitle": "oData BICS: json response for input-ready query contains \"X\" instead of \"true\"", "RefUrl": "/notes/2593335 "}, {"RefNumber": "2598094", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Characteristic Values are sorted incorrectly", "RefUrl": "/notes/2598094 "}, {"RefNumber": "2597998", "RefComponent": "BW-BEX-OT-BICS-RSRT", "RefTitle": "Hierarchy node variable with multiple values is invalid", "RefUrl": "/notes/2597998 "}, {"RefNumber": "2597372", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The Mass Maintenance for Query Runtime Properties runs into an error message when saving", "RefUrl": "/notes/2597372 "}, {"RefNumber": "2597653", "RefComponent": "BW4-AE-DBIF", "RefTitle": "Partition pruning: Incorrect designation in message", "RefUrl": "/notes/2597653 "}, {"RefNumber": "2597182", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX execution on SAP HDB: Message E003(RSPLF)", "RefUrl": "/notes/2597182 "}, {"RefNumber": "2597215", "RefComponent": "BW-BEX-ET", "RefTitle": "BICS: Information message of inactive conditions", "RefUrl": "/notes/2597215 "}, {"RefNumber": "2597134", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Function module RSEC_GET_AUTH_FOR_USER returns unexpected result", "RefUrl": "/notes/2597134 "}, {"RefNumber": "2595384", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime when calculating a calculated member", "RefUrl": "/notes/2595384 "}, {"RefNumber": "2595780", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error Message: Setting NLS:\"\" is not allowed for this query.", "RefUrl": "/notes/2595780 "}, {"RefNumber": "2595772", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Fix for Note 2550759", "RefUrl": "/notes/2595772 "}, {"RefNumber": "2595729", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Logging of hierarchy deletions", "RefUrl": "/notes/2595729 "}, {"RefNumber": "2594240", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Further optimization of accesses to OLAP processor", "RefUrl": "/notes/2594240 "}, {"RefNumber": "2594629", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: No generation of TADIR entry in non-changeable system", "RefUrl": "/notes/2594629 "}, {"RefNumber": "2593529", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Correction of invalid hash definition", "RefUrl": "/notes/2593529 "}, {"RefNumber": "2594186", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "CDS view: Initial entries for display of text", "RefUrl": "/notes/2594186 "}, {"RefNumber": "2584696", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Derivation of adso flag from adso template", "RefUrl": "/notes/2584696 "}, {"RefNumber": "2593206", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Runtime error 'UNCAUGHT_EXCEPTION'", "RefUrl": "/notes/2593206 "}, {"RefNumber": "2592157", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Long runtime when calling the function module 'BICS_PROV_GET_EFFECTIVE_SELECT'", "RefUrl": "/notes/2592157 "}, {"RefNumber": "2592347", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP: Deletion of records from Error Stack via Error Stack maintenance does not work correctly.", "RefUrl": "/notes/2592347 "}, {"RefNumber": "2591012", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Runtime improvement for .[LEVEL<XX>].MEMBERS", "RefUrl": "/notes/2591012 "}, {"RefNumber": "2591284", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Analysis Office: search function does not return a transient query", "RefUrl": "/notes/2591284 "}, {"RefNumber": "2591052", "RefComponent": "BW4-AE-CORE", "RefTitle": "System error in program LCL_STABLE_SID and form IF_RRSI_LOCAL_SID~SID_VAL_CONVERT-1-", "RefUrl": "/notes/2591052 "}, {"RefNumber": "2590828", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: No values displayed for a hierarchy", "RefUrl": "/notes/2590828 "}, {"RefNumber": "2590584", "RefComponent": "BW-BEX-OT-DBIF-CON", "RefTitle": "HDB Compression on non optimized NCUM cube fails sometimes", "RefUrl": "/notes/2590584 "}, {"RefNumber": "2587633", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "DrillDownMember and DrillDownLevel: Runtime improvement", "RefUrl": "/notes/2587633 "}, {"RefNumber": "2589876", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Add Word Support (sapaow) to FM RSAO_CREATE_LAUNCH_FILE", "RefUrl": "/notes/2589876 "}, {"RefNumber": "2588159", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Exit variables not reprocessed in planning sequences", "RefUrl": "/notes/2588159 "}, {"RefNumber": "2587846", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Dump ITAB_DUPLICATE_KEY when workbook with restrictions is refreshed", "RefUrl": "/notes/2587846 "}, {"RefNumber": "2589266", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BPC: No authorization despite sufficient data access profile", "RefUrl": "/notes/2589266 "}, {"RefNumber": "2587509", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "nearline storage and conversion in SAP HANA/BWA", "RefUrl": "/notes/2587509 "}, {"RefNumber": "2586926", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Incorrect data when using THJ hierarchy (2)", "RefUrl": "/notes/2586926 "}, {"RefNumber": "2586790", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for Query: activation fails: Variable attribute column not found", "RefUrl": "/notes/2586790 "}, {"RefNumber": "2586430", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Values missing in query with current member variables", "RefUrl": "/notes/2586430 "}, {"RefNumber": "2586346", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Variable is not referenced to the query definition", "RefUrl": "/notes/2586346 "}, {"RefNumber": "2585906", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Excessive accesses to the OLAP processor when using display hierarchies", "RefUrl": "/notes/2585906 "}, {"RefNumber": "2584774", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Customer exit: Unexpected behavior for compounding and full authorization", "RefUrl": "/notes/2584774 "}, {"RefNumber": "2584389", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query with Current Member Variables: Exactly one primary key must be specified", "RefUrl": "/notes/2584389 "}, {"RefNumber": "2584540", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "RAW data types in SAP HANA runtime are not supported", "RefUrl": "/notes/2584540 "}, {"RefNumber": "2584148", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "NCUM: Query termination for key figure with FIRST aggregation", "RefUrl": "/notes/2584148 "}, {"RefNumber": "2583628", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error SN_OFF_BACK-03- in CL_RSR_RRK0_HIERARCHY", "RefUrl": "/notes/2583628 "}, {"RefNumber": "2582708", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Unexpected data during reading of time-dependent attributes", "RefUrl": "/notes/2582708 "}, {"RefNumber": "2570096", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "DSO generated programs not required for HANA optimized", "RefUrl": "/notes/2570096 "}, {"RefNumber": "2581967", "RefComponent": "BW-BEX-OT-WSP", "RefTitle": "Workspace Query Designer: No Upload of Local Data", "RefUrl": "/notes/2581967 "}, {"RefNumber": "2581504", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Problems for characteristic with 'SAP-HANA.View' master data access", "RefUrl": "/notes/2581504 "}, {"RefNumber": "2581478", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Update of validity for planning", "RefUrl": "/notes/2581478 "}, {"RefNumber": "2581337", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Resposting of DSO data in SAP HDB", "RefUrl": "/notes/2581337 "}, {"RefNumber": "2581359", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Missing values for unassigned node", "RefUrl": "/notes/2581359 "}, {"RefNumber": "2580872", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Error during creation from DataSource template", "RefUrl": "/notes/2580872 "}, {"RefNumber": "2580499", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Message RS_ADSO_MODEL 144 in wrong language", "RefUrl": "/notes/2580499 "}, {"RefNumber": "2579635", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Time consistency check for initial time values", "RefUrl": "/notes/2579635 "}, {"RefNumber": "2579452", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: GET_RELATED does not take query dependency into account", "RefUrl": "/notes/2579452 "}, {"RefNumber": "2579483", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Formula Variable with Replacement Path and exceeding Offset", "RefUrl": "/notes/2579483 "}, {"RefNumber": "2579582", "RefComponent": "BW-WHM-DBA", "RefTitle": "Exception DATE_INVALID raised for SID values check during aDSO request activation", "RefUrl": "/notes/2579582 "}, {"RefNumber": "2579553", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for Query: Near-line storage setting not considered correctly", "RefUrl": "/notes/2579553 "}, {"RefNumber": "2579310", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Shortdump SAPSQL_ARRAY_INSERT_DUPREC in hierarchy activation (2)", "RefUrl": "/notes/2579310 "}, {"RefNumber": "2577510", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Creation fails due to reserved name", "RefUrl": "/notes/2577510 "}, {"RefNumber": "2578666", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Planning function, distribution with reference data: Performance problems", "RefUrl": "/notes/2578666 "}, {"RefNumber": "2577599", "RefComponent": "BW4-ME-ADSO", "RefTitle": "adso: Creation from InfoObject template fails", "RefUrl": "/notes/2577599 "}, {"RefNumber": "2577490", "RefComponent": "BW4-ME-ADSO", "RefTitle": "SEGR: Error message \"Local resource is outdated...\"", "RefUrl": "/notes/2577490 "}, {"RefNumber": "2569153", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: Hierarchy state optimization", "RefUrl": "/notes/2569153 "}, {"RefNumber": "2576958", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Value help does not show any values", "RefUrl": "/notes/2576958 "}, {"RefNumber": "2576342", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "BPC: No authorization despite sufficient data access profile", "RefUrl": "/notes/2576342 "}, {"RefNumber": "2575794", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "InA: Batch processing of InA requests not working in case of complex selections", "RefUrl": "/notes/2575794 "}, {"RefNumber": "2575745", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Error during activation of adso after upgrade to 7.50", "RefUrl": "/notes/2575745 "}, {"RefNumber": "2538050", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: Changes in hierarchy variable not reflected II (follow-up of 2364428)", "RefUrl": "/notes/2538050 "}, {"RefNumber": "2463089", "RefComponent": "BI-RA-BICS-BW", "RefTitle": "BICS: Drill operations on input ready query may cause dump with enabled OLAP bypass", "RefUrl": "/notes/2463089 "}, {"RefNumber": "2574194", "RefComponent": "BW4-ME-ADSO", "RefTitle": "SEGR: Error when saving", "RefUrl": "/notes/2574194 "}, {"RefNumber": "2573810", "RefComponent": "BW-BEX-OT-BICS-EQ", "RefTitle": "ABAP BICS: Fix for paging and reading texts", "RefUrl": "/notes/2573810 "}, {"RefNumber": "2573066", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Empty value help on (advanced)DSO", "RefUrl": "/notes/2573066 "}, {"RefNumber": "2573737", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "condition and many characteristics in drill", "RefUrl": "/notes/2573737 "}, {"RefNumber": "2363252", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Error message BRAIN 564 \"Input ready formula does not have any inversed formulas\"", "RefUrl": "/notes/2363252 "}, {"RefNumber": "2572849", "RefComponent": "BW4-ME-ADSO", "RefTitle": "ADSO: Termination of data activation with non-cumulatives (II)", "RefUrl": "/notes/2572849 "}, {"RefNumber": "2572349", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Orphaned Calculation Scenarios", "RefUrl": "/notes/2572349 "}, {"RefNumber": "2554828", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Texts of nodes that can be posted to are not displayed correctly", "RefUrl": "/notes/2554828 "}, {"RefNumber": "2559772", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Calculated member: Longer runtime/authorization problems", "RefUrl": "/notes/2559772 "}, {"RefNumber": "2571705", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "LOPD: No logging for certain standard queries", "RefUrl": "/notes/2571705 "}, {"RefNumber": "2572376", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "BW Query on HCPR/AggrWithJoin fails with HANA error", "RefUrl": "/notes/2572376 "}, {"RefNumber": "2572069", "RefComponent": "BW4-ME-ADSO", "RefTitle": "DTO: Display of COLD partitions as 'MIXED'", "RefUrl": "/notes/2572069 "}, {"RefNumber": "2571471", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for Query: apply convex hull filter", "RefUrl": "/notes/2571471 "}, {"RefNumber": "2570984", "RefComponent": "BW-WHM-MTD-CTS", "RefTitle": "Query cannot be saved if it contains original and non-original objects", "RefUrl": "/notes/2570984 "}, {"RefNumber": "2568876", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Data access profiles ignored during authorization check", "RefUrl": "/notes/2568876 "}, {"RefNumber": "2570994", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Optimization for the function RSZ_X_MEMBERS_GET", "RefUrl": "/notes/2570994 "}, {"RefNumber": "2570649", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for method MEMBER_GET of class CL_RSR_MDX_META_DATA", "RefUrl": "/notes/2570649 "}, {"RefNumber": "2570538", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "BI Analysis Office: incorrect authorization check during first save of a query view", "RefUrl": "/notes/2570538 "}, {"RefNumber": "2569767", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "InA: Hierarchy dimension returns incorrect parent element of first entry", "RefUrl": "/notes/2569767 "}, {"RefNumber": "2569429", "RefComponent": "BW-SYS-DB-DB4", "RefTitle": "IBM i: Runtime problems for input help for flat InfoCubes (II)", "RefUrl": "/notes/2569429 "}, {"RefNumber": "2568999", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "Dummy", "RefUrl": "/notes/2568999 "}, {"RefNumber": "2563489", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "HCPR with time dep. JOIN uses wrong filter/select field", "RefUrl": "/notes/2563489 "}, {"RefNumber": "2568463", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: No data for calendar year/calendar week (0CALWEEK)", "RefUrl": "/notes/2568463 "}, {"RefNumber": "2568283", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Database error/SQL statement too large", "RefUrl": "/notes/2568283 "}, {"RefNumber": "2567991", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "AO Workbook Conversion does not convert view state correctly", "RefUrl": "/notes/2567991 "}, {"RefNumber": "2567864", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZC: Variable cannot be used in the InfoProvider XYZ...", "RefUrl": "/notes/2567864 "}, {"RefNumber": "2567407", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "CX_SY_RANGE_OUT_OF_BOUNDS when sorting query result", "RefUrl": "/notes/2567407 "}, {"RefNumber": "2567022", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Inconsistent Input Parameter (parameter: <unknown>, value <unknown>)", "RefUrl": "/notes/2567022 "}, {"RefNumber": "2565824", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Dynamic Formula Produces Exception", "RefUrl": "/notes/2565824 "}, {"RefNumber": "2563959", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Formulas calculated in SAP HANA: IF, DIV, MOD", "RefUrl": "/notes/2563959 "}, {"RefNumber": "2565828", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Hierachy Nodes Text values are not shown in BEx Analyzer when SDPR is used", "RefUrl": "/notes/2565828 "}, {"RefNumber": "2565701", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Use of unsupported read mode", "RefUrl": "/notes/2565701 "}, {"RefNumber": "2564327", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "The Operator %, %A or %_A produces no warning if operands have different dimensions.", "RefUrl": "/notes/2564327 "}, {"RefNumber": "2564263", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Non-cumulative Key Figure Value doubled in Query on HCPR", "RefUrl": "/notes/2564263 "}, {"RefNumber": "2564236", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS: Check KIDs error message in case of normalization", "RefUrl": "/notes/2564236 "}, {"RefNumber": "2564198", "RefComponent": "BW-WHM-MTD-HMOD", "RefTitle": "External SAP HANA view for Query: filter not used Partprovider", "RefUrl": "/notes/2564198 "}, {"RefNumber": "2563985", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Runtime error when calling the BAPI GetCubes", "RefUrl": "/notes/2563985 "}, {"RefNumber": "2561421", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "normalization and hierarchical structure", "RefUrl": "/notes/2561421 "}, {"RefNumber": "2563843", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "PSA: Editing a single record in an ErrorStack results in clearing of initial values for Date Fields.", "RefUrl": "/notes/2563843 "}, {"RefNumber": "2555966", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A formula with the operator MOD (modulo) returns \"*\".", "RefUrl": "/notes/2555966 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DW4CORE", "From": "100", "To": "100", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}