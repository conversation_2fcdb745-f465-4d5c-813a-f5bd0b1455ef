{"Request": {"Number": "116095", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 599, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014593882017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000116095?language=E&token=89871E8C2946BFC2FC6798391CFEC148"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000116095", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000116095/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "116095"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 167}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.10.2004"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "116095 - Solution Tools Plug-In (TCC Basis Tools and Trace Tools)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The Solution Tools Plug-In (formerly TCC Basis Tools), like the SQL Trace Interpreter SQLR, the Service Data Collection Center SDCC, download modules as well as other tools are used by the SAP Support Services (GoingLive Check, EarlyWatch Check, and EarlyWatch Alert).<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SQL Trace, SQLR, Trace Interpreter, EarlyWatch Alert, SDCC,&#x00A0;&#x00A0;Solution Tools Plug-In, ST-PI</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The Solution Tools Plug-In (formerly: TCC Basis Tools and Trace Tools) are used as of Release 4.0B but are not available in the standard system. They have to be subsequently imported and are available as add-ons.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>The following applies to SAP Releases 4.0B, 4.5B, 4.6B-4.6D, 6.10-6.20:</B><br /><br />Import the add-on <B>corresponding to your release</B>. It is available on the SAP Service Marketplace at http://service.sap.com/installations or http://service.sap.com/patches&#x00A0;&#x00A0; --&gt; 'Entry by Application Group' --&gt; 'Plug-Ins' --&gt; 'SAP Solution Tools Plug-In'<br /><br /><br />Refer to SAP Note 539977 for installation instructions.<br /><br /><B>The following applies to SAP Releases 3.0D-3.0F, 3.1G, and 3.1H-3.1I:</B><br />Refer to SAP Note 597323 for installation instructions.<br /><br />In all other cases refer to SAP Note 560630.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-BO (Backoffice Service Delivery)"}, {"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "MODE"}, {"Key": "Transaction codes", "Value": "SE38"}, {"Key": "Transaction codes", "Value": "DICTIONARY"}, {"Key": "Transaction codes", "Value": "DB2"}, {"Key": "Transaction codes", "Value": "SAINT"}, {"Key": "Transaction codes", "Value": "ST05"}, {"Key": "Transaction codes", "Value": "SE09"}, {"Key": "Transaction codes", "Value": "SDCC"}, {"Key": "Transaction codes", "Value": "SQLR"}, {"Key": "Transaction codes", "Value": "SM01"}, {"Key": "Responsible                                                                                         ", "Value": "D001524"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D022880)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000116095/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000116095/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116095/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116095/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116095/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116095/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116095/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116095/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000116095/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "792941", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/792941"}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680"}, {"RefNumber": "769623", "RefComponent": "SV-SMG-SDD", "RefTitle": "Installation/delta upgrade of ST-PI 2005_1", "RefUrl": "/notes/769623"}, {"RefNumber": "597323", "RefComponent": "SV-SMG-SDD", "RefTitle": "TCC basis tools in releases EARLIER than Release 4.0B!", "RefUrl": "/notes/597323"}, {"RefNumber": "574177", "RefComponent": "BC-DB-LVC", "RefTitle": "DBIF_DSQL2_CONNECTSTR_ERROR for LCA in non-APO systems", "RefUrl": "/notes/574177"}, {"RefNumber": "562578", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/562578"}, {"RefNumber": "560630", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST-PI: Solution Tools plug-in - prerequisite not met!", "RefUrl": "/notes/560630"}, {"RefNumber": "555198", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/555198"}, {"RefNumber": "549298", "RefComponent": "SV-SMG", "RefTitle": "RSORAVSH: LOAD_PROGRAM_NOT FOUND during the run of RSCOLL00", "RefUrl": "/notes/549298"}, {"RefNumber": "541392", "RefComponent": "BC-DB-DBI", "RefTitle": "st05: \"Statement summary\" download", "RefUrl": "/notes/541392"}, {"RefNumber": "540442", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/540442"}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977"}, {"RefNumber": "534812", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/534812"}, {"RefNumber": "455356", "RefComponent": "SV-SMG-OP", "RefTitle": "Requirements for System Monitoring in Solution Manager", "RefUrl": "/notes/455356"}, {"RefNumber": "452407", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "CALL_FUNCTION_NOT_ACTIVE for STUO function modules", "RefUrl": "/notes/452407"}, {"RefNumber": "444003", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/444003"}, {"RefNumber": "429186", "RefComponent": "BC-CTS-TLS", "RefTitle": "Deleting recently imported DD entries", "RefUrl": "/notes/429186"}, {"RefNumber": "383270", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/383270"}, {"RefNumber": "376897", "RefComponent": "SV-SMG-INS", "RefTitle": "Installing the SAP Solution Manager user interface", "RefUrl": "/notes/376897"}, {"RefNumber": "360995", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/360995"}, {"RefNumber": "188100", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/188100"}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939"}, {"RefNumber": "178631", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/178631"}, {"RefNumber": "152389", "RefComponent": "IS-OIL-BC", "RefTitle": "Oil: Customizing for SQL Trace Interpreter", "RefUrl": "/notes/152389"}, {"RefNumber": "133017", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/133017"}, {"RefNumber": "1228898", "RefComponent": "SV-SMG-SDD", "RefTitle": "Installation/delta upgrade ST-PI 2008_1", "RefUrl": "/notes/1228898"}, {"RefNumber": "1108861", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1108861"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1228898", "RefComponent": "SV-SMG-SDD", "RefTitle": "Installation/delta upgrade ST-PI 2008_1", "RefUrl": "/notes/1228898 "}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680 "}, {"RefNumber": "539977", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy for add-on ST-PI", "RefUrl": "/notes/539977 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "769623", "RefComponent": "SV-SMG-SDD", "RefTitle": "Installation/delta upgrade of ST-PI 2005_1", "RefUrl": "/notes/769623 "}, {"RefNumber": "549298", "RefComponent": "SV-SMG", "RefTitle": "RSORAVSH: LOAD_PROGRAM_NOT FOUND during the run of RSCOLL00", "RefUrl": "/notes/549298 "}, {"RefNumber": "541392", "RefComponent": "BC-DB-DBI", "RefTitle": "st05: \"Statement summary\" download", "RefUrl": "/notes/541392 "}, {"RefNumber": "455356", "RefComponent": "SV-SMG-OP", "RefTitle": "Requirements for System Monitoring in Solution Manager", "RefUrl": "/notes/455356 "}, {"RefNumber": "452407", "RefComponent": "BC-CCM-MON-ORA", "RefTitle": "CALL_FUNCTION_NOT_ACTIVE for STUO function modules", "RefUrl": "/notes/452407 "}, {"RefNumber": "376897", "RefComponent": "SV-SMG-INS", "RefTitle": "Installing the SAP Solution Manager user interface", "RefUrl": "/notes/376897 "}, {"RefNumber": "429186", "RefComponent": "BC-CTS-TLS", "RefTitle": "Deleting recently imported DD entries", "RefUrl": "/notes/429186 "}, {"RefNumber": "597323", "RefComponent": "SV-SMG-SDD", "RefTitle": "TCC basis tools in releases EARLIER than Release 4.0B!", "RefUrl": "/notes/597323 "}, {"RefNumber": "560630", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST-PI: Solution Tools plug-in - prerequisite not met!", "RefUrl": "/notes/560630 "}, {"RefNumber": "187939", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP Servicetools Update (RTCCTOOL)", "RefUrl": "/notes/187939 "}, {"RefNumber": "574177", "RefComponent": "BC-DB-LVC", "RefTitle": "DBIF_DSQL2_CONNECTSTR_ERROR for LCA in non-APO systems", "RefUrl": "/notes/574177 "}, {"RefNumber": "152389", "RefComponent": "IS-OIL-BC", "RefTitle": "Oil: Customizing for SQL Trace Interpreter", "RefUrl": "/notes/152389 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "620", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B45", "URL": "/supportpackage/SAPKH40B45"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B49", "URL": "/supportpackage/SAPKH40B49"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B49", "URL": "/supportpackage/SAPKE40B49"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B45", "URL": "/supportpackage/SAPKE40B45"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B23", "URL": "/supportpackage/SAPKH45B23"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B27", "URL": "/supportpackage/SAPKH45B27"}, {"SoftwareComponentVersion": "SAP_APPL 46A", "SupportPackage": "SAPKH46A01", "URL": "/supportpackage/SAPKH46A01"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B08", "URL": "/supportpackage/SAPKB46B08"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B09", "URL": "/supportpackage/SAPKB46B09"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B13", "URL": "/supportpackage/SAPKB46B13"}, {"SoftwareComponentVersion": "SAP_BASIS 610", "SupportPackage": "SAPKB61020", "URL": "/supportpackage/SAPKB61020"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62004", "URL": "/supportpackage/SAPKB62004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}