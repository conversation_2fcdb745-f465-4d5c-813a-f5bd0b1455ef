{"Request": {"Number": "619876", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 617, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015451032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000619876?language=E&token=C89648AAF300AD2EBBCB0F201204AFE3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000619876", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000619876/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "619876"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.02.2012"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "619876 - Oracle9i: Automatic PGA Memory Management"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br /><br />This note describes the Oracle feature <B>'Automatic PGA' (Program Global Area)</B>. This feature is new in Oracle9i.<br /><br />This note applies to Oracle Releases 9i, 10g and 11g. <br /><br />Other names for 'Automatic PGA' are:<br />- 'Automatic PGA Tuning'<br />- 'Automatic SQL Execution Memory Management'<br />- 'Automatic PGA Memory Management'<br />- 'Oracle Managed PGA'<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br /><br />PGA, Program Global Area, work area<br />sort_area_size, sort_area_retained_size,<br />hash_area_size,<br />create_bitmap_area_size,<br />bitmap_merge_area_size,<br />'Aggregated PGA Memory'<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />Documentation note on Oracle9i: <B> 'Automatic PGA Tuning' </B><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /></p> <b>Support by BR*Tools and SAPDBA<br /><br />This new Oracle9i feature is transparent for BR*Tools and SAPDBA. We recommend BR*Tools Version 6.20 patch level 103 (or higher) that is compatible with Oracle 9I.<br /></b><br /> <b>General Recommendation<br /></b><br /> <p>This feature should be activated when you upgrade the Oracle database to Oracle9i. It is activated by default for new SAP installations.<br /><br />This feature is particularly suited to and beneficial when <B>used with SAP BW applications (Data Warehouse)</B> due to the memory-intensive SQL operations typical of the Data Warehouse area, (large sorts and joins).<br /></p> <b>Terms and synonyms used<br /></b><br /> <p>In the following sections, the terms 'work area' and 'PGA' are used synonymously. Similarly, the terms main memory, physical memory and memory are used synonymously. Shadow process, shadow and server process are also synonymous terms.<br /></p> <b>Automatic PGA - Description<br /></b><br /> <p>The <B>PGA (=Program Global Area)</B> is the part of the memory that is privately assigned to an <B>Oracle server process (shadow process, 'shadow')</B> and that contains data and information (for example, open cursors) that only this server process needs to process queries and commands. Examples of these kinds of memory-intensive operations are<br />- Sorting operations (ORDER BY, GROUP BY)<br />- Hash join<br />- Bitmap merge join<br />- Bitmap create<br />- Bulk operations (bulk load, bulk insert)<br /><br />The PGA consists of an area which can be optimized ('tunable') and an area which cannot be optimized ('non tunable').<br /><br />Since the speed (performance) of queries is also largely dependent on the amount of available PGA memory (also 'work area'), you should pay attention to the tuning of this memory.<br /><br />Performance usually increases considerably if more memory is available. Ideally, the work area should be just large enough to contain all the data and internal structures to execute the current SQL operation (example: memory sort). This ideal size is called <B>Optimal Work Area Size</B>. Making the work area too large does not improve performance, since the allocated memory is therefore occupied in vain. However, if it is too small, the system has to use the disk as a temporary storage area (temporary tablespace) for the sort operation, which causes the response time to increase significantly due to the additional I/O. If a once-off transfer of an interim result to the disk is sufficient, this is referred to as a <B>'one-pass'</B> sort, otherwise, it is called a <B>'multi-pass'</B> sort, and the size of the work areas is referred to as a <B>'one pass' work area size</B> or a <B>'multi-pass' work area size</B>. In the case of a multi-pass, the response time increases drastically compared to the optimum time. For this reason, this worst case should not occur or only occur very seldom. The aim here is to have most work areas in the optimum range so that most queries are processed in the optimum response time (for example, 90% in the case of OLTP). A small number of queries can then be in the 'one-pass' area (for example, less than 10% in the case of OLTP). You should avoid the 'multi-pass' area if possible.<br /><br />Up to Oracle 8I, the database administrator was responsible for setting up the PGA accurately and optimally. This occurred by specifying the following init.ora parameter:</p> <UL><LI>sort_area_size</LI></UL> <UL><LI>hash_area_size,</LI></UL> <UL><LI>bitmap_merge_size</LI></UL> <UL><LI>create_bitmap_area_size,</LI></UL> <p>These PGA parameters specify the allocated PGA memory of an Oracle shadow process in the manual PGA management mode.<br /><br />Disadvantages of the previous PGA management:<br />The most problematic aspect of the previous approach was that the values were the same size for all shadow processes. Furthermore, you selected these sizes depending on the anticipated dataset as well as on the total number of work areas, that is, the number of shadow processes. Both sizes can vary, so that it is difficult to find the best configuration in each case. With Windows NT/2000, there was the additional restriction of the maximum amount of memory that could be allocated, and the fact that the PGA memory was part of the SGA memory (ORA-04030: out of memory)<br /><br />With Oracle 9i, it is now possible to leave this PGA tuning task to the Oracle server, thereby making the database easier to administer (and more efficient). As of Oracle 9i, you can use this feature to adjust the above parameter automatically and dynamically, thereby avoiding problems with too little or too much allocated PGA memory.<br /><br />The database administrator now only has to determine the maximum total PGA memory of an instance (PGA_AGGREGATE_TARGET). To maximize overall performance through efficient memory usage, the Oracle server uses an intelligent algorithm to divide this memory on the existing Oracle server processes depending on demand.<br /><br />The size of the total PGA memory can be changed dynamically at the instance runtime. Since the Oracle server is most familiar with the SQL commands currently being executed, we recommend that you use this feature instead of trying to configure the PGA manually as previously. Doing this results in a higher throughput as well as a shorter response time.<br /><br />The following is a short definition of terms, which is important for the rest of the note: For a work area, the term<br />-<B>-'optimal size'</B> (with reference to an SQL operation) is used, if the work area is (just) large enough for the SQL operation to be handled completely in a single program run in the main memory. The execution is then included in the statistics under optimal_executions<br />-<B>'One-pass size' </B>(with reference to an SQL operation) is used, if the work area size is smaller than optimal, and an additional processing run must therefore be carried out. This size of work area is then also called a 'one pass work area size'.<br />- <B>'Multi-pass size'</B> (with reference to an SQL operation) means that several additional passes are necessary to execute the SQL operation. The response time increases significantly. Therefore, this case should not occur.<br /></p> <b>Automatic PGA - Functions, prerequisites and restrictions<br /></b><br /> <p>The old PGA parameters sort_area_size, Hash_area_size, bitmap_merge_area_size, create_bitmap_area_size, sort_area_retained_size<br />are ignored by Oracle if 'Automatic PGA Tuning' is active.<br /><br />The following two parameters are crucial for this feature:<br />- WORKAREA_SIZE_POLICY<br />- PGA_AGGREGATE_TARGET<br /><br /><B>WORKAREA_SIZE_POLICY</B><br />The parameter WORKAREA_SIZE_POLICY controls whether Automatic PGA is activated. In Release 9i, Automatic PGA is deactivated by default (MANUAL), as of Release 10g, it is activated by default (AUTO).<br />SQL&gt;alter system set workarea_size_policy=AUTO; -- nur Oracle9i<br /><br /><B>PGA_AGGREGATE_TARGET</B><br />This parameter defines the maximum amount of memory (in bytes, Kilo (K), Mega (M), Giga (G)) that can be used for PGA. It is dynamically adjustable, for example:<br />SQL&gt;alter system set pga_aggregate_target=1024M;<br />PGA_AGGREGATE_TARGET limits both the globally available PGA memory for the work areas of all Oracle shadow processes and (using internal Oracle limits) the work area of an individual shadow process. This prevents a single very large sort process from using up too much memory.<br /><br />Caution: Since the Oracle shadow processes allocate and use this memory, and since these processes run on the database server, you must take the memory requirement of the PGA into account when calculating the available memory for the SGA (sga_max_size).<br /><br />When calculating an access plan, the Oracle Cost Based Optimizer (CBO) takes the PGA_AGGREGATE_TARGET parameter into account by including the minimum and maximum amounts of available main memory from the parameter for a later execution time into the calculation. Switching on this feature may therefore lead to changes in the access plans.<br /><br />The allocation of the total PGA memory occurs according to an Oracle algorithm. This takes the following into account:</p> <UL><LI>The total PGA size of all server processes cannot exceed the value of PGA_AGGREGATE_TARGET.</LI></UL> <UL><LI>The system tries to allocate each process as much memory as it requires.</LI></UL> <UL><LI>Throughput and response time should be optimized equally. Memory-intensive operations are given priority when memory is allocated.</LI></UL> <p>Over and above these general aims, the algorithm takes account of some additional limits that ensure that the system does not allocate the total PGA memory to a single shadow process. These limits are (in the current Version 9.2, for example)</p> <UL><LI>a maximum of 5% of the pga_aggregate_target for a shadow process</LI></UL> <UL><LI>a maximum of 30% of the pga_aggregate_target for a shadow process in the case of parallel operations</LI></UL> <UL><LI>a maximum of 200 MB for a shadow process</LI></UL> <p>Caution: In theory, these limits may change if you import a patch, a patch set or if you use a new Oracle version.<br /><br /><B>STATISTICS_LEVEL,</B><br />In order that the views V$PGA_TARGET_ADVICE and V$PGA_TARGET_ADVICE_HISTOGRAM (see below) contain information on the setting of pga_aggregate_target, statistics_level has to apply to the profile parameter:<br />statistics_level=TYPICAL<br />This also applies to the default if the parameter is not set.<br /></p> <b>Automatic PGA - Advantages<br /></b><br /> <p>This feature provides the following advantages:</p> <UL><LI>Simple PGA memory tuning</LI></UL> <UL><LI>Reduced administration work for the DBA (only one parameter)</LI></UL> <UL><LI>The total PGA memory is easier to quantify than the per sort area per dedicated database shadow process</LI></UL> <UL><LI>No recalculation of the sort_area_size is necessary if the number of work processes increases.</LI></UL> <UL><LI>Better memory use: Physical memory is only allocated by the shadow process that actually needs the memory for a sort or a similar process</LI></UL> <UL><LI>A shadow process returns allocated PGA memory that is no longer required to the Oracle PGA management so that another shadow process can use this memory. Therefore, the allocated memory is used more efficiently. In contrast, in the case of manual PGA management, a shadow process only returns the PGA memory allocated at the start of the process to the operating system once the process has ended.</LI></UL> <UL><LI>Options to achieve efficient monitoring, tuning and simulation within the Oracle server</LI></UL> <UL><LI>Higher system performance, shorter response times, more efficient memory usage; The ideal situation, in which the optimum amount of PGA memory is available to the Oracle shadow process, means that SQL queries are processed faster. The response time (OLTP) decreases or the system performance (OLAP, DSS) increases.<br /></LI></UL> <p>In particular, the fact that a shadow process can release allocated PGA memory again (that is, return it to the operating system) is important for SAP Systems because, in this case, a dedicated Oracle shadow process is tied to an SAP work process, usually for the duration of the work process.<br /><br />However, one possible disadvantage is that too much PGA memory may be reserved in advance and consequently wasted if the PGA_AGGREGATE_TARGET calculated is too high. To prevent this, you should use the monitoring options, which enable you to recognize an over-allocation of PGA memory.<br /></p> <b>Automatic PGA - Special features<br /></b><br /> <UL><LI>The system has previously ignored the initialization parameters, such as *_area_size, that affect the PGA if 'Automatic PGA' is active.</LI></UL> <UL><LI>Before you can activate 'Automatic PGA Tuning', you must set pga_aggregate_target (otherwise: ORA-04032).</LI></UL> <UL><LI>You can only use 'Automatic PGA memory management' with the Oracle Dedicated Server model, not with Multi Threaded or Shared Server (MTS). For MTS, the *_AREA_SIZE parameters are valid as previously.</LI></UL> <UL><LI>Current PL/SQL or Java procedures in Oracle cannot use the automatic PGA memory. This was not relevant previously to SAP applications.<br /></LI></UL> <b>Automatic PGA - Migration<br /></b><br /> <p>When implementing this feature, you should take particular care that the sort areas (specified by the sort_area_size and other PGA parameters) previously reserved for each process do not become smaller. This would cause sorts previously executed in the memory to be executed on disk from now on, which would make them considerably slower.<br /><br />To specify PGA_AGGREGATE_TARGET, proceed as follows:<br />1. Step: Calculate or estimate (empirically) a good start value<br />2. Step: Monitor PGA_AGGREGATE_TARGET (under a representative system workload)<br />3. Step: Optimize PGA_AGGREGATE_TARGET<br /><br />The steps mentioned above for calculating PGA_AGGREGATE_TARGET are now presented in more detail:<br /><br /><B>Step 1: Calculating/estimating a good start value</B><br /> <br /><br />The following general recommendations apply to a standalone database server:<br />For Data Warehouse:<br />PGA_AGGREGATE_TARGET = &lt;Total physical memory&gt; * 40%<br /><br />For OLTP:<br />PGA_AGGREGATE_TARGET = &lt;Total physical memory&gt; * 20%<br /> <br /><br />You should use the following formula as an additional guide value and lower limit:<br /><br />pga_aggregate_target &gt;= (sort_area_size+hash_area_size+...) *<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(#shadow processes / 10)<br /><br />This formula is based on the assumption that not all shadow processes are simultaneously busy with larger sort tasks. Therefore, only (the number of shadow processes/10). You can use the Oracle profile parameter <B>processes</B> to determine the number of shadow processes. This parameter determines the maximum number of Oracle shadow processes. Previously (with conventional PGA), each shadow process, whether it was used or not, permanently reserved sort_area_size (SAP R/3: typical R/3 value is 2M) or sort_area_size+hash_area_size+bitmap_merge_size+create_bitmap_area_size (SAP BW) of private PGA memory, which, in the case of a large number of these processes, led to a correspondingly large demand for physical memory on the database server. The number of Oracle shadow processes is typically eight to twelve times the number of SAP application servers, as approximately eight to twelve Disp+Work processes run per SAP application server.<br /><br />Since the database server must have sufficient physical memory for the SGA, for the PGA and, if necessary, for other non-Oracle processes, pga_aggregate_target is also restricted by the following formula:<br /><br />sga_max_size+pga_aggregate_target &lt;= &lt;phys.memory of DB server&gt;<br /><br />The implicit restriction that applies behind this formula is that no paging should take place during the Oracle processes.<br /><br /><B>Step 2: Monitoring PGA_AGGREGATE_TARGET</B><br /><br />Even if the complete 'Tuning PGA Automatic' process passes off automatically, it is nevertheless important and necessary to monitor this process to see whether this feature is suitably set up for the system environment or whether this feature still needs to be changed.<br /><br />The following Oracle performance views are available and contain information about PGA monitoring if automatic PGA is active:<br /></p> <UL><LI>V$PGASTAT</LI></UL> <UL><LI>V$SQL_WORKAREA</LI></UL> <UL><LI>V$SQL_WORKAREA_HISTOGRAM</LI></UL> <UL><LI>V$SQL_WORKAREA_ACTIVE</LI></UL> <UL><LI>V$PROCESS with the following new columns:<br />PGA_USED_MEM, PGA_ALLOC_MEM, PGA_MAX_MEM<br /></LI></UL> <p>The following views are relevant for the actual tuning (see step 3 also)</p> <UL><LI>V$PGA_TARGET_ADVICE</LI></UL> <UL><LI>V$PGA_TARGET_ADVICE_HISTOGRAM<br /></LI></UL> <p>V$SYSSTAT and V$SESSTAT also contain new work area statistics for monitoring automatic PGA memory tuning.<br /><br />Before tuning the PGA memory, you should familiarize yourself with the statistics provided by the views mentioned above.<br /><br />Examples of interesting sizes are provided below in the 'Automatic PGA - Monitoring' section.<br /><br /><B>Step 3: Optimizing/tuning PGA_AGGREGATE_TARGET</B><br /><br />For tuning, that is, for the optimal setting for PGA_AGGREGATE_TARGET, the Oracle server contains the following two performance views:</p> <UL><LI>V$PGA_TARGET_ADVICE</LI></UL> <UL><LI>V$PGA_TARGET_ADVICE_HISTOGRAM<br /></LI></UL> <p>Using the statistics provided in these two views, you can set the parameter PGA_AGGREGATE_TARGET optimally.<br /><br />The contents of these views are valid from start of the instance, and are therefore initialized when restarting. We recommend that you monitor these two views again after each change of PGA_AGGREGATE_TARGET.<br /><br />Guidelines and SQL commands for tuning PGA_AGGREGATE_TARGET are provided below in the 'Automatic PGA - Tuning' section.<br /></p> <b>Automatic PGA - Monitoring<br /></b><br /> <p>The following contains examples of SQL queries on the PGA statistics<br />that are relevant for monitoring and tuning the PGA:<br /><br /><B>V$PGASTAT</B><br /><br />SQL&gt;select * from v$pgastat<br /><br />The following main statistics are of interest here:<br /><B>'aggregate PGA target parameter'</B><br />This displays the value to which PGA_AGGREGATE_TARGET is set<br /><br /><B>'aggregate PGA auto target'</B><br />This displays the value that Oracle can use for work areas that run in the automatic PGA mode. It is effective if this value is not small in relation to the total size of the PGA_AGGREGATE_TARGET aggregate PGA target parameter. Therefore, there should still be sufficient space remaining for the work area.<br /><br /><B>'over allocation count'</B><br />This value specifies how often the value of PGA_AGGREGATE_TARGET did not meet the minimum memory requests of the server process. As a result, Oracle had to add additional PGA memory internally again over and above the maximum value specified in PGA_AGGREGATE_TARGET. If 'over allocation count' is greater than zero, this means that the value of PGA_AGGREGATE_TARGET is too low. You must then increase PGA_AGGREGATE_TARGET. If PGA_AGGREGATE_TARGET is set optimally, the value of 'over allocation count' should be zero.<br /><br /><B>'cache hit percentage'</B><br />This value shows the performance/efficiency of the Oracle automatic PGA memory management since the instance started. The ideal result of 100%, which is not usually achieved (nor does it have to be), means that the work areas of the server processes were the optimum size for all queries and that no one-pass or multi-pass operations occurred.<br /><br /><B>V$SQL_WORKAREA_HISTOGRAM</B><br />This view displays how often how much PGA memory was needed and whether the sort was executed optimally (that is, in the memory) or as a one-pass or a multi-pass. From these statistics, which firstly provide an overview of the frequency and the memory requirement of SQL queries, you can then draw conclusions as to whether the total PGA is sufficiently assessed.<br /><br />For example, the following queries deliver useful assertions regarding this view:<br /><br />Query 1: What work area sizes were needed for the SQL queries executed to date and how often did this happen in each case? Could the queries be processed completely in the main memory?<br />SQL&gt; select<br />&#x00A0;&#x00A0;low_optimal_size/1024 low_opt_size,<br />&#x00A0;&#x00A0;(high_optimal_size+1)/1024 high_opt_size,<br />&#x00A0;&#x00A0;optimal_executions,<br />&#x00A0;&#x00A0;onepass_executions,<br />&#x00A0;&#x00A0;multipasses_executions<br />from v$sql_workarea_histogram<br />where total_executions != 0;<br /><br />From a performance point of view, the aim is to configure the work area large enough that no multi-pass executions are necessary.<br /><br />Query 2: What is the percentage relationship between SQL queries of a specific work area size in each case? How were the differently sized queries processed in each case?<br />SQL&gt; select<br />&#x00A0;&#x00A0;optimal_count, round(optimal_count*100/total,2) optimal_perc,<br />&#x00A0;&#x00A0;onepass_count, round(onepass_count*100/total,2) onepass_perc,<br />&#x00A0;&#x00A0;multipass_count, round(multipass_count*100/total,2) multipass_perc<br />from<br />&#x00A0;&#x00A0;(select decode(sum(total_executions),0,1,sum(total_executions))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;total,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sum (optimal_executions) optimal_count,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sum (onepass_executions) onepass_count,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sum (multipasses_executions) multipass_count<br />&#x00A0;&#x00A0;from v$sql_workarea_histogram<br />&#x00A0;&#x00A0; where low_optimal_size &gt; 1*1024 -- for 1K optimal size<br />&#x00A0;&#x00A0; );<br /><br /><B>V$SQL_WORKAREA_ACTIVE</B><br />This view displays currently active SQL queries, that is, SQL queries of an instance currently being executed with their work area memory allocation behavior. Depending on the application, the display in this view is very dynamic.<br /><br /><B>V$SQL_WORKAREA</B><br /><br />Example of a query: Display the ten largest work areas<br />SQL&gt;SELECT *<br />&#x00A0;&#x00A0;FROM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;( SELECT workarea_address,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;operation_type,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;policy,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;estimated_optimal_size<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FROM V$SQL_WORKAREA<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORDER BY estimated_optimal_size)<br />&#x00A0;&#x00A0;WHERE ROWNUM &lt;=10;<br /><br />Example of an additional query: Display the SQL statements that could be not be processed as 'optimal pass' operations, therefore, for which the work area was too small (relative to the optimum):<br />SQL&gt;col sql_text format A80 wrap<br />SELECT sql_text, sum(ONEPASS_EXECUTIONS) onepass_cnt,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;sum(MULTIPASSES_EXECUTIONS) mpass_cnt<br />FROM V$SQL s, V$SQL_WORKAREA wa<br />WHERE s.address = wa.address<br />GROUP BY sql_text<br />HAVING sum(ONEPASS_EXECUTIONS+MULTIPASSES_EXECUTIONS)&gt;0;<br /><br /><B>V$PROCESS</B><br /><br />Example of a query: Display the PGA memory allocation of the Oracle server processes:<br />SQL&gt; SELECT program, pga_used_mem, pga_alloc_mem, pga_max_mem<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; FROM V$PROCESS;<br /><br /><B>V$SYSSTAT</B><br /><br />Example of a query: Determine the portion of the work areas of the optimum size: How often was a work area used in the optimal-pass, one-pass and multi-pass mode (the absolute and percentage portion):<br />SQL&gt;SELECT name profile,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;cnt,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;decode(total, 0, 0, round(cnt*100/total)) percentage<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;FROM<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(SELECT name, value cnt, (sum(value) over ()) total<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FROM V$SYSSTAT<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WHERE name like 'workarea exec%');<br /><br /><B>V$SESSTAT</B><br /></p> <b>Automatic PGA - Tuning<br /></b><br /> <p>The aim of PGA tuning: Determining the optimum value for PGA_AGGREGATE_TARGET<br /><br />Query: What is the minimum size setting for PGA_AGGREGATE_TARGET?<br />SQL&gt;SELECT round(PGA_TARGET_FOR_ESTIMATE/1024/1024) target_mb,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ESTD_PGA_CACHE_HIT_PERCENTAGE cache_hit_perc,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; ESTD_OVERALLOC_COUNT<br />&#x00A0;&#x00A0;FROM v$pga_target_advice;<br /><br />Interpreting the result and tuning of PGA_AGGREGATE_TARGET:<br /><B>1. Avoid PGA memory over-allocation</B><br />Set PGA_AGGREGATE_TARGET at least large enough so that the following applies: ESTD_OVERALLOC_COUNT=0. If you set PGA_AGGREGATE_TARGET smaller (that is, too small), Oracle would automatically over-allocate this minimum amount of memory.<br /><B>2. Maximize the PGA cache hit percentage</B><br />The optimum, ideal cache_hit_perc value would be 100%, however, this is only achievable with a very high memory requirement. Here, you should assess the marginal benefit of each additional MB and, based on that, decide whether it justifies the memory usage. The optimum value for PGA_AGGREGATE_TARGET is the value at which a further increase would not result in any relevant increase of the PGA cache hit percentage. If the database server has sufficient physical main memory for this optimum PGA size, set PGA_AGGREGATE_TARGET to this value. Otherwise, set PGA_AGGREGATE_TARGET to a size so that there is still enough memory remaining for the SGA.<br />Rule of thumb: A cache hit ratio of 90% for BW systems and of 60% for OLTP should be sufficient.<br /><br />Simulating a higher value for PGA_AGGREGATE_TARGET<br /><br />The following query returns an assertion regarding the (estimated) effect of a higher PGA_AGGREGATE_TARGET value on the execution behavior (displayed in the V$SQL_WORKAREA_HISTOGRAM view):<br /><br />For example: What would the contents of V$SQL_WORKAREA_HISTOGRAM be if PGA_AGGREGATE_TARGET was twice as large (pga_target_factor = 2)?<br />SQL&gt;SELECT LOW_OPTIMAL_SIZE/1024 low_kb,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(HIGH_OPTIMAL_SIZE+1)/1024 high_kb,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;estd_optimal_executions estd_opt_cnt,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;estd_onepass_executions estd_onepass_cnt,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;estd_multipasses_executions estd_mpass_cnt<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;FROM v$pga_target_advice_histogram<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;WHERE pga_target_factor = 2<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AND estd_total_executions != 0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ORDER BY 1;<br /><br />In this case, pga_target_factor can have the following values 1, 1.2, 1.4, 1.6, 1.8, 2, 3, 4, 6, 8.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-DBA (Database Administration)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Database System", "Value": "Oracle 10"}, {"Key": "Database System", "Value": "ORACLE 9.2"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> (D029385)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000619876/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619876/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619876/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619876/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619876/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619876/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619876/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619876/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000619876/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576"}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268"}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554"}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011"}, {"RefNumber": "758642", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-00600 [qesmmCValStat4] on Oracle 9.2.0.5", "RefUrl": "/notes/758642"}, {"RefNumber": "725067", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: KGH Heap Memory corruption", "RefUrl": "/notes/725067"}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624"}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "502782", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note ora-4030", "RefUrl": "/notes/502782"}, {"RefNumber": "335230", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-4030 with NT and Business Information Warehouse", "RefUrl": "/notes/335230"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576 "}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624 "}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554 "}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268 "}, {"RefNumber": "502782", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP Note ora-4030", "RefUrl": "/notes/502782 "}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678 "}, {"RefNumber": "335230", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-4030 with NT and Business Information Warehouse", "RefUrl": "/notes/335230 "}, {"RefNumber": "725067", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: KGH Heap Memory corruption", "RefUrl": "/notes/725067 "}, {"RefNumber": "758642", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "ORA-00600 [qesmmCValStat4] on Oracle 9.2.0.5", "RefUrl": "/notes/758642 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}