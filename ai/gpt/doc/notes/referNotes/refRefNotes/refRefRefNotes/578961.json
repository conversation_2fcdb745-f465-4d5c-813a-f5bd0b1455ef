{"Request": {"Number": "578961", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1380, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002906542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=4B7BA2E1718385D2AF7CDA25CAB7450A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "578961"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.01.2003"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Argentina"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Argentina", "value": "XX-CSC-AR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "578961 - AR: Credit Invoice - Transports and Correction Instructions"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have a company code in Argentina. You require the functionality to issue and receive Credit invoices. You do not wish to apply the support packages given in Note 568446 but apply the changes manually.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Argentina; Credit Invoice</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Legal change.<br />For minimum Support Package Level see below.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If you do not wish to apply the support packages, you can choose to apply the transports given in the attached file credit_invoice.zip. Please refer to note 13719 how to apply such transports. In addition, you have to apply the complete correction instructions of this Note and regenerate the maintenance views as described below.<br />Since this includes creation of new function modules and includes, the corrections should only be applied by an experienced consultant.<br /><br />Furthermore, you have to apply the Note 568446.<br /><br />Before applying the transports, you have to ensure that your current support package level is within the limits given below. Note that you apply the transports at your own risk. If you encounter problems when applying the transport, the only viable solution may be to apply the support package given in Note 568446.<br /><br /><br /><B>1. Applying the transport files</B><br /><br />Before the transport files can be applied, ensure that the following restrictions are fulfilled:<br /><br /><br />Release 4.6C:<br /><br />Minimum support package 23,<br />Maximum support package 38,<br />Relevant Transport files K904819.U9C, R904819.U9C<br />Not necessary from support package 39<br /><br /><br />Release 4.6B:<br /><br />Minimum support package 09,<br />Maximum support package 47,<br />Relevant files K902209.U9B, R902209.U9B<br />Not necessary from support package 48<br /><br /><br />Release 4.5B:<br /><br />Minimum support package 25,<br />Maximum support package 57,<br />Relevant Transport files K901342.U4D, R901342.U4D<br />Not necessary from support package 58<br /><br /><br />Release 4.0B:<br /><br />Minimum support package 43,<br />Maximum support package 79,<br />Relevant Transport files K901710.U4B, R901710.U4B<br />Not necessary from support package 80<br /><br /><br /><B>2. Applying the correction instructions</B><br /><br />After having applied the transports, apply the correction instructions given in this Note. Since this includes creation of new function modules and includes, the corrections should only be applied by an experienced consultant.<br /><br /><br /><B>3. Creating and regenerating the maintenance views</B><br /><br />The next step is to create the maintenance dialogue screens for the views V_1ACI_LIMIT and V_1ACI_MSG_RATES. In addition, the view V_T003_B_I must be regenerated.<br /><br />This can be done as follows:<br />Start transaction SE11 to maintain the view V_1ACI_LIMIT.<br />From the menu, choose \"Utilities -&gt; Table maintenance generator\"<br />Enter the authorization group \"FC21\"<br />Enter function group \"J1AQ\"<br />Select Maintenance type \"one step\"<br />Enter overview screen number \"2000\"<br />Press the button \"Create\".<br /><br />If you have problems generating the view, it may be necessary to set the original system of the view to \"SAP\" as described in note 13719.<br /><br />Start transaction SE11 to maintain the view V_1ACI_MSG_RATES.<br />From the menu, choose \"Utilities -&gt; Table maintenance generator\"<br />Enter the authorization group \"FC21\"<br />Enter function group J1AQ<br />Select Maintenance type \"one step\"<br />Enter overview screen number \"100\"<br />Select standard recording routine<br />Press the button \"Create\".<br /><br />The view V_T003_B_I must be regenerated. This can be done as follows:<br />Start transaction SE11 to maintain the view V_T003_B_I.<br />From the menu, choose \"Utilities -&gt; Table maintenance generator\"<br />In the following screen, choose the menu entry<br />\"Generated objects -&gt; change\"<br />Confirm the warning message about modifying the function group<br />On the resulting popup \"Change generation elements\", mark the following reason for changing:<br />New field / sec. table in structure<br />Confirm the selection.<br />Another popup window \"Change generation elements: Detail\" will appear.<br />Select the overview screen for regeneration.<br />Mark the field type as \"Normal field\"<br />Confirm the warning message and save the changes.<br /><br /><br /><B>4. Apply Note 568446</B><br /><br />The Steps 1-3 have served to bring your system in the same state as if you had applied the relevant support package from Note 568446. The next step is to implement the Note 568446 and follow the documentation given in that Note on how the customizing settings must be made.<br /><br /><br /><B>5. Register the function modules against the relevant business transaction events</B><br /><br />In order to register the function modules to the relevant Business<br />transaction events, you have to perform the following IMG activity in<br />the R/3 system:<br />Financial Accounting<br />&#x00A0;&#x00A0;-&gt; Financial Accounting Global Settings<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&gt; Business Transaction Events<br />Choose the following menu entry:<br />Settings -&gt; P/S function modules -&gt; ...of a customer<br />Create the following entry:<br />Event \"********\"<br />Product \"ZARCI\"<br />Country \"AR\"<br />Appl<br />Function module:<br />(In release 4.6C and 4.6B) \"TURKEY_BOE_PRINT_********\"<br />(In release 4.0B and 4.5B) \"ARGENTINA_BOE_PRINT_********\"<br />Save the entry.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI (Financial Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "D032248"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D034907)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "credit_invoice.zip", "FileSize": "224", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001979742002&iv_version=0004&iv_guid=52AC47055BB3F24C8C351F6FF2EB1FDC"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "568446", "RefComponent": "XX-CSC-AR", "RefTitle": "Argentina: Credit invoice funcionality", "RefUrl": "/notes/568446"}, {"RefNumber": "453148", "RefComponent": "XX-CSC-BR-SD", "RefTitle": "One Time Customer Brazil: Transfer to Nota Fiscal incomplete", "RefUrl": "/notes/453148"}, {"RefNumber": "307594", "RefComponent": "XX-CSC-AR", "RefTitle": "New field names in Argentinian localization", "RefUrl": "/notes/307594"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "307594", "RefComponent": "XX-CSC-AR", "RefTitle": "New field names in Argentinian localization", "RefUrl": "/notes/307594 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "568446", "RefComponent": "XX-CSC-AR", "RefTitle": "Argentina: Credit invoice funcionality", "RefUrl": "/notes/568446 "}, {"RefNumber": "453148", "RefComponent": "XX-CSC-BR-SD", "RefTitle": "One Time Customer Brazil: Transfer to Nota Fiscal incomplete", "RefUrl": "/notes/453148 "}, {"RefNumber": "572540", "RefComponent": "XX-CSC-AR", "RefTitle": "Argentina: Errors in credit invoice functionality", "RefUrl": "/notes/572540 "}, {"RefNumber": "569546", "RefComponent": "XX-CSC-AR", "RefTitle": "Bill of exchange list: Credit Invoice in Argentinia", "RefUrl": "/notes/569546 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/**********/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 9, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "307594 ", "URL": "/notes/307594 ", "Title": "New field names in Argentinian localization", "Component": "XX-CSC-AR"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45B", "ValidTo": "46C", "Number": "307594 ", "URL": "/notes/307594 ", "Title": "New field names in Argentinian localization", "Component": "XX-CSC-AR"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46B", "ValidTo": "46B", "Number": "453148 ", "URL": "/notes/453148 ", "Title": "One Time Customer Brazil: Transfer to Nota Fiscal incomplete", "Component": "XX-CSC-BR-SD"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "430742 ", "URL": "/notes/430742 ", "Title": "Print of Bill of exchange transaction record", "Component": "XX-CSC-TR"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "524282 ", "URL": "/notes/524282 ", "Title": "RFTR_BOE_REPRINT: Transaction record number", "Component": "XX-CSC-TR"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "566194 ", "URL": "/notes/566194 ", "Title": "RFTR_BOE_REPRINT: List shows invalid transaction records", "Component": "XX-CSC-TR"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "566367 ", "URL": "/notes/566367 ", "Title": "Turkey: New functionality in check/BoE handling tools", "Component": "XX-CSC-TR"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "577564 ", "URL": "/notes/577564 ", "Title": "J1A6: wrong FI document displayed", "Component": "XX-CSC-AR"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "470", "Number": "393988 ", "URL": "/notes/393988 ", "Title": "RFTR_BOE_REPRINT: Wrong transaction record title", "Component": "XX-CSC-TR"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "470", "Number": "521316 ", "URL": "/notes/521316 ", "Title": "RFIDTRBOE1/2: Default value of company code not considered", "Component": "XX-CSC-TR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}