{"Request": {"Number": "1468591", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1262, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000008688502017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001468591?language=E&token=412744B51236AB1ACE79BBB08D4066AA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001468591", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001468591/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1468591"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 24}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.08.2014"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-INS-CFG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Setup and Configuration of the Solution Manager system"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation, Configuration and Upgrade of Solution Manager", "value": "SV-SMG-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Setup and Configuration of the Solution Manager system", "value": "SV-SMG-INS-CFG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-INS-CFG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1468591 - SAP Solution Manager - basic functions SP25 - SP27"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Note the following: This SAP Note is no longer updated. If you require information about important SAP Notes for SAP Solution Manager 7.0 SP25, SP26, or SP27, create a customer message using the component SV-SMG-INS-CFG.<br/>*********************************************************************<br/>*********************************************************************</p>\r\n<p>This SAP Note is the central correction note of SAP Solution Manager 7.0 SP25, SP26 und SP27. This SAP Note is necessary to guarantee the basic functions of your SAP Solution Manager. It is a composite note that is linked to additional SAP Notes. It is expanded and updated regularly.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Basic Configuration, basic configuration, Solution Manager</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Before you implement the central correction note, ensure the following:</p>\r\n<ul>\r\n<li>No inactive objects exist in your system.<br/>Tip: You can display a list of inactive objects in the object navigator (transaction SE80). To do this, select &quot;Inactive Objects&quot; from the dropdown menu and enter &quot;*&quot; in the user field.</li>\r\n</ul>\r\n<ul>\r\n<li>All available SAP Notes are updated or have been reset to their original state.<br/>For this, you navigate to the modification adjustment (transaction SPAU) and check the status of the SAP Notes. You can find further information about the modification adjustment in the documentation and in the SDN (for example here: http://scn.sap.com/docs/DOC-10312).</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>You should implement this SAP Note by default using the SAP Solution Manager configuration (SOLMAN_SETUP).<br/><br/>Adhere to the following procedure when you implement this SAP Note:</p>\r\n<ol>1. Implement SAP Note 875986 to ensure that you use the current version of the Note Assistant (transaction SNOTE).</ol><ol>2. Call the Note Assistant (transaction SNOTE) again.</ol><ol>3. When you implement an SAP Note, the system does not automatically take into account whether there are newer versions of required SAP Notes that have already been implemented in the SAP Support Portal.</ol>\r\n<p>              To ensure that all prerequisite SAP Notes are implemented in the current version, call the Note Assistant (transaction SNOTE), choose &quot;Goto -&gt; SAP Note Browser -&gt; Execute (F8)&quot;, and then choose &quot;Download Latest Version of SAP Notes&quot; in the application toolbar.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As a result, when you implement this SAP Note using the Note Assistant, the system checks whether the necessary SAP Notes have been fully implemented. If this is not the case, the Note Assistant implements the latest version.</p>\r\n<ol>4. Implement this central correction note.</ol>\r\n<p><strong>This note is divided into three sections:</strong></p>\r\n<p><br/>1) Corrections that can be implemented automatically using the<br/>    Note Assistant (transaction SNOTE), and which must be implemented.<br/>    SAP Notes that were already implemented in the correct version<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;remain untouched.<br/><br/>2) Corrections with manual steps<br/><br/>3) Additional information (FAQs, general notes)<br/><br/>Table legend:<br/><br/>    o  The &quot;Date&quot; column contains the day on which the listed SAP Note was included in this SAP Note, or when a relevant change was made.<br/><br/>    o  The &quot;Note&quot; column contains the number of the SAP Note. The individual notes are listed under &quot;Correction Instructions&quot; or &quot;Related Notes&quot;.<br/><br/>######################################################################<br/><br/>######################################################################<br/>1) Corrections that can be implemented automatically using the Note Assistant (transaction SNOTE), and which must be implemented.<br/>Notes that were already implemented in the correct version remain untouched.<br/>######################################################################<br/>Many corrections can be implemented automatically using transaction SNOTE.<br/>To make downloading and implementing easier, the corrections have been attached to this note.<br/>Implement this note using transaction SNOTE.<br/>After this note has been updated, you must implement it again using transaction SNOTE. When you do this, only the differences (delta) are implemented.<br/>The corrections from the following notes are included.<br/>The Note Assistant guarantees that the note is implemented correctly <br/>and that the Support Package level of the system taken into account.<br/><br/>If the system prompts you for a framework program, select any framework program.<br/><br/>This note has many other prerequisite notes. These notes change and activate a high number of objects. However, some of the objects may not be activated. Nevertheless, the notes have the status &quot;Completely implemented&quot; in transaction SNOTE.<br/>To ensure that all objects are activated, call transaction SA38, call the report SCWB_NOTE_ACTIVATE and enter note number 1468591. Execute the report. If there are inactive objects, the system displays them in a window from which you can activate them directly.<br/><br/>Software component ST 400:<br/>Date*       Note<br/>08.10.2010&#x00A0;&#x00A0;1507671<br/>12.11.1010&#x00A0;&#x00A0;1482211<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1519709<br/>11.02.2011&#x00A0;&#x00A0;1529251<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1529869<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1542831<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1544202<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1538950<br/>25.03.2011&#x00A0;&#x00A0;1550829<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1560988<br/>26.04.2011&#x00A0;&#x00A0;1565332<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1573809<br/>September 28, 2011  1603361  (SP27)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1604572<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1614015<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1614389<br/>December 1, 2011  1639681  (SP27)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1656963<br/>February 5, 2012  1498926  (SP25)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1565332<br/>            1567200  (SP25 + SP26)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1597516<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1601853<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1603790<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1604572<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1612285<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1614015<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1614389<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1621643<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1639681<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1644615<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1648230<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1663545<br/>July 12, 2012  1544202  (SP27)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1567200<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1597516<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1601853<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1603790<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1612285<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1621643<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1644147<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1644615<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1647911<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1648230<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1663545<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1666532<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1696061<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1722791<br/>December 6, 2012  1450580  (SP25 + SP26)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1644147<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1666532<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1696061<br/>            1729591  (SP25 + SP26 + SP27)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1734135<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1739631<br/>            1643865  (SP26)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1722791<br/>            1710170  (SP26 + SP27)<br/>27.08.2014</p>\r\n<p style=\"padding-left: 60px;\">1406440  (SP25 + SP26 + SP27)<br/>1820291<br/>1837889<br/>1849566</p>\r\n<p><br/><br/>Software component SAP Basis:<br/>Date*       SAP Note<br/>11.02.2011&#x00A0;&#x00A0;1540720<br/>25.03.2011&#x00A0;&#x00A0;1542016<br/>September 28, 2011  1576773  (SP27)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1583346<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1591053<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1592368<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1615861<br/>December 1, 2011  1602519  (SP27)<br/>March 5, 2012  1475974  (SP25 + SP26)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1526886<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1576773<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1583346<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1591053<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1592368<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1602519<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1615861<br/>July 12, 2012  1542016  (SP27)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1579696<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1694458<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1738511<br/>December 6, 2012  1694458  (SP25 + SP26)<br/>27.08.2014</p>\r\n<p style=\"padding-left: 60px;\">1629551  (SP25 + SP26 + SP27)<br/>1682750<br/>1763274<br/>1813468<br/>1885750</p>\r\n<p><br/>Software component SAP_ABA:<br/>Date*       SAP Note<br/>11.01.2011&#x00A0;&#x00A0;1536589<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1445221<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1541306<br/>25.03.2011&#x00A0;&#x00A0;1538765<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1546812<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1560168<br/>26.04.2011&#x00A0;&#x00A0;1548084<br/>September 28, 2011  1601249  (SP27)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1601515<br/>March 5, 2012  1601249  (SP25 + SP26)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1601515<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1629224<br/>July 12, 2012  1560168  (SP27)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1629224<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1698047<br/>December 6, 2012  1475278  (SP25 + SP26 + SP27)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1679697<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1743244<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1763793<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1774418</p>\r\n<p>August 27, 2014  1566634  (SP25 + SP26 + SP27)<br/><br/>Software component BBPCRM:<br/>Date*       SAP Note<br/>08.10.2010&#x00A0;&#x00A0;1438476<br/><br/>Software component SAP_BW:<br/>Date*       SAP Note<br/>September 28, 2011  1477732  (SP27)<br/>March 5, 2012  1477732  (SP25 + SP26)<br/><br/>Software component BI_Cont:<br/>Date*       SAP Note<br/>08.10.2010&#x00A0;&#x00A0;1510178<br/>11.02.2011&#x00A0;&#x00A0;1493494<br/>March 5, 2012  1604906  (SP25 + SP26)<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1618787<br/>July 12, 2012  1604906  (SP27)<br/><br/>Software component ST-BCO:<br/>Date*       SAP Note<br/>September 28, 2011  1618787  (SP27)<br/><br/>Software component ST-A/PI:<br/>Date*       SAP Note<br/>December 1, 2011  1601011  (SP27)<br/>March 5, 2012  1601011  (SP26)<br/>July 12, 2012  1559499  (SP27)<br/><br/>Software component ST-SER:<br/>March 5, 2012  1608457  (SP25 + SP26)<br/>July 12, 2012  1608457  (SP27)<br/><br/>Software component SAP_AP:<br/>December 6, 2012  1457391  (SP25 + SP26 + SP27)<br/><br/>######################################################################<br/>2) Corrections with manual steps<br/>######################################################################<br/>The notes listed are linked as &quot;Related Notes&quot;.<br/><br/>Determine the relevant section according to the Support Package level of your SAP Solution Manager system.<br/><br/>Go through the notes in the specified order (from top to bottom).<br/><br/>Table legend:<br/><br/>The column &quot;incl&quot; indicates notes with corrections that can be implemented automatically using Note 1468591.<br/>The &quot;auto&quot; column indicates notes for which the manual steps are executed as follows. <br/>Transaction: SOLMAN_SETUP -&gt; Basic configuration -&gt; Step 2 &quot;Import SAP Note&quot; -&gt; Execute Rework. Alternatively, after you have implemented Note 1334252, execute the function module AGS_SISE_MANU_NOTE_ACTS in transaction SE37. <br/><br/>The &quot;Cat&quot; column specifies the type of the note. Possible values:<br/>A = Mandatory; you must take this into account<br/>B = Optional, if required (for example, error message texts, analysis reports)<br/>C = Corrections; for data inconsistencies; manual start of reports<br/><br/>2.1) Regardless of Support Package level<br/><br/>Date*    SAP Note  incl  Cat Description (short)<br/>December 6, 2012  1546204              A  POWL - Category creation and deletion Issues<br/>            1783371              A  Maint.Opt: System landscape data<br/><br/>2.2) ST 400 Support Package 25<br/><br/>Date*       Note      incl  auto Cat Description (short)<br/>February 11, 2011  1540720   x          A  ICM parameters are not transferred<br/>            1542831   x         B  BP_GEN does not add a system user to role employee<br/>            1544202   x          A  Data missing in Incident Management BI Reporting<br/>            1538950  x          A   Random scheduling of background job<br/>March 5, 2012  1503456   no         A  POWL: Corrections related to lead sel<br/>            1560034   no        A  POWL: Records are not shown when<br/>            1604205   no         A  Corrections for unified rendering 701<br/>            (this note requires a lot of memory space; for this reason, we recommend<br/>             that you trigger the corrections separately and manually).<br/>            1604572   x         A  OSS performance problems due to solman_setup background jobs<br/>December 6, 2012  1582591              A  POWL : Alert list information doesn&#39;t refresh<br/>            1697052              A  WDA: Link in FormattedTextView leads<br/>            1710384              A  WD ABAP ALV performance improvement<br/>            1559499              B   DPC data providers as of ST-PI 2008_1_XX SP4<br/>            1734250              B  POWL: Error in detail area update<br/>            1748138              B  POWL: Unnecessary update of detail area<br/><br/>2.3) ST 400 Support Package 26<br/><br/>Date*       Note      incl  auto Cat Description (short)<br/>March 5, 2012  1503456   no         A  POWL: Corrections related to lead sel<br/>            1560034   no        A  POWL: Records are not shown when there is only one entry.<br/>            1604205   no         A  Corrections for unified rendering 701<br/>            (this note requires a lot of memory space; for this reason, we recommend<br/>             that you trigger the corrections separately and manually).<br/>            1604572   x         A  OSS performance problems due to solman_setup background jobs<br/>December 6, 2012  1582591              A  POWL : Alert list information does not refresh<br/>            1697052              A  WDA: Link in FormattedTextView leads<br/>            1710384              A  WD ABAP ALV performance improvement<br/>            1559499              B   DPC data providers as of ST-PI 2008_1_XX SP4<br/>            1734250              B  POWL: Error in detail area update<br/>            1741470              B  WD ABAP WD ABAP ALV error when context changes are dispatched<br/>            1748138              B  POWL: Unnecessary update of detail area<br/>            1550886              B  BPMon: Monitor Object and Key Figure not taken by AlertInbox<br/>            1558026              A  SP26: Central note for Service Delivery Work Center<br/>            1647911              A  Work Center: no action when confirming in preview<br/><br/>2.4) ST 400 Support Package 27<br/><br/>Date*       Note      incl  auto Cat Description (short)<br/>April 28, 2011  1604572   x          A  OSS performance problems due to SOLMA<br/>            1604205                 Corrections for unified rendering 701<br/>            (this note requires a lot of memory space; for this reason, we recommend<br/>             that you trigger the corrections separately and manually).<br/>July 12, 2012  1544202   x          A  Im Incident Management BI Reporting<br/>            1697052              A  WDA: Link in FormattedTextView leads<br/>            1710384              A  WD ABAP ALV performance improvement<br/>December 6, 2012  1582591              A  POWL : Alert list information does not refresh<br/>            1647911              A  Work Center: no action when confirming in preview<br/>            1560034              A  POWL: Records are not shown when there is only one entry.<br/>            1734250              B  POWL: Error in detail area update<br/>            1748138              B  POWL: Unnecessary update of detail area<br/><br/>######################################################################  3) Additional information (FAQs, general notes)<br/>13.08.09&#x00A0;&#x00A0;875986<br/>######################################################################<br/><br/>If applicable, implement the notes below in the managed systems.<br/><br/>09.04.10&#x00A0;&#x00A0;1355948<br/>05.03.12&#x00A0;&#x00A0;1615861<br/>12.07.12&#x00A0;&#x00A0;1559499<br/><br/><br/>The notes listed are linked as &quot;Related Notes&quot;.<br/><br/><br/><br/><br/></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D027512)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D047253)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001468591/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468591/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468591/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468591/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468591/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468591/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468591/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468591/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001468591/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "875986", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS up to 702", "RefUrl": "/notes/875986"}, {"RefNumber": "1885750", "RefComponent": "BC-CCM-BTC", "RefTitle": "Dump ITAB_ILLEGAL_SORT_ORDER in CL_SBAL_LOGGER", "RefUrl": "/notes/1885750"}, {"RefNumber": "1849566", "RefComponent": "SV-SMG-SVC", "RefTitle": "Users without authorization see the installations", "RefUrl": "/notes/1849566"}, {"RefNumber": "1837889", "RefComponent": "SV-SMG-SUP", "RefTitle": "VAR scenario: Unjustified deactivation of S users", "RefUrl": "/notes/1837889"}, {"RefNumber": "1820291", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Project Documentation tab flagged as changed in Template", "RefUrl": "/notes/1820291"}, {"RefNumber": "1813468", "RefComponent": "BC-WD-ABA", "RefTitle": "Web Dynpro: Conversion for non-printable chars (NON-UNICODE)", "RefUrl": "/notes/1813468"}, {"RefNumber": "1783371", "RefComponent": "SV-SMG-MAI", "RefTitle": "MOpz: System landscape data incomplete in Customer Profile", "RefUrl": "/notes/1783371"}, {"RefNumber": "1774418", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary refresh triggered when using query api", "RefUrl": "/notes/1774418"}, {"RefNumber": "1763793", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Column headers are rendered incorrectly", "RefUrl": "/notes/1763793"}, {"RefNumber": "1763274", "RefComponent": "BC-WD-ABA", "RefTitle": "Select options: Error when deleting a parameter", "RefUrl": "/notes/1763274"}, {"RefNumber": "1748138", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary update of detail area", "RefUrl": "/notes/1748138"}, {"RefNumber": "1743244", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Better description needed in query template dropdown", "RefUrl": "/notes/1743244"}, {"RefNumber": "1741470", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV error when context changes are dispatched", "RefUrl": "/notes/1741470"}, {"RefNumber": "1739631", "RefComponent": "SV-SMG-SVD", "RefTitle": "Service delivery session information not updated accordingly", "RefUrl": "/notes/1739631"}, {"RefNumber": "1738511", "RefComponent": "SV-SMG-IMP", "RefTitle": "Editing MS Office documents in SAP Solution Manager", "RefUrl": "/notes/1738511"}, {"RefNumber": "1734250", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Error in detail area update", "RefUrl": "/notes/1734250"}, {"RefNumber": "1734135", "RefComponent": "SV-SMG-SDG", "RefTitle": "Service Delivery Readiness status not calculated correctly", "RefUrl": "/notes/1734135"}, {"RefNumber": "1729591", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Error: No Long Name Found in SMD_HASH_TABLE for Hash-ID", "RefUrl": "/notes/1729591"}, {"RefNumber": "1722791", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Changes by Compare and Adjust on transaction tab are lost", "RefUrl": "/notes/1722791"}, {"RefNumber": "1710384", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV performance improvements", "RefUrl": "/notes/1710384"}, {"RefNumber": "1710170", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager Software Prerequisites", "RefUrl": "/notes/1710170"}, {"RefNumber": "1698047", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Reset of service sessions not possible due to failure", "RefUrl": "/notes/1698047"}, {"RefNumber": "1697052", "RefComponent": "BC-WD-ABA", "RefTitle": "WDA: Link in FormattedTextView causes a dump", "RefUrl": "/notes/1697052"}, {"RefNumber": "1696061", "RefComponent": "SV-SMG-SVD", "RefTitle": "Performance Improvement for Service Sessions in DSWP", "RefUrl": "/notes/1696061"}, {"RefNumber": "1694458", "RefComponent": "BC-CUS-TOL-PAD", "RefTitle": "SOLAR_PROJECT_ADMIN:Select Countries not Cleared/Refreshed", "RefUrl": "/notes/1694458"}, {"RefNumber": "1682750", "RefComponent": "BC-WD-ABA", "RefTitle": "FormattedTextView: Unexpected characters (such as #)", "RefUrl": "/notes/1682750"}, {"RefNumber": "1679697", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Selection criteria ignored during POWL rendering", "RefUrl": "/notes/1679697"}, {"RefNumber": "1666532", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Duration evaluation displays incorrect values", "RefUrl": "/notes/1666532"}, {"RefNumber": "1663545", "RefComponent": "SV-SMG-SUP", "RefTitle": "Correction in module DSWP_GET_BUSTRANS_USAGE", "RefUrl": "/notes/1663545"}, {"RefNumber": "1656963", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Commit command for logging the sending of alerts to SAP", "RefUrl": "/notes/1656963"}, {"RefNumber": "1648230", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: Character ',' disappears in long text", "RefUrl": "/notes/1648230"}, {"RefNumber": "1647911", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: no action when confirming in preview area", "RefUrl": "/notes/1647911"}, {"RefNumber": "1644615", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: time Changed At in watch list is not correct", "RefUrl": "/notes/1644615"}, {"RefNumber": "1644147", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: CRM order lock is lost in edit mode", "RefUrl": "/notes/1644147"}, {"RefNumber": "1643865", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_SDK_SP_AUTO_CLOSE_2 - Status is not set", "RefUrl": "/notes/1643865"}, {"RefNumber": "1639681", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert For ABAP System Not Sent To SAP", "RefUrl": "/notes/1639681"}, {"RefNumber": "1629551", "RefComponent": "BC-WD-ABA", "RefTitle": "Business Graphics: Dump due to missing resources", "RefUrl": "/notes/1629551"}, {"RefNumber": "1629224", "RefComponent": "CA-NO", "RefTitle": "RNOTIFPUDATE01: Customer messages not updated", "RefUrl": "/notes/1629224"}, {"RefNumber": "1621643", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: No authority check when changing to edit mode", "RefUrl": "/notes/1621643"}, {"RefNumber": "1618787", "RefComponent": "BW-BCT-CMS", "RefTitle": "Generic Dataloader:aggregation of hash based data is wrong", "RefUrl": "/notes/1618787"}, {"RefNumber": "1615861", "RefComponent": "BC-MID-RFC", "RefTitle": "Trust relationship cannot be created using transaction SMSY", "RefUrl": "/notes/1615861"}, {"RefNumber": "1614389", "RefComponent": "SV-SMG-BPR", "RefTitle": "Business function contents of BPR are not updated", "RefUrl": "/notes/1614389"}, {"RefNumber": "1614015", "RefComponent": "SV-SMG-IMP", "RefTitle": "Business functions deleted when importing Support Packages", "RefUrl": "/notes/1614015"}, {"RefNumber": "1612285", "RefComponent": "SV-SMG-SUP", "RefTitle": "Time records are lost after canceling processor change", "RefUrl": "/notes/1612285"}, {"RefNumber": "1608457", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert Session Not Sent To SAP", "RefUrl": "/notes/1608457"}, {"RefNumber": "1604906", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Generic Dataloader dumps; buffer overflow", "RefUrl": "/notes/1604906"}, {"RefNumber": "1604572", "RefComponent": "SV-SMG-INS", "RefTitle": "OSS performance problems due to solman_setup background jobs", "RefUrl": "/notes/1604572"}, {"RefNumber": "1604205", "RefComponent": "BC-WD-ABA", "RefTitle": "Corrections for unified rendering 701/10 V (UR mimes)", "RefUrl": "/notes/1604205"}, {"RefNumber": "1603790", "RefComponent": "SV-SMG-SUP", "RefTitle": "Cannot find BP for system user when creating a message", "RefUrl": "/notes/1603790"}, {"RefNumber": "1603361", "RefComponent": "SV-SMG-DIA-SRV-CHK", "RefTitle": "Solution Manager EhP1 SP27 prerequisite check update", "RefUrl": "/notes/1603361"}, {"RefNumber": "1602519", "RefComponent": "BC-ABA-TO", "RefTitle": "FREE_SELECTIONS_RANGE_2_WHERE: Problem w/ special characters", "RefUrl": "/notes/1602519"}, {"RefNumber": "1601853", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work center: locks remain after switching to display mode", "RefUrl": "/notes/1601853"}, {"RefNumber": "1601515", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Incorrect decimal values in tables after save", "RefUrl": "/notes/1601515"}, {"RefNumber": "1601249", "RefComponent": "CA-NO", "RefTitle": "RNOTIFUPDATE07: No creation of notifications", "RefUrl": "/notes/1601249"}, {"RefNumber": "1601011", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service prep. for specific upcoming services ST-A/PI 01N*", "RefUrl": "/notes/1601011"}, {"RefNumber": "1597516", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: ERROR_MESSAGE_STATE when creating a new message", "RefUrl": "/notes/1597516"}, {"RefNumber": "1592368", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD: Dump GETWA_NOT_ASSIGNED", "RefUrl": "/notes/1592368"}, {"RefNumber": "1591053", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV: Dump due to incorrect ToolBar configuration", "RefUrl": "/notes/1591053"}, {"RefNumber": "1583346", "RefComponent": "BC-CTS-ORG-PLS", "RefTitle": "\"Illegal access to a string\" error in CTS+ browser", "RefUrl": "/notes/1583346"}, {"RefNumber": "1582591", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL : Alert list information doesn't refresh", "RefUrl": "/notes/1582591"}, {"RefNumber": "1579696", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Dump when canceling attribute dialog for a document", "RefUrl": "/notes/1579696"}, {"RefNumber": "1576773", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Test case with name \"<Name not found>\"", "RefUrl": "/notes/1576773"}, {"RefNumber": "1573809", "RefComponent": "SV-SMG-DIA-SRV-CHK", "RefTitle": "Prerequisite check fail for ST-A/PI", "RefUrl": "/notes/1573809"}, {"RefNumber": "1567200", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "BPMon: Service Desk Message related error", "RefUrl": "/notes/1567200"}, {"RefNumber": "1566634", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Missing check names in the message list", "RefUrl": "/notes/1566634"}, {"RefNumber": "1565332", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "BPMon: show solutions and processes with active monitoring", "RefUrl": "/notes/1565332"}, {"RefNumber": "1560988", "RefComponent": "SV-SMG-SYS", "RefTitle": "Errors when creating a solution via workcenter", "RefUrl": "/notes/1560988"}, {"RefNumber": "1560168", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Text contents lost on saving", "RefUrl": "/notes/1560168"}, {"RefNumber": "1560034", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL : Records are not shown when only one entry", "RefUrl": "/notes/1560034"}, {"RefNumber": "1559499", "RefComponent": "SV-SMG-MON-SYS", "RefTitle": "DPC data providers as of ST-PI 2008_1_XX SP4", "RefUrl": "/notes/1559499"}, {"RefNumber": "1558026", "RefComponent": "SV-SMG-SVD", "RefTitle": "SP26 Central note for Service Delivery Work Center", "RefUrl": "/notes/1558026"}, {"RefNumber": "1550886", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "BPMon: Monitor Object and Key Figure not taken by AlertInbox", "RefUrl": "/notes/1550886"}, {"RefNumber": "1550829", "RefComponent": "SV-SMG-SVD", "RefTitle": "SD: Session status not refreshed after view switch", "RefUrl": "/notes/1550829"}, {"RefNumber": "1548084", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Closed sessions are displayed", "RefUrl": "/notes/1548084"}, {"RefNumber": "1546812", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL - Dump when stardard query is made inactive", "RefUrl": "/notes/1546812"}, {"RefNumber": "1546204", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL - Category creation and deletion Issues", "RefUrl": "/notes/1546204"}, {"RefNumber": "1544202", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Data missing in Incident Management BI Reporting", "RefUrl": "/notes/1544202"}, {"RefNumber": "1542831", "RefComponent": "SV-SMG-SUP", "RefTitle": "BP_GEN does not add a system user to role employee", "RefUrl": "/notes/1542831"}, {"RefNumber": "1542016", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "Project authorization required in STWB_WORK", "RefUrl": "/notes/1542016"}, {"RefNumber": "1541306", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL - Detail Component Is not updated Properly", "RefUrl": "/notes/1541306"}, {"RefNumber": "1540720", "RefComponent": "BC-CCM-SLD", "RefTitle": "ICM parameters are not transferred by RZ70", "RefUrl": "/notes/1540720"}, {"RefNumber": "1538950", "RefComponent": "SV-SMG-INS", "RefTitle": "Random scheduling of background jobs in SOLMAN_SETUP", "RefUrl": "/notes/1538950"}, {"RefNumber": "1538765", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL ->Add interface method to get detail comp", "RefUrl": "/notes/1538765"}, {"RefNumber": "1536589", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: selection getting disappeared after clicking Apply btn", "RefUrl": "/notes/1536589"}, {"RefNumber": "1529869", "RefComponent": "SV-SMG-INS", "RefTitle": "SOLMAN_SETUP: dump on click on 'Save' button", "RefUrl": "/notes/1529869"}, {"RefNumber": "1529251", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert ALERTS are not sent to SAP", "RefUrl": "/notes/1529251"}, {"RefNumber": "1526886", "RefComponent": "SV-SMG-IMP", "RefTitle": "Document links on attribute dialog are not displayed", "RefUrl": "/notes/1526886"}, {"RefNumber": "1519709", "RefComponent": "SV-SMG-SVD", "RefTitle": "SD: Invisible session link in Japanese log-on", "RefUrl": "/notes/1519709"}, {"RefNumber": "1510178", "RefComponent": "SV-SMG-DIA", "RefTitle": "Infocubes remain in loading mode after Housekeeping timeout", "RefUrl": "/notes/1510178"}, {"RefNumber": "1507671", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: Installed product versions are overwritten", "RefUrl": "/notes/1507671"}, {"RefNumber": "1503456", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Corrections related to lead selection", "RefUrl": "/notes/1503456"}, {"RefNumber": "1498926", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: Relevant ABAP product instances from the SLD", "RefUrl": "/notes/1498926"}, {"RefNumber": "1493494", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "Generic Dataloader missing data in BW System", "RefUrl": "/notes/1493494"}, {"RefNumber": "1482211", "RefComponent": "SV-SMG-SDG", "RefTitle": "Issue could not be created: MISSING_PARAMETER", "RefUrl": "/notes/1482211"}, {"RefNumber": "1477732", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Characteristic filter set by variable exit not refreshed", "RefUrl": "/notes/1477732"}, {"RefNumber": "1475974", "RefComponent": "BC-WD-ABA", "RefTitle": "WD ABAP: HotKeys in nested views", "RefUrl": "/notes/1475974"}, {"RefNumber": "1475278", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Winword report cover page in wrong language", "RefUrl": "/notes/1475278"}, {"RefNumber": "1457391", "RefComponent": "AP-MD-IBA", "RefTitle": "Bad Performance while accessing table IBST.", "RefUrl": "/notes/1457391"}, {"RefNumber": "1450580", "RefComponent": "SV-SMG-SUP", "RefTitle": "SOLMAN_WORKCENTER: Dump CREATE_OBJECT_CLASS_NOT_FOUND", "RefUrl": "/notes/1450580"}, {"RefNumber": "1445221", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL Initial Lead Selection Update", "RefUrl": "/notes/1445221"}, {"RefNumber": "1438476", "RefComponent": "CRM-MD-INB", "RefTitle": "Partner inheritance for partner determination failing", "RefUrl": "/notes/1438476"}, {"RefNumber": "1435043", "RefComponent": "SV-SMG-DIA-APP-WA", "RefTitle": "E2E Workload Analysis - No applicable data found", "RefUrl": "/notes/1435043"}, {"RefNumber": "1406440", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Collective Corrections for EFWK Mainextractor (ST400)", "RefUrl": "/notes/1406440"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1710170", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP Solution Manager Software Prerequisites", "RefUrl": "/notes/1710170 "}, {"RefNumber": "1558026", "RefComponent": "SV-SMG-SVD", "RefTitle": "SP26 Central note for Service Delivery Work Center", "RefUrl": "/notes/1558026 "}, {"RefNumber": "1550829", "RefComponent": "SV-SMG-SVD", "RefTitle": "SD: Session status not refreshed after view switch", "RefUrl": "/notes/1550829 "}, {"RefNumber": "1739631", "RefComponent": "SV-SMG-SVD", "RefTitle": "Service delivery session information not updated accordingly", "RefUrl": "/notes/1739631 "}, {"RefNumber": "1546812", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL - Dump when stardard query is made inactive", "RefUrl": "/notes/1546812 "}, {"RefNumber": "1679697", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Selection criteria ignored during POWL rendering", "RefUrl": "/notes/1679697 "}, {"RefNumber": "1738511", "RefComponent": "SV-SMG-IMP", "RefTitle": "Editing MS Office documents in SAP Solution Manager", "RefUrl": "/notes/1738511 "}, {"RefNumber": "875986", "RefComponent": "BC-UPG-NA", "RefTitle": "Note Assistant: Important notes for SAP_BASIS up to 702", "RefUrl": "/notes/875986 "}, {"RefNumber": "1567200", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "BPMon: Service Desk Message related error", "RefUrl": "/notes/1567200 "}, {"RefNumber": "1519709", "RefComponent": "SV-SMG-SVD", "RefTitle": "SD: Invisible session link in Japanese log-on", "RefUrl": "/notes/1519709 "}, {"RefNumber": "1546204", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL - Category creation and deletion Issues", "RefUrl": "/notes/1546204 "}, {"RefNumber": "1743244", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Better description needed in query template dropdown", "RefUrl": "/notes/1743244 "}, {"RefNumber": "1435043", "RefComponent": "SV-SMG-DIA-APP-WA", "RefTitle": "E2E Workload Analysis - No applicable data found", "RefUrl": "/notes/1435043 "}, {"RefNumber": "1477732", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Characteristic filter set by variable exit not refreshed", "RefUrl": "/notes/1477732 "}, {"RefNumber": "1559499", "RefComponent": "SV-SMG-MON-SYS", "RefTitle": "DPC data providers as of ST-PI 2008_1_XX SP4", "RefUrl": "/notes/1559499 "}, {"RefNumber": "1783371", "RefComponent": "SV-SMG-MAI", "RefTitle": "MOpz: System landscape data incomplete in Customer Profile", "RefUrl": "/notes/1783371 "}, {"RefNumber": "1615861", "RefComponent": "BC-MID-RFC", "RefTitle": "Trust relationship cannot be created using transaction SMSY", "RefUrl": "/notes/1615861 "}, {"RefNumber": "1457391", "RefComponent": "AP-MD-IBA", "RefTitle": "Bad Performance while accessing table IBST.", "RefUrl": "/notes/1457391 "}, {"RefNumber": "1774418", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary refresh triggered when using query api", "RefUrl": "/notes/1774418 "}, {"RefNumber": "1763793", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Column headers are rendered incorrectly", "RefUrl": "/notes/1763793 "}, {"RefNumber": "1734250", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Error in detail area update", "RefUrl": "/notes/1734250 "}, {"RefNumber": "1748138", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Unnecessary update of detail area", "RefUrl": "/notes/1748138 "}, {"RefNumber": "1503456", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: Corrections related to lead selection", "RefUrl": "/notes/1503456 "}, {"RefNumber": "1741470", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV error when context changes are dispatched", "RefUrl": "/notes/1741470 "}, {"RefNumber": "1734135", "RefComponent": "SV-SMG-SDG", "RefTitle": "Service Delivery Readiness status not calculated correctly", "RefUrl": "/notes/1734135 "}, {"RefNumber": "1538765", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL ->Add interface method to get detail comp", "RefUrl": "/notes/1538765 "}, {"RefNumber": "1541306", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL - Detail Component Is not updated Properly", "RefUrl": "/notes/1541306 "}, {"RefNumber": "1536589", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL: selection getting disappeared after clicking Apply btn", "RefUrl": "/notes/1536589 "}, {"RefNumber": "1445221", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL Initial Lead Selection Update", "RefUrl": "/notes/1445221 "}, {"RefNumber": "1560034", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL : Records are not shown when only one entry", "RefUrl": "/notes/1560034 "}, {"RefNumber": "1582591", "RefComponent": "BC-MUS-POW", "RefTitle": "POWL : Alert list information doesn't refresh", "RefUrl": "/notes/1582591 "}, {"RefNumber": "1694458", "RefComponent": "BC-CUS-TOL-PAD", "RefTitle": "SOLAR_PROJECT_ADMIN:Select Countries not Cleared/Refreshed", "RefUrl": "/notes/1694458 "}, {"RefNumber": "1710384", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV performance improvements", "RefUrl": "/notes/1710384 "}, {"RefNumber": "1729591", "RefComponent": "SV-SMG-DIA-SRV-EFW", "RefTitle": "Error: No Long Name Found in SMD_HASH_TABLE for Hash-ID", "RefUrl": "/notes/1729591 "}, {"RefNumber": "1696061", "RefComponent": "SV-SMG-SVD", "RefTitle": "Performance Improvement for Service Sessions in DSWP", "RefUrl": "/notes/1696061 "}, {"RefNumber": "1722791", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Changes by Compare and Adjust on transaction tab are lost", "RefUrl": "/notes/1722791 "}, {"RefNumber": "1450580", "RefComponent": "SV-SMG-SUP", "RefTitle": "SOLMAN_WORKCENTER: Dump CREATE_OBJECT_CLASS_NOT_FOUND", "RefUrl": "/notes/1450580 "}, {"RefNumber": "1698047", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Reset of service sessions not possible due to failure", "RefUrl": "/notes/1698047 "}, {"RefNumber": "1697052", "RefComponent": "BC-WD-ABA", "RefTitle": "WDA: Link in FormattedTextView causes a dump", "RefUrl": "/notes/1697052 "}, {"RefNumber": "1644147", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: CRM order lock is lost in edit mode", "RefUrl": "/notes/1644147 "}, {"RefNumber": "1602519", "RefComponent": "BC-ABA-TO", "RefTitle": "FREE_SELECTIONS_RANGE_2_WHERE: Problem w/ special characters", "RefUrl": "/notes/1602519 "}, {"RefNumber": "1666532", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Duration evaluation displays incorrect values", "RefUrl": "/notes/1666532 "}, {"RefNumber": "1663545", "RefComponent": "SV-SMG-SUP", "RefTitle": "Correction in module DSWP_GET_BUSTRANS_USAGE", "RefUrl": "/notes/1663545 "}, {"RefNumber": "1644615", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: time Changed At in watch list is not correct", "RefUrl": "/notes/1644615 "}, {"RefNumber": "1639681", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert For ABAP System Not Sent To SAP", "RefUrl": "/notes/1639681 "}, {"RefNumber": "1648230", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: Character ',' disappears in long text", "RefUrl": "/notes/1648230 "}, {"RefNumber": "1656963", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Commit command for logging the sending of alerts to SAP", "RefUrl": "/notes/1656963 "}, {"RefNumber": "1629224", "RefComponent": "CA-NO", "RefTitle": "RNOTIFPUDATE01: Customer messages not updated", "RefUrl": "/notes/1629224 "}, {"RefNumber": "1647911", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: no action when confirming in preview area", "RefUrl": "/notes/1647911 "}, {"RefNumber": "1608457", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert Session Not Sent To SAP", "RefUrl": "/notes/1608457 "}, {"RefNumber": "1498926", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: Relevant ABAP product instances from the SLD", "RefUrl": "/notes/1498926 "}, {"RefNumber": "1565332", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "BPMon: show solutions and processes with active monitoring", "RefUrl": "/notes/1565332 "}, {"RefNumber": "1643865", "RefComponent": "SV-SMG-SUP", "RefTitle": "AI_SDK_SP_AUTO_CLOSE_2 - Status is not set", "RefUrl": "/notes/1643865 "}, {"RefNumber": "1621643", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: No authority check when changing to edit mode", "RefUrl": "/notes/1621643 "}, {"RefNumber": "1612285", "RefComponent": "SV-SMG-SUP", "RefTitle": "Time records are lost after canceling processor change", "RefUrl": "/notes/1612285 "}, {"RefNumber": "1591053", "RefComponent": "BC-WD-CMP-ALV-ABA", "RefTitle": "WD ABAP ALV: Dump due to incorrect ToolBar configuration", "RefUrl": "/notes/1591053 "}, {"RefNumber": "1618787", "RefComponent": "BW-BCT-CMS", "RefTitle": "Generic Dataloader:aggregation of hash based data is wrong", "RefUrl": "/notes/1618787 "}, {"RefNumber": "1604572", "RefComponent": "SV-SMG-INS", "RefTitle": "OSS performance problems due to solman_setup background jobs", "RefUrl": "/notes/1604572 "}, {"RefNumber": "1614015", "RefComponent": "SV-SMG-IMP", "RefTitle": "Business functions deleted when importing Support Packages", "RefUrl": "/notes/1614015 "}, {"RefNumber": "1614389", "RefComponent": "SV-SMG-BPR", "RefTitle": "Business function contents of BPR are not updated", "RefUrl": "/notes/1614389 "}, {"RefNumber": "1592368", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD: Dump GETWA_NOT_ASSIGNED", "RefUrl": "/notes/1592368 "}, {"RefNumber": "1482211", "RefComponent": "SV-SMG-SDG", "RefTitle": "Issue could not be created: MISSING_PARAMETER", "RefUrl": "/notes/1482211 "}, {"RefNumber": "1597516", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work Center: ERROR_MESSAGE_STATE when creating a new message", "RefUrl": "/notes/1597516 "}, {"RefNumber": "1601515", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Incorrect decimal values in tables after save", "RefUrl": "/notes/1601515 "}, {"RefNumber": "1603790", "RefComponent": "SV-SMG-SUP", "RefTitle": "Cannot find BP for system user when creating a message", "RefUrl": "/notes/1603790 "}, {"RefNumber": "1601853", "RefComponent": "SV-SMG-SUP", "RefTitle": "Work center: locks remain after switching to display mode", "RefUrl": "/notes/1601853 "}, {"RefNumber": "1604906", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Generic Dataloader dumps; buffer overflow", "RefUrl": "/notes/1604906 "}, {"RefNumber": "1604205", "RefComponent": "BC-WD-ABA", "RefTitle": "Corrections for unified rendering 701/10 V (UR mimes)", "RefUrl": "/notes/1604205 "}, {"RefNumber": "1583346", "RefComponent": "BC-CTS-ORG-PLS", "RefTitle": "\"Illegal access to a string\" error in CTS+ browser", "RefUrl": "/notes/1583346 "}, {"RefNumber": "1603361", "RefComponent": "SV-SMG-DIA-SRV-CHK", "RefTitle": "Solution Manager EhP1 SP27 prerequisite check update", "RefUrl": "/notes/1603361 "}, {"RefNumber": "1601249", "RefComponent": "CA-NO", "RefTitle": "RNOTIFUPDATE07: No creation of notifications", "RefUrl": "/notes/1601249 "}, {"RefNumber": "1601011", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service prep. for specific upcoming services ST-A/PI 01N*", "RefUrl": "/notes/1601011 "}, {"RefNumber": "1550886", "RefComponent": "SV-SMG-MON-BPM", "RefTitle": "BPMon: Monitor Object and Key Figure not taken by AlertInbox", "RefUrl": "/notes/1550886 "}, {"RefNumber": "1544202", "RefComponent": "SV-SMG-SUP-REP", "RefTitle": "Data missing in Incident Management BI Reporting", "RefUrl": "/notes/1544202 "}, {"RefNumber": "1573809", "RefComponent": "SV-SMG-DIA-SRV-CHK", "RefTitle": "Prerequisite check fail for ST-A/PI", "RefUrl": "/notes/1573809 "}, {"RefNumber": "1579696", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Dump when canceling attribute dialog for a document", "RefUrl": "/notes/1579696 "}, {"RefNumber": "1576773", "RefComponent": "SV-SMG-IMP-BIM", "RefTitle": "Test case with name \"<Name not found>\"", "RefUrl": "/notes/1576773 "}, {"RefNumber": "1507671", "RefComponent": "SV-SMG-SYS", "RefTitle": "SMSY: Installed product versions are overwritten", "RefUrl": "/notes/1507671 "}, {"RefNumber": "1560988", "RefComponent": "SV-SMG-SYS", "RefTitle": "Errors when creating a solution via workcenter", "RefUrl": "/notes/1560988 "}, {"RefNumber": "1529869", "RefComponent": "SV-SMG-INS", "RefTitle": "SOLMAN_SETUP: dump on click on 'Save' button", "RefUrl": "/notes/1529869 "}, {"RefNumber": "1540720", "RefComponent": "BC-CCM-SLD", "RefTitle": "ICM parameters are not transferred by RZ70", "RefUrl": "/notes/1540720 "}, {"RefNumber": "1560168", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Text contents lost on saving", "RefUrl": "/notes/1560168 "}, {"RefNumber": "1548084", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Closed sessions are displayed", "RefUrl": "/notes/1548084 "}, {"RefNumber": "1526886", "RefComponent": "SV-SMG-IMP", "RefTitle": "Document links on attribute dialog are not displayed", "RefUrl": "/notes/1526886 "}, {"RefNumber": "1542016", "RefComponent": "SV-SMG-TWB-PLN", "RefTitle": "Project authorization required in STWB_WORK", "RefUrl": "/notes/1542016 "}, {"RefNumber": "1538950", "RefComponent": "SV-SMG-INS", "RefTitle": "Random scheduling of background jobs in SOLMAN_SETUP", "RefUrl": "/notes/1538950 "}, {"RefNumber": "1542831", "RefComponent": "SV-SMG-SUP", "RefTitle": "BP_GEN does not add a system user to role employee", "RefUrl": "/notes/1542831 "}, {"RefNumber": "1493494", "RefComponent": "SV-SMG-MON-EEM", "RefTitle": "Generic Dataloader missing data in BW System", "RefUrl": "/notes/1493494 "}, {"RefNumber": "1529251", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert ALERTS are not sent to SAP", "RefUrl": "/notes/1529251 "}, {"RefNumber": "1475974", "RefComponent": "BC-WD-ABA", "RefTitle": "WD ABAP: HotKeys in nested views", "RefUrl": "/notes/1475974 "}, {"RefNumber": "1510178", "RefComponent": "SV-SMG-DIA", "RefTitle": "Infocubes remain in loading mode after Housekeeping timeout", "RefUrl": "/notes/1510178 "}, {"RefNumber": "1438476", "RefComponent": "CRM-MD-INB", "RefTitle": "Partner inheritance for partner determination failing", "RefUrl": "/notes/1438476 "}, {"RefNumber": "1475278", "RefComponent": "SV-SMG-SVD-SWB", "RefTitle": "Winword report cover page in wrong language", "RefUrl": "/notes/1475278 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST", "From": "400", "To": "400", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST 400", "SupportPackage": "SAPKITL439", "URL": "/supportpackage/SAPKITL439"}, {"SoftwareComponentVersion": "ST 400", "SupportPackage": "SAPKITL440", "URL": "/supportpackage/SAPKITL440"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST", "NumberOfCorrin": 3, "URL": "/corrins/0001468591/162"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 82, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BBPCRM", "ValidFrom": "500", "ValidTo": "500", "Number": "1438476 ", "URL": "/notes/1438476 ", "Title": "Partner inheritance for partner determination failing", "Component": "CRM-MD-INB"}, {"SoftwareComponent": "BI_CONT", "ValidFrom": "704", "ValidTo": "704", "Number": "1493494 ", "URL": "/notes/1493494 ", "Title": "Generic Dataloader missing data in BW System", "Component": "SV-SMG-MON-EEM"}, {"SoftwareComponent": "BI_CONT", "ValidFrom": "704", "ValidTo": "704", "Number": "1510178 ", "URL": "/notes/1510178 ", "Title": "Infocubes remain in loading mode after Housekeeping timeout", "Component": "SV-SMG-DIA"}, {"SoftwareComponent": "BI_CONT", "ValidFrom": "704", "ValidTo": "704", "Number": "1604906 ", "URL": "/notes/1604906 ", "Title": "Generic Dataloader dumps; buffer overflow", "Component": "SV-SMG-SUP-REP"}, {"SoftwareComponent": "BI_CONT", "ValidFrom": "704", "ValidTo": "704", "Number": "1618787 ", "URL": "/notes/1618787 ", "Title": "Generic Dataloader:aggregation of hash based data is wrong", "Component": "BW-BCT-CMS"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "701", "Number": "1743244 ", "URL": "/notes/1743244 ", "Title": "POWL: Better description needed in query template dropdown", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "702", "Number": "1763793 ", "URL": "/notes/1763793 ", "Title": "POWL: Column headers are rendered incorrectly", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "700", "ValidTo": "702", "Number": "1774418 ", "URL": "/notes/1774418 ", "Title": "POWL: Unnecessary refresh triggered when using query api", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "701", "Number": "1475278 ", "URL": "/notes/1475278 ", "Title": "Winword report cover page in wrong language", "Component": "SV-SMG-SVD-SWB"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "701", "Number": "1546812 ", "URL": "/notes/1546812 ", "Title": "POWL - Dump when stardard query is made inactive", "Component": "BC-MUS-POW"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "701", "Number": "1548084 ", "URL": "/notes/1548084 ", "Title": "Closed sessions are displayed", "Component": "SV-SMG-SVD-SWB"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "701", "Number": "1560168 ", "URL": "/notes/1560168 ", "Title": "Text contents lost on saving", "Component": "SV-SMG-SVD-SWB"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "701", "Number": "1566634 ", "URL": "/notes/1566634 ", "Title": "Missing check names in the message list", "Component": "SV-SMG-SVD-SWB"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "701", "Number": "1601249 ", "URL": "/notes/1601249 ", "Title": "RNOTIFUPDATE07: No creation of notifications", "Component": "CA-NO"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "701", "Number": "1601515 ", "URL": "/notes/1601515 ", "Title": "Incorrect decimal values in tables after save", "Component": "SV-SMG-SVD-SWB"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "701", "Number": "1629224 ", "URL": "/notes/1629224 ", "Title": "RNOTIFPUDATE01: Customer messages not updated", "Component": "CA-NO"}, {"SoftwareComponent": "SAP_ABA", "ValidFrom": "701", "ValidTo": "701", "Number": "1698047 ", "URL": "/notes/1698047 ", "Title": "Reset of service sessions not possible due to failure", "Component": "SV-SMG-SVD-SWB"}, {"SoftwareComponent": "SAP_AP", "ValidFrom": "700", "ValidTo": "700", "Number": "1457391 ", "URL": "/notes/1457391 ", "Title": "Bad Performance while accessing table IBST.", "Component": "AP-MD-IBA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "1540720 ", "URL": "/notes/1540720 ", "Title": "ICM parameters are not transferred by RZ70", "Component": "BC-CCM-SLD"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "1579696 ", "URL": "/notes/1579696 ", "Title": "Dump when canceling attribute dialog for a document", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "702", "Number": "1738511 ", "URL": "/notes/1738511 ", "Title": "Editing MS Office documents in SAP Solution Manager", "Component": "SV-SMG-IMP"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "730", "Number": "1576773 ", "URL": "/notes/1576773 ", "Title": "Test case with name \"<Name not found>\"", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "731", "Number": "1602519 ", "URL": "/notes/1602519 ", "Title": "FREE_SELECTIONS_RANGE_2_WHERE: Problem w/ special characters", "Component": "BC-ABA-TO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "740", "Number": "1885750 ", "URL": "/notes/1885750 ", "Title": "Dump ITAB_ILLEGAL_SORT_ORDER in CL_SBAL_LOGGER", "Component": "BC-CCM-BTC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1475974 ", "URL": "/notes/1475974 ", "Title": "WD ABAP: HotKeys in nested views", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1526886 ", "URL": "/notes/1526886 ", "Title": "Document links on attribute dialog are not displayed", "Component": "SV-SMG-IMP"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1542016 ", "URL": "/notes/1542016 ", "Title": "Project authorization required in STWB_WORK", "Component": "SV-SMG-TWB-PLN"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1583346 ", "URL": "/notes/1583346 ", "Title": "\"Illegal access to a string\" error in CTS+ browser", "Component": "BC-CTS-ORG-PLS"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1591053 ", "URL": "/notes/1591053 ", "Title": "WD ABAP ALV: Dump due to incorrect ToolBar configuration", "Component": "BC-WD-CMP-ALV-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1592368 ", "URL": "/notes/1592368 ", "Title": "CD: Dump GETWA_NOT_ASSIGNED", "Component": "BC-SRV-ASF-CHD"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1615861 ", "URL": "/notes/1615861 ", "Title": "Trust relationship cannot be created using transaction SMSY", "Component": "BC-MID-RFC"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1629551 ", "URL": "/notes/1629551 ", "Title": "Business Graphics: Dump due to missing resources", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1682750 ", "URL": "/notes/1682750 ", "Title": "FormattedTextView: Unexpected characters (such as &#xd;)", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1694458 ", "URL": "/notes/1694458 ", "Title": "SOLAR_PROJECT_ADMIN:Select Countries not Cleared/Refreshed", "Component": "BC-CUS-TOL-PAD"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1741470 ", "URL": "/notes/1741470 ", "Title": "WD ABAP ALV error when context changes are dispatched", "Component": "BC-WD-CMP-ALV-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1763274 ", "URL": "/notes/1763274 ", "Title": "Select options: Error when deleting a parameter", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1813468 ", "URL": "/notes/1813468 ", "Title": "Web Dynpro: Conversion for non-printable chars (NON-UNICODE)", "Component": "BC-WD-ABA"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "700", "ValidTo": "702", "Number": "1477732 ", "URL": "/notes/1477732 ", "Title": "Characteristic filter set by variable exit not refreshed", "Component": "BW-BEX-ET-WEB"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1406440 ", "URL": "/notes/1406440 ", "Title": "Collective Corrections for EFWK Mainextractor (ST400)", "Component": "SV-SMG-DIA-SRV-EFW"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1450580 ", "URL": "/notes/1450580 ", "Title": "SOLMAN_WORKCENTER: Dump CREATE_OBJECT_CLASS_NOT_FOUND", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1498926 ", "URL": "/notes/1498926 ", "Title": "SMSY: Relevant ABAP product instances from the SLD", "Component": "SV-SMG-SYS"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1507671 ", "URL": "/notes/1507671 ", "Title": "SMSY: Installed product versions are overwritten", "Component": "SV-SMG-SYS"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1519709 ", "URL": "/notes/1519709 ", "Title": "SD: Invisible session link in Japanese log-on", "Component": "SV-SMG-SVD"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1529251 ", "URL": "/notes/1529251 ", "Title": "EarlyWatch Alert ALERTS are not sent to SAP", "Component": "SV-SMG-OP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1529869 ", "URL": "/notes/1529869 ", "Title": "SOLMAN_SETUP: dump on click on 'Save' button", "Component": "SV-SMG-INS"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1538950 ", "URL": "/notes/1538950 ", "Title": "Random scheduling of background jobs in SOLMAN_SETUP", "Component": "SV-SMG-INS"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1542831 ", "URL": "/notes/1542831 ", "Title": "BP_GEN does not add a system user to role employee", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1544202 ", "URL": "/notes/1544202 ", "Title": "Data missing in Incident Management BI Reporting", "Component": "SV-SMG-SUP-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1550829 ", "URL": "/notes/1550829 ", "Title": "SD: Session status not refreshed after view switch", "Component": "SV-SMG-SVD"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1560988 ", "URL": "/notes/1560988 ", "Title": "Errors when creating a solution via workcenter", "Component": "SV-SMG-SYS"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1565332 ", "URL": "/notes/1565332 ", "Title": "BPMon: show solutions and processes with active monitoring", "Component": "SV-SMG-MON-BPM"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1567200 ", "URL": "/notes/1567200 ", "Title": "BPMon: Service Desk Message related error", "Component": "SV-SMG-MON-BPM"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1573809 ", "URL": "/notes/1573809 ", "Title": "Prerequisite check fail for ST-A/PI", "Component": "SV-SMG-DIA-SRV-CHK"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1597516 ", "URL": "/notes/1597516 ", "Title": "Work Center: ERROR_MESSAGE_STATE when creating a new message", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1601853 ", "URL": "/notes/1601853 ", "Title": "Work center: locks remain after switching to display mode", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1603361 ", "URL": "/notes/1603361 ", "Title": "Solution Manager EhP1 SP27 prerequisite check update", "Component": "SV-SMG-DIA-SRV-CHK"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1603790 ", "URL": "/notes/1603790 ", "Title": "Cannot find BP for system user when creating a message", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1604572 ", "URL": "/notes/1604572 ", "Title": "OSS performance problems due to solman_setup background jobs", "Component": "SV-SMG-INS"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1612285 ", "URL": "/notes/1612285 ", "Title": "Time records are lost after canceling processor change", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1614015 ", "URL": "/notes/1614015 ", "Title": "Business functions deleted when importing Support Packages", "Component": "SV-SMG-IMP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1614389 ", "URL": "/notes/1614389 ", "Title": "Business function contents of BPR are not updated", "Component": "SV-SMG-BPR"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1621643 ", "URL": "/notes/1621643 ", "Title": "Work Center: No authority check when changing to edit mode", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1639681 ", "URL": "/notes/1639681 ", "Title": "EarlyWatch Alert For ABAP System Not Sent To SAP", "Component": "SV-SMG-OP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1643865 ", "URL": "/notes/1643865 ", "Title": "AI_SDK_SP_AUTO_CLOSE_2 - Status is not set", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1644147 ", "URL": "/notes/1644147 ", "Title": "Work Center: CRM order lock is lost in edit mode", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1644615 ", "URL": "/notes/1644615 ", "Title": "Work Center: time Changed At in watch list is not correct", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1648230 ", "URL": "/notes/1648230 ", "Title": "Work Center: Character ',' disappears in long text", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1656963 ", "URL": "/notes/1656963 ", "Title": "Commit command for logging the sending of alerts to SAP", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1663545 ", "URL": "/notes/1663545 ", "Title": "Correction in module DSWP_GET_BUSTRANS_USAGE", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1666532 ", "URL": "/notes/1666532 ", "Title": "Duration evaluation displays incorrect values", "Component": "SV-SMG-SUP-REP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1710170 ", "URL": "/notes/1710170 ", "Title": "SAP Solution Manager Software Prerequisites", "Component": "SV-SMG-INS-CFG"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1729591 ", "URL": "/notes/1729591 ", "Title": "Error: No Long Name Found in SMD_HASH_TABLE for Hash-ID", "Component": "SV-SMG-DIA-SRV-EFW"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1734135 ", "URL": "/notes/1734135 ", "Title": "Service Delivery Readiness status not calculated correctly", "Component": "SV-SMG-SDG"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1739631 ", "URL": "/notes/1739631 ", "Title": "Service delivery session information not updated accordingly", "Component": "SV-SMG-SVD"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1820291 ", "URL": "/notes/1820291 ", "Title": "Project Documentation tab flagged as changed in Template", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1837889 ", "URL": "/notes/1837889 ", "Title": "VAR scenario: Unjustified deactivation of S users", "Component": "SV-SMG-SUP"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "400", "Number": "1849566 ", "URL": "/notes/1849566 ", "Title": "Users without authorization see the installations", "Component": "SV-SMG-SVC"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "710", "Number": "1696061 ", "URL": "/notes/1696061 ", "Title": "Performance Improvement for Service Sessions in DSWP", "Component": "SV-SMG-SVD"}, {"SoftwareComponent": "ST", "ValidFrom": "400", "ValidTo": "710", "Number": "1722791 ", "URL": "/notes/1722791 ", "Title": "Changes by Compare and Adjust on transaction tab are lost", "Component": "SV-SMG-IMP-BIM"}, {"SoftwareComponent": "ST-A/PI", "ValidFrom": "01N_40B_R3", "ValidTo": "01N_710BCO", "Number": "1601011 ", "URL": "/notes/1601011 ", "Title": "Service prep. for specific upcoming services ST-A/PI 01N*", "Component": "SV-SMG-SDD"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "400", "ValidTo": "400", "Number": "1604906 ", "URL": "/notes/1604906 ", "Title": "Generic Dataloader dumps; buffer overflow", "Component": "SV-SMG-SUP-REP"}, {"SoftwareComponent": "ST-BCO", "ValidFrom": "400", "ValidTo": "400", "Number": "1618787 ", "URL": "/notes/1618787 ", "Title": "Generic Dataloader:aggregation of hash based data is wrong", "Component": "BW-BCT-CMS"}, {"SoftwareComponent": "ST-PI", "ValidFrom": "2008_1_620", "ValidTo": "2008_1_710", "Number": "1559499 ", "URL": "/notes/1559499 ", "Title": "DPC data providers as of ST-PI 2008_1_XX SP4", "Component": "SV-SMG-MON-SYS"}, {"SoftwareComponent": "ST-SER", "ValidFrom": "701_2010_1", "ValidTo": "701_2010_1", "Number": "1608457 ", "URL": "/notes/1608457 ", "Title": "EarlyWatch Alert Session Not Sent To SAP", "Component": "SV-SMG-OP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}