{"Request": {"Number": "834343", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 349, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015876302017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000834343?language=E&token=4C0BA401346EFEE52A226910EEB6AA28"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000834343", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000834343/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "834343"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.02.2008"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "834343 - Oracle 9.2 Platform Support for Async IO"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Slow IO Performance</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>async io, aio, performance, direct io, concurrent io, RAC, cluster filesystem</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />For database performance reasons it is highly recommended to use all three optimizations of async io, direct io and concurrent io together where available. The optimal database IO performance can only be achieved if all optimizations are used.<br /><br />In general FILESYSTEMIO_OPTIONS = SETALL is recommended, for details, please see the table below.<br /><br />Note: this note is valid for single instance databases only.<br /><br /><U>Platform / Filesystem&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Async IO&#x00A0;&#x00A0;&#x00A0;&#x00A0;Direct IO&#x00A0;&#x00A0;&#x00A0;&#x00A0; Concurrent IO</U><br /><br />Raw (1,2)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br />NetApp NFS (2,11,12)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No<br />HP-UX 11.x JFS (3)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; No&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No<br />HP Tru64 5.1x Advfs (4)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br />HP Tru64 5.1x CFS (4)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br />IBM AIX 5L JFS&#x00A0;&#x00A0;(5)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No<br />IBM AIX 5L JFS2 (5)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br />IBM AIX 5L GPFS (6)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No<br />Linux SLES8/9,RHEL3/4 EXT3 (2)&#x00A0;&#x00A0; Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No<br />Linux SLES8/9 ReiserFS (2,10)&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No<br />Linux SLES9,RHEL4 OCFS2&#x00A0;&#x00A0;(2,8)&#x00A0;&#x00A0; Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br />Solaris 8,9,10 UFS (4)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br />Solaris 9,10 QFS (4,7)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br />Veritas VxFS with Quick IO&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br />Windows 2003 NTFS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br />Windows 2003 OCFS (9)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Yes<br /><br />(1) HP-UX:<br /> configure async driver and rebuild HP-UX kernel:<br />  /sbin/mknod /dev/async&#x00A0;&#x00A0;c 101 0x0<br />  chown oracle:dba&#x00A0;&#x00A0;/dev/async<br /> <br />(2) Linux<br /> Steps a) and b) are only needed for Oracle 9.2.0.x (x&lt;= 6)<br /> except for Oracle Release 9.2.0.6 on platform Linux x86_64.<br /> a) apply Oracle bug fixes 2448994 and 3208258<br /> b) rebuild Oracle kernel<br /> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;make -f ins_rdbms.mk async_on<br /> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;make -f ins_rdbms.mk ioracle<br /> c) specify init.ora parameter:<br /> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;filesystemio_options=setall<br /> d) SuSE SLES9 only:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;for SP1 install rpms libaio-0.3.102-1.2 and<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;libaio-devel-0.3.102-1.2 (from SuSE support webpage)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Service Pack x (x&gt;=2)<br /> <br />(3) Direct IO Support needs to be enabled through filesystem<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;mount options mincache=direct,convosync=direct.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Never use these mount options for the filesystem with Oracle<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;binaries(i.e. Oracle_Home location).<br /> <br />(4) Make sure that the init.ora parameter<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;filesystemio_options=setall is specified.<br /> <br />(5) Direct IO and Concurrent IO optimizations need to be enabled by<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;using the filesystem mount option.&#x00A0;&#x00A0;(mount -o dio or mount -o cio).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;The setting of filesystemio_options parameter is ignored in 9.2<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;on the AIX platform.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Concurrent IO automatically enables Direct IO.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Never use the cio or dio mount option for the filesystem<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;with Oracle binaries (i.e. Oracle_Home location).<br /><br />(6) RAC only: GPFS is the cluster filesystem of IBM<br /><br />(7) RAC only: QFS is the cluster filesystem of SUN.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Minimum version is QFS 4.3. The following mount options need to<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;be specified in /etc/opt/SUNWsamfs/samfs.cmd for filesystems<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;holding database, redo log and control files:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; - mh_write<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; - qwrite<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; - forcedirectio<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note:&#x00A0;&#x00A0;NEVER use above mount options for QFS filesystems holding<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; binaries (i.e. Oracle_Home location)<br /><br />(8) RAC only: OCFS is the cluster filesystem of Oracle for Linux<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;platforms. Minimum OCFS version required is OCFS2 1.2.0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(available on http://www.ocfs.org)<br /><br />(9) RAC only: OCFS is the cluster filesystem of Oracle for Windows<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;platforms. Minimum OCFS version required is 9.2.0.7<br /><br />(10) In order to use both Async_io and Direct_io with ReiserFS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; you need to use the mount option \"-o notail\"<br /><br />(11) Oracle only supports database files on NetApp NFS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; (NEVER put database files on STANDARD NFS)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; The following required mount options are only valid<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; for database files, never use them for Oracle binaries (i.e. &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; Oracle_Home location).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; All platforms require the following mount options when using<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; NetApp NFS filesystems for database files:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;rw,bg,hard,nointr,proto=tcp,vers=3,rsize=32768,wsize=32768<br />Below are additional platform specific required mount options:<br />&#x00A0;&#x00A0;LINUX:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;timeo=600<br />...SOLARIS:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;forcedirectio<br />&#x00A0;&#x00A0; AIX (5.3 and higher):&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;timeo=600,cio<br />&#x00A0;&#x00A0; AIX (5.2 and lower):&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; timeo=600<br />&#x00A0;&#x00A0; HP-UX (11.23 and higher):&#x00A0;&#x00A0;timeo=600,forcedirectio<br /><br />(12) To use async io for Linux with NetApp NFS the following<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; prerequisites need to be met:<br />  - RHEL4 Update 3 + bugzilla fix 161362<br />  - RHEL4 Update 4<br /> &#x00A0;&#x00A0;&#x00A0;&#x00A0;- SLES9 SP3<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; With SLES8 you cannot combine async io and direct io<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; when using NetApp NFS.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; You have to set filesystemio_options=directIO<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; when using SLES8.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5004095)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5000979)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000834343/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000834343/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000834343/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000834343/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000834343/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000834343/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000834343/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000834343/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000834343/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "936441", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle settings for R3load based system copy", "RefUrl": "/notes/936441"}, {"RefNumber": "797084", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 9: Installation notes", "RefUrl": "/notes/797084"}, {"RefNumber": "793113", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle I/O configuration", "RefUrl": "/notes/793113"}, {"RefNumber": "758989", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor performance with TRUNCATEs", "RefUrl": "/notes/758989"}, {"RefNumber": "695841", "RefComponent": "BC-DB-ORA", "RefTitle": "TRUNCATE TABLE can take a long time", "RefUrl": "/notes/695841"}, {"RefNumber": "632556", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle 9.2.0.* database parameterization for BW", "RefUrl": "/notes/632556"}, {"RefNumber": "632427", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle 8.1.7* database parameterization for BW", "RefUrl": "/notes/632427"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "567745", "RefComponent": "BW", "RefTitle": "Composite note BW 3.x performance: DB-specific setting", "RefUrl": "/notes/567745"}, {"RefNumber": "124361", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle parameterization (R/3 >= 4.x, Oracle 8.x/9.x)", "RefUrl": "/notes/124361"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "793113", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle I/O configuration", "RefUrl": "/notes/793113 "}, {"RefNumber": "936441", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle settings for R3load based system copy", "RefUrl": "/notes/936441 "}, {"RefNumber": "797084", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 9: Installation notes", "RefUrl": "/notes/797084 "}, {"RefNumber": "124361", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle parameterization (R/3 >= 4.x, Oracle 8.x/9.x)", "RefUrl": "/notes/124361 "}, {"RefNumber": "632427", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle 8.1.7* database parameterization for BW", "RefUrl": "/notes/632427 "}, {"RefNumber": "632556", "RefComponent": "BW-SYS-DB", "RefTitle": "Oracle 9.2.0.* database parameterization for BW", "RefUrl": "/notes/632556 "}, {"RefNumber": "567745", "RefComponent": "BW", "RefTitle": "Composite note BW 3.x performance: DB-specific setting", "RefUrl": "/notes/567745 "}, {"RefNumber": "758989", "RefComponent": "BC-DB-ORA", "RefTitle": "Poor performance with TRUNCATEs", "RefUrl": "/notes/758989 "}, {"RefNumber": "695841", "RefComponent": "BC-DB-ORA", "RefTitle": "TRUNCATE TABLE can take a long time", "RefUrl": "/notes/695841 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}