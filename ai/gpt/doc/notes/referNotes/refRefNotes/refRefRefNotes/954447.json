{"Request": {"Number": "954447", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 3212, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005619232017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000954447?language=E&token=80DC0F302A73AACA74EF2EB4C13CBEF9"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000954447", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000954447/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "954447"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with High Priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.06.2006"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AT-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "XX-CSC-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-AT-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-AT-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "954447 - IS-H AT: ELDA - Current Adjustments (P321, ELDAL)"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=954447&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/954447/D\" target=\"_blank\">/notes/954447/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Austria (AT).<br />During the Austrian data exchange with social insurance companies (EDI procedures P321 and ELDAL), enhancements or corrections.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>EDI, P321, ELDAL, insurance verification, VZE</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Advance development</p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Before you implement the source code corrections, you must perform the following manual tasks:<br /><br />First change the following function module:</p> <UL><LI>Call transaction SE37.</LI></UL> <UL><LI>In the &quot;Function Module&quot; input field, enter the name of the function module ISH_AT_KUE_ADJUST and choose &quot;Change&quot;.</LI></UL> <UL><LI>Choose the &quot;Import&quot; tab page.</LI></UL> <UL><LI>In the first new row, enter the following values:</LI></UL> <UL><UL><LI>Parameter Name: SS_COMMIT</LI></UL></UL> <UL><UL><LI>Typing: TYPE</LI></UL></UL> <UL><UL><LI>Associated Type: ISH_ON_OFF</LI></UL></UL> <UL><UL><LI>Default value: &#39;X&#39;</LI></UL></UL> <UL><UL><LI>Optional: Select the checkbox.</LI></UL></UL> <UL><UL><LI>Pass Value: Do not select checkbox</LI></UL></UL> <UL><UL><LI>Short text: Commit (if not test run)</LI></UL></UL> <UL><LI>In the second new row, enter the following values:</LI></UL> <UL><UL><LI>Parameter Name: SS_ONLY_OPEN_INSERT</LI></UL></UL> <UL><UL><LI>Typing: TYPE</LI></UL></UL> <UL><UL><LI>Associated Type: ISH_ON_OFF</LI></UL></UL> <UL><UL><LI>Default value: &#39; &#39;</LI></UL></UL> <UL><UL><LI>Optional: Select the checkbox.</LI></UL></UL> <UL><UL><LI>Pass Value: Do not select checkbox</LI></UL></UL> <UL><UL><LI>Short text: Only create new, open applications</LI></UL></UL> <UL><LI>Save the function module.</LI></UL> <UL><LI>Activate the function module.</LI></UL> <p><br />If you are using Release 4.63B, you must now change the following type group manually:</p> <UL><LI>Call transaction SE80.</LI></UL> <UL><LI>Click on &quot;Edit Object&quot; button in the application toolbar.</LI></UL> <UL><LI>Go to the Dictionary tab.</LI></UL> <UL><LI>Select &quot;Type Group&quot; and enter the value NWATE in the input field. Choose &quot;Change&quot;.</LI></UL> <UL><LI>Position at the end of the line<br />nwate_co_landkz_iso_a2_uk type t005-intca   value &#39;UK&#39;,<br />and use the Enter key to insert a blank line.</LI></UL> <UL><LI>Enter the following text in this blank line:<br />nwate_co_landkz_unknown   type t005-intca   value &#39;YY&#39;,</LI></UL> <UL><LI>Save and activate these changes.</LI></UL> <p><br />Now implement the attached attachment as follows (note the sequence):</p> <OL>1. Unpack the attached file HW954447_600.zip for IS-H Version 600, HW954447_472.zip for IS-H Version 472 or HW954447_463B.zip for IS-H Version 463B.</OL> <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>2. Import the unpacked requests into your system.</OL> <p><br />Implement the source code corrections.<br /><br />The following enhancements and corrections are now available:</p> <UL><LI>Insurance Verification Reminder: The new report IS-H: AT Insurance Verification Reminder (RNWATKOSMA) is available. You can use this report to dun insurance verification for inpatient and outpatient cases. For more information, see the report documentation. The report RN1KOSMA has been locked. Transaction NWATKUMA now refers to the report RNWATKOSMA.</LI></UL> <UL><LI>EDI Evidence List: The new report IS-H AT: Data Exchange Evidence List (RNWAT_EDI_LIST) is available. The report is used as a tool for monitoring data exchange (EDI procedure P321, ELDAL). For more information, see the report documentation.</LI></UL> <UL><LI>State &quot;Unknown&quot; (field STAAT, DGLKZ, PALKZ, USLKZ): If one of the state fields is not filled, the system reports the state key &quot;YY&quot; (unknown).</LI></UL> <UL><LI>State &quot;Stateless&quot; (field STAAT, DGLKZ, PALKZ, USLKZ): You can define the attribute &quot;Stateless&quot; as a valid value (state) in Customizing (table T005 - field LANDK = &quot;XX&quot;).</LI></UL> <UL><LI>Automatic postprocessing (report IS-H EDI: Message Receipt, Automatic Postprocessing (RNC301I1)): If the report still cannot import the message correctly three days after the creation of a message, the system automatically sets the status of the message from &quot;Automatic&quot; to &quot;To Be Processed Manually&quot;.</LI></UL> <UL><LI>Import file (procedure P321): If you want to send a confirmation or &quot;Other&quot; VZE, if a rejection exists, the system reverses the rejection and automatically updates the current VZE.</LI></UL> <UL><LI>Import file (procedure P321): If a cancellation of the insurance verification (message type K04) is followed directly by a new insurance verification, the system imports the cancellation with the status &quot;OK&quot; (no manual processing required).</LI></UL> <UL><LI>Import file (procedure P321): If a message is accompanied by a comment, the system assigns the same status to both messages.</LI></UL> <UL><LI>Identification part - Check for social insurance number: In the case of date conflicts of each type, the system reports &quot;0000&quot; + date of birth in the SI Number field. Up to now, the system has only applied this solution if the check digit is incorrect.</LI></UL> <UL><LI>EDI procedure ELDAL: To create an ELDAL message (service notification), the system waits until the corresponding admission display (case/insurance provider/quarter) is available. Previously, the system created the construction progress report with the status &quot;With Errors&quot;.</LI></UL> <UL><LI>EDI procedure ELDAL: If a diagnosis occurs more than once, the system only reports this once.</LI></UL> <UL><LI>EDI procedure ELDAL: For manual events (triggered by report RNWATELDALU0) (without comparison), the existence of a confirmation of IV is also a prerequisite for sending the messages.</LI></UL> <UL><LI>Report IS-H AT: Manual Creation of Dispatch Order for Outpatient Admission Notification (RNWATELDAU1), IS-H AT: Manual Creation of Dispatch Order for Outpatient Service Data (RNWATELDALU0): The Visit Date field is now mandatory. The period entered must not exceed the quarterly limits.</LI></UL> <UL><LI>All EDI reports: Nursing and departmental OUs are now available on the selection screen and on the output list. In addition, you can now enter a large number of individual cases in the selection without restriction.</LI></UL> <p><br />Note that you must also implement SAP Note 955556 for the complete function. This SAP Note deals with the implicit change of reference messages when changing the status in the EDI workbench. For technical reasons, a separate SAP Note had to be created here.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON><PERSON> (C5008711)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000954447/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "HW954447_463B.zip", "FileSize": "136", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000242072006&iv_version=0004&iv_guid=0F29ED31DDE30141884C585DD29B0381"}, {"FileName": "HW954447_600.zip", "FileSize": "135", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000242072006&iv_version=0004&iv_guid=DFB0CAADCC06E841A3520FBB7507F09B"}, {"FileName": "HW954447_472.zip", "FileSize": "136", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000242072006&iv_version=0004&iv_guid=0C5DB0F49CADCB4F905B416C8529F6CF"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "997653", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Standard Implementation EDIVKA 5.0 (Delta)", "RefUrl": "/notes/997653"}, {"RefNumber": "955556", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: ELDA - Current Adjustments (RNCEDIWORK2)", "RefUrl": "/notes/955556"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "997653", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: Standard Implementation EDIVKA 5.0 (Delta)", "RefUrl": "/notes/997653 "}, {"RefNumber": "955556", "RefComponent": "XX-CSC-AT-IS-H", "RefTitle": "IS-H AT: ELDA - Current Adjustments (RNCEDIWORK2)", "RefUrl": "/notes/955556 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "463B", "To": "463B", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 463B", "SupportPackage": "SAPKIPHD32", "URL": "/supportpackage/SAPKIPHD32"}, {"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF16", "URL": "/supportpackage/SAPKIPHF16"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60006INISH", "URL": "/supportpackage/SAPK-60006INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 3, "URL": "/corrins/0000954447/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 105, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "548089 ", "URL": "/notes/548089 ", "Title": "IS-H AT: ELDAA - BBA Notification for Companions/Newborns", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "553889 ", "URL": "/notes/553889 ", "Title": "IS-H AT: ELDA - Alphanum. Inst. No. for Cost Object Assignment", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "576431 ", "URL": "/notes/576431 ", "Title": "IS-H AT: BBA Notification - Legal Changes", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "576521 ", "URL": "/notes/576521 ", "Title": "IS-H AT: ELDA - Search for Canceled Reversed Admission Unl", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "577583 ", "URL": "/notes/577583 ", "Title": "IS-H AT: ELDAA - Missing Institute Number for BBAS", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "580744 ", "URL": "/notes/580744 ", "Title": "IS-H AT: ELDA(A) - Remaining NC301 Entries with HOLDDT", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "583814 ", "URL": "/notes/583814 ", "Title": "IS-H AT: SCORING - Determination of Insurance Provider to Be Reported", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "595966 ", "URL": "/notes/595966 ", "Title": "IS-H AT: ELDA - Manual Message Order Creation", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "597395 ", "URL": "/notes/597395 ", "Title": "IS-H AT: ELDA(A) - Performance Improvements", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "600180 ", "URL": "/notes/600180 ", "Title": "IS-H AT: Lock Problem - Work Processes Blocked", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "611228 ", "URL": "/notes/611228 ", "Title": "IS-H AT: Insurance Verification - RNWAT_KUE_ADJUST", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "617808 ", "URL": "/notes/617808 ", "Title": "IS-H AT: ELDA - Record Sequence for Cancellation", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "627348 ", "URL": "/notes/627348 ", "Title": "IS-H AT: Insurance Verification - RNWAT_KUE_ADJUST (2)", "Component": "IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "631054 ", "URL": "/notes/631054 ", "Title": "IS-H AT: ELDA - Extension Display", "Component": "IS-H-CM-OUT"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "635917 ", "URL": "/notes/635917 ", "Title": "IS-H AT: Insurance Verification - Check Partially Billed", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "636247 ", "URL": "/notes/636247 ", "Title": "IS-H AT: ELDA - Day Clinic", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "641669 ", "URL": "/notes/641669 ", "Title": "IS-H AT: ELDA - Patient Master Data Changes", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "658402 ", "URL": "/notes/658402 ", "Title": "IS-H AT: Insurance Verification - Self-Payer Indicator (in NKIP)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "663947 ", "URL": "/notes/663947 ", "Title": "IS-H AT: Insurance Verification - Social Insurance Recognition", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "673825 ", "URL": "/notes/673825 ", "Title": "IS-H AT: ELDA - Admission Notification for Change of Insurance Provider", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "686076 ", "URL": "/notes/686076 ", "Title": "IS-H AT: Insurance Verification - Performance (RNWAT_KUE_ADJUST)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "686917 ", "URL": "/notes/686917 ", "Title": "IS-H AT: RN1KOSMA - catch dump for empty selection table", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "694534 ", "URL": "/notes/694534 ", "Title": "IS-H AT: ELDA - Insurance Verification and Current VV Date Limits", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "715568 ", "URL": "/notes/715568 ", "Title": "IS-H AT: ELDA - Title Conversion", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "716827 ", "URL": "/notes/716827 ", "Title": "IS-H AT: ELDA - Determination of Class", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "748956 ", "URL": "/notes/748956 ", "Title": "IS-H AT: Insurance Verification Reminder RN1KOSMA", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "749176 ", "URL": "/notes/749176 ", "Title": "IS-H AT: ELDA - Calculation of Previous Treatment Days", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "753527 ", "URL": "/notes/753527 ", "Title": "IS-H AT: ELDA - IV Requests for Several IRs", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "757853 ", "URL": "/notes/757853 ", "Title": "IS-H AT: ELDA - Insurance Subgroup for IV Import", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "768823 ", "URL": "/notes/768823 ", "Title": "IS-H AT: ELDA - Extension Display", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "776215 ", "URL": "/notes/776215 ", "Title": "IS-H AT: ELDA - Institution Indicator (Field KANKZ)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "786907 ", "URL": "/notes/786907 ", "Title": "IS-H AT: ELDA: Import Insurance Verification - Differences", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "788229 ", "URL": "/notes/788229 ", "Title": "IS-H AT: Scoring - Current Adjustments", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "789039 ", "URL": "/notes/789039 ", "Title": "IS-H AT: ELDA - Update Termination Patient Merge", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "789300 ", "URL": "/notes/789300 ", "Title": "IS-H AT: ELDA - Discharge Notification Field ENTS via TNCMAPP", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "792097 ", "URL": "/notes/792097 ", "Title": "IS-H AT: Insurance Verification - Performance IV Document List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "801331 ", "URL": "/notes/801331 ", "Title": "IS-H AT: Scoring - Adjustments or Bug fixes", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "807225 ", "URL": "/notes/807225 ", "Title": "IS-H AT: ELDA - End of EHIC Data Record", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "809569 ", "URL": "/notes/809569 ", "Title": "IS-H AT: RN1KOSMA - Determine Reminder Deadline", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "811846 ", "URL": "/notes/811846 ", "Title": "IS-H AT: ELDA/SCORE Changes to Settings", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "821244 ", "URL": "/notes/821244 ", "Title": "IS-H AT: ELDA - Import Insurance Verification", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "829574 ", "URL": "/notes/829574 ", "Title": "IS-H AT: ELDA - Checks for EEA Patients UK/GB", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "842007 ", "URL": "/notes/842007 ", "Title": "IS-H AT: ELDA Outpatient Admission notification or Construction Progress Report", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "844919 ", "URL": "/notes/844919 ", "Title": "IS-H AT: Error in Insurance Verification Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "848914 ", "URL": "/notes/848914 ", "Title": "IS-H AT: EDI Scoring - Check for System Information", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "855450 ", "URL": "/notes/855450 ", "Title": "IS-H AT: ELDA - Incorrect EHIC Record Cannot Be Imported", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "857550 ", "URL": "/notes/857550 ", "Title": "IS-H AT: ELDA - Use of BAdI Methods, BETREU/URS", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "858293 ", "URL": "/notes/858293 ", "Title": "IS-H AT: Data Exchange - Care Certificate, Cause of Treatment", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "859390 ", "URL": "/notes/859390 ", "Title": "IS-H AT: Data Exchange - Convert Title with TNCMAPP", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "859391 ", "URL": "/notes/859391 ", "Title": "IS-H AT: ELDA - DB Lock RNC30100 (NRIV)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "859789 ", "URL": "/notes/859789 ", "Title": "IS-H AT: Data Exchange - Control User-Defined Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "860185 ", "URL": "/notes/860185 ", "Title": "IS-H AT: Data Exchange - Outpatient Admission Activate Ins. Rel.", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "861548 ", "URL": "/notes/861548 ", "Title": "IS-H AT: Data Exchange - Error Corrections (VVP,LDA)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "861759 ", "URL": "/notes/861759 ", "Title": "IS-H AT: Insurance relationships are not saved", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "861881 ", "URL": "/notes/861881 ", "Title": "IS-H AT: Data Exchange - Describe First Names with Umlauts", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "862327 ", "URL": "/notes/862327 ", "Title": "IS-H AT: Data Exchange - Event Report Visit Date To", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "863402 ", "URL": "/notes/863402 ", "Title": "IS-H AT: ELDA - Remaining, Incorrect STO_AUFN", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "864116 ", "URL": "/notes/864116 ", "Title": "IS-H AT: ELDA - Missing Cancellations for Case Type Change", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "865892 ", "URL": "/notes/865892 ", "Title": "IS-H AT: Data Exchange - Event Report DB Lock NRIV", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "866111 ", "URL": "/notes/866111 ", "Title": "IS-H AT: ELDA - Outpatient IV Groups", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "867866 ", "URL": "/notes/867866 ", "Title": "IS-H AT: Data Exchange - S/T/U Diagnoses for ENTL", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "870398 ", "URL": "/notes/870398 ", "Title": "IS-H AT: Data Exchange - IV Request Check Outpatient", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "870555 ", "URL": "/notes/870555 ", "Title": "IS-H AT: Data Exchange - ELDAL Without Free Text Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "871435 ", "URL": "/notes/871435 ", "Title": "IS-H AT: Data Exchange - No Outpatient Movement Before July 1, 2005", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "872143 ", "URL": "/notes/872143 ", "Title": "IS-H AT: ELDA - Cancel Outpatient Admission Notification After Confirmation", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "872907 ", "URL": "/notes/872907 ", "Title": "IS-H AT: ELDAL - R<PERSON><PERSON><PERSON>LDALU0 Submitted RNWATELDAU1", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "879247 ", "URL": "/notes/879247 ", "Title": "IS-H AT: Data Exchange - P321 IV Multiple IR for COC", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "879321 ", "URL": "/notes/879321 ", "Title": "IS-H AT: Insurance Verification - Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "879950 ", "URL": "/notes/879950 ", "Title": "IS-H AT: Data Exchange - STATE Only Conditional Required Field", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "880952 ", "URL": "/notes/880952 ", "Title": "IS-H AT: Data Exchange - Outpatient Manual Events", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "884505 ", "URL": "/notes/884505 ", "Title": "IS-H AT: ELDAL - Cancel Outpatient Performance Data", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "885083 ", "URL": "/notes/885083 ", "Title": "IS-H AT: Previous Patient Days in EDI Based on Service Rule AK", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "885516 ", "URL": "/notes/885516 ", "Title": "IS-H AT: Data Exchange - VZE Without Start Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "885633 ", "URL": "/notes/885633 ", "Title": "IS-H AT: Insurance Verification Evidence List for Rejections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "887107 ", "URL": "/notes/887107 ", "Title": "IS-H AT: Data Exchange - Outpatient Visit in the future", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "889299 ", "URL": "/notes/889299 ", "Title": "IS-H AT: Data Exchange - ELDAL Receipt of Error Messages", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "902717 ", "URL": "/notes/902717 ", "Title": "IS-H AT: Data Exchange - Ignore P321 F13 and F14", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "904414 ", "URL": "/notes/904414 ", "Title": "IS-H AT: Data Exchange - P321 AUF/ENT-STO Without Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "909690 ", "URL": "/notes/909690 ", "Title": "IS-H AT: Determine Care Certificate Using TNWAT_BETR", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "909988 ", "URL": "/notes/909988 ", "Title": "IS-H AT: ELDA - Insurance Verification Overlapping IV Requests", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "911049 ", "URL": "/notes/911049 ", "Title": "IS-H AT: Add \\&quot;Without IR/SI\\&quot; to Report RNWATKUELIST", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "914170 ", "URL": "/notes/914170 ", "Title": "IS-H AT: ELDA - Required Field Checks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "917405 ", "URL": "/notes/917405 ", "Title": "IS-H AT: Scoring - Check Discharge Date for NPFO Cases", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "917463 ", "URL": "/notes/917463 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) by RNC301I0", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "918052 ", "URL": "/notes/918052 ", "Title": "IS-H AT: Data Exchange P321 - Extension Display", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "918382 ", "URL": "/notes/918382 ", "Title": "IS-H AT: Data Exchange P321 - Disp. Adm. Despite Final Billing", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "918521 ", "URL": "/notes/918521 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) - NV2001 Locks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "921499 ", "URL": "/notes/921499 ", "Title": "IS-H AT: ELDAL - Insurance Provider Responsible for Service (D.5)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "921827 ", "URL": "/notes/921827 ", "Title": "IS-H AT: Report RNWATKUELIST - No IV Display Without IR", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "925087 ", "URL": "/notes/925087 ", "Title": "IS-H AT: ELDA - Rejection Reason for Outpatient Insurance Verification", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "926716 ", "URL": "/notes/926716 ", "Title": "IS-H AT: ELDA - NRIV Lock, ELDAL - First Visit Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "927795 ", "URL": "/notes/927795 ", "Title": "IS-H AT: Data Exchange - Event Processing via Reports", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "928673 ", "URL": "/notes/928673 ", "Title": "IS-H AT: Data Exchange - Change of Health/Illness Newborn", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "936603 ", "URL": "/notes/936603 ", "Title": "IS-H AT: Data Exchange - Various Corrections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "939868 ", "URL": "/notes/939868 ", "Title": "IS-H AT: Report RNWATKUELIST - Selection of Rejection Reasons", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "940988 ", "URL": "/notes/940988 ", "Title": "IS-H AT: Data Exchange - Lock During Import of IP", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "943845 ", "URL": "/notes/943845 ", "Title": "IS-H AT: ELDA - Current Adjustments", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "748956 ", "URL": "/notes/748956 ", "Title": "IS-H AT: Insurance Verification Reminder RN1KOSMA", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "749176 ", "URL": "/notes/749176 ", "Title": "IS-H AT: ELDA - Calculation of Previous Treatment Days", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "753527 ", "URL": "/notes/753527 ", "Title": "IS-H AT: ELDA - IV Requests for Several IRs", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "757853 ", "URL": "/notes/757853 ", "Title": "IS-H AT: ELDA - Insurance Subgroup for IV Import", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "768823 ", "URL": "/notes/768823 ", "Title": "IS-H AT: ELDA - Extension Display", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "776215 ", "URL": "/notes/776215 ", "Title": "IS-H AT: ELDA - Institution Indicator (Field KANKZ)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "786907 ", "URL": "/notes/786907 ", "Title": "IS-H AT: ELDA: Import Insurance Verification - Differences", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "788229 ", "URL": "/notes/788229 ", "Title": "IS-H AT: Scoring - Current Adjustments", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "789039 ", "URL": "/notes/789039 ", "Title": "IS-H AT: ELDA - Update Termination Patient Merge", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "789300 ", "URL": "/notes/789300 ", "Title": "IS-H AT: ELDA - Discharge Notification Field ENTS via TNCMAPP", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "792097 ", "URL": "/notes/792097 ", "Title": "IS-H AT: Insurance Verification - Performance IV Document List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "801331 ", "URL": "/notes/801331 ", "Title": "IS-H AT: Scoring - Adjustments or Bug fixes", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "807225 ", "URL": "/notes/807225 ", "Title": "IS-H AT: ELDA - End of EHIC Data Record", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "809569 ", "URL": "/notes/809569 ", "Title": "IS-H AT: RN1KOSMA - Determine Reminder Deadline", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "811846 ", "URL": "/notes/811846 ", "Title": "IS-H AT: ELDA/SCORE Changes to Settings", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "821244 ", "URL": "/notes/821244 ", "Title": "IS-H AT: ELDA - Import Insurance Verification", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "829574 ", "URL": "/notes/829574 ", "Title": "IS-H AT: ELDA - Checks for EEA Patients UK/GB", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "839456 ", "URL": "/notes/839456 ", "Title": "IS-H AT: ELDA - Outpatient Service Report", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "842007 ", "URL": "/notes/842007 ", "Title": "IS-H AT: ELDA Outpatient Admission notification or Construction Progress Report", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "844919 ", "URL": "/notes/844919 ", "Title": "IS-H AT: Error in Insurance Verification Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "848914 ", "URL": "/notes/848914 ", "Title": "IS-H AT: EDI Scoring - Check for System Information", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "849774 ", "URL": "/notes/849774 ", "Title": "IS-H AT: Scoring - Performance Problems", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "852366 ", "URL": "/notes/852366 ", "Title": "IS-H AT: Scoring - Performance Problems (2)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "855450 ", "URL": "/notes/855450 ", "Title": "IS-H AT: ELDA - Incorrect EHIC Record Cannot Be Imported", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "857550 ", "URL": "/notes/857550 ", "Title": "IS-H AT: ELDA - Use of BAdI Methods, BETREU/URS", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "858293 ", "URL": "/notes/858293 ", "Title": "IS-H AT: Data Exchange - Care Certificate, Cause of Treatment", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "859390 ", "URL": "/notes/859390 ", "Title": "IS-H AT: Data Exchange - Convert Title with TNCMAPP", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "859391 ", "URL": "/notes/859391 ", "Title": "IS-H AT: ELDA - DB Lock RNC30100 (NRIV)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "859789 ", "URL": "/notes/859789 ", "Title": "IS-H AT: Data Exchange - Control User-Defined Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "861548 ", "URL": "/notes/861548 ", "Title": "IS-H AT: Data Exchange - Error Corrections (VVP,LDA)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "861881 ", "URL": "/notes/861881 ", "Title": "IS-H AT: Data Exchange - Describe First Names with Umlauts", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "862327 ", "URL": "/notes/862327 ", "Title": "IS-H AT: Data Exchange - Event Report Visit Date To", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "863402 ", "URL": "/notes/863402 ", "Title": "IS-H AT: ELDA - Remaining, Incorrect STO_AUFN", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "864116 ", "URL": "/notes/864116 ", "Title": "IS-H AT: ELDA - Missing Cancellations for Case Type Change", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "865892 ", "URL": "/notes/865892 ", "Title": "IS-H AT: Data Exchange - Event Report DB Lock NRIV", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "866111 ", "URL": "/notes/866111 ", "Title": "IS-H AT: ELDA - Outpatient IV Groups", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "867866 ", "URL": "/notes/867866 ", "Title": "IS-H AT: Data Exchange - S/T/U Diagnoses for ENTL", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "870398 ", "URL": "/notes/870398 ", "Title": "IS-H AT: Data Exchange - IV Request Check Outpatient", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "870555 ", "URL": "/notes/870555 ", "Title": "IS-H AT: Data Exchange - ELDAL Without Free Text Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "871435 ", "URL": "/notes/871435 ", "Title": "IS-H AT: Data Exchange - No Outpatient Movement Before July 1, 2005", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "872143 ", "URL": "/notes/872143 ", "Title": "IS-H AT: ELDA - Cancel Outpatient Admission Notification After Confirmation", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "872907 ", "URL": "/notes/872907 ", "Title": "IS-H AT: ELDAL - R<PERSON><PERSON><PERSON>LDALU0 Submitted RNWATELDAU1", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "879321 ", "URL": "/notes/879321 ", "Title": "IS-H AT: Insurance Verification - Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "879950 ", "URL": "/notes/879950 ", "Title": "IS-H AT: Data Exchange - STATE Only Conditional Required Field", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "880952 ", "URL": "/notes/880952 ", "Title": "IS-H AT: Data Exchange - Outpatient Manual Events", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "884505 ", "URL": "/notes/884505 ", "Title": "IS-H AT: ELDAL - Cancel Outpatient Performance Data", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "885083 ", "URL": "/notes/885083 ", "Title": "IS-H AT: Previous Patient Days in EDI Based on Service Rule AK", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "885516 ", "URL": "/notes/885516 ", "Title": "IS-H AT: Data Exchange - VZE Without Start Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "885633 ", "URL": "/notes/885633 ", "Title": "IS-H AT: Insurance Verification Evidence List for Rejections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "887107 ", "URL": "/notes/887107 ", "Title": "IS-H AT: Data Exchange - Outpatient Visit in the future", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "888950 ", "URL": "/notes/888950 ", "Title": "IS-H AT: Data Exchange - Loss of HI Addr. at Import VZE", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "889299 ", "URL": "/notes/889299 ", "Title": "IS-H AT: Data Exchange - ELDAL Receipt of Error Messages", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "902717 ", "URL": "/notes/902717 ", "Title": "IS-H AT: Data Exchange - Ignore P321 F13 and F14", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "904414 ", "URL": "/notes/904414 ", "Title": "IS-H AT: Data Exchange - P321 AUF/ENT-STO Without Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "909690 ", "URL": "/notes/909690 ", "Title": "IS-H AT: Determine Care Certificate Using TNWAT_BETR", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "909988 ", "URL": "/notes/909988 ", "Title": "IS-H AT: ELDA - Insurance Verification Overlapping IV Requests", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "911049 ", "URL": "/notes/911049 ", "Title": "IS-H AT: Add \\&quot;Without IR/SI\\&quot; to Report RNWATKUELIST", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "914170 ", "URL": "/notes/914170 ", "Title": "IS-H AT: ELDA - Required Field Checks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "917405 ", "URL": "/notes/917405 ", "Title": "IS-H AT: Scoring - Check Discharge Date for NPFO Cases", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "917463 ", "URL": "/notes/917463 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) by RNC301I0", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "918052 ", "URL": "/notes/918052 ", "Title": "IS-H AT: Data Exchange P321 - Extension Display", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "918382 ", "URL": "/notes/918382 ", "Title": "IS-H AT: Data Exchange P321 - Disp. Adm. Despite Final Billing", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "918521 ", "URL": "/notes/918521 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) - NV2001 Locks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "921499 ", "URL": "/notes/921499 ", "Title": "IS-H AT: ELDAL - Insurance Provider Responsible for Service (D.5)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "921827 ", "URL": "/notes/921827 ", "Title": "IS-H AT: Report RNWATKUELIST - No IV Display Without IR", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "921944 ", "URL": "/notes/921944 ", "Title": "IS-H AT: ELDA - Memory space problems during file import", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "925087 ", "URL": "/notes/925087 ", "Title": "IS-H AT: ELDA - Rejection Reason for Outpatient Insurance Verification", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "926716 ", "URL": "/notes/926716 ", "Title": "IS-H AT: ELDA - NRIV Lock, ELDAL - First Visit Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "927795 ", "URL": "/notes/927795 ", "Title": "IS-H AT: Data Exchange - Event Processing via Reports", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "928673 ", "URL": "/notes/928673 ", "Title": "IS-H AT: Data Exchange - Change of Health/Illness Newborn", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "936603 ", "URL": "/notes/936603 ", "Title": "IS-H AT: Data Exchange - Various Corrections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "939868 ", "URL": "/notes/939868 ", "Title": "IS-H AT: Report RNWATKUELIST - Selection of Rejection Reasons", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "940487 ", "URL": "/notes/940487 ", "Title": "IS-H AT: ELDA - Memory Problems Importing Large Files", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "940988 ", "URL": "/notes/940988 ", "Title": "IS-H AT: Data Exchange - Lock During Import of IP", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "943845 ", "URL": "/notes/943845 ", "Title": "IS-H AT: ELDA - Current Adjustments", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "600", "Number": "879247 ", "URL": "/notes/879247 ", "Title": "IS-H AT: Data Exchange - P321 IV Multiple IR for COC", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "879321 ", "URL": "/notes/879321 ", "Title": "IS-H AT: Insurance Verification - Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "879950 ", "URL": "/notes/879950 ", "Title": "IS-H AT: Data Exchange - STATE Only Conditional Required Field", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "880952 ", "URL": "/notes/880952 ", "Title": "IS-H AT: Data Exchange - Outpatient Manual Events", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "884505 ", "URL": "/notes/884505 ", "Title": "IS-H AT: ELDAL - Cancel Outpatient Performance Data", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "885083 ", "URL": "/notes/885083 ", "Title": "IS-H AT: Previous Patient Days in EDI Based on Service Rule AK", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "885516 ", "URL": "/notes/885516 ", "Title": "IS-H AT: Data Exchange - VZE Without Start Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "885633 ", "URL": "/notes/885633 ", "Title": "IS-H AT: Insurance Verification Evidence List for Rejections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "887107 ", "URL": "/notes/887107 ", "Title": "IS-H AT: Data Exchange - Outpatient Visit in the future", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "888950 ", "URL": "/notes/888950 ", "Title": "IS-H AT: Data Exchange - Loss of HI Addr. at Import VZE", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "889299 ", "URL": "/notes/889299 ", "Title": "IS-H AT: Data Exchange - ELDAL Receipt of Error Messages", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "902717 ", "URL": "/notes/902717 ", "Title": "IS-H AT: Data Exchange - Ignore P321 F13 and F14", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "904414 ", "URL": "/notes/904414 ", "Title": "IS-H AT: Data Exchange - P321 AUF/ENT-STO Without Diagnoses", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "909988 ", "URL": "/notes/909988 ", "Title": "IS-H AT: ELDA - Insurance Verification Overlapping IV Requests", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "911049 ", "URL": "/notes/911049 ", "Title": "IS-H AT: Add \\&quot;Without IR/SI\\&quot; to Report RNWATKUELIST", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "914170 ", "URL": "/notes/914170 ", "Title": "IS-H AT: ELDA - Required Field Checks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "917405 ", "URL": "/notes/917405 ", "Title": "IS-H AT: Scoring - Check Discharge Date for NPFO Cases", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "917463 ", "URL": "/notes/917463 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) by RNC301I0", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "918052 ", "URL": "/notes/918052 ", "Title": "IS-H AT: Data Exchange P321 - Extension Display", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "918382 ", "URL": "/notes/918382 ", "Title": "IS-H AT: Data Exchange P321 - Disp. Adm. Despite Final Billing", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "918521 ", "URL": "/notes/918521 ", "Title": "IS-H AT: ELDA - Lock Number Range (NRIV) - NV2001 Locks", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "921499 ", "URL": "/notes/921499 ", "Title": "IS-H AT: ELDAL - Insurance Provider Responsible for Service (D.5)", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "921827 ", "URL": "/notes/921827 ", "Title": "IS-H AT: Report RNWATKUELIST - No IV Display Without IR", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "921944 ", "URL": "/notes/921944 ", "Title": "IS-H AT: ELDA - Memory space problems during file import", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "925087 ", "URL": "/notes/925087 ", "Title": "IS-H AT: ELDA - Rejection Reason for Outpatient Insurance Verification", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "926716 ", "URL": "/notes/926716 ", "Title": "IS-H AT: ELDA - NRIV Lock, ELDAL - First Visit Date", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "927795 ", "URL": "/notes/927795 ", "Title": "IS-H AT: Data Exchange - Event Processing via Reports", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "928673 ", "URL": "/notes/928673 ", "Title": "IS-H AT: Data Exchange - Change of Health/Illness Newborn", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "936603 ", "URL": "/notes/936603 ", "Title": "IS-H AT: Data Exchange - Various Corrections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "938453 ", "URL": "/notes/938453 ", "Title": "IS-H AT: Correction Collection for Version 6.00 to Patch 02", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "939868 ", "URL": "/notes/939868 ", "Title": "IS-H AT: Report RNWATKUELIST - Selection of Rejection Reasons", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "940487 ", "URL": "/notes/940487 ", "Title": "IS-H AT: ELDA - Memory Problems Importing Large Files", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "940988 ", "URL": "/notes/940988 ", "Title": "IS-H AT: Data Exchange - Lock During Import of IP", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "942332 ", "URL": "/notes/942332 ", "Title": "IS-H AT: Mass Scoring - Dump CALL_FUNCTION_REMOTE_ERROR", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "943845 ", "URL": "/notes/943845 ", "Title": "IS-H AT: ELDA - Current Adjustments", "Component": "XX-CSC-AT-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=954447&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=02\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/954447/D\" target=\"_blank\">/notes/954447/D</a>."}}}}