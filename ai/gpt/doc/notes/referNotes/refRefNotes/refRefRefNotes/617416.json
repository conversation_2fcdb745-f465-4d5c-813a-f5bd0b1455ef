{"Request": {"Number": "617416", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 264, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015445332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000617416?language=E&token=130957E020A6814721AE14CE9D4A84F4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000617416", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000617416/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "617416"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.01.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "617416 - Oracle9i: Dynamic SGA"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br /><br />This is a documentation note to describe Oracle's new dynamic storage management, known as<B> the Dynamic System Global Area ('Dynamic SGA')</B>, a new feature of Oracle9i.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br /><br />Documentation note on Oracle9i: Dynamic SGA<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br /></p> <b>Structure of this note</b><br /> <OL>1. Support with BR*Tools and SAPDBA</OL> <OL>2. General Recommendation</OL> <OL>3. Dynamic SGA - description</OL> <OL>4. Dynamic SGA - operation, prerequisites, restrictions</OL> <OL>5. Dynamic SGA - advantages</OL> <OL>6. Dynamic SGA - migration</OL> <OL>7. Dynamic SGA - important notes</OL> <OL>8. Dynamic SGA - migration</OL> <OL>9. Dynamic SGA - administration</OL> <OL>10. Dynamic SGA - monitoring</OL> <OL>11. Dynamic SGA - memory areas + SGA size<br /></OL> <b>Support with BR*Tools and SAPDBA<br /></b><br /> <p>This new Oracle9i feature is transparent for BR*Tools and SAPDBA. Because of the SPFILE support, a BR*Tools Version 6.20 patch level 103 or higher is recommended.<br /></p> <b>General Recommendation<br /></b><br /> <p>In the context of the database upgrade to Oracle9I, we recommend that you convert to 'Dynamic SGA' to use the advantages of this feature. As of SAP WebAS 6.40, this feature becomes the SAP standard for installations based on Oracle9i.<br /></p> <b>Dynamic SGA - description<br /></b><br /> <p>Up to and including Oracle8i (8.1.7.x), both the SGA (= System Global Area) as a whole, and the memory areas existing within the SGA, were fixed with regard to their size when the instance was started (=static). The database had to be restarted for SGA configuration changes. With Oracle9I, now most of the buffer areas in the SGA can be changed dynamically (enlarged and reduced) and thereby adjusted optimally, for example, to different workloads. This feature is the start of an ongoing development of the Oracle server to be able to make changes to the SGA configuration or to allow them to be carried out automatically - without stopping the instance.<br /><br />This feature is also complemented by a<B>'Buffer Cache Advisor'</B>; this mechanism allows the performance behavior of the instance to be predicted with different buffer cache sizes.<br /><br />'Multiple Block size', in other words tablespaces with different block sizes (2k, 4k, 8k, 16k, 32k), are also only possible with Oracle9i. The 'Dynamic SGA' feature is a prerequisite for 'Multiple Block Size'. At present we can not yet say if 'Multiple Block Size' will be supported in the SAP environment.<B>We therefore recommend that you do not use this in an SAP System.</B><br /><br /><br />The use of variously configured SGAs is already common today and has until now only been achieved by restarting the instance, therefore involving a short system downtime. The 'Dynamic SGA' feature now allows a reconfiguration of the SGA during runtime (for example when the load profile is changed or if there is less physical memory available).<br /></p> <b>Dynamic SGA - operation, prerequisites, restrictions<br /></b><br /> <p>To use this new feature, new parameters must be used:<br /></p> <UL><LI>sga_max_size:<br />This static parameter defines the maximum size of the SGA (in bytes) up to which the SGA can increase dynamically. The sizes for the Buffer Cache, Shared Pool and Large Pool can be adjusted at runtime, as long as the total of its sizes including the other components (fixed SGA, variable, SGA, Redo buffers) do not exceed the limit of sga_max_size. This parameter serves primarily to prevent an 'Oversizing' of the SGA and paging.<br />The SGA increases if required in certain units ('granules'). With most platforms the SGA increases up to a size of 128 MB in 4 MB steps, then in steps of 16 MB (Windows 32 bit: 8MB). Oracle rounds off a specified size of an SGA component, possibly also to the next complete granule size.<br />If sga_max_size is not set explicitly, Oracle sets sga_max_size as a default value (if db_cache_size is set) to the total of all SGA components when the instance is started. It follows that the SGA cannot be larger than during the start, rather it can only be smaller. It would therefore be useful to set this parameter explicitly onto a useful maximum value, up to which the SGA can grow dynamically - without a system downtime, without paging occurring.<br />The value specified for sga_max_size is allocated when the instance starts, even if the sum of the individual SGA components is smaller.<br /></LI></UL> <UL><LI>db_cache_size:<br />This new parameter activates the dynamic SGA and defines the size of the buffer cache. The old parameter db_block_buffers is obsolete. If you set both db_block_buffers and sga_max_size or db_cache_size, the system returns&#x00A0;&#x00A0;the following error message:&#x00A0;&#x00A0;ORA-00381: cannot use both new and old parameters for buffer cache size specification<br /></LI></UL> <p>The following parameters cannot be combined with the Dynamic SGA Feature:</p> <UL><LI>DB_BLOCK_BUFFERS</LI></UL> <UL><LI>BUFFER_POOL_KEEP</LI></UL> <UL><LI>BUFFER_POOL_RECYCLE<br /></LI></UL> <p>The following areas of the SGA can be changed dynamically with Oracle 9.2 if the SGA is configured dynamically:</p> <UL><LI>Buffer Cache: DB_CACHE_SIZE</LI></UL> <UL><LI>Shared Pool : SHARED_POOL_SIZE</LI></UL> <UL><LI>Large Pool : LARGE_POOL_SIZE<br /></LI></UL> <p>The following cannot be changed dynamically or only changeable by restarting the instance, as before:</p> <UL><LI>Maximum SGA size: SGA_MAX_SIZE</LI></UL> <UL><LI>Redo Log Buffer: LOG_BUFFER</LI></UL> <UL><LI>Java Pool: JAVA_POOL_SIZE<br /></LI></UL> <b>Restriction for Windows platform</b><br /> <p>The 'Dynamic SGA' feature cannot be combined with the extended storage management of Microsoft Windows 2000 (32 bit), the 'Address Windowing Extensions (AWE)'. (Metalink note 225349.1). For Oracle systems on Windows, which require correspondingly large SGAs, the 64-bit architecture (Windows on IA64) should therefore be taken into consideration.<br /></p> <b>Dynamic SGA - advantages<br /></b><br /> <p>The Dynamic SGA Feature offers the following advantages as against the memory management in Oracle8i:</p> <UL><LI>The dynamic change of Buffer Cache and Shared Pool (without restart)</LI></UL> <UL><LI>Recycle Pool and Keep Pool no longer within the Buffer Cache</LI></UL> <UL><LI>Oracle-internal Cache Advisor: Parameter STATISTICS_LEVEL, view V$DB_CACHE_ADVICE</LI></UL> <UL><LI>Only the SGA total size SGA_MAX_SIZE is fixed; within this size the other buffer areas can be changed dynamically (they can be enlarged and reduced)</LI></UL> <UL><LI>This now offers you better options to adjust the SGA dynamically to requirements</LI></UL> <UL><LI>With V$DB_CACHE_ADVICE the SGA size can be adjusted optimally</LI></UL> <UL><LI>The compiling of data for the Cache Advisor can be switched on and off</LI></UL> <UL><LI>You can examine different loads and therefore create different SGA profiles</LI></UL> <UL><LI>Suitable for single-instance and RAC<br /></LI></UL> <b>Dynamic SGA - migration<br /></b><br /> <p>Until now the 'show sga' command showed that which was actually allocated by the SGA. With dynamic SGA, however, the system displays the maximum possible memory (based on sga_max_size), and not the memory that is actually allocated at the moment!<br /></p> <b>Dynamic SGA - important notes<br /></b><br /> <p>Sun Solaris 8: Refer to note 360438!<br />Sun Solaris 9: Refer to note 550585!<br /></p> <b>Dynamic SGA - migration<br /></b><br /> <p>To convert to a dynamic SGA, you can proceed as follows:<br /><br />1. Display the current settings<br />&#x00A0;&#x00A0; SQL&gt;show sga<br />&#x00A0;&#x00A0; SQL&gt;show parameter db_block_size<br />Old parameters that are still active:<br />&#x00A0;&#x00A0; SQL&gt;show parameter db_block_buffers<br />&#x00A0;&#x00A0;SQL&gt;show parameter buffer_pool_keep<br />&#x00A0;&#x00A0; SQL&gt;show parameter buffer_pool_recycle<br />New parameters to be set:<br />&#x00A0;&#x00A0; SQL&gt;show parameter sga_max_size<br />&#x00A0;&#x00A0; SQL&gt;show parameter cache_size<br />&#x00A0;&#x00A0; SQL&gt;show parameter statistics_level<br /><br />2. Specification of the new parameters<br /><br />Specification of Keep pool and Recycle pool:<br />&#x00A0;&#x00A0; SQL&gt;show parameter buffer_pool_keep<br />&#x00A0;&#x00A0;SQL&gt;show parameter buffer_pool_recycle<br />&#x00A0;&#x00A0;Set the new parameters the same as the old values, that is:<br />&#x00A0;&#x00A0;db_recycle_cache_size = buffer_pool_recycle<br />&#x00A0;&#x00A0;db_keep_cache_size = buffer_pool_keep<br />&#x00A0;&#x00A0; See note 564861.<br />&#x00A0;&#x00A0; As a rule, these two parameters are not set.<br /><br />Calculation of sga_max_size:<br />The following must be established for this parameter:</p> <UL><LI>Current SGA size (lower limit for sga_max_size)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; SQL&gt;show sga</LI></UL> <UL><LI>Physical memory of the database server (max. upper limit)<br />Determining the physical memory:<br />Unix: For example, using the top command (/usr/local/bin/top -&gt; Memory)<br />Windows: For example, from the System Settings (Task Manager)</LI></UL> <UL><LI>How much physical memory of the database server can be occupied by the Oracle instance (upper limit)?</LI></UL> <UL><LI>An Oracle shadow process is generated for each database connection (Dedicated Server Model). The memory occupied by these processes also has to be taken into account when calculating the memory available for the SGA (refer to note 619876), to prevent a paging from occurring.</LI></UL> <UL><LI>There may be other Oracle instances on the same server or other applications that must divide the physical memory.</LI></UL><UL><LI>For performance reasons, as much physical memory as possible should be assigned to the Oracle instance, but at least as much as before. However, the SGA must not become so large that paging occurs (swap, temporary storing of memory pages on disk). This disadvantage would outweigh the advantage of a large SGA.<br /></LI></UL> <p>Calculation of db_cache_size:<br />Consider the following: Up to now, the size of the Buffer Cache was specified by the number of blocks (db_block_buffers). The new parameter db_cache_size is specified in bytes. For an unchanged size of the Buffer Cache, set<br /><br />&#x00A0;&#x00A0; db_cache_size = db_block_buffers * db_block_size<br /><br />Specification of db_2k_cache_size, db_4k_cache_size and so on.<br />As long as no tablespaces are to be created in the database that use a different block size than the primary block size (standard block size), no buffers should be allocated in the SGA for these block sizes either.<br /><br />db_cache_advice and statistics_level<br />The<B>statistics_level</B> parameter should be set to TYPICAL or ALL. Only at this point are the statistics provided in the V$DB_CACHE_ADVICE view.<br /><br />The STATISTICS_LEVEL parameter continues to activate more 'Advisor' Oracle servers internally.<br /><br />The parameter DB_CACHE_ADVICE, which was provided up to now especially for V$DB_CACHE_ADVICE, was replaced by the parameter STATISTICS_LEVEL. DB_CACHE_ADVICE should therefore no longer be used.<br />&#x00A0;&#x00A0; Up to now, the following applied:<br />&#x00A0;&#x00A0; SQL&gt;alter system set DB_CACHE_ADVICE=ON;<br />&#x00A0;&#x00A0;Now the following applies:<br />&#x00A0;&#x00A0; SQL&gt;alter system set statistics_level='TYPICAL';<br /><br />3. Setting the new parameters<br />Set the value calculated above for 'X'.<br />&#x00A0;&#x00A0; SQL&gt;connect / as sysdba<br />&#x00A0;&#x00A0;SQL&gt;REM Delete old SGA parameter<br />&#x00A0;&#x00A0; SQL&gt;alter system reset buffer_pool_keep scope = spfile sid='*';<br />&#x00A0;&#x00A0; SQL&gt;alter system reset buffer_pool_recycle scope = spfile sid='*';<br />&#x00A0;&#x00A0;SQL&gt;alter system reset db_block_buffers scope = spfile sid='*';<br />&#x00A0;&#x00A0; SQL&gt;REM Set new SGA parameter<br />&#x00A0;&#x00A0;SQL&gt;alter system set sga_max_size=X scope = spfile;<br />&#x00A0;&#x00A0;SQL&gt;alter system set db_cache_size=X scope = spfile;<br />&#x00A0;&#x00A0;SQL&gt;REM statistics_level = ALL or TYPICAL<br />&#x00A0;&#x00A0; SQL&gt;alter system set statistics_level='TYPICAL' scope = spfile;<br />If necessary, set:<br />&#x00A0;&#x00A0; SQL&gt;alter system set db_keep_cache_size=X scope = spfile;<br />&#x00A0;&#x00A0; SQL&gt;alter system set db_recycle_cache_size=X scope = spfile;<br /><br />4. Restart the database to activate the new parameters:<br />&#x00A0;&#x00A0;SQL&gt;connect / as sysdba<br />&#x00A0;&#x00A0; SQL&gt;shutdown<br />&#x00A0;&#x00A0; SQL&gt;startup<br />&#x00A0;&#x00A0; SQL&gt;show sga<br />The following SGA parameters must now have values &gt; 0:<br />&#x00A0;&#x00A0; SQL&gt;show parameter sga_max_size<br />&#x00A0;&#x00A0; SQL&gt;show parameter db_cache_size<br />The following old SGA parameters must now have the value 0:<br />&#x00A0;&#x00A0; SQL&gt;show parameter db_block_buffers<br />&#x00A0;&#x00A0;SQL&gt;show parameter buffer_pool_keep<br />&#x00A0;&#x00A0; SQL&gt;show parameter buffer_pool_recycle<br />The following parameter is unchanged:<br />&#x00A0;&#x00A0; SQL&gt;show parameter db_block_size<br /></p> <b>Dynamic SGA - administration<br /></b><br /> <p>Using the performance view V$DB_CACHE_ADVICE, you can set the size of the SGA optimally. Oracle collects the required data as soon as the init.ora parameter DB_CACHE_ADVICE is set accordingly.<br />SQL&gt;ALTER SYSTEM SET STATISTICS_LEVEL='TYPICAL';<br />V$DB_CACHE_ADVICE compiles all data since the last time the instance was started. On live SAP Systems with an average CPU utilization of 30-60% on the database server, the CPU overhead for the additional compiling and formatting of this information acceptable.<br /><br />Resetting of sga_max_size to the minimum Oracle default value:<br />SQL&gt;alter system reset sga_max_size scope = spfile sid = '*';<br /><br />Example for the setting of sga_max_size:<br />SQL&gt;alter system set sga_max_size = 800M scope = spfile;<br /></p> <b>Dynamic SGA - monitoring<br /></b><br /> <p>The size of the SGA can increase due to an administrative command (for example, by enlarging the buffer cache), can reduce (for example, by reducing the buffer cache) or indeed due to Oracle-internal, Oracle-controlled processes.<br /><br />The following Oracle performance views inform you about the status of SGA components and about size change operations:</p> <UL><LI>V$SGA_CURRENT_RESIZE_OPS<br />Current SGA size change operation(s)</LI></UL> <UL><LI>V$SGA_RESIZE_OPS<br />List of the last 100 SGA size change operations carried out</LI></UL> <UL><LI>V$SGA_DYNAMIC_COMPONENTS<br />V$SGA_DYNAMIC_COMPONENTS shows, for example, the current size and the previous minimum and maximum size of an SGA component, the number of size changes that have occurred on an SGA component, the type of the last size-changing operations on a component and the current granule size:<br />SQL&gt;select component, current_size, granule_size<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;from v$sga_dynamic_components;</LI></UL> <UL><LI>V$SGA_DYNAMIC_FREE_MEMORY<br />Display of the memory available for future SGA enhancements</LI></UL> <p></p> <b>Dynamic SGA&#x00A0;&#x00A0;memory area<br /></b><br /> <p>After an instance is started, you can display the SGA with the following command:<br />SQL&gt;show sga<br /><br />Example:<br />SQL&gt;show sga<br />Total System Global Area&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;135339268 bytes<br />Fixed Size&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;453892 bytes<br />Variable Size&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;109051904 bytes<br />Database Buffers&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;25165824 bytes<br />Redo Buffers&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;667648 bytes<br />SQL&gt;<br /><br />The following different areas of the SGA are displayed:<br /><br /><STRONG>'Total System Global Area'</STRONG><br /></p> <UL><LI>(Maximum) total size of the SGA (including all subareas') in bytes<br /></LI></UL> <p><STRONG>'Fixed Size'</STRONG><br /></p> <UL><LI>Contains general information on the status of the database and the instance; is required by the background processes</LI></UL> <UL><LI>Does not contain any user data</LI></UL> <UL><LI>Usually this area is smaller than 100k<br /></LI></UL> <p><STRONG>'Variable Size'</STRONG><br /></p> <UL><LI>This area depends on the parameters shared_pool_size, large_pooL_size,java_pool_size.<br /></LI></UL> <p><STRONG>'Database Buffers'</STRONG><br /></p> <UL><LI>Size of the Buffer Cache; contains copies of the data blocks from the data files</LI></UL> <UL><LI>Size: block_size * db_block_buffers or db_cache_size<br /></LI></UL> <p><STRONG>'Redo Buffers'</STRONG><br /></p> <UL><LI>Circular memory in the SGA which contains the change information written into the Redologs</LI></UL> <UL><LI>Affected by the parameter log_buffers, but the displayed values 'Redo Buffers' and the profile parameter 'LOG_BUFFER' do not agree<br /></LI></UL> <p>The size of the physical memory occupied by the SGA can be calculated approximately with the following formula:<br />DB_CACHE_SIZE+DB_KEEP_CACHE_SIZE+DB_RECYCLE_CACHE_SIZE+DB_nK_CACHE_SIZE+<br />SHARED_POOL_SIZE+LARGE_POOL_SIZE+JAVA_POOL_SIZE+LOG_BUFFERS+1M<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-DBA (Database Administration)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "D000674"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000617416/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000617416/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000617416/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000617416/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000617416/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000617416/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000617416/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000617416/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000617416/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939558", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-600 during dynamic change of System Global Area (SGA)", "RefUrl": "/notes/939558"}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576"}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011"}, {"RefNumber": "743328", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-27102", "RefUrl": "/notes/743328"}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624"}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "579540", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00381, unable to start the Oracle 9.x database", "RefUrl": "/notes/579540"}, {"RefNumber": "550585", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP relevant patches for Solaris 9 on SPARC", "RefUrl": "/notes/550585"}, {"RefNumber": "360438", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/360438"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576 "}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}, {"RefNumber": "712624", "RefComponent": "BC-DB-ORA", "RefTitle": "High CPU consumption by Oracle", "RefUrl": "/notes/712624 "}, {"RefNumber": "598678", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: New functions", "RefUrl": "/notes/598678 "}, {"RefNumber": "550585", "RefComponent": "BC-OP-SUN", "RefTitle": "SAP relevant patches for Solaris 9 on SPARC", "RefUrl": "/notes/550585 "}, {"RefNumber": "939558", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-600 during dynamic change of System Global Area (SGA)", "RefUrl": "/notes/939558 "}, {"RefNumber": "743328", "RefComponent": "BC-DB-ORA", "RefTitle": "Composite SAP note: ORA-27102", "RefUrl": "/notes/743328 "}, {"RefNumber": "579540", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-00381, unable to start the Oracle 9.x database", "RefUrl": "/notes/579540 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}