{"Request": {"Number": "194271", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 700, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014764002017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000194271?language=E&token=1C9175CBC042BAEC975FCEA5979991EE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000194271", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000194271/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "194271"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.09.2001"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-HU"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hungary"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hungary", "value": "XX-CSC-HU", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-HU*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "194271 - Legal changes for Hungarian taxes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>As of January 1, 2000, there is a new tax law (48. &#x00A7; (4)) in Hungary. The new law only changes the refundability of the negative tax payable.<br />To meet the demands of the law, a list of open vendor invoices is needed and the relevant value-added tax is required.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Hungary, tax, tax reporting, negative tax payable, RFUMSVHU<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Missing lists for taxes of open invoices<br />Precondition:</p> <UL><UL><LI>Partial payments are not allowed to be created for residual items.</LI></UL></UL> <p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Apart from the list of the tax on sales and purchases program (RFUMSV00) the tax return can be created using this report.<br /><br />Important: This note has changed!<br />The Sapserv paths and the description of the report are new.<br /><br />Delivery of the report:<br />The report is delivered via sapserv3 and via Hot Packages.<br /><br />Single delivery via Sapserv3<br />To load the report into the system, you must carry out the following steps:<br />For UNIX platform:<br />As root</p> <UL><LI>ftp sapserv3</LI></UL> <UL><UL><LI>userename: ftp</LI></UL></UL> <UL><UL><LI>password:&#x00A0;&#x00A0;ftp</LI></UL></UL> <UL><LI>cd/general/R3server/abap/note.0194271</LI></UL> <UL><LI>bin</LI></UL> <UL><LI>get R000165.K3I</LI></UL> <UL><LI>ascii</LI></UL> <UL><LI>get K000165.K3I</LI></UL> <UL><LI>bye<br /></LI></UL> <p>After the files were imported, the owner and the authorization must be set. It is essential that the &lt;SID&gt;adm can read file R000165.K3I and can read and write file K000165.K3I.</p> <UL><LI>chown &lt;SID&gt;adm:sapsys [RK]000165.K3I</LI></UL> <UL><LI>chmod 777 [RK]000165.K3I</LI></UL> <UL><LI>mv R000165.K3I&#x00A0;&#x00A0;/usr/sap/trans/data</LI></UL> <UL><LI>mv K000165.K3I&#x00A0;&#x00A0;/usr/sap/trans/cofiles</LI></UL> <p>As &lt;SID&gt;adm, the transport must be executed on the application server.</p> <UL><LI>cd /usr/sap/trans/bin</LI></UL> <UL><LI>tp addtobuffer K3IK000165 &lt;SID&gt;</LI></UL> <UL><LI>tp import K3IK000165 &lt;SID&gt; client000 u26<br /></LI></UL> <p><br />For other platforms, such as NT, AS/400, and so on, the steps described in Note 13719 must be executed.<br /><br />Delivery via Hot Packages:</p> <UL><UL><LI>Release 3.1H Hot Package 73</LI></UL></UL> <UL><UL><LI>Release 3.1I Hot Package 45 (and not via Hot Package 44 !)</LI></UL></UL> <UL><UL><LI>Release 4.0B Hot Package 17</LI></UL></UL> <UL><UL><LI>Release 4.5B Hot Package 2<br /></LI></UL></UL> <p>Description of the report:<br />Report RFUMSVHU generates two lists of the open vendor invoices and the relevant value-added taxes.<br />The report selects the documents to be processed on the basis of open vendor items. The following selection criteria can be entered:</p> <UL><LI>Account number of the vendors</LI></UL> <UL><LI>Company code</LI></UL> <UL><LI>Search ID/search string</LI></UL> <UL><LI>Key date for the open items</LI></UL> <UL><LI>Fiscal year</LI></UL> <UL><LI>Posting period</LI></UL> <UL><LI>Posting date</LI></UL> <UL><LI>Document date</LI></UL> <UL><LI>Document type</LI></UL> <UL><LI>Document number</LI></UL> <UL><LI>Tax code</LI></UL> <UL><LI>Special G/L indicator</LI></UL> <UL><LI>Date of tax return</LI></UL> <UL><LI>Time of tax return<br /></LI></UL> <p>as well as the selection parameters of the free selections.<br />Explicit special G/L indicators should be included in addition to the default special G/L indicator \" \".<br />Every report generates an initial list and an exceptions list</p> <UL><LI>of the open vendor items of which the documents correspond to the selection criteria and</LI></UL> <UL><LI>of the relevant tax on sales/purchases items of these invoices.</LI></UL> <p>The list shows documents which</p> <UL><LI>contain open or partially cleared vendor credit items and</LI></UL> <UL><LI>the document does not contain any vendor debit items.</LI></UL> <p>The initial list contains (among others) the following data:</p> <UL><LI>Company code</LI></UL> <UL><LI>Vendor credit items (payables)</LI></UL> <UL><LI>Partial payments</LI></UL> <UL><LI>Document number</LI></UL> <UL><LI>Reference</LI></UL> <UL><LI>Account number of the vendor</LI></UL> <UL><LI>Name of the vendor</LI></UL> <UL><LI>Address of the vendor</LI></UL> <UL><LI>Information for each tax on sales/purchases code</LI></UL> <UL><UL><LI>Name of tax</LI></UL></UL> <UL><UL><LI>Tax base</LI></UL></UL> <UL><UL><LI>Tax amount</LI></UL></UL> <UL><UL><LI>Partially paid tax (portion of the tax in accordance with the relationship of invoice amount to the corresponding partial payments)</LI></UL></UL> <UL><UL><LI>Local currency</LI></UL></UL> <p>The exceptions list contains documents</p> <UL><LI>that contain open or partially cleared vendor credit items and</LI></UL> <UL><LI>the document also contains vendor debit items.</LI></UL> <p>The exception list is only displayed if an exception occurs. The exception list contains the following data: company code, fiscal year</p> <UL><LI>Document number, document type</LI></UL> <UL><LI>Account number of the vendor</LI></UL> <UL><LI>Name of vendor</LI></UL> <UL><LI>Address of vendor</LI></UL> <UL><LI>Tax code</LI></UL> <UL><LI>Name of tax</LI></UL> <UL><LI>Vendor items</LI></UL> <UL><LI>Partial payments</LI></UL> <UL><LI>Tax</LI></UL> <UL><LI>Local currency<br /></LI></UL> <p>The amounts in the exception list are summarized for each document and for each company code.<br />Both the initial list and the exception list allow branching to the document display.</p> <h3 data-toc-skip>Example</H3> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL-GL-F (Value Added Tax (VAT))"}, {"Key": "Responsible                                                                                         ", "Value": "D025903"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000194271/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000194271/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000194271/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000194271/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000194271/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000194271/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000194271/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000194271/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000194271/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "196981", "RefComponent": "XX-CSC-HU", "RefTitle": "Gesetzesänderung bei ungarischen Steuern", "RefUrl": "/notes/196981"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "196981", "RefComponent": "XX-CSC-HU", "RefTitle": "Gesetzesänderung bei ungarischen Steuern", "RefUrl": "/notes/196981 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H73", "URL": "/supportpackage/SAPKH31H73"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H73", "URL": "/supportpackage/SAPKE31H73"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I45", "URL": "/supportpackage/SAPKH31I45"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I44", "URL": "/supportpackage/SAPKH31I44"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I45", "URL": "/supportpackage/SAPKE31I45"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I44", "URL": "/supportpackage/SAPKE31I44"}, {"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B38", "URL": "/supportpackage/SAPKH40B38"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B38", "URL": "/supportpackage/SAPKE40B38"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B17", "URL": "/supportpackage/SAPKH45B17"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}