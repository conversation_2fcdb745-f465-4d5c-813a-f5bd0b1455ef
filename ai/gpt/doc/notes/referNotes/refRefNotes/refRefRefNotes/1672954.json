{"Request": {"Number": "1672954", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 248, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017372132017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001672954?language=E&token=145A88C4C4368F0E2FB6D716C47A05EC"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001672954", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001672954/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1672954"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.09.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1672954 - Oracle 11g, 12c, 18c  and 19c: Usage of hugepages on Linux"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Usage of hugepages on a Linux system that runs an Oracle database.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Oracle, Linux, PageTables, hugepages, huge pages, use_large_pages</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Oracle Database 11gR2,&#160; 12c, 18c or 19c on Linux</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Motivation for hugepages:<br /></strong></p>\r\n<p>Depending on the used hardware architecture especially enabled applications can use much larger pages, so called hugepages, which helps reducing the administrative overhead inside the Linux memory management.<br /><br />Please note that the sizes differ on several hardware architectures:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Architecture</td>\r\n<td>PageSize</td>\r\n<td>HugePageSize</td>\r\n</tr>\r\n<tr>\r\n<td>x86_64</td>\r\n<td>4 KB</td>\r\n<td>2 MB</td>\r\n</tr>\r\n<tr>\r\n<td>i386</td>\r\n<td>4 KB</td>\r\n<td>2 MB</td>\r\n</tr>\r\n<tr>\r\n<td>ppc64</td>\r\n<td>64 KB</td>\r\n<td>16 MB</td>\r\n</tr>\r\n<tr>\r\n<td>ia64</td>\r\n<td>64 KB</td>\r\n<td>256 MB</td>\r\n</tr>\r\n<tr>\r\n<td>s390x</td>\r\n<td>4 KB</td>\r\n<td>1 MB</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Please consider using hugepages especially when running larger Oracle SGAs in combination with many Oracle shadow processes. The numbers in the following example are for the x86_64 architecture but are transferable to the other architectures as well.<br />Every process has a private page table to manage its virtual memory. The virtual memory is managed in 4 KB chunks and every chunk needs one Page Table Entry (PTE) to handle it. Every single PTE requires 8 Bytes of additional administrative data to allow the Linux operating system to manage the memory mapping.<br /><br />On a Linux system you can see the memory currently used system-wide by the Linux Kernel for PTEs as the \"PageTables\" entry in /proc/meminfo:<br /><br />#&#160; grep PageTables /proc/meminfo <br />PageTables:&#160;&#160;&#160;&#160;&#160;&#160;&#160; 3479720 kB<br /><br />Please note that the amount of memory displayed there is not available for normal applications and you might need to adapt your sizing accordingly.<br /><br />Assuming an Oracle SGA of 60 GB, this results in the following possible overhead per shadow process:<br /><br />&#160;&#160;60 GB = 15 728 640 pages * 4 KB<br />&#160;&#160;15 728 640 pages * 8 Bytes per PTE&#160;&#160;= 120 MB per process<br /><br />As this amount of administrative data is needed per (shadow) process, for example 300 processes can need up to 36 GB (120 MB * 300) of additional memory, which has to be considered when calculating the needed system resources.<br /><br />Now compared with the usage of hugepage:<br /><br />&#160;&#160;60 GB = 30720 pages * 2 MB<br />&#160;&#160;30720 pages * 8 Bytes per PTE =&#160;&#160;240 KB per process<br /><br />For the 300 shadow processes we need only 70 MB of additional memory.</p>\r\n<p><strong>How to calculate the number of needed hugepages:</strong></p>\r\n<p><br />Rules of thumb:<br />&#160;&#160; Use the shown bytes in sqlplus \"show sga\" and add 1%<br />Exact:<br />&#160;&#160;# ipcs&#160;&#160;-m | grep&#160;&#160;&lt;oracle binary OS owner&gt;<br />The sum of the used shared memory of the Oracle OS owner.<br />If more than one database instance should use hugepages, you have to sum the values of each database instance.<br /><br />For example we have an Oracle database LAS with a SGA of 20 GB:<br /><br />&#160;&#160;SQL&gt; show sga;<br /><br />&#160;&#160;Total System Global Area 2.1379E+10 bytes<br />&#160;&#160;Fixed Size&#160;&#160;&#160;&#160;&#160;&#160;2235904 bytes<br />&#160;&#160;Variable Size&#160;&#160; 1.5972E+10 bytes<br />&#160;&#160;Database Buffers&#160;&#160;5368709120 bytes<br />&#160;&#160;Redo Buffers&#160;&#160;&#160;&#160; 36102144 bytes<br />&#160;&#160;SQL&gt;<br /><br />&#160;&#160;# ipcs -m | grep oralas<br />&#160;&#160;0x38671d2c 3997702&#160;&#160;&#160;&#160;oralas&#160;&#160;&#160;&#160;660&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;21476933632 30<br /><br /><br />Calculate the number of hugepages:<br /><br />&#160;&#160;21476933632 Bytes / 2 MB = (21476933632 / 2097152) bytes = 10241 pages</p>\r\n<p><strong>How to implement hugepages:</strong><br /><strong>Steps to implement hugepages on the OS side:</strong></p>\r\n<p><br />&lt;n&gt; is the number of hugepages with size of 2 MB on a x86_64 hardware architecture.<br />&lt;oracle binary OS owner&gt; is the OS owner of the installed Oracle binaries in $ORACLE_HOME/bin<br /><br />Set the following Linux kernel parameter:<br /><br />&#160;&#160;# sysctl -w vm.nr_hugepages= &lt;n&gt;<br /><br />To make the value of the parameter available for every time you restart the computer, edit the /etc/sysctl.conf file and add the following entry:<br /><br />&#160;&#160; vm.nr_hugepages= &lt;n&gt;<br /><br />Run the following command to check the available hugepages:<br />&#160;&#160;# grep HugePages /proc/meminfo<br /><br />Additional change required in /etc/security/limits.conf:<br />&#160;&#160;&lt;oracle binary OS owner&gt;&#160;&#160; soft memlock unlimited<br />&#160;&#160;&lt;oracle binary OS owner&gt;&#160;&#160; hard memlock unlimited<br /><br />If there exist more than one ORACLE_HOME and hugepages should be used for all database instances, entries for each user must be added to /etc/security/limits.conf.<br /><br />For example the settings of an Oracle database LAS, we define the number of hugepages:<br /><br />&#160;&#160;# cat /etc/sysctl.conf | grep vm<br />&#160;&#160;vm.nr_hugepages=10241<br /><br />&#160;&#160;# grep HugePages /proc/meminfo<br />&#160;&#160;HugePages_Total:&#160;&#160;10241<br />&#160;&#160;HugePages_Free:&#160;&#160;&#160;&#160;7082<br />&#160;&#160;HugePages_Rsvd:&#160;&#160;&#160;&#160;7082<br />&#160;&#160;HugePages_Surp:&#160;&#160;&#160;&#160;&#160;&#160; 0<br /><br />&#160;&#160;# cat /etc/security/limits.conf | grep oralas<br />&#160;&#160;oralas&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;soft&#160;&#160;&#160;&#160;memlock&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;unlimited<br />&#160;&#160;oralas&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;hard&#160;&#160;&#160;&#160;memlock&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;unlimited</p>\r\n<p><strong>Steps to implement hugepages on the database side:</strong></p>\r\n<p>The Parameter use_large_pages controls the usage of hugepages in the<br />database and should be added in the spfile and has three possible<br />values:</p>\r\n<ul>\r\n<li>use_large_pages=false<br />No hugepages will be used by this database instance, but normal 4 KB pages.</li>\r\n</ul>\r\n<ul>\r\n<li>use_large_pages=true<br />The database will use hugepages if there is a sufficiently amount of hugepages available, else it will startup with 4 KB pages.<br />Since 11.2.0. 3 the availiable hugepages will be used and the rest for SGA will be filled with 4 KB pages.</li>\r\n</ul>\r\n<ul>\r\n<li>use_large_pages=only<br />If the number of available hugepages is not sufficient or not correctly configured, the database instance will fail to start showing following errors:<br /><br />No hugepages configured or not correctly configured:<br />ora-27102 :&#160;&#160;out of memory Linux_x86_64 Error 12 :cannot allocate memory<br />Not sufficiently hugepages configured:<br />ora-03113 : end-of-file on communication channel</li>\r\n</ul>\r\n<ul>\r\n<li>use_large_pages=auto_only</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">&#160;Introduced with Oracle 19c.&#160; It is the default setting for Exadata systems.&#160; It specifies that, during startup, the instance will calculate and request the number of hugepages it requires. If the&#160; &#160; &#160; &#160; &#160;operating system can fulfill this request, then the instance will start successfully. If the operating system cannot fulfill this request, then the instance will fail to start.</p>\r\n<p>The option <strong>auto</strong> work well if system memory is not fragmented (immediately after system boot). Contiguous physical pages are availaible to reserve new hugepage. If the system has been running for a long time, then finding a new hugepage will stall the entire system. So it is preferred to set the required number of hugepages before starting the database instance. <strong>It is not recommended to use this option.<br /></strong></p>\r\n<ul>\r\n<li>use_large_pages=auto<br />Option auto since 11.2.0.3. Reconfigure hugepages in the starting process of the database instance. The instance will calculate and request the number of hugepages it requires. If the operating system cannot fulfill this request, then the instance will start with a combination of hugepages and regular pages.</li>\r\n</ul>\r\n<p>In the alert.log you can see how many hugepages are in use by the Oracle database instance.<br /><br /><strong>For example:</strong></p>\r\n<p>We see the startup of the LAS database (version 11gR2)&#160;instance, with using hugepages:<br /><br />&#160;&#160;Wed Jan 04 18:19:41 2012<br />&#160;&#160;SMP system found. enable_NUMA_support disabled (FALSE)<br />&#160;&#160;Starting ORACLE instance (normal)<br />&#160;&#160;****************** Huge Pages Information *****************<br />&#160;&#160;Huge Pages memory pool detected (total: 10241 free: 10241)<br />&#160;&#160;DFLT Huge Pages allocation successful (allocated: 10241)<br />&#160;&#160;***********************************************************</p>\r\n<p>Starting an&#160;database instance using&#160;hugepages&#160;with&#160;Oracle database 12cR1 output differs a little:</p>\r\n<p>Thu Sep 01 09:05:59 2016<br />**********************************************************************<br />Thu Sep 01 09:05:59 2016<br />Dump of system resources acquired for SHARED GLOBAL AREA (SGA)</p>\r\n<p>Thu Sep 01 09:05:59 2016<br />&#160;Per process system memlock (soft) limit = UNLIMITED<br />Thu Sep 01 09:05:59 2016<br />&#160;Expected per process system memlock (soft) limit to lock<br />&#160;SHARED GLOBAL AREA (SGA) into memory: 26G<br />Thu Sep 01 09:05:59 2016<br />&#160;Available system pagesizes:<br />&#160; 4K, 2048K<br />Thu Sep 01 09:05:59 2016<br />&#160;Supported system pagesize(s):<br />Thu Sep 01 09:05:59 2016<br />&#160; PAGESIZE&#160; AVAILABLE_PAGES&#160; EXPECTED_PAGES&#160; ALLOCATED_PAGES&#160; ERROR(s)<br />Thu Sep 01 09:05:59 2016<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160; 4K&#160;&#160;&#160;&#160;&#160;&#160; Configured&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 8&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 8&#160;&#160;&#160;&#160;&#160;&#160;&#160; NONE<br />Thu Sep 01 09:05:59 2016<br />&#160;&#160;&#160;&#160; 2048K&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 14000&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13089&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; 13089&#160;&#160;&#160;&#160;&#160;&#160;&#160; NONE<br />Thu Sep 01 09:05:59 2016<br />**********************************************************************</p>\r\n<p><strong>Remark about SUSE Linux:&#160;</strong></p>\r\n<p>Installations of the Oracle Database running on SLES needs an additional kernel parameter setting vm.hugetlb_shm_group to allow a specific group using hugepages, more details are described in the&#160;<a target=\"_blank\" href=\"https://docs.oracle.com/en/database/oracle/oracle-database/19/ladbi/configuring-additional-kernel-settings-for-suse-linux.html#GUID-7A4DF920-A200-42F1-93CF-EF1D45240092\">Database Installation Guide for Linux</a> for 19c.&#65279;</p>\r\n<p><strong>Summary:</strong></p>\r\n<p>Hugepages for Oracle database can be used for:</p>\r\n<ul>\r\n<li>saving your memory, caused by lesser PTEs and larger page size</li>\r\n</ul>\r\n<ul>\r\n<li>lowering CPU usage, caused by the soft page-fault processing</li>\r\n</ul>\r\n<ul>\r\n<li>locking the SGA pages into the RAM, they won't paged out when memory shortage happens</li>\r\n</ul>\r\n<p><strong>Please note:</strong> Automatic Memory Management (AMM) is not supported in combination with hugepages, see MOS note 749851.1.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-OP-LNX (Linux)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5120534)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5004095)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001672954/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001672954/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001672954/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001672954/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001672954/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001672954/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001672954/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001672954/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001672954/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011"}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868"}, {"RefNumber": "1871318", "RefComponent": "BC-DB-ORA", "RefTitle": "Linux: Disable Transparent HugePages for Oracle Database", "RefUrl": "/notes/1871318"}, {"RefNumber": "1808268", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "SAP on Oracle VM - Oracle VM Support", "RefUrl": "/notes/1808268"}, {"RefNumber": "171356", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/171356"}, {"RefNumber": "1635808", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "Oracle Linux 6.x SAP Installation and Upgrade", "RefUrl": "/notes/1635808"}, {"RefNumber": "1496410", "RefComponent": "BC-OP-LNX-RH", "RefTitle": "Red Hat Enterprise Linux 6.x: Installation and Upgrade", "RefUrl": "/notes/1496410"}, {"RefNumber": "1431798", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Database Parameter Settings", "RefUrl": "/notes/1431798"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3413813", "RefComponent": "BC-OP-LNX-ESX", "RefTitle": "Performance problems on VmWare on Linux", "RefUrl": "/notes/3413813 "}, {"RefNumber": "2813101", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-27125 error occurs when starting Oracle Instance - Netweaver", "RefUrl": "/notes/2813101 "}, {"RefNumber": "2563075", "RefComponent": "BC-OP-LNX", "RefTitle": "Missing memory on Hugepages enabled Linux system - NetWeaver", "RefUrl": "/notes/2563075 "}, {"RefNumber": "2658595", "RefComponent": "BC-DB-ORA", "RefTitle": "Failed to register memory(-ENOMEM) with HCA", "RefUrl": "/notes/2658595 "}, {"RefNumber": "2369910", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/2369910 "}, {"RefNumber": "1310037", "RefComponent": "BC-OP-LNX-SUSE", "RefTitle": "SUSE LINUX Enterprise Server 11: Installation notes", "RefUrl": "/notes/1310037 "}, {"RefNumber": "1871318", "RefComponent": "BC-DB-ORA", "RefTitle": "Linux: Disable Transparent HugePages for Oracle Database", "RefUrl": "/notes/1871318 "}, {"RefNumber": "1431798", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11.2.0: Database Parameter Settings", "RefUrl": "/notes/1431798 "}, {"RefNumber": "618868", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle performance", "RefUrl": "/notes/618868 "}, {"RefNumber": "1496410", "RefComponent": "BC-OP-LNX-RH", "RefTitle": "Red Hat Enterprise Linux 6.x: Installation and Upgrade", "RefUrl": "/notes/1496410 "}, {"RefNumber": "1808268", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "SAP on Oracle VM - Oracle VM Support", "RefUrl": "/notes/1808268 "}, {"RefNumber": "1635808", "RefComponent": "BC-OP-LNX-OLNX", "RefTitle": "Oracle Linux 6.x SAP Installation and Upgrade", "RefUrl": "/notes/1635808 "}, {"RefNumber": "789011", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle memory areas", "RefUrl": "/notes/789011 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}