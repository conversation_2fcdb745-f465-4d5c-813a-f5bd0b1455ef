{"Request": {"Number": "1431752", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 302, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016964432017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001431752?language=E&token=DBBCAEBB1DF4C7C0E0B08E8F7A155B78"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001431752", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001431752/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1431752"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 44}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.02.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1431752 - Oracle 10.2.0: Patches/Patch Collections for ********"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>!! Warning !!</strong><br /><strong>The download of any Oracle software and/or Oracle patches from the SAP Software Download Center (SWDC) is exclusively permitted through a valid Oracle support agreement.</strong><br /><strong>This support agreement may solely be entered into through your local SAP entity or Oracle directly.</strong><br /><strong>Oracle support agreements with third-party providers other than SAP or Oracle, do not authorize any download of Oracle software and/or Oracle patches from the SAP Software Download Center (SWDC).</strong><br /><strong>Before commencing any Oracle download, please ensure that this prerequisite has been fulfilled.</strong><br /><strong>Failing to do so may lead to a breach of your agreement and disruption of support services by SAP.</strong><br /><br /><strong>Until further notice and per policy of Oracle, customers located in Russia, Belarus and Venezuela are not allowed to download Oracle software and/or Oracle patches from SWDC.</strong><br /><strong>Please see link for further details and complete list of impacted countries:</strong><br /><strong><a target=\"_blank\" href=\"https://www.oracle.com/corporate/security-practices/corporate/governance/global-trade-compliance.html\">https://www.oracle.com/corporate/security-practices/corporate/governance/global-trade-compliance.html</a></strong><br /><br /><strong>In case of any doubts or questions around your support agreement, please contact your local SAP contract center for more information.</strong></p>\r\n<p><strong>************************************</strong><strong>************************************</strong></p>\r\n<p><strong>Oracle ********.0 Extended Support</strong></p>\r\n<p><br />If you want to download Oracle ********.0 patches, please see SAP Hot News note 1654734 first. That note provides important information about a new download policy valid in the Oracle Extended Support phase.</p>\r\n<p><strong>Migration of Oracle Patches on the SAP Support Portal</strong></p>\r\n<p><br />Due to legal reasons the Oracle patches on the SAP Support Portal must be migrated from their current location, the \"Database Patches\" area located at http://service.sap.com/oracle-download, to the SAP Software Download Center at http://service.sap.com/swdc. That migration affects also the names of the Oracle patches provided on the SAP Software Download Center, both for the Unix and the Windows platforms.<br /><br />Oracle will migrate its patches to the SAP Software Download Center according to the following schedule:<br /><br />&#160;&#160;********.0: Patchday February 2013<br />&#160;&#160;11.2.0.2.0: Patchday May 2013<br />&#160;&#160;11.2.0.3.0: Patchday June 2013<br /><br />For more information about the migration process, see SAP Note 509314.</p>\r\n<p><strong>Oracle Database Patches for ******** for SAP</strong></p>\r\n<p><br />This note describes which patches for Oracle Database release ******** are released for SAP. All the patches mentioned in this note need to be installed to ensure that the SAP system functions properly.<br /><br />To download Oracle ********.0 patches from the SAP Support Portal follow the path below:<br /><br />&#160;&#160;http://service.sap.com/oracle<br />&#160;&#160;-&gt; Oracle Patches ******** (in the right-hand side info page)<br /><br />The next updates for this note are scheduled for the following dates:<br /><br />&#160;&#160; no further regular SBP for ******** scheduled<br /><br />Please note, CPUJul2013 will be the terminal PSU/CPU for the Oracle release ********. The CPUJul2013 is scheduled within the SAP Bundle Patch (SBP) SAP10205P_1308 in August 2013.<br /><br />Besides those scheduled updates this note will change in exceptional cases only.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br />Oracle Release ********<br />Patch Set Update / PSU<br />SAP Bundle Patch / SBP</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><br />You must apply the SAP Bundle Patches on Unix/Linux or the patch collection on Windows. For more information, see Note 839187.<br /><br />To apply the current SAP bundle patches or the patch collection, you must follow the instructions of the corresponding README.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Contents of this note</strong></p>\r\n<p><br />1.&#160;&#160;SAP Bundle Patches for Unix Platforms<br />1.1 SAP Bundle Patches released for SAP<br />1.2 SAP Bundle Patch Information<br />1.3 Installing an SAP Bundle Patch<br />1.4 Change history (Unix)<br /><br />2.&#160;&#160;Patch Collections for Windows Platforms<br />2.1 Patch Collections released for SAP<br />2.2 Generic Windows Patches<br />2.3 Change History (Windows)</p>\r\n<p><strong>1. SAP Bundle Patches for Unix Platforms</strong><br /> <strong>1.1 SAP Bundle Patches released for SAP</strong></p>\r\n<p>The last 2 SBPs are listed:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Oracle Release</th><th>SBP Release Date</th><th>SBP Name</th></tr>\r\n</tbody>\r\n</table></div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr>\r\n<td>********.12</td>\r\n<td>2013-08</td>\r\n<td>SAP10205P_1308</td>\r\n</tr>\r\n<tr>\r\n<td>********.11</td>\r\n<td>2013-05</td>\r\n<td>SAP10205P_1305</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />SAP Bundle Patch SAP10205P_1302 requires at least MOPatch version 2.1.14.&#160;&#160;Update your MOPatch version according to the instructions in the SBP readme before installing the SBP itself.</p>\r\n<p><strong>1.2 SAP Bundle Patch Information</strong></p>\r\n<p>This section contains corrections and important information regarding the installation of the SBPs listed above.<br /><br /><strong>22. November 2011</strong><br /><br />Based on a technical reason (missing Merge 12750959) the SBP SAP_102055_201111 has been withdrawn. Customers who already applied this SBP should apply December SBP SAP_102055_201112 as soon as it is available.</p>\r\n<p><strong>1.3 Installing an SAP Bundle Patch</strong></p>\r\n<ol><ol>1. Follow the path below:</ol></ol>\r\n<p><br /><br /></p>\r\n<ol><ol>&#160;&#160;http://service.sap.com/oracle</ol></ol><ol>&#160;&#160;-&gt; Oracle Patches ******** (in the right-hand side info page)</ol><ol>2. Open the platform directory and download the SAP Bundle Patch file SAP&lt;Release&gt;P_&lt;Date&gt;-&lt;GUID&gt;.ZIP, the SBP README README&lt;Release&gt;P_&lt;Date&gt;-&lt;GUID&gt;.HTM, and the SBP patch history BUGSFIXED&lt;Release&gt;P_&lt;Date&gt;-&lt;GUID&gt;.HTM to your database server.</ol><ol>3. Use MOPatch (SAP note 1027012) to install the SAP bundle patch in accordance with the instructions in the SBP README. When you do this, pay particular attention to the patch-specific installation instructions.</ol>\r\n<p><br />Remarks:</p>\r\n<ul>\r\n<li>Due to the complexity, the installation of a new PSU (if it is contained in the SAP bundle patch) takes a considerably longer amount of time than a normal installation of an individual patch. The runtime depends on how powerful your database server is. You can track the progress of the installation in the MOPatch log file specified below.</li>\r\n</ul>\r\n<ul>\r\n<li>You must use MOPatch to install SBPs. The direct use of OPatch to install SBPs or individual patches from an SBP is not supported unless specifically stated.</li>\r\n</ul>\r\n<p><strong>1.4 Change history (Unix)</strong></p>\r\n<p><br /><strong>15. August 2013</strong><br />SAP10205P_1308 released for customers (includes CPUJul2013).<br /><br /><strong>14. August 2013</strong><br />SAP10205P_1308 is delayed.<br />The next planned release date for this SBP is 16. August 2013.<br /><br /><br /><strong>15. May 2013</strong><br />SAP10205P_1305 released for customers (includes CPUApr2013).<br /><br /><strong>14. February 2013</strong><br />SAP10205P_1302 released for customers (includes CPUJan2013).<br />This SBP is the first released on the new SAP Software Download Center.<br />This SBP requires MOPatch 2.1.14.<br /><br /><strong>10. January 2013</strong><br />No SBP scheduled.<br /><br /><strong>11. December 2012</strong><br />No SBP scheduled.<br /><br /><strong>16. November 2012</strong><br />SBP_102059_201211 released for customers (includes CPUOct2012)<br /><br /><strong>13. November 2012</strong><br />SBP_102059_201211 is delayed.<br />The next planned release date for this SBP is 15. November 2012.<br /><br /><strong>11. October 2012</strong><br />No SBP scheduled.<br /><br /><strong>13. September 2012</strong><br />No SBP scheduled.<br /><br /><strong>14. August 2012</strong><br />SBP_102058_201205 released for customers (includes CPUJul2012)<br /><br /><strong>10. July 2012</strong><br />SBP_102057_201207 released for customers<br /><br /><strong>12. June 2012</strong><br />SBP_102057_201206 released for customers<br /><br /><strong>15. May 2012</strong><br />SBP_102057_201205 released for customers (includes CPUApr2012)<br /><br /><strong>10. May 2012</strong><br />SBP_102057_201205 is delayed.<br />The next planned release date for this SBP is 15. May 2012.<br /><br /><strong>12. April 2012</strong><br />No SBP scheduled.<br /><br /><strong>13. March 2012</strong><br />SBP_102056_201203 released for customers<br /><br /><strong>14. February 2012</strong><br />SBP_102056_201202 released for customers (includes CPUJan2012)<br /><br /><strong>11. January 2012</strong><br />No SBP scheduled.<br /><br /><strong>13. December 2011</strong><br />SBP_102055_201112 released for customers<br /><br /><strong>10. November 2011</strong><br />SBP_102055_201111 released for customers (includes CPUOct2011)<br /><br /><strong>12. October 2011</strong><br />SBP_102054_201110 released for customers<br /><br /><strong>13. September 2011</strong><br />SBP_102054_201109 released for customers<br /><br /><strong>10. August 2011</strong><br />SBP_102054_201108 released for customers (includes CPUJul2011)<br /><br /><strong>13. July 2011</strong><br />SBP_102053_201107 released for customers<br /><br /><strong>10. June 2011</strong><br />SBP_102053_201106 released for customers<br />SBP for platforms Linux IA64 and Linux on Power released for customers.<br /><br /><strong>12. May 2011</strong><br />SBP_102053_201105 released for customers (includes CPUApr2011)<br /><br /><strong>12. April 2011</strong><br />SBP_102052_201104 released for customers<br /><br /><strong>11. March 2011</strong><br />SBP_102052_201103 released for customers<br /><br /><strong>10. February 2011</strong><br />SBP_102052_201102 released for customers (includes CPUJan2011)<br /><br /><strong>11. January 2011</strong><br />SBP_102051_201101 released for customers<br /><br /><strong>Please note:</strong> As described in the README.htm you have to de-install patch 10111124 manually before applying the SBP_102051_201101.<br /><br /><br /><strong>14. December 2010</strong><br />SBP_102051_201012 released for customers<br /><br /><br /><strong>10. November 2010</strong><br />SBP_102051_201011 released for customers (includes CPUOct2010)<br /><br />This SBP contains a <strong>new MOPatch version</strong> (2.1.5), which has to be installed before the SBP will be applied. Please see the SBP README.<br /><br /></p>\r\n<p><strong>2. Patch Collections for Windows Platforms</strong></p>\r\n<p><br /><span style=\"text-decoration: underline;\">General Information</span></p>\r\n<ul>\r\n<li>The last two (most recent) Oracle patch collections released by SAP for each Windows platform are listed. It is recommended to use the most recent patch collection.</li>\r\n</ul>\r\n<ul>\r\n<li>Additional required generic patches are also listed.</li>\r\n</ul>\r\n<ul>\r\n<li>Please apply Oracle patch collection and Oracle generic patches as described in the relevant README file for the patch collection or the generic patch. In the patch collection README file, the term \"bundle patch\" is used instead of \"patch collection\".</li>\r\n</ul>\r\n<ul>\r\n<li>To apply the patch collection and the generic patches use OPatch (see Note 839182).</li>\r\n</ul>\r\n<ul>\r\n<li>In order to determine which Oracle patch collection is currently installed in your system, use the following query:<br />SQL&gt; select action, namespace, version, id, comments<br />&#160;&#160;&#160;&#160;from dba_registry_history<br />&#160;&#160;&#160;&#160;where version = '********.0'<br />&#160;&#160;&#160;&#160; order by action_time desc;</li>\r\n</ul>\r\n<ul>\r\n<li>To install all generic patches in one opatch run, download all the patch files into a patch directory &lt;patch_dir&gt; on your database server, open a command window and run 'opatch napply &lt;patch_dir&gt;' (without extracting the patch files before).</li>\r\n</ul>\r\n<p><strong>2.1 Patch Collections released for SAP</strong></p>\r\n<p><br /><strong><strong>Patch collections MS Windows x86-64 (64-bit)</strong></strong><br /><br /> PATCHBUNDLE10205P_22-10010776.ZIP (Patch: 16803782)<br /> PATCHBUNDLE10205P_20-10010776.ZIP (Patch: 15848062)<br /><br /><strong><strong>Patch collections for platform MS Windows (32-bit)</strong></strong><br /><br /> PATCHBUNDLE10205P_22-10010774.ZIP (Patch: 16803780)<br /> PATCHBUNDLE10205P_20-10010774.ZIP (Patch: 15848060)<br /><br /><strong><strong>Patch collections for platform MS Windows Itanium (64-bit)</strong></strong><br /><br /> PATCHBUNDLE10205P_22-10010775.ZIP (Patch: 16803781)<br /> PATCHBUNDLE10205P_20-10010775.ZIP (Patch: 15848061)<br /><br />Be aware that you are required to hold a valid Oracle 10.2 Extended Support contract to request, download or deploy ******** patch collections. However, Oracle 10.2 Extended Support is free for Windows on Itanium.</p>\r\n<p><strong>2.2 Generic Windows Patches</strong></p>\r\n<p><br /> P9445675GEN10205P_1-*.ZIP<br /> P9584028GEN10205P_1-*.ZIP<br /> P9843740GEN10205P_1-*.ZIP<br /> P10357961GEN10205P_1-*.ZIP<br /> P13617518GEN10205P_1-*.ZIP</p>\r\n<p><strong>2.3 Change History (Windows)</strong></p>\r\n<p><br /><strong>14. August 2013</strong><br />Patchcollection 22 released for customers (includes CPUJul2013).<br /><br /><strong>14. February 2013</strong><br />Patchcollection 20 released for customers (includes CPUJan2013).<br />This patchcollection is the first released on the new SAP Software Download Center.<br /><br /><strong>13. November 2012</strong><br />Patchcollection 19 released for customers (includes CPUOct2012)<br /><br /><strong>14. August 2012</strong><br />Patchcollection 18 released for customers (includes CPUJul2012)<br /><br /><strong>10. May 2012</strong><br />Patchcollection 16 released for customers (includes CPUApr2012)<br /><br /><strong>13. March 2012</strong><br />Generic patch 10431010 removed<br />Generic patch 13617518 released for customers<br /><br /><strong>27. February 2012</strong><br />Status update for MS Windows Itanium platform<br /><br /><strong>14. February 2012</strong><br />Patchcollection 14 released for customers (includes CPUJan2012)<br /><br /><strong>10. November 2011</strong><br />Patchcollection 12 released for customers (includes CPUOct2011)<br /><br /><strong>17. October 2011</strong><br />Patchcollection 10: patch numbers of patchcollection 10 rectified<br /><br /><strong>10. August 2011</strong><br />Patchcollection 10 released for customers (includes CPUJul2011)<br /><br /><strong>13. July 2011</strong><br />Generic patch 9445675 released for customers<br /><br /><strong>10. May 2011</strong><br />Patchcollection 08 released for customers (includes CPUApr2011)<br /><br /><strong>12. April 2011</strong><br />Generic patch 9843740 released for customers<br /><br /><strong>10. February 2011</strong><br />Patchcollection 05 released for customers (includes CPUJan2011)<br /><br /><strong>25. January 2011</strong><br />******** Patch collections for Windows Itanium are not available.<br /><br /><strong>14. December 2010</strong><br />Patchcollection 04 released for customers<br /><br /><strong>10. November 2010</strong><br />Patchcollection 01 released for customers (includes CPUOct2010)<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA-INS (Installation SAP System)"}, {"Key": "Database System", "Value": "ORACLE"}, {"Key": "Database System", "Value": "Oracle 10.2"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (C5000979)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (C5016411)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001431752/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001431752/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001431752/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001431752/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001431752/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001431752/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001431752/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001431752/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001431752/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "871735", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Current patch set", "RefUrl": "/notes/871735"}, {"RefNumber": "850306", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Critical Patch Update Program", "RefUrl": "/notes/850306"}, {"RefNumber": "839187", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Applying patch set/patches/patch collection", "RefUrl": "/notes/839187"}, {"RefNumber": "839182", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle patch installation with OPatch", "RefUrl": "/notes/839182"}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576"}, {"RefNumber": "819829", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Instant Client Installation and Configuration on Unix or Linux", "RefUrl": "/notes/819829"}, {"RefNumber": "1900628", "RefComponent": "BC-DB-ORA", "RefTitle": "SPU - Oracle Security Patch Update July 2013", "RefUrl": "/notes/1900628"}, {"RefNumber": "1860292", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches April 2013 (Oracle Critical Update Program)", "RefUrl": "/notes/1860292"}, {"RefNumber": "1822955", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches January 2013 (Oracle Critical Update Program)", "RefUrl": "/notes/1822955"}, {"RefNumber": "1790307", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches October 2012 (Oracle Critical Update Program)", "RefUrl": "/notes/1790307"}, {"RefNumber": "1753600", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches July 2012 (Oracle Critical Update Program)", "RefUrl": "/notes/1753600"}, {"RefNumber": "1718792", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches April 2012 (Oracle Critical Update Program)", "RefUrl": "/notes/1718792"}, {"RefNumber": "1684424", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches January 2012 (Oracle Critical Update Program)", "RefUrl": "/notes/1684424"}, {"RefNumber": "1654734", "RefComponent": "BC-DB-ORA", "RefTitle": "Ext Support contract required for ******** PSU, SBP & Client", "RefUrl": "/notes/1654734"}, {"RefNumber": "1651427", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches October 2011 (Oracle Critical Update Program)", "RefUrl": "/notes/1651427"}, {"RefNumber": "1633801", "RefComponent": "BC-DB-ORA", "RefTitle": "DBV check code 6401 when using transportable tablespaces", "RefUrl": "/notes/1633801"}, {"RefNumber": "1619685", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches July 2011 (Oracle Critical Update Program)", "RefUrl": "/notes/1619685"}, {"RefNumber": "1590858", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches April 2011 (Oracle Critical Update Program)", "RefUrl": "/notes/1590858"}, {"RefNumber": "1525673", "RefComponent": "BC-DB-ORA", "RefTitle": "Optimizer merge fix for Oracle ********", "RefUrl": "/notes/1525673"}, {"RefNumber": "1461804", "RefComponent": "BC-DB-ORA", "RefTitle": "Query failed with ORA-00600 [kdsgrp1]", "RefUrl": "/notes/1461804"}, {"RefNumber": "1237838", "RefComponent": "BC-DB-ORA", "RefTitle": "Current version of pre-upgrade information script", "RefUrl": "/notes/1237838"}, {"RefNumber": "1227404", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Configuration Manager (OCM)", "RefUrl": "/notes/1227404"}, {"RefNumber": "1175996", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g/11g patches check", "RefUrl": "/notes/1175996"}, {"RefNumber": "1027012", "RefComponent": "BC-DB-ORA", "RefTitle": "MOPatch - Install Multiple Oracle Patches in One Run", "RefUrl": "/notes/1027012"}, {"RefNumber": "1017936", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Upgrade history", "RefUrl": "/notes/1017936"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "871735", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Current patch set", "RefUrl": "/notes/871735 "}, {"RefNumber": "830576", "RefComponent": "BC-DB-ORA", "RefTitle": "Parameter recommendations for Oracle 10g", "RefUrl": "/notes/830576 "}, {"RefNumber": "1900628", "RefComponent": "BC-DB-ORA", "RefTitle": "SPU - Oracle Security Patch Update July 2013", "RefUrl": "/notes/1900628 "}, {"RefNumber": "1860292", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches April 2013 (Oracle Critical Update Program)", "RefUrl": "/notes/1860292 "}, {"RefNumber": "1822955", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches January 2013 (Oracle Critical Update Program)", "RefUrl": "/notes/1822955 "}, {"RefNumber": "1525673", "RefComponent": "BC-DB-ORA", "RefTitle": "Optimizer merge fix for Oracle ********", "RefUrl": "/notes/1525673 "}, {"RefNumber": "1790307", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches October 2012 (Oracle Critical Update Program)", "RefUrl": "/notes/1790307 "}, {"RefNumber": "819829", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Instant Client Installation and Configuration on Unix or Linux", "RefUrl": "/notes/819829 "}, {"RefNumber": "1753600", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches July 2012 (Oracle Critical Update Program)", "RefUrl": "/notes/1753600 "}, {"RefNumber": "1718792", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches April 2012 (Oracle Critical Update Program)", "RefUrl": "/notes/1718792 "}, {"RefNumber": "1684424", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches January 2012 (Oracle Critical Update Program)", "RefUrl": "/notes/1684424 "}, {"RefNumber": "839182", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle patch installation with OPatch", "RefUrl": "/notes/839182 "}, {"RefNumber": "1027012", "RefComponent": "BC-DB-ORA", "RefTitle": "MOPatch - Install Multiple Oracle Patches in One Run", "RefUrl": "/notes/1027012 "}, {"RefNumber": "1633801", "RefComponent": "BC-DB-ORA", "RefTitle": "DBV check code 6401 when using transportable tablespaces", "RefUrl": "/notes/1633801 "}, {"RefNumber": "1654734", "RefComponent": "BC-DB-ORA", "RefTitle": "Ext Support contract required for ******** PSU, SBP & Client", "RefUrl": "/notes/1654734 "}, {"RefNumber": "1651427", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches October 2011 (Oracle Critical Update Program)", "RefUrl": "/notes/1651427 "}, {"RefNumber": "1619685", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches July 2011 (Oracle Critical Update Program)", "RefUrl": "/notes/1619685 "}, {"RefNumber": "1590858", "RefComponent": "BC-DB-ORA", "RefTitle": "CPU Patches April 2011 (Oracle Critical Update Program)", "RefUrl": "/notes/1590858 "}, {"RefNumber": "1461804", "RefComponent": "BC-DB-ORA", "RefTitle": "Query failed with ORA-00600 [kdsgrp1]", "RefUrl": "/notes/1461804 "}, {"RefNumber": "1175996", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10g/11g patches check", "RefUrl": "/notes/1175996 "}, {"RefNumber": "1237838", "RefComponent": "BC-DB-ORA", "RefTitle": "Current version of pre-upgrade information script", "RefUrl": "/notes/1237838 "}, {"RefNumber": "839187", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 10.2.0: Applying patch set/patches/patch collection", "RefUrl": "/notes/839187 "}, {"RefNumber": "1017936", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: Upgrade history", "RefUrl": "/notes/1017936 "}, {"RefNumber": "1227404", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Configuration Manager (OCM)", "RefUrl": "/notes/1227404 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}