{"Request": {"Number": "1109743", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 242, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016404492017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001109743?language=E&token=5E3D6F65323FF1F4FBACD38EC2C079D9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001109743", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001109743/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1109743"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 42}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.06.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1109743 - Use of Index Key (Prefix) Compression for Oracle Databases"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Database is using too much disk space for indexes.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>disk space, performance, compression, disk I/O, Index, prefix compression</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ul>\r\n<li>Index Key (Prefix) Compression is supported in SAP environments from Oracle Release 10.2 on.</li>\r\n<ul>\r\n<li>Index Key Compression is not maintained after drop/create of an index through the SAP application. A rebuild of an index (for instance through BR*Space) maintains the index key compression. &#160;An index rebuild is necessary to enable Index Key Compression after an index was dropped and recreated.</li>\r\n</ul>\r\n<li>Index Key Compression is only available for B*Tree indexes and not for Bitmap Indexes.</li>\r\n<li>Index Key Compression is part of the Oracle Database Enterprise Edition. Therefore no additional licenses or options needed.</li>\r\n<li>Please make sure that your PSAPTEMP Temporary Tablespace is at least as big as the biggest index to rebuild. Without sufficient temporary tablespace the index rebuild may fail.</li>\r\n<li>Please check the free space of the tablespace containing the index to be rebuilt. As a rule of thumb you should have enough free space in the tablespace to store the index to be rebuilt twice.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Use Index Key Compression by rebuilding existing B*Tree indexes in the database to store B*Tree indexes as space efficient as possible.<br /><br /><strong>What is key compression?</strong><br /><br />Key compression allows repeating values for columns in a B*Tree index to be replaced by shorter tokens in the index blocks. Key compression looks at the values of the leading columns of the index and replaces repeating values by shorter tokens. To get the maximum benefit out of key compression you need to compute the optimal mix of the length of the repeating values in the leading columns of an index and the selectivity of these repeating values in the leading part of an index.<br /><br /><span style=\"text-decoration: underline;\">What are the advantages and disadvantages of key compression?</span></p>\r\n<ul>\r\n<li>Saves disk space for indexes and reduces total database size on disk</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Customer experiences show that up to 75% less disk space is needed for key compressed indexes. Even after index reorganisations have taken place an additional up to 20% total disk space reduction for the whole database can be achieved using index compression. Without any reorganizations done before the total space savings for the complete database may be higher than 20% using index compression as index compression implicitly reorganizes any index.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Real world example:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The size of the of index 'GLPCA&#126;1' index was reduced from 18GB to 4.5GB.</p>\r\n<ul>\r\n<li>Reduces physical disk I/O and logical buffer cache I/O improving buffer cache quality</li>\r\n</ul>\r\n<ul>\r\n<li>Higher CPU consumption</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Every compression technique comes with higher CPU consumption. The higher CPU consumption is more than compensated by doing less logical I/O for index blocks in the database buffer cache.</p>\r\n<ul>\r\n<li>Improved overall database throughput</li>\r\n</ul>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Early customer experiences have shown a 10-20% better database throughput for an SAP system by using index key compression in a non CPU bound environment.</p>\r\n<p>To make index key compression work two steps need to be done:<br /><br /><span style=\"text-decoration: underline;\">Step 1:</span> Compute the number of the leading columns of an index to deliver the highest compression rate<br /><br /><span style=\"text-decoration: underline;\">Step 2:</span> Rebuild the index with the optimal number of the leading columns to be compressed.<br /><br />There is alternative to step 1 for ERP/ECC customers. Based on experience from many customers two sample scripts have been created which do contain the SQL statements to compress the typical large indexes of an ERP/ECC system.<br />The first script \"IX_COMP_TYPICAL_ONLINE.txt\" may be used while the SAP system is up. The other script \"IX_COMP_TYPICAL_OFFLINE.txt\" can only be used when the SAP system is down. The offline version rebuilds indexes faster and requires less temp space in PSAPTEMP.<br />Please rename \"IX_COMP_TYPICAL_ONLINE.txt\" to \"IX_COMP_TYPICAL_ONLINE.sql\" or \"IX_COMP_TYPICAL_OFFLINE.txt\" to \"IX_COMP_TYPICAL_OFFLINE.sql\" and execute the script by calling sqlplus.<br /><br />Example:<br />sqlplus sapsr3/sap<br />start IX_COMP_TYPICAL_ONLINE <br /><br />For non-ERP/ECC systems or for compressing additional indexes the following steps need to be executed.<br /><br />A PL/SQL package \"ind_comp.sql\" is provided as an attachment to this note which performs both steps.<br /><br /><br /><span style=\"text-decoration: underline;\">Installation of PL/SQL package \"ind_comp.sql\"</span><br />Step 1:<br />Select the Output Directory and create it in Oracle:<br /><br />sqlplus<br />connect / as sysdba<br />create directory \"&#126;IND_COMP_DIR\" as '&lt;os directory&gt;';<br />grant read, write on directory \"&#126;IND_COMP_DIR\" to &lt;sapr3&gt;;<br /><br />Step 2.<br />Rename attached file from \"ind_comp.txt\" to \"ind_comp.sql\".<br /><br />Step 3.<br />Login into the Oracle database with the sapsid user and compile the PL/SQL Package.<br /><br />Example:<br />sqlplus sapsr3/sap<br />start ind_comp<br /><br /><span style=\"text-decoration: underline;\">How to use PL/SQL package \"ind_comp\" ?</span><br /><br />The function get_column of the PL/SQL package accepts three parameters:<br />ind_comp.get_column (table info, space, opmode, parallel_degree, tablespace_name);</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Parameter</th><th>Description</th></tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>table info</td>\r\n<td>You can specify either a table name or a</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>number of tables.</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>space</td>\r\n<td>Default value is FALSE not to generate space</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>queries before and after the index is</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>changed.</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>opmode</td>\r\n<td>Default value is ONLINE generating only</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>INDEX REBUILD commands therefore ignoring all</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>partitioned indexes. When specifying the</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>value OFFLINE all partitioned indexes will be</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>dropped and created. Using OFFLINE requires</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>downtime of the SAP system for the index</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>creation phase.</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>parallel_degree</td>\r\n<td>Degree of parallelisation for queries of the</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>ind_comp package and for the generated</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>compression commands. Default value is 4.</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>Use this parameter if you want to change the</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>degree of parallelism.</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>tablespace_name</td>\r\n<td>Name of the tablespace where the compressed</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>indexes will be created. If the tablespace</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>name is not specified, the indexes will be</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>created in the same tablespace as they were</td>\r\n</tr>\r\n<tr>\r\n<td>&nbsp;</td>\r\n<td>before.</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><br />Examples:</p>\r\n<ul>\r\n<li>exec ind_comp.get_column('GLPCA');<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This computes the optimal compression factor for all indexes of the table GLPCA.</li>\r\n<li>exec ind_comp.get_column('GLPCA', true);<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This computes the optimal compression factor for all indexes of the table GLPCA. For each index of the table GLPCA the size before and after the compression is generated.</li>\r\n<li>exec ind_comp.get_column(10);<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This computes the optimal compression factor for all non-partitioned indexes of the 10 largest tables.</li>\r\n<li>exec ind_comp.get_column(10, parallel_degree =&gt; 10);<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This computes the optimal compression factor for all non-partitioned indexes of the 10 largest tables, but uses a parallelization factor of 10.</li>\r\n<li>exec ind_comp.get_column('/BIC/B0000980000', opmode =&gt; 'OFFLINE');<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This computes the optimal compression factor for all partitioned indexes on the table /BIC/B0000980000.</li>\r\n<li>exec ind_comp.get_column(10, opmode =&gt; 'OFFLINE');<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This computes the optimal compression factor for all partitioned and non-partitioned indexes of the 10 largest tables. This scenario should be used for SAP BW systems.</li>\r\n<li>exec ind_comp.get_column(10, true, 'OFFLINE');<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;This computes the optimal compression factor for all partitioned and non-partitioned indexes of the 10 largest tables. Additional queries for and after the compression are being generated.</li>\r\n</ul>\r\n<p>Example: &#160;&#160;&#160;&#160;&#160;&#160;<br />sqlplus sapsr3/sap<br />&#160;&#160;&#160;set serveroutput on size 1000000<br />&#160;&#160;&#160;exec ind_comp.get_column('RFBLG');<br /><br /><strong>Processing table RFBLG</strong><br /><strong>\"RFBLG&#126;0\" compress 2;</strong><br /><br />An additional output is written to an ASCII file in the /tmp directory on UNIX systems. On Windows it is the directory C:\\TEMP. The file has the name of the table name specified as input parameter to the get_column function of the ind_comp package. In the above example you will find the ASCII file \"GLPCA_IX_COMP.sql\" in the /tmp directory.<br /><br />This file can be used in a seperate sqlplus session to rebuild the index with the optimal compression factor. Never rebuild an index during high load times of the system.<br /><br />Example: &#160;&#160;&#160;&#160;&#160;&#160; <br />sqlplus sapsr3/sap<br />&#160; start GLPCA_IX_COMP<br /><br /><br />In addition the package \"ind_comp\" creates a summary file \"IX_COMP.sql\" in the /tmp directory where all index rebuilds and index<br />create statements for all partitioned and non-partitioned tables are stored.<br /><br /><span style=\"text-decoration: underline;\"><strong><strong>CAUTION:</strong></strong></span><br /><strong>Partitioned indexes cannot be rebuild and must be recreated. Therefore the output file \"IX_COMP.sql\" created by the \"ind_comp\" package in 'OFFLINE' mode can only be executed during the downtime of the SAP system !</strong><br /><br />The execution of the PL/SQL package to compute the optimal index compression factor can be done at any time and requires no downtime i.e. you can run ind_comp.get_column while the SAP system is up.<br /><br /><strong>It is advised not to run the package during peak times of the SAP system !</strong><br /><br /><span style=\"text-decoration: underline;\">Possible reasons, why compression does not show any or small space savings</span></p>\r\n<ul>\r\n<li>High UNIFORM value for locally managed tablespaces. If UNIFORM is used then a value of 10MB is sufficient. As an alternative do not use UNIFORM but instead use AUTOALLOCATE.</li>\r\n<li>High INITIAL, NEXT and PCTINCREASE values for dictionary managed tablespaces. Adjust accordingly.</li>\r\n<li>High INITIAL_EXTENT value for index to be compressed. Previous index reorganisations can create the situation that an index has only one large initial extent. In this case the initial extent size should be changed by adding the storage clause to the rebuild index statement.<br /><br />Example:<br />alter index \"RFBLG&#126;0\" rebuild online compress 2 parallel 4 pctfree 1 storage (initial 1m);<br /><br /></li>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Database System", "Value": "ORACLE"}, {"Key": "Database System", "Value": "Oracle 10"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (C5025128)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (C5000979)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001109743/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109743/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109743/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109743/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109743/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109743/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109743/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109743/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109743/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "IX_COMP_ONLINE_TYPICAL.TXT", "FileSize": "10", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000778782007&iv_version=0042&iv_guid=58453DE4ACA3AA46932D797634A61D9F"}, {"FileName": "IX_COMP_OFFLINE_TYPICAL.TXT", "FileSize": "9", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000778782007&iv_version=0042&iv_guid=A37A9ADE843AA54CB80905A38AD689C5"}, {"FileName": "ind_comp.txt", "FileSize": "39", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000778782007&iv_version=0042&iv_guid=B7BE6312FC2BAC40AE05584E57C64FE0"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "974876", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Transparent Data Encryption (TDE)", "RefUrl": "/notes/974876"}, {"RefNumber": "912620", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle indexes", "RefUrl": "/notes/912620"}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268"}, {"RefNumber": "701235", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Usage of Oracle compression and BW", "RefUrl": "/notes/701235"}, {"RefNumber": "334224", "RefComponent": "BC-DB-ORA", "RefTitle": "Important notes for creating indexes", "RefUrl": "/notes/334224"}, {"RefNumber": "1818320", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g Advanced Compression for LONG data restriction", "RefUrl": "/notes/1818320"}, {"RefNumber": "1464156", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for index compression in BRSPACE 7.20", "RefUrl": "/notes/1464156"}, {"RefNumber": "1436352", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 11g Advanced Compression for SAP Systems", "RefUrl": "/notes/1436352"}, {"RefNumber": "1289494", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle compression", "RefUrl": "/notes/1289494"}, {"RefNumber": "105047", "RefComponent": "BC-DB-ORA", "RefTitle": "Support for Oracle functions in the SAP environment", "RefUrl": "/notes/105047"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2138262", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 12c Advanced Compression for SAP Systems", "RefUrl": "/notes/2138262 "}, {"RefNumber": "1915634", "RefComponent": "SCM-BAS-MD-INC", "RefTitle": "Poor performance with many or huge FFF classes, PSPs", "RefUrl": "/notes/1915634 "}, {"RefNumber": "1436352", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 11g Advanced Compression for SAP Systems", "RefUrl": "/notes/1436352 "}, {"RefNumber": "974876", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Transparent Data Encryption (TDE)", "RefUrl": "/notes/974876 "}, {"RefNumber": "1289494", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle compression", "RefUrl": "/notes/1289494 "}, {"RefNumber": "105047", "RefComponent": "BC-DB-ORA", "RefTitle": "Support for Oracle functions in the SAP environment", "RefUrl": "/notes/105047 "}, {"RefNumber": "1464156", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Support for index compression in BRSPACE 7.20", "RefUrl": "/notes/1464156 "}, {"RefNumber": "334224", "RefComponent": "BC-DB-ORA", "RefTitle": "Important notes for creating indexes", "RefUrl": "/notes/334224 "}, {"RefNumber": "1818320", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g Advanced Compression for LONG data restriction", "RefUrl": "/notes/1818320 "}, {"RefNumber": "828268", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Database 10g: New functions", "RefUrl": "/notes/828268 "}, {"RefNumber": "912620", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle indexes", "RefUrl": "/notes/912620 "}, {"RefNumber": "701235", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Usage of Oracle compression and BW", "RefUrl": "/notes/701235 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}