{"Request": {"Number": "2234970", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 298, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018194632017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002234970?language=E&token=51E66190140464C6585205B1CF77E2BE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002234970", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002234970/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2234970"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.06.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-UTL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Workbench Utilities"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Workbench Utilities", "value": "BC-DWB-UTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-UTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2234970 - Job EU_INIT"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The background job EU_INIT is started. This obstructs ongoing operations and takes up system resources. The job EU_INIT is scheduled repeatedly even though this is not desired.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Where-used list, SE84, navigation index, TBOM, environment analysis, CDMC, WBCROSSGT, SAPRSEUB, SAPRSEUC, EU_INIT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>None</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Information about the job EU_INIT:</p>\r\n<p>The job compiles the where-used list index in the system.&#x00A0;This index is required for the functions of the where-used list or environment analysis in the system. There are also other applications that access this information, for example the TBOM function in Solution Manager and the Custom Code Analyzer (see SAP Note 2185390).</p>\r\n<p>In the job EU_INIT, programs are investigated for the use of other objects using compiler functions. This is stored in an index to enable fast searching. The index consists of several tables.</p>\r\n<p>Either the program SAPRSEUC or SAPRSEUB runs behind the EU_INIT job. For more information, see SAP Note 18023. The program SAPRSEUC only analyzes customer-defined objects. The program SAPRSEUB analyzes the entire system (all existing programs). The runtime, index size, and required system resources differ accordingly. For more information about this, see SAP Note 2039618.</p>\r\n<p>As a general rule, only the index for customer objects is initially compiled automatically in a customer system. This takes place using the program SAPRSEUC. Using SE80, a job called EU_INIT is automatically scheduled. The program SAPRSEUC runs behind this. Scheduling is also possible using the program SAPRSEUJ as described in SAP Note 18023.</p>\r\n<p>If the index should also be available for all SAP objects, the customer must actively execute the program SAPRSEUB in the background. SAP Note 28022. This starts a more complex index build for all objects - SAP objects and customer objects. An additional run of SAPRSEUC is not required. This status of the index - whether just customer objects or all objects have been analyzed - is saved.&#x00A0;When the build is started again or the index is updated, this is done for the program set that was current in the last run as stored in this status. This means that if the entire index has been compiled once, updates also take place for all objects again.</p>\r\n<p><strong>If you use an MSSQL database, you must implement SAP Note 1554667 before starting it; otherwise, database problems occur<span style=\"font-size: medium; font-family: Arial;\">. </span></strong></p>\r\n<p>If updates are required from SAP side such as new object types in the system, an update is started automatically. For this reason, the job might be started automatically again.</p>\r\n<p>If the entire index has been compiled using SAPRSEUB, the index build must be updated again following the import of a Support Package or an upgrade, since new or changed SAP objects have reached the system.&#x00A0;This does not take place automatically. In this case, the customer must actively start the program SAPRSEUB again. If the index is active for customer objects only, no action is required.</p>\r\n<p>Generally, the job must run only in the systems in which you require a where-used list of objects. As a rule, this applies only to development systems and systems in which other applications that need the index run - Solution Manager, for example.</p>\r\n<p>The job must only run once. Due to its long runtime, it is frequently not possible for the job to complete in one day. Alternatively, it might need to be stopped for other reasons (system resources, for example). This is not a problem because the job has an automatic restart mechanism.<br />The rule is as follows: If the job has completely finished once, an end indicator is set in the system. The job EU_PUT that runs daily checks whether this end indicator is set or whether EU_INIT is still running. If EU_INIT is not currently running and the end indicator is not set, EU_PUT reschedules EU_INIT for 20:00 on the current day. The job EU_INIT continues working at the point at which it was stopped.<br />If the job EU_INIT obstructs ongoing operations, it can therefore be stopped without causing any problems, since it is restarted automatically in the evening. Please note the following: If a full build of the index is started with the program SAPRSEUB and this is terminated or stopped, a restart is scheduled with the name EU_INIT, too.<br /><br />If you do not require the job EU_INIT to be scheduled or restarted, you can achieve this by executing the program SAPRSEUB_STOP.&#x00A0;This program sets the end indicator for the job; automatic start is then deactivated. In addition, the status of the index is reset to indicate that only customer objects are analyzed. If the job is stopped using this program, the index is not complete. In this system, it is then no longer possible to guarantee that the where-used list and other analysis functions will work properly. This should only be done in systems in which the index is not required or if a full build has been started and the status needs to be reset.</p>\r\n<p>The restart mechanism remains switched off until the job EU_INIT is directly restarted by a user.</p>\r\n<p>If the job was started accidentally or the index is not required in the system - for example, in a production system - the content of the index can be deleted, too. This releases the required resources. For more information about this, see SAP Note 2039618. We do not recommend deletion in a development system.</p>\r\n<p>As of Release 7.50 or SAP Note 1949236, the function module RS_WHERE_USED_LIST_CHECK_STATE is available. You can use this to find out about the current status of the index build. To do so, execute the function module in the test environment of the function module library.</p>\r\n<p>The return values mean the following:</p>\r\n<p>CHECK_RC: 0 means that the index is current; 1 means that an update is recommended. The index is not current. This value must be interpreted in conjunction with the other return values.</p>\r\n<p>CUS: 'X' The index was compiled for all customer objects.</p>\r\n<p>SAP: 'X' The index was compiled for all objects (SAP + customer objects).</p>\r\n<p>IS_RUNNING: 'X' The index build is currently running. A job is active.</p>\r\n<p>PROGNAME: Last analyzed program. Analysis status. The analysis is performed in accordance with the alphabetical order of the programs.</p>\r\n<p>UNKNOWN: 'X' The up-to-dateness status of the index could not be determined. This is the case in older releases and after the implementation of SAP Note 1949236. It means that certain status values have not yet been stored, so the current status cannot be determined. CHECK_RC is 1 in this case. An update is recommended.</p>\r\n<p>VERSION_WRONG = 'X' The index version is incorrect. An update is definitely required. CHECK_RC = 1.</p>\r\n<p>UPGRADE_OR_SP = 'X' There are new objects in the system. A Support Package has been imported or an upgrade has been carried out. CHECK_RC = 1. An update is only required if SAP = 'X', meaning that the entire index is active.</p>\r\n<p>&#x00A0;SDATE and STIME: Start date and start time of the last run</p>\r\n<p>&#x00A0;FDATE and FTIME: End date and end time of the last run</p>\r\n<p>In the case of older releases, the values SDATE,&#x00A0;STIME,&#x00A0;FDATE, and FTIME are not filled, since these values were not stored.&#x00A0;Once the job has run again, these values are available. 1</p>\r\n<p>&#x00A0;</p>\r\n<p><br />&#x00A0;<br /> <br /><br /></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DWB (ABAP Workbench, Java IDE and Infrastructure)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D030328)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002234970/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002234970/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002234970/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002234970/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002234970/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002234970/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002234970/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002234970/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002234970/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "759407", "RefComponent": "BC-DWB-UTL", "RefTitle": "EU_INIT job is started repeatedly/you want to stop this job", "RefUrl": "/notes/759407"}, {"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022"}, {"RefNumber": "2228460", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Runtime of job EU_INIT, SAPRSEUB, performance", "RefUrl": "/notes/2228460"}, {"RefNumber": "2185390", "RefComponent": "BC-DWB-CEX", "RefTitle": "Custom Code Analyzer", "RefUrl": "/notes/2185390"}, {"RefNumber": "2039618", "RefComponent": "BC-DWB-TOO", "RefTitle": "Size of table WBCROSSGT", "RefUrl": "/notes/2039618"}, {"RefNumber": "1984730", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Job EU_INIT should be stopped", "RefUrl": "/notes/1984730"}, {"RefNumber": "1949236", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Start of job EU_INIT following upgrade or SP import; WBCROSSGT", "RefUrl": "/notes/1949236"}, {"RefNumber": "1923499", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "STATIC or Semi-dynamic TBOM has a very small content", "RefUrl": "/notes/1923499"}, {"RefNumber": "1917231", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Runtime improvement for where-used list; job EU_INIT, table WBCROSSGT", "RefUrl": "/notes/1917231"}, {"RefNumber": "18023", "RefComponent": "BC-DWB", "RefTitle": "Jobs EU_INIT, EU_REORG, EU_PUT", "RefUrl": "/notes/18023"}, {"RefNumber": "1766550", "RefComponent": "BC-DWB-TOO", "RefTitle": "Where Used inconsistent index update on multiple servers", "RefUrl": "/notes/1766550"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2801600", "RefComponent": "BC-DWB-UTL", "RefTitle": "How to stop the Where-Used List", "RefUrl": "/notes/2801600 "}, {"RefNumber": "2733460", "RefComponent": "BC-DWB-UTL", "RefTitle": "Program SAPRSEUC also compiles where-used list index for SAP objects", "RefUrl": "/notes/2733460 "}, {"RefNumber": "2516597", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Support troubleshooting guide: where-used-list - Internal Use Only", "RefUrl": "/notes/2516597 "}, {"RefNumber": "2185390", "RefComponent": "BC-DWB-CEX", "RefTitle": "Custom Code Analyzer", "RefUrl": "/notes/2185390 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}