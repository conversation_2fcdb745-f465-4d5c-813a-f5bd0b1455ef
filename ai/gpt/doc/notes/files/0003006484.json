{"Request": {"Number": "0003006484", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 199, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003006484?language=E&token=E59576944831E24375EBD35AD5175D6C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003006484", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3006484"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.12.2020"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-MON-BPM-DCM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Consistency Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "3006484 - RC_COLLECT_ANALYSIS_DATA ends with dump DATA_OFFSET_NEGATIVE"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ul>\r\n<li>The report RC_COLLECT_ANALYSIS_DATA for the SAP Readiness Check 2.0 for SAP S/4HANA cancels with dump DATA_OFFSET_NEGATIVE, when you select \"Interface\" in the Scope Selection for Discovery Phase.</li>\r\n<li>Job log&#160; details :</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Job started<br />Step 001 started (program RC_COLLECT_ANALYSIS_DATA, variant XXX, user ID XXX)<br />Start to collect analysis data<br />Target S/4HANA PPMS stack: XXXXXXXXX<br />Job TMW_RC_BPA_DATA_COLL is scheduled to collect Business Process Analytics data.<br />Job TMW_RC_HANAS_DATA_COLL is scheduled to collect HANA Sizing data.<br />Job TMW_RC_DVM_DATA_COLL is scheduled to collect Data Volume Management data.<br />Job TMW_RC_SITEM_DATA_COLL is scheduled to collect Simplification Item relevance check data<br />Job TMW_RC_INNOVA_DATA_COLL is scheduled to collect Innovation Potential data.<br />Start to collect Transaction Usage (ST03N) data<br />Start to collect transaction object directory information<br />Transaction object directory information collected<br />Transaction Usage (ST03N) data is collected<br />Start to collect Business Warehouse Extractor data<br />Business Warehouse Extractor data is collected<br />Start to collect Inerface data<br />Internal session terminated with a runtime error DATA_OFFSET_NEGATIVE (see ST22)&#160;Job cancelled</p>\r\n<ul>\r\n<li>In ST22, you can see this dump:</li>\r\n</ul>\r\n<p style=\"padding-left: 60px;\">Category&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;ABAP programming error<br />Runtime Errors&#160; &#160; &#160; &#160; &#160; &#160; &#160;DATA_OFFSET_NEGATIVE<br />Except.&#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; CX_SY_RANGE_OUT_OF_BOUNDS<br />ABAP Program&#160; &#160; &#160; &#160; &#160; &#160; &#160; /SSA/EIM<br />Application Component SV-SMG-MON-BPM</p>\r\n<p style=\"padding-left: 60px;\">Error in the ABAP Application Program</p>\r\n<p style=\"padding-left: 60px;\">The current ABAP program \"/SSA/EIM\" had to be terminated because it has come across a statement that unfortunately cannot be executed.</p>\r\n<p style=\"padding-left: 60px;\">Error analysis<br />An exception has occurred which is explained in more detail below.<br />The exception is assigned to class 'CX_SY_RANGE_OUT_OF_BOUNDS' and was not caught in procedure&#160;\"EXTRACT_BLACKLIST_RELEASE\" \"(FORM)\", nor was it propagated by a RAISING clause.<br />Since the caller of the procedure could not have anticipated this exception, the current program was terminated.<br /> The reason for the exception is: <br />In the current program \"/SSA/EIM\", an attempt was made to access field \"IV_S4HANA_REL\"using offset \"-4\".<br />Subfield access with a negative offset is not allowed however.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<p id=\"\">SAP ERP</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<ol>\r\n<li>You have started the job via the menu item \"Program -&gt; Execute in Background\" instead of via button \"Schedule Analysis\".</li>\r\n<li>Your user is missing authorizations.</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<ol>\r\n<li>Execute the program using button \"Schedule Analysis\".</li>\r\n<li>The user executing the program must have authorization object SM_BPM_DET with the characteristic of OBJECT_MS&#160;for all collected KPI&#180;s.</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<ul>\r\n<li>Make sure that the status of the notes is \"Completly Implemented\" and the latest version of the notes is used:</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><a target=\"_blank\" href=\"/notes/2745851\">2745851</a> - Business Process Improvement Content for SAP Readiness Check 2.0 and Next Generation SAP Business Scenario Recommendations<br /><a target=\"_blank\" href=\"/notes/2758146\">2758146</a> - SAP Readiness Check 2.0 &amp; Next Generation SAP Business Scenario Recommendations.</p>\r\n<ul>\r\n<li>Make sure you implement the newest version of ST-A/PI collective SAP Notes as indicated in <a target=\"_blank\" href=\"/notes/2745851\">2745851</a>.</li>\r\n</ul>\r\n"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SCS-S4R (SAP Readiness Check)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I043942)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I043942)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003006484/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003006484/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003006484/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003006484/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003006484/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003006484/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003006484/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003006484/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003006484/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2758146", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA & Process Discovery (evolution of SAP Business Scenario Recommendations) or SAP Innovation and Optimization Pathfinder", "RefUrl": "/notes/2758146"}, {"RefNumber": "2745851", "RefComponent": "SV-SMG-MON-BPM-ANA", "RefTitle": "Business Process Improvement Content for \"SAP Readiness Check 2.0\"/\"Process Discovery (evolution of SAP Business Scenario Recommendations)\"/\"SAP Innovation and Optimization Pathfinder\"/\"SAP Signavio Process Insights-Discovery\"", "RefUrl": "/notes/2745851"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP ERP 6.0"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}