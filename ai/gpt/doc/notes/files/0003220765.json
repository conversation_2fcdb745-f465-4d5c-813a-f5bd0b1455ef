{"Request": {"Number": "0003220765", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 181, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003220765?language=E&token=CEC46B92534A1C9B9EB2D0AC0E2443B1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003220765", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003220765/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3220765"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.07.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SCS-S4R"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Readiness Check"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "3220765 - Dump DBSQL_CONVERSION_ERROR when executing the report RC_COLLECT_ANALYSIS_DATA"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Job&#160;RC_COLLECT_ANALYSIS_DATA cancels due to the following error:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"1\" cellspacing=\"0\" style=\"width: 848px; height: 49px;\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>Start to collect SLT replications data<br />Internal session terminated with a runtime error DBSQL_CONVERSION_ERROR (see ST22)&#160; &#160; </p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>In the meanwhile, the following short dump appears when executing the report&#160;RC_COLLECT_ANALYSIS_DATA for Readiness Check,</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"1\" cellspacing=\"0\" style=\"width: 1129px; height: 555px;\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>Category ABAP programming error<br />Runtime Errors DBSQL_CONVERSION_ERROR<br />Except. CX_SY_OPEN_SQL_DB<br />ABAP Program RC_COLLECT_ANALYSIS_DATA<br />Application Component SV-SMG-CM</p>\r\n<p>Short Text<br />Conversion not possible.</p>\r\n<p>Error analysis&#160; &#160;<br />......<br />Either the conversion is not supported for the target field type, the&#160;target field is too small to take the value, or the data does not have&#160;the required format for the target field. The value to be converted is&#160;\"4646186579 \". Field \"???\" in the select list was just being edited.&#160;The table&#160;field has ABAP type 8 length 8 with 0 decimal places. The ABAP&#160;field has type \"I\" length 4 with 0 decimal places.</p>\r\n<p>Source Code Extract</p>\r\n<p>| 2559| if sy-subrc = 0. |<br />| 2560| &lt;ls_result&gt;-counter = &lt;ls_triggers_counter&gt;-counter. |<br />| 2561| else. |<br />| 2562| clear lv_counter. |<br />| 2563| |<br />|&gt;&gt;&gt;&gt;&gt;| select count(*) |<br />| 2565| into lv_counter |<br />| 2566| from (&lt;lv_value&gt;).</p>\r\n<p>Contents of system fields</p>\r\n<p>|SY-TITLE|SAP Readiness Check for SAP S/4HANA - Collect Analysis Data |<br />|SY-MSGTY|I |<br />|SY-MSGID|00 |<br />|SY-MSGNO|001 |<br />|SY-MSGV1|Start to collect SLT replications data</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<p>ABAP system for Readiness Check</p>\r\n<p>Version 77 or lower of Note <a target=\"_blank\" href=\"/notes/2758146\">2758146</a> is implemented</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<p>Executing report&#160;&#160;RC_COLLECT_ANALYSIS_DATA in se38</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p id=\"\">Program error due to the data conversion</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>Please download and implement the version 78 or higher of Note <a target=\"_blank\" href=\"/notes/2758146\">2758146</a> in snote.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p id=\"\">&#160;DBSQL_CONVERSION_ERROR;&#160;CX_SY_OPEN_SQL_DB;&#160;RC_COLLECT_ANALYSIS_DATA;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I058882)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I071739)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003220765/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003220765/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003220765/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003220765/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003220765/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003220765/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003220765/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003220765/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003220765/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2758146", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA & Process Discovery (evolution of SAP Business Scenario Recommendations) or SAP Innovation and Optimization Pathfinder", "RefUrl": "/notes/2758146"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP S/4HANA 1909"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 3.0, "Quality-Votes": 1, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 1, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}