{"Request": {"Number": "0003236443", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 282, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001516972022"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003236443?language=E&token=191DFA0549F7251AEE3D9C7E23C0DC32"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003236443", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003236443/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3236443"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 24}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2024-01-17"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SCS-S4R"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Readiness Check"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Support Technology Cloud Services", "value": "SV-SCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Readiness Check", "value": "SV-SCS-S4R", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SCS-S4R*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3236443 - SAP Readiness Check for SAP Cloud ALM"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are either considering or planning to move your SAP Solution Manager 7.2 system completely or partially to SAP Cloud ALM. As a result, you want to analyze your existing SAP Solution Manager system with the SAP Readiness Check tool for SAP Cloud ALM, to help scope and plan your project.</p>\r\n<p>SAP Readiness Check for SAP Cloud ALM supports the analysis of SAP Solution Manager 7.2 systems.</p>\r\n<p>This SAP Note provides guidance on how to implement and execute SAP Readiness Check for SAP Cloud ALM. Additionally, it provides answers to frequently asked questions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Readiness Check; SAP Cloud ALM; SAP Readiness Check for SAP Cloud ALM; RC_ALM_ANALYSIS_DATA_COLLECTION; /SDF/RC_ALM_COLLECT_DATA; CALM</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>To run SAP Readiness Check for SAP Cloud ALM, data collectors are required to gather master and transactional data, as well as important customizing settings and application-relevant KPIs. However, only statistical data and no transactional data is exported.</p>\r\n<p>To install the data collection framework and the associated collectors, implement this SAP Note in your SAP Solution Manager system or update the ST-PI component to Support Package 21 or higher. We recommend implementing or updating this Note in your system before executing a new analysis to always run with the latest updates.</p>\r\n<p>Please note that the collector program /SDF/RC_ALM_COLLECT_DATA will only run in an SAP Solution Manager 7.2 system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong><em>Execution of the Data Collectors</em></strong></p>\r\n<p>To execute the data collectors, proceed as follows:</p>\r\n<ol start=\"1\">\r\n<li>Execute the program /SDF/RC_ALM_COLLECT_DATA via transaction SA38 in the productive client of the production system to schedule and download the data archive by performing the following three steps:</li>\r\n<ol start=\"1\">\r\n<li>Choose&#160;<em>Schedule Analysis</em>&#160;to schedule the master job to collect data.</li>\r\n<li>Once the data collection jobs finish, choose the&#160;<em>Download Analysis Data</em>&#160;option from within the program /SDF/RC_ALM_COLLECT_DATA.</li>\r\n<li>If analysis data has been already collected and you want to trigger a new collection, choose <em>Delete Buffered Analysis Data</em> before scheduling a new analysis.</li>\r\n</ol>\r\n<li>Optional: At this point, feel free to review the content of the archive file. The data is presented in a human-readable format.</li>\r\n</ol>\r\n<p><strong><em>Upload the Collected Data</em></strong></p>\r\n<p>To upload the collected data, take the following steps:</p>\r\n<ol start=\"1\">\r\n<li>Launch the landing page for the <a target=\"_blank\" href=\"https://me.sap.com/readinesscheck\">SAP Readiness Check cloud application</a>.</li>\r\n<li>Choose&#160;<em>Start New Analysis</em>.</li>\r\n<li>Provide a name for the analysis, locate the data archive file that was generated from the program&#160;<strong>/SDF/RC_ALM_COLLECT_DATA</strong>, review and acknowledge the Terms of Use and Disclaimer, and then choose&#160;<em>Create</em>.</li>\r\n<li>After a short period of time, usually less than 5 minutes, the status of the analysis will change from&#160;<em>In Preparation</em>&#160;to&#160;<em>Available</em>. You will need to refresh your browser or choose the&#160;<em>Refresh</em>&#160;icon on the page to get the updated status.</li>\r\n<li>Once the analysis is&#160;<em>Available</em>, you can open the analysis to find an interactive dashboard where you can explore the results of the analysis. The&#160;<em>Learn More</em>&#160;side panel and the embedded help capabilities can assist you as you explore and start to capture the next steps.</li>\r\n</ol>\r\n<p><strong>Note:</strong></p>\r\n<ul>\r\n<li>The program /SDF/RC_ALM_COLLECT_DATA will trigger a background job. You can check the background job log to ensure that the data was collected properly. To open the job log, select the Display Analysis Log option.</li>\r\n<li>The same user that triggers the collection analysis with the program /SDF/RC_ALM_COLLECT_DATA is set in the background job. Therefore, this user needs to have sufficient authorizations to read the analyzed data. Attached to this note is a template role with the required authorizations. You can upload this role via transaction PFCG and copy it into your system to assign it to the user executing the analysis.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong><em>Frequently Asked Questions</em></strong></p>\r\n<p><strong>1. What is SAP Readiness Check for SAP Cloud ALM?</strong></p>\r\n<p>SAP Readiness Check for SAP Cloud ALM is a self-service solution to help customers assess the current usage of an existing SAP Solution Manager 7.2 system in preparation for the transition to SAP Cloud ALM. The results provided by this analysis will provide information about the current state of equivalent capabilities in SAP Cloud ALM and additional information to support the transition activities.</p>\r\n<p><strong>2. On which system and client shall SAP Readiness Check for SAP Cloud ALM be executed?</strong></p>\r\n<p>SAP Readiness Check for SAP Cloud ALM should be executed in the SAP Solution Manager 7.2 production system (or in a recent copy of the productive system) and must be run in the productive client.<br />The analysis is client dependent. If you have more than one productive client, the analysis needs to be performed for every client.</p>\r\n<p><strong>3. What authorization is required to perform SAP Readiness Check for SAP Cloud ALM?</strong></p>\r\n<p>The user ID executing the analysis job must have Execute (16) rights in the <em>Activity</em> authorization field within authorization object S_DEVELOP. It must also have additional authorizations required to gather the analyzed data to complete successfully. We recommend executing the collection using the predelivered SOLMAN_ADMIN user or a user with equivalent authorizations. As a template, a role is attached to this Note with the complete required authorization objects and values, and it can be used to upload it to your system.</p>\r\n<p><strong>4. What SAP Support Portal authorizations are required to create a new analysis and/or view SAP Readiness Check for SAP Cloud ALM analysis results?</strong></p>\r\n<p><a target=\"_blank\" href=\"/notes/3310759\">SAP Note 3310759 - Revised Authorization Concept for SAP Readiness Check</a>&#160;details the S-user authorization requirements to access the SAP Readiness Check cloud application.</p>\r\n<p><strong>5. Who from SAP has access to my SAP Readiness Check for SAP Cloud ALM result set?</strong></p>\r\n<p>By default, only SAP Readiness Check development and support teams have access to the analysis results. When creating the analysis session, customers have the choice to allow all SAP employees access to the results. Alternatively, customers can selectively add individuals from SAP and/or partner entities by manually adding email addresses in the <em>Settings</em> function in the dashboard. Lastly, customers can also share the generated offline version of the results as they wish.</p>\r\n<p><strong><em>Delivery</em></strong></p>\r\n<p>Use SAP&#8217;s Note Assistant to implement the correction instructions or implement Support Package 21 of ST-PI component. You can find more information about Note Assistant in <strong>SAP Support Portal</strong>, under <a target=\"_blank\" href=\"https://support.sap.com/en/my-support/knowledge-base/note-assistant.html\">Note Assistant</a>.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I333244)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I816576)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003236443/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003236443/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003236443/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003236443/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003236443/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003236443/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003236443/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003236443/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003236443/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Z_RC_CALM_TEMPLATE.SAP", "FileSize": "25", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900001354892022&iv_version=0024&iv_guid=00109B36BC761EDEAD8E840983230C20"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3335093", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP Cloud ALM - troubleshooting guide", "RefUrl": "/notes/3335093"}, {"RefNumber": "3374561", "RefComponent": "SV-SMG-CM", "RefTitle": "Runtime error SQL_CAUGHT_RABAX is raised in job RC_ALM_ANALYSIS_DATA_COLLECTION - Change Request Management", "RefUrl": "/notes/3374561"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "ST-PI", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74023INSTPI", "URL": "/supportpackage/SAPK-74023INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74024INSTPI", "URL": "/supportpackage/SAPK-74024INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74025INSTPI", "URL": "/supportpackage/SAPK-74025INSTPI"}, {"SoftwareComponentVersion": "ST-PI 740", "SupportPackage": "SAPK-74026INSTPI", "URL": "/supportpackage/SAPK-74026INSTPI"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "ST-PI", "NumberOfCorrin": 8, "URL": "/corrins/0003236443/212"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 8, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "ST-PI", "ValidFrom": "740", "ValidTo": "740", "Number": "3236443 ", "URL": "/notes/3236443 ", "Title": "SAP Readiness Check for SAP Cloud ALM", "Component": "SV-SCS-S4R"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}