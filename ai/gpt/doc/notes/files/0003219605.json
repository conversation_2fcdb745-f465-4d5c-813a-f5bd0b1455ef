{"Request": {"Number": "0003219605", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 408, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000906882022"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003219605?language=E&token=AE2E0D2CB4711417EB297FF69ABAFB3D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003219605", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3219605"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.07.2022"}, "SAPComponentKey": {"_label": "Component", "value": "FIN-MIG-AA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data migration for Asset Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financials", "value": "FIN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Simple Finance data migration", "value": "FIN-MIG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-MIG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data migration for Asset Accounting", "value": "FIN-MIG-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FIN-MIG-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3219605 - RC FDQ AA: false positive error message FIN_AA_RECON 764, wrong ledger checked"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Reconciliation Report for Asset Accounting is run via Financial Data Quality check included in&#160;Readiness Check for SAP S/4HANA. Error message FIN_AA_RECON 764 - \"ANLC values without general ledger assignment in the open year\" is displayed within transaction code&#160;FIN_AA_CORR_DISPLAY.</p>\r\n<p>It&#160;is a false positive; the corresponding FI-GL Balance Carry Forward has been performed to same or higher fiscal year as FI-AA Balance Carry Forward.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">Readiness Check, RC, Financial Data Quality, FDQ, Reconciliation Report for Asset Accounting,&#160;Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion, FIN_AA_CORR_RECONCILE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Program error: in some cases, AA irrelevant&#160;ledgers might be checked.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p id=\"\">Implement the attached correction instructions or the corresponding Support Package.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SCS-S4R (SAP Readiness Check)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I520275)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I548179)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003219605/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003219605/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003219605/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003219605/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003219605/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003219605/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003219605/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003219605/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003219605/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "618", "To": "618", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60035", "URL": "/supportpackage/SAPKH60035"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60225", "URL": "/supportpackage/SAPKH60225"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60324", "URL": "/supportpackage/SAPKH60324"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60425", "URL": "/supportpackage/SAPKH60425"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60522", "URL": "/supportpackage/SAPKH60522"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60631", "URL": "/supportpackage/SAPKH60631"}, {"SoftwareComponentVersion": "SAP_FIN 617", "SupportPackage": "SAPK-61725INSAPFIN", "URL": "/supportpackage/SAPK-61725INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 618", "SupportPackage": "SAPK-61819INSAPFIN", "URL": "/supportpackage/SAPK-61819INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 2, "URL": "/corrins/0003219605/15841"}, {"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 7, "URL": "/corrins/0003219605/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 9, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "2896400 ", "URL": "/notes/2896400 ", "Title": "Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "616", "Number": "3026403 ", "URL": "/notes/3026403 ", "Title": "FIN_AA_CORR_DISPLAY:  false positive error message FIN_AA_RECON 764", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "2896400 ", "URL": "/notes/2896400 ", "Title": "Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "2896400 ", "URL": "/notes/2896400 ", "Title": "Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "2896400 ", "URL": "/notes/2896400 ", "Title": "Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "2896400 ", "URL": "/notes/2896400 ", "Title": "Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "606", "ValidTo": "606", "Number": "2896400 ", "URL": "/notes/2896400 ", "Title": "Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "616", "ValidTo": "616", "Number": "2896400 ", "URL": "/notes/2896400 ", "Title": "Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "617", "ValidTo": "617", "Number": "2896400 ", "URL": "/notes/2896400 ", "Title": "Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "617", "ValidTo": "618", "Number": "3026403 ", "URL": "/notes/3026403 ", "Title": "FIN_AA_CORR_DISPLAY:  false positive error message FIN_AA_RECON 764", "Component": "FIN-MIG-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "618", "ValidTo": "618", "Number": "2896400 ", "URL": "/notes/2896400 ", "Title": "Reconciliation of Asset Accounting Inconsistencies in ECC prior to S/4HANA Conversion", "Component": "FIN-MIG-AA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}