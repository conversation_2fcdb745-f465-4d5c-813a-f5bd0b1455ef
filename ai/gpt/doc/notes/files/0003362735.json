{"Request": {"Number": "0003362735", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 237, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003362735?language=E&token=D2584707DF93E39342A0ADA7353C07F1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003362735", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3362735"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.08.2023"}, "SAPComponentKey": {"_label": "Component", "value": "CA-TRS-PRCK"}, "SAPComponentKeyText": {"_label": "Component", "value": "S/4HANA Conversion Pre-Checks"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "3362735 - Simplification Item catalog can not be fetched from SAP through SAPOSS RFC connection"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You would like to run&#160;Simplification Item Check as per SAP Note&#160;<a target=\"_blank\" href=\"/notes/2399707\">2399707</a> - Simplification Item Check but you encouter an error while trying to download the content.</p>\r\n<p>The error message is: \"Simplification Item catalog can not be fetched from SAP through SAPOSS RFC connection. Action canceled\"</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<p>Executing report RC_COLLECT_ANALYSIS_DATA or RC_BW_COLLECT_ANALYSIS_DATA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>SAPOSS is no longer functional as the connectivity to this has been shut down on 08.01.2020 (\"<a target=\"_blank\" href=\"/notes/2874259\">Support Backbone Update</a>\"). You can find more information from SAP Note&#160;<a target=\"_blank\" href=\"/notes/2836302\">2836302</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>Refer to SAP Note&#160;<a target=\"_blank\" href=\"/notes/2882166\" title=\"2882166  - Issues in Check Framework and SAP Readiness Check after Support Backbone Update\">2882166 - Issues in Check Framework and SAP Readiness Check after Support Backbone Update</a>&#160;which provides information to fix the root cause of the issue.</p>\r\n<p>In general implement&#160;newest release&#160;SAP Note&#160;<a target=\"_blank\" class=\"_e75a791d-denali-editor-page-rtfLink\" href=\"/notes/2399707\">2399707</a>&#160;and make sure SAP-SUPPORT_PORTAL connection exist and is functional.</p>\r\n<p>In case the download does not work anyway, connections to SAP Support Backbone do not exist or you prefer a quick workaround so follow below steps:</p>\r\n<p>1. Open Simplification Item Catalog content from&#160;<a target=\"_blank\" href=\"https://me.sap.com/sic/overview\">https://me.sap.com/sic/overview</a>&#160;with your S-User.<br />2. Click \"Download Simplification Item\" icon to download the content to your local in a whole.<br />3. For S/4 HANA, execute the check framework report&#160;/SDF/RC_START_CHECK. Click the \"Upload Simplification Item Catalog from file\" button, upload the content from your local.<br />&#160; &#160; For BW/4 HANA, execute RC_BW_COLLECT_ANALYSIS_DATA. Click \"Goto\"-&gt;\"Upload Target BW/4HANA Version from file\". Choose the zip you've already downloaded.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p id=\"\"><a target=\"_blank\" href=\"/notes/2951527\">2951527</a> - Dropdown list empty for target release S/4 HANA or BW/4 HANA</p>\r\n<p><a target=\"_blank\" href=\"https://i71.wdf.sap.corp/sap/support/notes/2968380\">2968380</a>&#160;- SAP Readiness Check Report 2.0 - troubleshooting guide</p>\r\n"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SCS-S4R (SAP Readiness Check)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D073590)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D073590)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003362735/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003362735/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003362735/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003362735/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003362735/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003362735/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003362735/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003362735/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003362735/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}