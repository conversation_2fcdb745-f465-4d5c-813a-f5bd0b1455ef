
<h1>Discover</h1>
<p>In the Discover Phase, customers become familiar with the benefits of SAP HANA and SAP BW/4HANA, and the benefits it can bring to customers’ business. Figure: Activities in the Discover Phase The phase is structured into two parts: In a first step many customers want to (re)define their analytics strategy, which includes the User Experience component (‘frontend analysis tools’) as well as the backend part e.g. with a data warehouse. In this step customers want to evaluate the role of SAP BW/4HANA in a newly redefined analytics landscape. Customers who have already participated In the SAP BW/4HANA Discovery Workshop already have a functional and technical overview and may have defined their specific approach. The Analytics Strategy Workshop works out potential customer-specific road map options. In addition, Max Attention customers who would like to address the topic of data and data management holistically, may start with the service component Data Management Solution Architecture and Roadmap. In a second step a trial system could be provisioned to support the impact evaluation. For trial systems there are two versions available in the SAP Cloud Appliance Library: SAP BW/4HANA 2021 SP04 Developer Edition SAP BW/4HANA 2021 SP04 To learn about the current SAP BW/4HANA release, it is recommended to look at the central release information/restriction note. The current SAP BW/4HANA version is SAP BW/4HANA 2023 SP00. The SAP notes for SAP BW/4HANA 2023 SP00 are provided in the accelerator section. Regarding SAP BW/4HANA releases and the availability of landscape transformation tools supporting the data transformation to SAP BW/4HANA, you may check the product road map listed in the accelerator section.</p>
<div>
	<h2>Strategic Planning</h2>
	<p>SAP commits long-term support for SAP BW/4HANA until 2040. End of mainstream maintenance for the different releases are: SAP BW/4HANA 2.0: 31/12/2024 SAP BW/4HANA 2021: 31/12/2027 SAP BW/4HANA 2023: 31/12/2030. A new release version of SAP BW/4HANA is planned for 2026. SAP BW/4HANA remains a key solution in SAP’s portfolio and will be improved in the hybrid capabilities. You may safeguard your investments by retaining mature and complex on-premises systems or decide to move to the private cloud in support of the RISE with SAP strategy. Note: As a (functionaly limited) alternative in the public cloud to SAP BW/4HANA, SAP offers the SAP Datasphere, SAP BW Bridge. See the task “Discover the value of SAP Datasphere, SAP BW bridge” for details. SAP BW/4HANA 2023 comes with the following innovation updates: Administration Object synchronization between SAP bW/4HANA and SAP BW Bridge Enhancements Data Transfer Process (DTP) Analytic Authorizations for InfoObjects via ODATA Messaging Request Houskeeping Tasklist Data Protection &amp; Privacy Support of Datastore Objects (advanced) type Data Mart Propagation of anonymized data New formula editor for Anonymization Rules Support update of anonymized data into Cold Store targets Enhancements Modeling Additional Re-modeling capabilities for Datastore Object User Interface Structure Overview and Notes in SAP BW Query Designer Preview functionality Anonymization Copy filter definitions in Data Transfer Processes Package Explorer Data Transfer Process – Copy&amp;Paste Data Transfer Process – Enhanced Process Chain Monitoring Available Paths/Scenarios to SAP BW/4HANA There are three major paths/scenarios to SAP BW/4HANA: Path 1: New implementation: For those who would like to implement a new SAP BW/4HANA system by either migrating a legacy system or by running a net-new installation of SAP BW/4HANA. Path 2: System conversion: For those who want to run a conversion of an existing SAP Business Warehouse system to SAP BW/4HANA. There are three types available for this scenario: 2a: In-Place Conversion 2b: Remote Conversion with parallel operations 2c: Shell Conversion For the In-Place Conversion and the Remote Conversion, classic BW objects (e.g. InfoCubes, classic DSO, BEx Report) need to be migrated to the related new HANA-optimized object type. If your current SAP landscape has been highly maintained (SAP BW powered by SAP HANA and mainly usage of new HANA-optimized BW object types), you most likely can convert to SAP BW/4HANA in just a few steps. For the Shell Conversion, neither master nor transaction data are transferred. This allows for optimized modeling and a re-design of the data models. Only selected data models and flows are transferred to target SAP BW/4HANA system using transports. Path 3: System Consolidation: For those who would like to consolidate their existing data warehouse landscape or carve out selected data models or flows into an existing SAP BW/4HANA system. System Consolidation can be conducted within a customer-specific project with SAP. Challenges of a Remote Conversion There are some challenges to be considered for creating a parallel productive SAP BW/4HANA system: Challenges SAP BW/4HANA system shall be used productively for system conversion (risk &amp; runtime mitigation) Both systems shall be operated in parallel temporarily, hence data supply must be synchronized Delta management in the source systems must be made aware of the new SAP BW/4HANA system Source systems must not have any downtime (data booking must not be interrupted) Solution Delta queue cloning and synchronizing Requirements and Constraints It is recommended to run this activity upfront of an SAP BW/4HANA implementation project and in case customers want to rethink their analytics strategy. Procedure Check the Conversion Readiness of the current SAP Business Warehouse Run an Analytics Strategy Workshop. This service component describes the main components of the SAP Analytics strategy from UI as well as from a data warehousing perspective, and works out potential customer-specific road map options. Decide on the innovation strategy and a high-level analytics road map Define the strategic analytics road map and value case Results As a result, there should be A customer specific 3-5-year roadmap for analytics and/or data warehouse approach, which has been commonly worked out and agreed in the workshop main change activities and sequences on the defined roadmap Key values from business as well as from IT perspective supporting this roadmap Demo scenarios for the one or other scenario in BW/4, SAP Lumira or other involved components. How SAP can support SAP offers 2 overview workshops to get familiar with SAP BW/4HANA: Discovery Workshop for SAP BW/4HANA The discovery workshop for SAP BW/4HANA helps customers identify crucial benefits and suggested approaches for their road to SAP BW/4HANA in order to leverage latest functional and technical innovations and move forward to the future-proof, scalable and high-performance enterprise data warehousing platform by SAP. It helps to evaluate possible transition scenarios towards SAP BW/4HANA for increased predictability, identify areas of added value due to functional and technical innovations and ensure security of investment by planning the road to the future data warehousing platform. During a preparation phase relevant customer information will be gathered. If applicable, SAP will not only analyze the information gathered from the customer but also the existing system itself by conducting various checks inside the customer system. After all relevant information is gathered and the customer requirements have been identified the workshop material will be adjusted according to the outcome of the preparation phase. The 2-day on-site workshop itself will cover all of the topics of relevance that have been identified. Areas of interest might be the following: SAP BW/4HANA Roadmap SAP BW/4HANA functional overview and technical innovations SAP BW/4HANA in combination with SAP S/4HANA Transition paths to SAP BW/4HANA and offered system conversion tools Prerequisites for SAP BW/4HANA and results of system analysis and pre-checks Business use cases and SAP BW/4HANA business content Analytics with SAP BW/4HANA using SAP BusinessObjects Cloud and SAP BusinessObjects Design Studio Viable recommendation for the next steps on the road to SAP BW/4HANA The Discovery Workshop for SAP BW/4HANA is not part of any SAP Value Assurance service package, however it is not free of charge. Customers should contact the local SAP sales representative for ordering details. Architecture Point of View – SAP HANA data warehousing (including SAP BW/4HANA and SAP Datasphere) With the Architecture Point of View about SAP HANA Data Warehousing, customers get support in an early phase of a project to define one or several customer specific roadmap options for Data Warehousing. Main topics in this APoV are: SAP Intelligent Data Warehouse, SAP BW/4HANA, SAP HANA SQL Data Warehousing, SAP Datasphere and integration aspects with Data Lakes. The Architecture Point of View services are part of the Value Assurance services.</p>
	<div>
		<h3>Check the Conversion Readiness of the current SAP Business Warehouse</h3>
		<p>There could be show stoppers (e.g. incompatible Add-Ons installed in the existing SAP BW system) or requirements that may have impact on your conversion project. The objective of this task is to identify all items that could have a major project impact as early as possible, so as to be able to adapt the overall project plan accordingly. Prerequisites This task is in particular important for System Conversion and Landscape Transformation scenarios (in case they start with a system conversion). Procedure Check the required release levels and minimum Support Packages of the original system for the selected conversion type ins SAP Note 3365187 - Information/Restriction Note for Release SAP BW/4HANA 2023. SAP recommends to check the following areas, and to document the results: Are the technical requirements fulfilled (software levels, Unicode…)? Database migration to SAP HANA On what release levels are the source systems (connections with ODP 1.0 or ODP 2.0) How much custom code needs to be adapted? How many simplification items are relevant and have to be processed Results The results document of this task describes the implications of SAP BW/4HANA which need to be addressed either before starting the transition project, or as part of the transition project. How SAP Can Support To provide customers a better view on the implications of a transition to SAP BW/4HANA, SAP has created a SAP Readiness Check for SAP BW/4HANA self-service. This version of SAP Readiness Check for SAP BW/4HANA provides the following insights: Compatibility of BW objects (SAP BW 7.30 or higher) Readiness of connected source systems (SAP BW 7.30 or higher) Compatibility of installed add-ons Compatibility of custom code SAP HANA sizing A comprehensive overview of all simplifications for SAP BW/4HANA, compared to classic SAP Business Warehouse products, is captured in the simplification list linked in SAP Note 2421930 – Simplification List for SAP BW/4HANA. For a given customer, only a limited number of simplification items from this extensive list will be applicable. SAP Readiness Check for SAP BW/4HANA provides customers with a better view of the results for their specific system landscapes when converting to SAP BW/4HANA. Figure: SAP Readiness Check for SAP BW/4HANA Dashboard SAP Readiness Check for SAP BW/4HANA supports all releases that are suitable for conversion to SAP BW/4HANA: Shell conversion for SAP BW 7.0 or higher on Any-DB Remote conversion for SAP BW 7.30 or higher on Any-DB In-Place conversion for SAP BW 7.50 or higher on SAP HANA The Checks in Detail Object Compatibility: Identified BW objects in the analyzed system and recommendations on required activities for the conversion. Figure: Object Compatibility in SAP Readiness Check for SAP BW/4HANA The Object Compatibility check determines the BW objects that are compatible with SAP BW/4HANA and those objects that are no longer available. The check will also identify those objects that can be automatically converted, those that will be deleted, and those requiring manual adjustment. The check then provides links to the documentation of the corresponding simplification items, that is, the matching SAP Notes of the SAP BW/4HANA simplification list. Custom Code Compatibility: SAP BW/4HANA compatibility of custom code in the analyzed system, including recommendations on changes required for the conversion. Figure: Custom Code Analysis in SAP Readiness Check for SAP BW/4HANA Due to changes introduced to BW objects and the simplification of various other BW-related ABAP function modules, classes, reports, exits, interfaces, variables, and other objects in SAP BW/4HANA, customers need an overview of the adjustments required to their custom code developments to make their system and solutions compatible with SAP BW/4HANA. Understanding the specific implications on the analyzed system is key to a successful conversion project. The Custom Code Compatibility check provides information about custom code in the analyzed system, which might not be compatible with SAP BW/4HANA. The charts at the top of the page show an overview of potential issues. The chart on the left shows the issues grouped by the BW area they were found in, that is, the BW object the custom code is related to, and the chart on the right groups the issues by type, across all areas. In the table below the charts, you can switch between views, which show all findings grouped by area. Source System Readiness: Evaluation of the source systems, that is, those systems that are connected to the analyzed SAP BW system, with respect to their readiness to connect to SAP BW/4HANA. Figure: Source System Readiness in SAP Readiness Check for SAP BW/4HANA With SAP BW/4HANA, SAP simplifies the inbound interfaces that are used to load data into the SAP BW/4HANA system to ODP, SAP HANA, and file source systems. Systems that were previously connected by using other technologies (for example, Myself system, service API (SAPI), DBConnect, or UDConnect) need to be adapted to fulfill this requirement. The Source System Readiness check provides an overview of the source systems that are connected to your SAP BW system and their compatibility with SAP BW/4HANA. The identified source systems are assigned to one of the following status categories: Ready for ODP 2.0: The source system is compatible with ODP 2.0. Ready for ODP 1.0: The source system is compatible with ODP 1.0, but the limitations mentioned in SAP Note 2481315 apply. Adjustment Required: The source system is not ready for ODP yet or is using a different technology, which is not available anymore with SAP BW/4HANA. System Failure: The RFC call to the source system failed so we are unable to provide any information. Please check the content of the Comments Add-On Compatibility: Compatibility of add-ons installed in the analyzed SAP BW system. SAP BW/4HANA does not support all add-ons that are supported by SAP BW 7.x. For example, all consolidation and planning functionalities are moved to the SAP Business Planning and Consolidation, version for SAP BW/4HANA add-on. Figure: Add-on Compatibility in SAP Readiness Check for SAP BW/4HANA The Add-On Compatibility check provides an overview of the add-ons that are installed in the analyzed system. The identified add-ons are assigned to one of the following categories: Compatible: The add-on is compatible with SAP BW/4HANA. Validation Required: The add-on needs to be validated for compatibility with SAP BW/4HANA. A follow-up with the vendor (SAP or third party) is required. Incompatible: The add-on is incompatible with SAP BW/4HANA. In case of an in-place conversion, it needs to be uninstalled. Database Sizing: Sizing estimation for the target SAP BW/4HANA system. The Database Sizing check for SAP BW/4HANA provides guidance on how to determine a suitable SAP HANA database sizing configuration for the target system, based on the information collected in the analyzed system. The check supports SAP BW systems running on SAP HANA or any other database supported by SAP. Figure: Database Sizing in SAP Readiness Check for SAP BW/4HANA The Current Database section shows the data distribution by BW object type in the database of the analyzed system in more detail. The Recommended SAP HANA Sizing for New System section contains the recommended SAP HANA database size, number of nodes, and CPU power. The table below lists the most relevant sizing figures that were found by the sizing report in the analyzed system. This information can be used to identify data reduction potential, supporting you to reduce the target database size and to clean up the current system. Typically, large quantities of data stored in Persistent Staging Areas (PSAs), change logs, and/or control or custom tables indicate data deletion potential. Data Collection Preparation For the preparation steps of the data collection, refer to the requirements section of SAP Note 3061594 which is the central note for SAP Readiness Check for SAP BW/4HANA. The preparation of the data collection includes the deployment of several SAP Notes. This step is supported by the notes analyzer functionality with an upload file attached to SAP Note 257059. If you wish to include information about necessary custom code adaptations, you will first need to run the report RS_B4HANA_CODE_SCAN. Unlike the other checks, the Custom Code Compatibility check does not actually perform an analysis itself but rather processes the results of this report. Please schedule the report to run in the background as it may take a long time. Once the job has completed, you can prepare the results as described in SAP Note 3010910. Data Collection To schedule the data collectors, proceed as follows: Execute RC_BW_COLLECT_ANALYSIS_DATA via transaction SA38 in the productive client of the to-be-analyzed system (to scope and plan a project, it is recommended to analyze the production system) and perform the following steps: Select the checks to be included in the analysis. Figure: Data Collection for SAP Readiness Check for SAP BW/4HANA Choose Schedule Analysis to schedule the master job to collect data. Once the data collection jobs have finished, choose the Download Analysis Data option from within the program RC_BW_COLLECT_ANALYSIS_DATA. [Optional] At this point, please feel free to review the content of the archive file. The data is presented in a human-readable format and can be manually masked if required for security purposes. Upload the collected data to SAP Readiness Check available in SAP for Me (in the Service &amp; Support area, ALM section). Please see the accelerator section for the landing page of the SAP Readiness Check, which contains all important information on preparation, execution, and analysis. If you face any technical issues please open a message in component SV-SCS-S4R.</p>
		<h3>Define an Innovation Strategy and a High-Level Analytics and Data Management Road Map</h3>
		<p>The objective of this task is to create a company specific innovation strategy for analytics, and to derive the high-level multi-year road map from there. Procedure To define the analytics road map, you will need to clarify questions like: What are the business requirements for analytics? What are pain points in the current setup for analytics? Are there foreseeable new analytics business scenarios, which need to be covered in the near future? Which SAP components up to now are utilized for the analytics landscape? What is the maintenance level of the current landscape (outdated release versions of the involved SAP products?) Are there major changes planned in the OLTP landscape or other analytics-related components, which might influence the current analytics landscape (e.g. SAP S/4HANA implementation)? Results As a result, there is a high-level multi-year road map which describes the main change activities and sequences them on a high-level time-line. How SAP Can Support SAP provides several services in the Discover phase that help to enhance the customer’s analytics strategy and redefine the vision to help ensure it is futureproof to support future business. The Analytics &amp; Data Management Vision Workshop provides insight into the driving factors influencing customers around Data Management strategies. Industry trends &amp; concepts such as ‘Cloud Data Lakes’ and ‘Embedded Machine Learning’ will be explored and how these new concepts can be met with SAP solutions in the area of Data Management. This service is available for MaxAttention customers. In addition, Max Attention customers who would like to address the topic of data and data management holistically, may start with the service component Data Management Solution Architecture and Roadmap instead. This service component comes with three scope options where customers can pick from: Data Strategy Framework, which creates a business outcome driven data strategy which fits to your corporate strategy Data Architecture supports the creation of a data process matrix, meta data catalog, and application and data distribution diagram IT Architecture for Data identifies the optimal deployment of SAP Data Management tools. The service component creates a road map sequencing the individual change activities on a timeline. The Analytics Strategy Workshop helps to define a future-proven analytics and data management architecture based on the business and IT requirements, ready for the Intelligent Enterprise. It includes topics such as SAP S/4HANA embedded analytics, data warehousing, analytic frontends, and integration of data lakes, covering on-premise and cloud solutions. (The Analytics Strategy Workshop is explained in detail as a separate task in this activity). The Analytics Strategy Workshop is available as a service component in the Value Assurance service “Planning the Digital Transformation” and as a service component in the new SAP MaxAttention service “Architecture Transformation”. This focus package shows an overview of all recommended and optional services which support the conversion to SAP BW/4HANA (some services are only available under a MaxAttention contract). Figure: PE Focus package for conversion to SAP BW/4HANA</p>
		<h3>Understand the Benefits and Functionality of SAP Analytics Cloud</h3>
		<p>Understand the concept and how to work with SAP Analytics Cloud. Procedure Make yourself familiar with SAP Analytics Cloud with the help of the webinars and guided playlists on the SAP Analytics Cloud homepage https://www.sapanalytics.cloud. You may also check the information offered in the Release Navigator for SAP Analytics Cloud (see accelerator section for details). How SAP Can Support SAP offers the service “Architecture Point of View – SAP Analytics Cloud and Frontend tools” which provides a very good introduction to understand the benefits of SAP Analytics Cloud. The &quot;Enablement Service for SAP Analytics Cloud&quot; is a PS service which includes Overview and Architecture of SAP Analytics Cloud Data Connectivity Data Load – Excel to SAP Analytics Cloud Modeling Management of SAP Analytics Cloud User Administration of SAP Analytics Cloud SAP Enterprise Support customers can refer to the learning journey for SAP Analytics Cloud.</p>
		<h3>Understand the Benefits of SAP Datasphere</h3>
		<p>SAP Datasphere is the next generation of SAP Data Warehouse Cloud and delivers a unified experience for data integration, data cataloging, semantic modeling, data warehousing, data federation, and data virtualization. For a closer look into SAP Datasphere, refer to the article “A deeper Look Into How SAP Datasphere Enables a Business Data Fabric” by Juergen Mueller (see accelerator section). SAP Enterprise Support customers can refer to the learning journey for SAP Datasphere. You may also check the information offered in the Release Navigator for SAP Datasphere (see accelerator section for details).</p>
		<h3>Understand the Benefits of SAP Datasphere, SAP BW bridge</h3>
		<p>Moving to SAP Datasphere, SAP BW bridge, could be an alternative or a successor step to moving to SAP BW/4HANA and is mentioned here to support an informed decision how to move forward with your analytics strategy. SAP Datasphere, SAP BW bridge provides customers who are running SAP Business Warehouse or SAP BW/4HANA with access to the public cloud as it offers SAP BW capabilities directly in SAP Datasphere: The connectivity and business content for SAP BW-based data integration (extractors) from SAP ECC and S/4HANA The SAP Business Warehouse layer for loading data with partitioning, monitoring, and error handling, all tailored to the needs of your company SAP Datasphere, SAP BW bridge has the following key qualities: It offers SAP BW capabilities directly in SAP Datasphere: Connectivity and Business Content providing proven SAP BW-based data integration (Extractors) from SAP ECC and S/4HANA Enterprise-ready staging layers of SAP BW for managing data loading with partitioning, monitoring, error handling It allows reuse for business continuity: Leverage SAP BW data structures, transformations, customizations, and skills – quickly extending your SAP BW investments to the public cloud Connect with confidence: Integrate with on-premises SAP ERP data with familiar connectivity and semantic flexibility – retaining instant access while expanding your analytics depth Innovate with cloud agility: Empower your business to rapidly innovate on SAP BW data with an open, unified data &amp; analytics cloud service – scaling innovation and efficiency in the cloud Customers can convert from SAP BW directly or from SAP BW/4HANA (both on-premises or as private cloud edition) to SAP Datasphere, SAP BW bridge: A high-level description of the conversion to SAP Datasphere, SAP BW bridge, is provided in the Run phase of this SAP BW/4HANA roadmap in the activity “Improve and innovate solution”. Target Scenarios for SAP BW bridge Greenfield with SAP Legacy Sources Customers building a new data warehouse in the cloud with SAP Legacy systems as data sources that will only be migrated to a cloud-based system in the future. Expecting the same level of data integration, and convenience functions as known from SAP BW/4HANA. Conversion of SAP BW 7.x to SAP BW/4HANA 2023 Customers with an SAP BW (SAP BW 7.3 and upwards, any DB) moving their data warehouse to the cloud expecting to retain their data, and their legacy data flows, but renovating their data consumption layer with SAP Analytics Cloud or 3rd party clients on top, and expanding their data footprint to cloud, and non-SAP sources. Hybrid with SAP BW/4HANA (on premise or private cloud) and SAP Datasphere, SAP BW bridge Customers with an on-premise SAP BW/4HANA looking for a path into the cloud for their data warehouse workload. Starting with hybrid scenarios for consumption to combine SAP BW/4HANA data, and SAP Datasphere data, and then easily moving more and more of the SAP BW/4HANA data flows to the cloud, and successively transition them to modern SAP Datasphere data ingestion approaches. With SAP BW/4HANA 2023, an object synchronization between SAP BW/4HANA and SAP BW bridge is possible. For a very informative overview of SAP Datasphere, SAP BW bridge, technical details, and the description of a new implementation scenario, refer to the blog “SAP Datasphere, SAP BW bridge: Overview and Technical Deep Dive”. A high-level description of the conversion to SAP Datasphere, SAP BW bridge, will be provided in the Run phase of this roadmap in the activity “Improve and innovate solution”. SAP BW bridge is based on the same codeline as SAP BW/4HANA and can therefore be combined very easily. This means that data models can slowly be removed from SAP BW/4HANA at your own pace and transferred to the SAP BW bridge. The synchronization option allows data models to be overwritten multiple times in the target system if changes in the original data flow in SAP BW/4HANA had to be implemented later. After the respective data model has been completely adopted and kept in sync, the original data flow in SAP BW/4HANA can be switched off and the data supply for the respective reports comes via SAP Datasphere with SAP BW bridge.</p>
		<h3>Run an Analytics Strategy Workshop</h3>
		<p>In the Analytics Strategy Workshop service component SAP supports customers who are considering renewing their Analytical Frontend and Data Warehousing strategy to get a holistic overview of the topics that are key in the decision process for a new Analytics setup. Key aspects of this workshop are: Provide comprehensive information about the SAP Analytics Strategy Assess the customer’s as-is data warehouse and BI frontend architecture, business requirements and IT/Analytics strategy Work out together with the customer the SAP recommended target solutions from product component perspective Define a high-level analytics road map for the next 1-3 years The workshop provides a holistic overview of the main topics for the transition, including a high-level overview about the transition approaches, tools, sizing and technical dependencies. Integration and orchestration is another important topic which has to be discussed early in the project. Outcome of the workshop should be one or several roadmap options with brief description of the key mile-stones and high-level time-lines. The workshop also gives an overview about possible conversion paths to SAP BW/4HANA, which is usually part of the strategic analytics road map discussion. The delivery approach of the Analytics Strategy Workshop service component can be split into 4 major steps: Figure 1.5: Steps of the Analytics Strategy Workshop Details on the Analytics Strategy Workshop service component can be found in the accelerators section.</p>
		<h3>Discover the Value of SAP Business Planning and Consolidation (BPC) version for SAP BW/4HANA</h3>
		<p>SAP Business Planning and Consolidation (SAP BPC) is available since June 2017 – SAP Business Planning and Consolidation 11.0, version for SAP BW/4HANA. The newest release is SAP Business Planning and Consolidation 2021, version for SAP BW/4HANA, this version is compatible with SAP BW/4HANA 2021 and SAP BW/4HANA 2023. How SAP can Support In the Discovery phase, SAP BPC can be included in an Analytics Strategy Workshop (see task Run an Analytics Strategy Workshop), including the following topics according to the customer’s needs: Overview of SAP BPC BPC Standard versus Embedded Model Conversion Scenarios</p>
		<h3>Check the Value of available SAP BW/4HANA Content Add-ons</h3>
		<p>Business Content for SAP BW∕4HANA combines enterprise data warehouse capabilities of SAP BW∕4HANA with explorative and interactive real-time analytics using the SAP HANA in-memory database. In addition to SAP Business Planning and Consolidation for SAP BW/4HANA, several other add-ons are available which replace current add-on functionality in SAP BW. Make yourself familiar with the available add-ons in the SAP help portal.</p>
		<h3>Create a Strategic Road Map &amp; Value Case</h3>
		<p>The objective of this task is to derive the customer specific road map for implementing SAP BW/4HANA considering the findings from previous tasks. This task may be covered in the Analytics Strategy Workshop or in the post processing phase, depending on the complexity of the customer situation. Prerequisites The definition of the road map is based on: Key findings from previous tasks Identified architecture impact Consideration of parallel initiatives and projects Procedure The following steps should be considered (recommended step sequence): Develop the building blocks (such as functional, organizational or technical steps) required to reach the defined target architecture Perform a transformation maturity assessment to identify the ability of the customer to execute the transformation e.g. related to organizational change, budget, technical capabilities and project experiences Determine the risks of transformation Compose a consolidated road map of building blocks to implement the target solution landscape Agree on requirements &amp; conditions for the business case calculation Define KPIs, and link them to the identified value drivers Determine the value driver Determine the cost driver Upload all documents to the SAP Solution Manager so information can be shared across the project team. Results There is a documented implementation road map and a value case available. How SAP Can Support In the Analytics Strategy Workshop described in the tasks before a first decision should be made if the customer proceeds with a new implementation of SAP BW/4HANA or a system conversion. Both cases are covered in the Migration Planning Workshop in the Transition Planning activity.</p>
	</div>
	<h2>Trial System Provisioning</h2>
	<p>To support the value identification, and the impact evaluation in the Discover phase, it could be beneficial to have access to a trial system. At this point in time in the project, there may be no hardware available to run the new solution on premise, therefore installing a pre-configured appliance is potentially not possible. A cloud trial system is the preferred option in most cases. Requirements and Constraints User account at cloud provider required (e.g. Amazon Web Services (AWS)). Procedure Provide a Trial System Results As a result, project members have access to the trial system for demoing, value identification, and impact evaluation. How SAP Can Support SAP provides the cloud image of a pre-configured system, which runs at the cloud provider.</p>
	<div>
		<h3>Provide a Trial System</h3>
		<p>Request a Trial system in the cloud and provide it to your project team. Prerequisites User account at cloud provider required (e.g. Amazon Web Services (AWS)). Procedure You can access the cloud appliance via the SAP Cloud Appliance Library (SAP CAL, see link in the accelerator section). It is a pay-per-use model hosted on Amazon Web Services (AWS). When using the SAP CAL option, you can choose between a 30-day trial, or a longer-lasting engagement. With the trial, only AWS hosting fees need to be paid by the customer. Note: Check your cloud provider page for prices. You find the recommended VM sizes in the details section of the selected image. If you opt to go beyond the 30-day limit, an SAP CAL subscription license and a regular SAP license for the product is required. Besides the SAP backend, you also receive access to a MS Windows Terminal Server (WTS) with the pre-configured Fiori Launchpad URL, SAP GUI logon, and other useful frontend software. Go to the CAL landing page at SAP.COM Inform yourself about the Cloud Appliance Library, and the different options you have Click “Start your trial now” Accept terms and conditions Select the right trial image Click “Try now” Accept terms and conditions Maintain cloud provider account information Click “Create Instance” Figure 1.6: Relation of user, SAP Cloud Appliance Library and AWS Cloud Results Once the trial image has been started by the cloud provider (the instance is up and running within 15 - 60 minutes depending on the complexity of the solution), you have access to the trial system. How SAP Can Support SAP provides the cloud image of a pre-configured SAP system, which runs at the cloud provider. You may refer to the Getting Started Guide on the CAL page for details.</p>
	</div>
</div>
<h1>Prepare</h1>
<p>Once the business case has been approved, the project is initiated in the Prepare phase. Figure 2.1: Activities in the Prepare Phase The formal setup of the project needs to be aligned with the customer project manager. In general, each company or implementation partner has a methodology to plan and execute projects. SAP’s implementation methodology is called “SAP Activate”. See Accelerator’s section for more details. Note that currently the SAP Activate methodology is always described in the context of SAP S/4HANA, but a lot of the SAP Activate content is generic and can help with the implementation of other SAP products, too. Project team enablement also starts at this point in time. If the customer wants to go for prototyping it takes place in the Prepare phase. Prototyping is an optional small project on its own. It requires dedicated planning, execution and final evaluation. The transition project itself is planned at a high-level in the Transition Planning activity in the Migration Planning Workshop (conversion) or the Transition Planning for new Implementation Workshop (new implementation). Results from the Discover phase – in particular the functional implementation road map - need to be considered as well. The project plan will be further detailed out in the Explore phase. If not yet done in the Discover phase, a readiness assessment is performed on the current SAP BW system to identify aspects that could have a major impact on the system conversion project. The planning workshops often reveal preparation activities (e.g. the database migration to SAP HANA), which could be done before the Explore phase starts. Those larger preparation activities need to be planned in detail in the Project Management work stream and will be executed in the Transition Preparation activity. These can be complete pre-projects which have to be performed before the actual transition project can go into the Explore phase. Two additional activities could start early: For conversion projects: The cleanup of custom code which is not in use, and which could be decommissioned before the conversion starts. The installation and configuration of SAP Solution Manager The Prepare phase ends with a first Quality Gate to ensure proper project preparation.</p>
<div>
	</div>
	<h2>Project Initiation</h2>
	<p>An important part of the Prepare Phase of a project is the formal setup of the project. This needs to be aligned between SAP and the customer. Note that the following description relates to an on-premise project. The setup in a RISE with SAP project may differ and contain additional/different roles and project management tools. The assumptions with respect to Project Management are as follows: The customer has bought one of the SAP Value Assurance service packages. The SAP Technical Quality Manager (TQM) and the access to SAP’s Mission Control Center and Premium Mission Critical Support is part of every Value Assurance package. The SAP TQM is your contact partner to discuss all topics related to your transition project. The TQM makes sure that all issues are resolved and you receive the services and support activities at the right point in time with the right skills from SAP. SAP TQM tasks and deliverables: Setup of the governance structure for your project Leadership for scope definition and execution of all tasks done by SAP Definition and review of quality gates SAP Mission Control Center: Access to the whole SAP back office organisation including SAP Development Verification of engagement plans Access to premium mission critical support Support for gap clarifications There is a project manager from the customer and/or implementation partner, and the SAP TQM who supports to run this project. The roles and responsibilities in the project, especially those that are leading the project (customer or partner) are defined. The customer is managing the project by either using a 3rd party software (e.g. Microsoft MS Project), or SAP IT Portfolio &amp; Project Management. SAP provides project templates for download as a starting point, which need to be adapted to the customer project specifics. The Quality Gates are managed in SAP Solution Manager by the SAP TQM. All important documents and Quality Gate check lists are stored there. There are several applications in SAP Solution Manager to service this aim: IT Portfolio &amp; Project Management (ITPPM; formerly known as cProjects) Project road map (transaction RMMAIN) Project Management (transactions SOLAR*) As of SAP Solution Manager 7.2 ITPPM should be used for managing Quality Gates. Documents that need to be shared across the project team (e.g. Risk-Response-Log), are stored in SAP Solution Manager, e.g. by attaching them to the corresponding activity and/or task in SAP ITPPM. Quality Gate check lists can be sent to SAP for reporting and safeguarding reasons. Project management in the context of an SAP implementation has been documented in detail in SAP Activate road maps (e.g. “SAP Activate methodology for Business Suite and On-Premise – Agile”). See accelerator section for a link. All general project management activities, tasks, and accelerators can be taken from there, by filtering on the Project Management work stream. This road map focuses on additional project tasks which are owned by the SAP Technical Quality Manager (TQM). Requirements and Constraints This activity is required for all scenarios. Procedure Conduct Handover Meeting(s) from Opportunity Phase Review Order Form with Customer Identify Stakeholders, Stakeholders’ Requirements and Expectations and Project/Deliverable Acceptance Criteria Set Stakeholder Expectations for Agile Project Create Project Charter Create Project Management Plan Results Once this activity has been completed, the transition project has been successfully initiated. How SAP Can Support SAP Value Assurance supports this task with multiple service components, and offers the following benefits: An established service plan complete with governance model A defined scope and approach for the digital journey Established quality gates for measurable, on-time results Access to deep, expert knowledge on a global level Alignment on Best Practices and gap verification to reduce overall complexity Timely scorecard-based measurement for success The service always includes the Focus TQM described above. In addition, you may request additional resources like the Project Manager / Project Lead, and Engagement Architect from SAP as part of this service. See accelerator section for a service component description.</p>
	<div>
		<h3>Conduct Handover Meeting(s) from Opportunity Phase</h3>
		<p>The purpose of this task is to efficiently transition the project from the opportunity team to the delivery team. During this task the project manager schedules and conducts internal meetings to transfer key information from the opportunity phase into the project. Procedure Typically, during these calls the account team and project team review the following areas: project background, business case, business drivers, customer goals, scope, RACI, success metrics for the project, customer expectations, delivery model, Statement of Work, Order Form, assumptions.</p>
		<h3>Review Order Form with Customer</h3>
		<p>The purpose of this task is to clarify any questions in regard to the Signed SAP Contract (SOW), Order Form, confirm scope, RACI and resolve any issues. This includes review of the delivery model. Procedure Review Order Form Assumptions Review Order Form Scope Review Order Form RACI Review Order Form Resources &amp; Budget Review Order Form Customer Staffing Review Order Form Solution Scope</p>
		<h3>Identify Stakeholders, Stakeholders’ Requirements and Expectations and Project/Deliverable Acceptance Criteria</h3>
		<p>The stakeholder identification process is a critical process of initiating the project and aligning the project objectives with the expectations and requirements of the stakeholders. It is critical for the success of the project to identify, involve stakeholders and keep them engaged throughout the project.</p>
		<h3>Set Stakeholder Expectations for Agile Project</h3>
		<p>The purpose of this task is to build agile awareness of key stakeholders, secure commitment to adopt standard SAP functionality where possible. It is recommended to conduct informal and formal meetings to set the expectations and confirm the project approach with key project stakeholders.</p>
		<h3>Create Project Charter</h3>
		<p>The Project Charter formally documents the business needs and benefits of the project to confirm alignment with customer key stakeholders. It authorizes the project and is based on the Order Form(s) and Business Case. This document outlines the purpose, high level approach and key characteristics that define the project. The key benefit of creating the Project Charter is a well-defined project start and boundaries, and a direct way for senior management to accept and commit to the project. Project Charter Contents Current Situation Proposed Resolution Solution Description Project Goal Project Objectives Business Case Summary Total Estimated Project Costs Key Dates Project Stakeholders Critical Success Factors Risk Assessment Procedure Define Project Purpose or Project Justification and measurable Project Objectives High-Level Project Description and Boundaries Project Success and Approval Criteria Assumptions and Constraints High-Level Requirements High-Level Solution and Project Scope Summary Milestone Schedule Summary Budget Stakeholder Group and Key Names Stakeholders High-Level Risks</p>
		<h3>Create Project Management Plan</h3>
		<p>The Project Management Plan is the document that describes how the project will be executed, monitored and controlled. It integrates and consolidates all subsidiary plans and baselines from the planning process. Procedures Establish Scope Baseline Establish Schedule Baseline Establish Cost Baseline Establish Quality Baseline Define Scope Management Plan Define Requirements Management Plan Define Schedule Management Plan Define Cost Management Plan Define Quality Management Plan Define Process Improvement Plan Define Human Resources Management Plan Define Communications Management Plan and Project Reporting Standards Define Risk Management Process Define Procurement Management Plan Define Stakeholder Management Plan Define Change Management Process Define Issue Management Process Define Project Constraints Define Project Standards Obtain Project Management Plan Sign-Off</p>
	</div>
	<h2>Establish Project Governance</h2>
	<p>Project Governance is an oversight function that is critical to the success of the project. It provides the project manager(s) with a framework for consistently managing and controlling the project which includes tools for decision making, role definition, responsibilities, accountability and the alignment of stakeholders around the purpose of the project.</p>
	<div>
		<h3>Define Roles and Responsibilities</h3>
		<p>The purpose of this task is to determine the structure and composition of the team and to ensure that roles and responsibilities are clearly defined. The assignment of people to roles should also consider their qualifications and availability for the whole project time frame.</p>
		<h3>Define Project Organization</h3>
		<p>The purpose of this task is to define the organizational structure of the project team.</p>
		<h3>Review Project Management Plan</h3>
		<p>The purpose of this deliverable is to review the project management plan and the subsidiary plans on the basis of the project scope defined in the project charter.</p>
	</div>
	<h2>Plan Project</h2>
	<p>The purpose of this deliverable is to properly plan the project to guide both project execution and project control. Procedure : Create Scope Statement Create WBS Create Project Schedule Create Budget Plan Quality Plan Communications Plan Risks Plan Procurement Plan Stakeholders Management</p>
	<div>
		<h3>Create Scope Statement</h3>
		<p>The purpose of this task is to prepare the scope document with the pre-defined content according to the Statement of Work (SOW) and solution documentation. The project scope statement evolves through the initiation and planning of the project and clearly and explicitly defines the deliverables of the proposed project. This information and supporting documents align key stakeholders around what the project is going to deliver.</p>
		<h3>Create WBS</h3>
		<p>The purpose of this task is to create the work breakdown structure (WBS) for the project, which is a deliverable-oriented, hierarchical decomposition of the work to be executed by the project team to complete the project. It is the basis for the organization and coordination of the project. A WBS consists of WBS elements that describe project tasks and subtasks to perform within a defined time period. Top-down view of how activities fit into the overall project structure Defines the total scope of the project (specified in the approved scope statement) Work packages at the lowest level can be subdivided into activtities/tasks</p>
		<h3>Create Project Schedule</h3>
		<p>The purpose of this task is to create a project schedule. The detailed project schedule defines the work to be performed, the resources and associated time commitments required for the project, and the individual phases of the project. The work breakdown structure (WBS) serves as the foundation for the project schedule and deliverables to be produced as part of the project. Procedures: Define Activities Sequence Activities Estimate Activity Resources Estimate Activity Durations Develop Schedule</p>
		<h3>Create Budget</h3>
		<p>The purpose of this task is to create a project budget, that outlines all the costs associated with the project, including labor, hardware, software, cloud provisioning, contracting fees, and facilities. The project budget is a living document that the project manager maintains. At this stage the project manager sets the budget baseline and will manage the budget according to the cost management plan that is developed as part of the Project Management Plans task. Procedures: Estimate costs Determine Budget</p>
		<h3>Plan Quality</h3>
		<p>The purpose of this task is to identify the standards of the project and how the quality will be managed and validated throughout the implementation of the project</p>
		<h3>Plan Communications</h3>
		<p>The purpose of this task is to plan the communication management and define the communication requirements for the project.</p>
		<h3>Plan Risks</h3>
		<p>The purpose of this task is to plan and define the project risks. Procedures: Identify Risks Assess Risks : Estimating and Evaluating Risk Create Risk Register ( refer to the &quot;Risk Register Template&quot; provided ) Define Risk Responses</p>
		<h3>Plan Procurement</h3>
		<p>The purpose of this task is to plan and prepare the guidelines for all the procurement activities on the project. This includes materials, products or services identified for outside procurement.</p>
		<h3>Plan Stakeholders Management</h3>
		<p>The purpose of this task is to plan the appropriate management strategies to identify and engage stakeholders.</p>
	</div>
	<h2>Project Kick-Off and On-Boarding</h2>
	<p>The purpose of this deliverable is to kick-off the project and ensure that all required information is shared with the project team resources, key stakeholders, and anybody involved in the project. The goal of the kickoff meeting is to ensure that everybody involved in the project understands the goals and objectives of the project, as well as the schedule that the project team will follow, which is one of the key ingredients for a successful project execution.</p>
	<div>
		<h3>Prepare for Kick-off Meeting</h3>
		<p>The purpose of this task is to prepare the kick-off meeting which will help ensure the involvement of the team and other key resources and their commitment to the project schedule.</p>
		<h3>Conduct Kick-off Meeting</h3>
		<p>The purpose of this task is to schedule and prepare the project kick-off meeting. The kick-off meeting includes discussion of project objectives, organizational structure, roles and responsibilities, project governance, schedule, scope, communication standards, change request process, and decision-making process. The kick-off meeting is attended by the project team, key stakeholders, project sponsor and company executives. Some resources may attend the kick-off meeting remotely. In such cases it is important to ensure good audio and video infrastructure so the remote participants can take full part in the kickoff meeting session. Use the attached accelerator as a template to prepare kick-off meeting materials specific to your project</p>
		<h3>Prepare Team On-boarding Document</h3>
		<p>The purpose of this activity is to prepare the onboarding package for external consultants from SAP and partner companies.</p>
		<h3>On-board Project Team</h3>
		<p>The purpose of this activity is to prepare the on-boarding package for external consultants and new project team members from SAP and partner companies coming to the project. The on-boarding package contains the essential information that each new team member needs to understand, which is the purpose of the project, goals, operating procedures, and other key information. The on-boarding package typically contains the following information: Project objectives, scope, and goals including SAP solutions being implemented Project schedule including milestones Project governance including key project stakeholders Organizational chart for the project-showing both internal and external resources Overview of the existing SAP landscape Outline of regular project meetings Travel policies, dress code, project location, and other project guidelines as needed.</p>
	</div>
	<h2>Project Team Enablement</h2>
	<p>Description The purpose of this task is to develop a comprehensive training strategy that provides all team members with a learning path for acquiring the skills and knowledge needed to complete the project successfully. Requirements and Constraints The project has been initiated successfully. Procedure Provide key self-learning material to the project team Train the customer project team. Results As a result, the project team has been trained successfully.</p>
	<div>
		<h3>Provide Key Self-Learning Material to the Project Team</h3>
		<p>The purpose of this task is to make key self-learning material and information sources available to the project team members. Prerequisites The project has been initiated. Procedure Organize self-learning possibilities for IT professional to understand the new solution with digital learning environments (e.g. Learning Hub). This may include: Grant project team member access to SAP Best Practices documentation Ensure that all team members can access the Best Practices documentation in the new SAP Signavio Process Navigator (see accelerator section). Ensure project team member access to the SAP Learning Hub Ensure that all project team members can access the SAP Learning Hub. The Learning Hub is central location which allows access to different SAP Learning Rooms. SAP Learning Rooms are interactive social learning environments. Available online 24/7, they facilitate informal learning and knowledge sharing as well as expert-hosted live sessions, videos, quizzes, and postings on a wide range of SAP topics. To access the learning hub, read the blog in the accelerator section. This roadmap provides links to SAP Learning Journeys in the accelerator sections. These learning journeys are one of the core resources of SAP Learning Hub. SAP Learning Hub is offered in different editions, depending on your contract and learning needs: SAP Learning Hub, edition for SAP Enterprise Support Help key users, IT, and line of business users get the most out of SAP software –through access to expert-level webinars, best practices, tutorials, and more. SAP Learning Hub, edition for SAP Preferred Success Access digital learning resources, gain hands-on experience and collaborate with fellow peers in an edition exclusively available for SAP Preferred Success customers. SAP Learning Hub, partner edition Drive performance and customer success with an all-inclusive digital offering – available exclusively for members of the SAP PartnerEdge program. SAP Learning Hub, solutions edition Solution editions of SAP® Learning Hub are one of the fastest routes to solution-specific success, accelerating onboarding, software adoption, and proficiency for everyone involved in implementation and deployment. This comprehensive digital platform offers digital learning resources within eight SAP solution portfolios – SAP S/4HANA® Cloud, digital platforms, procurement and networks, analytics, customer experience, supply chain management, people engagement, and finance SAP Learning Hub, business edition Empower your entire workforce with overview knowledge about SAP software. Sign up to SAP Learning Hub Go to the accelerator SAP Enterprise Support Academy Landing Page. Click on ‘Learn More and Sign Up’ and follow the steps outlined. Select ‘Program Entitlement’ to check eligibility and find out more information. Click on ‘Sign-Up’ and register with your S-User ID and password. A welcome email will be sent shortly after signing up. Confirm the popup message (only relevant for first time access to the SAP Learning Hub, edition for SAP Enterprise Support and SAP Preferred Success). Please note that S-User assignment to the SAP Learning Hub, edition for SAP Enterprise Support can take up to two hours from the time you have received the welcome email. Log in using the link provided in the registration email. Provide training material on project support tools from SAP Ensure the project team is properly trained on the usage of SAP Solution Manager. SAP Solution Manager provides a central point of access for a variety of essential project support functions. The reference materials provide more details about available Solution Manager trainings, Focused Build for SAP Solution Manager, and a video describing how SAP Solution Manager is perfect for SAP implementation. SAP provides customers with an SAP Solution Manager tool set that supports customers in implementation of the project standards for the purposes of the project or operations. It is highly recommended to use the SAP Solution Manager Focused Build service solution. Additional details can be found in the accelerator section. Alternatively, you can check if the current capabilities of SAP Cloud Application Lifecycle Management (SAP Cloud ALM) are sufficient for your project requirements. SAP Cloud ALM is a public cloud solution running on SAP BTP. It is constantly extended. Use the links from the accelerator sections, or training from openSAP. SAP Enterprise Support Customers can find information and sessions on SAP Activate and SAP Solution Manager in the “Prepare” learning journey. Results Key learning resources are available to the project team for self-learning.</p>
		<h3>Train the Customer Project Team</h3>
		<p>In case the self-learning material is not sufficient to train the project team, it is the purpose of this task further train the project team to ensure efficient design and implementation. Prerequisites The project has been initiated, and the project team has been set up. Procedure The starting point for creating knowledge transfer in transformation projects is the identification of the learning requirements and the analysis of the digital learning opportunities for the professional IT team members within the customer organization. Matching the project scopes and objectives with the given resources and their requirements will identify the skill-gaps for the project team members at the very first beginning of the project. Proceed as follows: Identify the processes in scope and analyze the required skills together with the project team Evaluate SAP individual standard training from SAP Education courses (Classroom and Web-based Trainings) Develop enablement plan including training, mentoring, coaching for individual project team members / project team groups Organize and run system application training with individual learning methods Results The customer project team is ready for application design and implementation. How SAP Can Support SAP offers SAP Learning Classes with instructor-led courses in training centers and virtually to support customers in implementation projects. See accelerator section for details.</p>
	</div>
	<h2>Project Standards, Infrastructure, and Solution</h2>
	<p>The purpose of this deliverable is to provide consistent means of executing and governing project work in an efficient and effective manner. The key objective of Project Standards is to identify, define, approve, and communicate standards related to project execution. Where and when applicable, the current customer processes and procedures, related to the project standards should be considered when defining the most suitable standards for the project.</p>
	<div>
		<h3>Define Project Standards</h3>
		<p>The purpose of this task is to identify, define, approve, and communicate standards related to project execution. Project standards provide a consistent means of executing and governing project work in an efficient and effective manner. Project standards are elaborated throughout the Prepare phase (some may be fine-tuned at a later stage of the project).During the Prepare phase the foundational project standards must be defined, approved, and communicated to the project team. Communication of project standards should be included in project on-boarding communications for project team members. Given the integrated nature of project standards, changes must be managed in accordance with the integrated change control procedure. The project team needs to establish, at minimum, the project standards for the following areas: Requirements Management Business Process Modeling Solution Configuration and Documentation Custom Code / Development Authorizations / Security Test Planning and Execution Change and Release Management Post-implementation Support and Operations.</p>
		<h3>Determine Operational Standards</h3>
		<p>The purpose of the Operational Standards task is to provide consistent means of executing and governing project work in an efficient and effective manner. The key objective of Project Standards is to identify, define, approve, and communicate standards related to project execution. Where and when applicable, the current customer processes and procedures, related to the project standards should be considered when defining the most suitable standards for the project. Determine Solution Documentation Procedure The purpose of this task is to determine the customers framework to centrally document and relate business processes and technical information of SAP and non-SAP Solutions in Solution Manager, ensuring transparency, efficient maintenance and collaboration. Determine Solution Implementation Procedure The purpose of this task, also known as Innovation Management, is to determine the customers approach for the identification, adaptation and implementation of new and enhanced business and technical scenarios. It is part of the application lifecycle and designed to decouple technical installation from business innovation using SAP Solution Manager to implement the innovation in the system landscape. Determine Template Management Procedure The purpose of this task is to determine the customers template management approach that allows customers with multi-site SAP installations to efficiently manage their business processes across geographical distances - from initial template definition to template implementation and template optimization, for example as part of a global rollout. Determine Test Management Procedure The purpose of this task is to determine the customers approach for managing functional and performance testing of SAP-centric business processes to ensure validated system behavior after software change events. Determine Change Control Management Procedure The purpose of this task is to determine the customers approach of handling business and technology-driven changes, using a standardized process leading to improved reliability of solution and minimized risk through segregation of duties and transparency of changes. Determine Application Incident Management Procedure The purpose of this task is to determine the customers approach of a centralized and common incident and issue message processing for multiple organization levels. The process offers a communication channel with all relevant stakeholders of an incident, including business user, customer-side SAP experts, SAP Service &amp; Support and Partner Support employees. The Application incident Management is integrated in all ALM processes of SAP Solution Manager, in any SAP Business Suite solution and can be connected to a Non-SAP Help Desk application Determine Technical Operations The purpose of this task is to determine the customers approach of monitoring, alerting, analyzing and administrating of SAP solutions. It allows customers to reduce TCO by predefined content and centralized tools for all aspects of operations in SAP Solution Manager, including End-to-End reporting functionality either out-of-the-box or individually created by customers. Determine Business Process Operations This task uses SAP Business Process Integration &amp; Automation Management (BPIAM) to cover the most important application related operations topics necessary to ensure the smooth and reliable flow of the core business processes to meet a company&#x27;s business requirements. Determine Maintenance Management The purpose of this task is to determine the customers approach of Maintenance Management, how SAP Notes are handled and how they are applied (upon request e.g. current incident in productive environment, information from SAP about potential issue (Hot News, Security Notes). Determine Upgrade Management The purpose of this task is to determine the customers approach for the identification, adaptation and implementation of new and enhanced business and technical scenarios. It uses Solution Manager to manage the upgrade project holistically and effectively end-to-end and allows SAP customers to better understand and manage the major technical risks and challenges in an upgrade project, and to make the upgrade project a &quot;non-event for the business&quot;.</p>
		<h3>Define and Set Up Agile Project Standards and Scrum Tools</h3>
		<p>The purpose of this task is to define and set up SCRUM standards and tools, such as SCRUM board, burn-down chart, product backlog template and retrospective template. Organization of daily stand-up meetings, scrum-of-scrum-, release/sprint planning and retrospective meetings. At this time the project also needs to prepare definition of Ready and definition of Done for each critical step in the iteration. At minimum the team needs to prepare and agree on a definition of Ready and Done for following: Ready for Build (e.g. backlog item has proper size, it is clearly defined and understood, test procedure is defined, etc.) Ready for Demo (e.g. backlog item is fully developed, and unit tested) Ready for Integration Test (e.g. backlog item integration test is defined (automated), test data is ready) Ready for Release (e.g. backlog item user level documentation is complete, ALM Solution Documentation is ready, Feature passed Integration Test) Note that definition of Ready for one step in the implementation process == definition of done of the previous step. Using clear definition of Ready and Done is critical for SCRUM based implementations.</p>
		<h3>Check Availability of an ALM Tool for Project Management</h3>
		<p>SAP Solution Manager supports heterogeneous system environments. Its functions cover all aspects of implementation, deployment, operation, and continuous improvement of solutions. As a centralized, robust solution, SAP Solution Manager combines tools, content, and direct access to SAP, to increase the reliability of solutions and lower total cost of ownership. SAP Solution Manager is the pivotal hub for collaboration in the ecosystem, as it empowers communication between all the stakeholders in a solution, including project teams, SAP partners, consulting and SAP Active Global Support. SAP recommends customers to utilize the pre-configured environment for SAP Solution Manager Focused Build that will provide ready-to-run SAP Solution Manager environment. Alternatively, the customer may decide to install and setup the SAP Solution Manager following the installation guide, that provides step-by-step process for installation and setup of SAP Solution Manager. The general sequence for the implementation of an SAP Solution Manager system is as follows: You plan the implementation (such as scope, hardware and software requirements, release restrictions). You plan the system landscape for your use cases. You install the components of your SAP Solution Manager system. You configure your system. You set up the connection to the managed systems. The objective of this task is to check availability of SAP Solution Manager environment for the project team. This should be validated early in the project, if not before the project starts. If there is no setup activity needed to be planned to install Solution Manager, ensure that the technical team has completed the general Solution Manager set-up and then has covered the process specific configuration in the following areas: Process Documentation ITSM Project Management and Requirements and activities scheduled to include the set up of: Test Management Change Control Management Application Operations Please ensure the following has been completed by the technical team: customer specific access roles have been created access roles created to provide digital signature approvals to documents users created in the system (with appropriate access roles) and Business Partners created and connected to users for all project team members users can all access SolMan and there is an appropriate schedule of project team training for each project phase / key activity using SolMan. the procedure for requesting access to SolMan and other systems and tools is agreed, setup and communicated.</p>
		<h3>Set up Project Team Logistics and Infrastructure</h3>
		<p>The objective of this task is to setup a physical project team environment and ensure that the project team members have an appropriate level of access to the customer facility, project room and the project systems. This activity also involves the setup of project team work stations/computers and/or mobile devices (as necessary for the project support). The project manager together with the technical team works on setup of the following (the questions in each area are geared to help provide guidance): Project Equipment Is there a requirement for hardware (e.g. servers,laptops, etc.)? Are there any additional software licenses required? Are the software licenses purchased? Was the software installed for the project team members? Is there an area planned for the project equipment? Is there project equipment located in different geographical sites? Is there any computer lab required for the project? Physical Project Environment Has the required work space been arranged at all project sites? Are there sufficient conference rooms planned for the project team? Has multimedia equipment been planned for all conference rooms (e.g. video conferencing, projectors, etc.)? Has physical access been granted for all project team members in different geographical sites? Is there a working area assigned to the project members? IT Infrastructure and Access Is the LAN access available to the Project Team? Has the LAN access been tested? Do the Project Team Members have the necessary printer drivers installed? Has IT access been granted for the following: E-mail servers Internet access, Intranet access Team collaboration space like SAP Jam MS-Office Project team specific applications like modeling tool, test management tools, etc. Customer Policies Is there a specific project policy applicable (communication, confidentiality, back-up, etc.)? Has the project policy been communicated? Physical Security Has all necessary access been granted indifferent project sites? Have all sites policies been communicated (e.g. security, safety, emergency exits and etc.)? Telephone/Voice Mail Is there a telephone number issued to project team members? Has the Project Team Member been told how to set-up the voice mail? Is there a central phone book for the project team members? Project Team Member Equipment Is the required equipment (computer, laptops, mobile and etc.) available to all project team members? Are office supplies available to the project member? Are the required software licenses purchased? (if needed)</p>
		<h3>Setup and Test Remote Support</h3>
		<p>SAP customers are advised to set up remote access to SAP Support to ensure timely response to incidents and support tickets. Set up remote access so that support can quickly diagnose and solve incidents you report, without needing to be at your desk or on the phone. This is especially helpful when dealing with different time zones. In addition to solving your incidents faster, setting up a remote connection between your company and SAP will also allow you to receive services, such as Early Watch, and Continuous Quality Checks, that you may be entitled to through the Support Programs your company has purchased. Follow the guidance on SAP Support Portal linked from the accelerators page to setup the remote access.</p>
	</div>
	<h2>Transition Planning</h2>
	<p>The planning of the transition project helps to properly define the scope and execution plan for the upcoming transition project. The result is a first version of an action plan, which can serve as the basis for a project plan. The project plan will be constantly refined throughout the project (in particular during the Prepare and the Explore phase) as part of the project management work stream. There are three conversion paths/scenarios to SAP BW4HANA (both on premise and in the cloud): Path 1: New implementation: For those who would like to implement a new SAP BW/4HANA system by either migrating a legacy system or by running a net-new installation of SAP BW/4HANA. Path 2: System conversion: For those who want to run a conversion of an existing SAP Business Warehouse system to SAP BW/4HANA. There are three types planned for this scenario: in-place system conversion (via SAP BW7.5 powered by SAP HANA) Remote conversion with parallel operations Shell conversion Path 3: System Consolidation: For those who would like to consolidate their existing data warehouse landscape or carve out selected data models or flows into an existing SAP BW/4HANA system. System Consolidation can be conducted within a customer-specific project with SAP. In the Transition Planning activity, the decision for one of the scenarios should already be clear. Note: As Selective Data Transition is very customer-specific, this roadmap documentation focusses on the New Implementation and the system conversion scenarios. Requirements and Constraints Proper transition planning is required for all scenarios. However, the conversion readiness only needs to be checked in case of a system conversion or selective data transition scenario. See activity Strategic Planning, task Check the Conversion Readiness of the current SAP Business Warehouse.</p>
	<div>
		<h3>Transition Planning - New Implementation</h3>
		<p>During an implementation project, you have to consider many aspects and to take various decisions. The major planning steps of this process are outlined below: Scope and Requirements You determine the scope of your SAP BW/4HANA implementation Landscape Planning (will be detailed out in the Technical Architecture WS) You determine the system landscape and consider the landscape-relevant aspects concerning your required use case Hardware &amp; software Prerequisites (will be detailed out in the Technical Architecture WS) Use the Product Availability Matrix (PAM) to check which platforms (operating systems, databases, browsers) are supported for your SAP BW/4HANA components Check the recommended minimum database versions Release Restrictions Check SAP Notes for any release restrictions (see SAP Note 3365187 - Release Information/Restrictions Note for SAP BW/4HANA 2023) Results The required implementation steps and preparatory steps should now be defined in an action plan. How SAP Can Support In case of a new implementation SAP covers the following scope in the Transition Planning Workshop: Customer specific information gathering Workshop Day 1: Functional and Technical Overview Workshop Day 2: Proposed Customer Specific Content and Approaches Readiness Report Creation and handover to Customer Feedback and Follow-Up In addition, SAP offers a lot of information on the different paths to SAP BW/4HANA in the SAP BW/4HANA Community. In the SAP Community for SAP BW/4HANA consultants report on their experiences made during the early adoption phase and give a step-by-step description of the implementation / conversion process which may be very informative for consultants as well as customers. For customers who have a productive SAP BW but want a fresh start, SAP is planning to offer an option to transport certain objects from their existing SAP BW system to a newly installed SAP BW/4HANA system. For a tool to check the transports, see SAP Note 2361350.</p>
		<h3>New Implementation with SAP BPC</h3>
		<p>All steps for the new BPC implementation must be planned in this activity. The BPC project can consist of either an implementation of a new planning or consolidation process, a migration of a non-SAP legacy solution, a migration of an SAP legacy solution (e.g. BPS, BCS) a re-implementation of SAP BPC 2021 Procedure Same as for BW/4HANA – see task “Transition Planning – New Implementation”. Results The required implementation steps and preparatory steps should now be defined in an action plan. How SAP can Support For migration projects, SAP offers the Migration and Upgrade Planning Workshop (MPW) for SAP BW/4HANA which can be enhanced with BPC specific topics. In case of a new implementation without any predecessor tool, SAP can support with a Technical Architecture and Infrastructure service.</p>
		<h3>Transition Planning - In-Place System Conversion</h3>
		<p>The In-place Conversion takes your existing SAP BW landscape through a transition process that transfers step-by-step data models and flows to objects compatible with SAP BW/4HANA. Prerequisite is SAP BW 7.5 powered by SAP HANA, the last major release of SAP BW. During the preparation time, you will use the Pre-Check Tool to analyze the meta data repository of your SAP BW system and lay out a to-do list for the transition process, run the Sizing Report to determine the required size of hardware for SAP BW/4HANA, and use the Code Scan Tool to analyze your custom developments – including code embedded in various SAP BW objects like transformations – for compatibility with SAP BW/4HANA. When you get to the realization phase, you will spend most time using the Scope Transfer Tool, which allows you to create SAP HANA-optimized objects for all your classic SAP BW data models and flows. Pick a data target, like a MultiProvider and the tool will determine all dependent object like InfoCubes, classic DataStore Objects, transformations, and DTPs and allow you to quickly create equivalent CompositeProviders and Advanced DataStore Ob-jects including matching transformations and DTPs. During an In-place Conversion, the transfer happens inside your SAP BW on SAP HANA system. For In-place Conversion, several sprints using the Scope Transfer Tool will lead to a system that runs only data model and flows that are compatible with SAP BW/4HANA… but still in SAP BW (along the way you use another cool tool to adjust user roles and authorizations, which will be much appreciated by your security team). The ultimate step is therefore a technical system conversion that removes the old SAP BW code and replaces it with SAP BW/4HANA. This is when you finally get to use the new features like Data Tiering Optimization and the SAP BW/4HANA Cockpit which provides you with some SAP Fiori apps for process chain modeling and monitoring and for the administration of InfoProviders. Figure 2.4: Basic Sequence of in-place Conversion Customers starting from BW on anyDB must start with a database migration to SAP HANA. This is a separate project which should run during the Transition Preparation activity. Procedure Check the conversion readiness of the current SAP BW system (system conversion scenarios only) Define the scope and the objectives of the transition Define the cutover approach (high-level) Clarify the custom code adaption Clarify operational readiness Define the technical architecture (high level) Define the data migration architecture (important for new implementation and landscape transformation) Results As a result, a first version of an action plan has been documented which could serve as the basis for a project plan. A high-level transition planning document should be stored in SAP Solution Manager. How SAP Can Support Migration Planning Workshop For conversion projects, SAP offers the Migration Planning Workshop (MPW) for SAP BW/4HANA. The MPW helps to define the scope and execution plan for the customer’s conversion project. Main objectives / deliverables of the workshop are: Decision or at least the decision support for the right transition approach to SAP BW/4HANA (in-place or Remote conversion, system landscape transformation) Verification of the prerequisites for the chosen transition approach The onsite portion of the service includes knowledge transfer of key concepts and consideration, as a means to ensure that the customer is making informed decisions. In case customer decides to perform an In-Place system conversion, check the Prerequisites Checklist for the In-Place Conversion and identify the required preparation steps to get the system and data conversion-ready The outcome of the workshop includes development of a system-level transition road map a customer tailored high-level milestone project plan the Prerequisites Checklist with the identified preparation steps (if applicable) The MPW covers many of the tasks described before and summarizes the results in a service report. This consolidated view of critical project planning decisions is why SAP considers the Migration Planning Workshop as a “mandatory service” for system conversion. Details on the MPW can be found in the accelerator section. Delivery approach and scope are: The business process streams are assessed in order to define the Best Practice scope, review existing GAPs, collect information for data migration, quick-sizing and Interface-architecture. Create/review high-level architecture with Interfaces Create/review Quick-sizing Empower the customer for data migration respectively review existing data migration strategy (optional) Develop/review of high-level-scope statement and project schedule, project governance and operational standards (optional) Provide substantial knowledge on SAP Activate Methodology, Data Migration Approach and Strategy, SAP BW/4HANA Sizing Note: The same sizing process used for SAP BW powered by SAP HANA applies to SAP BW/4HANA. See SAP Note 2296290. For hardware sizing to run a BW/4HANA system, refer also to SAP Note 2363248 (see accelerator section). If the customer has decided to perform an in-place system conversion, he may make use of the SAP Best Practices for In-place system conversion to SAP BW/4HANA as a PS service for the Realize phase. This PS service provides the customer with a proven project approach and configuration guides for the conversion of an existing SAP BW to SAP BW/4HANA, without reimplementation and with minimal impact on business operations. The tool-based conversion to SAP BW/4HANA includes step-by-step documentation with project WBS structure, streamlined project methodology, and optimized project tools. Best practices help to plan and convert the system, gaining all the benefits of SAP BW/4HANA. Note that filling the Prerequisites Checklist in the MPW is mandatory as a preparation for the in-place conversion.</p>
		<h3>In-Place Conversion of SAP BPC</h3>
		<p>Both remote and in-place conversion are supported for BPC standard and embedded models. SAP BPC 2021 supports SAP BW/4HANA 2021 and SAP BW/4HANA 2023. Procedure The in-place conversion of BPC is supported by the Transfer Toolbox (version 1.5) for BPC Standard as well as for BPC Embedded. Refer to SAP Note 2645732 – Conversion from SAP BPC for NetWeaver to SAP BPC for BW/4HANA Central Note to understand and plan the required steps for the in-place conversion. Results As a result, a first version of an action plan has been documented which could serve as the basis for a project plan. A high-level transition planning document should be stored in SAP Solution Manager. How SAP can Support The Migration and Upgrade Planning Workshop for SAP BW/4HANA (see task Transition Planning – In-Place Conversion) can be enhanced with BPC-specific topics: Conversion steps Sizing approach</p>
		<h3>Transition Planning - Remote Conversion</h3>
		<p>To perform a Remote Conversion, the first step is to install a brand-new SAP BW/4HANA system. A task list is available that automates and therefore greatly simplifies the initial system setup. The Remote Conversion approach enables you to move whole data flow or transfer only selected data flows including the data from SAP BW 7.3 with any DB to a new installation of SAP BW/4HANA. You are able decide whether you want to build a clean system, leave old and unused objects behind, and reduce unnecessary layers of your data warehouse. If appli-cable, the Remote Conversion process includes a Unicode conversion. Carve-out scenarios are also supported. Remote Conversions are based on new installations of SAP BW/4HANA. This approach is unique in that it eliminates the need to convert an entire legacy system. No matter whether you run SAP BW on SAP HANA or some other database, the Remote Conversion enables you to pinpoint the scenarios you want to convert while still in an old system. These scenar-ios are then transferred into your fresh installation and converted accordingly. You can then proceed with transferring data between your old and new installation. The Remote Conversion is available for SAP BW 7.3 and later releases. Among other advantages, this approach only includes objects that will remain relevant going forward and saves you the effort of converting your database. It thus represents the chance to build a clean system, leave old and unused objects behind while migrating to SAP BW/4HANA system. Figure 2.5: Basic Sequence of the Remote Conversion Benefits of Remote Conversion Efficient and lean transfer from SAP BW 7.3 or higher on anyDB to SAP BW/4HANA Tailors the scope of the migration to focus on relevant objects instead of converting the entire system Leaves outdated and unused objects behind in the current data warehouse One-step approach for conversion as no prerequisites such as DB migration to SAP HANA or SAP BW upgrade on the productive system are required Risk mitigation due to a parallel system. A copy of the data still remains in the source system during conversion, hence the retrieval of original data is possible in case of errors Uses BW4/HANA Conversion Cockpit as a single point of control, monitoring, and documentation Retains business continuity during conversion Procedure For customers planning a Remote Conversion it is beneficial, too, to attend a Migration Planning Workshop (MPW) to develop a customer-specific high-level project plan. See the detailed description of the MPW in the task “Transition Planning – In-Place Conversion”. How SAP Can Support SAP offers the Migration Planning Workshop – see the task Transition Planning – In-Place Conversion. In case the customer decides to perform Remote conversion, check the Prerequisites Checklist for the Remote Conversion and identify the required preparation steps to get the system and data conversion-ready.</p>
		<h3>Remote Conversion with SAP BPC to SAP BPC embedded</h3>
		<p>Remote conversion means that a completely new BPC 11.1 or BPC 2021 (on SAP BW/4HANA) system is installed next to the productive BPC on anyDB system and all objects and data from BPC on anyDB are transferred into the new BPC system with on-the-fly transformation. As a consequence, there is no need for a prior database migration or a BPC system upgrade. Procedure The Conversion Cockpit can be used for a remote conversion of BPC Embedded. The prerequisite for a remote conversion with the Conversion Cockpit is that the following objects are not in use: All ‘Local Objects’ (objects created directly in a system which can’t be transported), including local InfoProviders, local models, local dimensions and master data, local hierarchy and local queries BPC web templates, including reports and input forms The remote conversion for BPC Standard is covered by the BPC-own “Backup and Restore Tool” (transaction UJBR). An alternative remote conversion scenario for BPC Standard is not planned. How SAP can Support SAP offers the Migration and Upgrade Planning Workshop – see the task Transition Planning – In-Place Conversion. The Remote conversion requires a Data Migration Assessment for Remote Conversion to SAP BW/4HANA which has to take place in the Explore phase – see activity Data Migration Design.</p>
		<h3>Transition Planning – Shell Conversion</h3>
		<p>If you want a fresh start i.e. preferring a greenfield approach, a Shell Conversion might be what is best for you. It is available for release 7.0 to 7.5 and works like a Remote Conversion using a parallel SAP BW/4HANA landscape. Shell Conversion does not include the transfer and synchronization of existing data sets. Instead at your convenience, you can choose to load data from original sources, load data from original SAP BW system, or simply ignore historical data and start fresh. Figure 2.6: Basic Sequence of Shell Conversion Benefits of Shell Conversion compared to a Remote Conversion The Shell conversion allows for a re-design in the target system as the objects initially are not filled in the target. For the scope selection the shell conversion is more flexible compared to the remote conversion because dependencies through request/transaction ID do not need to be considered Requirements for original SAP BW system SAP BW 7.0 to 7.5 Any supported database platform Installation of SAP BW/4HANA Transfer Cockpit using support package or SAP Notes Procedure New SAP BW/4HANA system is installed on additional hardware. Data models are transferred to target SAP BW/4HANA system using transports. Neither master nor transaction data are transferred. Options at customer’s convenience: Reload data from original sources Load data from original SAP BW system Ignore historical data and start fresh For customers planning a Shell Conversion it is beneficial, too, to attend a Migration and Upgrade Planning Workshop (MPW) to develop a customer-specific high-level project plan. See the detailed description of the MPW in the task Transition Planning – In-Place Conversion. How SAP Can Support SAP offers the Migration and Upgrade Planning Workshop – see the task Transition Planning – In-Place Conversion.</p>
		<h3>Transition Planning - System Consolidation</h3>
		<p>System Consolidation to SAP BW/4HANA is ideal for SAP customers with multiple SAP BW, or SAP BW on SAP HANA systems, or hybrid cases, who want to perform a consolidation into one global SAP BW/4HANA system or perform selective data migrations. who want to consolidate their landscape or carve out selected data models or flows into an existing SAP BW/4HANA system who want to stay with their current data warehouse landscape and move gradually to SAP BW/4HANA innovations How SAP Can Support System Consolidation is very customer-specific and can be conducted within a customer-specific project with SAP.</p>
		<h3>Transition Project: Scope Definition and Objectives</h3>
		<p>To define and document the objectives of the transition project, you should create a scope document.The scope documentation serves as a general guideline for the transition project. It has to describe the starting point of the project as well as the project objectives and the target solution landscape. The starting point typically describes the SAP solution in focus, the transition target product and the systems in scope (either to be converted, or to be replaced by a new installation). In addition, already planned changes to the SAP solution and further boundary conditions have to be determined. The objectives comprise the business and IT requirements related to the new functionality and technology, e.g. Provision of new functionality Reduction of costs or complexity Performance improvements for specific business functions Landscape adaptations to comply with changing market and business requirements Ideally, the objectives are defined in a way that they can serve as success criteria for the transition project. Further success criteria can be derived from boundary conditions, e.g. business downtime requirements for the transition project. Requirements and Constraints There is an implementation strategy and road map already available as input for the scope document. Procedure Document the scope of the transition project. Clarify and describe the following general aspects: Expectations from the business Expectations from IT Conversion target product including anticipated functional quick wins SAP solution in scope for the conversion Related SAP systems (name, SAP products, release, database size, and business criticality) Process the subsequent tasks in the road map to enhance the scoping document with boundary conditions and requirements from the business, as well as requirements and prerequisites from target product perspective. The transition project can be combined with already planned infrastructure changes, e.g. the replacement of server hardware or the introduction of a virtualization solution for the application server layer. This can help to reduce the overall effort for all change activities. Consequently, further limitations or opportunities might exist, which need to be incorporated. These boundary conditions need to be known to the transition project to integrate them in the planning and execution. Document already planned software or hardware changes in the SAP solution landscape, which are to be synchronized with the transitionproject. Examples are: OS upgrades and/or, -patches SAP support package updates, enhancement package upgrades, release upgrades, patches Unicode conversions Virtualization solution changes HA/DR solution changes Server hardware changes Storage infrastructure changes Backup tool or hardware changes Document further infrastructure changes or limitations, which can affect the transition project timeframe, e.g. data center moves or data center limitations. Document planned landscape changes, e.g. system splits, consolidations or moves. Document maintenance windows that could be utilized by the transition project. Document release plans or calendars of development projects in the target SAP solution. Before planning more detailed transition steps, the current SAP solution needs to be described in detail, so that the required target SAP solution architecture and related transition steps could be derived (“Inventory”). So, if for example the server hardware needs to be changed, the capabilities of the current server hardware and the related technical architecture of the SAP systems need to be understood in detail to design the future technical architecture and its mapping to the new hardware. Additionally, components depending on the changed database or other changed components in the SAP solution (e.g. the embedded Frontend Server for SAP Fiori) have to be detected to later verify how they integrate with the future SAP solution. Components in focus of the inventory are: SAP systems, both production and non-production Frontend infrastructure (server side) Operating systems Virtualization environments Server hardware File systems Storage infrastructures Software components directly interfacing with the source databases: High availability and disaster recovery solutions Backup solutions Monitoring solutions Software components accessing data within the target environment by direct access APIs (libraries, ODBC, JDBC …), e.g. reporting, analytics or replication software. Document the following findings: Components to be newly introduced Components, planned to be changed Components that interface with the components to be changed For SAP systems and their databases document the following: Software compatibility statements / version requirements of the target environment database as documented in the SAP Product Availability Matrix Current hardware, OS and virtualization platform versions Unicode enablement status of SAP systems Verify and sign-off the scoping document. Upload all documents to the SAP Solution Manager so information can be shared across the project team. Results As a result, there is a scope document available in SAP Solution Manager. How SAP Can Support Refer to the results of the workshops that have been performed (Migration Planning Workshop, SAP BW/4HANA Discovery Workshop).</p>
		<h3>Clarify Operational Readiness</h3>
		<p>With the introduction of a new solution like SAP BW/4HANA, your current IT support framework will probably change. For this, many aspects need to be considered. SAP provides guidance on the target IT support process activities, tools and resources required for you to safely and efficiently operate the new SAP solutions in its environment. SAP provides, for example, Best Practices on daily DB administration procedures, troubleshooting procedures, monitoring tools, knowledge content for the resources to be ramped up for the post go live operation… Clarification on the steps required to ensure the IT Operational Readiness will be given later in the Explore Phase (see activity Operations Impact Evaluation). As a result, a number of tasks will be defined and included in the conversion plan. Procedure Customers who are new to SAP should look at SAP’s general recommendations to set up a Customer Center of Expertise. At least a primary CCOE certification should be gained. SAP has also published SAP Support Standards which should be implemented as part of the standard IT support processes. See accelerator section for guidance. How SAP Can Support See Primary CCOE Certification program in the accelerators section.</p>
		<h3>Define Technical Architecture</h3>
		<p>Depending on the SAP BW release and database at the beginning of the project, IT architecture will most likely change (e.g. new HW, new OS, new HA/DR setup options…). System Landscape of an In-Place Conversion For an in-place conversion, the original SAP BW system needs to have a minimum release level. The following start releases are supported: SAP Business Warehouse 7.5 powered by SAP HANA (support package 5 or higher) An in-place conversion of SAP BW 7.51 or higher to SAP BW/4HANA is not possible (since SAP BW/4HANA is based on SAP Basis 7.50) Figure: System Landscape for In-Place Conversion System Landscape of a Remote Conversion The system landscape for the remote conversion typically involves an original (or sender) system, a target (or receiver) system, and one or more source systems. The main activities performed in the system landscape during a remote conversion include a scope selection, a transport handling, and a data transfer. Original (Sender) System The original system is your existing SAP BW development (DEV), quality (QA) or production (PROD) system. The original system must be set up as a control system for SAP Landscape Transformation. Target (Receiver) System The target system is the SAP BW/4HANA system, where the new and optimized SAP BW/4HANA objects will reside. The figure below shows a typical landscape and communication flow among the systems in a remote conversion for transition SAP BW/4HANA. Figure: System Landscape for a Remote Conversion For a remote conversion, the original SAP BW system needs to have a minimum release level. The following start releases are supported: SAP Business Warehouse 7.3 SAP Enhancement Package 1 for Business Warehouse 7.3 SAP Business Warehouse 7.4 SAP Business Warehouse 7.5 The remote conversion is independent of the database platform of your existing system (i.e. all supported database platforms are allowed; see Product Availability Matrix). See the following table for minimum and recommended support package level: Figure 2.7: Release and Support Package Levels of the original SAP BW system SAP BW/4HANA exclusively runs on SAP HANA. SAP BW/4HANA 2021 requires SAP HANA 2.0 SP05 Rev.56 or higher. SAP BW/4HANA 2023 required SAP HANA 2.0 SPS 7 Rev. 71 or higher. System Landscape for Shell Conversion The system landscape for the shell conversion involves an original system, a target system, and one or more source systems. The main activities performed in the system landscape during a shell conversion include a scope selection, a transport handling, and optionally the load of historical data. Original System The original system is your existing SAP BW development (DEV), quality (QA) or production (PROD) system. Target System The target system is the SAP BW/4HANA system, where the new and optimized SAP BW/4HANA objects will reside. The figure below shows a typical landscape and communication flow among the systems in a shell conversion for transition SAP BW/4HANA. Figure: System Landscape for a Shell Conversion Requirements and Constraints There is an implementation road map (high level) available describing the “to-be” business architecture of SAP BW/4HANA. Invite your hardware provider to join this task. Procedure If not done in the Discover phase (activity Strategic Planning): Collect information for the following topics (as far as already available): Future and existing technical platform (hardware platform, virtualization solution, OS) General SAP application server architecture Availability SLAs for unplanned downtimes Data center strategy HA architecture and DR strategy and architecture Non-production and production landscape System to data center mapping Planned server hardware (application servers and SAP HANA) and storage hardware Integration with cloud services (e.g. with SAP HANA Cloud Platform for side-by-side extensions of SAP S/4HANA Backup solution Clarify and document the following topics: What is the “to-be” SAP Landscape? What is the anticipated SAP architecture (main technical components)? What is the sizing relevant information (SAPS, memory, disk space) of all SAP components? What technical options are in scope (e.g. Tailored Data Center Integration (TDI))? What is the required HA / DR strategy, based on availability requirements, business downtime SLAs and the existing data center strategy? What is the ideal backup strategy? What is the strategy for the non-production landscape based on change management requirements? Based on the results, create a first sketch of a technical deployment plan, by mapping the systems and technical components to the hardware. The technical deployment plan documents which system runs on which server. This deployment plan is the basis for ordering the hardware at your hardware provider. The plan is constantly refined throughout the project. Results As a result of this task, a technical deployment plan exists. How SAP Can Support The Technical Architecture and Infrastructure service component supports customers in the technical design of a scalable, flexible, and maintainable SAP solution that fits to their specific requirements. Ideally, the information described in the procedure (above) should be provided by the customer beforehand. This is especially relevant when customers plan to migrate/transition to SAP HANA and SAP BW/4HANA which introduces often new IT infrastructure platforms to an existing SAP environment. In case of such a transition, the new sizing report for SAP BW/4HANA can be used – SAP Note 2296290. Here is an example output of the report: Figure 2.9: Output of a Sizing Report In case of a new implementation a basic sizing will be done during the Technical Architecture and Infrastructure service component with the SAP Quicksizer, see accelerator section: Figure 2.10: SAP BW/4HANA view in the SAP Quicksizer The Quicksizer results are rough estimations which should be verified with the hardware partner. An advanced sizing is recommended during the Explore phase.</p>
	</div>
	<h2>Transition Preparation</h2>
	<p>This activity covers all preparation work which happens before the project starts. Depending on the type of conversion (in-place or remote) there might be larger ‘pre’-projects required, which must be identified and scheduled right before the SAP BW/4HANA transition project starts. However, significant preparation items should have been discussed in the appropriate planning workshops in the Transition Planning activity upfront. Examples for in-place system conversion preparation activities could be: Migration from anyDB to SAP HANA Unicode Conversion Upgrade from an older SAP BW like SAP BW7.4 to SAP BW7.5 Examples for in-place and remmote conversion preparation activities could be: Clean-up unused custom code Identify critical custom code Requirements and Constraints The transition preparation activity is mainly required for the in-place system conversion. It relies on findings from the Transition Planning activity. Procedure Execute on Follow-Ups from Transition Planning</p>
	<div>
		<h3>Execute on Follow-Ups from Transition Planning</h3>
		<p>The goal of this task is to execute all preparation activities which should happen before the transition project starts. Prerequisites There is a high-level transition planning document stored in SAP Solution Manager. This preparation activity is of particular relevance in case of a system conversion. Procedure Properly analyze all preparation items which have been identified in the Transition Planning Plan in detail the execution of these items. This could result in own projects depending on the scope of the item. It is in the responsibility of the Project Management work stream to take care of proper project planning. Execute the item according to the plan. How SAP Can Support SAP provides additional information and services for typical preparation activities in case of a system conversion, for instance: Migration of any database to SAP HANA, release upgrade of SAP BW, notes implementation, Unicode conversion. For details of possible steps see also next task (Transition Preparation for In-Place System Conversion).</p>
		<h3>Transition Preparation for In-place System Conversion</h3>
		<p>Parts of the transition preparation can be: Migration from anyDB to SAP HANA Unicode Conversion Upgrade from an older SAP BW like SAP BW7.4 to SAP BW7.5 Implementation of required SAP Notes Work through all gaps which have been identified in the readiness check Procedure These preparation steps differ very much in time and effort. Most of them like the database migration and the SAP BW upgrade, but also the Unicode conversion should be set up as separate projects. In the Migration and Upgrade Planning Workshop, a mandatory check has been performed to identify which requirements are still open before the system conversion to SAP BW/4HANA can be performed. All these identified steps should either be performed here or in the activity Prep Steps &amp; Data Model Adjustment in the Explore phase. For a migration from anyDB to SAP HANA, you should run the HANA BW sizing report to get information about the hardware resources required to migrate onto HANA. Refer to SAP Note 2296290 –New Sizing Report for SAP BW/4HANA. See accelerator section for details. Results The system is now on release SAP BW7.5 on SAP HANA. As a next step, the BW/4HANA Starter Add-on will be installed. How SAP Can Support On help.cap.com SAP offers the SAP BW/4HANA Master Guide as well as the Conversion Guide for SAP BW/4HANA (pdf) with detailed information on required preparation steps for the conversion to SAP BW/4HANA and all required SAP notes. SAP supports customers with separate projects to run a database migration or an SAP BW upgrade. These should be planned as sub projects to the transition to SAP BW/4HANA.</p>
		<h3>Prepare Tools for Project Support</h3>
		<p>There should be at least an SAP Solution Manager 7.2 already set up and configured. Alternatively, you can use SAP Cloud ALM. SAP Solution Manager does both support project execution (e.g. scoping business processes together with SAP Activate), as well as support operations of the new SAP BW/4HANA system after Go-Live (e.g. via system monitoring and root cause analysis). This road map considers SAP Solution Manager 7.2 as available. In case this release level is not available on-premise, SAP offers SAP Solution Manager also via Cloud Appliance Library (CAL). SAP Cloud ALM is a public cloud solution, and always up-to-date. Only configuration of SAP Cloud ALM is required. Procedure SAP Solution Manager In case there is an SAP Solution Manager 7.2 available on-premise, update to the latest support pack. In case this release level is not available on premise, SAP offers SAP Solution Manager also via Cloud Appliance Library (CAL), and use this SAP Solution Manager for project support. The general sequence for the implementation of an SAP Solution Manager system is as follows: You plan the implementation (such as scope, hardware and software requirements, and release restrictions). You plan the system landscape for your use cases. You install the components of your SAP Solution Manager system. You configure your system. You set up the connection to the managed systems. Once SAP Solution Manager is set up, the following areas are key for project support: Solution Implementation – the identification, adaptation, and implementation of new and enhanced business scenarios. Solution Documentation – centralized documentation repository ensuring the relationship of business process definitions with technical configuration decisions. Change Control Management – synchronized deployment of application configuration with integrated project control and quality management. SAP provides customers with a SAP Solution Manager toolset that supports customers in implementation of the project standards for the purposes of the project or operations. It is highly recommended to use the SAP Solution Manager Focused Build service solution. Additional details can be found in the accelerator section. SAP Cloud ALM For SAP Cloud ALM, follow the information provided in the “SAP Cloud ALM for Implementation - Expert Portal”. All sections listed there are relevant for implementation projects. Results You have set up SAP Solution Manager, and have configured it for project support. How SAP Can Support SAP offers a workshop on Agile Implementation Methodology and Tool Coach. SAP can install SAP Solution Manager for you. The corresponding service offer is called “Implementation of SAP Solution Manager for IT Operations”. Please ask your SAP contact (e.g. SAP Client Partner) for more information. Alternatively consider using SAP Solution Manager from the Cloud Appliance Library (CAL). In addition, SAP supports this activity with the “SAP Operations Platform Starter Pack” service component. The SAP Operations Platform Starter Pack is applicable when you need direct assistance with the basic configuration and use of the SAP Solution Manager or Cloud ALM.</p>
		<h3>Perform Data Volume Planning</h3>
		<p>Data volume planning is the starting point of data volume management to analyze the current database and determine how much data can be archived or deleted upfront of a conversion to SAP BW/4HANA. Data Volume Management should be considered prior to a system conversion, in order to reduce the amount of data to be converted (has implications on duration / downtime of the cutover) as well as to reduce the amount of data in memory (has implications of hardware costs). The Data Volume Planning service component is indicated especially if: The database size is greater than 500 GB and/or The database grows faster than 20 GB per month Procedure A detailed look at the system identifies the major pain points and gives an outlook on the most beneficial measures to take (for example, deletion or data archiving) when implementing a data volume strategy. In some cases, the implementation of an enhanced data volume reporting may be the most reasonable first step to take in case the data on the system is still young and therefore the expected effect of a data volume strategy may be small. One central tool supporting data volume reduction is the SAP DVM Work center in SAP Solution Manager, including tools with a special focus on SAP HANA. Guided Self Service: You can generate a best practice document to determine data that can be reduced most efficiently in an SAP system before the conversion. You can use the same tool after the conversion to develop a blueprint for a DVM strategy. Reorganization and Compression: You can use this tool without a SAP HANA context in order to simulate the savings gained by reorganizing tables or databases or compressing the database. In addition, you can simulate the future system size of your system. This is useful for a forecast of the impact any planned measures may cause. Results A decision and road map on how to proceed with DVM upfront of a transition to SAP BW/4HANA. How SAP Can Support In general, the service component starts with a detailed remote data analysis in order to determine the most relevant business objects for data reduction with their available reduction methods, the distribution across fiscal years (data growth) and dependencies on the service type status values as well as possible dependencies to other documents. Based on such detailed analysis of data volume strategy a data management and data archiving strategy will be developed, based on general SAP best practices.</p>
	</div>
	<h2>Sandbox Creation and Prototyping</h2>
	<p>Prototyping is an optional small project on its own. It gives customers a guided start into cloud based, real-time and/or advanced analytics and/or prebuild machine learning solutions and a hands-on experience to realize a use case with a pre-defined scope. As a result, installation and configuration time and effort in the “real” project may be reduced significantly.</p>
	<div>
		<h3>Jump-Start with Setup of Sandbox System</h3>
		<p>Procedure The prototyping approach enables you to evaluate the solution in a short time frame with your business scenarios using your real data, thereby enabling you to quickly validate the value addition, identify and mitigate risks, if any, at an early stage and efficiently plan your IT investments. How SAP Can Support The service Jump-start for Analytics &amp; Data Management helps customers to benefit from SAP’s knowledge and guidance to realize a functioning prototype in a sandbox environment. The approach is to guide customers in realizing a use case in the area of SAP´s cloud analytics solutions, SAP´s advanced analytics solutions or to enhance customers financial processes by applying prebuilt machine learning solutions. It helps customers to identify and visualize key performance indicators (KPI´s) that are relevant for a single business use case or support a customer in leveraging predictive modeling capabilities.</p>
	</div>
	<h2>Execution, Monitoring, and Controlling of Results</h2>
	<p>The purpose of this deliverable is to execute the project management plan and control and monitor the work defined in the project scope statement. Management plans developed during the project preparation phase (see deliverable &quot;Project Management Plan&quot;) guide the approach to management, execution, and control of project activities. The project manager is responsible for ensuring that the management plans are applied at the appropriate level of control. Tasks Direct and Manage Project Execution Monitor and Control Project Activities Manage Issues, Risks and Changes Communicate Status and Progress to Project Stakeholders</p>
	<div>
		<h3>Direct and Manage Project Execution</h3>
		<p>The purpose of this activity is to assure that the project is executed according to what was agreed to in project charter, scope statement and project management plan.</p>
		<h3>Monitor and Control Project Activities</h3>
		<p>The purpose of this activity is to ensure that resources are assigned to all scheduled project activities (and tasks) and that work is progressing and deliverables are produced as expected.</p>
		<h3>Manage Issues, Risks and Changes</h3>
		<p>The purpose of this activity is to capture and manage project issues and risks and changes related to those e.g. changes of project scope, timelines, costs etc.</p>
		<h3>Communicate Status and Progress to Project Stakeholders</h3>
		<p>The purpose of this activity is to make sure that project stakeholders are aware of status and progress of the project including potential disturbances due to existing risks and issues.</p>
	</div>
	<h2>Organizational Change Management Plan</h2>
	<p>The purpose of this deliverable is to present an overview of all planned change management activities. It guarantees that all activities are related to each other, as well as to the overall project plan, and ensures the traceability of OCM activities.</p>
	<div>
		<h3>Prepare Organizational Change Management Roadmap</h3>
		<p>The purpose of this activity is to collect all required input for the OCM roadmap and document it in a format suitable for communication and inclusion into the project management plan.</p>
		<h3>Conduct OCM Workshop with Project Manager and all Project Workstream Owners</h3>
		<p>The purpose of this workshop is to align with the entire project management team regarding OCM activities to be conducted. The OCM roadmap, along with the stakeholder analysis on hand at this stage should be shared. An initial communication strategy should be drafted as part of this workshop.</p>
	</div>
	<h2>Phase Closure and Sign-Off Phase Deliverables</h2>
	<p>The purpose of the phase closure and sign-off deliverable is to: Ensure that all required deliverables from this phase and the project are complete and accurate, and close any outstanding issues Identify lessons learned during the phase to prepare for formal phase closure Capture customer feedback and potential Customer References Tasks Conduct Knowledge Management Gate Conduct Project Quality Gate Conduct Project Management Review Service Obtain Customer Sign-Off for Phase Completion</p>
	<div>
		<h3>Conduct Knowledge Management Gate</h3>
		<p>The purpose of this task is to collect knowledge assets and lessons learned at the end of each phase of the project that can be reused later by other projects. Collecting documents, experiences, project highlights and lessons learned throughout the duration of the project can help to facilitate the project by providing quick access and insights into key deliverables from earlier stages of the project.</p>
		<h3>Conduct Project Quality Gate</h3>
		<p>The purpose of this task is to conduct regular quality checks at defined or critical stages of the project lifecycle to assess the health of the project: Specifically checking that deliverables have been completed with recommended practices Assuring project planning Validating open risks and issues, and measuring customer satisfaction The deliverables assessed at each quality gate will be performed using the quality gate checklist /PtD System with defined expectations to the maturity of particular project deliverables /aspects. Note: New additional key deliverables need to be added in the quality gate checklist by the Project Manager to the different project types.</p>
		<h3>Conduct Project Management Review Service</h3>
		<p>The purpose of this task is to execute a Project Management Review that provides a proactive quality assurance review, with an impartial analysis of all aspects of the project - across all project management disciplines, enabling early detection of project issues with actionable recommendations.</p>
		<h3>Obtain Customer Sign-Off for Phase Completion</h3>
		<p>The purpose of this task is to ensure that all required deliverables from this phase and the project are complete and accurate, and close any outstanding issues. The project manager should identify lessons learned during the phase to prepare for formal project closure and capture customer feedback and potential customer references. Procedure: Identify and list all key deliverables from phase delivery. Confirm status of completion and any outstanding actions. Provide signoff document to signatory. Obtain phase signoff and agreement for any possible outstanding actions. When sign-off is obtained, the project can proceed to the next phase. Use the Phase Signoff Template accelerator for this task.</p>
	</div>
</div>
<h1>Explore</h1>
<p>Once the prepare phase has been finalized considering a detailed planning for the functional and technical work streams the explore phase will be kicked off. Figure 3.1: Activities in the Explore Phase In the Solution Adoption work stream, the training strategy is developed for the end users. In the Application Design &amp; Configuration work stream, the Data Model Adjustment takes place and the data/object transformation process coming in the Realize Phase is prepared. Especially for new implementation scenarios, customers should attend the BW/4HANA Enablement service to get familiar with the BW/4HANA concept and functionality, but also for conversion scenarios to learn how to use the conversion tools. Security Design should be done in this phase, too. The conversion to SAP BW/4HANA may impact your custom code. In the Extensibility work stream, you will need to identify and prioritize affected custom objects that are used productively, and that have to be adjusted as part of the system conversion project. Based on the anticipated application changes, customers should also start creating a strategy for testing and end user training (Testing work stream). Analytics: The Analytics work stream contains the Analytics Design, offering the Analytics Design Workshop which helps to create a detailed road map and blueprint, and explains the new data models in BW/4HANA to enable the customer to start with the data model adjustment. In the Data Management work stream, data load needs to be prepared and planned for the new implementation or system consolidation. In the Data Volume Planning and Data Volume Design activities, a scoping for data volume management (DVM) is defined and the strategy for DVM is developed which will be executed in the Realize phase. The technical design document is created in the Technical Architecture and Infrastructure work stream. Customers will need to further prepare the platform for project delivery. This touches SAP Solution Manager, and additional components depending on the implementation scenario. A sandbox can be created for technical and functional experience gathering. Of course, it depends on the scenario how the sandbox is created. At the end of the Explore phase the DEV environment needs to be set up (again scenario specific). Operations and Support runs an Operations Impact Analysis, to identify IT Support operational areas that require adjustment to safely and efficiently operate the new system as of Go-Live. The actions taken here depend on the SAP operational experience of the customer.</p>
<div>
	</div>
	<h2>Phase Initiation</h2>
	<p>The purpose of the phase initiation deliverable is to formally recognize that a new project phase starts.</p>
	<div>
		<h3>Review Deliverables of Explore Phase</h3>
		<p>The purpose of this task is to review all Deliverables of the Explore Phase with the Customer.</p>
		<h3>Review Acceptance Criteria</h3>
		<p>The purpose of this task is to review the acceptance criteria with the Customer.</p>
		<h3>Review RACI Chart for Explore Phase</h3>
		<p>The purpose of this task is to review the RACI Chart for the Explore Phase with the Customer.</p>
	</div>
	<h2>Execution, Monitoring and Controlling Results</h2>
	<p>The purpose of this deliverable is to execute the project management plan and control and monitor the work defined in the project scope statement. Management plans developed during the project Prepare phase guide the approach to management, execution, and control of project activities. The project manager is responsible for ensuring that the management plans are applied at the appropriate level of control.</p>
	<div>
		<h3>Update Project Management Plan</h3>
		<p>The purpose of this task is to update the project management plan and the subsidiary plans based on the changes agreed during the projects change management process.</p>
		<h3>Direct and Manage Project Execution</h3>
		<p>The purpose of this task is to assure that the project is executed according to what was agreed to in project charter, scope statement and project management plan.</p>
		<h3>Conduct SCRUM Meetings</h3>
		<p>The purpose of this task is to plan, setup and conduct various SCRUM meetings in order to keep the project teams aligned around common vision, share information amongst the SCRUM teams and align individuals within each team. The purpose of these standard meetings is to inform others about work that individual team members do on given day and surface any blockers that team members need help addressing . In Agile implementation project the teams conduct the following meetings: Daily SCRUM meeting short (about 15 minutes) session within each SCRUM team facing the SCRUM board team members report to the team about what they work on and whether they are encountering any issues that they need help with. Objective of this session is to inform and align the team work. Regular SCRUM of SCRUMs meeting session in which SCRUM Masters from individual SCRUM teams share information about what each SCRUM team works on, surface any cross-team alignment topics and resolve any issues that arise between the teams. The session is very similar to integration team meetings conducted in traditional projects. Note: Cadence of these sessions needs to be calibrated, but they should be done on at least bi-weekly basis. The more often they are done the shorter they can be. Sprint Demo the purpose of this meeting is to demonstrate the results of the sprint to customer and obtain acceptance to release the features developed during the sprint. The meeting is attended by Product Owner team, End users and SCRUM team. It is conducted at the end of sprint. Sprint Retrospective or process improvement meeting is conducted right after the Sprint Demo meeting prior to closing the sprint. The objective of this meeting is for the SCRUM team to conduct retrospective of the sprint, identify potential improvements of the process, prioritize and select top improvements to implement in the next sprint.</p>
		<h3>Monitor and Control Project Activities</h3>
		<p>The purpose of this activity is to ensure that resources are assigned to all scheduled project activities (and tasks) and that work is progressing and deliverables are produced as expected.</p>
		<h3>Manage Issues, Risks and Changes</h3>
		<p>The purpose of this activity is to capture and manage project issues and risks and changes related to those e.g. changes of project scope, timeline, costs etc.</p>
		<h3>Communicate Status and Progress to Project Stakeholders</h3>
		<p>The purpose of this task is to ensure that each contract is closed by verifying that all work specified in the contractual arrangement was completed, and that all defined deliverables were accepted.</p>
	</div>
	<h2>Planning</h2>
	<p>The detailed project planning started in the Prepare phase and is continued across the Explore phase. The aim is to constantly adapt and refine the initial plan from the Prepare phase. In particular, the implications from the different design workshops need to be included in the project plan. Requirements and Constraints This activity is required for all scenarios. Procedure Refine Project Planning</p>
	<div>
		<h3>Refine Project Planning</h3>
		<p>The goal of this task is to refine the project plan based on the learnings and decisions from the Explore phase. This task continues throughout the Prepare and Explore phase. Procedure Project management in the context of an SAP implementation has been documented in detail in SAP Activate road maps (e.g. “SAP Activate methodology for Business Suite and On-Premise – Agile”). See accelerator section for a link. All general project management activities, tasks, and accelerators can be taken from there, by filtering on the Project Management work stream (see accelerator section). Please make sure to include the results from the &quot;Application: Design &amp; Configuration&quot; work stream so far.</p>
	</div>
	<h2>Learning Design</h2>
	<p>In this activity, the training requirements for key user and end users are analyzed and documented. Based on the analysis, a training plan will be designed, and executed in the following project phases. Requirements and Constraints This activity is required for all activities. It is based on an initial assessment processed in the Prepare phase of the project (see activityTransition Planning). The project team has been enabled already and is not in scope of this activity. Procedure Develop a Training Concept Results A training concept has been developed for key users and end users.</p>
	<div>
		<h3>Develop a Training Concept</h3>
		<p>The objective of this task is to develop a training concept for key users and end users based on planned applications, existing skills levels, knowledge gaps, and locations of training requirements. Procedure To develop a training concept for key users and end users, proceed as follows: Evaluate the business processes (including user interfaces) in scope for implementation and analyze the required skills. Evaluate SAP individual standard training from SAP Education courses (Classroom and Web-based Trainings) Develop and document a training concept including obligatory and recommended training per user role, mentoring, and coaching and in alignment with the project plan Per future role, assign users to trainings. Please keep in mind: Skill levels, knowledge gaps, and training requirements could depend on the location of end users. Special care needs to be taken for key users: Key users are often involved in various implementation activities throughout the project (e.g. testing the newly implemented functionality). Therefore, they need to be trained early. Key users could potentially support and run end-user trainings. After Go-Live, key users play an important role in customer’s incident management process. In case of issues or questions, end users can contact their local key users first, before filing an incident. SAP recommends establishing a network of well-trained key users as part of the transition project. Results A training concept has been developed and documented. How SAP Can Support SAP offers SAP Learning Classes in person or virtually to support customers in implementation projects. The SAP BW/4HANA Learning Journey provides an overview of the corresponding course offerings. See accelerator section for details.</p>
	</div>
	<h2>Organizational Change Management (OCM)</h2>
	<p>The planning and execution of Organizational Change Management in the context of an SAP implementation has been described in detail already. Most current information on Organizational Change Management can be found in the road map “SAP Activate methodology for Business Suite and On-Premise – Agile”. See the accelerator section for a link. In the context of SAP Activate, Organizational Change Management is part of the “Solution Adoption” workstream (the Road Map Viewer offers to filter on workstream names). Procedure Execute Change Impact Analysis</p>
	<div>
		<h3>Execute Change Impact Analysis</h3>
		<p>The objective of this task is to execute an organizational change impact analysis. Procedure Execute a Change Impact Analysis as documented in SAP Activate. Follow the link in the accelerator section, and run the analysis as described in the road map “SAP Activate methodology for Business Suite and On-Premise – Agile” (work stream “Application: Solution Adoption”).</p>
	</div>
	<h2>Prep Steps for Conversions</h2>
	<p>If you want to run an in-place conversion or a remote conversion you should perform the required preparation steps as shown in the figure below. Figure 3.2 Preparation steps for the in-place and remote conversion Procedure Use the Maintenance Planner to prepare the installation of the SAP BW/4HANA Starter Add-on (in-place conversion) Run the “Pre-Checks” (program RS_B4HANA_CONVERSION_CONTROL and Simplification List for SAP BW/4HANA) Install SAP Notes for the installation of SAP BW/4HANA Add-On (if required) Run the Custom Code Check (see Activity Custom Code Impact Analysis) Make yourself familiar with the new data model and concept in SAP BW/4HANA and the conversion tools offered by SAP. A good starting point is the SAP First Guidance – complete functional scope (CFS) for SAP BW/4HANA (see accelerator section). How SAP Can Support SAP offers an Analytics Design Workshop (ADW) which helps in refining the implementation aspects and creating a transition project plan for moving the existing Analytics solution landscape to SAP BW/4HANA. The workshop offers empowerment options to better understand implementation aspects and the new modelling concept and gives a good basis for the data model adjustment. For more information on the ADW, see the activity Analytics Design. In addition, SAP offers an SAP BW/4HANA Enablement Service which is offered as SAP Professional Service (CRM 50139425). It gives an introduction and enablement for the next generation data warehousing with SAP BW/4HANA. The following topics can be covered (optionally): Modelling for BW/4 HANA Query Building (Eclipse-based) ETL Processes Transporting SQL Based Data Warehousing Data Acquisition (SDI /SDQ) Data Management Reporting Conversion Tools</p>
	<div>
		<h3>Install and Run the Pre-Checks</h3>
		<p>SAP supports your conversion project by providing pre-checks that identify some important steps you need to take to ensure your system is compatible with the conversion process. Perform these checks before starting the realization phase, in order to have enough time to solve any issues before starting the conversion processes. Pre-checks are shipped to customers that want to convert to SAP BW/4HANA in the form of SAP Notes. Customers can use these pre-checks to find out what mandatory steps they must carry out before converting to SAP BW/4HANA. The results list the instances that need to be addressed before attempting the conversion process. Procedure Follow the instructions provided in SAP Note 2383530 and use the SAP BW Note Analyzer to install the pre-check tools (see Appendix Landscape Preparation for more details). Make sure to use latest version of this SAP Note and regularly check for updates of the attached XML files. Implement and run the SAP BW Note Analyzer in your development system and transport the recorded changes to the other systems in your landscape (quality assurance, consolidation, production). Then run the SAP BW Note Analyzer again in the other systems to validate that all changes are applied correctly. After the installation, you can also check if the tools are up-to-date directly in the SAP BW/4HANA Transfer Cockpit. You need authorization for authorization objects S_RS_B4H. Start the Transfer Cockpit (transaction RSB4HCONV) and execute the pre-check (this executes program RS_B4HANA_CONVERSION_CONTROL). Results The pre-check program determines which objects are compatible with SAP BW/4HANA and which objects are not available in SAP BW/4HANA and can be automatically converted, deleted, or need manual adjustments. The pre-check then provides links to the documentation of the corresponding Simplification Items (i.e. the matching SAP Notes). Refer to the Conversion Guide for SAP BW/4HANA 2.0, chapter 3.11, for detailed documentation.</p>
		<h3>Prepare the Installation of the SAP BW/4HANA Starter Add-on</h3>
		<p>The SAP BW/4HANA Starter add-on is an SAP NetWeaver add-on that supports you when performing an in-place conversion of an SAP BW 7.5 system running on an SAP HANA database as its primary database. You can implement the SAP BW/4HANA Starter add-on as of SAP BW 7.5 Support Package Stack 5 and use it to prepare a conversion of the system to SAP BW/4HANA. Before the Add-on can be installed, it may be necessary to install corresponding SAP Notes. Procedure Follow the instructions for the preparation steps under “Installing the SAP BW/4HANA Starter Add-on” in the Conversion Guide for SAP B/4HANA 2.0, chapter 4.2: Update Software Patch Manager and Add-on Installation Tool Download Software using Maintenance Planner Results The BW/4HANA Starter Add-on can be installed. How SAP Can Support SAP offers SAP Notes which give guidance for the handling and usage of the SAP BW/4HANA Starter Add-on (SAP Note 2189708) and for prerequisites and installation (SAP Note 2246699). Refer to the Accelerators section for details.</p>
		<h3>General Application Preparations for In-Place Conversion</h3>
		<p>Before starting in-place conversion activities, you need to do the preparatory activities described in this section. Procedure Remove Client 066 Client 066 had been used to deliver services by SAP Active Global Support (SAP NetWeaver 7.4 and older). This client is not used anymore, therefore you can safely remove the client 066 if available. The client is not used in SAP BW/4HANA either. To prevent issues, for example, with job scheduling, you have to remove it before starting the system conversion. To remove client 066, proceed as described in SAP Note 1749142. Uninstall Add-ons If you have add-ons (from SAP or other vendors) installed on your SAP BW system that are not released for the go-to SAP BW/4HANA release, you need to uninstall them. If you do not uninstall these apps, the Maintenance Planner will not allow a conversion of your system. 1. Check SAP Note 2189708 for a list of add-ons that are not compatible with SAP BW/4HANA 2.0 and must be uninstalled. 2. If you have an add-on installed on your system which is not supported anymore on SAP BW/4HANA, prior to the system conversion, the add-on must be uninstalled. 3. A list of add-ons which can be uninstalled is available in SAP Note 2011192. 4. If your add-on is not listed there, please open a customer incident on the component BW-B4H-LM. Uninstall SAP Fiori Apps If you have SAP Fiori apps installed locally on your SAP BW system, you need to uninstall them, if they are not released for SAP User Interface Technology 7.50 or higher (SAP_UI 7.50, 7.51 or 7.52). If you do not uninstall these apps, the Maintenance Planner will not allow a conversion for your system. Results You have performed all preparatory steps for the in-place conversion.</p>
		<h3>General Application Preparations for Remote Conversion</h3>
		<p>Executing a scope transfer during a remote conversion will require additional disk space that you have to provide. Procedure Provide enough free disk space in the original and target system to store about 20 percent of the expected data volume to be transferred. If, for example, you want to transfer one terabyte of data from the original system to the target system, ensure that you have 200 GB of free disk space in the original system. SAP BW/4HANA Conversion Cockpit compresses the data for transfer and uses the free space in the original system to store the data in the table cluster CNVLTRL_BWCLU. You can reclaim the hard disk space after the scope transfer is completed. Results There is enough disk space available for the scope transfer during the remote conversion.</p>
		<h3>Installation of the SAP Landscape Transformation Add-on for Remote Conversion</h3>
		<p>Carry out the following preparation in the sender, receiver and the ERP systems: Install the prepare package as described in SAP Note 2383530. Install the Landscape Transformation add-on on the original SAP BW system (sender), target SAP BW/4HANA system (receiver) and control system (if one is available). See SAP Notes 1577441, 1577503, 1648747) Install ODP-related notes in your source systems as described in SAP Note 2383530. Define authorization roles in the customer system. Procedure Follow the description on Installing SAP Landscape Transformation Add-on in the Conversion Guide for SAP BW/4HANA 2.0. Results The system is now ready for Remote Conversion with the sender, receiver and control systems set up.</p>
		<h3>Check the List of object-specific Preparations</h3>
		<p>There may be also application-specific preparation steps you have to perform for an in-place, remote, or shell conversion. These steps are all part of the Simplification List, the most important ones are also listed in the Conversion Guide. Procedure Check the list of object specific preparations in the Conversion Guide and perform the mandatory steps described in the corresponding SAP Notes. Results The application-specific preparation steps are performed.</p>
	</div>
	<h2>Data Volume Design</h2>
	<p>Data volume design is the follow-up of data volume planning. It should support the implementation of a data management and data archiving strategy or alternatively supports the assessment of existing strategies. Requirements and Constraints This activity is of general interest for all scenarios.</p>
	<div>
		<h3>Define Data Volume Strategy</h3>
		<p>Based on the analysis results from the data volume planning, the blueprint of a data management and archiving strategy is developed from a technical point of view and using general SAP best practices. In the second phase, the data volume strategy is realized. Procedure determine the type and amount of data that can be archived, as well as the corresponding archiving objects reveal connections between tables and archiving objects provide information on which archiving objects should be used and in which order and frequency consider retention periods as well as the requirements of your business teams maintain the customizing for the archiving objects and the definition of archiving selection variants test archiving of some documents to demonstrate the retrieval of archived data Results There is a documented and implemented strategy on Data Volume Management. How SAP Can Support Data Volume Strategy is part of the Data Volume Management (DVM) service component.. Data Volume Strategy includes all necessary steps to prepare for the implementation and for the assessment of the following methodologies: Data avoidance Data summarization Data deletion Data archiving During the test archiving as preparation for Go-Live, the service also offers assistance, if help is required on performing mass tests and performance tests.</p>
	</div>
	<h2>Security Design</h2>
	<p>SAP BW/4HANA uses the user administration and authentication mechanisms from the Application Server for ABAP. The security recommendations and guidelines for user administration and authentication described in the Security Guide for SAP NetWeaver Application Server for ABAP therefore also apply to SAP BW/4HANA. In addition to these guidelines, SAP provides information about user administration and authentication that specifically applies to SAP BW/4HANA. Requirements and Constraints Security Design should be a part of every transition scenario. The SAP BW/4HANA conversion project involves many areas of the SAP BW due to its technical complexity. This results in a comparably large involvement of business users and IT users during the project. IT users are responsible for all IT administration and remodeling tasks which will mainly arise during the project and business users will be affected by necessary testing activities after changes occurred. The use of the Transfer Cockpit in productive systems could require special alignment between business users who are owners of business processes and their data and the IT users responsible for transferring the data flow including the inherent data to new SAP BW/4HANA supported objects. During the scope transfer of a dataflow/unsupported BW objects the data of the involved InfoProviders needs to be accessed. For a period of time the authorizations to access the InfoProviders data must be assigned to the executing user of the Transfer Cockpit. Procedure Supply the affected data owners with the authorization for the Transfer Cockpit and execute the tested and prepared Transfer Cockpit scenario in the productive environment.</p>
	<div>
		<h3>Run Authorization Concept Check and Adoption</h3>
		<p>During a scope transfer with the Transfer Cockpit, legacy objects will be replaced by their SAP HANA-optimized counterparts (e.g. InfoCube by DataStore object (advanced ADSO)). After the transfer, the user needs a proper authorization for the SAP HANA-optimized object instead of the legacy object (e.g. InfoCube). Procedure After the SAP BW/4HANA Conversion authorization objects need to be adjusted, for more information please check SAP Note 2468657 BW4SL Standard Authorizations which contains a list with SAP BW authorization objects and their availability in SAP BW/4HANA. Results You are able to implement SAP BW analysis authorizations and SAP HANA analytics privileges. How SAP Can Support SAP supports the security design with a Security Design Workshop. With the SAP Security Design Workshop for SAP BW/4HANA, customers who are implementing SAP BW/4HANA with or without mixed modeling, get guidance, best practice information and knowledge transfer about SAP BW analysis authorizations and SAP HANA analytical privileges.</p>
	</div>
	<h2>Integration Design</h2>
	<p>In this activity the data integration scenarios to SAP BW/4HANA should be evaluated and designed. Evaluate the following data integration options: Write Interface Enabled DataStore Object With the new DataStore object setting “Write Interface Enabled”, other tools can write directly into the Inbound Queue ot the DataStore Object. This allows integration of data from SAP Data Services SAP NetWeaver PI SAP Data Intelligence Integration (3.0 on prem.) SAP Cloud Platform Integration (CPI) 3rd party tools Figure: Data Sources pushing data to the write interface Enabled DataStore Object SAP Cloud Data Integration Data from SAP Cloud solutions like SAP S/4HANA Cloud, SAP C/4HANA, SAP Success Factors etc. is integrated via the connections displayed in the figure below. Figure: SAP Cloud Data Integration to SAP BW/4HANA Open Hub Destination Data can also be distributed from SAP BW/4HANA to other storage locations via the Open Hub Destination. Figure: Data Distribution via Open Hub Destination Integration with SAP Data Intelligence SAP Data Intelligence handles Machine Learning and is an all-in-one data orchestration solution that discovers, refines, enriches, and governs any type, variety, and volume of data across the entire distributed data landscape. SAP Data Intelligence can be integrated with SAP BW/4HANA (as data source) and SAP BW/4HANA and SAP Analytics Cloud as Machine Learning consumption platform for the BW and Analytics team. Figure: Integraion with SAP Data Intelligence How SAP Can Support SAP offers the Analytics Design Workshop which covers the integration design aspects of the analytics landscape. See the activity “Analytics Design” for details on the Analytics Design Workshop.</p>
	<div>
		<h3>Decide on Integration with SAP Analytics Cloud</h3>
		<p>The objective of this task is to decide on the integration with SAP Analytics Cloud. SAP Analytics Cloud combines business intelligence, planning and predictive analytics into one cloud solution that is entirely based on the SAP Cloud platform. The Software-as-a-Service (SaaS) solution provides access to all the possible data so that the user can visualize, plan and predict using real-time data. SAP Analytics Cloud is a foundation of a real-time steering in the digital age. It allows integration of all internal and external data sources in only one solution. Figure 3.9: Data Connections Environment Procedure Decide if SAP BW/4HANA should be connected to SAP Analytics Cloud via import data connection or live data connection – refer to the guided playlist for SAP Analytics Cloud “Connections Overview” for details (see accelerator section). Results As a result, you design the integration of SAP BW/4HANA with SAP Analytics Cloud.</p>
	</div>
	<h2>Analytics Design</h2>
	<p>Based on the findings from the Discover phase, which the customer typically has derived in an Analytics Strategy Workshop, now the analytics design needs to be done in detail with a detailed roadmap and project plan. Requirements and Constraints The high-level analytics architecture should have been defined in the Discover phase.</p>
	<div>
		<h3>Run an Analytics Design Workshop</h3>
		<p>The customer needs to get an in-depth technical understanding of the new SAP Analytics technology coming with SAP BW/4HANA and has to define all required steps in the detailed roadmap which are necessary for the transition of the existing analytics landscape to SAP BW/4HANA. Prerequisites Ideally, an analytics strategy workshop has already been conducted to define the strategic SAP solution components for analytics, resulting in a target analytics architecture. Alternatively, the customer has already conducted similar activities to get familiar with strategic SAP analytics solution components around SAP S/4HANA embedded analytics, enterprise data warehousing (SAP BW based, or SAP HANA based), and the BI strategy (SAP Analytics Cloud, SAP Digital Boardroom, and SAP BusinessObjects BI platform), and defined a strategic analytics infrastructure. Procedure The outcome of the Analytics Strategy Workshop and the Migration and Upgrade Planning Workshop should be a high-level architecture for the target solution landscape and a filled checklist of the mandatory preparation steps for the system conversion. Based on these outcomes, the detailed roadmap and the detailed project plan should be created. Customer should be empowered to understand all aspects of the data model transformation to SAP BW/4HANA Model a blueprint for the target architecture with integration of BW and HANA models Results The target architecture has been defined and a detailed project plan is available. The customer is prepared to perform the preparation steps and start the actual configuration of the data models for the system conversion (activity Prep Steps and Data Model Adjustment). How SAP Can Support SAP offers the Analytics Design Workshop which covers Detailed design of the target architecture the creation of the detailed roadmap and project plan the Empowerment of the team to understand the implementation/conversion aspects modeling of a blueprint Figure 3.7: Topics covered in the Analytics Design Workshop The Analytics Design Workshop ideally refers to the results of the Analytics Strategy Workshop and a Migration and Upgrade Planning Workshop. A scoping process prior to the workshop helps to tailor the service to the customer requirements.</p>
	</div>
	<h2>Custom Code Analysis</h2>
	<p>As part of the transition or system conversion, customers need to identify code, that has to be adjusted (“must-do’s”), and code that should be adjusted (“should-do’s”). Requirements and Constraints This activity is relevant in case own code needs to be made ready for SAP BW/4HANA (e.g. the system conversion case).</p>
	<div>
		<h3>Perform Custom Code Check and Adoption</h3>
		<p>As documented in simplification items, SAP BW/4HANA provides many simplifications of application functionality compared to SAP BW. Therefore, many programs, function modules, and classes (millions of lines of standard code) and thousands of standard dictionary objects of SAP BW are not available in SAP BW/4HANA. Any use of such objects needs to be replaced in custom code. It&#x27;s recommended to use only documented standard interfaces (API). SAP BW-specific customer enhancements (often called &quot;customer exits&quot;, transaction CMOD) are not available in SAP BW/4HANA. For several SAP BW releases, SAP has offered corresponding enhancement spots. If customer enhancements are used in SAP BW, the code will have to be adjusted and converted to enhancement spots in SAP BW/4HANA. Procedure To support you in detecting custom code that needs to be adapted for the move to SAPBW/4HANA, SAP is providing a Code Scan Tool as part of the SAP BW/4HANA Transfer Cockpit. For example, you can verify, if custom code embedded in transformations will be compatible with the SAP BW/4HANA data structures and scope. For more information, see details related to custom code within SAP Documentation: SAP Help and in SAP Note 2462639. Additionally, you may refer to the custom code analysis results in SAP Readiness Check for SAP BW/4HANA (see task “Check the Conversion Readiness of the current SAP Business Warehouse” in the Discover phase). How SAP Can Support The Migration and Upgrade Planning Workshop delivers a plan for custom code management and first insights on the necessary adjustment steps. SAP Readiness Check for SAP BW/4HANA gives a comprehensive overview of critical custom code in the analyzed source system. SAP offers the Intelligent Custom Code Management Transformation service component to help in transforming the ABAP code properly to achieve maximum benefit. The main goal of this service offering is to evaluate the results of the ABAP Analysis tools. Typically, the list of identified critical SAP ABAP custom objects is large and can be categorized into 3 types of objects: which has to be adjusted (“must-do’s”) which should be adjusted (“should-do’s”) which can be adjusted (“nice-to-have”) The Intelligent Custom Code Management Transformation service component can be delivered in several flavors: Custom Code Retirement: It offers a tailored strategy for identification and decommissioning of unused custom code. Custom code retirement is an important innovation readiness aspect, helping customers reduce maintenance costs and free up resources for new innovation projects. Custom Code Quality Improvement: It offers a customer-tailored approach for efficient custom code quality management. The purpose of a custom code quality improvement project is to describe the overall quality management approach, the related tools, and the necessary steps that must be performed to continuously monitor and improve the quality of custom code objects in a customer’s solution Custom Code Impact Analysis: It helps to evaluate the impact of change on custom code. With the results of the analysis, the adjustments on custom code can be reviewed and even mitigated semi-automatedly. Custom Code Governance: It ensures that the system is as close as possible to standard and that customers have a future oriented governance model for extensibility.</p>
	</div>
	<h2>Test Planning</h2>
	<p>To minimize the number of issues during or after Go-Live, it is critical to manage the quality of the solution. As a part of any scheduled maintenance event, it is necessary to consider and plan the testing cycles required to mitigate production support issues. At this phase of a system conversion project, it is necessary to evaluate the existing test and quality management processes and procedures that could be leveraged to support the project. The following key elements of the test planning must be documented in the test strategy: Project Testing Objectives &amp; Assumptions, e.g.: Unit Testing is complete before Integration Testing Unit Testing is only required for delta functionality Test Scope Types of Testing, e.g. Unit Testing Business Process (String) Testing Integration testing Data Conversion Testing User Acceptance Testing Testing Approach Description, how different test types relate to each other, e.g. successful unit test is a pre-requisite for doing a string test or migration test results might lead into a pre-requisite for a user acceptance testing Testing Deliverables, e.g. Test processes per phase, test environment, test tools Test Automation Testing Tools Which tools will be used to perform different tests (e.g. SAP Solution Manager)? Defect Management Description of how defects will be documented (e.g. Test Workbench in SAP Solution Manager) Roles and Responsibilities Description of required test roles and responsibilities, e.g. Test Lead and responsibilities of individual project team members related to testing Requirements and Constraints This activity is required for all scenarios. Procedure Make yourself familiar with the tools in test management, and set up test management in SAP Solution Manager Test Scope Determination Detailed Test Planning</p>
	<div>
		<h3>Set Up Test Management in SAP Solution Manager</h3>
		<p>The goal of this task is to make you familiar with the test tools in SAP Solution Manager, and their setup. How SAP Can Support SAP offers Expert Guided Implementation (EGI) sessions to Enterprise Support customers. See the SAP Enterprise Support Academy for getting a complete overview. Some EGI sessions that help you set up Test Management in SAP Solution Manager are listed in the accelerator section.</p>
		<h3>Test Scope Determination</h3>
		<p>The scope of testing for a project, regardless if the project is executed independently or as part of a release, needs to be determined early in order to ensure the testing environments and materials are available for execution. With such a variety of testing cycles (e.g. integration, regression, performance, cutover, user acceptance, etc.) available, it is important to define the cycles that are required to support the planned conversion event. Prerequisites Test and quality management processes and procedures should already be in place. Procedure Evaluate the existing process and procedures to determine the different testing cycles required to support the project. Utilize the existing process and procedures to guide the decision-making process on how to determine the applicable test cases and test scripts. As a best practice to compile test cases and test scripts, it is recommended to define business critical transactions/reports, evaluate most frequently used transactions/reports, and analyze prior productions support issues. In particular for new implementation: Determine the overall test data approach by aligning with the overall data migration approach. The test data approach will be documented as part of the testing strategy. Document your findings in a test strategy document, and store it centrally in SAP Solution Manager Results As a result, a test strategy, and a defined set of test cycles, test cases, and test scripts in scope to support the conversion project are created.</p>
		<h3>Detailed Test Planning</h3>
		<p>Scoping and planning the tests required for the transition project is a requirement regardless if the project is executed as a “pure” technical conversion, or as a new implementation project. The focus is to determine which of the testing cycles (e.g. functional, scenario, integration, regression, user acceptance, performance, and/or cutover) are required to fulfill the quality gate criteria of the Realize phase. Furthermore, the start date, duration, criteria, and resources for each of the required testing cycles needs to be planned. Procedure Evaluate and enable test management and test automation tools to support the testing cycles. Execute the tasks within this activity utilizing the SAP Application Lifecycle Management Best Practices for Test Management. Tailor the templates for Test Strategy, and the high-level Functional Test Plan to your needs. Create a detailed testing plan that is integrated with the project plan and aligned with the overall SAP Software Change Management strategy. The plan should support the objective of mitigating risk both to the end-state solution and the cutover process required to position the end-state.</p>
	</div>
	<h2>Data Migration Design for Remote Conversion</h2>
	<p>As a preparation of the Remote Conversion Execution Service it is mandatory to run a Data Migration Assessment (Remote Conversion Assessment) in the Explore phase to set the scene for the data transfer from the old BW system to the SAP BW/4HANA which will be installed as new implementation. The findings of the assessment have to be taken into consideration to close the gaps before the data migration into the new system can take place. The Assessment is the starting point for planning and preparing the Remote Conversion. The graphic displays the detailed steps from planning to go-live of a Remote Conversion. Figure 3.8: Remote Conversion Details Requirements and Constraints The Data Migration Assessment is only mandatory for conversion scenarios which issue the Remote Conversion Execution Service offered by SAP. Procedure Perform the Data Migration Assessment for the Remote conversion Perform the Preparation steps and prepare the data model adjustment for the Remote conversion In case of a remote conversion including SAP BPC, perform the migration of SAP BPC relevant data</p>
	<div>
		<h3>Perform the Data Migration Assessment</h3>
		<p>With the BW/4HANA Remote Conversion Assessment, SAP helps customers to prepare the landscape and the project for a successful remote conversion with the Remote Conversion Execution Service. SAP experts will assess your landscape transformation or data migration requirements in detail, performing technical analysis on your SAP systems and leading you through a series of discussions that identify your detailed transformation requirements. A tailor-made solution proposal for the remote conversion will be provided. Procedure The Remote Conversion Assessment service aims to provide information on the Remote Conversion and supporting the customer in planning the preparation activities for the execution phase. The Assessment is mandatory if the remote conversion is done with the Remote Conversion Execution Service. Key Objectives of the Service: Helping in the preparation of the remote conversion with Remote Conversion Execution Service Check of the sender system and preparation activities (like Transfer Cockpit and software installation) Identify and tailor the scope Feasibility check of the planned transition Creation of a WBS and define responsibilities Project Planning: Planning of the execution cycles and Go-live Creation of an effort estimation How SAP Can Support SAP offers the Data Migration Assessment service component as a mandatory first step in a Remote Conversion scenario in combination with the Remote Conversion Execution Service. The Data Migration Assessment helps to identify all the steps which have to be taken in the task Perform the Preparation Steps and prepare the Data Model Adjustment for the Remote Conversion and in the activity Data Migration &amp; Verification.</p>
		<h3>Make yourself familiar with the SAP BW/4HANA Conversion Cockpit for the Remote Conversion</h3>
		<p>If the customer performs a remote conversion to SAP BW/4HANA, they may want to transport certain configuration and data models from the existing landscape to SAP BW/4HANA. The SAP BW/4HANA Conversion Cockpit is used to convert from classic SAP BW to SAP BW/4HANA system in a remote conversion and enables the migration of objects and dataflows on SAP BW7.3 or higher (and not 3.x). It contains guided step-by-step activities and a status management and progress tracking ensuring a good documentation of executed activities and prevention of manual errors. Procedure Make use of the outcome of the Data Migration Assessment and define the steps which have to be taken to fulfill all requirements for the data transfer. Make yourself familiar with the documentation of the SAP BW/4HANA Conversion Cockpit in the Conversion Guide for SAP BW/4HANA. How SAP Can Support The transfer of the objects is supported by the SAP Professional Service offering Remote System Conversion to SAP BW/4HANA. For a detailed description refer to the activity Data Migration &amp; Verification in the Realize phase. For PE customers, an “Enablement Workshop for Data and Analytics” is an additional offering which comes with several flavors. The “Enablement on BW/4 Migration Tools” flavor can support the customer with an introduction to the migration tools required for data migration in the remote conversion scenario.</p>
		<h3>Perform the Migration of Data relevant for SAP BPC</h3>
		<p>If the customer performs a remote conversion to SAP BW/4HANA with SAP BPC, they may want to transport certain configuration and data models from the existing landscape to SAP BW/4HANA. Procedure In case of a remote conversion of BPC Standard Model to BPC powered by BW/4HANA, it is necessary to use the backup &amp; restore tool UJBR. In case of a remote conversion of BPC Embedded Model to BPC powered by BW/4HANA, the procedure is identical to the conversion of other BW objects. How SAP can Support See in task Perform the Preparation Steps and prepare the Data Model Adjustment for the Remote Conversion, usage of the Transfer Cockpit.</p>
	</div>
	<h2>Sandbox System Setup for Remote Conversion</h2>
	<p>The purpose of this activity is to set up the sandbox system either as an SAP Cloud Appliance installation or on premise. How SAP Can Support SAP offers the installation of a sandbox (separate installation, or as part of the “Fast Start Bundle”) in the service component System Installation.</p>
	<div>
		<h3>Setup of the Sandbox System</h3>
		<p>The goal of this task is to provide a sandbox system that is available for testing purposes by the project team to begin the Realize phase. Procedure Install a software appliance for project jump-starts. The appliance can be consumed in two ways: Hosted in the cloud: Customers can access the appliance via the SAP Cloud Appliance Library (SAP CAL, https://cal.sap.com) in a pay-per-use model hosted on Amazon Web Services (AWS). When using the SAP CAL option, the customer can choose between a 30-day trial and a longer-lasting engagement. With the trial, only AWS hosting fees need to be paid by the customer. If the customer opts to go beyond the 30-day limit, an SAP CAL subscription license and a regular SAP BW/4HANA license is required. Installed on-premise: If customers/partners prefer an on-site installation on their own hardware, they can order a Blu-Ray disc from SAP and unpack the appliance from the Blu-Ray. A regular SAP BW/4HANA license is required. See also activity Provide a Trial System in the Discover phase, and the task Define Cutover Approach in the Prepare phase. Perform the following steps as described in “Provisioning SAP BW/4HANA System” in the Conversion Guide for SAP S/4HANA: Installing SAP HANA Download Software using Maintenance Planner Installing SAP BW/4HANA Initial System Configuration Creating Source Systems Updating System Landscape using SAP BW Note Analyzer Results The sandbox system is set up. How SAP Can Support SAP offers the installation of a sandbox in the service Jump-Start for Analytics and Data Management (see task Sandbox Creation and Prototyping in the Prepare phase).</p>
	</div>
	<h2>DEV Setup for Remote Conversion</h2>
	<p>Before configuration and development work starts in the Realize phase, the development system (DEV) needs to be set up. Requirements and Constraints This activity is required for New Implementation, Remote Conversion, and Shell Conversion. There is a technical design document stored in SAP Solution Manager which includes the technical deployment plan, and the software components which need to be installed / updated. Procedure DEV Setup or re-use of Sandbox environment Install the SAP BW/4HANA Basis Content Add-on and the SAP BW/4HANA Content Add-on Result The Dev system is installed and set up with the content add-ons.</p>
	<div>
		<h3>Setup of the DEV System</h3>
		<p>The goal of this task is to provide a viable, correctly configured technical development environment that is available for use by the project team to begin the Realize phase. Procedure Proceed as follows: Decide if the sandbox environment should be re-used as the DEV environment. In this case no additional system installation is required. When having used the virtual appliance as sandbox environment, a client that is ”ready-to-activate” is already in place and can be used. Please note: A sandbox which has been set up by using the SAP BluRays, and which is used as a DEV system afterwards may have activated business functionality which is subject to additional licensing. For a new installation, perform the following steps as described in “Provisioning SAP BW/4HANA System” in the Conversion Guide for SAP S/4HANA 2.0, chapter 5.1: Installing SAP HANA Download Software using Maintenance Planner Installing SAP BW/4HANA Installing SAP Landscape Transformation 2.0 Add-on Initial System Configuration Creating Source Systems Updating System Landscape using SAP BW Note Analyzer Results Finally, the DEV environment is set up. How SAP Can Support SAP offers the (Greenfield) Implementation Service for SAP BW/4HANA on Premise which is offered as an SAP Professional Service. This service contains a mandatory scope item, to install SAP HANA, SAP BW/4HANA On-Premise and configure one source system (ECC) using ODP technology. In detail the following steps are covered: Installation of an SAP HANA System and SAP BW/4HANA system in the development environment Basic configuration of SAP HANA and SAP BW/4HANA development system Replication of global settings and exchange rates Implementation of ODP 2.0 if basis level component in ECC source system is appropriate (SAP_BASIS &gt;=730) Basic configuration in order to connect one SAP ECC source system In addition, the service offers the following scope options: Activate FI or CP: Activation of one content area from FI or CO from the business content. Activate SD/MM (no Cockpit): Activation of one content area from MM or SD from the business content, if LO Cockpit is already configured in source system. Activate SD/MM and LO Cockpit: Activation of one content area from MM or SD from the business content. LO Cockpit configuration for one area from MM or SD. ODP 1.0 Framework: Implementation of ODP 1.0 if SAP BASIS level in source system is &lt;730. The activation of the business content can be done in the activity &quot;Configuration&quot; in the Realize phase.</p>
	</div>
	<h2>Sizing</h2>
	<p>Run a capacity estimation on the required hardware for application server and databases (CPU, RAM, storage, network). There are several levels for sizing: Budget sizing for smaller companies, using very simple algorithms. Advanced sizing for medium to large companies, using throughput estimates, questionnaires, formulas. Advanced sizing uses standard tools (e.g. Quick Sizer) and focus on core business processes. Expert sizing is based on a custom capacity model which for instance may also include custom coding. Timing: Explore phase (non-production systems), realize phase (production and production-like systems) Requirements and Constraints This activity is required for all scenarios. There is an implementation plan available which provides first information about the target solution. Procedure Perform / Check Sizing Results There is a documented estimate on the required hardware, which can be discussed with the hardware vendor.</p>
	<div>
		<h3>Perform / Check Sizing</h3>
		<p>The objective of this task is to either perform a sizing, or in case a first estimate has been provided already check the initial sizing. Procedure An initial sizing should have been provided either as part of the “Technical Architecture and Infrastructure Workshop&quot;, or as part of the “Migration and Upgrade Planning Workshop” in the Prepare phase. In case initial sizing has not been done so far or needs to be updated, you should follow the sizing procedures documented in the SAP note 1793345 “Sizing for SAP Suite on HANA” (see accelerator section). For new implementation, initial sizing is performed in the Quicksizer tool (see “General Information on Sizing” in the accelerator section). Run this activity for all related infrastructure components (e.g. including Front End Server). In case of an in-place or remote conversion, the sizing report should be executed periodically in the productive system throughout the project to check on the growth of the source system. For a Shell conversion it is mandatory to check the sizing report of the source system and manually calculate the sizing considering the objects that will be transferred to the new system. Results A sizing has been performed and is documented. How SAP Can Support SAP can perform this task with the “Size to Scale” (former Advanced Sizing) service component. The service component fits to all three on-premise transition scenarios and can be also extended to capacity management requirements of very large systems including aspects like data avoidance strategies, data tiering, or landscape scalability. SAP Enterprise Support customers can also listen to the Meet the Expert recording “Guided Sizing for SAP HANA”. This session gives an overview about the current knowledge on SAP HANA sizing and the principles on how to size SAP HANA in a Greenfield and migration situation.</p>
		<h3>Perform Sizing for SAP BPC embedded</h3>
		<p>The objective of this task is to either perform a sizing, or in case a first estimate has been provided already check the initial sizing. Procedure The sizing for BPC needs to be performed on top of the BW sizing. The approach is different depending on the BPC Model and the conversion type. For an initial sizing of BPC Standard Model, the paper-based sizing guide for BPC needs to be consulted. An initial sizing for BPC Embedded model is performed with the help of the SAP Quicksizer for BW/4HANA. BPC sizing in case of system conversion is usually done with the help of the report /SDF/HANA_BW_SIZING. How SAP can Support An initial sizing can be provided either as part of the “Technical Architecture Workshop”, or as part of the “Migration Planning Workshop” in the Prepare phase. SAP can perform this task with the “Advanced Sizing” service component.</p>
	</div>
	<h2>Operations Impact Evaluation</h2>
	<p>With the introduction of a new product like SAP BW/4HANA, the current IT support framework will change. The impact of the new product on the support framework may vary on the scenario. Example scenarios include the following: On-premise solution IT is responsible for the entire SAP BW/4HANA and needs to learn about the technical and functional specifications of the solution. Hosted solution IT is responsible for monitoring application performance, troubleshooting, and all application support activities. Cloud-based solution IT is responsible for security and user administration, software logistics, development, and testing of solution changes. The IT team therefore requires the necessary knowledge and skills to complete these tasks. If the solution is the first to be implemented in the cloud, the current support operations need to be reviewed to include, for example, communication with the provider of the cloud-based services. Third-party support When application support is handled by a third party, knowledge about the solution must be transferred from the project team to the third party. Business process owners, business relationship managers, and service desk agents within customer’s organization still need to learn about the solution, and they should also consider changes in the incident or change management support processes. Greenfield customers All the topics mentioned in this activity are relevant for “Greenfield” customers. The effort to prepare for the support of SAP BW/4HANA will depend on many factors like the current support model, the maturity of the organization, the future support strategy and deployment, like hosting hardware, cloud deployment. SAP provides optional support to “Greenfield” customers to help them with every aspect of the implementation of the future support framework. The extent of this optional support varies according to the support framework currently in place for running and operating the legacy systems that are replaced by SAP BW/4HANA. The technical solution design document (see activity Technical Architecture Definition in the Explore phase) provides a list of technical components that needs to be operated after the project go-live. Many other tasks mentioned in this road map are an important source of information on how and what needs to be supported by IT once the new solution is live. This includes the Technical Architecture, the Delta Design and Configuration, Product Enhancements. The goal of this activity is to review the list of potentially affected IT operational activities and to evaluate the exact changes required in the current customer IT support framework based on the project scope. Requirements and Constraints This activity is recommended for all scenarios. Procedure SAP provides information on the recommended IT Support framework to safely and efficiently operate SAP BW/4HANA after Go-Live. This includes: Identifying the target IT support framework for the future customer operations where the support processes, people and tools will ensure efficient operations of the migrated and converted solution. Recommending Best Practices for the support tools in the areas of monitoring, troubleshooting and software logistics Defining the changes to the current support framework required to support the future solution, and their respective severity/priority Adding Activities to the project plan to implement those changes Additional SAP IT operational support in the Realize Phase of the project to help ramp-up support resources and improve their knowledge transfer, to prototype or implement required tools, and to further adapt process and procedures. Executing technical checks for the support tools mentioned above and ensuring that the changes to the support framework with high priorities have been implemented and tested. Please see the accelerator section for more information. See also task Clarify on Operational Readiness as part of activity Transition Planning (Prepare phase). Results At the end of this activity the impact of SAP BW/4HANA on the existing support organization has been documented and stored in SAP Solution Manager. How SAP Can Support SAP can support this task with the Operations Impact Evaluation service component which supports customers in: Clarifying the impact of the SAP BW/4HANA on IT Operations by reviewing the target support framework for the operations of the new solution: this includes the target support processes, support tools, and support teams. Identifying the required support skills/roles and knowledge on SAP BW/4HANA for the support resources. Defining what needs to change in the current support framework and the corresponding list of operations implementations activities which need to be included into the overall project plan to ensure the operational readiness of the support organization. The customer will be responsible for managing those activities using SAP&#x27;s recommendations, Best Practices, knowledge maps and knowledge transfer plans. The Operations Impact Evaluation will specify further the SAP detailed involvement required to support the customer if they themselves cannot implement the recommendations defined during this activity (for lack of resources or lack of knowledge). Figure 3.11: Example of an operations service plan created in an Operations Impact Evaluation Workshop The review of the recommended target support framework drives the definition of project activities that will fill the gaps between the current IT support framework and the target support framework created by the introduction of SAP BW/4HANA.</p>
	<div>
		<h3>Run Operations Impact Evaluation</h3>
		<p>The objective of this task is to evaluate the impact on IT operations when implementing SAP BW/4HANA. Procedure During the Operations Impact Evaluation activity, the SAP BW/4HANA project scope is analyzed to evaluate potential operational risks and areas in the support framework that need to be looked at and modified/implemented prior to the go-live. The aim is to define the list of operational activities which: Need to be newly set up: support processes like incident management need to be able to handle the new component. Are existing, but have to be modified: For example, daily backup routines need to be adjusted to properly fit the new SAP BW/4HANA. Support tools like monitoring, troubleshooting or software logistics tools need to be in place. Can be retired: For example, DB routines and scripts for AnyDB can be retired. AnyDB monitoring set up should be retired as well. All the relevant support areas need to be analyzed in a comprehensive manner that is analyzing all the roles and skills required for the support of the SAP BW/4HANA, the processes/procedures, the operations documentation and the enabling support tools. SAP can support you in all these activities as part of the SAP BW/4HANA service packages where a systematic approach to operational activities will ensure you analyze all the changes in IT operational activities caused by the new solution. Once the affected support areas are analyzed in a systematic way, a road map is defined that includes the key activities for IT to fill the gaps and prepare the future IT support framework. The key activities required are many and include: Defining the sourcing strategy for a new role: project resource moving to operations, ramp up of a current resource to support the new solution, hiring or handover of activity to partner Setting up and configuring tools and where SAP will be engaged to support Documenting operating procedures by project resources or by operational resources Organizing for knowledge transfer to ensure the future operational resources have the required knowledge and skills – this includes formal education of current operational resources, training, hands on and shadowing on new solution. It also includes training of all the IT support resources involved in the support of the new solution like the service desk Operations cut-over activities (e.g. team access, roll over of open defects…) Retirement of some part of the current support framework An SAP engagement model should be created as part of an SAP BW/4HANA service package for SAP to support you in where your knowledge is minimum or where your resources are busy on operational topics. The possible services include for example: SAP Solution Manager prototyping or implementation services EGIs on how to use SAP Solution Manager SAP trainings Based on the road map, you will then be able to define the detailed activities to be added to the project plan and align it with the SAP Engagement Model specific to your needs together with the SAP TQM. Results There is a plan documented and stored in SAP Solution Manager on how to adjust IT operations to be ready to safely and efficiently operate SAP BW/4HANA as of Go-Live. How SAP Can Support See Operations Impact Evaluation in the activity description.</p>
	</div>
	<h2>Release and Sprint Plan</h2>
	<p>The purpose of this deliverable is to refine the Initial Release Plan that was defined earlier. At this time the team also defined sprint plan for the first few iterations. SAP provides customers with a SAP Solution Manager tool set that supports customers in implementation of the project standards for the purposes of the project or operations. It is highly recommended to use the SAP Solution Manager Focused Build service solution. Additional details can be found in the SAP Solution Manager Focused Build accelerator. The Focused Build material currently relates to SolMan 7.1 functionality and the above training materials will be updated for SolMan 7.2. In this update step there will be an opportunity to create greater alignment with existing WBS . Project Managers may also want to consider more significant updates to the WBS to align with the project approach of the Focused Build approach.</p>
	<div>
		<h3>Update and Prioritize Backlog</h3>
		<p>The purpose of this task is to update the backlog with delta requirements / gaps and to prioritize the backlog items by business importance. The SAP Activate Methodology recommends using the MSCW prioritization framework (e.g. the Must-Have, Should-Have, Could-Have, Would-Have) for an initial grouping of the backlog requirements. The business process owner is responsible for determining the relative priority of each requirement. Once the requirements are assigned to the appropriate group, the business owner needs to determine a sequence of requirements in each of those groups. This will help the project team to understand the relative priority of items in each group and this will facilitate selection of the high priority items during the sprint planning in Realize phase. During prioritization the following dimensions need to be considered: Dependencies and Integration The project team will help business owner assess the impact of the requirement on other requirements (technical risk, dependencies, integration points); Scale The desirability of the feature to a broad base of users (business impact, acceptance); Importance The desirability of the requirement to a small number of important users or customers (influencing key stakeholders, business value). How to establish clear priorities: In agile projects the Process Owner must prioritize and force rank list of all requirements in project backlog. No two items can end up being ‘equal’ on the list (e.g. have the same priority and ranking). The main reason for this is to prevent that everything is rated as a “Must Have”. The MSCW prioritization (Must-Have, Should-Have, Could-Have, Would-Have) is used for an initial grouping of requirements. Secondary step is to rank items within the same priority group.</p>
		<h3>Estimate Backlog Items Effort</h3>
		<p>The objective of this task is to estimate effort for the backlog items. This activity is done by the project team that has the expertise to estimate the effort required to complete the backlog items (requirements) from the backlog. For the project team it is critical to understand when we consider the backlog item done. Definition of done must be clearly understood by everybody involved in the project. See examples below for recommended definitions. The project team needs to ensure that the estimates in the backlog include all activities required for completion of the item in the sprint and for completion of the item in the release. The project team should follow these guidelines during the estimation process: Estimates are done by the experts in the team who are implementing the functionality and have experience from similar projects More expert opinions lead to better estimation results Everybody on the team participates in the estimation process Verbal communication is preferred over detailed written specs It is possible to use Planning Poker especially for estimates where experts disagree widely (see next slide) Clear the assumptions of estimates prior to estimating - e.g. what is the definition of done for sprint and for the release Avoid anchoring, it invalidates estimates – e.g. “I would say this is easy so it should be X ideal person days” Estimate in the same unit of measure for the entire project (e.g. Ideal Person Days / Story Points) If consensus cannot be reached, defer the estimate of requirement to later time</p>
		<h3>Conduct Release and Sprint Planning Meeting</h3>
		<p>The objective of this task is to prepare a release and sprint plan that will guide the project team in building the requirements in the backlog into a fully functional and integrated solution that supports customer’s business. The starting point for the release and sprint planning is the prioritized and estimated backlog (activities covered in the previous tasks). The image below shows an example of the iterative agile approach to build functionality for one release. Release represents functionality that is deployed to the production system and rolled out to the end users. Each release is built in sequence of time-boxed sprints in which the project team develops the highest priority capabilities as determined by the business owners. One or multiple sprints may be needed to finalize functionality for end-to-end scenario (or Epic as it is known in agile projects) During each sprint the project team conducts following activities: Sprint Planning Meeting Sprint Execution Activities Daily Stand-up Meeting Sprint Demo Sprint Retrospective During the project the team will run two types of sprints: Build sprints - the goal of these sprints is to build and unit test the new capabilities Firm-up sprints - the goal of these sprints is to conduct testing of strings of process steps to continuously ensure integration. During this sprint the team also develops solution documentation</p>
		<h3>Validate Release and Sprint Plan Against SoW</h3>
		<p>The purpose of this task is to review the release and sprint plans against the statement of work(SoW). The objective of this task is to identify any new features that have surfaced during the baseline build that may not be part of the original scope and may require amendment of the SoW or Change Request. It is important to ensure that the project manager validates that the requirements and user stories in the backlog document are in alignment with the scope of the project as it is reflected in the SoW and other contractual documents. The Project Manager (PM) should also ensure the alignment of the release and sprint plan with the high-level plan covered in the SoW. In case any discrepancies are discovered the PM needs to take appropriate corrective action, like raising a change request according to the integrated change request management plan defined in the Prepare phase.</p>
	</div>
	<h2>Organizational Change Management Impact Analysis</h2>
	<p>The purpose of the OCM impact analysis deliverable is to ensure that the OCM and technical changes in business processes have been identified and documented by comparing the as-is and the to-be business processes.</p>
	<div>
		<h3>Validate Organizational Alignment Approach</h3>
		<p>The purpose of this task is to validate the chosen approach with key stakeholders for the continued project execution.</p>
		<h3>Establish Baseline of Current State</h3>
		<p>The purpose of this task is to define the baseline for where organizational change management starts and against which progress, and success of the OCM-activities are measured.</p>
	</div>
	<h2>Phase Closure and Sign-Off Phase Deliverables</h2>
	<p>The purpose of the phase closure and sign-off deliverable is to: Ensure that all required deliverables from this phase and the project are complete and accurate, and close any outstanding issues Identify lessons learned during the phase to prepare for formal phase closure Capture customer feedback and potential Customer References</p>
	<div>
		<h3>Conduct Knowledge Management Gate</h3>
		<p>The purpose of this task is to collect knowledge assets and lessons learned at the end of each phase of the project that can be reused later by other projects. Collecting documents, experiences, project highlights and lessons learned throughout the duration of the project can help to facilitate the project by providing quick access and insights into key deliverables from earlier stages of the project.</p>
		<h3>Conduct Project Quality Gate</h3>
		<p>The purpose of the Quality Gate is to ensure that both compliance and project management standards are being upheld within projects. A Quality Gate is a checklist milestone at the end of a project phase. Prior to moving into the next phase, each project manager demonstrates that they have been compliant with the mandatory deliverables associated with a methodology while ensuring best practice standards have been applied to ensure quality. A Quality Gate looks at the following topics in detail: Conduct regular quality checks at defined or critical stages of the project lifecycle to assess the health of the project. Ensure that all key deliverables and actions of the gate have been completed in compliance with recommended practices and to the customer’s satisfaction. Enable project management to continuously communicate the process and build quality directly into the project. Provide a tool to effectively manage project expectations and monitor customer satisfaction. The deliverables assessed at each quality gate will be performed using the quality gate checklist with defined expectations to the maturity of particular project deliverables. Note(s): New additional key deliverables need to be added in the quality gate checklist by the Project Manager to the different project types.</p>
		<h3>Execute Baseline Retrospective</h3>
		<p>The purpose of this task is to conduct retrospective meeting with the SCRUM team to identify potential improvements of the SCRUM process. The objective of this task is to serve as continuous improvement mechanism for the team to adjust to changing project environment and needs. The team will select one or two key improvements to implement in the next iteration and handles them as user stories that are added to the product backlog, prioritized and tracked along with other user stories.</p>
		<h3>Conduct Project Management Review Service</h3>
		<p>The purpose of this task is to execute a Project Management Review that provides a proactive quality assurance review, with an impartial analysis of all aspects of the project - across all project management disciplines, enabling early detection of project issues with actionable recommendations.</p>
		<h3>Conduct Design Review Service</h3>
		<p>The purpose of this task is to execute a Design Review service that provides a proactive quality assurance review of the design of the SAP solution which is being implemented. It delivers an impartial analysis of all aspects of the solution design - across all relevant design perspectives - and leads to an early detection of potential solution risks and offers actionable risk mitigation recommendations.</p>
		<h3>Manage Fulfilled Contracts</h3>
		<p>The purpose of this task is to ensure that each contract is closed by verifying that all work specified in the contractual arrangement was completed, and that all defined deliverables were accepted.</p>
		<h3>Obtain Customer Sign-Off for Phase Completion</h3>
		<p>Purpose of this task is to obtain customer approval (sign-off).</p>
	</div>
</div>
<h1>Realize</h1>
<p>Once Quality Gate 2 (i.e. Q2) – Explore-to-Realize has been passed successfully, the functional and technical implementation takes place in the Realize Phase. Figure 4.1: Activities in the Realize Phase In the Solution Adoption work stream, the trainings for the end users start. In the Application Design &amp; Configuration work stream the SAP BW/4HANA Implementation Service or the data and object transformation takes place according to the applied scenario. SAP can safeguard the activities via the Integration Validation offering, which covers key aspects like Exception Management, Integration, and Performance and Scalability. In the Data Management work stream, Load and verification runs ensure business data can be migrated with acceptable time and quality. In the Extensibility work stream, impacted custom code is adjusted and tested. Application changes are thoroughly tested in the Testing work stream. The technical infrastructure is set up in the Technical Architecture and Infrastructure work stream, according to the technical design document. In parallel, the sizing of the productive instance is verified. The transition of the quality assurance system takes place according to the detailed transition plan. Technical adjustments may be implemented as part of the activity. At the end of the Realize phase, the preparation for cutover starts, which for instance includes the setup of the new productive system in case of a new installation. According to the Operations Impact Evaluation, the operations implementation (people, processes tools) takes place in the Operations and Support work stream.</p>
<div>
	</div>
	<h2>Phase Initiation</h2>
	<p>The purpose of this deliverable is to formally recognize that a new project phase starts. Tasks Review Deliverables of Realize Phase Review Acceptance Criteria Review RACI Chart for Realize Phase with Customer</p>
	<div>
		<h3>Review Deliverables of Realize Phase</h3>
		<p>The purpose of this task is to review all Deliverables of the Realize Phase with the customer.</p>
		<h3>Review Acceptance Criteria</h3>
		<p>The purpose of this task is to review the acceptance criteria with the customer.</p>
		<h3>Review RACI Chart for Realize Phase</h3>
		<p>The purpose of this task is to review the R.A.C.I Chart for the Explore Phase with the Customer. It is important to clarify roles and responsibilities in completing tasks and deliverables during this phase</p>
	</div>
	<h2>Plan Realize Phase</h2>
	<p>The purpose of this activity is to define plans for Sprints, Solution Reviews and prepare all business-process-related tests according to customer- specific configurations.</p>
	<div>
		<h3>Plan Sprints and Solution Reviews</h3>
		<p>The purpose of this task is to plan Sprints and prepare to evaluate the proposed solution.</p>
		<h3>Plan Testing</h3>
		<p>The purpose of this task is to plan the test sessions from a Project Management point of view. Procedure Review Test entry and exit criteria with customer Schedule Test Sessions and Plan Resources</p>
	</div>
	<h2>Sprint Initiation (Iterative)</h2>
	<p>The purpose of this task is to initiate sprint in a formal Sprint Planning Meeting.</p>
	<div>
		<h3>Conduct Sprint Planning Meeting (Iterative)</h3>
		<p>The purpose of this task is to initiate sprint by selecting the set of user stories that will be implemented in the sprint (scope of the sprint). During the sprint planning meeting the SCRUM team estimates the stories and clarifies with the product owner team any questions that may still remain open. The team commits to deliver the set of highest priority stories from the backlog. This meeting formally sets the scope for the sprint. This meeting is conducted in the beginning of each sprint.</p>
	</div>
	<h2>Execution Plan for Realize Phase</h2>
	<p>The purpose of this deliverable is to execute the work defined in the Realize Phase and manage the Sprints and Testing according to previously defined plans. During test execution all issues must be logged and documented in the system for traceability purpose.</p>
	<div>
		<h3>Manage Sprints</h3>
		<p>The purpose of this task is to ensure consistent execution and monitoring of sprint activities.</p>
		<h3>Manage Unit/String Tests</h3>
		<p>The purpose of this task is to properly manage the Unit/Spring Test. During the test all issues must be logged and documented in the system for traceability purpose.</p>
		<h3>Manage Integration Test</h3>
		<p>The purpose of this task is to properly manage the integration test. During the test all issues must be logged and documented in the system for traceability purpose.</p>
		<h3>Manage Security Test</h3>
		<p>The purpose of this task is to properly manage the security test. During the test all issues must be logged and documented in the system for traceability purpose.</p>
		<h3>Manage User Acceptance Test</h3>
		<p>The purpose of this task is to properly manage the User Acceptance test (UAT). During the test all issues must be logged and documented in the system for traceability purpose.</p>
	</div>
	<h2>Sprint Closing</h2>
	<p>The purpose of this deliverable is to formally close the sprint by conducting the Sprint Review Meeting with key users, product owner group and key stakeholders; formally sign-off the results and conduct sprint retrospective.</p>
	<div>
		<h3>Conduct Sprint Review Meeting</h3>
		<p>The purpose of this task is to demonstrate the results of the sprint to the product owner team, key users and key project stakeholders. The results of the demo are either acceptance of backlog item or in case the feature is not accepted the user story is amended by the product owner team, prioritized and remains in the product backlog ready for the next sprint planning meeting. The demo in sprint review meeting serves as an input to formal sign-off for the sprint.</p>
		<h3>Signoff Sprint Results</h3>
		<p>The purpose of this task is to obtain formal acceptance and signoff of the sprint results following the sprint review meeting. The product owner team is the party that provides the sign-off to the project team. In case some features are not accepted they are brought back into the product backlog as requirement, properly prioritized and included in planning for next sprint.</p>
		<h3>Conduct Sprint Retrospective</h3>
		<p>The purpose of this task is to conduct a retrospective meeting with the SCRUM team to identify potential improvements of the SCRUM process. The objective of this task is to serve as continuous improvement mechanism for the team to adjust to a changing project environment and needs. The team will select one or two key improvements to implement in the next iteration and handles them as user stories that are added to the product backlog, prioritized and tracked along with other user stories.</p>
	</div>
	<h2>Learning Realization</h2>
	<p>In this activity the training material for the end-user will be created. Requirements and Constraints This activity is required for all scenarios. The following preconditions are required before end-user training material creation can start: End-user training material, a training environment, logistics, and infrastructure is available. Skilled instructors are available based on the framework defined in the training strategy document from activity. Procedure Train your Key User Create Training Material for End User Results At the end of this activity, the training for the end user is ready to be delivered (including systems, demo data etc.).</p>
	<div>
		<h3>Train your Key User</h3>
		<p>The goal of this task is to train the key user as planned in the activity Learning Design in the Explore phase. Procedure Train your key user as planned in the Explore phase. Training could happen e.g. via class-room training, or on the sandbox system. Please note: Key users should be trained in such a way that they can support the creation of training material for end users, or the test of newly implemented functionality. Results Key users are trained. How SAP Can Support SAP can support the training of key user as part of an individual offer from SAP Education. See accelerator section for details.</p>
		<h3>Create Training Material for End User</h3>
		<p>The goal of this task is to create the required training material according to the training concept (see activity Learning Design in the Explore phase). Procedure Based on the training concept the usage of a recording and authoring tool for creating the end user training material needs to be discussed and decided. SAP recommends SAP Enable Now (successor of the SAP Workforce Performance Builder (WPB)) as the tool for creating the training material, supporting translations, developing e-learning with or/and without audio voice description. SAP Enable Now can be used for SAP and Non-SAP applications and is therefore suitable for a seamless recording and documentation process for the end user in one tool. The final documents can be exported in standard formats as PDF, MS Word, and PowerPoint. Furthermore, SAP Enable Now can also support the test phase with documentation of test cases and results. You can find all important documents on SAP Enable Now in the SAP Online Help Portal (e.g. “What’s New in SAP Enable Now”, “Master Guide”, or “Creating Documents for HP Quality Center”) – see accelerator section. SAP is following the approach to enable the customer key users to develop the end user training material mainly themselves. SAP can coach the key users in developing the right structure and in reviewing the documents. The goal is to transfer the knowledge as much as possible to the key users to enable them for the end user training. As a prerequisite the key users should already be trained didactically, methodically, and for using the tool appropriately during the Explore phase and the key user training work package. The following list describes possible end user training documents required to be developed for every training: Course Concept For each training event (classroom training), a course concept is created together with the customer. The course concept defines the required content to be communicated. The course concept is converted it into an appropriate structure and recorded in a template for training delivery. Training Manual The training manual contains instructions, notes, and references to important points during the training. They explain the processes, and provide the trainer with the structure, times, and procedures for the courses (up to max. one page per content day). Conceptual Design Slides Conceptual design slides are created for the training events as a visual aid, and a guideline during training. They provide not only a general overview of the course but also the main procedures as well as role-specific and process-specific information. The slides have a modular structure and are discussed during training (up to max. five concept slides per content day). Work Instructions for Transaction Steps The work instructions contain not only step-by-step explanations for the relevant transactions, but also screenshots from the customers SAP system as well as explanations of screens and input fields. Exercises, Including Data Exercises are an integral part of each training measure. Active processing phases within a training event help participants to understand and learn about sub-processes and functions relating to their new system. The exercises are carried out in a customer SAP system during the training event to ensure that the training includes a large proportion of practical applications. Up to four exercise scenarios are generally needed for each training day. Training data needs to be available early, so that the key user can already try out during training material creation. Simulations Simulations help to visualize operating steps in the SAP system. They enable end users to practice in an environment that is separate from a genuine SAP system and thus allow them to acquire the necessary experience. Simulations can only be created once a suitable authoring tool is available (e.g. SAP Enable Now). E-Learning E-Learning helps to reduce the effort for classroom training. As a self-learning offering, e-learning is also available after the project has ended. This enables new employees to become acquainted with topics independently. Trained employees can &quot;refer back to&quot; various topics or &quot;take a look at&quot; the system simulations as required. Creating customer-specific e-learning content requires a stable and fully developed / customized SAP system. SAP assumes that e-learning will merely comprise of less complex and overview content such as process overview per stream, main functional features, highlighting key changes and benefits. SAP develops e-learning content comprising approximately 56 hours of net learning time. Net learning time describes the amount of time that an average learner requires to work through the program without any interruptions. Web Based Training (WBT) Web based training is a reasonable addition to e-learning and has the advantage that a course can be attended location-independently by one or more persons. Communication with the trainer via phone or chat is feasible, during which all open questions resulting from previous self-learning experience can be answered by an expert. If possible, the course material and the exercises and solutions could be derived from the corresponding classroom training courses. If not, a course concept, training manual, conceptual design slides, work instructions for transaction steps, and exercises (including data and simulations) should be developed Based on the end user training the customer training system need to be set up by the customer key users. SAP can support this activity. The technical set up is finalized by the IT team. In addition to the development of the end-user training material and the training system set up, the customer key users should have a final workshop with SAP experts to finally prepare the end user trainings, and to define the tandem approach for the first end user trainings. The tandem approach means that the customer key user will be accompanied during the first training sessions by an experienced SAP trainer to get confidence and safety in performing the trainings. SAP expects DEV and QAS (or a dedicated training system) to be completely available during training development period. SAP recommends a systematic buildup of the training client in the QAS test system during the Realize phase. The training client should be available when training material creation starts (e.g. processes, master and booking data). Results The training is ready to be delivered to the end user.</p>
	</div>
	<h2>Realize the In-place Conversion</h2>
	<p>This activity covers all steps in the project landscape that are displayed in the “in-place conversion” phase (t5 phase) in the following graphic: Figure 4.2 Detailed steps of the In-place Conversion The SAP BW/4HANA Starter add-on makes it possible to transition from the existing object types, processes and interfaces to the new and optimized ones in SAP BW/4HANA. To do this, you need to perform the following steps in the SAP BW system after installing the add-on: 1. Run the Pre-Check to check whether the objects and processes that the system uses are compatible with SAP BW/4HANA 2. Transfer incompatible objects and processes to compatible ones with the SAP BW/4HANA Transfer Cockpit. 3. Switch the system to an operating mode that only allows objects and processes that are compatible with SAP BW/4HANA. Switching the system to B4H mode prepares the SAP BW system for conversion to SAP BW/4HANA. 4. Check whether all system-wide clean-up activities have been completed. 5. Switching the system to an operating mode from which the technical upgrade can be performed Switching the system to ready-for-conversion mode completes preparation of the SAP BW system for conversion to SAP BW/4HANA. It can be performed again with the SAP BW/4HANA Transfer Cockpit. Procedure Install the SAP BW/4HANA Starter Add-on Update to the latest version of SAP BW/4HANA Transfer Cockpit Use the Authorization Transfer Tool of the SAP BW/4HANA Transfer Cockpit Perform the Object Transfer using the Scope Transfer Tool of the SAP BW/4HANA Transfer Cockpit Perform Custom Code Adjustments Execute the Transfer of Standard Authorizations with the SAP BW/4HANA Transfer Cockpit Transfer ADP Processes with the ADP Transfer Tool Clean-up Objects using the Cleanup Tool Switch to Ready for Conversion mode Result The system is ready for the technical conversion.</p>
	<div>
		<h3>Install the SAP BW/4HANA Starter Add-on</h3>
		<p>The SAP BW/4HANA Starter add-on makes it possible to transition from the existing object types, processes and interfaces to the new and optimized ones in SAP BW/4HANA. Procedure Download the software with Maintenance Planner and install the SAP BW/4HANA Starter Add-on with the Add-on Installation tool as described in the Conversion Guide for SAP BW/4HANA. Result The SAP BW/4HANA Starter Add-on is installed.</p>
		<h3>Update to the latest Version of the SAP BW/4HANA Transfer Cockpit</h3>
		<p>Most of the next steps of the in-place conversion are performed with the SAP BW/4HANA Transfer Cockpit. Make sure to use the latest version of the Cockpit. Procedure Use the SAP BW Note Analyzer with the XML files attached to SAP Note 2383530 to update the Transfer Cockpit to the newest version as described in the Conversion Guide for SAP BW/4HANA. Results You can now start using the SAP BW/4HANA Transfer Cockpit. How SAP Can Support The task lists used in the SAP BW/4HANA Transfer Cockpit are documented in detail in the “Task Lists Documentation for Conversion to SAP BW/4HANA” (see accelerator section).</p>
		<h3>Prepare the Transfer of Standard Authorizations</h3>
		<p>Conversion from SAP BW to SAP BW/4HANA requires also conversion of authorization objects. There are four types of actions that need to be applied for respective authorization objects: • Assume – Nothing to do. Authorizations will continue to work after conversion • Adjust – Check and adapt values of authorization objects • Replace – Change authorization object and adapt its values • Obsolete – Not needed/supported authorization object that should be removed The assignment of actions to existing authorization objects can be found in SAP Note 2468657. The SAP BW/4HANA Transfer Cockpit provides an Authorization Transfer Tool to automate the transfer of existing security roles. Procedure Follow the procedure for authorization objects transfer described in the Conversion Guide for SAP BW/4HANA. Results The transfer of authorizations is prepared.</p>
		<h3>Perform the Object Transfer Using the Scope Transfer Tool  of the SAP BW/4HANA Transfer Cockpit</h3>
		<p>Data models and data flows are converted to HANA-optimized objects that are compatible with SAP BW/4HANA by using the Scope Transfer Tool of the SAP BW/4HANA Transfer Cockpit.. Object types that are not available in SAP BW/4HANA are replaced by other object types or features. A “scope” is a collection of objects that you want to convert together. The following table lists the object types that are not available anymore and can be converted using the Transfer Cockpit, together with their successors and the Simplification Item (see the corresponding SAP Notes for details and limitations regarding each object type): The following object types will be automatically adjusted during a scope transfer: Figure: Objects types with automatic adjustment during scope transfer Procedure The Transfer Cockpit transfers the unsupported objects to new supported objects, copies the data to the new objects and the old objects are deleted. You can find a detailed documentation of the tasks performed during a scope transfer in the system (transaction STC01) or on the SAP Help Portal → Task List Documentation for Conversion to SAP BW/4HANA. The relevant objects are identified by the readiness check report. The Transfer Cockpit activities are generally the most time-consuming activities in this project phase. The activities must be performed in each system of the system landscape separately. Generally, the changes cannot be transported. How SAP can Support For a complete and up-to-date list of unsupported objects, processes and functionalities, together with their successors/replacements and the relevant conversion method, refer to the latest version of the SAP Simplification List for SAP BW/4HANA. Tips and hints for troubleshooting scope transfers are available in the Conversion Guide for SAP BW/4HANA.</p>
		<h3>Execute the Transfer of Standard Authorizations with SAP BW/4HANA Transfer Cockpit</h3>
		<p>After the successful transfer of objects using the Scope Transfer Tool, the transfer of standard authorizations must be completed using a delta run. Procedure Follow the description of the Transfer Process (Delta Run) in the Conversion Guide for SAP BW/4HANA 2.0. Results The system will generate the new roles and assign them to the same users as the corresponding original roles.</p>
		<h3>Transfer ADP Processes with the ADP Transfer Tool</h3>
		<p>The APD Transfer Tool (transaction RSB4HAPD) converts APD processes into corresponding process chains. The tool supports the most common elements of APD processes. However, there are restrictions for some of the APD nodes and some types of nodes are not supported by the transfer tool at all. Procedure Refer to the description in the Conversion Guide for SAP BW/4HANA for transferring the APD processes. Results The APD processes are converted into process chains for SAP BW/4HANA.</p>
		<h3>Clean-up Objects using the Cleanup Tools</h3>
		<p>Objects that are not compatible with SAP BW/4HANA and are not required anymore, need to be deleted from the system so they do not to block a system conversion (or switching of operating modes). Procedure Delete objects that are not required anymore as described in the Conversion Guide for SAP BW/4HANA. The following object types can be deleted: SAP BW Technical Content SAP BW Objects SAP BW Queries Results You have cleaned up the system from objects that are not required anymore.</p>
		<h3>Run the technical In-Place Conversion to SAP BW/4HANA</h3>
		<p>Before starting the technical system conversion in the project landscape, you have to download the software using Maintenance Planner. The technical system conversion is performed using the Software Update Manager (SUM) 2.0. SUM 2.0 is currently available with SP18. Procedure 1. Start the Maintenance Planner and select “Plan a Conversion to SAP BW/4HANA” and “SAP BW/4HANA 2023&quot;. Select your target Support Package. 2. Download the Stack XML file 3. Run the technical system conversion with the SUM as described in the Conversion Guide for SAP BW/4HANA. Results The implementation is finished now. Perform the follow-on activities of the task list “SAP_BW4_AFTER_CONVERSION” in the SAP BW/4HANA Transfer Cockpit. How SAP Can Support SAP offers the service “In-Place system conversion for SAP BW/4HANA”. This service covers the conversion of an existing SAP BW running on any database system to SAP BW/4HANA.</p>
	</div>
	<h2>System Configuration</h2>
	<p>After the setup of the Dev system, you need to run the post-installation steps and start with the configuration of the system. Requirements and Constraints This activity is required for new implementation, remote conversion and shell conversion. Procedure Run the post-installation steps as described in the Master Guide (see accelerator section). Activate the required business content. Result The basic configuration of the new SAP BW/4HANA system is done.</p>
	<div>
		<h3>Configure the new SAP BW/4HANA System</h3>
		<p>Configure the new SAP BW/4HANA system and connect the source systems. Activate the business content. Procedure Run tasklist SAP BW4_SETUP_SIMPLE in transaction STC01. The task list performs the basic system setup tasks, like creating and configuring the SAP BW/4HANA background user, setting the SAP BW/4HANA client and installing the essential technical content. Use the Implementation Guide (IMG) to add further settings Install the permanent SAP license. Install the SAP BW/4HANA Basis Content Add-on, if not yet done with the system setup. Refer to SAP Note 2393067 – Installation Note Install the SAP BW/4HANA Content Add-on, if not yet done with the system setup Activate the content areas from the business content as required for your business. Configure the connection to your source systems. This may require the implementation of ODP 1.0 in case of source systems &lt; 730 and the implementation of ODP 2.0 if the basis level component SAP_BASIS is &gt;= 730. How SAP Can Support SAP offers the (Greenfield) Implementation Service for SAP BW/4HANA (on premise or customer private cloud) as Professional Service. This service contains a mandatory scope item, to install SAP HANA, SAP BW/4HANA and configure one source system (ECC) using ODP technology. The following steps are covered in detail: Installation of an SAP HANA system and SAP BW/4HANA system in the development environment Basic configuration of SAP HANA and SAP BW/4HANA development system Replication of global settings and exchange rates Implementation of ODP 2.0 if basis level component in ECC source system is appropriate (SAP_BASIS &gt;=730) Basic configuration in order to connect one SAP ECC source system In addition, the service offers the following scope options: Activate FI or CP: Activation of one content area from FI or CO from the business content Activate SD/MM (no Cockpit): Activation of one content area from MM or SD from the business content, if LO Cockpit is already configured in source system Activate SD/MM and LO Cockpit: Activation of one content area from MM or SD from the business content. LO Cockpit configuration for one area from MM or SD. ODP 1.0 Framework: Implementation of ODP 1.0 if SAP BASIS level in source system is &lt;730.</p>
	</div>
	<h2>Data Volume Configuration &amp; Execution</h2>
	<p>In this activity the Data Volume Strategy that has been defined and (partly) implemented is further realized (see description of activity Data Volume Design). Data Tiering can be part of the data volume strategy. Data Tiering allows you to assign data to various storage areas and storage media. The criteria for this decision are data type, operational considerations, performance requirements, frequency of access, and security requirements. Customers often employ a multi-temperature or data tiering storage strategy, with data classified depending on frequency of access and certain other criteria as either hot, warm or cold. Depending on this classification, the data is kept in different storage areas or media. In addition to frequently accessed active data, large SAP BW∕4HANA systems also contain a large volume of data that is only accessed rarely. The data is either never or rarely needed in Data Warehouse processes or for analysis. The main challenge of implementing a data tiering strategy is to seamlessly integrate the warm and cold memory areas and to make these areas invisible to the outside, in order to ensure that all required functions are applied to this data. SAP provided data tiering solutions for this, helping to reduce TCO thanks to optimized SAP HANA main memory resource management. The following solutions are available in SAP BW∕4HANA for data tiering: Figure: Data Tiering Options in SAP BW/4HANA Data Tiering Optimization (DTO) Data Tiering Optimization (DTO) is the strategic solution for data tiering in SAP BW∕4HANA. It offers the following advantages: A single data tiering solution for hot data (hot store in SAP HANA), warm data (warm store in SAP HANA) and cold data (cold store to SAP IQ, Hadoop, SAP Vora) Central definition of the data temperature based on partitions of the DataStore object (advanced) Moving data to the dedicated storage location as a simple and periodically performed housekeeping activity (TCO reduction) Smooth co-existence of Data Tiering Optimization and the existing near-line storage approach, as the same technical concepts are used for the storage areas for cold data, such as locking archived data Note: Near-line storage with SAP IQ and Hadoop, a solution for the cold store, is still supported in SAP BW∕4HANA and provides: Continuity for data archiving scenarios that are already implemented with near-line storage with SAP IQ or Hadoop before SAP BW∕4HANA Data Tiering Optimization is introduced Support for more complex data archiving scenarios that are not covered by SAP BW∕4HANA Data Tiering Optimization For new DataStore objects however, you should use Data Tiering Optimization wherever possible. The following graphic shows an SAP HANA Scale-out landscape with extension node: Figure: SAP HANA Scale-out landscape with extension node Tasks Configure SAP HANA Extension Node as a Warm Store Configure Hadoop or SAP Vora as Cold Store Results Data Tiering Optimization is prepared and the DataStore objects can be configured accordingly.</p>
	<div>
		<h3>Configure SAP HANA Extension Node as a Warm Store</h3>
		<p>To implement a multi-temperature memory strategy in your SAP BW∕4HANA system, as of SAP HANA 1.0 SPS12 (DSP), you can use the extended node concept for warm data. The data (with no functional restrictions) is saved in a dedicated SAP HANA scale-out node. This enables you to optimize usage of the main memory in SAP HANA. Based on typical data distribution in a SAP BW∕4HANA system, a single extension node is normally used. Multiple extension nodes should only be used in exceptional circumstances. If you do use data tiering optimization with multiple extension nodes, SAP HANA 2.0 is a prerequisite. Procedure Configure SAP HANA for use with extension node (see SAP Note 2343647). Specify the data tiering properties (information about which temperatures or memory areas should be supported by an object) in the modeling screen for the relevant DataStore objects. Use the definition of an object temperature or partition temperature to specify which data is stored in which memory area. Define a data tiering optimization job which regularly moves data to the extension nodes for the corresponding configured DataStore objects. Results Performance and memory usage are increased as only hot data is stored on the standard node whereas warm data is stored on the extension node.</p>
		<h3>Configure Hadoop or SAP Vora as Cold Store</h3>
		<p>The goal of this task is to configure Hadoop or SAP Vora as cold store. Procedure For Hadoop, all configuration steps are described in the online help “Configuring Hadoop as a cold store/Neral-Line Storage Solution” (see accelerator section): Perform configuration steps for Hadoop Perform configuration steps on SAP HANA Perform configuration steps in SAP BW/4HANA For SAP Vora, all configuration steps are described in the online help “SAP Vora as a Cold Store” (see accelerator section): Create a connection to the SAP Vora cluster Create a remote source Create a connection to the cold store. In Customizing, choose SAP BW/4HANA Customizing Implementation Guide &gt; SAP BW/4HANA &lt; Information Lifecycle Management &gt; Edit Cold Store Connection Create a database schema in the Vora system Select the SAP Vora cold store connection as the default connection For more information about using SAP Vora as a cold store, see SAP Note 2608405. Results Either Hadoop or SAP Vora has been configured as cold store. How SAP Can Support SAP offers a set of videos on how to deal with the partitions for hot, warm and cold data. The videos are linked in the introduction blog “SAP BW/4HANA Data Tiering Optimization” (see accelerator section). The BW near-line storage Empowering Service is a workshop that can be ordered as a follow-up to the Data Volume Management service components. It includes a results presentation of the strategy service. In addition, the workshop covers the following topics: Housekeeping for SAP NetWeaver BW: tasks to be performed on a regular basis Overview of near-line storage, including theoretical concepts and partner solutions The data archiving process in the context of near-line storage and all related technical details that are required for a successful near-line storage implementation project Archiving, restoration, and reloading of DataStore objects Different locking mechanisms and data consistency (design time vs. runtime) Data access for reporting purposes or by means of the available lookup API for data staging purposes Automating the archiving and data transfer via process chains Best practices with regards to modeling your enterprise data warehouse, DataStore objects, and respective data archiving processes Important aspects during the life cycle of your near-line storage solution from a BW point of view</p>
	</div>
	<h2>Integration Validation</h2>
	<p>When implementing and running Solution Landscapes that drive mission-critical business processes, the integration of solutions can be complex and challenging. The implementation work is typically distributed across many teams and in most cases many stakeholders, including custom-built and third-party software. Integration Validation helps to introduce solutions into a production environment smoothly, while maintaining ongoing operations with minimal disruption. This offering from SAP combines tried-and-tested processes and tools, such as the SAP Solution Manager Application Management solution, with a clear governance model and holistic, one-issue tracking methodology (“single source of the truth”). The organizational framework for each Integration Validation project is provided by the Innovation Control Center (ICC), which is formed from the Customer Center of Expertise (CCOE) location. It unites customer experts, partners, and SAP staff as one team in one room and works in principle like a NASA control room. Every mission-critical application and technology component is represented as well as every implementation and operation services provider. Besides others, IV addresses the following aspects: Data consistency: All data integration is posted automatically, for example, from sales and distribution and materials management to financials and controlling. In distributed solution landscapes, the consistency of the data across software systems must be subject to checks and validations at any time. Data passed via interfaces between software systems must be consistent. This requires transactional security of the interface technology (end-to-end transactional consistency). All integration queues and interfaces have to be monitored. Business process monitoring and exception management: There must be 100% transparency of status and completion of business processes. The continuous flow of documents throughout the business process must be monitored. This includes backlogs of business processes and indicators for throughput, as well as alerts indicating business exceptions, for example, out-of-stock situations. There must be 100% transparency of exceptions and backlogs. Performance and scalability: Response time for defined critical transactions should be lower or equal to what was specified in the business requirements. Batch runtime for defined critical batch jobs should be lower or equal to what was specified in the business requirements. Batch processing for critical jobs that are part of the core business process should fit in the given batch-processing window. Volume growth and resource consumption must have a linear relationship. Adequate load balancing must be in place to support throughput of performance-critical business processes. Requirements and Constraints This activity is recommended for all scenarios. Tasks Identify the IV Scope Initiate the Corresponding IV Support Activities Initiate SAP Going-Live Check</p>
	<div>
		<h3>Identify the IV Scope</h3>
		<p>The goal of this task is to identify the scope of Integration Validation in the context of the implementation of new functionality or embedded solutions. Procedure Validating the integration of a complex solution is a challenging and extensive task. To improve the efficiency and effectiveness of the task, a collaborative approach should be employed. This makes knowledge transfer an integral part of Integration Validation, enabling all parties involved – the customer, the system integrator, and SAP experts – to work together to validate the solution. The final cross-check is led by SAP, with support from the customer and system integrator. The check results documented in SAP Solution Manager provide the basis for this cross-check. To jointly determine the scope of Integration Validation and to develop a detailed plan of how to validate the solution, product and operations standards, the following information is required: The critical core business processes, including all relevant interfaces and the underlying software landscape Volumes of data to be processed for the critical business process, including peak volume Performance KPIs (transactional, batch, and batch window) In close cooperation with the customer and partner, SAP drives the validation of the customer’s Solution Landscape based on the scope defined. Throughout the process of validation, SAP provides knowledge transfer on an on-going basis and supports the customer and partner as they validate all related core business process steps and interfaces. See accelerator section for more details. For complex scenarios, Integration Validation can be very detailed, and possibly structured as a project on its own. The concept needs to be tailored to the project scope. See the road map “ESRV Integration Validation (V2.2)” in transaction RMMAIN in SAP Solution Manager for details. However, in the context of an implementation of new functionality or an embedded solution, Integration Validation is usually focusing on performance related aspects of newly implemented core business functionality. Results The scope of Integration Validation in the context of the project has been documented and stored in SAP Solution Manager.</p>
		<h3>Initiate the Corresponding IV Support Activities</h3>
		<p>Based on the IV scope which has been defined before, the goal of this task is to initiate the corresponding support activities. Prerequisites The scope of IV has been defined. How SAP Can Support IV is usually executed with SAP participating. The SAP TQM will initiate the right IV support activities from SAP: Feasibility Check service component: Assess the technical feasibility of the transition project with focus on compatibility, business continuity, performance and consistency. Integration Check service component: The Integration Check is an assessment service and aims to verify that the built solution supports the integration test with focus on consistency, performance and stability across systems. Interfaces integration, Data Volume and Business Process Management are assessed. Integration Validation service component: This service component provides technical validation of core business processes with respect to non-functional requirements, identification and addressing of technical risks prior to Go-Live and during the production cutover including hyper-care phase. A comprehensive status of technical Go-Live readiness for core business processes (Integration Validation matrix) is part of the service component. Business Process Performance Optimization service component: When response times are not meeting business needs – sometimes despite extensive hardware, the service component analyzes critical business process steps and recommends how to optimize them. Technical Performance Optimization service component: The technical performance optimization service component improves your SAP solution by helping you to configure your SAP S/4HANA system in an optimal way. The identification and elimination of costly performance bottlenecks optimizes the response times and throughput of your SAP solution. Interface Management service component: The SAP Interface Management service component helps analyze and optimize the performance and stability of critical interfaces as part of business process steps. Fiori Optimization service component: This service component deals with the performance of your Fiori applications, with the following focus points: Interfaces &amp; Integration (ODATA Services) and Code Check &amp; Best Practices (SAPUI5 / Fiori Frontend). Typically, the service starts with baseline measurements, followed by an iterative optimization &amp; tuning phase. Volume Test Optimization service component: The SAP Volume Test Optimization service component ensures that the results of volume testing for implementation or conversion projects are reliable. With reliable test results, you can determine with confidence whether your hardware resources are sufficient, and the system configuration is suitable for a Go-Live. Empowering service component: This service component supports you in case of general empowering requirements. See accelerator section for details. SAP Enterprise Support Customers can order the CQC for TechnicalPerformance Optimization (TPO).</p>
		<h3>Initiate SAP Going-Live Check</h3>
		<p>The SAP GoingLive Check safeguards your SAP solution to a smooth start of production, which is the final milestone of a successful implementation project. The goal of this task is to request the SAP GoingLive Check. Prerequisites The SAP GoingLive Check is recommended for scenarios with new embedded solutions. Procedure Order SAP Going Live Check This check is suitable for new installations. SAP Enterprise Support customers can use the Continuous Quality Check (CQC) for Implementation instead. The service is delivered remotely. An analysis session should be performed 6 weeks in front of the Go-Live date, to have sufficient time to fix identified issues. The verification session runs some weeks after Go-Live, to check the system in its productive use. See accelerator section for detailed service information. Schedule the appropriate service for the SAP system, and additionally for the SAP Gateway in case it runs as a separate instance. Results You have successfully requested the SAP GoingLive Check. How SAP Can Support In case the transition project is supported by a premium engagement, the SAP TQM will order the correct check for you.</p>
	</div>
	<h2>Move from SAP Business Explorer to SAP Business Objects BI and Modeling Tools for SAP BW/4HANA</h2>
	<p>The SAP Business Explorer tools (SAP BEx tools) are not supported any more. Queries in SAP BW/4HANA are now defined in the BW Modeling Tools. The logical successor for SAP BEx toolset is SAP Analytics Cloud. For customers wanting to use the on-premise analytics portfolio, SAP BusinessObjects BI Suite is still supported. For the visualization and analysis of data, you can use SAP Analytics Cloud or SAP BusinessObjects tools, such as SAP BusinessObjects Analysis, Edition for Microsoft Office or SAP BusinessObjects Lumira. These tools are especially designed with the needs of SAP BW users in mind. With the conversion from SAP BW to SAP BW/4HANA, all BW queries will be automatically migrated. Figure: Transition SAP BEx reports and applications to SAP Business Objects We recommend the following migration paths: BEx Analyzer -&gt;SAP BusinessObjects Analysis for Microsoft Office a semi-automated transition of BEx Analyzer Workbooks to SAP BusinessObjects Analysis for Microsoft Office is available as a service offering. SAP BEx Web Application Designer -&gt; SAP Analytics Cloud or SAP Lumira 2.0 BEx Web based planning -&gt; SAP Analytics Cloud for planning Report Designer -&gt; SAP Crystal Reports Query Designer -&gt; BW/4HANA Modeling Tools Tasks Transition of BEx Analyzer Workbooks to SAP BusinessObjects Analysis for Microsoft Office Install Modeling Tools for SAP BW/4HANA</p>
	<div>
		<h3>Transition of BEx Analyzer Workbooks to SAP BusinessObjects Analysis for Microsoft Office</h3>
		<p>With the conversion to SAP S/4HANA, customers need to transfer their BEx workbooks to SAP BusinessObjects Analysis for Microsoft Office. There is no direct tools support available for customers. A semi-automated transfer way is offered with an SAP service.</p>
		<h3>Install Modeling Tools for SAP BW/4HANA</h3>
		<p>The Modeling Tools for SAP BW∕4HANA represent a new modeling IDE (Integrated Development Environment) that is built on top of the Eclipse platform. Their main objective is to support BW model developers working in the increasingly complex BI environments by providing them with state-of-the-art modeling tools. These tools include integration with SAP HANA modeling and consumption of SAP HANA elements in BW Open ODS views or CompositeProviders, with powerful UI (user interface) capabilities. Prerequisites The preparation prior to the installation of front end components includes all steps that you need to perform, in order to ensure that your work with BW Modeling Tools runs smoothly. The preparation includes completing the following activities for each BW application server you want to work with: Implementing corrections for ABAP Development Tools Implementing corrections for BW Modeling Tools Installation requirements Establishing secure network communication Configuring profile parameters for assertion tickets Configuring BW Web services Downloading required packages from SAP Marketplace Procedure Follow the implementation steps described in the Installation Guide for Modeling Tools for SAP BW/4HANA. Results The Modeling Tools for SAP BW/4HANA are implemented and can be used.</p>
	</div>
	<h2>Analytics Configuration</h2>
	<p>Analytics configuration describes the preparation steps that are required to analyze data from SAP BW/4HANA in SAP Analytics Cloud. To use SAP Analytics Cloud for data exploration and visualization, you need to create data models and stories. Prerequisite To analyze data from SAP BW/4HANA in SAP Analytics Cloud, as a first step a data connection between SAP BW/4HANA and SAP Analytics Cloud needs to be established. This is described in the activity Integration Implementation.</p>
	<div>
		<h3>Create Data Models in SAP Analytics Cloud</h3>
		<p>Data models are the foundation for data exploration and data visualizations in your SAP Analytics Cloud stories. Learn how to create and work with data models. Procedure Follow the instructions on how to create your first model in this youtube video or in the SAP Help Portal -&gt; SAP Analytics Cloud help -&gt; Data Preparation and Wrangling (Datasets and Models) -&gt; Getting your Model Ready for Stories -&gt; Create a new Model. Check the SAP Analytics Cloud Community subpage on Data Modeling and Wrangling https://community.sap.com/topics/cloud-analytics/modeling. Results You have learned the fundamentals of data models.</p>
	</div>
	<h2>Custom Code Management Execution</h2>
	<p>Leveraging the analysis conducted during the Explore phase of the project (see activity Custom Code Impact Analysis), the list of mandatory changes is completed within the Realize phase of the project. Custom code adjustment should take place in the already converted DEV system, and be transported to QAS for testing, and then later into PRD. The transport of adjusted custom code objects to PRD takes place as part of the system conversion Go-Live. Requirements and Constraints This activity is relevant in case own code needs to be made ready for SAP BW/4HANA (e.g. the system conversion case). In case of a new implementation, or a landscape transformation, some custom code from an old SAP BW system might be in scope to be used in the new SAP BW/4HANA system. The custom code work list of affected objects has been created (see activity Custom Code Impact Analysis). The development system has been converted to SAP BW/4HANA. Procedure Adjust Affected Custom Code Test and Optimize Your Custom Code Results Custom code has been adjusted for SAP BW/4HANA.</p>
	<div>
		<h3>Adjust Affected Custom Code</h3>
		<p>Adjust the custom code you have identified in the steps before and make it SAP BW/4HANA ready. How SAP Can Support SAP offers the Intelligent Custom Code Management Transformation service component which helps identify and prepare required adjustments to the custom code (see also Custom Code Analysis in the Explore phase).</p>
	</div>
	<h2>Integration Implementation</h2>
	<p>In the activity integration implementation, the customer specific integration design from the Explore phase is implemented, especially the integration with SAP Analytics Cloud and SAP Data Warehouse Cloud is set up now.</p>
	<div>
		<h3>Set up Integration with SAP Analytics Cloud</h3>
		<p>Connect the SAP BW/4HANA to SAP Analytics Cloud. There are two types of data connections possible: live data connections and import data connections. Live Data Connections Are available for cloud and on-premise data sources Do not replicate your data in SAP Analytics Cloud Use existing data models for analysis Update your data visualizations and stories with new data in real-time Import Data Connections Are available for cloud and on-premise data sources Replicate data within SAP Analytics Cloud Create new data models through the SAP Analytics Cloud Modeler Update your data visualizations and stories when refreshed Prerequisite The decision to connect SAP BW/4HANA to SAP Analytics Cloud with a Live Data Connection or an Import Data Connection has been taken. Procedure Live Data Connection A live connection to the SAP BW/4HANA from SAP Analytics Cloud means that queried data from SAP BW/4HANA always remains behind the corporate firewall and does not enter the cloud. You can check the SAP Analytics Cloud Community, topic Data Connectivity, for details. To configure a live data connection, the steps are: Configure CORS (Cross-Origin Resource Sharing) on your SAP NetWeaver system Add SAP Analytics Cloud to the http Allowlist Verify end-users‘ web browser configuration and access Add a remote system to SAP Analytics Cloud Configure SSO (single sign-on) Enable Browsers to Connect from the Internet without VPN Import Data Connection With the Import Data Connection, data is queried from SAP BW/4HANA and is imported to SAP Analytics Cloud. You can check the SAP Analytics Cloud Community, topic Data Connectivity, for details. The following setup steps must be performed, using the SAP Analytics Cloud Agent Simple Deployment Kit (for more information, see „SAP Analytics Cloud Agent Simple Deployment Kit“ in the accelerator section): Install the cloud connector. For more information, see „Installing the SAPCP Cloud Connector“. Install the SAP Analytics Cloud agent. For more information, see „Installing SAP Analytics Cloud Agent“. Install the SAP Java Connector (must be installed using the instructions in the Post-Setup Guide included in the kit - for more information, see „Installing the SAP Java Connector“). Configure the SAPCP cloud connector. For more information, see „Configuring the SAPCP Cloud Connector“. Configure the SAP Analytics Cloud agent in SAP Analytics Cloud For more information, see „Configuring SAP Analytics Cloud Agent“.</p>
		<h3>Investigate the Extension to the Cloud with SAP Datasphere</h3>
		<p>For SAP BW/4HANA customers, SAP Datasphere is the perfect complement to the on-premise investment. SAP BW/4HANA objects work in SAP Datasphere so that the existing investment can be leveraged. Resources can be further optimized by empowering business users and freeing up IT with SAP Data Warehouse Cloud’s self-service modeling. For deeper understanding of the SAP Datasphere and SAP BW/4HANA integration, refer to the blogs and articles in the SAP Datasphere community, like: Business cases show how customers have extended their SAP BW/4HANA to the Cloud creating a hybrid data warehousing strategy. The product plan for SAP BW/4HANA in combination with SAP Datasphere</p>
	</div>
	<h2>Test Preparation</h2>
	<p>The purpose of this activity is to prepare all business-process-related tests according to customer- specific configurations. Requirements and Constraints This activity is required for all scenarios. Procedure Prepare Tests How SAP Can Support The test framework, defined in the Test Preparation during the Explore phase, determines the scope for the test preparation. See accelerator section for details.</p>
	<div>
		<h3>Prepare Tests</h3>
		<p>As determined in the evaluation of the existing test materials and documented within the testing plan, additional assets may need to be developed to support the execution of the testing cycles. Develop the missing test materials and test scripts in accordance with the detailed test plan. Procedure Within each implemented solution scope, the following steps need to be executed: Extend best-practice test cases Develop delta process test cases Finalize integration and user-acceptance test cases and plan Prepare approval procedure Prepare tool adaption and delta user acceptance test training Test scripts are provided in SAP Signavio Process Navigator. SAP Signavio Process Navigator is a free service that is integrated into SAP for Me to provide access to SAP solution scenarios, respective solution processes, and contextual process information such as applicatons, integrations, and implemented solution capabilities. SAP Signavio Process Navigator provides flow diagrams for solution values and solution processes detailing solution activities, test scripts, setup instructions, and other implementation information.</p>
	</div>
	<h2>Test Execution</h2>
	<p>In this activity, integration test, regression test and user acceptance test take place. Requirements and Constraints This activity is required for all scenarios. The unit test has been done as part of the development process in the DEV system already. Procedure Prepare a test environment with the required test data as defined in the activity Test Planning. Once the tests have been planned and test data is available on the test systems, testing can begin. The typical basic process for the Realize phase is as follows: Software developers perform unit tests in the DEV systems. Depending on the type and scope of the test cycle, various functional tests are performed. Manual testers are provided with the tester handout document and receive details regarding their test package by e-mail. Automated tests are scheduled or started directly. Every test that is executed is logged and documented with test notes and a test status is set manually or automatically. If the system responds in an unexpected way during manual testing, for example, if an error message appears, the tester records the incident in the corresponding ITSM system, attaching screenshots, log messages, and so on. Usually, this also has to be done manually even for automated tests. The incident is sent to the persons responsible for the analysis and categorization of defects, who then correct the defect in the development system. The correction is transported to the test system according to the existing arrangements and timelines, where it is then retested. Given the complexity and heterogeneity of modern software solutions, SAP recommends performing the activity Integration Validation, especially for important business processes. This involves gathering and subsequently evaluating a substantial amount of data from the software applications that are active while a given business process is being executed. This type of validation also allows you to identify the hidden warnings and error messages that frequently occur at the interfaces between applications. Furthermore, the operations team should monitor the testing system as if it were production in order to gain early visibility and hands-on experience to possible production issues. If large-scale changes are made or new software solutions are implemented, load tests should be performed before these are used in production. These tests simulate a situation in which the expected load (known number of users and background load in a load-peak situation) is simulated. While doing so, system behavior in handling large data volumes can be inspected. Throughout the entire test cycle, test coordinators monitor the test status and progress, as well as the processing status of incidents that have been reported. The quality of the test data and test scripts directly affect the stability of the productive system following the Go-Live of the change event. Consider an array of representative variances when preparing for the execution of the regression test cycle. It is important to execute realistic data sets that representative production operations of critical business process.</p>
	<div>
		<h3>Perform Integration / Regression / User Acceptance Test</h3>
		<p>The goal of this task is to perform an integration test, regression test and user acceptance test. Procedure Integration Testing is performed to verify proper execution of the entire application including interfaces to external applications. This ensures that the integrated components are functioning properly according to the requirements and specifications. The objective is to perform the end-to-end process integration between all SAP and non-SAP components of the Solution Landscape to validate that all application systems are functioning properly. Integration testing in combination with Regression Testing ensures that all business scenarios have been tested prior to the User Acceptance Testing. How to proceed for Integration Test: Prepare Integration Test plan: Define and document integration test cases, end-to-end customer business process scenarios, according to the test plan. Test plans and test case documentation is stored in Solution Manager. Prepare and document Integration Test Case #1 – n: The purpose of this task is to document the integration test cases outlined in the integration test plan. This activity also contains aligned setup of relevant test data that will be commonly used. Execute Integration Test Case #1 – n: Perform the Integration test according to the previously defined plan. During test execution all issues must be logged and documented in the system for traceability purpose. Perform defect resolution for Integration Test: Resolve any issues identified during the Integration Test. It is crucial that the issues are re-tested by the users that reported them (or designated re-testers) and that they are confirmed. Obtain Integration Test Sign Off: Obtain customer approval (sign-off) of the integration test. The conversion to SAP BW/4HANA may impact productive business processes even following a successful cutover. In order to mitigate the risks and issues to those business processes, it is necessary to regression test them as a part of the project or Release. How to proceed for Regression Test: Prepare a detailed regression test plan with test cases and test scripts. Set up test management procedures to track the progress of the test execution. Set up defect tracking to ensure all identified issues are addressed. Execute the regression test scripts based on the test plan and test cases. Document any anomalies or defects. Monitor the test system as if it were production, as this will provide an indication of end-state operations. For example, errors in the system log, which may not be noticed by testers, could cause instability in the production system. Therefore, it is important to leverage the regression testing cycle to proactively address such issues. Resolve any defects, and retest to ensure all identified issues are closed. How to proceed for User Acceptance (UA) Test: Prepare User Acceptance Test plan: Update the existing integration test cases, end-to-end customer business process scenarios, based on the learnings from previous test phase. UA test plans and test case documentation are stored in Solution Manager. Prepare and document User Acceptance Test Case #1 – n: The purpose of this task is to document the UA test case outlined in the UA test plan. This activity also contains aligned setup of relevant test data that will be commonly used. Execute User Acceptance Test Case #1 – n: Perform the test according to previously defined plan. During the test all issues must be logged and documented in the system for traceability purpose. Perform defect resolution for User Acceptance Test: Resolve any issues identified during testing. It is crucial that the issues are re-tested by the users that reported them (or designated re-testers) and that they are confirmed. Obtain User Acceptance Test Sign Off: Obtain customer approval (sign-off) of the User Acceptance test.</p>
	</div>
	<h2>QAS Setup</h2>
	<p>This activity sets up a quality assurance environment (QAS). Requirements and Constraints This activity is required for the New Implementation, Remote Conversion, and Shell Conversion scenario. There is a technical design document stored in SAP Solution Manager which includes the technical deployment plan, and the software components which need to be installed / updated. The DEV environment has been set up successfully. Tasks Perform the QAS Setup</p>
	<div>
		<h3>Perform QAS Setup</h3>
		<p>The goal of this task is to provide a viable, correctly configured technical quality assurance environment that is available for use by the project team to test configuration and development in a “production like” environment. Procedure Proceed as follows: Execute the technical installation of the required SAP Products for QAS environment as documented in the technical design document (see also activity “Dev Setup” in the Explore phase) Run the technical system setup. Transport development and configuration changes from DEV to QAS. Also consider potential manual rework activities. Results Finally, the QAS environment is ready for testing.</p>
	</div>
	<h2>Object and Data Transfer for Remote Conversion</h2>
	<p>Depending on the type of the remote scenario, data transfer takes place in one of the following ways: • BW objects and data flows are transferred to the new BW/4HANA system (Remote Conversion) • Only selected data models are transferred without data (Shell Conversion). In the case of a Shell Conversion, historical data is ignored, and the system starts fresh. However, you have the option to load data from the old BW system or from source systems. See separate activity “Object transfer in a Shell conversion”. The Planning and preparation of the Remote Conversion has taken place in the Explore phase with the Assessment, followed by customer activities like system setup and software installation of the SAP BW/4HANA system in QAS. In the Realize phase the first test cycle of the data migration can start. The SAP BW/4HANA Conversion Cockpit is used to convert from classic SAP BW data models and data flows to HANA-optimized objects. The converted objects will be collected in SAP BW, transported to and created in the new SAP BW/4HANA system. The BW/4HANA Conversion Cockpit offers status management and progress tracking to ensure a comprehensive documentation of the executed activities. All activities are performed from within the BW/4HANA Conversion Cockpit with a step-by-step guidance. In case of issues, the data transfer activity picks up the process from the latest state – re-execution of already performed steps is not required. Prerequisites The BW sender system and the BW/4HANA receiver system are set up and the necessary pre-checks are done. Tasks Perform Object Conversion using SAP BW/4HANA Conversion Cockpit Transfer of ADP processes using the BW/4HANA Conversion Cockpit</p>
	<div>
		<h3>Perform Object Conversion using SAP BW/4HANA Conversion Cockpit</h3>
		<p>The Conversion Cockpit next transfers the cluster tables using RFC to the SAP BW/4HANA system, including Unicode conversion, if required. After the transfer and conversion of the data flow related BW objects, the transfer of data for the selected data flows take place. The BW/4HANA Conversion Cockpit picks up data from infoCubes, DataStore Objects, Characteristics and Request Metadata from read reports in the sending SAP BW system into cluster tables in the sending system. Figure 4.8: Data Transfer in the Remote Conversion The task lists used in the SAP BW/4HANA Transfer Cockpit are documented in detail in the “Task Lists Documentation for Conversion to SAP BW/4HANA” (see accelerator section). Procedure To initiate the transfer of data models and data flows from SAP BW to SAP BW/4HANA and convert them to HANA-optimized objects, log in to the original SAP BW sytem, start the SAP BW/4HANA Transfer Cockpit (transaction RSB4HCONV) and select “Execute Scope Transfer” in the section “Realization Phase”. To load a new scenario, choose “New”. After creating the package, the system automatically navigates to the SAP BW/4HANA Conversion Cockpit. Proceed in the SAP BW/4HANA Conversion Cockpit as described in the Conversion Guide for SAP BW/4HANA.</p>
		<h3>Transfer APD Processes using the BW/4HANA Conversion Cockpit</h3>
		<p>The APD Transfer Tool can be used to convert APD processes in SAP BW into process chains in SAP BW/4HANA. Procedure Refer to the Conversion Guide for SAP BW/4HANA 2.0, chapter 5.6, for a detailed description of the APD transfer. Note: The APD Transfer Tool and SAP Notes related to remote conversion must be installed on the SAP BW/4HANA system already. The BW/4HANA Conversion Cockpit offers status management and progress tracking to ensure a comprehensive documentation of the executed activities. All activities are performed from within the BW/4HANA Conversion Cockpit with a step-by-step guidance. In case of issues, the data transfer activity picks up the process from the latest state – re-execution of already performed steps is not required. How SAP Can Support SAP offers the service &quot;Execution for Remote Conversion&quot; to BW/4HANA with the following scope: SAP-led execution of data migrations and landscape transformations including tailored support for going live and for a defined post-go-live period Procedure guidance for customer’s tasks in the transformation project Expert support, tailored to the customer‘s situation, to secure the success of the transformation project, such as providing special expertise for performance optimization for landscape transformation technology</p>
		<h3>Object transfer in a Shell Conversion</h3>
		<p>In a Shell Conversion, only a set of selected objects is transferred to the target system. This is done with the Scope Transfer Tool in the SAP BW/4HANA Transfer Cockpit of the original sAP BW System and in transaction STC01 with the task list SAP_BW4_TRANSFER_REMOTE_SHELL. Procedure Open the SAP BW/4HANA Transfer Cockpit and click “Execute Scope Transfer”. Select “Remote (Metadata only)”. Figure 4.10: Select Transfer Scenario Choose that you want to create a new conversion run. The task list “SAP_BW4_TRANSFER_REMOTE_SHELL” opens. Select the step “Collect scope for transfer (Data Flow)” - here you define which objects should be converted - and execute the task list run. Figure 4.11: Task List SAP_BW4_TRANSFER_REMOTE_SHELL Note: To limit the scope to the least number of objects based on dependencies, you can select “Minimal scope”. This option allows separating the transfer of the reporting layer from the transfer of the staging layer. This way you can, for example, convert a MultiProvider to a CompositeProvider without selecting the Part Providers of the MultiProvider. As a next step, define the object mapping between the old objects and HANA-optimized objects. Proceed with the task list until the end of the Prepare phase. Results The selected Data Models and data flows are transferred from SAP BW to SAP BW/4HANA and converted to HANA-optimized objects.</p>
	</div>
	<h2>Cutover Preparation</h2>
	<p>The purpose of this activity is to perform the final preparation steps for cutover. The cutover plan will be tested in the Dress Rehearsal activity in the Deploy phase. Requirements and Constraints This activity is required for all scenarios. However, preparation steps are scenario specific. There is a technical design document stored in SAP Solution Manager which includes the technical deployment plan, and the software components which need to be installed / updated. Procedure Production System Setup Create Cutover Plan (New Implementation) or Create Cutover Plan (System Conversion) or Create Cutover Plan (Landscape Transformation (Example: Client Transfer)</p>
	<div>
		<h3>Production System Setup</h3>
		<p>The goal of this task is to provide a viable, correctly configured production environment that is available for use by the project team to execute final Go-Live simulations. This environment will be used as the future production system (PRD) as of Go-Live. Prerequisites This task is required for those scenarios which set up a new production system. Prerequisite is that the productive hardware is already available and set up. Procedure Execute the technical installation of the required SAP Products for PRD environment as documented in the technical design document. See the installation guide on how to install the components. Run the technical system setup as documented in the administration guide. Transport development and configuration changes from the new QAS to PRD. Consider also the manual rework activities as described in the administration guide. Results Finally, the PRD environment is ready for final Go-Live simulations.</p>
		<h3>Create Cutover Plan (New Implementation)</h3>
		<p>The objective of this task is to create the cutover plan. The plan is based on the learnings from the legacy data migration runs which have been performed so far. Procedure Execute Go Live Simulations 1 – n The purpose of this task is to rehearse or simulate the cutover activities. During simulation, the main objective is to validate and document the tasks, sequence, and duration of items on the cutover plan. These simulations help determine data load efficiency, timing, and sequencing and foster an understanding of requirements for the detailed cutover schedule. The number of simulations varies from project to project, so do the objectives for each simulation. Rehearsals are repeated until go-live risk is minimized. Load simulations give the project team a chance to review the data for errors and data cleansing issues. A new implementation may require three simulations: Technical – Project team participation only for the purpose of validating steps Dry run – Full execution of the cutover schedule with the entire team for the purpose of validating steps and data Final – Full execution of the entire cutover schedule for the purpose of flawless execution to obtain exact timings Results After completing the simulations, the project team has a very good understanding of the potential issues as well as timing and sequence of the final production system data load. The project team is able to refine the cutover schedule and make sure that it realistically reflects the time and effort required for all activities during cutover. Equally important is a contingency plan to handle issues that cannot be resolved within the planned business downtime.</p>
		<h3>Create Cutover Plan (System Conversion)</h3>
		<p>The objective of this task is to create the cutover plan. The plan is based on the learnings from the load &amp; verification runs which have been performed so far. Procedure The conversion of the production system requires a clearly defined cutover plan and will typically be controlled by a cutover manager. You can look up a sample cutover plan for system conversion in the accelerator section for getting insight into the level of detail the cutover plan should have. A cutover plan documents the end-to-end activities of the cutover; from the steps leading up to the event, through to the end of the conversion. High-level tasks commonly found in cutover plans include: Prerequisite steps for the production conversion Ramp-down activities (e.g. batch jobs, interfaces, etc.) Pre-conversion validation reports End-user lockout Technical migration and business data conversion Post conversion changes (e.g. transports, parameter changes, etc.) Technical post conversion validation reports (e.g. checking for business data consistency) Business driven system validation and comparison of the pre and post conversion reports Go/No-Go decision Ramp-Up activities (e.g. batch jobs, interfaces, etc.) User unlock The cutover plan does not detail the technical conversion to the level that is captured in the cookbook. It is common to highlight specific tasks from the cookbook within the cutover plan to ensure the process is on schedule. Every task within the cutover plan should have an assigned owner, estimated duration, and identified dependencies. Whenever possible, assign a name to the owner of the task, and not a team or group of resources. The owner of each task should validate and approve the task to ensure they understand their responsibilities. If there are any tasks required to specifically enable the conversion activities, the cutover plan should include related tasks to reset the values to the intended productive state. The cutover plan should also include a contingency plan to revert the changes in the event there is a No-Go decision. If the contingency plan does not exist within the actual cutover plan, the cutover plan should have reference to the location of the fallback plan. Based on the additional conversion runs, proceed as follows: Document key steps of the migration and conversion activities. Assign owners to all tasks. Review the tasks with the owners to confirm ownership and document the estimated duration. Conduct at least one full walk-through of the plan as an entire team. Document the fallback or contingency plan to safely return production operations in the event of a No-Go. Work with the business process owners and the batch schedule owners to document the steps required to safely and quickly ramp-down and ramp-up production operations. Long running batch jobs should be identified so they can be rescheduled ahead of time. In the event a batch job needs to be manually terminated at the time of the cutover, it is important to have the termination procedures, or the owners, documented and readily available. Results As a result, you will get a validated cutover plan, which documents all the steps leading up to and through a successful Go-Live. Equally important, is a contingency plan in the event there is an unforeseen issue that is unable to be resolved within the planned business downtime.</p>
	</div>
	<h2>Sizing &amp; Scalability Verification</h2>
	<p>In this activity, the sizing estimation performed in the Explore phase (see activity Sizing in the Explore phase) is further detailed out and verified afterwards. The initial assumptions made are now challenged with KPIs from reality. Requirements and Constraints This activity is optional for all scenarios. There is a documented sizing estimation from the Explore phase available. Procedure Optional: Perform Sizing Verification Optional: Perform Scalability Verification</p>
	<div>
		<h3>Optional: Perform Sizing Verification</h3>
		<p>The goal of this task is to validate the sizing estimation from the Explore phase based on the planned application design. Prerequisites There is a documented sizing estimation from the Explore phase stored in SAP Solution Manager. There is a pre-production system already available. Procedure To validate the sizing projections, SAP proposes a two-fold approach: Load testing of the pre-production system Workload analyses across all systems on the critical path in production. The latter is based on technical resource consumption monitoring, the sizing report and application workload analysis. Results The sizing estimation for the productive system has been validated. How SAP Can Support SAP can perform this task with the “Size to Scale” (former name “Advanced Sizing”) service component.</p>
		<h3>Optional: Perform Scalability Verification</h3>
		<p>To guarantee the success of the smooth processing of core business processes, these processes need to be measured, tested and compared between the source and target systems. As a goal, the converted SAP system shall deliver the same or better performance indicators as compared to before the conversion. Therefore, the results have to be compared against the predefined performance baseline. Prerequisites There is a test system available which has comparable hardware with the future productive system. Procedure For the proper verification a test conversion will be performed on comparable hardware containing the representative data volume. The different scenarios for single and mass load testing and verification are set up and processed. Define the success factor for the load tests Perform mass load testing Report and review process Optimization, Re-design and Re-test identified components according to the predefined performance baseline Communication of the results and possible changes in the production environment The results will be measured over the defined timeframe and compared to the baseline. Results The performance gain has been properly measured and documented in SAP Solution Manager. How SAP Can Support SAP can provide support for the test and optimization of the Volume Test, and/or provide specific business case optimization where required. Please contact the embedded support team for more details.</p>
	</div>
	<h2>IT Infrastructure Setup and Test</h2>
	<p>In the Realize phase the technical infrastructure has to be installed and configured as required for the Go-Live. Prior to the Go-Live, a technical verification is proposed to ensure that SAP Best Practices are followed. The technical infrastructure follows the technical design document created in activity Technical Design. Requirements and Constraints This activity is recommended for all scenarios. A technical design document has been created and stored in SAP Solution Manager. Procedure Set Up IT Infrastructure Set Up Integration with SAP Analytics Cloud Test IT Infrastructure Results The technical infrastructure has been properly tested.</p>
	<div>
		<h3>Set Up IT Infrastructure</h3>
		<p>The goal of this task is to set up the IT infrastructure. It covers (besides others): The setup of the server hardware, OS and (optional) virtualization platform: In the event SAP HANA is delivered as an appliance, only sizing, floor space, power, cooling, and network need to be considered. Keep additional setup activities for the SAP application server, or the storage layer in case of TDI (Tailored Datacenter Integration) in mind. The setup of the storage solution: The physical setup of the storage infrastructure – storage systems, storage network, connecting host systems to the storage network – requires comprehensive knowledge of the selected components, and is usually done by system engineers of the storage supplier. Integration of the new components into the existing IT environment (e.g. integration into the existing backup or monitoring solution, network). Setup of High Availability, Disaster Recovery, and Backup. Prerequisites The IT infrastructure components are available and ready for setup. Procedure Proceed as follows: Install the IT infrastructure as designed in the technical design document and dictated by the conversion method and related conversion guides. Document or enhance the installation process in a cookbook for use with future builds. The IT infrastructure is often set up by the hardware partner. Results The IT infrastructure is set up.</p>
		<h3>Test IT Infrastructure</h3>
		<p>Once set up, the IT Infrastructure should be tested thoroughly. Prerequisites The IT infrastructure has been set up. Procedure Proceed as follows: Execute the infrastructure tests based on the test cases and test plan. This should include the following scenarios: Performance Flexibility procedures (e.g. by moving system load to other hosts (e.g. by using virtualization technics), adding instances, changing instances) High Availability Disaster Recovery Backup and Restore Infrastructure Security Document any anomalies or defects. Monitor the test system as if it were production, as this will provide an indication of end-state operations. It is important to leverage this testing cycle to proactively address issues that could arise in production. Measure the performance against the defined key performance indicators to ensure the infrastructure operates within the boundary conditions of the business (see activity performance verification). If already available, test the productive hardware as well at this point in time, in order to validate the configuration. Otherwise, the productive hardware is tested in the Deploy phase. Resolve any defects, and retest to ensure all identified issues are closed. Results The IT infrastructure has been tested properly.</p>
	</div>
	<h2>Operations Implementation</h2>
	<p>Based on the outcome of the Operations Impact Evaluation in the Explore phase, support operations need to be implemented or adjusted. This may affect the IT support staff (their roles and responsibilities; knowledge transfer), support processes and procedures (including proper documentation), and the setup and configuration of support tools. Requirements and Constraints This activity is recommended for all scenarios. The documented results of the Operations Impact Evaluation (Explore phase) are stored in SAP Solution Manager. Procedure Collect detailed Operations Requirements Roles and Responsibilities Support Processes and Procedures Operations Support Tools Operations Documentation Knowledge Transfer Results IT support operations have been prepared to operate SAP BW/4HANA as of Go-Live. How SAP can Support SAP has multiple offerings to SAP Enterprise Support customers with respect to operations implementation: SAP ESRV System Monitoring: System, Database and Host Monitoring – empowering and knowledge transfer are the main goals of this service SAP ESRV Integration Management: Empowerment and knowledge transfer on interface and connection monitoring are the main goals of this service SAP ESRV Root Cause Analysis and Exception Management: Empowerment and knowledge transfer SAP ESRV Data Volume Management (see Activity Data Volume Planning) SAP ESRV Test Management: This service is used to implement the ALM process “Test Management” on a pilot basis at a customer. The pilot implementation addresses the processes of User Acceptance-, Functional-, integration-, and Regression Testing (including Test Scope Identification and Test Automation) SAP ESRV Security Engagement: This service will help the customer on tuning their operations on different aspects based on the needs of the customer, for example: Identify the key security topics of interest Complement the view by a high level 360 degree review on potentially relevant security areas and by recent security news Define the focus spots to be addressed and set up a project plan Define Improvements of the customer’s security situation System Administration Workshop SAP Operations Platform Starter Pack: Guidance for the customer on the initial configuration of SAP Solution Manager or SAP Cloud ALM SAP Empowering Services: Empowering IT resources to maintain, operate, administer an SAP component</p>
	<div>
		<h3>Collect detailed Operations Requirements</h3>
		<p>The Operations Impact Evaluation main outcome is the definition of a list of relevant changes to the current support framework with the corresponding project activities that will support the implementation of those changes. This list is based on the future customer support strategy and deployment model, as well as on the current support framework and the solution that is implemented. In other words, it works like the software change management process and a list of change requests to the IT support framework has been defined. Those have severities and priorities based on the customer management decisions. This activity takes place during the Explore phase with the primary goal to raise the awareness of IT upper management on how the current IT support framework will be impacted. New project activities will be defined to fill the gaps, and this will mean changes in the project plan, potentially additional cost and resource conflicts that will have to be addressed. Analyzing the necessary changes to the IT support framework later in the project will make it more and more difficult to manage these conflicts. Not planning the future support framework will increase the risks of issues in operations after go-live and can be the cause of poor customer satisfaction and increased project costs as often project resources end up with extensions in their involvement to support the new solution. The downside of having the Operations Impact Evaluation take place early in the project is that a lot of details are not yet known, and the specific requirements for the changes to the support framework cannot be always gathered. This needs to happen later in the project. During the Operations Implementation, the project activities defined and prioritized during the Operations Impact Evaluation need to be executed, in other words all the IT related change requests need to be implemented. The first step will be to define the detailed information required for their implementation, their detailed specifications such as which interfaces need to be monitored, key objects to be monitored to ensure data consistency or which access needs to be granted to the future support team members. For this to happen, a coordination task needs to take place where the detailed information is gathered from other project activities. Prerequisites The Operations Impact Evaluation has taken place and the detailed activities necessary to ready the future IT support framework for the operations have been added to the project plan. Procedure The detailed information required to prepare the future IT Support Framework is well known from other project tracks, for example: The Business Process priorities are defined during the Application Design which includes the critical processes, interfaces, and jobs. This will serve as a direct input to the monitoring setup with information on the underlying systems and the solution components. The new/modified roles and authorization objects will be defined as well during Application Design and during the Technical Architecture. This will bring input to finalizing the new Access Management process with the differences in role assignments, and this for business users as well as for the support resources. The inventory of modifications to non-standard SAP code will be defined during Development. This will be a critical input to Knowledge Transfer for the new technical support resources. During the Operations Impact Evaluation, activities related to gathering detailed information will be defined for each area of the support framework that needs to be modified. The way to gather the related information will depend on the project structure and responsibilities. Note: Defining a transition manager to manage all the Operations Implementation activities will make it more efficient than having each activity responsible go to the project resource having the knowledge required for his activity. The transition manager can regroup the detailed information required for the different activities and gather the corresponding details from the best project resources at once. How SAP Can Support When SAP is engaged in a Premium Engagement including other services included in this road map, the gathering of detailed information will be eased. For example, Integration Validation will be a very valuable source of detailed information on the monitoring setup and the critical areas to be supported. Custom Code will be a key source of information for the application support knowledge transfer.</p>
		<h3>Roles and Responsibilities</h3>
		<p>Whatever the current IT support framework is: Operational activities are handled by support personnel organized in roles. These personnel belong to different groups and even different companies when a service provider is engaged, with responsibilities and skill levels that help them in the execution of their activities. When you implement SAP BW/4HANA, there are changes in the roles and responsibilities of some of these resources. These changes will depend highly on your current solution (if you already have SAP implemented), on your support strategy (if you engage a Service Provider to support your SAP BW/4HANA solution), and on your deployment strategy (cloud vs on-premise). How SAP Can Support SAP has a lot of experience and recommendations on the roles and responsibilities design including all steps required to define in detail the roles and responsibilities of the different support resources that are required for the efficient support of the new solution. Please contact SAP for a tailored offering in case you need support.</p>
		<h3>Support Processes and Procedures</h3>
		<p>With the implementation of SAP BW/4HANA, the IT support processes may need to be modified to reflect new operational requirements. The changed IT support processes need to be documented and tested. Prerequisites The documented results of the Operations Impact Evaluation (Explore phase) are stored in SAP Solution Manager. Procedure The following IT support processes are potentially affected with the implementation of SAP BW/4HANA: Event Management: Analyze process change in event management caused by new/modified/retired monitors Retire obsolete monitors, and monitor templates (in SAP Solution Manager) Retire obsolete manual monitoring procedures Incident Management: Update assignment groups, review incident triage Update support resources Update incident categorization Process flow and escalation management including partners and SAP Remove obsolete incident attributes Problem Management: Update problem management process to include new analysis tools Service Level Management: Update SLAs if required (due to changed business KPIs) Update SLA reporting Access Management: Access for new solution / tools granted to new support team members including partner resources New/changed access management process for new/changed solution items (e.g. SAP HANA database) Change Management: Update support resources including requesters and new approvers Update categorization, if required Process flow including partners and escalation management Include project defects in incident database Remove obsolete change attributes Evaluate current change process to include e.g. SAP HANA changes Test Management: Test library should include new test plans/scripts Job Management: Adapt job schedule caused by the new, modified and retired jobs (certain batch jobs may no longer be required, as a result of some reports being able to execute in dialog) Data Volume Management: Changed tools and activities due to new concepts in data volume management (e.g. data aging) Adapt process in those areas where classic data archiving is no longer available System Recovery: Adapt system recovery process to reflect the changed HA/DR architecture IT support processes need to be tested, and access to the new support tools need to be considered. Training on the IT support process changes needs to be in place (or communication in case of small changes). How SAP Can Support SAP has created Best Practice documents for IT support standards (see accelerator section). These documents give first guidance on how IT support processes should be set up. SAP has a lot of experience and additional recommendations on IT support process design that are required for the efficient support of the new SAP BW/4HANA. Please contact SAP for a tailored offering in case you need support. Please also remember the Primary CCOE Certification which need to be in place for Enterprise Support customers.</p>
		<h3>Operations Support Tools</h3>
		<p>Based on the results of the Operations Impact Evaluation, requirements on IT operational support tools need to be finalized to safely operate the new solution (this may also include surrounding components like the Fiori Front End Server). The tools need to be adjusted or newly set up. Deprecated IT operational tools need to be carved out, and IT operational procedures have to be adjusted accordingly. For “Greenfield” customers, the effort required to implement the tools will be much greater. This will require additional effort for the support resources to learn how to use the tools, especially SAP Solution Manager. Prerequisites The documented results of the Operations Impact Evaluation (Explore phase) are stored in SAP Solution Manager. Procedure With the introduction of SAP BW/4HANA, it may be required to adjust existing IT operational support tools, or to set up new tools (e.g. SAP HANA cockpit in case this is the first system on top of SAP HANA). The following list gives you some common examples where adjustment activity may take place. The main list should have been created in the Operations Impact Evaluation (see activity Operations Impact Evaluation in the Explore phase for details). See the Accelerators section for more information on how to execute the necessary adjustments. sAP has dedicated offerings to support this task. Adjust SAP Solution Manager configuration with respect to: General system management (“Managed System Setup”) Monitoring elements (e.g. critical interfaces, critical batch jobs, End-User-Experience, SAP HANA DB), and monitoring customizing (e.g. alert thresholds) Change Request Management and CTS+ (e.g. to be able to handle SAP HANA and/or Fiori objects in a synchronized way) Adjust SAP HANA tools: HANA Cockpit to manage the SAP HANA database SAP HANA Studio e.g. for HANA programming, or HANA dictionary SAP DB Control Center for central &amp; aggregated DB monitoring DBA Cockpit e.g. for running DB administration activities Plan to ramp down support tools (e.g. DB specific scripts your database administrators used in the past), which are no longer required, and adjust IT support procedure descriptions accordingly.</p>
		<h3>Operations Documentation</h3>
		<p>IT operations documentation should be stored centrally in an operations documentation repository, which is then shared across all operational support teams. Prerequisites The documented results of the Operations Impact Evaluation (Explore phase) are available. Procedure Adapt all operational documentation that changes with SAP BW/4HANA. The content needs to be provided by the project team, based on SAP standard documentation that is modified with respect to customer solution specific information. Store the updated operations handbook centrally, either in a company Content Management System, or in SAP Solution Manager. Based on activity Operations Impact Evaluation, and on activity Operations Support Tools, the operational procedures are updated in the operations handbook. The following areas are typically included in the operations handbook (besides others): System Description: Outlines new functions and capabilities, high level architecture, integration details, number of users, expected volumes, use cases, priorities, etc. System Architecture: Architecture, sizing and technical setup information of Solution Manager and other operations tools. Access, Roles and Profiles: Identifies user groups, roles and role approval list Restart and Recovery Procedures: Outlines how to restart or recover from process failures and clearly describes error messages and conditions. Backup / Recovery: Documented process of the backup / recovery methodology; includes standard and emergency backup scheduling and approval process. Batch Scheduling: Documents and presents the batch job schedule. Includes details on the jobs (e.g. stop, restart, criticality, owner, failure procedure), and the batch scheduling tools (if applicable). Run Books: A collection of routinely executed procedures either performed through automated means or manual execution by system administrators (example: system stop and start procedures). Storage Management: Provides technical information on the storage and when to add storage; may also contain instructions on data volume management. Disaster Recovery: Documented process of the recovery steps in case of a disaster (and the disaster declaration procedure itself). Maintenance Management Strategy: Documents the process to implement patches and upgrades (in alignment with the change management strategy). Network Management: Maintenance instructions for the network, network settings and parameters. If applicable, also contains vendor contact information. Non-Functional Requirements: The requirements that do not affect the solution but affect the behavior of the system. It includes availability, maintainability, performance, scalability, security, and system usability. Output Management: Defines the settings and management for all output mechanisms such as printers, fax machines, emails, etc. OS &amp; DB parameters: Defines the operating system and database parameters (and a procedure description on how to change parameters according to the change management process). Vendor Information: Vendor contact information for operations support, and the minimum set of information which needs to be provided. IT Calendar: Identifies agreed maintenance windows, backups and additional technology/infrastructure activities in calendar format. How SAP Can Support SAP has a lot of experience in creating a tailored operations handbook. Please contact SAP for a tailored offering in case you need support.</p>
		<h3>Knowledge Transfer</h3>
		<p>The primary goal of this activity is to analyze all the aspects of the knowledge required to sustain the implementation of the SAP solution, and to ‘grow and groom’ the future SAP IT support resources to effectively and efficiently support the solution. Additional goals of the strategy are to: Define a repetitive process that can be applied for each release and to new hires Reduce or mitigate risk through ownership and accountability Develop metrics to capture and assess performance for knowledge transfer capabilities The objectives of the knowledge transfer are to: Identify roles and responsibilities involved in the knowledge transfer process Transfer knowledge, skills and abilities required to provide solution life cycle support Develop a formal process for monitoring and evaluating the effectiveness of the knowledge transfer process based on objectives and performance metrics Prerequisites The documented results of the Operations Impact Evaluation (Explore phase) are available. Procedure To define the Knowledge Transfer Approach for the Go-Live of a new solution, you will need to take many aspects into account including: The alignment of the knowledge transfer with the overall system conversion project plan The project scope and initial definition of all the knowledge transfer areas to be planned: the functional areas are to be defined, and all technical areas (especially the new ones like Fiori) as well as new support tools The project methodology and documentation The future support organization The availability of the project support resources now and in the future (workload and time) The hyper care phase exit criteria as per contract The sponsorship for the knowledge transfer activities, both for the project and the future IT support operations team The following criteria could be considered to decide when knowledge transfer to the IT support operations team is complete: All high-priority failures resolved All high-priority gaps implemented All deferred requirements fully documented and specified All deferred requirements development planning completed All deferred requirements testing planned All deferred requirements release scheduling planned Master Data is operational, and no critical issues exist Interfaces are running stable All business-critical reports are available Knowledge transfer is documented Service Desk call level is manageable with the team in place, to conduct Level-1 support immediately There are different types of knowledge transfer activities: Formal Training: Standard training as per the SAP Education Catalog Formal Session: T2O/Project trainer provides initial KT session Self-Study: KT Recipient reads documentation provided by T2O/Project team if no other type of training is provided. On the job training: T2O/Project trainer executes an activity with KT Recipient overseeing the execution; KT Recipient participates to testing activities Shadow support / activities: T2O/Project trainer assigns a specific task to a KT Recipient, KT Recipient executes and documents execution.</p>
	</div>
	<h2>Execution, Monitoring and Controlling Results</h2>
	<p>The purpose of this deliverable is for the project team to carry out the planned and approved project work and measures project performance to identify variances from the plan. Executing manages the scope of the work to be done, while monitoring the execution, identifies variances from the plan, monitors quality results, controls progress against the plan, controls changes throughout the project, assesses and responds to risks, and communicates project status. The Project Management Plan developed during the Prepare phase for each of the PM knowledge areas, guides the team&#x27;s approach to management, execution, monitoring, and control of project activities. This methodology provides steps to be considered when each activity is engaged and supported by the team&#x27;s monitoring/controlling plans. The project manager is responsible for ensuring that the monitoring/controlling plans developed in the planning phase are applied at the appropriate level of control.</p>
	<div>
		<h3>Direct and Manage Project Execution</h3>
		<p>The purpose of this task is for the project manager and the project team to execute the project management plan and to accomplish the work defined in the project scope statement. In executing the project work, the project team will perform the following types of activities: Perform project tasks to reach project objectives Expend effort and funds to accomplish project objectives Staff, train, and manage the project team members assigned to the project Obtain, manage, and utilize resources Implement and execute the planned methods and standards Create, control, verify, and validate project deliverables Manage the scope of the approved work Manage risks and implement risk response activities Manage issues to closure Adapt approved changes into the scope, plans, and project environment Establish and manage internal and external communications Collect project data and report on progress and performance In monitoring/controlling work results, the project team performs the following types of activities: Compare actual project performance to the project management plan Assess performance to determine whether corrective or preventive actions are necessary Monitor and control project risks Provide metrics to support progress and performance reporting and forecasting Monitor implementation of approved changes Take corrective action as needed Management plans developed during the Prepare phase guide the team&#x27;s approach to management, execution, and control of project activities. See “Prepare Project Management Plan” task in the Prepare phase for details. The project manager is responsible for ensuring that the management plans are applied at the appropriate level of control.</p>
		<h3>Update Project Management Documents</h3>
		<p>The purpose of this task is to ensure that the project manager keeps the key project management documents up-to-date. This task includes refinement of the project schedule, refinement of the project budget, and appropriate updates to the management plans, scope document and business case to reflect the detailed scope information created during the project. The project management documents are, together, a comprehensive body of project documents that includes the project schedule, budget/cost information, monitoring/controlling plans for each of the nine knowledge areas of the Project Management Body of Knowledge (PMBOK), and other information as appropriate. The project management plan document developed for each of the knowledge areas provide the foundation for the consistent application of project management practices. This task includes updates to: Project management plan Project WBS Project schedule Project budget Business case Scope document Backlog document (created in Explore phase)</p>
		<h3>Manage Project Issues, Risks, and Changes</h3>
		<p>The purpose of the Manage Project Issues, Risks, and Changes task is to have a consistent, comprehensive approach to managing project issues and is a critical component of an effective project management system. Effective issue management involves the appropriate level of management making decisions on issues and tracking progress on issue resolution in accordance with the project issue management procedure. Identified issues are maintained in one, central environment. The Issue Management handling involves the following steps: An issue is raised and properly classified as it is related to the system conversion, solution implementation, or solution operations of an SAP solution or technology. It is created in the issue tracking tool, either by SAP, a customer or a system integrator. During the issue creation, the person raising the issues assigns the priority and the responsible person for resolution of the issue. The Project Manager follows up on issues on a regular basis in addition to the standard issue management process defined for the project (part of the management plans) Critical issues will be reviewed as an input for each Quality Gate review meeting. Open issues are reviewed and updated on a regular basis and communicated to the appropriate stakeholder groups as part of the regular project reporting and communication. From SAP&#x27;s perspective, issue tracking allows for better visibility and transparency of open issues, problems, action items, and associated action plans to the project management team. A central issue tracking system (e.g. a support or an incident ticket system) allows stakeholders to manage and maintain lists of issues that require action and resolution to ensure the success of the project.</p>
		<h3>Communicate Project Status and Progress</h3>
		<p>The purpose of this task is to communicate project status and progress. Throughout the project, a number of project performance reports need to be produced for different purposes and audiences, as defined in the project communication plan. The project manager and project team are responsible for clearly communicating the progress of the key activities, completion of the deliverables, and status against the schedule and budget. The project teams typically prepare: Team member status update typically produced weekly and shared with the team lead. It is recommended that this report is kept very lightweight and provided to the team lead via e-mail or in a team meeting. Team status reports on regular cadence, typically weekly. The team status reports are prepared by the team leads and are delivered to the project manager as an input for the project status report. The team status report may be reviewed in regular team review with the Project Manager or provided in a predefined format. Project status report, using the project status report template. The project status report is created weekly, based on the input from individual teams and additional information like issues list, risk list, etc. Executive status report is typically prepared on monthly or quarterly cadence and is provided to the project executive steering group. Generally, this report reuses the key information from the weekly project status report, expands on the value and benefits of the program, and includes discussion of the decisions that are needed from the executive steering group. Additionally, throughout the project, as more is known, the project communication matrix should be reviewed and updated. The communication matrix documents the project team’s approach to communication (including status reporting). It captures the analysis completed as part of communications planning and serves as a tool to guide the project team throughout the project.</p>
		<h3>Plan and Execute Agile Sprints</h3>
		<p>The purpose of this task is for the project team to plan and execute agile sprints. The project team runs the Realize phase in a sequence of sprints in which the project team follows the process outlined below. The goal of these sprints is to incrementally and iteratively build the solution capabilities captured in the backlog, test them and review their completion with the customer process owners (product owner in agile terminology). Figure: Steps of Agile Sprints During each sprint the project team conducts the following activities: Sprint Planning Meeting At the beginning of the sprint the project team runs a planning meeting during which the team (jointly with the process owner) selects the highest priority items from the backlog and conducts detailed planning for the execution activities in the sprint. Each backlog item is further decomposed to tasks that need to be completed during the sprint. These tasks may be configuration, coding, unit testing, data preparation, documentation and others. These tasks are then captured in the sprint backlog and estimated to validate the original estimates for each backlog item. As a result of this planning, the team has clarity on what needs to be completed for each backlog item included in the sprint and has confidence that the team has sufficient capacity to complete the work. Sprint Execution Activities During the sprint the team members execute the tasks that have been planned and the team keeps track of the progress on the team board. The team board contains swim lanes showing the status of each backlog item and each task. Teams are encouraged to use boards on the wall and use the post-it notes to keep visual track of their progress. The team also regularly updates the burn-down chart that is used to track the progress of completing the individual tasks and backlog items. The team reviews the progress on a daily basis in the daily stand-up meeting. Daily Stand-up Meeting During the daily stand-up meeting the team members review the (a) progress they have made since the last meeting; (b) plans for activities and tasks until the next meeting; and (c) any issues of blockers that may prevent them from completing the tasks they are working on. The daily stand-up meeting is not a project status meeting, but rather a session designed to help the team communicate the progress with each other. Sprint Demo Towards the end of the sprint the team conducts the sprint demo meeting during which the team demonstrates to the business owners (product owner in agile projects) the completed functionality. The project team seeks acceptance of the completed features. During this meeting the business owner may request additional items to be added to the backlog (or items to be removed from the backlog). It is recommended that the team previews the functionality with process owners prior to this meeting. Many projects had great experience with having the business process owners demo the completed functionality to the rest of the business users and decisions makers (instead of the project team members). Sprint Retrospective SCRUM Master organizes and facilitates the retrospective meeting for the team. The meeting is typically scheduled shortly after the sprint demo meeting. Purpose of the meeting is to continuously improve the Scrum process using lessons learned from the sprint execution. Meeting participants answer the following questions: What went well during the sprint? What do we want to preserve? What can be improved for the next sprint and how? The team selects one or two improvement opportunities and puts it into the backlog for the next sprint. This way the agile process gets improved in an incremental way and remains responsive to the changing environment of the project.</p>
		<h3>Perform Scrum-Of-Scrums Meeting (Iterative)</h3>
		<p>A SCRUM of SCRUMs Meeting is conducted to coordinate the work between different project SCRUM teams. SCRUM of SCRUMs meetings allow teams to discuss their work, focusing especially on areas of overlap and integration. The meeting cadence is defined for the entire project and follows the same structure. Some teams conduct daily SCRUM of SCRUMs meeting while others consider weekly meeting sufficient. The main driver for the meeting cadence is the level of collaboration between the individual SCRUM teams and level of dependencies between the features built in each SCRUM team.</p>
	</div>
	<h2>OCM Alignment Activities</h2>
	<p>The purpose of this deliverable is to identify and assess other Organizational Change Management activities related to the OCM execution plan.</p>
	<div>
		<h3>OCM and Testing alignment</h3>
		<p>The purpose of this task is to understand the testing strategy, validate its alignment with the project charter, and to identify any potential roadblocks or risks associated with functionality not working as expected. Any deviation from expectations should be communicated to project management and project sponsors. Procedure: Perform Assessment of Testing Strategy and Execution Capture Feedback from Testing Team</p>
		<h3>OCM and Data Migration alignment</h3>
		<p>The purpose of this task is to understand the data migration strategy, validate its alignment with the project charter, and to identify any potential roadblocks or risks associated with data quality or availability not as expected. Any deviation from expectations should be communicated to project management and project sponsors. Procedure: Perform Assessment of Data Migration Strategy and Execution Capture Feedback of Data Migration Team</p>
	</div>
	<h2>Phase Closure and Sign-Off Phase Deliverables</h2>
	<p>The purpose of the phase closure and sign-off deliverable is to. Ensure that all required deliverables from this phase and the project are complete and accurate, and close any outstanding issues Identify lessons learned during the phase to prepare for formal phase closure Capture customer feedback and potential Customer References Tasks Conduct Knowledge Management Gate Conduct Project Quality Gate Conduct Project Management Review Service Conduct Design Review Service Manage Fulfilled Contracts Obtain Customer Sign-Off for Phase Completion</p>
	<div>
		<h3>Conduct Knowledge Management Gate</h3>
		<p>The purpose of this task is to collect knowledge assets and lessons learned at the end of each phase of the project that can be reused later by other projects. Collecting documents, experiences, project highlights and lessons learned throughout the duration of the project can help to facilitate the project by providing quick access and insights into key deliverables from earlier stages of the project.</p>
		<h3>Conduct Project Quality Gate</h3>
		<p>The purpose of the Quality Gate is to ensure that both compliance and project management standards are being upheld within our projects. A Quality Gate is a checklist milestone at the end of a project phase. Prior to moving into the next phase each project manager must demonstrate that they have complied with the mandatory deliverables associated with a methodology while ensuring best practice standards have been applied to ensure quality. In detail: To ensure that all key deliverables and actions of the gate have been completed in compliance with recommended practices and to the customer’s satisfaction To enable project management to continuously communicate the process and build quality directly into the project To provide a tool to effectively manage project expectations and monitor customer satisfaction. The deliverables assessed at each quality gate will be performed using the quality gate checklist with defined expectations to the maturity of particular project deliverables. Note: New additional key deliverables need to be added to the quality gate checklist by the Project Manager to the different project types.</p>
		<h3>Manage Fulfilled Contracts</h3>
		<p>The purpose of this task is to ensure that each contract is closed by verifying that all work specified in the contractual arrangement was completed, and that all defined deliverables were accepted.</p>
		<h3>Obtain Customer Sign-Off for Phase Completion</h3>
		<p>The purpose of this task is to conduct formal closure of the project phase and sign-off the deliverables completed during the phase. The main goal of this process is to: Ensure that all required deliverables from this phase and the project are complete and accurate, and close any outstanding issues Identify lessons learned during the phase to prepare for formal project closure Capture customer feedback and potential customer references When sign-off is obtained, the project can proceed to the next phase. Use the attached sign-off sheet for this task.</p>
	</div>
</div>
<h1>Deploy</h1>
<p>Once quality gate Q3 – Realize-to-Deploy has been passed successfully, the final preparation for Go-Live starts in the Deploy Phase. Figure 5.1: Activities in the Deploy Phase End user trainings for the new solution are running in the Solution Adoption work stream. In the Application Design and Configuration work stream, the implementation activities will come to an end. Integration validation ensures the required performance. Testing (in particular regression and user acceptance testing) is taken care of in the Testing work stream. All affected custom code should have been adapted and tested in the Realize phase already. Overall there is nothing to do in the Extensibility work stream in the Deploy phase. In the Technical Architecture and Infrastructure work stream, final IT service setup activities followed by the final rehearsal of the cut-over procedure will take place. Most importantly, this work stream processes the implementation of the productive system which will be finalized at the Go-Live weekend. Operations and Support ensures the IT operations team is ready to operate the new system environment safely and securely. Of course, the IT operations team will continue to gain real-life operational experience in the hyper care phase after Go-Live. Quality gate Q4 Deploy-to-Run will ensure that everything is ready for Go-Live. The final “Go” decision is the start for the implementation of the productive system. The Deploy phase ends with the production cutover at the Go-Live weekend.</p>
<div>
	</div>
	<h2>Phase Initiation</h2>
	<p>The purpose of the phase initiation deliverable is to formally recognize that a new project phase starts.</p>
	<div>
		<h3>Allocate Resources and Update Project Schedule</h3>
		<p>The purpose of this task is to confirm resource availability for the particular phase.</p>
		<h3>Perform Kickoff Meeting</h3>
		<p>The purpose of this task is to ensure the involvement of the team and other key resources and their commitment to the project schedule. The meeting is also used to examine the approach for the specific project phase.</p>
	</div>
	<h2>Learning Realization</h2>
	<p>This activity continues from the Realization phase. Based on the learning material which has been created there, end user training takes place in this phase. Requirements and Constraints This activity is recommended for all scenarios. The development of learning material has finished. Procedure Create Training Execution Plan Execute End User Training Results The end users have been trained. They are enabled to use the new system.</p>
	<div>
		<h3>Create Training Execution Plan</h3>
		<p>The training execution plan is based on the results as of the training concept, the Learning Needs Analysis, and the end user assignment. Create a training execution plan. Procedure The training execution plan should include: The scheduling of the trainings The training approach and method (as of the concept) The assigned instructors (Co-instructor / Key Users) Location, duration, date and time Required training material (content) Training system information (access information, exercises, etc.) The training execution plan should include all planned trainings and courses across the project phases, roles, functions and processes for a complete overview of all training course activities.</p>
		<h3>Execute End User Training</h3>
		<p>Based on the results of the Learning Needs Analysis and the training concept, the end user training covers the training needs for all end users. Prerequisites A training execution plan has been created before. Procedure The training will be performed either by the customer key user in a tandem approach with SAP trainers, or by SAP trainers only. Assumptions and/or pre-requisites for the trainings are described in the training concept prior to the end user trainings. Examples are: Language of the trainings (i.e. English). Maximum number of participants per training course (i.e. max. 12-15 participants). Training location of the training course. Customer SAP training system Results As a result end users are trained. SAP recommends issuing participation certifications and collecting end user feedback. How SAP Can Support With event and invitation management, SAP can support this task as part of an individual education consulting offer from SAP Education.Event and invitation management is the process of booking training facilities /resources, managing courses in the learning management solution, and publishing learning content in the digital environment. For instance, SAP can: Send invitations to trainers and end users including all necessary information Prepare facilities (virtual or onsite) for training delivery Allocate properly sized and equipped rooms for training (including network connection) Organize training material distribution.</p>
	</div>
	<h2>Integration Validation</h2>
	<p>The Integration Validation activities initiated in the Realize phase, are continued and finalized in this activity. Requirements and Constraints This activity is recommended for all scenarios. Procedure Finalize the Integration Validation activities which have started in the Realize phase (see activity Integration Validation). Results As the result of this activity, integration validation has been finished.</p>
	<div>
		<h3>Finalize Integration Validation</h3>
		<p>The goal of this task is to finish IV activities before Go-Live. Prerequisites Integration Validation has started in the Realize phase. How SAP Can Support IV is usually executed with SAP participating. The SAP TQM will initiate the right IV support activities from SAP: Integration Validation service component: This service component provides technical validation of core business processes with respect to non-functional requirements, identification and addressing of technical risks prior to Go-Live and during the production cutover including hyper-care phase. A comprehensive status of technical Go-Live readiness for core business processes (Integration Validation matrix) is part of the service component. Business Process Technical Validation service component: This service component ensures technical readiness of the core business process for Go-Live. It addresses areas like data consistency, exception management, performance and scalability, system integration, batch and volume processing. In the focus is the technical validation of the core business processes and preparation for the subsequent efficient operation of the software solution. Technical Performance Optimization service component: The technical performance optimization service component improves your SAP solution by helping you to configure your system in an optimal way. The identification and elimination of costly performance bottlenecks optimizes the response times and throughput of your SAP solution. SAP Enterprise Support Customers can order the CQC for Technical Performance Optimization (TPO).</p>
	</div>
	<h2>Dress Rehearsal</h2>
	<p>In preparation for the Go-Live of the transition project, it is imperative to execute an end-to-end dress rehearsal of the cutover procedures. The rehearsal should be executed about two to four weeks prior to the Go-Live. All intended changes to be included in the cutover should be available for the dress rehearsal. This includes any changes that result from the testing cycles, as even a single transport could greatly impact the duration of the process. From this point forward, changes to production should be restricted in order to mitigate risks to the cutover procedures (system conversion only). If there is a need to make a change to production after this point, it should be carefully evaluated, and the impact should be fully understood. In some cases, there may be a requirement to postpone the Go-Live and re-execute the dress rehearsal in order to accommodate intrusive changes. Requirements and Constraints This activity is recommended for all scenarios. Prerequisite for this activity is: The detailed cutover plan with owners, dependencies and durations fully documented. The involvement of all task owners. A test environment representative of the source and target platforms for production. The technical cookbook, which details all of the required technical migration steps. Procedure Perform Cut-Over Rehearsal</p>
	<div>
		<h3>Perform Cut-Over Rehearsal</h3>
		<p>The goal of this task is to execute an end-to-end dress rehearsal of the cutover procedures. Procedure Execute the cutover plan in its entirety in a non-productive environment, which is representative of the current state and end-state of production. The dress rehearsal is intended to be used to confirm the ownership, sequence, and duration of the cutover procedures. If significant changes to the process are required as a result of the dress rehearsal, there may be a need to postpone the Go-Live. It is also very critical to communicate the latest plan to related parties to ensure a smooth Production Cutover for the last time.</p>
		<h3>Finalize IT Infrastructure Services</h3>
		<p>Due to cost saving reasons the hardware for production is set up just in front of Go-Live (system conversion case). In case of new implementation, it might be required to have the hardware ready by the end of the Realize phase (see activity Cutover Preparation). In this activity all remaining tasks required to finalize the IT infrastructure need to be completed. Procedure Proceed as follows: Complete the setup of the IT infrastructure hosting production (e.g. hardware setup, network connections, etc…). Correct all critical open items which have been detected in the IT infrastructure test (see activity IT Infrastructure Setup and Test in the Realize phase for details). Finalize IT infrastructure service definition and documentation as part of the IT service catalog (properly explaining for instance what SLAs IT is offering to the Lines of Business for a particular IT infrastructure service). Results As a result, the IT infrastructure is ready for hosting production.</p>
	</div>
	<h2>Operations Readiness</h2>
	<p>This activity checks the customer’s ability to operate SAP HANA and SAP BW/4HANA as of Go-Live. Requirements and Constraints This activity is recommended for all scenarios. Procedure Operational Readiness (System Conversion) or Operational Readiness (New Implementation)</p>
	<div>
		<h3>Operational Readiness (System Conversion)</h3>
		<p>The purpose of this task is to perform an Operations Readiness check. In case of a system conversion, the customer operated a productive SAP BW system over a longer period of time. Standard IT support processes (e.g. change management, event management) have been designed and operated for SAP already. The customer already knows how to operate SAP. Only the delta to safely operate SAP BW/4HANA needs to be checked. Procedure Check if all operational aspects have been implemented as planned (see Operations Implementation activity in the Realize phase). This covers: Roles and Responsibilities Support Processes and Procedures Operations Support Tools Operations Documentation Knowledge Transfer Results The IT support organization is ready to operate SAP BW/4HANA as of Go-Live. How SAP Can Support SAP offers an Operations Readiness check as part of the Transition to Operations service. The scope covers tools for monitoring, troubleshooting, and software logistics. It includes as well a status review of the IT Operations changes defined during the Operations Impact Evaluation. Ideally the check is performed a couple of weeks before Go-Live.</p>
		<h3>Operational Readiness (New Implementation)</h3>
		<p>In case the customer knows already how to operate SAP BW, then only the delta to safely operate SAP BW/4HANA needs to be checked in this task. However, in case of a new SAP customer, all core IT support processes (as documented in the SAP Support standards) need to be checked with respect to SAP BW/4HANA. Procedure For customers who know how to operate SAP BW already: Check if all operational aspects have been implemented as planned (see Operations Implementation activity in the Realize phase). This covers: Roles and Responsibilities Support Processes and Procedures Operations Support Tools Operations Documentation Knowledge Transfer For new SAP customers: Check if all operational aspects have been implemented as planned (see Operations Implementation activity in the Realize phase). Check if all IT Support Processes have been implemented / adjusted with respect to SAP BW/4HANA operations (see SAP Support Standards). Check if primary CCOE certification has been gained. How SAP Can Support SAP offers an Operations Readiness check as part of the Transition to Operations service. The scope covers tools for monitoring, troubleshooting, and software logistics. It also includes a status review of the IT Operations changes defined during the Operations Impact Evaluation. Ideally the check is performed a couple of weeks before Go-Live. See accelerator section for details. For new SAP customers, SAP offers additional expertise and help to check and ensure operational readiness before Go-Live. See also the Organizational and Production Support Readiness Check as part of OCM in this phase. Please contact SAP for a tailored offering in case you need support.</p>
	</div>
	<h2>Execution, Monitoring and Controlling Results</h2>
	<p>The purpose of this deliverable is to execute the project management plan and control and monitor the work defined in the project scope statement. Management plans developed during the project preparation phase guide the approach to management, execution, and control of project activities. The project manager is responsible for ensuring that the management plans are applied at the appropriate level of control.</p>
	<div>
		<h3>Update Project Management Plan</h3>
		<p>The purpose of this task is to update the project management plan and the subsidiary plans based on the changes agreed during the projects change management process.</p>
		<h3>Direct and Manage Project Execution</h3>
		<p>The purpose of this activity is to assure that the project is executed according to what was agreed to in project charter, scope statement and project management plan.</p>
		<h3>Monitor and Control Project Activities</h3>
		<p>The purpose of this task is to assure that resources are assigned to all scheduled project activities (and tasks) and that work is progressing, and deliverables are produced as expected.</p>
		<h3>Manage Issues, Risks and Changes</h3>
		<p>The purpose of this task is to capture and manage project issues and risks and changes related to those e.g. changes of project scope, timeline, costs etc.</p>
		<h3>Communicate Status and Progress to Project Stakeholders</h3>
		<p>The purpose of this task is to make sure that project stakeholders are aware of status and progress of the project including potential disturbances due to existing risks and issues.</p>
	</div>
	<h2>Release Closing</h2>
	<p>The purpose of this deliverable is to formally close the release and prepare for next release and/or sprint planning meeting.</p>
	<div>
		<h3>Prepare Product Backlog for Next Release/Sprint</h3>
		<p>The purpose of this task is to &#x27;groom&#x27; the product backlog. Product Owner Team needs to detail the user stories to ready them for next release/sprint planning meeting. The stories need to meet the definition of Ready for Build so they are understood by the SCRUM team and can be estimated during the sprint planning meeting.</p>
		<h3>Conduct Release Retrospective</h3>
		<p>The purpose of this task is to conduct retrospective meetings with the project team to identify potential improvements of the SCRUM process. The objective of this task is to serve as a continuous improvement mechanism for the team to adjust to changing project environment and needs. The team will select one or two key improvements to implement in the next iteration and handles them as user stories that are added to the product backlog, prioritized and tracked along with other user stories.</p>
		<h3>Update the Release and Sprint Plan</h3>
		<p>The purpose of this task is to update the Release and Sprint Plan according to changed priorities and focus of the team. It is the accountability of the Product Owner to maintain the Release and Sprint plan and keep it current during the project.</p>
	</div>
	<h2>Production Cutover</h2>
	<p>The purpose of this deliverable is to perform the cutover to the production software and go live. Requirements and Constraints At this point, the organizational, business, functional, technical, and system aspects of the project are ready to be used in production. This activity is mandatory for all scenarios. The steps being performed are of course scenario specific. Procedure Convert Productive System (System Conversion) or Production Cutover (New Implementation) or Production Cutover (Selective Data Transition) Results After completion of this activity, the productive SAP BW/4HANA system is available for the end users. How SAP Can Support The “SAP Going Live Support” service component is based on a standardized method to support critical situations during production cutover. SAP experts contribute their knowledge and expertise remotely to minimize the risks for the Go-Live. Suddenly slow running applications in the new productive system are addressed by a Technical Performance Optimization service component (SAP Enterprise Support order the CQC for Technical Performance Optimization (TPO) instead): The technical performance optimization service component improves your SAP solution by helping you to configure your SAP BW/4HANA system in an optimal way. The identification and elimination of costly performance bottlenecks optimizes the response times and throughput of your SAP solution. SAP Enterprise Support customers can request a Continuous Quality Check (CQC) for “Going-Live Support”. Ask your SAP Enterprise Support Advisor for details.</p>
	<div>
		<h3>Convert Productive System (System Conversion)</h3>
		<p>The goal of this task is to convert the productive system. Procedure Proceed as follows: Request Restore Point of Production System Prior to Final Cutover Activities Execute the conversion of the production system following the tasks defined in the cutover plan. Document the actual duration of each step to support future projects. Capture any variances to the plan along with the decision maker who approved the change. The cutover manager(s) should proactively notify task owners of upcoming tasks, to ensure their availability. Regularly communicate status to stakeholders. After conversion has finished (including mandatory post-processing activities), the system has to be tested and validated Obtain system sign-off Results The customer approval (sign-off) documents the agreement with the stakeholders that cutover tasks have been executed, the go-live acceptance criteria have been met, and the cutover is finished. It indicates formal approval to end the cutover activities. At this point, the solution is live in production.</p>
		<h3>Production Cutover (New Implementation)</h3>
		<p>The goal of this task is to cut over production. Procedure Execute the cutover following the tasks defined in the cutover plan. This includes the final production data load. Document the actual duration of each step to support future projects. Capture any variances to the plan along with the decision maker who approved the change. The cutover manager(s) should proactively notify task owners of upcoming tasks, to ensure their availability. Regularly communicate status to stakeholders. After the data is loaded, testing and data reconciliation must be completed. Obtain system sign-off Results The customer approval (sign-off) documents the agreement with the stakeholders that cutover tasks have been executed, the go-live acceptance criteria have been met, and the cutover is finished. It indicates formal approval to end the cutover activities. At this point, the solution is live.</p>
	</div>
	<h2>Post Go-Live End User Training</h2>
	<p>The purpose of EU training is to ensure that end users have adopted the solution, knowledge resources are maintained, and responses to the end-user acceptance survey are positive</p>
	<div>
		<h3>Prepare End-User Training for Scope Option</h3>
		<p>The purpose of this task is to adapt available training material and make it suitable for End User training</p>
		<h3>Deliver End-User Training for Scope Option</h3>
		<p>This task executes the delivery of EU training as well as capturing feedback on both the training session and the trainer.</p>
		<h3>Collect Training Evaluations Feedback</h3>
		<p>The purpose of this task is to capture feedback on both the training session and the trainer delivering the material.</p>
		<h3>Perform People Readiness Assessment</h3>
		<p>This task checks how prepared the people in the organization are with regards to the identified changes and if they have received an end user training.</p>
	</div>
	<h2>Production Support After Go Live</h2>
	<p>The purpose of the production support and transfer to solution deliverable is to confirm that the resources and processes are in place to support the ongoing solution and to complete the steps required to close the project and finish documentation.</p>
	<div>
		<h3>Finalize Solution Manager Update – Solution Documentation</h3>
		<p>Purpose of this task is to hand over the finalized implementation project and its content and set it up as a productive solution in SAP Solution Manager. This provides the basis for the follow-up activities in the Operate Phase.</p>
		<h3>Obtain Solution Transition to Production Acceptance Protocol Sign-Off</h3>
		<p>Purpose of this task is to obtain customer approval (sign-off)</p>
	</div>
	<h2>Hyper Care Support</h2>
	<p>After the Go-Live it is important to verify how the new workload behaves as opposed to the old system, and to use the Hyper Care phase in particular to improve system performance. Workload analysis With the analysis of the current hardware consumption, the load distribution across the different applications and task types, as well as average response times you establish a kind of benchmark to measure the success of the conversion. As response times are very sensitive KPIs, it makes sense to capture its data over a long period of time, ideally more than six months (this can be established by collecting monitoring data long term in SAP Solution Manager). Health check and scalability analysis The scalability analysis contains system health checks (DB buffer, wait time, etc.) as well as the identification of statements that cause bottleneck situations. Sizing verification Customers should monitor the technical KPIs in terms of CPU and memory consumption to assess the actual usage vs. the deployed hardware. Requirements and Constraints This activity is required for all scenarios. Precondition is that the Monitoring Infrastructure (SAP Solution Manager preferred) is already set up. Procedure Monitor Resource Consumption Analyze Workload Check System Scalability Run Going-Live Service (Verification Session) How SAP Can Support SAP can join the hyper care phase by continuing the ”SAP Going Live Support” and the “Technical Performance Optimization” service components. With the monitoring of the core business processes and the system environment of the new system, SAP experts contribute their knowledge remotely to minimize the risk for instable operations and performance. SAP experts will also work on open issues and can address them to the SAP development directly if necessary.</p>
	<div>
		<h3>Monitor Resource Consumption</h3>
		<p>The objective of this task is to identify the resource consumption after Go-Live and to manage it permanently to answer questions like: What is the current workload profile? Is the resource consumption in a reasonable ratio to the business logic and value? Is the resource consumption stable or does it increase even though there is no additional functional or business load in the system? Procedure The following KPIs are to be measured: Physical CPU consumption over time (SAP application and DB server): [average per month / week / day] Workload profile (SAP application and DB server): [peaks, averages, load balancing] Consider seasonal fluctuations: [e.g. period end closing] Memory consumption (SAP application and DB server): [buffer settings and usage]</p>
		<h3>Analyze Workload</h3>
		<p>Workload analysis first has the goal to provide information about which applications, transactions, jobs, processes dominate the workload consumption. Then, in the second step, candidates for performance optimization will get identified and prioritized. Procedure You measure the following KPIs Resource consumption per task type: [#steps * (CPU time + DB time)] Resource consumption per transaction/process: [#steps * (CPU time + DB time)] Duration of background jobs: [seconds] Response time per dialog transaction: [average time, time distribution] Most expensive SQL statements : [#executions, elapse time] Create a list of the top consumers and the most important SQL statements. Decide which item should be checked and further optimized. How SAP Can Support SAP can support both the code analysis and the optimization.</p>
		<h3>Check System Scalability</h3>
		<p>A system scalability check has the goal to understand the top resource consumers with respect to: Adequacy and optimization potential on technical level Adequacy and optimization potential on service level Adequacy and optimization potential on business level The scalability check focuses on identifying the resources that form the bottleneck for a further increase of the load. It helps to guarantee that the system is not laid out for irrelevant tasks and to identify the load drivers from the business. Procedure Proceed as follows: Sort the list of top consumers by the consumption of the resource that is the largest bottleneck. Check, starting with the largest resource consumer: Whether it is possible to reduce the resource consumption by optimizing the database or the coding that is responsible for the resource consumption. Whether it is possible to avoid the bottleneck by optimizing load balancing and scheduling of services. Whether the business value obtained from the service justifies the resource consumption. As a result, the load drivers on your system are thoroughly understood. The top resource consumers are well optimized and their business relevance is known. Optimal support for the business can be provided, even for changing business requirements. Knowing the load drivers for the top resource consumers gives the ability to predict the effect of changing business beforehand.</p>
		<h3>Follow-up on Going-Live Check (Verification Session)</h3>
		<p>Depending on the project scope, you have either ordered the analysis session of an SAP OS/DB Migration Check, or an SAP Going-Live Functional Upgrade Check, or an SAP Going-Live Check for Implementation (see activity Integration Validation in the Realize phase for details). Four to six weeks after Go-Live the verification session of this service should take place. This session analyzes the converted system and provides corrective measures to avoid potential bottlenecks. See accelerator section for details. Procedure Proceed as follows: SAP runs the Going-Live Check. Follow up and solve all yellow and red issues identified in the service report. How SAP Can Support SAP delivers the SAP Going-Live Check ( verification session) in this task.</p>
	</div>
	<h2>Handover to Support Organization</h2>
	<p>Once the hyper care phase ends it is important to fully enable the regular support organization at customer site to safely and securely operate the upgraded SAP system. This includes (but is not limited to): The finalization of system documentation The finalization of operational procedures as part of the operations handbook The check of the customer support organization. Procedure Resolve and Close Open Issues Handover Operations Responsibility How SAP Can Support SAP supports this activity with the “Handover to Support” scope option as part of the Build Execution service. The Handover to Support scope option: Evaluates process management framework quality like documentation, configuration, testing validation, or authorization management Evaluates process management knowledge transfer and IT support team capabilities for future maintenance Evaluates handover protocol procedures Builds handover conditions, recommendations, and supports customer adoption activities The Handover to Support scope option builds on top of deliverables created in the functional design and execution services. Please ask your SAP contact (e.g. SAP Client Partner) for more information.</p>
	<div>
		<h3>Resolve and Close Open Issues</h3>
		<p>The purpose of this task is to achieve a closure of all open project issues which is a prerequisite for the final project closure. Procedure The purpose of this task is to achieve a closure of all open project issues. In case this is not possible within an acceptable timeframe, prepare for an agreement with the IT operations team to take responsibility to resolve and close the issue. Hand over the current analysis and correction state to the IT operations team.</p>
		<h3>Handover Operations Responsibility</h3>
		<p>In this task, operations responsibility is formally handed over from the team who operated the new SAP system (usually a mix of resources from the project team and IT support) to the IT support operations team. Prerequisites System documentation is complete and available. Operations procedures are fully documented in the operations handbook. The IT support operations team is set up and trained to safely and securely operate and troubleshoot the new SAP system. The top issues and priority incidents identified during hyper care, are either solved, or there is a documented work around and move-forward plan available. Procedure Hand over operations responsibility to the IT support operations team.</p>
	</div>
	<h2>Project Closure and Sign-Off Project Deliverables</h2>
	<p>The purpose of the phase closure and sign-off deliverable is to: Ensure that all required deliverables from this phase and the project are complete and accurate, and close any outstanding issues Identify lessons learned during the phase to prepare for formal phase closure Capture customer feedback and potential Customer References</p>
	<div>
		<h3>Conduct Knowledge Management Gate</h3>
		<p>The purpose of this task is to collect knowledge assets and lessons learned at the end of each phase of the project that can be reused later by other projects. Collecting documents, experiences, project highlights and lessons learned throughout the duration of the project can help to facilitate the project by providing quick access and insights into key deliverables from earlier stages of the project.</p>
		<h3>Conduct Project Quality Gate</h3>
		<p>The purpose of the Quality Gate is to ensure that both compliance and project management standards are being upheld within our projects. A Quality Gate is a checklist milestone at the end of a project phase. Prior to moving into the next phase each project manager must demonstrate that they have complied with the mandatory deliverables associated with a methodology while ensuring best practice standards have been applied to ensure quality A Quality Gate looks at the following topics in detail: Conduct regular quality checks at defined or critical stages of the project lifecycle to assess the health of the project. Ensure that all key deliverables and actions of the gate have been completed in compliance with recommended practices and to the customer’s satisfaction. Enable project management to continuously communicate the process and build quality directly into the project. Provide a tool to effectively manage project expectations and monitor customer satisfaction. The deliverables assessed at each quality gate will be performed using the quality gate checklist with defined expectations to the maturity of particular project deliverables. Note: New additional key deliverables need to be added in the quality gate checklist by the Project Manager to the different project types</p>
		<h3>Conduct Project Management Review Service</h3>
		<p>The purpose of Project Management Review is to provide a proactive quality assurance review, with an impartial analysis of all aspects of the project – across all project management disciplines, enabling early detection of project issues with actionable recommendations.</p>
		<h3>Manage fulfilled Contracts</h3>
		<p>The purpose of this task is to ensure that each contract is closed by verifying that all work specified in the contractual arrangement was completed, and that all defined deliverables were accepted.</p>
		<h3>Resolve and close open Issues</h3>
		<p>The purpose of this task is to achieve a closure of all open project issues which is a prerequisite for the final project closure.</p>
		<h3>Finalize Project Closeout Report</h3>
		<p>The purpose of this task is to document the results of the project, both regarding achieved objectives, deliverables as well as adherence to schedule, costs and delivered value.</p>
		<h3>Obtain Sign-off for Project Closure and Results Acceptance</h3>
		<p>The purpose of this activity is to formally close the project by obtaining customer signatures on dedicated deliverables/documents e.g. Project Quality Gate, Project Closeout Report.</p>
	</div>
	<h2>QG5 – Transition to Support Organization</h2>
	<p>When reaching quality gate 5, operations of the new SAP system has been matured, and the conversion project comes to an end. Requirements and Constraints This quality gate runs together with the activity Project Closure and Sign-Off Project Deliverables. Procedure Run Q-Gate Transition to Support Organization</p>
	<div>
		<h3>Run Q-Gate Transition to Support Organization</h3>
		<p>Objective The objective of this task is to run the last Q-Gate. The main purpose of managing the quality gates is to ensure quality within the project and the transition to the support organization. All open tasks or issues should be assigned from project team members to support organization members and if in place, the SAP engagement team (e.g. SAP MaxAttention). Prerequisites In order to support the appropriate Quality Gates for the engagement, the SAP TQM and Quality Manager must have access to the Project Charter and any primary document giving an overview of the project schedule and the major milestones defined by the customer’s Project Management Office. Procedure The quality gate includes the review of the project status, the confirmation of major deliverables being completed, and the formal sign-off of the quality gates based on the defined completion criteria. The process owner is the project manager. To get the most possible value out of the quality gate, it is recommended to perform /execute the quality gate by a quality manager (from customer/ partner or SAP side) who is independent of the project management constraints (time, cost and budget) but has experience in managing projects and generally experienced in the technical and operational background. In exceptional cases project participated key team members (customer project manager supported by the SAP TQM) may take over the role of the quality manager. How to proceed: Process begins with a preparation call to plan the deliverables to be checked during the quality gate, and to identify participants at the assessment meeting (include the customer project manager, SAP TQM (if SAP has delivered services within the project phase), program manager as applicable and representation from others on invitation partner project manager, PMO and additional experts to support specific technical or business aspects. At the beginning of the quality gate the customer project manager presents the overall project status. The assigned quality manager reviews the fulfillment of the requirements of the checklist items. He verifies the quality of deliverables regarding completeness, accuracy (if applicable) and actuality to reach the defined and agreed customer expectations. The result of the quality gate is a signed quality check list which contain agreed action items for follow-up. When the Q-Gate is not passed the transition to new stage is on hold and action items have to be defined for follow-up. Store the Q-Gate results in SAP Solution Manager if possible. Decide on sending the Q-Gate results to SAP. Results The filled-out Q-Gate check list shows the status of the project at the end of the Hyper Care phase. The team has handed over all open tasks, issues to the operations team and, if in place the SAP Engagement (MaxAttention). How SAP Can Support The SAP TQM supports the Q-Gate as part of the Value Assurance Foundation. The results of the Q-Gate can be sent to the SAP MCC for support.</p>
	</div>
</div>
<h1>Run</h1>
<p>The transition project has ended with the Deploy phase. In the Run phase the aim is to establish safe and efficient operations of the newly created solution. This includes the operations platform, core IT support processes, the setup / fine tune of new / additional operations tools, and the enablement of the operational support team. Moreover, a continuous operations improvement should be established to improve IT operations based on newly gained experience. In addition, this is the right time to plan for further innovations which could be implemented according to the overall implementation strategy, which has been created in the Discover phase of the project (or separately, as part of a business transformation work stream). The implementation strategy can now be reviewed and enriched based on system usage experience which has been gained in the first weeks after Go-Live. Figure 6.1: Activities in the Run Phase</p>
<div>
	</div>
	<h2>Operate Solution</h2>
	<p>With project end, the customer support organization is responsible to operate the new solution. The aim of this activity is to ensure efficient daily operations. This affects IT support people, IT support processes, and tools. In addition, the customer support organization should seek for continuous improvement. Requirements and Constraints Go-Live and hyper care has finished successfully. The customer support organization is responsible for operating the new solution. Procedure IT is in charge to ensure business continuity on the one hand. On the other hand, IT needs to enable business change at the required speed and with no disruption. IT support should not be organized in a way to only “keep the lights on” – instead, safe and efficient IT support guarantees business continuity AND continuous improvement. Both aspects are covered in this activity. Safely and Efficiently Operate the new Solution Continuously Optimize IT Operations Note that this activity deals with the organization of IT support and improvement. Business improvement will be covered in the activity Improve and Innovate Solution.</p>
	<div>
		<h3>Safely and Efficiently Operate the new Solution</h3>
		<p>The purpose of this task is to safely and efficiently ensure business continuity and the operability of the solution. Operability is the ability to maintain IT systems in a functioning and operating condition, guaranteeing systems availability and required performance levels to support the execution of the enterprise’s business operations. Procedure Operating an SAP solution can easily fill an own road map. Therefore, this task can only list the most important aspects to consider: Install and configure the solution operation platform if not already done. I If not yet done configure core IT support processes like: Incident and Problem Management Change Management Configure efficient system management - SAP has developed a concept called “Run SAP like a Factory”. Core elements are: Application Operations Business Process Operations Operations Control Center (OCC) One approach to efficiently operate IT operations is to implement an OCC, which collects all critical alerts centrally, and proactively reacts before issues turn into problems. The OCC is tightly integrated with the Mission Control Center (MCC) at SAP. Based on the alert information, the OCC can establish a continuous improvement process to avoid critical alerts in future. This could feed into the next task Continuously Optimize IT Operations. How SAP Can Support SAP has a large set of offerings to SAP MaxAttention customers with respect to both configuration and enablement of operations functionality. For example SAP can configure Application Operations in your environment, and trains your IT support experts in using the tools daily. Ask your TQM for more details.</p>
		<h3>Continuously Optimize IT Operations</h3>
		<p>The purpose of this task is to continuously improve IT operations (e.g. via automation or switching from a re-active to a pro-active operations approach). Procedure This task can only name some out of the many improvement options: Advanced Customer Center of Expertise (aCCOE) An alternative approach to improve IT operations is to set up an aCCOE. The advanced certification for Customer COEs covers the full spectrum of SAP solution operations. Based on the SAP standards for solution operations and the Run SAP methodology, a team with advanced certification has integrated quality management in place, bringing transparency to the challenges and issues faced by the organization as a whole. This is paramount for mission critical operations. Visibility, alignment, and a common understanding of those top issues are enabled through the center&#x27;s ability to maintain a single source of truth - one central area where everything is tracked and from which all information flows. See accelerator section for details. Regular assessments of IT operations Run regular assessments and review on IT operations efficiency. How SAP Can Support SAP supports a tailored creation of an OCC for SAP Premium Engagement customers, and the advanced certification for customer COE’s. Contact your TQM for details.</p>
	</div>
	<h2>Improve and Innovate Solution</h2>
	<p>The aim of this activity is to further improve and simplify your new solution to realize the maximum benefit. On the one hand, this requires the periodic update, by implementing feature and support packs, to bring the latest innovations from SAP into your solution. On the other hand, a new planning cycle needs to be initiated together with your peers from the business units, to identify innovations which are mostly required. Procedure Periodically update your SAP system Initiate a new innovation cycle</p>
	<div>
		<h3>Periodically Update your SAP System</h3>
		<p>The goal of this task is to keep the SAP system current, by implementing innovations and corrections from SAP in a timely manner. This is handled by an efficient maintenance management process. Procedure Implement a maintenance management process for your SAP system and implement feature and support packs from SAP in a timely manner. Please note: More details on SAP feature and support pack implementation will be added to the road map in a future version.</p>
		<h3>Initiate a new Innovation Cycle</h3>
		<p>The goal of this task is to close the loop but initiating a new innovation cycle. Procedure Innovation is not a one-step-process, but a continuous journey. Your SAP system runs stable and is updated with latest innovations from SAP. Now it is time to review the innovation and implementation strategy you have created at the beginning of the project. Business requirements may have changed meanwhile, and your company may have gained experience on what is possible with the new solutions from SAP. Adjust your innovation strategy accordingly and start the next round by entering the Design phase of the next innovation project. How SAP Can Support There are multiple options how SAP can support your innovation and improvement journey, for instance check the Analytics &amp; Data Management Vision Workshop, Innovation Discovery Workshop, and Design at Scale for the Intelligent Enterprise.</p>
	</div>
	<h2>Conversion of SAP BW/4HANA to SAP Datasphere, SAP BW Bridge</h2>
	<p>SAP offers the possibility of a tool-supported move of SAP BW/4HANA investments to SAP Datasphere, SAP BW bridge. SAP BW bridge is a feature of SAP Datasphere, that provides a path to the public cloud for SAP BW and SAP BW/4HANA customers. The conversion can be done as a shell conversion or a remote conversion, as described in the following task.</p>
	<div>
		<h3>Move from SAP BW or SAP BW/4HANA to SAP Datasphere, SAP BW Bridge</h3>
		<p>As described in Strategic Planning in the Discover phase, moving to SAP Datasphere, SAP BW bridge, could be a successor step to moving to SAP BW/4HANA and combining it in a hybrid scenario with SAP Datasphere. SAP Datasphere, SAP BW bridge is a functional enhancement of SAP Data Warehouse Cloud. It provides customers who are running SAP Business Warehouse or SAP BW/4HANA with access to the public cloud. It offers SAP BW capabilities directly in SAP Datasphere: The connectivity and business content for SAP BW-based data integration (extractors) from SAP ECC and S/4HANA The SAP Business Warehouse layer for loading data with partitioning, monitoring, and error handling, all tailored to the needs of your company Shell Conversion If you want to keep selected data models (without data) from your current SAP BW/4HANA system, you may choose to perform a shell conversion to SAP Datasphere, SAP BW bridge instead of starting from scratch. Condition details for the Shell Conversion A provisioned SAP Datasphere, SAP BW bridge is needed. The Transfer Cockpit can be used to transfer selected data models without data into an SAP BW bridge tenant. Support of carve-out and system consolidation scenarios Accelerate greenfield approach by transferring and converting data models and flows Minimum start release: SAP BW 7.30 to 7.50 running on SAP HANA or Any-DB, and SAP BW/4HANA 2021 The basic sequence of the shell conversion steps are indicated in the below grafic: The entire shell conversion is described step by step in the Conversion Guide for SAP Data Warehouse Cloud, SAP BW Bridge (Shell Conversion) (see accelerator section). The execution of the shell conversion is supported by the SAP PS service “Conversion Execution Service for SAP Datasphere”: Delivery Approach &amp; Scope: Assess customer requirements and agree on service scope. Establish a target SAP Datasphere environment and connect one SAP source system (e.g. to an SAP ERP or SAP S/4HANA source system). SAP-led conversion execution including tailored support to the customer’s situation. Convert dataflows in scope from classic SAP BW to SAP Datasphere, SAP BW bridge with the transfer tool. Handover documentation at the end of the service. Remote Conversion A remote conversion to SAP Datasphere, SAP BW Bridge has become available, too. The Remote Conversion offers the possibility to transfer the metadata plus the business data from your on-premise BW system to a target BW system, There are some differences between the Shell and Remote Conversion in terms of the scope collection logic for the metadata transfer, especially because of the relationship and dependencies of the request management. When using the Remote Conversion, data and request Information need to be copied and converted from the sender into the receiver system (Request ID to TSN), therefore all objects on which a data request is touching become relevant for the scope collection in order to ensure consistency. The Remote Conversion to SAP Datasphere, BW bridge is supported by the tooling from BW 7.3 (SP10 and higher) and BW/4HANA 2021 (SP00 and higher) releases. The DMIS add-on installation is required (not needed for the Shell Conversion). For detailed information about pre-requisites and preparations, see: SAP Note 3141688 – Conversion from SAP BW or SAP BW/4HANA to SAP Datasphere, SAP BW Bridge. For the conversion procedure and tips and tricks, check the Runbook for SAP Datasphere, SAP BW Bridge. Condition details for the Remote Conversion A provisioned SAP Datasphere, SAP BW bridge is needed. The Transfer Cockpit can be used to transfer selected data models into an SAP BW bridge tenant and to perform a remote data transfer into that tenant. Support of carve-out and consolidation scenarios Transport data models and remote data transfer Risk mitigation due to parallel system Minimum start release: SAP BW 7.30 to 7.50 running on SAP HANA or Any-DB, and SAP BW/4HANA 2021 The basic sequence of the shell conversion steps are indicated in the below grafic:</p>
	</div>
</div>